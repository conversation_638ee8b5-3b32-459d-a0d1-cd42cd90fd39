package com.meiyunji.sponsored.service.strategy.service.impl;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.ImmutableMap;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IUserService;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.autoRule.po.AdAutoRuleTemplateDisabled;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.kafka.AdStrategyTemplateEnableKafkaProducer;
import com.meiyunji.sponsored.service.log.enums.ItemTypeEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogActionEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogTargetEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.*;
import com.meiyunji.sponsored.service.strategy.enums.AdItemType;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.enums.PolicyTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplateDisabled;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyTemplateService;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.strategyTask.dao.AdvertiseStrategyTaskDao;
import com.meiyunji.sponsored.service.strategyTask.po.AdvertiseStrategyTask;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.vo.DisabledTemplateDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Service
@Slf4j
public class AdvertiseStrategyTemplateServiceImpl implements IAdvertiseStrategyTemplateService {

    @Autowired
    private AdvertiseStrategyTemplateDao strategyTemplateDao;
    @Autowired
    private AdvertiseStrategyStatusDao advertiseStrategyStatusDao;
    @Autowired
    private AdvertiseStrategyAdGroupDao advertiseStrategyAdGroupDao;
    @Autowired
    private IUserService userService;
    @Autowired
    private AdvertiseStrategyTemplateSequenceDao advertiseStrategyTemplateSequenceDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private AdvertiseStrategyTaskDao advertiseStrategyTaskDao;
    @Autowired
    private IAdvertiseStrategyTemplateDisabledDao advertiseStrategyTemplateDisabledDao;
    @Autowired
    private RedisTemplate redisTemplateNew;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private AdStrategyTemplateEnableKafkaProducer adStrategyTemplateEnableKafkaProducer;

    @Override
    public Result<Page<AdvertiseStrategyTemplate>> pageList(int puid, AdvertiseStrategyTemplateRequest request) {
        Result<Page<AdvertiseStrategyTemplate>> result = new Result<>();
        try {
            Page<AdvertiseStrategyTemplate> voPage = new Page<>();

            //转移场景，查询出非本类型的模板id，再在pageList查询时not in
            if (StringUtils.isNotBlank(request.getAddWayType())) {
                List<Long> templateIds = Lists.newArrayList();
                if ("AD_GROUP_TARGET".equals(request.getAddWayType())) {
                    List<Long> advertiseStrategyStatusDaoListTemplateIds = advertiseStrategyStatusDao.getListTemplateIds(puid, request.getShopIdList(), request.getAddWayType());
                    if (CollectionUtils.isNotEmpty(advertiseStrategyStatusDaoListTemplateIds)) {
                        templateIds.addAll(advertiseStrategyStatusDaoListTemplateIds);
                    }
                } else {
                    List<Long> advertiseStrategyAdGroupDaoListTemplateIds = advertiseStrategyAdGroupDao.getListTemplateIds(puid, request.getShopIdList());
                    if (CollectionUtils.isNotEmpty(advertiseStrategyAdGroupDaoListTemplateIds)) {
                        templateIds.addAll(advertiseStrategyAdGroupDaoListTemplateIds);
                    }
                }
                request.setTemplateIdList(templateIds);
            }

            //分时启停的转移场景：要求前端传startStopItemType的值，并且templateId的值为转移对象当前所在的模板id

            Page<AdvertiseStrategyTemplate> page = strategyTemplateDao.pageList(puid, request);
            voPage.setPageNo(page.getPageNo());
            voPage.setPageSize(page.getPageSize());
            voPage.setTotalSize(page.getTotalSize());
            voPage.setTotalPage(page.getTotalPage());
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                List<AdvertiseStrategyTemplate> list = page.getRows();
                Map<Long, List<AdvertiseStrategyTask>> taskMap = null;
                List<AdvertiseStrategyTemplate> advertiseStrategyTemplateList = Lists.newArrayListWithExpectedSize(list.size());
                //收集组受控模板id
                Map<Long, Integer> groupCountMap = new HashMap<>();
                List<Long> templateIds = list.stream().map(AdvertiseStrategyTemplate::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(templateIds)) {
                    List<TemplateStatusCountVo> countVoList = advertiseStrategyAdGroupDao.getTemplateUsageAmount(puid, templateIds, request.getItemType());
                    if (CollectionUtils.isNotEmpty(countVoList)) {
                        groupCountMap = countVoList.stream().collect(Collectors.toMap(TemplateStatusCountVo::getTemplateId, TemplateStatusCountVo::getStatusCount));
                    }
                }

                //异步任务
                List<AdvertiseStrategyTask> taskList = advertiseStrategyTaskDao.queryListByTemplateIdList(puid, request.getShopIdList(), templateIds);
                if (CollectionUtils.isNotEmpty(taskList)) {
                    taskMap = taskList.stream().collect(Collectors.groupingBy(AdvertiseStrategyTask::getOldTemplateId));
                }

                for (AdvertiseStrategyTemplate advertiseStrategyTemplate : list) {
                    Integer groupCount = groupCountMap.get(advertiseStrategyTemplate.getId());
                    if (Objects.nonNull(groupCount)) {
                        advertiseStrategyTemplate.setUsageAmount(groupCount);
                        advertiseStrategyTemplate.setAddWayType("AD_GROUP_TARGET");
                    }

                    if (MapUtils.isNotEmpty(taskMap) && taskMap.containsKey(advertiseStrategyTemplate.getId())) {
                        List<AdvertiseStrategyTask> tasks = taskMap.get(advertiseStrategyTemplate.getId());
                        for (AdvertiseStrategyTask advertiseStrategyTask : tasks) {
                            if (advertiseStrategyTask.getState().equals(0)) {
                                advertiseStrategyTemplate.setState(0);
                                break;
                            }
                        }
                    }
                    advertiseStrategyTemplateList.add(advertiseStrategyTemplate);
                }
                voPage.setRows(advertiseStrategyTemplateList);
            }
            result.setCode(Result.SUCCESS);
            result.setData(voPage);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setData(null);
            log.error("traceId:{} puid:{} shopIdList:{} 分页查询模板数据异常:", request.getTraceId(), puid, request.getShopIdList(), e);
        }
        return result;
    }

    @Override
    public Result<Long> insertTemplate(Integer puid, AdvertiseStrategyTemplate template, String loginIp, String traceId) {
        Result<Long> result = new Result<>();
        if (verifyUserCreateTemplate(puid, template.getItemType())) {
            return ResultUtil.error("免费套餐最多使用1条，使用更多请升级套餐");
        }

        if (StringUtils.isBlank(template.getTemplateName())) {
            return ResultUtil.error("模板名称为空");
        }

        if (template.getTemplateName().length() > 100) {
            return ResultUtil.error("模板名称不能超过100个字符");
        }

        if (strategyTemplateDao.existByName(puid, template.getTemplateName().trim(),template.getItemType())) {
            return ResultUtil.error("模板名称已存在");
        }

        // 增加日志操作记录(新增)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        List<OperationContent> operationContents = new ArrayList<>();
        List<AdManageOperationLog> list = new ArrayList<>();

        try {
            Long id = advertiseStrategyTemplateSequenceDao.genId();
            template.setId(id);
            strategyTemplateDao.insertTemplate(puid,template);
            result.setCode(Result.SUCCESS);
            result.setData(id);
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} shopId:{} 模板插入异常:", traceId, puid, template.getShopId(), e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo(result.getMsg());
        }

        adManageOperationLog.setAction(OperationLogActionEnum.ADD.getOperationValue());
        OperationContent operationContent = new OperationContent();
        operationContent.setTitle("新建模板");
        operationContent.setNewValue(template.getTemplateName());
        operationContents.add(operationContent);

        if (ItemTypeEnum.PORTFOLIO.getItemType().equals(template.getItemType())) {
            // TODO 增加日志
            if (PbUtil.isPortfolioHour(template.getChildrenItemType())) {
                MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(template.getMarketplaceId());
                List<PortfolioHourRuleVo> ruleVoList = JSONUtil.jsonToArray(template.getRule(), PortfolioHourRuleVo.class);
                for (PortfolioHourRuleVo vo : ruleVoList) {
                    OperationContent switchRuleContent = new OperationContent();
                    switchRuleContent.setTitle("新建模板");
                    switchRuleContent.setNewValue(PbUtil.builderLogInfo(vo, m).toString());
                    operationContents.add(switchRuleContent);
                }
                OperationContent content1 = new OperationContent();
                OperationContent content2 = new OperationContent();
                content1.setTitle("预算回调");
                content1.setName("预算上限类型");
                content2.setTitle("预算回调");
                content2.setName("日期范围金额");
                OriginValueVo oldValueVo = JSONUtil.jsonToObject(template.getReturnValue(),OriginValueVo.class);
                content1.setNewValue(PolicyTypeEnum.valueOf(oldValueVo.getPolicy()).getName());
                if (!PolicyTypeEnum.noBudget.name().equals(oldValueVo.getPolicy())) {
                    if (oldValueVo.getAmount() != null) {
                        content2.setNewValue(oldValueVo.getAmount().toString());
                    }
                } else {
                    content2.setNewValue("-");
                }
                operationContents.add(content1);
                operationContents.add(content2);
            } else {
                StringBuilder builder = new StringBuilder();
                List<PortfolioRuleVo> ruleVoList = JSONUtil.jsonToArray(template.getRule(), PortfolioRuleVo.class);
                for (PortfolioRuleVo vo : ruleVoList) {
                    OperationContent switchRuleContent = new OperationContent();
                    switchRuleContent.setTitle("新建模板");
                    if ("MONTH_DAY".equals(template.getType())) {
                        switchRuleContent.setName("按月调整");
                        builder.append(vo.getStartDate()).append("~").append(vo.getEndDate()).
                                append(" 预算值调整为 ").append(vo.getAmount());
                    } else {
                        switchRuleContent.setName(getDayOfTheWeek(vo.getSiteDate()));
                        builder.append(" 预算值调整为 ").append(vo.getAmount());
                    }
                    switchRuleContent.setNewValue(builder.toString());
                    operationContents.add(switchRuleContent);
                    builder.delete(0, builder.length());
                }
                OperationContent content1 = new OperationContent();
                OperationContent content2 = new OperationContent();
                content1.setTitle("预算回调");
                content1.setName("预算上限类型");
                content2.setTitle("预算回调");
                content2.setName("日期范围金额");
                OriginValueVo oldValueVo = JSONUtil.jsonToObject(template.getReturnValue(),OriginValueVo.class);

                if ("noBudget".equals(oldValueVo.getPolicy())) {
                    content1.setNewValue("无预算上限");
                } else if ("dateRange".equals(oldValueVo.getPolicy())) {
                    content1.setNewValue("日期范围");
                } else {
                    content1.setNewValue("每月定期");
                }
                if (oldValueVo.getAmount() != null) {
                    content2.setNewValue(oldValueVo.getAmount().toString());
                }
                if ("noBudget".equals(oldValueVo.getPolicy())) {
                    content2.setNewValue("-");
                }
                operationContents.add(content1);
                operationContents.add(content2);
            }
        }
        adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContents));
        filterAdManageLog(puid, template.getCreateUid(), loginIp, template, adManageOperationLog, list, operationContent);

        return result;
    }

    public boolean verifyUserCreateTemplate (Integer puid, String itemType) {
        // 是否免费用户
        boolean isFree = userService.isFree(puid);   // 付费用户不做限制
        if (!isFree) {
            return false;
        }

        return strategyTemplateDao.verifyTemplateCount(puid, itemType);
    }

    @Override
    public Result<Long> updateTemplate(Integer puid, AdvertiseStrategyTemplate template, String loginIp, String traceId) {
        Result<Long> result = new Result<>();

        // 增加日志操作记录(编辑模板)
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        List<AdManageOperationLog> list = new ArrayList<>();

        try {
            if (template.getTemplateName().length() > 100) {
                return ResultUtil.error("模板名称不能超过100个字符");
            }
            //版本号处理
            AdvertiseStrategyTemplate oldTemplate = strategyTemplateDao.selectByPrimaryKey(puid, template.getId());
            if (oldTemplate == null) {
                result.setCode(Result.ERROR);
                result.setMsg("当前模板已移除");
                return result;
            }
            Integer versionId = oldTemplate.getVersion();
            template.setVersion(versionId + 1);
            template.setMarketplaceId(oldTemplate.getMarketplaceId());
            template.setShopId(oldTemplate.getShopId());
            strategyTemplateDao.updateTemplate(puid,template);
            result.setCode(Result.SUCCESS);
            result.setData(template.getId());
            result.setMsg("修改成功");

            // 日志信息拼接
            getOperationContent(adManageOperationLog, template, oldTemplate, template.getItemType());
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} id:{} 模板修改异常:", traceId, puid, template.getId(), e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());

            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo(result.getMsg());
        }

        filterAdManageLog(puid, template.getUpdateUid(), loginIp, template, adManageOperationLog, list, null);

        return result;
    }


    @Override
    public Result<String> deleteTemplate(Integer puid, Long id, Integer uid, String loginIp, String traceId) {
        Result<String> result = new Result<>();

        // 增加日志操作记录(编辑:删除)
        AdvertiseStrategyTemplate template = strategyTemplateDao.selectByPrimaryKey(puid, id);
        if (template == null) {
            result.setCode(Result.ERROR);
            result.setMsg("当前模板已移除");
            return result;
        }
        AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
        OperationContent operationContent = new OperationContent();
        List<AdManageOperationLog> list = new ArrayList<>();
        adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
        operationContent.setTitle("删除模板");
        operationContent.setNewValue(template.getTemplateName());
        adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));

        try {
            if (advertiseStrategyStatusDao.existListByTemplateId(puid,id) > 0) {
                adManageOperationLog.setResult(1);
                adManageOperationLog.setResultInfo("当前模板存在受控对象不能删除");
                filterAdManageLog(puid, uid, loginIp, template, adManageOperationLog, list, operationContent);
                return ResultUtil.error("当前模板存在受控对象不能删除");
            }
            // 删除模板
            strategyTemplateDao.deleteTemplateId(puid, id);
            result.setCode(Result.SUCCESS);
            result.setMsg("删除成功");
            adManageOperationLog.setResult(0);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("当前模板删除异常");
            log.error("traceId:{} templateId:{} 查询当前模板数据异常:", traceId,id, e);
            adManageOperationLog.setResult(1);
            adManageOperationLog.setResultInfo("当前模板删除异常：" + e.getMessage());
        }

        filterAdManageLog(puid, uid, loginIp, template, adManageOperationLog, list, operationContent);
        return result;
    }

    @Override
    public Result<AdvertiseStrategyTemplate> getTemplateById(Integer puid, Long id, String marketplaceId) {
        Result<AdvertiseStrategyTemplate> result = new Result<>();
        try {
            AdvertiseStrategyTemplate advertiseStrategyTemplate = strategyTemplateDao.selectByPrimaryKey(puid,id);
            if (advertiseStrategyTemplate != null) {
                result.setCode(Result.SUCCESS);
                result.setData(advertiseStrategyTemplate);
            } else {
                result.setCode(Result.SUCCESS);
                result.setData(null);
                result.setMsg("当前模板无数据");
            }
        } catch (Exception e) {
            log.error("templateId={} 查询当前模板数据异常:",id,e);
            result.setCode(Result.ERROR);
            result.setMsg("当前模板数据异常");
        }
        return result;
    }

    @Override
    public Result<AdvertiseStrategyTemplate> copyTemplate(Integer puid, Long id) {
        Result<AdvertiseStrategyTemplate> result = new Result<>();
        try {
            AdvertiseStrategyTemplate advertiseStrategyTemplate = strategyTemplateDao.selectByPrimaryKey(puid,id);
            result.setCode(Result.SUCCESS);
            result.setData(advertiseStrategyTemplate);
        }catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("复制模板异常");
            log.error("templateId={}  复制模板异常:",id,e);
        }
        return result;
    }

    @Override
    public Result<List<AdvertiseStrategyTemplate>> getList(Integer puid,String templateName,String itemType) {
        Result<List<AdvertiseStrategyTemplate>> result = new Result<>();
        try {
            List<AdvertiseStrategyTemplate> list = new ArrayList<>();
            List<AdvertiseAutoRuleTemplate> advertiseAutoRuleTemplateList =  advertiseAutoRuleTemplateDao.getList(puid, templateName,itemType);
            List<AdvertiseStrategyTemplate> advertiseStrategyTemplateList = strategyTemplateDao.getList(puid,templateName,itemType);
            if (CollectionUtils.isNotEmpty(advertiseAutoRuleTemplateList)) {
                for (AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate : advertiseAutoRuleTemplateList) {
                    AdvertiseStrategyTemplate advertiseStrategyTemplate = new AdvertiseStrategyTemplate();
                    advertiseStrategyTemplate.setId(advertiseAutoRuleTemplate.getId());
                    advertiseStrategyTemplate.setTemplateName(advertiseAutoRuleTemplate.getTemplateName());
                    list.add(advertiseStrategyTemplate);
                }
            }
            if (CollectionUtils.isNotEmpty(advertiseStrategyTemplateList)) {
                for (AdvertiseStrategyTemplate advertiseStrategyTemplate : advertiseStrategyTemplateList){
                    AdvertiseStrategyTemplate vo = new AdvertiseStrategyTemplate();
                    vo.setId(advertiseStrategyTemplate.getId());
                    vo.setTemplateName(advertiseStrategyTemplate.getTemplateName());
                    list.add(advertiseStrategyTemplate);
                }
            }
            result.setCode(Result.SUCCESS);
            result.setData(list);
        }catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("模板下拉框查询异常");
            log.error("puid={} templateName={} 模板下拉框查询异常:",puid,templateName,e);
        }
        return result;
    }

    @Override
    public Result<List<AdvertiseStrategyTemplate>> transferTemplate(Integer puid, String templateName, Integer shopId) {
        Result<List<AdvertiseStrategyTemplate>> result = new Result<>();
//        try {
//            List<AdvertiseStrategyTemplate> list = strategyTemplateDao.getHourList(puid,templateName,shopId);
//            result.setCode(Result.SUCCESS);
//            result.setData(list);
//        }catch (Exception e) {
//            result.setCode(Result.ERROR);
//            result.setMsg("复制模板异常");
//            log.error("puid={} shopId={} 复制模板异常:{}",puid,shopId,e);
//        }
        return result;
    }

    @Override
    public Page<AdvertiseStrategyTemplate> excelExport(int puid, AdvertiseStrategyTemplateRequest strategyTemplateVo) {
        Page<AdvertiseStrategyTemplate> voPage = new Page<>();
        Page<AdvertiseStrategyTemplate> page = strategyTemplateDao.pageList(puid,strategyTemplateVo);
        voPage.setPageNo(page.getPageNo());
        voPage.setPageSize(page.getPageSize());
        voPage.setTotalSize(page.getTotalSize());
        voPage.setTotalPage(page.getTotalPage());
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            voPage.setRows(page.getRows());
        }
        return voPage;
    }

    @Override
    public void updateTemplateStatus(int puid, List<AdvertiseStrategyTemplate> templateList, String status, Long updateUid, Boolean isAuto) {
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }
        List<Long> templateIdList = new ArrayList<>(templateList.size());
        List<AdvertiseStrategyTemplateDisabled> disabledList = new ArrayList<>(templateList.size());

        //查询sellerId
        List<Integer> shopIdList = templateList.stream().map(AdvertiseStrategyTemplate::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.getScAndVcByIds(shopIdList);
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

        //根据seller组装templateId
        List<TemplateEnableVo> templateEnableVoList = new ArrayList<>(templateList.size());
        List<TemplateEnableVo> templateDisabledVoList = new ArrayList<>(templateList.size());
        Date date = new Date();
        List<AdManageOperationLog> list = new ArrayList<>();
        for (AdvertiseStrategyTemplate x : templateList) {
            templateIdList.add(x.getId());
            ShopAuth shopAuth = shopAuthMap.get(x.getShopId());
            if (Objects.isNull(shopAuth)) {
                continue;
            }
            String itemType = computeItemType(x);
            if (AdStrategyEnableStatusEnum.DISABLED.getCode().equals(status)) {
                disabledList.add(new AdvertiseStrategyTemplateDisabled(puid, x.getShopId(), shopAuth.getSellingPartnerId(), x.getId(), itemType, date, date));
                templateDisabledVoList.add(new TemplateEnableVo(shopAuth.getSellingPartnerId(), itemType, x.getId(), AdStrategyEnableStatusEnum.DISABLED.getCode()));
            } else {
                templateEnableVoList.add(new TemplateEnableVo(shopAuth.getSellingPartnerId(), itemType, x.getId(), AdStrategyEnableStatusEnum.ENABLED.getCode()));
            }

            AdManageOperationLog adManageOperationLog = new AdManageOperationLog();
            OperationContent operationContent = new OperationContent();

            adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
            operationContent.setTitle("应用状态");
            AdStrategyEnableStatusEnum newStatus = AdStrategyEnableStatusEnum.geEnumByCode(status);
            operationContent.setNewValue(newStatus == null ? status : newStatus.getDesc());
            AdStrategyEnableStatusEnum oldStatus = AdStrategyEnableStatusEnum.geEnumByCode(x.getStatus());
            operationContent.setPreviousValue(oldStatus == null ? x.getStatus() : oldStatus.getDesc());
            adManageOperationLog.setOperationContent(JSONUtil.objectToJson(operationContent));
            if (!Boolean.TRUE.equals(isAuto)) {
                fillLog(puid, Math.toIntExact(updateUid), "", x, adManageOperationLog, list, null);
            }
        }

        //更新模板状态
        strategyTemplateDao.updateStrategyTemplateStatus(puid, templateIdList, status, updateUid);
        log.info("分时策略更新模板状态成功, puid: {}, templateIdList: {}, status: {}, uid: {}", puid, templateIdList, status, updateUid);

        //写入广告主库
        //禁用
        if (AdStrategyEnableStatusEnum.DISABLED.getCode().equals(status) && CollectionUtils.isNotEmpty(disabledList)) {
            advertiseStrategyTemplateDisabledDao.insert(disabledList);
            log.info("分时策略禁用模板，写入广告主库成功, puid: {}, templateIdList: {}, status: {}, uid: {}", puid, templateIdList, status, updateUid);
            //发消息，aadas的template_status更新为暂停
            if(CollectionUtils.isNotEmpty(templateDisabledVoList)) {
                adStrategyTemplateEnableKafkaProducer.send(puid, templateDisabledVoList);
            }
        } else {
            //启用
            //删除主库
            advertiseStrategyTemplateDisabledDao.deleteByTemplateId(puid, templateIdList);
            log.info("分时策略启用模板，写入广告主库成功, puid: {}, templateIdList: {}, status: {}, uid: {}", puid, templateIdList, status, updateUid);
            //发消息，aadas的template_status更新为启用
            if (CollectionUtils.isNotEmpty(templateEnableVoList)) {
                adStrategyTemplateEnableKafkaProducer.send(puid, templateEnableVoList);
            }
        }
        log.info("分时策略更新模板状态写入主库/redis和发送kafka成功, puid: {}, templateIdList: {}, status: {}, uid: {}", puid, templateIdList, status, updateUid);

        if (!Boolean.TRUE.equals(isAuto)) {
            adManageOperationLogService.printAdOtherOperationLog(list);
        }
    }

    @Override
    public void disableAllTemplateStatus(Integer puid, Integer shopId, Boolean isAuto) {
        //已开启的模板
        log.info("分时策略自动更新模板状态, puid: {}, shopId: {}", puid, shopId);
        List<AdvertiseStrategyTemplate> disabledList = strategyTemplateDao.getEnabledTemplate(puid, shopId);

        if (CollectionUtils.isEmpty(disabledList)) {
            return;
        }

        updateTemplateStatus(puid, disabledList, AutoRuleEnableStatusEnum.DISABLED.getCode(), null, isAuto);
    }

    @Override
    public void retryDisableTemplate(Integer puid, List<AdvertiseStrategyTemplate> templateList) {
        if (CollectionUtils.isEmpty(templateList)) {
            return;
        }

        List<AdvertiseStrategyTemplateDisabled> disabledList = new ArrayList<>(templateList.size());
        List<Long> disabledIdList = new ArrayList<>(templateList.size());

        //查询sellerId
        List<Integer> shopIdList = templateList.stream().map(AdvertiseStrategyTemplate::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.getScAndVcByIds(shopIdList);
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

        Date date = new Date();
        templateList.forEach(x -> {
            ShopAuth shopAuth = shopAuthMap.get(x.getShopId());
            if (Objects.nonNull(shopAuth)) {
                String itemType = computeItemType(x);
                disabledIdList.add(x.getId());
                disabledList.add(new AdvertiseStrategyTemplateDisabled(puid, x.getShopId(), shopAuth.getSellingPartnerId(), x.getId(), itemType, date, date));
            }
        });

        if (CollectionUtils.isEmpty(disabledList)) {
            redisTemplateNew.delete(String.format(RedisConstant.STRATEGY_DISABLED_TEMPLATE_KEY, puid));
            return;
        }

        advertiseStrategyTemplateDisabledDao.insert(disabledList);

        //删除
        advertiseStrategyTemplateDisabledDao.deleteByExcludeTemplateIdList(puid, disabledIdList);
    }

    /**
     * 需要考虑用户禁用后，还在移除和添加受控对象，广告主库的数据是不会变的，所以这里只能信任itemType和childrenItemType等参数
     * @param template
     * @return
     */
    private String computeItemType(AdvertiseStrategyTemplate template) {
        //活动预算
        if (AdItemType.CAMPAIGN.name().equals(template.getItemType())) {
            return TaskTimeType.campaignBudget.name();
        }
        //组合天和小时
        if (AdItemType.PORTFOLIO.name().equals(template.getItemType())) {
            if ("HOUR".equals(template.getChildrenItemType())) {
                return TaskTimeType.portfolioHourAmount.name();
            } else {
                return TaskTimeType.portfolioAmount.name();
            }
        }
        //广告位
        if (AdItemType.CAMPAIGN_PLACEMENT.name().equals(template.getItemType())) {
            return TaskTimeType.campaignPlacement.name();
        }
        //启停
        if (AdItemType.START_STOP.name().equals(template.getItemType())) {
            return TaskTimeType.campaignState.name();
        }
        //投放
        if (AdItemType.TARGET.name().equals(template.getItemType())) {
            return TaskTimeType.targetBid.name();
        }

        return "";
    }


    /**
     * 填充日志对象公用方法
     * @param puid
     * @param uid
     * @param loginIp
     * @param template
     * @param adManageOperationLog
     * @param list
     * @param content
     */
    private void filterAdManageLog(Integer puid, Integer uid, String loginIp, AdvertiseStrategyTemplate template,AdManageOperationLog adManageOperationLog, List<AdManageOperationLog> list, OperationContent content) {
        if (template == null) {
            return;
        }
        fillLog(puid, uid, loginIp, template, adManageOperationLog, list, content);
        adManageOperationLogService.printAdOtherOperationLog(list);
    }





    private void fillLog (Integer puid, Integer uid, String loginIp, AdvertiseStrategyTemplate template,AdManageOperationLog adManageOperationLog, List<AdManageOperationLog> list, OperationContent content) {
        if (template == null) {
            return;
        }
        adManageOperationLog.setPuid(puid);
        adManageOperationLog.setUid(uid);
        adManageOperationLog.setIp(loginIp);
        adManageOperationLog.setTemplateId(template.getId());
        adManageOperationLog.setTargetId(String.valueOf(template.getId()));
        adManageOperationLog.setShopId(template.getShopId());
        if (StringUtils.isNotBlank(template.getMarketplaceId())) {
            adManageOperationLog.setMarketplaceId(template.getMarketplaceId());
        }
        // 存模板id和模板名称关系(便于后续模板删除后查询以往的信息)
        adManageOperationLog.setTarget(template.getTemplateName() + "：" + template.getId());
        if (ItemTypeEnum.CAMPAIGN.getItemType().equals(template.getItemType())) {
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.BUDGET_TEMPLATE.getTargetValue());
        }
        if (ItemTypeEnum.CAMPAIGN_PLACEMENT.getItemType().equals(template.getItemType())) {
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.PLACEMENT_TEMPLATE.getTargetValue());
        }
        if (ItemTypeEnum.TARGET.getItemType().equals(template.getItemType())) {
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.BIDDING_TEMPLATE.getTargetValue());
        }
        if (ItemTypeEnum.START_STOP.getItemType().equals(template.getItemType())) {
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.SWITCH_TEMPLATE.getTargetValue());
        }
        if (ItemTypeEnum.PORTFOLIO.getItemType().equals(template.getItemType())) {
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.PORTFOLIO_TEMPLATE.getTargetValue());
        }
        if (ItemTypeEnum.getItemTypeEnumItemValue(template.getItemType()) == null) {
            adManageOperationLog.setOperationObject(OperationLogTargetEnum.UNCLASSIFIED_OBJECT.getTargetValue());
        }
        list.add(adManageOperationLog);
        if (content != null) {
            List<OperationContent> contentList = new ArrayList<>();
            contentList.add(content);
            adManageOperationLog.setMessage(adManageOperationLog.handleMessage(contentList));
        }
    }



    /**
     * 拼接编辑模板信息
     * @param adManageOperationLog
     * @param newTemplate
     * @param oldTemplate
     * @param itemType
     */
    private void getOperationContent(AdManageOperationLog adManageOperationLog, AdvertiseStrategyTemplate newTemplate, AdvertiseStrategyTemplate oldTemplate, String itemType) {
        List<OperationContent> operationContents = new ArrayList<>();

        adManageOperationLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());

        if (! newTemplate.getTemplateName().equals(oldTemplate.getTemplateName())) {
            OperationContent nameContent = new OperationContent();
            nameContent.setName("模板名称");
            nameContent.setPreviousValue(oldTemplate.getTemplateName());
            nameContent.setNewValue(newTemplate.getTemplateName());
            operationContents.add(nameContent);
        }

        String rule = newTemplate.getRule();
        String oldRule = oldTemplate.getRule();
        String oldReturnValue = oldTemplate.getReturnValue();
        String newReturnValue = newTemplate.getReturnValue();
        StringBuilder builder = new StringBuilder();
        String symbol = "%";

        if (!oldRule.equals(rule)) {
            if (ItemTypeEnum.CAMPAIGN.getItemType().equals(itemType)) {
                String budgetStitchContent = " ";
                if ("numerical".equals(newTemplate.getChildrenItemType())) {
                    budgetStitchContent = " 预算值调整为 ";
                    symbol = "";
                }
                if ("proportion".equals(newTemplate.getChildrenItemType())) {
                    budgetStitchContent = " 原预算 ";
                }
                List<CampaignRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, CampaignRuleVo.class);
                for (CampaignRuleVo vo : ruleVoList) {
                    OperationContent campaignRuleContent = new OperationContent();
                    builder.append(vo.getStartTimeSite()).append(":00 ~ ").append(vo.getEndTimeSite()).append(":00").append(budgetStitchContent).append(vo.getBudgetValue()).append(symbol);
                    campaignRuleContent.setTitle("编辑模板");
                    campaignRuleContent.setName(getDayOfTheWeek(vo.getSiteDate()));
                    campaignRuleContent.setNewValue(builder.toString());
                    operationContents.add(campaignRuleContent);
                    builder.delete(0, builder.length());
                }
            }
            if (ItemTypeEnum.CAMPAIGN_PLACEMENT.getItemType().equals(itemType)) {
                List<CampaignPlacementRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, CampaignPlacementRuleVo.class);
                for (CampaignPlacementRuleVo vo : ruleVoList) {
                    OperationContent campaignPlacementRuleContent = new OperationContent();
                    builder.append(vo.getStartTimeSite()).append(":00 ~ ").append(vo.getEndTimeSite()).append(":00 ");
                    stitchCampaignPlacementInformation(builder, vo);
                    campaignPlacementRuleContent.setTitle("编辑模板");
                    campaignPlacementRuleContent.setName(getDayOfTheWeek(vo.getSiteDate()));
                    campaignPlacementRuleContent.setNewValue(builder.toString());
                    operationContents.add(campaignPlacementRuleContent);
                    builder.delete(0, builder.length());
                }
            }
            if (ItemTypeEnum.TARGET.getItemType().equals(itemType)) {
                List<TargetRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, TargetRuleVo.class);
                for (TargetRuleVo vo : ruleVoList) {
                    OperationContent targetRuleContent = new OperationContent();
                    builder.append(vo.getStartTimeSite()).append(":00 ~ ").append(vo.getEndTimeSite()).append(":00 ");
                    stitchTargetInformation(builder, vo.getBiddingType(), vo.getBiddingValue(), vo.getBiddingMaxValue(), vo.getBiddingMinValue());
                    targetRuleContent.setTitle("编辑模板");
                    targetRuleContent.setName(getDayOfTheWeek(vo.getSiteDate()));
                    targetRuleContent.setNewValue(builder.toString());
                    operationContents.add(targetRuleContent);
                    builder.delete(0, builder.length());
                }
            }
            if (ItemTypeEnum.START_STOP.getItemType().equals(itemType)) {
                List<StartStopRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, StartStopRuleVo.class);
                for (StartStopRuleVo vo : ruleVoList) {
                    OperationContent switchRuleContent = new OperationContent();
                    builder.append(vo.getStartTimeSite()).append(":00 ~ ").append(vo.getEndTimeSite()).append(":00 ").append(ItemTypeEnum.getItemTypeEnumItemValue(vo.getState()));
                    switchRuleContent.setTitle("编辑模板");
                    switchRuleContent.setName(getDayOfTheWeek(vo.getSiteDate()));
                    switchRuleContent.setNewValue(builder.toString());
                    operationContents.add(switchRuleContent);
                    builder.delete(0, builder.length());
                }
            }
            if (ItemTypeEnum.PORTFOLIO.getItemType().equals(itemType)) {
                if (PbUtil.isPortfolioHour(oldTemplate.getChildrenItemType())) {
                    MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(oldTemplate.getMarketplaceId());
                    List<PortfolioHourRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, PortfolioHourRuleVo.class);
                    for (PortfolioHourRuleVo vo : ruleVoList) {
                        OperationContent switchRuleContent = new OperationContent();
                        switchRuleContent.setTitle("编辑模板");
                        switchRuleContent.setNewValue(PbUtil.builderLogInfo(vo, m).toString());
                        operationContents.add(switchRuleContent);
                        builder.delete(0, builder.length());
                    }
                } else {
                    List<PortfolioRuleVo> ruleVoList = JSONUtil.jsonToArray(rule, PortfolioRuleVo.class);
                    for (PortfolioRuleVo vo : ruleVoList) {
                        OperationContent switchRuleContent = new OperationContent();
                        switchRuleContent.setTitle("编辑模板");
                        if ("MONTH_DAY".equals(newTemplate.getType())) {
                            switchRuleContent.setName("按月调整");
                            builder.append(vo.getStartDate()).append("~").append(vo.getEndDate()).
                                    append(" 预算值调整为 ").append(vo.getAmount());
                        } else {
                            switchRuleContent.setName(getDayOfTheWeek(vo.getSiteDate()));
                            builder.append(" 预算值调整为 ").append(vo.getAmount());
                        }
                        switchRuleContent.setNewValue(builder.toString());
                        operationContents.add(switchRuleContent);
                        builder.delete(0, builder.length());
                    }
                }
            }
        }

        if (ItemTypeEnum.PORTFOLIO.getItemType().equals(itemType)) {
            if (!oldReturnValue.equals(newReturnValue)) {
                OperationContent content1 = new OperationContent();
                OperationContent content2 = new OperationContent();
                content1.setTitle("预算回调");
                content1.setName("预算上限类型");
                content2.setTitle("预算回调");
                content2.setName("日期范围金额");
                OriginValueVo oldValueVo = JSONUtil.jsonToObject(oldReturnValue, OriginValueVo.class);
                OriginValueVo newValueVo = JSONUtil.jsonToObject(newReturnValue, OriginValueVo.class);
                if ("noBudget".equals(oldValueVo.getPolicy())) {
                    content1.setPreviousValue("无预算上限");
                    content2.setPreviousValue("-");
                } else if ("dateRange".equals(oldValueVo.getPolicy())) {
                    content1.setPreviousValue("日期范围");
                    content2.setPreviousValue(oldValueVo.getAmount().toString());
                } else {
                    content1.setPreviousValue("每月定期");
                    content2.setPreviousValue(oldValueVo.getAmount().toString());
                }
                if ("noBudget".equals(newValueVo.getPolicy())) {
                    content1.setNewValue("无预算上限");
                    content2.setNewValue("-");
                } else if ("dateRange".equals(newValueVo.getPolicy())) {
                    content1.setNewValue("日期范围");
                    content2.setNewValue(newValueVo.getAmount().toString());
                } else {
                    content1.setNewValue("每月定期");
                    content2.setNewValue(newValueVo.getAmount().toString());
                }
                operationContents.add(content1);
                operationContents.add(content2);
            }
        }

        adManageOperationLog.setMessage(adManageOperationLog.handleMessage(operationContents));
        String operaContent = JSONUtil.objectToJson(operationContents);
        adManageOperationLog.setOperationContent(operaContent);

    }

    /**
     * 转换为星期几文案
     * @param siteDate
     * @return
     */
    private String getDayOfTheWeek(Integer siteDate) {
        if (siteDate == 1) {
            return "每周一";
        }
        if (siteDate == 2) {
            return "每周二";
        }
        if (siteDate == 3) {
            return "每周三";
        }
        if (siteDate == 4) {
            return "每周四";
        }
        if (siteDate == 5) {
            return "每周五";
        }
        if (siteDate == 6) {
            return "每周六";
        }
        if (siteDate == 7) {
            return "每周日";
        }
        if (siteDate == 0) {
            return "每日";
        }
        return "";
    }


    /**
     * 拼接广告位模板编辑信息
     * @param builder
     * @param vo
     */
    private void stitchCampaignPlacementInformation(StringBuilder builder, CampaignPlacementRuleVo vo) {
        if (vo.getAdPlaceTopType() == 0) {
            builder.append("顶部（首页）竞价上浮 ").append(vo.getAdPlaceTopValue()).append("%").
                    append("且调整后最大值不大于 ").append(vo.getAdPlaceTopMaxValue()).append("%").append(";");
        }
        if (vo.getAdPlaceTopType() == 1) {
            builder.append("顶部（首页）竞价上浮绝对值 ").append(vo.getAdPlaceTopValue()).append("%").
                    append("且调整后最大值不大于 ").append(vo.getAdPlaceTopMaxValue()).append("%").append(";");
        }
        if (vo.getAdPlaceTopType() == 2) {
            builder.append("顶部（首页）竞价下浮 ").append(vo.getAdPlaceTopValue()).append("%").
                    append("且调整后最小值不小于 ").append(vo.getAdPlaceTopMinValue()).append("%").append(";");
        }
        if (vo.getAdPlaceTopType() == 3) {
            builder.append("顶部（首页）竞价下浮绝对值 ").append(vo.getAdPlaceTopValue()).append("%").
                    append("且调整后最大值不大于 ").append(vo.getAdPlaceTopMinValue()).append("%").append(";");
        }
        if (vo.getAdPlaceTopType() == 4) {
            builder.append("顶部（首页）竞价比例 ").append(vo.getAdPlaceTopValue()).append("%").append(";");
        }
        if (vo.getAdPlaceProductType() == 0) {
            builder.append(" 产品页面竞价上浮 ").append(vo.getAdPlaceProductValue()).append("%").
                    append("且调整后最大值不大于 ").append(vo.getAdPlaceProductMaxValue()).append("%").append(";");
        }
        if (vo.getAdPlaceProductType() == 1) {
            builder.append(" 产品页面竞价上浮绝对值 ").append(vo.getAdPlaceProductValue()).append("%").
                    append("且调整后最大值不大于 ").append(vo.getAdPlaceProductMaxValue()).append("%").append(";");
        }
        if (vo.getAdPlaceProductType() == 2) {
            builder.append(" 产品页面竞价竞价下浮 ").append(vo.getAdPlaceProductValue()).append("%").
                    append("且调整后最小值不小于 ").append(vo.getAdPlaceProductMinValue()).append("%").append(";");
        }
        if (vo.getAdPlaceProductType() == 3) {
            builder.append(" 产品页面竞价下浮绝对值 ").append(vo.getAdPlaceProductValue()).append("%").
                    append("且调整后最大值不大于 ").append(vo.getAdPlaceProductMinValue()).append("%").append(";");
        }
        if (vo.getAdOtherType() != null && vo.getAdOtherType() == 4) {
            builder.append(" 搜索结果其余位置竞价比例 ").append(vo.getAdOtherValue()).append("%").append(";");
        }
        if (vo.getAdPlaceProductType() == 4) {
            builder.append(" 产品页面竞价比例 ").append(vo.getAdPlaceProductValue()).append("%").append(";");
        }
        if (StringUtils.isNotBlank(vo.getStrategy())) {
            String strategy = null;
            if ("legacyForSales".equalsIgnoreCase(vo.getStrategy())) {
                strategy = "动态竞价-只降低";
            } else if ("autoForSales".equalsIgnoreCase(vo.getStrategy())) {
                strategy = "动态竞价-提高和降低";
            } else if ("manual".equalsIgnoreCase(vo.getStrategy())) {
                strategy = "固定竞价";
            } else if ("ruleBased".equalsIgnoreCase(vo.getStrategy())) {
                strategy = "基于规则的竞价";
            }
            builder.append(" 基于竞价策略调整: ").append(strategy);
        }
    }


    /**
     * 拼接竞价模板编辑信息
     * @param builder
     * @param biddingType
     * @param biddingValue
     * @param biddingMaxValue
     * @param biddingMinValue
     */
    private void stitchTargetInformation(StringBuilder builder, Integer biddingType, BigDecimal biddingValue, BigDecimal biddingMaxValue, BigDecimal biddingMinValue) {
        if (biddingType == 0 || biddingType == 1) {
            builder.append("原始竞价 增加 ").append(biddingValue);
            if (biddingType == 1) {
                builder.append("%");
            }
            if (biddingMaxValue != null) {
                builder.append(" 且不大于 ").append(biddingMaxValue);
            }
            builder.append(";");
        }
        if (biddingType == 2 || biddingType == 3) {
            builder.append("原始竞价 减少 ").append(biddingValue);
            if (biddingType == 3) {
                builder.append("%");
            }
            if (biddingMinValue != null) {
                builder.append(" 且不小于 ").append(biddingMinValue);
            }
            builder.append(";");
        }
        if (biddingType == 4) {
            builder.append("自定义新竞价 ").append(biddingValue).append(" ;");
        }
    }


}
