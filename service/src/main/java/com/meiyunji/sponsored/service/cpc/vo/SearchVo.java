package com.meiyunji.sponsored.service.cpc.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @ClassName SearchVo
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/6/18 11:24
 **/
@Data
public class SearchVo {
    private Integer puid;
    private Integer shopId;
    private String marketplaceId;
    private String startDate;
    private String endDate;
    private Date start;
    private Date end;
    private String searchType;
    private String searchValue;
    private String orderField; //排序字段
    private String orderValue; //排序值
    private String adGroupName;
    private String tabType; //产品使用(parentAsin,asin,sku)
    private String campaignId;  //产品使用
    private String groupId;    //产品使用
    private List<Integer> shopIds;
    private String adFormat;

    private String spCampaignType;  //活动类型
    private List<String> campaignIds;
    private String spGroupType;    //广告组类型
    private List<String> groupIds;
    private String matchType;    // 匹配类型 broad，phrase，exact
    private String spTargetType;  // 投放类型 0、 自动定位 1、 类目定位 2、ASIN定位
    private String type;
    private String adType;
    private String reportName;

    /**
     * 下载类型
     */
    private String downloadType;
    private String campaignSite;
}
