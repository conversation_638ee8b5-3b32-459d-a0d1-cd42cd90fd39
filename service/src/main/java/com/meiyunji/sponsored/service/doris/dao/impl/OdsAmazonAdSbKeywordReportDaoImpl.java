package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.bo.AdKeywordOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.RepeatTargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.KeywordDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.ThemeKeywordTextEnum;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.qo.KeywordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbKeywordReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbKeywordReport;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * sb关键词报告(OdsAmazonAdSbKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
@Repository
@Slf4j
public class OdsAmazonAdSbKeywordReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdSbKeywordReport> implements IOdsAmazonAdSbKeywordReportDao {

    @Autowired
    private IOdsAmazonAdCampaignAllDao odsAmazonAdCampaignAllDao;

    private static final Map<String, String> sbKeywordFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> sbKeywordContainRateFieldSumMap = Maps.newHashMap();

    static {
        sbKeywordContainRateFieldSumMap.put(DashboardDataFieldEnum.COST.getCode(), "ifnull(sum(cost * c.rate), 0) as cost ");
        sbKeywordContainRateFieldSumMap.put(DashboardDataFieldEnum.TOTAL_SALES.getCode(), "ifnull(sum(sales14d * c.rate), 0) as totalSales ");
        sbKeywordContainRateFieldSumMap.put(DashboardDataFieldEnum.ACOS.getCode(), " ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(sales14d * c.rate), 0), 4), 0) as acos ");
        sbKeywordContainRateFieldSumMap.put(DashboardDataFieldEnum.ROAS.getCode(), " ifnull(ROUND(ifnull(sum(sales14d * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) as roas ");

        sbKeywordFieldSumMap.put(DashboardDataFieldEnum.IMPRESSIONS.getCode(), "ifnull(sum(report.impressions), 0) as impressions ");
        sbKeywordFieldSumMap.put(DashboardDataFieldEnum.CLICKS.getCode(), "ifnull(sum(report.clicks), 0) as clicks ");
        sbKeywordFieldSumMap.put(DashboardDataFieldEnum.ORDER_NUM.getCode(), " ifnull(sum(conversions14d), 0) as orderNum ");
        sbKeywordFieldSumMap.put(DashboardDataFieldEnum.SALE_NUM.getCode(), "ifnull(sum(units_sold14d), 0) as saleNum ");
        sbKeywordFieldSumMap.put(DashboardDataFieldEnum.CLICK_RATE.getCode(), " ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) as clickRate ");//点击率
        sbKeywordFieldSumMap.put(DashboardDataFieldEnum.CONVERSION_RATE.getCode(), " ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) as conversionRate ");//转化率
    }
    @Override
    public String buildQueryAdKeywordChartsSql(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate,
                                               List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                               List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        boolean isWhere = false;
        StringBuilder sql = new StringBuilder();
        sql.append("select keyword_text keyword, match_type matchType, ifnull(sum(r.cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ");
        sql.append("ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sql.append("ifnull(sum(conversions14d), 0) orderNum, ifnull(sum(units_sold14d), 0) saleNum ");
        sql.append("from ").append(getJdbcHelper().getTable()).append(" r ");
        sql.append("join (select puid,month,`from`,rate from dim_currency_rate where puid = ? and `to` = ? ) c ");
        sql.append("on r.puid = c.puid and r.count_month = c.month and r.puid = ? ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("and r.puid = ?  ");

        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);
        argsList.add(puid);


        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and r.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.shop_id", shopIdList, argsList));
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.count_day)", siteToday, argsList));
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("r.campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            isWhere = true;
            sql.append(" where r.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }

            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        if (Boolean.TRUE.equals(noZero) && DashboardDataFieldEnum.noZeroFieldSet.contains(dashboardDataFieldEnum.getCode())) {
            if (isWhere) {
                sql.append(" and ");
            } else {
                sql.append(" where ");
                isWhere = true;
            }
            sql.append(" r." + getColumn(dashboardDataFieldEnum.getCode()) +" <> 0 ");
        }

        sql.append("group by keyword, matchType ");
        return sql.toString();
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getKeywordTopList(Integer puid, List<String> marketplaceIdList,
                                                                    List<Integer> shopIdList, String currency,
                                                                    String startDate, String endDate,
                                                                    DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                                    Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                    List<String> campaignIds, Boolean noZero) {
        boolean isWhere = false;
        if (Objects.isNull(sbKeywordFieldSumMap.get(dataField.getCode())) && Objects.isNull(sbKeywordContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT 'sb' as `type`, report.keyword_id keywordId,");
        if (Objects.nonNull(sbKeywordContainRateFieldSumMap.get(dataField.getCode()))) {
            for (String rateField : sbKeywordContainRateFieldSumMap.keySet()) {
                sb.append(sbKeywordContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            sb.append(sbKeywordFieldSumMap.get(dataField.getCode()));
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        if (Objects.nonNull(sbKeywordContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
            sb.append(" and report.puid = ? ");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            isWhere = true;
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            sb.append("and report.keyword_id in ('").append(StringUtils.join(keywordIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sb.append(" and ");
            } else {
                sb.append(" where ");
            }
            sb.append(" report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by report.keyword_id");
        sb.append(" ORDER BY ").append(dataField.getCode());
        if (StringUtils.isNotEmpty(orderBy.getCode())) {
            sb.append(" ").append(orderBy.getCode());
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getKeywordTopInfoList(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency,
                                                                        String startDate, String endDate, DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                                        DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                        List<String> campaignIds, Boolean noZero) {
        if (Objects.isNull(sbKeywordFieldSumMap.get(dataField.getCode())) && Objects.isNull(sbKeywordContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT 'sb' as `type`, report.keyword_id keywordId,");
        if (Objects.nonNull(sbKeywordContainRateFieldSumMap.get(dataField.getCode()))) {
            sbKeywordFieldSumMap.forEach((k, v) -> {
                    sb.append(v);
                    sb.append(",");
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            for (String rateField : sbKeywordContainRateFieldSumMap.keySet()) {
                sb.append(sbKeywordContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sbKeywordFieldSumMap.forEach((k, v) -> {
                if (!k.equals((dataField.getCode()))) {
                    sb.append(v);
                    sb.append(",");
                }
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        if (Objects.isNull(sbKeywordContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month and report.puid = ?");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(" and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(" and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            sb.append(" and report.keyword_id in ('").append(StringUtils.join(keywordIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }

        sb.append(" and report.count_day >= ? and report.count_day <= ? ");
        argsList.add(startDate);
        argsList.add(endDate);
        sb.append(" group by report.keyword_id");
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<String> getKeywordPageIdList(Integer puid, KeywordsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select distinct keyword_id from ods_t_amazon_ad_word_root_keyword_sb ");
        sqlSb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        //查出关键词列表页的所有keywordId
        StringBuilder sb = new StringBuilder("select ANY(k.keyword_id) keywordId from ods_t_amazon_ad_keyword_sb k ");
        //只筛选出有报告数据的
        sb.append(" join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id ");
        sb.append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        //有竞价高级筛选或竞价排序时需连广告组表获取默认竞价
        if (((param.getUseAdvanced() && param.getBidMax() != null || param.getBidMin() != null))
                || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sb.append(this.getKeywordPageWhereSql(puid, param, argsList));
        sb.append(" group by k.keyword_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getKeywordPageHavingSql(param, argsList));
        }
        sqlSb.append(" and keyword_id in (").append(sb).append(") ");
        sqlSb.append(" and word_root = ? ");
        argsList.add(param.getWordRoot());
        return getJdbcTemplate().queryForList(sqlSb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public int getKeywordAllCount(Integer puid, KeywordsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select k.keyword_id ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_keyword_sb k" );
        // 没有高级筛选或排序时，只查基础数据表即为列表页所有的关键词，有高级筛选或排序时需连报告表过滤
        if (param.getUseAdvanced() || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()))) {
            sb.append(" left join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id ");
            sb.append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        // 有竞价高级筛选或竞价排序时需连广告组表获取默认竞价
        if (((param.getUseAdvanced() && param.getBidMax() != null || param.getBidMin() != null))
                || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        // where条件过滤
        sb.append(this.getKeywordPageWhereSql(puid, param, argsList));
        sb.append(" group by k.keyword_id ");
        if (param.getUseAdvanced()) {
            // having条件过滤
            sb.append(this.getKeywordPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        //排序
        Object[] args = argsList.toArray();
        return countPageResult(puid, sqlCountSb.toString(), args);
    }

    @Override
    public Page<KeywordPageVo> getKeywordPage(Integer puid, KeywordsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select k.keyword_id ");
        StringBuilder sqlSb = new StringBuilder("select ANY(k.keyword_id) keywordId, any(k.keyword_text) keywordText ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_keyword_sb k" );
        // 没有高级筛选或排序时，只查基础数据表即为列表页所有的关键词，有高级筛选或排序时需连报告表过滤
        if (param.getUseAdvanced() || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()))) {
            sb.append(" left join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id ");
            sb.append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        // 有竞价高级筛选或竞价排序时需连广告组表获取默认竞价
        if (((param.getUseAdvanced() && param.getBidMax() != null || param.getBidMin() != null))
                || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        // where条件过滤
        sb.append(this.getKeywordPageWhereSql(puid, param, argsList));
        sb.append(" group by k.keyword_id ");
        if (param.getUseAdvanced()) {
            // having条件过滤
            sb.append(this.getKeywordPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        sqlSb.append(sb);
        // 封装排序
        sqlSb.append(SqlStringReportUtil.getDorisTargetPageOrderBySql(param.getOrderField(), param.getOrderType(), "ANY(k.create_time) desc", param.getType(), param.isQueryJoinSearchTermsRank()) + ", keywordId desc");
        Object[] args = argsList.toArray();
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), sqlCountSb.toString(), args, sqlSb.toString(), args, KeywordPageVo.class);
    }

    @Override
    public List<String> getKeywordIdListByPage(Integer puid, String startStr, String endStr, KeywordsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String sql = this.getKeywordIdListByPageSql(puid, startStr, endStr, param, argsList, false);
        return this.getJdbcTemplate().queryForList(sql, String.class, argsList.toArray());
    }

    private String getKeywordIdListByPageSql(Integer puid, String startStr, String endStr, KeywordsPageParam param, List<Object> argsList, boolean isLeftJoin) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select k.keyword_id from ods_t_amazon_ad_keyword_sb k ");
        if (isLeftJoin) {
            sql.append(" LEFT ");
        }
        sql.append(" JOIN ");
        sql.append(this.getJdbcHelper().getTable())
                .append(" r on r.puid = k.puid and r.shop_id = k.shop_id and r.keyword_id = k.keyword_id ")
                .append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startStr);
        argsList.add(endStr);
        //若有竞价排序或者竞价的高级筛选，需要连广告组表，获取广告组默认竞价
        if (param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null)) {
            sql.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sql.append(this.getKeywordPageWhereSql(puid, param, argsList));
        sql.append(" group by k.keyword_id ");
        if (param.getUseAdvanced()) {
            sql.append(this.getKeywordPageHavingSql(param, argsList));
        }
        return sql.toString();
    }

    @Override
    public List<WordRootTopVo> getWordRootTopList(Integer puid, KeywordTopQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select word_root, count(distinct keyword_id) count ")
                .append(" from ods_t_amazon_ad_word_root_keyword_sb ");
        sqlSb.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(qo.getShopId());
        //查出关键词列表页的所有keywordId
        StringBuilder sb = new StringBuilder("select ANY(k.keyword_id) keywordId from ods_t_amazon_ad_keyword_sb k ");
        //只查有报告数据的
        sb.append(" join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id ");
        sb.append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(qo.getShopId());
        argsList.add(qo.getStartDate());
        argsList.add(qo.getEndDate());
        //有竞价高级筛选或竞价排序时需连广告组表获取默认竞价
        if (((qo.getUseAdvanced() && qo.getBidMax() != null || qo.getBidMin() != null))
                || (StringUtils.isNotBlank(qo.getOrderField()) && StringUtils.isNotBlank(qo.getOrderType()) && SqlStringReportUtil.BID.equals(qo.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(qo.getShopId());
        }
//        //aba排名参数高级筛选联表
//        if (qo.isQueryJoinSearchTermsRank()) {
//            sb.append(" left join ods_t_week_search_terms_analysis a on lower(k.keyword_text) = a.search_term and a.start_date=? and a.marketplace_id = ? ");
//            argsList.add(qo.getLastWeekSearchTermsRankDate());
//            argsList.add(qo.getMarketplaceId());
//
//            if (qo.getSearchFrequencyRankMin() != null) {
//                sb.append(" and ifnull(search_frequency_rank, 2147483647) >= ? ");
//                argsList.add(qo.getSearchFrequencyRankMin());
//            }
//            if (qo.getSearchFrequencyRankMax() != null) {
//                sb.append(" and ifnull(search_frequency_rank, 2147483647) <= ? ");
//                argsList.add(qo.getSearchFrequencyRankMax());
//            }
//            if (qo.getWeekRatioMin() != null) {
//                sb.append(" and ifnull(round(week_ratio*100,2), -2147483648) >= ? ");
//                argsList.add(qo.getWeekRatioMin());
//            }
//            if (qo.getWeekRatioMax() != null) {
//                sb.append(" and ifnull(round(week_ratio*100,2), -2147483648) <= ? ");
//                if (qo.getWeekRatioMin() == null) {
//                    sb.append(" and ifnull(round(week_ratio*100,2),-2147483648) > -2147483648 ");
//                }
//                argsList.add(qo.getWeekRatioMax());
//            }
//        }
        sb.append(this.getKeywordPageWhereSql(puid, qo, argsList));
        sb.append(" group by k.keyword_id ");
        if (qo.getUseAdvanced()) {
            sb.append(this.getKeywordPageHavingSql(qo, argsList));
        }
        sqlSb.append(" and keyword_id in (").append(sb).append(") ");
        if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
            WordRoot.WordFrequencyType wordFrequencyType = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(qo.getWordFrequencyType()));
            if (wordFrequencyType != null) {
                sqlSb.append(" and word_frequency_type = ? ");
                argsList.add(qo.getWordFrequencyType());
            }
        }
        sqlSb.append(" group by word_root order by count desc ");
        if (qo.getTop() != null) {
            sqlSb.append(" limit ").append(qo.getTop().toString());
        }
        return getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTopVo.class));
    }

    @Override
    public List<KeywordPageVo> getReportListByKeywordIds(Integer puid, Integer shopId, List<String> keywordIds, String startStr, String endStr) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select  ANY(keyword_id) keywordId,'sb' type,")
                .append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(cost), 0)  cost,")
                .append("IFNULL(SUM(units_sold14d), 0) order_num,")
                .append("IFNULL(SUM(conversions14d), 0) sale_num,")
                .append("IFNULL(SUM(conversions14d_same_sku), 0) ad_sale_num,")
                .append("IFNULL(SUM(sales14d), 0)      total_sales,")
                .append("IFNULL(SUM(sales14d_same_sku), 0)         `ad_sales`,")
                .append("IFNULL(MAX(top_of_search_is), 0) max_top_is,")
                .append("IFNULL(MIN(top_of_search_is), 0) min_top_is,")
                .append("IFNULL(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append("IFNULL(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append("IFNULL(sum(video5second_views), 0) video5SecondViews,")
                .append("IFNULL(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append("IFNULL(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append("IFNULL(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append("IFNULL(sum(video_complete_views), 0) video_complete_views,")
                .append("IFNULL(sum(video_unmutes), 0) video_unmutes,")
                .append("IFNULL(sum(viewable_impressions), 0) viewable_impressions,")
                .append("IFNULL(sum(branded_searches14d), 0) brandedSearches")
                .append(" from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startStr);
        argsList.add(endStr);
        sb.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
        sb.append(" group by keyword_id ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(KeywordPageVo.class));
    }

    @Override
    public AdMetricDto getKeywordPageSumMetricData(Integer puid, KeywordsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder topSb = new StringBuilder("select SUM(cost) sumCost,SUM(sale_num) sumAdOrderNum,SUM(total_sales) sumAdSale, SUM(order_num) sumOrderNum from ( ");
        StringBuilder sqlSb = new StringBuilder(" select ANY(k.puid) puid, ANY(k.shop_id) shopId, ANY(k.ad_group_id) adGroupId, ANY(k.bid) bid, SUM(cost) cost,SUM(conversions14d) sale_num,SUM(sales14d) total_sales, SUM(units_sold14d) order_num ");
        sqlSb.append(" from ods_t_amazon_ad_keyword_sb k join ods_t_amazon_ad_sb_keyword_report r ")
                .append(" on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id ")
                .append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());

        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sqlSb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sqlSb.append(this.getKeywordPageWhereSql(puid, param, argsList));
        sqlSb.append(" group by k.keyword_id ");
        if (param.getUseAdvanced()) {
            sqlSb.append(this.getKeywordPageHavingSql(param, argsList));
        }
        sqlSb = new StringBuilder(topSb).append(sqlSb).append(" ) s");
        Object[] args = argsList.toArray();
        List<AdMetricDto> list = getJdbcTemplate().query(sqlSb.toString(), args, new BeanPropertyRowMapper<>(AdMetricDto.class));
        return list.size() == 1 ? list.get(0) : null;
    }

    @Override
    public List<AdHomePerformancedto> getReportAggregateByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr, KeywordsPageParam param, boolean isGroupByDate) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' type,");
        if (isGroupByDate) {
            sql.append(" ANY(count_date) count_date, ");
        }
        sql.append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(cost), 0)  adCost,")
                .append("IFNULL(SUM(conversions14d), 0) ad_order_num,")
                .append("IFNULL(SUM(units_sold14d), 0) salesNum,")
                .append("IFNULL(SUM(conversions14d_same_sku), 0) ad_sale_num,")
                .append("IFNULL(SUM(sales14d), 0)      adSale,")
                .append("IFNULL(SUM(sales14d_same_sku), 0)         `ad_sales`,")
                .append("IFNULL(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append("IFNULL(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append("IFNULL(sum(video5second_views), 0) video5SecondViews,")
                .append("IFNULL(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append("IFNULL(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append("IFNULL(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append("IFNULL(sum(video_complete_views), 0) video_complete_views,")
                .append("IFNULL(sum(video_unmutes), 0) video_unmutes,")
                .append("IFNULL(sum(viewable_impressions), 0) viewable_impressions,")
                .append("IFNULL(sum(branded_searches14d), 0) brandedSearches")
                .append(" from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append("  and count_day >= ? and count_day <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);
        sql.append(" and keyword_id in ( ");
        sql.append(this.getKeywordIdListByPageSql(puid, startStr, endStr, param, argsList, false));
        sql.append(" ) ");
        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class));
    }

    @Override
    public AdHomePerformancedto getReportAggregateCompareDataByKeywordIdList(Integer puid, KeywordsPageParam param, String compareStartData, String compareEndData) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select 'sb' type,");
        sql.append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(cost), 0)  adCost,")
                .append("IFNULL(SUM(conversions14d), 0) ad_order_num,")
                .append("IFNULL(SUM(units_sold14d), 0) salesNum,")
                .append("IFNULL(SUM(conversions14d_same_sku), 0) ad_sale_num,")
                .append("IFNULL(SUM(sales14d), 0)      adSale,")
                .append("IFNULL(SUM(sales14d_same_sku), 0)         `ad_sales`,")
                .append("IFNULL(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append("IFNULL(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append("IFNULL(sum(video5second_views), 0) video5SecondViews,")
                .append("IFNULL(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append("IFNULL(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append("IFNULL(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append("IFNULL(sum(video_complete_views), 0) video_complete_views,")
                .append("IFNULL(sum(video_unmutes), 0) video_unmutes,")
                .append("IFNULL(sum(viewable_impressions), 0) viewable_impressions,")
                .append("IFNULL(sum(branded_searches14d), 0) brandedSearches");
        sql.append(" from ").append(this.getJdbcHelper().getTable());
        sql.append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sql.append(" and count_day >= ? and count_day <= ? ");
        argsList.add(compareStartData);
        argsList.add(compareEndData);
        sql.append(" and keyword_id in ( ").append(this.getKeywordIdListByPageSql(puid, param.getStartDate(), param.getEndDate(), param, argsList, true)).append(" ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }

    private String getKeywordPageWhereSql(Integer puid, KeywordsPageParam param, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where k.puid = ? and k.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", StringUtil.splitStr(param.getCampaignId()), argsList));
        }
        //广告组合查询
        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            sb.append(" and k.campaign_id in ( ")
                    .append(odsAmazonAdCampaignAllDao.getCampaignIdsByPortfolioIdSql(puid, param.getShopId(), argsList, param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), null, null))
                    .append(" ) ");
        }
        //广告组ID搜索
        if (StringUtils.isNotBlank(param.getGroupId())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.ad_group_id", StringUtil.splitStr(param.getGroupId()), argsList));
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.keyword_id", param.getKeywordIds(), argsList));
        }
        //词根id筛选
        if (CollectionUtils.isNotEmpty(param.getWordRootKeywordIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.keyword_id", param.getWordRootKeywordIds(), argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(param.getAdStrategyTypeList())){
            String sql = AdTargetStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(),param.getAutoRuleGroupIds(), argsList, "k.keyword_id","k.ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                if(!param.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode())){
                    sb.append(sql);
                    sb.append(" and k.match_type != 'theme' ");
                }else{
                    sb.append(" and ( ");
                    // 去掉第一个and
                    sql = sql.replaceFirst("and", "");
                    sb.append(sql);
                    sb.append(" or k.match_type = 'theme' )");
                }
            }
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(param.getSearchType())) { //模糊搜索
                sb.append(" and lower(k.keyword_text) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue().toLowerCase()) + "%");
            } else {//默认精确
                if (param.getListSearchValue().size() > 1) {
                    List<String> lowerCaseValue = new ArrayList<>();
                    for (String value : param.getListSearchValue()) {
                        lowerCaseValue.add(value.trim().toLowerCase());
                    }
                    sb.append(SqlStringUtil.dealInList("lower(k.keyword_text)", lowerCaseValue, argsList));
                } else {
                    sb.append(" and lower(k.keyword_text) = ? ");
                    argsList.add(param.getSearchValue().trim().toLowerCase());
                }
            }
        }
        //状态
        if (StringUtils.isNotBlank(param.getStatus())) {
            sb.append(SqlStringUtil.dealInList("k.state", StringUtil.splitStr(param.getStatus()), argsList));
        }
        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            sb.append(" and k.match_type = ? ");
            argsList.add(param.getMatchType());
        }
        return sb.toString();
    }

    /**
     * 关键词投放列表页having条件sql拼接
     */
    private String getKeywordPageHavingSql(KeywordsPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getDorisSbTargetPageHavingSql(qo, argsList);
    }

    public static final Map<String, String> keywordSbOrderFieldMap = Maps.newHashMap();
    static {
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.IMPRESSIONS.getCode(), " ,impressions as impressions ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.CLICKS.getCode(), " ,clicks as clicks ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.COST.getCode(), " ,cost as cost ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.ORDER_NUM.getCode(), " ,conversions14d as adOrder ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.TOTAL_SALES.getCode()," ,sales14d as adSales ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.CPC.getCode()," ,cost as cost,clicks as clicks ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.CPA.getCode()," ,cost as cost,conversions14d as adOrder ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.ACOS.getCode()," ,cost as cost,sales14d as adSales ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.ROAS.getCode()," ,cost as cost,sales14d as adSales ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.CONVERSION_RATE.getCode()," ,clicks as clicks,conversions14d as adOrder ");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.CLICK_RATE.getCode()," ,clicks as clicks,impressions as impressions ");
        // ABA排名通过分布查询进行排序
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.SEARCH_FREQUENCY_RANK.getCode(),"");
        keywordSbOrderFieldMap.put(KeywordDataFieldEnum.WEEK_RATIO.getCode(),"");
    }
    @Override
    public List<AdKeywordOrderBo> getSbKeywordTexts(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSp = new StringBuilder("select keyword_text keywordText ");
        if (StringUtils.isNotBlank(param.getOrderField())) {
            sqlSp.append(keywordSbOrderFieldMap.get(param.getOrderField()));
        }
        sqlSp.append("from ods_t_amazon_ad_sb_keyword_report where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sqlSp.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sqlSp.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            sqlSp.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        if (CollectionUtils.isNotEmpty(keywordTexts)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : keywordTexts) {
                lowerCaseList.add(str.toLowerCase());
            }
            sqlSp.append(SqlStringUtil.dealInList("lower(keyword_text)",lowerCaseList,argsList));
        }
        return getJdbcTemplate().query(sqlSp.toString(), new ObjectMapper<>(AdKeywordOrderBo.class), argsList.toArray());
    }

    @Override
    public List<KeywordLibsVo> getSbKeywordReportData(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_id, any(keyword_text) keywordText, sum(cost * c.rate) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales from ods_t_amazon_ad_sb_keyword_report r ");
        sql.append("join (select * from dim_currency_rate where puid = ? ");
        argsList.add(puid);
        if (StringUtils.isNotBlank(param.getTo())){
            sql.append(" and `to` = ? ");
            argsList.add(param.getTo());
        }
        sql.append(" ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        if (CollectionUtils.isNotEmpty(keywordTexts)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : keywordTexts) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(keyword_text)",lowerCaseList,argsList));
        }
        sql.append(" group by keyword_id");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(KeywordLibsVo.class), argsList.toArray());
    }

    @Override
    public List<KeywordLibsDetailVo> getSbKeywordReportData(Integer puid, KeywordLibsPageParam param, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_id keywordId, any(keyword_text) keywordText, sum(cost * c.rate) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales from ods_t_amazon_ad_sb_keyword_report r ");
        sql.append("join (select * from dim_currency_rate where puid = ? and `to` = ? ");
        argsList.add(puid);
        if (StringUtils.isNotBlank(param.getTo())){
            argsList.add(param.getTo());
        }
        sql.append(" ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIds())){
            sql.append(SqlStringUtil.dealInList("shop_id",param.getShopIds(),argsList));
        }
        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            sql.append(SqlStringUtil.dealInList("keyword_id",keywordIds,argsList));
        }
        sql.append(" group by keyword_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(KeywordLibsDetailVo.class), argsList.toArray());
    }

    @Override
    public List<KeywordLibsVo> getSbCompKeywordReportData(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywords) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_id, any(keyword_text) keywordText, sum(cost * c.rate) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales from ods_t_amazon_ad_sb_keyword_report r ");
        sql.append("join (select * from dim_currency_rate where puid = ? ");
        argsList.add(puid);
        if (StringUtils.isNotBlank(param.getTo())){
            sql.append(" and `to` = ? ");
            argsList.add(param.getTo());
        }
        sql.append(" ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        if (StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getCompareStartDate());
            argsList.add(param.getCompareEndDate());
        }
        if (CollectionUtils.isNotEmpty(keywords)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : keywords) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(keyword_text)",lowerCaseList,argsList));
        }
        sql.append(" group by keyword_id");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(KeywordLibsVo.class), argsList.toArray());
    }

    /**
     * 用户排除为0 的字段处理
     * @param orderByField
     * @return
     */
    private String getColumn(String orderByField) {

        switch (orderByField) {
            case "totalSales":
                return "sales14d";
            case "orderNum":
                return "conversions14d";
            case "saleNum":
                return "units_sold14d";
            default:
                return orderByField ;
        }
    }

    @Override
    public List<RepeatTargetingCountVo> getSbKeywordReportData(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> sbKeywordIdList) {
        if (CollectionUtils.isEmpty(sbKeywordIdList)) {
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        RepeatTargetingTypeEnum filed = null;
        if (StringUtils.isNotBlank(vo.getTargetFields())) {
            filed = RepeatTargetingTypeEnum.fieldMap.get(vo.getTargetFields());
        }
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, match_type matchType ");
        if (filed != null) {
            sql.append(filed.getSbInSide());
        }
        sql.append(" from ods_t_amazon_ad_sb_keyword_report where puid = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(vo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(vo.getStartDate()) && StringUtils.isNotBlank(vo.getEndDate())) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(vo.getStartDate());
            argsList.add(vo.getEndDate());
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", vo.getSbAdGroupId(), argsList));
        }
        for (RepeatTargetingCountVo keyword : sbKeywordIdList) {
            String keywordText = keyword.getKeywordText();
            if (ThemeKeywordTextEnum.getTextByCode(keywordText) != null) {
                keyword.setKeywordText(ThemeKeywordTextEnum.getTextByCode(keywordText));
            }
        }
        sql.append(SqlStringUtil.dealMultiInList(Arrays.asList("keyword_text", "match_type"), sbKeywordIdList, argsList, Arrays.asList(j -> j.getKeywordText().toLowerCase(), i -> i.getMatchType().toUpperCase())));
        sql.append(" group by keyword_text, match_type ");
        List<RepeatTargetingCountVo> query = getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingCountVo.class), argsList.toArray());
        for (RepeatTargetingCountVo keyword : sbKeywordIdList) {
            String keywordText = keyword.getKeywordText();
            if (ThemeKeywordTextEnum.getCodeByText(keywordText) != null) {
                keyword.setKeywordText(ThemeKeywordTextEnum.getCodeByText(keywordText));
            }
        }
        return query;
    }

    @Override
    public List<RepeatTargetingCountVo> getSbCompKeywordReportData(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> sbKeywordIdList) {
        if (CollectionUtils.isEmpty(sbKeywordIdList)) {
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        RepeatTargetingTypeEnum filed = null;
        if (StringUtils.isNotBlank(vo.getTargetFields())) {
            filed = RepeatTargetingTypeEnum.fieldMap.get(vo.getTargetFields());
        }
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, match_type matchType ");
        if (filed != null) {
            sql.append(filed.getSbInSide());
        }
        sql.append(" from ods_t_amazon_ad_sb_keyword_report where puid = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(vo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(vo.getCompareStartDate()) && StringUtils.isNotBlank(vo.getCompareEndDate())) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(vo.getCompareStartDate());
            argsList.add(vo.getCompareEndDate());
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", vo.getSbAdGroupId(), argsList));
        }
        for (RepeatTargetingCountVo keyword : sbKeywordIdList) {
            String keywordText = keyword.getKeywordText();
            if (ThemeKeywordTextEnum.getTextByCode(keywordText) != null) {
                keyword.setKeywordText(ThemeKeywordTextEnum.getTextByCode(keywordText));
            }
        }
        sql.append(SqlStringUtil.dealMultiInList(Arrays.asList("keyword_text", "match_type"), sbKeywordIdList, argsList, Arrays.asList(j -> j.getKeywordText().toLowerCase(), i -> i.getMatchType().toUpperCase())));
        sql.append(" group by keyword_text, match_type ");
        for (RepeatTargetingCountVo keyword : sbKeywordIdList) {
            String keywordText = keyword.getKeywordText();
            if (ThemeKeywordTextEnum.getCodeByText(keywordText) != null) {
                keyword.setKeywordText(ThemeKeywordTextEnum.getCodeByText(keywordText));
            }
        }
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingCountVo.class), argsList.toArray());
    }

    @Override
    public Page<RepeatTargetingCountVo> getSbKeywordReportDataOrderByIndex(Integer puid, RepeatTargetingCountPageVo vo) {
        List<Object> argsList = new ArrayList<>();
        RepeatTargetingTypeEnum filed = null;
        if (StringUtils.isNotBlank(vo.getTargetFields())) {
            filed = RepeatTargetingTypeEnum.fieldMap.get(vo.getTargetFields());
        } else {
            filed = RepeatTargetingTypeEnum.fieldMap.get("adCost");
        }
        StringBuilder sqlCount = new StringBuilder("select count(*) from ( ");
        StringBuilder sql = new StringBuilder("select keywordText, matchType, ");
        sql.append(filed.getOrderSide());
        sql.append(" from (select k.keyword_text keywordText, k.match_type matchType ");
        sql.append(filed.getSbInSide());
        sql.append("from ods_t_amazon_ad_keyword_sb k left join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id and r.puid = ? and r.marketplace_id = ?");
        argsList.add(puid);
        argsList.add(vo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("r.shop_id", vo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(vo.getStartDate()) && StringUtils.isNotBlank(vo.getEndDate())) {
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(vo.getStartDate());
            argsList.add(vo.getEndDate());
        }
        sql.append(" where k.puid = ? and k.marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(vo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("k.shop_id", vo.getShopIdList(), argsList));
        }
        //词组数量筛选
        if (CollectionUtils.isNotEmpty(vo.getKeywordSize())) {
            sql.append(SqlStringUtil.dealInList("(k.keyword_size", vo.getKeywordSize(), argsList));
            if (vo.getKeywordSize().contains(5)) {
                sql.append(" or keyword_size > 5");
            }
            sql.append(")");
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("k.ad_group_id", vo.getSbAdGroupId(), argsList));
        }
        //匹配类型筛选
        if (CollectionUtils.isNotEmpty(vo.getMatchType())) {
            sql.append(SqlStringUtil.dealInList("k.match_type", vo.getMatchType(), argsList));
        }
        //关键词筛选
        //模糊与精准匹配查询
        if (StringUtils.isNotEmpty(vo.getSearchValue()) && StringUtils.isNotEmpty(vo.getSearchType())) {
            if ("exact".equalsIgnoreCase(vo.getSearchType())) {
                sql.append(" and lower(k.keyword_text) = ? ");
                argsList.add(vo.getSearchValue().trim().toLowerCase());
            } else {
                sql.append(" and lower(k.keyword_text) like ? ");
                argsList.add("%" + vo.getSearchValue().trim().toLowerCase() + "%");
            }
        }
        sql.append(" group by k.keyword_text, k.match_type ) s ");
        sql.append(" order by targetValue ");
        if (StringUtils.isNotBlank(vo.getOrderType())) {
            sql.append(vo.getOrderType());
        } else {
            sql.append("desc ");
        }
        sqlCount.append(sql + ") t");
        return getPageResultByClass(vo.getPageNo(), vo.getPageSize(), sqlCount.toString(), argsList.toArray(), sql.toString(), argsList.toArray(), RepeatTargetingCountVo.class);
    }

    @Override
    public List<RepeatTargetingCountVo> getSbCompKeywordReportDataOrderByIndex(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> sbKeywordIdList) {
        if (CollectionUtils.isEmpty(sbKeywordIdList)) {
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        RepeatTargetingTypeEnum filed = null;
        if (StringUtils.isNotBlank(vo.getTargetFields())) {
            filed = RepeatTargetingTypeEnum.fieldMap.get(vo.getTargetFields());
        }
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, match_type matchType ");
        if (filed != null) {
            sql.append(filed.getInSide());
        }
        sql.append(" from ods_t_amazon_ad_sb_keyword_report where puid = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(vo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(vo.getCompareStartDate()) && StringUtils.isNotBlank(vo.getCompareEndDate())) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(vo.getCompareStartDate());
            argsList.add(vo.getCompareEndDate());
        }
        sql.append(SqlStringUtil.dealMultiInList(Arrays.asList("keyword_text", "match_type"), sbKeywordIdList, argsList, Arrays.asList(RepeatTargetingCountVo::getKeywordText, i -> i.getMatchType().toUpperCase())));
        sql.append(" group by keyword_text,match_type ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingCountVo.class), argsList.toArray());
    }

    @Override
    public List<RepeatTargetingDetailVo> getSbReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_id keywordId, any(ad_group_id) adGroupId, sum(cost) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d) totalSales, ifnull(ROUND(ifnull(sum(cost), 0)/ ifnull(sum(sales14d), 0), 4), 0) acos, ");
        sql.append("ifnull(ROUND(ifnull(sum(sales14d), 0)/ ifnull(sum(cost), 0), 2), 0) roas, ifnull(ROUND(ifnull(sum(cost)/sum(clicks),0), 2), 0) cpc, ifnull(ROUND(ifnull(sum(cost)/sum(conversions14d),0), 2), 0) cpa, ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) salesConversionRate, ");
        sql.append("ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ifnull(sum(clicks), 0) clicks from ods_t_amazon_ad_sb_keyword_report ");
        sql.append(" where puid = ? and marketplace_id = ?");
        argsList.add(puid);
        argsList.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(detailPageVo.getStartDate()) && StringUtils.isNotBlank(detailPageVo.getEndDate())) {
            sql.append("and count_day >= ? and count_day <= ?");
            argsList.add(detailPageVo.getStartDate());
            argsList.add(detailPageVo.getEndDate());
        }
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
        }
        sql.append(" group by keyword_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingDetailVo.class), argsList.toArray());
    }

    @Override
    public List<RepeatTargetingDetailVo> getSbCompReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_id keywordId, any(ad_group_id) adGroupId, sum(cost) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d) totalSales, ifnull(ROUND(ifnull(sum(cost), 0)/ ifnull(sum(sales14d), 0), 4), 0) acos, ");
        sql.append("ifnull(ROUND(ifnull(sum(sales14d), 0)/ ifnull(sum(cost), 0), 2), 0) roas, ifnull(ROUND(ifnull(sum(cost) / sum(clicks), 0), 2), 0) cpc, ifnull(ROUND(ifnull(sum(cost) / sum(conversions14d), 0), 2), 0) cpa, ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) salesConversionRate, ");
        sql.append("ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ifnull(sum(clicks), 0) clicks from ods_t_amazon_ad_sb_keyword_report ");
        sql.append(" where puid = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(detailPageVo.getCompareStartDate()) && StringUtils.isNotBlank(detailPageVo.getCompareEndDate())) {
            sql.append("and count_day >= ? and count_day <= ?");
            argsList.add(detailPageVo.getCompareStartDate());
            argsList.add(detailPageVo.getCompareEndDate());
        }
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
        }
        sql.append(" group by keyword_id ");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingDetailVo.class), argsList.toArray());
    }

    @Override
    public List<RepeatTargetingTotalVo> getRepeatTargetingReportTotalData(Integer puid, RepeatTargetingDetailPageVo totalDataVo, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ifnull(sum(conversions14d), 0) saleNum, ifnull(sum(sales14d), 0) totalSales, ");
        sql.append("ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate, ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) salesConversionRate, ifnull(sum(cost)/sum(conversions14d),0) cpa, ");
        sql.append("ifnull(sum(cost)/sum(clicks),0) cpc, ifnull(ROUND(ifnull(sum(sales14d), 0)/ ifnull(sum(cost), 0), 4), 0) roas, ifnull(ROUND(ifnull(sum(cost), 0)/ ifnull(sum(sales14d), 0), 4), 0) acos, ifnull(sum(cost), 0) cost ");
        sql.append(" from ods_t_amazon_ad_sb_keyword_report where puid = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(totalDataVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(totalDataVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", totalDataVo.getShopIdList(), argsList));
        }
        if (StringUtils.isNotBlank(totalDataVo.getStartDate()) && StringUtils.isNotBlank(totalDataVo.getEndDate())) {
            sql.append("  and count_day >= ? and count_day <= ? ");
            argsList.add(totalDataVo.getStartDate());
            argsList.add(totalDataVo.getEndDate());
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(totalDataVo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", totalDataVo.getSbAdGroupId(), argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
        }
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(RepeatTargetingTotalVo.class), argsList.toArray());
    }

    @Override
    public Page<RepeatTargetingDetailVo> getIndexReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        RepeatTargetingTypeEnum field = null;
        if (StringUtils.isNotBlank(detailPageVo.getOrderField())) {
            field = RepeatTargetingTypeEnum.fieldMap.get(detailPageVo.getOrderField());
        }
        StringBuilder sqlCount = new StringBuilder("select count(*) from ( ");
        StringBuilder sql = new StringBuilder(" select keyword_id keywordId, type ");
        if (field != null) {
            sql.append(field.getOutSide());
        }
        sql.append(" from (select k.keyword_id, 'sb' as type ");
        if (StringUtils.isNotBlank(detailPageVo.getOrderField())) {
            sql.append(field.getSbInSide());
        }
        sql.append(" from ods_t_amazon_ad_keyword_sb k left join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id and r.puid = ? and r.marketplace_id = ?");
        argsList.add(puid);
        argsList.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("r.shop_id", detailPageVo.getShopIdList(), argsList));
        }
        sql.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(detailPageVo.getStartDate());
        argsList.add(detailPageVo.getEndDate());
        sql.append(" and lower(r.match_type) = ? and r.keyword_text = ?");
        argsList.add(detailPageVo.getDetailMatchType().trim().toLowerCase());
        String keywordText = detailPageVo.getKeywordText();
        if (ThemeKeywordTextEnum.getTextByCode(keywordText) != null) {
            keywordText = ThemeKeywordTextEnum.getTextByCode(keywordText);
        }
        argsList.add(keywordText);
        sql.append(" where k.puid = ? and lower(k.match_type) = ? ");
        argsList.add(puid);
        argsList.add(detailPageVo.getDetailMatchType().trim().toLowerCase());
        sql.append(" and k.keyword_text = ?");
        argsList.add(detailPageVo.getKeywordText());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("k.shop_id", detailPageVo.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("k.keyword_id", keywordIds, argsList));
        }
        sql.append(" group by k.keyword_id ) s");
        if (field != null && StringUtils.isNotBlank(detailPageVo.getOrderType())) {
            sql.append(" order by ");
            sql.append(field.getOrderSide());
            sql.append(detailPageVo.getOrderType());
        } else {
            sql.append(" order by keywordId desc ");
        }
        sqlCount.append(sql + " ) t");
        return getPageResultByClass(detailPageVo.getPageNo(), detailPageVo.getPageSize(), sqlCount.toString(), argsList.toArray(), sql.toString(), argsList.toArray(), RepeatTargetingDetailVo.class);
    }

    @Override
    public List<OdsAmazonAdSbKeywordReport> getReportByKeywordIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startDate, String endDate,
                                                                               List<String> keywordIds, boolean changeRate, String currency) {
        List<Object> args = new ArrayList<>();
        //子查询sql
        StringBuilder sql = new StringBuilder("SELECT count_day,")
                .append("ifnull(sum(`clicks`), 0) clicks, ifnull(sum(`impressions`), 0) impressions, ")
                .append("ifnull(sum(`conversions14d`), 0) conversions14d, ifnull(sum(`conversions14d_same_sku`), 0) conversions14d_same_sku,")
                .append("ifnull(sum(`units_sold14d`), 0) units_sold14d, ");
        if (changeRate) {
            sql.append(" ifnull(sum(`cost` * d.rate), 0) cost, ")
                    .append(" ifnull(sum(`sales14d` * d.rate), 0) sales14d, ")
                    .append(" ifnull(sum(`sales14d_same_sku` * d.rate), 0) sales14d_same_sku ");
        } else {
            sql.append(" ifnull(sum(`cost`), 0) cost, ")
                    .append(" ifnull(sum(`sales14d`), 0) sales14d, ")
                    .append(" ifnull(sum(`sales14d_same_sku`), 0) sales14d_same_sku ");
        }
        sql.append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        if (changeRate) {
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            //关联币种表、汇率表
            sql.append(" join ");
            sql.append(" ( select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? and c.month >= ? and c.month <= ? ");
            args.add(puid);
            args.add(currency);
            args.add(start);
            args.add(end);
            sql.append(SqlStringUtil.dealInList("m.marketplace_id", marketplaceIds, args));
            sql.append(" ) d ");
            sql.append(" on d.marketplace_id = r.marketplace_id and d.month = r.count_month ");
        }
        sql.append(" where`puid`=? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIds, args));
        sql.append(" and `count_day`>=? and count_day<=? ");
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, args));
        sql.append(" group by count_day ");
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(OdsAmazonAdSbKeywordReport.class), args.toArray());

    }
}

