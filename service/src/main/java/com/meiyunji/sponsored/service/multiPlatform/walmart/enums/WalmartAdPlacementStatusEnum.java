package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;


@Getter
public enum WalmartAdPlacementStatusEnum {
    EXCLUDED("excluded", "开启"),
    INCLUDED("included", "暂停"),
    ;
    private String code;
    private String msg;

    WalmartAdPlacementStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static WalmartAdPlacementStatusEnum getWalmartAdPlacementStatusEnumByCode (String code) {
        for (WalmartAdPlacementStatusEnum en : WalmartAdPlacementStatusEnum.values()) {
            if (en.getCode().equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
