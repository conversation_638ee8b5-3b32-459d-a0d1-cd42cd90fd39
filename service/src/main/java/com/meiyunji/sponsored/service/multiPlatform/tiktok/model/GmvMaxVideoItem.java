package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class GmvMaxVideoItem {
    //id = itemId + "#" + 传入的spuId
    private String id;
    private String itemId;
    private String text;
    private List<String> spuIdList;
    private Identity identityInfo;
    private GmvMaxVideo videoInfo;

    public static GmvMaxVideoItem fromTkGmvMaxVideoItem(com.tiktok.advertising.model.gmv_max.VideoItem item) {
        GmvMaxVideoItem result = new GmvMaxVideoItem();
        result.setItemId(item.getItemId());
        result.setText(item.getText());
        result.setSpuIdList(item.getSpuIdList());
        if (item.getIdentityInfo() != null) {
            result.setIdentityInfo(Identity.fromTkIdentity(item.getIdentityInfo()));
        }
        if (item.getVideoInfo() != null) {
            result.setVideoInfo(GmvMaxVideo.fromTkGmvMaxVideo(item.getVideoInfo()));
        }
        return result;
    }

    public static com.tiktok.advertising.model.gmv_max.VideoItem toTkGmvMaxVideoItem(GmvMaxVideoItem item) {
        com.tiktok.advertising.model.gmv_max.VideoItem result = new com.tiktok.advertising.model.gmv_max.VideoItem();
        result.setItemId(item.getItemId());
//        result.setText(item.getText());
        result.setSpuIdList(item.getSpuIdList());
        if (item.getIdentityInfo() != null) {
            result.setIdentityInfo(Identity.toTkIdentity(item.getIdentityInfo()));
        }
        if (item.getVideoInfo() != null) {
            result.setVideoInfo(GmvMaxVideo.toTkGmvMaxVideo(item.getVideoInfo()));
        }
        return result;
    }

    public String buildGmvMaxVideoItemIdentifyId() {
        return this.itemId + "#" + String.join(",", spuIdList);
    }

}
