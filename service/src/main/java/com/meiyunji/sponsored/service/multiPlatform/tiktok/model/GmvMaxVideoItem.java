package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class GmvMaxVideoItem {
    //前端需要唯一id做选中绑定
    //自定义作品的唯一id是 作品id（itemId） + 选中的单个商品id（spuId） +  达人id（identityId） +  达人类型（identityType）
    //已授权作品的唯一id是 作品id（itemId） + 接口返回的spuidList（spuIdList）  +  达人id（identityId） +  达人类型（identityType）
    private String id;
    private String itemId;
    private String text;
    private List<String> spuIdList;
    private Identity identityInfo;
    private GmvMaxVideo videoInfo;
    //请求的spuId列表
    private List<String> requestSpuIdList;

    public static GmvMaxVideoItem fromTkGmvMaxVideoItem(com.tiktok.advertising.model.gmv_max.VideoItem item) {
        GmvMaxVideoItem result = new GmvMaxVideoItem();
        result.setItemId(item.getItemId());
        result.setText(item.getText());
        result.setSpuIdList(item.getSpuIdList());
        if (item.getIdentityInfo() != null) {
            result.setIdentityInfo(Identity.fromTkIdentity(item.getIdentityInfo()));
        }
        if (item.getVideoInfo() != null) {
            result.setVideoInfo(GmvMaxVideo.fromTkGmvMaxVideo(item.getVideoInfo()));
        }
        return result;
    }

    public static com.tiktok.advertising.model.gmv_max.VideoItem toTkGmvMaxVideoItem(GmvMaxVideoItem item) {
        com.tiktok.advertising.model.gmv_max.VideoItem result = new com.tiktok.advertising.model.gmv_max.VideoItem();
        result.setItemId(item.getItemId());
//        result.setText(item.getText());
        result.setSpuIdList(item.getSpuIdList());
        if (item.getIdentityInfo() != null) {
            result.setIdentityInfo(Identity.toTkIdentity(item.getIdentityInfo()));
        }
        if (item.getVideoInfo() != null) {
            result.setVideoInfo(GmvMaxVideo.toTkGmvMaxVideo(item.getVideoInfo()));
        }
        return result;
    }

    public String buildGmvMaxVideoItemIdentifyId() {
        String id = this.itemId + "#" + String.join(",", this.spuIdList);
        if (this.getIdentityInfo() != null) {
            if (this.getIdentityInfo().getIdentityId() != null) {
                id = id + "#" + this.getIdentityInfo().getIdentityId();
            }
            if (this.getIdentityInfo().getIdentityType() != null) {
                id = id + "#" + Identity.IdentityTypeEnum.getTypeByDesc(this.getIdentityInfo().getIdentityType());
            }
        }
        return id;
    }

    public String buildGmvMaxVideoItemIdentifyId(List<String> spuIdList) {
        String id = this.itemId + "#" + String.join(",", spuIdList);
        if (this.getIdentityInfo() != null) {
            if (this.getIdentityInfo().getIdentityId() != null) {
                id = id + "#" + this.getIdentityInfo().getIdentityId();
            }
            if (this.getIdentityInfo().getIdentityType() != null) {
                id = id + "#" + Identity.IdentityTypeEnum.getTypeByDesc(this.getIdentityInfo().getIdentityType());
            }
        }
        return id;
    }

}
