package com.meiyunji.sponsored.service.product.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.dto.ProductStatusDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.SPPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SPPageVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.post.response.AsinPageResponse;
import com.meiyunji.sponsored.service.post.service.Impl.PostServiceImpl;
import com.meiyunji.sponsored.service.product.service.IProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ProductServiceImpl implements IProductService {

    @Autowired
    private IAdProductRightService productRightService;

    @Autowired
    private PostServiceImpl postService;

    @Autowired
    private IOdsProductDao odsProductDao;

    private Ods



    public Page<SPPageVo> getPageListWithProductRight(int puid, SPPageParam param,boolean isVc) {
        log.info("getScAdvertisablePageList begin ,param:{}",param);
        boolean isNeedCheck = productRightService.checkUidNeedProductRight(puid, Lists.newArrayList(param.getShopId()));
        Page<AsinPageResponse.AsinInfo> productRightPageList = Lists.newArrayList();
        if (isVc) {

        }else {
            productRightPageList = odsProductDao.getProductRightPageList(puid, param, isNeedCheck);
        }
        Page<SPPageVo> voPage = new Page<>();
        if (productRightPageList != null && CollectionUtils.isNotEmpty(productRightPageList.getRows())) {
            List<AsinPageResponse.AsinInfo> rows = productRightPageList.getRows();
            Map<String, ProductStatusDto> productEligibilityStatusMap = postService.getProductEligibilityStatusMap(puid, param.getShopId(), param.getType(), rows);

            String domain = AmznEndpoint.getByMarketplaceId(rows.get(0).getMarketplaceId()).getDomain();

            List<SPPageVo> voList = Lists.newArrayList();
            voPage.setRows(voList);
            voPage.setPageNo(param.getPageNo());
            voPage.setTotalPage(productRightPageList.getTotalPage());
            voPage.setPageSize(productRightPageList.getPageSize());
            voPage.setTotalSize(productRightPageList.getTotalSize());

            Set<Long> parentIdList = rows.stream().map(AsinPageResponse.AsinInfo::getParentId).collect(Collectors.toSet());
            List<OdsProduct> sameParentIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(parentIdList)) {
                sameParentIdList = odsProductDao.getListByParentId(puid, param.getShopId(), Constants.US, Lists.newArrayList(parentIdList));
            }

            Map<Long, List<OdsProduct>> sameParentMap = sameParentIdList.stream()
                    .collect(Collectors.groupingBy(OdsProduct::getParentId));
            for (AsinPageResponse.AsinInfo product : rows) {
                String key = postService.getKey(product.getAsin(), product.getMsku());
                SPPageVo vo = new SPPageVo();
                convertPoToSPPageVo(product, vo, sameParentMap.get(product.getParentId()));
                //产品资格状态
                vo.setIsMeetConditions(!productEligibilityStatusMap.containsKey(key));
                vo.setDomain(domain);
                voList.add(vo);
            }
        }
        return voPage;
    }


    @Override
    public Page<SPPageVo> getScAdvertisablePageList(int puid, SPPageParam param) {
        log.info("getScAdvertisablePageList begin ,param:{}",param);
        boolean isNeedCheck = productRightService.checkUidNeedProductRight(puid, Lists.newArrayList(param.getShopId()));
        Page<AsinPageResponse.AsinInfo> productRightPageList = odsProductDao.getProductRightPageList(puid, param, isNeedCheck);
        Page<SPPageVo> voPage = new Page<>();
        if (productRightPageList != null && CollectionUtils.isNotEmpty(productRightPageList.getRows())) {
            List<AsinPageResponse.AsinInfo> rows = productRightPageList.getRows();
            Map<String, ProductStatusDto> productEligibilityStatusMap = postService.getProductEligibilityStatusMap(puid, param.getShopId(), param.getType(), rows);

            String domain = AmznEndpoint.getByMarketplaceId(rows.get(0).getMarketplaceId()).getDomain();

            List<SPPageVo> voList = Lists.newArrayList();
            voPage.setRows(voList);
            voPage.setPageNo(param.getPageNo());
            voPage.setTotalPage(productRightPageList.getTotalPage());
            voPage.setPageSize(productRightPageList.getPageSize());
            voPage.setTotalSize(productRightPageList.getTotalSize());

            Set<Long> parentIdList = rows.stream().map(AsinPageResponse.AsinInfo::getParentId).collect(Collectors.toSet());
            List<OdsProduct> sameParentIdList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(parentIdList)) {
                sameParentIdList = odsProductDao.getListByParentId(puid, param.getShopId(), Constants.US, Lists.newArrayList(parentIdList));
            }

            Map<Long, List<OdsProduct>> sameParentMap = sameParentIdList.stream()
                    .collect(Collectors.groupingBy(OdsProduct::getParentId));
            for (AsinPageResponse.AsinInfo product : rows) {
                String key = postService.getKey(product.getAsin(), product.getMsku());
                SPPageVo vo = new SPPageVo();
                convertPoToSPPageVo(product, vo, sameParentMap.get(product.getParentId()));
                //产品资格状态
                vo.setIsMeetConditions(!productEligibilityStatusMap.containsKey(key));
                vo.setDomain(domain);
                voList.add(vo);
            }
        }
        return voPage;
    }

        private void convertPoToSPPageVo(AsinPageResponse.AsinInfo  product, SPPageVo vo, List<OdsProduct> sameParentList) {
            if (product.getIsVariation() == 2) {
                vo.setId(Integer.parseInt(String.valueOf(product.getId())));
                vo.setShopId(product.getShopId());
                vo.setSku(product.getMsku());
                vo.setAsin(product.getAsin());
                vo.setOnlineStatus(product.getOnlineStatus());
                vo.setTitle(product.getTitle());
                vo.setImgUrl(product.getImgUrl());
                //设置父asin
                if (StringUtils.isNotBlank(product.getParentAsin())) {
                    vo.setBatchCreateParentAsin(product.getParentAsin());
                } else {
                    vo.setBatchCreateParentAsin(product.getAsin());
                }
                if (CollectionUtils.isNotEmpty(sameParentList)) {
                    // 过滤掉删除的
                    sameParentList = sameParentList.stream().filter(e -> e.getDxmPublishState() == null || !e.getDxmPublishState().equals("delete")).collect(Collectors.toList());
                    if (sameParentList.size() > 1) {
                        List<SPPageVo> childList = new ArrayList<>(sameParentList.size());
                        vo.setChildList(childList);
                        SPPageVo childVo;
                        for (OdsProduct p : sameParentList) {
                            //原来的逻辑是排除父msku:p.getId()不等于product.getId().
                            //改版要包含本身数据,前端需要.
                            childVo = new SPPageVo();
                            childList.add(childVo);
                            childVo.setId(Integer.parseInt(String.valueOf(product.getId())));
                            childVo.setShopId(p.getShopId());
                            childVo.setSku(p.getSku());
                            childVo.setAsin(p.getAsin());
                            childVo.setOnlineStatus(p.getOnlineStatus());
                            childVo.setTitle(p.getTitle());
                            childVo.setImgUrl(p.getMainImage());
                            childVo.setDomain(vo.getDomain());
                            //设置父asin
                            if (StringUtils.isNotBlank(p.getParentAsin())) {
                                childVo.setBatchCreateParentAsin(p.getParentAsin());
                            } else {
                                childVo.setBatchCreateParentAsin(p.getAsin());
                            }
                        }
                    }
                }
            } else {
                vo.setId(Integer.parseInt(String.valueOf(product.getId())));
                vo.setShopId(product.getShopId());
                vo.setSku(product.getMsku());
                vo.setAsin(product.getAsin());
                vo.setOnlineStatus(product.getOnlineStatus());
                vo.setTitle(product.getTitle());
                vo.setImgUrl(product.getImgUrl());
                //单体无父asin取自己
                vo.setBatchCreateParentAsin(product.getAsin());
            }
        }
}
