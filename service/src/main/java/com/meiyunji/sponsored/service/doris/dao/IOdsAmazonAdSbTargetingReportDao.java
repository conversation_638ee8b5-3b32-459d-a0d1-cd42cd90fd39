package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;

import java.util.List;

/**
 * sb商品投放报告(OdsAmazonAdSbTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
public interface IOdsAmazonAdSbTargetingReportDao extends IDorisBaseDao<OdsAmazonAdSbTargetingReport> {

    List<DashboardAdTargetingMatrixTopDto> getTargetingTopList(Integer puid, List<String> marketplaceIdList,
                                                             List<Integer> shopIdList, String currency,
                                                             String startDate, String endDate,
                                                             DashboardDataFieldEnum dataField, List<String> targetIdList,
                                                             Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                               List<String> campaignIds, Boolean noZero);

    List<DashboardAdTargetingMatrixTopDto> getTargetingTopInfoList(Integer puid, List<String> marketplaceIdList,
                                                               List<Integer> shopIdList, String currency,
                                                               String startDate, String endDate,
                                                               DashboardDataFieldEnum dataField, List<String> targetIdList,
                                                               DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                   List<String> campaignIds, Boolean noZero);

    /**
     * 商品投放高级筛选个数
     */
    int getTargetAllCount(Integer puid, TargetingPageParam param);

    /**
     * 商品投放列表页
     */
    Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param);

    /**
     * 根据投放id查询报告数据
     */
    List<TargetPageVo> getReportListByTargetIds(Integer puid, Integer shopId, List<String> targetIdList, String startStr, String endStr);

    /**
     * 商品投放列表页占比数据
     */
    AdMetricDto getTargetPageSumMetricData(Integer puid, TargetingPageParam param);

    /**
     * 商品投放汇总查询所有keywordId
     */
    List<String> getTargetIdListByPage(Integer puid, TargetingPageParam param);

    /**
     * 商品投放汇总根据keywordId查询按天维度数据
     */
    List<AdHomePerformancedto> getReportAggregateByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupByDate);

    AdHomePerformancedto getReportAggregateCompareData(Integer puid, TargetingPageParam param, String startStr, String endStr);
    /**
     * 根据投放Id查询对应的报告数据
     * @param puid
     * @param param
     * @param sbTargetIds
     * @return
     */
    List<AsinLibsDetailVo> getSbAsinReportDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> sbTargetIds);

    List<AdAsinOrderBo> getOrderFieldByAsins(Integer puid, List<String> shopIds, PageListAsinsRequest param, List<String> asinList);

    List<OdsAmazonAdSbTargetingReport> getReportByTargetIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startDate, String endDate,
                                                                       List<String> targetIds, boolean changeRate, String currency);

    List<AsinLibsDetailVo> getAsinReportByAsins(Integer puid, PageListAsinsRequest param,
                                               String startDate, String endDate,
                                               List<String> asinList, String currency);

}


