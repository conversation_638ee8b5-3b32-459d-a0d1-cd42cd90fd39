package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordExtendDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordExtend;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetExtendInfo;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * AmazonAdKeywordReport
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdKeywordExtendDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdKeywordExtend> implements IAmazonAdKeywordExtendDao {


    @Override
    public void insertList(Integer puid, List<AmazonAdKeywordExtend> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,`ad_group_id`,")
                .append("`keyword_id`,`estimated_impression_upper`,`estimated_impression_lower`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdKeywordExtend keywordExtend : list) {
            sql.append(" (?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(keywordExtend.getShopId());
            argsList.add(keywordExtend.getMarketplaceId());
            argsList.add(keywordExtend.getCampaignId());
            argsList.add(keywordExtend.getAdGroupId());
            argsList.add(keywordExtend.getKeywordId());
            argsList.add(keywordExtend.getEstimatedImpressionUpper());
            argsList.add(keywordExtend.getEstimatedImpressionLower());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `estimated_impression_upper`=values(estimated_impression_upper),`estimated_impression_lower`=values(estimated_impression_lower)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdKeywordExtend> selectByShopIdAndKeywordIdList(Integer puid, Integer shopId, List<String> keywordIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT * from " + getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        selectSql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getRowMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<TargetExtendInfo> selectByShopIdAndKeywordIdList(Integer puid, List<Integer> shopIdList, List<String> keywordIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT keyword_id targetId, estimated_impression_upper estimatedImpressionUpper, estimated_impression_lower estimatedImpressionLower from " + getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        selectSql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetExtendInfo.class));
        } finally {
            hintManager.close();
        }
    }
}