package com.meiyunji.sponsored.service.multiple.targets.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormat;
import com.meiyunji.sponsored.service.excel.excelTools.ExportFormatType;
import lombok.Data;

/**
 * 多店铺投放层级导出dto
 */
@Data
public class TargetExportDto {

    @ExcelProperty(value = "店铺名称")
    private String shopName;

    @ExcelProperty(value = "关键词")
    private String keywordText;

    @ExcelProperty(value = "定位")
    private String target;

    @ExcelProperty(value = "有效状态")
    private String state;

    @ExcelProperty(value = "广告活动状态")
    private String servingStatusName;

    @ExcelProperty(value = "匹配类型")
    private String matchType;

    @ExcelProperty(value = "筛选条件")
    private String selectType;

    @ExcelProperty(value = "推广类型")
    private String type;

    @ExcelProperty(value = "投放类型")
    private String campaignTargetingType;

    @ExcelProperty(value = "所属广告组")
    private String advertisingGroup;

    @ExcelProperty(value = "所属广告活动")
    private String advertisingActivities;

    @ExcelProperty(value = "广告组合")
    private String portfolioName;

    @ExcelProperty(value = "建议竞价")
    private String suggestBid;

    @ExcelProperty(value = "建议竞价范围")
    private String suggestBidScope;

    @ExportFormat(value = ExportFormatType.CURRENCY)
    @ExcelProperty(value = "竞价")
    private String bid;

    @ExcelProperty(value = "广告花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCost;

    @ExcelProperty(value = "广告花费占比")
    private String adCostPercentage;

    @ExcelProperty(value = "曝光量")
    private Long impressions;

    @ExcelProperty(value = "搜索结果首页首位IS")
    private String topImpressionShare;

    @ExcelProperty(value = "可见展示次数")
    private Long viewImpressions;

    @ExcelProperty(value = "点击量")
    private Long clicks;

    @ExcelProperty(value = "每笔订单花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String cpa;

    @ExcelProperty(value = "平均点击费用")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adCostPerClick;

    @ExcelProperty(value = "每千次展现费用")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String vcpm;

    @ExcelProperty(value = "点击率")
    private String ctr;

    @ExcelProperty(value = "广告转化率")
    private String cvr;

    @ExcelProperty(value = "ACoS")
    private String acos;

    @ExcelProperty(value = "RoAS")
    private String roas;

    @ExcelProperty(value = "ACoTS")
    private String acots;

    @ExcelProperty(value = "ASoTS")
    private String asots;

    @ExcelProperty("广告笔单价")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String advertisingUnitPrice;

    @ExcelProperty(value = "广告订单量")
    private Long adOrderNum;

    @ExcelProperty(value = "广告订单量占比")
    private String adOrderNumPercentage;

    @ExcelProperty(value = "本广告产品订单量")
    private Long adSaleNum;

    @ExcelProperty(value = "其他产品广告订单量")
    private Long adOtherOrderNum;

    @ExcelProperty(value = "广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSale;

    @ExcelProperty(value = "广告销售额占比")
    private String adSalePercentage;

    @ExcelProperty(value = "本广告产品销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adSales;

    @ExcelProperty(value = "其他产品广告销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String adOtherSales;

    @ExcelProperty(value = "广告销量")
    private Long orderNum;

    @ExcelProperty(value = "广告销量占比")
    private String orderNumPercentage;

    @ExcelProperty(value = "本广告产品销量")
    private Long adSelfSaleNum;

    @ExcelProperty(value = "其他产品广告销量")
    private Long adOtherSaleNum;

    @ExcelProperty(value = "“品牌新买家”订单量")
    private Long ordersNewToBrandFTD;

    @ExcelProperty(value = "“品牌新买家”订单百分比")
    private String orderRateNewToBrandFTD;

    @ExcelProperty(value = "“品牌新买家”销售额")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String salesNewToBrandFTD;

    @ExcelProperty(value = "“品牌新买家”销售额百分比")
    private String salesRateNewToBrandFTD;

    @ExcelProperty(value = "“品牌新买家”订单转化率")
    private String ordersNewToBrandPercentageFTD;

    @ExcelProperty("“品牌新买家”观看量")
    private String newToBrandDetailPageViews;

    @ExcelProperty("加购次数")
    private String addToCart;

    @ExcelProperty("加购率")
    private String addToCartRate;

    @ExcelProperty("单次加购花费")
    @ExportFormat(value = ExportFormatType.CURRENCY)
    private String ecpAddToCart;

    @ExcelProperty("5秒观看次数")
    private String video5SecondViews;

    @ExcelProperty("5秒观看率")
    private String video5SecondViewRate;

    @ExcelProperty("视频播至1/4次数")
    private String videoFirstQuartileViews;

    @ExcelProperty("视频播至1/2次数")
    private String videoMidpointViews;

    @ExcelProperty("视频播至3/4次数")
    private String videoThirdQuartileViews;

    @ExcelProperty("视频完整播放次数")
    private String videoCompleteViews;

    @ExcelProperty("视频取消静音")
    private String videoUnmutes;

    @ExcelProperty("观看率")
    private String viewabilityRate;

    @ExcelProperty("观看点击率")
    private String viewClickThroughRate;

    @ExcelProperty("品牌搜索次数")
    private String brandedSearches;

    @ExcelProperty("DPV")
    private String detailPageViews;

    @ExcelProperty("标签")
    private String adTag;

    @ExcelProperty("ABA搜索词排名")
    private String searchFrequencyRank;

    @ExcelProperty("排名周变化率")
    private String weekRatio;

    @ExcelProperty(value = "广告策略标签")
    private String adStrategyTag;

    @ExcelProperty(value = "关键词翻译")
    private String keywordTextCn;
}
