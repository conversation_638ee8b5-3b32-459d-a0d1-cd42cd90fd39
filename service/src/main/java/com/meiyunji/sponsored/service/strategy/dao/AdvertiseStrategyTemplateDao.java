package com.meiyunji.sponsored.service.strategy.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.vo.AdvertiseStrategyTemplateRequest;
import io.swagger.models.auth.In;
import org.springframework.stereotype.Repository;

import java.util.List;

public interface AdvertiseStrategyTemplateDao extends IBaseShardingDao<AdvertiseStrategyTemplate> {

    AdvertiseStrategyTemplate selectByPrimaryKey(int puid, Long id);

    int updateByPrimaryKey(int puid, AdvertiseStrategyTemplate record);

    int batchInsert(int puid, List<AdvertiseStrategyTemplate> list);

    /**
     * 分页查询
     *
     * @param puid
     * @param param
     * @return
     */
    Page<AdvertiseStrategyTemplate> pageList(int puid, AdvertiseStrategyTemplateRequest param);

    /**
     * 插入
     * @param puid
     * @param template
     */
    Integer insertTemplate(Integer puid, AdvertiseStrategyTemplate template);

    /**
     * 更新
     * @param puid
     * @param template
     */
    Integer updateTemplate(Integer puid, AdvertiseStrategyTemplate template);

    /**
     * 查询模板名称是否有重复
     * @param puid
     * @param name
     * @return
     */
    boolean existByName(Integer puid, String name,String itemType);

    /**
     * 策略表新增或修改时更新模板表的应用数量和执行预览字段
     *
     * @param puid
     * @param usageAmount
     * @param id
     * @param executePreview
     * @return
     */
    void updateExecutePreviewOrNumber(Integer puid, Integer usageAmount,Integer executePreview,Long id);

    /**
     * 模板列表查询
     *
     * @param puid
     * @param templateName
     * @return
     */
    List<AdvertiseStrategyTemplate> getList(Integer puid,String templateName,String itemType);

    /**
     * 删除模板
     *
     * @param puid
     * @param id
     * @return
     */
    void deleteTemplateId(Integer puid,Long id);

    /**
     * 免费用户模板数量统计
     *
     * @param puid
     * @param itemType
     * @return
     */
    boolean verifyTemplateCount(Integer puid, String itemType);

    /**
     * 模板列表查询
     *
     * @param puid
     * @param templateName
     * @return
     */
    List<AdvertiseStrategyTemplate> getListByTemplateId(Integer puid, List<Long> templateIdList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);


    void updateStrategyTemplateStatus(int puid, List<Long> templateIdList, String status, Long updateUid);

    List<AdvertiseStrategyTemplate> getEnabledTemplate(Integer puid, Integer shopId);

    AdvertiseStrategyTemplate getTemplateByTaskId(Integer puid, Integer shopId, Long taskId);
}