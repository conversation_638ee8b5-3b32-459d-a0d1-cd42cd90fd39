package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.api.client.util.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.ams.api.service.AdReportHourlyRequestPb;
import com.meiyunji.sellfox.ams.api.service.AdReportHourlyResponsePb;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.grpc.common.AdHourReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAdHourlyReportAnalysisService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdReportHourlyVO;
import com.meiyunji.sponsored.service.util.HourlyAndWeeklyDataHandler;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.util.ReflectionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/12/22 9:44
 * @describe:
 */

@Service
public class AdHourlyReportAnalysisServiceImpl implements IAdHourlyReportAnalysisService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcShopDataService cpcShopDataService;

    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;

    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;

    @Autowired
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;

    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;

    @Deprecated
    @Override
    public AdHourReportResponsePb.AdHourReportResponse.AdHour getAdHourlyReport(AdHourReportRequest param,
                                                                                Function<AdReportHourlyRequestPb.AdReportHourlyRequest, AdReportHourlyResponsePb.AdReportHourlyResponse> hourlyFunc, Supplier<List<AdReportHourlyVO>> reportListSup, String type, String costType) {
        AdPageBasicData pageBasic = param.getPageBasic();
        Integer puid = Optional.of(pageBasic.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElseGet(null);
        ReportDateModelPb.ReportDateModel dateModel = Optional.of(pageBasic.getDateModel()).orElse(ReportDateModelPb.ReportDateModel.HOURLY);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (Objects.isNull(shopAuth)) {
            return null;
        }

        List<AdReportHourlyVO> list = Lists.newArrayList();
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), pageBasic.getStartDate().replace("-", ""), pageBasic.getEndDate().replace("-", ""));

        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getHourReportList(shopAuth, param, hourlyFunc, type, costType);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getDayReportList(reportListSup.get());
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getWeekReportList(param, reportListSup.get());
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getMonthReportList(reportListSup.get());
        }

        AdHourReportResponsePb.AdHourReportResponse.AdHour.Builder hourlyBuilder = AdHourReportResponsePb
                .AdHourReportResponse.AdHour.newBuilder();
        // 字段排序
        if (CollectionUtils.isNotEmpty(list)) {
            for (AdReportHourlyVO vo : list) {
                if (Constants.SB.equalsIgnoreCase(param.getPageBasic().getType()) || Constants.SD.equalsIgnoreCase(param.getPageBasic().getType())) {
                    vo.setAdOtherSaleNum(0);
                    vo.setAdOtherSaleNumCompare(0);
                    vo.setAdOtherSaleNumCompareRate(BigDecimal.ZERO);
                }
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }
            boolean isSorted = StringUtils.isNotBlank(pageBasic.getOrderField()) &&
                    Constants.isADOrderField(pageBasic.getOrderField(), AdReportHourlyVO.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, pageBasic.getOrderField(), pageBasic.getOrderType());
            }
        }
        hourlyBuilder.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> PbUtil.toAdReportHourlyPb(key, shopSalesByDate)).collect(Collectors.toList()));
        hourlyBuilder.setSummary(PbUtil.toAdReportHourlyPb(summary(list, shopSalesByDate), shopSalesByDate));
        hourlyBuilder.addAllChart(ReportChartUtil.getAdtHourChartData(list, false));
        //对比数据,chart图数据
        if (pageBasic.getIsCompare().getValue() == 1) {
            List<AdPlacementHourVo> placementHourVos = list.stream().map(item -> {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(item.getLabel());
                vo.setAdCost(item.getAdCostCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setClicks(item.getClicksCompare());
                vo.setCpa(item.getCpaCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcots(item.getAcotsCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setSelfAdOrderNum(item.getSelfAdOrderNumCompare());
                vo.setOtherAdOrderNum(item.getOtherAdOrderNumCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdSelfSale(item.getAdSelfSaleCompare());
                vo.setAdOtherSale(item.getAdOtherSaleCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdSelfSaleNum(item.getAdSelfSaleNumCompare());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNumCompare());
                return vo;
            }).collect(Collectors.toList());
            hourlyBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(placementHourVos, true));
        }
        return hourlyBuilder.build();
    }

    @Override
    public AdHourReportResponsePb.AdHourReportResponse.AdHour getAdHourlyReportFromDoris(AdHourReportRequest param, Function<CampaignHourlyReportSelectDto, List<AmazonMarketingStreamData>> hourlyFunc, Supplier<List<AdReportHourlyVO>> reportListSup, Supplier<List<AdReportHourlyVO>> reportListCompareSup, String type, String costType) {
        AdPageBasicData pageBasic = param.getPageBasic();
        Integer puid = Optional.of(pageBasic.getPuid()).map(Int32Value::getValue).orElse(null);
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElseGet(null);
        ReportDateModelPb.ReportDateModel dateModel = Optional.of(pageBasic.getDateModel()).orElse(ReportDateModelPb.ReportDateModel.HOURLY);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (Objects.isNull(shopAuth)) {
            return null;
        }

        List<AdReportHourlyVO> list = Lists.newArrayList();
        List<AdReportHourlyVO> compares = new ArrayList<>();
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), pageBasic.getStartDate().replace("-", ""), pageBasic.getEndDate().replace("-", ""));
        Int32Value isCompare = param.getPageBasic().getIsCompare();
        boolean booleanCompare = isCompare.getValue() == 1;
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getHourReportListFromDoris(shopAuth, param, hourlyFunc, type, costType);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            if (booleanCompare && reportListCompareSup != null) {
                compares = reportListCompareSup.get();
                list = getDayReportList(param, reportListSup.get(), compares);
            } else {
                list = getDayReportList(reportListSup.get());
            }
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            if (booleanCompare && reportListCompareSup != null) {
                compares = reportListCompareSup.get();
                list = getWeekReportList(param, reportListSup.get(), compares);
            } else {
                list = getWeekReportList(param, reportListSup.get());
            }
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            if (booleanCompare && reportListCompareSup != null) {
                compares = reportListCompareSup.get();
                list = getMonthReportList(param, reportListSup.get(), compares);
            } else {
                list = getMonthReportList(reportListSup.get());
            }
        }

        AdHourReportResponsePb.AdHourReportResponse.AdHour.Builder hourlyBuilder = AdHourReportResponsePb
                .AdHourReportResponse.AdHour.newBuilder();
        // 字段排序
        if (CollectionUtils.isNotEmpty(list)) {
            for (AdReportHourlyVO vo : list) {
                if (Constants.SB.equalsIgnoreCase(param.getPageBasic().getType()) || Constants.SD.equalsIgnoreCase(param.getPageBasic().getType())) {
                    vo.setAdOtherSaleNum(0);
                    vo.setAdOtherSaleNumCompare(0);
                    vo.setAdOtherSaleNumCompareRate(BigDecimal.ZERO);
                }
                vo.setAcots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
                vo.setAsots(shopSalesByDate.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSalesByDate, 4, RoundingMode.HALF_UP));
            }
            boolean isSorted = StringUtils.isNotBlank(pageBasic.getOrderField()) &&
                    Constants.isADOrderField(pageBasic.getOrderField(), AdReportHourlyVO.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, pageBasic.getOrderField(), pageBasic.getOrderType());
            }
        }
        hourlyBuilder.addAllList(list.stream().filter(Objects::nonNull)
                .map(key -> {
                    AdHourReportResponsePb.AdReportHourRpcVo adReportHourlyPb = PbUtil.toAdReportHourlyPb(key, shopSalesByDate);
                    if(!SBCampaignCostTypeEnum.VCPM.getCode().equals(costType)){
                        adReportHourlyPb = adReportHourlyPb.toBuilder().setVcpm("-").build();
                    }
                    return adReportHourlyPb;
                }).collect(Collectors.toList()));
        AdReportHourlyVO summaryVO = summary(list, shopSalesByDate);
        if (pageBasic.getIsCompare().getValue() == 1 && dateModel != ReportDateModelPb.ReportDateModel.HOURLY) {
            AdReportHourlyVO compareVO = summary(compares, shopSalesByDate);
            summaryVO.compareDataSet(compareVO);
        }
        hourlyBuilder.setSummary(PbUtil.toAdReportHourlyPb(summaryVO, shopSalesByDate));
        hourlyBuilder.addAllChart(ReportChartUtil.getAdtHourChartData(list, false));
        //对比数据,chart图数据
        if (pageBasic.getIsCompare().getValue() == 1) {
            List<AdPlacementHourVo> placementHourVos = list.stream().map(item -> {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(item.getLabel());
                vo.setAdCost(item.getAdCostCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setClicks(item.getClicksCompare());
                vo.setCpa(item.getCpaCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcots(item.getAcotsCompare());
                vo.setAcos(item.getAcosCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setSelfAdOrderNum(item.getSelfAdOrderNumCompare());
                vo.setOtherAdOrderNum(item.getOtherAdOrderNumCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdSelfSale(item.getAdSelfSaleCompare());
                vo.setAdOtherSale(item.getAdOtherSaleCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdSelfSaleNum(item.getAdSelfSaleNumCompare());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNumCompare());
                return vo;
            }).collect(Collectors.toList());
            hourlyBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(placementHourVos, true));
        }
        return hourlyBuilder.build();
    }

    @Deprecated
    @Override
    public List<AdReportHourlyVO> getHourReportList(ShopAuth shopAuth, AdHourReportRequest param,
                                                    Function<AdReportHourlyRequestPb.AdReportHourlyRequest, AdReportHourlyResponsePb.AdReportHourlyResponse> func, String type, String costType) {
        if (shopAuth == null) {
            return null;
        }
        AdPageBasicData pageBasicInfo = param.getPageBasic();

        // sd 或者 sb 小时级数据
        if (Constants.SD.equalsIgnoreCase(type) || Constants.SB.equalsIgnoreCase(type)) {
            return amazonAdFeedReportService.listByAdHourCampaignTypeAndCostType(shopAuth, param, type, costType);
        }
        //获取小时级数据
        AdReportHourlyRequestPb.AdReportHourlyRequest.Builder requestBuilder = AdReportHourlyRequestPb.AdReportHourlyRequest.newBuilder();
        requestBuilder.setSellerId(shopAuth.getSellingPartnerId());
        requestBuilder.setMarketplaceId(shopAuth.getMarketplaceId());
        requestBuilder.setStartDate(pageBasicInfo.getStartDate());
        requestBuilder.setEndDate(pageBasicInfo.getEndDate());
        Integer puid = Optional.of(pageBasicInfo.getPuid()).map(Int32Value::getValue).orElse(null);
        Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).ifPresent(requestBuilder::addAllWeekday);
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
        Optional.ofNullable(predicateEnum).map(PlacementPageParam.placementPredicateEnum::getContent).ifPresent(requestBuilder::addPlacement);
        Optional.of(param.getCampaignId()).filter(StringUtils::isNotBlank).ifPresent(requestBuilder::addCampaignId);
        Optional.of(param.getGroupId()).filter(StringUtils::isNotBlank).ifPresent(requestBuilder::addGroupId);
//        if (StringUtils.isNotBlank(param.getAsin())) {
//            List<String> adIdList = amazonAdProductDao.adIdListByAsin(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getAsin());
//            if (CollectionUtils.isEmpty(adIdList)) {
//                return null;
//            }
//            requestBuilder.addAllAdId(adIdList);
//        }

        AdReportHourlyResponsePb.AdReportHourlyResponse statisticsByHourResponse = func.apply(requestBuilder.build());
        Map<Integer, AdReportHourlyResponsePb.HourlyReportHourData> hourlyReportDataMap = new HashMap<>();
        //组装数据,按小时维度组织
        hourlyReportDataMap = getIntegerHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);

        //获取小时对比数据
        AdReportHourlyResponsePb.AdReportHourlyResponse compareResponse;
        Map<Integer, AdReportHourlyResponsePb.HourlyReportHourData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(pageBasicInfo.getIsCompare().getValue())) {
            requestBuilder.setStartDate(pageBasicInfo.getStartDateCompare());
            requestBuilder.setEndDate(pageBasicInfo.getEndDateCompare());
            compareResponse = func.apply(requestBuilder.build());
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        List<AdReportHourlyVO> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdReportHourlyVO adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            HourlyAndWeeklyDataHandler.filterSumMetricData(voList, adMetricDto);
            HourlyAndWeeklyDataHandler.filterMetricData(voList, adMetricDto);
        }

        return voList;
    }

    @Override
    public List<AdReportHourlyVO> getHourReportListFromDoris(ShopAuth shopAuth, AdHourReportRequest param,
                                                             Function<CampaignHourlyReportSelectDto, List<AmazonMarketingStreamData>> func, String type, String costType) {
        if (shopAuth == null) {
            return null;
        }
        AdPageBasicData pageBasicInfo = param.getPageBasic();

        // sd 或者 sb 小时级数据
        if (Constants.SD.equalsIgnoreCase(type) || Constants.SB.equalsIgnoreCase(type)) {
            return amazonAdFeedReportService.listByAdHourCampaignTypeAndCostType(shopAuth, param, type, costType);
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), pageBasicInfo.getStartDate()));
        selectDto.setEndDate(pageBasicInfo.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        selectDto.setWeekdayList(weekdayList);
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
        Optional.ofNullable(predicateEnum).map(PlacementPageParam.placementPredicateEnum::getContent).ifPresent(data -> selectDto.setPlacements(Collections.singletonList(data)));
        Optional.of(param.getCampaignId()).filter(StringUtils::isNotBlank).ifPresent(data -> selectDto.setCampaignIds(Collections.singletonList(data)));
        Optional.of(param.getGroupId()).filter(StringUtils::isNotBlank).ifPresent(data -> selectDto.setAdGroupList(Collections.singletonList(data)));
        Integer puid = Optional.of(pageBasicInfo.getPuid()).map(Int32Value::getValue).orElse(null);

        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }

        List<AmazonMarketingStreamData> statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : func.apply(selectDto));
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        //组装数据,按小时维度组织
        hourlyReportDataMap = getIntegerHourlyReportDataMapFromDoris(statisticsByHourResponse, hourlyReportDataMap);

        //获取小时对比数据
        List<AmazonMarketingStreamData> compareResponse;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(pageBasicInfo.getIsCompare().getValue())) {
            selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), pageBasicInfo.getStartDateCompare()));
            selectDto.setEndDate(pageBasicInfo.getEndDateCompare());
            boolean returnCompareEmptyList = false;
            if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
                List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
                if (CollectionUtils.isEmpty(adIdList)) {
                    returnCompareEmptyList = true;
                }
                selectDto.setAdIds(adIdList);
            }
            compareResponse = (returnCompareEmptyList ? new ArrayList<>() : func.apply(selectDto));
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMapFromDoris(compareResponse, hourlyReportDataCompareMap);
        }

        List<AdReportHourlyVO> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdReportHourlyVO adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            HourlyAndWeeklyDataHandler.filterSumMetricData(voList, adMetricDto);
            HourlyAndWeeklyDataHandler.filterMetricData(voList, adMetricDto);
        }

        return voList;
    }


    @Override
    public List<AdReportHourlyVO> getDayReportList(List<AdReportHourlyVO> reportList) {
        return getDayWeekOrMonth(reportList, x -> x);
    }


    @Override
    public List<AdReportHourlyVO> getDayReportList(AdHourReportRequest param, List<AdReportHourlyVO> reportList, List<AdReportHourlyVO> comparisonList) {
        List<AdReportHourlyVO> dayWeekOrMonth = getDayWeekOrMonth(reportList, x -> x);
        Int32Value isCompare = param.getPageBasic().getIsCompare();
        if (isCompare.getValue() == 1) {
            List<AdReportHourlyVO> dayWeekOrMonthComparison = getDayWeekOrMonth(comparisonList, x -> x);
            return paddingDayCompare(param, dayWeekOrMonth, dayWeekOrMonthComparison);
        } else {
            return dayWeekOrMonth;
        }
    }

    private List<AdReportHourlyVO> paddingDayCompare(AdHourReportRequest param, List<AdReportHourlyVO> day, List<AdReportHourlyVO> dayComparison) {
        //天的数据比较特殊，列表数据不用返回每一天数据，所以对比是要对应取值
        //从开始时间遍历到结束时间，对比值有值同样要返回该条数据
        AdPageBasicData pageBasic = param.getPageBasic();
        LocalDate start = LocalDate.parse(pageBasic.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(pageBasic.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endCompare = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdReportHourlyVO> reportHourlyVOS = new ArrayList<>();
        //将两个数组抓换成 Map 按日期
        Map<String, AdReportHourlyVO> dayMap = day.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
        Map<String, AdReportHourlyVO> dayCompareMap = dayComparison.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));

        for (; !start.isAfter(end); start = start.plusDays(1),startCompare = startCompare.plusDays(1)) {
            AdReportHourlyVO adReportHourlyVO = dayMap.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdReportHourlyVO adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
                continue;
            }
            AdReportHourlyVO vo;
            if (adReportHourlyVO == null) {
                vo = new AdReportHourlyVO();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = adReportHourlyVO;
            }
            setCompare(vo, adReportHourlyVOCompare);
            reportHourlyVOS.add(vo);
        }
        return reportHourlyVOS;
    }


    private List<AdReportHourlyVO> paddingMothCompare(AdHourReportRequest param, List<AdReportHourlyVO> moth, List<AdReportHourlyVO> dayComparison) {
        //天的数据比较特殊，列表数据不用返回每一天数据，所以对比是要对应取值
        //从开始时间遍历到结束时间，对比值有值同样要返回该条数据
        AdPageBasicData pageBasic = param.getPageBasic();
        LocalDate start = LocalDate.parse(pageBasic.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(pageBasic.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate endCompare = LocalDate.parse(pageBasic.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdReportHourlyVO> reportHourlyVOS = new ArrayList<>();
        //将两个数组抓换成 Map 按日期
        Map<String, AdReportHourlyVO> dayMap = moth.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
        Map<String, AdReportHourlyVO> dayCompareMap = dayComparison.stream().collect(Collectors.toMap(AdReportHourlyVO::getLabel, Function.identity()));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end)); start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdReportHourlyVO adReportHourlyVO = dayMap.get(start.format(formatter));
            AdReportHourlyVO adReportHourlyVOCompare = dayCompareMap.get(startCompare.format(formatter));
            if (adReportHourlyVO == null && adReportHourlyVOCompare == null) {
                continue;
            }
            AdReportHourlyVO vo;
            if (adReportHourlyVO == null) {
                vo = new AdReportHourlyVO();
                vo.setLabel(start.format(formatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = adReportHourlyVO;
            }
            setCompare(vo, adReportHourlyVOCompare);
            reportHourlyVOS.add(vo);
        }
        return reportHourlyVOS;
    }

    private List<AdReportHourlyVO> paddingWeekCompare(List<AdReportHourlyVO> weekMoths, List<AdReportHourlyVO> weekMothComparisons) {
        //日周月已经按照时间全部填充，我们只需要一一对应对比就可以了
        if (CollectionUtils.isEmpty(weekMoths)) {
            return weekMoths;
        }
        for (int i = 0; i < weekMoths.size() && i < weekMothComparisons.size(); i++) {
            setCompare(weekMoths.get(i), weekMothComparisons.get(i));
        }
        return weekMoths;
    }


    private void setCompare(AdReportHourlyVO vo, AdReportHourlyVO voCompare){
        //对比值
        if (voCompare != null) {
            vo.setAdCostCompare(voCompare.getAdCost());
            vo.setImpressionsCompare(voCompare.getImpressions());
            vo.setClicksCompare(voCompare.getClicks());
            vo.setCpaCompare(voCompare.getCpa());
            vo.setAdCostPerClickCompare(voCompare.getAdCostPerClick());
            vo.setCtrCompare(voCompare.getCtr());
            vo.setCvrCompare(voCompare.getCvr());
            vo.setAcosCompare(voCompare.getAcos());
            vo.setRoasCompare(voCompare.getRoas());
            vo.setAdOrderNumCompare(voCompare.getAdOrderNum());
            vo.setSelfAdOrderNumCompare(voCompare.getSelfAdOrderNum());
            vo.setOtherAdOrderNumCompare(voCompare.getOtherAdOrderNum());
            vo.setAdSaleCompare(voCompare.getAdSale());
            vo.setAdSelfSaleCompare(voCompare.getAdSelfSale());
            vo.setAdOtherSaleCompare(voCompare.getAdOtherSale());
            vo.setAdSaleNumCompare(voCompare.getAdSaleNum());
            vo.setAdSelfSaleNumCompare(voCompare.getAdSelfSaleNum());
            vo.setAdOtherSaleNumCompare(voCompare.getAdOtherSaleNum());
            vo.setAcotsCompare(voCompare.getAcots());
            vo.setAsotsCompare(voCompare.getAsots());
            vo.afterPropertiesSet();
        } else {
            ReflectionUtil.setCompareNull(vo);
        }
    }


    @Override
    public List<AdReportHourlyVO> getWeekReportList(AdHourReportRequest param, List<AdReportHourlyVO> reportList) {
        List<AdReportHourlyVO> reports = reportList;
        AdPageBasicData pageBasicInfo = param.getPageBasic();
        reports = ReportChartUtil.getAdWeekReportVos(pageBasicInfo.getStartDate(), pageBasicInfo.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            HourlyAndWeeklyDataHandler.filterSumMetricData(reports, adMetricDto);
            HourlyAndWeeklyDataHandler.filterMetricData(reports, adMetricDto);
        }
        return reports;
    }

    @Override
    public List<AdReportHourlyVO> getMonthReportList(List<AdReportHourlyVO> reportList) {
        return getDayWeekOrMonth(reportList, ReportChartUtil::getAdMonthReportVos);
    }

    @Override
    public List<AdReportHourlyVO> getMonthReportList(AdHourReportRequest param, List<AdReportHourlyVO> reportList, List<AdReportHourlyVO> compareReportList) {
        List<AdReportHourlyVO> dayWeekOrMonth = getDayWeekOrMonth(reportList, ReportChartUtil::getAdMonthReportVos);
        Int32Value isCompare = param.getPageBasic().getIsCompare();
        if (isCompare.getValue() == 1) {
            List<AdReportHourlyVO> dayWeekOrMonthComparison = getDayWeekOrMonth(compareReportList, ReportChartUtil::getAdMonthReportVos);
            return paddingMothCompare(param, dayWeekOrMonth, dayWeekOrMonthComparison);
        } else {
            return dayWeekOrMonth;
        }
    }


    @Override
    public List<AdReportHourlyVO> getWeekReportList(AdHourReportRequest param, List<AdReportHourlyVO> reportList, List<AdReportHourlyVO> compareReportList) {
        AdPageBasicData pageBasicInfo = param.getPageBasic();
        List<AdReportHourlyVO> reports = ReportChartUtil.getAdWeekReportVos(pageBasicInfo.getStartDate(),
                pageBasicInfo.getEndDate(), reportList);
        Int32Value isCompare = param.getPageBasic().getIsCompare();
        if (isCompare.getValue() == 1) {
            List<AdReportHourlyVO> dayWeekOrMonthComparison = ReportChartUtil.getAdWeekReportVos(pageBasicInfo.getStartDateCompare(),
                    pageBasicInfo.getEndDateCompare(), compareReportList);
            return paddingWeekCompare(reports, dayWeekOrMonthComparison);
        } else {
            return reports;
        }
    }

    public List<AdReportHourlyVO> getDayWeekOrMonth(List<AdReportHourlyVO> reportList,
                                                    Function<List<AdReportHourlyVO>, List<AdReportHourlyVO>> function) {
        List<AdReportHourlyVO> reports = reportList;
        reports = function.apply(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            HourlyAndWeeklyDataHandler.filterSumMetricData(reports, adMetricDto);
            HourlyAndWeeklyDataHandler.filterMetricData(reports, adMetricDto);
        }
        return reports;
    }

    private Map<Integer, AdReportHourlyResponsePb.HourlyReportHourData> getIntegerHourlyReportDataMap(AdReportHourlyResponsePb.AdReportHourlyResponse statisticsByHourResponse,
                                                                                                      Map<Integer, AdReportHourlyResponsePb.HourlyReportHourData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMapFromDoris(List<AmazonMarketingStreamData> statisticsByHourResponse,
                                                                                           Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourResponse)) {
            hourlyReportDataMap = statisticsByHourResponse.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private AdReportHourlyVO handleVo(Integer hour, AdReportHourlyResponsePb.HourlyReportHourData data,
                                      AdReportHourlyResponsePb.HourlyReportHourData dataCompare) {
        AdReportHourlyVO adCampaignHourVoCompare = HourlyAndWeeklyDataHandler.convertTo(dataCompare);
        AdReportHourlyVO adCampaignHourVo = HourlyAndWeeklyDataHandler.convertTo(data);
        AdReportHourlyVO vo = AdReportHourlyVO.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .adCost(adCampaignHourVo.getAdCost())
                .clicks(adCampaignHourVo.getClicks())
                .impressions(adCampaignHourVo.getImpressions())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .cpa(adCampaignHourVo.getCpa())
                .acos(adCampaignHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getClicks()), BigDecimal.valueOf(100)),
                        BigDecimal.valueOf(adCampaignHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getAdOrderNum()),
                        BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignHourVo.getClicks())))
                .roas(Objects.isNull(adCampaignHourVo.getAdSale()) || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdSale()) == 0 || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdCost()) == 0 ?
                        BigDecimal.ZERO : adCampaignHourVo.getAdSale().divide(adCampaignHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adCampaignHourVoCompare.getClicks())
                .impressionsCompare(adCampaignHourVoCompare.getImpressions())
                .adSaleNumCompare(adCampaignHourVoCompare.getAdSaleNum())
                .adSaleCompare(adCampaignHourVoCompare.getAdSale())
                .adCostCompare(adCampaignHourVoCompare.getAdCost())
                .adOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    private AdReportHourlyVO handleVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare) {
        AdReportHourlyVO adCampaignHourVoCompare = HourlyAndWeeklyDataHandler.convertTo(dataCompare);
        AdReportHourlyVO adCampaignHourVo = HourlyAndWeeklyDataHandler.convertTo(data);
        AdReportHourlyVO vo = AdReportHourlyVO.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .adCost(adCampaignHourVo.getAdCost())
                .clicks(adCampaignHourVo.getClicks())
                .impressions(adCampaignHourVo.getImpressions())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .cpa(adCampaignHourVo.getCpa())
                .acos(adCampaignHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getClicks()), BigDecimal.valueOf(100)),
                        BigDecimal.valueOf(adCampaignHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getAdOrderNum()),
                        BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignHourVo.getClicks())))
                .roas(Objects.isNull(adCampaignHourVo.getAdSale()) || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdSale()) == 0 || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdCost()) == 0 ?
                        BigDecimal.ZERO : adCampaignHourVo.getAdSale().divide(adCampaignHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adCampaignHourVoCompare.getClicks())
                .impressionsCompare(adCampaignHourVoCompare.getImpressions())
                .adSaleNumCompare(adCampaignHourVoCompare.getAdSaleNum())
                .adSaleCompare(adCampaignHourVoCompare.getAdSale())
                .adCostCompare(adCampaignHourVoCompare.getAdCost())
                .adOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .adCostPerClickCompare(adCampaignHourVoCompare.getAdCostPerClick())
                .cpaCompare(adCampaignHourVoCompare.getCpa())
                .acosCompare(adCampaignHourVoCompare.getAcos())
                .roasCompare(adCampaignHourVoCompare.getRoas())
                .ctrCompare(adCampaignHourVoCompare.getCtr())
                .cvrCompare(adCampaignHourVoCompare.getCvr())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    public static AdReportHourlyVO summary(List<AdReportHourlyVO> list, BigDecimal shopSales) {
        AdReportHourlyVO vo = new AdReportHourlyVO();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdReportHourlyVO ad : list) {
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));

            vo.setViewableImpressions(MathUtil.add(vo.getViewableImpressions(), ad.getViewableImpressions()));
            vo.setOrdersNewToBrand(MathUtil.add(vo.getOrdersNewToBrand(), ad.getOrdersNewToBrand()));
            vo.setUnitsOrderedNewToBrand(MathUtil.add(vo.getUnitsOrderedNewToBrand(), ad.getUnitsOrderedNewToBrand()));
            vo.setSalesNewToBrand(MathUtil.add(vo.getSalesNewToBrand(), ad.getSalesNewToBrand()));
        }
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(shopSales, 4, RoundingMode.HALF_UP));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
        vo.setAsots(shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));


        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.afterPropertiesSet();//为各对比率属性设值
        if (BigDecimal.ZERO.compareTo(vo.getAdCost()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (BigDecimal.ZERO.compareTo(vo.getAdSale()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdOrderNum() == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        if (vo.getAdSaleNum() == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2, RoundingMode.HALF_UP));
        }
        return vo;
    }
}
