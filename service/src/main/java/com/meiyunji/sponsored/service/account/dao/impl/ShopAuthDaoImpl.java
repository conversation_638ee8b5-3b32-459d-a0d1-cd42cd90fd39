package com.meiyunji.sponsored.service.account.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopSpStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopStatusEnum;
import com.meiyunji.sponsored.service.post.response.ShopSitesResponse;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import com.meiyunji.sponsored.service.vo.ShopAuthSellerInfoVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Repository;

import java.util.*;


/**
 * ShopAuth
 *
 * <AUTHOR>
 */
@Repository
public class ShopAuthDaoImpl extends BaseDaoImpl<ShopAuth> implements IShopAuthDao {
    private final static String SHOPAUTH_PUID_KEY = "shopauth_puid_%s_%s"; //判断puid是否缓存了配置信息
    private final static int TIME_OUT = 60 * 60 * 4;//过期时间 1小时
    private String cacheKeyPrefix = "shopauth-"; // cache 前缀
    private int cacheValidation = 5 * 60; // cache 有效期 5 分钟

    private final static String vcShopTableName = "t_vc_shop_auth";
    private final static String scShopTableName = "t_shop_auth";

    @Override
    public ShopAuth getByIdAndPuid(Object id, Integer puid) {
        return super.getByIdAndPuid(id, puid);
    }

    private void saveRedis(ShopAuth shopAuth) {
        /*String key = String.format(SHOPAUTH_PUID_KEY, shopAuth.getPuid(), shopAuth.getId());
        RedisUtil.set(key, JSONUtil.objectToJson(shopAuth), TIME_OUT);*/
    }

    private void saveRedis(Integer id) {
        /*ShopAuth shopAuth = getById(id);
        if (shopAuth == null)
            return;
        String key = String.format(SHOPAUTH_PUID_KEY, shopAuth.getPuid(), shopAuth.getId());
        RedisUtil.set(key, JSONUtil.objectToJson(shopAuth), TIME_OUT);*/
    }

    private void delRedis(Integer puid, Integer id) {
        /*String key = String.format(SHOPAUTH_PUID_KEY, puid, id);
        RedisUtil.del(key);*/
    }

    private void delRedisByPuid(Integer puid) {
        /*String key = String.format(SHOPAUTH_PUID_KEY, puid, "*");
        RedisUtil.del(key);*/
    }

    @Override
    public int updateById(ShopAuth shopAuth) {
       // 当adAccessToken为空时，打印调用栈
        try {
            if (shopAuth != null && shopAuth.getAdAccessToken() == null) {
                logger.error("checkAdAccessTokenForNUll adAccessTokenUpdateById: {}, stackTrace: {}", shopAuth.getAdAccessToken(), ThreadPoolUtil.getStackTrace());
            }
        } catch (Exception e) {
            logger.error("print stack stack error: {}", e);
        }
        int index = super.updateById(shopAuth);
        saveRedis(shopAuth);
        return index;
    }

    @Async
    public void cleanCache(Object id) {
//        Integer shopId = (Integer)id;
//        String cacheKey = cacheKeyPrefix + shopId;
//        RedisUtil.del(cacheKey);
    }

//    @Override
//    public ShopAuth getById(Object id) {
//        Integer shopId = (Integer)id;
//        String cacheKey = cacheKeyPrefix + shopId;
//        ShopAuth obj = RedisUtil.get(cacheKey, ShopAuth.class);
//        if (obj != null) {
//            return obj;
//        }
//
//        obj = super.getById(id);
//        if (obj != null) {
//            RedisUtil.set(cacheKey, obj, cacheValidation, false);
//        }
//
//        return obj;
//    };

    @Override
    public int updateAdToken(int puid, Integer shopId, String refreshToken, String accessToken, String adStatus) {
        String sql = "update t_shop_auth set update_time = now(),ad_refresh_token=?,ad_access_token=?,ad_status=?,ad_auth_time=now() where id = ? and puid=?";
        cleanCache(shopId);
        delRedis(puid, shopId);
        return getJdbcTemplate().update(sql, refreshToken, accessToken, adStatus, shopId, puid);
    }

    @Override
    public int updateAdAccessToken(Integer puid, String sellingPartnerId, String region, String accessToken) {
        try {
            if (accessToken == null) {
                logger.error("checkAdAccessTokenForNUll updateAdAccessToken puid: {}, adAccessToken: {}, stackTrace: {}", puid, accessToken, ThreadPoolUtil.getStackTrace());
            }
        } catch (Exception e) {
            logger.error("print stack stack error: {}", e);
        }
        String sql = "update t_shop_auth set update_time = now(),ad_access_token=? where puid = ? and selling_partner_id=? and region=? and ad_refresh_token is not null";
        delRedisByPuid(puid);
        return getJdbcTemplate().update(sql, accessToken, puid, sellingPartnerId, region);
    }

    @Override
    public int updateSingleField(int id, String fieldName, Object fieldValue, boolean updateTime) {
        StringBuilder sql = new StringBuilder("update t_shop_auth set ");
        sql.append(fieldName).append(" = ? ");
        if (updateTime) {
            sql.append(", update_time = now() ");
        }
        sql.append("where id = ?");
        cleanCache(Integer.valueOf(id));
        int index = update(getJdbcTemplate(), sql.toString(), new Object[]{fieldValue, id});
        saveRedis(id);
        return index;
    }

    @Override
    public List<String> getAllMarketplaceIdByPuid(int puid) {
        String sql = "SELECT DISTINCT(marketplace_id) FROM t_shop_auth WHERE puid = ? ";
        return getJdbcTemplate().queryForList(sql, String.class, puid);
    }

    @Override
    public List<Integer> getShopByMid(int puid, String marketplaceId) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_shop_auth WHERE puid = ?");
        StringBuilder whereSql = new StringBuilder();
        List<Object> argList = Lists.newArrayList();
        argList.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            whereSql.append(" and marketplace_id = ?");
            argList.add(marketplaceId);
        }
        whereSql.append(" and ad_status = 'auth'");
        sql.append(whereSql);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, argList.toArray());
    }

    @Override
    public List<ShopAuth> getListByPuid(int puid) {
        String sql = "select * from t_shop_auth where puid=?";
        return getJdbcTemplate().query(sql, new Object[]{puid}, getMapper());
    }

    @Override
    public List<ShopAuth> getAllId() {
        String sql = "select id,marketplace_id,puid from t_shop_auth order by id";
        return getJdbcTemplate().query(sql, getMapper());
    }

    @Override
    public List<Integer> getAllShopId() {
        String sql = "select id from t_shop_auth";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<Integer> getAllShopId(int puid) {
        String sql = "select id from t_shop_auth where puid=?";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public List<Integer> getAllAdShopId(int puid) {
        String sql = "select id from t_shop_auth where puid=? and ad_status='auth'";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public List<String> getAllSellerId(Integer puid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null && puid > 0) {
            builder.equalTo("puid", puid);
        }
        return listDistinctFieldByCondition("selling_partner_id", builder.build(), String.class);
//        String sql = "select distinct selling_partner_id from t_shop_auth";
//        return getJdbcTemplate().queryForList(sql, String.class);
    }

    @Override
    public List<String> getSellerIdByPuid(Integer puid) {
        String sql = "select distinct selling_partner_id from t_shop_auth WHERE puid = ?";
        return getJdbcTemplate().queryForList(sql, String.class, puid);
    }

    @Override
    public List<ShopAuth> listByPuidAndSellerId(Integer puid, String sellerId) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        condition.equalTo("1", 1);
        if (puid != null) {
            condition.equalTo("puid", puid);
        }
        if (StringUtils.isNotBlank(sellerId)) {
            condition.equalTo("selling_partner_id", sellerId);
        }
        return super.listByCondition(condition.build());
    }

    @Override
    public List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid);
        if (CollectionUtils.isNotEmpty(mkList)) {
            condition.in("marketplace_id", mkList.toArray());
        }
        if (CollectionUtils.isNotEmpty(shopList)) {
            condition.in("id", shopList.toArray());
        }
        return listByCondition(condition.build());
    }

    /**
     * 获取token
     * <p>
     * 根据大区分组
     * <p>
     * 获取大于timeSecond时长/秒 的数据
     *
     * @return
     */
    @Override
    public List<ShopAuth> getSellingToken(Integer puid, Integer shopId, long timeSecond) {
        StringBuilder shopSql = new StringBuilder("SELECT * FROM t_shop_auth where status=0 ");
        List<Object> args = Lists.newArrayList();
        if (shopId != null) {
            shopSql.append(" and id=? ");
            args.add(shopId);
        }
        StringBuilder sql = new StringBuilder("select a.* from ( ").append(shopSql).append(" ) a left join t_sync_time b on a.puid = b.puid and a.selling_partner_id = b.selling_partner_id  and b.sync_type=30 where   (b.sync_status = 0 or b.sync_status is null) and (time_to_sec(timediff(now(), b.sync_last_time)) > (" + timeSecond + ") or b.sync_last_time is null) ");

        if (puid != null) {
            sql.append(" and a.puid=?");
            args.add(puid);
        }

        return getJdbcTemplate().query(sql.toString(), getMapper(), args.toArray());
    }

    @Override
    public List<ShopAuth> getShopAuthGroupBySellerId(Integer puid, int syncType, int status) {
        StringBuilder sql = new StringBuilder("select a.* from t_shop_auth a  left join t_sync_time b on a.puid = b.puid and a.id = b.shop_id and b.sync_type=? where 1=1 ");
        List<Object> args = Lists.newArrayList(syncType);
        if (puid != null) {
            sql.append(" and a.puid = ? ");
            args.add(puid);
        } else {
            if (status == 0) {
                sql.append(" and (b.sync_status = ? or b.sync_status is null)");
            } else {
                sql.append(" and b.sync_status = ? ");
            }
            args.add(status);

            sql.append(" GROUP BY a.`selling_partner_id` ");
        }

        return getJdbcTemplate().query(sql.toString(), getMapper(), args.toArray());
    }

    @Override
    public List<ShopAuth> listBySellerId(String sellerId) {
        String sql = "SELECT * FROM t_shop_auth WHERE selling_partner_id = ? ";
        return getJdbcTemplate().query(sql, getMapper(), sellerId);
    }


    @Override
    public List<Integer> getIdByPuidAndShop(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select id from t_shop_auth where 1=1");
        List<Object> args = Lists.newArrayList();
        if (puid != null) {
            sql.append(" and puid=?");
            args.add(puid);
        }
        if (shopId != null) {
            sql.append(" and id=?");
            args.add(shopId);
        }
        return getJdbcTemplate().queryForList(sql.toString(), args.toArray(), Integer.class);
    }

    @Override
    public Integer getCountByShopIds(Integer puid, List<Integer> shopIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid).greaterThan("status", 0)
                .in("id", shopIds.toArray());
        return super.getCountByCondition(builder.build());
    }

    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    @Override
    public ShopAuth getBySellerIdAndMarketplaceId(Integer puid, String sellerId, String marketplaceId) {

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellerId)
                .equalTo("marketplace_id", marketplaceId)
                .limit(1)
                .build();

        return super.getByCondition(condition);
    }

    @Override
    public List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds) {

        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIds.toArray())
                .equalTo("status", ShopStatusEnum.NORMAL.value())
                .build();

        return super.listByCondition(condition);
    }

    @Override
    public List<ShopAuth> listAllByIds(Integer puid, List<Integer> shopIds) {

        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIds.toArray())
                .build();

        return super.listByCondition(condition);
    }

    /**
     * 获取所有有效的店铺
     *
     * @return
     */
    @Override
    public List<ShopAuth> listAllValidShop() {

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("status", ShopStatusEnum.NORMAL.value())
                .build();

        return super.listByCondition(condition);
    }

    @Override
    public List<ShopAuth> listAllValidAdShop(Integer puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("ad_status", ShopAdStatusEnum.AUTH.getName())
                .build();
        return super.listByCondition(condition);
    }

    @Override
    public int updateSpAccessToken(Integer puid, String sellingPartnerId, RegionEnum region, String accessToken) {
        String sql = "update t_shop_auth set update_time = now(),sp_access_token=? where puid = ? and selling_partner_id=? and region=? and sp_refresh_token is not null";
        delRedisByPuid(puid);
        return getJdbcTemplate().update(sql, accessToken, puid, sellingPartnerId, region.getRegion());
    }

    @Override
    public List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region).build());
    }

    @Override
    public List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region, List<Integer> shopIds) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region).in("id", shopIds.toArray()).build());
    }

    @Override
    public int countByPuidSellerIdRegion(int puid, String sellingPartnerId, String region) {
        return getCountByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region).build());
    }

    @Override
    public int updateAuth(int puid, String sellingPartnerId, String region, String mwsAuthToken, String refreshToken, String accessToken) {
        String sql = "UPDATE t_shop_auth SET mws_auth_token = ?, sp_refresh_token = ?, sp_access_token = ?, " +
                "sp_auth_time = NOW(), sp_status = ?, update_id = ?, `status` = 0, deduct_time = NOW(), update_time = NOW() " +
                "WHERE puid = ? AND selling_partner_id = ? AND region = ?";
        delRedisByPuid(puid);
        return getJdbcTemplate().update(sql, mwsAuthToken, refreshToken, accessToken, ShopSpStatusEnum.AUTH.getName(), puid, puid, sellingPartnerId, region.toLowerCase());
    }

    @Override
    public ShopAuth getByMarketplaceId(int puid, String sellerId, String marketplaceId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellerId)
                .equalTo("marketplace_id", marketplaceId).build();
        return getByCondition(condition);
    }

    @Override
    public ShopAuth getBySellerIdAndMarketplaceId(String sellerId, String marketplaceId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("selling_partner_id", sellerId)
                .equalTo("marketplace_id", marketplaceId).build();
        return getByCondition(condition);
    }

    @Override
    public int cleanAdToken(int puid, Integer shopId) {
        String sql = "update t_shop_auth set update_time = now(),ad_refresh_token=null ,ad_access_token=null,ad_status='unauth',ad_auth_time=null where id = ? and puid=?";
        cleanCache(shopId);
        delRedis(puid, shopId);
        return getJdbcTemplate().update(sql, shopId, puid);
    }

    @Override
    public int updateStatus(Integer puid, Integer shopId, Integer status) {
        String sql = "update t_shop_auth set update_time = now(),`status` = ? where id = ? and puid=?";
        cleanCache(shopId);
        delRedis(puid, shopId);
        return getJdbcTemplate().update(sql, status, shopId, puid);
    }

    @Override
    public int updateAdStatus(Integer puid, Integer shopId, String adStatus) {
        String sql = "update t_shop_auth set update_time = now(),`ad_status` = ? where id = ? and puid=?";
        cleanCache(shopId);
        delRedis(puid, shopId);
        return getJdbcTemplate().update(sql, adStatus, shopId, puid);
    }

    @Override
    public List<ShopAuth> listLimit(String marketplace, int maximumPoolSize) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("marketplace_id", marketplace)
                .equalTo("status", 0)
                .limit(maximumPoolSize).build();
        return listByCondition(condition);
    }

    @Override
    public List<ShopAuth> getBySellerIdAndRegion(Integer puid, String sellingPartnerId, String region) {
        StringBuilder sql = new StringBuilder("SELECT * FROM t_shop_auth WHERE puid = ? and selling_partner_id = ?  and region = ?");
        return getJdbcTemplate().query(sql.toString(), new Object[]{puid, sellingPartnerId, region}, getRowMapper());
    }

    @Override
    public List<String> getSellerIdByShopIds(List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .inIntList("id", shopIdList.toArray(new Integer[shopIdList.size()]))
                .build();
        return listDistinctFieldByCondition("selling_partner_id", condition, String.class);
    }

    @Override
    public ShopAuth getByName(Integer puid, String shopName) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("name", shopName)
                .build();
        return getByCondition(condition);
    }

    @Override
    public List<Integer> getAllValidAdShopIdByLimit(Integer puid, Integer shopId, int start, int limit) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_shop_auth WHERE ad_status=?");

        List<Object> argList = new ArrayList<>();
        argList.add(ShopAdStatusEnum.AUTH.name());
        if (puid != null) {
            sql.append(" AND `puid` = ?");
            argList.add(puid);
        }
        if (shopId != null) {
            sql.append(" AND `id` = ?");
            argList.add(shopId);
        }

        sql.append(" ORDER BY `id` LIMIT ?, ?");
        argList.add(start);
        argList.add(limit);

        return getJdbcTemplate().queryForList(sql.toString(), argList.toArray(), Integer.class);
    }

    @Override
    public List<ShopAuth> getAllValidShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("status", ShopStatusEnum.NORMAL.value());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public int updateAdAuthDate(Integer puid, Integer shopId, java.time.LocalDateTime localDateTime) {
        String sql = "UPDATE `t_shop_auth` SET `ad_auth_sync_next_time` = ? WHERE puid = ? AND id = ? ";
        delRedisByPuid(puid);
        return getJdbcTemplate().update(sql, localDateTime, puid, shopId);
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimitDate(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();


        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }

        return listByCondition(builder.build());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("ad_status", ShopAdStatusEnum.AUTH.name());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public List<ShopByPuidDto> getAllValidAdShopByPuid(Integer puid) {
        StringBuilder sql = new StringBuilder("select puid, id shopId, marketplace_id marketplaceId from t_shop_auth ");
        List<Object> args = new ArrayList<>(2);
        sql.append(" where ad_status != ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        sql.append(" and puid = ? ");
        args.add(puid);
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(ShopByPuidDto.class), args.toArray());
    }

    @Override
    public List<Integer> getAllValidAdShopPuidByLimit(int start, int limit) {
        StringBuilder sql = new StringBuilder("select distinct puid from t_shop_auth ");
        List<Object> args = new ArrayList<>(4);
        sql.append(" where ad_status != ? order by puid limit ?, ?");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        args.add(start);
        args.add(limit);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimitOrderByAdAuthTime(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("ad_status", ShopAdStatusEnum.AUTH.name());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.orderByDesc("ad_auth_time");
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public List<Integer> getAllPuidByLimit(int start, int limit) {
        return getJdbcTemplate().queryForList("SELECT puid FROM t_shop_auth GROUP BY puid LIMIT ?, ?",
                Integer.class, start, limit);
    }

    @Override
    public List<ShopAuth> getAllPartner(Integer puid) {
        return getJdbcTemplate().query("SELECT selling_partner_id, region FROM `t_shop_auth` WHERE `puid` = ? GROUP BY selling_partner_id, region", getMapper(), puid);
    }

    @Override
    public int updateShopStatusBySellerAndRegion(Integer puid, String sellingPartnerId, String region, Integer status) {
        String sql = "UPDATE `t_shop_auth` SET `status` = ?, update_time = NOW(3) WHERE puid = ? AND selling_partner_id = ? AND region = ?";
        delRedisByPuid(puid);
        return getJdbcTemplate().update(sql, status, puid, sellingPartnerId, region);
    }

    @Override
    public ShopAuth getByPuidAndSellerId(Integer puid, String sellId, String marketplaceId) {
        String sql = "SELECT id,puid,region,`name`,marketplace_id,selling_partner_id FROM t_shop_auth where puid = ? and selling_partner_id = ? and marketplace_id = ?";
        List<ShopAuth> list = getJdbcTemplate().query(sql, new Object[]{puid, sellId, marketplaceId}, getRowMapper());
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<ShopAuth> getByPuidAndSellerId(Integer puid, String sellerId) {
        String sql = "SELECT * FROM t_shop_auth where puid = ? and selling_partner_id = ?";
        return getJdbcTemplate().query(sql, new Object[]{puid, sellerId}, getRowMapper());
    }

    @Override
    public List<ShopAuth> getByPuidAndSellerIdAvailable(Integer puid, String sellerId) {
        String sql = "SELECT * FROM t_shop_auth where puid = ? and selling_partner_id = ? and status=0";
        return getJdbcTemplate().query(sql, new Object[]{puid, sellerId}, getRowMapper());
    }

    @Override
    public int copyToDeleteShop(int shopId) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO `t_shop_auth_delete`(");
        sql.append("`id`,`puid`,`name`,`selling_partner_id`,`region`,`marketplace_id`,");
        sql.append("`mws_auth_token`,`sp_refresh_token`,`sp_access_token`,`sp_auth_time`,");
        sql.append("`sp_status`,`ad_refresh_token`,`ad_access_token`,`ad_auth_time`,");
        sql.append("`ad_status`,`status`,`create_id`,`update_id`,`create_time`,`update_time`");
        sql.append(") SELECT `id`,`puid`,`name`,`selling_partner_id`,`region`,`marketplace_id`,");
        sql.append("`mws_auth_token`,`sp_refresh_token`,`sp_access_token`,`sp_auth_time`,");
        sql.append("`sp_status`,`ad_refresh_token`,`ad_access_token`,`ad_auth_time`,");
        sql.append("`ad_status`,`status`,`create_id`,`update_id`,`create_time`,`update_time` ");
        sql.append("FROM `t_shop_auth` WHERE id = ?;");

        cleanCache(Integer.valueOf(shopId));
        return getJdbcTemplate().update(sql.toString(), shopId);
    }

    @Override
    public int clearAdAuth(Integer puid, Integer id) {
        String sql = "UPDATE `t_shop_auth` SET update_time = now(), `ad_refresh_token` = NULL, `ad_access_token` = NULL, `ad_auth_time` = NULL, `ad_status` = ? WHERE `puid` = ? AND `id` = ?";
        cleanCache(Integer.valueOf(id));
        delRedis(puid, id);
        return getJdbcTemplate().update(sql, ShopAdStatusEnum.UNAUTH.getName(), puid, id);
    }

    @Override
    public List<String> getAuthNormalSellers() {
        String sql = "select distinct selling_partner_id from t_shop_auth where `status`=0";
        return getJdbcTemplate().queryForList(sql, String.class);
    }

    @Override
    public List<String> getMWSSellers() {
        String sql = "select distinct selling_partner_id from t_shop_auth where `status`=2";
        return getJdbcTemplate().queryForList(sql, String.class);
    }

    @Override
    public int updateSaState() {
        String sql = "UPDATE `t_shop_auth` SET `status` = 2 WHERE marketplace_id = 'A17E79C6D8DWNP'";

        return getJdbcTemplate().update(sql);
    }

    @Override
    public int updateDeductTime(Integer puid) {
        String sql = "UPDATE `t_shop_auth` SET `deduct_time` = NOW(3) WHERE puid = ?";

        return getJdbcTemplate().update(sql, puid);
    }

    @Override
    public List<ShopAuth> listByPuids(List<Integer> puids) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .in("puid", puids.toArray())
                .build();
        return listByCondition(conditionBuilder);
    }


    @Override
    public List<ShopAuth> getByMarketplaceIds(int puid, String sellerId, String marketplaceId) {

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellerId)
                .inStrList("marketplace_id", marketplaceId.split(",")).build();


        return listByCondition(condition);
    }

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    public List<ShopAuth> getByMarketplaceIds(int puid, String[] marketplaceIds) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("marketplace_id", marketplaceIds).build();
        return listByCondition(condition);
    }
    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getByPuidAndMarketplaceIds(int puid, String[] marketplaceIds) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        condition.equalTo("puid", puid);
        if (ArrayUtils.isNotEmpty(marketplaceIds)) {
            condition.in("marketplace_id", marketplaceIds);
        }

        return listByCondition(condition.build());
    }

    /**
     * 获取已授权对应marketplaceId的店铺
     */
    public List<ShopAuth> getShopAuthByMarketplaceId(String marketplaceId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("ad_status", ShopAdStatusEnum.AUTH.name())
                .limit(100).build();
        return listByCondition(condition);
    }

    @Override
    public List<ShopAuth> getShopAuthByAuthAndTokenIsNull() {
        StringBuilder sql = new StringBuilder("SELECT * FROM t_shop_auth WHERE ad_status = ? and ad_access_token is null ");
        return getJdbcTemplate().query(sql.toString(), new Object[]{ShopAdStatusEnum.AUTH.name()}, getRowMapper());
    }

    @Override
    public int updateAdAccessTokenAndRefreshToken(Integer id, String accessToken, String refreshToken, Date authTime) {
        String sql = "update t_shop_auth set update_time = now(),ad_access_token=?,ad_refresh_token=?,ad_auth_time = ?  where id = ? ";
        return getJdbcTemplate().update(sql, accessToken, refreshToken, authTime, id);
    }

    @Override
    public List<String> marketplaceIdListByShopIds(List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .in("id", shopIdList.toArray())
                .build();
        return listDistinctFieldByCondition("marketplace_id", condition, String.class);
    }

    @Override
    public List<Integer> IdListByShopIds(List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .in("id", shopIdList.toArray())
                .build();
        return listDistinctFieldByCondition("id", condition, Integer.class);
    }

    @Override
    public List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIdList.toArray())
                .notEqualTo("ad_status", ShopAdStatusEnum.UNAUTH.name())
                .build();
        return listByCondition(condition);
    }


    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        List<Object> args = new ArrayList<>();
        String sql = "select * from t_shop_auth where 1=1 " + SqlStringUtil.dealInList("selling_partner_id", sellerIds, args) + SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args);
        return getJdbcTemplate().query(sql, getMapper(), args.toArray());
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puids) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        if (CollectionUtils.isNotEmpty(puids)) {
            condition.in("puid", puids.toArray());
        }
        condition.equalTo("ad_status", ShopAdStatusEnum.AUTH.name());
        return listByCondition(condition.build());
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        if (CollectionUtils.isNotEmpty(puid)) {
            condition.in("puid", puid.toArray());
        }
        if (CollectionUtils.isNotEmpty(shopId)) {
            condition.in("id", shopId.toArray());
        }
        condition.equalTo("ad_status", ShopAdStatusEnum.AUTH.name());
        return listByCondition(condition.build());
    }

    @Override
    public List<ShopAuthBo> getShopAuthBoByIds(int puid, List<Integer> idList) {
        String sql = "select id, marketplace_id marketplaceId, name from " + this.getJdbcHelper().getTable() + " where ";
        ConditionBuilder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .in("id", idList.toArray())
            .build();
        return getJdbcTemplate().query(sql + builder.getSql(), builder.getValues(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }

    @Override
    public List<ShopAuthBo> getShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        String sql = "select id, marketplace_id marketplaceId, name from " + this.getJdbcHelper().getTable() + " where ";
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", idList.toArray())
                .in("marketplace_id", marketplaceIdList.toArray())
                .build();
        return getJdbcTemplate().query(sql + builder.getSql(), builder.getValues(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }

    @Override
    public List<ShopAuthBo> getAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        String sql = "select id, marketplace_id marketplaceId, name from " + this.getJdbcHelper().getTable() + " where ";
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", idList.toArray())
                .in("marketplace_id", marketplaceIdList.toArray())
                .notEqualTo("ad_status", ShopAdStatusEnum.UNAUTH.name())
                .build();
        return getJdbcTemplate().query(sql + builder.getSql(), builder.getValues(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }

    @Override
    public List<Integer> queryRandomSequentList() {
        String sql = "select min(id) from t_shop_auth union select max(id) from t_shop_auth";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<ShopAuth> unionQueryShopsByIdsWithConditions(List<Integer> ids) {
        StringBuilder sql = new StringBuilder();
        for (int i = 0; i < ids.size(); i++) {
            sql.append("(select * from t_shop_auth  WHERE id > ? and ad_status = 'auth' and status = 0  and marketplace_id = 'ATVPDKIKX0DER' limit 1) union all ");
        }
        String substring = sql.substring(0, sql.length() - 11);
        return getJdbcTemplate().query(substring, getMapper(), ids.toArray());
    }

    @Override
    public List<ShopAuthSellerInfoVo> getSellerInfoByIdList(ArrayList<Integer> idList) {
        List<Object> args = new ArrayList<>();
        String sql = "select id, puid, selling_partner_id as sellerId, marketplace_id as marketplaceId from t_shop_auth where ad_status = 'auth' " + SqlStringUtil.dealInList("id", idList, args);
        return getJdbcTemplate().query(sql, new BeanPropertyRowMapper<>(ShopAuthSellerInfoVo.class), args.toArray());
    }

    /**
     * 获取一个有广告授权的店铺
     * @return
     */
    @Override
    public ShopAuth getOneByAdStatus(Integer puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("ad_status", ShopAdStatusEnum.AUTH.getName())
                .limit(1)
                .build();
        return this.getByCondition(condition);
    }

    @Override
    public List<Integer> listAllValidOrExpireAdShopByIds(Integer puid, List<Integer> shopIds) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_shop_auth WHERE 1=1 ");

        List<Object> argList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("id", shopIds, argList));
        }

        if (Objects.nonNull(puid)) {
            sql.append(" AND `puid` = ?");
            argList.add(puid);
        }

        sql.append(" AND `ad_status` in ('auth','expire') ");


        sql.append(" ORDER BY `id` desc");

        return getJdbcTemplate().queryForList(sql.toString(), argList.toArray(), Integer.class);
    }

    @Override
    public List<ShopSitesResponse.ShopSite> getAuthShopListByPuid(int puid, String marketplaceId, List<Integer> shopIdList) {
        StringBuilder sql = new StringBuilder("SELECT id shopId, name shopName, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, ad_status status, support_low_cost_store supportLowCostStore ");
        sql.append(" from ").append(getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder();
        List<Object> argList = Lists.newArrayList();
        whereSql.append(" where puid = ? ");
        argList.add(puid);
        whereSql.append(SqlStringUtil.dealInList("id", shopIdList, argList));
        if (StringUtils.isNotBlank(marketplaceId)) {
            whereSql.append(" and marketplace_id = ?");
            argList.add(marketplaceId);
        }
        whereSql.append(" AND `ad_status` in ('auth','expire') ");
        sql.append(whereSql);
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(ShopSitesResponse.ShopSite.class), argList.toArray());
    }





    @Override
    public ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? and puid = ? ");

        args.add(id);
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? and puid = ? ");
        args.add(id);
        args.add(puid);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }




    @Override
    public List<Integer> getScAndVcShopByMid(int puid, String marketplaceId) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            scSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }
        scSql.append(" and ad_status = 'auth'");
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            vcSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }
        vcSql.append(" and ad_status = 'auth'");
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());

    }

    @Override
    public List<ShopAuth> getScAndVcListByPuid(int puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getScAndVcAllId() {
        StringBuilder scSql = new StringBuilder("select id,marketplace_id marketplaceId,puid,type  from " + scShopTableName );
        StringBuilder vcSql = new StringBuilder("select id,marketplace_id marketplaceId,puid,type from " + vcShopTableName );
        return getJdbcTemplate().query(scSql + " union all  " + vcSql + " order by id ", new BeanPropertyRowMapper<>(ShopAuth.class));
    }

    @Override
    public List<Integer> getScAndVcAllShopId() {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id from " + scShopTableName);
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName);
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<Integer> getScAndVcAllShopId(int puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<Integer> getScAndVcAllAdShopId(int puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id  from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        scSql.append(" and ad_status = 'auth'");
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        vcSql.append(" and ad_status = 'auth'");
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<String> getScAndVcAllSellerId(Integer puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select selling_partner_id  from " + scShopTableName + " ");
        StringBuilder vcSql = new StringBuilder("select selling_partner_id from " + vcShopTableName + " ");
        if (puid != null && puid > 0) {
            scSql.append(" where puid = ? ");
            args.add(puid);
            vcSql.append(" where puid = ? ");
            args.add(puid);
            return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
        } else {
            return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class);
        }
    }



    @Override
    public List<String> getScAndVcSellerIdByPuid(Integer puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select selling_partner_id  from " + scShopTableName + " ");
        StringBuilder vcSql = new StringBuilder("select selling_partner_id from " + vcShopTableName + " ");
        scSql.append(" where puid = ? ");
        args.add(puid);
        vcSql.append(" where puid = ? ");
        args.add(puid);
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
    }



    @Override
    public List<ShopAuth> listScAndVcByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);


        if (CollectionUtils.isNotEmpty(mkList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", mkList, args));
        }

        if (CollectionUtils.isNotEmpty(shopList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopList, args));
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);

        if (CollectionUtils.isNotEmpty(mkList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", mkList, args));
        }

        if (CollectionUtils.isNotEmpty(shopList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopList, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());

    }



    @Override
    public List<ShopAuth> listScAndVcValidByIds(Integer puid, List<Integer> shopIds) {

        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(" and status <> ? ");
        args.add(ShopStatusEnum.NORMAL.name());
        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        scSql.append(" and status <> ? ");
        args.add(ShopStatusEnum.NORMAL.name());
        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }





    @Override
    public List<ShopAuth> listScAndVcAllValidAdShop(Integer puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ?  ");
        args.add(puid);
        scSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getScAndVcAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (puid != null) {
            vcSql.append(" and puid = ? ");
            args.add(puid);
        }

        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());

        if (shopId != null) {
            vcSql.append(" and id = ? ");
            args.add(shopId);
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1=1  ");
        if (puid != null) {
            scSql.append(" and puid = ? ");
            args.add(puid);
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());


        if (shopId != null) {
            scSql.append(" and id = ? ");
            args.add(shopId);
        }
        String s = scSql + " union all " + vcSql + " limit ? , ? ";
        args.add(start);
        args.add(limit);

        return getJdbcTemplate().query(s, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcByPuidAndMarketplaceIds(int puid, String[] marketplaceIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        if (ArrayUtils.isNotEmpty(marketplaceIds)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", Lists.newArrayList(marketplaceIds), args));
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        if (ArrayUtils.isNotEmpty(marketplaceIds)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", Lists.newArrayList(marketplaceIds), args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    @Override
    public List<String> marketplaceIdScAndVcListByShopIds(List<Integer> shopIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select distinct marketplace_id  from " + scShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select distinct marketplace_id  from " + vcShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
    }

    @Override
    public List<Integer> IdListScAndVcByShopIds(List<Integer> shopIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id from " + scShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select id from " + vcShopTableName + "  where 1=1 ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        }
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, Integer.class, args.toArray());
    }

    @Override
    public List<ShopAuth> getScAndVcAuthShopByShopIdList(int puid, List<Integer> shopIdList) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));

        vcSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        scSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args));
        vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args));
        scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));

        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getScAndVcAdAuthShopByShopIdList(List<Integer> puids) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puids)) {
            vcSql.append(SqlStringUtil.dealInList("puid", puids, args));
        }
        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puids)) {
            scSql.append(SqlStringUtil.dealInList("puid", puids, args));
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getScAndVcAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId) {


        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puid)) {
            vcSql.append(SqlStringUtil.dealInList("puid", puid, args));
        }
        if (CollectionUtils.isNotEmpty(shopId)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopId, args));
        }
        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puid)) {
            scSql.append(SqlStringUtil.dealInList("puid", puid, args));
        }
        if (CollectionUtils.isNotEmpty(shopId)) {
            scSql.append(SqlStringUtil.dealInList("id", shopId, args));
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuthBo> getScAndVcShopAuthBoByIds(int puid, List<Integer> idList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'SC' as `type` from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }

        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'VC' as `type` from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }


    @Override
    public List<ShopAuthBo> getScAndVcAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {


        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'SC' as `type` from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }
        scSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name, 'VC' as `type` from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }

        vcSql.append(" and ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());

        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));


    }

    @Override
    public List<Integer> queryScAndVcRandomSequentList() {

        String sql = "select min(id) from (select min(id) id from t_shop_auth union  select min(id) id from t_vc_shop_auth) s  union select max(id) from  (select max(id) id from t_shop_auth union  select max(id) id from t_vc_shop_auth) v ";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }


    @Override
    public List<ShopAuthSellerInfoVo> getScAndVcSellerInfoByIdList(ArrayList<Integer> idList) {

        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, puid, selling_partner_id as sellerId, marketplace_id as marketplaceId, 'SC' as `type` from " + scShopTableName + "  where  ad_status = 'auth' ");
        scSql.append(SqlStringUtil.dealInList("id", idList, args));
        StringBuilder vcSql = new StringBuilder("select id, puid, selling_partner_id as sellerId, marketplace_id as marketplaceId, 'VC' as `type` from " + vcShopTableName + "  where  ad_status = 'auth' ");
        vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuthSellerInfoVo.class), args.toArray());
    }




    /**
     * 获取sc和vc店铺
     * @param puid
     * @param shopIds
     * @param adStatus
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(adStatus)) {
            vcSql.append(" and ad_status = ? ");
            args.add(adStatus);
        } else {
            vcSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        }
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(adStatus)) {
            scSql.append(" and ad_status  ? ");
            args.add(adStatus);
        } else {
            scSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }




    /**
     * SC 和 VC 查询时共用同一个实体返回，要不然改的地方太多了，实在很难受
     *
     * @param id
     * @return
     */
    @Override
    public ShopAuth getScAndVcById(int id) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? ");

        args.add(id);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? ");
        args.add(id);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }

    private String getScSql() {

        return "SELECT id, puid, name, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, sp_refresh_token spRefreshToken, sp_access_token spAccessToken, sp_auth_time spAuthTime,"
                + " status, create_id createId, update_id updateId, create_time createTime, update_time updateTime, ad_refresh_token adRefreshToken, ad_access_token adAccessToken, ad_auth_time adAuthTime, ad_status adStatus, 'SC' as `type` "
                + " FROM t_shop_auth "
                + " where ";
    }

    private String getVcSql() {

        return "SELECT id, puid, name, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, refresh_token spRefreshToken, access_token spAccessToken, auth_time spAuthTime,"
                + " status, create_id createId, update_id updateId, create_time createTime, update_time updateTime, ad_refresh_token adRefreshToken, ad_access_token adAccessToken, ad_auth_time adAuthTime, ad_status adStatus, 'VC' as `type` "
                + " FROM t_vc_shop_auth "
                + " where ";
    }


    /**
     * 获取sc和vc店铺
     *
     * @param shopIds
     * @param adStatus
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcShopListByShopIdsAndAdStatus(List<Integer> shopIds, String adStatus) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");

        if (StringUtils.isNotBlank(adStatus)) {
            vcSql.append(" and ad_status = ? ");
            args.add(adStatus);
        } else {
            vcSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");

        if (StringUtils.isNotBlank(adStatus)) {
            scSql.append(" and ad_status  ? ");
            args.add(adStatus);
        } else {
            scSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));

        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    /**
     * SC 和 VC 查询时共用同一个实体返回，要不然改的地方太多了，实在很难受
     *
     * @param ids
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcByIds(List<Integer> ids) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("id", ids, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("id", ids, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    @Override
    public ShopAuth getScAndVcByIdAndPuid(int id, int puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? and puid = ? ");

        args.add(id);
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? and puid = ? ");
        args.add(id);
        args.add(puid);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }

    @Override
    public List<ShopAuthBo> getScAndVcShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select id, marketplace_id marketplaceId, name from " + scShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            scSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }
        StringBuilder vcSql = new StringBuilder("select id, marketplace_id marketplaceId, name from " + vcShopTableName + "  where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            vcSql.append(SqlStringUtil.dealInList("id", idList, args));
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIdList, args));
        }

        return getJdbcTemplate().query(scSql + " union all " + vcSql, args.toArray(), new BeanPropertyRowMapper<>(ShopAuthBo.class));
    }



    @Override
    public List<ShopAuth> listScAndVcAllByIds(int puid, List<Integer> shopIds) {


        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }




    @Override
    public ShopAuth getScAndVcBySellerIdAndMarketplaceId(String sellerId, String marketplaceId) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");


        vcSql.append(" and selling_partner_id = ? ");
        args.add(sellerId);

        vcSql.append(" and marketplace_id = ? ");
        args.add(marketplaceId);


        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");

        scSql.append(" and selling_partner_id = ? ");
        args.add(sellerId);

        scSql.append(" and marketplace_id = ? ");
        args.add(marketplaceId);

        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        return query.size() == 1 ? query.get(0) : null;
    }


    @Override
    public List<ShopAuth> listScAndVcAllByPuid(int puid) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }







    @Override
    public List<ShopAuth> listValidShopByIds(Integer puid, List<Integer> shopIds) {
        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIds.toArray())
                .in("ad_status", new Object[]{"auth","expire"})
                .build();
        return super.listByCondition(condition);
    }
}