package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;


@Getter
public enum WalmartAdKeywordMatchTypeEnum {
    EXACT("exact", "精准匹配"),
    PHRASE("phrase", "词组匹配"),
    BROAD("broad", "广泛匹配"),
    ;
    private String code;
    private String msg;

    WalmartAdKeywordMatchTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static Map<String, WalmartAdKeywordMatchTypeEnum> keywordMatchTypeEnMap() {
        return Arrays.stream(WalmartAdKeywordMatchTypeEnum.values()).collect(Collectors.toMap(WalmartAdKeywordMatchTypeEnum::getCode, v1 -> v1));
    }
}
