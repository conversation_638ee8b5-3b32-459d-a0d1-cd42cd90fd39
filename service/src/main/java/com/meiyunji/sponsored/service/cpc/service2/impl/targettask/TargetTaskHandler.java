package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTask;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTaskDetail;

import java.util.Collections;
import java.util.List;
import java.util.Map;

public interface TargetTaskHandler {
    /**
     * 处理投放任务
     *
     * @param adTargetTask
     * @param adTargetTaskDetails
     */
    void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails);

    /**
     * 根据targetId获取投放详情信息
     *
     * @param puid
     * @param targetIds
     * @param sourceShopIds
     * @param targetingType
     * @return
     */
    default Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        return Collections.emptyMap();
    }
}
