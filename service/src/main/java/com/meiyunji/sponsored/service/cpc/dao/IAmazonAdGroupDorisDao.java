package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroupDorisAllReport;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProduct;

import java.util.List;

public interface IAmazonAdGroupDorisDao extends IDorisBaseDao<AmazonAdGroupDorisAllReport> {

    /**
     * sp 广告组分页
     */
    Page<AmazonAdGroupDorisAllReport> listAmazonAdGroupPage(Integer puid, GroupPageParam param);

    /**
     * 只查询数量
     */
    int listAmazonAdGroupAllCount(Integer puid, GroupPageParam param);
    /**
     * sp 导出列表页查询所有广告组id
     * @param puid
     * @param param
     * @return
     */
    List<String> idListByAmazonAdGroup(Integer puid, GroupPageParam param);

    /**
     * 列表页查询广告组id，只查询基础表
     * @param puid
     * @param param
     * @return
     */
    List<String> idListByGroupPageParamSelAmazonAdGroup(Integer puid, GroupPageParam param);

    /**
     * 获取doris中的详情
     */
    List<AmazonAdGroup> listByAdGroupIds(Integer puid, Integer shopId, List<String> adGroupIds);

    /**
     * 求汇总
     */
    AdMetricDto getSumAdMetric(Integer puid, GroupPageParam param);

    /**
     * 根据广告组id 进行分组
     */
    List<AdHomePerformancedto> listTotalAmazonAdGroupGroupByAdGroupId(Integer puid, GroupPageParam param);

    AdHomePerformancedto getTotalAmazonAdGroupCompareData(Integer puid, GroupPageParam param, String start, String end);

    /**
     * 根据日期分组
     */
    List<AdHomePerformancedto> listTotalAmazonSpAdGroupGroupDateById(Integer puid, GroupPageParam param, List<String> groupId);

    List<AmazonAdGroupDorisAllReport> getGroupClickData(MultiShopGroupByProductParam param, List<OdsAmazonAdProduct> productGroup);

    Page<AdGroupOptionVo> pageAllGroupsByType(Integer puid, String shopId, List<String> typeAList, String groupType, String name, String campaignIds, String groupIds, String portfolioId, int pageNo, int pageSize);

    Page<MultiShopGroupListVo> getMultiShopGroupList(Integer puid, MultiShopGroupListParam param);
}
