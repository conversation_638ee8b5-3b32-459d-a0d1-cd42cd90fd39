package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adTagSystem.resp.QueryShopInfoResp;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.util.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * amazon广告所有类型活动表(OdsAmazonAdCampaignAll)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:16
 */
@Repository
public class OdsAmazonAdCampaignAllDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdCampaignAll> implements IOdsAmazonAdCampaignAllDao {

    @Override
    public List<OdsAmazonAdCampaignAll> listByCampaignId(Integer puid, List<Integer> shopIdList, List<String> campaignIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select shop_id shopId, marketplace_id marketplaceId, campaign_id campaignId, name, budget ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsAmazonAdCampaignAll.class));
    }


    @Override
    public List<Integer> getShopIdsByCampaignIdsOrPortfolioIds(int puid, List<Integer> shopIdList, List<String> campaignIds, List<String> portfolioIds) {

        StringBuilder sb = new StringBuilder();
        sb.append(" select shop_id ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("campaign_Id", campaignIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ");
                if (portfolioIds.size() > 1) {
                    sb.append(SqlStringUtil.dealInListOr("portfolio_id", portfolioIds, argsList));
                }
                sb.append(" ) ");
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
        }
        sb.append(" group by puid, shop_id ");
        return getJdbcTemplate().queryForList(sb.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public String getCampaignIdsByPortfolioIdSql(Integer puid, Integer shopId, List<Object> argsList, String portfolioId, String type, String state, String servingStatus) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = this.getServingStatus(servingStatusList);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
        }
        ConditionBuilder build = builder.build();
        argsList.addAll(Arrays.asList(build.getValues()));
        return "select campaign_id from ods_t_amazon_ad_campaign_all where " + build.getSql();
    }

    @Override
    public List<QueryShopInfoResp.ShopVo> getCampaignCountGroupByShopId(Integer puid, List<String> shopIdList, List<String> campaignIdList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select shop_id shopId, count(campaign_id) campaignNum ")
                .append(" from ").append(this.getJdbcHelper().getTable());
        List<Object> argsList = new ArrayList<>();
        sb.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sb.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        sb.append(" group by shop_id ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(QueryShopInfoResp.ShopVo.class));
    }

    private List<String> getServingStatus(List<String> servingStatusList) {
        List<String> statusList = new ArrayList<>();
        for (String servingStatusKey : servingStatusList) {
            List<String> servingStatusValueList = Constants.SERVER_STATUS_SELECT.get(servingStatusKey);
            if (servingStatusValueList != null) {
                statusList.addAll(servingStatusValueList);
            }
        }
        return statusList;
    }

    @Override
    public List<String> queryProductRightSqlGetCampaignIds(List<Object> args, String sql) {
        return getJdbcTemplate().queryForList(sql, args.toArray(), String.class);
    }
}

