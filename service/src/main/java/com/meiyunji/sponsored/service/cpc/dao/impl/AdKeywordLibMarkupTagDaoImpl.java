package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAdKeywordLibMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTagRelationIdDTO;
import com.meiyunji.sponsored.service.cpc.po.AdKeywordLibMarkupTag;
import com.meiyunji.sponsored.service.cpc.vo.AdKeywordLibMarkupTagUidVo;
import com.meiyunji.sponsored.service.cpc.vo.AdKeywordLibMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.ClearAdTagVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 标签管理
 *
 * <AUTHOR>
 */
@Repository
public class AdKeywordLibMarkupTagDaoImpl extends BaseShardingDaoImpl<AdKeywordLibMarkupTag> implements IAdKeywordLibMarkupTagDao {

    @Override
    public void insertList(List<AdKeywordLibMarkupTag> list) {
        StringBuilder sql = new StringBuilder("insert into `t_ad_keyword_lib_markup_tag` (`puid`, `uid`, `tag_id`, `relation_id`, " +
                "`type`, `ad_type`,`target_type`, `create_id`, `update_id`, `create_time`, `update_time`) values");
        List<Object> argsList = Lists.newArrayList();
        for (AdKeywordLibMarkupTag e : list) {
            sql.append(" (?, ?,?,?, ?,?, ?,?, ?,  now(3),now(3)),");
            argsList.add(e.getPuid());
            argsList.add(e.getUid());
            argsList.add(e.getTagId());
            argsList.add(e.getRelationId());
            argsList.add(e.getType());
            argsList.add(e.getAdType());
            argsList.add(e.getTargetType());
            argsList.add(e.getCreateId());
            argsList.add(e.getUpdateId());

        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `update_id`=values(update_id),update_time=now(3)");
        getJdbcTemplate(list.get(0).getPuid()).update(sql.toString(), argsList.toArray());
    }


    @Override
    public List<String> getRelationIds(Integer puid, Integer uid, String type, List<Integer> tagIds) {
        StringBuilder sql = new StringBuilder("select DISTINCT relation_id from t_ad_keyword_lib_markup_tag where puid = ? ");
        List<Object> args = Lists.newArrayList(puid);
        if (uid != null) {
            sql.append(" and ( uid = 0 or uid = ? ) ");
            args.add(uid);
        }
        if (CollectionUtils.isNotEmpty(tagIds)) {
            sql.append(SqlStringUtil.dealInList("tag_id", tagIds, args));
        }
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<String> getRelationIdsByUid(Integer uid, String type, List<Integer> tagIds) {
        return null;
    }

    @Override
    public List<AdTagRelationIdDTO> getRelationIdsAndTagIds(Integer puid, Integer uid, String type) {
        StringBuilder sql = new StringBuilder("select relation_id, GROUP_CONCAT(tag_id) tagIdList from t_ad_keyword_lib_markup_tag where puid = ? ");
        List<Object> args = Lists.newArrayList(puid);
        if (uid != null) {
            sql.append(" and ( uid = 0 or uid = ? ) ");
            args.add(uid);
        }
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        sql.append(" group by relation_id ");
        return getJdbcTemplate(puid).query(sql.toString(), (re, i) -> AdTagRelationIdDTO.builder()
                .relationId(re.getString("relation_id"))
                .tagIdList(re.getString("tagIdList"))
                .build(), args.toArray());
    }

    /**
     * 删除 指定指定标签id数据
     *
     * @param puid
     * @param tagId
     * @return
     */
    @Override
    public int deleteByTagId(Integer puid, Integer uid, Long tagId) {
        String sql = "delete from t_ad_keyword_lib_markup_tag where puid = ? and uid = ? and tag_id = ? ";
        return getJdbcTemplate(puid).update(sql, puid, uid, tagId);
    }

    @Override
    public int deleteAllByRelationId(Integer puid, Integer uid, String type, String adType, String targetType, List<String> relationId, List<Long> tagIdList) {
        StringBuilder sql = new StringBuilder("delete from t_ad_keyword_lib_markup_tag where puid = ? and ( uid = 0 or uid = ? )  ");
        List<Object> args = Lists.newArrayList(puid, uid);
        sql.append(SqlStringUtil.dealInList("relation_id", relationId, args));
        if (CollectionUtils.isNotEmpty(tagIdList)) {
            sql.append(SqlStringUtil.dealInList("tag_id", tagIdList, args));
        }
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if (StringUtils.isNotBlank(targetType)) {
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        return getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public List<AdKeywordLibMarkupTagVo> getRelationVos(Integer puid, Integer uid, String type, String adType, String targetType, Long tagId, List<String> relationIds) {
        StringBuilder sql = new StringBuilder("select puid, uid,relation_id,GROUP_CONCAT(DISTINCT tag_id) tagIdsStr from t_ad_keyword_lib_markup_tag where puid = ? and (uid = 0 or uid = ? ) ");
        List<Object> args = Lists.newArrayList(puid, uid);

        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if (tagId != null) {
            sql.append(" and tag_id = ? ");
            args.add(tagId);
        }
        if (StringUtils.isNotBlank(targetType)) {
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        if (CollectionUtils.isNotEmpty(relationIds)) {
            sql.append(SqlStringUtil.dealInList("relation_id", relationIds, args));
        }
        sql.append(" group by relation_id ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdKeywordLibMarkupTagVo>() {
            @Override
            public AdKeywordLibMarkupTagVo mapRow(ResultSet re, int i) throws SQLException {
                AdKeywordLibMarkupTagVo dto = AdKeywordLibMarkupTagVo.builder()
                        .relationId(re.getString("relation_id"))
                        .uid(re.getInt("uid"))
                        .puid(re.getInt("puid"))
                        .tagIdsStr(re.getString("tagIdsStr"))
                        .build();
                dto.setTagIdsList(dto.getTagIdsStr());
                return dto;
            }
        }, args.toArray());
    }

    @Override
    public List<AdKeywordLibMarkupTag> getMarkupTagByRelationId(Integer puid, Integer uid, String type, List<String> relationIds) {
        StringBuilder sql = new StringBuilder("select relation_id,tag_id from t_ad_keyword_lib_markup_tag where puid = ? and (uid = 0 or uid = ? ) ");
        List<Object> args = Lists.newArrayList(puid, uid);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (CollectionUtils.isNotEmpty(relationIds)) {
            sql.append(SqlStringUtil.dealInList("relation_id", relationIds, args));
        }
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AdKeywordLibMarkupTag> getMarkupTagByRelationIdAndCreateId(Integer puid, Integer uid, String type, List<String> relationIds) {
        StringBuilder sql = new StringBuilder("select relation_id,tag_id from t_ad_keyword_lib_markup_tag where puid = ? and (uid = 0 or uid = ? ) ");
        List<Object> args = Lists.newArrayList(puid, uid);
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (Objects.nonNull(uid)) {
            sql.append(" and create_id = ? ");
            args.add(uid);
        }
        if (CollectionUtils.isNotEmpty(relationIds)) {
            sql.append(SqlStringUtil.dealInList("relation_id", relationIds, args));
        }
        return getJdbcTemplate(puid).query(sql.toString(), getRowMapper(), args.toArray());    }

    /**
     * 删除指点关联id的数据
     *
     * @param puid
     * @param clearAdTagVo
     * @return
     */
    @Override
    public int deleteByRelationIdAndAdTagId(Integer puid, ClearAdTagVo clearAdTagVo) {
        StringBuilder sql = new StringBuilder("delete from t_ad_keyword_lib_markup_tag where puid = ? and (uid = ? or uid = 0)  ");
        List<Object> args = Lists.newArrayList(puid, clearAdTagVo.getUid());
        sql.append(" and tag_id = ? ");
        args.add(Long.valueOf(clearAdTagVo.getAdTagId()));
        sql.append(SqlStringUtil.dealInList("relation_id", clearAdTagVo.getIdList().stream().map(String::valueOf).collect(Collectors.toList()), args));
        if (StringUtils.isNotBlank(clearAdTagVo.getType())) {
            sql.append(" and type = ? ");
            args.add(clearAdTagVo.getType());
        }
        if (StringUtils.isNotBlank(clearAdTagVo.getAdType())) {
            sql.append(" and ad_type = ? ");
            args.add(clearAdTagVo.getAdType());
        }
        if (StringUtils.isNotBlank(clearAdTagVo.getTargetType())) {
            sql.append(" and target_type = ? ");
            args.add(clearAdTagVo.getTargetType());
        }

        return getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public List<AdKeywordLibMarkupTagUidVo> getCountByTagId(Integer puid, Integer uid, List<Integer> uidList) {
        StringBuilder sb = new StringBuilder("select tag_id id, count(DISTINCT kl.keyword_text) num from t_ad_keyword_lib_markup_tag mt left join t_amazon_ad_keywords_lib kl on mt.puid = kl.puid and mt.relation_id = kl.id ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where mt.puid = ? and mt.uid = ? ");
        argsList.add(puid);
        argsList.add(uid);
        if (CollectionUtils.isNotEmpty(uidList)) {
            sb.append(SqlStringUtil.dealInList("kl.uid", uidList, argsList));
        }
        sb.append(" group by mt.tag_id");
        return getJdbcTemplate(puid).query(sb.toString(), (re, i) -> AdKeywordLibMarkupTagUidVo.builder()
                .count(re.getLong("num"))
                .id(re.getLong("id"))
                .build(), argsList.toArray());
    }

    @Override
    public List<AdKeywordLibMarkupTagUidVo> getAllCountByTagId(Integer puid, Integer uid, List<Integer> uidList) {
        StringBuilder sb = new StringBuilder("select tag_id id, count(DISTINCT kl.keyword_text) num from t_ad_keyword_lib_markup_tag mt left join t_amazon_ad_keywords_lib kl on mt.puid = kl.puid and mt.relation_id = kl.id ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where mt.puid = ? and (mt.uid = ? or mt.uid = 0 ) ");
        argsList.add(puid);
        argsList.add(uid);
        if (CollectionUtils.isNotEmpty(uidList)) {
            sb.append(SqlStringUtil.dealInList("kl.uid", uidList, argsList));
        }
        sb.append(" group by mt.tag_id");
        return getJdbcTemplate(puid).query(sb.toString(), (re, i) -> AdKeywordLibMarkupTagUidVo.builder()
                .count(re.getLong("num"))
                .id(re.getLong("id"))
                .build(), argsList.toArray());
    }

    @Override
    public int countKeywordByTagId(Integer puid, Integer uid, List<Integer> uidList, List<Long> adTagId) {
        StringBuilder sb = new StringBuilder("select count(DISTINCT kl.keyword_text) num from t_ad_keyword_lib_markup_tag mt left join t_amazon_ad_keywords_lib kl on mt.puid = kl.puid and mt.relation_id = kl.id ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where mt.puid = ? and mt.uid = ? ");
        argsList.add(puid);
        argsList.add(uid);
        sb.append(SqlStringUtil.dealInList("mt.tag_id", adTagId, argsList));
        if (CollectionUtils.isNotEmpty(uidList)) {
            sb.append(SqlStringUtil.dealInList("kl.uid", uidList, argsList));
        }
        Integer max = getJdbcTemplate(puid).queryForObject(sb.toString(), argsList.toArray(), Integer.class);
        return max == null ? 0 : max;
    }

    @Override
    public List<AdKeywordLibMarkupTagUidVo> getAllAsinCountByTagId(int puid, int uid, List<Integer> uidList) {
        StringBuilder sb = new StringBuilder("select tag_id id, count(DISTINCT kl.asin) num from t_ad_keyword_lib_markup_tag mt left join t_amazon_ad_asins_lib kl on mt.puid = kl.puid and mt.relation_id = kl.id ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where mt.puid = ? and (mt.uid = ? or mt.uid = 0 ) ");
        argsList.add(puid);
        argsList.add(uid);
        if (CollectionUtils.isNotEmpty(uidList)) {
            sb.append(SqlStringUtil.dealInList("kl.uid", uidList, argsList));
        }
        sb.append(" group by mt.tag_id");
        return getJdbcTemplate(puid).query(sb.toString(), (re, i) -> AdKeywordLibMarkupTagUidVo.builder()
                .count(re.getLong("num"))
                .id(re.getLong("id"))
                .build(), argsList.toArray());
    }

    @Override
    public int countAsinByTagId(int puid, int uid, List<Integer> uidList, List<Long> tagIdList) {
        StringBuilder sb = new StringBuilder("select count(DISTINCT kl.asin) num from t_ad_keyword_lib_markup_tag mt left join t_amazon_ad_asins_lib kl on mt.puid = kl.puid and mt.relation_id = kl.id ");
        List<Object> argsList = new ArrayList<>();
        sb.append(" where mt.puid = ? and mt.uid = ? ");
        argsList.add(puid);
        argsList.add(uid);
        sb.append(SqlStringUtil.dealInList("mt.tag_id", tagIdList, argsList));
        if (CollectionUtils.isNotEmpty(uidList)) {
            sb.append(SqlStringUtil.dealInList("kl.uid", uidList, argsList));
        }
        Integer max = getJdbcTemplate(puid).queryForObject(sb.toString(), argsList.toArray(), Integer.class);
        return max == null ? 0 : max;
    }
}