package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IWxCpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;


/**
 * <AUTHOR>
 * @date 2023/1/4
 * 顾客反馈order_num数据不准,排查发现应该使用sale_num字段作为订单量值,以下把order_num值设为sale_num的值,但字段名不变
 */
@Repository
public class WxCpcQueryTargetingReportDaoImpl extends BaseShardingSphereDaoImpl<CpcQueryTargetingReport> implements IWxCpcQueryTargetingReportDao {


    /**
     * 产品要求不管是搜索词投放还是asin投放，只要是asin格式都需要查询出来，
     * 为了不影响其它地方查询新建此方法用于查询两张表带asin格式的数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    @Override
    public Page pageKeywordAndTargetManageList(int puid, CpcQueryWordDto dto, Page page) {

        StringBuilder sql = new StringBuilder("SELECT `query`, type, keywordId, target_id,main_image, keywordText,matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,")
                .append("total_sales,order_num  from ( ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectTargetSql = new StringBuilder("SELECT `query`,'target' as type,'' as keywordId, target_id,main_image, '' as keywordText,'' as matchType,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append("SUM(total_sales) total_sales, SUM(order_num) order_num ")
                .append("FROM `t_cpc_query_targeting_report` ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByTargetIdAndIsAsin(puid,dto,argsList);
        selectTargetSql.append(whereSql);

        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,'keyword' as type, keyword_id as keywordId, '' as target_id,")
                .append(" '' as main_image, keyword_text as keywordText,match_type as matchType,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append("SUM(total_sales) total_sales ,SUM(order_num) order_num ")
                .append("FROM `t_cpc_query_keyword_report`");

        String keywordWhereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        selectKeywordSql.append(keywordWhereSql);

        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p ");

        if(StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())){
            String orderField = ReportService.getOrderField(dto.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                sql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcQueryTargetingReport.class);
    }


    /**
     * 产品要求不管是搜索词投放还是asin投放，只要是asin格式都需要查询出来，
     * 为了不影响其它地方查询新建此方法用于查询两张表带asin格式的数据
     * @param puid
     * @param dto
     * @return
     */
    @Override
    public List<AdHomePerformancedto> getKeywordAndTargetListAllTargetingReportByDate(Integer puid, CpcQueryWordDto dto) {
        dto.setCampaignId("");
        dto.setGroupId("");
        StringBuilder sql;
        //订单量字段展示修改为 sale_num 更为准确
        List<Object> argsList = Lists.newArrayList();

        if (StringUtils.isNotBlank(dto.getTargetId())) {
            sql = new StringBuilder("SELECT target_id,'' as keyword_id, count_date, ")
                    .append("SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost,SUM(sale_num) sale_num, ")
                    .append("SUM(total_sales) total_sales, SUM(order_num) order_num ")
                    .append("FROM `t_cpc_query_targeting_report` ");
            String whereSql = getWhereSqlCountByTargetIdAndIsAsin(puid,dto,argsList);
            sql.append(whereSql);
        } else {
            sql = new StringBuilder("SELECT '' as target_id, keyword_id, count_date, ")
                    .append("SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num, ")
                    .append("SUM(total_sales) total_sales, SUM(order_num) order_num ")
                    .append("FROM `t_cpc_query_keyword_report` ");

            String keywordWhereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
            sql.append(keywordWhereSql);
        }

        Object[] args = argsList.toArray();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    return  AdHomePerformancedto.builder()
                            .targetId(re.getString("target_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            .build();
                }
            }, args);
        } finally {
            hintManager.close();
        }

    }


    @Override
    public List<AdHomePerformancedto> getReportByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList,
                                                              CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select  count_date, sum(impressions) impressions, sum(clicks) clicks,sum(cost) cost,sum(sale_num) sale_num,");
        sql.append(" sum(total_sales) total_sales, sum(order_num) order_num ");
        sql.append(" from t_cpc_query_targeting_report  where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));

        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                //模糊搜索
                if("blur".equals(dto.getSearchType())){
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchValue())+"%");
                }else{
                    //默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }



    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();

        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotBlank(dto.getKeywordId())) {
            whereSql.append(" and keyword_id = ? ");
            argsList.add(dto.getKeywordId());
        }
        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')紧密匹配，宽泛匹配 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)){
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        if (CollectionUtils.isNotEmpty(matchTypes)){
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypes,argsList));
        }
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        //广告组合查询
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }

        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                //模糊搜索
                if("blur".equals(dto.getSearchType())){
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(" and (").append(SqlStringUtil.dealLikeListOr(field,dto.getSearchVelueList(),argsList,false)).append(")");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                        argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchVelueList().get(0))+"%");
                    }

                }else{
                    //默认精确
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(SqlStringUtil.dealInList(field,dto.getSearchVelueList(),argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchVelueList().get(0));
                    }
                }
            }
        }
        whereSql.append(" group by keyword_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }


    private String getWhereSqlCountByTargetIdAndIsAsin(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        if (StringUtils.isNotBlank(dto.getTargetId())) {
            whereSql.append(" and target_id = ? ");
            argsList.add(dto.getTargetId());
        }
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        //广告组合查询
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        whereSql.append(" and query REGEXP '" + ASIN_REGEX + "' ");
        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                //模糊搜索
                if("blur".equals(dto.getSearchType())){
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(" and (").append(SqlStringUtil.dealLikeListOr(field,dto.getSearchVelueList(),argsList,false)).append(")");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                        argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchVelueList().get(0))+"%");
                    }

                }else{
                    //默认精确
                    if(dto.getSearchVelueList().size() > 1){
                        whereSql.append(SqlStringUtil.dealInList(field,dto.getSearchVelueList(),argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchVelueList().get(0));
                    }
                }
            }
        }
        whereSql.append(" group by target_id,`query` ");
        if(dto.getUseAdvanced()){
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }



}