package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.walmart.oms.advertiser.base.dto.UpdateEditableCampaignPlacementsDTO;
import com.walmart.oms.advertiser.base.vo.CampaignResponseVO;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/21 11:06
 * @describe:
 */
public interface IWalmartAdvertisingPlacementClientService {
    CampaignResponseVO createPlacement(List<UpdateEditableCampaignPlacementsDTO> placementDto) throws ServiceException;
    void updateEditableCampaignPlacements(List<UpdateEditableCampaignPlacementsDTO> placementDto) throws ServiceException;
}
