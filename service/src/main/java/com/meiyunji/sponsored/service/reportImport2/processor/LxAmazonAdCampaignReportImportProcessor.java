package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.alibaba.fastjson.parser.JSONLexer;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport.constanst.LxReportConstant;
import com.meiyunji.sponsored.service.reportImport.model.LxCampaignReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdCampaignReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;
import java.util.zip.ZipInputStream;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdCampaignReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdCampaignReport> {


    protected final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;

    protected LxAmazonAdCampaignReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao, IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao, IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient, IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonAdCampaignAllReportDao = amazonAdCampaignAllReportDao;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdCampaignReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdCampaignReport report = new LxAmazonAdCampaignReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= 500) {
                    dealReport(importMessage, reports, shopAuth);
                    reports = Lists.newArrayListWithExpectedSize(500);
                }
            }
            jsonReader.endArray();
            jsonReader.close();
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdCampaignReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdCampaignReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);

        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        List<AmazonAdCampaignAllReport> insertReport = new ArrayList<>();

        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }


            if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdCampaignAllReport spReport = getSpReport(puid, shopAuth, importMessage.getCountDate(), e, amazonAdCampaignAll);
                insertReport.add(spReport);

            } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdCampaignAllReport sbReport = getSbReport(puid, shopAuth, importMessage.getCountDate(), e, amazonAdCampaignAll);
                insertReport.add(sbReport);

            } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonAdCampaignAllReport sdReport = getSdReport(puid, shopAuth, importMessage.getCountDate(), e, amazonAdCampaignAll);
                insertReport.add(sdReport);
            }
        });


        if (CollectionUtils.isNotEmpty(insertReport)) {
            amazonAdCampaignAllReportDao.insertOrUpdateList(puid, insertReport);
        }
    }

    private AmazonAdCampaignAllReport getSpReport(Integer puid, ShopAuth shopAuth, String countDate, LxAmazonAdCampaignReport report, AmazonAdCampaignAll campaignAll) {
        AmazonAdCampaignAllReport amazonAdCampaignReport = new AmazonAdCampaignAllReport();
        amazonAdCampaignReport.setSales7d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdCampaignReport.setSales7dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);

        amazonAdCampaignReport.setUnitsOrdered7d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdCampaignReport.setConversions7d(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdCampaignReport.setConversions7dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdCampaignReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdCampaignReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdCampaignReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        amazonAdCampaignReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        amazonAdCampaignReport.setPuid(puid);
        amazonAdCampaignReport.setShopId(shopAuth.getId());
        amazonAdCampaignReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdCampaignReport.setCountDate(countDate);

        amazonAdCampaignReport.setCampaignId(campaignAll.getCampaignId());
        amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
        amazonAdCampaignReport.setCampaignType(Constants.PLACEMENT_ALL); //All表示汇总信息
        amazonAdCampaignReport.setPlacement(Constants.PLACEMENT_ALL);
        amazonAdCampaignReport.setIsSummary(1);
        amazonAdCampaignReport.setCampaignName(campaignAll.getName());
        return amazonAdCampaignReport;
    }


    private AmazonAdCampaignAllReport getSbReport(Integer puid, ShopAuth shopAuth, String countDate, LxAmazonAdCampaignReport report, AmazonAdCampaignAll campaignAll) {
        AmazonAdCampaignAllReport amazonAdCampaignReport = new AmazonAdCampaignAllReport();
        amazonAdCampaignReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdCampaignReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);

        amazonAdCampaignReport.setUnitsOrdered14d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdCampaignReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdCampaignReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdCampaignReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdCampaignReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdCampaignReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        amazonAdCampaignReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        amazonAdCampaignReport.setPuid(puid);
        amazonAdCampaignReport.setShopId(shopAuth.getId());
        amazonAdCampaignReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdCampaignReport.setCountDate(countDate);
        amazonAdCampaignReport.setType(CampaignTypeEnum.sb.getCampaignType());
        amazonAdCampaignReport.setCampaignType(Optional.ofNullable(campaignAll.getAdFormat()).orElse("manual"));
        amazonAdCampaignReport.setAdFormat(Optional.ofNullable(campaignAll.getAdFormat()).orElse("manual"));
        amazonAdCampaignReport.setCampaignId(campaignAll.getCampaignId());
        amazonAdCampaignReport.setPlacement(Constants.PLACEMENT_ALL);
        amazonAdCampaignReport.setIsSummary(1);
        amazonAdCampaignReport.setCampaignName(campaignAll.getName());

        //还没有定义
//        amazonAdCampaignReport.setDpv14d(isDxmNumeric(report.getDpv()) ? Integer.valueOf(report.getDpv()) : 0);
//        amazonAdCampaignReport.setVideo5SecondViewRate(isDxmNumeric(report.getVideo5SecondViewRate()) ? new BigDecimal(report.getVideo5SecondViewRate().replaceAll("%", "")).doubleValue() : 0);
//        amazonAdCampaignReport.setVideo5SecondViews(isDxmNumeric(report.getVideo5SecondViews()) ? Integer.valueOf(report.getVideo5SecondViews()) : 0);
//        amazonAdCampaignReport.setVideoFirstQuartileViews(isDxmNumeric(report.getVideoFirstQuartileViews()) ? Integer.valueOf(report.getVideoFirstQuartileViews()) : 0);
//        amazonAdCampaignReport.setVideoMidpointViews(isDxmNumeric(report.getVideoMidpointViews()) ? Integer.valueOf(report.getVideoMidpointViews()) : 0);
//        amazonAdCampaignReport.setVideoThirdQuartileViews(isDxmNumeric(report.getVideoThirdQuartileViews()) ? Integer.valueOf(report.getVideoThirdQuartileViews()) : 0);
//        amazonAdCampaignReport.setVideoUnmutes(isDxmNumeric(report.getVideoUnmutes()) ? Integer.valueOf(report.getVideoUnmutes()) : 0);
//        amazonAdCampaignReport.setVideoCompleteViews(isDxmNumeric(report.getVideoCompleteViews()) ? Integer.valueOf(report.getVideoCompleteViews()) : 0);
//
//        amazonAdCampaignReport.setVctr(isDxmNumeric(report.getVctr()) ? new BigDecimal(report.getVctr().replaceAll("%", "")).doubleValue() : 0);
//        amazonAdCampaignReport.setVtr(isDxmNumeric(report.getVtr()) ? new BigDecimal(report.getVtr().replaceAll("%", "")).doubleValue() : 0);


        return amazonAdCampaignReport;
    }


    private AmazonAdCampaignAllReport getSdReport(Integer puid, ShopAuth shopAuth, String countDate, LxAmazonAdCampaignReport report, AmazonAdCampaignAll campaignAll) {
        AmazonAdCampaignAllReport amazonAdCampaignReport = new AmazonAdCampaignAllReport();
        amazonAdCampaignReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        amazonAdCampaignReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);

        amazonAdCampaignReport.setUnitsOrdered14d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        amazonAdCampaignReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        amazonAdCampaignReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        amazonAdCampaignReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        amazonAdCampaignReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        amazonAdCampaignReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        amazonAdCampaignReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        amazonAdCampaignReport.setPuid(puid);
        amazonAdCampaignReport.setShopId(shopAuth.getId());
        amazonAdCampaignReport.setMarketplaceId(shopAuth.getMarketplaceId());
        amazonAdCampaignReport.setCountDate(countDate);
        amazonAdCampaignReport.setType(CampaignTypeEnum.sd.getCampaignType());
        amazonAdCampaignReport.setCountDate(countDate);
        amazonAdCampaignReport.setCampaignType(campaignAll.getTactic());
        amazonAdCampaignReport.setTacticType(campaignAll.getTactic());
        amazonAdCampaignReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        amazonAdCampaignReport.setCampaignId(campaignAll.getCampaignId());
        amazonAdCampaignReport.setPlacement(Constants.PLACEMENT_ALL);
        amazonAdCampaignReport.setIsSummary(1);
        amazonAdCampaignReport.setCampaignName(campaignAll.getName());

        return amazonAdCampaignReport;
    }


    private void setCampaignReportOtherData(LxCampaignReport report, AmazonAdCampaignAllReport sbCampaignReport) {
        try {
            sbCampaignReport.setViewImpressions(report.getViewImpressions().equalsIgnoreCase(LxReportConstant.NO_SUPPORT_FIELD) ? null
                    : Integer.valueOf(report.getViewImpressions()));
        } catch (Exception exception) {
            log.error("setCampaignReportOtherData execute setViewImpressions errorMsg: {}", exception.getMessage());
        }

        try {
            sbCampaignReport.setOrdersNewToBrand14d(report.getOrdersNewToBrand14d().equalsIgnoreCase(LxReportConstant.NO_SUPPORT_FIELD) ? 0
                    : Integer.parseInt(report.getOrdersNewToBrand14d()));
        } catch (Exception exception) {
            log.error("setCampaignReportOtherData execute setOrdersNewToBrand14d errorMsg: {}", exception.getMessage());
        }

        try {
            sbCampaignReport.setOrderRateNewToBrand14d(report.getOrderRateNewToBrand14d().equalsIgnoreCase(LxReportConstant.NO_SUPPORT_FIELD) ? 0
                    : new BigDecimal(report.getOrderRateNewToBrand14d()).doubleValue());
        } catch (Exception exception) {
            log.error("setCampaignReportOtherData execute setOrderRateNewToBrand14d errorMsg: {}", exception.getMessage());
        }


        try {
            sbCampaignReport.setSalesNewToBrand14d(report.getSalesNewToBrand14d().equalsIgnoreCase(LxReportConstant.NO_SUPPORT_FIELD) ? BigDecimal.ZERO :
                    new BigDecimal(report.getSalesNewToBrand14d()));
        } catch (Exception exception) {
            log.error("setCampaignReportOtherData execute setSalesNewToBrand14d errorMsg: {}", exception.getMessage());
        }


        try {
            sbCampaignReport.setUnitsOrderedNewToBrand14d(report.getUnitsOrderedNewToBrand14d().equalsIgnoreCase(LxReportConstant.NO_SUPPORT_FIELD) ? 0 :
                    Integer.parseInt(report.getUnitsOrderedNewToBrand14d()));
        } catch (Exception exception) {
            log.error("setCampaignReportOtherData execute setUnitsOrderedNewToBrand14d errorMsg: {}", exception.getMessage());
        }


    }


}
