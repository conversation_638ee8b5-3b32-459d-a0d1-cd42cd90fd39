package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazon.advertising.spV3.targeting.CreateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.entity.CreateTargetEntityV3;
import com.amazon.advertising.spV3.targeting.entity.TargetApiResponseV3;
import com.amazon.advertising.spV3.targeting.entity.TargetSuccessResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:21
 */
@Service(AdTargetTaskConstant.SP_TARGET_HANDLER)
@Slf4j
public class SpTargetHandler implements TargetTaskHandler {

    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;
    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private ICpcTargetingService cpcTargetingService;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        createTargeting(adTargetTask, adTargetTaskDetails);
    }

    public void createTargeting(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        if (CollectionUtils.isEmpty(adGroupIdSet)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        int originalTaskDetailSize = adTargetTaskDetails.size();
        List<AmazonAdTargeting> amazonAdTargetings = convertAddTargetingVoToPO(
                adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<AmazonAdTargeting>> amazonAdTargetingPartition = Lists.partition(amazonAdTargetings, AdTargetTaskConstant.MAX_SP_TARGET_SIZE);
        for (List<AmazonAdTargeting> amazonAdTargetingList : amazonAdTargetingPartition) {
            Result result = createTargetV3(amazonAdTargetingList, adTargetTaskDetailMap, shop);
            List<AdManageOperationLog> targetLogs = Lists.newArrayListWithExpectedSize(2);
            for (AmazonAdTargeting targeting : amazonAdTargetingList) {
                AdManageOperationLog targetLog = adManageOperationLogService.getTargetsLog(null, targeting);
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(targeting.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                targetLog.setIp(adTargetTask.getLoginIp());
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
                if (StringUtils.isNotBlank(targeting.getTargetId())) {
                    targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    if (StringUtils.isNotBlank(targeting.getError())) {
                        targetLog.setResultInfo(targeting.getError());
                    } else {
                        targetLog.setResultInfo(result.getMsg());
                    }

                }
                targetLogs.add(targetLog);
            }
            //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
            adManageOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            if (result.success()) {
                amazonAdTargetingList = amazonAdTargetingList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(adTargetTask.getPuid(), amazonAdTargetingList, Constants.TARGETING_TYPE_ASIN);
                cpcTargetingService.saveDoris(shop.getPuid(), shop.getId(), amazonAdTargetingList.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()));

                // 维护广告组类型
                Set<String> needUpdateAdGroupIds = amazonAdTargetingList.stream()
                        .map(AmazonAdTargeting::getAdGroupId).collect(Collectors.toSet());
                String marketplaceId = amazonAdTargetingList.get(0).getMarketplaceId();
                amazonAdGroupDao.updateAdGroupType(adTargetTask.getPuid(), adTargetTask.getShopId(), marketplaceId, needUpdateAdGroupIds, Constants.GROUP_TYPE_TARGETING);

                cpcTargetingApiService.syncTargetings(shop, null, null, amazonAdTargetingList.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.joining(",")));
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdTargetings.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private Result createTargetV3(List<AmazonAdTargeting> targetings, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth shop) {
        AmazonAdTargeting one = targetings.get(0);
        List<CreateTargetEntityV3> targetingList = cpcTargetingApiService.makeCreateTargetingClausesV3(targetings);
        CreateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        }
        if (response == null) {
            for (AmazonAdTargeting amazonAdTargeting : targetings) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            TargetApiResponseV3 data = response.getData();
            if (CollectionUtils.isNotEmpty(data.getTargetingClauses().getSuccess())) {
                for (TargetSuccessResultV3 successResultV3 : data.getTargetingClauses().getSuccess()) {
                    AmazonAdTargeting amazonAdTargeting = targetings.get(successResultV3.getIndex());
                    amazonAdTargeting.setTargetId(successResultV3.getTargetId());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getTargetingClauses().getError())) {
                for (ErrorItemResultV3 errorItemResultV3 : data.getTargetingClauses().getError()) {
                    String errorMsg = AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    error.append("targetValue:").append(targetings.get(errorItemResultV3.getIndex()).getTargetingValue()).append(",desc:").append(errorMsg).append(";");

                    AmazonAdTargeting amazonAdTargeting = targetings.get(errorItemResultV3.getIndex());
                    amazonAdTargeting.setError(errorMsg);

                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMsg, errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    adTargetTaskDetail.setFailureReasonDetail(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        String formatMsg = msg;
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
            formatMsg = targetTaskComponent.getError(msg, response.getError().getMessage());
        }
        for (AmazonAdTargeting amazonAdTargeting : targetings) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatMsg);
            adTargetTaskDetail.setFailureReasonDetail(msg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(msg);
    }

    private List<AmazonAdTargeting> convertAddTargetingVoToPO(Integer uid, Map<String, AmazonAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>(adTargetTaskDetails.size());
        AmazonAdTargeting amazonAdTargeting;
        Expression expression;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
        List<Integer> sourceShopIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, sourceShopIds, targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                amazonAdTargeting = new AmazonAdTargeting();
                amazonAdTargetings.add(amazonAdTargeting);
                amazonAdTargeting.setPuid(amazonAdGroup.getPuid());
                amazonAdTargeting.setShopId(amazonAdGroup.getShopId());
                amazonAdTargeting.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                amazonAdTargeting.setAdGroupId(amazonAdGroup.getAdGroupId());
                amazonAdTargeting.setDxmGroupId(amazonAdGroup.getId());
                amazonAdTargeting.setCampaignId(amazonAdGroup.getCampaignId());
                amazonAdTargeting.setProfileId(amazonAdGroup.getProfileId());
                amazonAdTargeting.setExpressionType(Constants.MANUAL);
                amazonAdTargeting.setState(CpcStatusEnum.enabled.name());
                amazonAdTargeting.setType(type);
                amazonAdTargeting.setBid(adTargetTaskDetail.getBid().doubleValue());
                amazonAdTargeting.setCreateId(uid);
                amazonAdTargeting.setTargetTaskDetailId(adTargetTaskDetail.getId());

                if (adTargetTaskDetail.getSuggested() != null) {
                    amazonAdTargeting.setSuggested(adTargetTaskDetail.getSuggested());
                }
                if (adTargetTaskDetail.getRangeStart() != null) {
                    amazonAdTargeting.setRangeStart(adTargetTaskDetail.getRangeStart());
                }
                if (adTargetTaskDetail.getRangeEnd() != null) {
                    amazonAdTargeting.setRangeEnd(adTargetTaskDetail.getRangeEnd());
                }

                List<Expression> expressions = new ArrayList<>();
                if (TargetTypeEnum.asin.name().equals(type)) {
                    expression = new Expression();
                    if (StringUtils.isNotBlank(adTargetTaskDetail.getMatchType())) {
                        expression.setType(adTargetTaskDetail.getMatchType());
                    } else {
                        expression.setType(ExpressionEnum.asinSameAs.value());
                    }
                    amazonAdTargeting.setSelectType(expression.getType());
                    expressions.add(expression);
                    if (StringUtils.isEmpty(adTargetTaskDetail.getTargetId())) {
                        expression.setValue(adTargetTaskDetail.getTargetObject());
                        amazonAdTargeting.setTargetingValue(adTargetTaskDetail.getTargetObject());
                        amazonAdTargeting.setImgUrl(adTargetTaskDetail.getImgUrl());
                        amazonAdTargeting.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                    } else {
                        AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                        expression.setValue(adTargetDetail.getTargetObject());
                        amazonAdTargeting.setTargetingValue(adTargetDetail.getTargetObject());
                        amazonAdTargeting.setImgUrl(adTargetDetail.getImgUrl());
                        amazonAdTargeting.setTitle(adTargetDetail.getTargetObjectDesc());
                    }
                } else {
                    // 类目投放任务肯定包含targetId
                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinCategorySameAs.value());
                    expression.setValue(adTargetDetail.getCategoryId());
                    expressions.add(expression);
                    amazonAdTargeting.setTargetingValue(adTargetTaskDetail.getTargetObject());

                    if (StringUtils.isNotBlank(adTargetDetail.getBrandId())) {
                        expression = new Expression();
                        expression.setType(ExpressionEnum.asinBrandSameAs.value());
                        expression.setValue(adTargetDetail.getBrandId());
                        expressions.add(expression);
                    }
                    if (StringUtils.isNotBlank(adTargetDetail.getMinPrice()) || StringUtils.isNotBlank(adTargetDetail.getMaxPrice())) {
                        expression = new Expression();
                        if (StringUtils.isBlank(adTargetDetail.getMinPrice())) {
                            expression.setType(ExpressionEnum.asinPriceLessThan.value());
                            expression.setValue(adTargetDetail.getMaxPrice());
                            expressions.add(expression);
                        } else if (StringUtils.isBlank(adTargetDetail.getMaxPrice())) {
                            expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                            expression.setValue(adTargetDetail.getMinPrice());
                            expressions.add(expression);
                        } else {
                            expression.setType(ExpressionEnum.asinPriceBetween.value());
                            expression.setValue(adTargetDetail.getMinPrice() + "-" + adTargetDetail.getMaxPrice());
                            expressions.add(expression);
                        }
                    }
                    if (adTargetDetail.getMinReviewRating() != null || adTargetDetail.getMaxReviewRating() != null) {
                        expression = new Expression();
                        if (adTargetDetail.getMinReviewRating() == null) {
                            expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                            expression.setValue(String.valueOf(adTargetDetail.getMaxReviewRating()));
                            expressions.add(expression);
                        } else if (adTargetDetail.getMaxReviewRating() == null) {
                            expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                            expression.setValue(String.valueOf(adTargetDetail.getMinReviewRating()));
                            expressions.add(expression);
                        } else {
                            expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                            expression.setValue(adTargetDetail.getMinReviewRating() + "-" + adTargetDetail.getMaxReviewRating());
                            expressions.add(expression);
                        }
                    }
                    if (adTargetDetail.getAsinIsPrimeShippingEligible() != null) {
                        expression = new Expression();
                        expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                        expression.setValue(adTargetDetail.getAsinIsPrimeShippingEligible());
                        expressions.add(expression);
                    }
                }
                amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressions));
            }
        }

        return amazonAdTargetings;
    }

    @Override
    public Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        List<CommonAmazonAdTargeting> list = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
        return list.stream().map(each -> {
            AdTargetDetailDto adTargetDetail = new AdTargetDetailDto();
            adTargetDetail.setTargetId(each.getTargetId());
            String type = each.getType();
            if (TargetTypeEnum.asin.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                adTargetDetail.setTargetObjectDesc(each.getTitle());
                adTargetDetail.setImgUrl(each.getImgUrl());
            } else if (TargetTypeEnum.category.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                boolean isSp = TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType);
                targetTaskComponent.fillCategoryDetail(adTargetDetail, each, isSp);
            }
            return adTargetDetail;
        }).collect(Collectors.toMap(AdTargetDetailDto::getTargetId, Function.identity(), (newVal, oldVal) -> newVal));
    }
}
