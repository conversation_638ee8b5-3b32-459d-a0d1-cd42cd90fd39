package com.meiyunji.sponsored.service.config.nacos;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Set;

@Data
@RefreshScope
@Component
public class AdManageLimitConfig {

    /**
     * 活动汇总数据限制
     */
    @Value("${ad.campaign.aggregate.limit:10000000}")
    private Integer aggregateLimit;

    /**
     * 活动小时级数据限制
     */
    @Value("${ad.campaign.hour.limit:20000}")
    private Integer hourLimit;

    /**
     * 投放汇总数据限制
     */
    @Value("${ad.target.aggregate.limit:10000000}")
    private Integer targetAggregateLimit;

    /**
     * 投放基础数据限制
     */
    @Value("${ad.target.base.limit:4000000}")
    private Integer targetBaseLimit;

    /**
     * 投放小时级数据限制
     */
    @Value("${ad.target.hour.limit:20000}")
    private Integer targetHourLimit;

    /**
     * 投放层级多店铺黑名单
     */
    @Value("#{'${ad.target.black:}'.empty ? null : '${ad.target.black:}'.split(',')}")
    private Set<String> targetBlackList;
}
