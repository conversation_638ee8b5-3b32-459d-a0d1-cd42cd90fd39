package com.meiyunji.sponsored.service.attribution.service.impl;

import com.amazon.advertising.attribution.Publishers;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.attribution.api.AttributionApiClient;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionPublisherDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionPublisher;
import com.meiyunji.sponsored.service.attribution.service.IAmazonAdAttributionPublisherService;
import com.meiyunji.sponsored.service.cache.UserPlanTypeCacheService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wade
 * @date: 2022/3/12 11:55
 * @describe:
 */
@Service
@Slf4j
public class AmazonAdAttributionPublisherService implements IAmazonAdAttributionPublisherService {

    private final IAmazonAdProfileDao amazonAdProfileDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final RedisService redisService;
    private final IAmazonAdAttributionPublisherDao amazonAdAttributionPublisherDao;
    private final AttributionApiClient attributionApiClient;

    @Autowired
    private UserPlanTypeCacheService userPlanTypeCacheService;

    public AmazonAdAttributionPublisherService(
            IAmazonAdProfileDao amazonAdProfileDao, IScVcShopAuthDao shopAuthDao,
            RedisService redisService, IAmazonAdAttributionPublisherDao amazonAdAttributionPublisherDao,
            AttributionApiClient attributionApiClient) {
        this.amazonAdProfileDao = amazonAdProfileDao;
        this.shopAuthDao = shopAuthDao;
        this.redisService = redisService;
        this.amazonAdAttributionPublisherDao = amazonAdAttributionPublisherDao;
        this.attributionApiClient = attributionApiClient;
    }

    @Override
    public void syncAttributionPublisher() {
        Set<Integer> expirePuidSet = userPlanTypeCacheService.getCache();
        List<Integer> excludePuidList = CollectionUtils.isEmpty(expirePuidSet) ? null : new ArrayList<>(expirePuidSet);
        AmazonAdProfile profile = amazonAdProfileDao.getAttributionAdvertiser(excludePuidList);
        if (Objects.isNull(profile)) {
            log.warn("同步attribution广告渠道数据, 没有符合条件的profile");
            return;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(profile.getShopId());
        if (shopAuth != null) {
            Publishers publishers = attributionApiClient.getPublishers(shopAuth,
                    profile.getProfileId(), profile.getMarketplaceId());
            if (publishers != null && CollectionUtils.isNotEmpty(publishers.getPublishers())) {
                List<AmazonAdAttributionPublisher> list = publishers.getPublishers().stream().map(item -> AmazonAdAttributionPublisher.builder().
                        publisherId(item.getId()).publisherName(item.getName())
                        .macroEnable(item.getMacroEnabled() ? 1 : 0).build()).collect(Collectors.toList());
                redisService.set(RedisConstant.SEllFOX_AD_AMAZON_ATTRIBUTION_PUBLISHERS, JSONUtil.objectToJson(list));
                amazonAdAttributionPublisherDao.insertOrUpdate(list);
            }
        }
    }

    @Override
    public List<AmazonAdAttributionPublisher> getPublishers(String name, Boolean macroEnable) {
        String value = redisService.getString(RedisConstant.SEllFOX_AD_AMAZON_ATTRIBUTION_PUBLISHERS);
        if (StringUtils.isNotBlank(value)) {
            List<AmazonAdAttributionPublisher> publishers = JSONUtil.jsonToArray(value, AmazonAdAttributionPublisher.class);
            if (StringUtils.isNotBlank(name) && macroEnable != null) {
                return publishers.stream().filter(item -> item.getPublisherName().equalsIgnoreCase(name))
                        .filter(item -> (item.getMacroEnable() == 1) == macroEnable).collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(name)) {
                return publishers.stream().filter(item -> item.getPublisherName().equalsIgnoreCase(name)).collect(Collectors.toList());
            } else if (macroEnable != null) {
                return publishers.stream().filter(item -> (item.getMacroEnable() == 1) == macroEnable).collect(Collectors.toList());
            }
            return publishers;
        }
        return amazonAdAttributionPublisherDao.list(name, macroEnable);
    }

    @Override
    public AmazonAdAttributionPublisher getPublisherById(String publiserId) {
        String value = redisService.getString(RedisConstant.SEllFOX_AD_AMAZON_ATTRIBUTION_PUBLISHERS);
        if (StringUtils.isNotBlank(value)) {
            List<AmazonAdAttributionPublisher> publishers = JSONUtil.jsonToArray(value, AmazonAdAttributionPublisher.class);
            return publishers.stream().filter(item -> item.getPublisherId().equalsIgnoreCase(publiserId)).limit(1).findAny().orElse(null);
        }
        return amazonAdAttributionPublisherDao.getPublisherById(publiserId);
    }

    @Override
    public AmazonAdAttributionPublisher getPublisherByName(String name) {
        String value = redisService.getString(RedisConstant.SEllFOX_AD_AMAZON_ATTRIBUTION_PUBLISHERS);
        if (StringUtils.isNotBlank(value)) {
            List<AmazonAdAttributionPublisher> publishers = JSONUtil.jsonToArray(value, AmazonAdAttributionPublisher.class);
            Stream<AmazonAdAttributionPublisher> stream = publishers.stream();
            if (StringUtils.isNotBlank(name)) {
                stream.filter(item -> item.getPublisherName().equalsIgnoreCase(name));
            }
            return stream.limit(1).findAny().orElse(null);
        }
        return amazonAdAttributionPublisherDao.getPublisherByName(name);
    }

}
