package com.meiyunji.sponsored.service.multiple.targets.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 投放基础信息
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
public class TargetInfo {

    /**
     * 数据库主键id
     */
    private Long id;

    /**
     * 投放id
     */
    private String targetId;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 站点
     */
    private String marketplaceId;

    /**
     * 广告组id
     */
    private String adGroupId;

    /**
     * 活动id
     */
    private String campaignId;

    /**
     * 关键词投放内容
     */
    private String keywordText;

    /**
     * 商品投放内容
     */
    private String targetText;

    /**
     * 匹配类型  broad, exact, phrase
     */
    private String matchType;

    /**
     * 状态
     */
    private String state;

    /**
     * 服务状态
     */
    private String servingStatus;

    /**
     * 竞价
     */
    private BigDecimal bid;

    /**
     * 竞价建议值，用于刚进编辑页面展示
     */
    private BigDecimal suggested;

    /**
     * 竞价建议范围最小值，用于刚进编辑页面展示
     */
    private BigDecimal rangeStart;

    /**
     * 竞价建议范围最大值，用于刚进编辑页面展示
     */
    private BigDecimal rangeEnd;

    /**
     * 关键词广告排名
     */
    private String advRank;

    /**
     * 商品投放精准：asinSameAs  扩展类型 asinExpandedFrom
     */
    private String selectType;

    /**
     * 商品投放类型：category,asin,negativeAsin
     */
    private String productTargetType;

    /**
     * expression字段的第一个type值作为投放类型
     */
    private String targetType;

    /**
     * sd投放类型
     */
    private String tacticType;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 类目路径
     */
    private String categoryPath;

    /**
     * asin标题（asin投放时）
     */
    private String title;

    /**
     * asin图片（asin投放时）
     */
    private String imgUrl;

    /**
     * [{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
     */
    private String resolvedExpression;
}
