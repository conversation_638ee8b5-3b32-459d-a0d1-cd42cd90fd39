package com.meiyunji.sponsored.service.multiPlatform.walmart.util;

import com.csvreader.CsvReader;

import org.apache.http.client.methods.HttpGet;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.List;

public class ImportCsv {

    public static List<String[]> readCsvFile(String url){
        List<String[]> csvList = new ArrayList<>();
        InputStream in = null;
        CsvReader creader = null;
        HttpGet gm = null;
        try {
            HttpUtil util = new HttpUtil();
            //读取UEL
            gm = new HttpGet();
            in = util.getInputStream(gm, url);
            //读取本地文件
//            creader = new CsvReader(url, ',', Charset.forName("GBK"));
            if(in == null){
                return null;
            }
            creader = new CsvReader(in, ',', Charset.forName("UTF-8"));
            creader.readHeaders(); // 跳过表头   如果需要表头的话，不要写这句。
            while (creader.readRecord()) { //逐行读入除表头的数据
                csvList.add(creader.getValues());
            }
            return csvList;

        } catch (Exception ex) {
            ex.printStackTrace();
            return null;

        } finally {
            if(in != null){
                try {
                    in.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if(gm != null){
                gm.releaseConnection();
            }
            if(creader != null){
                creader.close();
            }
        }
    }

    public static void main(String[] args) throws Throwable {
        HttpUtil util = new HttpUtil();

        ImportCsv aa = new ImportCsv();
        List<String[]> csvList = aa.readCsvFile("https://sweeper-production-merchant-export.s3-us-west-1.amazonaws.com/52bd3ddb34067e4620a4c62d-589a90b95cab4b1871d5f169-2017-02-08-03%3A30%3A01.csv?Signature=xALmsu8%2Bl%2FDxL%2BnWO1sY333dq8c%3D&Expires=1486794547&AWSAccessKeyId=AKIAJFT6XO7RY2S4TSRQ");
//        List<String[]> csvList = aa.readCsvFile("C:\\Users\\<USER>\\Downloads\\52bd3ddb34067e4620a4c62d-589576e6c75d9f75c2d73809-2017-02-04-06-38-30.csv");
    }

}