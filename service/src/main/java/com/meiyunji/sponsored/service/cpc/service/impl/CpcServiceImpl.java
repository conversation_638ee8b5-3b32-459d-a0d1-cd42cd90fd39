package com.meiyunji.sponsored.service.cpc.service.impl;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISellfoxShopUserDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dao.impl.IAmazonAdKeywordsLibDetailDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.ICpcService;
import com.meiyunji.sponsored.service.cpc.service.ICpcSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbKeywordService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbNeKeywordService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeKeywordApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcSuggestedApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignNeKeywords;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignNetargetingSp;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName CpcServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/4/23 17:12
 **/
@Service
public class CpcServiceImpl implements ICpcService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private ICpcSyncService cpcSyncService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcSuggestedApiService cpcSuggestedApiService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao groupDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao neKeywordDao;
    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;
    @Autowired
    private IAmazonSbAdKeywordDao keywordDao;
    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;
    @Autowired
    private IAmazonAdKeywordsLibDetailDao amazonAdKeywordsLibDetailDao;
    @Autowired
    ISellfoxShopUserDao sellfoxShopUserDao;
    @Autowired
    IKeywordDao monitorKeywordDao;
    @Autowired
    IAmazonAdKeywordsLibDao amazonAdKeywordsLibDao;
    @Autowired
    private ICpcSbKeywordService cpcSbKeywordService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private ICpcKeywordsService cpcKeywordsService;
    @Autowired
    private ICpcTargetingService cpcTargetingService;
    @Resource
    private ICpcSbNeKeywordService cpcSbNeKeywordService;

    @Override
    public List<QueryVo> listKeywords(int puid, List<QueryVo> voList) {
        List<String> keywordIdList = voList.stream().map(e->e.getItemId()).distinct().collect(Collectors.toList());
        List<AmazonAdKeyword> list = amazonAdKeywordDaoRoutingService.listKeywords(puid,keywordIdList);
        if(list != null && list.size()>0){
            Map<String,AmazonAdKeyword> map = list.stream().collect(Collectors.toMap(AmazonAdKeyword :: getKeywordId,e->e,(v1,v2)->v1));
            voList.forEach(vo->{
                AmazonAdKeyword po = map.get(vo.getItemId());
                if(po != null){
                    vo.setAdGroupId(po.getAdGroupId());
                    vo.setCampaignId(po.getCampaignId());
                    vo.setItemId(po.getKeywordId());
                    vo.setBid(po.getBid());
                    vo.setType(po.getMatchType());
                }
            });
        }
        return voList;
    }

    /**
     *
     * @param puid
     * @param updateId
     * @param loginIp
     * @param dto
     * @return
     */
    @Override
    public Result createKeywords(Integer puid, Integer updateId, String loginIp, SubmitQueryDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(dto.getShopId(), puid);
        if (shopAuth == null) {
            return ResultUtil.error("没有CPC授权");
        }
        //先获取配置信息
        AmazonAdProfile profile = amazonAdProfileDao.getProfileByMarketId(puid,dto.getShopId(),dto.getMarketplaceId());
        if(profile == null){
            return ResultUtil.error("没有站点对应的配置信息");
        }
        StringBuilder error = new StringBuilder("");
        List<QueryVo> queryVos = dto.getQuerys();
        List<AmazonAdKeyword> keywordList = voToKeyword(updateId,puid,shopAuth.getId(),profile.getProfileId(),shopAuth.getMarketplaceId(),queryVos);
        if(Constants.BIDDABLE.equals(dto.getType())){
            createBiddableKeyword(dto.getShopId(),profile.getProfileId(), dto.getMarketplaceId(),keywordList,error);
        }else if(Constants.NEGATIVE.equals(dto.getType())){
            createNegativeKeyword(dto.getShopId(),profile.getProfileId(), dto.getMarketplaceId(),keywordList,error);
        }else{
            return ResultUtil.error("类型错误");
        }

        /**
         * TODO 关键词投放增加日志
         * 操作类型：搜索词新增（关键词投放/否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        //对提交结果进行处理
        List<Integer> success = new ArrayList<>();
        List<Integer> fail = new ArrayList<>();
        List<AdManageOperationLog> keywordLogs = Lists.newArrayListWithExpectedSize(2);
        List<AmazonAdKeyword> successList = new ArrayList<>();
        StringBuilder stringErrors = new StringBuilder();
        for (AmazonAdKeyword keyword: keywordList) {
            keyword.setType(dto.getType());
            AdManageOperationLog keywordLog = adManageOperationLogService.getkeywordsLog(null, keyword);
            keywordLog.setIp(loginIp);
            keywordLog.setUid(updateId);
            keywordLog.setPuid(puid);
            keywordLog.setMarketplaceId(shopAuth.getMarketplaceId());
            keywordLog.setShopId(shopAuth.getId());
            if(StringUtils.isNotBlank(keyword.getKeywordId())){
                successList.add(keyword);
                success.add(keyword.getIndex());
                keywordLog.setTargetId(keyword.getKeywordId());
                keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }  else {
                fail.add(keyword.getIndex());
                keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                String msg = "添加失败";
                if(StringUtils.isNotBlank(keyword.getError())){
                    msg = keyword.getError();
                }
                String errorStr = "keywordText:" + keyword.getKeywordText() + ",desc:" +msg;
                keywordLog.setResultInfo(errorStr);
                stringErrors.append("第"+(keyword.getIndex()+1)+"条："+errorStr+";");
            }
            keywordLogs.add(keywordLog);
        }
        //原否定关键词和关键词投放在一张表中，现在拆分开了，代码改动量太大，没有好的办法，只能修改逻辑，改代码时请注意！！！
        if(CollectionUtils.isNotEmpty(successList)){
            if(Constants.BIDDABLE.equals(dto.getType())){
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(puid, successList, Constants.BIDDABLE);
                //写入doris
                cpcKeywordsService.saveDoris(puid, shopAuth.getId(), successList.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList()), true);
            }else if(Constants.NEGATIVE.equals(dto.getType())){
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(puid, successList, Constants.NEGATIVE);
            }

        }
        //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);


        Result result = ResultUtil.success();
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(2);
        map.put("success",success);
        map.put("fail",fail);
        if(StringUtils.isNotBlank(stringErrors.toString())){
            result.setMsg(stringErrors.toString());
        }
        if(StringUtils.isNotBlank(error.toString())){
            map.put("failMsg", error.toString());
        }
        result.setData(map);
        return result;
    }

    @Override
    public List<QueryVo> listTargets(int puid, List<QueryVo> voList) {
        List<String> tagertIdList = voList.stream().map(e->e.getItemId()).distinct().collect(Collectors.toList());
        List<AmazonAdTargeting> list = amazonAdTargetDaoRoutingService.listTargets(puid, tagertIdList);
        if(list != null && list.size()>0){
            Map<String,AmazonAdTargeting> map = list.stream().collect(Collectors.toMap(AmazonAdTargeting :: getTargetId,e->e,(v1,v2)->v1));
            voList.forEach(vo->{
                AmazonAdTargeting po = map.get(vo.getItemId());
                if(po != null){
                    vo.setAdGroupId(po.getAdGroupId());
                    vo.setCampaignId(po.getCampaignId());
                    vo.setItemId(po.getTargetId());
                    vo.setBid(po.getBid());
                    vo.setType(po.getType());
                }
            });
        }
        return voList;
    }

    @Override
    public Result createTargets(Integer puid, Integer updateId, String loginIp, SubmitQueryDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(dto.getShopId(), puid);
        if (shopAuth == null) {
            return ResultUtil.error("没有CPC授权");
        }

        //先获取配置信息
        AmazonAdProfile profile = amazonAdProfileDao.getProfileByMarketId(puid,dto.getShopId(),dto.getMarketplaceId());
        if(profile == null){
            return ResultUtil.error("没有站点对应的配置信息");
        }
        List<QueryVo> queryVos = dto.getQuerys();
        List<AmazonAdTargeting> targetings = voToTarget(updateId,puid,shopAuth.getId(),shopAuth.getMarketplaceId(),profile.getProfileId(),dto.getQuerys());

        if(Constants.TARGETING_TYPE_ASIN.equals(dto.getType())){
            cpcSyncService.createTargetV3(dto.getShopId(),profile.getProfileId(),dto.getMarketplaceId(),targetings);

        }else if(Constants.TARGETING_TYPE_NEGATIVEASIN.equals(dto.getType())){
            cpcSyncService.createNegativeTargetV3(dto.getShopId(),profile.getProfileId(),dto.getMarketplaceId(),targetings);

        }else{
            return ResultUtil.error("类型错误");
        }
        /**
         * TODO 商品投放增加日志
         * 操作类型：搜索词新增（商品投放/否定商品投放）
         * 逻辑：获取亚马逊返回的结果，通过targetId获取成功失败结果返回日志
         * start
         */
        StringBuilder stringErrors = new StringBuilder();
        List<AdManageOperationLog> targetLogs = Lists.newArrayListWithExpectedSize(2);
        List<Integer> success = new ArrayList<>();
        List<Integer> fail = new ArrayList<>();
        List<AmazonAdTargeting> successList = new ArrayList<>();
        for (AmazonAdTargeting target: targetings) {
            target.setType(dto.getType());
            if (StringUtils.isNotBlank(target.getExpression())) {
                List<Expression> expressionList = JSONUtil.jsonToArray(target.getExpression(), Expression.class);
                target.setTargetingValue(expressionList.get(0).getValue());
            }
            AdManageOperationLog targetLog = adManageOperationLogService.getTargetsLog(null, target);
            targetLog.setIp(loginIp);
            targetLog.setUid(updateId);
            targetLog.setPuid(puid);
            targetLog.setMarketplaceId(shopAuth.getMarketplaceId());
            targetLog.setShopId(shopAuth.getId());

            if(StringUtils.isNotBlank(target.getTargetId())){
                successList.add(target);
                success.add(target.getIndex());
                targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                targetLog.setTargetId(target.getTargetId());
            } else {
                fail.add(target.getIndex());
                targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                String msg = "添加失败";
                if(StringUtils.isNotBlank(target.getError())){
                    msg = target.getError();
                }
                String errorStr = "targetValue:" + target.getTargetingValue() + ",desc:" +msg;
                targetLog.setResultInfo(errorStr);
                stringErrors.append("第"+(target.getIndex()+1)+"条："+errorStr+";");

            }
            targetLogs.add(targetLog);
        }
        //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
        adManageOperationLogService.batchLogsMergeByAdGroup(targetLogs);
        amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(shopAuth.getPuid(),successList, dto.getType());

        //写入doris
        cpcTargetingService.saveDoris(puid, shopAuth.getId(), successList.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()));

        Result result = ResultUtil.success();
        Map<String,Object> map = Maps.newHashMapWithExpectedSize(2);
        map.put("success",success);
        map.put("fail",fail);
        if(StringUtils.isNotBlank(stringErrors.toString())){
            map.put("failMsg", stringErrors.toString());
        }
        result.setData(map);
        return result;
    }

    @Override
    public Result getKeywordSuggestBid(Integer puid, Integer shopId, String marketplaceId, String adGroupId, List<AmazonAdKeyword> keywords) {
        //获取广告组信息
        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid,shopId,marketplaceId,adGroupId);
        if(amazonAdGroup == null ){
            return ResultUtil.error("没有广告组信息");
        }
        //获取建议竞价
        return cpcSyncService.getSuggestBidByKeyword(amazonAdGroup.getShopId(),amazonAdGroup.getProfileId(),amazonAdGroup.getMarketplaceId(),amazonAdGroup.getAdGroupId(),keywords);
    }

    @Override
    public Result getQuerySuggestBid(Integer puid, Integer shopId, String adGroupId, String query, String type) {
        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(puid, shopId, adGroupId);
        if (amazonAdGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(shop.getMarketplaceId());
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        List<List<Expression>> expressios = new ArrayList<>();
        List<Expression> list = new ArrayList<>();
        Expression expression = new Expression();
        expression.setValue(query);
        expression.setType(type);
        list.add(expression);
        expressios.add(list);
        return cpcSuggestedApiService.getQuerySuggestBid(shop, amazonAdGroup, expressios);
    }

    @Override
    public Map<String, String> getNames(Integer puid, GetNamesDto dto) {
        if(Constants.ITEM_TYPE_CAMPAIGN.equals(dto.getItemType())){
            List<Map<String,Object>> list = amazonAdCampaignDao.getCampaignNames(puid,dto);
            return getItemIdAndNameMap(list);
        }else if(Constants.ITEM_TYPE_GROUP.equals(dto.getItemType())){
            List<Map<String,Object>> list = null;
            if(CampaignTypeEnum.sp.getCampaignType().equals(dto.getType())){
                if (dto.getNegativeKeyword() && StringUtils.isNotBlank(dto.getCampaignId())) {  // 添加否定关键词
                    AmazonAdCampaignAll campaign = amazonAdCampaignDao.getByCampaignId(puid, dto.getShopId(), dto.getMarketplaceId(), dto.getCampaignId());
                    if (campaign != null) {
                        if ("auto".equals(campaign.getTargetingType())) {
                            dto.setAdGroupType(Constants.GROUP_TYPE_AUTO);
                        } else {
                            dto.setAdGroupType(Constants.GROUP_TYPE_KEYWORD);
                        }
                    }
                }
                if (dto.getNegativeProduct() && StringUtils.isNotBlank(dto.getCampaignId())) {  //添加否定投放
                    AmazonAdCampaignAll campaign = amazonAdCampaignDao.getByCampaignId(puid, dto.getShopId(), dto.getMarketplaceId(), dto.getCampaignId());
                    if (campaign != null) {
                        if ("auto".equals(campaign.getTargetingType())) {
                            dto.setAdGroupType(Constants.GROUP_TYPE_AUTO);
                        } else {
                            dto.setAdGroupType(Constants.GROUP_TYPE_TARGETING);
                        }
                    }
                }
                list = amazonAdGroupDao.getAdGroupNames(puid,dto);
            } else if(CampaignTypeEnum.sb.getCampaignType().equals(dto.getType())) {
                list = amazonSbAdGroupDao.getAdGroupNames(puid,dto);
            }

            return getItemIdAndNameMap(list);
        }
        return null;
    }

    @Override
    public List<CampAndGroupVo> getCampAndGroup(int puid, Integer shopId, String marketplaceId) {
        List<AmazonAdCampaignAll> campaigns = amazonAdCampaignDao.getNamesAndState(puid,shopId);
        List<CampAndGroupVo> campAndGroupVos = Lists.newArrayListWithExpectedSize(campaigns.size());

        List<String> campaignList = new ArrayList<>(campaigns.size());
        for (AmazonAdCampaignAll campaign : campaigns) {
            if (StringUtils.isNotBlank(campaign.getCampaignId())) {
                campaignList.add(campaign.getCampaignId());
            }
        }
        Map<String, List<Map<String,Object>>> mapList = new HashMap<>();
        if (campaignList.size() > 0) {
            List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getListByCampaignIds(puid, shopId, campaignList);
            Map<String, Object> groupMap;
            for (AmazonAdGroup adGroup : amazonAdGroupList) {
                if (StringUtils.isBlank(adGroup.getAdGroupId())) {  //过滤为空的广告组
                    continue;
                }
                List<Map<String, Object>> maps = mapList.get(adGroup.getCampaignId());
                if (maps != null && maps.size() > 0) {
                    groupMap = new HashMap<>();
                    groupMap.put("id", adGroup.getAdGroupId());
                    groupMap.put("name", adGroup.getName());
                    maps.add(groupMap);
                    mapList.put(adGroup.getCampaignId(), maps);
                } else {
                    maps = new ArrayList<>();
                    groupMap = new HashMap<>();
                    groupMap.put("id", adGroup.getAdGroupId());
                    groupMap.put("name", adGroup.getName());
                    maps.add(groupMap);
                    mapList.put(adGroup.getCampaignId(), maps);
                }
            }
        }
        if(campaigns.size() > 0){
            campaigns.forEach(e->{
                CampAndGroupVo vo = new CampAndGroupVo();
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getName());
                vo.setState(e.getState());
                List<Map<String, Object>> adGroupMap = mapList.get(e.getCampaignId());
//                List<Map<String,Object>> adGroupMap = amazonAdGroupDao.getAdGroupNames(puid,shopId,marketplaceId,e.getCampaignId());
                vo.setAdGroupMap(getItemIdAndNameMap(adGroupMap));
                campAndGroupVos.add(vo);
            });
        }
        return campAndGroupVos;
    }


    private Map<String, String> getItemIdAndNameMap(List<Map<String,Object>> list){
        if(list != null && list.size()>0){
            Map<String, String> map = Maps.newHashMapWithExpectedSize(list.size());
            list.forEach(temp->{
                map.put(String.valueOf(temp.get("id")),String.valueOf(temp.get("name")));
            });
            return map;
        }
        return null;
    }

    private List<AmazonAdTargeting> voToTarget(Integer uid,Integer puid,Integer shopId,String marketplaceId,String profileId,List<QueryVo> querys) {
        List<AmazonAdTargeting> list = Lists.newArrayListWithExpectedSize(querys.size());
        querys.forEach(vo->{
            AmazonAdTargeting po = new AmazonAdTargeting();
            po.setPuid(puid);
            po.setShopId(shopId);
            po.setMarketplaceId(marketplaceId);
            po.setProfileId(profileId);
            po.setCreateId(uid);
            po.setAdGroupId(vo.getAdGroupId());
            po.setCampaignId(vo.getCampaignId());
            //默认手动
            po.setExpressionType(Constants.MANUAL);
            Expression expression = new Expression();
            if(StringUtils.isNotBlank(vo.getExpressionType())){
                expression.setType(vo.getExpressionType());
            } else {
                expression.setType(ExpressionEnum.asinSameAs.value());
            }
            
            expression.setValue(vo.getItemValue());
            po.setSelectType(expression.getType());
            po.setExpression(JSONUtil.objectToJson(Collections.singleton(expression)));
            po.setBid(vo.getBid());
            po.setIndex(vo.getIndex());
            list.add(po);
        });
        return list;
    }

    private List<AmazonAdKeyword> voToKeyword(Integer uid,Integer puid,Integer shopId,String profileId,String marketplaceId,List<QueryVo> keywords) {
        List<AmazonAdKeyword> list = Lists.newArrayListWithExpectedSize(keywords.size());
        keywords.forEach(vo->{
            AmazonAdKeyword po = new AmazonAdKeyword();
            po.setPuid(puid);
            po.setShopId(shopId);
            po.setMarketplaceId(marketplaceId);
            po.setProfileId(profileId);
            po.setCreateId(uid);
            po.setAdGroupId(vo.getAdGroupId());
            po.setCampaignId(vo.getCampaignId());
            po.setKeywordText(StringUtil.replaceSpecialSymbol(vo.getItemValue()));
            po.setMatchType(vo.getType());
            po.setBid(vo.getBid());
            po.setIndex(vo.getIndex());
            list.add(po);
        });
        return list;
    }

    private List<ApiResponseV3<KeywordSuccessResultV3>> createNegativeKeyword(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> negativeKeywordList, StringBuilder error) {
        List<ApiResponseV3<KeywordSuccessResultV3>> resultList = Lists.newArrayListWithCapacity(negativeKeywordList.size());
        List<AmazonAdKeyword> temp = Lists.newArrayListWithExpectedSize(100);
        Result result;
        //没有上限，只是提交的时候按100条每次分页提交
        for(AmazonAdKeyword keyword : negativeKeywordList){
            temp.add(keyword);
            if(temp.size() == 100){
                result = cpcSyncService.createNegativeKeywordV3(shopId,profileId,marketPlaceId,negativeKeywordList);
                //提取错误信息
                if(result.getCode() == 1){
                    error.append(result.getMsg());
                }
                if(result.getData() != null){
                    resultList.add((ApiResponseV3<KeywordSuccessResultV3>) result.getData());
                }
                temp = Lists.newArrayListWithExpectedSize(100);
            }
        }
        if(temp != null && temp.size()>0){
            result = cpcSyncService.createNegativeKeywordV3(shopId,profileId,marketPlaceId,negativeKeywordList);
            //提取错误信息
            if(result.getCode() == 1){
                error.append(result.getMsg());
            }
            if(result.getData() != null){
                resultList.add((ApiResponseV3<KeywordSuccessResultV3>)result.getData());
            }
        }
        return resultList;
    }

    private List<ApiResponseV3<KeywordSuccessResultV3>> createBiddableKeyword(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> biddableKeywordList, StringBuilder biddableError) {
        List<ApiResponseV3<KeywordSuccessResultV3>> resultList = new ArrayList<>();
        List<AmazonAdKeyword> temp = Lists.newArrayListWithExpectedSize(100);
        Result result;
        //没有上限，只是提交的时候按200条每次分页提交
        for(AmazonAdKeyword keyword : biddableKeywordList){
            temp.add(keyword);
            if(temp.size() == 200){
                result = cpcSyncService.createBiddableKeywordV3(shopId,profileId,marketPlaceId,temp);
                //提取错误信息
                if(result.getCode() == 1){
                    biddableError.append(result.getMsg());
                }
                if(result.getData() != null){
                    resultList.add((ApiResponseV3<KeywordSuccessResultV3>) result.getData());
                }
                temp = Lists.newArrayListWithExpectedSize(200);
            }
        }
        if(temp != null && temp.size()>0){
            result = cpcSyncService.createBiddableKeywordV3(shopId,profileId,marketPlaceId,temp);
            //提取错误信息
            if(result.getCode() == 1){
                biddableError.append(result.getMsg());
            }
            if(result.getData() != null){
                resultList.add((ApiResponseV3<KeywordSuccessResultV3>) result.getData());
            }
        }
        return resultList;
    }
    private void checkGroupPublish(String publishState) throws ServiceException{
        if(Constants.GROUP_PUBLISH_DRAFT.equals(publishState)
                || Constants.GROUP_PUBLISH_PUBLISHING.equals(publishState)
                || Constants.GROUP_PUBLISH_ERROR.equals(publishState)){
            throw new ServiceException("发布中广告组禁止操作");
        }
    }

    @Override
    public Result createKeywordsTarget(AddKeywordDto dto) {
/*        ShopAuth shopAuth = shopAuthDao.getByIdAndPuid(dto.getShopId(), dto.getPuid());
        if (shopAuth == null) {
            return ResultUtil.error("没有CPC授权");
        }
        //先获取配置信息
        AmazonAdProfile profile = amazonAdProfileDao.getProfileByMarketId(dto.getPuid(),dto.getShopId(),dto.getMarketplaceId());
        if(profile == null){
            return ResultUtil.error("没有站点对应的配置信息");
        }*/
        Set<Integer> success = new HashSet<>();
        Set<Integer> fail = new HashSet<>();
        Map<String, AmazonSbAdGroup> sbAdGroupMap = new HashMap<>();
        Map<String,List<Integer>> resultMap = new HashMap<>();
        List<KeywordVo> keywordVoList = dto.getKeywords();

        //TODO 跨店铺添加关键词投放：根据shopId分组，批量请求亚马逊接口添加数据
        //创建的关键词投放以shopId分组
        Map<Integer, List<KeywordVo>> keywordsVoMap = keywordVoList.stream()
                .filter(item -> item.getShopId() != null)
                .collect(Collectors.groupingBy(KeywordVo:: getShopId));
        List<Integer> shopIdsList = keywordVoList.stream().map(KeywordVo::getShopId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //店铺以shopId分组
        Map<Integer, ShopAuth> shopAuthMap = shopAuthDao.listValidByIds(dto.getPuid(), shopIdsList)
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ShopAuth::getId, e -> e, (e1, e2) -> e1));
        //配置以shopId分组
        Map<Integer, AmazonAdProfile> profileMap = amazonAdProfileDao.listByShopIds(Lists.newArrayList(shopIdsList))
                .stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(AmazonAdProfile::getShopId, e -> e, (e1, e2) -> e1));
        //根据shopId分组，获取以campaignId为key的Map
        Map<Integer, List<AmazonSbAdGroup>> sbAdGroupShopIdMap = new HashMap<>();

        /* 排除数据：
        1.排除没有添加关键词的或者部分店铺不存在又或者不存在profile配置
        2.部分不存在keywordText以及匹配类型的关键词*/
        Iterator<KeywordVo> keywordVoIterator = keywordVoList.iterator();
        while (keywordVoIterator.hasNext()) {
            KeywordVo e = keywordVoIterator.next();
            if (CollectionUtils.isEmpty(keywordsVoMap.get(e.getShopId())) || shopAuthMap.get(e.getShopId()) == null || profileMap.get(e.getShopId()) == null) {
                fail.add(e.getIndex());
                keywordVoIterator.remove();
            }
            if (Constants.SB.equalsIgnoreCase(dto.getType()) && Constants.BIDDABLE.equals(dto.getTargetType())) {
                if (StringUtils.isBlank(e.getKeywordText()) || StringUtils.isBlank(e.getMatchType())
                        || StringUtils.isBlank(e.getBid()) || StringUtils.isBlank(e.getCampaignId())) {
                    fail.add(e.getIndex());
                    keywordVoIterator.remove();
                }
            } else {
                if (org.apache.commons.lang.StringUtils.isBlank(e.getKeywordText()) || org.apache.commons.lang.StringUtils.isBlank(e.getMatchType())
                        || org.apache.commons.lang.StringUtils.isBlank(e.getCampaignId())) {
                    fail.add(e.getIndex());
                    keywordVoIterator.remove();
                }
            }
        }

        if (Constants.SB.equalsIgnoreCase(dto.getType())) {
            List<String> adGroupIds = keywordVoList.stream().map(KeywordVo::getAdGroupId).distinct().collect(Collectors.toList());

            long l1 = Instant.now().toEpochMilli();
            List<AmazonSbAdGroup> adGroups = groupDao.getListByShopIdsAndGroupIds(dto.getPuid(), shopIdsList, adGroupIds);
            sbAdGroupShopIdMap = adGroups.stream()
                    .collect(Collectors.groupingBy(AmazonSbAdGroup::getShopId));
            logger.info("获取sb广告组，共耗时{}", Instant.now().toEpochMilli() - l1);

            if (CollectionUtils.isEmpty(adGroups)) {
                return ResultUtil.returnErr("没有广告组信息");
            }
            //排除部分不存在广告组信息以及部分已存在的否定关键词
            Iterator<KeywordVo> it = keywordVoList.iterator();
            KeywordVo next;
            while (it.hasNext()) {
                next = it.next();
                List<AmazonSbAdGroup> adGroupList = sbAdGroupShopIdMap.get(next.getShopId());
                if (CollectionUtils.isEmpty(adGroupList)) {
                    fail.add(next.getIndex());
                    it.remove();
                    continue;
                }
                sbAdGroupMap = adGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity()));
                //移除不存在广告组信息的否定关键词
                AmazonSbAdGroup amazonSbAdGroup = sbAdGroupMap.get(next.getAdGroupId());

                if(amazonSbAdGroup == null){
                    fail.add(next.getIndex());
                    it.remove();
                    continue;
                }
                //排除部分已存在的否定关键词
                if (Constants.NEGATIVE.equals(dto.getTargetType())) {
                    if (neKeywordDao.exist(dto.getPuid(), amazonSbAdGroup.getShopId(), amazonSbAdGroup.getAdGroupId(), next.getKeywordText(), next.getMatchType())) {
                        success.add(next.getIndex());
                        it.remove();
                    }
                }
                if (Constants.BIDDABLE.equals(dto.getTargetType())) {
                    if (keywordDao.exist(dto.getPuid(), amazonSbAdGroup.getShopId(), amazonSbAdGroup.getAdGroupId(), next.getKeywordText(), next.getMatchType())) {
                        success.add(next.getIndex());
                        it.remove();
                    }
                }
            }
        }

        if (keywordVoList.size() == 0) {
            resultMap.put("success",Lists.newArrayList(success));
            resultMap.put("fail",Lists.newArrayList(fail));
            return ResultUtil.success(resultMap);
        }

        //以店铺维度分组批量创建关键词投放数据。
        Result result = ResultUtil.success();
        try {
            if (Constants.SP.equals(dto.getType())) {
                long t1 = Instant.now().toEpochMilli();
                Iterator iterator = shopIdsList.iterator();
                while(iterator.hasNext()) {
                    Integer shopId = (Integer) iterator.next();
                    result = createSpKeywords(keywordsVoMap.get(shopId), dto.getTargetType(), shopAuthMap.get(shopId), profileMap.get(shopId), success, fail);
                }
                logger.info("创建sp关键词投放，共耗时：{}", Instant.now().toEpochMilli() - t1);
            }

            if (Constants.SB.equals(dto.getType())) {
                if (Constants.NEGATIVE.equals(dto.getTargetType())) {
                    long t2 = Instant.now().toEpochMilli();
                    Iterator iterator = shopIdsList.iterator();
                    while(iterator.hasNext()) {
                        Integer shopId = (Integer) iterator.next();
                        result = createSbNeKeywords(keywordsVoMap.get(shopId), dto.getUid(), shopAuthMap.get(shopId), profileMap.get(shopId), success, fail);
                    }
                    logger.info("创建sb否定关键词投放，共耗时：{}", Instant.now().toEpochMilli() - t2);
                }
                if (Constants.BIDDABLE.equals(dto.getTargetType())) {
                    long t3 = Instant.now().toEpochMilli();
                    Iterator iterator = shopIdsList.iterator();
                    while(iterator.hasNext()) {
                        Integer shopId = (Integer) iterator.next();
                        result = createSbKeywords(keywordsVoMap.get(shopId), dto.getUid(), shopAuthMap.get(shopId), profileMap.get(shopId), success, fail);
                    }
                    logger.info("创建sb关键词投放，共耗时：{}", Instant.now().toEpochMilli() - t3);
                }
            }
            //TODO 需求：在关键词库里添加否定非否定投放关键词时，需要更新关键词列表对应关键词的投放以及否定投放的预览数等汇总字段
//            syncAdKeywordLibDetail(dto, keywordVoList, success);
        } catch (Exception e) {
            logger.error("创建关键词失败，报异常:{}", e);
        }
        return result;
    }

    private Result createSpKeywords(List<KeywordVo> keywordVos, String targetType, ShopAuth shopAuth, AmazonAdProfile profile, Set<Integer> success, Set<Integer> fail) {

        StringBuilder error = new StringBuilder("");
        List<AmazonAdKeyword> keywordList = keywordVos.stream().map(item -> {
            AmazonAdKeyword po = new AmazonAdKeyword();
            po.setPuid(shopAuth.getPuid());
            po.setShopId(shopAuth.getId());
            po.setProfileId(profile.getProfileId());
            po.setMarketplaceId(shopAuth.getMarketplaceId());
            po.setAdGroupId(item.getAdGroupId());
            po.setCampaignId(item.getCampaignId());
            po.setKeywordText(StringUtil.replaceSpecialSymbol(item.getKeywordText()));
            po.setMatchType(item.getMatchType());
            po.setType(targetType);
            po.setIndex(item.getIndex());
            if (StringUtils.isNotEmpty(item.getBid())) {
                po.setBid(Double.valueOf(item.getBid()));
            }
            return po;
        }).collect(Collectors.toList());
        if(Constants.BIDDABLE.equals(targetType)){
            createBiddableKeyword(shopAuth.getId(),profile.getProfileId(), shopAuth.getMarketplaceId(),keywordList,error);
        }else if(Constants.NEGATIVE.equals(targetType)){
            createNegativeKeyword(shopAuth.getId(),profile.getProfileId(), shopAuth.getMarketplaceId(),keywordList,error);
        }else{
            return ResultUtil.error("类型错误");
        }

        Result result = ResultUtil.success();
        //对提交结果进行处理

        Map<Integer, KeywordVo> collect = keywordVos.stream().collect(Collectors.toMap(KeywordVo::getIndex, e -> e, (e1, e2) -> e2));
        List<AmazonAdKeyword> successKeywordList = new ArrayList<>();
        for (AmazonAdKeyword keyword : keywordList) {
            KeywordVo keywordVo = collect.get(keyword.getIndex());
            if(StringUtils.isNotBlank(keyword.getKeywordId())){
                keywordVo.setKeywordId(keyword.getKeywordId());
                success.add(keyword.getIndex());
                successKeywordList.add(keyword);
            } else {
                fail.add(keyword.getIndex());
            }

        }
        if(CollectionUtils.isNotEmpty(successKeywordList)){
            if(Constants.BIDDABLE.equals(targetType)){
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(shopAuth.getPuid(), successKeywordList, Constants.BIDDABLE);
                //写入doris
                cpcKeywordsService.saveDoris(shopAuth.getPuid(), shopAuth.getId(), successKeywordList.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList()), true);
            }else if(Constants.NEGATIVE.equals(targetType)){
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(shopAuth.getPuid(), successKeywordList, Constants.NEGATIVE);
            }
        }
        Map<String,List<Integer>> map = Maps.newHashMapWithExpectedSize(2);
        map.put("success",Lists.newArrayList(success));
        map.put("fail",Lists.newArrayList(fail));
        result.setData(map);
        return result;
    }

    private Result createSbNeKeywords(List<KeywordVo> keywordVoList, Integer uid, ShopAuth shopAuth, AmazonAdProfile profile, Set<Integer> success, Set<Integer> fail) {

        List<AmazonSbAdNeKeyword> amazonAdNeKeywords = convertAddSbNeKeywordsVoToPo(uid, keywordVoList);

        Result result = cpcSbNeKeywordApiService.createNeKeywords(shopAuth, profile, amazonAdNeKeywords);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdNeKeyword> succList = amazonAdNeKeywords.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = neKeywordDao.listByKeywordId(shopAuth.getPuid(), shopAuth.getId(), succList.stream()
                    .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                neKeywordDao.batchAdd(shopAuth.getPuid(), succList);
                List<String> collect = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList());
                cpcSbNeKeywordService.saveDoris(shopAuth.getPuid() , shopAuth.getId(), collect);
            } catch (Exception e) {
                logger.error("createSbNeKeyword:", e);
            }

            //创建成功, 需要在同步 获取否定关键词状态
            List<String> keywordIds = new ArrayList<>();
            List<String> campaignIdList = succList.stream().peek(e->{
                if (StringUtils.isNotBlank(e.getKeywordId())) {
                    keywordIds.add(e.getKeywordId());
                }
            }).map(AmazonSbAdNeKeyword::getCampaignId).distinct().collect(Collectors.toList());
            ThreadPoolUtil.getNegativeSyncPool().execute(()->{
                try {
                    cpcSbNeKeywordApiService.syncNeKeywords(shopAuth, StringUtil.joinString(campaignIdList,","), keywordIds);
                } catch (Exception e) {
                    logger.info("添加成功后同步否定异常");
                }
            });


        }

        List<AmazonSbAdNeKeyword> failList = amazonAdNeKeywords.stream().filter(e -> org.apache.commons.lang3.StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(succList)){
            Set<Integer> collect = succList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdNeKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                success.addAll(collect);

                /*TODO 注意:给外层的对象返回keywordId
                key:index,value:keywordId*/
                Map<Integer, String> keywordIdMap = succList.stream().filter(e -> e != null && e.getIndex() != null).collect(Collectors.toMap(AmazonSbAdNeKeyword::getIndex, AmazonSbAdNeKeyword::getKeywordId));
                keywordVoList.stream().filter(item -> collect.contains(item.getIndex())).peek(item -> {
                    if (keywordIdMap.get(item.getIndex()) != null) {
                        String keywordId = keywordIdMap.get(item.getIndex());
                        item.setKeywordId(keywordId);
                    }
                }).collect(Collectors.toList());
            }
        }
        if(CollectionUtils.isNotEmpty(failList)){
            Set<Integer> collect = failList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdNeKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                fail.addAll(collect);
            }
        }
        Map<String,List<Integer>> resultMap = Maps.newHashMapWithExpectedSize(2);
        resultMap.put("success", Lists.newArrayList(success));
        resultMap.put("fail",Lists.newArrayList(fail));
        if (failList.size() == 0) {
            return ResultUtil.success(resultMap);
        }

        StringBuilder errMsg = new StringBuilder("创建失败的否定关键词原因：");
        failList.forEach((e) -> {
            if (org.apache.commons.lang3.StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });
        Result<Object> objectResult = ResultUtil.returnErr(errMsg.toString());
        objectResult.setData(resultMap);
        return objectResult;
    }

    private Result createSbKeywords(List<KeywordVo> keywordVoList, Integer uid, ShopAuth shopAuth, AmazonAdProfile profile, Set<Integer> success, Set<Integer> fail) {

        List<AmazonSbAdKeyword> amazonAdKeywords = convertAddSbKeywordsVoToPo(uid, keywordVoList);

        Result result = cpcSbKeywordApiService.createKeywords(shopAuth, profile, amazonAdKeywords);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdKeyword> succList = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = keywordDao.listByKeywordId(shopAuth.getPuid(), shopAuth.getId(), succList.stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                keywordDao.batchAdd(shopAuth.getPuid(), succList);
            } catch (Exception e) {
                logger.error("createSbKeyword:", e);
            }
            //创建成功, 需要在同步 获取关键词状态
            List<String> campaignIdList = succList.stream().map(AmazonSbAdKeyword::getCampaignId).distinct().collect(Collectors.toList());
            cpcSbKeywordApiService.syncKeywords(shopAuth, StringUtil.joinString(campaignIdList,","));
            //写入doris
            cpcSbKeywordService.saveDoris(shopAuth.getPuid(), shopAuth.getId(), succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));
        }

        List<AmazonSbAdKeyword> failList = amazonAdKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(succList)){
            Set<Integer> collect = succList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                success.addAll(collect);

                /*TODO 注意:给外层的对象返回keywordId
                key:index,value:keywordId*/
                Map<Integer, String> keywordIdMap = succList.stream().filter(e -> e != null && e.getIndex() != null).collect(Collectors.toMap(AmazonSbAdKeyword::getIndex, AmazonSbAdKeyword::getKeywordId));
                keywordVoList.stream().filter(item -> collect.contains(item.getIndex())).peek(item -> {
                    if (keywordIdMap.get(item.getIndex()) != null) {
                        String keywordId = keywordIdMap.get(item.getIndex());
                        item.setKeywordId(keywordId);
                    }
                }).collect(Collectors.toList());
            }
        }
        if(CollectionUtils.isNotEmpty(failList)){
            Set<Integer> collect = failList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                fail.addAll(collect);
            }
        }
        Map<String,List<Integer>> resultMap = Maps.newHashMapWithExpectedSize(2);
        resultMap.put("success",Lists.newArrayList(success));
        resultMap.put("fail",Lists.newArrayList(fail));
        if (failList.size() == 0) {
            return ResultUtil.success(resultMap);
        }

        StringBuilder errMsg = new StringBuilder("创建失败的关键词原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });

        Result<Object> objectResult = ResultUtil.returnErr(errMsg.toString());
        objectResult.setData(resultMap);
        return objectResult;
    }

    // vo -> po
    private List<AmazonSbAdNeKeyword> convertAddSbNeKeywordsVoToPo(Integer uid, List<KeywordVo> neKeywords) {
        List<AmazonSbAdNeKeyword> keywordList = new ArrayList<>(neKeywords.size());
        AmazonSbAdNeKeyword neKeyword;
        for (KeywordVo vo : neKeywords) {
            neKeyword = new AmazonSbAdNeKeyword();
            neKeyword.setIndex(vo.getIndex());
            neKeyword.setPuid(vo.getPuid());
            neKeyword.setShopId(vo.getShopId());
            neKeyword.setMarketplaceId(vo.getMarketplaceId());
            neKeyword.setAdGroupId(vo.getAdGroupId());
            neKeyword.setCampaignId(vo.getCampaignId());
            neKeyword.setKeywordText(vo.getKeywordText());
            neKeyword.setMatchType(vo.getMatchType());
            neKeyword.setCreateId(uid);
            neKeyword.setCreateInAmzup(1);
            keywordList.add(neKeyword);
        }

        return keywordList;
    }

    // vo -> po
    private List<AmazonSbAdKeyword> convertAddSbKeywordsVoToPo(Integer uid, List<KeywordVo> keywords) {
        List<AmazonSbAdKeyword> keywordList = new ArrayList<>(keywords.size());
        for (KeywordVo vo : keywords) {
            keywordList.add(convertAddKeyword(vo,uid));
        }
        return keywordList;
    }

    private AmazonSbAdKeyword convertAddKeyword(KeywordVo vo,Integer uid){
        AmazonSbAdKeyword amazonAdKeyword = new AmazonSbAdKeyword();
        amazonAdKeyword.setPuid(vo.getPuid());
        amazonAdKeyword.setShopId(vo.getShopId());
        amazonAdKeyword.setMarketplaceId(vo.getMarketplaceId());
        amazonAdKeyword.setAdGroupId(vo.getAdGroupId());
        amazonAdKeyword.setCampaignId(vo.getCampaignId());
        amazonAdKeyword.setKeywordText(vo.getKeywordText());
        amazonAdKeyword.setMatchType(vo.getMatchType());
        amazonAdKeyword.setBid(new BigDecimal(vo.getBid()));
        amazonAdKeyword.setIndex(vo.getIndex());

        if (StringUtils.isNotBlank(vo.getSuggested())) {
            amazonAdKeyword.setSuggested(new BigDecimal(vo.getSuggested()));
        }
        if (StringUtils.isNotBlank(vo.getRangeStart())) {
            amazonAdKeyword.setRangeStart(new BigDecimal(vo.getRangeStart()));
        }
        if (StringUtils.isNotBlank(vo.getRangeEnd())) {
            amazonAdKeyword.setRangeEnd(new BigDecimal(vo.getRangeEnd()));
        }
        amazonAdKeyword.setCreateId(uid);
        amazonAdKeyword.setCreateInAmzup(1);
        return amazonAdKeyword;
    }

    //同步关键词库列表(先添加到详情表里，然后再把详情表的数据汇总到列表里)
    private void syncAdKeywordLibDetail(AddKeywordDto dto, List<KeywordVo> keywordVoList, Set<Integer> success) {
        if (success.isEmpty() || keywordVoList.isEmpty()) {
            return;
        }
        /*在添加否定，非否定投放的页面，修改关键词文本keywordText并且是在关键词库表里不存在的名称，导致关键词库详情新增一条数据。
        会先查询关键词库表里是否存在该关键词，不存在舍去。*/
        List<String> keywordTextList = amazonAdKeywordsLibDao.getKeywordText(dto.getPuid(), dto.getUid());
        //往关键词库详情列表adKeywordLibDetail添加数据
        //注意：detailList既要找出创建成功的数据，又要找出已经存在的。
        List<AmazonAdKeywordsLibDetail> detailList = keywordVoList.stream().filter(item -> success.contains(item.getIndex()) && isContainsKeywordIgnoreCase(keywordTextList, item.getKeywordText())).map(item -> {
            AmazonAdKeywordsLibDetail detail = new AmazonAdKeywordsLibDetail();
            detail.setPuid(dto.getPuid());
            detail.setShopId(item.getShopId());
            detail.setMarketplaceId(item.getMarketplaceId());
            detail.setCampaignId(item.getCampaignId());
            detail.setKeywordId(item.getKeywordId());
            detail.setKeywordText(item.getKeywordText());
            detail.setMatchType(item.getMatchType());
            if (StringUtils.isNotEmpty(item.getAdGroupId())) {
                detail.setAdGroupId(item.getAdGroupId());
            }
            detail.setBid(Double.valueOf(StringUtils.isNotEmpty(item.getBid())? item.getBid() : "0.0"));
            detail.setSuggested(Double.valueOf(StringUtils.isNotEmpty(item.getSuggested()) ? item.getSuggested() : "0.0"));
            detail.setType(dto.getType());
            detail.setTargetType(dto.getTargetType());
            //创建默认是开启状态的
            detail.setState("enabled");
            detail.setCreateTime(DateUtil.getTodayDate());
            detail.setUpdateTime(DateUtil.getTodayDate());
            return detail;
        }).collect(Collectors.toList());

        if (detailList.isEmpty()) {
            return;
        }

        long t1 = Instant.now().toEpochMilli();
        amazonAdKeywordsLibDetailDao.insertOnDuplicateKeyUpdate(dto.getPuid(), detailList);
        logger.info("同步新增的关键词的数据，共耗时：{}", Instant.now().toEpochMilli() - t1);
        //把详情表的数据汇总到列表
        List<String> keywordTexts = detailList.stream().map(item -> item.getKeywordText()).distinct().collect(Collectors.toList());
        syncAdKeywordsLibList(dto.getPuid(), dto.getUid(), keywordTexts);
    }

    //List.contains的作用方法(忽略大小写)
    private boolean isContainsKeywordIgnoreCase(List<String> keywordTextList, String keyword) {
        for (String key : keywordTextList) {
            if (keyword.equalsIgnoreCase(key)) {
                return true;
            }
        }
        return false;
    }

    private void syncAdKeywordsLibList(Integer puid, Integer uid, List<String> keywordTexts) {
        List<AmazonAdKeywordsLib> adKeywordsLibs = amazonAdKeywordsLibDao.getListByKeyowrdTexts(puid, uid, keywordTexts);
        if (CollectionUtils.isEmpty(adKeywordsLibs)) {
            return ;
        }
        //获取的汇总数据
        try {
            //获取uid下的shopId
            List<Integer> shopIds = sellfoxShopUserDao.getShopIdListByUser(puid, uid);

            //非否定投放预览
            long t1 = Instant.now().toEpochMilli();
            List<AmazonAdKeywordsLibDetail> keywordsLibVos = amazonAdKeywordsLibDetailDao.getAggregateKeyword(puid,shopIds, keywordTexts, Constants.BIDDABLE);
            Map<String, AmazonAdKeywordsLibDetail> keywordsMap = keywordsLibVos.stream().map(item -> {
                AmazonAdKeywordsLibDetail detail = new AmazonAdKeywordsLibDetail();
                BeanUtils.copyProperties(item, detail);
                detail.setKeywordText(item.getKeywordText().toLowerCase());
                return detail;
            }).collect(Collectors.toMap(AmazonAdKeywordsLibDetail::getKeywordText, Function.identity()));
            logger.info("查询非否定投放预览，共耗时：{}", Instant.now().toEpochMilli() - t1);

            //否定投放预览(包含sp,sb，以及包含活动，广告组)
            long t2 = Instant.now().toEpochMilli();
            List<AmazonAdKeywordsLibDetail> neKeywordsLibVo = amazonAdKeywordsLibDetailDao.getAggregateKeyword(puid,shopIds, keywordTexts, Constants.NEGATIVE);
            Map<String, AmazonAdKeywordsLibDetail> neKeywordsMap = neKeywordsLibVo.stream().map(item -> {
                AmazonAdKeywordsLibDetail neDetail = new AmazonAdKeywordsLibDetail();
                BeanUtils.copyProperties(item, neDetail);
                neDetail.setKeywordText(item.getKeywordText().toLowerCase());
                return neDetail;
            }).collect(Collectors.toMap(AmazonAdKeywordsLibDetail::getKeywordText,Function.identity()));
            logger.info("查询否定投放预览，共耗时：{}", Instant.now().toEpochMilli() - t2);
            //关键词监控数
            long t3 = Instant.now().toEpochMilli();
            List<MonitorKeywordVo> monitorKeywords = monitorKeywordDao.getCountByPuidAndKeywordText(puid, keywordTexts);
            Map<String, MonitorKeywordVo> monKeywordMap = monitorKeywords.stream().map(item -> {
                MonitorKeywordVo vo = new MonitorKeywordVo();
                BeanUtils.copyProperties(item, vo);
                vo.setKeywordText(item.getKeywordText().toLowerCase());
                return vo;
            }).collect(Collectors.toMap(MonitorKeywordVo::getKeywordText, Function.identity()));
            logger.info("查询关键词监控数，共耗时：{}", Instant.now().toEpochMilli() - t3);

            adKeywordsLibs.stream().peek(item -> {
                if (keywordsMap.get(item.getKeywordText().toLowerCase()) != null) {
                    AmazonAdKeywordsLibDetail detail = keywordsMap.get(item.getKeywordText().toLowerCase());
                    item.setTargetNum(Optional.ofNullable(detail.getTargetNum()).orElse(0));
                    item.setSingleQuantity7d(Optional.ofNullable(detail.getSingleQuantity7d()).orElse(0));
                    item.setAverageAcos7d(Optional.ofNullable(detail.getAverageAcos7d()).orElse(BigDecimal.ZERO));
                }
                if (neKeywordsMap.get(item.getKeywordText().toLowerCase()) != null) {
                    AmazonAdKeywordsLibDetail neDetail = neKeywordsMap.get(item.getKeywordText().toLowerCase());
                    item.setNegateTargetNum(Optional.ofNullable(neDetail.getTargetNum()).orElse(0));
                }
                if (monKeywordMap.get(item.getKeywordText().toLowerCase()) != null) {
                    MonitorKeywordVo monDetail = monKeywordMap.get(item.getKeywordText().toLowerCase());
                    item.setKeywordMonitor(Optional.ofNullable(monDetail.getCount()).orElse(0));
                }
            }).collect(Collectors.toList());


            amazonAdKeywordsLibDao.insertOnDuplicateKeyUpdate(puid, adKeywordsLibs);
        } catch (Exception e){
            logger.error("puid={}  uid={} 关键词添加异常{}",puid, uid,e);
        }
    }
}
