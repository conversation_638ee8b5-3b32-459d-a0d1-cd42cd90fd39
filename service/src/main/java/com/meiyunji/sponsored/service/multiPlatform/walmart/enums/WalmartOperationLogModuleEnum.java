package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

public enum WalmartOperationLogModuleEnum {

    /**
     * 一级模块
     */
    WALMART_AD("WALMART_AD","Walmart广告")


    ;


    private String moduleType;

    private String moduleValue;

    public String getModuleType() {
        return moduleType;
    }

    public void setModuleType(String moduleType) {
        this.moduleType = moduleType;
    }

    public String getModuleValue() {
        return moduleValue;
    }

    public void setModuleValue(String moduleValue) {
        this.moduleValue = moduleValue;
    }

    WalmartOperationLogModuleEnum(String moduleType, String moduleValue) {
        this.moduleType = moduleType;
        this.moduleValue = moduleValue;
    }

    public static String getOperationLogModuleEnumModuleValue(String moduleType) {
        WalmartOperationLogModuleEnum[] values = values();
        for (WalmartOperationLogModuleEnum value : values) {
            if (moduleType.equalsIgnoreCase(value.getModuleType())) {
                return value.getModuleValue();
            }
        }
        return null;
    }
}
