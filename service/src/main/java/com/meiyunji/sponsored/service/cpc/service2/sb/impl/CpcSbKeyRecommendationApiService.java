package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.mode.BaseError;
import com.amazon.advertising.sb.entity.keywordRecommendation.KeywordRecommendationClient;
import com.amazon.advertising.sb.entity.keywordRecommendation.ListKeywordRecommendationResponse;
import com.amazon.advertising.sb.entity.keywordRecommendation.SBKeywordSuggestion;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Created by lm on 2021/8/3.
 */
@Component
@Slf4j
public class CpcSbKeyRecommendationApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public Result<List<SBKeywordSuggestion>> getKeywordRecommendationList(ShopAuth shop, AmazonAdProfile amazonAdProfile,
                                                                          List<String> asinList,String url,
                                                                          Integer maxNumSuggestions, String goal,
                                                                          String adFormat) {

        ListKeywordRecommendationResponse response = cpcApiHelper.call(shop, () -> KeywordRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getList(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), asinList, maxNumSuggestions,
                null, url, goal, adFormat, null));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResultList());
        }

        String errMsg;
        BaseError error = response.getError();
        if (error != null && StringUtils.isNotBlank(error.getDetails())) {
            errMsg = error.getDetails();
        } else {
            errMsg = response.getStatusMessage();
        }
        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }

    public Result<List<SBKeywordSuggestion>> getKeywordRecommendationListNew(ShopAuth shop, AmazonAdProfile amazonAdProfile,
                                                                          List<String> asinList,String url,
                                                                          Integer maxNumSuggestions, String goal,
                                                                          String adFormat, List<String> creativeAsins) {

        ListKeywordRecommendationResponse response = cpcApiHelper.call(shop, () -> KeywordRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getList(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), asinList, maxNumSuggestions,
                null, url, goal, adFormat, creativeAsins));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResultList());
        }

        String errMsg;
        BaseError error = response.getError();
        if (error != null && StringUtils.isNotBlank(error.getDetails())) {
            errMsg = error.getDetails();
        } else {
            errMsg = response.getStatusMessage();
        }
        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }
}
