package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISellfoxShopUserDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.bo.AdKeywordOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.KeywordsLibBo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dao.impl.IAmazonAdKeywordsLibDetailDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAdKeywordLibTagService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcKeywordsLibService;
import com.meiyunji.sponsored.service.cpc.service2.IKeywordsLibAndAsinLibLimitService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;

import com.meiyunji.sponsored.service.dataWarehouse.service.SearchAnalysisStatsV2Client;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroup;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import com.meiyunji.sponsored.service.feign.param.CountByPuidAndKeywordTextParam;
import com.meiyunji.sponsored.service.feign.service.KeywordRankMonitorFeignService;
import com.meiyunji.sponsored.service.newDashboard.enums.KeywordDataFieldEnum;
import com.meiyunji.sponsored.service.searchAnalysis.po.SearchTermsAnalysis;

import com.meiyunji.sponsored.service.searchTermsAnalysis.SearchTermsAnalysisInterface;
import com.meiyunji.sponsored.service.searchTermsAnalysis.SearchTermsAnalysisStrategy;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.searchTermsAnalysis.qo.SearchTermsAnalysisTrendQo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.SearchTermsListTrendsVo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.dto.MarketplaceStartDateDto;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.MarketplaceRankVo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.MuiltMarketplaceRankVo;
import com.meiyunji.sponsored.service.util.AdPageUtil;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.redisson.misc.Hash;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.util.CollectionSplitUtil.splitList;


/**
 * Created by lm on 2021/8/3.
 */
@Service
@Slf4j
public class CpcKeywordsLibServiceImpl implements ICpcKeywordsLibService {

    @Autowired
    IAmazonAdKeywordsLibDetailDao amazonAdKeywordsLibDetailDao;
    @Autowired
    IAmazonAdKeywordsLibDao amazonAdKeywordsLibDao;
    @Autowired
    ISellfoxShopUserDao sellfoxShopUserDao;
    @Autowired
    IKeywordDao keywordDao;
    @Autowired
    IScVcShopAuthDao shopAuthDao;
    @Autowired
    IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    IAdKeywordLibTagDao adKeywordLibTagDao;
    @Autowired
    IAdKeywordLibMarkupTagDao adKeywordLibMarkupTagDao;
    @Autowired
    SearchAnalysisStatsV2Client searchAnalysisStatsV2Client;
    @Resource
    private IAdKeywordLibMarkupAsinDao adKeywordLibMarkupAsinDao;
    @Autowired
    private IOdsAmazonAdKeywordReportDao odsAmazonAdKeywordReportDao;
    @Autowired
    private IOdsAmazonAdNeKeywordSbDao odsAmazonAdNeKeywordSbDao;
    @Autowired
    private IOdsAmazonAdKeywordSbDao odsAmazonAdKeywordSbDao;
    @Autowired
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;

    @Autowired
    private IOdsAmazonAdGroupDao odsAmazonAdGroupDao;

    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Autowired
    private IAmazonAdPortfolioService portfolioService;
    @Autowired
    private IOdsAmazonAdSbKeywordReportDao odsAmazonAdSbKeywordReportDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNeKeywordDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;
    @Autowired
    private SearchTermsAnalysisStrategy searchTermsAnalysisStrategy;
    @Autowired
    private IOdsWeekSearchTermsAnalysisDao odsWeekSearchTermsAnalysisDao;
    @Autowired
    private IAdKeywordLibTagService adKeywordLibTagService;
    @Autowired
    private IKeywordsLibAndAsinLibLimitService keywordsLibAndAsinLibLimitService;
    @Autowired
    private KeywordRankMonitorFeignService keywordRankMonitorFeignService;


    //计算百分比
    public static final BigDecimal RATE = BigDecimal.valueOf(100L);

    @Override
    public Result<KeywordLibsPageListVo> pageList(KeywordLibsPageParam param) {
        if (StringUtils.isNotEmpty(param.getRemark()) || StringUtils.isNotEmpty(param.getSource())) {
            param.setUidList(Collections.singletonList(param.getUid()));//当筛选来源和备注时，只筛选当前uid的数据
        }
        //puid添加关键词最多添加10000
        int keywordCount = amazonAdKeywordsLibDao.getKeywordsCountByPuid(param.getPuid());
        KeywordLibsPageListVo keywordLibsPageListVo = new KeywordLibsPageListVo();
        keywordLibsPageListVo.setKeywordCount(keywordCount);
        List<Long> keywordIdList = null;
        if (CollectionUtils.isNotEmpty(param.getAdTagIds())) {
            keywordIdList = adKeywordLibTagService.getRelationIdsByTagFilter(param.getPuid(), param.getUid(),
                    AdTagTypeEnum.KEYWORDLIB.getType(), param.getAdTagIds(), param.getAdTagQueryType());
            if (CollectionUtils.isEmpty(keywordIdList)) {
                keywordLibsPageListVo.setPage(new Page<>(param.getPageNo(), param.getPageSize()));
                return ResultUtil.returnSucc(keywordLibsPageListVo);
            }
        }
        //若有标记ASIN筛选并且标记标签筛选后有id，根据ASIN查出过滤的id
        if (StringUtils.isNotBlank(param.getAsin()) && StringUtils.isNotBlank(param.getMarketplaceId())) {
            List<Long> ids = adKeywordLibMarkupAsinDao.keywordsLibIdListByAsin(param.getPuid(), param.getUidList(), param.getMarketplaceId(), param.getAsin());
            //为空清空id列表，后续直接返回空列表页。不为空则与标记标签的id取交集
            if (CollectionUtils.isEmpty(ids)) {
                keywordLibsPageListVo.setPage(new Page<>(param.getPageNo(), param.getPageSize()));
                return ResultUtil.returnSucc(keywordLibsPageListVo);
            }
            if (CollectionUtils.isNotEmpty(keywordIdList)) {
                keywordIdList.retainAll(ids);
                if (CollectionUtils.isEmpty(keywordIdList)) {
                    keywordLibsPageListVo.setPage(new Page<>(param.getPageNo(), param.getPageSize()));
                    return ResultUtil.returnSucc(keywordLibsPageListVo);
                }
            } else {
                keywordIdList = ids;
            }
        }
        if (CollectionUtils.isNotEmpty(keywordIdList)){
            List<KeywordsLibBo> keywordsLibBos = amazonAdKeywordsLibDao.getKeywordsLibBoByIds(param.getPuid(), keywordIdList, param.getUidList());
            if (CollectionUtils.isNotEmpty(keywordsLibBos)) {
                param.setKeyTagText(keywordsLibBos.stream().map(KeywordsLibBo::getKeywordText).distinct().collect(Collectors.toList()));
            } else {
                keywordLibsPageListVo.setPage(new Page<>(param.getPageNo(), param.getPageSize()));
                return ResultUtil.returnSucc(keywordLibsPageListVo);
            }
            keywordIdList = null;
        }

        //查询列表页所选页数展示的关键词
        Page<KeywordLibsVo> keywordPage = new Page<>();
        if (StringUtils.isBlank(param.getOrderField()) || StringUtils.isBlank(param.getOrderType()) || "createTime".equals(param.getOrderField())
                || "singleQuantity7d".equals(param.getOrderField()) || "averageAcos7d".equals(param.getOrderField())) {
            //1.无排序
            keywordPage = amazonAdKeywordsLibDao.getKeywordText(param.getUid(), param, keywordIdList);
            List<String> searchTerms = keywordPage.getRows().stream().map(KeywordLibsVo::getKeywordText).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(searchTerms)) {
                List<OdsWeekSearchTermsAnalysis> weekSearchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, param.getSite());
                Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = weekSearchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o, n) -> n));
                keywordPage.getRows().forEach(each -> {
                    OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(each.getKeywordText().toLowerCase());
                    if (searchTermsAnalysis != null) {
                        each.setSearchFrequencyRank(Optional.ofNullable(searchTermsAnalysis.getSearchFrequencyRank()).orElse(0));
                        each.setWeekRatio(Optional.ofNullable(searchTermsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                });
            }
        } else {
            //2.若是报告指标排序
            this.getOrderByReportPage(param, keywordIdList, keywordPage);
        }

        List<KeywordLibsVo> rows = keywordPage.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            keywordLibsPageListVo.setPage(new Page<>(param.getPageNo(), param.getPageSize()));
            return ResultUtil.returnSucc(keywordLibsPageListVo);
        }
        List<String> keywords = rows.stream().map(KeywordLibsVo::getKeywordText).collect(Collectors.toList());

        Map<String, Integer> keywordCountMap = new HashMap<>();
        //投放数量，内存中汇总计算
        List<KeywordLibsVo> keywordCountList = odsAmazonAdKeywordDao.getSpKeywordCount(param.getPuid(), param.getShopIdList(), param, keywords);
        keywordCountList.addAll(odsAmazonAdKeywordSbDao.getSbKeywordCount(param.getPuid(), param.getShopIdList(), param, keywords));
        for (KeywordLibsVo keywordVo : keywordCountList) {
            if (keywordCountMap.containsKey(keywordVo.getKeywordText().toLowerCase())) {
                keywordCountMap.put(keywordVo.getKeywordText().toLowerCase(), keywordVo.getTargetNum() + keywordCountMap.get(keywordVo.getKeywordText().toLowerCase()));
            } else {
                keywordCountMap.put(keywordVo.getKeywordText().toLowerCase(), keywordVo.getTargetNum());
            }
        }
        Map<String, Integer> nekeywordCountMap = new HashMap<>();
        //否定投放数量，内存中汇总计算(这里查Doris可能会出现数据不一致的情况，改成查MySQL)
        List<KeywordLibsVo> neKeywordCont = amazonAdCampaignNeKeywordsDao.getCampaignNeKeywordCount(param.getPuid(), param.getShopIdList(), param, keywords);
        neKeywordCont.addAll(amazonAdNeKeywordDao.getSpNeKeywordCount(param.getPuid(), param.getShopIdList(), param, keywords));
        neKeywordCont.addAll(amazonSbAdNeKeywordDao.getSbNeKeywordCount(param.getPuid(), param.getShopIdList(), param, keywords));
        for (KeywordLibsVo keywordVo : neKeywordCont) {
            if (nekeywordCountMap.containsKey(keywordVo.getKeywordText().toLowerCase())) {
                nekeywordCountMap.put(keywordVo.getKeywordText().toLowerCase(), keywordVo.getNegateTargetNum() + nekeywordCountMap.get(keywordVo.getKeywordText().toLowerCase()));
            } else {
                nekeywordCountMap.put(keywordVo.getKeywordText().toLowerCase(), keywordVo.getNegateTargetNum());
            }
        }

        Map<String, KeywordLibsVo> reportCompDataMap = new HashMap<>();
        Map<String, KeywordLibsVo> reportDataMap = new HashMap<>();
        if (StringUtils.isNotBlank(param.getTo())) {
            //报告数据(对比指标也是同样方式)，取前端传入的币种，内存中汇总计算
            List<KeywordLibsVo> spKeywordReportData = odsAmazonAdKeywordReportDao.getSpKeywordReportData(param.getPuid(), param.getShopIdList(), param, keywords);
            List<KeywordLibsVo> sbKeywordReportData = odsAmazonAdSbKeywordReportDao.getSbKeywordReportData(param.getPuid(), param.getShopIdList(), param, keywords);
            Map<String, KeywordLibsVo> map = new HashMap<>();
            for (KeywordLibsVo k : spKeywordReportData) {
                map.put(k.getKeywordText().toLowerCase(), null);
            }
            Map<String, KeywordLibsVo> voMap = mergeKeywordData(map, spKeywordReportData);
            reportDataMap = mergeKeywordData(voMap, sbKeywordReportData);

            //报告数据---对比指标查询
            List<KeywordLibsVo> spCompKeywordReportData;
            List<KeywordLibsVo> sbCompKeywordReportData;
            if (StringUtils.isNotBlank(param.getCompareStartDate())) {
                spCompKeywordReportData = odsAmazonAdKeywordReportDao.getSpCompKeywordReportData(param.getPuid(), param.getShopIdList(), param, keywords);
                sbCompKeywordReportData = odsAmazonAdSbKeywordReportDao.getSbCompKeywordReportData(param.getPuid(), param.getShopIdList(), param, keywords);
                Map<String, KeywordLibsVo> compMap = new HashMap<>();
                for (KeywordLibsVo k : spCompKeywordReportData) {
                    compMap.put(k.getKeywordText().toLowerCase(), null);
                }
                Map<String, KeywordLibsVo> voCompMap = mergeKeywordData(compMap, spCompKeywordReportData);
                reportCompDataMap = mergeKeywordData(voCompMap, sbCompKeywordReportData);
            }
        }

        List<KeywordLibsVo> list = amazonAdKeywordsLibDao.listByUidListAndKeywordText(param.getPuid(), param.getUidList(), keywords);
        Map<Integer, User> mapUser = userDao.listByIds(param.getPuid(), list.stream().map(KeywordLibsVo::getUid)
                .collect(Collectors.toList())).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        //查询基础数据
        Map<String, List<KeywordLibsVo>> map = list
                .stream().collect(Collectors.groupingBy(k -> k.getKeywordText().toLowerCase()));
        //获取关键词监控数量
        List<String> keywordsMonitor = new ArrayList<>(map.keySet());
        List<MonitorKeywordVo> monitorKeywords = new ArrayList<>();
        keywordsMonitor.forEach(i -> {
            MonitorKeywordVo monitorKeywordVo = new MonitorKeywordVo();
            monitorKeywordVo.setKeywordText(i);
            monitorKeywordVo.setUid(param.getUid());
            monitorKeywords.add(monitorKeywordVo);
        });
        this.getKeywordMonitor(param.getPuid(), monitorKeywords);
        Map<String, MonitorKeywordVo>monitorMap = monitorKeywords.stream()
                .collect(Collectors.toMap(v -> v.getKeywordText().toLowerCase(), v -> v));
        for (KeywordLibsVo vo : rows) {
            this.buildKeywordLibsPageVo(param, keywordCountMap, nekeywordCountMap, reportDataMap, reportCompDataMap, map, vo, mapUser, monitorMap);
        }
        fillAdTagData(param.getPuid(), param.getUid(), rows);
        //若没有筛选ASIN标签，则填充ASIN标签列表
//        if (StringUtils.isBlank(param.getAsin()) && StringUtils.isBlank(param.getMarketplaceId())) {
//            this.fillAsinTagList(param.getPuid(), param.getUidList(), rows);
//        }
        this.fillAsinTagList(param.getPuid(), param.getUidList(), rows);
        //获取ABA排名以及周排名变化率
//        fillABARankField(keywordPage.getRows());
        // 添加搜索词排名字段，仅调接口获取
//        addSearchTermRankField(rows);

        //二期列表页返回值
        keywordLibsPageListVo.setPage(keywordPage);
        return ResultUtil.returnSucc(keywordLibsPageListVo);
    }

    /**
     * 对查询结果进行合并处理
     *
     * @param toBeMergeList
     */
    private Map<String, AdKeywordOrderBo> mergeOrderData(Map<String, AdKeywordOrderBo> map, List<AdKeywordOrderBo> toBeMergeList) {
        for (AdKeywordOrderBo keywordList : toBeMergeList) {
            AdKeywordOrderBo existingKeyword = map.get(keywordList.getKeywordText());
            if (existingKeyword != null) {
                if (keywordList.getImpressions() != null) {
                    //曝光量
                    existingKeyword.setImpressions(existingKeyword.getImpressions() + keywordList.getImpressions());
                }
                if (keywordList.getAdSales() != null) {
                    //广告销售额
                    existingKeyword.setAdSales(existingKeyword.getAdSales().add(keywordList.getAdSales()));
                }
                if (keywordList.getClicks() != null) {
                    //广告点击量
                    existingKeyword.setClicks(existingKeyword.getClicks() + keywordList.getClicks());
                }
                if (existingKeyword.getAdOrder() != null) {
                    //广告订单量
                    existingKeyword.setAdOrder(existingKeyword.getAdOrder() + keywordList.getAdOrder());
                }
                if (keywordList.getCost() != null) {
                    //广告花费
                    existingKeyword.setCost(existingKeyword.getCost().add(keywordList.getCost()));
                }
                map.put(keywordList.getKeywordText(), existingKeyword);
            } else {
                map.put(keywordList.getKeywordText(), keywordList);
            }
        }
        return map;
    }

    /**
     * 对查询结果进行合并处理
     *
     * @param toBeMergeList
     */
    private Map<String, KeywordLibsVo> mergeKeywordData(Map<String, KeywordLibsVo> map, List<KeywordLibsVo> toBeMergeList) {
        for (KeywordLibsVo keywordList : toBeMergeList) {
            KeywordLibsVo existingKeyword = map.get(keywordList.getKeywordText().toLowerCase());
            if (existingKeyword != null) {
                if (keywordList.getImpressions() != null) {
                    //曝光量
                    existingKeyword.setImpressions(existingKeyword.getImpressions() + keywordList.getImpressions());
                }
                if (keywordList.getTotalSales() != null) {
                    //广告销售额
                    existingKeyword.setTotalSales(existingKeyword.getTotalSales().add(keywordList.getTotalSales()));
                }
                if (keywordList.getClicks() != null) {
                    //广告点击量
                    existingKeyword.setClicks(existingKeyword.getClicks() + keywordList.getClicks());
                }
                if (existingKeyword.getImpressions() != null && existingKeyword.getImpressions() != 0) {
                    //广告点击率
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(existingKeyword.getClicks()), BigDecimal.valueOf(existingKeyword.getImpressions()));
                    existingKeyword.setClickRate(MathUtil.multiply(divide, RATE));
                }
                if (existingKeyword.getSaleNum() != null) {
                    //广告订单量
                    existingKeyword.setSaleNum(existingKeyword.getSaleNum() + keywordList.getSaleNum());
                }
                if (existingKeyword.getSaleNum() != null && existingKeyword.getClicks() != null && existingKeyword.getClicks() != 0) {
                    //广告转化率
                    existingKeyword.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(existingKeyword.getSaleNum()), BigDecimal.valueOf(existingKeyword.getClicks())), RATE));
                }
                if (keywordList.getCost() != null) {
                    //广告花费
                    existingKeyword.setCost(existingKeyword.getCost().add(keywordList.getCost()));
                }
                if (existingKeyword.getCost() != null && existingKeyword.getClicks() != null && existingKeyword.getClicks() != 0) {
                    //cpc
                    existingKeyword.setCpc(MathUtil.divideCompatibleZero(existingKeyword.getCost(), BigDecimal.valueOf(existingKeyword.getClicks())));
                }
                if (existingKeyword.getSaleNum() != null && existingKeyword.getSaleNum() != 0) {
                    //cpa
                    existingKeyword.setCpa(MathUtil.divideCompatibleZero(existingKeyword.getCost(), BigDecimal.valueOf(existingKeyword.getSaleNum())));
                }
                if (existingKeyword.getTotalSales() != null && existingKeyword.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    existingKeyword.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(existingKeyword.getCost(), existingKeyword.getTotalSales()), RATE));
                }
                if (existingKeyword.getCost() != null && existingKeyword.getCost().compareTo(BigDecimal.ZERO) != 0) {
                    //roas
                    existingKeyword.setRoas(MathUtil.divideCompatibleZero(existingKeyword.getTotalSales(), existingKeyword.getCost()));
                }
                map.put(keywordList.getKeywordText().toLowerCase(), existingKeyword);
            } else {
                if (keywordList.getImpressions() != null && keywordList.getImpressions() != 0) {
                    //广告点击率
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(keywordList.getClicks()), BigDecimal.valueOf(keywordList.getImpressions()));
                    keywordList.setClickRate(MathUtil.multiply(divide, RATE));
                }
                if (keywordList.getClicks() != null && keywordList.getClicks() != 0) {
                    //广告转化率
                    keywordList.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(keywordList.getSaleNum()), BigDecimal.valueOf(keywordList.getClicks())), RATE));
                }
                if (keywordList.getClicks() != null && keywordList.getClicks() != 0) {
                    //cpc
                    keywordList.setCpc(MathUtil.divideCompatibleZero(keywordList.getCost(), BigDecimal.valueOf(keywordList.getClicks())));
                }
                if (keywordList.getSaleNum() != null && keywordList.getSaleNum() != 0) {
                    //cpa
                    keywordList.setCpa(MathUtil.divideCompatibleZero(keywordList.getCost(), BigDecimal.valueOf(keywordList.getSaleNum())));
                }
                if (keywordList.getTotalSales() != null && keywordList.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    keywordList.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(keywordList.getCost(), keywordList.getTotalSales()), RATE));
                }
                if (keywordList.getCost() != null && keywordList.getCost().compareTo(BigDecimal.ZERO) != 0) {
                    //roas
                    keywordList.setRoas(MathUtil.divideCompatibleZero(keywordList.getTotalSales(), keywordList.getCost()));
                }
                map.put(keywordList.getKeywordText().toLowerCase(), keywordList);
            }
        }
        return map;
    }

    /**
     * 对查询结果进行合并处理
     *
     * @param toBeMergeList
     */
    private Map<String, KeywordLibsDetailVo> mergeKeywordReportData(Map<String, KeywordLibsDetailVo> map, List<KeywordLibsDetailVo> toBeMergeList) {
        for (KeywordLibsDetailVo keywordList : toBeMergeList) {
            KeywordLibsDetailVo existingKeyword = map.get(keywordList.getKeywordId());
            if (existingKeyword != null) {
                if (keywordList.getImpressions() != null) {
                    //曝光量
                    existingKeyword.setImpressions(existingKeyword.getImpressions() + keywordList.getImpressions());
                }
                if (keywordList.getTotalSales() != null) {
                    //广告销售额
                    existingKeyword.setTotalSales(existingKeyword.getTotalSales().add(keywordList.getTotalSales()));
                }
                if (keywordList.getClicks() != null) {
                    //广告点击量
                    existingKeyword.setClicks(existingKeyword.getClicks() + keywordList.getClicks());
                }
                if (existingKeyword.getImpressions() != null && existingKeyword.getImpressions() != 0) {
                    //广告点击率
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(existingKeyword.getClicks()), BigDecimal.valueOf(existingKeyword.getImpressions()));
                    existingKeyword.setClickRate(MathUtil.multiply(divide, RATE));
                }
                if (existingKeyword.getSaleNum() != null) {
                    //广告订单量
                    existingKeyword.setSaleNum(existingKeyword.getSaleNum() + keywordList.getSaleNum());
                }
                if (existingKeyword.getClicks() != null && existingKeyword.getClicks() != 0) {
                    //广告转化率
                    existingKeyword.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(existingKeyword.getSaleNum()), BigDecimal.valueOf(existingKeyword.getClicks())), RATE));
                }
                if (keywordList.getCost() != null) {
                    //广告花费
                    existingKeyword.setCost(existingKeyword.getCost().add(keywordList.getCost()));
                }
                if (existingKeyword.getClicks() != null && existingKeyword.getClicks() != 0) {
                    //cpc
                    existingKeyword.setCpc(MathUtil.divideCompatibleZero(existingKeyword.getCost(), BigDecimal.valueOf(existingKeyword.getClicks())));
                }
                if (existingKeyword.getSaleNum() != null && existingKeyword.getSaleNum() != 0) {
                    //cpa
                    existingKeyword.setCpa(MathUtil.divideCompatibleZero(existingKeyword.getCost(), BigDecimal.valueOf(existingKeyword.getSaleNum())));
                }
                if (existingKeyword.getTotalSales() != null && existingKeyword.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    existingKeyword.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(existingKeyword.getCost(), existingKeyword.getTotalSales()), RATE));
                }
                if (existingKeyword.getCost() != null && existingKeyword.getCost().compareTo(BigDecimal.ZERO) != 0) {
                    //roas
                    existingKeyword.setRoas(MathUtil.divideCompatibleZero(existingKeyword.getTotalSales(), existingKeyword.getCost()));
                }
                map.put(keywordList.getKeywordId(), existingKeyword);
            } else {
                if (keywordList.getImpressions() != null && keywordList.getImpressions() != 0) {
                    //广告点击率
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(keywordList.getClicks()), BigDecimal.valueOf(keywordList.getImpressions()));
                    keywordList.setClickRate(MathUtil.multiply(divide, RATE));
                }
                if (keywordList.getClicks() != null && keywordList.getClicks() != 0) {
                    //广告转化率
                    keywordList.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(keywordList.getSaleNum()), BigDecimal.valueOf(keywordList.getClicks())), RATE));
                }
                if (keywordList.getClicks() != null && keywordList.getClicks() != 0) {
                    //cpc
                    keywordList.setCpc(MathUtil.divideCompatibleZero(keywordList.getCost(), BigDecimal.valueOf(keywordList.getClicks())));
                }
                if (keywordList.getSaleNum() != null && keywordList.getSaleNum() != 0) {
                    //cpa
                    keywordList.setCpa(MathUtil.divideCompatibleZero(keywordList.getCost(), BigDecimal.valueOf(keywordList.getSaleNum())));
                }
                if (keywordList.getTotalSales() != null && keywordList.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    keywordList.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(keywordList.getCost(), keywordList.getTotalSales()), RATE));
                }
                if (keywordList.getCost() != null && keywordList.getCost().compareTo(BigDecimal.ZERO) != 0) {
                    //roas
                    keywordList.setRoas(MathUtil.divideCompatibleZero(keywordList.getTotalSales(), keywordList.getCost()));
                }
                map.put(keywordList.getKeywordId(), keywordList);
            }
        }
        return map;
    }

    private void fillABARankField(List<KeywordLibsVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        //只获取当前页关键词里的搜索词排名及周变化率字段
        List<String> searchTermList = rows.stream()
                .map(item -> item.getKeywordText().trim().toLowerCase())
                .distinct()
                .collect(Collectors.toList());
        String searchTerms = StringUtil.joinString(searchTermList, StringUtil.SPLIT_COMMA);

        // 调取stateV2接口获取搜索词排名字段
        List<SearchTermsAnalysis> searchTermsAnalysisList = Lists.newArrayList();
        // 抛异常不捕获，避免影响程序执行
        try {
            log.info("start request stateV2 searchTermsAnalysis api");
            long s1 = Instant.now().toEpochMilli();
            searchTermsAnalysisList = searchAnalysisStatsV2Client.getSearchTermAnalysis(searchTerms);
            log.info("调用stateV2接口获取搜索词排名及周变化率，共耗时：{}", Instant.now().toEpochMilli() - s1);
        } finally {
            log.info("end request stateV2 searchTermsAnalysis api");
        }
        if (CollectionUtils.isEmpty(searchTermsAnalysisList)) {
            return;
        }
        Map<String, SearchTermsAnalysis> searchTermsAnalysisMap = searchTermsAnalysisList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(item -> item.getSearchTerm().trim().toLowerCase(), item -> item, (e1, e2) -> e1));
        rows.stream().peek(item -> {
            if (StringUtils.isNotBlank(item.getKeywordText()) && searchTermsAnalysisMap.get(item.getKeywordText().trim().toLowerCase()) != null) {
                SearchTermsAnalysis termsAnalysis = searchTermsAnalysisMap.get(item.getKeywordText().trim().toLowerCase());
                item.setSearchFrequencyRank(termsAnalysis.getSearchFrequencyRank());
                item.setWeekRatio(Optional.ofNullable(termsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            }
        }).collect(Collectors.toList());
    }

    @Override
    public Result<List<KeywordTimeVo>> getRankTrend(KeywordTrendParam param) {
        List<KeywordTimeVo> keywordLibsVos = amazonAdKeywordsLibDao.getKeywordTextTime(param.getPuid(), param.getUid(), param);
        Map<String, List<KeywordTimeVo>> map = keywordLibsVos.stream().collect(Collectors.groupingBy(k -> k.getKeywordText().toLowerCase()));
        List<KeywordTimeVo> result = new ArrayList<>(map.size());
        map.forEach((key, value) -> {
            // 排序从小到大
            value.sort(Comparator.comparing(KeywordTimeVo::getId));
            Optional<KeywordTimeVo> keywordTimeVo = value.stream().filter(k -> k.getUid().equals(param.getUid())).findFirst();
            result.add(keywordTimeVo.orElse(value.get(0)));
        });
        if (StringUtils.isBlank(param.getTrendType())) {
            return ResultUtil.returnSucc(result);
        }
        SearchTermsAnalysisInterface searchTermsAnalysisService = searchTermsAnalysisStrategy.getServiceByPeriodType(param.getTrendType());
        if (searchTermsAnalysisService == null) {
            return ResultUtil.returnSucc(result);
        }
        SearchTermsAnalysisTrendQo searchTermsAnalysisTrendQo = new SearchTermsAnalysisTrendQo();
        searchTermsAnalysisTrendQo.setSearchTerms(param.getKeywordTexts());
        searchTermsAnalysisTrendQo.setDepartmentNames(param.getDepartments());
        searchTermsAnalysisTrendQo.setEndDate(param.getEndDate());
        searchTermsAnalysisTrendQo.setMarketplaceId(param.getMarketplaceId());
        List<SearchTermsListTrendsVo> trends = searchTermsAnalysisService.getTrend(searchTermsAnalysisTrendQo);

        Map<String, KeywordTimeVo> keywordTimeVoMap = result.stream().collect(Collectors.toMap(each -> each.getKeywordText().toLowerCase(), Function.identity(), (o, n) -> n));
        Map<String, SearchTermsListTrendsVo> trendsVoMap = trends.stream().collect(Collectors.toMap(each -> each.getSearchTerm().toLowerCase(), Function.identity(), (o, n) -> n));
        for (Map.Entry<String, KeywordTimeVo> entry : keywordTimeVoMap.entrySet()) {
            SearchTermsListTrendsVo searchTermsListTrendsVo = trendsVoMap.get(entry.getKey());
            if (searchTermsListTrendsVo != null) {
                entry.getValue().setTrendsVos(searchTermsListTrendsVo);
            }
        }
        return ResultUtil.returnSucc(result);
    }

    /**
     * TODO 重构此方法
     */
    @Override
    public Result deleteKeyword(Integer puid, Integer uid, List<String> keywordTextList) {
        if (CollectionUtils.isEmpty(keywordTextList)) {
            return ResultUtil.error("未指定删除的关键词");
        }
        //删除标签数据
        List<Long> ids = amazonAdKeywordsLibDao.getIdListByKeyowrdTexts(puid, uid, keywordTextList);
        if (CollectionUtils.isNotEmpty(ids)) {
            adKeywordLibMarkupTagDao.deleteAllByRelationId(puid, uid, AdTagTypeEnum.KEYWORDLIB.getType(), null, null, ids.stream().map(String::valueOf).collect(Collectors.toList()), null);
            adKeywordLibMarkupAsinDao.deleteByKeywordsLibId(puid, uid, ids);
            int delCount = amazonAdKeywordsLibDao.deleteByKeywordText(puid, uid, ids);
            return delCount > 0 ? ResultUtil.success() :  ResultUtil.error("该数据已被删除");
        }
        return ResultUtil.success();
    }

    @Override
    public Result deleteKeywordById(Integer puid, List<Integer> uid, List<Long> ids) {
//        List<AmazonAdKeywordsLib> list = amazonAdKeywordsLibDao.getTargetIds(puid, uid, ids);
//        if (list.size() != ids.size()) {
//            return ResultUtil.error("无删除权限, 请刷新页面!");
//        }
//        log.info("删除对象 = {}", JSONUtil.objectToJson(list));
//        adKeywordLibMarkupTagDao.deleteAllByRelationId(puid, list.stream().map(k -> k.getId().toString()).collect(Collectors.toList()));
//        adKeywordLibMarkupAsinDao.deleteAllByKeywordsLibId(puid, ids);
//        int delCount = amazonAdKeywordsLibDao.deleteByKeywordId(puid, ids);
//        if (delCount > 0) {
        return ResultUtil.success();
//        } else {
//            return ResultUtil.error("该数据已被删除");
//        }
    }

    @Override
    public Result<AddKeywordLibVo> createKeywords(Integer puid, Integer uid, List<KeywordLibsVo> vo, List<Integer> uidList) {
        //一次最多可添加1000条
        if (vo.size() > 1000) {
            return ResultUtil.error("一次最多可添加1000条");
        }
        //获取uid下的shopId
        List<String> keywordTexts = vo.stream().map(KeywordLibsVo::getKeywordText).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        //对一次性添加进关键词库的关键词进行大小写去重处理
        keywordTexts = keywordTexts.stream().map(String::toLowerCase).distinct().collect(Collectors.toList());
        // 按词分组 直接查找puid维度下的
        List<AmazonAdKeywordsLib> keywordsLibs = amazonAdKeywordsLibDao.getListAllByKeyowrdTexts(puid, keywordTexts);

        if (!keywordsLibAndAsinLibLimitService.checkAddCountLimit(puid, keywordTexts)) {
            return ResultUtil.error("已添加的关键词和ASIN超过最大限制");
        };
        List<Integer> shopIds = sellfoxShopUserDao.getShopIdListByUser(puid, uid);
        List<AdKeywordLibTag> keywordLibTags = vo.get(0).getAdTags();
        if (CollectionUtils.isNotEmpty(keywordLibTags) && keywordLibTags.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
            return ResultUtil.error("单个关键词最多可添加20个标签，请调整后重新添加");
        }
        List<KeywordLibMarkupAsinVo> keywordLibAsinTags = vo.get(0).getAsinTags();
        if (CollectionUtils.isNotEmpty(keywordLibAsinTags) && keywordLibAsinTags.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
            return ResultUtil.error("单个关键词最多可添加20个asin标签，请调整后重新添加");
        }

        for (KeywordLibsVo keywordLibsVo : vo) {
            if (keywordLibsVo.getKeywordText().length() > 80) {
                return ResultUtil.error("关键词长度不能超过80字符");
            }
        }

        if (!this.checkTagExist(puid, uid, vo)) {
            return ResultUtil.error("您选择的标签不存在，请重新选择标签或刷新页面数据");
        }
        Result<AddKeywordLibVo> result = ResultUtil.success();
        //获取的汇总数据
        try {
            AddKeywordLibVo addKeywordLibVo = checkTagLimit(puid, uid, vo, uidList);
            result.setData(addKeywordLibVo);
            vo = addKeywordLibVo.getAddVos();
            if (CollectionUtils.isEmpty(vo)) {
                return result;
            }

            //非否定投放预览
            long t1 = Instant.now().toEpochMilli();
            List<AmazonAdKeywordsLibDetail> keywordsLibVos = amazonAdKeywordsLibDetailDao.getAggregateKeyword(puid, shopIds, keywordTexts, Constants.BIDDABLE);
            Map<String, AmazonAdKeywordsLibDetail> keywordsMap = keywordsLibVos.stream()
                    .filter(item -> Lists.newArrayList(Constants.ENABLED, Constants.PAUSED).contains(item.getState()))
                    .map(item -> {
                        AmazonAdKeywordsLibDetail detail = new AmazonAdKeywordsLibDetail();
                        BeanUtils.copyProperties(item, detail);
                        detail.setKeywordText(item.getKeywordText().toLowerCase());
                        return detail;
                    }).collect(Collectors.toMap(AmazonAdKeywordsLibDetail::getKeywordText, Function.identity()));
            log.info("查询非否定投放预览，共耗时：{}", Instant.now().toEpochMilli() - t1);

            //关键词监控数
            long t3 = Instant.now().toEpochMilli();
            List<MonitorKeywordVo> monitorKeywords = keywordDao.getCountByPuidAndKeywordText(puid, keywordTexts);
            Map<String, MonitorKeywordVo> monKeywordMap = monitorKeywords.stream().map(item -> {
                MonitorKeywordVo monitorKeywordVo = new MonitorKeywordVo();
                BeanUtils.copyProperties(item, monitorKeywordVo);
                monitorKeywordVo.setKeywordText(item.getKeywordText().toLowerCase());
                return monitorKeywordVo;
            }).collect(Collectors.toMap(MonitorKeywordVo::getKeywordText, Function.identity()));
            log.info("查询关键词监控数，共耗时：{}", Instant.now().toEpochMilli() - t3);

            //获取关键词加入词库时的ABA排名
            long t4 = Instant.now().toEpochMilli();
            List<AmazonAdKeywordsLib> keywordAbaRankVo = this.fillKeywordABARankField(keywordTexts);
            Map<String, AmazonAdKeywordsLib> rankMap = keywordAbaRankVo.stream().map(item -> {
                AmazonAdKeywordsLib rankVo = new AmazonAdKeywordsLib();
                BeanUtils.copyProperties(item, rankVo);
                rankVo.setKeywordText(item.getKeywordText().toLowerCase());
                return rankVo;
            }).collect(Collectors.toMap(AmazonAdKeywordsLib::getKeywordText, Function.identity()));
            log.info("查询关键词ABA排名，共耗时：{}", Instant.now().toEpochMilli() - t4);

            // 获取关键词加入词库时的ABA排名（多站点）
            long t5 = Instant.now().toEpochMilli();
            //获取该词近四周的所有有数据站点的开始时间和站点
            Map<String , List<MuiltMarketplaceRankVo>> rkMap = new HashMap<>();
            List<OdsWeekSearchTermsAnalysis> mkVo = odsWeekSearchTermsAnalysisDao.getLatestDateAndMarketplaceId(keywordTexts);
            if (mkVo != null && !mkVo.isEmpty()) {
                List<MarketplaceStartDateDto> muiltMarketplaceRankList = mkVo.stream().map(e -> {
                    MarketplaceStartDateDto marketplaceStartDate = new MarketplaceStartDateDto();
                    marketplaceStartDate.setKeywordText(e.getSearchTerm());
                    marketplaceStartDate.setMarketplaceId(e.getMarketplaceId());
                    marketplaceStartDate.setStartDate(new SimpleDateFormat("yyyy-MM-dd").format(e.getStartDate()));
                    return marketplaceStartDate;
                }).collect(Collectors.toList());
                // 根据查出的站点和时间取出对应排名
                List<MuiltMarketplaceRankVo> rankVo = new ArrayList<>();
                List<List<MarketplaceStartDateDto>> batches = splitList(muiltMarketplaceRankList, 9000); // 每批 9000 个元素，可根据实际情况调整
                for (List<MarketplaceStartDateDto> batch : batches) { //最多查两次，doris不支持in()集合里超过一万条数据
                    List<MuiltMarketplaceRankVo> batchRankVo = odsWeekSearchTermsAnalysisDao.getRank(batch,keywordTexts);
                    rankVo.addAll(batchRankVo);
                }
                rkMap = rankVo.stream().collect(Collectors.groupingBy(
                        MuiltMarketplaceRankVo::getSearchTerm,
                        Collectors.toList()
                ));
            }
            log.info("查询关键词多站点ABA排名，共耗时：{}", Instant.now().toEpochMilli() - t5);

            Map<String, List<MuiltMarketplaceRankVo>> finalRkMap = rkMap;
            List<AmazonAdKeywordsLib> keywordsLibList = vo.stream().map(item -> {
                AmazonAdKeywordsLib keywordsLib = new AmazonAdKeywordsLib();
                BeanUtils.copyProperties(item, keywordsLib);
                AmazonAdKeywordsLibDetail detail = keywordsMap.get(item.getKeywordText().toLowerCase());
                keywordsLib.setTargetNum(0);
                keywordsLib.setNegateTargetNum(0);
                if (detail != null) {
                    keywordsLib.setSingleQuantity7d(Optional.ofNullable(detail.getSingleQuantity7d()).orElse(0));
                    keywordsLib.setAverageAcos7d(Optional.ofNullable(detail.getAverageAcos7d()).orElse(BigDecimal.ZERO));
                }

                MonitorKeywordVo monDetail = monKeywordMap.get(item.getKeywordText().toLowerCase());
                if (monDetail != null) {
                    keywordsLib.setKeywordMonitor(Optional.ofNullable(monDetail.getCount()).orElse(0));
                }

                AmazonAdKeywordsLib rankDetail = rankMap.get(item.getKeywordText().toLowerCase());
                if (rankDetail != null) {
                    keywordsLib.setAbaRank(Optional.ofNullable(rankDetail.getAbaRank()).orElse(0));
                }
                if (finalRkMap.get(item.getKeywordText().toLowerCase()) != null) {
                    List<MuiltMarketplaceRankVo> rankVo1 = finalRkMap.get(item.getKeywordText().toLowerCase());
                    //只保存站点和对应排名的信息
                    List<MarketplaceRankVo> rankVo2 = new ArrayList<>();
                    for (MuiltMarketplaceRankVo sourceVo : rankVo1) {
                        MarketplaceRankVo targetVo = new MarketplaceRankVo();
                        targetVo.setMarketplaceId(sourceVo.getMarketplaceId());
                        targetVo.setSearchFrequencyRank(sourceVo.getSearchFrequencyRank());
                        rankVo2.add(targetVo);
                    }
                    String rk = JSONUtil.objectToJson(rankVo2);
                    keywordsLib.setJoiningRank(Optional.ofNullable(rk).orElse(""));
                }
                return keywordsLib;
            }).collect(Collectors.toList());

            amazonAdKeywordsLibDao.insertOnDuplicateKeyUpdate(puid, keywordsLibList);
            //添加标签
            if (CollectionUtils.isNotEmpty(keywordLibTags) || CollectionUtils.isNotEmpty(keywordLibAsinTags)) {
                this.addKeywordLibTag(puid, uid, keywordsLibList, keywordLibTags, keywordLibAsinTags, addKeywordLibVo);
            }
            result.setCode(Result.SUCCESS);
        } catch (Exception e) {
            log.error("puid={}  uid={} 关键词添加异常{}", puid, uid, e);
            result.setCode(Result.ERROR);
            result.setMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 获取关键词监控
     */
    private void getKeywordMonitor(Integer puid, List<MonitorKeywordVo> rows) {
        List<String> keywordTexts = rows.stream().map(MonitorKeywordVo::getKeywordText).collect(Collectors.toList());
        CountByPuidAndKeywordTextParam param = new CountByPuidAndKeywordTextParam();
        param.setPuid(puid);
        param.setUid(rows.get(0).getUid());
        param.setKeywordList(keywordTexts);
        List<MonitorKeywordVo> monitorKeywordVos = null;
        try {
            monitorKeywordVos = keywordRankMonitorFeignService.getCountByPuidAndKeywordText(param);
        } catch (Exception e) {
            log.error("广告定时任务调用工具服务异常:", e);
            return;
        }
        if (CollectionUtils.isEmpty(monitorKeywordVos)) {
            return;
        }
        Map<String, MonitorKeywordVo> monKeywordNumMap = monitorKeywordVos.stream().collect(Collectors.toMap(vo -> vo.getKeywordText().toLowerCase(), Function.identity(), (e1, e2) -> e1));
        rows.forEach(e -> {
            if (MapUtils.isNotEmpty(monKeywordNumMap) && monKeywordNumMap.containsKey(e.getKeywordText())) {
                e.setCount(monKeywordNumMap.get(e.getKeywordText()).getCount());
            }
        });
    }

    /**
     * 获取关键词添加进关键词库时的ABA排名
     *
     * @param keywordTexts
     * @return
     */
    private List<AmazonAdKeywordsLib> fillKeywordABARankField(List<String> keywordTexts) {
        List<AmazonAdKeywordsLib> rankList = new ArrayList<>();
        keywordTexts.forEach(i -> {
            AmazonAdKeywordsLib item = new AmazonAdKeywordsLib();
            item.setKeywordText(i);
            item.setAbaRank(0);
            rankList.add(item);
        });
        if (CollectionUtils.isEmpty(keywordTexts)) {
            return rankList;
        }
        //只获取添加关键词的搜索词排名
        List<String> searchTermList = keywordTexts.stream().map(String::toLowerCase).distinct().collect(Collectors.toList());
        String searchTerms = StringUtil.joinString(searchTermList, StringUtil.SPLIT_COMMA);
        // 调取stateV2接口获取搜索词排名字段
        List<SearchTermsAnalysis> searchTermsAnalysisList = Lists.newArrayList();
        // 抛异常不捕获，避免影响程序执行
        try {
            log.info("start request stateV2 searchTermsAnalysis api");
            long s1 = Instant.now().toEpochMilli();
            searchTermsAnalysisList = searchAnalysisStatsV2Client.getSearchTermAnalysis(searchTerms);
            log.info("调用stateV2接口获取搜索词排名及周变化率，共耗时：{}", Instant.now().toEpochMilli() - s1);
        } finally {
            log.info("end request stateV2 searchTermsAnalysis api");
        }
        if (CollectionUtils.isEmpty(searchTermsAnalysisList)) {
            return rankList;
        }
        Map<String, SearchTermsAnalysis> searchTermsAnalysisMap = searchTermsAnalysisList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(item -> item.getSearchTerm().trim().toLowerCase(), item -> item, (e1, e2) -> e1));
        rankList.stream().peek(item -> {
            if (StringUtils.isNotBlank(item.getKeywordText()) && searchTermsAnalysisMap.get(item.getKeywordText().trim().toLowerCase()) != null) {
                SearchTermsAnalysis termsAnalysis = searchTermsAnalysisMap.get(item.getKeywordText().trim().toLowerCase());
                item.setAbaRank(termsAnalysis.getSearchFrequencyRank());
            }
        }).collect(Collectors.toList());
        return rankList;
    }

    /**
     * 关键词不能超过20个标签/asin标签
     */
    private AddKeywordLibVo checkTagLimit(Integer puid, Integer uid, List<KeywordLibsVo> vo, List<Integer> uidList) {
        // 查找权限范围内关键词记录
        List<KeywordLibsVo> keywordsLibs = amazonAdKeywordsLibDao.listByUidListAndKeywordText(puid, uidList, vo.stream().map(KeywordLibsVo::getKeywordText).collect(Collectors.toList()));
        Map<String, List<KeywordLibsVo>> keywordsMap = keywordsLibs.stream().collect(Collectors.groupingBy(k -> k.getKeywordText().toLowerCase()));
        List<Long> ids = keywordsLibs.stream().map(KeywordLibsVo :: getId).collect(Collectors.toList());
        List<String> idStrings = keywordsLibs.stream().map(keywordLibsVo -> keywordLibsVo.getId().toString()).collect(Collectors.toList());
        // 找到自定义标签和公告标签 已经有tag的字段
        Map<String, AdKeywordLibMarkupTagVo> adKeywordMap = adKeywordLibMarkupTagDao.getRelationVos(puid, uid, null, null, null, null, idStrings)
                .stream().peek(e -> e.setTagIdsList(e.getTagIdsStr())).collect(Collectors.toMap(AdKeywordLibMarkupTagVo::getRelationId, Function.identity(), (e1, e2) -> e2));
        // 以关键词库的Id 分组
        Map<Long, List<AdKeywordLibMarkupAsin>> adKeywordAsinMap = adKeywordLibMarkupAsinDao.listByKeywordsLibIdsList(puid, uidList, ids).stream().collect(Collectors.groupingBy(AdKeywordLibMarkupAsin::getKeywordsLibId));

        List<KeywordLibsVo> addList = new ArrayList<>();
        List<String> tagReachedLimit = new ArrayList<>();
        List<String> tagNeedModify = new ArrayList<>();
        List<String> asinReachedLimit = new ArrayList<>();
        List<String> asinNeedModify = new ArrayList<>();

        boolean existRepeatedAdded = false;
        // 校验tag/asinTag
        for (KeywordLibsVo e : vo) {
            List<KeywordLibsVo> keywordsLib = keywordsMap.get(e.getKeywordText().toLowerCase());
            // 是否为数据库已存在的关键词
            if (keywordsLib != null && !existRepeatedAdded) {
                existRepeatedAdded = true;
            }
            // 没有记录的keyword
            if (keywordsLib == null) {
                addList.add(e);
                continue;
            }
            // 代表自己没有添加过这个词
            if (keywordsLib.stream().noneMatch(k -> uid.equals(k.getUid()))) {
                addList.add(e);
                continue;
            }
            boolean isAdd = false;
            // tag
            Set<Long> addTagIds = ListUtils.emptyIfNull(e.getAdTags()).stream().map(AdKeywordLibTag::getId).collect(Collectors.toSet());
            Set<Long> tagIds = getTag(keywordsLib, adKeywordMap);
            tagIds.addAll(addTagIds);
            if (tagIds.size() <= Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                isAdd = true;
            } else if (addTagIds.size() >= Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                tagReachedLimit.add(e.getKeywordText());
            } else {
                tagNeedModify.add(e.getKeywordText());
            }
            // asinTag
            Set<String> asinTagIds = ListUtils.emptyIfNull(e.getAsinTags()).stream().map(KeywordLibMarkupAsinVo::getAsin).collect(Collectors.toSet());
            Set<String> asinTag = getAsinTag(keywordsLib, adKeywordAsinMap);
            asinTag.addAll(asinTagIds);
            if (asinTag.size() <= Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                isAdd = true;
            } else if (asinTagIds.size() >= Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                asinReachedLimit.add(e.getKeywordText());
            } else {
                asinNeedModify.add(e.getKeywordText());
            }
            if (isAdd) {
                addList.add(e);
            }
        }

        List<String> success = addList.stream()
                .map(KeywordLibsVo::getKeywordText)
                .filter(e -> !tagReachedLimit.contains(e) && !tagNeedModify.contains(e) && !asinReachedLimit.contains(e) && !asinNeedModify.contains(e))
                .collect(Collectors.toList());
        return AddKeywordLibVo.builder()
                .addVos(addList)
                .success(success)
                .tagReachedLimit(tagReachedLimit)
                .tagNeedModify(tagNeedModify)
                .asinReachedLimit(asinReachedLimit)
                .asinNeedModify(asinNeedModify)
                .existRepeatedAdded(existRepeatedAdded)
                .build();
    }

    private Set<Long> getTag(List<KeywordLibsVo> keywordsLib, Map<String, AdKeywordLibMarkupTagVo> adKeywordMap) {
        Set<Long> longs = new HashSet<>();
        for (KeywordLibsVo keywordLibsVo : keywordsLib) {
            AdKeywordLibMarkupTagVo adKeywordLibMarkupTagVo = adKeywordMap.get(keywordLibsVo.getId().toString());
            if (adKeywordLibMarkupTagVo != null) {
                if (CollectionUtils.isNotEmpty(adKeywordLibMarkupTagVo.getTagIds())) {
                    longs.addAll(adKeywordLibMarkupTagVo.getTagIds());
                }
            }
        }
        return longs;
    }

    private Set<String> getAsinTag(List<KeywordLibsVo> keywordsLib, Map<Long, List<AdKeywordLibMarkupAsin>> map) {
        Set<String> longs = new HashSet<>();
        for (KeywordLibsVo keywordLibsVo : keywordsLib) {
            List<AdKeywordLibMarkupAsin> adKeywordLibMarkupTagVo = map.get(keywordLibsVo.getId());
            if (CollectionUtils.isNotEmpty(adKeywordLibMarkupTagVo)) {
                longs.addAll(adKeywordLibMarkupTagVo.stream().map(AdKeywordLibMarkupAsin::getAsin).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
        }
        return longs;
    }

    @Override
    public Result<Page<KeywordLibsDetailVo>> getKeywordDetail(KeywordLibsPageParam param) {
        StopWatch sw = new StopWatch("getKeywordDetail");
        Integer puid = param.getPuid();
        Page<KeywordLibsDetailVo> page = new Page<>();
        page.setRows(new ArrayList<>());
        page.setPageSize(param.getPageSize());
        page.setPageNo(param.getPageNo());
        try {
            //获取uid下的shopId
            sw.start("获取店铺id");
            List<Integer> shopIds = param.getShopIdList().stream().map(Integer::valueOf).collect(Collectors.toList());
            sw.stop();
            param.setShopIds(shopIds);
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                sw.start("获取广告活动id");
                List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(puid, shopIds, param.getSearchPortfolioList());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
                        campaignIds.retainAll(param.getSearchCampaignList());
                        if (CollectionUtils.isEmpty(campaignIds)) {
                            return ResultUtil.returnSucc(page);
                        }
                    } else {
                        param.setSearchCampaignList(campaignIds);
                    }
                } else {
                    return ResultUtil.returnSucc(page);
                }
                sw.stop();
            }

            sw.start("查询分页展示的关键词id");
            //查询分页展示的关键词id
            page = odsAmazonAdKeywordReportDao.getKeywordIdListByPage(puid, param.getStartDate(), param.getEndDate(), param);
            if (CollectionUtils.isEmpty(page.getRows())) {
                return ResultUtil.returnSucc(page);
            }
            List<KeywordLibsDetailVo> keywordList = page.getRows();
            List<String> spKeywordIds = keywordList.stream().filter(item -> "sp".equals(item.getType())).map(KeywordLibsDetailVo::getKeywordId).collect(Collectors.toList());
            List<String> sbKeywordIds = keywordList.stream().filter(item -> "sb".equals(item.getType())).map(KeywordLibsDetailVo::getKeywordId).collect(Collectors.toList());
            sw.stop();
            sw.start("查询报告数据：sp");
            //查询报告数据
            List<KeywordLibsDetailVo> spReportData = CollectionUtils.isNotEmpty(spKeywordIds) ? odsAmazonAdKeywordReportDao.getSpKeywordReportData(puid, param, spKeywordIds) : new ArrayList<>();
            sw.stop();
            sw.start("查询报告数据：sb");
            List<KeywordLibsDetailVo> sbReportData = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(sbKeywordIds)) {
                sbReportData = odsAmazonAdSbKeywordReportDao.getSbKeywordReportData(puid, param, sbKeywordIds);
            }
            Map<String, KeywordLibsDetailVo> map = new HashMap<>();
            for (KeywordLibsDetailVo k : spReportData) {
                map.put(k.getKeywordId(), null);
            }
            Map<String, KeywordLibsDetailVo> voMap = mergeKeywordReportData(map, spReportData);
            Map<String, KeywordLibsDetailVo> reportDataMap = mergeKeywordReportData(voMap, sbReportData);
            sw.stop();

            sw.start("查询基础数据：sp");
            //查询基础数据
            List<KeywordLibsDetailVo> spKeywordDetailBasicData = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(spKeywordIds)) {
                spKeywordDetailBasicData = amazonAdKeywordShardingDao.getKeywordDetail(puid, param, spKeywordIds);
            }
            sw.stop();
            sw.start("查询基础数据：sb");
            List<KeywordLibsDetailVo> sbKeywordDetailBasicData = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(sbKeywordIds)) {
                sbKeywordDetailBasicData = amazonSbAdKeywordDao.getKeywordDetail(puid, param, sbKeywordIds);
            }
            sw.stop();
            //基础数据转成map
            Map<String, KeywordLibsDetailVo> spKeywordDetailBasic = spKeywordDetailBasicData.stream().collect(Collectors.toMap(KeywordLibsDetailVo::getKeywordId, item -> item));
            Map<String, KeywordLibsDetailVo> sbKeywordDetailBasic = sbKeywordDetailBasicData.stream().collect(Collectors.toMap(KeywordLibsDetailVo::getKeywordId, item -> item));

            List<String> campaignIdList = new ArrayList<>();
            campaignIdList.addAll(spKeywordDetailBasicData.stream().map(KeywordLibsDetailVo::getCampaignId).filter(Objects::nonNull).collect(Collectors.toList()));
            campaignIdList.addAll(sbKeywordDetailBasicData.stream().map(KeywordLibsDetailVo::getCampaignId).filter(Objects::nonNull).collect(Collectors.toList()));
            List<String> groupIdList = spKeywordDetailBasicData.stream().map(KeywordLibsDetailVo::getAdGroupId).filter(Objects::nonNull).collect(Collectors.toList());
            List<String> sbGroupIdList = sbKeywordDetailBasicData.stream().map(KeywordLibsDetailVo::getAdGroupId).filter(Objects::nonNull).collect(Collectors.toList());

            //获取店铺名称
            long t1 = Instant.now().toEpochMilli();
            Map<Integer, ShopAuth> shopAuthMap = shopAuthDao.listAllByIds(puid, shopIds).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            log.info("获取店铺名称，共耗时：{}", Instant.now().toEpochMilli() - t1);
            //获取广告活动名称
            long t2 = Instant.now().toEpochMilli();
            Map<String, AmazonAdCampaignAll> campaignAllMap = amazonAdCampaignAllDao.getNameByShopIdsAndCampaignIds(puid, shopIds, campaignIdList, null, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
            log.info("获取广告活动名称，共耗时：{}", Instant.now().toEpochMilli() - t2);
            //获取广告组合名称
            long t = Instant.now().toEpochMilli();
            List<String> portfolioIdList = campaignAllMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
            List<KeywordLibsPortfolioListVO> portfolioListVo = portfolioService.getAllPortfolioName(puid, shopIds, portfolioIdList);
            Map<String, KeywordLibsPortfolioListVO> portfolioMap = portfolioListVo.parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(KeywordLibsPortfolioListVO::getPortfolioId, Function.identity()));
            log.info("获取广告组合名称，共耗时：{}", Instant.now().toEpochMilli() - t);
            Map<String, AmazonAdGroup> adGroupMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(groupIdList)) {
                //获取sp广告组名称
                long t3 = Instant.now().toEpochMilli();
                adGroupMap.putAll(amazonAdGroupDao.getNameByShopIdsAndGroupIds(puid, shopIds, groupIdList, null).
                        parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity())));
                log.info("获取sp广告组名称，共耗时：{}", Instant.now().toEpochMilli() - t3);
            }

            Map<String, AmazonSbAdGroup> sbAdGroupMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sbGroupIdList)) {
                //获取sb广告组名称
                long t4 = Instant.now().toEpochMilli();
                sbAdGroupMap.putAll(amazonSbAdGroupDao.getNameListByShopIdsAndGroupIds(puid, shopIds, campaignIdList, sbGroupIdList, null).
                        parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity())));
                log.info("获取sb广告组名称，共耗时：{}", Instant.now().toEpochMilli() - t4);
            }

            long t5 = Instant.now().toEpochMilli();
            keywordList.stream().filter(Objects::nonNull).peek(e -> {
                //基础数据
                KeywordLibsDetailVo basicData;
                if (Constants.SP.equals(e.getType())) {
                    basicData = spKeywordDetailBasic.get(e.getKeywordId());
                } else {
                    basicData = sbKeywordDetailBasic.get(e.getKeywordId());
                }
                if (basicData != null) {
                    e.setPuid(basicData.getPuid());
                    e.setCreateTime(StringUtils.isNotBlank(basicData.getCreateTime()) && basicData.getCreateTime().length() > 19 ? basicData.getCreateTime().substring(0, 19) : basicData.getCreateTime());
                    e.setMatchType(basicData.getMatchType());
                    e.setState(basicData.getState());
                    e.setShopId(basicData.getShopId());
                    e.setCampaignId(basicData.getCampaignId());
                    e.setAdGroupId(basicData.getAdGroupId());
                    e.setMarketplaceId(basicData.getMarketplaceId());
                    e.setBid(basicData.getBid());
                    e.setMatchType(basicData.getMatchType());
                }

                ShopAuth shop = shopAuthMap.get(e.getShopId());
                if (shop != null && StringUtils.isNotEmpty(shop.getName())) {
                    e.setShopName(shop.getName());
                }

                //获取广告活动名称
                AmazonAdCampaignAll campaign = campaignAllMap.get(e.getCampaignId());
                if (campaign != null && e.getType().equalsIgnoreCase(campaign.getType()) && StringUtils.isNotEmpty(campaign.getName())) {
                    e.setCampaignName(campaign.getName());
                }

                //获取广告组合名称
                if (campaign != null && campaign.getPortfolioId() != null) {
                    KeywordLibsPortfolioListVO portfolioListVO = portfolioMap.get(campaign.getPortfolioId());
                    if (portfolioListVO != null && StringUtils.isNotEmpty(portfolioListVO.getPortfolioName())) {
                        e.setPortfolioName(portfolioListVO.getPortfolioName());
                        e.setPortfolioId(portfolioListVO.getPortfolioId());
                        e.setIsHidden(portfolioListVO.getIsHidden());
                    }
                }

                //获取广告组名称(关键词只有sp,sb)
                if (e.getAdGroupId() != null) {
                    if (Constants.SP.equals(e.getType())) {
                        AmazonAdGroup adGroup = adGroupMap.get(e.getAdGroupId());
                        if (adGroup != null && StringUtils.isNotEmpty(adGroup.getName())) {
                            e.setAdGroupName(adGroup.getName());
                        }
                    } else {
                        AmazonSbAdGroup adSbGroup = sbAdGroupMap.get(e.getAdGroupId());
                        if (adSbGroup != null && StringUtils.isNotEmpty(adSbGroup.getName())) {
                            e.setAdGroupName(adSbGroup.getName());
                        }
                    }
                }

                //报告数据
                KeywordLibsDetailVo report = reportDataMap.get(e.getKeywordId());
                if (report != null) {
                    e.setImpressions(report.getImpressions());
                    e.setClicks(report.getClicks());
                    e.setClickRate(report.getClickRate());
                    e.setSaleNum(report.getSaleNum());
                    e.setSalesConversionRate(report.getSalesConversionRate());
                    e.setTotalSales(report.getTotalSales());
                    e.setCost(report.getCost());
                    e.setCpc(report.getCpc());
                    e.setCpa(report.getCpa());
                    e.setAcos(report.getAcos());
                    e.setRoas(report.getRoas());
                }
            }).collect(Collectors.toList());

            page.setRows(keywordList);
            log.info("封装店铺、广告活动、广告组名称，共耗时{}", Instant.now().toEpochMilli() - t5);
            log.info(sw.prettyPrint());
        } catch (Exception e) {
            log.info("get keyword lib detail msg error,", e);
        }
        return ResultUtil.returnSucc(page);
    }

    @Override
    public Result<Page<KeywordLibsDetailVo>> getKeywordNeTargetDetail(KeywordLibsPageParam param) {
        List<Integer> shopIds = param.getShopIdList().stream().map(Integer::parseInt).collect(Collectors.toList());
        Map<Integer, String> shopNameMap = shopAuthService.getShopNameMap(param.getPuid(), shopIds);
        Page<KeywordLibsDetailVo> page = new Page<>();
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(), shopIds, param.getSearchPortfolioList());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
                    campaignIds.retainAll(param.getSearchCampaignList());
                    if (CollectionUtils.isEmpty(campaignIds)) {
                        return ResultUtil.returnSucc(page);
                    }
                } else {
                    param.setSearchCampaignList(campaignIds);
                }
            } else {
                return ResultUtil.returnSucc(page);
            }
        }
        page = amazonAdNeKeywordDao.getKeywordNeTargetList(param.getPuid(), shopIds, param, param.getKeywordText());
        if (CollectionUtils.isEmpty(page.getRows())) {
            return ResultUtil.returnSucc(page);
        }
        Set<String> campaignIds = new HashSet<>();
        Set<String> adgroupIds = new HashSet<>();
        Set<String> sbAdgroupIds = new HashSet<>();
        page.getRows().forEach(e -> {
            campaignIds.add(e.getCampaignId());
            if (Constants.SP.equals(e.getType())) {
                adgroupIds.add(e.getAdGroupId());
            } else {
                sbAdgroupIds.add(e.getAdGroupId());
            }
        });
        List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.getNameByShopIdsAndCampaignIds(param.getPuid(), shopIds, new ArrayList<>(campaignIds), null, null);
        Map<String, String> campaignNameMap = campaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, AmazonAdCampaignAll::getName));
        Map<String, String> campaignIdportfolioIdMap = campaignAllList.stream().filter(campaign -> campaign.getPortfolioId() != null).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, AmazonAdCampaignAll::getPortfolioId));
        List<String> portfolioIdList = campaignAllList.stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        List<KeywordLibsPortfolioListVO> portfolioListVo = portfolioService.getAllPortfolioName(param.getPuid(), shopIds, portfolioIdList);
        Map<String, KeywordLibsPortfolioListVO> portfolioMap = portfolioListVo.parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(KeywordLibsPortfolioListVO::getPortfolioId, Function.identity()));
        Map<String, String> adGroupNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adgroupIds)) {
            adGroupNameMap.putAll(amazonAdGroupDao.getNameByShopIdsAndGroupIds(param.getPuid(), shopIds, new ArrayList<>(adgroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, AmazonAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sbAdgroupIds)) {
            adGroupNameMap.putAll(amazonSbAdGroupDao.getNameListByShopIdsAndGroupIds(param.getPuid(), shopIds, null, new ArrayList<>(sbAdgroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, AmazonSbAdGroup::getName)));
        }
        page.getRows().forEach(e -> {
            e.setShopName(shopNameMap.getOrDefault(e.getShopId(), ""));
            e.setPortfolioId(campaignIdportfolioIdMap.get(e.getCampaignId()));
            e.setCampaignName(campaignNameMap.getOrDefault(e.getCampaignId(), ""));
            e.setAdGroupName(adGroupNameMap.getOrDefault(e.getAdGroupId(), ""));
            e.setCreateTime(StringUtils.isNotBlank(e.getCreateTime()) && e.getCreateTime().length() > 19 ? e.getCreateTime().substring(0, 19) : e.getCreateTime());
            KeywordLibsPortfolioListVO portfolio = portfolioMap.get(campaignIdportfolioIdMap.get(e.getCampaignId()));
            if (portfolio != null) {
                e.setPortfolioName(portfolio.getPortfolioName());
                e.setIsHidden(portfolio.getIsHidden());
            }
        });
        return ResultUtil.returnSucc(page);
    }

    @Override
    public Result<KeywordLibsTotalVo> getKeywordDetailAggregateData(KeywordLibsPageParam param) {
        Integer puid = param.getPuid();
        List<KeywordLibsTotalVo> vo;
        KeywordLibsTotalVo totalVo = new KeywordLibsTotalVo();
        List<Integer> shopIds = param.getShopIdList().stream().map(Integer::valueOf).collect(Collectors.toList());
        try {
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(param.getPuid(), shopIds, param.getSearchPortfolioList());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
                        campaignIds.retainAll(param.getSearchCampaignList());
                        if (CollectionUtils.isEmpty(campaignIds)) {
                            return ResultUtil.returnSucc(totalVo);
                        }
                    } else {
                        param.setSearchCampaignList(campaignIds);
                    }
                } else {
                    return ResultUtil.returnSucc(totalVo);
                }
            }
            //查询报告数据
            vo = odsAmazonAdKeywordReportDao.getKeywordTotalData(puid, param);
            //汇总指标数据
            getKeywordAggregateDataVo(totalVo, vo);

        } catch (Exception e) {
            log.info("get keyword lib detail msg error,", e);
        }
        return ResultUtil.returnSucc(totalVo);
    }

    private void getKeywordAggregateDataVo(KeywordLibsTotalVo totalVo, List<KeywordLibsTotalVo> vo) {
        vo.forEach(e -> {
            //投放数量
            totalVo.setPutNum(e.getPutNum());
            //点击量
            totalVo.setClicks(e.getClicks());
            //曝光量
            totalVo.setImpressions(e.getImpressions());
            //广告订单数
            totalVo.setSaleNum(e.getSaleNum());
            //广告销售额
            totalVo.setTotalSales(e.getTotalSales());
            if (e.getCost() != null) {
                //广告花费
                totalVo.setCost(e.getCost());
                //CPC
                totalVo.setCpc(MathUtil.divideCompatibleZero(e.getCost(), BigDecimal.valueOf(e.getClicks())));
                //CPA
                totalVo.setCpa(MathUtil.divideCompatibleZero(e.getCost(), BigDecimal.valueOf(e.getSaleNum())));
                //ACoS
                totalVo.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(e.getCost(), e.getTotalSales()), RATE));
                //ROAS
                totalVo.setRoas(MathUtil.divideCompatibleZero(e.getTotalSales(), e.getCost()));
            }
            if (e.getClicks() != null) {
                //点击率
                totalVo.setClickRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(e.getClicks()), BigDecimal.valueOf(e.getImpressions())), RATE));
                //广告转化率
                totalVo.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(e.getSaleNum()), BigDecimal.valueOf(e.getClicks())), RATE));
            }
        });

    }

    @Override
    public Result updateRemark(KeywordLibsVo vo) {
        AmazonAdKeywordsLib amazonAdKeywordsLib = amazonAdKeywordsLibDao.getKeywordByKeywordText(vo.getPuid(), vo.getUid(), vo.getKeywordText());
        if (amazonAdKeywordsLib == null) {
            amazonAdKeywordsLib = new AmazonAdKeywordsLib();
            amazonAdKeywordsLib.setTargetNum(0);
            amazonAdKeywordsLib.setNegateTargetNum(0);
            amazonAdKeywordsLib.setSingleQuantity7d(0);
            amazonAdKeywordsLib.setAverageAcos7d(BigDecimal.ZERO);
            amazonAdKeywordsLib.setKeywordText(vo.getKeywordText().trim());
            amazonAdKeywordsLib.setRemark(vo.getRemark());
            amazonAdKeywordsLib.setPuid(vo.getPuid());
            amazonAdKeywordsLib.setUid(vo.getUid());
            amazonAdKeywordsLib.setUpdateId(vo.getUid());
            amazonAdKeywordsLib.setCreateId(vo.getUid());
            amazonAdKeywordsLib.setKeywordMonitor(0);
            amazonAdKeywordsLib.setAbaRank(0);
            amazonAdKeywordsLib.setSource("manual");
            amazonAdKeywordsLibDao.insertOnDuplicateKeyUpdate(vo.getPuid(), Lists.newArrayList(amazonAdKeywordsLib));
            return ResultUtil.success();
        }
        int updateCount = amazonAdKeywordsLibDao.updateRemark(vo.getPuid(), vo.getUid(), vo.getKeywordText(), vo.getRemark(), vo.getUid());
        if (updateCount > 0) {
            return ResultUtil.success();
        } else {
            return ResultUtil.error("更新失败");
        }
    }

    /**
     * 获取所有关键字文本
     *
     * @param puid puid
     * @param uid  uid 不传则不查询
     * @return {@link List}<{@link String}>
     */
    @Override
    public List<String> getAllKeywordTextByPuidAndUid(Integer puid, List<Integer> uid) {
        if (puid == null) {
            return null;
        }
        return amazonAdKeywordsLibDao.getAllKeywordText(puid, uid);
    }

    private void fillAdTagData(Integer puid, Integer uid, List<KeywordLibsVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        Set<Long> keywordIdSet = new HashSet<>();
        for (KeywordLibsVo keywordLibsVo : rows) {
            keywordIdSet.addAll(keywordLibsVo.getItemVos().stream().map(KeywordLibsVo.KeywordLibsItemVo::getId).collect(Collectors.toSet()));
        }
        List<String> keywordIds = keywordIdSet.stream().map(String::valueOf).collect(Collectors.toList());
        List<AdKeywordLibMarkupTag> relationVos = adKeywordLibMarkupTagDao.getMarkupTagByRelationId(puid, uid, AdTagTypeEnum.KEYWORDLIB.getType(), keywordIds);
        if (CollectionUtils.isEmpty(relationVos)) {
            return;
        }
        List<Long> collect = relationVos.stream().map(AdKeywordLibMarkupTag::getTagId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<AdKeywordLibTag> byLongIdList = adKeywordLibTagDao.getListByLongIdList(puid, collect);
        if (CollectionUtils.isEmpty(byLongIdList)) {
            return;
        }
        Map<Long, AdKeywordLibTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdKeywordLibTag::getId, Function.identity(), (e1, e2) -> e2));
        // 根据标签分组
        Map<Long, Set<Long>> tagMap = relationVos.stream().collect(Collectors.groupingBy(AdKeywordLibMarkupTag::getTagId, Collectors.mapping(k -> Long.parseLong(k.getRelationId()), Collectors.toSet())));
        // 根据关键词Id 分组
        Map<Long, Set<Long>> relationMap = relationVos.stream().collect(Collectors.groupingBy(k -> Long.parseLong(k.getRelationId()), Collectors.mapping(AdKeywordLibMarkupTag::getTagId, Collectors.toSet())));
        for (KeywordLibsVo vo : rows) {
            if (CollectionUtils.isEmpty(vo.getItemVos())) {
                continue;
            }
            Set<Long> tagIds = new HashSet<>();
            for (KeywordLibsVo.KeywordLibsItemVo keywordLibsItemVo : vo.getItemVos()) {
                Set<Long> set = relationMap.get(keywordLibsItemVo.getId());
                if (set == null) {
                    continue;
                }
                tagIds.addAll(set);
            }
            if (CollectionUtils.isEmpty(tagIds)) {
                continue;
            }
            Set<Long> keywordId = vo.getItemVos().stream().map(KeywordLibsVo.KeywordLibsItemVo::getId).collect(Collectors.toSet());
            // 仅展示5条标签
            List<AdKeywordLibTag> collect1 = tagIds.stream().map(k -> copyAdKeywordLibTag(adTagMap.get(k))).limit(Constants.KEYWORD_LIB_TAG_MAX_SIZE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect1)) {
                // 设置tag下面的主键Id
                collect1.forEach(k -> {
                    Set<Long> set = tagMap.get(k.getId());
                    if (set == null) {
                        return;
                    }
                    Set<Long> retainSet = new HashSet<>(set);
                    retainSet.retainAll(keywordId);
                    if (CollectionUtils.isNotEmpty(retainSet)) {
                        k.setIdList(new ArrayList<>(retainSet));
                    }
                });
            }
            vo.setAdTags(collect1);
        }
    }

    private AdKeywordLibTag copyAdKeywordLibTag(AdKeywordLibTag adKeywordLibTag) {
        AdKeywordLibTag adKeywordLibTag1 = new AdKeywordLibTag();
//        adKeywordLibTag1.setTagSort(adKeywordLibTag.getTagSort());
//        adKeywordLibTag1.setPuid(adKeywordLibTag.getPuid());
        adKeywordLibTag1.setUid(adKeywordLibTag.getUid());
        adKeywordLibTag1.setId(adKeywordLibTag.getId());
        adKeywordLibTag1.setName(adKeywordLibTag.getName());
        adKeywordLibTag1.setType(adKeywordLibTag.getType());
        adKeywordLibTag1.setColor(adKeywordLibTag.getColor());
        return adKeywordLibTag1;
    }

    @Override
    public Result<List<KeywordLibsVo>> exportList(KeywordLibsPageParam param) {

        //TODO adTagId查询出标签relationId（关键词库的id）。
//        List<Integer> keywordIdList = null;
/*        List<String> keywordIds = adKeywordLibMarkupTagDao.getRelationIds(param.getPuid(), param.getUid(), AdTagTypeEnum.KEYWORDLIB.getType(), param.getAdTagId(), null, null);
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            keywordIdList = keywordIds.stream()
                    .map(Integer::parseInt).filter(Objects::nonNull).collect(Collectors.toList());
        }*/
//        List<KeywordLibsVo> keywordLibsVoList = amazonAdKeywordsLibDao.listByCondition(param.getUid(),param, keywordIdList);
        param.setPageNo(1);
        param.setPageSize(10000);
        Result<KeywordLibsPageListVo> result = pageList(param);
        List<KeywordLibsVo> keywordLibsVoList = result.getData().getPage().getRows();
//        fillAdTagData(param.getPuid(), param.getUid(), keywordLibsVoList);
//        //若没有筛选ASIN标签，则填充ASIN标签列表
//        if (StringUtils.isBlank(param.getAsin()) && StringUtils.isBlank(param.getMarketplaceId())) {
//            this.fillAsinTagList(param.getPuid(), param.getUidList(), keywordLibsVoList);
//        }
//        // 添加搜索词排名字段，仅调接口获取
//        addSearchTermRankField(keywordLibsVoList);
        return ResultUtil.returnSucc(keywordLibsVoList);
    }

    @Override
    public List<KeywordLibsShopListVO> getKeywordDetailShopListPage(KeywordLibsPageParam param) {
        Integer puid = param.getPuid();
        List<KeywordLibsShopListVO> list = new ArrayList<>();
        //根据puid和keyword_text查询shop list
        try {
            list = amazonAdKeywordsLibDetailDao.getKeywordsDetailShopList(puid, param);
        } catch (Exception e) {
            log.error("query keyword lib detail shop list by puid end keyword_text error，puid:{},keyword_text:{}, e", puid, param.getKeywordText(), e);
        }
        List<Integer> shopIdList = list.parallelStream().filter(Objects::nonNull).map(KeywordLibsShopListVO::getShopId).distinct().collect(Collectors.toList());
        //通过shopId获取店铺名称
        Map<Integer, String> shopNameMap = shopAuthService.getShopNameMap(puid, shopIdList);
        list.stream().filter(Objects::nonNull).forEach(d -> {
                    //还需要根据marketplaceId获取站点信息
                    d.setMarketplace(getMarketplace(d.getMarketplaceId()).getLeft());
                    d.setMarketplaceCN(getMarketplace(d.getMarketplaceId()).getRight());
                    d.setShopName(shopNameMap.getOrDefault(d.getShopId(), ""));
                }
        );
        return list;
    }

    @Override
    public Page<KeywordLibsPortfolioListVO> getKeywordDetailPortfolioListPage(KeywordLibsPageParam param) {
        Integer puid = param.getPuid();
        Page<KeywordLibsPortfolioListVO> page = new Page<>();
        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        List<KeywordLibsPortfolioListVO> dataRow = Lists.newArrayList();
        //根据puid和shopId查询portfolio list
        try {
            List<OdsAmazonAdKeyword> list;
            // 过滤出广告活动
            if (Constants.BIDDABLE.equalsIgnoreCase(param.getTargetType())) {
                list = odsAmazonAdKeywordDao.listAllCampaignByCondition(puid, param.getShopIds(), param);
            } else {
                list = buildList(amazonAdNeKeywordDao.listAllCampaignByCondition(puid, param.getShopIds(), param));
            }
            if (CollectionUtils.isEmpty(list)) {
                page.setRows(dataRow);
                return page;
            }
            List<AmazonAdCampaignAll> amazonAdCampaignAlls = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid,
                    list.stream().map(OdsAmazonAdKeyword::getShopId).distinct().collect(Collectors.toList()),
                    list.stream().map(OdsAmazonAdKeyword::getCampaignId).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(list)) {
                page.setRows(dataRow);
                return page;
            }
            List<String> portfolioIds = amazonAdCampaignAlls.stream().map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isEmpty(portfolioIds)) {
                page.setRows(dataRow);
                return page;
            }
            param.setSearchPortfolioList(portfolioIds);
            //处理页面下拉搜索中传输的name，将其转化成id的形式进行查询
            page = portfolioService.getPortfolioName(puid, param);
        } catch (Exception e) {
            log.error("query keyword lib detail portfolio list by puid error，puid:{}, e", puid, e);
        }
        return page;
    }

    @Override
    public Page<KeywordLibsCampaignListVO> getKeywordDetailCampaignListPage(KeywordLibsPageParam param) {
        Integer puid = param.getPuid();
        Page<KeywordLibsCampaignListVO> page = new Page<>();
        List<KeywordLibsCampaignListVO> dataRow = Lists.newArrayList();

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        //根据puid和keyword_text查询campaign list
        filterPortfolio(param);
        try {
            Page<OdsAmazonAdKeyword> campaignPage;
            if (Constants.BIDDABLE.equalsIgnoreCase(param.getTargetType())) {
                campaignPage = odsAmazonAdKeywordDao.listPageByCondition(puid, param.getShopIds(), param);
            } else {
                campaignPage = convert(amazonAdNeKeywordDao.listPageByCondition(puid, param.getShopIds(), param));
            }
            page.setTotalPage(campaignPage.getTotalPage());
            page.setTotalSize(campaignPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(campaignPage.getRows())) {
                Map<String, AmazonAdCampaignAll> amazonAdCampaignAlls = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid,
                                campaignPage.getRows().stream().map(OdsAmazonAdKeyword::getShopId).distinct().collect(Collectors.toList()),
                                campaignPage.getRows().stream().map(OdsAmazonAdKeyword::getCampaignId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
                dataRow = campaignPage.getRows().stream().map(k -> {
                    KeywordLibsCampaignListVO keywordLibsCampaignListVO = new KeywordLibsCampaignListVO();
                    keywordLibsCampaignListVO.setShopId(k.getShopId());
                    keywordLibsCampaignListVO.setCampaignId(k.getCampaignId());
                    AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAlls.get(k.getCampaignId());
                    if (amazonAdCampaignAll != null) {
                        keywordLibsCampaignListVO.setCampaignName(amazonAdCampaignAll.getName());
                        keywordLibsCampaignListVO.setMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                        keywordLibsCampaignListVO.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                    }
                    return keywordLibsCampaignListVO;
                }).collect(Collectors.toList());
            }
            //通过shopId获取广告活动名称
        } catch (Exception e) {
            log.error("query keyword lib detail campaign list by puid end keyword_text error，puid:{},keyword_text:{}, e", puid, param.getKeywordText(), e);
        }
        page.setRows(dataRow);
        return page;
    }

    /**
     * 广告组合 和 名称 过滤广告活动
     */
    private void filterPortfolio(KeywordLibsPageParam param) {
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList()) || StringUtils.isNotBlank(param.getSearchVal())) {
            String portfolioId = CollectionUtils.isNotEmpty(param.getSearchPortfolioList()) ? String.join(",", param.getSearchPortfolioList()) : "";
            //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsAndShopIdsByPortfolioId(param.getPuid(), param.getShopIds(), portfolioId, param.getSearchVal());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setSearchCampaignList(campaignIds);
            } else {
                // 此处可以直接终结 不查询
                param.setSearchCampaignList(Lists.newArrayList("-1"));
            }
        }
    }

    private Page<OdsAmazonAdKeyword> convert(Page<AmazonAdNeKeyword> amazonAdNeKeywordPage) {
        return new Page<>(amazonAdNeKeywordPage.getPageNo(), amazonAdNeKeywordPage.getPageSize(), amazonAdNeKeywordPage.getTotalPage(), amazonAdNeKeywordPage.getTotalSize(), buildList(amazonAdNeKeywordPage.getRows()));
    }

    private List<OdsAmazonAdKeyword> buildList(List<AmazonAdNeKeyword> amazonAdNeKeywords) {
        if (CollectionUtils.isEmpty(amazonAdNeKeywords)) {
            return new ArrayList<>();
        }
        return amazonAdNeKeywords.stream().map(k -> {
            OdsAmazonAdKeyword odsAmazonAdKeyword = new OdsAmazonAdKeyword();
            odsAmazonAdKeyword.setShopId(k.getShopId());
            odsAmazonAdKeyword.setCampaignId(k.getCampaignId());
            return odsAmazonAdKeyword;
        }).collect(Collectors.toList());
    }

    @Override
    public Page<KeywordLibsGroupListVO> getKeywordDetailGroupListPage(KeywordLibsPageParam param) {
        Integer puid = param.getPuid();
        Page<KeywordLibsGroupListVO> page = new Page<>();
        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        List<KeywordLibsGroupListVO> dataRow = Lists.newArrayList();
        try {
            List<OdsAmazonAdKeyword> list;
            if (Constants.BIDDABLE.equalsIgnoreCase(param.getTargetType())) {
                list = odsAmazonAdKeywordDao.listAllGroupByCondition(puid, param.getShopIds(), param);
            } else {
                list = odsAmazonAdKeywordDao.listAllNeGroupByCondition(puid, param.getShopIds(), param);
            }
            if (CollectionUtils.isEmpty(list)) {
                page.setRows(dataRow);
                return page;
            }
            param.setSearchAdGroupList(list.stream().map(OdsAmazonAdKeyword::getAdGroupId).collect(Collectors.toList()));
            Page<OdsAmazonAdGroup> odsAmazonAdGroupPage = odsAmazonAdGroupDao.listPageByByCondition(puid, param.getShopIds(), param);
            page.setTotalPage(odsAmazonAdGroupPage.getTotalPage());
            page.setTotalSize(odsAmazonAdGroupPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(odsAmazonAdGroupPage.getRows())) {
                dataRow = odsAmazonAdGroupPage.getRows().stream().map(k -> {
                    KeywordLibsGroupListVO keywordLibsGroupListVO = new KeywordLibsGroupListVO();
                    keywordLibsGroupListVO.setAdGroupId(k.getAdGroupId());
                    keywordLibsGroupListVO.setAdGroupName(k.getName());
                    keywordLibsGroupListVO.setShopId(k.getShopId());
                    keywordLibsGroupListVO.setMarketplaceId(k.getMarketplaceId());
                    keywordLibsGroupListVO.setType(k.getAdGroupType());
                    return keywordLibsGroupListVO;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("query keyword lib detail group list by puid end keyword_text error，puid:{},keyword_text:{}, e", puid, param.getKeywordText(), e);
        }
        page.setRows(dataRow);
        return page;
    }

    // 获取statsV2接口搜索词分析搜索词排名及周变化率字段
    private void addSearchTermRankField(List<KeywordLibsVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        // 只获取当前页关键词里的搜索词排名及周变化率字段
        List<String> searchTermList = rows.stream()
                .map(item -> item.getKeywordText().trim().toLowerCase())
                .distinct()
                .collect(Collectors.toList());
        String searchTerms = StringUtil.joinString(searchTermList, StringUtil.SPLIT_COMMA);

        // 调取stateV2接口获取搜索词排名字段
        List<SearchTermsAnalysis> searchTermsAnalysisList = Lists.newArrayList();
        // 抛异常不捕获，避免影响程序执行
        try {
            log.info("start request stateV2 searchTermsAnalysis api");
            long s1 = Instant.now().toEpochMilli();
            searchTermsAnalysisList = searchAnalysisStatsV2Client.getSearchTermAnalysis(searchTerms);
            log.info("调用stateV2接口获取搜索词排名及周变化率，共耗时：{}", Instant.now().toEpochMilli() - s1);
        } finally {
            log.info("end request stateV2 searchTermsAnalysis api");
        }
        if (CollectionUtils.isEmpty(searchTermsAnalysisList)) {
            return;
        }
        Map<String, SearchTermsAnalysis> searchTermsAnalysisMap = searchTermsAnalysisList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(item -> item.getSearchTerm().trim().toLowerCase(), item -> item, (e1, e2) -> e1));
        rows.stream().peek(item -> {
            if (StringUtils.isNotBlank(item.getKeywordText()) && searchTermsAnalysisMap.get(item.getKeywordText().trim().toLowerCase()) != null) {
                SearchTermsAnalysis termsAnalysis = searchTermsAnalysisMap.get(item.getKeywordText().trim().toLowerCase());
                item.setSearchFrequencyRank(termsAnalysis.getSearchFrequencyRank());
                item.setWeekRatio(Optional.ofNullable(termsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            }
        }).collect(Collectors.toList());
    }

    /**
     * 填充ASIN标签列表
     *
     * @param puid
     * @param uid
     * @param keywordLibsVoList
     */
    private void fillAsinTagList(Integer puid, List<Integer> uid, List<KeywordLibsVo> keywordLibsVoList) {
        if (CollectionUtils.isEmpty(keywordLibsVoList)) {
            return;
        }
        Set<Long> keywordIdSet = new HashSet<>();
        for (KeywordLibsVo keywordLibsVo : keywordLibsVoList) {
            keywordIdSet.addAll(keywordLibsVo.getItemVos().stream().map(KeywordLibsVo.KeywordLibsItemVo::getId).collect(Collectors.toSet()));
        }
        List<Long> keywordsLibIdList = new ArrayList<>(keywordIdSet);

        List<AdKeywordLibMarkupAsin> markupAsinList = adKeywordLibMarkupAsinDao.listByKeywordsLibIdsList(puid, uid, keywordsLibIdList);
        if (CollectionUtils.isEmpty(markupAsinList)) {
            return;
        }
        Map<String, Set<Long>> map = markupAsinList.stream().collect(Collectors.groupingBy(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId(), Collectors.mapping(AdKeywordLibMarkupAsin::getKeywordsLibId, Collectors.toSet())));
        Map<Long, List<AdKeywordLibMarkupAsin>> markupAsinMap = markupAsinList.stream().collect(Collectors.groupingBy(AdKeywordLibMarkupAsin::getKeywordsLibId));
        for (KeywordLibsVo keywordLibsVo : keywordLibsVoList) {
            Set<String> asin = new HashSet<>();
            for (KeywordLibsVo.KeywordLibsItemVo keywordLibsItemVo : keywordLibsVo.getItemVos()) {
                List<AdKeywordLibMarkupAsin> list = markupAsinMap.get(keywordLibsItemVo.getId());
                if (list != null) {
                    asin.addAll(list.stream().map(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId()).collect(Collectors.toSet()));
                }
            }
            Set<Long> keywordId = keywordLibsVo.getItemVos().stream().map(KeywordLibsVo.KeywordLibsItemVo::getId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(asin)) {
                keywordLibsVo.setAsinTags(asin.stream().map(e -> {
                    String[] asinArray = e.split(StringUtil.SPECIAL_COMMA);
                    KeywordLibMarkupAsinVo vo = new KeywordLibMarkupAsinVo();
                    vo.setAsin(asinArray[0]);
                    vo.setMarketplaceId(asinArray[1]);
                    vo.setMarketplaceCN(AmznEndpoint.getByMarketplaceId(asinArray[1]).getMarketplaceCN());
                    Set<Long> set = map.get(e);
                    if (set != null) {
                        Set<Long> retainSet = new HashSet<>(set);
                        retainSet.retainAll(keywordId);
                        if (CollectionUtils.isNotEmpty(retainSet)) {
                            vo.setIds(new ArrayList<>(retainSet));
                        }
                    }
                    return vo;
                }).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 批量添加标签
     */
    private void addKeywordLibTag(Integer puid, Integer uid, List<AmazonAdKeywordsLib> keywordsLibList, List<AdKeywordLibTag> keywordLibTags, List<KeywordLibMarkupAsinVo> keywordLibAsinTags, AddKeywordLibVo addKeywordLibVo) {
        // 标记标签
        List<AdKeywordLibMarkupTag> keywordLibMarkupTags = Lists.newArrayList();
        List<AdKeywordLibMarkupAsin> keywordLibMarkupAsins = Lists.newArrayList();

        List<String> realKeywordTexts = keywordsLibList.stream()
                .map(AmazonAdKeywordsLib::getKeywordText)
                .filter(Objects::nonNull).collect(Collectors.toList());
        // 这里就不改了 标记标签 只标记自己的关键词
        List<AmazonAdKeywordsLib> keywordsLibs = amazonAdKeywordsLibDao.getListByKeyowrdTexts(puid, uid, realKeywordTexts);
        // 不存在标签设置不标记标签
        if (CollectionUtils.isNotEmpty(keywordLibTags)) {
            Map<Long, AdKeywordLibTag> adKeywordLibTags = adKeywordLibTagDao.listByUidAndId(puid, uid, keywordLibTags.stream().map(AdKeywordLibTag::getId).collect(Collectors.toList())).stream().collect(Collectors.toMap(AdKeywordLibTag::getId, Function.identity(), (o, n) -> n));
            keywordsLibs.stream()
                    .filter(e -> !addKeywordLibVo.getTagNeedModify().contains(e.getKeywordText())
                            && !addKeywordLibVo.getTagReachedLimit().contains(e.getKeywordText()))
                    .forEach(e -> keywordLibTags.forEach(item -> {
                        AdKeywordLibMarkupTag keywordLibMarkupTag = new AdKeywordLibMarkupTag();
                        keywordLibMarkupTag.setPuid(puid);
                        keywordLibMarkupTag.setUid(adKeywordLibTags.get(item.getId()).getUid() == 0 ? 0 : uid);
                        keywordLibMarkupTag.setType(AdTagTypeEnum.KEYWORDLIB.getType());
                        keywordLibMarkupTag.setCreateId(uid);
                        keywordLibMarkupTag.setUpdateId(uid);
                        keywordLibMarkupTag.setTagId(item.getId());
                        keywordLibMarkupTag.setRelationId(String.valueOf(e.getId()));
                        keywordLibMarkupTags.add(keywordLibMarkupTag);
                    }));
        }

        if (CollectionUtils.isNotEmpty(keywordLibAsinTags)) {
            keywordsLibs.stream()
                    .filter(e -> !addKeywordLibVo.getAsinReachedLimit().contains(e.getKeywordText()) && !addKeywordLibVo.getAsinNeedModify().contains(e.getKeywordText()))
                    .forEach(e -> keywordLibAsinTags.forEach(item -> {
                        AdKeywordLibMarkupAsin adKeywordLibMarkupAsin = new AdKeywordLibMarkupAsin();
                        adKeywordLibMarkupAsin.setPuid(puid);
                        adKeywordLibMarkupAsin.setUid(uid);
                        adKeywordLibMarkupAsin.setKeywordsLibId(e.getId());
                        adKeywordLibMarkupAsin.setAsin(item.getAsin());
                        adKeywordLibMarkupAsin.setMarketplaceId(item.getMarketplaceId());
                        adKeywordLibMarkupAsin.setCreateId(uid);
                        adKeywordLibMarkupAsin.setUpdateId(uid);
                        keywordLibMarkupAsins.add(adKeywordLibMarkupAsin);
                    }));
        }

        if (CollectionUtils.isNotEmpty(keywordLibMarkupTags)) {
            adKeywordLibMarkupTagDao.insertList(keywordLibMarkupTags);
        }
        if (CollectionUtils.isNotEmpty(keywordLibMarkupAsins)) {
            adKeywordLibMarkupAsinDao.batchInsert(keywordLibMarkupAsins);
        }
    }

    private MutablePair<String, String> getMarketplace(String marketplaceId) {
        AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
        if (amznEndpoint != null) {
            if ("A17E79C6D8DWNP".equals(marketplaceId)) {
                //沙特站简写
                return new MutablePair<>(amznEndpoint.getMarketplace(), "沙特");
            } else {
                return new MutablePair<>(amznEndpoint.getMarketplace(), amznEndpoint.getMarketplaceCN());
            }
        }
        return new MutablePair<>("", "");
    }

    /**
     * 构建关键词库列表页vo
     *
     * @param param
     * @param keywordCountMap
     * @param nekeywordCountMap
     * @param reportDataMap
     * @param reportCompDataMap
     * @param dataKeywordMap
     * @param vo
     * @param monitorMap
     */
    private void buildKeywordLibsPageVo(KeywordLibsPageParam param, Map<String, Integer> keywordCountMap,
                                        Map<String, Integer> nekeywordCountMap,
                                        Map<String, KeywordLibsVo> reportDataMap,
                                        Map<String, KeywordLibsVo> reportCompDataMap,
                                        Map<String, List<KeywordLibsVo>> dataKeywordMap,
                                        KeywordLibsVo vo,
                                        Map<Integer, User> mapUser, Map<String, MonitorKeywordVo> monitorMap) {
        vo.setPuid(param.getPuid());
        vo.setUid(param.getUid());
        List<KeywordLibsVo> keywordLibsVo = dataKeywordMap.get(vo.getKeywordText().toLowerCase());
        // 按照Id 由小到大排序
        keywordLibsVo.sort(Comparator.comparing(KeywordLibsVo::getId));
        KeywordLibsVo keywordLibsVo1 = keywordLibsVo.get(0);

        // 查找是否有自己添加的关键词
        Optional<KeywordLibsVo> listUser = keywordLibsVo.stream().filter(k -> param.getUid().equals(k.getUid())).findFirst();
        vo.setId(listUser.isPresent() ? listUser.get().getId() : 0L);
        vo.setRemark(listUser.isPresent() ? listUser.get().getRemark() : "-");
        vo.setCreateTime(listUser.orElse(keywordLibsVo1).getCreateTime());
        vo.setSingleQuantity7d(listUser.orElse(keywordLibsVo1).getSingleQuantity7d());
        vo.setAverageAcos7d(listUser.orElse(keywordLibsVo1).getAverageAcos7d());
        MonitorKeywordVo monitorKeywordVo = monitorMap.get(vo.getKeywordText().toLowerCase());
        if (monitorKeywordVo != null && param.getUid().equals(monitorKeywordVo.getUid())) {
            vo.setKeywordMonitor(monitorKeywordVo.getCount());
        }
        vo.setSource(listUser.orElse(keywordLibsVo1).getSource());
        vo.setAbaRank(listUser.orElse(keywordLibsVo1).getAbaRank());
        vo.setState(listUser.orElse(keywordLibsVo1).getState());
        vo.setItemVos(keywordLibsVo.stream().map(k -> {
            KeywordLibsVo.KeywordLibsItemVo itemVo = new KeywordLibsVo.KeywordLibsItemVo();
            itemVo.setId(k.getId());
            itemVo.setAbaRank(k.getAbaRank());
            itemVo.setUid(k.getUid());
            User user = mapUser.get(k.getUid());
            if (user != null) {
                itemVo.setName(user.getUserNickname());
            }
            itemVo.setAddDate(k.getCreateTime());
            itemVo.setSource(k.getSource());
            itemVo.setRemark(k.getRemark());
            return itemVo;
        }).collect(Collectors.toList()));

        vo.setTargetNum(keywordCountMap.get(vo.getKeywordText().toLowerCase()));
        vo.setNegateTargetNum(nekeywordCountMap.get(vo.getKeywordText().toLowerCase()));
        //报告数据
        KeywordLibsVo reportVo = reportDataMap.get(vo.getKeywordText().toLowerCase());
        if (reportVo != null) {
            if (reportVo.getImpressions() != null) {
                vo.setImpressions(reportVo.getImpressions());
            }
            if (reportVo.getClicks() != null) {
                vo.setClicks(reportVo.getClicks());
            }
            if (reportVo.getSaleNum() != null) {
                vo.setSaleNum(reportVo.getSaleNum());
            }
            if (reportVo.getTotalSales() != null) {
                vo.setTotalSales(reportVo.getTotalSales());
            }
            if (reportVo.getCost() != null) {
                vo.setCost(reportVo.getCost());
            }
            if (reportVo.getCpc() != null) {
                vo.setCpc(reportVo.getCpc());
            }
            if (reportVo.getCpa() != null) {
                vo.setCpa(reportVo.getCpa());
            }
            if (reportVo.getClickRate() != null) {
                vo.setClickRate(reportVo.getClickRate());
            }
            if (reportVo.getSalesConversionRate() != null) {
                vo.setSalesConversionRate(reportVo.getSalesConversionRate());
            }
            if (reportVo.getAcos() != null) {
                vo.setAcos(reportVo.getAcos());
            }
            if (reportVo.getRoas() != null) {
                vo.setRoas(reportVo.getRoas());
            }
        }
        //对比报告数据
        KeywordLibsVo compareReportVo = reportCompDataMap.get(vo.getKeywordText().toLowerCase());
        if (compareReportVo != null) {
            if (compareReportVo.getImpressions() != null) {
                vo.setCompareImpressions(compareReportVo.getImpressions());
            }
            if (compareReportVo.getClicks() != null) {
                vo.setCompareClicks(compareReportVo.getClicks());
            }
            if (compareReportVo.getSaleNum() != null) {
                vo.setCompareSaleNum(compareReportVo.getSaleNum());
            }
            if (compareReportVo.getTotalSales() != null) {
                vo.setCompareTotalSales(compareReportVo.getTotalSales());
            }
            if (compareReportVo.getCost() != null) {
                vo.setCompareCost(compareReportVo.getCost());
            }
            if (compareReportVo.getCpc() != null) {
                vo.setCompareCpc(compareReportVo.getCpc());
            }
            if (compareReportVo.getCpa() != null) {
                vo.setCompareCpa(compareReportVo.getCpa());
            }
            if (compareReportVo.getClickRate() != null) {
                vo.setCompareClickRate(compareReportVo.getClickRate());
            }
            if (compareReportVo.getSalesConversionRate() != null) {
                vo.setCompareSalesConversionRate(compareReportVo.getSalesConversionRate());
            }
            if (compareReportVo.getAcos() != null) {
                vo.setCompareAcos(compareReportVo.getAcos());
            }
            if (compareReportVo.getRoas() != null) {
                vo.setCompareRoas(compareReportVo.getRoas());
            }
            //差异数据报告
            vo.setDiffImpressions(new BigDecimal(vo.getCompareImpressions()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(vo.getImpressions()).subtract(new BigDecimal(vo.getCompareImpressions())).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getCompareImpressions()), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffClicks(new BigDecimal(vo.getCompareClicks()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(vo.getClicks()).subtract(new BigDecimal(vo.getCompareClicks())).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getCompareClicks()), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffSaleNum(new BigDecimal(vo.getCompareSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(vo.getSaleNum()).subtract(new BigDecimal(vo.getCompareSaleNum())).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getCompareSaleNum()), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffTotalSales(vo.getCompareTotalSales().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getTotalSales().subtract(vo.getCompareTotalSales()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareTotalSales(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffCost(vo.getCompareCost().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getCost().subtract(vo.getCompareCost()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareCost(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffCpc(vo.getCompareCpc().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getCpc().subtract(vo.getCompareCpc()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareCpc(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffCpa(vo.getCompareCpa().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getCpa().subtract(vo.getCompareCpa()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareCpa(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffClickRate(vo.getCompareClickRate().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getClickRate().subtract(vo.getCompareClickRate()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareClickRate(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffSalesConversionRate(vo.getCompareSalesConversionRate().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getSalesConversionRate().subtract(vo.getCompareSalesConversionRate()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareSalesConversionRate(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffAcos(vo.getCompareAcos().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getAcos().subtract(vo.getCompareAcos()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareAcos(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffRoas(vo.getCompareRoas().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getRoas().subtract(vo.getCompareRoas()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareRoas(), 2, RoundingMode.HALF_UP).toPlainString());
        }
    }

    /**
     * 关键词库列表页报告排序获取分页后的关键词
     *
     * @param param
     * @param keywordIdList
     * @param keywordPage
     */
    private void getOrderByReportPage(KeywordLibsPageParam param, List<Long> keywordIdList, Page<KeywordLibsVo> keywordPage) {
        List<AdKeywordOrderBo> keywordOrderBos = amazonAdKeywordsLibDao.getKeywordTextByReportingIndex(param.getUidList(), param, keywordIdList);
        //2.1 查询报告获取所有关键词需要排序的指标值或者ABA搜索词排名值，并在内存中进行汇总排序和分页，算出页面展示的关键词keywordTexts
        List<String> keywordTexts = keywordOrderBos.stream().map(AdKeywordOrderBo::getKeywordText).collect(Collectors.toList());
        // 没有词 截断
        if (CollectionUtils.isEmpty(keywordTexts)) {
            keywordPage.setRows(new ArrayList<>());
            return;
        }
        // 关键词库词新增上限，需要将in改为分片查询
        List<AdKeywordOrderBo> spKeywordTextsList = new ArrayList<>();
        List<AdKeywordOrderBo> sbKeywordTextsList = new ArrayList<>();
        List<List<String>> batches = splitList(keywordTexts, 9000);
        for (List<String> batch : batches) {
            List<AdKeywordOrderBo> spKeywordTexts = odsAmazonAdKeywordReportDao.getSpKeywordTexts(param.getPuid(), param.getShopIdList(), param, batch);
            List<AdKeywordOrderBo> sbKeywordTexts = odsAmazonAdSbKeywordReportDao.getSbKeywordTexts(param.getPuid(), param.getShopIdList(), param, batch);
            spKeywordTextsList.addAll(spKeywordTexts);
            sbKeywordTextsList.addAll(sbKeywordTexts);
        }
        Map<String, AdKeywordOrderBo> map = new HashMap<>();
        for (AdKeywordOrderBo keywordOrderBo : keywordOrderBos) {
            map.put(keywordOrderBo.getKeywordText(), null);
        }
        //合并两份报告数据的查询字段的数据
        Map<String, AdKeywordOrderBo> voMap = this.mergeOrderData(map, spKeywordTextsList);
        voMap = this.mergeOrderData(voMap, sbKeywordTextsList);

        //获取ABA排名
        Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
        // 最多连续查两次aba表
        List<OdsWeekSearchTermsAnalysis> weekSearchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(keywordTexts, param.getSite());
        searchTermsAnalysisMap.putAll(weekSearchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o, n) -> n)));

        //根据查询出来的字段进行计算，放入到orderFiled中
        for (AdKeywordOrderBo keyword : keywordOrderBos) {
            String key = keyword.getKeywordText().toLowerCase();
            if (voMap.get(key) != null) {
                AdKeywordOrderBo value = voMap.get(key);
                if (KeywordDataFieldEnum.IMPRESSIONS.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(BigDecimal.valueOf(value.getImpressions()));
                } else if (KeywordDataFieldEnum.CLICKS.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(BigDecimal.valueOf(value.getClicks()));
                } else if (KeywordDataFieldEnum.CLICK_RATE.getCode().equals(param.getOrderField())) {
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(value.getClicks()), BigDecimal.valueOf(value.getImpressions()));
                    BigDecimal orderField = MathUtil.multiply(divide, RATE);
                    keyword.setOrderField(orderField);
                } else if (KeywordDataFieldEnum.COST.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(value.getCost());
                } else if (KeywordDataFieldEnum.ORDER_NUM.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(BigDecimal.valueOf(value.getAdOrder()));
                } else if (KeywordDataFieldEnum.CONVERSION_RATE.getCode().equals(param.getOrderField())) {
                    BigDecimal orderField = MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(value.getAdOrder()), BigDecimal.valueOf(value.getClicks())), RATE);
                    keyword.setOrderField(orderField);
                } else if (KeywordDataFieldEnum.TOTAL_SALES.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(value.getAdSales());
                } else if (KeywordDataFieldEnum.CPC.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(MathUtil.divideCompatibleZero(value.getCost(), BigDecimal.valueOf(value.getClicks())));
                } else if (KeywordDataFieldEnum.CPA.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(MathUtil.divideCompatibleZero(value.getCost(), BigDecimal.valueOf(value.getAdOrder())));
                } else if (KeywordDataFieldEnum.ACOS.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(MathUtil.multiply(MathUtil.divideCompatibleZero(value.getCost(), value.getAdSales()), RATE));
                } else if (KeywordDataFieldEnum.ROAS.getCode().equals(param.getOrderField())) {
                    keyword.setOrderField(MathUtil.multiply(MathUtil.divideCompatibleZero(value.getAdSales(), value.getCost()), RATE));
                }
            }
            OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(keyword.getKeywordText().toLowerCase());
            if (KeywordDataFieldEnum.SEARCH_FREQUENCY_RANK.getCode().equalsIgnoreCase(param.getOrderField())) {
                // 兼容特殊排序逻辑
                keyword.setOrderField(Optional.ofNullable(searchTermsAnalysis).map(OdsWeekSearchTermsAnalysis::getSearchFrequencyRank).map(String::valueOf).map(BigDecimal::new).orElse(new BigDecimal(Integer.MAX_VALUE)));
            }
            if (KeywordDataFieldEnum.WEEK_RATIO.getCode().equalsIgnoreCase(param.getOrderField())) {
                // 兼容特殊排序逻辑
                keyword.setOrderField(Optional.ofNullable(searchTermsAnalysis).map(OdsWeekSearchTermsAnalysis::getWeekRatio).orElse(new BigDecimal(Integer.MIN_VALUE)));
            }
        }

        //对合并后的数据进行排序，根据orderFiled字段里的数据进行排序
        List<AdKeywordOrderBo> orderList = AdPageUtil.getOrderPage(param.getPageNo(), param.getPageSize(), keywordOrderBos, param.getOrderType(), keywordPage);
        List<KeywordLibsVo> keywordList = orderList.stream().map(bo -> {
            KeywordLibsVo vo = new KeywordLibsVo();
            vo.setKeywordText(bo.getKeywordText());
            OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(bo.getKeywordText().toLowerCase());
            if (searchTermsAnalysis != null) {
                vo.setSearchFrequencyRank(Optional.ofNullable(searchTermsAnalysis).map(OdsWeekSearchTermsAnalysis::getSearchFrequencyRank).orElse(0));
                vo.setWeekRatio(Optional.ofNullable(searchTermsAnalysis).map(OdsWeekSearchTermsAnalysis::getWeekRatio).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
            return vo;
        }).collect(Collectors.toList());
        keywordPage.setRows(keywordList);
    }

    private boolean checkTagExist(Integer puid, Integer uid, List<KeywordLibsVo> vos) {
        Set<Long> ids = new HashSet<>();
        for (KeywordLibsVo vo : vos) {
            List<AdKeywordLibTag> adTags = vo.getAdTags();
            if (CollectionUtils.isNotEmpty(adTags)) {
                ids.addAll(adTags.stream().map(AdKeywordLibTag::getId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            int count = adKeywordLibTagDao.getByAllUidAndIdList(puid, uid, new ArrayList<>(ids));
            return count == ids.size();
        }
        return true;
    }
}
