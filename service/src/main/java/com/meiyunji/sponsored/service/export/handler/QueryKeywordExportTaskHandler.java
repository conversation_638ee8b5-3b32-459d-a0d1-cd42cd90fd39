package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.base.AdReportBeanConvertProcess;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.helper.CpcPageWordRootHelper;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service.ICpcQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service.ICpcSbQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCommonService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdStrategyVo;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.ReportExcelVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.enums.AdvertisingQueryKeywordExportFieldEnum;
import com.meiyunji.sponsored.service.enums.SbMatchValueEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.util.GrayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.base.AdReportBeanConvertProcess.convertRpcVoToReportVo;

@Slf4j
@Service(AdManagePageExportTaskConstant.QUERY_KEYWORD)
public class QueryKeywordExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcSbQueryKeywordReportService cpcSbQueryKeywordReportService;
    @Autowired
    private ICpcQueryKeywordReportService cpcQueryKeywordReportService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private ICpcCommonService cpcCommonService;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;
    private static String currency;

    /**
     * 导出
     *
     * @param task
     */
    @Override
    public void export(AdManagePageExportTask task) {
        CpcQueryWordDto param = JSONUtil.jsonToObject(task.getParam(), CpcQueryWordDto.class);
        if (param == null) {
            log.error(String.format("ad-query-搜索词(querywords) export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
        currency = AmznEndpoint.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode().value();
        String fileName = auth.getName() + "_搜索词" + "_" + param.getStart() + "_" + param.getEnd();
        Page pageTemp = new Page();
        pageTemp.setPageNo(1);
        pageTemp.setPageSize(Constants.EXPORT_MAX_SIZE);
        Page page;

        boolean supportAbaRankOrder = GrayUtil.isHit(param.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        param.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(param, false));

        if ("sb".equalsIgnoreCase(param.getType())) {
            page = cpcSbQueryKeywordReportService.dorisPageExportList(param.getPuid(), param, pageTemp);
            // 填充广告策略标签
            cpcSbQueryKeywordReportService.fillAdStrategy(param, page.getRows());
        } else {
            page = cpcQueryKeywordReportService.dorisPageExportList(param.getPuid(), param, pageTemp);
            // 填充广告策略标签
            cpcQueryKeywordReportService.fillAdStrategy(param, page.getRows());
        }
        // 选词根会有问题,还是先全部查doris
//        else {
//            cpcCommonService.clearUnsupportedOrderField(param);
//            if ("sb".equalsIgnoreCase(param.getType())) {
//                page = cpcSbQueryKeywordReportService.pageManageList(param.getPuid(), param, pageTemp);
//            } else {
//                page = cpcQueryKeywordReportService.pageManageList(param.getPuid(), param, pageTemp);
//            }
//
//            if (supportAbaRankExport) {
//                List<ReportVo> rows = page.getRows();
//                Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
//                List<String> searchTerms = rows.stream().map(ReportVo::getQuery).distinct().collect(Collectors.toList());
//                List<OdsWeekSearchTermsAnalysis> searchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, param.getMarketplaceId());
//                searchTermsAnalysisMap.putAll(searchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o, n) -> n)));
//                rows.forEach(e -> {
//                    OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(e.getQuery().toLowerCase());
//                    if (searchTermsAnalysis != null) {
//                        e.setSearchFrequencyRank(searchTermsAnalysis.getSearchFrequencyRank());
//                        e.setWeekRatio(Optional.ofNullable(searchTermsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
//                    }
//                });
//            }
//        }
        List<ReportVo> rows = page.getRows();
        List<ReportRpcVo> rpcVos;
        if (CollectionUtils.isNotEmpty(rows)) {
            rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                ReportRpcVo.Builder builder1 = convertVoToRpcMessage(item);
                if ("sp".equalsIgnoreCase(param.getType())) {
                    builder1.setOrderNum(item.getSaleNum() == null ? Int32Value.of(0) : Int32Value.of(item.getSaleNum()));
                    builder1.setSaleNum(item.getOrderNum() == null ? Int32Value.of(0) : Int32Value.of(item.getOrderNum()));
                    if (item.getSaleNum() != null && item.getClicks() != null) {
                        Double aDouble = calculationRateDouble(Double.valueOf(item.getOrderNum()), Double.valueOf(item.getClicks()));
                        builder1.setSalesConversionRate(DoubleValue.of(aDouble));
                    }
                }
                return builder1.build();
            }).collect(Collectors.toList());
        } else {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        List<List<ReportRpcVo>> partition = Lists.partition(rpcVos, Constants.EXPORT_MAX_SIZE);

        List<String> urlList;
        if (StringUtils.isNotBlank(param.getExportSortField()) && Objects.nonNull(param.getFreezeNum())) {
            urlList = newExport(partition, param, fileName, auth, supportAbaRankOrder);
        } else {
            urlList = oldExport(partition, param, fileName);
        }

        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private List<String> newExport(List<List<ReportRpcVo>> partition, CpcQueryWordDto param, String fileName, ShopAuth shop, boolean supportAbaRankExport) {
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();
        //组装需要排除的字段
        List<String> excludeFileds;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("ordersNewToBrand14d", "orderRateNewToBrand14d", "salesNewToBrand14d", "salesRateNewToBrand14d", "ordersNewToBrandPercentage14d", "impressionRank", "impressionShare",
                "video5SecondViews", "video5SecondViewRate", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews", "videoCompleteViews", "videoUnmutes", "viewabilityRate", "viewClickThroughRate", "viewImpressions");
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("adSaleNum", "adOtherOrderNum", "adSales", "adOtherSales", "saleNum", "adSelfSaleNum", "adOtherSaleNum", "orderNumPercentage");
        } else {
            excludeFileds = Lists.newArrayList();
        }
        if (!supportAbaRankExport) {
            excludeFileds.add("searchFrequencyRank");
            excludeFileds.add("weekRatio");
        }
        excludeFileds.addAll(Lists.newArrayList(param.getExcludList()));

        //自定义排序：根据param的exportSortField，中的字段作为表格中的表头字段顺序导出
        List<String> sortFields = Arrays.asList(param.getExportSortField().split(","));
        //排除字段
        sortFields = sortFields.stream().filter(item -> !excludeFileds.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortFields)) {
            return downloadUrl;
        }
        // 默认导出广告策略标签
        sortFields.add("adStrategyTag");
        //导出
        for (List<ReportRpcVo> partitionList : partition) {
            List<ReportExcelVo> listExports = new LinkedList<>();
            for (ReportRpcVo reportVo : partitionList) {
                ReportVo vo = AdReportBeanConvertProcess.convertRpcVoToReportVo(reportVo);
                ReportExcelVo excelVo = getExcelVo(vo, currency);
                // 广告策略标签筛选
                if(CollectionUtils.isNotEmpty(reportVo.getAdStrategysList())){
                    List<String> adStrategyTag = reportVo.getAdStrategysList().stream().map(it-> AdQueryStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
                    excelVo.setAdStrategyTag(String.join(",", adStrategyTag));
                }else{
                    excelVo.setAdStrategyTag("");
                }
                listExports.add(excelVo);
            }
            if (CollectionUtils.isEmpty(listExports)) {
                continue;
            }

            String url = customFieldSortExport(listExports, sortFields, param.getFreezeNum(), shop, fileName);
            downloadUrl.add(url);
        }
        return downloadUrl;

    }

    private String customFieldSortExport(List<ReportExcelVo> reportExcelVos, List<String> sortFields, Integer freezeNum, ShopAuth shop, String fileName) {

        //Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild()
            .rate();
        //冻结前n列前1行
        if (freezeNum != null) {
            build.freezeRowAndCol(freezeNum, 1);
        }
        build.noModleHandler(getCurrencyIndex(sortFields));

        //构建行
        List<List<Object>> rows = new ArrayList<>(reportExcelVos.size());
        for (ReportExcelVo cpVo : reportExcelVos) {
            rows.add(buildExportSingleRow(cpVo, sortFields));
        }
        //构建表头
        List<String> headNames = new ArrayList<>(sortFields.size());
        for (String sortField : sortFields) {
            AdvertisingQueryKeywordExportFieldEnum fieldEnum = AdvertisingQueryKeywordExportFieldEnum.fromPoParamKey(sortField);
            if (fieldEnum == null) {
                log.error("sortFields 包含非法字符，导出阻止，返回空, sortFields:{}", sortFields);
                throw new RuntimeException("sortFields 包含非法字符，导出阻止，返回空, sortFields:" + sortFields);
            }
            headNames.add(fieldEnum.getTableColName());
        }
        //导出
        return excelService.exportByCustomColSort(shop.getPuid(), headNames, rows, fileName + "(" + (0) + ")", build);
    }

    private List<Object> buildExportSingleRow(ReportExcelVo cpVo, List<String> sortFields) {
        List<Object> cols = new ArrayList<>(AdvertisingQueryKeywordExportFieldEnum.values().length);
        for (String fieldName : sortFields) {
            AdvertisingQueryKeywordExportFieldEnum fieldEnum = AdvertisingQueryKeywordExportFieldEnum.fromPoParamKey(fieldName);
            if (fieldEnum == null) {
                return Collections.emptyList();
            }
            Object value = getObjectByField(cpVo, fieldEnum);
            cols.add(value);
        }

        return cols;
    }

    private Object getObjectByField(ReportExcelVo keyVo, AdvertisingQueryKeywordExportFieldEnum fieldEnum) {
        Object value = null;
        switch (fieldEnum) {
            case QUERY:
                value = keyVo.getQuery();
                break;
            case QUERY_CN:
                value = keyVo.getQueryCn();
                break;
            case ADVERTISING_UNIT_PRICE:
                value = keyVo.getAdvertisingUnitPrice();
                break;
            case AD_ORDER_NUM:
                value = keyVo.getOrderNum();
                break;
            case AD_ORDER_NUM_PERCENTAGE:
                value = keyVo.getAdOrderNumPercentage();
                break;
            case CVR:
                value = keyVo.getSalesConversionRate();
                break;
            case PUTIN:
                value = keyVo.getKeywordText();
                break;
            case MATCH_TYPE:
                value = keyVo.getMatchType();
                break;
            case GROUP_NAME:
                value = keyVo.getAdGroupName();
                break;
            case CAMPAIGN_NAME:
                value = keyVo.getCampaignName();
                break;
            case PORTFOLIO_NAME:
                value = keyVo.getPortfolioName();
                break;
            case AD_COST:
                value = keyVo.getCost();
                break;
            case AD_COST_PERCENTAGE:
                value = keyVo.getAdCostPercentage();
                break;
            case IMPRESSIONS:
                value = keyVo.getImpressions();
                break;
            case CLICKS:
                value = keyVo.getClicks();
                break;
            case CPA:
                value = keyVo.getCpa();
                break;
            case CPC:
                value = keyVo.getCpc();
                break;
            case CTR:
                value = keyVo.getClickRate();
                break;
            case ACOS:
                value = keyVo.getAcos();
                break;
            case ROAS:
                value = keyVo.getRoas();
                break;
            case ACOTS:
                value = keyVo.getAcots();
                break;
            case ASOTS:
                value = keyVo.getAsots();
                break;
            case SELF_AD_ORDER_NUM:
                value = keyVo.getAdSaleNum();
                break;
            case OTHER_AD_ORDER_NUM:
                value = keyVo.getAdOtherOrderNum();
                break;
            case AD_SALE:
                value = keyVo.getSales();
                break;
            case AD_SALE_PERCENTAGE:
                value = keyVo.getAdSalePercentage();
                break;
            case AD_SELF_SALE:
                value = keyVo.getAdSales();
                break;
            case AD_OTHER_SALES:
                value = keyVo.getAdOtherSales();
                break;
            case AD_SALE_NUM:
                value = keyVo.getSaleNum();
                break;
            case AD_SALE_NUM_PERCENTAGE:
                value = keyVo.getOrderNumPercentage();
                break;
            case AD_SELF_SALE_NUM:
                value = keyVo.getAdSelfSaleNum();
                break;
            case AD_OTHER_SALE_NUM:
                value = keyVo.getAdOtherSaleNum();
                break;
            case IMPRESSION_RANK:
                value = keyVo.getImpressionRank();
                break;
            case IMPRESSION_SHARE:
                value = keyVo.getImpressionShare();
                break;
            case ORDERS_NEW_TO_BRAND_FTD:
                value = keyVo.getOrdersNewToBrand14d();
                break;
            case ORDER_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getOrderRateNewToBrand14d();
                break;
            case ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD:
                value = keyVo.getOrdersNewToBrandPercentage14d();
                break;
            case SALES_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesNewToBrand14d();
                break;
            case SALES_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesRateNewToBrand14d();
                break;
            case VIDEO_5_SECOND_VIEWS:
                value = keyVo.getVideo5SecondViews();
                break;
            case VIDEO_5_SECOND_VIEW_RATE:
                value = keyVo.getVideo5SecondViewRate();
                break;
            case VIDEO_FIRST_QUARTILE_VIEWS:
                value = keyVo.getVideoFirstQuartileViews();
                break;
            case VIDEO_MIDPOINT_VIEWS:
                value = keyVo.getVideoMidpointViews();
                break;
            case VIDEO_THIRD_QUARTILE_VIEWS:
                value = keyVo.getVideoThirdQuartileViews();
                break;
            case VIDEO_COMPLETE_VIEWS:
                value = keyVo.getVideoCompleteViews();
                break;
            case VIDEO_UNMUTES:
                value = keyVo.getVideoUnmutes();
                break;
            case VIEW_IMPRESSIONS:
                value = keyVo.getViewImpressions();
                break;
            case VIEWABILITY_RATE:
                value = keyVo.getViewabilityRate();
                break;
            case VIEW_CLICK_THROUGH_RATE:
                value = keyVo.getViewClickThroughRate();
                break;
            case SEARCH_FREQUENCY_RANK:
                value = keyVo.getSearchFrequencyRank();
                break;
            case WEEK_RATIO:
                value = keyVo.getWeekRatio();
                break;
            case QUERY_TAG:
                value = keyVo.getQueryTag();
                break;
            case AD_STRATEGY_TAG:
                value = keyVo.getAdStrategyTag();
                break;
            default:
        }
        return value;
    }

    private List<Integer> getCurrencyIndex(List<String> sortFields) {
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            AdvertisingQueryKeywordExportFieldEnum keywordExportFieldEnum = AdvertisingQueryKeywordExportFieldEnum.fromPoParamKey(sortFields.get(i));
            //出现这种情况就是有问题，暂时不考虑
            if (keywordExportFieldEnum == null) {
                return Collections.emptyList();
            }
            if (keywordExportFieldEnum.getCurrencyStyle()) {
                currencyIndex.add(i);
            }
        }

        return currencyIndex;
    }

    private List<String> oldExport(List<List<ReportRpcVo>> partition, CpcQueryWordDto param, String fileName) {
        boolean supportAbaRankExport = GrayUtil.isHit(param.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankExportWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        int count = 0;
        WriteHandlerBuild build = setWriteHandlerBuild().currencyNew(ReportExcelVo.class);

        List<String> urlList = new ArrayList<>();
        for (List<ReportRpcVo> reportVos : partition) {
            List<ReportExcelVo> listExports = Lists.newLinkedList();
            for (ReportRpcVo reportVo : reportVos) {
                ReportVo vo = AdReportBeanConvertProcess.convertRpcVoToReportVo(reportVo);
                ReportExcelVo excelVo = getExcelVo(vo, currency);
                // 广告策略标签筛选
                if(CollectionUtils.isNotEmpty(reportVo.getAdStrategysList())){
                    List<String> adStrategyTag = reportVo.getAdStrategysList().stream().map(it-> AdQueryStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
                    excelVo.setAdStrategyTag(String.join(",", adStrategyTag));
                }else{
                    excelVo.setAdStrategyTag("");
                }
                listExports.add(excelVo);
            }
            if (CollectionUtils.isNotEmpty(listExports)) {
                List<String> excludeFileds = Lists.newArrayList();
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("ordersNewToBrand14d", "orderRateNewToBrand14d", "salesNewToBrand14d", "salesRateNewToBrand14d", "ordersNewToBrandPercentage14d", "impressionRank", "impressionShare",
                        "video5SecondViews", "video5SecondViewRate", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews", "videoCompleteViews", "videoUnmutes", "viewabilityRate", "viewClickThroughRate", "viewImpressions");
                }
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("adSaleNum", "adOtherOrderNum", "adSales", "adOtherSales", "saleNum", "adSelfSaleNum", "adOtherSaleNum", "orderNumPercentage");
                }
                if (!supportAbaRankExport) {
                    excludeFileds.add("searchFrequencyRank");
                    excludeFileds.add("weekRatio");
                }
                excludeFileds.addAll(Lists.newArrayList(param.getExcludList()));
                urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), listExports, fileName + "(" + count++ + ")", ReportExcelVo.class, build, excludeFileds));
            }
        }
        return urlList;
    }

    private static WriteHandlerBuild setWriteHandlerBuild() {
        WriteHandlerBuild build = new WriteHandlerBuild();
        build = build.rate();
        //需要进行格式化的表头
        List<String> formatHeads = new ArrayList<>();
        formatHeads.add("impressions");
        formatHeads.add("clicks");
        build.integerFormat(formatHeads);
        return build;
    }

    private static Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1 * 100, value2, 2);
    }

    private static ReportExcelVo buildExportVo(ReportRpcVo reportRpcVo) {
        ReportVo vo = convertRpcVoToReportVo(reportRpcVo);
        return getExcelVo(vo, currency);
    }

    private static ReportRpcVo.Builder convertVoToRpcMessage(ReportVo reportVo) {
        ReportRpcVo.Builder reportBuilder = ReportRpcVo.newBuilder();

        if (reportVo != null) {
            if (reportVo.getShopId() != null) {
                reportBuilder.setShopId(Int32Value.of(reportVo.getShopId()));
            }
            if (reportVo.getCountDate() != null) {
                reportBuilder.setCountDate(reportVo.getCountDate());
            }
            reportBuilder.setCpc(DoubleValue.of(Optional.ofNullable(reportVo.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setCost(DoubleValue.of(Optional.ofNullable(reportVo.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setSales(DoubleValue.of(Optional.ofNullable(reportVo.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAcos(DoubleValue.of(Optional.ofNullable(reportVo.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setRoas(DoubleValue.of(Optional.ofNullable(reportVo.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAcots(DoubleValue.of(Optional.ofNullable(reportVo.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAsots(DoubleValue.of(Optional.ofNullable(reportVo.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setImpressions(Int32Value.of(Optional.ofNullable(reportVo.getImpressions()).orElse(0)));
            reportBuilder.setClicks(Int32Value.of(Optional.ofNullable(reportVo.getClicks()).orElse(0)));
            reportBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getOrderNum()).orElse(0)));
            reportBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getSaleNum()).orElse(0)));
            reportBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(reportVo.getClickRate()).orElse(0.0)));
            reportBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getSalesConversionRate()).orElse(0.0)));
            reportBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getAdConversionRate()).orElse(0.0)));
            reportBuilder.setCpa(DoubleValue.of(Optional.ofNullable(reportVo.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(reportVo.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setNaturalClicks(Optional.ofNullable(reportVo.getNaturalClicks()).orElse("0"));
            reportBuilder.setNaturalOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getNaturalOrderNum()).orElse(0)));
            reportBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(reportVo.getAdClickRatio()).orElse(0.0)));
            reportBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getAdConversionRate()).orElse(0.0)));
            // 广告花费占比
            reportBuilder.setAdCostPercentage(StringUtils.isNotBlank(reportVo.getAdCostPercentage()) ? reportVo.getAdCostPercentage() : "0.00");
            // 广告销售额占比
            reportBuilder.setAdSalePercentage(StringUtils.isNotBlank(reportVo.getAdSalePercentage()) ? reportVo.getAdSalePercentage() : "0.00");
            // 广告订单量占比
            reportBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(reportVo.getAdOrderNumPercentage()) ? reportVo.getAdOrderNumPercentage() : "0.00");
            // 广告销量占比
            reportBuilder.setOrderNumPercentage(StringUtils.isNotBlank(reportVo.getOrderNumPercentage()) ? reportVo.getOrderNumPercentage() : "0.00");

            if (reportVo.getCampaignId() != null) {
                reportBuilder.setCampaignId(reportVo.getCampaignId());
            }
            if (reportVo.getCampaignName() != null) {
                reportBuilder.setCampaignName(reportVo.getCampaignName());
            }
            if (reportVo.getAdGroupId() != null) {
                reportBuilder.setAdGroupId(reportVo.getAdGroupId());
            }
            if (reportVo.getAdGroupName() != null) {
                reportBuilder.setAdGroupName(reportVo.getAdGroupName());
            }
            if (reportVo.getKeywordText() != null) {
                reportBuilder.setKeywordText(reportVo.getKeywordText());
            }
            if (reportVo.getMatchType() != null) {
                reportBuilder.setMatchType(reportVo.getMatchType());
            }
            if (reportVo.getKeywordId() != null) {
                reportBuilder.setKeywordId(reportVo.getKeywordId());
            }
            if (reportVo.getSku() != null) {
                reportBuilder.setSku(reportVo.getSku());
            }
            if (reportVo.getAsin() != null) {
                reportBuilder.setAsin(reportVo.getAsin());
            }
            if (reportVo.getParentAsin() != null) {
                reportBuilder.setParentAsin(reportVo.getParentAsin());
            }
            if (reportVo.getTitle() != null) {
                reportBuilder.setTitle(reportVo.getTitle());
            }
            if (reportVo.getMainImage() != null) {
                reportBuilder.setMainImage(reportVo.getMainImage());
            }
            if (reportVo.getQuery() != null) {
                reportBuilder.setQuery(reportVo.getQuery());
            }
            if (reportVo.getQueryCn() != null) {
                reportBuilder.setQueryCn(reportVo.getQueryCn());
            }
            if (reportVo.getNegaType() != null) {
                reportBuilder.setNegaType(reportVo.getNegaType());
            }
            if (reportVo.getTargetingType() != null) {
                reportBuilder.setTargetingType(reportVo.getTargetingType());
            }
            if (reportVo.getTargetingExpression() != null) {
                reportBuilder.setTargetingExpression(reportVo.getTargetingExpression());
            }
            if (reportVo.getTargetId() != null) {
                reportBuilder.setTargetId(reportVo.getTargetId());
            }
            if (reportVo.getAdId() != null) {
                reportBuilder.setAdId(reportVo.getAdId());
            }
            if (reportVo.getTargetingText() != null) {
                reportBuilder.setTargetingText(reportVo.getTargetingText());
            }
            if (reportVo.getSpCampaignType() != null) {
                reportBuilder.setSpCampaignType(reportVo.getSpCampaignType());
            }
            if (reportVo.getSpGroupType() != null) {
                reportBuilder.setSpGroupType(reportVo.getSpGroupType());
            }
            if (reportVo.getSpTargetType() != null) {
                reportBuilder.setSpTargetType(reportVo.getSpTargetType());
            }
            if (reportVo.getPortfolioName() != null) {
                reportBuilder.setPortfolioName(reportVo.getPortfolioName());
            }
            //标签填充
            if (reportVo.getIsBroad() != null) {
                reportBuilder.setIsBroad(BoolValue.of(reportVo.getIsBroad()));
            }
            if (reportVo.getIsPhrase() != null) {
                reportBuilder.setIsPhrase(BoolValue.of(reportVo.getIsPhrase()));
            }
            if (reportVo.getIsExact() != null) {
                reportBuilder.setIsExact(BoolValue.of(reportVo.getIsExact()));
            }
            if (reportVo.getIsNegativeExact() != null) {
                reportBuilder.setIsNegativeExact(BoolValue.of(reportVo.getIsNegativeExact()));
            }
            if (reportVo.getIsNegativePhrase() != null) {
                reportBuilder.setIsNegativePhrase(BoolValue.of(reportVo.getIsNegativePhrase()));
            }
            /**
             * TODO 广告报告重构
             * 本广告产品销售额
             */
            reportBuilder.setAdSales(DoubleValue.of(Optional.ofNullable(reportVo.getAdSales()).orElse(BigDecimal.ZERO).doubleValue()));
            //本广告产品订单量
            reportBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdSaleNum()).orElse(0)));
            //本广告产品销量
            reportBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdSelfSaleNum()).orElse(0)));
            //其他产品广告订单量
            reportBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getAdOtherOrderNum()).orElse(0)));
            //其他产品广告销售额
            reportBuilder.setAdOtherSales(DoubleValue.of(Optional.ofNullable(reportVo.getAdOtherSales()).orElse(BigDecimal.ZERO).doubleValue()));
            //其他产品广告销量
            reportBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdOtherSaleNum()).orElse(0)));
            //“品牌新买家”订单量
            if (reportVo.getOrdersNewToBrand14d() != null) {
                reportBuilder.setOrdersNewToBrandFTD(Int32Value.of(reportVo.getOrdersNewToBrand14d()));
            }
            //“品牌新买家”销售额
            if (reportVo.getSalesNewToBrand14d() != null) {
                reportBuilder.setSalesNewToBrandFTD(DoubleValue.of(reportVo.getSalesNewToBrand14d().doubleValue()));
            }
            //搜索词展示量排名
            if (reportVo.getImpressionRank() != null) {
                reportBuilder.setImpressionRank(Int32Value.of(reportVo.getImpressionRank()));
            }
            //搜索词展示份额
            if (reportVo.getImpressionShare() != null) {
                reportBuilder.setImpressionShare(DoubleValue.of(reportVo.getImpressionShare().doubleValue()));
            }
            //“品牌新买家”订单百分比
            if (reportVo.getOrderRateNewToBrand14d() != null) {
                reportBuilder.setOrderRateNewToBrandFTD(DoubleValue.of(reportVo.getOrderRateNewToBrand14d()));
            }
            //“品牌新买家”销售额百分比
            if (reportVo.getSalesRateNewToBrand14d() != null) {
                reportBuilder.setSalesRateNewToBrandFTD(DoubleValue.of(reportVo.getSalesRateNewToBrand14d()));
            }
            //“品牌新买家”订单转化率
            if (reportVo.getOrdersNewToBrandPercentage14d() != null) {
                reportBuilder.setOrdersNewToBrandPercentageFTD(DoubleValue.of(reportVo.getOrdersNewToBrandPercentage14d()));
            }

            reportBuilder.setVideo5SecondViews(Optional.ofNullable(reportVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideo5SecondViewRate(Optional.ofNullable(reportVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoFirstQuartileViews(Optional.ofNullable(reportVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoMidpointViews(Optional.ofNullable(reportVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoThirdQuartileViews(Optional.ofNullable(reportVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoCompleteViews(Optional.ofNullable(reportVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoUnmutes(Optional.ofNullable(reportVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewImpressions(Optional.ofNullable(reportVo.getViewImpressions()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewabilityRate(Optional.ofNullable(reportVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewClickThroughRate(Optional.ofNullable(reportVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setAdvertisingUnitPrice(Optional.ofNullable(reportVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
            reportBuilder.setSearchFrequencyRank(reportVo.getSearchFrequencyRank());
            reportBuilder.setWeekRatio(Optional.ofNullable(reportVo.getWeekRatio()).map(String::valueOf).orElse("0"));
            // 广告策略标签信息
            if(CollectionUtils.isNotEmpty(reportVo.getStrategyList())){
                List<com.meiyunji.sponsored.rpc.vo.AdStrategy> strategyList = new ArrayList<>();
                for (AdStrategyVo strategyVo : reportVo.getStrategyList()) {
                    com.meiyunji.sponsored.rpc.vo.AdStrategy.Builder strategyBuilder = com.meiyunji.sponsored.rpc.vo.AdStrategy.newBuilder();
                    strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
                    strategyBuilder.setStatus(strategyVo.getStatus());
                    strategyList.add(strategyBuilder.build());
                }
                reportBuilder.addAllAdStrategys(strategyList);
            }
        }

        return reportBuilder;
    }

    private static ReportExcelVo getExcelVo(ReportVo obj, String currency) {
        ReportExcelVo excelVo = new ReportExcelVo();
        excelVo.setCountDate(obj.getCountDate());
        excelVo.setQuery(obj.getQuery());
        excelVo.setQueryCn(obj.getQueryCn());
        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(obj.getKeywordText()) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(obj.getKeywordText())) {
            excelVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(obj.getKeywordText()) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(obj.getKeywordText())) {
            excelVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
        } else {
            excelVo.setKeywordText(obj.getKeywordText());
        }

        SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(obj.getKeywordText());

        if (keywordGroupValueEnumByTextCn  != null) {
            excelVo.setKeywordText(keywordGroupValueEnumByTextCn.getTextCn());
        }

        if (StringUtils.isNotEmpty(obj.getMatchType())) {
            if (Constants.PHRASE.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("词组匹配");
            } else if (Constants.EXACT.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("精确匹配");
            } else if (Constants.BROAD.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("广泛匹配");
            } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("紧密匹配");
            } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("宽泛匹配");
            } else if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("主题");
            } else {
                excelVo.setMatchType(obj.getMatchType());
            }
        }
        if (StringUtils.isNotEmpty(obj.getTargetingExpression())) {
            if (Constants.TARGETING_EXPRESSION_SUBSTITUTES.equalsIgnoreCase(obj.getTargetingExpression())) {
                excelVo.setTargetingExpression("同类商品");
            } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getTargetingExpression())) {
                excelVo.setTargetingExpression("紧密匹配");
            } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getTargetingExpression())) {
                excelVo.setTargetingExpression("宽泛匹配");
            } else if (Constants.TARGETING_EXPRESSION_COMPLEMENTS.equalsIgnoreCase(obj.getTargetingExpression())) {
                excelVo.setTargetingExpression("关联商品");
            } else {
                excelVo.setTargetingExpression(obj.getTargetingExpression());
            }
        }
        //导出标签填充
        List<String> queryTagList = new ArrayList<>();
        if (obj.getIsBroad() != null && obj.getIsBroad()) {
            queryTagList.add("投-广泛");
        }
        if (obj.getIsExact() != null && obj.getIsExact()) {
            queryTagList.add("投-精确");
        }
        if (obj.getIsPhrase() != null && obj.getIsPhrase()) {
            queryTagList.add("投-词组");
        }
        if (obj.getIsNegativePhrase() != null && obj.getIsNegativePhrase()) {
            queryTagList.add("否-词组");
        }
        if (obj.getIsNegativeExact() != null && obj.getIsNegativeExact()) {
            queryTagList.add("否-精确");
        }
        if (queryTagList.isEmpty()) {
            excelVo.setQueryTag("无标签");
        } else {
            StringBuilder queryTagBuilder = new StringBuilder();
            for (int i = 0; i < queryTagList.size(); i++) {
                queryTagBuilder.append(queryTagList.get(i));
                if (i < queryTagList.size() - 1) {
                    queryTagBuilder.append(",");
                }
            }
            excelVo.setQueryTag(queryTagBuilder.toString());
        }
        excelVo.setAdGroupName(obj.getAdGroupName());
        excelVo.setCampaignName(obj.getCampaignName());
        if (StringUtils.isNotEmpty(obj.getTargetingType())) {
            excelVo.setTargetingType(Constants.AUTO.equals(obj.getTargetingType()) ? "自动" : "手动");
        }
        excelVo.setPortfolioName(obj.getPortfolioName());
        excelVo.setImpressions(obj.getImpressions());
        excelVo.setClicks(obj.getClicks());
        excelVo.setRoas(obj.getRoas() == null ? "-" : obj.getRoas().toString());
        excelVo.setClickRate(obj.getClickRate() == null ? "-" : obj.getClickRate() + "%");
        excelVo.setCost(obj.getCost() != null ? currency + obj.getCost() : "-");
        excelVo.setCpc(obj.getCpc() != null ? currency + obj.getCpc() : "-");
        excelVo.setOrderNum(obj.getSaleNum());
        excelVo.setSalesConversionRate(obj.getSalesConversionRate() == null ? "-" : obj.getSalesConversionRate() + "%");
        excelVo.setCpa(obj.getCpa() != null ? currency + obj.getCpa() : "-");
        excelVo.setSales(obj.getSales() != null ? currency + obj.getSales() : "-");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos() + "%");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos() + "%");
        excelVo.setAcots(obj.getAcots() == null ? "-" : obj.getAcots() + "%");
        excelVo.setAsots(obj.getAsots() == null ? "-" : obj.getAsots() + "%");
        excelVo.setRoas(obj.getRoas() == null ? "-" : obj.getRoas().toString());
        /**
         * TODO 广告报告重构
         * 本广告产品销售额
         */
        excelVo.setAdSales(obj.getAdSales() != null ? currency + obj.getAdSales() : "-");
        //本广告产品订单量
        excelVo.setAdSaleNum(obj.getAdSaleNum());
        //广告销量
        excelVo.setSaleNum(obj.getOrderNum());
        //本广告产品销量
        excelVo.setAdSelfSaleNum(obj.getAdSelfSaleNum());
        //其他产品广告订单量
        excelVo.setAdOtherOrderNum(obj.getAdOtherOrderNum());
        //其他产品广告销售额
        excelVo.setAdOtherSales(obj.getAdOtherSales() != null ? currency + obj.getAdOtherSales() : "-");
        //其他产品广告销量
        excelVo.setAdOtherSaleNum(obj.getAdOtherSaleNum());
        //“品牌新买家”订单量
        excelVo.setOrdersNewToBrand14d(obj.getOrdersNewToBrand14d());
        //“品牌新买家”销售额
        excelVo.setSalesNewToBrand14d(obj.getSalesNewToBrand14d() != null ? currency + obj.getSalesNewToBrand14d() : "-");
        //搜索词展示量排名
        excelVo.setImpressionRank(obj.getImpressionRank());
        //搜索词展示份额
        excelVo.setImpressionShare(obj.getImpressionShare() == null ? "-" : obj.getImpressionShare() + "%");
        //“品牌新买家”订单百分比
        excelVo.setOrderRateNewToBrand14d(obj.getOrderRateNewToBrand14d() == null ? "-" : obj.getOrderRateNewToBrand14d() + "%");
        //“品牌新买家”销售额百分比
        excelVo.setSalesRateNewToBrand14d(obj.getSalesRateNewToBrand14d() == null ? "-" : obj.getSalesRateNewToBrand14d() + "%");
        //“品牌新买家”订单转化率
        excelVo.setOrdersNewToBrandPercentage14d(obj.getOrdersNewToBrandPercentage14d() == null ? "-" : obj.getOrdersNewToBrandPercentage14d() + "%");
        // 广告花费占比
        excelVo.setAdCostPercentage(StringUtils.isNotBlank(obj.getAdCostPercentage()) ? obj.getAdCostPercentage() + "%" : "0.00%");
        // 广告销售额占比
        excelVo.setAdSalePercentage(StringUtils.isNotBlank(obj.getAdSalePercentage()) ? obj.getAdSalePercentage() + "%" : "0.00%");
        // 广告订单量占比
        excelVo.setAdOrderNumPercentage(StringUtils.isNotBlank(obj.getAdOrderNumPercentage()) ? obj.getAdOrderNumPercentage() + "%" : "0.00%");
        // 广告销量占比
        excelVo.setOrderNumPercentage(StringUtils.isNotBlank(obj.getOrderNumPercentage()) ? obj.getOrderNumPercentage() + "%" : "0.00%");

        excelVo.setVideo5SecondViews(Optional.ofNullable(obj.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideo5SecondViewRate(Optional.ofNullable(obj.getVideo5SecondViewRate()).map(String::valueOf).map(s -> s.concat("%")).orElse("0.00%"));
        excelVo.setVideoFirstQuartileViews(Optional.ofNullable(obj.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoMidpointViews(Optional.ofNullable(obj.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoThirdQuartileViews(Optional.ofNullable(obj.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoCompleteViews(Optional.ofNullable(obj.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoUnmutes(Optional.ofNullable(obj.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        excelVo.setViewImpressions(Optional.ofNullable(obj.getViewImpressions()).map(String::valueOf).orElse("0"));
        excelVo.setViewabilityRate(Optional.ofNullable(obj.getViewabilityRate()).map(String::valueOf).map(s -> s.concat("%")).orElse("0.00%"));
        excelVo.setViewClickThroughRate(Optional.ofNullable(obj.getViewClickThroughRate()).map(String::valueOf).map(s -> s.concat("%")).orElse("0.00%"));
        excelVo.setAdvertisingUnitPrice(Optional.ofNullable(obj.getAdvertisingUnitPrice()).map(String::valueOf).map(e -> currency + e).orElse("0"));
        if (obj.getSearchFrequencyRank() > 0) {
            excelVo.setSearchFrequencyRank(String.valueOf(obj.getSearchFrequencyRank()));
            excelVo.setWeekRatio(ExportStringUtil.modifyFormat(obj.getWeekRatio().toPlainString()));
        } else {
            excelVo.setSearchFrequencyRank("-");
            excelVo.setWeekRatio("-");
        }


        return excelVo;
    }
}
