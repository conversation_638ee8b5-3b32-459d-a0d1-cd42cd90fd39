package com.meiyunji.sponsored.service.newDashboard.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2024-03-22  16:24
 */

@Data
@ApiModel
public class DashboardBaseReqVo {

    @ApiModelProperty("puid")
    private Integer puid;

    @ApiModelProperty("站点id集合")
    private List<String> marketplaceIdList;

    @ApiModelProperty("店铺id集合")
    private List<Integer> shopIdList;

    @ApiModelProperty("币种")
    private String currency;

    @ApiModelProperty("开始时间")
    private String startDate;

    @ApiModelProperty("结束时间")
    private String endDate;

    @ApiModelProperty("自定义环比周期开始时间")
    private String momStartDate;

    @ApiModelProperty("自定义环比周期结束时间")
    private String momEndDate;

    @ApiModelProperty("同比周期是否超出2年")
    private Boolean yoyOverLimit = false;

    @ApiModelProperty("自定义同比周期开始时间")
    private String yoyStartDate;

    @ApiModelProperty("自定义同比周期结束时间")
    private String yoyEndDate;

    @ApiModelProperty("广告活动id")
    private List<String> campaignIds;

    @ApiModelProperty("广告组合id")
    private List<String> portfolioIds;

    @ApiModelProperty("站点今天")
    private Boolean siteToday = false;

    @ApiModelProperty("排除0")
    private Boolean noZero = false;


    
}
