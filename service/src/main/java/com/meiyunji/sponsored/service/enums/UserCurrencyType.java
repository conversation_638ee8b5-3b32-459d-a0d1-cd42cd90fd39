package com.meiyunji.sponsored.service.enums;

import com.google.common.collect.Lists;

import java.util.List;

public enum UserCurrencyType {

    CNY("CNY", "人民币", "￥", "", 1, "AAHKV2X7AFYLW"),
    USD("USD", "美元", "US$", "na", 2, "ATVPDKIKX0DER"),
    MXN("MXN", "墨西哥比索", "MX$", "na", 5, "A1AM78C64UM0Y8"),
    JPY("JPY", "日元", "JP￥", "fe", 4, "A1VC38T7YXB528"),
    INR("INR", "印度卢比", "₲", "eu", 6, "A21TJRUUN4KGV"),
    HKD("HKD", "港币", "HK$", "", 3, "AAHKV2X7AFYLW"),
    GBP("GBP", "英镑", "￡", "eu", 7, "A1F83G8C2ARO7P"),
    EUR("EUR", "欧元", "€", "eu", 8, "A1PA6795UKMFR9,A13V1IB3VIYZZH,APJ6JRA9NG5V4,A1RKKUPIHCS9HS,A1805IZSGTT6HS"),//德国,法国,意大利,西班牙,荷兰
    CAD("CAD", "加拿大元", "CA$", "na", 9, "A2EUQ1WTGCTBG2"),
    AUD("AUD", "澳大利亚元", "AU$", "fe", 10, "A39IBJ37TRP1C6"),
    AED("AED", "阿联酋迪拉姆", "د.إ", "eu", 11, "A2VIGQ35RCS4UG"),
    SAR("SAR", "沙特里亚尔", "﷼", "eu", 12, "A17E79C6D8DWNP"),
    SGD("SGD", "新加坡元", "S$", "fe", 13, "A19VAU5U5O7RUS"),
    TRY("TRY", "土耳其里拉", "₺", "eu", 14, "A33AVAJ2PDY3EV"),
    BRL("BRL", "巴西里亚尔", "R$", "na", 15, "A2Q3Y263D00KWC"),
    SEK("SEK", "瑞典克朗", "kr", "eu", 16, "A2NODRKZP88ZB9"),
    PLN("PLN", "波兰兹罗提", "zł", "eu", 17,"A1C3SOZRARQ6R3"),


            ;

    private String code;
    private String nameCN;
    private String icon;
    private String region;
    private Integer sort;
    private String marketplaceId;

    UserCurrencyType(String code, String nameCN, String icon, String region, Integer sort, String marketplaceId) {
        this.code = code;
        this.nameCN = nameCN;
        this.icon = icon;
        this.region = region;
        this.sort = sort;
        this.marketplaceId = marketplaceId;
    }

    public String code() {
        return code;
    }

    public String nameCN() {
        return nameCN;
    }

    public String icon() {
        return icon;
    }

    public Integer sort() {
        return sort;
    }

    public String region() {
        return region;
    }

    public String marketplaceId() {
        return marketplaceId;
    }

    public static UserCurrencyType getByCode(String code) {
        for (UserCurrencyType value : values()) {
            if (value.code.equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getIconByMarketplaceId(String marketplaceId) {
        for (UserCurrencyType value : values()) {
            if (value.marketplaceId.equals(marketplaceId)) {
                return value.icon;
            }
        }
        return null;
    }

    public static UserCurrencyType getByName(String nameCN) {
        for (UserCurrencyType value : values()) {
            if (value.nameCN.equals(nameCN)) {
                return value;
            }
        }
        return null;
    }


    public static boolean isBaseCurrency(String code) {
        for (UserCurrencyType value : values()) {
            if (value.code.equals(code)) {
                return true;
            }
        }
        return false;
    }

    public static Integer getSort(String code) {
        for (UserCurrencyType value : values()) {
            if (value.code.equals(code)) {
                return value.sort;
            }
        }
        return 0;
    }

    public static List<String> getAllCodes() {
        List<String> codes = Lists.newArrayList();
        for(UserCurrencyType type : UserCurrencyType.values()){
            codes.add(type.code());
        }
        return  codes;
    }
}
