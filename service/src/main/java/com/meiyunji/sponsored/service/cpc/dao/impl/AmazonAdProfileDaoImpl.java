package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * AmazonAdProfile
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdProfileDaoImpl extends BaseDaoImpl<AmazonAdProfile> implements IAmazonAdProfileDao {

    @Override
    public List<AmazonAdProfile> getDataProfile(Integer limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.lessThanOrEqualTo("ad_auth_sync_next_time", LocalDateTime.now());
        builder.orderByAsc("ad_auth_sync_next_time");
        if (limit != null) {
            builder.limit(limit);
        }
        return super.listByCondition(builder.build());
    }

    @Override
    public List<AmazonAdProfile> queryAllProfile(Integer limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.leftBracket().equalTo(LogicType.EPT, "is_sp", 1)
                .equalTo(LogicType.OR, "is_sb", 1)
                .equalTo(LogicType.OR, "is_sd", 1)
                .rightBracket()
                .lessThanOrEqualTo("product_metadata_sync_next_time", LocalDateTime.now());
        if (limit != null) {
            builder.limit(limit);
        }
        return super.listByCondition(builder.build());
    }

    @Override
    public AmazonAdProfile getProfile(int puid, int shopId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .build();
        return getByCondition(builder);
    }

    @Override
    public AmazonAdProfile getProfileByMarketId(Integer puid, Integer shopId, String marketPlaceId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .build();
        return getByCondition(builder);
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String marketplaceId) {
        String sql = "select count(*) from t_amazon_ad_profile where puid=? and shop_id=? and marketplace_id=? ";
        Integer count = getJdbcTemplate().queryForObject(sql, new Object[]{puid, shopId, marketplaceId}, Integer.class);
        return count > 0 ? true : false;
    }

    @Override
    public List<Integer> getShopIds() {
        String sql = "select shop_id from t_amazon_ad_profile";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<String> getMarketplaceIds(Integer puid, Integer shopId) {
        String sql = "select marketplace_id from t_amazon_ad_profile where puid=? and shop_id=?";
        List<String> list = getJdbcTemplate().queryForList(sql, new Object[]{puid, shopId}, String.class);
        return list;
    }

    @Override
    public List<AmazonAdProfile> getList(int puid) {
        String sql = "select * from t_amazon_ad_profile where puid=? ";
        return getJdbcTemplate().query(sql, new Object[]{puid}, getMapper());
    }

    @Override
    public List<AmazonAdProfile> listByShopIds(List<Integer> shopIdList) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .in("shop_id", shopIdList.toArray())
                .build();
        return listByCondition(conditionBuilder);
    }

    @Override
    public List<AmazonAdProfile> listByMarketplaceIdList(Integer puid, List<String> marketplaceIdList) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("marketplace_id", marketplaceIdList.toArray(new String[]{}))
                .build();
        return listByCondition(conditionBuilder);
    }

    @Override
    public void updateAdType(Integer puid, Integer shopId, Boolean sp, Boolean sb, Boolean sd) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_profile set ");
        List<Object> args = new ArrayList<>();
        if (sp != null) {
            sql.append(" is_sp = ?, ");
            args.add(sp);
        }
        if (sb != null) {
            sql.append(" is_sb = ?, ");
            args.add(sb);
        }
        if (sd != null) {
            sql.append(" is_sd = ?, ");
            args.add(sd);
        }
        sql.append(" update_time=now() where puid = ? and shop_id = ?");
        args.add(puid);
        args.add(shopId);
        getJdbcTemplate().update(sql.toString(), args.toArray());
    }

    @Override
    public void updateAdPricing(Integer puid, Integer id, boolean isPricing) {
        String sql = "update t_amazon_ad_profile set is_pricing = ? ,  update_time=now() where puid = ? and id = ?";
        List<Object> args = new ArrayList<>();
        args.add(isPricing);
        args.add(puid);
        args.add(id);
        getJdbcTemplate().update(sql, args.toArray());
    }

    @Override
    public void updateAdAuthDate(Integer puid, Integer shopId, LocalDateTime localDateTime) {
        String sql = "UPDATE `t_amazon_ad_profile` SET `ad_auth_sync_next_time` = ? WHERE puid = ? AND shop_id = ? ";
        getJdbcTemplate().update(sql, localDateTime, puid, shopId);
    }

    @Override
    public void updateDailyBudget(Integer puid, Integer id, Double dailyBudget) {
        String sql = "update t_amazon_ad_profile set daily_budget = ? ,  update_time=now() where puid = ? and id = ?";
        List<Object> args = new ArrayList<>();
        args.add(dailyBudget);
        args.add(puid);
        args.add(id);
        getJdbcTemplate().update(sql, args.toArray());
    }

    @Override
    public List<AmazonAdProfile> listForPricingAvailable() {
        String sql = "select * from t_amazon_ad_profile where is_pricing=? ";
        return getJdbcTemplate().query(sql, new Object[]{1}, getMapper());
    }


    @Override
    public AmazonAdProfile getAttributionAdvertiser(List<Integer> excludePuidList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.in("attribution_status", new Object[]{1, 2});
        if (CollectionUtils.isNotEmpty(excludePuidList)) {
            builder.notIn("puid", excludePuidList.toArray());
        }
        builder.limit(1);
        return getByCondition(builder.build());
    }

    @Override
    public List<AmazonAdProfile> getAttributionAvailableShopsByPuid(Integer puid, List<Integer> shopIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("attribution_status", new Object[]{1, 2})
                .build();
        return listByCondition(builder);
    }

    @Override
    public List<AmazonAdProfile> getNeedSyncAttribution(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("shop_id", shopId);
        }
        builder.and().leftBracket().lessThan(LogicType.EPT, "attribution_status_next_sync_time", LocalDate.now()).
                or().isNull(LogicType.EPT, "attribution_status_next_sync_time").rightBracket();
        builder.orderByAsc("attribution_status_next_sync_time");
        //isnull 兼容旧数据
        builder.limit(500);
        //先限制100条,第一次同步打散数据,上线可以删除
        return listByCondition(builder.build());
    }

    @Override
    public List<AmazonAdProfile> getListByPuidOrShopId(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("shop_id", shopId);
        }
        return listByCondition(builder.build());
    }

    @Override
    public List<AmazonAdProfile> getByCreateTimeRange(LocalDateTime start, LocalDateTime end) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.between("create_time", start, end);
        builder.isNull("report_sync_status");
        return listByCondition(builder.build());
    }

    @Override
    public List<AmazonAdProfile> getNeedStopSyncProfile() {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.isNull("report_sync_status");
        builder.and().leftBracket().equalTo(LogicType.EPT, "is_sp", 1)
                .equalTo(LogicType.OR, "is_sb", 1)
                .equalTo(LogicType.OR, "is_sd", 1).rightBracket();
        return listByCondition(builder.build());
    }

    @Override
    public List<AmazonAdProfile> getValidSpAdProfile() {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("is_sp", 1);
        return listByCondition(builder.build());
    }

    @Override
    public void updateNextTime(LocalDateTime nextTime, Integer id) {
        String sql = "UPDATE t_amazon_ad_profile set product_metadata_sync_next_time=? where id=? ";
        getJdbcTemplate().update(sql, nextTime, id);
    }

    @Override
    public AmazonAdProfile getByAccountIdAndMarketplaceId(String accountId, String marketplaceId) {
        return getByCondition(new ConditionBuilder.Builder()
                .equalTo("account_id", accountId)
                .equalTo("marketplace_id", marketplaceId).limit(1).build());
    }

    @Override
    public List<Integer> getAllPuid() {
        String sql = "select distinct puid from t_amazon_ad_profile where 1=1 ";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<AmazonAdProfile> getAllAmazonAdProfile() {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        return listByCondition(builder.build());
    }

    @Override
    public void strategyUpdate(Integer puid, Integer shopId, String topBudget) {
        String sql = "update t_amazon_ad_profile set daily_budget = ? ,  update_time=now() where puid = ? and shop_id = ?";
        List<Object> args = new ArrayList<>();
        args.add(topBudget);
        args.add(puid);
        args.add(shopId);
        getJdbcTemplate().update(sql, args.toArray());
    }


    @Override
    public List<AmazonAdProfile> getByCreateTimeRange(LocalDateTime start, LocalDateTime end, Integer index, Integer limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.between("create_time", start, end);
        builder.orderByAsc("id");
        builder.limitByOffset(index, limit);
        return listByCondition(builder.build());
    }

    @Override
    public List<Integer> getAllPuidByLimit(int start, int limit) {
        String sql = "select distinct puid from " + getJdbcHelper().getTable() + " order by puid limit ?, ? ";
        return getJdbcTemplate().queryForList(sql, Integer.class, start, limit);
    }

    @Override
    public List<AmazonAdProfile> getByPuidAndShopIdList(int puid, ArrayList<Integer> shopIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .build();
        return listByCondition(builder);
    }

    @Override
    public List<AmazonAdProfile> getProfilesByProfileId(List<String> profileIds) {
        List<Object> args = new ArrayList<>();
        String sql = "select * from t_amazon_ad_profile where  1=1  " + SqlStringUtil.dealInList("profile_id",profileIds, args);
        return getJdbcTemplate().query(sql,args.toArray() , getMapper());
    }

    @Override
    public List<AmazonAdProfile> getProfilesByShopIds(List<Integer> shopIds) {
        List<Object> args = new ArrayList<>();
        String sql = "select * from t_amazon_ad_profile where 1=1 " + SqlStringUtil.dealInList("shop_id", shopIds, args);
        return getJdbcTemplate().query(sql,args.toArray() , getMapper());
    }


}