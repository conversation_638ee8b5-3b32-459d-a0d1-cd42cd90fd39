package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dao.ICpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IWxCpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.CpcQueryTargetingReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;


/**
 * CpcQueryKeywordReport
 * <AUTHOR>
 * @date 2023/1/4
 * 顾客反馈order_num数据不准,排查发现应该使用sale_num字段作为订单量值,以下把order_num值设为sale_num的值,但字段名不变
 */
@Repository
public class WxCpcQueryKeywordReportDaoImpl extends BaseShardingSphereDaoImpl<CpcQueryKeywordReport> implements IWxCpcQueryKeywordReportDao {

    @Override
    public Page pageManageList(int puid, CpcQueryWordDto dto, Page page) {

        StringBuilder sql = new StringBuilder("SELECT `query`,`query_cn`,type, keyword_id,target_id,")
                .append(" keyword_text,match_type,targeting_expression,targeting_type,")
                .append(" ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("impressions,clicks,cost,sale_num,")
                .append("total_sales,order_num FROM (  ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");

        //订单量字段展示修改为 sale_num 更为准确
        StringBuilder selectKeywordSql = new StringBuilder("SELECT `query`,`query_cn`,'keyword' as type, keyword_id, '' as target_id,")
                .append(" keyword_text,match_type,'' as targeting_expression,'' as targeting_type,")
                .append(" ad_group_id, ad_group_name, campaign_id, campaign_name,")
                .append(" SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost, SUM(sale_num) sale_num,")
                .append(" SUM(total_sales) total_sales, SUM(order_num) order_num ")
                .append(" FROM `t_cpc_query_keyword_report` ");
        List<Object> argList = new ArrayList<>();
        String whereSql = getWhereSqlCountByKeyword(puid, dto, argList);
        if (whereSql == null) {
            return new Page<>(page.getPageNo(), page.getPageSize(), 0, 0, Lists.newArrayList());
        }
        selectKeywordSql.append(whereSql);


        StringBuilder selectTargetSql = new StringBuilder("SELECT `query`, `query_cn`,'target' as type,'' as keyword_id,  target_id, ")
                .append(" '' as keyword_text,'' as match_type,targeting_expression,targeting_type,ad_group_id,ad_group_name,campaign_id,campaign_name,")
                .append("SUM(impressions) impressions, SUM(clicks) clicks, SUM(cost) cost,SUM(sale_num) sale_num, ")
                .append("SUM(total_sales) total_sales, SUM(order_num) order_num ")
                .append("FROM `t_cpc_query_targeting_report` ");

        String whereSqlTarget = getWhereSqlCountByTargetId(puid, dto, argList);
        if (whereSqlTarget == null) {
            return new Page<>(page.getPageNo(), page.getPageSize(), 0, 0, Lists.newArrayList());
        }
        selectTargetSql.append(whereSqlTarget);


        sql.append(selectKeywordSql);
        sql.append(" UNION ALL ");
        sql.append(selectTargetSql);
        sql.append(" ) p ");

        if (StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())) {
            String orderField = ReportService.getOrderField(dto.getOrderField(), false);
            if (StringUtils.isNotBlank(orderField)) {
                sql.append(" order by ").append(orderField);
                if ("desc".equals(dto.getOrderValue())) {
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(selectKeywordSql);
        countSql.append(" UNION ALL ");
        countSql.append(selectTargetSql);
        countSql.append(" ) p ");
        //排除asin格式搜素词
        countSql.append(" ");

        return getPageByMapper(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), argList.toArray(), sql.toString(), argList.toArray(), new RowMapper<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo>() {
            @Override
            public com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo mapRow(ResultSet res, int i) throws SQLException {
                com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo optionVo = com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo.builder()
                        .query(res.getString("query"))
                        .queryCn(res.getString("query_cn"))
                        .type(res.getString("type"))
                        .keywordId(res.getString("keyword_id"))
                        .targetId(res.getString("target_id"))
                        .keywordText(res.getString("keyword_text"))
                        .matchType(res.getString("match_type"))
                        .targetingExpression(res.getString("targeting_expression"))
                        .targetingType(res.getString("targeting_type"))
                        .adGroupId(res.getString("ad_group_id"))
                        .adGroupName(res.getString("ad_group_name"))
                        .campaignId(res.getString("campaign_id"))
                        .campaignName(res.getString("campaign_name"))
                        .impressions(res.getInt("impressions"))
                        .clicks(res.getInt("clicks"))
                        .cost(res.getBigDecimal("cost") != null ? res.getBigDecimal("cost") : BigDecimal.ZERO)
                        .saleNum(res.getInt("sale_num"))
                        //广告销售额
                        .totalSales(res.getBigDecimal("total_sales") != null ? res.getBigDecimal("total_sales") : BigDecimal.ZERO)
                        //广告销量
                        .salesNum(Optional.ofNullable(res.getInt("order_num")).orElse(0))
                        .build();
                return optionVo;
            }
        });
    }


    @Override
    public List<AdHomePerformancedto> getReportKeywordByDate(Integer puid, CpcQueryWordDto dto) {
        dto.setCampaignId("");
        dto.setGroupId("");
        StringBuilder sql = new StringBuilder("select keyword_id, count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales,")
                .append(" SUM(order_num) sales_num FROM `t_cpc_query_keyword_report` ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByDate(puid, dto, argsList);
        if (whereSql == null) {
            return Lists.newArrayList();
        }
        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                    .keywordId(re.getString("keyword_id"))
                    .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                    .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                    .countDate(re.getString("count_date"))
                    //广告销量
                    .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                    .build(), argsList.toArray());
        } finally {
            hintManager.close();
        }

    }


    @Override
    public List<AdHomePerformancedto> getReportKeywordByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> keywordIdList,
                                                                      CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append("SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales,")
                .append("SUM(order_num) sales_num ")
                .append(" FROM `t_cpc_query_keyword_report` where puid= ? and shop_id= ?  ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return Lists.newArrayList();
            } else {
                sql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdHomePerformancedto dto1 = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                        .build();
                return dto1;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdHomePerformancedto> getReportTargetByDate(Integer puid, CpcQueryWordDto dto) {
        dto.setCampaignId("");
        dto.setGroupId("");
        StringBuilder sql = new StringBuilder("select target_id, count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append("SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales,")
                .append("SUM(order_num) sales_num FROM `t_cpc_query_targeting_report` ");
        List<Object> argsList = Lists.newArrayList();
        String whereTargetSql = getWhereTargetSqlCountByDate(puid, dto, argsList);
        if (whereTargetSql == null) {
            return Lists.newArrayList();
        }
        sql.append(whereTargetSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                    .targetId(re.getString("target_id"))
                    .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                    .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                    .countDate(re.getString("count_date"))
                    //广告销量
                    .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                    .build(), argsList.toArray());
        } finally {
            hintManager.close();
        }


    }

    @Override
    public List<AdHomePerformancedto> getReportTargetByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList,
                                                                    CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder("select  count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(sale_num) sale_num,SUM(total_sales) total_sales,")
                .append(" SUM(order_num) sales_num ")
                .append(" FROM `t_cpc_query_targeting_report` where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return Lists.newArrayList();
            } else {
                sql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    sql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (re, i) -> {
                AdHomePerformancedto dto1 = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        //广告销量
                        .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                        .build();
                return dto1;
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }



    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')紧密匹配，宽泛匹配 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            //把matchTypes作为条件查不出数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        //end
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        if (CollectionUtils.isNotEmpty(matchTypes)){
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypes,argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        //广告组合查询
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return null;
            } else {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) {
                    //模糊搜索
                    if (dto.getSearchVelueList().size() > 1) {
                        whereSql.append(" and (").append(SqlStringUtil.dealLikeListOr(field, dto.getSearchVelueList(), argsList, false)).append(")");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                        argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchVelueList().get(0)) + "%");
                    }

                } else {
                    //默认精确
                    if (dto.getSearchVelueList().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getSearchVelueList(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchVelueList().get(0));
                    }
                }
            }
        }
        whereSql.append(" group by keyword_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }


    private String getWhereSqlCountByTargetId(int puid, CpcQueryWordDto dto, List<Object> argsList) {

        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'广泛匹配','词组匹配','精准匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'紧密匹配'，'宽泛匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('紧密匹配'，'宽泛匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by target_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        } else {
            //这张表只查'close-match','loose-match'
            matchTypes.addAll(Arrays.asList("close-match", "loose-match"));

        }
        //end
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        //广告组合查询
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return null;
            } else {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }

        whereSql.append(" and targeting_type = 'TARGETING_EXPRESSION_PREDEFINED' ");
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            whereSql.append(SqlStringUtil.dealInList("targeting_expression", matchTypes, argsList));
        }
        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                //模糊搜索
                if ("blur".equals(dto.getSearchType())) {
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {
                    //默认精确
                    whereSql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    whereSql.append(" and targeting_text = ? ");
                    // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        whereSql.append(" group by target_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }

    /**
     * 汇总
     * @param puid
     * @param dto
     * @param argsList
     * @return
     */
    private String getWhereSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=?  and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'紧密匹配'，'宽泛匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        // end
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            whereSql.append(SqlStringUtil.dealInList("match_type", matchTypes, argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return null;
            } else {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (StringUtils.isNotBlank(dto.getKeywordId())) {
            whereSql.append(" and keyword_id = ? ");
            argsList.add(dto.getKeywordId());
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    whereSql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        whereSql.append(" group by keyword_id,`query` ");
        if (dto.getUseAdvanced()) {

            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }


    private String getWhereTargetSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=?  and count_date>=? and count_date<=?  ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        whereSql.append(" and  query not REGEXP '" + ASIN_REGEX + "' ");
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'广泛匹配','词组匹配','精准匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'紧密匹配'，'宽泛匹配' 查询该表数据
         *   matchTypes：符合查询该表数据的条件('紧密匹配'，'宽泛匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                whereSql.append(" group by target_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        } else {
            //这张表只查'close-match','loose-match'
            matchTypes.addAll(Arrays.asList("close-match", "loose-match"));

        }
        //end
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return null;
            } else {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (StringUtils.isNotBlank(dto.getTargetId())) {
            whereSql.append(" and target_id = ? ");
            argsList.add(dto.getTargetId());
        }

//        whereSql.append(" and targeting_type = 'TARGETING_EXPRESSION_PREDEFINED' and targeting_expression in ('close-match','loose-match') ");
        whereSql.append(" and targeting_type = 'TARGETING_EXPRESSION_PREDEFINED' ");
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            whereSql.append(SqlStringUtil.dealInList("targeting_expression", matchTypes, argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcQueryTargetingReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) { //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {//默认精确
                    whereSql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            } else if ("keywordText".equalsIgnoreCase(dto.getSearchField())) {
                if (!dto.getSearchValue().equalsIgnoreCase("自动投放组")) {
                    whereSql.append(" and targeting_text = ? ");  // 需要查不出这个表的数据
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        whereSql.append(" group by target_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }

}