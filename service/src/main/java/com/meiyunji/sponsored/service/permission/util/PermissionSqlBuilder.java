package com.meiyunji.sponsored.service.permission.util;

import com.meiyunji.sponsored.common.permission.context.PermissionContext;
import com.meiyunji.sponsored.common.permission.context.PermissionContextHolder;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.service.permission.filter.PermissionFilter;
import com.meiyunji.sponsored.service.permission.filter.PermissionFilterFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;

/**
 * 通用权限SQL构建器
 * 支持多种权限过滤类型（Campaign、ASIN等）
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Component
@Slf4j
public class PermissionSqlBuilder {


    @Autowired
    private PermissionFilterFactory filterFactory;

    private static PermissionSqlBuilder instance;

    @PostConstruct
    public void init() {
        instance = this;
    }

    /**
     * 添加权限过滤条件到SQL
     * 从权限上下文中自动获取过滤类型
     *
     * @param sql 原始SQL StringBuilder
     * @param argsList 参数列表
     * @param fieldName 活动字段名称
     * @param isDoris true -使用子查询  false-使用campaignId in ()
     * @return 拼接权限过滤后的SQL
     */
    public static StringBuilder addPermissionFilter(StringBuilder sql, List<Object> argsList, String fieldName ,boolean isDoris) {
        try {
            // 1. 获取权限上下文
            PermissionContext context = PermissionContextHolder.getContext();
            if (context == null) {
                log.debug("没有权限上下文，跳过权限过滤");
                return sql;
            }

            // 2. 从上下文中获取过滤类型
            PermissionFilterType filterType = context.getFilterType();
            if (filterType == null) {
                log.debug("权限上下文中没有过滤类型，跳过权限过滤");
                return sql;
            }

            // 3. 获取对应的权限过滤器
            PermissionFilter filter = instance.filterFactory.getFilter(filterType);
            if (filter == null) {
                log.warn("未找到{}类型的权限过滤器", filterType);
                return sql;
            }

            // 4. 生成权限过滤SQL
            String permissionSql = filter.generateFilterSql(fieldName, context, argsList , isDoris);

            // 5. 拼接权限过滤条件
            if (StringUtils.isNotBlank(permissionSql)) {
                sql.append(permissionSql);
                log.debug("成功添加{}权限过滤: field={}", filterType, fieldName);
            } else {
                log.debug("{}权限过滤器返回空SQL", filterType);
            }

        } catch (Exception e) {
            log.error("添加权限过滤失败: field={}", fieldName, e);
        }

        return sql;
    }


}
