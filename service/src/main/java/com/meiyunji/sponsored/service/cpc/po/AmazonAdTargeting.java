package com.meiyunji.sponsored.service.cpc.po;

import com.amazon.advertising.mode.targeting.Expression;
import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.enums.BaseEnum;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.annotation.AdLogFormat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * AmazonAdTargeting
 * <AUTHOR>
 */
@DbTable(value = "t_amazon_ad_targeting")
public class AmazonAdTargeting extends BasePo {
	/**
	 * id
	 */
	@DbColumn(value = "id",autoIncrement = true,key = true)
	private Long id;
	/**
	 * md5(puid,shop_id,market_place_id,campaign_id,dxm_group_id,targeting_value,type)
	 */
	@DbColumn(value = "unique_key")
	private String uniqueKey;
	/**
	 * 商户uid
	 */
	@DbColumn(value = "puid")
	private Integer puid;
	/**
	 * 店铺ID
	 */
	@DbColumn(value = "shop_id")
	private Integer shopId;
	/**
	 * 站点
	 */
	@DbColumn(value = "marketplace_id")
	private String marketplaceId;
	/**
	 * 定位id
	 */
	@DbColumn(value = "target_id")
	private String targetId;
	/**
	 * 广告组id
	 */
	@DbColumn(value = "ad_group_id")
	private String adGroupId;
	/**
	 * dxm维护的广告组id
	 */
	@DbColumn(value = "dxm_group_id")
	private Long dxmGroupId;
	/**
	 * 活动id
	 */
	@DbColumn(value = "campaign_id")
	private String campaignId;
	/**
	 * 配置ID
	 */
	@DbColumn(value = "profile_id")
	private String profileId;
	/**
	 * manual,auto
	 */
	@DbColumn(value = "expression_type")
	private String expressionType;
	/**
	 * [{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
	 */
	@DbColumn(value = "expression")
	private String expression;
	/**
	 * [{"type": "asinCategorySameAs","value": "679433011"},{"type": "asinPriceBetween","value": "1-5"}]
	 */
	@DbColumn(value = "resolved_expression")
	private String resolvedExpression;
	/**
	 * 竞价(否定关键词没有)
	 */
	@DbColumn(value = "bid")
	@AdLogFormat(value = "竞价", methodStr = "getBidFixedScale")
	private Double bid;

	public String getBidFixedScale() {
		if (Objects.isNull(bid)) {
			return null;
		}
		return BigDecimal.valueOf(bid).setScale(2, RoundingMode.HALF_UP).toString();
	}

	/**
	 * category,asin
	 */
	@DbColumn(value = "type")
	private String type;
	/**
	 * categoryId,asin
	 */
	@DbColumn(value = "targeting_value")
	private String targetingValue;
	/**
	 * asin标题（asin投放时）
	 */
	@DbColumn(value = "title")
	private String title;
	/**
	 * asin图片（asin投放时）
	 */
	@DbColumn(value = "img_url")
	private String imgUrl;
	/**
	 * 类目路径
	 */
	@DbColumn(value = "category_path")
	private String categoryPath;
	/**
	 * 状态（enabled，paused，archived）
	 */
	@DbColumn(value = "state")
	@AdLogFormat(value = "状态", methodStr = "covertChState")
	private String state;
	/**
	 * TODO 商品投放增加日志
	 * 操作：日志打印状态需转换中文
	 */
	public String covertChState() {
		return AmazonAdTargeting.stateEnum.valueOf(state).getDescription();
	}

	@DbColumn(value = "serving_status")
	private String servingStatus;
	/**
	 * 竞价建议范围最小值，用于刚进编辑页面展示
	 */
	@DbColumn(value = "range_start")
	private BigDecimal rangeStart;
	/**
	 * 竞价建议范围最大值，用于刚进编辑页面展示
	 */
	@DbColumn(value = "range_end")
	private BigDecimal rangeEnd;
	/**
	 * 竞价建议值，用于刚进编辑页面展示
	 */
	@DbColumn(value = "suggested")
	private BigDecimal suggested;
	/**
	 * 创建人id
	 */
	@DbColumn(value = "create_id")
	private Integer createId;
	/**
	 * 修改人id
	 */
	@DbColumn(value = "update_id")
	private Integer updateId;

	@DbColumn(value = "is_pricing")
	private Integer isPricing;
	@DbColumn(value = "pricing_state")
	private Integer pricingState;

	/**
	 * 报告数据最新更新时间 yyyy-MM-dd
	 */
	@DbColumn(value = "data_update_time")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private LocalDate dataUpdateTime;

	@DbColumn(value = "select_type")
	private String selectType;
	/**
	 * 平台的创建时间，兼容否定关键词的新增字段，后续关键词表有新增获取平台创建时间需求直接用这个值作为数据库字段
	 */
	private LocalDateTime creationDate;

	//辅助字段
	private List<Expression> expressionList;

	public List<Expression> getExpressionList() {
		return expressionList;
	}
	//类目产品细化信息
	private String brand;
	private String priceRange;
	private String reviewRange;
	private String isPrime;
	private String servingStatusName;

	private String servingStatusDec;

	private String error;

	private Integer index;

	// 投放任务详情id
	private Long targetTaskDetailId;


	public void setServingStatus(String servingStatus) {
		this.servingStatus = servingStatus;
		if(StringUtils.isNotBlank(servingStatus)){
			servingStatusEnum byCode = UCommonUtil.getByCode(servingStatus, servingStatusEnum.class);
			if(byCode != null){
				this.servingStatusName = byCode.getName();
				this.servingStatusDec = byCode.getDescription();
			}else {
				this.servingStatusName =servingStatus;
				this.servingStatusDec = servingStatus;
			}
		}
	}

	public String getServingStatusName() {
		return servingStatusName;
	}

	public void setServingStatusName(String servingStatusName) {
		this.servingStatusName = servingStatusName;
	}

	public String getServingStatusDec() {
		return servingStatusDec;
	}

	public void setServingStatusDec(String servingStatusDec) {
		this.servingStatusDec = servingStatusDec;
	}

	public String getBrand() {
		return brand;
	}

	public void setBrand(String brand) {
		this.brand = brand;
	}

	public String getPriceRange() {
		return priceRange;
	}

	public void setPriceRange(String priceRange) {
		this.priceRange = priceRange;
	}

	public String getReviewRange() {
		return reviewRange;
	}

	public void setReviewRange(String reviewRange) {
		this.reviewRange = reviewRange;
	}

	public String getIsPrime() {
		return isPrime;
	}

	public void setIsPrime(String isPrime) {
		this.isPrime = isPrime;
	}

	public void setExpressionList(List<Expression> expressionList) {
		this.expressionList = expressionList;
	}

	public void setId(Long value) {
		this.id = value;
	}
	public Long getId() {
		return this.id;
	}
	public void setUniqueKey(String value) {
		this.uniqueKey = value;
	}
	public String getUniqueKey() {
		return this.uniqueKey;
	}
	public void MD5UniqueKey() {
		StringBuilder uniqueKey = new StringBuilder("").append(this.getPuid()).append(this.getShopId())
				.append(this.getMarketplaceId()).append(this.getCampaignId()).append(this.getDxmGroupId())
				.append(this.getTargetingValue()).append(this.getType()).append(this.getTargetId());
		this.uniqueKey = MD5Util.getMD5(uniqueKey.toString());
	}
	public void setPuid(Integer value) {
		this.puid = value;
	}
	public Integer getPuid() {
		return this.puid;
	}
	public void setShopId(Integer value) {
		this.shopId = value;
	}
	public Integer getShopId() {
		return this.shopId;
	}

	public String getMarketplaceId() {
		return marketplaceId;
	}

	public void setMarketplaceId(String marketplaceId) {
		this.marketplaceId = marketplaceId;
	}

	public void setTargetId(String value) {
		this.targetId = value;
	}
	public String getTargetId() {
		return this.targetId;
	}
	public void setAdGroupId(String value) {
		this.adGroupId = value;
	}
	public String getAdGroupId() {
		return this.adGroupId;
	}
	public void setDxmGroupId(Long value) {
		this.dxmGroupId = value;
	}
	public Long getDxmGroupId() {
		return this.dxmGroupId;
	}
	public void setCampaignId(String value) {
		this.campaignId = value;
	}
	public String getCampaignId() {
		return this.campaignId;
	}
	public void setProfileId(String value) {
		this.profileId = value;
	}
	public String getProfileId() {
		return this.profileId;
	}
	public void setExpressionType(String value) {
		this.expressionType = value;
	}
	public String getExpressionType() {
		return this.expressionType;
	}
	public void setExpression(String value) {
		this.expression = value;
	}
	public String getExpression() {
		return this.expression;
	}
	public void setResolvedExpression(String value) {
		this.resolvedExpression = value;
	}
	public String getResolvedExpression() {
		return this.resolvedExpression;
	}
	public void setBid(Double value) {
		this.bid = value;
	}
	public Double getBid() {
		return this.bid;
	}
	public void setType(String value) {
		this.type = value;
	}
	public String getType() {
		return this.type;
	}
	public void setTargetingValue(String value) {
		this.targetingValue = value;
	}
	public String getTargetingValue() {
		return this.targetingValue;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getImgUrl() {
		return imgUrl;
	}

	public void setImgUrl(String imgUrl) {
		this.imgUrl = imgUrl;
	}

	public void setCategoryPath(String value) {
		this.categoryPath = value;
	}
	public String getCategoryPath() {
		return this.categoryPath;
	}
	public void setState(String value) {
		this.state = value;
	}
	public String getState() {
		return this.state;
	}

	public BigDecimal getRangeStart() {
		return rangeStart;
	}

	public void setRangeStart(BigDecimal rangeStart) {
		this.rangeStart = rangeStart;
	}

	public BigDecimal getRangeEnd() {
		return rangeEnd;
	}

	public void setRangeEnd(BigDecimal rangeEnd) {
		this.rangeEnd = rangeEnd;
	}

	public BigDecimal getSuggested() {
		return suggested;
	}

	public void setSuggested(BigDecimal suggested) {
		this.suggested = suggested;
	}

	public Integer getCreateId() {
		return createId;
	}

	public void setCreateId(Integer createId) {
		this.createId = createId;
	}

	public Integer getUpdateId() {
		return updateId;
	}

	public void setUpdateId(Integer updateId) {
		this.updateId = updateId;
	}
	/**
	 * 创建时间
	 */
	@DbColumn(value = "create_time")
	private Date createTime;

	/**
	 * 更新时间
	 */
	@DbColumn(value = "update_time")
	private Date updateTime;

	public Date getCreateTime() {
		return createTime;
	}

	public void setCreateTime(Date createTime) {
		this.createTime = createTime;
	}

	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public Integer getIsPricing() {
		return isPricing;
	}

	public void setIsPricing(Integer isPricing) {
		this.isPricing = isPricing;
	}

	public Integer getPricingState() {
		return pricingState;
	}

	public void setPricingState(Integer pricingState) {
		this.pricingState = pricingState;
	}

	public LocalDate getDataUpdateTime() {
		return dataUpdateTime;
	}

	public void setDataUpdateTime(LocalDate dataUpdateTime) {
		this.dataUpdateTime = dataUpdateTime;
	}

	public String getServingStatus() {
		return servingStatus;
	}

	public Long getTargetTaskDetailId() {
		return targetTaskDetailId;
	}

	public void setTargetTaskDetailId(Long targetTaskDetailId) {
		this.targetTaskDetailId = targetTaskDetailId;
	}

	public LocalDateTime getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(LocalDateTime creationDate) {
		this.creationDate = creationDate;
	}

	/**
	 * 广告二级状态枚举类
	 */
	public enum servingStatusEnum implements BaseEnum {


		TARGETING_CLAUSE_ARCHIVED("TARGETING_CLAUSE_ARCHIVED","已归档","投放被设置为【已归档】"),
		TARGETING_CLAUSE_PAUSED("TARGETING_CLAUSE_PAUSED","已暂停","投放被设置为【已归档】"),
		TARGETING_CLAUSE_STATUS_LIVE("TARGETING_CLAUSE_STATUS_LIVE","正在投放","广告活动处于活动状态，可以获得展示量。您的广告将展示在符合条件的广告位中。"),
		TARGETING_CLAUSE_POLICING_SUSPENDED("TARGETING_CLAUSE_POLICING_SUSPENDED","监管暂停","投放被监管暂停使用"),
		CAMPAIGN_OUT_OF_BUDGET("CAMPAIGN_OUT_OF_BUDGET","广告活动 超出预算","广告活动-超出预算"),
		AD_GROUP_PAUSED("AD_GROUP_PAUSED","广告组-已暂停","广告组被设置为【已暂停】"),
		AD_GROUP_ARCHIVED("AD_GROUP_ARCHIVED","广告组-已归档","广告组被设置为【已归档】"),
		CAMPAIGN_PAUSED("CAMPAIGN_PAUSED","广告活动-已暂停","广告活动被设置为【已暂停】"),
		CAMPAIGN_ARCHIVED("CAMPAIGN_ARCHIVED","广告活动-已归档","广告活动被设置为【已归档】"),
		ACCOUNT_OUT_OF_BUDGET("ACCOUNT_OUT_OF_BUDGET","广告账户- 超出预算","广告账户- 超出预算（ACCOUNT OUT OF BUDGET）"),
		PENDING_START_DATE("PENDING_START_DATE","已安排","广告活动尚未开始，但已安排在一个未来的时间开始。"),
		AD_GROUP_INCOMPLETE("AD_GROUP_INCOMPLETE","广告组-不完整","广告组被设置为【不完整】"),
        CAMPAIGN_INCOMPLETE("CAMPAIGN_INCOMPLETE","不完整","广告活动被设置为【不完整】"),
        ENDED("ENDED","已结束","已结束"),
        PORTFOLIO_PENDING_START_DATE("PORTFOLIO_PENDING_START_DATE","广告组合-未开始","广告组合被设置为【未开始】"),
        PORTFOLIO_OUT_OF_BUDGET("PORTFOLIO_OUT_OF_BUDGET","广告组合-超预算","广告组合被设置为【超预算】"),
        PORTFOLIO_STATUS_ENABLED("PORTFOLIO_STATUS_ENABLED","广告组合-正在投放","广告组合被设置为【正在投放】"),
        PORTFOLIO_PAUSED("PORTFOLIO_PAUSED","广告组合-已暂停","广告组合被设置为【已暂停】"),
        PORTFOLIO_ARCHIVED("PORTFOLIO_ARCHIVED","广告组合-已归档","广告组合被设置为【已归档】"),
        PORTFOLIO_ENDED("PORTFOLIO_ENDED","广告组合-已结束","广告组合被设置为【已结束】"),
		ADVERTISER_PAYMENT_FAILURE("ADVERTISER_PAYMENT_FAILURE","付款失败","您的付款方式（如信用卡、借记卡或储蓄卡）已停用、金额不足或最近出现变动"),
		ADVERTISER_PAYMENT_FAILED("ADVERTISER_PAYMENT_FAILED","付款失败","您的付款方式（如信用卡、借记卡或储蓄卡）已停用、金额不足或最近出现变动"),
		AD_GROUP_POLICING_CREATIVE_REJECTED("AD_GROUP_POLICING_CREATIVE_REJECTED", "未获得批准", "您的广告组的素材已被拒绝，需要进行编辑"),

        ;

		public static String getServingStatusName(String servingStatus){
			servingStatusEnum[] values = values();
			for (servingStatusEnum value : values) {
				if(value.getCode().equals(servingStatus)){
					return value.getName();
				}
			}
			return "-"; // 匹配不到，返回占位符
		}

		public static String getServingStatusDesc(String servingStatus){
			servingStatusEnum[] values = values();
			for (servingStatusEnum value : values) {
				if(value.getCode().equals(servingStatus)){
					return value.getDescription();
				}
			}
			return "-"; // 匹配不到，返回占位符
		}

		private String code;
		private String name;
		private String desc;

		servingStatusEnum(String code,String name,String desc) {
			this.code = code;
			this.name = name;
			this.desc = desc;
		}

		@Override
		public String getCode() {
			return code;
		}


		public String getName() {
			return name;
		}

		@Override
		public String getDescription() {
			return desc;
		}
	}

	/**
	 * 商品投放状态
	 */
	public enum  stateEnum implements BaseEnum {
		enabled("enabled","开启"),
		paused("paused","停止"),
		archived("archived","归档"),
		;

		private String code;
		private String desc;

		stateEnum(String code, String desc) {
			this.code = code;
			this.desc = desc;
		}

		@Override
		public String getCode() {
			return code;
		}

		@Override
		public String getDescription() {
			return desc;
		}
	}

	public String getError() {
		return error;
	}

	public void setError(String error) {
		this.error = error;
	}

	public Integer getIndex() {
		return index;
	}

	public void setIndex(Integer index) {
		this.index = index;
	}

	public String getSelectType() {
		return selectType;
	}

	public void setSelectType(String selectType) {
		this.selectType = selectType;
	}
}