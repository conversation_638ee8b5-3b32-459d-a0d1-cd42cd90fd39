package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeTargetingDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeTargeting;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportParamsMappingEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportTableEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.util.*;
import java.util.stream.Collectors;

/**
 * amazon广告投放定位表(OdsAmazonAdNeTargeting)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Repository
public class OdsAmazonAdNeTargetingDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdNeTargeting> implements IOdsAmazonAdNeTargetingDao {

    @Override
    public List<OdsAmazonAdNeTargeting> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[]{}))
                .equalTo("type", Constants.TARGETING_TYPE_NEGATIVEASIN)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(),CpcStatusEnum.paused.name()})
                .build();
        String sql = "select type, targeting_value, ad_group_id from " + this.getJdbcHelper().getTable()+ " where "  + builder.getSql();
        return getJdbcTemplate().query(sql, builder.getValues(), new BeanPropertyRowMapper<>(OdsAmazonAdNeTargeting.class));
    }

    @Override
    public Page<String> pageGroupNeTargetWithoutReportFilter(NeTargetingPageParam param) {

        //未指定类型 或 指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" where puid = ? and shop_id = ? and type = 'negativeAsin' ");
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append("and ").append("targeting_value").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }

            StringBuilder countSql = new StringBuilder().append(" select count(*) ").append(baseSql);
            StringBuilder selectSql = new StringBuilder().append(" select target_id, create_time, creation_date ").append(baseSql).append(" order by ifnull(creation_date, create_time) desc");
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }

        //未指定类型 或 指定sb
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" where puid = ? and shop_id = ? ");
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append(" and ").append("target_text").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }

            StringBuilder countSql = new StringBuilder().append("select count(*) ").append(baseSql);
            StringBuilder selectSql = new StringBuilder().append("select target_id, create_time, creation_date ").append(baseSql).append(" order by ifnull(creation_date, create_time) desc");
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }

        //未指定类型 或 指定sb
        if (Constants.SD.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" where puid = ? and shop_id = ? ");
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(" and ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where ")
                    .append(SqlStringUtil.dealInListWithoutAnd("campaign_id", campaignIdList, argsList))
                    .append(" ) ");
            }
            //广告组合筛选
            baseSql.append(" and ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where campaign_id in ( ")
                .append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sd.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList))
                .append(" )) ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append(" and ").append("target_text").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }

            StringBuilder countSql = new StringBuilder().append("select count(*) ").append(baseSql);
            StringBuilder selectSql = new StringBuilder().append("select target_id, create_time, creation_date ").append(baseSql).append(" order by ifnull(creation_date, create_time) desc");
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }
        return new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, Collections.EMPTY_LIST);

    }

    /**
     * 筛选广告组层级否定投放前后30天报告数据
     * @param param param
     * @return Page<String> row = keywordId
     */
    @Override
    public Page<String> page30DaysGroupNeTargetWithReportFilter(@NotNull NeTargetingPageParam param) {
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();
        boolean doAdvancedFilter = Boolean.TRUE.equals(filterDto.getDoAdvancedFilter());
        boolean queryWord = Objects.nonNull(filterDto.getDataFrom()) && filterDto.getDataFrom() == 2;
        boolean order = StringUtils.isNotBlank(filterDto.getOrderField()) && StringUtils.isNotBlank(filterDto.getOrderType());
        NeTargetReportParamsMappingEnum orderFieldEnum = NeTargetReportParamsMappingEnum.getByFrontParam(filterDto.getOrderField());


        //指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            // Initialize DTO and arguments list
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" t left join (")
                .append(" SELECT target_id ")
                .append(SqlStringReportUtil.deal30SelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SP, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append(" FROM ")
                .append(queryWord ? NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getQueryReportTableNameFor30Days() : NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getTargetReportTableNameFor30Days())
                .append(" WHERE puid = ? AND shop_id = ? and report_date_type = ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportDateType());

            baseSql//.append(doAdvancedFilter ?SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithOnAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions(),  Constants.SP): SqlStringReportUtil.dealOnlyShowImpressionsWithOnAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append(" ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
                .append(" WHERE t.puid = ? AND t.shop_id = ? and t.type = 'negativeAsin' ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append(" and ").append("t.targeting_value").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
            baseSql.append(doAdvancedFilter ?SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append(" SELECT t.target_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append(" ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC ");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }
        //指定sb
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            // Initialize DTO and arguments list
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" t left join (")
                .append(" SELECT target_id ")
                .append(SqlStringReportUtil.deal30SelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SB, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append(" FROM ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getTargetReportTableNameFor30Days())
                .append(" WHERE puid = ? AND shop_id = ? and report_date_type = ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportDateType());

            baseSql//.append(doAdvancedFilter ?SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithOnAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions(), Constants.SB): SqlStringReportUtil.dealOnlyShowImpressionsWithOnAnd(filterDto.getOnlyShowImpressions()))
                .append(" ) r ")
                .append(" ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
                .append(" WHERE t.puid = ? AND t.shop_id = ? ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append(" and ").append("t.target_text").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
            baseSql.append(doAdvancedFilter ?SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append(" SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append(" SELECT t.target_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append(" ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append(" r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC ");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }
        //指定sd
        if (Constants.SD.equalsIgnoreCase(param.getType())) {
            // Initialize DTO and arguments list
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" t left join (")
                .append(" SELECT target_id ")
                .append(SqlStringReportUtil.deal30SelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SD, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append(" FROM ")
                .append(NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getTargetReportTableNameFor30Days())
                .append(" WHERE puid = ? AND shop_id = ? and report_date_type = ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportDateType());

            baseSql//.append(doAdvancedFilter ?SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithOnAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions(), Constants.SD): SqlStringReportUtil.dealOnlyShowImpressionsWithOnAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(" and t.ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where ")
                    .append(SqlStringUtil.dealInListWithoutAnd("campaign_id", campaignIdList, argsList))
                    .append(" ) ");
            }
            //广告组合筛选
            baseSql.append(" and t.ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where campaign_id in ( ")
                .append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sd.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList))
                .append(" )) ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append(" and ").append("t.target_text").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
            baseSql.append(doAdvancedFilter ?SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append(" SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append(" SELECT t.target_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append(" ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC ");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }
        return new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, Collections.EMPTY_LIST);
    }



    /**
     * 筛选广告组层级否定商品自定义时间范围内报告数据
     * @param param param
     * @return Page<String> row = keywordId
     */
    @Override
    public Page<String> pageGroupNeTargetWithReportFilter(@NotNull NeTargetingPageParam param) {
        // Initialize DTO and arguments list
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();
        boolean queryWord = Objects.nonNull(filterDto.getDataFrom()) && filterDto.getDataFrom() == 2;
        boolean order = StringUtils.isNotBlank(filterDto.getOrderField()) && StringUtils.isNotBlank(filterDto.getOrderType());
        NeTargetReportParamsMappingEnum orderFieldEnum = NeTargetReportParamsMappingEnum.getByFrontParam(filterDto.getOrderField());

        //未指定类型 或 指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" t left join (")
                .append("  SELECT target_id ")
                .append(SqlStringReportUtil.dealSelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SP, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append("  FROM ").append(queryWord ? NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getQueryReportTableName() : NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getReportTableName())
                .append("  WHERE puid = ? AND shop_id = ? and count_day >= ? and count_day <= ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportStartDate());
            argsList.add(filterDto.getReportEndDate());
            baseSql.append(" group by target_id HAVING 1=1 " );
            baseSql//.append(filterDto.getAdvanceFilterParams() != null ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithHavingAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithHavingAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? and t.type = 'negativeAsin' ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append("and ").append("t.targeting_value").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }

            baseSql.append(filterDto.getAdvanceFilterParams() != null ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append("SELECT t.target_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }

        //未指定类型 或 指定sp
        if (Constants.SB.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" t left join (")
                .append(" SELECT target_id ")
                .append(SqlStringReportUtil.dealSelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SB, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append("  FROM ").append(NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getReportTableName())
                .append("  WHERE puid = ? AND shop_id = ? and count_day >= ? and count_day <= ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportStartDate());
            argsList.add(filterDto.getReportEndDate());
            baseSql.append(" group by target_id HAVING 1=1 " );
            baseSql//.append(filterDto.getAdvanceFilterParams() != null ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithHavingAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithHavingAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(SqlStringUtil.dealInList("t.campaign_id", campaignIdList, argsList));
            }
            //广告组合筛选
            baseSql.append(" and t.campaign_id in ( ").append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList)).append(") ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append("and ").append("t.target_text").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }

            baseSql.append(filterDto.getAdvanceFilterParams() != null ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append("SELECT t.target_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }

        //未指定类型 或 指定sp
        if (Constants.SD.equalsIgnoreCase(param.getType())) {
            List<Object> argsList = new ArrayList<>();
            StringBuilder baseSql = new StringBuilder();

            // Start building the SQL query
            baseSql.append(" from ")
                .append(NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getBasicTableName())
                .append(" t left join (")
                .append("  SELECT target_id ")
                .append(SqlStringReportUtil.dealSelectSqlForNeTarget(filterDto.getAdvanceFilterParams(), filterDto.getOnlyShowImpressions(), Constants.SD, filterDto.getDoAdvancedFilter(), order, orderFieldEnum))
                .append("  FROM ").append(NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getReportTableName())
                .append("  WHERE puid = ? AND shop_id = ? and count_day >= ? and count_day <= ? ");
            // Add parameters to argsList
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(filterDto.getReportStartDate());
            argsList.add(filterDto.getReportEndDate());
            baseSql.append(" group by target_id HAVING 1=1 " );
            baseSql//.append(filterDto.getAdvanceFilterParams() != null ? SqlStringReportUtil.deal30AdvancedFilterForNeTargetWithHavingAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithHavingAnd(filterDto.getOnlyShowImpressions()))
                .append(") r ")
                .append("ON t.puid = ? AND t.shop_id = ? AND t.target_id = r.target_id ")
                .append("WHERE t.puid = ? AND t.shop_id = ? ");

            argsList.add(param.getPuid());
            argsList.add(param.getShopId());
            argsList.add(param.getPuid());
            argsList.add(param.getShopId());

            //广告活动筛选
            List<String> campaignIdList = StringUtil.stringToList(param.getCampaignId(), ",");
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                baseSql.append(" and t.ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where ")
                    .append(SqlStringUtil.dealInListWithoutAnd("campaign_id", campaignIdList, argsList))
                    .append(" ) ");
            }
            //广告组合筛选
            baseSql.append(" and t.ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where campaign_id in ( ")
                .append(getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sd.getCampaignType(), param.getStatus(), param.getServingStatus(), argsList))
                .append(" )) ");
            //广告组筛选
            List<String> adGroupList = StringUtil.stringToList(param.getGroupId(), ",");
            if (CollectionUtils.isNotEmpty(adGroupList)) {
                baseSql.append(SqlStringUtil.dealInList("t.ad_group_id", adGroupList, argsList));
            }
            //否定关键词状态筛选
            baseSql.append(SqlStringUtil.dealSingleField("t.state", param.getState(), argsList));
            //搜索keyword_text
            if (StringUtils.isNotBlank(param.getSearchValue()) && StringUtils.isNotBlank(param.getSearchField())) {
                baseSql.append("and ").append("t.target_text").append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }

            baseSql.append(filterDto.getAdvanceFilterParams() != null ? SqlStringReportUtil.dealAdvancedFilterForNeTargetWithWhereAnd(filterDto.getAdvanceFilterParams(), argsList, filterDto.getOnlyShowImpressions()): SqlStringReportUtil.dealOnlyShowImpressionsWithWhereAnd(filterDto.getOnlyShowImpressions()));

            // SQL to count the total rows
            StringBuilder countSql = new StringBuilder()
                .append("SELECT COUNT(*) ")
                .append(baseSql);

            // SQL to select keyword IDs, ordered by sum_acos and creation date
            StringBuilder selectSql = new StringBuilder()
                .append("SELECT t.target_id, t.create_time, t.creation_date ")
                .append(baseSql)
                .append("ORDER BY ");
            if (order && Objects.nonNull(orderFieldEnum)) {
                selectSql.append("r.")
                    .append(orderFieldEnum.getPoSumParam())
                    .append(filterDto.getOrderType())
                    .append(",")
                ;
            }
            selectSql.append(" IFNULL(t.creation_date, t.create_time) DESC");

            // Execute query and return paged results
            return getPageByMapper(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), (res, i) -> res.getString("target_id"));
        }

        return new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, Collections.EMPTY_LIST);
    }

    @Override
    public List<NeTargetReportDataDto> get30ReportByTargetIds(int puid, int shopId, List<String> keywordIds, NeTargetReportFilterDto neTargetReportFilterDto, String type) {
        boolean target = neTargetReportFilterDto.getDataFrom() != 2;

        //未指定类型 或 指定sp
        if (Constants.SP.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select target_id, cost, total_sales, impressions, clicks, sale_num ad_order_num ");
            sql.append(" from " + (target ? NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getTargetReportTableNameFor30Days() : NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getQueryReportTableNameFor30Days()))
                .append(" where puid = ? and shop_id = ? and report_date_type = ? ");
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportDateType());

            if (keywordIds.size() >= 10000) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList));
            } else {
                sql.append(SqlStringUtil.dealInList("target_id", keywordIds, argsList));
            }

            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        if (Constants.SB.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select target_id, cost, sales14d total_sales, impressions, clicks, conversions14d ad_order_num ");
            sql.append(" from " + (target ? NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getTargetReportTableNameFor30Days() : NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getQueryReportTableNameFor30Days()))
                .append(" where puid = ? and shop_id = ? and report_date_type = ? ");
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportDateType());

            if (keywordIds.size() >= 10000) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList));
            } else {
                sql.append(SqlStringUtil.dealInList("target_id", keywordIds, argsList));
            }

            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        if (Constants.SD.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select target_id, cost, sales14d total_sales, impressions, clicks, conversions14d ad_order_num ");
            sql.append(" from " + (target ? NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getTargetReportTableNameFor30Days() : NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getQueryReportTableNameFor30Days()))
                .append(" where puid = ? and shop_id = ? and report_date_type = ? ");
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportDateType());

            if (keywordIds.size() >= 10000) {
                sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList));
            } else {
                sql.append(SqlStringUtil.dealInList("target_id", keywordIds, argsList));
            }

            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        return Collections.emptyList();
    }

    @Override
    public List<NeTargetReportDataDto> getReportByTargetIds(int puid, int shopId, List<String> keywordIds, NeTargetReportFilterDto neTargetReportFilterDto, String type) {
        boolean target = neTargetReportFilterDto.getDataFrom() != 2;
        if (Constants.SP.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select target_id target_id "
                + ",sum(cost) cost "
                + ",sum(total_sales) total_sales "
                + ",sum(impressions) impressions "
                + ",sum(clicks) clicks "
                + ",sum(sale_num) ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getReportTableName() : NeTargetReportTableEnum.SP_AD_GROUP_NE_TARGET.getQueryReportTableName());
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportStartDate());
            argsList.add(neTargetReportFilterDto.getReportEndDate());
            sql.append(" where puid = ? and shop_id = ? and count_day between ? and ? ")
                .append(keywordIds.size() >= 10000 ? SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList) : SqlStringUtil.dealInList("target_id", keywordIds, argsList))
                .append(" group by target_id");
            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        if (Constants.SB.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select target_id target_id "
                    + ",sum(cost) cost "
                    + ",sum(sales14d) total_sales "
                    + ",sum(impressions) impressions "
                    + ",sum(clicks) clicks "
                    + ",sum(conversions14d) ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getReportTableName() : NeTargetReportTableEnum.SB_AD_GROUP_NE_TARGET.getQueryReportTableName());
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportStartDate());
            argsList.add(neTargetReportFilterDto.getReportEndDate());
            sql.append(" where puid = ? and shop_id = ? and count_day between ? and ? ")
                .append(keywordIds.size() >= 10000 ? SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList) : SqlStringUtil.dealInList("target_id", keywordIds, argsList))
                .append(" group by target_id");

            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        if (Constants.SD.equalsIgnoreCase(type)) {
            StringBuilder sql = new StringBuilder();
            sql.append("select target_id target_id "
                + ",sum(cost) cost "
                + ",sum(sales14d) total_sales "
                + ",sum(impressions) impressions "
                + ",sum(clicks) clicks "
                + ",sum(conversions14d) ad_order_num ")
                .append(" from ")
                .append(target ? NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getReportTableName() : NeTargetReportTableEnum.SD_AD_GROUP_NE_TARGET.getQueryReportTableName());
            List<Object> argsList = new ArrayList<>();
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(neTargetReportFilterDto.getReportStartDate());
            argsList.add(neTargetReportFilterDto.getReportEndDate());
            sql.append(" where puid = ? and shop_id = ? and count_day between ? and ? ")
                .append(keywordIds.size() >= 10000 ? SqlStringUtil.dealBitMapDorisInList("target_id", keywordIds, argsList) : SqlStringUtil.dealInList("target_id", keywordIds, argsList))
                .append(" group by target_id");

            return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(NeTargetReportDataDto.class), argsList.toArray());
        }
        return Collections.emptyList();

    }

    /**
     * 过滤服务状态
     *
     * @param servingStatusList
     * @return
     */
    private List<String> getServingStatus(List<String> servingStatusList) {
        List<String> statusList = new ArrayList<>();
        for (String servingStatusKey : servingStatusList) {
            List<String> servingStatusValueList = Constants.SERVER_STATUS_SELECT.get(servingStatusKey);
            if (servingStatusValueList != null) {
                statusList.addAll(servingStatusValueList);
            }
        }
        return statusList;
    }

    /**
     * 通过广告组合id查询活动id的sql
     *
     * @param puid
     * @param shopId
     * @param portfolioId
     * @param type
     * @param state
     * @param servingStatus
     * @return
     */
    private String getCampaignIdsByPortfolioIdSql(Integer puid, Integer shopId, String portfolioId, String type, String state, String servingStatus, List<Object> argsList) {
        // portfolioIds筛选掉-1的情况
        List<String> portfolioIds = new ArrayList<>();
        if (StringUtils.isNotBlank(portfolioId)) {
            portfolioIds = StringUtil.splitStr(portfolioId).stream().distinct()
                .filter(item -> !item.equals("-1")).collect(Collectors.toList());
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (StringUtils.isNotBlank(state)) {
            List<String> stateList = Arrays.asList(state.split(","));
            if (stateList.size() > 0) {
                builder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = getServingStatus(servingStatusList);
                if (org.apache.commons.collections.CollectionUtils.isNotEmpty(statusList)) {
                    builder.in("serving_status", statusList.toArray());
                }
            }
        }
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        // 选择‘未分类’：没有广告组合的广告活动
        if ("-1".equals(portfolioId)) {
            builder.isNull(LogicType.AND, "portfolio_id");
        } else if (org.apache.commons.collections.CollectionUtils.isNotEmpty(portfolioIds)) {
            if (portfolioId.contains("-1")) {
                builder.and().leftBracket().in(LogicType.EPT, "portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
                builder.isNull(LogicType.OR, "portfolio_id").rightBracket();
            } else {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[portfolioIds.size()]));
            }
        }
        if (StringUtils.isNotBlank(type)) {
            builder.in("type", StringUtil.splitStr(type).toArray());
            //            builder.equalTo("type", type);
        }
        argsList.addAll(Arrays.asList(builder.build().getValues()));
        return "select campaign_id from ods_t_amazon_ad_campaign_all where " + builder.build().getSql();
    }

    @Override
    public Page<AsinLibsDetailVo> getAsinNeTargetList(Integer puid, AsinLibsDetailParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuffer countSql = new StringBuffer();
        countSql.append("select count(*) from ( ");
        StringBuffer selectSql = new StringBuffer();
        selectSql.append("select create_time createTime, state, shop_id shopId, target_id targetId, campaign_id campaignId, marketplace_id marketplaceId, ad_group_id adGroupId, type, matchType, targetingText ");
        selectSql.append(" from ( ");
        if (CollectionUtils.isEmpty(param.getSearchAdGroupList())) {
            selectSql.append("select create_time, state, shop_id, marketplace_id, campaign_id, target_id , '' as ad_group_id, 'sp' as type, type matchType, target_text targetingText from ods_t_amazon_ad_campaign_netargeting_sp ");
            selectSql.append(" where puid = ? ");
            argsList.add(puid);
            if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
                selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getCountry())) {
                selectSql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
            }
            if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
                selectSql.append(" and campaign_id in ( ");
                selectSql.append(this.campaignSql(puid, param, argsList));
                selectSql.append(" ) ");
            }
            if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
                selectSql.append(SqlStringUtil.dealInList("campaign_id", param.getSearchCampaignList(), argsList));
            }
            selectSql.append(" and `type` = 'asin' and lower(target_text) = ? ");
            argsList.add(param.getAsin().toLowerCase());
            selectSql.append(" UNION ALL ");
        }
        selectSql.append(" select create_time, state, shop_id, marketplace_id, campaign_id, target_id, ad_group_id, 'sp' as type, type matchType, targeting_value targetingText from ods_t_amazon_ad_ne_targeting ");
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            selectSql.append(" and campaign_id in ( ");
            selectSql.append(this.campaignSql(puid, param, argsList));
            selectSql.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            selectSql.append(SqlStringUtil.dealInList("campaign_id", param.getSearchCampaignList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" and `type` = 'negativeAsin' and lower(targeting_value) = ? ");
        argsList.add(param.getAsin().toLowerCase());
        selectSql.append(" UNION ALL ");
        selectSql.append(" select create_time, state, shop_id, marketplace_id, campaign_id, target_id, ad_group_id, 'sb' as type, type matchType, target_text targetingText from ods_t_amazon_ad_netargeting_sb ");
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            selectSql.append(" and campaign_id in ( ");
            selectSql.append(this.campaignSql(puid, param, argsList));
            selectSql.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            selectSql.append(SqlStringUtil.dealInList("campaign_id", param.getSearchCampaignList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" and `type` = 'asin' and lower(target_text) = ? ");
        argsList.add(param.getAsin().toLowerCase());
        selectSql.append(" UNION ALL ");
        selectSql.append(" select create_time, state, shop_id, marketplace_id, '' campaign_id, target_id, ad_group_id, 'sd' as type, type matchType, target_text targetingText from ods_t_amazon_ad_netargeting_sd ");
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            selectSql.append(" and ad_group_id in ( ");
            selectSql.append("select ad_group_id from ods_t_amazon_ad_group_sd where campaign_id in (");
            selectSql.append(this.campaignSql(puid, param, argsList));
            selectSql.append(" and puid = ? ");
            argsList.add(puid);
            selectSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
            selectSql.append(" ) )");
        }
        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            selectSql.append(" and ad_group_id in ( ");
            selectSql.append("select ad_group_id from ods_t_amazon_ad_group_sd where ");
            selectSql.append(SqlStringUtil.dealInListNotAnd("campaign_id", param.getSearchCampaignList(), argsList));
            selectSql.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealInList("ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" and `type` = 'asin' and lower(target_text) = ? ) c");
        argsList.add(param.getAsin().toLowerCase());
        selectSql.append(" order by target_id desc");
        countSql.append(selectSql).append(" ) s");
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), AsinLibsDetailVo.class);
    }

    private String campaignSql(Integer puid, AsinLibsDetailParam param, List<Object> argsList) {
        StringBuilder sql = new StringBuilder(" select DISTINCT campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
        }
        if(org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getSearchPortfolioList())){
            sql.append(" and ( ");
            sql.append(SqlStringUtil.dealInListNotAnd("portfolio_id", param.getSearchPortfolioList(), argsList));
            if (param.getSearchPortfolioList().contains("-1")) {
                sql.append(" or portfolio_id is null ");
            }
            sql.append(" ) ");
        }
        return sql.toString();
    }

    @Override
    public List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select targeting_value asin, count(*) targetNum from ods_t_amazon_ad_ne_targeting " +
                "where puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)){
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getContryList())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getContryList(), argsList));
        }

        sql.append(" and `type` = 'negativeAsin' ");

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(asinList)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String asin : asinList) {
                lowerCaseList.add(asin.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(targeting_value)",lowerCaseList,argsList));
        }
        sql.append(" group by targeting_value");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AsinLibsVo.class), argsList.toArray());

    }

    @Override
    public Page<OdsAmazonAdTargeting> listPageByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildSpSelectSql(puid, shopIds, param, "ods_t_amazon_ad_ne_targeting d", args);
        String sbSql = buildSelectSql(puid, shopIds, param, "ods_t_amazon_ad_netargeting_sb d", args);
        String sbcSql = buildSelectSql(puid, shopIds, param, "ods_t_amazon_ad_campaign_netargeting_sp d", args);
        String totalSql = spSql + " union all " + sbSql + " union all  " + sbcSql ;
        if (CollectionUtils.isNotEmpty(param.getAdGroupList())) {
            String sdSql = buildSdSelectSql(puid, shopIds, param, "ods_t_amazon_ad_netargeting_sd d", args);
            totalSql = totalSql + " union all " + sdSql;
        }
        String countSql = " select count(*)  from ( " + totalSql + " ) r";
        String selectSql = " select shop_id, campaign_id, create_time from ( " + totalSql + " ) r order by create_time desc";
        Object[] arg = args.toArray();
        String sql = SqlStringUtil.exactSql(selectSql, args);
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), countSql, arg, selectSql, arg, OdsAmazonAdTargeting.class);
    }

    private String buildSdSelectSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sdSql = new StringBuilder("select d.shop_id, g.campaign_id,max(d.create_time) create_time from " + tableName +
                " join ods_t_amazon_ad_group_sd g on d.puid = g.puid and d.shop_id = g.shop_id and d.ad_group_id = g.ad_group_id ");
        sdSql.append(" where d.puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sdSql.append(SqlStringUtil.dealInList("d.shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getAdGroupList())) {
            sdSql.append(SqlStringUtil.dealInList("d.ad_group_id",param.getAdGroupList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sdSql.append(" and lower(d.target_text) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sdSql.append(" group by campaign_id,shop_id ");
        return sdSql.toString();
    }

    @Override
    public List<OdsAmazonAdTargeting> listAllNeGroupByCondition(Integer puid, List<Integer> shopIds, AsinLibsParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildGroupSpSql(puid, shopIds, param, "ods_t_amazon_ad_ne_targeting", args);
        String sbSql = buildGroupSql(puid, shopIds, param, "ods_t_amazon_ad_netargeting_sb", args);
        String sdSql = buildGroupSdSql(puid, shopIds, param, "ods_t_amazon_ad_netargeting_sd", args);
        String totalSql = spSql + " union all " + sbSql + " union all " + sdSql;
        String selectSql = " select shop_id , ad_group_id  from ( " + totalSql + " ) r ";
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(selectSql, arg, new ObjectMapper<>(OdsAmazonAdTargeting.class));
    }

    private String buildGroupSdSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, ad_group_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(" and ad_group_id in ( select ad_group_id from ods_t_amazon_ad_group_sd where ");
            sbSql.append(SqlStringUtil.dealBitMapDorisInNoAndList("campaign_id", param.getCampaignList(), args));
            sbSql.append(" ) ");
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(target_text) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" group by ad_group_id,shop_id ");
        return sbSql.toString();
    }

    private String buildGroupSpSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, ad_group_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(targeting_value) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" group by ad_group_id,shop_id ");
        return sbSql.toString();
    }

    private String buildGroupSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, ad_group_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(target_text) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" group by ad_group_id,shop_id ");
        return sbSql.toString();
    }

    private String buildSpSelectSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, campaign_id, max(d.create_time) create_time from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealInList("campaign_id",param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(targeting_value) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" and campaign_id is not null ");
        sbSql.append(" group by campaign_id,shop_id ");
        return sbSql.toString();
    }

    private String buildSelectSql(Integer puid, List<Integer> shopIds, AsinLibsParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, campaign_id, max(d.create_time) create_time from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignList())) {
            sbSql.append(SqlStringUtil.dealInList("campaign_id",param.getCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getAsin())) {
            sbSql.append(" and lower(target_text) = ? ");
            args.add(param.getAsin().toLowerCase());
        }
        sbSql.append(" and campaign_id is not null ");
        sbSql.append(" group by campaign_id,shop_id ");
        return sbSql.toString();
    }
}

