package com.meiyunji.sponsored.service.multiPlatform.tiktok.client;

import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

@Component
public class CampaignRateLimiter {

    // 令牌桶容量
    private final int capacity = 8;
    // 令牌生成速率(个/秒)
    private final int rate = 5;
    // 当前令牌数量
    private AtomicInteger tokens = new AtomicInteger(capacity);
    // 上次生成令牌的时间戳(毫秒)
    private AtomicLong lastRefillTime = new AtomicLong(System.currentTimeMillis());

    /**
     * 获取令牌
     */
    public void acquire() {
        // 先尝试获取令牌
        if (tokens.getAndUpdate(t -> t > 0 ? t - 1 : 0) > 0) {
            return;
        }

        // 没有令牌，需要等待
        synchronized (this) {
            // 先刷新令牌
            refill();
            // 再次尝试获取
            while (tokens.getAndUpdate(t -> t > 0 ? t - 1 : 0) <= 0) {
                try {
                    // 等待一段时间再试
                    Thread.sleep(100);
                    refill();
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    return;
                }
            }
        }
    }

    /**
     * 刷新令牌
     */
    private void refill() {
        long now = System.currentTimeMillis();
        long lastTime = lastRefillTime.get();
        // 计算应该生成的令牌数量
        int generatedTokens = (int) ((now - lastTime) / 1000 * rate);
        if (generatedTokens > 0) {
            // 确保令牌数量不超过容量
            tokens.updateAndGet(t -> Math.min(capacity, t + generatedTokens));
            lastRefillTime.set(now);
        }
    }

}
