package com.meiyunji.sponsored.service.cpc.vo;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * @author: sunlin<PERSON>
 * @email: <EMAIL>
 * @date: 2024-05-20  09:36
 */
@Data
public class MultiShopGroupListParam {
    //商户id
    private Integer puid;
    //店铺id集合
    private List<Integer> shopIdList;
    //广告组合id集合
    private List<String> portfolioIdList;
    //广告活动id集合
    private List<String> campaignIdList;
    //广告组名称
    private String searchValue;
    //广告组名称
    private List<String> searchValueList;
    //搜索类型，exact为精确匹配
    private String searchType;
    //组状态(enabled，paused，archived)
    private List<String> stateList;
    //广告类型(sp,sb,sd)
    private List<String> adTypeList;
    //页码
    private Integer pageNo;
    //页数
    private Integer pageSize;

    private Map<String, List<String>> campaignIdMap;
    //站点列表数组
    private List<String> marketplaceId;

    private Integer shopId;

    private String adGroupId;
    //广告组id集合
    private List<String> groupIdList;
    //是否需要根据状态排序
    private Boolean needOrder;

    private String groupType;

    //广告组投放类型
    private String adGroupType;
}
