package com.meiyunji.sponsored.service.cpc.dao.impl;


import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSpAdProductDorisDao;
import com.meiyunji.sponsored.service.cpc.dto.AdProductReportSearchTermsViewDto;
import com.meiyunji.sponsored.service.cpc.dto.GroupCampaignDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductDorisAllReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.SearchTermsViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Repository
public class AmazonSpAdProductDorisDaoImpl extends DorisBaseDaoImpl<AmazonAdProductDorisAllReport> implements IAmazonSpAdProductDorisDao {

    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private IOdsAmazonAdCampaignAllDao odsAmazonAdCampaignAllDao;

    @Autowired
    private IAdProductRightService adProductRightService;

    private static final Set<String> ORDER_FIELD_SET = Sets.newHashSet(
            "adSalesDoris", "impressionsDoris", "clicksDoris",
             "adCostPerClickDoris", "ctrDoris", "cvrDoris", "cpcDoris", "acosDoris", "roasDoris",
            "cpaDoris", "adOtherOrderNumDoris", "adOtherSalesDoris", "adOtherSaleNumDoris", "advertisingUnitPriceDoris");

    @Override
    public Page<AmazonAdProductDorisAllReport> listAmazonSpAdProductPage(Integer puid, AdProductPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.ad_id ad_id, ")
                .append(String.join(",", SQL_MAP.values()));
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        selectSql.append(",").append(getSqlField(bool ? param.getOrderField() : null)).append("  ");
        StringBuilder countSql = new StringBuilder("select count(*) from (select c.ad_id ad_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        selectSql.append(" from ods_t_amazon_ad_product c left join ods_t_amazon_ad_product_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        countSql.append(" from ods_t_amazon_ad_product c left join ods_t_amazon_ad_product_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id  and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_id ", false);
        selectSql.append(whereBuilder);
        countSql.append(whereBuilder);
        countSql.append(" ) r");
        // 查看是否排序
        selectSql.append(" order by ").append(getOrderField(bool ? param.getOrderField() : null)).append(" ");
        if (StringUtils.isBlank(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType())) {
            selectSql.append(" desc");
        }
        selectSql.append(" , ad_id desc");
        Object[] args = argsList.toArray();
        return getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdProductDorisAllReport.class);
    }


    @Override
    public int listAmazonSpAdProductAllCount(Integer puid, AdProductPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder countSql = new StringBuilder("select count(*) from (select c.ad_id ad_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        countSql.append(" from ods_t_amazon_ad_product c left join ods_t_amazon_ad_product_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id  and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_id ", false);
        countSql.append(whereBuilder);
        countSql.append(" ) r");
        // 查看是否排序
        Object[] args = argsList.toArray();
        return countPageResult(puid, countSql.toString(), args);
    }

    @Override
    public List<AmazonAdProduct> listByAdIds(Integer puid, Integer shopId, List<String> adIds) {
        StringBuilder sql = new StringBuilder(" select * from ods_t_amazon_ad_product where puid = ? AND shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("ad_id", adIds, argsList));
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(AmazonAdProduct.class), argsList.toArray());
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, AdProductPageParam param) {
        List<Object> argsList = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        keySet.add("costDoris");
        keySet.add("totalSalesDoris");
        keySet.add("orderNumDoris");
        keySet.add("saleNumDoris");
        boolean useAdvanced = param.getUseAdvanced() != null && param.getUseAdvanced();
        if (useAdvanced) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    keySet.addAll(fieldKey);
                }
            }
        }
        StringBuilder sumSql = new StringBuilder(" select ").append(useAdvanced ? " c.ad_id ad_id," : " ")
                .append(keySet.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
        sumSql.append(" from ods_t_amazon_ad_product c join ods_t_amazon_ad_product_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, useAdvanced ? " group by ad_id " : null, true);
        sumSql.append(whereBuilder);
        List<AdMetricDto> list = getJdbcTemplate().query(sumSql.toString(), (re, i) -> AdMetricDto.builder()
                .sumCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .sumAdSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("orderNumDoris")).orElse(BigDecimal.ZERO))
                .sumOrderNum(Optional.ofNullable(re.getBigDecimal("saleNumDoris")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
        if (useAdvanced) {
            if (CollectionUtils.isEmpty(list)) {
                return null;
            }
            return AdMetricDto.builder()
                    .sumCost(list.stream().map(AdMetricDto::getSumCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumAdSale(list.stream().map(AdMetricDto::getSumAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumAdOrderNum(list.stream().map(AdMetricDto::getSumAdOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .sumOrderNum(list.stream().map(AdMetricDto::getSumOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .build();
        } else {
            return list.size() > 0 ? list.get(0) : null;
        }
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonSpAdProductGroupAdId(Integer puid, AdProductPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.ad_id ad_id, ")
                .append(String.join(",", SQL_MAP.values()));
        selectSql.append(" from ods_t_amazon_ad_product c join ods_t_amazon_ad_product_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_id ", true);
        selectSql.append(whereBuilder);
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adId(re.getString("ad_id"))
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))  //销量字段订单
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .countDate("")
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */
                .adSaleNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                .type("sp")
                .build(), argsList.toArray());
    }

    @Override
    public AdHomePerformancedto listTotalAmazonSpAdProductCompareData(Integer puid, AdProductPageParam param, String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        startDate = DateUtil.getDateSqlFormat(startDate);
        endDate = DateUtil.getDateSqlFormat(endDate);
        StringBuilder sql = new StringBuilder(" select ").append(String.join(",", SQL_MAP.values()));
        sql.append(" from ods_t_amazon_ad_product_report r where r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(" and ad_id in ( ").append(this.idListAmazonSpAdProductPageSql(puid, param, argsList)).append(" ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))  //销量字段订单
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .countDate("")
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */
                .adSaleNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                .type("sp")
                .build(), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }

    private String idListAmazonSpAdProductPageSql(Integer puid, AdProductPageParam param, List<Object> argsList) {
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder sql = new StringBuilder("select ad_id from (select c.ad_id ad_id");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    sql.append(",");
                    sql.append(fieldKey.stream().map(SQL_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        sql.append(" from ods_t_amazon_ad_product c left join ods_t_amazon_ad_product_report r on r.puid = c.puid and r.shop_id=c.shop_id and r.ad_id=c.ad_id  and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildAdProductWhere(param, argsList, " group by ad_id ", false);
        sql.append(whereBuilder);
        sql.append(") a");
        return sql.toString();
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonSpAdProductGroupDateById(Integer puid, AdProductPageParam param, List<String> adId) {
        if (CollectionUtils.isEmpty(adId)) {
            return new ArrayList<>();
        }
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(" ods_t_amazon_ad_product_report ");
        sql.append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (adId.size() >= 10000) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", adId, argsList));
        } else {
            sql.append(SqlStringUtil.dealInList("ad_id", adId, argsList));
        }
        sql.append("  and count_day >= ? and count_day <= ? group by count_date ");
        argsList.add(startDate);
        argsList.add(endDate);
        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("sale_num")).orElse(0))  //销量字段订单
                .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicks")).orElse(0))
                .impressions(Optional.of(re.getInt("impressions")).orElse(0))
                .countDate(re.getString("count_date"))
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */
                .adSaleNum(Optional.of(re.getInt("ad_sale_num")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("sales_num")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("ad_order_num")).orElse(0))
                .type("sp")
                .build(), argsList.toArray());
    }

    /**
     *  构建主表的where语句
     */
    private StringBuilder buildAdProductWhere(AdProductPageParam param, List<Object> argsList, String groupSql, boolean isNull) {
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告组合查询
        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            whereSql.append(" and c.campaign_id in ( ")
                    .append(odsAmazonAdCampaignAllDao.getCampaignIdsByPortfolioIdSql(param.getPuid(), param.getShopId(), argsList, param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), null, null))
                    .append(" ) ");
        }
        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            if (campaignIds.size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.campaign_id", campaignIds, argsList));
            }
        }

        String getProductRightCampaignIdsSqlFromGrpc = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(param.getPuid(), Lists.newArrayList(param.getShopId()), Lists.newArrayList(CampaignTypeEnum.sp), argsList, "c.campaign_id");
        if (StringUtils.isNotBlank(getProductRightCampaignIdsSqlFromGrpc)) {
            whereSql.append(" and ").append(getProductRightCampaignIdsSqlFromGrpc);
        }



        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            if (groupIds.size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", groupIds, argsList));
            }
        }

        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            if (param.getAdIds().size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_id", param.getAdIds(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_id", param.getAdIds(), argsList));
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.asin", param.getListSearchValueNew(), argsList));
                }else {
                    whereSql.append(" and lower(c.asin) = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim().toLowerCase());
                }
            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("c.sku", param.getListSearchValueNew(), argsList));
                } else {
//                    whereSql.append(" and lower(c.sku) like '%").append(param.getListSearchValueNew().get(0).trim().toLowerCase()).append("%' ");
                    whereSql.append(" and lower(c.sku) like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim().toLowerCase() + "%");
                }
            } else if ("parentAsin".equalsIgnoreCase(param.getSearchField())) {
                List<String> asin = odsProductDao.listByParentAsin(param.getPuid(), param.getShopId(), param.getListSearchValueNew());
                if (asin.size() == 0) {
                    asin.add("-1");
                }
                whereSql.append(SqlStringUtil.dealInList("c.asin", asin, argsList));
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }
        }

        if (isNull) {
            whereSql.append(" and r.puid = ? ");
            argsList.add(param.getPuid());
        }
        if (StringUtils.isNotBlank(groupSql)) {
            whereSql.append(groupSql);
        }
        whereSql.append(subWhereSql(param, argsList));
        return whereSql;
    }

    /**
     * 高级查询的where语句
     */
    private StringBuilder subWhereSql(AdProductPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (param.getUseAdvanced()) {
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.ZERO;

            subWhereSql.append(" having 1=1 ");
            //展示量
            if (param.getImpressionsMin() != null) {
                subWhereSql.append(" and impressionsDoris >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if (param.getImpressionsMax() != null) {
                subWhereSql.append(" and impressionsDoris <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if (param.getClicksMin() != null) {
                subWhereSql.append(" and clicksDoris >= ?");
                argsList.add(param.getClicksMin());
            }
            if (param.getClicksMax() != null) {
                subWhereSql.append(" and clicksDoris <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (param.getClickRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if (param.getClickRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if (param.getCostMin() != null) {
                subWhereSql.append(" and costDoris >= ?");
                argsList.add(param.getCostMin());
            }
            if (param.getCostMax() != null) {
                subWhereSql.append(" and costDoris <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if (param.getCpcMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if (param.getCpcMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if (param.getOrderNumMin() != null) {
                subWhereSql.append(" and orderNumDoris >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if (param.getOrderNumMax() != null) {
                subWhereSql.append(" and orderNumDoris <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if (param.getSalesMin() != null) {
                subWhereSql.append(" and totalSalesDoris >= ?");
                argsList.add(param.getSalesMin());
            }
            if (param.getSalesMax() != null) {
                subWhereSql.append(" and totalSalesDoris <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if (param.getSalesConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if (param.getSalesConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if (param.getAcosMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if (param.getAcosMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and saleNumDoris >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }

            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and saleNumDoris <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }
            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 2) >= ? ");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/orderNumDoris, 0), 2) <= ? ");
                argsList.add(param.getCpaMax());
            }
            //本广告产品订单量（绝对值）adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }

            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherOrderNumMin());
            }

            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherOrderNumMax());
            }

            //本广告产品销售额（绝对值） adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }

            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }

            //其他产品广告销售额（绝对值）adOtherSalesMin
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSalesMin());
            }

            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSalesMax());
            }

            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdSelfSaleNumMax());
            }

            //其他产品广告销量（绝对值）adOtherSaleNumMin
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
        }
        return subWhereSql;
    }

    /**
     * 获取排序字段
     */
    private String getOrderField(String field) {
        if (StringUtils.isBlank(field)) {
            return " dataUpdateTime ";
        }
        if (ORDER_FIELD_SET.contains(field + "Doris")) {
            return " " + field + "Doris ";
        }
        Set<String> set = Sets.newHashSet("adCost", "adCostPercentage",
                "acots", "adSale", "adSalePercentage", "asots", "adOrderNum", "adOrderNumPercentage",
                "adSaleNum", "saleNum", "orderNum", "orderNumPercentage", "adSelfSaleNum");

        if (set.contains(field)) {
            switch (field) {
                case "adCost":
                case "adCostPercentage":
                case "acots":
                    return " costDoris ";
                case "adSale":
                case "adSalePercentage":
                case "asots":
                    return " totalSalesDoris ";
                // 广告订单量
                case "adOrderNum":
                    return " orderNumDoris ";
                case "adOrderNumPercentage":
                    return " orderNumDoris ";
                //本广告产品订单量
                case "adSaleNum":
                    return " adSaleNumDoris ";
                //广告销量
                case "saleNum":
                    return " orderNumDoris ";
                case "orderNum":
                case "orderNumPercentage":
                    return " saleNumDoris ";
                //本广告产品销量
                case "adSelfSaleNum":
                    return " adOrderNumDoris ";
                //其他产品广告销量
                default:
                    return " " + field + "Doris ";
            }
        } else {
            return " dataUpdateTime ";
        }
    }

    private static final Map<String, String> SQL_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costDoris", "IFNULL(sum(r.cost),0) `costDoris`");
            put("totalSalesDoris", "IFNULL(sum(r.total_sales),0) totalSalesDoris");
            put("adSalesDoris", "IFNULL(sum(r.ad_sales),0) adSalesDoris");
            put("impressionsDoris", "IFNULL(sum(`impressions`),0) impressionsDoris");
            put("clicksDoris", "IFNULL(sum(`clicks`),0) clicksDoris");

            put("orderNumDoris", "IFNULL(sum(r.sale_num),0) orderNumDoris");
            put("adOrderNumDoris", "IFNULL(sum(r.ad_order_num),0) adOrderNumDoris");
            put("saleNumDoris", "IFNULL(sum(r.order_num),0) saleNumDoris");
            put("adSaleNumDoris", "IFNULL(sum(r.ad_sale_num),0) adSaleNumDoris");
        }
    });

    /**
     * 获取排序的sql
     */
    private String getSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " any(c.create_time) dataUpdateTime ";
        }
        switch (field) {
            case "adCostPerClick":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as adCostPerClickDoris ";
//            case "vcpm":
//                return " ifnull((sum(r.cost)/sum(if (r.type = 'sb', `viewable_impressions`, `view_impressions`))) * 1000, 0) as vcpmDoris ";
            case "ctr":
                return " ifnull(sum(`clicks`)/sum(`impressions`),0) as ctrDoris ";
            case "cvr":
                return " ifnull(sum(sale_num)/sum(`clicks`),0) as cvrDoris ";
            case "cpc":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as cpcDoris ";
            case "acos":
                return " ifnull(sum(r.cost)/sum(total_sales),0) as acosDoris ";
            case "roas":
                return " ifnull(sum(total_sales)/sum(r.cost),0) as roasDoris ";
            case "cpa":
                return " ifnull(sum(r.cost)/sum(sale_num),0) as cpaDoris ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(sale_num - ad_sale_num),0) as adOtherOrderNumDoris ";
            //其他产品广告销售额
            case "adOtherSales":
                return "ifnull(sum(total_sales)-sum(ad_sales),0) as adOtherSalesDoris ";
            //广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(order_num - ad_order_num),0) as adOtherSaleNumDoris ";
            case  "advertisingUnitPrice":
                return " ifnull(sum(total_sales)/sum(sale_num),0) as advertisingUnitPriceDoris";
            default:
                return " any(c.create_time) dataUpdateTime ";
        }
    }

    /**
     * 获取高级排序 需要的字段
     */
    private Set<String> getSelectKey(AdProductPageParam param) {
        Set<String> keySet = new HashSet<>();
        if (param.getImpressionsMin() != null) {
            keySet.add("impressionsDoris");
        }
        if (param.getImpressionsMax() != null) {
            keySet.add("impressionsDoris");
        }
        //点击量
        if (param.getClicksMin() != null) {
            keySet.add("clicksDoris");
        }
        if (param.getClicksMax() != null) {
            keySet.add("clicksDoris");
        }
        //点击率（clicks/impressions）
        if (param.getClickRateMin() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        if (param.getClickRateMax() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        //花费
        if (param.getCostMin() != null) {
            keySet.add("costDoris");
        }
        if (param.getCostMax() != null) {
            keySet.add("costDoris");
        }
        //cpc  平均点击费用
        if (param.getCpcMin() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        if (param.getCpcMax() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        //广告订单量
        if (param.getOrderNumMin() != null) {
            keySet.add("orderNumDoris");
        }
        if (param.getOrderNumMax() != null) {
            keySet.add("orderNumDoris");
        }
        //广告销售额
        if (param.getSalesMin() != null) {
            keySet.add("totalSalesDoris");
        }
        if (param.getSalesMax() != null) {
            keySet.add("totalSalesDoris");
        }
        //订单转化率
        if (param.getSalesConversionRateMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("clicksDoris");
        }
        if (param.getSalesConversionRateMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("clicksDoris");
        }
        //acos
        if (param.getAcosMin() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        if (param.getAcosMax() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        // roas
        if (param.getRoasMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // roas
        if (param.getRoasMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            keySet.add("costDoris");
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            keySet.add("totalSalesDoris");
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            keySet.add("totalSalesDoris");
        }

//        //可见展示次数
//        if (param.getViewImpressionsMin() != null) {
//            keySet.add("viewImpressionsDoris");
//        }
//        if (param.getViewImpressionsMax() != null) {
//            keySet.add("viewImpressionsDoris");
//        }

        //广告销量
        if (param.getAdSalesTotalMin() != null) {
            keySet.add("saleNumDoris");
        }

        if (param.getAdSalesTotalMax() != null) {
            keySet.add("saleNumDoris");
        }
        //CPA
        if (param.getCpaMin() != null) {
            keySet.add("costDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getCpaMax() != null) {
            keySet.add("costDoris");
            keySet.add("orderNumDoris");
        }
//        //VCPM= 每千次展现费用=广告花费/可见展现次数x1000
//        if (param.getVcpmMin() != null) {
//            keySet.add("costDoris");
//            keySet.add("viewImpressionsDoris");
//        }
//        if (param.getVcpmMax() != null) {
//            keySet.add("costDoris");
//            keySet.add("viewImpressionsDoris");
//        }
        //本广告产品订单量（绝对值）adSaleNumMin
        if (param.getAdSaleNumMin() != null) {
            keySet.add("adSaleNumDoris");
        }
        if (param.getAdSaleNumMax() != null) {
            keySet.add("adSaleNumDoris");
        }

        //其他产品广告订单量（绝对值） adOtherOrderNumMin
        if (param.getAdOtherOrderNumMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adSaleNumDoris");
        }

        if (param.getAdOtherOrderNumMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adSaleNumDoris");
        }

        //本广告产品销售额（绝对值） adSalesMin
        if (param.getAdSalesMin() != null) {
            keySet.add("adSalesDoris");
        }

        if (param.getAdSalesMax() != null) {
            keySet.add("adSalesDoris");
        }

        //其他产品广告销售额（绝对值）adOtherSalesMin
        if (param.getAdOtherSalesMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        if (param.getAdOtherSalesMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
        if (param.getAdSelfSaleNumMin() != null) {
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdSelfSaleNumMax() != null) {
            keySet.add("adOrderNumDoris");
        }

        //其他产品广告销量（绝对值）adOtherSaleNumMin
        if (param.getAdOtherSaleNumMin() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdOtherSaleNumMax() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adOrderNumDoris");
        }

        //“品牌新买家”订单量（绝对值） orders_new_to_brand14d
        if (param.getOrdersNewToBrandFTDMin() != null) {
            keySet.add("ordersNewToBrand14dDoris");
        }
        if (param.getOrdersNewToBrandFTDMax() != null) {
            keySet.add("ordersNewToBrand14dDoris");
        }

        // 广告笔单价 筛选
        if (param.getAdvertisingUnitPriceMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("orderNumDoris");
        }
        if (param.getAdvertisingUnitPriceMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("orderNumDoris");
        }
        return keySet;
    }


    @Override
    public List<GroupCampaignDto> getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                                                String asin, String startDate, String endDate) {
        //不带asin 直接返回空数组
        if (StringUtils.isBlank(asin)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(" select ad_group_id  , any(campaign_id) as campaign_id  from ods_t_amazon_ad_product_report where puid = ? and count_day >= ? and count_day <= ? and asin = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        args.add(asin);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));
        }
        sql.append(" group by ad_group_id ");
        return getJdbcTemplate().query(sql.toString(), (re, i) -> GroupCampaignDto.builder()
                .adGroupId(re.getString("ad_group_id"))
                .campaignId(re.getString("campaign_id"))
                .build(), args.toArray());
    }




    @Override
    public List<String> getGroupIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                                  String asin, String startDate, String endDate) {
        //不带asin 直接返回空数组
        if (StringUtils.isBlank(asin)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(" select distinct ad_group_id from ods_t_amazon_ad_product_report where puid = ? and count_day >= ? and count_day <= ? and asin = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        args.add(asin);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, args));
        }


        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }



    @Override
    public List<AdProductReportSearchTermsViewDto> listAmazonSpAdProduct(Integer puid, SearchTermsViewParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder sql = new StringBuilder(" select shop_id, asin, ad_group_id, sku,");
        sql.append(" IFNULL(sum(cost),0) `cost`, IFNULL(sum(total_sales),0) total_sales, IFNULL(sum(ad_sales),0) ad_sales, ")
                .append(" IFNULL(sum(`impressions`),0) impressions, IFNULL(sum(sale_num),0) sale_num,  IFNULL(sum(clicks),0) clicks, ")
                .append(" IFNULL(sum(order_num),0) order_num, IFNULL(sum(ad_order_num),0) ad_order_num, IFNULL(sum(ad_sale_num),0) ad_sale_num ");
        sql.append(" from ods_t_amazon_ad_product_report  where puid= ? and count_day >= ? and  count_day <= ? ");
        argsList.add(puid);
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", param.getShopIdList(), argsList));
        sql.append(" and asin is not null and sku is not null ");
        sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", param.getAdGroupIdList(), argsList));
        sql.append(" group by shop_id, sku, asin, ad_group_id");
        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdProductReportSearchTermsViewDto.builder()
                .type("sp")
                .shopId(re.getInt("shop_id"))
                .adGroupId(re.getString("ad_group_id"))
                .asin(re.getString("asin"))
                .msku(re.getString("sku"))
                .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("ad_order_num")).orElse(0))  //销量字段订单
                .totalSales(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getLong("clicks")).orElse(0L))
                .impressions(Optional.of(re.getLong("impressions")).orElse(0L))
                /**
                 * TODO 广告报告重构
                 * 本广告产品订单量
                 */

                .orderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))

                .adOrderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                .totalSales(re.getBigDecimal("total_sales") != null ? re.getBigDecimal("total_sales") : BigDecimal.ZERO)

                .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))

                .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))

                .saleNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                .build(), argsList.toArray());
    }


}
