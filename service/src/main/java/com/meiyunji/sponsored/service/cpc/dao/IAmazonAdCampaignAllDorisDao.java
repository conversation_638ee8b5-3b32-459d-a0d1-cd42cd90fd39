package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.adTagSystem.param.TagAdCampaignListParam;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;

import java.math.BigDecimal;
import java.util.List;

public interface IAmazonAdCampaignAllDorisDao extends IDorisBaseDao<AmazonAdCampaignDorisAllReport> {

    /**
     * 查询广告活动列表分页的请求
     */
    Page<AmazonAdCampaignDorisAllReport> listAmazonAdCampaignAllPage(Integer puid, CampaignPageParam param);

    /**
     * 多店铺查询广告活动列表分页的请求,返回活动id集合
     */
    Page<AmazonAdCampaignDorisAllReport> listAmazonAdCampaignAllPageMultiple(Integer puid, CampaignPageParam param);

    /**
     * 根据活动id集合获取报告数据
     */
    List<AmazonAdCampaignDorisSumReport> listCampaignReport(CampaignPageParam param, Boolean export);

    int listAmazonAdCampaignAllCount(Integer puid, CampaignPageParam param);

    /**
     * 高级筛选统计个数
     */
    int listAmazonAdCampaignAllCountMultiple(Integer puid, CampaignPageParam param);

    List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, Integer shopId, List<String> campaignId);

    List<AmazonAdCampaignAll> listByCampaignIds(Integer puid, List<Integer> shopIdList, List<String> campaignId);
    /**
     * 获取汇总数据
     */
    AdMetricDto getSumAdMetric(Integer puid, CampaignPageParam param);
    /**
     * 获取汇总数据(多店铺)
     */
    AdMetricDto getSumAdMetricMultiple(CampaignPageParam param);

    /**
     * 获取汇总数据
     */
    AmazonAdCampaignDorisSumReport getSumReport(CampaignPageParam param, boolean selCompareDate);

    /**
     * 获取汇总数据
     */
    Integer countReport(CampaignPageParam param);

    /**
     * 获取每日预算汇总字段(多店铺)
     */
    BigDecimal getSumDailyBudget(CampaignPageParam param);

    /**
     * 获取所有活动id
     */
    List<String> getCampaginIdList(CampaignPageParam param);

    /**
     * 根据广告活动Id分组
     */
    List<AdHomePerformancedto> listTotalAmazonAdCampaignAllGroupCampaignId(Integer puid, CampaignPageParam param);

    /**
     * 根据Id查询
     */
    List<AdHomePerformancedto> listTotalAmazonAdCampaignAllGroupDateById(Integer puid, CampaignPageParam param, List<String> campaignIdList);

    /**
     * 根据天数分组统计汇总数据
     */
    List<AdHomePerformanceNewDto> listTotalGroupDateByIdMultiple(Integer puid, CampaignPageParam param);

    List<AdHomePerformancedto> getReportByCampaignIdsAndDate(Integer puid, Integer shopId, String startDate, String endDate, List<String> allCampaignId);

    List<AdHomePerformancedto> getAllDayReportByAllCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, List<String> allCampaignId);

    Page<AmazonAdPortfolioDorisAllReport> getSumReportByAllCampaignIds(Integer puid, PortfolioPageParam param, List<String> portfolioIds, boolean queryAll);

    AmazonAdCampaignDorisAllReport getSumReport(Integer puid, PortfolioPageParam param, List<String> portfolioIds);

    AmazonAdCampaignAll getMostSaleNumMarketplaceId(Integer puid, List<Integer> shopIdList, String startDate, String endDate);

    /**
     * 根据活动Id和日期查找报告数据
     */
    List<AmazonAdCampaignDorisAllReport> listByCampaignIdAndDate(Integer puid, List<Integer> shopId, List<String> ids, List<String> date, List<String> idDate);

    /**
     * 统计预算 不带币种
     */
    List<AdHomePerformancedto> totalBudgetByType(Integer puid, List<Integer> shopId, String status);

    /**
     * 统计预算 带币种 固定转换为USD
     */
    List<AdHomePerformancedto> totalBudgetByTypeAndRate(Integer puid, List<Integer> shopId, String status, String month);

    Page<AmazonAdCampaignAll> list4AdTag(TagAdCampaignListParam param);

    /**
     * 获取符合条件的活动id集合
     */
    List<String> getValidRecordByDate(Integer puid, List<Integer> shopIdList, String startDate, CampaignPageParam param);

    /**
     * 获取广告组合的报告数据
     */
    List<AmazonAdPortfolioDorisSumReport> listByPortfolio(Integer puid, List<Integer> shopId, String startDate, String endDate, List<String> portfolioIdList);

    /**
     * 统计广告组合的活动数量
     */
    List<AmazonAdPortfolioDorisAllReport> countCampaignByPortfolio(Integer puid, List<Integer> shopId, List<String> portfolioIdList);

    /**
     * 获取时间范围内超过限制美金范围内的店铺信息
     * @param puid 商户id
     * @param startDate
     * @param endDate
     * @param cost
     * @return
     */
    List<LimitCostShopInfo> listLimitCostShopInfo(Integer puid, String startDate, String endDate, BigDecimal cost);

    Page<AdCampaignOptionVo> pageCampaignsByType(Integer puid, String shopId, String type, String campaignType, String name, String campaignIds, String portfolioId, int pageSize, int pageNo);

    Page<AmazonAdCampaignAll> getMultiShopCampaignList(Integer puid, MultiShopCampaignListParam param);
}
