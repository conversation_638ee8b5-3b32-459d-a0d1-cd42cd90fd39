package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.productAd.ProductAdResult;
import com.amazon.advertising.sd.entity.productAd.*;
import com.amazon.advertising.sd.mode.ProductAd;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdProductDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdProduct;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SdSuggestedTargetVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.syncAd.enums.SdStateEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/7/7.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSdProductApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Autowired
    private IDorisService dorisService;

    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public Result create(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdProduct> amazonSdAdProducts) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        if (CollectionUtils.isEmpty(amazonSdAdProducts)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<ProductAd> productAds = amazonSdAdProducts.stream().map(e -> {
            ProductAd productAd = new ProductAd();
            if (StringUtils.isNotBlank(e.getCampaignId())) {
                productAd.setCampaignId(Long.valueOf(e.getCampaignId()));
            }
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                productAd.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            productAd.setState(e.getState());
            productAd.setSku(e.getSku());
            return productAd;
        }).collect(Collectors.toList());

        CreateProductAdsResponse response = cpcApiHelper.call(shop, () -> ProductAdClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), productAds));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "操作失败";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<ProductAdResult> resultList = response.getResultList();
            int index = 0;
            for (ProductAdResult productAdResult : resultList) {
                if ("SUCCESS".equals(productAdResult.getCode())) {
                    amazonSdAdProducts.get(index).setAdId(String.valueOf(productAdResult.getAdId()));
                } else {
                    amazonSdAdProducts.get(index).setErrMsg(AmazonErrorUtils.getError(StringUtils.isNotBlank(productAdResult.getDetails())
                            ? productAdResult.getDetails() : productAdResult.getDescription()));
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
        }

        return ResultUtil.error(errMsg);
    }

    public Result createNew(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdProduct> amazonSdAdProducts) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");

        }
        if (CollectionUtils.isEmpty(amazonSdAdProducts)) {
            return ResultUtil.returnErr("请求参数错误");

        }

        List<ProductAd> productAds = amazonSdAdProducts.stream().map(p -> {
            ProductAd productAd = new ProductAd();
            if (StringUtils.isNotBlank(p.getCampaignId())) {
                productAd.setCampaignId(Long.valueOf(p.getCampaignId()));
            }
            if (StringUtils.isNotBlank(p.getAdGroupId())) {
                productAd.setAdGroupId(Long.valueOf(p.getAdGroupId()));
            }
            productAd.setState(p.getState());
            Optional.ofNullable(p.getSku()).filter(StringUtils::isNotEmpty).ifPresent(productAd::setSku);
            Optional.ofNullable(p.getLandingPageType()).filter(StringUtils::isNotEmpty).ifPresent(productAd::setLandingPageType);
            Optional.ofNullable(p.getLandingPageURL()).filter(StringUtils::isNotEmpty).ifPresent(productAd::setLandingPageURL);
            Optional.ofNullable(p.getAdName()).filter(StringUtils::isNotEmpty).ifPresent(productAd::setAdName);
            return productAd;
        }).collect(Collectors.toList());

        String errMsg;
        int index = 0;

        for (List<ProductAd> partitionList : Lists.partition(productAds, 100)) {
            CreateProductAdsResponse response = cpcApiHelper.call(shop, () -> ProductAdClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
                    .create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(), partitionList));
            if (response == null) {
                for (ProductAd productAd : productAds) {
                    AmazonSdAdProduct productVo = amazonSdAdProducts.get(index);
                    productVo.setErrMsg("网络延迟，请稍后重试");
                    index++;
                }
                continue;
            }
            //处理返回结果中的错误信息
            if (CollectionUtils.isNotEmpty(response.getResultList())) {
                List<ProductAdResult> resultList = response.getResultList();
                for (ProductAdResult productAdResult : resultList) {
                    if ("SUCCESS".equals(productAdResult.getCode())) {
                        amazonSdAdProducts.get(index).setAdId(String.valueOf(productAdResult.getAdId()));
                    } else {
                        amazonSdAdProducts.get(index).setErrMsg(AmazonErrorUtils.getError(StringUtils.isNotBlank(productAdResult.getDetails())
                                ? productAdResult.getDetails() : productAdResult.getDescription()));
                    }
                    index++;
                }
            } else if (response.getError() != null) {
                errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
                for (ProductAd productAd : productAds) {
                    AmazonSdAdProduct productVo = amazonSdAdProducts.get(index);
                    productVo.setErrMsg(errMsg);
                    index++;
                }
            }
        }
        return ResultUtil.success();
    }

    public Result update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdProduct> amazonSdAdProducts) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        if (CollectionUtils.isEmpty(amazonSdAdProducts)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<ProductAd> productAds = amazonSdAdProducts.stream().map(e -> {
            ProductAd productAd = new ProductAd();
            if (StringUtils.isNotBlank(e.getAdId())) {
                productAd.setAdId(Long.valueOf(e.getAdId()));
            }
            productAd.setState(e.getState());
            return productAd;
        }).collect(Collectors.toList());

        UpdateProductAdsResponse response = cpcApiHelper.call(shop, () -> ProductAdClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), productAds));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告活动失败";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<ProductAdResult> resultList = response.getResultList();
            int index = 0;
            for (ProductAdResult productAdResult : resultList) {
                if ("SUCCESS".equals(productAdResult.getCode())) {
                    amazonSdAdProducts.get(index).setApiSucc(true);
                } else {
                    amazonSdAdProducts.get(index).setApiSucc(false);
                    amazonSdAdProducts.get(index).setErrMsg(AmazonErrorUtils.getError(StringUtils.isNotBlank(productAdResult.getDetails())
                            ? productAdResult.getDetails() : productAdResult.getDescription()));
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
        }

        return ResultUtil.error(errMsg);
    }

    public void syncProductAds(ShopAuth shop, String campaignId, String adGroupId, String adIds) {
        this.syncProductAds(shop, campaignId, adGroupId, adIds, false);
    }

    public void syncProductAds(ShopAuth shop, String campaignId, String adGroupId, String adIds, boolean excepThrow) {
        syncProductAds(shop, campaignId, adGroupId, adIds, null, excepThrow);
    }

    public void syncProductAds(ShopAuth shop, String campaignId, String adGroupId, String adIds, List<SdStateEnum> stateList, boolean excepThrow) {
        syncProductAds(shop, campaignId, adGroupId, adIds, stateList, excepThrow, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String groupIds){
        List<AmazonSdAdProduct> amazons = amazonSdAdProductDao.listByAdId(puid, shopId, StringUtil.stringToList(groupIds,","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
            key.setServingStatus(key.getServingStatus());
            return  AmazonServingStatusDto.build(key.getAdId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());}).collect(Collectors.toList());
    }

    /**
     * 同步所有的产品广告
     *
     * @param shop：
     */
    public void syncProductAds(ShopAuth shop, String campaignId, String adGroupId, String adIds, List<SdStateEnum> stateList, boolean excepThrow, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdGroup--配置信息为空");
            return;
        }
        ProductAdClient client = ProductAdClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = ProductAdClient.getInstance(true);
        }
        int startIndex = 0;
        int count = 500;
        ListProductAdsExResponse response;

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(SdStateEnum.values()).map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        }

        while (true) {
            int finalSartIndex = startIndex;
            ProductAdClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, stateFilter, adIds, adGroupId, campaignId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD product ad rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                ProductAdClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, stateFilter, adIds, adGroupId, campaignId));
                retry++;
            }
            if (AmazonResponseUtil.isError(response) && excepThrow) {
                throw new ServiceException("sd ad api call error, please try again");
            }
            if (response == null || CollectionUtils.isEmpty(response.getResultList())) {
                break;
            }

            int size = response.getResultList().size();
            AmazonSdAdProduct amazonSdAdProduct;
            List<AmazonSdAdProduct> amazonSdAdProducts = new ArrayList<>(size);
            for (ProductAd productAd : response.getResultList()) {
                amazonSdAdProduct = turnEntityToPO(productAd);
                if (StringUtils.isNotBlank(amazonSdAdProduct.getAdId())) {
                    amazonSdAdProduct.setPuid(shop.getPuid());
                    amazonSdAdProduct.setShopId(shop.getId());
                    amazonSdAdProduct.setMarketplaceId(shop.getMarketplaceId());
                    amazonSdAdProduct.setProfileId(amazonAdProfile.getProfileId());
                    amazonSdAdProducts.add(amazonSdAdProduct);
                }
            }

            if (amazonSdAdProducts.size() > 0) {
                Map<String, AmazonSdAdProduct> groupMap = amazonSdAdProductDao.listByAdId(shop.getPuid(), shop.getId(),
                        amazonSdAdProducts.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList()))
                        .stream().filter(e -> StringUtils.isNotBlank(e.getAdId()))
                        .collect(Collectors.toMap(AmazonSdAdProduct::getAdId, Function.identity()));

                List<AmazonSdAdProduct> insertList = new ArrayList<>();
                List<AmazonSdAdProduct> updateList = new ArrayList<>();
                AmazonSdAdProduct old;

                for (AmazonSdAdProduct c : amazonSdAdProducts) {
                    if (groupMap.containsKey(c.getAdId())) {
                        old = groupMap.get(c.getAdId());
                        if (StringUtils.isNotBlank(c.getState())) {
                            old.setState(c.getState());
                        }
                        if (StringUtils.isNotBlank(c.getSku())) {
                            old.setSku(c.getSku());
                        }
                        if (StringUtils.isNotBlank(c.getAsin())) {
                            old.setAsin(c.getAsin());
                        }
                        if (StringUtils.isNotBlank(c.getServingStatus())) {
                            old.setServingStatus(c.getServingStatus());
                        }
                        if (c.getCreationDate() != null) {
                            old.setCreationDate(c.getCreationDate());
                        }
                        if (c.getLastUpdatedDate() != null) {
                            old.setLastUpdatedDate(c.getLastUpdatedDate());
                        }
                        if (StringUtils.isNotBlank(c.getLandingPageType())) {
                            old.setLandingPageType(c.getLandingPageType());
                        }
                        if (StringUtils.isNotBlank(c.getLandingPageURL())) {
                            old.setLandingPageURL(c.getLandingPageURL());
                        }
                        if (StringUtils.isNotBlank(c.getAdName())) {
                            old.setAdName(c.getAdName());
                        }
                        updateList.add(old);
                    } else {
                        c.setCreateInAmzup(0);
                        insertList.add(c);
                    }
                }

                try {
                    amazonSdAdProductDao.batchAdd(shop.getPuid(), insertList);
                    amazonSdAdProductDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSdGroup:", e);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
    }

    /**
     * 归档
     *
     * @param amazonSdAdProduct：
     * @return ：Result
     */
    public Result archive(AmazonSdAdProduct amazonSdAdProduct) {
        if (amazonSdAdProduct == null) {
            return ResultUtil.error("没有广告产品信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonSdAdProduct.getShopId(), amazonSdAdProduct.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        ArchiveProductAdResponse response = cpcApiHelper.call(shop, () -> ProductAdClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop),
                amazonSdAdProduct.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonSdAdProduct.getAdId())));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getAdId() != null) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                msg = response.getResult().getDetails();
            } else {
                msg = response.getResult().getDescription();
            }
            msg = AmazonErrorUtils.getError(msg);
        }
        if (StringUtils.isBlank(msg)) {
            msg = "网络延迟，请稍后重试";
        }
        return ResultUtil.error(msg);
    }

    // 把接口返回的dto转换成po
    private AmazonSdAdProduct turnEntityToPO(ProductAd productAd) {
        AmazonSdAdProduct amazonSdAdProduct = new AmazonSdAdProduct();
        if (productAd.getCampaignId() != null) {
            amazonSdAdProduct.setCampaignId(productAd.getCampaignId().toString());
        }
        if (productAd.getAdGroupId() != null) {
            amazonSdAdProduct.setAdGroupId(productAd.getAdGroupId().toString());
        }
        if (productAd.getAdId() != null) {
            amazonSdAdProduct.setAdId(productAd.getAdId().toString());
        }

        amazonSdAdProduct.setState(productAd.getState());
        amazonSdAdProduct.setSku(productAd.getSku());
        amazonSdAdProduct.setAsin(productAd.getAsin());
        amazonSdAdProduct.setServingStatus(productAd.getServingStatus());

        if (StringUtils.isNotBlank(productAd.getCreationDate())) {
            amazonSdAdProduct.setCreationDate(DateUtil.getDateByMillisecond(Long.valueOf(productAd.getCreationDate())));
        }
        if (StringUtils.isNotBlank(productAd.getLastUpdatedDate())) {
            amazonSdAdProduct.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(productAd.getLastUpdatedDate())));
        }

        Optional.ofNullable(productAd.getLandingPageType()).filter(StringUtils::isNotEmpty).ifPresent(amazonSdAdProduct::setLandingPageType);
        Optional.ofNullable(productAd.getLandingPageURL()).filter(StringUtils::isNotEmpty).ifPresent(amazonSdAdProduct::setLandingPageURL);
        Optional.ofNullable(productAd.getAdName()).filter(StringUtils::isNotEmpty).ifPresent(amazonSdAdProduct::setAdName);
        return amazonSdAdProduct;
    }

    public Result<BatchResponseVo<AmazonSdAdProduct,AmazonSdAdProduct>> updateReturnErrorList(ShopAuth shop,AmazonAdProfile amazonAdProfile,List<AmazonSdAdProduct> amazonSdAdProducts) {
        if (CollectionUtils.isEmpty(amazonSdAdProducts)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<ProductAd> productAds = makeAds(amazonSdAdProducts);
        UpdateProductAdsResponse response = cpcApiHelper.call(shop, () -> ProductAdClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), productAds));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        //处理返回结果中的错误信息
        Map<String, AmazonSdAdProduct> amazonAdProductMap = amazonSdAdProducts.stream().collect(Collectors.toMap(AmazonSdAdProduct::getAdId, e -> e));
        BatchResponseVo<AmazonSdAdProduct, AmazonSdAdProduct> batchResponseVo = new BatchResponseVo<>();
        List<AmazonSdAdProduct> errorList = Lists.newArrayList();
        List<AmazonSdAdProduct> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getResultList() != null && response.getResultList().size() > 0) {

            List<ProductAdResult> resultList = response.getResultList();
            List<Long> successId = Lists.newArrayList();
            for (ProductAdResult productAdResult : resultList) {

                if ("SUCCESS".equals(productAdResult.getCode())) {
                    AmazonSdAdProduct amazonAdProductSuccess = amazonAdProductMap.remove(String.valueOf(productAdResult.getAdId()));
                    if (amazonAdProductSuccess != null) {
                        successList.add(amazonAdProductSuccess);
                    }
                    successId.add(amazonAdProductSuccess.getId());

                } else {
                    AmazonSdAdProduct amazonAdProductFail = amazonAdProductMap.remove(String.valueOf(productAdResult.getAdId()));
                    if (amazonAdProductFail != null) {
                        AmazonSdAdProduct amazonAdProductError = new AmazonSdAdProduct();
                        amazonAdProductError.setId(amazonAdProductFail.getId());
                        amazonAdProductError.setAsin(amazonAdProductFail.getAsin());
                        amazonAdProductError.setSku(amazonAdProductFail.getSku());
                        //更新失败数据处理
                        if (StringUtils.isNotBlank(productAdResult.getDescription())) {
                            amazonAdProductError.setFailReason(AmazonErrorUtils.getError(productAdResult.getDescription()));
                        } else {
                            amazonAdProductError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(amazonAdProductError);
                    }

                }
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdProductMap)) {
                amazonAdProductMap.forEach((k, v) -> {
                    AmazonSdAdProduct amazonAdProductError = new AmazonSdAdProduct();
                    amazonAdProductError.setId(v.getId());
                    amazonAdProductError.setSku(v.getSku());
                    amazonAdProductError.setAsin(v.getAsin());
                    amazonAdProductError.setFailReason("更新失败，请稍后重试");
                    errorList.add(amazonAdProductError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdProductMap)) {
                amazonAdProductMap.forEach((k, v) -> {
                    AmazonSdAdProduct amazonAdProductError = new AmazonSdAdProduct();
                    amazonAdProductError.setId(v.getId());
                    amazonAdProductError.setSku(v.getSku());
                    amazonAdProductError.setAsin(v.getAsin());
                    amazonAdProductError.setFailReason("更新失败，请稍后重试");
                    errorList.add(amazonAdProductError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonSdAdProducts.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    private List<ProductAd> makeAds(List<AmazonSdAdProduct> amazonSdAdProducts) {
        List<ProductAd> adProducts = Lists.newArrayList();
        for (AmazonSdAdProduct amazonAdProduct : amazonSdAdProducts) {
            ProductAd productAd = new ProductAd();
            productAd.setState(amazonAdProduct.getState());
            productAd.setAdId(Long.valueOf(amazonAdProduct.getAdId()));
            adProducts.add(productAd);
        }
        return adProducts;
    }
}
