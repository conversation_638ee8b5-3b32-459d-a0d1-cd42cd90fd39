package com.meiyunji.sponsored.service.export.handler;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.vo.CampaignNeTargetingSpRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeTargetingSpParam;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.vo.CampaignNeAsinExportVo;
import com.meiyunji.sponsored.service.vo.NegativeKeywordVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * @author: heqiwen
 * @email: <EMAIL>
 * @date: 2024-08-08  14:09
 */
@Service(AdManagePageExportTaskConstant.CAMPAIGN_NE_TARGET)
@Slf4j
public class CampaignNeTargetingSpExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private DynamicRefreshConfiguration configuration;

    @Override
    public void export(AdManagePageExportTask task) {
        CampaignNeTargetingSpParam param = JSONUtil.jsonToObject(task.getParam(), CampaignNeTargetingSpParam.class);
        if (param == null) {
            log.error(String.format("campaignNeTarget export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        //站点币种
        String currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();
        Page<CampaignNeTargetingSpRpcVo> voPage;
        if (configuration.getContainsReport()) {
            voPage = cpcSpCampaignService.neTargetingPageListV2(param);
        } else {
            voPage = cpcSpCampaignService.neTargetingPageList(param);
        }
        List<CampaignNeTargetingSpRpcVo> voList = voPage.getRows();

        if (CollectionUtils.isEmpty(voList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(NegativeKeywordVo.class);
        String fileName = shop.getName() + "_否定商品";
        List<String> urlList = new ArrayList<>();
        List<String> excludeFileds = Collections.emptyList();
        int count = 0;
        //集合分片
        List<List<CampaignNeTargetingSpRpcVo>> partition = Lists.partition(voList, Constants.EXPORT_MAX_SIZE);
        for (List<CampaignNeTargetingSpRpcVo> partionList : partition) {
            List<CampaignNeAsinExportVo> campaignNeAsinExportVoList = new LinkedList<>();
            for (CampaignNeTargetingSpRpcVo neKeyVo : partionList) {
                campaignNeAsinExportVoList.add(buildExportVo(neKeyVo, currency));
            }
            if (campaignNeAsinExportVoList.size() > 0) {
                urlList.add(excelService.easyExcelHandlerExport(task.getPuid(),
                        campaignNeAsinExportVoList, fileName + "(" + (count++) + ")", CampaignNeAsinExportVo.class, build, excludeFileds));
            }
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private CampaignNeAsinExportVo buildExportVo(CampaignNeTargetingSpRpcVo ckVo, String currency) {
        CampaignNeAsinExportVo nkVo = new CampaignNeAsinExportVo();
        nkVo.setAsin(ckVo.getAsin());
        nkVo.setCreateTime(ckVo.getCreateTime());
        //状态
        nkVo.setState(StateEnum.getStateValue(ckVo.getState()));
        //所属广告活动
        nkVo.setAdvertisingActivities(ckVo.getCampaignName());
        //推广类型
        nkVo.setType(CampaignTypeEnum.getCampaignValue(ckVo.getType()));
        //投放类型
        nkVo.setCampaignTargetingType(TargetingEnum.getTargetingValue(ckVo.getCampaignTargetingType()));
        nkVo.setPortfolioName(ckVo.getPortfolioName());

        if (ckVo.hasCreateName()) {
            nkVo.setCreateName(ckVo.getCreateName());
        }
        if (ckVo.hasAcos()) {
            nkVo.setAcos(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAcos())) + "%");
        }
        if (ckVo.hasAdOrderNum()) {
            nkVo.setAdOrderNum(ckVo.getAdOrderNum());
        }
        if (ckVo.hasClicks()) {
            nkVo.setClicks(ckVo.getClicks());
        }
        if (ckVo.hasImpressions()) {
            nkVo.setImpressions(ckVo.getImpressions());
        }
        if (ckVo.hasAdCost()) {
            nkVo.setAdCost(currency + CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAdCost())));
        }
        if (ckVo.hasAdSale()) {
            nkVo.setAdSale(currency + CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAdSale())));
        }
        if (ckVo.hasCtr()) {
            nkVo.setCtr(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getCtr())) + "%");
        }
        if (ckVo.hasCvr()) {
            nkVo.setCvr(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getCvr())) + "%");
        }
        if (ckVo.hasRoas()) {
            nkVo.setRoas(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getRoas())));
        }
        if (ckVo.hasAdCostPerClick()) {
            nkVo.setCpc(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getAdCostPerClick())));
        }
        if (ckVo.hasCpa()) {
            nkVo.setCpa(CalculateUtil.formatDecimal(BigDecimal.valueOf(ckVo.getCpa())));
        }

        return nkVo;
    }

    private static CampaignNeTargetingSpRpcVo buildGrpcVo(CampaignNeTargetingSpRpcVo campaignNeTargetingSpRpcVo) {
        CampaignNeTargetingSpRpcVo.Builder vo = CampaignNeTargetingSpRpcVo.newBuilder();
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getAsin())) {
            vo.setAsin(campaignNeTargetingSpRpcVo.getAsin());
        }
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getState())) {
            vo.setState(campaignNeTargetingSpRpcVo.getState());
        }
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getCampaignName())) {
            vo.setCampaignName(campaignNeTargetingSpRpcVo.getCampaignName());
        }
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getType())) {
            vo.setType(campaignNeTargetingSpRpcVo.getType());
        }
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getCampaignTargetingType())) {
            vo.setCampaignTargetingType(campaignNeTargetingSpRpcVo.getCampaignTargetingType());
        }
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getCreateTime())) {
            vo.setCreateTime(campaignNeTargetingSpRpcVo.getCreateTime());
        }
        if (StringUtils.isNotBlank(campaignNeTargetingSpRpcVo.getPortfolioName())) {
            vo.setPortfolioName(campaignNeTargetingSpRpcVo.getPortfolioName());
        }
        return vo.build();
    }
}
