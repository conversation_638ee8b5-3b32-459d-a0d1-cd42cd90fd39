package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAdPlacementAmazonBusinessReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdPlacementAmazonBusinessReportDao;
import com.meiyunji.sponsored.service.cpc.po.AdPlacementAmazonBusinessReport;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductCampaigns;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpAdPlacementAmazonBusinessReportV3Strategy extends AbstractReportProcessStrategy {
    private final IAdPlacementAmazonBusinessReportDao adPlacementAmazonBusinessReportDao;
    private final PartitionSqlUtil partitionSqlUtil;

    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;

    public SpAdPlacementAmazonBusinessReportV3Strategy(CosBucketClient dataBucketClient, IAdPlacementAmazonBusinessReportDao adPlacementAmazonBusinessReportDao, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.adPlacementAmazonBusinessReportDao = adPlacementAmazonBusinessReportDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 &&
                notification.getV3Type() == AmazonReportV3Type.sp_campaigns_placement_amazon_business;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
        String shopType = ShopTypeEnum.SC.getCode();
        if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
            shopType = ShopTypeEnum.VC.getCode();
        }
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath())))); JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductCampaigns> reports = Lists.newArrayListWithExpectedSize(batchSize);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductCampaigns report = new SponsoredProductCampaigns();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                reports.add(report);
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= batchSize) {
                    dealReports(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports, shopType);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        //入库新表
        List<AdPlacementAmazonBusinessReport> poAllList = getPoByAllReportCampaign(notification, reports, shopType);
        //分批入库
        List<List<AdPlacementAmazonBusinessReport>> partitionAll = Lists.partition(poAllList, batchSize);
        for (List<AdPlacementAmazonBusinessReport> campaignReports : partitionAll) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), campaignReports, 0, adPlacementAmazonBusinessReportDao::insertOrUpdateList);
        }
    }


    private List<AdPlacementAmazonBusinessReport> getPoByAllReportCampaign(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        List<AdPlacementAmazonBusinessReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AdPlacementAmazonBusinessReport amazonAdCampaignReport;
        for (SponsoredProductCampaigns report : reports) {
            amazonAdCampaignReport = new AdPlacementAmazonBusinessReport();
            amazonAdCampaignReport.setPuid(notification.getSellerIdentifier());
            amazonAdCampaignReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdCampaignReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdCampaignReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            amazonAdCampaignReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
            amazonAdCampaignReport.setCampaignType(report.getPlacementClassification());
            amazonAdCampaignReport.setPlacement(report.getPlacementClassification());
            amazonAdCampaignReport.setCampaignName(report.getCampaignName());

            //由于vc店铺都是取14天归因数，为了不改变查询代码，所以这里将入库字段进行对调
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                amazonAdCampaignReport.setConversions7d(report.getPurchases14d());
                amazonAdCampaignReport.setConversions14d(report.getPurchases7d());
                amazonAdCampaignReport.setConversions7dSameSKU(report.getPurchasesSameSku14d());
                amazonAdCampaignReport.setConversions14dSameSKU(report.getPurchasesSameSku7d());
                amazonAdCampaignReport.setSales7d(report.getSales14d() == null ? null : report.getSales14d());
                amazonAdCampaignReport.setSales14d(report.getSales7d() == null ? null : report.getSales7d());
                amazonAdCampaignReport.setSales7dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
                amazonAdCampaignReport.setSales14dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
                amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks14d());
                amazonAdCampaignReport.setUnitsOrdered14d(report.getUnitsSoldClicks7d());
                amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku14d());
                amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku7d());
            } else {
                amazonAdCampaignReport.setConversions7d(report.getPurchases7d());
                amazonAdCampaignReport.setConversions14d(report.getPurchases14d());
                amazonAdCampaignReport.setConversions7dSameSKU(report.getPurchasesSameSku7d());
                amazonAdCampaignReport.setConversions14dSameSKU(report.getPurchasesSameSku14d());
                amazonAdCampaignReport.setSales7d(report.getSales7d() == null ? null : report.getSales7d());
                amazonAdCampaignReport.setSales14d(report.getSales14d() == null ? null : report.getSales14d());
                amazonAdCampaignReport.setSales7dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
                amazonAdCampaignReport.setSales14dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
                amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks7d());
                amazonAdCampaignReport.setUnitsOrdered14d(report.getUnitsSoldClicks14d());
                amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku7d());
                amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku14d());
            }


            amazonAdCampaignReport.setConversions1d(report.getPurchases1d());
            amazonAdCampaignReport.setConversions1dSameSKU(report.getPurchasesSameSku1d());
            amazonAdCampaignReport.setConversions30d(report.getPurchases30d());
            amazonAdCampaignReport.setConversions30dSameSKU(report.getPurchasesSameSku30d());
            amazonAdCampaignReport.setSales1d(report.getSales1d() == null ? null : report.getSales1d());
            amazonAdCampaignReport.setSales1dSameSKU(report.getAttributedSalesSameSku1d() == null ? null : report.getAttributedSalesSameSku1d());
            amazonAdCampaignReport.setSales30d(report.getSales30d() == null ? null : report.getSales30d());
            amazonAdCampaignReport.setSales30dSameSKU(report.getAttributedSalesSameSku30d() == null ? null : report.getAttributedSalesSameSku30d());
            amazonAdCampaignReport.setUnitsOrdered1d(report.getUnitsSoldClicks1d());
            amazonAdCampaignReport.setUnitsOrdered1dSameSKU(report.getUnitsSoldSameSku1d());
            amazonAdCampaignReport.setUnitsOrdered30d(report.getUnitsSoldClicks30d());
            amazonAdCampaignReport.setUnitsOrdered30dSameSKU(report.getUnitsSoldSameSku30d());
            amazonAdCampaignReport.setClicks(report.getClicks());
            amazonAdCampaignReport.setCost(report.getCost());
            amazonAdCampaignReport.setImpressions(report.getImpressions());

            list.add(amazonAdCampaignReport);
        }
        return list;
    }
}
