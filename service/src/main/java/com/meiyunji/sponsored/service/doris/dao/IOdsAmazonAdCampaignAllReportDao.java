package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adTagSystem.dto.CampaignDto;
import com.meiyunji.sponsored.service.adTagSystem.param.CampaignTagDataParam;
import com.meiyunji.sponsored.service.adTagSystem.dto.CampaignTagDataReportDto;
import com.meiyunji.sponsored.service.cpc.dto.AdBaseReportDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.doris.bo.AmazonAdCampaignAllReportBo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.newDashboard.bo.ShopEffectDataBo;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdPlacementDataDto;
import com.meiyunji.sponsored.service.newDashboard.bo.EffectDataBo;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.ShopRankDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import org.joda.time.DateTime;

import java.util.List;

/**
 * 广告所有活动报告(OdsAmazonAdCampaignAllReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:17
 */
public interface IOdsAmazonAdCampaignAllReportDao extends IDorisBaseDao<OdsAmazonAdCampaignAllReport> {

    /**
     * 根据日期查询广告活动/广告组合图表数据
     * @param puid
     * @param shopIdList
     * @param marketplaceIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @return
     */
    List<CampaignOrGroupOrPortfolioDto> queryAdCampaignCharts(Integer puid, List<Integer> shopIdList,
                                                              List<String> marketplaceIdList, List<String> campaignIdList,
                                                              String currency, String startDate,
                                                              String endDate);

    /**
     * 查询店铺、站点排行榜数据
     */
    List<ShopRankDto> queryAdRankData(Integer puid, List<Integer> shopIdList,
                                      List<String> marketplaceIdList, List<String> idList,
                                      String currency, String startDate,
                                      String endDate, Integer queryField);

    /**
     * 根据当前时间段查询语句和同比日期查询语句组装查询Top图表查询数据
     * @param subSqlA
     * @param subSqlB
     * @param orderField
     * @param limit
     * @return
     */
    List<DashboardAdTopDataDto> queryAdCampaignYoyOrMomTop(String subSqlA, String subSqlB,
                                                      List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                      DashboardOrderByRateEnum orderField, String orderBy,
                                                      Integer limit, Boolean noZero);

    /**
     * 根据当前时间段查询语句和同比日期查询语句组装查询Top图表查询数据
     */
    List<DashboardAdTopDataDto> queryAdRankYoyOrMomTop(String subSqlA, String subSqlB,
                                                           List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                           DashboardOrderByRateEnum orderField, String orderBy,
                                                           Integer queryField, Integer limit, boolean isSelAllSub, Boolean noZero);

    /**
     * 广告看板获取广告效果、转化页面
     * @return
     */
    List<EffectDataBo> getEffectData(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency,
                                     String startDate, String endDate, String modelType, List<String> siteToday, Boolean isSiteToday,
                                     List<String> portfolioIds, List<String> campaignIds);

    /**
     * 广告看板获取广告效果、转化页面汇总
     * @return
     */
    EffectDataBo getEffectAggregateData(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency,
                                        String startDate, String endDate, String modelType, List<String> siteToday, Boolean isSiteToday,
                                        List<String> portfolioIds, List<String> campaignIds);

    String campaignQuerySql(Integer puid, List<Integer> shopIdList,
                                       List<String> marketplaceIdList, List<String> campaignIdList,
                                       String currency, String startDate,
                                       String endDate, List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);

    /**
     * 预算不足页面获取某指标前n个广告活动id
     */
    List<AmazonAdCampaignAllReportBo> campaignIdListByInsufficientBudget(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String startDate,
                                           String endDate, String currency, String orderBy, Integer limit, List<String> siteToday, Boolean isSiteToday,
                                                                         List<String> portfolioIds, List<String> campaignIdList, Boolean noZero);

    /**
     * 根据广告活动id查询基础数据和报告数据
     */
    List<AmazonAdCampaignAllReportBo> campaignAllReportBoByIds(Integer puid, List<Integer> shopIdList, List<String> campaignIdList,
                                                                     String startDate, String endDate);

    String rankQuerySql(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, List<String> idList,
                        String currency,String startDate, String endDate, List<Object> argsList, Integer queryField, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);


    List<AmazonAdCampaignAllReportBo> getAdOrderNumGroupByCountDayList(Integer puid, List<Integer> shopIdList,
                                                                       List<String> marketplaceIdList, List<String> campaignIdList,
                                                                       String startDate, String endDate, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds);

    /**
     * 广告活动标签列表页
     * @param puid
     * @param param
     * @return
     */
    List<CampaignTagDataReportDto> getCampaignTagData(Integer puid, CampaignTagDataParam param);

    /**
     * 获取广告活动标签对比数据
     */
    List<CampaignTagDataReportDto> getCampaignTagDataByTagIdList(Integer puid, CampaignTagDataParam buildCompareAggregateParam, List<Long> tagIdList);

    /**
     * 根据标签id查询对应活动id图表按天维度数据
     * @param puid
     * @param param
     * @param tagIdList
     * @param distinctCount 重复数量
     * @return
     */
    List<AdBaseReportDto> getCampaignTagChartData(Integer puid, CampaignTagDataParam param, List<Long> tagIdList, int distinctCount) ;

    /**
     * 根据店铺id和时间查询出有数据的店铺id
     */
    List<Integer> selectValidShopIds(Integer puid, List<Integer> shopIds, DateTime startDateTime, DateTime endTime);

    /**
     * 根据活动id集合时间获取活动报告时间
     */
    List<AmazonAdCampaignAllReport> getReportByCampaignIdListAll(Integer puid, List<Integer> shopIdList, String startDate,
                                                                 String endDate, List<String> campaignIdList, Boolean changeRate);

    List<ShopEffectDataBo> getEffectDataGroupByShop(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency, String startDate, String endDate,
                                                    String modelType, List<String> siteToday, Boolean isSiteToday,
                                                    List<String> portfolioIds, List<String> campaignIds);

    List<CampaignDto> getShopIdsAndCampaignIds(Integer puid, List<String> shopIdList, List<String> tagIdList);
}

