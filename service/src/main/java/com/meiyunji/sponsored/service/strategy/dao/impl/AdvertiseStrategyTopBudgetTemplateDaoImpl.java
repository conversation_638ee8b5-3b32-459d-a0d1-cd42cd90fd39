package com.meiyunji.sponsored.service.strategy.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetTemplateDao;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.strategy.vo.PageTopBudgetParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class AdvertiseStrategyTopBudgetTemplateDaoImpl extends BaseShardingDaoImpl<AdvertiseStrategyTopBudgetTemplate> implements AdvertiseStrategyTopBudgetTemplateDao {

    @Override
    public AdvertiseStrategyTopBudgetTemplate selectByPrimaryKey(int puid, Long id) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("id",id).build();
        return getByCondition(puid,conditionBuilder);
    }

    @Override
    public List<Integer> selectByShopIdList(int puid,String marketplaceId) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid",puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            conditionBuilder.equalTo("marketplace_id",marketplaceId);
        }
        return listDistinctFieldByCondition(puid,"shop_id", conditionBuilder.build(), Integer.class);
    }

    @Override
    public AdvertiseStrategyTopBudgetTemplate selectByTaskId(int puid, int shopId, Long taskId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("task_id",taskId).build();
        return getByCondition(puid,conditionBuilder);
    }

    @Override
    public Integer insertTemplate(Integer puid, AdvertiseStrategyTopBudgetTemplate template) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_advertise_strategy_top_budget_template`(`id`,`puid`,`shop_id`,`profile_id`,`marketplace_id`,`task_id`,");
        sql.append("`status`,`origin_value`,`reduction_value`,`type`,`rule`,`create_uid`,`update_uid`,`create_name`,");
        sql.append("`update_name`,`create_at`,`last_update_at`) values");
        List<Object> argsList = Lists.newArrayList();
        sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
        argsList.add(template.getId());
        argsList.add(puid);
        argsList.add(template.getShopId());
        argsList.add(template.getProfileId());
        argsList.add(template.getMarketplaceId());
        argsList.add(template.getTaskId());
        argsList.add(template.getStatus());
        argsList.add(template.getOriginValue());
        argsList.add(template.getReductionValue());
        argsList.add(template.getType());
        argsList.add(template.getRule());
        argsList.add(template.getCreateUid());
        argsList.add(template.getUpdateUid());
        argsList.add(template.getCreateName());
        argsList.add(template.getUpdateName());
        sql.deleteCharAt(sql.length()-1);
        return getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public Integer updateTemplate(Integer puid, AdvertiseStrategyTopBudgetTemplate template) {

        String sql = "update t_advertise_strategy_top_budget_template set `origin_value` = ?,`type` = ?,rule = ?,update_uid = ?,update_name=?, " +
                " last_update_at = now() where puid = ? and id = ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(template.getOriginValue());
        argsList.add(template.getType());
        argsList.add(template.getRule());
        argsList.add(template.getUpdateUid());
        argsList.add(template.getUpdateName());
        argsList.add(puid);
        argsList.add(template.getId());
        return getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public Integer updateStatus(int puid, Long templateId, String status) {
        String sql = "update t_advertise_strategy_top_budget_template set status = ? , last_update_at = now() where puid = ? and id = ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(status);
        argsList.add(puid);
        argsList.add(templateId);
        return getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public Integer updateReductionValue(int puid, Long templateId, String reductionValue) {
        String sql = "update t_advertise_strategy_top_budget_template set reduction_value = ? , last_update_at = now() where puid = ? and id = ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(reductionValue);
        argsList.add(puid);
        argsList.add(templateId);
        return getJdbcTemplate(puid).update(sql,argsList.toArray());
    }

    @Override
    public boolean existByName(Integer puid, String name,Integer shopId) {
        String sql = "select count(*) from t_advertise_strategy_top_budget_template where puid = ? and shop_id = ? and `template_name`=?";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId,name);
    }

    @Override
    public boolean existByStatus(Integer puid,Integer shopId) {
        String sql = "select count(*) from t_advertise_strategy_top_budget_template where puid = ? and shop_id = ? and `status`='ENABLED'";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId);
    }

    @Override
    public void deleteTemplateId(Integer puid, Long id) {
        String sql = "delete from t_advertise_strategy_top_budget_template  where puid = ? and id = ? ";
        getJdbcTemplate(puid).update(sql,puid, id);
    }

    @Override
    public AdvertiseStrategyTopBudgetTemplate selectByStatus(Integer puid, Integer shopId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .equalTo("status","ENABLED").build();
        return getByCondition(puid,conditionBuilder);
    }

    @Override
    public AdvertiseStrategyTopBudgetTemplate selectById(Integer puid, Integer shopId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId).build();
        return getByCondition(puid,conditionBuilder);
    }

    @Override
    public List<AdvertiseStrategyTopBudgetTemplate> getList(Integer puid, Integer shopId,String templateName) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid",puid);
        if (StringUtils.isNotBlank(templateName)) {
            conditionBuilder.like("template_name",templateName);
        }
        return listByCondition(puid,conditionBuilder.build());
    }

    @Override
    public Page<AdvertiseStrategyTopBudgetTemplate> getPageList(PageTopBudgetParam param) {
        StringBuilder sql = new StringBuilder("select t.id,t.puid,t.shop_id,t.marketplace_id,t.task_id,t.profile_id,");
        sql.append(" t.type,t.rule,t.origin_value,t.reduction_value,t.status,t.create_name,t.update_name,");
        sql.append(" t.create_at,t.last_update_at,t.create_uid,t.update_uid from t_advertise_strategy_top_budget_template t where t.puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        StringBuilder whereSql = new StringBuilder();
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            whereSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIds(), args));
        }
        if (Objects.nonNull(param.getUpdateTimeStart())) {
            whereSql.append(" and t.last_update_at >= ?");
            args.add(param.getUpdateTimeStart());
        }
        if (Objects.nonNull(param.getUpdateTimeEnd())) {
            whereSql.append(" and t.last_update_at <= ?");
            args.add(param.getUpdateTimeEnd());
        }
        if (CollectionUtils.isNotEmpty(param.getUpdateUidList())) {
            whereSql.append(SqlStringUtil.dealInList("t.update_uid", param.getUpdateUidList(), args));
        }
        if (CollectionUtils.isNotEmpty(param.getCreateUidList())) {
            whereSql.append(SqlStringUtil.dealInList("t.create_uid", param.getCreateUidList(), args));
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            whereSql.append(" and t.status = ?");
            args.add(param.getStatus());
        }
        StringBuilder countSql = new StringBuilder("select count(t.id) from t_advertise_strategy_top_budget_template t where t.puid = ?");
        sql.append(whereSql);
        countSql.append(whereSql);
        return this.getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(),
                args.toArray(), sql.toString(), args.toArray(), AdvertiseStrategyTopBudgetTemplate.class);
    }

    @Override
    public Page<AdvertiseStrategyTopBudgetTemplate> getAdmPageList(PageTopBudgetParam param) {
        StringBuilder sql = new StringBuilder("select t.id,t.puid,t.shop_id,t.marketplace_id,t.task_id,t.profile_id,");
        sql.append(" t.type,t.rule,t.origin_value,t.reduction_value,t.status,t.create_name,t.update_name,");
        sql.append(" t.create_at,t.last_update_at,t.create_uid,t.update_uid from t_advertise_strategy_top_budget_template t where t.puid = ?");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        StringBuilder whereSql = new StringBuilder();
        if (param.getId() != null) {
            whereSql.append(" and t.id != ?");
            args.add(param.getId());
        }
        StringBuilder countSql = new StringBuilder("select count(t.id) from t_advertise_strategy_top_budget_template t where t.puid = ? and t.shop_id = ?");
        sql.append(whereSql);
        countSql.append(whereSql);
        return this.getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(),
                args.toArray(), sql.toString(), args.toArray(), AdvertiseStrategyTopBudgetTemplate.class);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AdvertiseStrategyTopBudgetTemplate> getEnabledTemplate(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (Objects.nonNull(shopId)) {
            builder.equalTo("shop_id", shopId);
        }
        builder.equalTo("status", AutoRuleEnableStatusEnum.ENABLED.getCode());
        return listByCondition(puid, builder.build());
    }
}
