package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import com.meiyunji.sponsored.service.cpc.po.AdTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-09-12  16:10
 */
@Data
public class AudienceTargetViewVo extends StreamDataViewVo {
    //广告活动类型
    private String type;

    //id
    private Long id;

    //店铺id
    private Integer shopId;

    //站点id
    private String marketplaceId;

    //状态
    private String state;

    //投放类型
    private String targetType;

    //仅商品投放使用
    //商品名称
    private String title;

    //品牌名：brandName
    private String brandName;

    //商品价格范围:commodityPriceRange
    private String commodityPriceRange;

    //星级:rating
    private String rating;

    //配送:distribution
    private String distribution;

    //回溯期:lookback
    private String lookback;

    //投放id
    private String targetId;

    //投放文本
    private String targetText;

    //标签
    private List<AdTag> adTags;

    //是否开启分时调价
    private Integer isPricing;

    //分时调价任务状态
    private Integer pricingState;

    //二级状态
    private String servingStatus;

    //二级状态描述
    private String servingStatusDec;

    //二级状态名称
    private String servingStatusName;

    //广告组ID
    private String adGroupId;

    //广告组名称
    private String adGroupName;

    //广告组类型
    private String adGroupType;

    //广告活动ID
    private String campaignId;

    //广告活动名称
    private String campaignName;

    //广告活动投放类型
    private String campaignTargetingType;

    //每日预算
    private String dailyBudget;

    //广告组合ID
    private String portfolioId;

    //广告组合名称
    private String portfolioName;

    //广告组合隐藏状态
    private Integer isHidden;

    //竞价
    private String bid;

    //竞价日志
    private DataLogVo bidLog;

    //建议竞价
    private String suggestBid;

    //建议竞价范围开始
    private String rangeStart;

    //建议竞价范围结束
    private String rangeEnd;

    //IS字段
    private String topImpressionShare;

    //竞价日志更新标识
    private Boolean isUpdateBid;
    private Integer isStateBidding;
    private Integer pricingStateBidding;

    // 费用类型
    private String costType;
}
