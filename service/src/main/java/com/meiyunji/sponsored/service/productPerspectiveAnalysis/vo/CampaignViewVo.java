package com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-29  20:10
 */
@Data
public class CampaignViewVo extends StreamDataViewVo {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("shopId")
    private Integer shopId;

    @ApiModelProperty("广告活动ID")
    private String campaignId;

    @ApiModelProperty("广告活动类型")
    private String type;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;

    @ApiModelProperty("广告组合名称")
    private String portfolioName;

    @ApiModelProperty("广告组合隐藏状态 1:隐藏  0:可见")
    private Integer isHidden;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("状态")
    private String state;

    @ApiModelProperty("二级状态")
    private String servingStatus;

    @ApiModelProperty("二级状态描述")
    private String servingStatusDec;

    @ApiModelProperty("二级状态名称")
    private String servingStatusName;

    @ApiModelProperty("是否预算不足")
    private Boolean outOfBudget;

    @ApiModelProperty("日预算")
    private String dailyBudget;

    @ApiModelProperty("预算类型")
    private String budgetType;

    @ApiModelProperty("预算剩余")
    private BudgetUsage budgetUsage;

    @ApiModelProperty("建议预算")
    private MissBudgetRecommendation missBudgets;

    @ApiModelProperty("sp sb sd 投放类型统一转换成此字段")
    private String campaignTargetingType;

    @ApiModelProperty("投放类型")
    private String targetingType;

    @ApiModelProperty("广告活动竞价策略")
    private String strategy;

    @ApiModelProperty("详情")
    private String placementProductPage;

    @ApiModelProperty("首页")
    private String placementTop;

    @ApiModelProperty("企业购")
    private String placementSiteAmazonBusiness;

    @ApiModelProperty("startDate")
    private String startDate;

    @ApiModelProperty("endDate")
    private String endDate;

    // sb 活动上的投放类型  keyword、product
    @ApiModelProperty("sb 活动上的投放类型  keyword、product")
    private String targetType;

    @ApiModelProperty("广告形式")
    private String adFormat;

    @ApiModelProperty("预算是否开启分时调价")
    private Integer isBudgetPricing;

    @ApiModelProperty("预算分时调价任务状态")
    private Integer pricingBudgetState;

    @ApiModelProperty("广告位是否开启分时调价")
    private Integer isSpacePricing;

    @ApiModelProperty("广告位分时调价任务状态")
    private Integer pricingSpaceState;

    @ApiModelProperty("活动启停是否开启分时调价")
    private Integer isStatePricing;

    @ApiModelProperty("广告活动启停任务状态")
    private Integer pricingStartStopState;

    @ApiModelProperty("站点")
    private String marketplaceId;
    @ApiModelProperty("支付方式")
    private String costType;

    @ApiModelProperty("品牌id")
    private String brandEntityId;
    @ApiModelProperty("sb自动竞价开关(sb 设置为true，允许亚马逊自动优化放置在搜索顶部以下的出价)")
    private Boolean bidOptimization;
    @ApiModelProperty("sb当开启自动竞价(bidOptimization = true),调整竞价百分比")
    private Double bidMultiplier;

    @ApiModelProperty("预算日志")
    private DataLogVo budgetLog;
    @ApiModelProperty("广告位搜索结果顶部日志")
    private DataLogVo placementTopLog;
    @ApiModelProperty("广告位产品页面日志")
    private DataLogVo placementProductPageLog;
    @ApiModelProperty("其他搜索位置")
    private String placementRestOfSearch;
    @ApiModelProperty("其他搜索位置日志")
    private DataLogVo placementRestOfSearchLog;
    @ApiModelProperty("企业购位置日志")
    private DataLogVo placementSiteAmazonBusinessLog;


    @ApiModelProperty("预算剩余最近3天数据")
    private List<BudgetUsage> budgetUsages;

    @ApiModelProperty("IS字段")
    private String topImpressionShare;

    @ApiModelProperty("预算日志更新标识")
    private Boolean isUpdateBudget;
    @ApiModelProperty("广告位搜索结果顶部日志更新标识")
    private Boolean isUpdatePlacementTop;
    @ApiModelProperty("广告位产品页面日志更新标识")
    private Boolean isUpdatePlacementProductPage;
    @ApiModelProperty("广告位其余位置日志更新标识")
    private Boolean isUpdatePlacementRestOfSearch;
    @ApiModelProperty("企业购位置日志更新标识")
    private Boolean isUpdatePlacementSiteAmazonBusiness;

    @Data
    public static class BudgetUsage {
        private Boolean isNoData;
        private String dayType;
        private String date;
        private Double percent;
        private String lastUpdateAt;
        private Double currentBudget;
        private List<BudgetUsageItem> itemList;

    }

    @Data
    public static class BudgetUsageItem {
        private String updateAt;
        private Double percent;
        private Double currentBudget;
    }

    @Data
    public static class MissBudgetRecommendation {
        private String campaignId;
        private Double suggestedBudget;
        private Integer index;
        private Double estimatedMissedSalesLower;
        private Double estimatedMissedSalesUpper;
        private Integer estimatedMissedImpressionsLower;
        private Integer estimatedMissedImpressionsUpper;
        private Integer estimatedMissedClicksLower;
        private Integer estimatedMissedClicksUpper;
        private String startDate;
        private String endDate;
        private Double percentTimeInBudget;
        private Double suggestedBudgetIncreasePercent;
        private String ruleName;
        private String ruleId;
        private String code;
        private String details;
    }
}
