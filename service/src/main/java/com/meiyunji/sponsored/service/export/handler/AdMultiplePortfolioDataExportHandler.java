package com.meiyunji.sponsored.service.export.handler;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.portfolio.sevice.IMultiplePortfolioService;
import com.meiyunji.sponsored.service.multiple.portfolio.vo.MultiplePortfolioPageVo;
import com.meiyunji.sponsored.service.vo.AdvertisingPortfolioVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.export.util.ExportStringUtil.modifyFormat;

@Service(AdManagePageExportTaskConstant.MULTIPLE_PORTFOLIO)
@Slf4j
public class AdMultiplePortfolioDataExportHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IMultiplePortfolioService multiplePortfolioService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    /**
     * 广告组合导出
     *
     * @param task
     */
    @Override
    public void export(AdManagePageExportTask task) {
        PortfolioPageParam param = JSONUtil.jsonToObject(task.getParam(), PortfolioPageParam.class);
        if (param == null) {
            log.error(String.format("keyword export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        List<ShopAuth> shopList = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
        ShopAuth shop = shopList.get(0);
        List<MultiplePortfolioPageVo> allPortfolioVoList = multiplePortfolioService.exportAllPortfolioData(param);
        if (CollectionUtils.isEmpty(allPortfolioVoList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        // 是否跨币种
        boolean changeRate = MultipleUtils.changeRate(shopList);
        //先把pageVo转成grpcVo
        String fileName = shop.getName() + (shopList.size() > 1 ? "等" : "") + "_广告组合" + "_" + param.getStartDate() + "_" + param.getEndDate();
        int count = 0;
        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AdvertisingPortfolioVo.class);
        List<AdvertisingPortfolioVo> advertPfList = allPortfolioVoList.stream().filter(Objects::nonNull).map(it->buildExportVo(it,changeRate)).collect(Collectors.toList());
        urlList.add(excelService.easyExcelHandlerExport(task.getPuid(),
                advertPfList, fileName + "(" + (count) + ")", AdvertisingPortfolioVo.class, build));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private AdvertisingPortfolioVo buildExportVo(MultiplePortfolioPageVo pfVo, boolean changeRate) {
        String currency = AmznEndpoint.getByMarketplaceId(pfVo.getMarketplaceId()).getCurrencyCode().value();
        AdvertisingPortfolioVo advertPfVo = new AdvertisingPortfolioVo();

        advertPfVo.setShopName(pfVo.getShopName());
        // 组合名字
        advertPfVo.setName(pfVo.getName());
        // 状态
        advertPfVo.setState("enabled");
        // 二级状态名字
        advertPfVo.setServingStatusName(AmazonAdPortfolio.servingStatusEnum.getNameByCode(pfVo.getServingStatusName()));
        if (pfVo.getAmount() != null) {
            advertPfVo.setAmmount(currency + pfVo.getAmount());
        }
        if(AmazonAdPortfolio.PortfolioPolicyEnum.noBudget.getCode().equals(pfVo.getPolicy())){
            advertPfVo.setAmmount("-");
        }
        // 预算上限类型
        advertPfVo.setPolicy(AmazonAdPortfolio.PortfolioPolicyEnum.getDescByCode(pfVo.getPolicy()));
        // 预算金额
        // 预算开始日期
        advertPfVo.setBudgetStartDate(pfVo.getBudgetStartDate());
        // 预算结束日期
        advertPfVo.setBudgetEndDate(getDateState(pfVo.getBudgetEndDate()));
        // 广告活动数量
        advertPfVo.setCampaignNumber(StringUtil.getSafeValue(pfVo.getCampaignNumber()));
        // 曝光量
        advertPfVo.setImpressions(pfVo.getImpressions().intValue());
        // 点击量
        advertPfVo.setClicks(pfVo.getClicks().intValue());
        // 点击率
        advertPfVo.setCtr(modifyFormat(pfVo.getCtr()));
        // 广告花费 (数据为空时候展示为0.00)
        advertPfVo.setAdCost(currency + StringUtil.getSafePlainString(pfVo.getAdCost()));
        // 广告订单量
        advertPfVo.setAdOrderNum(pfVo.getAdOrderNum().intValue());
        // 平均点击费用 (数据为空时候展示为0.00)
        advertPfVo.setAdCostPerClick(currency + StringUtil.getSafePlainString(pfVo.getAdCostPerClick()));
        // 广告销售额 (数据为空时候展示为0.00)
        advertPfVo.setAdSale(currency + StringUtil.getSafePlainString(pfVo.getAdSale()));
        // ACos
        advertPfVo.setAcos(modifyFormat(pfVo.getAcos()));
        // Roas
        advertPfVo.setRoas(StringUtil.getSafePlainString(pfVo.getRoas()));
        // 订单转化率
        advertPfVo.setCvr(modifyFormat(pfVo.getCvr()));
        // ACoTS
        advertPfVo.setAcots(modifyFormat(pfVo.getAcots()));
        // ASoTS
        advertPfVo.setAsots(modifyFormat(pfVo.getAsots()));

        advertPfVo.setCpa(currency + StringUtil.getSafePlainString(pfVo.getCpa()));
        advertPfVo.setOrderNum(pfVo.getAdSaleNum().intValue());
        // 花费占比
        advertPfVo.setAdCostPercentage(changeRate ? "-" : modifyFormat(pfVo.getAdCostPercentage()));
        // 销售额占比
        advertPfVo.setAdSalePercentage(changeRate ? "-" : modifyFormat(pfVo.getAdSalePercentage()));
        // 订单量占比
        advertPfVo.setAdOrderNumPercentage(modifyFormat(pfVo.getAdOrderNumPercentage()));
        // 销量占比
        advertPfVo.setOrderNumPercentage(modifyFormat(pfVo.getAdSaleNumPercentage()));
        // 广告笔单价
        advertPfVo.setAdvertisingUnitPrice(currency + StringUtil.getSafePlainString(pfVo.getAdvertisingUnitPrice()));
        return advertPfVo;
    }

    private static String getDateState(String endDate) {
        if (endDate != null) {
            return endDate;
        } else {
            return "无结束日期";
        }
    }
}
