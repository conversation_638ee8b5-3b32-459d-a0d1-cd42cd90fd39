package com.meiyunji.sponsored.service.multiPlatform.shop.po;


import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@DbTable(value = "t_multi_platform_shop_auth")
public class MultiPlatformShopAuth extends BasePo {

    @DbColumn(value = "id", key = true)
    private Integer id;

    /**
     * 用户id
     */
    @DbColumn(value = "puid")
    private Integer puid;
    /**
     * 店铺名称
     */
    @DbColumn(value = "name")
    private String name;
    /**
     * TEMU平台类型
     * 全托:all_temu; 半托:half_temu; 美国本土:local_us;
     */
    @DbColumn(value = "temu_type")
    private String temuType;

    //前期设计问题,后面新加平台店铺类型使用些字段
    @DbColumn(value = "shop_type")
    private String shopType;

    //平台店铺类型
    @DbColumn(value = "platform_shop_type")
    private String platformShopType;

    //平台商户id  平台seller_id上级
    @DbColumn(value = "platform_merchant_id")
    private String platformMerchantId;

    //平台seller_id  平台店铺id上级
    @DbColumn(value = "platform_seller_id")
    private String platformSellerId;

    //平台店铺id
    @DbColumn(value = "platform_shop_id")
    private String platformShopId;
    /**
     * 平台类型
     */
    @DbColumn(value = "platform_type")
    private String platformType;
    /**
     * 平台类型
     */
    @DbColumn(value = "marketplace_code")
    private String marketplaceCode;
    /**
     * IOSS税号
     */
    @Deprecated
    @DbColumn(value = "ioss_number")
    private String iossNumber;
    /**
     * 币种
     */
    @DbColumn(value = "currency")
    private String currency;
    /**
     * 状态
     */
    @DbColumn(value = "status")
    private Integer status;
    /**
     * 欧区订单token状态
     */
    @DbColumn(value = "temu_eu_order_token_status")
    private Integer temuEuOrderTokenStatus;
    /**
     * 美区订单token状态
     */
    @DbColumn(value = "temu_us_order_token_status")
    private Integer temuUsOrderTokenStatus;
    /**
     * 美区全球订单token状态
     */
    @DbColumn(value = "temu_global_us_order_token_status")
    private Integer temuGlobalUsOrderTokenStatus;
    /**
     * 授权时间
     */
    @DbColumn(value = "auth_time")
    private LocalDateTime authTime;
    /**
     * 授权时间(目前temu有用)
     */
    @DbColumn(value = "auth_expired_time")
    private LocalDateTime authExpiredTime;
    /**
     * createId
     */
    @DbColumn(value = "create_id")
    private Integer createId;
    /**
     * createId
     */
    @DbColumn(value = "update_id")
    private Integer updateId;
    /**
     * 广告授权状态
     */
    @DbColumn(value = "ad_status")
    private Integer adStatus;


    /**
     * 授权时间
     */
    @DbColumn(value = "ad_auth_time")
    private LocalDateTime adAuthTime;

    /**
     * 广告账号名称
     */
    @DbColumn(value = "ad_account_name")
    private String adAccountName;

}
