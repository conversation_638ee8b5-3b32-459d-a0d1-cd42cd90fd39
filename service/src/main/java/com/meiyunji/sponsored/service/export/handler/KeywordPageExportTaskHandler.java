package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.KeywordDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.helper.CpcPageWordRootHelper;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCommonService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsPageVo;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.util.ExportStringUtil;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.util.GrayUtil;
import com.meiyunji.sponsored.service.util.ReportParamUtil;
import com.meiyunji.sponsored.service.vo.KeywordSbTargetingVo;
import com.meiyunji.sponsored.service.vo.KeywordTargetingVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-07-18  14:08
 */
@Service(AdManagePageExportTaskConstant.KEYWORD)
@Slf4j
public class KeywordPageExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcCommonService cpcCommonService;
    @Autowired
    private ICpcKeywordsService cpcKeywordsService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;

    @Override
    public void export(AdManagePageExportTask task) {
        KeywordsPageParam param = JSONUtil.jsonToObject(task.getParam(), KeywordsPageParam.class);
        if (param == null) {
            log.error(String.format("keyword export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        param.setMarketplaceId(shop.getMarketplaceId());
        // 获取页面数据
        boolean supportAbaRankOrder = GrayUtil.isHit(param.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        param.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(param, false));
        if (supportAbaRankOrder) {
            String date = weekSearchTermsAnalysisService.getLatestDate(shop.getMarketplaceId());
            param.setLastWeekSearchTermsRankDate(date);
        }
        this.keywordsPageParamDateFormat(param);
        List<KeywordsPageVo> voList = cpcCommonService.getExportAllKeyWordData(shop, param.getPuid(), param);
        //填充标签
        this.fillAdTagData(param.getPuid(), param.getShopId(), param, voList);
        // 填充广告策略
        cpcKeywordsService.fillAdStrategy(param, voList);
        // 填充广告策略标签
        List<KeywordDataResponse.KeywordsPageRpcVo> list = voList.stream().filter(Objects::nonNull).map(KeywordPageExportTaskHandler::buildGrpcVo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        //集合分片导出
        List<List<KeywordDataResponse.KeywordsPageRpcVo>> partition = Lists.partition(list, Constants.EXPORT_MAX_SIZE);
        boolean supportAbaRankExportWhiteList = GrayUtil.isHit(param.getPuid(),
            dynamicRefreshConfiguration.getSupportAbaRankExportWhiteList(),
            dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        String fileName = shop.getName() + "_关键词" + "_" + param.getStartDate() + "_" + param.getEndDate();
        List<String> urlList;
        if (StringUtils.isNotBlank(param.getExportSortField()) && Objects.nonNull(param.getFreezeNum())) {
            urlList = newExport(partition, supportAbaRankExportWhiteList, param, fileName, shop);
        } else {
            urlList = oldExport(partition, supportAbaRankExportWhiteList, param, fileName);
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private List<String> newExport(List<List<KeywordDataResponse.KeywordsPageRpcVo>> partition, boolean supportAbaRankExportWhiteList, KeywordsPageParam param, String fileName, ShopAuth shop) {
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();

        //组装需要排除的字段
        List<String> excludeFileds;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "ordersNewToBrandPercentageFTD",
                "Video5SecondViews", "Video5SecondViewRate", "VideoFirstQuartileViews", "VideoMidpointViews", "VideoThirdQuartileViews", "VideoCompleteViews", "VideoUnmutes", "ViewabilityRate",
                "ViewClickThroughRate", "BrandedSearches", "ViewImpressions");
            if (!supportAbaRankExportWhiteList) {
                excludeFileds.add("searchFrequencyRank");
                excludeFileds.add("weekRatio");
            }
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            excludeFileds = Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum", "searchFrequencyRank", "weekRatio");
        } else {
            excludeFileds = Collections.emptyList();
        }
        //自定义排序：根据param的exportSortField，中的字段作为表格中的表头字段顺序导出
        List<String> sortFields = Arrays.asList(param.getExportSortField().split(","));
        sortFields = sortFields.stream().filter(item -> !excludeFileds.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortFields)) {
            return downloadUrl;
        }
        // 默认导出广告策略标签
        sortFields.add("adStrategyTag");

        for (List<KeywordDataResponse.KeywordsPageRpcVo> list1 : partition) {
            List<KeywordTargetingVo> keywordTargetingVoList = new LinkedList<>();
            for (KeywordDataResponse.KeywordsPageRpcVo keyVo : list1) {
                keywordTargetingVoList.add(this.buildExportVo(param, keyVo));
            }
            if (CollectionUtils.isEmpty(keywordTargetingVoList)) {
                continue;
            }
            String url = customFieldSortExport(keywordTargetingVoList, sortFields, shop, fileName, param);
            downloadUrl.add(url);
        }
        return downloadUrl;
    }

    private String customFieldSortExport(List<KeywordTargetingVo> keywordTargetingVos, List<String> sortFields, ShopAuth shop, String fileName, KeywordsPageParam param) {
        //Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild()
            .rate();
        //冻结前n列前1行
        if (param.getFreezeNum() != null) {
            build.freezeRowAndCol(param.getFreezeNum(), 1);
        }
        //非对象导出货币样式
        build.noModleHandler(getCurrencyIndex(sortFields));

        //构建行
        List<List<Object>> rows = new ArrayList<>(keywordTargetingVos.size());
        for (KeywordTargetingVo keywordVo : keywordTargetingVos) {
            rows.add(buildExportCols(keywordVo, sortFields));
        }

        //构建表头
        List<String> headNames = new ArrayList<>(sortFields.size());
        for (String sortField : sortFields) {
            AdvertisingKeywordExportFieldEnum fieldEnum = AdvertisingKeywordExportFieldEnum.fromPoParamKey(sortField);
            if (fieldEnum == null) {
                log.error("sortFields 包含非法字符，导出阻止，返回空, sortFields:{}", sortFields);
                throw new RuntimeException("sortFields 包含非法字符，导出阻止，返回空, sortFields:" + sortFields);
            }
            headNames.add(fieldEnum.getTableColName());
        }
        //导出
        return excelService.exportByCustomColSort(shop.getPuid(), headNames, rows, fileName + "(" + (0) + ")", build);
    }

    private List<Object> buildExportCols(KeywordTargetingVo cpVo, List<String> sortFields) {
        List<Object> cols = new ArrayList<>(AdvertisingKeywordExportFieldEnum.values().length);
        for (String fieldName : sortFields) {
            AdvertisingKeywordExportFieldEnum fieldEnum = AdvertisingKeywordExportFieldEnum.fromPoParamKey(fieldName);
            if (fieldEnum == null) {
                return Collections.emptyList();
            }
            Object value = getObjectByField(cpVo, fieldEnum);
            cols.add(value);
        }

        return cols;
    }

    private List<Integer> getCurrencyIndex(List<String> sortFields) {
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            AdvertisingKeywordExportFieldEnum keywordExportFieldEnum = AdvertisingKeywordExportFieldEnum.fromPoParamKey(sortFields.get(i));
            //出现这种情况就是有问题，暂时不考虑
            if (keywordExportFieldEnum == null) {
                return Collections.emptyList();
            }
            if (keywordExportFieldEnum.getCurrencyStyle()) {
                currencyIndex.add(i);
            }
        }

        return currencyIndex;
    }

    private Object getObjectByField(KeywordTargetingVo keyVo, AdvertisingKeywordExportFieldEnum fieldEnum) {
        Object value = null;
        switch (fieldEnum) {
            case KEYWORD_TEXT:
                value = keyVo.getKeywordText();
                break;
            case KEYWORD_TEXT_CN:
                value = keyVo.getKeywordTextCn();
                break;
            case STATE:
                value = keyVo.getState();
                break;
            case CAMPAIGN_STATE:
                value = keyVo.getServingStatusName();
                break;
            case MATCH_TYPE:
                value = keyVo.getMatchType();
                break;
            case TYPE:
                value = keyVo.getType();
                break;
            case TARGETING_TYPE:
                value = keyVo.getCampaignTargetingType();
                break;
            case GROUP_NAME:
                value = keyVo.getAdvertisingGroup();
                break;
            case CAMPAIGN_NAME:
                value = keyVo.getAdvertisingActivities();
                break;
            case PORTFOLIO_NAME:
                value = keyVo.getPortfolioName();
                break;
            case SUGGEST_BID:
                value = keyVo.getSuggestBid();
                break;
            case SUGGEST_BID_RANGE:
                value = keyVo.getSuggestBidScope();
                break;
            case BID:
                value = keyVo.getBid();
                break;
            case AD_COST:
                value = keyVo.getAdCost();
                break;
            case AD_COST_PERCENTAGE:
                value = keyVo.getAdCostPercentage();
                break;
            case IMPRESSIONS:
                value = keyVo.getImpressions();
                break;
            case TOP_IMPRESSION_SHARE:
                value = keyVo.getTopImpressionShare();
                break;
            case VIEW_IMPRESSIONS:
                value = keyVo.getViewImpressions();
                break;
            case CLICKS:
                value = keyVo.getClicks();
                break;
            case CPA:
                value = keyVo.getCpa();
                break;
            case AD_COST_PER_CLICK:
                value = keyVo.getAdCostPerClick();
                break;
            case CTR:
                value = keyVo.getCtr();
                break;
            case CVR:
                value = keyVo.getCvr();
                break;
            case ACOS:
                value = keyVo.getAcos();
                break;
            case ROAS:
                value = keyVo.getRoas();
                break;
            case ACOTS:
                value = keyVo.getAcots();
                break;
            case ASOTS:
                value = keyVo.getAsots();
                break;
            case ADVERTISING_UNIT_PRICE:
                value = keyVo.getAdvertisingUnitPrice();
                break;
            case AD_ORDER_NUM:
                value = keyVo.getAdOrderNum();
                break;
            case AD_ORDER_NUM_PERCENTAGE:
                value = keyVo.getAdOrderNumPercentage();
                break;
            case SELF_AD_ORDER_NUM:
                value = keyVo.getAdSaleNum();
                break;
            case OTHER_AD_ORDER_NUM:
                value = keyVo.getAdOtherOrderNum();
                break;
            case AD_SALE:
                value = keyVo.getAdSale();
                break;
            case AD_SALE_PERCENTAGE:
                value = keyVo.getAdSalePercentage();
                break;
            case AD_SELF_SALE:
                value = keyVo.getAdSales();
                break;
            case AD_OTHER_SALES:
                value = keyVo.getAdOtherSales();
                break;
            case AD_SALE_NUM:
                value = keyVo.getOrderNum();
                break;
            case AD_SALE_NUM_PERCENTAGE:
                value = keyVo.getOrderNumPercentage();
                break;
            case AD_SELF_SALE_NUM:
                value = keyVo.getAdSelfSaleNum();
                break;
            case AD_OTHER_SALE_NUM:
                value = keyVo.getAdOtherSaleNum();
                break;
            case ORDERS_NEW_TO_BRAND_FTD:
                value = keyVo.getOrdersNewToBrandFTD();
                break;
            case ORDER_RAT_ENEW_TO_BRAND_FTD:
                value = keyVo.getOrderRateNewToBrandFTD();
                break;
            case SALES_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesNewToBrandFTD();
                break;
            case SALES_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesRateNewToBrandFTD();
                break;
            case ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD:
                value = keyVo.getOrdersNewToBrandPercentageFTD();
                break;
            case VIDEO_5_SECOND_VIEWS:
                value = keyVo.getVideo5SecondViews();
                break;
            case VIDEO_5_SECOND_VIEW_RATE:
                value = keyVo.getVideo5SecondViewRate();
                break;
            case VIDEO_FIRST_QUARTILE_VIEWS:
                value = keyVo.getVideoFirstQuartileViews();
                break;
            case VIDEO_MIDPOINT_VIEWS:
                value = keyVo.getVideoMidpointViews();
                break;
            case VIDEO_THIRD_QUARTILE_VIEWS:
                value = keyVo.getVideoThirdQuartileViews();
                break;
            case VIDEO_COMPLETE_VIEWS:
                value = keyVo.getVideoCompleteViews();
                break;
            case VIDEO_UNMUTES:
                value = keyVo.getVideoUnmutes();
                break;
            case VIEWABILITY_RATE:
                value = keyVo.getViewabilityRate();
                break;
            case VIEW_CLICK_THROUGH_RATE:
                value = keyVo.getViewClickThroughRate();
                break;
            case BRANDED_SEARCHES:
                value = keyVo.getBrandedSearches();
                break;
            case AD_TAGS:
                value = keyVo.getAdTag();
                break;
            case SEARCH_FREQUENCY_RANK:
                value = keyVo.getSearchFrequencyRank();
                break;
            case WEEK_RATIO:
                value = keyVo.getWeekRatio();
                break;
            case AD_STRATEGY_TAG:
                value = keyVo.getAdStrategyTag();
                break;
            default:
        }
        return value;
    }

    private List<String> oldExport(List<List<KeywordDataResponse.KeywordsPageRpcVo>> partition, boolean supportAbaRankExportWhiteList, KeywordsPageParam param, String fileName) {
        List<String> urlList = new ArrayList<>();
        int count = 0;
        //页面渲染
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        for (List<KeywordDataResponse.KeywordsPageRpcVo> list1 : partition) {
            List<KeywordTargetingVo> keywordTargetingVoList = new LinkedList<>();
            for (KeywordDataResponse.KeywordsPageRpcVo keyVo : list1) {
                keywordTargetingVoList.add(this.buildExportVo(param, keyVo));
            }
            if (keywordTargetingVoList.size() > 0) {
                //设置表头
                Class clazz = KeywordTargetingVo.class;
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    clazz = KeywordSbTargetingVo.class;
                }
                build = build.currencyNew(clazz);
                List<String> excludeFileds = Lists.newArrayList();
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "ordersNewToBrandPercentageFTD",
                        "Video5SecondViews", "Video5SecondViewRate", "VideoFirstQuartileViews", "VideoMidpointViews", "VideoThirdQuartileViews", "VideoCompleteViews", "VideoUnmutes", "ViewabilityRate",
                        "ViewClickThroughRate", "BrandedSearches", "ViewImpressions");
                    if (!supportAbaRankExportWhiteList) {
                        excludeFileds.add("searchFrequencyRank");
                        excludeFileds.add("weekRatio");
                    }
                }
                if (Constants.SB.equalsIgnoreCase(param.getType())) {
                    excludeFileds = Lists.newArrayList("adSelfSaleNum", "adOtherSaleNum", "searchFrequencyRank", "weekRatio");
                }
                urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), keywordTargetingVoList, fileName + "(" + count++ + ")", clazz, build, excludeFileds));
            }
        }
        return urlList;
    }

    private void keywordsPageParamDateFormat(KeywordsPageParam param) {
        //日期转换格式
        param.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    private KeywordTargetingVo buildExportVo(KeywordsPageParam param, KeywordDataResponse.KeywordsPageRpcVo keyVo) {
        KeywordTargetingVo ktVo = new KeywordTargetingVo();
        ktVo.setImpressions(keyVo.getImpressions().getValue());
        ktVo.setClicks(keyVo.getClicks().getValue());
        ktVo.setTopImpressionShare(ReportParamUtil.getExportTopIS(keyVo.getTopImpressionShare()));
        ktVo.setAdOrderNum(keyVo.getAdOrderNum().getValue());
        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keyVo.getKeywordText())) {
            ktVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keyVo.getKeywordText())) {
            ktVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
        } else {
            ktVo.setKeywordText(keyVo.getKeywordText());
        }
        ktVo.setKeywordTextCn(keyVo.getKeywordTextCn());
        SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(keyVo.getKeywordText());

        if (keywordGroupValueEnumByTextCn  != null) {
            ktVo.setKeywordText(keywordGroupValueEnumByTextCn.getTextCn());
        }


        // 广告组合
        ktVo.setPortfolioName(keyVo.getPortfolioName());
        //所属广告活动
        ktVo.setAdvertisingActivities(keyVo.getCampaignName());
        //所属广告组
        ktVo.setAdvertisingGroup(keyVo.getAdGroupName());
        //状态
        ktVo.setState(AllAdStateEnum.getStateValue(keyVo.getState()));
        ktVo.setServingStatusName(keyVo.getServingStatusName());
        //匹配类型
        ktVo.setMatchType(SbMatchValueEnum.getMatchValue(keyVo.getMatchType()));
        //建议竞价
        if (StringUtils.isNotBlank(keyVo.getSuggestBid())) {
            ktVo.setSuggestBid(ExportStringUtil.getSuggest(keyVo.getSuggestBid(), param.getIcon()));
        }
        if (StringUtils.isNotBlank(keyVo.getRangeStart()) && StringUtils.isNotBlank(keyVo.getRangeEnd())) {
            //建议竞价范围
            ktVo.setSuggestBidScope(ExportStringUtil.getSuggestBidScope(keyVo.getRangeStart(), keyVo.getRangeEnd(), param.getIcon()));
        }

        //竞价
        ktVo.setBid(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getBid()));
        //点击率
        ktVo.setCtr(ExportStringUtil.modifyFormat(keyVo.getCtr()));
        //订单转化率
        ktVo.setCvr(ExportStringUtil.modifyFormat(keyVo.getCvr()));
        //ACoS
        ktVo.setAcos(ExportStringUtil.modifyFormat(keyVo.getAcos()));
        //ACoTS
        ktVo.setAcots(ExportStringUtil.modifyFormat(keyVo.getAcots()));
        ktVo.setRoas(keyVo.getRoas());
        //ASoTS
        ktVo.setAsots(ExportStringUtil.modifyFormat(keyVo.getAsots()));
        //广告花费
        ktVo.setAdCost(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getAdCost()));
        //平均点击费用(特殊处理)
        ktVo.setAdCostPerClick(param.getCurrency() + ExportStringUtil.getAdCostPerClick(keyVo.getAdCostPerClick()));
        //广告销售额
        ktVo.setAdSale(param.getCurrency() + ExportStringUtil.getAdCostPerClick(keyVo.getAdSale()));
        //推广类型
        ktVo.setCampaignTargetingType(TargetingEnum.getTargetingValue(keyVo.getCampaignTargetingType()));
        //投放类型
        ktVo.setType(CampaignTypeEnum.getCampaignValue(keyVo.getType()));
        ktVo.setCpa(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getCpa()));
        ktVo.setAdSaleNum(keyVo.getAdSaleNum().getValue());
        ktVo.setAdOtherOrderNum(keyVo.getAdOtherOrderNum().getValue());
        ktVo.setAdSales(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getAdSales()));
        ktVo.setAdOtherSales(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getAdOtherSales()));
        ktVo.setOrderNum(keyVo.getOrderNum().getValue());
        ktVo.setAdSelfSaleNum(keyVo.getAdSelfSaleNum().getValue());
        ktVo.setAdOtherSaleNum(keyVo.getAdOtherSaleNum().getValue());
        ktVo.setOrdersNewToBrandFTD(keyVo.getOrdersNewToBrandFTD().getValue());
        ktVo.setOrderRateNewToBrandFTD(ExportStringUtil.modifyFormat(keyVo.getOrderRateNewToBrandFTD()));
        ktVo.setSalesNewToBrandFTD(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getSalesNewToBrandFTD()));
        ktVo.setSalesRateNewToBrandFTD(ExportStringUtil.modifyFormat(keyVo.getSalesRateNewToBrandFTD()));
        ktVo.setOrdersNewToBrandPercentageFTD(ExportStringUtil.modifyFormat(keyVo.getOrdersNewToBrandPercentageFTD()));
        // 花费占比
        ktVo.setAdCostPercentage(ExportStringUtil.modifyFormat(keyVo.getAdCostPercentage()));
        // 销售额占比
        ktVo.setAdSalePercentage(ExportStringUtil.modifyFormat(keyVo.getAdSalePercentage()));
        // 订单量占比
        ktVo.setAdOrderNumPercentage(ExportStringUtil.modifyFormat(keyVo.getAdOrderNumPercentage()));
        // 销量占比
        ktVo.setOrderNumPercentage(ExportStringUtil.modifyFormat(keyVo.getOrderNumPercentage()));

        ktVo.setVideo5SecondViews(keyVo.getVideo5SecondViews());
        ktVo.setVideo5SecondViewRate(ExportStringUtil.modifyFormat(keyVo.getVideo5SecondViewRate()));
        ktVo.setVideoFirstQuartileViews(keyVo.getVideoFirstQuartileViews());
        ktVo.setVideoMidpointViews(keyVo.getVideoMidpointViews());
        ktVo.setVideoThirdQuartileViews(keyVo.getVideoThirdQuartileViews());
        ktVo.setVideoCompleteViews(keyVo.getVideoCompleteViews());
        ktVo.setVideoUnmutes(keyVo.getVideoUnmutes());
        ktVo.setViewImpressions(keyVo.getViewImpressions().getValue());
        ktVo.setViewabilityRate(ExportStringUtil.modifyFormat(keyVo.getViewabilityRate()));
        ktVo.setViewClickThroughRate(ExportStringUtil.modifyFormat(keyVo.getViewClickThroughRate()));
        ktVo.setBrandedSearches(keyVo.getBrandedSearches());
        ktVo.setAdvertisingUnitPrice(param.getCurrency() + ExportStringUtil.formatToNumber(keyVo.getAdvertisingUnitPrice()));

        if (keyVo.getSearchFrequencyRank().getValue() > 0) {
            ktVo.setSearchFrequencyRank(String.valueOf(keyVo.getSearchFrequencyRank().getValue()));
            ktVo.setWeekRatio(ExportStringUtil.modifyFormat(keyVo.getWeekRatio()));
        } else {
            ktVo.setSearchFrequencyRank("-");
            ktVo.setWeekRatio("-");
        }

        ktVo.setAdTag(keyVo.getAdTag());
        ktVo.setAdStrategyTag(keyVo.getAdStrategyTag());
        return ktVo;
    }

    private static KeywordDataResponse.KeywordsPageRpcVo buildGrpcVo(KeywordsPageVo item) {
        KeywordDataResponse.KeywordsPageRpcVo.Builder vo = KeywordDataResponse.KeywordsPageRpcVo.newBuilder();
        if (StringUtils.isNotBlank(item.getKeywordText())) {
            vo.setKeywordText(item.getKeywordText());
        }
        if (StringUtils.isNotBlank(item.getKeywordTextCn())) {
            vo.setKeywordTextCn(item.getKeywordTextCn());
        }
        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
            vo.setCampaignTargetingType(item.getCampaignTargetingType());
        }
        if (StringUtils.isNotBlank(item.getCampaignName())) {
            vo.setCampaignName(item.getCampaignName());
        }
        if (StringUtils.isNotBlank(item.getAdGroupName())) {
            vo.setAdGroupName(item.getAdGroupName());
        }
        if (StringUtils.isNotBlank(item.getMatchType())) {
            vo.setMatchType(item.getMatchType());
        }
        if (StringUtils.isNotBlank(item.getState())) {
            vo.setState(item.getState());
        }
        if (StringUtils.isNotBlank(item.getType())) {
            vo.setType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getSuggestBid())) {
            vo.setSuggestBid(item.getSuggestBid());
        }
        if (StringUtils.isNotBlank(item.getRangeEnd())) {
            vo.setRangeEnd(item.getRangeEnd());
        }
        if (StringUtils.isNotBlank(item.getRangeStart())) {
            vo.setRangeStart(item.getRangeStart());
        }
        if (StringUtils.isNotBlank(item.getBid())) {
            vo.setBid(item.getBid());
        }
        if (StringUtils.isNotBlank(item.getPortfolioName())) {
            vo.setPortfolioName(item.getPortfolioName());
        }
        if (StringUtils.isNotBlank(item.getServingStatusName())) {
            vo.setServingStatusName(item.getServingStatusName());
        }

        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
        /**
         * TODO 广告报告重构
         */
        //每笔订单花费
        vo.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
        //本广告产品订单量
        vo.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
        //其他产品广告订单量
        vo.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
        //本广告产品销售额
        vo.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
        //其他产品广告销售额
        vo.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
        //广告销量
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        //本广告产品销量
        vo.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
        //其他产品广告销量
        vo.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
        //“品牌新买家”订单量
        vo.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
        //“品牌新买家”订单百分比
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
        //“品牌新买家”销售额
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
        //“品牌新买家”销售额百分比
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
        //“品牌新买家”订单转化率
        vo.setOrdersNewToBrandPercentageFTD(StringUtils.isNotBlank(item.getOrdersNewToBrandPercentageFTD()) ? item.getOrdersNewToBrandPercentageFTD() : "0");
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
        vo.setTopImpressionShare(Optional.ofNullable(item.getTopImpressionShare()).orElse(""));

        vo.setVideo5SecondViews(Optional.ofNullable(item.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViewRate(Optional.ofNullable(item.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
        vo.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        //处理标签，取出标签进行导出
        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
            List<String> adTag = item.getAdTags().stream().map(AdTag::getName).collect(Collectors.toList());
            String adTagName = "";
            if (CollectionUtils.isNotEmpty(adTag) && adTag.size() > 1) {
                adTagName = String.join(",", adTag);
                vo.setAdTag(adTagName);
            } else {
                adTagName = String.join("", adTag);
                vo.setAdTag(adTagName);
            }
        } else {
          vo.setAdTag("");
        }
        // 处理广告策略标签，取出标签名称进行导出
        if (CollectionUtils.isNotEmpty(item.getStrategyList())) {
            List<String> adStrategyTag = item.getStrategyList().stream().map(it-> AdTargetStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
            vo.setAdStrategyTag(String.join(",", adStrategyTag));
        }else{
            vo.setAdStrategyTag("");
        }
        // 兼容特殊排序逻辑
        if (item.getSearchFrequencyRank() != null) {
            if (item.getSearchFrequencyRank() == Integer.MAX_VALUE) {
                vo.setSearchFrequencyRank(Int32Value.of(0));
                vo.setWeekRatio(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
            } else {
                vo.setSearchFrequencyRank(Int32Value.of(item.getSearchFrequencyRank()));
                vo.setWeekRatio(Optional.ofNullable(item.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP).toPlainString());
            }
        }

        return vo.build();
    }

    private void fillAdTagData(Integer puid, Integer shopId, KeywordsPageParam param, List<KeywordsPageVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        List<String> relationIds = rows.stream().map(KeywordsPageVo::getKeywordId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.TARGET.getType(), param.getType(), AdMarkupTargetTypeEnum.KEYWORD.getType(), null, relationIds);
        if (CollectionUtils.isEmpty(relationVos)) {
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if (CollectionUtils.isEmpty(byLongIdList)) {
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (KeywordsPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getKeywordId());
            if (adMarkupTagVo == null) {
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if (tagIds == null) {
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }
}
