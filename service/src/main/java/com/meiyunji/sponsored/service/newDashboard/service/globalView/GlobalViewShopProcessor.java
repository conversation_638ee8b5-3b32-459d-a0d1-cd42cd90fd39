package com.meiyunji.sponsored.service.newDashboard.service.globalView;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.globalView.IGlobalViewDao;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.GlobalViewAdTypeDto;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.GlobalViewShopDto;
import com.meiyunji.sponsored.service.newDashboard.dto.globalView.base.GlobalViewBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewConstant;
import com.meiyunji.sponsored.service.newDashboard.enums.globalView.GlobalViewProcessorEnum;
import com.meiyunji.sponsored.service.newDashboard.vo.globalView.GlobalViewBaseReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * 全局概览-店铺列表
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class GlobalViewShopProcessor extends AbstractGlobalViewProcessor<GlobalViewBaseReqVo, GlobalViewShopDto> {

    @Resource
    private IGlobalViewDao globalViewDao;

    @Resource
    private GlobalViewAdTypeProcessor adTypeProcessor;

    @Resource
    private IScVcShopAuthDao shopAuthDao;


    @Override
    public Page<GlobalViewShopDto> getPageData(GlobalViewBaseReqVo req) {
        // 分页获取原始指标数据
        Page<GlobalViewBaseDataDto> page = globalViewDao.getPageData(req, GlobalViewProcessorEnum.SHOP);
        if (CollectionUtils.isEmpty(page.getRows())) {
            return new Page<>(req.getPageNo(), req.getPageSize());
        }
        // key值 marketplaceId_shopId  取shop_id
        List<Integer> shopIdList = StreamUtil.toList(page.getRows(), it -> Integer.parseInt(it.getKey().split("_")[1]));
        req.setShopIdList(shopIdList);
        // 获取店铺原始指标数据
        List<GlobalViewBaseDataDto> shopSaleList = globalViewDao.getShopSaleList(req, GlobalViewProcessorEnum.SHOP, req.getStartDate(), req.getEndDate());
        Map<String, GlobalViewBaseDataDto> shopSaleMap = StreamUtil.toMap(shopSaleList, GlobalViewBaseDataDto::getKey);
        // 获取展开广告类型指标信息
        Map<String, List<GlobalViewAdTypeDto>> adTypeMap = new HashMap<>();
        if (req.getNeedChild()) {
            req.setPageSize(Constants.EXPORT_MAX_SIZE);
            Page<GlobalViewAdTypeDto> pageData = adTypeProcessor.processPage(req);
            adTypeMap = StreamUtil.groupingBy(pageData.getRows(), GlobalViewAdTypeDto::getShopId);
        }
        // 获取站点信息
        List<ShopAuthBo> shopList = shopAuthDao.getShopAuthBoByIds(req.getPuid(), shopIdList);
        // 构建站点列表返回参数
        List<GlobalViewShopDto> shopViewList = buildShop(page, shopSaleMap, adTypeMap, shopList);
        return new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), shopViewList);
    }

    @Override
    public GlobalViewBaseDataDto getSumData(GlobalViewBaseReqVo req, List<GlobalViewShopDto> dataList) {
        GlobalViewBaseDataDto sumData = globalViewDao.getSumData(req, GlobalViewProcessorEnum.SHOP);
        GlobalViewBaseDataDto shopSumData = globalViewDao.getShopSumData(req);
        sumData.setShopSaleNum(shopSumData.getShopSaleNum());
        sumData.setShopSales(shopSumData.getShopSales());
        for (GlobalViewShopDto dto : dataList) {
            if (CollectionUtils.isEmpty(dto.getChildList())) {
                continue;
            }
            // 使用父层级总数计算占比
            calPercentData(dto.getChildList(), sumData);
        }
        return sumData;
    }

    @Override
    public List<GlobalViewShopDto> getMomData(GlobalViewBaseReqVo req, List<GlobalViewShopDto> dataList) {
        return getMomYoyData(req, req.getMomStartDate(), req.getMomEndDate(), dataList);
    }

    @Override
    public List<GlobalViewShopDto> getYoyData(GlobalViewBaseReqVo req, List<GlobalViewShopDto> dataList) {
        return getMomYoyData(req, req.getYoyStartDate(), req.getYoyEndDate(), dataList);
    }

    @Override
    public List<String> getExportHeader() {
        List<String> headerList = new ArrayList<>(GlobalViewConstant.baseHeaderList);
        headerList.add(0, "shopName");
        headerList.add(1, "marketplaceName");
        return headerList;
    }

    @Override
    public List<GlobalViewShopDto> getExportData(List<GlobalViewShopDto> dataList) {
        List<GlobalViewShopDto> list = new ArrayList<>();
        for (GlobalViewShopDto shop : dataList) {
            list.add(shop);
            if (CollectionUtils.isEmpty(shop.getChildList())) {
                continue;
            }
            shop.getChildList().forEach(it -> {
                GlobalViewShopDto dto = new GlobalViewShopDto();
                BeanUtils.copyProperties(it, dto);
                // 子行名称: shopName-广告类型
                dto.setShopName(shop.getShopName() + "-" + it.getCampaignType());
                dto.setMarketplaceName(shop.getMarketplaceName());
                list.add(dto);
            });
        }
        return list;
    }

    /**
     * 构建站点列表返回参数
     */
    private static List<GlobalViewShopDto> buildShop(Page<GlobalViewBaseDataDto> page, Map<String, GlobalViewBaseDataDto> shopSaleMap,
                                                     Map<String, List<GlobalViewAdTypeDto>> adTypeMap, List<ShopAuthBo> shopList) {
        List<GlobalViewShopDto> shopDtoList = new ArrayList<>();
        Map<Integer, ShopAuthBo> shopMap = StreamUtil.toMap(shopList, ShopAuthBo::getId);
        for (GlobalViewBaseDataDto row : page.getRows()) {
            GlobalViewShopDto shopDto = new GlobalViewShopDto();
            BeanUtils.copyProperties(row, shopDto);
            // key值 marketplaceId_shopId  取shop_id
            String shopId = row.getKey().split("_")[1];
            GlobalViewBaseDataDto shopSale = shopSaleMap.get(shopId);
            if (shopSale != null) {
                shopDto.setShopSales(shopSale.getShopSales());
                shopDto.setShopSaleNum(shopSale.getShopSaleNum());
            }
            // 获取店铺
            ShopAuthBo shop = shopMap.get(Integer.parseInt(shopId));
            shopDto.setShopId(shopId);
            shopDto.setShopName(shop.getName());
            shopDto.setMarketplaceId(shop.getMarketplaceId());
            AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId());
            shopDto.setMarketplaceName(Objects.nonNull(endpoint) ? endpoint.getMarketplaceCN() : "");
            shopDto.setChildList(adTypeMap.get(shopId));
            shopDtoList.add(shopDto);
        }
        return shopDtoList;
    }

    /**
     * 根据时间获取同环比原始指标数据
     */
    private List<GlobalViewShopDto> getMomYoyData(GlobalViewBaseReqVo req, String startDate, String endDate, List<GlobalViewShopDto> dataList) {
        List<GlobalViewShopDto> list = new ArrayList<>();
        List<GlobalViewBaseDataDto> listData = globalViewDao.getListData(req, GlobalViewProcessorEnum.SHOP, startDate, endDate);
        List<GlobalViewBaseDataDto> shopSaleList = globalViewDao.getShopSaleList(req, GlobalViewProcessorEnum.SHOP, startDate, endDate);
        Map<String, GlobalViewBaseDataDto> dataMap = StreamUtil.toMap(listData, GlobalViewBaseDataDto::getKey);
        Map<String, GlobalViewBaseDataDto> shopMap = StreamUtil.toMap(shopSaleList, GlobalViewBaseDataDto::getKey);
        for (GlobalViewShopDto dto : dataList) {
            GlobalViewShopDto shopDto = new GlobalViewShopDto();
            GlobalViewBaseDataDto data = dataMap.get(dto.getKey());
            GlobalViewBaseDataDto shop = shopMap.get(dto.getKey().split("_")[1]);
            if (data != null) {
                BeanUtils.copyProperties(data, shopDto);
            }
            if (shop != null) {
                shopDto.setShopSales(shop.getShopSales());
                shopDto.setShopSaleNum(shop.getShopSaleNum());
            }
            shopDto.setKey(dto.getKey());
            list.add(shopDto);
        }
        return list;
    }
}
