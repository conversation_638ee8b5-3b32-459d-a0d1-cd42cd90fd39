package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleActionType;
import com.meiyunji.sellfox.aadras.types.enumeration.RuleExecuteOperationType;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adGroup.AdGroupDefaultOrderEnum;
import com.meiyunji.sponsored.service.autoRule.vo.CampaignIdWithAdTypeVo;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.GroupInfoPageVo;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.strategy.vo.AdStrategyGroupParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.*;

/**
 * <AUTHOR> on 2021/7/6
 */
@Repository
public class AmazonSdAdGroupDaoImpl extends BaseShardingDaoImpl<AmazonSdAdGroup> implements IAmazonSdAdGroupDao {

    @Override
    public void batchAdd(int puid, List<AmazonSdAdGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdGroup po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getMarketplaceId());
            arg.add(po.getProfileId());
            arg.add(po.getCampaignId());
            arg.add(po.getAdGroupId());
            arg.add(po.getName());
            arg.add(po.getDefaultBid());
            arg.add(po.getState());
            arg.add(po.getTactic());
            arg.add(po.getServingStatus());
            arg.add(po.getBidOptimization());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getCreateInAmzup());
            arg.add(po.getCreateId());
            arg.add(po.getUpdateId());
            arg.add(Optional.ofNullable(po.getCreativeType()).filter(StringUtils::isNotEmpty).orElse(""));
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_ad_group_sd (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`campaign_id`,`ad_group_id`,`name`," +
                "`default_bid`,`state`,`tactic`,`serving_status`,`bid_optimization`,`creation_date`,`last_updated_date`,`create_in_amzup`," +
                "`create_id`,`update_id`,`creative_type`, `create_time`,`update_time`) values (?,?, ?,?, ?,?, ?,?, " +
                "?,?, ?,?, ?,?, ?,?, ?, ?, now(3), now(3))";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSdAdGroup> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;
        for (AmazonSdAdGroup po : list) {
            if (po.getPuid() == null || po.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(po.getName());
            arg.add(po.getDefaultBid());
            arg.add(po.getState());
            arg.add(po.getTactic());
            arg.add(po.getServingStatus());
            arg.add(po.getBidOptimization());
            arg.add(po.getCreationDate());
            arg.add(po.getLastUpdatedDate());
            arg.add(po.getUpdateId());
            arg.add(Optional.ofNullable(po.getCreativeType()).filter(StringUtils::isNotEmpty).orElse(""));
            arg.add(po.getPuid());
            arg.add(po.getShopId());
            arg.add(po.getAdGroupId());
            argList.add(arg.toArray());
        }

        String sql = "update `t_amazon_ad_group_sd` set `name` =?,`default_bid` =?,`state`=?,`tactic`=?,`serving_status`=?,`bid_optimization`=?," +
                "`creation_date`=?,`last_updated_date`=?, update_id=?, `creative_type`=?  where `puid` =? and `shop_id`=? and `ad_group_id`=?";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSdAdGroup> listByGroupId(int puid, int shopId, List<String> groupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("ad_group_id", groupIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdGroup> listByGroupId(int puid, List<Integer> shopIdList, List<String> groupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .in("ad_group_id", groupIds.toArray())
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public AmazonSdAdGroup getByGroupId(int puid, Integer shopId, String groupId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("ad_group_id", groupId)
                .build();

        return getByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdGroup> getByGroupIds(int puid, Integer shopId, List<String> groupIds) {

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .inStrList("ad_group_id", groupIds.stream().toArray(String[]::new))
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdGroup> getByAdGroupIds(int puid, List<String> groupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .inStrList("ad_group_id", groupIds.stream().toArray(String[]::new))
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdGroup> getByCampaignIds(int puid, Integer shopId, List<String> campaignIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .inStrList("campaign_id", campaignIds.stream().toArray(String[]::new))
                .build();

        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSdAdGroup> listAmazonSdAdGroup(int puid, Integer shopId, List<String> campaignIds, String status, String servingStatus) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .inStrList("campaign_id", campaignIds.stream().toArray(String[]::new));
        if (StringUtils.isNotBlank(status)) {
            List<String> stateList = Arrays.asList(status.split(","));
            if (stateList.size() > 0) {
                conditionBuilder.in("state", stateList.toArray());
            }
        }
        if (StringUtils.isNotBlank(servingStatus)) {
            List<String> servingStatusList = Arrays.asList(servingStatus.split(","));
            if (servingStatusList.size() > 0) {
                List<String> statusList = new ArrayList<>();
                for (String servingStatusKey : servingStatusList) {
                    List<String> servingStatusValueList = Constants.SERVER_STATUS_SELECT.get(servingStatusKey);
                    statusList.addAll(servingStatusValueList);
                }
                conditionBuilder.in("serving_status", statusList.toArray());
            }

        }
        return listByCondition(puid, conditionBuilder.build());
    }

    @Override
    public List<String> getGroupIdsByTactic(int puid, Integer shopId, String tactic) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("tactic", tactic)
                .build();
        return listDistinctFieldByCondition(puid,"ad_group_id", builder, String.class);
    }

    @Override
    public Page<AmazonSdAdGroup> pageList(int puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", param.getShopId())
                .equalToWithoutCheck("campaign_id", param.getCampaignId());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("name".equals(param.getSearchField())) {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        String orderBySql = " order by update_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonSdAdGroup> listByCondition(int puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignId());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("name".equals(param.getSearchField())) {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String campaignId, String name) {
        String sql = "select count(*) from t_amazon_ad_group_sd where puid = ? and shop_id = ? and campaign_id=? and `name`=? and `state` in ('enabled','paused')";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, campaignId, name);
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String adGroupId) {
        String sql = "select count(*) from t_amazon_ad_group_sd where puid = ? and shop_id = ? and ad_group_id = ?";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, shopId, adGroupId);
    }

    @Override
    public Page getPageList(Integer puid, GroupPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_group_sd ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select id FROM `t_amazon_ad_group_sd` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) { //广告活动标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), argsList));
        }
        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String sql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "ad_group_id", false);
            if(StringUtils.isNotEmpty(sql)){
                whereSql.append(sql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));;
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        selectSql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonSdAdGroup.class);
    }

    @Override
    public List<AmazonSdAdGroup> getList(Integer puid, GroupPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }

        if(CollectionUtils.isNotEmpty(param.getGroupIds())){
            builder.inStrList("ad_group_id",param.getGroupIds().toArray(new String[]{}));
        }
        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            List<Object> argsList = new ArrayList<>();
            String sql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "ad_group_id", false);
            if(StringUtils.isNotEmpty(sql)){
                builder.custom(sql, argsList.toArray(), true);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue().trim());
            } else {
                builder.like(param.getSearchField(), param.getSearchValue());
            }
        }

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            builder.inStrList("name", param.getSearchValueList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }

        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                builder.greaterThanOrEqualTo("default_bid", param.getBidMin());
            }
            if (param.getBidMax() != null) {
                builder.lessThanOrEqualTo("default_bid", param.getBidMax());
            }
        }

        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid,builder.build());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String groupId, LocalDate localDate) {
        String sql = "update t_amazon_ad_group_sd set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_id`=?";
        getJdbcTemplate(puid).update(sql,new Object[]{localDate,puid,shopId,groupId});
    }

    @Override
    public void batchUpdateAmazonSdAdGroup(Integer puid, List<AmazonSdAdGroup> list, String type) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_group_sd` set update_id=?,update_time=now() ");
        if (Constants.CPC_SD_GROUP_BATCH_BID.equals(type)) {
            sql.append(",`default_bid` = ? ");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            sql.append(",`state`=? ");
        } else {
            return ;
        }
        sql.append(" where id = ? and puid=? and shop_id=? and ad_group_id =?");
        List<Object[]> batchArgs = Lists.newArrayList();
        List<Object> batchArg = new ArrayList<>();
        for (AmazonSdAdGroup amazonSdAdGroup : list) {
            batchArg.add(amazonSdAdGroup.getUpdateId());
            if (Constants.CPC_SD_GROUP_BATCH_BID.equals(type)) {
                batchArg.add(amazonSdAdGroup.getDefaultBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                batchArg.add(amazonSdAdGroup.getState());

            }
            batchArg.add(amazonSdAdGroup.getId());
            batchArg.add(amazonSdAdGroup.getPuid());
            batchArg.add(amazonSdAdGroup.getShopId());
            batchArg.add(amazonSdAdGroup.getAdGroupId());
            batchArgs.add(batchArg.toArray());
            batchArg.clear();
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }

    @Override
    public List<String> getAdGroupIdsByGroup(Integer puid, GroupPageParam param){
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.ad_group_id from t_amazon_ad_group_sd c where c.puid = ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if(param.getShopId() != null){
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignId()), argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", groupIds, argsList));
            }
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getMultiGroupId());
            if (groupIds.size() < 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.ad_group_id", groupIds, argsList));
            }
        }
        //广告标签筛选
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getGroupIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", param.getGroupIds(), argsList));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String subSql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), argsList, "c.ad_group_id", false);
            if(StringUtils.isNotEmpty(subSql)){
                whereSql.append(subSql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and c.name like ? ");
            argsList.add("%" + param.getSearchValue().trim() + "%");
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonSdAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and c.default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }
        sql.append(whereSql);
        List<String> adGroupIds = getJdbcTemplate(puid).queryForList(sql.toString(),argsList.toArray(),String.class);
        return adGroupIds;
    }

    @Override
    public List<String> getAdGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("ad_group_id", adGroupId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"ad_group_id", builder, String.class);
    }

    @Override
    public List<AmazonSdAdGroup> listByPuidAndCampaignIds(int puid, List<String> campaignIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("campaign_id", campaignIds.toArray(new String[0]))
                .build());
    }

    @Override
    public List<AmazonSdAdGroup> listByPuidAndCampaignId(int puid, int shopId, String campaignId) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .equalTo("campaign_id", campaignId)
                .build());
    }

    @Override
    public List<AmazonSdAdGroup> getInfoByCampaignIdAndGroupId(int puid, int shopId, String campaignId, String groupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId);
        if (StringUtils.isNotEmpty(groupId)) {
            builder.equalTo("ad_group_id", groupId);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public Page<AmazonSdAdGroup> getPageListByStrategy(AdStrategyGroupParam param) {
        StringBuilder selectSql = new StringBuilder("select g.puid,g.shop_id,g.marketplace_id,g.ad_group_id,g.campaign_id,g.name,g.default_bid,g.serving_status,g.state,g.create_time " +
                " from t_amazon_ad_group_sd g left join t_amazon_ad_campaign_all c on (g.puid = c.puid and g.shop_id and g.marketplace_id = c.marketplace_id and g.campaign_id = c.campaign_id)");
        StringBuilder countSql = new StringBuilder("select count(g.id) from t_amazon_ad_group_sd g left join t_amazon_ad_campaign_all c on (g.puid = c.puid and g.shop_id and g.marketplace_id = c.marketplace_id and g.campaign_id = c.campaign_id)");
        StringBuilder whereSql = new StringBuilder(" where g.puid=? and g.shop_id=? and g.ad_group_id is not null and g.is_state_bidding = 0 and c.state in ('enabled','paused')");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getState())) {
            whereSql.append(" and g.state =  ? ");
            argsList.add(param.getState());
        } else {
            whereSql.append(" and g.state in ('enabled','paused')");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("g.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getAdGroupName())) {
            whereSql.append(" and g.name like ? ");
            argsList.add("%" + param.getAdGroupName() + "%");
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("g.serving_status", param.getServingStatusList(), argsList));
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        Object[] args = argsList.toArray();
        return this.getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonSdAdGroup.class);
    }

    @Override
    public List<String> queryAdGroupIdList(ControlledObjectParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    public void updatePricing(Integer puid, Integer shopId, String adGroupId, Integer isPricing, Integer pricingState, int updateId) {
        String sql = "update t_amazon_ad_group_sd set is_state_bidding=?,pricing_state_bidding=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_group_id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{isPricing, pricingState, updateId, puid, shopId, adGroupId});
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<String> getSdGroupIdListByParamAndIds(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id from t_amazon_ad_group_sd ");
        sb.append(this.getSdGroupPageWhereSql(puid, param, adGroupIdList, argsList));
        return getJdbcTemplate(puid).queryForList(sb.toString(), argsList.toArray(), String.class);
    }

    @Override
    public List<AllGroupOrderBo> getSdGroupIdAndOrderFieldList(Integer puid, GroupPageParam param, List<String> adGroupIdList, String orderField) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ad_group_id id")
                .append(StringUtils.isNotBlank(orderField) ? "," + orderField + " orderField" : "")
                .append(" from t_amazon_ad_group_sd ");
        sb.append(this.getSdGroupPageWhereSql(puid, param, adGroupIdList, argsList));
        return getJdbcTemplate(puid).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AllGroupOrderBo.class));
    }

    @Override
    public Page<GroupInfoPageVo> getAllSdGroupPage(Integer puid, GroupPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer countSql = new StringBuffer("SELECT COUNT(*) FROM t_amazon_ad_group_sd ");
        StringBuffer selectSql = new StringBuffer("SELECT id, 'sd' as type,puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, campaign_id campaignId, name name, tactic, profile_id profileId, state state, serving_status servingStatus, ");
        selectSql.append(" default_bid defaultBid, create_id createId, bid_optimization bidOptimization, creative_type creativeType FROM t_amazon_ad_group_sd ");
        String whereSql = getSdGroupPageWhereSql(puid, param, null, argsList);
        selectSql.append(whereSql);
        AdGroupDefaultOrderEnum orderEn = AdGroupDefaultOrderEnum.getAdGroupDefaultOrderEnumByKey(param.getOrderField());
        if (StringUtils.isNotBlank(param.getOrderType()) && Objects.nonNull(orderEn)) {
            selectSql.append(" order by ").append(orderEn.getOrderField()).append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? " desc" : "").append(" ,id desc ");
        } else {
            selectSql.append(" order by id desc ");
        }
        countSql.append(whereSql);//check 右括号
        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, GroupInfoPageVo.class);
    }

    @Override
    public List<GroupInfoPageVo> getSdGroupPageVoListByGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT id, 'sd' as type, puid, shop_id shopId, marketplace_id marketplaceId, ad_group_id adGroupId, campaign_id campaignId, name name, profile_id profileId, state state, serving_status servingStatus, ");
        selectSql.append(" default_bid defaultBid, create_id createId, tactic tactic, bid_optimization bidOptimization, creative_type creativeType");
        selectSql.append(" FROM t_amazon_ad_group_sd g ");
        selectSql.append(" where g.puid = ? and g.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupIdList)) {
            selectSql.append(SqlStringUtil.dealInList("g.ad_group_id", adGroupIdList, argsList));
            selectSql.append(" order by field(ad_group_id, ").append(StringUtil.joinString(adGroupIdList)).append(")");
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GroupInfoPageVo.class));

    }

    private String getSdGroupPageWhereSql(Integer puid, GroupPageParam param, List<String> groupIdList, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId(), ",");
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), argsList));
        }
        // 广告组Id查询
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId(), ",");
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getMultiGroupId())){
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", StringUtil.splitStr(param.getMultiGroupId()), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(groupIdList)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIdList, argsList));
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> status = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", status, argsList));
        }
        //广告组查询
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and name = ? ");
                argsList.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and ").append(param.getSearchField()).append(" like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("name", param.getSearchValueList(), argsList));
        }
        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ?");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ?");
                argsList.add(param.getBidMax());
            }
        }
        return whereSql.toString();
    }



    @Override
    public List<String> getAdCampaignIdsByGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"campaign_id", builder, String.class);
    }

    @Override
    public List<String> getAdCampaignIdsByGroupIds(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("marketplace_id", marketplaceIds.toArray())
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid,"campaign_id", builder, String.class);
    }

    @Override
    public AmazonSdAdGroup getByAdGroupId(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<String> queryAdGroupIdList(ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<AmazonSdAdGroup> getByShopIdsAndGroupIds(Integer puid, List<Integer> shopIds, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT 'sd' as `type`, ad_group_id, campaign_id, shop_id, marketplace_id, name");
        selectSql.append(" FROM t_amazon_ad_group_sd g ");
        selectSql.append(" where g.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIds)) {
            selectSql.append(SqlStringUtil.dealInList("g.shop_id", shopIds, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupIds)) {
            selectSql.append(SqlStringUtil.dealInList("g.ad_group_id", adGroupIds, argsList));
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(AmazonSdAdGroup.class));
    }

    @Override
    public List<AmazonSdAdGroup> getGroupByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> adGroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT ad_group_id, campaign_id, shop_id, marketplace_id, name, tactic, default_bid, bid_optimization");
        selectSql.append(" FROM t_amazon_ad_group_sd g ");
        selectSql.append(" where g.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIds)) {
            selectSql.append(SqlStringUtil.dealInList("g.shop_id", shopIds, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(adGroupIds)) {
            selectSql.append(SqlStringUtil.dealInList("g.ad_group_id", adGroupIds, argsList));
        }
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(AmazonSdAdGroup.class));
    }

    @Override
    public List<AmazonSdAdGroup> autoRuleAdGroup(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIdList, String state, String searchValue, List<String> servingStatusList) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopId);
        conditionBuilder.in("ad_group_id",groupIdList.toArray());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.in("campaign_id",campaignIds.toArray());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(servingStatusList)) {
            conditionBuilder.in("serving_status",servingStatusList.toArray());
        }
        if (StringUtils.isNotBlank(state)) {
            conditionBuilder.equalTo("state",state);
        }
        if (StringUtils.isNotBlank(searchValue)) {
            conditionBuilder.like("name",searchValue);
        }
        return listByCondition(puid, conditionBuilder.build());
    }

    @Override
    public void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message) {
        StringBuilder updateSql = new StringBuilder("update t_amazon_ad_group_sd ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and ad_group_id = ?");
        List<Object> args = Lists.newArrayList();
        if (RuleExecuteOperationType.restore.equals(message.getOperation())) {
            updateSql.append(" set default_bid=?");
            args.add(message.getRecoveryAdjustment().getExecuteValue());
        } else {
            if (RuleActionType.stateClose.equals(message.getPerformOperation().get(0).getRuleAction())) {
                updateSql.append(" set state=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            } else {
                updateSql.append(" set default_bid=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            }
        }
        updateSql.append(whereSql);
        args.add(message.getPuid());
        args.add(message.getShopId());
        args.add(message.getItemId());
        getJdbcTemplate(message.getPuid()).update(updateSql.toString(), args.toArray());
    }

    @Override
    public List<String> queryAutoRuleAdGroupIdList(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.equalTo("shop_id", param.getShopId());
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getCampaignIds())) {
            builder.in("campaign_id", param.getCampaignIds().toArray());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getItemIdList())) {
            builder.in("ad_group_id", param.getItemIdList().toArray());
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state", param.getState());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            builder.in("serving_status", param.getServingStatusList().toArray());
        }
        if (StringUtils.isNotEmpty(param.getSearchValue())) {
            builder.like("name", param.getSearchValue());
        }
        return listDistinctFieldByCondition(param.getPuid(), "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<String> getGroupIdByCampaignId(Integer puid, Integer shopId, List<CampaignIdWithAdTypeVo> sdCampaignIdList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(sdCampaignIdList)) {
            builder.in("campaign_id", sdCampaignIdList.toArray());
        }
        return listDistinctFieldByCondition(puid, "ad_group_id", builder.build(), String.class);
    }

    @Override
    public List<String> getIdListByCampaignIds(Integer puid, List<Integer> shopIds, List<String> campaignIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select ad_group_id from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        try {
            return getJdbcTemplate(puid).queryForList(sql.toString(), argsList.toArray(), String.class);
        } catch (Exception e) {
            logger.info("getIdListByCampaignIds error", e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<AmazonSdAdGroup> getNameByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> campaignList, List<String> groupIds, String groupName) {
        StringBuilder selectSql = new StringBuilder(" select ad_group_id, name, default_bid from t_amazon_ad_group_sd ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid= ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(campaignList)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignList, argsList));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        if (StringUtils.isNotEmpty(groupName)) {
            whereSql.append(" and name LIKE ?");
            argsList.add("%" + groupName +"%");
        }
        selectSql.append(whereSql);
        return getJdbcTemplate(puid).query(selectSql.toString(), argsList.toArray(), getRowMapper());
    }

    @Override
    public List<Map<String, Object>> getCampaignIdsByGroupIds(Integer puid, List<Integer> shopIds, Set<String> sdAdgroupIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select ad_group_id groupId, campaign_id campaignId from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(sdAdgroupIds)) {
            sql.append(SqlStringUtil.dealInList("ad_group_id", Arrays.asList(sdAdgroupIds.toArray()), argsList));
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonSdAdGroup> getAdGroupByIds(Integer puid, Integer shopId, List<String> groupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIds.toArray(new String[] {}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<DownloadCenterBaseDataBO> queryBaseData4DownloadByShopAdGroup(Integer puid, Integer shopId, List<String> adGroupIdList) {
        StringBuilder sql = new StringBuilder("select ad_group_id id, state from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and ad_group_id in ('").append(StringUtils.join(adGroupIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(DownloadCenterBaseDataBO.class), puid, shopId);
    }

}