package com.meiyunji.sponsored.service.multiple.targets.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdKeyword;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordSbDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordSb;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import com.meiyunji.sponsored.service.enums.SbMatchValueEnum;
import com.meiyunji.sponsored.service.multiple.targets.dto.GroupInfo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetDataDto;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetInfo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetSbOrderByEnum;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * sp关键词投放子类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class SbKeywordProcessor extends AbstractTargetProcessor {

    @Resource
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;

    @Resource
    private IOdsAmazonAdKeywordSbDao odsAmazonAdKeywordSbDao;

    @Resource
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;;

    @Override
    public void abstractCheckParam(TargetReqDto req) {
        if (StringUtils.isNotBlank(req.getMatchType())) {
            if (StringUtils.isBlank(SbMatchValueEnum.getMatchValue(req.getMatchType()))) {
                throw new SponsoredBizException("非法的匹配类型！");
            }
        }
        if (StringUtils.isNotBlank(req.getSearchField())) {
            KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(req.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
            if (searchFieldEnum == null) {
                throw new SponsoredBizException("非法的搜索词字段");
            }
        }
    }

    @Override
    public void abstractSetParam(TargetReqDto req) {
        // sb主题类型中英文转换
        if (StringUtils.isNotBlank(req.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(req.getSearchValue())) {
                req.setSearchValue(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(req.getSearchValue())) {
                req.setSearchValue(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES);
            }
        }
        // 设置ABA排名信息
        setAbaRankInfo(req);
    }

    @Override
    public Boolean abstractFilterTargetIds(TargetReqDto req) {
        if (StringUtils.isNotBlank(req.getWordRoot())) {
            List<String> keywordIdList = adManageTargetDorisDao.getKeywordIdListByWordRoot(req);
            if (CollectionUtils.isEmpty(keywordIdList)) {
                return true;
            }else{
                req.setWordRootKeywordIds(keywordIdList);
            }
        }
        return false;
    }

    @Override
    public void abstractBuildWhereSql(TargetReqDto req, StringBuilder whereSql, List<Object> argsList) {
        // 词根id筛选
        if (CollectionUtils.isNotEmpty(req.getWordRootKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("t.keyword_id", req.getWordRootKeywordIds(), argsList));
        }
        // 搜索词查询
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(req.getSearchType())) { //模糊搜索
                whereSql.append(" and lower(t.keyword_text) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(req.getSearchValue().toLowerCase()) + "%");
            } else {//默认精确
                List<String> listSearchValue = StringUtil.splitStr(req.getSearchValue().trim(), "%±%");
                if (listSearchValue.size() > 1) {
                    List<String> lowerCaseValue = new ArrayList<>();
                    for (String value : listSearchValue) {
                        lowerCaseValue.add(value.trim().toLowerCase());
                    }
                    whereSql.append(SqlStringUtil.dealInList("lower(t.keyword_text)", lowerCaseValue, argsList));
                } else {
                    whereSql.append("and lower(t.keyword_text) = ? ");
                    argsList.add(req.getSearchValue().trim().toLowerCase());
                }
            }
        }
        // 匹配类型搜索
        if (StringUtils.isNotBlank(req.getMatchType())) {
            whereSql.append(" and t.match_type = ? ");
            argsList.add(req.getMatchType());
        }
    }

    @Override
    public void abstractPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto) {
        // 获取ABA搜索词排名
        prepareAbaRank(req, export, CollectionUtil.newArrayList(dto.getTargetMap().values()), dto);
    }

    @Override
    public void abstractBuildParam(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        // sb主题投放转换
        if (MatchValueEnum.theme.getMatchType().equalsIgnoreCase(row.getMatchType())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase( row.getKeywordText())) {
                row.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase( row.getKeywordText())) {
                row.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
            }
        }
    }

    @Override
    public List<TargetInfo> abstractMysqlTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<AmazonSbAdKeyword> keywordList = amazonSbAdKeywordDao.getListKeywordByKeywordIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (AmazonSbAdKeyword amazonAdKeyword : keywordList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(amazonAdKeyword, TargetInfo.class);
                    targetInfo.setTargetId(amazonAdKeyword.getKeywordId());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<TargetInfo> abstractDorisTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 9000);
            for (List<String> targetIds : targetIdsList) {
                List<OdsAmazonAdKeywordSb> keywordList = odsAmazonAdKeywordSbDao.getByKeywordIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (OdsAmazonAdKeywordSb amazonAdKeyword : keywordList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(amazonAdKeyword, TargetInfo.class);
                    targetInfo.setTargetId(amazonAdKeyword.getKeywordId());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<GroupInfo> abstractGroupInfoList(TargetReqDto req, List<String> adGroupIdList) {
        List<GroupInfo> groupInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(adGroupIdList)){
            // 分批获取
            List<List<String>> adGroupIdsList = Lists.partition(adGroupIdList, 10000);
            for (List<String> adGroupIds : adGroupIdsList) {
                List<AmazonSbAdGroup> adGroupInfoList = amazonSbAdGroupDao.getListByShopIdsAndGroupIds(req.getPuid(), req.getShopIdList(), adGroupIds);
                for (AmazonSbAdGroup adGroup : adGroupInfoList) {
                    GroupInfo groupInfo = BeanUtil.copyProperties(adGroup, GroupInfo.class);
                    groupInfo.setDefaultBid(adGroup.getBid());
                    groupInfoList.add(groupInfo);
                }
            }
        }
        return groupInfoList;
    }

    @Override
    public List<String> excludeFiledList(TargetReqDto req) {
        return Lists.newArrayList("servingStatusName", "adSelfSaleNum", "adOtherSaleNum", "target","selectType","vcpm","newToBrandDetailPageViews","addToCart","addToCartRate","ecpAddToCart","detailPageViews");
    }

}
