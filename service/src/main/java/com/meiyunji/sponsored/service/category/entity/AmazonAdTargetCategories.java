package com.meiyunji.sponsored.service.category.entity;


import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

@Data
@DbTable("t_amazon_ad_target_categories")
public class AmazonAdTargetCategories extends BasePo {
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    @DbColumn("marketplace_id")
    private String marketplaceId;

    @DbColumn("category_id")
    private Long categoryId;

    @DbColumn("name")
    private String name;

    @DbColumn("path")
    private String path;

    @DbColumn("asin_count_range")
    private String asinCountRange;

    @DbColumn("parent_id")
    private Long parentId;

    @DbColumn("create_id")
    private Integer createId;

    @DbColumn("update_id")
    private Integer updateId;
}
