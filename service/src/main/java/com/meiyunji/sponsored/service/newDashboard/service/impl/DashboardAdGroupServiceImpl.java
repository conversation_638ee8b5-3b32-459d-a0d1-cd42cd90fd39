package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbGroupReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSdGroupReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.GroupShopAndMarketplaceDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdGroupService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;
import com.meiyunji.sponsored.service.util.SummaryReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: ys
 * @date: 2024/4/15 15:29
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdGroupServiceImpl implements IDashboardAdGroupService {

    @Autowired
    private IOdsAmazonAdGroupReportDao iOdsAmazonAdGroupReportDao;

    @Autowired
    private IOdsAmazonAdSbGroupReportDao iOdsAmazonAdSbGroupReportDao;

    @Autowired
    private IOdsAmazonAdSdGroupReportDao iOdsAmazonAdSdGroupReportDao;

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;

    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IExcelService excelService;

    @Override
    public DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page queryGroupCharts(DashboardCampaignOrGroupOrPortfolioReqVo req) {
        List<CampaignOrGroupOrPortfolioDto> resultList = getGroupInfoList(req);
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("get top group chars data list is null, queryField:{}, dataField:{}", req.getQueryField(), req.getDataField());
            return null;
        }
        //组装数据返回
        List<DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList> topList = resultList.stream().map(d -> {
            DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList.Builder vo = DashboardAdCampaignOrGroupOrPortfolioResponseVo
                    .Page.TopList.newBuilder();
            BeanUtils.copyProperties(d, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(d));
            vo.setCost(CalculateUtil.formatDecimal(d.getCost()));
            Optional.ofNullable(d.getShopId()).map(Integer::valueOf).ifPresent(vo::setShopId);
            Optional.ofNullable(d.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(d.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(d.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(d.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(d.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(d.getAcos()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(d.getRoas()).map(String::valueOf).ifPresent(vo::setRoas);
            Optional.ofNullable(d.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(d.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(d.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(d.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);
            Optional.ofNullable(d.getCostPercent()).ifPresent(vo::setCostPercent);
            Optional.ofNullable(d.getTotalSalesPercent()).ifPresent(vo::setTotalSalesPercent);
            Optional.ofNullable(d.getImpressionsPercent()).ifPresent(vo::setImpressionsPercent);
            Optional.ofNullable(d.getClicksPercent()).ifPresent(vo::setClicksPercent);
            Optional.ofNullable(d.getOrderNumPercent()).ifPresent(vo::setOrderNumPercent);
            Optional.ofNullable(d.getSaleNumPercent()).ifPresent(vo::setSaleNumPercent);
            return vo.build();
        }).collect(Collectors.toList());
        return PageUtils.getPageInfo(topList, req.getPageSize(), req.getPageNo());
    }


    private List<CampaignOrGroupOrPortfolioDto> getGroupInfoList(DashboardCampaignOrGroupOrPortfolioReqVo req) {
        //查询广告组top图表数据，且需要区分sp,sb,sd
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        List<String> portfolioIds = req.getPortfolioIds();
        String currency = req.getCurrency();
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        DashboardOrderByEnum orderByEn = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //先获取sp的图表数据
        List<CampaignOrGroupOrPortfolioDto> spTopList = querySpTopList(req, puid, shopIdList, marketplaceIdList, currency, limit, orderField, dataField, siteToday);
        //获取sb图表数据
        List<CampaignOrGroupOrPortfolioDto> sbTopList = querySbTopList(req, puid, shopIdList, marketplaceIdList, currency, limit, orderField, dataField, siteToday);
        //获取sd图表数据
        List<CampaignOrGroupOrPortfolioDto> sdTopList = querySdTopList(req, puid, shopIdList, marketplaceIdList, currency, limit, orderField, dataField, siteToday);

        List<CampaignOrGroupOrPortfolioDto> resultList = new ArrayList<>(spTopList);
        resultList.addAll(sbTopList);
        resultList.addAll(sdTopList);

        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        //还需要对结果进行排序，取前limit行数据
        Class<CampaignOrGroupOrPortfolioDto> cla = CampaignOrGroupOrPortfolioDto.class;
        Field[] fields = getAllField(cla);
        Map<String, Field> fieldMap = Arrays.stream(fields).peek(f -> f.setAccessible(true))
                .filter(e-> !e.isSynthetic()).collect(Collectors.toMap(Field::getName, v1 -> v1));
        StringBuilder compareKey = new StringBuilder(dataField.getCode());
        compareKey.append(orderField.getSuffix());
        resultList = resultList.stream().sorted((o1, o2) -> {
            try {
                int result = 0;
                Field compareField = fieldMap.get(compareKey.toString());
                if (compareField == null) {
                    compareField = fieldMap.get(dataField.getCode());
                }
                if (Objects.nonNull(compareField)) {
                    Object val1 = compareField.get(o1);
                    Object val2 = compareField.get(o2);
                    if(Objects.isNull(val1) && Objects.isNull(val2) ){
                        result = 0;
                    } else if(Objects.nonNull(val1) && Objects.isNull(val2) ){
                        result = 1;
                    } else if(Objects.isNull(val1)){
                        result = -1;
                    } else {
                        if(String.class.isAssignableFrom(compareField.getType())){
                            String rateValue1 = val1.toString().replace("%", "");
                            String rateValue2 = val2.toString().replace("%", "");
                            if ("--".equalsIgnoreCase(rateValue2) || "--".equalsIgnoreCase(rateValue1)) {
                                return 0;
                            } else if("null".equalsIgnoreCase(rateValue2) && "null".equalsIgnoreCase(rateValue1)) {
                                result = 0;
                            } else if(!"null".equalsIgnoreCase(rateValue1) && "null".equalsIgnoreCase(rateValue2) ){
                                result = 1;
                            } else  if("null".equalsIgnoreCase(rateValue1)){
                                result = -1;
                            } else {
                                result = new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                            }
                        }
                        if(Integer.class.isAssignableFrom(compareField.getType())){
                            Integer compareVal1 = (Integer) val1;
                            Integer compareVal2 = (Integer) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(BigDecimal.class.isAssignableFrom(compareField.getType())){
                            BigDecimal compareVal1 = (BigDecimal) val1;
                            BigDecimal compareVal2 = (BigDecimal) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(Long.class.isAssignableFrom(compareField.getType())){
                            Long compareVal1 = (Long) val1;
                            Long compareVal2 = (Long) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                    }
                }
                if (result == 0) {
                    if(Objects.isNull(o1.getGroupId()) && Objects.isNull(o2.getGroupId()) ){
                        result = 0;
                    }
                    if(Objects.nonNull(o1.getGroupId()) && Objects.isNull(o2.getGroupId()) ){
                        result = 1;
                    }
                    if(Objects.isNull(o1.getGroupId())){
                        result = -1;
                    }
                    result = o1.getGroupId().compareTo(o2.getGroupId());
                    if (result > 1) {
                        result = 1;
                    }
                    if (result < 0) {
                        result = -1;
                    }
                }
                return result;
            } catch (IllegalAccessException e) {
                log.error("compare group list data error", e);
            }
            return 0;
        }).collect(Collectors.toList());
        int subLimit = Math.min(limit, resultList.size());

        //按请求字段进行升降序
        if (DashboardOrderByEnum.DESC == orderByEn) {
            Collections.reverse(resultList);
        }

        resultList = resultList.subList(0, subLimit);
        //是否需要排序
        if (StringUtils.isNotBlank(req.getListOrderField()) || StringUtils.isNotBlank(req.getListOrderType())) {
            OrderByUtil.sortedByOrderField(resultList, req.getListOrderField(), req.getListOrderType(), "groupId");
        }
        return resultList;
    }
    @Override
    public List<String> exportGroupCharts(DashboardCampaignOrGroupOrPortfolioReqVo reqVo) {
        List<CampaignOrGroupOrPortfolioDto> groupList = getGroupInfoList(reqVo);
        String url = writeExcelAndUpload(groupList, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export group charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private String writeExcelAndUpload(List<CampaignOrGroupOrPortfolioDto> groupList, DashboardCampaignOrGroupOrPortfolioReqVo reqVo) {
        groupList.forEach(dataDto -> CalculateAdDataUtil.fillDisplay4Export(dataDto, reqVo.getCurrency()));
        List<String> headers = new ArrayList<>(baseHeaderList);
        //计算导出列
        if (reqVo.getPercent() != 1) {
            headers = headers.stream().filter(x -> !x.contains("Percent")).collect(Collectors.toList());
        }
        if (reqVo.getMom() != 1) {
            headers = headers.stream().filter(x -> !x.contains("MomRate")).collect(Collectors.toList());
        }
        if (reqVo.getYoy() != 1) {
            headers = headers.stream().filter(x -> !x.contains("YoyRate")).collect(Collectors.toList());
        }

        String orderBy = Optional.ofNullable(DashboardOrderByEnum.orderByMap.get(reqVo.getOrderBy()))
                .map(DashboardOrderByEnum::getExportDesc).orElse("");
        String limitCnt = Optional.ofNullable(reqVo.getLimit()).map(String::valueOf).orElse("");
        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), groupList, fileName + orderBy + limitCnt,
                CampaignOrGroupOrPortfolioDto.class, headers, true);

    }

    private List<String> baseHeaderList = Arrays.asList("groupName","marketplaceName","shopName",
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate", "displayRoas", "roasMomRate", "roasYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");

    private static final String fileName = "广告组";

    private List<CampaignOrGroupOrPortfolioDto> querySpTopList(DashboardCampaignOrGroupOrPortfolioReqVo req, Integer puid,
                                                               List<Integer> shopIdList, List<String> marketplaceIdList,
                                                               String currency, int limit,
                                                               DashboardOrderByRateEnum orderField, DashboardDataFieldEnum dataField, List<String> siteToday) {
        List<Object> spArgsListFirst = Lists.newArrayList();
        List<Object> spArgsListSecond = Lists.newArrayList();
        String spSubSqlA = iOdsAmazonAdGroupReportDao.groupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getStartDate(), req.getEndDate(), spArgsListFirst, siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField);
        spArgsListSecond.addAll(spArgsListFirst);
        String spSubSqlB = iOdsAmazonAdGroupReportDao.groupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getYoyStartDate(), req.getYoyEndDate(), spArgsListFirst, null , null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
        Supplier<List<DashboardAdTopDataDto>> currentAndYoyList = () -> iOdsAmazonAdGroupReportDao.queryAdGroupYoyOrMomTop(spSubSqlA, spSubSqlB,
                spArgsListFirst, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<String> groupYoyListSup = Lists.newArrayList();
        Supplier<List<CampaignOrGroupOrPortfolioDto>> momList = () -> iOdsAmazonAdGroupReportDao
                .queryAdGroupCharts(puid, shopIdList, marketplaceIdList, groupYoyListSup, currency, req.getMomStartDate(),
                        req.getMomEndDate());
        String spSubSqlC = iOdsAmazonAdGroupReportDao.groupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getMomStartDate(),req.getMomEndDate(), spArgsListSecond, null , null,  req.getPortfolioIds(), req.getCampaignIds(),null, null);
        Supplier<List<DashboardAdTopDataDto>> currentAndMomList = () -> iOdsAmazonAdGroupReportDao.queryAdGroupYoyOrMomTop(spSubSqlA, spSubSqlC,
                spArgsListSecond, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<String> groupMomIdList = Lists.newArrayList();
        //还需要查同比
        Supplier<List<CampaignOrGroupOrPortfolioDto>> yoyList = () -> iOdsAmazonAdGroupReportDao.queryAdGroupCharts(puid, shopIdList,
                marketplaceIdList, groupMomIdList, currency, req.getYoyStartDate(),req.getYoyEndDate());
        Function<List<String>, Map<String, GroupShopAndMarketplaceDto>> groupNameMapFunc = groupIdList -> {
            List<AmazonAdGroup> spGroupInfoList = amazonAdGroupDao.listInfoByGroupInfoAndShopIdList(puid, shopIdList, groupIdList);
            return spGroupInfoList.parallelStream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, g -> GroupShopAndMarketplaceDto.builder()
                    .groupId(g.getAdGroupId())
                    .name(g.getName())
                    .type(g.getType())
                    .campaignId(g.getCampaignId())
                    .shopId(g.getShopId())
                    .marketplaceId(g.getMarketplaceId())
                    .build(), (e1, e2)-> e2));
        };
        return getGroupTopList(req.getPuid(), orderField, currentAndYoyList, groupYoyListSup, momList, currentAndMomList,
                groupMomIdList, yoyList, req.getYoyOverLimit(), groupNameMapFunc);
    }

    private List<CampaignOrGroupOrPortfolioDto> querySbTopList(DashboardCampaignOrGroupOrPortfolioReqVo req, Integer puid,
                                                               List<Integer> shopIdList, List<String> marketplaceIdList,
                                                               String currency, int limit,
                                                               DashboardOrderByRateEnum orderField, DashboardDataFieldEnum dataField, List<String> siteToday) {
        List<Object> spArgsListFirst = Lists.newArrayList();
        List<Object> spArgsListSecond = Lists.newArrayList();
        String sbSubSqlA = iOdsAmazonAdSbGroupReportDao.sbGroupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getStartDate(), req.getEndDate(), spArgsListFirst, siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField);
        spArgsListSecond.addAll(spArgsListFirst);
        String sbSubSqlB = iOdsAmazonAdSbGroupReportDao.sbGroupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getYoyStartDate(), req.getYoyEndDate(), spArgsListFirst, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
        Supplier<List<DashboardAdTopDataDto>> sbCurrentAndYoyList = () -> iOdsAmazonAdSbGroupReportDao.queryAdSbGroupYoyOrMomTop(sbSubSqlA, sbSubSqlB,
                spArgsListFirst, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<String> sbGroupYoyListSup = Lists.newArrayList();
        Supplier<List<CampaignOrGroupOrPortfolioDto>> momList = () -> iOdsAmazonAdSbGroupReportDao
                .queryAdSbGroupCharts(puid, shopIdList, marketplaceIdList, sbGroupYoyListSup, currency, req.getMomStartDate(),
                        req.getMomEndDate());
        String sbSubSqlC = iOdsAmazonAdSbGroupReportDao.sbGroupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getMomStartDate(),req.getMomEndDate(), spArgsListSecond, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
        Supplier<List<DashboardAdTopDataDto>> sbCurrentAndMomList = () -> iOdsAmazonAdSbGroupReportDao.queryAdSbGroupYoyOrMomTop(sbSubSqlA, sbSubSqlC,
                spArgsListSecond, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<String> sbGroupMomIdList = Lists.newArrayList();
        //还需要查同比
        Supplier<List<CampaignOrGroupOrPortfolioDto>> yoyList = () -> iOdsAmazonAdSbGroupReportDao.queryAdSbGroupCharts(puid, shopIdList,
                marketplaceIdList, sbGroupMomIdList, currency, req.getYoyStartDate(),req.getYoyEndDate());
        Function<List<String>, Map<String, GroupShopAndMarketplaceDto>> groupNameMapFunc = groupIdList -> {
            List<AmazonSbAdGroup> sbGroupInfoList = amazonSbAdGroupDao.getGroupByShopIdsAndGroupIds(puid, shopIdList, groupIdList);
            return sbGroupInfoList.parallelStream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, g -> GroupShopAndMarketplaceDto.builder()
                    .groupId(g.getAdGroupId())
                    .name(g.getName())
                    .type(g.getType())
                    .campaignId(g.getCampaignId())
                    .shopId(g.getShopId())
                    .marketplaceId(g.getMarketplaceId())
                    .build()));
        };
        return getGroupTopList(req.getPuid(), orderField, sbCurrentAndYoyList, sbGroupYoyListSup, momList, sbCurrentAndMomList,
                sbGroupMomIdList, yoyList, req.getYoyOverLimit(), groupNameMapFunc);
    }

    private List<CampaignOrGroupOrPortfolioDto> querySdTopList(DashboardCampaignOrGroupOrPortfolioReqVo req, Integer puid,
                                                               List<Integer> shopIdList, List<String> marketplaceIdList,
                                                               String currency, int limit,
                                                               DashboardOrderByRateEnum orderField, DashboardDataFieldEnum dataField, List<String> siteToday) {
        List<Object> sdArgsListFirst = Lists.newArrayList();
        List<Object> sdArgsListSecond = Lists.newArrayList();
        String sdSubSqlA = iOdsAmazonAdSdGroupReportDao.sdGroupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getStartDate(), req.getEndDate(), sdArgsListFirst, siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField);
        sdArgsListSecond.addAll(sdArgsListFirst);
        String sdSubSqlB = iOdsAmazonAdSdGroupReportDao.sdGroupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getYoyStartDate(), req.getYoyEndDate(), sdArgsListFirst, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
        Supplier<List<DashboardAdTopDataDto>> sdCurrentAndYoyList = () -> iOdsAmazonAdSdGroupReportDao.queryAdSdGroupYoyOrMomTop(sdSubSqlA, sdSubSqlB,
                sdArgsListFirst, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<String> sdGroupYoyListSup = Lists.newArrayList();
        Supplier<List<CampaignOrGroupOrPortfolioDto>> momList = () -> iOdsAmazonAdSdGroupReportDao
                .queryAdSdGroupCharts(puid, shopIdList, marketplaceIdList, sdGroupYoyListSup, currency, req.getMomStartDate(),
                        req.getMomEndDate());
        String sdSubSqlC = iOdsAmazonAdSdGroupReportDao.sdGroupQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getMomStartDate(),req.getMomEndDate(), sdArgsListSecond, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null);
        Supplier<List<DashboardAdTopDataDto>> sdCurrentAndMomList = () -> iOdsAmazonAdSdGroupReportDao.queryAdSdGroupYoyOrMomTop(sdSubSqlA, sdSubSqlC,
                sdArgsListSecond, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
        List<String> sdGroupMomIdList = Lists.newArrayList();
        //还需要查同比
        Supplier<List<CampaignOrGroupOrPortfolioDto>> yoyList = () -> iOdsAmazonAdSdGroupReportDao.queryAdSdGroupCharts(puid, shopIdList,
                marketplaceIdList, sdGroupMomIdList, currency, req.getYoyStartDate(),req.getYoyEndDate());
        Function<List<String>, Map<String, GroupShopAndMarketplaceDto>> sdGroupNameMapFunc = groupIdList -> {
            List<AmazonSdAdGroup> sdGroupInfoList = amazonSdAdGroupDao.getByShopIdsAndGroupIds(puid, shopIdList, groupIdList);
            return sdGroupInfoList.parallelStream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, g -> GroupShopAndMarketplaceDto.builder()
                    .groupId(g.getAdGroupId())
                    .name(g.getName())
                    .type(g.getType())
                    .campaignId(g.getCampaignId())
                    .shopId(g.getShopId())
                    .marketplaceId(g.getMarketplaceId())
                    .build()));
        };
        return getGroupTopList(req.getPuid(), orderField, sdCurrentAndYoyList, sdGroupYoyListSup, momList, sdCurrentAndMomList,
                sdGroupMomIdList, yoyList, req.getYoyOverLimit(), sdGroupNameMapFunc);
    }

    private List<CampaignOrGroupOrPortfolioDto> getGroupTopList(Integer puid, DashboardOrderByRateEnum orderField,
                                                                Supplier<List<DashboardAdTopDataDto>> currentAndYoyListSup,List<String> groupYoyListSup,
                                                                Supplier<List<CampaignOrGroupOrPortfolioDto>> momListSup, Supplier<List<DashboardAdTopDataDto>> currentAndMomListSup,
                                                                List<String> groupMomListSup, Supplier<List<CampaignOrGroupOrPortfolioDto>> yoyListSup,
                                                                boolean yoyOverLimit, Function<List<String>, Map<String, GroupShopAndMarketplaceDto>> groupNameMapFunc) {
        //查询当前日期时间段内广告活动图表信息
        //当前时间段查询sql
        List<DashboardAdTopDataDto> currentAndSubList;
        List<CampaignOrGroupOrPortfolioDto> resultList = Lists.newArrayList();
        //如果是按占比排序，即当前时间段值/汇总值,先计算同比,或按同比排序，即当前时间段值/同比时间段值
        if (DashboardOrderByRateEnum.PERCENT == orderField || Stream.of(DashboardOrderByRateEnum.YOY_RATE,
                DashboardOrderByRateEnum.YOY_VALUE).anyMatch(r -> r == orderField)) {
            //同比sql
//            subSqlB = subBSup.get();
            currentAndSubList = currentAndYoyListSup.get();
            List<String> groupIdList = currentAndSubList.parallelStream().map(DashboardAdTopDataDto::getGroupId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(groupIdList)) {
                groupYoyListSup.addAll(groupIdList);
                //还需要查询环比
                List<CampaignOrGroupOrPortfolioDto> momGroupList = momListSup.get();
                Map<String, CampaignOrGroupOrPortfolioDto> momGroupMap = momGroupList.parallelStream()
                        .collect(Collectors.toMap(CampaignOrGroupOrPortfolioDto::getGroupId, v1 -> v1, (v1, v2)-> v2));
                resultList = currentAndSubList.stream().map(c -> {
                    //当前查询list已经包含了当期及同比的计算属性和汇总属性
                    //还需要计算环比占比，同比占比
                    CampaignOrGroupOrPortfolioDto momGroup = Optional.ofNullable(momGroupMap.get(c.getGroupId())).orElseGet(() -> {
                        CampaignOrGroupOrPortfolioDto tempDto = new CampaignOrGroupOrPortfolioDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    CampaignOrGroupOrPortfolioDto currentDto = new CampaignOrGroupOrPortfolioDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    CampaignOrGroupOrPortfolioDto yoyDto = convertBasicAndCalData(c);
                    CampaignOrGroupOrPortfolioDto summaryDto = convertSummaryData(c);
                    computeCampaignData(currentDto, yoyDto, momGroup, summaryDto, yoyOverLimit);
                    return currentDto;
                }).collect(Collectors.toList());
            }
        }

        //如果是按环比排序，即当前时间段值/环比时间段值
        if (Stream.of(DashboardOrderByRateEnum.MOM_RATE,
                DashboardOrderByRateEnum.MOM_VALUE).anyMatch(r -> r == orderField)) {
            currentAndSubList = currentAndMomListSup.get();
            List<String> campaignIdList = currentAndSubList.parallelStream().map(DashboardAdTopDataDto::getCampaignId).collect(Collectors.toList());
            groupMomListSup.addAll(campaignIdList);
            //还需要查同比
            List<CampaignOrGroupOrPortfolioDto> yoyList = yoyListSup.get();
            Map<String, CampaignOrGroupOrPortfolioDto> yoyMap = yoyList.parallelStream()
                    .collect(Collectors.toMap(CampaignOrGroupOrPortfolioDto::getGroupId, v1 -> v1));
            resultList = currentAndSubList.stream().map(c -> {
                CampaignOrGroupOrPortfolioDto yoyGroup = Optional.ofNullable(yoyMap.get(c.getGroupId())).orElseGet(() -> {
                    CampaignOrGroupOrPortfolioDto tempDto = new CampaignOrGroupOrPortfolioDto();
                    CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                    return tempDto;
                });
                CampaignOrGroupOrPortfolioDto currentDto = new CampaignOrGroupOrPortfolioDto();
                BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                CampaignOrGroupOrPortfolioDto monDto = convertBasicAndCalData(c);
                CampaignOrGroupOrPortfolioDto summaryDto = convertSummaryData(c);
                computeCampaignData(currentDto, yoyGroup, monDto, summaryDto, yoyOverLimit);
                return currentDto;
            }).collect(Collectors.toList());
        }

        //设置广告组名称
        List<String> adGroupIds = resultList.parallelStream().map(CampaignOrGroupOrPortfolioDto::getGroupId)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, GroupShopAndMarketplaceDto> groupInfoMap = groupNameMapFunc.apply(adGroupIds);
        //填充结果中的店铺名称和站点名称
        Set<Integer> shopIds = groupInfoMap.values().parallelStream().map(GroupShopAndMarketplaceDto::getShopId).collect(Collectors.toSet());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIds));
        Map<Integer, String> shopNameMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, ShopAuth::getName));
        resultList.forEach(r -> {
            GroupShopAndMarketplaceDto dto = groupInfoMap.get(r.getGroupId());
            if (Objects.nonNull(dto)) {
                Optional.ofNullable(dto.getName()).ifPresent(r::setGroupName);
                Optional.ofNullable(shopNameMap.get(dto.getShopId())).ifPresent(r::setShopName);
                Optional.ofNullable(dto.getShopId()).map(String::valueOf).ifPresent(r::setShopId);
                Optional.ofNullable(dto.getCampaignId()).map(String::valueOf).ifPresent(r::setCampaignId);
                Optional.ofNullable(dto.getType()).ifPresent(r::setType);
                Optional.ofNullable(dto.getMarketplaceId()).ifPresent(r::setMarketplaceId);
                Optional.ofNullable(SummaryReportUtil.getByMarketplaceId(dto.getMarketplaceId()))
                        .map(SummaryReportUtil::getMarketplaceCN).ifPresent(r::setMarketplaceName);
            }

        });
        return resultList;
    }

    private CampaignOrGroupOrPortfolioDto convertBasicAndCalData(DashboardAdTopDataDto subInfo) {
        CampaignOrGroupOrPortfolioDto dto = new CampaignOrGroupOrPortfolioDto();
        dto.setCost(subInfo.getSubCost());
        dto.setTotalSales(subInfo.getSubTotalSales());
        dto.setImpressions(subInfo.getSubImpressions());
        dto.setClicks(subInfo.getSubClicks());
        dto.setOrderNum(subInfo.getSubOrderNum());
        dto.setSaleNum(subInfo.getSubSaleNum());
        dto.setAcos(subInfo.getSubAcos());
        dto.setRoas(subInfo.getSubRoas());
        dto.setClickRate(subInfo.getSubClickRate());
        dto.setConversionRate(subInfo.getSubConversionRate());
        dto.setCpc(subInfo.getSubCpc());
        dto.setCpa(subInfo.getSubCpa());
        return dto;
    }

    private CampaignOrGroupOrPortfolioDto convertSummaryData(DashboardAdTopDataDto subInfo) {
        CampaignOrGroupOrPortfolioDto dto = new CampaignOrGroupOrPortfolioDto();
        dto.setCost(subInfo.getAllCost());
        dto.setTotalSales(subInfo.getAllTotalSales());
        Optional.ofNullable(subInfo.getAllImpressions()).map(BigDecimal::longValue).ifPresent(dto::setImpressions);
        Optional.ofNullable(subInfo.getAllClicks()).map(BigDecimal::intValue).ifPresent(dto::setClicks);
        Optional.ofNullable(subInfo.getAllOrderNum()).map(BigDecimal::intValue).ifPresent(dto::setOrderNum);
        Optional.ofNullable(subInfo.getAllSaleNum()).map(BigDecimal::intValue).ifPresent(dto::setSaleNum);
        return dto;
    }

    private void computeCampaignData(CampaignOrGroupOrPortfolioDto dto, CampaignOrGroupOrPortfolioDto yoyDto,
                                     CampaignOrGroupOrPortfolioDto momDto, CampaignOrGroupOrPortfolioDto summary, boolean yoyOverLimit) {
        if (Objects.isNull(dto)) {
            return;
        }

        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
        }else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalData(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflex(dto, yoyDto);//填充环比增长值
            CalculateAdDataUtil.calAdYoyDataReflex(dto, yoyDto);//填充环比增长率
        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdMomData(dto, momDto);//填充同比增长率
            CalculateAdDataUtil.calAdMomValueReflex(dto, momDto);//填充同比增长值
        }
        CalculateAdDataUtil.calAdPercentData(dto, summary);
    }

    private <T> Field[] getAllField(Class<T> cla) {
        Class clazz = cla;
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }
}
