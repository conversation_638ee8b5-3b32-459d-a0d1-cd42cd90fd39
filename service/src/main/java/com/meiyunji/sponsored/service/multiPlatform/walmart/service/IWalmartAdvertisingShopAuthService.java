package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;

import java.util.List;
import java.util.Map;

/**
 * @author: ys
 * @date: 2025/3/20 20:19
 * @describe:
 */
public interface IWalmartAdvertisingShopAuthService {

    Map<Integer, MultiPlatformShopAuth> getAdShopAuth(Integer puid, List<Integer> shopIdListReq);
}
