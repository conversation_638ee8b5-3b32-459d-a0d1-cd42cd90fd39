package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.cpc.dto.ProductReportParentAsinUpdateDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.doris.util.DorisJSONUtil;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AsinOrderNumDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AmazonAdProductReport
 *
 * <AUTHOR>
 */
@Repository
@Slf4j
public class AmazonAdProductReportDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdProductReport> implements IAmazonAdProductReportDao {

    @Autowired
    private DynamicRefreshNacosConfiguration nacosConfiguration;

    @Autowired
    private IDorisService dorisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertList(Integer puid, List<AmazonAdProductReport> list) {
        //插入原表
        insertListOriginAndHotTable(puid, list, getJdbcHelper().getTable());

        //写入开关开启且数据是95天内的，就插入热表
        if (nacosConfiguration.isHotTableWriteEnable()) {
            //筛选出95天内的数据
            List<AmazonAdProductReport> hotList = list.stream()
                .filter(k -> (StringUtils.isNotBlank(k.getCountDate())))
                .filter(k -> DateUtil.getDayBetween(DateUtil.strToDate(k.getCountDate(), DateUtil.PATTERN_YYYYMMDD), new Date()) <= com.meiyunji.sponsored.common.base.Constants.HOT_SAVE_DAYS)
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(hotList)) {
                //插入热表
                insertListOriginAndHotTable(puid, hotList, getHotTableName());
            }
        }

    }

    @Override
    public void insertDorisList(Integer puid, List<AmazonAdProductReport> list) {
        if (!dynamicRefreshConfiguration.verifyGroupAndAdReport(puid)){
            return;
        }
        try {
            String time = LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
            List<Map<String, Object>> map = list.stream().map(k -> {
                Map<String, Object> objectMap = DorisJSONUtil.dbObj2FieldMap(k);
                LocalDateTimeUtil.setDorisValue(objectMap, k.getCountDate(), time);
                return objectMap;
            }).collect(Collectors.toList());
            dorisService.saveDorisMapByRoutineLoad("doris_ods_t_amazon_ad_product_report", map);
        } catch (Exception e) {
            log.error("save doris kafka error = {}", e.getMessage());
        }
    }

    private void insertListOriginAndHotTable (Integer puid, List<AmazonAdProductReport> list, String tableName) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ");
        sql.append(tableName);
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,");
        sql.append("`ad_group_id`,`ad_id`,`count_date`,`sku`,`asin`,`ad_group_name`,`campaign_name`,`parent_asin`,`cost`,");
        sql.append(" `cost_rmb`,`cost_usd`,`total_sales`,`total_sales_rmb`,`total_sales_usd`,`ad_sales`,`ad_sales_rmb`,");
        sql.append("`ad_sales_usd`,`impressions`,`clicks`,`order_num`,`ad_order_num`,`sale_num`,`ad_sale_num`,`natural_sales`,");
        sql.append("`natural_sales_rmb`,`natural_sales_usd`,`natural_clicks`,`natural_order_num`,`create_time`,`update_time` )VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdProductReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCampaignId());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getAdId());
            argsList.add(report.getCountDate());
            argsList.add(report.getSku());
            argsList.add(report.getAsin());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getCampaignName());
            argsList.add(report.getParentAsin());
            argsList.add(report.getCost());
            argsList.add(report.getCostRmb());
            argsList.add(report.getCostUsd());
            argsList.add(report.getTotalSales());
            argsList.add(report.getTotalSalesRmb());
            argsList.add(report.getTotalSalesUsd());
            argsList.add(report.getAdSales());
            argsList.add(report.getAdSalesRmb());
            argsList.add(report.getAdSalesUsd());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getOrderNum());
            argsList.add(report.getAdOrderNum());
            argsList.add(report.getSaleNum());
            argsList.add(report.getAdSaleNum());
            argsList.add(report.getNaturalSales());
            argsList.add(report.getNaturalSalesRmb());
            argsList.add(report.getNaturalSalesUsd());
            argsList.add(report.getNaturalClicks());
            argsList.add(report.getNaturalOrderNum());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `ad_group_name`=values(ad_group_name),`campaign_name`=values(campaign_name), `sku`=values(sku), `asin`=values(asin), ")
                .append("`cost`=values(cost),cost_rmb=values(cost_rmb),cost_usd=values(cost_usd),`total_sales`=values(total_sales),`total_sales_rmb`=values(total_sales_rmb),`total_sales_usd`=values(total_sales_usd),")
                .append("`ad_sales`=values(ad_sales),`ad_sales_rmb`=values(ad_sales_rmb),`ad_sales_usd`=values(ad_sales_usd),`impressions`=values(impressions),`clicks`=values(clicks),")
                .append("`order_num`=values(order_num),`ad_order_num`=values(ad_order_num),`sale_num`=values(sale_num),`ad_sale_num`=values(ad_sale_num),`natural_sales`=values(natural_sales),`natural_sales_rmb`=values(natural_sales_rmb),`natural_sales_usd`=values(natural_sales_usd),")
                .append("`natural_clicks`=values(natural_clicks),`natural_order_num`=values(natural_order_num)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page getPageList(Integer puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,ad_id,ad_group_id,count_date,campaign_id,ad_group_name,campaign_name,")
                .append("`sku`,`asin`,`parent_asin`,")
                .append("sum(`cost`) cost,sum(`cost_rmb`) cost_rmb,sum(`cost_usd`) cost_usd,sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb,sum(`total_sales_usd`) total_sales_usd,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`sale_num`) order_num,sum(`ad_sale_num`) ad_order_num,sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd,")
                .append("sum(`natural_sales`) natural_sales, sum(`natural_sales_rmb`) natural_sales_rmb, sum(`natural_sales_usd`) natural_sales_usd,")
                .append("sum(`natural_clicks`) natural_clicks,sum(`natural_order_num`) natural_order_num,sum(`order_num`) sale_num,sum(`ad_order_num`) ad_sale_num FROM ");
        String queryTable = getTableNameByStartDate(search.getStart());
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ( select `ad_id` FROM ");
        countSql.append(queryTable);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if(StringUtils.isNotBlank(search.getSearchType())&&StringUtils.isNotBlank(search.getSearchValue())){
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            if("sku".equals(search.getSearchType())){
                whereSql.append(" and sku_ci like ?");
                argsList.add("%"+search.getSearchValue()+"%");
            }else if("asin".equals(search.getSearchType())){
                whereSql.append(" and asin = ?");
                argsList.add(search.getSearchValue());
            }else if("parentAsin".equals(search.getSearchType())){
                whereSql.append(" and parent_asin like ?");
                argsList.add("%"+search.getSearchValue()+"%");
            }
        }
        if (StringUtils.isNotBlank(search.getCampaignId())) {
            whereSql.append(" and campaign_id = ?");
            argsList.add(search.getCampaignId());
        }
        if (StringUtils.isNotBlank(search.getGroupId())) {
            whereSql.append(" and ad_group_id= ?");
            argsList.add(search.getGroupId());
        }
        whereSql.append(" group by shop_id,`campaign_id`,ad_group_id,`asin`");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){
            String orderField = ReportService.getOrderField(search.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,ad_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdProductReport.class);
    }


    @Override
    public AmazonAdProductReport getSumReport(Integer puid, Integer shopId, String marketplaceId, ReportParam param) {
        String fields = getFieldByTabType(param.getTabType(),false);
        if(StringUtils.isBlank(fields)){
            return null;
        }
        StringBuilder sql = new StringBuilder("SELECT asin, sku, sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd, sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd, sum(`ad_sales`) ad_sales,")
                .append("sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd, sum(`impressions`) impressions,")
                .append("sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num, sum(`ad_sale_num`) ad_sale_num,")
                .append("sum(`natural_sales`) natural_sales, sum(`natural_sales_rmb`) natural_sales_rmb, sum(`natural_sales_usd`) natural_sales_usd,")
                .append("sum(`natural_clicks`) natural_clicks,sum(`natural_order_num`) natural_order_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? and ").append(fields).append("=?");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        args.add(param.getTabId());
        if(StringUtils.isNotBlank(param.getCampaignId())){
            sql.append(" and campaign_id=?");
            args.add(param.getCampaignId());
        }
        if(StringUtils.isNotBlank(param.getGroupId())){
            sql.append(" and ad_group_id=?");
            args.add(param.getGroupId());
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdProductReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdProductReport> getSumReports(Integer puid, Integer shopId, String marketplaceId, ReportParam param) {
        String fields = getFieldByTabType(param.getTabType(),false);
        if(StringUtils.isBlank(fields)){
            return null;
        }
        StringBuilder sql = new StringBuilder("SELECT asin,count_date, sku, sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd, sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd, sum(`ad_sales`) ad_sales,")
                .append("sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd, sum(`impressions`) impressions,")
                .append("sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num, sum(`ad_sale_num`) ad_sale_num,")
                .append("sum(`natural_sales`) natural_sales, sum(`natural_sales_rmb`) natural_sales_rmb, sum(`natural_sales_usd`) natural_sales_usd,")
                .append("sum(`natural_clicks`) natural_clicks,sum(`natural_order_num`) natural_order_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=?  ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        sql.append(SqlStringUtil.dealInList(fields,param.getTabIdList(),args));
        if(StringUtils.isNotBlank(param.getCampaignId())){
            sql.append(" and campaign_id=?");
            args.add(param.getCampaignId());
        }
        if(StringUtils.isNotBlank(param.getGroupId())){
            sql.append(" and ad_group_id=?");
            args.add(param.getGroupId());
        }

        sql.append(" group by count_date,"+fields+ " ");


        HintManager hintManager = HintManager.getInstance();
        try {
            List<AmazonAdProductReport> list = getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
            return list;
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdProductReport> getListReportByDate(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate) {
        Date date = DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD);
        String sql = "select * from " + getTableNameByStartDate(date) + " where puid= ? and shop_id=? and marketplace_id=? and `count_date` >= ? and `count_date` <= ? and impressions > 0 and asin is not null";
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(startDate);
        args.add(endDate);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql,args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductReport> getTaskSumReport(Integer puid, Integer shopId, String marketplaceId, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder();
        sql.append("select sku, sum(`cost`) cost, sum(`total_sales`) total_sales, sum(`sale_num`) sale_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date >= ? and count_date < ? GROUP by sku");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(startDate);
        args.add(endDate);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductReport> getChartList(Integer puid, Integer shopId, String marketplaceId,ReportParam param) {
        String fields = getFieldByTabType(param.getTabType(),false);
        if(StringUtils.isBlank(fields)){
            return null;
        }
        StringBuilder sql = new StringBuilder("SELECT asin, sku, count_date,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd, sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd, sum(`ad_sales`) ad_sales,")
                .append("sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd, sum(`impressions`) impressions,")
                .append("sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num, sum(`ad_sale_num`) ad_sale_num,")
                .append("sum(`natural_sales`) natural_sales, sum(`natural_sales_rmb`) natural_sales_rmb, sum(`natural_sales_usd`) natural_sales_usd,")
                .append("sum(`natural_clicks`) natural_clicks,sum(`natural_order_num`) natural_order_num FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? and ").append(fields).append("=? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(param.getStartDate());
        args.add(param.getEndDate());
        args.add(param.getTabId());
        if(StringUtils.isNotBlank(param.getCampaignId())){
            sql.append(" and campaign_id=?");
            args.add(param.getCampaignId());
        }
        if(StringUtils.isNotBlank(param.getGroupId())){
            sql.append(" and ad_group_id=?");
            args.add(param.getGroupId());
        }
        sql.append(" group by count_date order by count_date");

        HintManager hintManager = HintManager.getInstance();
        try {
            return  getJdbcTemplate(puid, hintManager).query(sql.toString(),args.toArray(),getMapper());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page detailPageList(Integer puid, Integer shopId, String marketplaceId,ReportParam param,Page page) {
        String fields = getFieldByTabType(param.getTabType(),false);
        if(StringUtils.isBlank(fields)){
            return page;
        }
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,`ad_group_name`,`campaign_name`,asin,parent_asin, sku, count_date,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd, sum(`total_sales`) total_sales,")
                .append("sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd, sum(`ad_sales`) ad_sales,")
                .append("sum(`ad_sales_rmb`) ad_sales_rmb, sum(`ad_sales_usd`) ad_sales_usd, sum(`impressions`) impressions,")
                .append("sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num, sum(`ad_sale_num`) ad_sale_num,")
                .append("sum(`natural_sales`) natural_sales, sum(`natural_sales_rmb`) natural_sales_rmb, sum(`natural_sales_usd`) natural_sales_usd,")
                .append("sum(`natural_clicks`) natural_clicks,sum(`natural_order_num`) natural_order_num FROM ");
        String queryTable = getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD));
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*)").append(" from ( select count_date FROM ");
        countSql.append(queryTable);
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? and ")
                .append(fields).append("=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        argsList.add(param.getTabId());
        if(StringUtils.isNotBlank(param.getCampaignId())){
            whereSql.append(" and campaign_id=?");
            argsList.add(param.getCampaignId());
        }
        if(StringUtils.isNotBlank(param.getGroupId())){
            whereSql.append(" and ad_group_id=?");
            argsList.add(param.getGroupId());
        }

        whereSql.append(" group by count_date ");
        countSql.append(whereSql).append(") t");
        selectSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String orderField = ReportService.getOrderField(param.getOrderField(),true);
            if(StringUtils.isNotBlank(orderField)){
                selectSql.append(" order by ").append(orderField);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append(" desc");
                }
            }
        }else{
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdProductReport.class);
    }

    @Override
    public List<Map<String, Object>> getCampaignNames(int puid, Integer shopId, String marketplaceId, String tabType, String tabId) {
        String fields = getFieldByTabType(tabType,false);
        if(StringUtils.isBlank(fields)){
            return null;
        }
        StringBuilder sql = new StringBuilder("select campaign_id id,campaign_name name from t_amazon_ad_product_report where puid=? and shop_id=? and marketplace_id=? and ")
                .append(fields).append("=? GROUP BY campaign_id");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.toString(),new Object[]{puid,shopId,marketplaceId,tabId});
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<Map<String, Object>> getAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId, String tabType, String tabId) {
        String fields = getFieldByTabType(tabType,false);
        if(StringUtils.isBlank(fields)){
            return null;
        }
        StringBuilder sql = new StringBuilder("select ad_group_id id,ad_group_name name from t_amazon_ad_product_report where puid=? and shop_id=? and marketplace_id=? and campaign_id=? and ")
                .append(fields).append("=? GROUP BY ad_group_id");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.toString(),new Object[]{puid,shopId,marketplaceId,campaignId,tabId});
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<Map<String, Object>> getCampaignOrAdGroupNames(int puid, Integer shopId, String marketplaceId, String campaignId) {
        String sql;
        List<Object> arg = Lists.newArrayList();
        arg.add(puid);
        arg.add(shopId);
        arg.add(marketplaceId);
        if (StringUtils.isBlank(campaignId)) {
            sql = "select campaign_id id,campaign_name name from t_amazon_ad_product_report where puid=? and shop_id=? and marketplace_id=? GROUP BY campaign_id";
        } else {
            sql = "select ad_group_id id,ad_group_name name from t_amazon_ad_product_report where puid=? and shop_id=? and marketplace_id=? and campaign_id=? GROUP BY ad_group_id";
            arg.add(campaignId);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql,arg.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductReport> listSumReports(Integer puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> adIds) {
        String queryTable = getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD));
        String sql = "SELECT ad_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                " FROM "+ queryTable +" where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .in("ad_id", adIds.toArray())
                .groupBy("ad_id")
                .build();

        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductReport> listReports(Integer puid, Integer shopId, String startDate, String endDate, String adId) {
        String queryTable = getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD));
        String sql = "SELECT count_date,campaign_id,ad_group_id,ad_id,shop_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                " FROM " + queryTable + " where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("ad_id", adId)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductReport> getListByDate(Integer puid, Integer shopId, String marketplaceId, String startTime, String endTime, String filed) {
       ConditionBuilder.Builder builder =  new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo(filed, startTime)
                .lessThanOrEqualTo(filed, endTime);

        return listByCondition(puid,builder.build());
    }

    @Override
    public List<AmazonAdProductReport> getProductListByParentAsinIsNull(Integer puid, Integer shopId, String marketplaceId,  Date date) {
        //optimize sql
        String sql = "select * from t_amazon_ad_product_report where puid=? and shop_id=? and marketplace_id=? " +
                "and update_time > ? and (parent_asin is null or parent_asin = '') ";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), new Object[]{puid,shopId, marketplaceId, date});
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getSkuPageByDate(Integer puid, Integer shopId, String marketplaceId, long start, long limit, String dateStr) {
        StringBuilder sb = new StringBuilder(" select distinct sku from ").append(this.getJdbcHelper().getTable()).append(" where ");
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", dateStr)
                .orderBy("sku")
                .limitByOffset(start, limit)
                .build();
        sb.append(builder.getSql());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), builder.getValues(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchUpdateParentAsin(Integer puid, Integer shopId, List<AmazonAdProductReport> newList) {
        //更新总表
        batchUpdateParentAsinOrigin(puid, shopId, newList);
        if (nacosConfiguration.isHotTableWriteEnable()) {
            //更新hot表
            batchUpdateParentAsinHot(puid, shopId, newList);
        }
    }

    @Override
    public void batchUpdateParentAsin(Integer puid, Integer shopId, String marketplaceId, List<ProductReportParentAsinUpdateDto> updateList, String dateStr) {
        //更新总表
        batchUpdateParentAsinOrigin(puid, shopId, marketplaceId, updateList, dateStr);
        if (nacosConfiguration.isHotTableWriteEnable()) {
            //更新hot表
            batchUpdateParentAsinHot(puid, shopId, marketplaceId, updateList, dateStr);
        }
    }

    private void batchUpdateParentAsinOrigin(Integer puid, Integer shopId, List<AmazonAdProductReport> newList) {
        StringBuilder sql = new StringBuilder("update " + getJdbcHelper().getTable() + " set `parent_asin` = ? where puid= ? and shop_id= ? and id=?   ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdProductReport product : newList) {
            batchArg = new Object[]{
                    product.getParentAsin(),
                    puid,
                    shopId,
                    product.getId()
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    private void batchUpdateParentAsinOrigin(Integer puid, Integer shopId, String marketplaceId, List<ProductReportParentAsinUpdateDto> updateList, String dateStr) {
        StringBuilder sql = new StringBuilder("update " + getJdbcHelper().getTable() + " set `parent_asin` = ? where puid= ? and shop_id= ? and marketplace_id = ? and sku = ? and count_date >= ? ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (ProductReportParentAsinUpdateDto product : updateList) {
            batchArg = new Object[]{
                    product.getParentAsin(),
                    puid,
                    shopId,
                    marketplaceId,
                    product.getSku(),
                    dateStr
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    private void batchUpdateParentAsinHot(Integer puid, Integer shopId, String marketplaceId, List<ProductReportParentAsinUpdateDto> updateList, String dateStr) {
        StringBuilder sql = new StringBuilder("update " + getHotTableName() + " set `parent_asin` = ? where puid= ? and shop_id= ? and marketplace_id = ? and sku = ? and count_date >= ? ");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (ProductReportParentAsinUpdateDto product : updateList) {
            batchArg = new Object[]{
                    product.getParentAsin(),
                    puid,
                    shopId,
                    marketplaceId,
                    product.getSku(),
                    dateStr
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    private void batchUpdateParentAsinHot(Integer puid, Integer shopId, List<AmazonAdProductReport> newList) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getHotTableName());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`count_date`,`ad_id`,`parent_asin`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdProductReport report : newList) {
            sql.append(" (?, ?, ?, ?, ?, ?),");
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getAdId());
            argsList.add(report.getParentAsin());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `parent_asin`=values(parent_asin) ");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> listSumReportByAdIds(Integer puid, Integer shopId, String startStr, String endStr, AdProductPageParam param, List<String> adIds) {
        if (CollectionUtils.isEmpty(adIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select  'sp' as type, `ad_id`,sum(cost) `cost`,sum(impressions) `impressions`,sum(clicks) `clicks`,sum(total_sales) `total_sales`,sum(ad_sales)  `ad_sales`,sum(sale_num)  `order_num`,sum(ad_order_num) `ad_order_num`,sum(sale_num) `sale_num`,sum(ad_sale_num) `ad_sale_num`,sum(order_num) `sales_num`  ");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD))).append(" where puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("ad_id", adIds, argsList));

        whereSql.append("  and count_date >= ? and count_date <= ? group by ad_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .adId(re.getString("ad_id"))
                            .type(re.getString("type"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getSpReportByDate(Integer puid, Integer shopId, String startStr, String endStr, AdProductPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sp' `type`,c.ad_id ad_id, count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(" t_amazon_ad_product c join ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" r on r.puid=c.puid and r.shop_id=c.shop_id AND r.marketplace_id = c.marketplace_id and r.ad_id=c.ad_id where c.puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {
            //广告组id
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", groupIds, argsList));
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_id", param.getAdIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.asin",param.getListSearchValueNew(),argsList));
                }else {
                    whereSql.append(" and c.asin = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if(param.getListSearchValueNew().size() > 1){
                    whereSql.append(SqlStringUtil.dealInList("c.sku",param.getListSearchValueNew(),argsList));
                }else {
                    whereSql.append(" and c.sku like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim() + "%");
                }

            }
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? group by c.ad_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adId(re.getString("ad_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .type(re.getString("type"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> getSpReportByAdIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> adIdList) {
        if (CollectionUtils.isEmpty(adIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sp' `type`,count_date,sum(cost) cost, sum(total_sales) total_sales,sum(ad_sales) ad_sales,sum(impressions) impressions,");
        sql.append(" sum(clicks)  clicks,sum(order_num)  sales_num,sum(ad_order_num)  ad_order_num,sum(sale_num)  sale_num,sum(ad_sale_num) ad_sale_num from  ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startStr, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("ad_id", adIdList, argsList));

        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sales_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .type(re.getString("type"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getProductListByUpdateTime(Integer puid, Integer shopId, Date date) {
        String sql = "select ad_id from (select sum(`impressions`) impressions, ad_id from t_amazon_ad_product_report where " +
                " puid = ? and shop_id=?  and update_time > ? group by ad_id having impressions > 0) a ";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, new Object[]{puid,shopId,date}, String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdAsinProductDto> getAsinPageProductList(AdAsinPageParam param, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT asin, parent_asin, sku, campaign_id, campaign_name, ad_group_id, ad_group_name, SUM(order_num) as totalSaleNum, SUM(ad_order_num) as adSaleNum FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            sql.append("and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sql.append("and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(asinList)) {
            sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        }
        sql.append("group by asin, campaign_id, ad_group_id");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(sql.toString(), new RowMapper<AdAsinProductDto>() {
                @Override
                public AdAsinProductDto mapRow(ResultSet re, int i) throws SQLException {
                    AdAsinProductDto dto = AdAsinProductDto.builder()
                            .asin(re.getString("asin"))
                            .parentAsin(re.getString("parent_asin"))
                            .sku(re.getString("sku"))
                            .campaignId(re.getString("campaign_id"))
                            .campaignName(re.getString("campaign_name"))
                            .adGroupId(re.getString("ad_group_id"))
                            .adGroupName(re.getString("ad_group_name"))
                            .orderNum(Optional.ofNullable(re.getInt("totalSaleNum")).orElse(0))
                            .adSelfSaleNum(Optional.ofNullable(re.getInt("adSaleNum")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AdAsinProductDto getAsinPageProduct(AdAsinPageParam param) {
        List<Object> argsList = new ArrayList<>();
        List<AdAsinProductDto> list;
        StringBuilder sql = new StringBuilder("SELECT asin, parent_asin, sku, campaign_id, campaign_name, ad_group_id, ad_group_name, SUM(order_num) as totalSaleNum, SUM(ad_order_num) as adSaleNum FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            sql.append("and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sql.append("and count_date >= ? and count_date <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            sql.append("and campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (StringUtils.isNotBlank(param.getAdGroupId())) {
            sql.append("and ad_group_id = ? ");
            argsList.add(param.getAdGroupId());
        }
        sql.append("and asin = ? ");
        argsList.add(param.getAsin());
        HintManager hintManager = HintManager.getInstance();
        try {
            list = getJdbcTemplate(param.getPuid(), hintManager).query(sql.toString(), new RowMapper<AdAsinProductDto>() {
                @Override
                public AdAsinProductDto mapRow(ResultSet re, int i) throws SQLException {
                    AdAsinProductDto dto = AdAsinProductDto.builder()
                            .asin(re.getString("asin"))
                            .parentAsin(re.getString("parent_asin"))
                            .sku(re.getString("sku"))
                            .campaignId(re.getString("campaign_id"))
                            .campaignName(re.getString("campaign_name"))
                            .adGroupId(re.getString("ad_group_id"))
                            .adGroupName(re.getString("ad_group_name"))
                            .orderNum(Optional.ofNullable(re.getInt("totalSaleNum")).orElse(0))
                            .adSelfSaleNum(Optional.ofNullable(re.getInt("adSaleNum")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
            return list.size() > 0 && list != null ? list.get(0) : null;
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdAsinAggregateDto> getAsinAggregateDto(AdAsinPageParam param, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT SUM(sale_num) as totalSaleNum, SUM(ad_sale_num) as adSaleNum FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getMarketplaceId())) {
            sql.append("and marketplace_id = ? ");
            argsList.add(param.getMarketplaceId());
        }
        sql.append("and count_date >= ? and count_date <= ?");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(asinList)) {
            sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(sql.toString(), new RowMapper<AdAsinAggregateDto>() {
                @Override
                public AdAsinAggregateDto mapRow(ResultSet re, int i) throws SQLException {
                    AdAsinAggregateDto dto = AdAsinAggregateDto.builder()
                            .orderNum(Optional.ofNullable(re.getInt("totalSaleNum")).orElse(0))
                            .adSelfSaleNum(Optional.ofNullable(re.getInt("adSaleNum")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public AmazonAdProductReport getListByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String groupId) {
        Date date = new Date();
        String startTime = DateUtil.dateToStrWithFormat(DateUtil.addDay(date, -30), "yyyyMMdd");

        String sql = " SELECT asin,sum(ad_sales) ad_sales FROM " + getTableNameByStartDate(DateUtil.strToDate(startTime, DateUtil.PATTERN_YYYYMMDD)) + " WHERE puid = ? AND shop_id = ? AND  campaign_id=? and count_date >= ?  ";
        List<AmazonAdProductReport> list ;
        HintManager hintManager = HintManager.getInstance();

        try {
            if (StringUtils.isNotBlank(groupId)) {
                sql += " and ad_group_id=? group by asin,ad_sales ORDER BY ad_sales DESC LIMIT 1";
                list = getJdbcTemplate(puid, hintManager).query(sql, new Object[]{puid, shopId, campaignId, startTime, groupId}, getMapper());

            } else {
                sql += " group by asin,ad_sales ORDER BY ad_sales DESC LIMIT 1";
                list = getJdbcTemplate(puid, hintManager).query(sql, new Object[]{puid, shopId, campaignId, startTime}, getMapper());
            }

            return CollectionUtils.isEmpty(list) ? null : list.get(0);
        } finally {
            hintManager.close();
        }
    }

    /**
     * 汇总广告组数据
     * @param puid
     * @param shopId
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<AmazonAdProductReport> listSumReportsByAdGroup(Integer puid, Integer shopId, String marketplaceId, LocalDate startDate, LocalDate endDate) {
        StringBuilder sql = new StringBuilder("SELECT puid, " +
                "       shop_id, " +
                "       count_date, " +
                "       campaign_id, " +
                "       ad_group_id, " +
                "       campaign_name, " +
                "       ad_group_name, " +
                "       SUM(clicks)       clicks, " +
                "       SUM(impressions)  impressions, " +
                "       SUM(cost)         cost, " +
                "       SUM(ad_sales)     ad_sales, " +
                "       SUM(total_sales)  total_sales, " +
                "       SUM(order_num)    order_num, " +
                "       SUM(ad_order_num) ad_order_num, " +
                "       SUM(sale_num)     sale_num, " +
                "       SUM(ad_sale_num)  ad_sale_num " +
                " " +
                "FROM " +
                getTableNameByStartDate(Date.from(startDate.atStartOfDay(ZoneId.systemDefault()).toInstant())) +
                " WHERE puid = ? " +
                "  AND shop_id = ? " +
                "  AND marketplace_id = ? " +
                "  AND count_date >= ? " +
                "  AND count_date <= ? " +
                "GROUP BY puid, shop_id, count_date, campaign_id, ad_group_id, campaign_name, ad_group_name ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(shopId);
        args.add(marketplaceId);
        args.add(startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));
        args.add(endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")));

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), (rs, rowNum)-> {
                AmazonAdProductReport report = new AmazonAdProductReport();
                report.setPuid(rs.getInt("puid"));
                report.setShopId(rs.getInt("shop_id"));
                report.setCountDate(rs.getString("count_date"));
                report.setCampaignId(rs.getString("campaign_id"));
                report.setAdGroupId(rs.getString("ad_group_id"));
                report.setCampaignName(rs.getString("campaign_name"));
                report.setAdGroupName(rs.getString("ad_group_name"));
                report.setClicks(rs.getInt("clicks"));
                report.setImpressions(rs.getInt("impressions"));
                report.setCost(Optional.ofNullable(rs.getBigDecimal("cost")).orElse(BigDecimal.ZERO));
                report.setAdSales(Optional.ofNullable(rs.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO));
                report.setTotalSales(Optional.ofNullable(rs.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO));
                report.setOrderNum(rs.getInt("order_num"));
                report.setAdOrderNum(rs.getInt("ad_order_num"));
                report.setSaleNum(rs.getInt("sale_num"));
                report.setAdSaleNum(rs.getInt("ad_sale_num"));
                return report;
            }, args.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdProductReport> getReportByAdId(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String adId) {
        StringBuilder sql = new StringBuilder();
        sql.append("select * from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" where puid = ? and shop_id = ? and marketplace_id = ? and ad_id = ? and count_date >= ? and count_date <= ?");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), getRowMapper(), puid, shopId, marketplaceId, adId, startDate, endDate);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdProductReport> getSales(Integer puid, Integer shopId, String startDate, String endDate,List<String> asins, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder("SELECT puid,shop_id,marketplace_id,count_date,asin,campaign_id,");
        sql.append(" sum(sale_num) as sale_num,sum(ad_sale_num) as ad_sale_num");
        sql.append(" FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN_YYYYMMDD)));
        StringBuilder whereSql = new StringBuilder(" WHERE puid = ? and shop_id = ? and count_date >= ? and count_date <= ? ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(startDate);
        args.add(endDate);
        if (CollectionUtils.isNotEmpty(asins)) {
            whereSql.append(SqlStringUtil.dealInList("asin", asins, args));
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, args));
        }
        whereSql.append(" group by count_date");
        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid,hintManager).query(sql.toString(),getRowMapper(),args.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AsinOrderNumDto> getTopOrderNumAsinRecentLast7Day(InitAsinInfoReqVo reqVo) {
        StringBuilder sql = new StringBuilder("SELECT shop_id shopId, marketplace_id marketplaceId, asin, sum(ad_order_num) adOrderNum from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(reqVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        List<Object> args = Lists.newArrayList();
        initAsinWhereSql(reqVo, sql, args);
        sql.append(" group by shop_id, marketplace_id, asin order by adOrderNum desc limit 1");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(reqVo.getPuid(), hintManager).query(sql.toString(),  new BeanPropertyRowMapper<>(AsinOrderNumDto.class), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<InitAsinInfoDto> getInitAsinInfoList(InitAsinInfoReqVo reqVo) {
        StringBuilder sql = new StringBuilder("SELECT shop_id, marketplace_id, SUM(order_num) order_num from ");
        sql.append(sql.append(getTableNameByStartDate(DateUtil.strToDate(reqVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD))));
        List<Object> args = Lists.newArrayList();
        initAsinWhereSql(reqVo, sql, args);
        sql.append(" and asin = ?");
        args.add(reqVo.getAsin());
        sql.append(" group by shop_id, marketplace_id");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(reqVo.getPuid(), hintManager).query(sql.toString(),  new BeanPropertyRowMapper<>(InitAsinInfoDto.class), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByAsin(Integer puid, List<Integer> shopIdList, String marketPlaceId, String asin, String startDate, String endDate) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("asin", asin)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .build();
        String sql = "select `shop_id`, `campaign_id`, `ad_group_id`, `ad_id` from " + getJdbcHelper().getTable() + " where " + builder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdProductPerspectiveBO.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<InitOneAsinVo> getOneByTime(InitAsinInfoReqVo reqVo) {
        StringBuilder sql = new StringBuilder("SELECT `shop_id`, `marketplace_id`, `asin` from ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(reqVo.getStartDate(), DateUtil.PATTERN_YYYYMMDD)));
        List<Object> args = Lists.newArrayList();
        initAsinWhereSql(reqVo, sql, args);
        sql.append(" limit 1");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(reqVo.getPuid(), hintManager).query(sql.toString(),  new BeanPropertyRowMapper<>(InitOneAsinVo.class), args.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<AsinListDto> getAsinPageByPuidAndShopId(AsinListReqVo reqVo) {
        StringBuilder selectSql = new StringBuilder("select asin, parent_asin parentAsin, sku msku, shop_id shopId, marketplace_id marketplaceId from ").append(this.getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("select count(distinct asin) from ").append(this.getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id in (").append(StringUtils.join(reqVo.getShopIdList(), ",")).append(") ");
        whereSql.append(" and marketplace_id = ? ");

        List<Object> argsList = Lists.newArrayList();
        argsList.add(reqVo.getPuid());
        argsList.add(reqVo.getMarketplaceId());
        if (StringUtils.isNotEmpty(reqVo.getStart())) {
            whereSql.append(" and count_date >= ? ");
            argsList.add(reqVo.getStart());
        }
        if (StringUtils.isNotEmpty(reqVo.getEnd())) {
            whereSql.append(" and count_date <= ? ");
            argsList.add(reqVo.getEnd());
        }
        if (StringUtils.isNotBlank(reqVo.getSearchValue()) && StringUtils.isNotBlank(reqVo.getSearchType())) {
            AsinListReqVo.SearchTypeEnum searchTypeEnum = UCommonUtil.getByCode(reqVo.getSearchType(), AsinListReqVo.SearchTypeEnum.class);
            if (searchTypeEnum != null) {
                whereSql.append(" and ").append(searchTypeEnum.getField()).append(" like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(reqVo.getSearchValue()) + "%");
            }
        }
        if (StringUtils.isNotBlank(reqVo.getSearchType())) {
            if (AsinListReqVo.SearchTypeEnum.PARENT_ASIN.getCode().equals(reqVo.getSearchType())) {
                whereSql.append(" and parent_asin is not null and parent_asin != '' ");
            }
        }
        whereSql.append(" and asin is not null and asin != ''");
        selectSql.append(whereSql).append(" group by shop_id, asin, sku ");
        countSql.append(whereSql);
        selectSql.append(" order by asin ");
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(reqVo.getPuid(), reqVo.getPageNo(), reqVo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AsinListDto.class);
    }

    private void initAsinWhereSql(InitAsinInfoReqVo reqVo, StringBuilder sql, List<Object> args) {
        sql.append(" WHERE puid = ? ");
        args.add(reqVo.getPuid());
        sql.append(" and shop_id in ( ").append(StringUtils.join(reqVo.getShopIdList(), ",")).append(" ) ");
        if (StringUtils.isNotBlank(reqVo.getMarketplaceId())) {
            sql.append(" and marketplace_id = ? ");
            args.add(reqVo.getMarketplaceId());
        } else if (CollectionUtils.isNotEmpty(reqVo.getMarketplaceIdList())) {
            sql.append(" and marketplace_id in ( \"").append(StringUtils.join(reqVo.getMarketplaceIdList(), "\",\"")).append("\" ) ");
        }
        sql.append(" and count_date >= ? and count_date <= ? ");
        args.add(reqVo.getStartDate());
        args.add(reqVo.getEndDate());
    }


    private StringBuilder subWhereSql(AdProductPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /**************************************sp广告产品高级筛选新增查询指标*************************************/

            //CPA:广告花费/广告订单量
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) >= ?");
                argsList.add(param.getCpaMin());
            }

            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ROUND(ifnull(cost/sale_num,0), 4), 2) <= ?");
                argsList.add(param.getCpaMax());
            }

            //本广告产品订单量
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他产品广告订单量
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) >= ?");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_sale_num, 0) <= ?");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本广告产品销售额
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(param.getAdSalesMax());
            }
            //其他产品广告销售额
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(param.getAdOtherSalesMax());
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(sales_num, 0) >= ?");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(sales_num, 0) <= ?");
                argsList.add(param.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(param.getAdSelfSaleNumMax());
            }
            //其他广告产品销量
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(sales_num - ad_order_num, 0) >= ?");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(sales_num - ad_order_num, 0) <= ?");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 广告笔单价 广告销售额÷广告订单量×100%
            if (param.getAdvertisingUnitPriceMin() != null){
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null){
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }

        }
        return subWhereSql;
    }

    private String getFieldByTabType(String tabType, boolean select){
        if(Constants.PRODUCT_TYPE_PARENTASIN.equals(tabType)){
            return select?" parent_asin,":" parent_asin ";
        }else if(Constants.PRODUCT_TYPE_ASIN.equals(tabType)){
            return select?" asin,sku,parent_asin,":" asin ";
        }else if(Constants.PRODUCT_TYPE_SKU.equals(tabType)){
            return select?" asin,sku,parent_asin,":" sku ";
        }
        return null;
    }

    @Override
    public List<AmazonAdProductReport> getReportByAdIdsGroupByCountDate(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> adIds) {
        String sql = "SELECT count_date,campaign_id,ad_group_id,ad_id,shop_id,sum(`cost`) cost, sum(`cost_rmb`) cost_rmb, sum(`cost_usd`) cost_usd," +
                "sum(`total_sales`) total_sales, sum(`total_sales_rmb`) total_sales_rmb, sum(`total_sales_usd`) total_sales_usd," +
                "sum(`ad_sales`) ad_sales,sum(`ad_sales_rmb`) ad_sales_rmb,sum(`ad_sales_usd`) ad_sales_usd," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`order_num`) order_num, sum(`ad_order_num`) ad_order_num, sum(`sale_num`) sale_num," +
                "sum(`ad_sale_num`) ad_sale_num " +
                "FROM `t_amazon_ad_product_report` where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .in("ad_id", adIds.toArray(new String[]{}))
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, getMapper(), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getAdIdsByProduct(int puid, int shopId, String marketplaceId, String start, List<String> adId) {
        StringBuilder sql = new StringBuilder(" select distinct ad_id from " + getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)) + "");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and marketplace_id = ? and count_date >= ? and impressions > 0 ");
        List<Object> argsList = Lists.newArrayList(puid, shopId, marketplaceId, start);
        whereSql.append(SqlStringUtil.dealInList("ad_id", adId, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.append(whereSql).toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<ReportMonitorBo> getReportLevelMonitorBoList(List<ShopDTO> shopDTOS, AdTypeEnum adType, String startCountDate, String endCountDate) {
        List<ReportMonitorBo> resultBolist = new ArrayList<>();
        String productTable;
        //通过广告类型得到对应表名
        switch (adType) {
            case sp: {
                productTable = getTableNameByStartDateAndTableName(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_product_report");
            } break;
            case sd: {
                productTable = getTableNameByStartDateAndTableName(DateUtil.strToDate(startCountDate, DateUtil.PATTERN_YYYYMMDD), "t_amazon_ad_sd_product_report");
            } break;
            default:
                return resultBolist;
        }
        Map<Integer, List<Integer>> puidShopIdsMap = shopDTOS.stream().collect(Collectors.groupingBy(ShopDTO::getPuid, Collectors.mapping(ShopDTO::getShopId, Collectors.toList())));
        String finalProductTable = productTable;
        puidShopIdsMap.forEach((puid, shopIds) -> {
            if (CollectionUtils.isEmpty(shopIds)) {
                return;
            }
            StringBuilder sql = new StringBuilder();
            sql.append("select sum(cost) total_cost,sum(clicks) total_clicks,sum(impressions) total_impressions, puid, shop_id from ")
                .append(finalProductTable)
                .append(" where puid = ? and count_date between ? and ? ");
            List<Object> args = Lists.newArrayList(puid, startCountDate, endCountDate);
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args))
                .append(" group by shop_id ");
            HintManager hintManager = HintManager.getInstance();
            try {
                List<ReportMonitorBo> reportMonitorBoList = getJdbcTemplate(puid, hintManager).query(sql.toString(), args.toArray(), (row, i) -> ReportMonitorBo.builder()
                    .totalCost(row.getBigDecimal("total_cost"))
                    .totalClicks(row.getLong("total_clicks"))
                    .totalImpressions(row.getLong("total_impressions"))
                    .puid(row.getInt("puid"))
                    .shopId(row.getInt("shop_id"))
                    .build());
                resultBolist.addAll(reportMonitorBoList);
            } finally {
                hintManager.close();
            }
        });
        return resultBolist;
    }



    @Override
    public List<InvoiceProductDto> getInvoiceProductList(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end) {
        if (CollectionUtils.isEmpty(campaignId)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT sum(cost) cost, asin, sku, campaign_id, max(update_time) update_time FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        sql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(start);
        argsList.add(end);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        sql.append(" group by puid, shop_id, campaign_id, sku ");
        sql.append(" limit 100000 ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<InvoiceProductDto>() {
                @Override
                public InvoiceProductDto mapRow(ResultSet re, int i) throws SQLException {
                    InvoiceProductDto dto = InvoiceProductDto.builder()
                            .asin(re.getString("asin"))
                            .sku(re.getString("sku"))
                            .campaignId(re.getString("campaign_id"))
                            .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .updateTime(re.getTimestamp("update_time"))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }




    @Override
    public LocalDateTime getInvoiceMaxUpdateTime(Integer puid, Integer shopId, String marketplaceId, List<String> campaignId, String start, String end) {
        if (CollectionUtils.isEmpty(campaignId)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT max(update_time) FROM ");
        sql.append(getTableNameByStartDate(DateUtil.strToDate(start, DateUtil.PATTERN_YYYYMMDD)));
        sql.append(" WHERE puid = ? and shop_id = ? and marketplace_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        sql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(start);
        argsList.add(end);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        sql.append(" and  cost > 0 ");

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql.toString(), LocalDateTime.class, argsList.toArray());
        } finally {
            hintManager.close();
        }

    }
}