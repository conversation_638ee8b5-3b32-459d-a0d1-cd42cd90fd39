package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

@Getter
public enum WalmartOperationLogActionEnum {

    /**
     * 操作类型
     */
    ADD("ADD", "新增"),
    EDIT_STATE_OR_DELETE("editState", "编辑状态/删除"),
    EDIT_NAME("editName", "编辑名称"),
    EDIT_BID("editBid", "编辑竞价"),
    EDIT_DATE("editDate", "编辑日期"),
    EDIT_BUDGET("editBudget", "编辑预算"),
    EDIT_BID_STRATEGY("editBidStrategy", "编辑竞价策略"),
    EDIT_PLACEMENT_MULTIPLIER("editPlacementMultiplier", "编辑广告位竞价倍数"),
    EDIT_PLATFORM_MULTIPLIER("editPlatformMultiplier", "编辑平台竞价倍数")
    ;

    private String operationType;

    private String operationValue;

    WalmartOperationLogActionEnum(String operationType, String operationValue) {
        this.operationType = operationType;
        this.operationValue = operationValue;
    }

    public static String getValByOperationType(String operationType){
        WalmartOperationLogActionEnum[] values = values();
        for (WalmartOperationLogActionEnum value : values) {
            if(operationType.equalsIgnoreCase(value.getOperationType())){
                return value.getOperationValue();
            }
        }
        return "";
    }
}
