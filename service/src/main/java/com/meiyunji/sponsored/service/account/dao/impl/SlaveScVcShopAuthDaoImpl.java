package com.meiyunji.sponsored.service.account.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.SlaveBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ScAndVcShopAuth;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/**
 * ShopAuth
 *
 * <AUTHOR>
 */
@Repository
public class SlaveScVcShopAuthDaoImpl extends SlaveBaseDaoImpl<ScAndVcShopAuth> implements ISlaveScVcShopAuthDao {



    private final static String vcShopTableName = "t_vc_shop_auth";
    private final static String scShopTableName = "t_shop_auth";


    @Override
    public ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? and puid = ? ");

        args.add(id);
        args.add(puid);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? and puid = ? ");
        args.add(id);
        args.add(puid);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }



    @Override
    public List<ShopAuth> listAllByIds(int puid, List<Integer> shopIds) {


        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<String> getSellerIdByShopIds(List<Integer> shopIdList) {
        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select selling_partner_id  from " + scShopTableName + " ");
        StringBuilder vcSql = new StringBuilder("select selling_partner_id from " + vcShopTableName + " ");
        scSql.append(" where 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        vcSql.append(" where 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        return getJdbcTemplate().queryForList(scSql + " union all " + vcSql, String.class, args.toArray());
    }



    @Override
    public List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (puid != null) {
            vcSql.append(" and puid = ? ");
            args.add(puid);
        }

        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());

        if (shopId != null) {
            vcSql.append(" and id = ? ");
            args.add(shopId);
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1=1  ");
        if (puid != null) {
            scSql.append(" and puid = ? ");
            args.add(puid);
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());


        if (shopId != null) {
            scSql.append(" and id = ? ");
            args.add(shopId);
        }
        args.add(start);
        args.add(limit);


        return getJdbcTemplate().query(scSql + " union all " + vcSql + " limit ?, ? " , new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());


    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimitOrderByIdDesc(Integer puid, Integer shopId, int start, int limit) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (puid != null) {
            vcSql.append(" and puid = ? ");
            args.add(puid);
        }

        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());

        if (shopId != null) {
            vcSql.append(" and id = ? ");
            args.add(shopId);
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1=1  ");
        if (puid != null) {
            scSql.append(" and puid = ? ");
            args.add(puid);
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());


        if (shopId != null) {
            scSql.append(" and id = ? ");
            args.add(shopId);
        }
        String s = scSql + " union all " + vcSql;
        s = s + " order by id limit ?, ? ";


        args.add(start);
        args.add(limit);



        return getJdbcTemplate().query(s, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());

    }

    @Override
    public List<ShopByPuidDto> getAllValidAdShopByPuid(Integer puid) {


        List<Object> args = new ArrayList<>();
        StringBuilder scSql = new StringBuilder("select puid, id shopId, marketplace_id marketplaceId, 'SC' as `type` from " + scShopTableName + "  where ad_status != ? and puid = ?  ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        args.add(puid);


        StringBuilder vcSql = new StringBuilder("select puid, id shopId, marketplace_id marketplaceId, 'VC' as `type` from " + vcShopTableName + "  where ad_status != ? and puid = ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        args.add(puid);
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopByPuidDto.class),  args.toArray());

    }

    @Override
    public List<Integer> getAllValidAdShopPuidByLimit(int start, int limit) {
        StringBuilder sql = new StringBuilder("select distinct puid from  ");
        List<Object> args = new ArrayList<>(4);
        StringBuilder scSql = new StringBuilder(" select distinct puid puid from " + scShopTableName + "  where ad_status != ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        StringBuilder vcSql = new StringBuilder("select distinct puid puid from " + vcShopTableName + "  where ad_status != ?  ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        sql.append(" (").append(scSql).append("  union all ").append(vcSql).append(") p");
        sql.append(" order by puid limit ?, ?");
        args.add(start);
        args.add(limit);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }



    /**
     * 获取已授权对应marketplaceId的店铺
     */
    @Override
    public List<ShopAuth> getShopAuthByMarketplaceId(String marketplaceId) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        if (StringUtils.isNotBlank(marketplaceId)) {
            vcSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        if (StringUtils.isNotBlank(marketplaceId)) {
            scSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }

        return getJdbcTemplate().query(scSql + " union all " + vcSql + " limit 100 ", new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }







    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args));
        vcSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args));
        scSql.append(SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));

        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puids) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puids)) {
            vcSql.append(SqlStringUtil.dealInList("puid", puids, args));
        }
        vcSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        if (CollectionUtils.isNotEmpty(puids)) {
            scSql.append(SqlStringUtil.dealInList("puid", puids, args));
        }
        scSql.append(" and ad_status = ? ");
        args.add(ShopAdStatusEnum.AUTH.name());
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public ShopAuth getByShopAuth(String sellerId, String marketplaceId) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        if (StringUtils.isNotBlank(sellerId)) {
            vcSql.append(" and selling_partner_id = ? ");
            args.add(sellerId);
        }
        if (StringUtils.isNotBlank(marketplaceId)) {
            vcSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }

        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        if (StringUtils.isNotBlank(sellerId)) {
            scSql.append(" and selling_partner_id = ? ");
            args.add(sellerId);
        }
        if (StringUtils.isNotBlank(marketplaceId)) {
            scSql.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql + " limit 1 ", new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query)) {
            return query.get(0);
        } else {
            return null;
        }
    }


    @Override
    public ShopAuth getValidAdShopById(Integer puid, Integer shopId) {


        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? and id = ?  and ad_status = ? ");
        args.add(puid);
        args.add(shopId);
        args.add(ShopAdStatusEnum.AUTH.name());

        StringBuilder scSql = new StringBuilder(
                getScSql() + "  puid = ? and id = ?  and ad_status = ? ");
        args.add(puid);
        args.add(shopId);
        args.add(ShopAdStatusEnum.AUTH.name());
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql + " limit 1 ", new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query)) {
            return query.get(0);
        } else {
            return null;
        }
    }




    /**
     * 获取sc和vc店铺
     * @param puid
     * @param shopIds
     * @param adStatus
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus) {
        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(adStatus)) {
            vcSql.append(" and ad_status = ? ");
            args.add(adStatus);
        } else {
            vcSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            vcSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        }
        StringBuilder scSql = new StringBuilder(
                getScSql() + " puid = ? ");
        args.add(puid);
        if (StringUtils.isNotBlank(adStatus)) {
            scSql.append(" and ad_status  ? ");
            args.add(adStatus);
        } else {
            scSql.append(" and ad_status <> ? ");
            args.add(ShopAdStatusEnum.UNAUTH.name());
        }

        if (CollectionUtils.isNotEmpty(shopIds)) {
            scSql.append(SqlStringUtil.dealInList("id", shopIds, args));
        }
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }



    /**
     * 获取sc和vc店铺
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1=1 ");

        vcSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args) + SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1=1 ");
        scSql.append(SqlStringUtil.dealInList("selling_partner_id", sellerIds, args) + SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }


    /**
     * SC 和 VC 查询时共用同一个实体返回，要不然改的地方太多了，实在很难受
     *
     * @param id
     * @return
     */
    @Override
    public ShopAuth getScAndVcById(int id) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " id = ? ");

        args.add(id);
        StringBuilder scSql = new StringBuilder(
                getScSql() + " id = ? ");
        args.add(id);
        List<ShopAuth> query = getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
        if (CollectionUtils.isNotEmpty(query) && query.size() == 1) {
            return query.get(0);
        } else {
            return null;
        }
    }

    private String getScSql() {

        return "SELECT id, puid, name, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, sp_refresh_token spRefreshToken, sp_access_token spAccessToken, sp_auth_time spAuthTime,"
                + " status, create_id createId, update_id updateId, create_time createTime, update_time updateTime, ad_refresh_token adRefreshToken, ad_access_token adAccessToken, ad_auth_time adAuthTime, ad_status adStatus, 'SC' as `type` "
                + " FROM t_shop_auth "
                + " where ";
    }

    private String getVcSql() {

        return "SELECT id, puid, name, selling_partner_id sellingPartnerId, region, marketplace_id marketplaceId, refresh_token spRefreshToken, access_token spAccessToken, auth_time spAuthTime,"
                + " status, create_id createId, update_id updateId, create_time createTime, update_time updateTime, ad_refresh_token adRefreshToken, ad_access_token adAccessToken, ad_auth_time adAuthTime, ad_status adStatus, 'VC' as `type` "
                + " FROM t_vc_shop_auth "
                + " where ";
    }


    /**
     * SC 和 VC 查询时共用同一个实体返回，要不然改的地方太多了，实在很难受
     *
     * @param ids
     * @return
     */
    @Override
    public List<ShopAuth> getScAndVcByIds(List<Integer> ids) {

        List<Object> args = new ArrayList<>();
        StringBuilder vcSql = new StringBuilder(
                getVcSql() + " 1 = 1 ");
        vcSql.append(SqlStringUtil.dealInList("id", ids, args));
        StringBuilder scSql = new StringBuilder(
                getScSql() + " 1 = 1 ");
        scSql.append(SqlStringUtil.dealInList("id", ids, args));
        return getJdbcTemplate().query(scSql + " union all " + vcSql, new BeanPropertyRowMapper<>(ShopAuth.class), args.toArray());
    }

    @Override
    public List<Integer> getAllAdAuthShopPuid() {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select distinct puid from ").append(scShopTableName);
        sql.append(" where ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        sql.append(" union ");
        sql.append(" select distinct puid from ").append(vcShopTableName);
        sql.append(" where ad_status <> ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }


}