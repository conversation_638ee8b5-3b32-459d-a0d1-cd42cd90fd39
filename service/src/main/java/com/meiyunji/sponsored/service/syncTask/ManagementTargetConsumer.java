package com.meiyunji.sponsored.service.syncTask;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbTargetApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdNeTargetingApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdTargetingApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignNeTargetingApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcKeywordsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcNeKeywordsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.SbAdGroupTypeDto;
import com.meiyunji.sponsored.service.cpc.vo.SpAdGroupTypeDto;
import com.meiyunji.sponsored.service.enums.AdSbStateV3;
import com.meiyunji.sponsored.service.enums.SbAdGroupTypeEnum;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamLogService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamRedisCountService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorStreamTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorTypeEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.TargetingTypeEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.TargetingTypePartitionEnum;
import com.meiyunji.sponsored.service.syncTask.message.ManagementTargetStreamMessage;
import com.meiyunji.sponsored.service.syncTask.message.TargetStreamBaseMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ManagementTargetConsumer {

    @Resource
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcSdTargetingApiService sdTargetingApiService;

    @Autowired
    private CpcSdNeTargetingApiService sdNeTargetingApiService;

    @Autowired
    private CpcKeywordsApiService keywordsApiService;

    @Autowired
    private CpcNeKeywordsApiService neKeywordsApiService;

    @Autowired
    private CpcTargetingApiService targetingApiService;

    @Autowired
    private CpcCampaignNeTargetingApiService campaignNeTargetingApiService;

    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;

    @Autowired
    private IAmazonSbAdNeTargetingDao amazonSbAdNeTargetingDao;

    @Autowired
    private CpcSbTargetApiService cpcSbTargetApiService;

    @Autowired
    private CpcSbNeTargetApiService cpcSbNeTargetApiService;

    @Autowired
    private IAmazonManagementStreamTaskRetryService managementStreamTaskRetryService;

    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;

    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private ISlaveAmazonAdProfileDao slaveAmazonAdProfileDao;

    @Resource
    private ISlaveScVcShopAuthDao slaveShopAuthDao;
    @Autowired
    private IAmazonManagementStreamLogService amazonManagementStreamLogService;
    @Autowired
    private AdvertiseStrategyManagementTargetConsumer advertiseStrategyManagementTargetConsumer;
    @Autowired
    private IAmazonManagementStreamRedisCountService amazonManagementStreamRedisCountService;

    public void process(List<ManagementTargetStreamMessage> messages) throws Exception {
        Set<String> sellerIds = new HashSet<>();
        Set<String> marketplaceIds = new HashSet<>();
        //转成店铺维度数据
        log.info("targeting stream data, {}", JSON.toJSONString(messages));
        Map<String, List<ManagementTargetStreamMessage>> collect = new HashMap<>();
        if (CollectionUtils.isEmpty(messages)) {
            log.info("ad stream target message empty");
            return;
        }
        for (ManagementTargetStreamMessage message : messages) {
            sellerIds.add(message.getAdvertiserId());
            marketplaceIds.add(message.getMarketplaceId());
            collect.computeIfAbsent(getKey(message), key -> new ArrayList<>()).add(message);
        }
        Date nowDate = new Date();
        amazonManagementStreamLogService.printAllManagementStreamCount(MonitorTypeEnum.all, MonitorStreamTypeEnum.ad_targeting, nowDate, messages.size());
        //查询shopAuth
        List<ShopAuth> shopAuths = slaveShopAuthDao.getBySellerIdsAndMarketplaceIds(Lists.newArrayList(sellerIds), Lists.newArrayList(marketplaceIds));
        if (CollectionUtils.isEmpty(shopAuths)) {
            log.info("targeting stream not fund shopAuths,sellerId:{},marketplaceId:{}", StringUtils.join(sellerIds, ","), StringUtils.join(marketplaceIds, ","));
            return;
        }

        //查询所有的shopProfile
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<AmazonAdProfile> profilesByShopIds = slaveAmazonAdProfileDao.getProfilesByShopIds(shopIds);
        if (CollectionUtils.isEmpty(profilesByShopIds)) {
            log.info("campaign stream not fund profile, {}", StringUtils.join(shopIds, ","));
            return;
        }

        //sellerId+marketplaceId -- shopAuth映射map
        Map<String, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(this::getKey, Function.identity()));
        Map<Integer, AmazonAdProfile> adProfileMap = profilesByShopIds.stream().collect(Collectors.toMap(AmazonAdProfile::getShopId, Function.identity()));

        //遍历各店铺消息集合
        for (Map.Entry<String, List<ManagementTargetStreamMessage>> entry : collect.entrySet()) {
            ShopAuth shopAuth = shopAuthMap.get(entry.getKey());
            if (shopAuth == null) {
                continue;
            }
            AmazonAdProfile adProfile = adProfileMap.get(shopAuth.getId());
            if (adProfile == null) {
                continue;
            }

            List<ManagementTargetStreamMessage> managementAdStreamMessages = entry.getValue();
            amazonManagementStreamLogService.printPuidManagementStreamCount(shopAuth.getPuid(), shopAuth.getId(), MonitorTypeEnum.puid, MonitorStreamTypeEnum.ad_targeting, nowDate, managementAdStreamMessages.size());
            //处理当前店铺
            processTargetingMessage4Shop(shopAuth, adProfile, managementAdStreamMessages);
            //开启异步任务处理广告组strem触发
            try {
                CompletableFuture.runAsync(()->{
                    try {
                        advertiseStrategyManagementTargetConsumer.processGroupStrategy(shopAuth, adProfile, managementAdStreamMessages);
                    } catch (Exception e) {
                        log.error("puid:{} shop:{} 分时策略stream线程触发异常: ", shopAuth.getPuid(), shopAuth.getId(), e);
                    }
                }, ThreadPoolUtil.getStreamGroupStrategyProcessPool());
            } catch (Exception e) {
                log.error("puid:{} shop:{} 分时策略stream触发异常: ", shopAuth.getPuid(), shopAuth.getId(), e);
            }
        }
    }

    /**
     * 处理一个店铺的投放数据
     * @param shopAuth
     * @param shopTargetingMessageList
     */
    private void processTargetingMessage4Shop(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> shopTargetingMessageList) {

        //根据SP，SB，SD分组
        Map<String, List<ManagementTargetStreamMessage>> stringListMap = shopTargetingMessageList.stream().collect(Collectors.groupingBy(ManagementTargetStreamMessage::getAdProduct));

        for (Map.Entry<String, List<ManagementTargetStreamMessage>> e : stringListMap.entrySet()) {
            String k = e.getKey();
            List<ManagementTargetStreamMessage> messageList = e.getValue();
            AdProductEnum adProductEnum = AdProductEnum.getAdProductEnum(k);
            if (adProductEnum == null) {
                continue;
            }
            if (AdProductEnum.SPONSORED_PRODUCTS == adProductEnum) {
                //处理SP
                handleSPTargetingMessage(shopAuth, messageList);
            } else if (AdProductEnum.SPONSORED_BRANDS == adProductEnum) {
                //处理SB
                handleSBTargetingMessage(shopAuth, adProfile, messageList);
            } else if (AdProductEnum.SPONSORED_DISPLAY == adProductEnum) {
                //处理SD
                handleSDTargetingMessage(shopAuth, messageList);
            }

        }
    }

    /**
     * 处理sp投放数据，需要判断关键词、商品、以及对应是否是否定
     * @param shopAuth
     * @param spTargetingMessageList
     */
    private void handleSPTargetingMessage(ShopAuth shopAuth, List<ManagementTargetStreamMessage> spTargetingMessageList) {
        //根据投放类型和否定进行分组，同时收集所有的投放数据用于维护adGroupType
        log.info("targeting stream data, sp all targeting, {}", JSON.toJSONString(spTargetingMessageList));
        //分组
        Map<TargetingTypePartitionEnum, List<ManagementTargetStreamMessage>> spTargetTypeMessageMap = new HashMap<>();
        Map<String, SpAdGroupTypeDto> groupIdTypeMap = new HashMap<>();

        spTargetingMessageList.forEach(message -> {
            //关键词
            if (TargetingTypeEnum.KEYWORD.getTargetType().equals(message.getTargetType())) {
                if (message.isNegative()) {
                    //否词，根据广告组id是否为空区分是组还是活动的否词
                    spTargetTypeMessageMap.computeIfAbsent(StringUtils.isBlank(message.getAdGroupId()) ? TargetingTypePartitionEnum.SP_CAMPAIGN_NEKEYWORD : TargetingTypePartitionEnum.SP_NEKEYWORD, key -> new ArrayList<>()).add(message);
                    //否词可能是关键词词投放也可能是自动投放，根据CpcAdSyncServiceImpl#confirmAdGroupType的逻辑，也是需要从活动计算或关键词表是否有数据计算
                    //也就是依靠非否词来计算，自动投放一定会有投放可以识别出自动，但是如果关键词投放且只有否投我们也无法判断，所以否词这里就不做处理了
                } else {
                    spTargetTypeMessageMap.computeIfAbsent(TargetingTypePartitionEnum.SP_KEYWORD, key -> new ArrayList<>()).add(message);
                    //关键词投放则一定是关键词
                    groupIdTypeMap.putIfAbsent(message.getAdGroupId(), new SpAdGroupTypeDto(message.getCampaignId(), message.getAdGroupId(), Constants.GROUP_TYPE_KEYWORD));
                }
            } else {
                //商品
                if (message.isNegative()) {
                    //否定，根据广告组id是否为空区分是组还是活动的否定
                    spTargetTypeMessageMap.computeIfAbsent(StringUtils.isBlank(message.getAdGroupId()) ? TargetingTypePartitionEnum.SP_CAMPAIGN_NETARGETING : TargetingTypePartitionEnum.SP_NETARGETING, key -> new ArrayList<>()).add(message);
                    //否投可能是商品投放也可能是自动投放，根据CpcAdSyncServiceImpl#confirmAdGroupType的逻辑，也是需要从活动计算或投放表是否有数据计算
                    //也就是依靠非否投来计算，自动投放一定会有投放可以识别出自动，但是如果商品投放且只有否投我们也无法判断，所以否投这里就不做处理了
                } else {
                    spTargetTypeMessageMap.computeIfAbsent(TargetingTypePartitionEnum.SP_TARGETING, key -> new ArrayList<>()).add(message);
                    //非关键词投放则根据消息的TargetType判断是自动还是商品
                    if (TargetingTypeEnum.AUTO.getTargetType().equals(message.getTargetType())) {
                        groupIdTypeMap.putIfAbsent(message.getAdGroupId(), new SpAdGroupTypeDto(message.getCampaignId(), message.getAdGroupId(), Constants.GROUP_TYPE_AUTO));
                    } else {
                        if (message.getProductCategoryTarget().getTargetingClause().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())) {
                            groupIdTypeMap.putIfAbsent(message.getAdGroupId(), new SpAdGroupTypeDto(message.getCampaignId(), message.getAdGroupId(), Constants.GROUP_TYPE_KEYWORD));
                        } else {
                            groupIdTypeMap.putIfAbsent(message.getAdGroupId(), new SpAdGroupTypeDto(message.getCampaignId(), message.getAdGroupId(), Constants.GROUP_TYPE_TARGETING));
                        }

                    }
                }
            }
        });

        spTargetTypeMessageMap.forEach((k, v) -> {
            //按照亚马逊限制分片
            List<List<ManagementTargetStreamMessage>> partitionList = Lists.partition(v,  StreamConstants.SP_MAX_TARGET_IDS_COUNT);
            for (List<ManagementTargetStreamMessage> messageList : partitionList) {
                Set<String> targetIdSet = messageList.stream().map(ManagementTargetStreamMessage::getTargetId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(targetIdSet)) {
                    continue;
                }

                //活动上的否定投放参数不统一，单独处理
                if (TargetingTypePartitionEnum.SP_CAMPAIGN_NETARGETING.equals(k)) {
                    //活动上的否定投放
                    log.info("targeting stream data, sp campaign netargeting, {}", JSON.toJSONString(v));
                    List<String> targetIdList = new ArrayList<>(targetIdSet);
                    try {
                        campaignNeTargetingApiService.syncCampaignNeTargeting(shopAuth, null, targetIdList, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdList.size());
                    } catch (Exception e) {
                        log.error(String.format("targeting stream data, sp campaign netargeting error, campaign netargetIdList: %s", JSON.toJSONString(targetIdList)) , e);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.CAMPAIGN_NE_TARGET, targetIdList);
                    }
                    continue;
                }

                //其余投放参数为逗号分隔的参数
                String targetId = StringUtils.join(targetIdSet, ",");

                switch (k) {
                    case SP_KEYWORD:
                        //关键词投放
                        log.info("targeting stream data, sp keyword, {}", JSON.toJSONString(v));
                        try {
                            keywordsApiService.syncKeywords(shopAuth, null, null, targetId, null ,true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                        } catch (Exception e) {
                            log.error(String.format("targeting stream data, sp keyword error, keywordIdList: %s", targetId), e);
                            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.KEYWORD, new ArrayList<>(targetIdSet));
                        }
                        break;
                    case SP_NEKEYWORD:
                        //否定关键词
                        log.info("targeting stream data, sp nenekeyword, {}", JSON.toJSONString(v));
                        try {
                            neKeywordsApiService.syncNeKeywords(shopAuth, null, null, targetId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                        } catch (Exception e) {
                            log.error(String.format("targeting stream data, sp nenekeyword error, nekeywordIdList: %s", targetId), e);
                            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.NE_KEYWORD, new ArrayList<>(targetIdSet));
                        }
                        break;
                    case SP_CAMPAIGN_NEKEYWORD:
                        //活动上的否定关键词
                        log.info("targeting stream data, sp campaign nenekeyword, {}", JSON.toJSONString(v));
                        try {
                            neKeywordsApiService.syncCampaignNeKeywords(shopAuth, null, targetId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                        } catch (Exception e) {
                            log.error(String.format("targeting stream data, sp campaign nenekeyword error, campaign nekeywordIdList: %s", targetId), e);
                            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.CAMPAIGN_NE_KEYWORD, new ArrayList<>(targetIdSet));
                        }
                        break;
                    case SP_TARGETING:
                        //商品投放
                        log.info("targeting stream data, sp targeting, {}", JSON.toJSONString(v));
                        try {
                            targetingApiService.syncTargetings(shopAuth, null, null, targetId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                        } catch (Exception e) {
                            log.error(String.format("targeting stream data, sp targeting error, targetIdList: %s", targetId), e);
                            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.TARGET, new ArrayList<>(targetIdSet));
                        }

                        break;
                    case SP_NETARGETING:
                        //否定投放
                        log.info("targeting stream data, sp netargeting, {}", JSON.toJSONString(v));
                        try {
                            targetingApiService.syncNeTargetings(shopAuth, null, null, targetId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                        } catch (Exception e) {
                            log.error(String.format("targeting stream data, sp netargeting error, netargetIdList: %s", targetId), e);
                            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_PRODUCTS, AmazonStreamTaskTypeEnum.NE_TARGET, new ArrayList<>(targetIdSet));
                        }
                        break;
                }
            }
        });

        //维护adGroupType
        if (!groupIdTypeMap.isEmpty()) {
            cpcAdSyncService.confirmSpAdGroupTypeByTargetData(shopAuth, new ArrayList<>(groupIdTypeMap.values()));
        }
    }


    /**
     * 处理sb投放数据，需要判断关键词、商品、以及对应是否是否定
     * @param shopAuth
     * @param adProfile
     * @param sbTargetingMessageList
     */
    private void handleSBTargetingMessage(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> sbTargetingMessageList) {
        //根据投放类型和否定进行分组，同时收集所有的投放数据用于维护adGroupType
        Map<TargetingTypePartitionEnum, List<ManagementTargetStreamMessage>> sbTargetTypeMessageMap = new HashMap<>();
        List<SbAdGroupTypeDto> sbAdGroupTypeDtoList = new ArrayList<>();
        Set<String> groupIdSet = new HashSet<>();

        sbTargetingMessageList.forEach(message -> {
            SbAdGroupTypeEnum sbAdGroupTypeEnum = null;
            if (TargetingTypeEnum.KEYWORD.getTargetType().equals(message.getTargetType())) {
                sbTargetTypeMessageMap.computeIfAbsent(message.isNegative() ? TargetingTypePartitionEnum.SB_NEKEYWORD : TargetingTypePartitionEnum.SB_KEYWORD, key -> new ArrayList<>()).add(message);
                sbAdGroupTypeEnum = SbAdGroupTypeEnum.keyword;
            } else {
                sbTargetTypeMessageMap.computeIfAbsent(message.isNegative() ? TargetingTypePartitionEnum.SB_NETARGETING : TargetingTypePartitionEnum.SB_TARGETING, key -> new ArrayList<>()).add(message);
                sbAdGroupTypeEnum = SbAdGroupTypeEnum.product;
            }
            //收集数据用于维护adGroupType
            if (!groupIdSet.contains(message.getAdGroupId())) {
                sbAdGroupTypeDtoList.add(new SbAdGroupTypeDto(message.getCampaignId(), message.getAdGroupId(), sbAdGroupTypeEnum));
                groupIdSet.add(message.getAdGroupId());
            }
        });

        sbTargetTypeMessageMap.forEach((k, v) -> {

            //解析入库
            switch (k) {
                case SB_KEYWORD:
                    //关键词投放
                    log.info("targeting stream data, sb keyword, {}", JSON.toJSONString(v));
                    keywordHandler(shopAuth, adProfile, v);
                    break;
                case SB_NEKEYWORD:
                    //否定关键词
                    log.info("targeting stream data, sb nenekeyword, {}", JSON.toJSONString(v));
                    neKeywordHandler(shopAuth, adProfile, v);
                    break;
                case SB_TARGETING:
                    //商品投放
                    log.info("targeting stream data, sb targeting, {}", JSON.toJSONString(v));
                    handleSBTargeting(shopAuth, adProfile, v);
                    break;
                case SB_NETARGETING:
                    //否定投放
                    log.info("targeting stream data, sb netargeting, {}", JSON.toJSONString(v));
                    handleSBNeTargeting(shopAuth, adProfile, v);
                    break;
            }
        });

        //维护adGroupType
        cpcAdSyncService.confirmSbAdGroupTargetTypeByTargetData(shopAuth, sbAdGroupTypeDtoList);
    }

    private void keywordHandler(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> messageList){
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        List<AmazonSbAdKeyword> list = new ArrayList<>(messageList.size());
        try {
            for (ManagementTargetStreamMessage x : messageList) {
                AmazonSbAdKeyword keywordSb = AmazonSbAdKeyword.builder()
                        .puid(shopAuth.getPuid())
                        .shopId(shopAuth.getId())
                        .marketplaceId(shopAuth.getMarketplaceId())
                        .profileId(adProfile.getProfileId())
                        .keywordId(x.getTargetId())
                        .adGroupId(x.getAdGroupId())
                        .campaignId(x.getCampaignId())
                        .keywordText(x.getKeywordTarget().getKeyword())
                        .matchType(Optional.ofNullable(getKeywordMatchType(x.getKeywordTarget().getMatchType())).orElse(""))
                        .dataUpdateTime(x.getAudit().getLastUpdatedDateTime().toLocalDate())
                        .build();
                if (StringUtils.isNotBlank(x.getState())) {
                    keywordSb.setState(Objects.requireNonNull(AdSbStateV3.fromValue(x.getState())).getOldValue());
                }
                if (x.getBid() > 0) {
                    keywordSb.setBid(BigDecimal.valueOf(x.getBid()));
                }
                list.add(keywordSb);
            }
            amazonSbAdKeywordDao.batchInsertOrUpdate(shopAuth.getPuid(), list);
            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), list.size());
        } finally {

            //维护到重试表再次同步
            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(),
                    shopAuth.getId(),
                    AdProductEnum.SPONSORED_BRANDS,
                    AmazonStreamTaskTypeEnum.KEYWORD,
                    messageList.stream().map(TargetStreamBaseMessage::getTargetId).collect(Collectors.toList()));
        }
    }

    private void neKeywordHandler(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> messageList){
        if (CollectionUtils.isEmpty(messageList)) {
            return;
        }
        List<AmazonSbAdNeKeyword> list = new ArrayList<>(messageList.size());
        try {
            for (ManagementTargetStreamMessage x : messageList) {
                AmazonSbAdNeKeyword keywordSb = AmazonSbAdNeKeyword.builder()
                        .puid(shopAuth.getPuid())
                        .shopId(shopAuth.getId())
                        .marketplaceId(shopAuth.getMarketplaceId())
                        .profileId(adProfile.getProfileId())
                        .keywordId(x.getTargetId())
                        .adGroupId(x.getAdGroupId())
                        .campaignId(x.getCampaignId())
                        .keywordText(x.getKeywordTarget().getKeyword())
                        .matchType(Optional.ofNullable(getNeKeywordMatchType(x.getKeywordTarget().getMatchType())).orElse(""))
                        .build();
                if (StringUtils.isNotBlank(x.getState())) {
                    keywordSb.setState(Objects.requireNonNull(AdSbStateV3.fromValue(x.getState())).getOldValue());
                }
                list.add(keywordSb);
            }
            amazonSbAdNeKeywordDao.batchInsertOrUpdate(shopAuth.getPuid(), list);
            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), list.size());
        } finally {
            //维护到重试表再次同步
            managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(),
                    shopAuth.getId(),
                    AdProductEnum.SPONSORED_BRANDS,
                    AmazonStreamTaskTypeEnum.NE_KEYWORD,
                    messageList.stream().map(TargetStreamBaseMessage::getTargetId).collect(Collectors.toList()));
        }
    }

    private String getKeywordMatchType(String matchType) {
        if (StringUtils.isEmpty(matchType)) {
            log.error("amazon keyword match type response is empty");
            return "";
        }
        MatchValueEnum.getMatchValueEnumByMatchType(matchType);
        return Optional.ofNullable(MatchValueEnum.getMatchValueEnumByMatchType(matchType))
                .map(MatchValueEnum::getMatchType)
                .map(String::toLowerCase).orElse("");
    }

    private String getNeKeywordMatchType(String matchType) {
        if (StringUtils.isEmpty(matchType)) {
            log.error("amazon neKeyword match type response is empty");
            return "";
        }
        return NekeyMatchEnum.getMatchValueByAmazon(matchType);
    }

    /**
     * 处理sb投放
     * @param shopAuth
     * @param adProfile
     * @param sbTargetMessageList
     */
    private void handleSBTargeting(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> sbTargetMessageList) {

        //分组，ASIN投放可以直接入库(避免并发覆盖，同时写入定时任务表等定时任务再触发一次接口)，品类投放则因为stream给的是分类和品牌id，所以需要调用接口
        Map<Boolean, List<ManagementTargetStreamMessage>> isAsinTargetMap = sbTargetMessageList.stream()
                .filter(this::isValidTarget)
                .collect(Collectors.groupingBy(this::isAsinTarget));

        //asin投放
        List<ManagementTargetStreamMessage> asinTargetMessageList = isAsinTargetMap.get(true);
        if (CollectionUtils.isNotEmpty(asinTargetMessageList)) {
            convertAndSaveSbAsinTarget(shopAuth, adProfile, asinTargetMessageList);
        }

        //分类和细化投放
        List<ManagementTargetStreamMessage> categoryTargetMessageList = isAsinTargetMap.get(false);
        if (CollectionUtils.isNotEmpty(categoryTargetMessageList)) {
            Set<String> groupIdSet = categoryTargetMessageList.stream().map(ManagementTargetStreamMessage::getAdGroupId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(groupIdSet)) {
                //分片
                List<List<String>> partitionList = Lists.partition(new ArrayList<>(groupIdSet),  StreamConstants.SB_MAX_TARGET_IDS_COUNT);
                partitionList.forEach(x -> {
                    String groupId = StringUtils.join(x, ",");
                    try {
                        cpcSbTargetApiService.syncTargets(shopAuth, null, groupId, null , true,  dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), x.size());
                    } catch (Exception e) {
                        log.error(String.format("targeting stream data, sb targeting error, groupIdList: %s", groupId), e);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_BRANDS, AmazonStreamTaskTypeEnum.TARGET, x);
                    }
                });
            }
        }
    }

    /**
     * 处理sb 投放数据，asin数据直接入库
     * @param shopAuth
     * @param adProfile
     * @param sbTargetMessageList
     */
    private void convertAndSaveSbAsinTarget(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> sbTargetMessageList) {

        List<AmazonSbAdTargeting> list = new ArrayList<>(sbTargetMessageList.size());
        Set<String> groupIdSet = new HashSet<>();

        try {
            for (ManagementTargetStreamMessage x : sbTargetMessageList) {

                AmazonSbAdTargeting amazonSbAdTargeting = new AmazonSbAdTargeting();
                amazonSbAdTargeting.setTargetId(x.getTargetId());
                amazonSbAdTargeting.setPuid(shopAuth.getPuid());
                amazonSbAdTargeting.setShopId(shopAuth.getId());
                amazonSbAdTargeting.setMarketplaceId(shopAuth.getMarketplaceId());
                amazonSbAdTargeting.setProfileId(adProfile.getProfileId());
                amazonSbAdTargeting.setCampaignId(x.getCampaignId());
                amazonSbAdTargeting.setAdGroupId(x.getAdGroupId());
                if (StringUtils.isNotBlank(x.getState())) {
                    amazonSbAdTargeting.setState(AdSbStateV3.fromValue(x.getState()).getOldValue());
                }
                if (x.getBid() > 0) {
                    amazonSbAdTargeting.setBid(BigDecimal.valueOf(x.getBid()));
                }

                amazonSbAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                amazonSbAdTargeting.setTargetText(StringUtils.remove(x.getProductCategoryTarget().getTargetingClause().split("=")[1], "\""));
                Expression expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(amazonSbAdTargeting.getTargetText());
                amazonSbAdTargeting.setExpression(JSON.toJSONString(expression));
                amazonSbAdTargeting.setResolvedExpression(amazonSbAdTargeting.getExpression());
                list.add(amazonSbAdTargeting);
                groupIdSet.add(x.getAdGroupId());
            }

            amazonSbAdTargetingDao.insertOrUpdate(shopAuth.getPuid(), list);
        } finally {
            //维护到重试表再次同步
            if (CollectionUtils.isNotEmpty(groupIdSet)) {
                managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(),
                        shopAuth.getId(),
                        AdProductEnum.SPONSORED_BRANDS,
                        AmazonStreamTaskTypeEnum.TARGET,
                        new ArrayList<>(groupIdSet));
            }
        }
    }

    /**
     * 处理sb否定投放
     * @param shopAuth
     * @param adProfile
     * @param sbNeTargetMessageList
     */
    private void handleSBNeTargeting(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> sbNeTargetMessageList) {

        //分组，ASIN投放可以直接入库(避免并发覆盖，同时写入定时任务表等定时任务再触发一次接口)，品类投放则因为stream给的是品牌id，所以需要调用接口
        Map<Boolean, List<ManagementTargetStreamMessage>> isAsinTargetMap = sbNeTargetMessageList.stream()
                .filter(this::isValidTarget)
                .collect(Collectors.groupingBy(this::isAsinTarget));

        //asin投放
        List<ManagementTargetStreamMessage> asinTargetMessageList = isAsinTargetMap.get(true);
        if (CollectionUtils.isNotEmpty(asinTargetMessageList)) {
            convertAndSaveSbAsinNeTarget(shopAuth, adProfile, asinTargetMessageList);
        }

        //处理否定品牌的投放
        List<ManagementTargetStreamMessage> categoryNeTargetMessageList = isAsinTargetMap.get(false);
        if (CollectionUtils.isNotEmpty(categoryNeTargetMessageList)) {
            Set<String> groupIdSet = categoryNeTargetMessageList.stream().map(ManagementTargetStreamMessage::getAdGroupId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(groupIdSet)) {
                //分片
                List<List<String>> partitionList = Lists.partition(new ArrayList<>(groupIdSet),  StreamConstants.SB_MAX_NE_TARGET_IDS_COUNT);
                partitionList.forEach(x -> {
                    String groupId = StringUtils.join(x, ",");
                    try {
                        cpcSbNeTargetApiService.syncNeTargets(shopAuth, null, groupId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                    } catch (Exception e) {
                        log.error(String.format("targeting stream data, sb netargeting error, groupIdList: %s", groupId), e);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_BRANDS, AmazonStreamTaskTypeEnum.NE_TARGET, x);
                    }
                });
            }
        }
    }

    /**
     * 处理sb 否定投放数据，asin数据直接入库
     * @param shopAuth
     * @param adProfile
     * @param sbNeTargetMessageList
     * @return
     */
    private void convertAndSaveSbAsinNeTarget(ShopAuth shopAuth, AmazonAdProfile adProfile, List<ManagementTargetStreamMessage> sbNeTargetMessageList) {

        List<AmazonSbAdNeTargeting> list = new ArrayList<>(sbNeTargetMessageList.size());
        Set<String> groupIdSet = new HashSet<>();

        try {
            for (ManagementTargetStreamMessage x : sbNeTargetMessageList) {

                AmazonSbAdNeTargeting amazonSbAdNeTargeting = new AmazonSbAdNeTargeting();
                amazonSbAdNeTargeting.setTargetId(x.getTargetId());
                amazonSbAdNeTargeting.setPuid(shopAuth.getPuid());
                amazonSbAdNeTargeting.setShopId(shopAuth.getId());
                amazonSbAdNeTargeting.setMarketplaceId(shopAuth.getMarketplaceId());
                amazonSbAdNeTargeting.setProfileId(adProfile.getProfileId());
                amazonSbAdNeTargeting.setCampaignId(x.getCampaignId());
                amazonSbAdNeTargeting.setAdGroupId(x.getAdGroupId());
                if (StringUtils.isNotBlank(x.getState())) {
                    amazonSbAdNeTargeting.setState(AdSbStateV3.fromValue(x.getState()).getOldValue());
                }

                amazonSbAdNeTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                amazonSbAdNeTargeting.setTargetText(StringUtils.remove(x.getProductCategoryTarget().getTargetingClause().split("=")[1], "\""));
                Expression expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(amazonSbAdNeTargeting.getTargetText());
                amazonSbAdNeTargeting.setExpression(JSON.toJSONString(expression));
                amazonSbAdNeTargeting.setResolvedExpression(amazonSbAdNeTargeting.getExpression());

                list.add(amazonSbAdNeTargeting);
                groupIdSet.add(x.getAdGroupId());
            }

            amazonSbAdNeTargetingDao.insertOrUpdate(shopAuth.getPuid(), list);
        } finally {

            //维护到重试表再次同步
            if (CollectionUtils.isNotEmpty(groupIdSet)) {
                managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(),
                        shopAuth.getId(),
                        AdProductEnum.SPONSORED_BRANDS,
                        AmazonStreamTaskTypeEnum.NE_TARGET,
                        new ArrayList<>(groupIdSet));
            }
        }
    }


    /**
     * 处理sd投放数据，只需要判断是否是否定
     * @param shopAuth
     * @param sdTargetingMessageList
     */
    private void handleSDTargetingMessage(ShopAuth shopAuth, List<ManagementTargetStreamMessage> sdTargetingMessageList) {
        //根据是否否定进行分组
        Map<Boolean, List<ManagementTargetStreamMessage>> sdMessageMap = sdTargetingMessageList.stream().collect(Collectors.groupingBy(ManagementTargetStreamMessage::isNegative));

        sdMessageMap.forEach((k, v) -> {
            //按照亚马逊限制分片
            List<List<ManagementTargetStreamMessage>> partitionList = Lists.partition(v, StreamConstants.SD_MAX_TARGET_IDS_COUNT);

            for (List<ManagementTargetStreamMessage> messageList : partitionList) {

                Set<String> targetIdSet = messageList.stream().map(ManagementTargetStreamMessage::getTargetId).collect(Collectors.toSet());
                if (CollectionUtils.isEmpty(targetIdSet)) {
                    continue;
                }
                String targetId =  StringUtils.join(targetIdSet, ",");

                if (k) {
                    //否定
                    log.info("targeting stream data, sd netargeting, {}", JSON.toJSONString(messageList));
                    try {
                        sdNeTargetingApiService.syncNeTargetings(shopAuth, null, null, targetId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                    } catch (Exception e) {
                        log.error(String.format("targeting stream data, sd netargeting process error, netargetIdList: %s", targetId), e);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_DISPLAY, AmazonStreamTaskTypeEnum.NE_TARGET, new ArrayList<>(targetIdSet));
                    }

                } else {
                    //投放
                    log.info("targeting stream data, sd targeting, {}", JSON.toJSONString(messageList));
                    try {
                        sdTargetingApiService.syncTargetings(shopAuth, null, null, targetId, null, true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                        amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(LocalDateTime.now(), targetIdSet.size());
                    } catch (Exception e) {
                        log.error(String.format("targeting stream data, sd targeting process error, netargetIdList: %s", targetId), e);
                        managementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), AdProductEnum.SPONSORED_DISPLAY, AmazonStreamTaskTypeEnum.TARGET, new ArrayList<>(targetIdSet));
                    }
                }
            }
        });
    }

    private String getKey(ManagementTargetStreamMessage targetingStreamMessage) {
        return targetingStreamMessage.getAdvertiserId() + "#" + targetingStreamMessage.getMarketplaceId();
    }

    private String getKey(ShopAuth shopAuth) {
        return shopAuth.getSellingPartnerId() + "#" + shopAuth.getMarketplaceId();
    }


    /**
     * 判断是否是有效的投放数据
     * @param message
     * @return
     */
    private boolean isValidTarget(ManagementTargetStreamMessage message) {
        if (Objects.isNull(message.getProductCategoryTarget()) || StringUtils.isAnyBlank(message.getTargetId(), message.getAdGroupId(), message.getCampaignId(), message.getProductCategoryTarget().getTargetingClause())) {
            return false;
        }
        return true;
    }


    private boolean isAsinTarget(ManagementTargetStreamMessage message) {
        String targetingClause = message.getProductCategoryTarget().getTargetingClause();
        if (!targetingClause.startsWith(Constants.TARGETING_TYPE_ASIN)) {
            return false;
        }
        String[] split = targetingClause.split("=");
        if (split.length != 2) {
            return false;
        }
        return true;
    }
}
