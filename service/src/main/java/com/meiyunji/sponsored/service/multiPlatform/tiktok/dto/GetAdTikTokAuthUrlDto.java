package com.meiyunji.sponsored.service.multiPlatform.tiktok.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GetAdTikTokAuthUrlDto {

    private Integer puid;

    private Integer uid;

    private Integer shopId;

    private String token;

    public GetAdTikTokAuthUrlDto(Integer puid, Integer uid, Integer shopId) {
        this.puid = puid;
        this.uid = uid;
        this.shopId = shopId;
    }
}
