package com.meiyunji.sponsored.service.batchCreate.dao.impl;

import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchNameTemplateDao;
import com.meiyunji.sponsored.service.batchCreate.dto.template.MaxTemplateNoDto;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNameTemplate;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2023-09-06  18:41
 */

@Repository
public class AmazonAdBatchNameTemplateDaoImpl extends BaseShardingDaoImpl<AmazonAdBatchNameTemplate> implements IAmazonAdBatchNameTemplateDao {

    @Override
    public MaxTemplateNoDto getCountAndMaxTemplateNo(Integer puid, Integer uid, Byte templateType) {
        String sql = "select count(*) templateCount, IFNULL(max(template_no), 0) maxTemplateNo from t_amazon_ad_batch_name_template where puid = ? and uid = ? and template_type = ? and is_delete = 1";

        MaxTemplateNoDto maxTemplateNoDto = getJdbcTemplate(puid).queryForObject(sql, (rs, rowNum) -> {
            MaxTemplateNoDto dto = new MaxTemplateNoDto();
            dto.setTemplateCount(rs.getInt("templateCount"));
            dto.setMaxTemplateNo(rs.getInt("maxTemplateNo"));
            return dto;
        }, puid, uid ,templateType);

        return maxTemplateNoDto;
    }

    @Override
    public MaxTemplateNoDto getCountByPuidAndUid(Integer puid, Integer uid, Byte templateType) {
        String sql = "select count(*) templateCount from t_amazon_ad_batch_name_template where puid = ? and uid = ? and template_type = ? and is_delete = 1";

        MaxTemplateNoDto maxTemplateNoDto = getJdbcTemplate(puid).queryForObject(sql, (rs, rowNum) -> {
            MaxTemplateNoDto dto = new MaxTemplateNoDto();
            dto.setTemplateCount(rs.getInt("templateCount"));
            return dto;
        }, puid, uid ,templateType);

        return maxTemplateNoDto;
    }

    @Override
    public List<AmazonAdBatchNameTemplate> queryList(Integer puid, Integer uid, Byte templateType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("uid", uid)
                .equalTo("template_type", templateType)
                .equalTo("is_delete", 1)
                .orderByDesc("id");
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdBatchNameTemplate> queryListByPuidAndUid(Integer puid, Integer uid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("uid", uid)
                .equalTo("is_delete", 1)
                .orderByDesc("id");
        return listByCondition(puid, builder.build());
    }

    @Override
    public AmazonAdBatchNameTemplate getById(Integer puid, Long id) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("id", id)
                .equalTo("is_delete", 1)
                .orderByDesc("id");
        return getByCondition(puid, builder.build());
    }

    @Override
    public int updateByPuidAndTemplateId(Integer puid, AmazonAdBatchNameTemplate template) {
        StringBuilder sb = new StringBuilder("update ");
        sb.append(getJdbcHelper().getTable());
        sb.append(" set template_name = ? , template_content = ? ");
        sb.append(" where id = ? ");
        sb.append(" and is_delete = 1");
        List<Object> args = new ArrayList<>();
        args.add(template.getTemplateName());
        args.add(template.getTemplateContent());
        args.add(template.getId());
        return getJdbcTemplate(puid).update(sb.toString(), args.toArray());
    }

    @Override
    public int deleteByIdList(Integer puid, List<Long> idList) {
        StringBuilder sb = new StringBuilder("update ");
        sb.append(getJdbcHelper().getTable());
        sb.append(" set is_delete = 0");
        sb.append(" where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(idList)) {
            sb.append(SqlStringUtil.dealInList("id", idList, args));
        }
        return getJdbcTemplate(puid).update(sb.toString(), args.toArray());
    }

    @Override
    public AmazonAdBatchNameTemplate getByUidAndNameAndTempType(Integer puid, Integer uid, String temName, Integer templateType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("uid", uid)
                .equalTo("template_name", temName)
                .equalTo("is_delete", 1)
                .equalTo("template_type", templateType);
        return getByCondition(puid, builder.build());
    }

    @Override
    public int getDefaultByUidAndTempType(Integer puid, Integer uid, Integer templateType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("uid", uid)
                .equalTo("default_temp", 1)
                .equalTo("is_delete", 1)
                .equalTo("template_type", templateType);
        return getCountByCondition(puid, builder.build());
    }
}