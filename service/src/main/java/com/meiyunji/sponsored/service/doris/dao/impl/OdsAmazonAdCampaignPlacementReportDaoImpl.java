package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dto.AmazonAdCampaignAllPlacementSqlDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignDorisAllReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignPlacementReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignPlacementReport;
import com.meiyunji.sponsored.service.enums.AmazonAdvertisePredicateEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdPlacementDataDto;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 广告位活动报告(OdsAmazonAdCampaignPlacementReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-31 10:46:17
 */
@Repository
@Slf4j
public class OdsAmazonAdCampaignPlacementReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdCampaignPlacementReport> implements IOdsAmazonAdCampaignPlacementReportDao {

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    /**
     * 获取灰度表名
     * @param puid
     * @return
     */
    private String getGrayTableName(Integer puid) {
        String tableName = "ods_t_amazon_ad_campaign_all_report";
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPagePlacementReport())) {
            tableName = "ods_t_amazon_ad_campaign_placement_report";
        }
        return tableName;
    }

    @Override
    public List<DashboardAdPlacementDataDto> queryAdPlacementCharts(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select campaign_type, ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(sales7d * c.rate), 0) totalSales, ");
        sql.append("ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sql.append("ifnull(sum(conversions7d), 0) orderNum, ifnull(sum(units_ordered7d), 0) saleNum ");
        sql.append("from ").append(getGrayTableName(puid)).append("  report ");
        sql.append("join (select * from dim_currency_rate where puid = ? and `to` = ? ) c ");
        sql.append("on report.puid = c.puid and report.count_month = c.month and report.puid = ? ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = report.marketplace_id and c.`from` = m.currency ");
        sql.append("where report.puid = ? and report.type = 'sp' and report.campaign_type != 'all' ");

        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);
        argsList.add(puid);

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append(SqlStringUtil.dealDorisInList("report.marketplace_id", marketplaceIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealDorisInList("report.shop_id", shopIdList, argsList));
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sql.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sql.append(" and report.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sp' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        sql.append(" group by report.campaign_type ");
        log.info(sql.toString());
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(DashboardAdPlacementDataDto.class), argsList.toArray());
    }

    @Override
    public int listAmazonAdCampaignAllPlacementAllCount(Integer puid, PlacementPageParam param) {
        // 三种广告位 union all
        List<Object> argsList = new ArrayList<>();
        AmazonAdCampaignAllPlacementSqlDto amazonAdCampaignAllPlacementSqlDto = getCampaignAllPlacementTemplate(puid, param, argsList);
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        StringBuilder countSql = new StringBuilder("select count(*) from( ");
        if (StringUtils.isBlank(param.getPredicate())) {
            addTwoArgs(PlacementPageParam.AllPlacementPredicateEnum.values().length - 1, argsList);
            List<String> count = new ArrayList<>(3);
            for (PlacementPageParam.AllPlacementPredicateEnum predicateEnum : PlacementPageParam.AllPlacementPredicateEnum.values()) {
                count.add(amazonAdCampaignAllPlacementSqlDto.getCountSql().replaceAll("#campaignType#", predicateEnum.getContent()));
            }
            countSql.append(String.join(" UNION ALL ", count));
            countSql.append(" ) r ");
        } else {
            PlacementPageParam.AllPlacementPredicateEnum predicateEnum = PlacementPageParam.AllPlacementPredicateEnum.valueOf(param.getPredicate());
            countSql.append(amazonAdCampaignAllPlacementSqlDto.getCountSql().replaceAll("#campaignType#", predicateEnum.getContent()));
            countSql.append(" ) r ");
        }
        Object[] args = argsList.toArray();
        return countPageResult(puid, countSql.toString(), args);
    }

    @Override
    public Page<AmazonAdCampaignDorisAllReport> listAmazonAdCampaignAllPlacementPage(Integer puid, PlacementPageParam param) {
        // 三种广告位 union all
        List<Object> argsList = new ArrayList<>();
        AmazonAdCampaignAllPlacementSqlDto amazonAdCampaignAllPlacementSqlDto = getCampaignAllPlacementTemplate(puid, param, argsList);
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        StringBuilder selectSql = new StringBuilder();
        StringBuilder countSql = new StringBuilder("select count(*) from( ");
        if (StringUtils.isBlank(param.getPredicate())) {
            addTwoArgs(PlacementPageParam.AllPlacementPredicateEnum.values().length - 1, argsList);
            selectSql.append("select * from( ");
            List<String> select = new ArrayList<>(3);
            List<String> count = new ArrayList<>(3);
            for (PlacementPageParam.AllPlacementPredicateEnum predicateEnum : PlacementPageParam.AllPlacementPredicateEnum.values()) {
                select.add(amazonAdCampaignAllPlacementSqlDto.getSelectSql().replaceAll("#campaignType#", predicateEnum.getContent()).replaceAll("#sortType#", predicateEnum.getSort()));
                count.add(amazonAdCampaignAllPlacementSqlDto.getCountSql().replaceAll("#campaignType#", predicateEnum.getContent()));
            }
            selectSql.append(String.join(" UNION ALL ", select));
            countSql.append(String.join(" UNION ALL ", count));
            selectSql.append(" ) r ");
            countSql.append(" ) r ");
        } else {
            PlacementPageParam.AllPlacementPredicateEnum predicateEnum = PlacementPageParam.AllPlacementPredicateEnum.valueOf(param.getPredicate());
            selectSql.append(amazonAdCampaignAllPlacementSqlDto.getSelectSql().replaceAll("#campaignType#", predicateEnum.getContent()).replaceAll("#sortType#", predicateEnum.getSort()));
            countSql.append(amazonAdCampaignAllPlacementSqlDto.getCountSql().replaceAll("#campaignType#", predicateEnum.getContent()));
            countSql.append(" ) r ");
        }
        selectSql.append(" order by ").append(bool ? getPlacementOrderField(param.getOrderField()) : getPlacementOrderField(null)).append(" ");
        if (StringUtils.isBlank(param.getOrderType()) || "desc".equalsIgnoreCase(param.getOrderType())) {
            selectSql.append(" desc");
        }
        if (StringUtils.isNotBlank(bool ? param.getOrderField() : null)) {
            selectSql.append(" ,campaign_id desc,sortType desc");
        }
        Object[] args = argsList.toArray();
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignDorisAllReport.class);
    }

    @Override
    public AdMetricDto getPlacementSumAdMetric(Integer puid, PlacementPageParam param) {
        List<Object> argsList = new ArrayList<>();
        Set<String> keySet = new HashSet<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        keySet.add("costDoris");
        keySet.add("totalSalesDoris");
        keySet.add("orderNumDoris");
        keySet.add("saleNumDoris");
        boolean useAdvanced = param.getUseAdvanced() != null && param.getUseAdvanced();
        // 是否高级查询 用来判断增加字段
        if (useAdvanced) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_PLACEMENT_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    keySet.addAll(fieldKey);
                }
            }
        }
        StringBuilder sumSql;
        // 普通查询 不需要模版
        if (useAdvanced) {
            sumSql = new StringBuilder(" select c.campaign_id campaign_id, r.campaign_type campaign_type, ")
                    .append(keySet.stream().map(SQL_PLACEMENT_MAP::get).collect(Collectors.joining(",")));
        } else {
            sumSql = new StringBuilder(" select ")
                    .append(keySet.stream().map(SQL_PLACEMENT_MAP::get).collect(Collectors.joining(",")));
        }
        sumSql.append(" from ods_t_amazon_ad_campaign_all c join ")
                .append(getGrayTableName(puid))
                .append(" r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.shop_id = ? and r.is_summary = 0 and r.count_day >= ? and r.count_day <= ? ")
                .append(StringUtils.isNotBlank(param.getPredicate()) ? "and r.campaign_type = '" + PlacementPageParam.AllPlacementPredicateEnum.valueOf(param.getPredicate()).getContent() + "'" : " and r.campaign_type IN ('Top of Search on-Amazon','Detail Page on-Amazon', 'Other on-Amazon') ").append(" where c.puid= ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildCampaignAllPlacementWhere(param, argsList, useAdvanced ? " group by campaign_id,campaign_type " : null, true);
        sumSql.append(whereBuilder);
        List<AdMetricDto> list = getJdbcTemplate().query(sumSql.toString(), (re, i) -> AdMetricDto.builder()
                .sumCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .sumAdSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("saleNumDoris")).orElse(BigDecimal.ZERO))
                .sumOrderNum(Optional.ofNullable(re.getBigDecimal("orderNumDoris")).orElse(BigDecimal.ZERO))
                .build(), argsList.toArray());
        return CollectionUtils.isNotEmpty(list) ? AdMetricDto.builder()
                .sumCost(list.stream().map(AdMetricDto::getSumCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .sumAdSale(list.stream().map(AdMetricDto::getSumAdSale).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .sumAdOrderNum(list.stream().map(AdMetricDto::getSumAdOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .sumOrderNum(list.stream().map(AdMetricDto::getSumOrderNum).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .build() : null;
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonAdCampaignAllPlacementGroupCampaignId(Integer puid, PlacementPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select c.campaign_id campaign_id, r.campaign_type campaign_type, ")
                .append(String.join(",", SQL_PLACEMENT_MAP.values()));
        selectSql.append(" from  ods_t_amazon_ad_campaign_all c join ")
                .append(getGrayTableName(puid))
                .append(" r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.shop_id = ? and r.is_summary = 0 and r.`type` = 'sp' ")
                .append(StringUtils.isNotBlank(param.getPredicate()) ? "and r.campaign_type = '" + PlacementPageParam.AllPlacementPredicateEnum.valueOf(param.getPredicate()).getContent() + "' " : " and r.campaign_type IN ('Top of Search on-Amazon','Detail Page on-Amazon', 'Other on-Amazon') ")
                .append(" and r.count_day >= ? and r.count_day <= ? where c.puid= ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildCampaignAllPlacementWhere(param, argsList, " group by campaign_id, campaign_type", true);
        selectSql.append(whereBuilder);
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .campaignId(re.getString("campaign_id"))
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .countDate("")
                .ordersNewToBrand14d(0)
                .salesNewToBrand14d(BigDecimal.ZERO)
                .viewImpressions(0)
                .unitsOrderedNewToBrand14d(0)
                .type("sp")
                .adSaleNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                .build(), argsList.toArray());
    }

    @Override
    public AdHomePerformancedto listTotalPlacementCompareData(Integer puid, PlacementPageParam param, String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        startDate = DateUtil.getDateSqlFormat(startDate);
        endDate = DateUtil.getDateSqlFormat(endDate);
        StringBuilder sql = new StringBuilder(" select ").append(String.join(",", SQL_PLACEMENT_MAP.values()))
                .append(" from  ").append(getGrayTableName(puid)).append(" r ");
        sql.append(" where r.puid = ? and r.shop_id = ? and r.is_summary = 0 and r.`type` = 'sp' ")
                .append(StringUtils.isNotBlank(param.getPredicate()) ? "and r.campaign_type = '" + PlacementPageParam.AllPlacementPredicateEnum.valueOf(param.getPredicate()).getContent() + "' " : " and r.campaign_type IN ('Top of Search on-Amazon','Detail Page on-Amazon', 'Other on-Amazon') ")
                .append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(" and concat(campaign_id, '#', campaign_type) in ( ").append(this.idlistAmazonAdCampaignAllPlacementPageSql(puid, param, argsList)).append(" ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adCost(Optional.ofNullable(re.getBigDecimal("costDoris")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("saleNumDoris")).orElse(0))
                .adSale(Optional.ofNullable(re.getBigDecimal("totalSalesDoris")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicksDoris")).orElse(0))
                .impressions(Optional.of(re.getInt("impressionsDoris")).orElse(0))
                .ordersNewToBrand14d(0)
                .salesNewToBrand14d(BigDecimal.ZERO)
                .viewImpressions(0)
                .unitsOrderedNewToBrand14d(0)
                .type("sp")
                .adSaleNum(Optional.of(re.getInt("adSaleNumDoris")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("adSalesDoris")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("orderNumDoris")).orElse(0))
                //本广告产品销量
                .orderNum(Optional.of(re.getInt("adOrderNumDoris")).orElse(0))
                .build(), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }

    private String idlistAmazonAdCampaignAllPlacementPageSql(Integer puid, PlacementPageParam param, List<Object> args) {
        // 三种广告位 union all
        List<Object> argsList = new ArrayList<>();
        AmazonAdCampaignAllPlacementSqlDto amazonAdCampaignAllPlacementSqlDto = getCampaignAllPlacementTemplate(puid, param, argsList);
        StringBuilder sql = new StringBuilder("select concat(campaign_id, '#', campaign_type) from( ");
        if (StringUtils.isBlank(param.getPredicate())) {
            addTwoArgs(PlacementPageParam.AllPlacementPredicateEnum.values().length - 1, argsList);
            List<String> count = new ArrayList<>(3);
            for (PlacementPageParam.AllPlacementPredicateEnum predicateEnum : PlacementPageParam.AllPlacementPredicateEnum.values()) {
                count.add(amazonAdCampaignAllPlacementSqlDto.getCountSql().replaceAll("#campaignType#", predicateEnum.getContent()));
            }
            sql.append(String.join(" UNION ALL ", count));
            sql.append(" ) r ");
        } else {
            PlacementPageParam.AllPlacementPredicateEnum predicateEnum = PlacementPageParam.AllPlacementPredicateEnum.valueOf(param.getPredicate());
            sql.append(amazonAdCampaignAllPlacementSqlDto.getCountSql().replaceAll("#campaignType#", predicateEnum.getContent()));
            sql.append(" ) r ");
        }
        args.addAll(argsList);
        return sql.toString();
    }

    @Override
    public List<AdHomePerformancedto> listTotalAmazonAdCampaignAllPlacementGroupDateById(Integer puid, PlacementPageParam param, List<String> campaignIdList) {
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return new ArrayList<>();
        }
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT count_date,sum(cost) cost,sum(sales7d) total_sales,"
                + " sum(sales7d_same_sku) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions7d) order_num, sum(conversions7d_same_sku) ad_sale_num,"
                + " sum(units_ordered7d) sale_num,sum(units_ordered7d_same_sku) ad_order_num "
                + " FROM ");
        sql.append(getGrayTableName(puid));
        sql.append(" where puid= ? and shop_id= ? and type = 'sp' and is_summary = 0 ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (campaignIdList.size() > 10000) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignIdList, argsList));
        } else {
            sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));
        }
        if (StringUtils.isBlank(param.getPredicate())) {
            sql.append(" and campaign_type in  ('Top of Search on-Amazon','Detail Page on-Amazon', 'Other on-Amazon') ");
        } else {
            sql.append(" and campaign_type = ? ");
            AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), AmazonAdvertisePredicateEnum.class);
            argsList.add(predicateEnum != null ? predicateEnum.getValue() : "");
        }
        sql.append("  and count_day >= ? and count_day <= ? group by count_date ");
        argsList.add(startDate);
        argsList.add(endDate);
        return getJdbcTemplate().query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.of(re.getInt("order_num")).orElse(0))
                .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                .clicks(Optional.of(re.getInt("clicks")).orElse(0))
                .impressions(Optional.of(re.getInt("impressions")).orElse(0))
                .countDate(re.getString("count_date"))
                .ordersNewToBrand14d(0)
                .salesNewToBrand14d(BigDecimal.ZERO)
                .viewImpressions(0)
                .unitsOrderedNewToBrand14d(0)
                .type("sp")
                .adSaleNum(Optional.of(re.getInt("ad_sale_num")).orElse(0))
                //本广告产品销售额
                .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                //广告销量
                .salesNum(Optional.of(re.getInt("sale_num")).orElse(0))
                .orderNum(Optional.of(re.getInt("ad_order_num")).orElse(0))
                .build(), argsList.toArray());
    }

    private AmazonAdCampaignAllPlacementSqlDto getCampaignAllPlacementTemplate(Integer puid, PlacementPageParam param, List<Object> argsList) {
        StringBuilder selectSql = new StringBuilder(" select c.campaign_id campaign_id, '#sortType#' as sortType,  IFNULL(r.campaign_type,'#campaignType#') campaign_type, ")
                .append(String.join(",", SQL_PLACEMENT_MAP.values()));
        boolean bool = StringUtils.isNotBlank(param.getOrderType()) && !"null".equalsIgnoreCase(param.getOrderType());
        if (bool) {
            String field = getPlacementSqlField(param.getOrderField());
            if (StringUtils.isNotBlank(field)) {
                selectSql.append(",").append(field).append("  ");
            }
        } else {
            selectSql.append(",").append(getPlacementSqlField(null)).append("  ");
        }

        StringBuilder countSql = new StringBuilder("select c.campaign_id campaign_id , IFNULL(r.campaign_type,'#campaignType#') campaign_type");
        if (param.getUseAdvanced()) {
            Set<String> strings = getSelectKey(param);
            if (CollectionUtils.isNotEmpty(strings)) {
                Set<String> fieldKey = strings.stream().filter(SQL_PLACEMENT_MAP::containsKey).collect(Collectors.toSet());
                if (CollectionUtils.isNotEmpty(fieldKey)) {
                    countSql.append(",");
                    countSql.append(fieldKey.stream().map(SQL_PLACEMENT_MAP::get).collect(Collectors.joining(",")));
                }
            }
        }
        String startDate = DateUtil.getDateSqlFormat(param.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(param.getEndDate());
        // 此处写死SP类型 后续有需求再改
        selectSql.append(" from ods_t_amazon_ad_campaign_all c left join ");
        selectSql.append(getGrayTableName(puid));
        selectSql.append(" r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id and r.puid = ? and r.shop_id = ? and r.is_summary = 0 and r.`type` = 'sp' and r.campaign_type = '#campaignType#' and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        countSql.append(" from ods_t_amazon_ad_campaign_all c left join ");
        countSql.append(getGrayTableName(puid));
        countSql.append(" r on r.puid = c.puid and r.shop_id=c.shop_id and r.campaign_id=c.campaign_id  and r.puid = ? and r.shop_id = ? and r.is_summary = 0 and r.`type` = 'sp' and r.campaign_type = '#campaignType#' and r.count_day >= ? and r.count_day <= ? where c.puid= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(startDate);
        argsList.add(endDate);
        argsList.add(puid);
        StringBuilder whereBuilder = buildCampaignAllPlacementWhere(param, argsList, " group by campaign_id,campaign_type ", false);
        selectSql.append(whereBuilder);
        countSql.append(whereBuilder);
        AmazonAdCampaignAllPlacementSqlDto amazonAdCampaignAllPlacementSqlDto = new AmazonAdCampaignAllPlacementSqlDto();
        amazonAdCampaignAllPlacementSqlDto.setSelectSql(selectSql.toString());
        amazonAdCampaignAllPlacementSqlDto.setCountSql(countSql.toString());
        return amazonAdCampaignAllPlacementSqlDto;
    }

    private StringBuilder buildCampaignAllPlacementWhere(PlacementPageParam param, List<Object> argsList, String groupSql, boolean isNUll) {
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" and c.type = 'sp' ");
        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            if (param.getCampaignIdList().size() <= 10000) {
                whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
            } else {
                whereSql.append(SqlStringUtil.dealBitMapDorisInList("c.campaign_id", param.getCampaignIdList(), argsList));
            }
        }
        //通过param.campaignId查询具体的广告活动
        if (StringUtils.isNotEmpty(param.getCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", StringUtil.splitStr(param.getCampaignId()), argsList));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
            List<String> servings = list.stream().filter(StringUtils::isNotBlank).map(Constants.SERVER_STATUS_SELECT::get).filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(servings)) {
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", servings, argsList));
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId()) && param.getPortfolioId().equals("-1")) {
            whereSql.append(" and c.portfolio_id is null ");
        }
        if (StringUtils.isNotBlank(param.getStrategyType())) {
            whereSql.append(" and c.strategy = ?");
            argsList.add(param.getStrategyType());
        }
        if (isNUll) {
            whereSql.append(" and r.is_summary = 0 ");
        }
        if (StringUtils.isNotBlank(groupSql)) {
            whereSql.append(groupSql);
        }
        whereSql.append(subWhereSql(param, argsList));
        return whereSql;
    }

    private StringBuilder subWhereSql(PlacementPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (param.getUseAdvanced()) {
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.ZERO;

            subWhereSql.append(" having 1=1 ");
            //展示量
            if (param.getImpressionsMin() != null) {
                subWhereSql.append(" and impressionsDoris >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if (param.getImpressionsMax() != null) {
                subWhereSql.append(" and impressionsDoris <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if (param.getClicksMin() != null) {
                subWhereSql.append(" and clicksDoris >= ?");
                argsList.add(param.getClicksMin());
            }
            if (param.getClicksMax() != null) {
                subWhereSql.append(" and clicksDoris <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (param.getClickRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if (param.getClickRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if (param.getCostMin() != null) {
                subWhereSql.append(" and costDoris >= ?");
                argsList.add(param.getCostMin());
            }
            if (param.getCostMax() != null) {
                subWhereSql.append(" and costDoris <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if (param.getCpcMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if (param.getCpcMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if (param.getOrderNumMin() != null) {
                subWhereSql.append(" and saleNumDoris >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if (param.getOrderNumMax() != null) {
                subWhereSql.append(" and saleNumDoris <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if (param.getSalesMin() != null) {
                subWhereSql.append(" and totalSalesDoris >= ?");
                argsList.add(param.getSalesMin());
            }
            if (param.getSalesMax() != null) {
                subWhereSql.append(" and totalSalesDoris <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if (param.getSalesConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(saleNumDoris/clicksDoris,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if (param.getSalesConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(saleNumDoris/clicksDoris,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if (param.getAcosMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if (param.getAcosMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/costDoris,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(costDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(totalSalesDoris,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and orderNumDoris >= ? ");
                argsList.add(param.getAdSalesTotalMin());
            }

            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and orderNumDoris <= ? ");
                argsList.add(param.getAdSalesTotalMax());
            }

            //CPA
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/saleNumDoris, 0), 2) >= ? ");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(costDoris/saleNumDoris, 0), 2) <= ? ");
                argsList.add(param.getCpaMax());
            }
            //本广告产品订单量（绝对值）adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdSaleNumMax());
            }

            //其他产品广告订单量（绝对值） adOtherOrderNumMin
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adSaleNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherOrderNumMin());
            }

            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(saleNumDoris - adSaleNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherOrderNumMax());
            }

            //本广告产品销售额（绝对值） adSalesMin
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdSalesMin());
            }

            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdSalesMax());
            }

            //其他产品广告销售额（绝对值）adOtherSalesMin
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSalesMin());
            }

            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(totalSalesDoris - adSalesDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSalesMax());
            }

            //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdSelfSaleNumMax());
            }

            //其他产品广告销量（绝对值）adOtherSaleNumMin
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adOrderNumDoris, 0) >= ? ");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(orderNumDoris - adOrderNumDoris, 0) <= ? ");
                argsList.add(param.getAdOtherSaleNumMax());
            }

            // 广告笔单价 筛选
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/saleNumDoris, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(totalSalesDoris/saleNumDoris, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }
        }
        return subWhereSql;
    }

    // 添加参数
    private void addTwoArgs(int num, List<Object> argsList) {
        int size = argsList.size();
        for (int i = 0; i < num; i++) {
            for (int m = 0; m < size; m++) {
                argsList.add(argsList.get(m));
            }
        }
    }

    private String getPlacementOrderField(String field) {
        if (StringUtils.isBlank(field)) {
            return " dataUpdateTime desc,campaign_id desc,sortType ";
        }

        switch (field) {
            case "dailyBudget":
                return " budgetDoris ";
            case "adCost":
            case "adCostPercentage":
            case "acots":
                return " costDoris ";
            case "topImpressionShare":
                return " maxTopOfSearch ";
            case "adSale":
            case "adSalePercentage":
            case "asots":
                return " totalSalesDoris ";
            // 广告订单量
            case "adOrderNum":
                return " saleNumDoris ";
            case "adOrderNumPercentage":
                return " saleNumDoris ";
            //本广告产品订单量
            case "adSaleNum":
                return " adSaleNumDoris ";
            //广告销量
            case "saleNum":
                return " salesNumDoris ";
            case "orderNum":
            case "orderNumPercentage":
                return " orderNumDoris ";
            //本广告产品销量
            case "adSelfSaleNum":
                return " adOrderNumDoris ";
            //其他产品广告销量
            case "ordersNewToBrandFTD":
                return " ordersNewToBrand14dDoris ";
            case "salesNewToBrandFTD":
                return " salesNewToBrand14dDoris ";
            case "unitsOrderedNewToBrandFTD":
                return " unitsOrderedNewToBrand14dDoris ";
            case "brandedSearches":
                return " brandedSearches14dDoris ";
            case "detailPageViews":
                return " detailPageView14dDoris ";
            default:
                return " " + field + "Doris ";
        }
    }

    private static final Map<String, String> SQL_PLACEMENT_MAP = Collections.unmodifiableMap(new HashMap<String, String>() {
        {
            put("costDoris", "IFNULL(sum(r.cost),0) `costDoris`");
            put("totalSalesDoris", "IFNULL(sum(sales7d),0) totalSalesDoris");
            put("adSalesDoris", "IFNULL(sum(sales7d_same_sku),0) adSalesDoris");
            put("impressionsDoris", "IFNULL(sum(`impressions`),0) impressionsDoris");
            put("clicksDoris", "IFNULL(sum(`clicks`),0) clicksDoris");
            put("orderNumDoris", "IFNULL(sum(units_ordered7d),0) orderNumDoris");
            put("adOrderNumDoris", "IFNULL(sum(units_ordered7d_same_sku),0) adOrderNumDoris");
            put("saleNumDoris", "IFNULL(sum(conversions7d),0) saleNumDoris");
            put("adSaleNumDoris", "IFNULL(sum(conversions7d_same_sku),0) adSaleNumDoris");
        }
    });

    private String getPlacementSqlField(String field) {
        if (StringUtils.isBlank(field)) {
            return " any(c.create_time) dataUpdateTime ";
        }
        switch (field) {
            case "name":
                return " any(c.name) nameDoris ";
            case "dailyBudget":
                return " any(c.budget) budgetDoris";
            case "startDate":
                return " any(c.start_date) startDateDoris";
            case "endDate":
                return " any(c.end_date) endDateDoris";
            case "topImpressionShare":
                return " max(top_of_search_is) maxTopOfSearch ";
            case "adCostPerClick":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as adCostPerClickDoris ";
            case "ctr":
                return " ifnull(sum(`clicks`)/sum(`impressions`),0) as ctrDoris ";
            case "cvr":
                return " ifnull(sum(conversions7d)/sum(`clicks`),0) as cvrDoris ";
            case "cpc":
                return " ifnull(sum(r.cost)/sum(`clicks`),0) as cpcDoris ";
            case "acos":
                return " ifnull(sum(r.cost)/sum(if (r.type = 'sp', sales7d, sales14d)),0) as acosDoris ";
            case "roas":
                return " ifnull(sum(if (r.type = 'sp', sales7d, sales14d))/sum(r.cost),0) as roasDoris ";
            case "cpa":
                return " ifnull(sum(r.cost)/sum(conversions7d),0) as cpaDoris ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(sum(conversions7d-conversions7d_same_sku),0) as adOtherOrderNumDoris ";
            //其他产品广告销售额
            case "adOtherSales":
                return "ifnull(sum(if (r.type = 'sp', sales7d, sales14d))-sum(if (r.type = 'sp', sales7d_same_sku, sales14d_same_sku)),0) as adOtherSalesDoris ";
            //广告销量
            case "adOtherSaleNum":
                return " ifnull(sum(units_ordered7d - units_ordered7d_same_sku),0) as adOtherSaleNumDoris ";
            case "advertisingUnitPrice":
                return " ifnull(sum(sales7d)/sum(conversions7d),0) as advertisingUnitPriceDoris";
            default:
                return " ";
        }
    }

    private Set<String> getSelectKey(PlacementPageParam param) {
        Set<String> keySet = new HashSet<>();
        if (param.getImpressionsMin() != null) {
            keySet.add("impressionsDoris");
        }
        if (param.getImpressionsMax() != null) {
            keySet.add("impressionsDoris");
        }
        //点击量
        if (param.getClicksMin() != null) {
            keySet.add("clicksDoris");
        }
        if (param.getClicksMax() != null) {
            keySet.add("clicksDoris");
        }
        //点击率（clicks/impressions）
        if (param.getClickRateMin() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        if (param.getClickRateMax() != null) {
            keySet.add("clicksDoris");
            keySet.add("impressionsDoris");
        }
        //花费
        if (param.getCostMin() != null) {
            keySet.add("costDoris");
        }
        if (param.getCostMax() != null) {
            keySet.add("costDoris");
        }
        //cpc  平均点击费用
        if (param.getCpcMin() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        if (param.getCpcMax() != null) {
            keySet.add("costDoris");
            keySet.add("clicksDoris");
        }
        //广告订单量
        if (param.getOrderNumMin() != null) {
            keySet.add("saleNumDoris");
        }
        if (param.getOrderNumMax() != null) {
            keySet.add("saleNumDoris");
        }
        //广告销售额
        if (param.getSalesMin() != null) {
            keySet.add("totalSalesDoris");
        }
        if (param.getSalesMax() != null) {
            keySet.add("totalSalesDoris");
        }
        //订单转化率
        if (param.getSalesConversionRateMin() != null) {
            keySet.add("saleNumDoris");
            keySet.add("clicksDoris");
        }
        if (param.getSalesConversionRateMax() != null) {
            keySet.add("saleNumDoris");
            keySet.add("clicksDoris");
        }
        //acos
        if (param.getAcosMin() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        if (param.getAcosMax() != null) {
            keySet.add("costDoris");
            keySet.add("totalSalesDoris");
        }
        // roas
        if (param.getRoasMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // roas
        if (param.getRoasMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMin() != null) {
            keySet.add("costDoris");
        }
        // acots  需要乘以店铺销售额
        if (param.getAcotsMax() != null) {
            keySet.add("costDoris");
        }
        // asots 需要乘以店铺销售额
        if (param.getAsotsMin() != null) {
            keySet.add("totalSalesDoris");
        }
        // asots  需要乘以店铺销售额
        if (param.getAsotsMax() != null) {
            keySet.add("totalSalesDoris");
        }

        //广告销量
        if (param.getAdSalesTotalMin() != null) {
            keySet.add("orderNumDoris");
        }

        if (param.getAdSalesTotalMax() != null) {
            keySet.add("orderNumDoris");
        }
        //CPA
        if (param.getCpaMin() != null) {
            keySet.add("costDoris");
            keySet.add("saleNumDoris");
        }
        if (param.getCpaMax() != null) {
            keySet.add("costDoris");
            keySet.add("saleNumDoris");
        }

        //本广告产品订单量（绝对值）adSaleNumMin
        if (param.getAdSaleNumMin() != null) {
            keySet.add("adSaleNumDoris");
        }
        if (param.getAdSaleNumMax() != null) {
            keySet.add("adSaleNumDoris");
        }

        //其他产品广告订单量（绝对值） adOtherOrderNumMin
        if (param.getAdOtherOrderNumMin() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adSaleNumDoris");
        }

        if (param.getAdOtherOrderNumMax() != null) {
            keySet.add("saleNumDoris");
            keySet.add("adSaleNumDoris");
        }

        //本广告产品销售额（绝对值） adSalesMin
        if (param.getAdSalesMin() != null) {
            keySet.add("adSalesDoris");
        }

        if (param.getAdSalesMax() != null) {
            keySet.add("adSalesDoris");
        }

        //其他产品广告销售额（绝对值）adOtherSalesMin
        if (param.getAdOtherSalesMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        if (param.getAdOtherSalesMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("adSalesDoris");
        }

        //本广告产品销量（绝对值）adSelfSaleNumMin units_ordered7d_same_sku
        if (param.getAdSelfSaleNumMin() != null) {
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdSelfSaleNumMax() != null) {
            keySet.add("adOrderNumDoris");
        }

        //其他产品广告销量（绝对值）adOtherSaleNumMin
        if (param.getAdOtherSaleNumMin() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adOrderNumDoris");
        }
        if (param.getAdOtherSaleNumMax() != null) {
            keySet.add("orderNumDoris");
            keySet.add("adOrderNumDoris");
        }

        if (param.getAdvertisingUnitPriceMin() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("saleNumDoris");
        }

        if (param.getAdvertisingUnitPriceMax() != null) {
            keySet.add("totalSalesDoris");
            keySet.add("saleNumDoris");
        }

        return keySet;
    }
}
