package com.meiyunji.sponsored.service.multiPlatform.walmart.service;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItem;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdItemListCreateResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartItemBidRecommend;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdItemsVo;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.vo.SearchItemsDataResponseVO;


import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingItemService {

    int delete(Integer puid, Long id);

    abstract void add(WalmartAdvertisingItem group);

    /**
     * 更新
     */
    int update(Integer puid, WalmartAdvertisingItem item);

    Page<WalmartAdvertisingItemPageDTO> getPageList(WalmartAdItemsVo reqVo);
    WalmartAdvertisingItemPageDTO getAggregateData(WalmartAdItemsVo reqVo);

    int syncItemByCampaigns(Integer puid, List<Integer> shopIdList,
                            WalmartAdvertiserClient advertiserClient, List<WalmartAdvertisingCampaign> campaigns) throws ServiceException;

    WalmartAdItemListCreateResp itemAddToGroup(int puid, WalmartAdvertisingItemAddToGroupDTO dto) throws ServiceException;
    Boolean updateBidOrStatus(int puid, Integer shopId, WalmartAdItemUpdateDTO req);
    Page<WalmartItemBidRecommend> getBidRecommend(int puid, List<Integer> shopIdList,
                                                  List<String> itemIdList, Integer pageNo,
                                                  Integer pageSize);

    List<SearchItemsDataResponseVO> getItemSuggest(int puid, Integer shopId, List<String> itemIdList);

    int deleteByCampaignId(Integer puid, Integer shopId, String campaignId);

    List<WalmartAdvertisingItem> getByCampaignId(Integer puid, Integer shopId, String campaignId);
}
