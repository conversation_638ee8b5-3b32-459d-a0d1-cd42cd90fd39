package com.meiyunji.sponsored.service.cpc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.PageThreadConfig;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.enums.QueryKeywordPageThreadEnum;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.query.AutoRuleQueryReportDataRpcVo;
import com.meiyunji.sponsored.rpc.vo.ProductRpcVo;
import com.meiyunji.sponsored.rpc.vo.RankParamVo;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterCampaignBaseDataBO;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermAggregateBO;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermBO;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.ICpcQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdCampaignAllService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.ResolvedExpressionParseHelper;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.service.SearchAnalysisStatsV2Client;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeKeyword;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.export.ReportFillBaseDataHelper;
import com.meiyunji.sponsored.service.feign.param.CountByPuidAndKeywordTextParam;
import com.meiyunji.sponsored.service.feign.service.KeywordRankMonitorFeignService;
import com.meiyunji.sponsored.service.monitor.dao.IAmazonAdActiveMonitorDao;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.searchAnalysis.po.SearchTermsAnalysis;
import com.meiyunji.sponsored.service.searchTermsAnalysis.WeekSearchTermsAnalysisService;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.util.GrayUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IWordRootKeywordSpDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CpcQueryKeywordReportServiceImpl extends ReportService<CpcQueryKeywordReport> implements ICpcQueryKeywordReportService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    @Autowired
    private IOdsCpcQueryKeywordReportDao odsCpcQueryKeywordReportDao;
    @Autowired
    private IOdsCpcQueryTargetingReportDao odsCpcQueryTargetingReportDao;
    @Autowired
    private IOdsCpcSbQueryKeywordReportDao odsCpcSbQueryKeywordReportDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private SearchAnalysisStatsV2Client searchAnalysisStatsV2Client;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IProductApi productDao;
    @Autowired
    private IAmazonAdCampaignAllService amazonAdCampaignAllService;
    @Autowired
    private IWordRootKeywordSpDao wordRootKeywordSpDao;
    @Autowired
    private IAmazonAdActiveMonitorDao amazonAdActiveMonitorDao;
    @Autowired
    private KeywordRankMonitorFeignService keywordRankMonitorFeignService;
    @Autowired
    private PageThreadConfig pageThreadConfig;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IOdsAmazonWordRootQueryDao odsAmazonWordRootQueryDao;
    @Autowired
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;
    @Autowired
    private IOdsAmazonAdNeKeywordDao odsAmazonAdNeKeywordDao;
    @Autowired
    private WeekSearchTermsAnalysisService weekSearchTermsAnalysisService;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private ReportFillBaseDataHelper reportFillBaseDataHelper;
    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IWordTranslateService wordTranslateService;

    @Override
    public AllQueryWordDataResponse.AdQueryWordsHomeVo getDorisAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }


        if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(dto.getSearchValue());
            if (keywordGroupValueEnumByTextCn != null) {
                dto.setSearchValue(keywordGroupValueEnumByTextCn.getKeywordText());
            }
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());

        dto.setShopSales(shopSaleDto != null && shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);

        //组装vo
        long allKeywordTime = System.currentTimeMillis();
        if (dto.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(shopAuth.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
        }
        Page pageVo = this.getDorisAdQueryWordsPageVo(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 {} ==============================", System.currentTimeMillis() - allKeywordTime);
        // 填充广告策略
        fillAdStrategy(dto, pageVo.getRows());
        //处理分页
        AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.Builder pageBuilder = AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(pageVo.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(pageVo.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(pageVo.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(pageVo.getTotalSize()));
        List<ReportVo> rows = pageVo.getRows();

        //填充ABA展示信息（仅支持美国站）
//        long t1 = Instant.now().toEpochMilli();
//        fillABARankField(rows);
//        log.info("填充ABA展示信息花费时间：{}", Instant.now().toEpochMilli() - t1);

        if (CollectionUtils.isNotEmpty(rows)) {

            //环比数据
            Map<String, ReportVo> compareQueryMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(),
                        dto.getCompareStartDate(), dto.getCompareEndDate());
                BigDecimal shopSalesByDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSalesByDataCompare = BigDecimal.ZERO;
                } else {
                    shopSalesByDataCompare = shopSaleDtoCompare.getSumRange();
                }
                dto.setShopSales(shopSalesByDataCompare);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());
                //通过asin精确查询本来
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).distinct().collect(Collectors.joining(StringUtil.SPECIAL_COMMA)));
                // 查对比数据的时候不需要联表
                boolean isQueryJoinSearchTermsRank = dto.isQueryJoinSearchTermsRank();
                dto.setQueryJoinSearchTermsRank(false);
                Page<ReportVo> pageCompare = this.getDorisAdQueryWordsPageVo(puid, dto, new Page(1, page.getPageSize()));
                // 还原字段
                dto.setQueryJoinSearchTermsRank(isQueryJoinSearchTermsRank);
                compareQueryMap = pageCompare.getRows().stream()
                        .collect(Collectors.toMap(k -> (StringUtils.isNotBlank(k.getKeywordId()) ? k.getKeywordId() : k.getTargetId()).concat("#")
                                .concat(Optional.ofNullable(k.getQuery()).orElse("")), Function.identity(), (a, b) -> a));
            }

            //调用运营工具服务接口
            //getKeywordMonitor(puid, rows);
            Map<String, ReportVo> finalCompareQueryMap = compareQueryMap;
            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                        ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
                        if (org.apache.commons.lang.StringUtils.isNotBlank(item.getCountDate())) {
                            voBuilder.setCountDate(item.getCountDate());
                        }

                        if (item.getShopId() != null) {
                            voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        }
                        if (item.getMarketplaceId() != null) {
                            voBuilder.setMarketplaceId(item.getMarketplaceId());
                        }
                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAsots(DoubleValue.of(Optional.ofNullable(item.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcots(DoubleValue.of(Optional.ofNullable(item.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
                        voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(item.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(item.getAdClickRatio()).orElse(0.0)));
                        voBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(item.getAdConversionRate()).orElse(0.0)));
                        voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
                        voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");


                        voBuilder.setNaturalClicks(org.apache.commons.lang.StringUtils.isNotBlank(item.getNaturalClicks()) ? item.getNaturalClicks() : "0");

                        if (item.getKeywordMonitor() != null) {
                            voBuilder.setKeywordMonitor(item.getKeywordMonitor());
                        } else {
                            voBuilder.setKeywordMonitor(0);
                        }
                        if (StringUtils.isNotBlank(item.getCampaignId())) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupId())) {
                            voBuilder.setAdGroupId(item.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupType())) {
                            voBuilder.setAdGroupType(item.getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupName())) {
                            voBuilder.setAdGroupName(item.getAdGroupName());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupState())) {
                            voBuilder.setAdGroupState(item.getAdGroupState());
                        }

                        if (StringUtils.isNotBlank(item.getCampaignName())) {
                            voBuilder.setCampaignName(item.getCampaignName());
                        }
                        if (StringUtils.isNotBlank(item.getCampaignStatus())) {
                            voBuilder.setCampaignState(item.getCampaignStatus());
                        }

                        if (StringUtils.isNotBlank(item.getKeywordText())) {
                            voBuilder.setKeywordText(item.getKeywordText());
                        }
                        if (StringUtils.isNotBlank(item.getMatchType())) {
                            voBuilder.setMatchType(item.getMatchType());
                        }
                        if (StringUtils.isNotBlank(item.getSku())) {
                            voBuilder.setSku(item.getSku());
                        }
                        if (StringUtils.isNotBlank(item.getAsin())) {
                            voBuilder.setAsin(item.getAsin());
                        }

                        if (StringUtils.isNotBlank(item.getQueryId())) {
                            voBuilder.setQueryId(item.getQueryId());
                        }

                        voBuilder.setQueryType(AmazonAd.AdQueryTypeEnum.SP_QUERY.getSearchField());

                        if (StringUtils.isNotBlank(item.getQuery())) {
                            voBuilder.setQuery(item.getQuery());
                        }

                        if (StringUtils.isNotBlank(item.getQueryCn())) {
                            voBuilder.setQueryCn(item.getQueryCn());
                        }

                        if (StringUtils.isNotBlank(item.getParentAsin())) {
                            voBuilder.setParentAsin(item.getParentAsin());
                        }
                        if (StringUtils.isNotBlank(item.getTitle())) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (StringUtils.isNotBlank(item.getMainImage())) {
                            voBuilder.setMainImage(item.getMainImage());
                        }
                        if (StringUtils.isNotBlank(item.getNegaType())) {
                            voBuilder.setNegaType(item.getNegaType());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingType())) {
                            voBuilder.setTargetingType(item.getTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getKeywordId())) {
                            voBuilder.setKeywordId(item.getKeywordId());
                        }
                        if (StringUtils.isNotBlank(item.getAdId())) {
                            voBuilder.setAdId(item.getAdId());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingText())) {
                            voBuilder.setTargetingText(item.getTargetingText());
                        }
                        if (StringUtils.isNotBlank(item.getSpCampaignType())) {
                            voBuilder.setSpCampaignType(item.getSpCampaignType());
                        }
                        if (StringUtils.isNotBlank(item.getSpGroupType())) {
                            voBuilder.setSpGroupType(item.getSpGroupType());
                        }

                        if (StringUtils.isNotBlank(item.getSpTargetType())) {
                            voBuilder.setSpTargetType(item.getSpTargetType());
                        }

                        if (StringUtils.isNotBlank(item.getTargetId())) {
                            voBuilder.setTargetId(item.getTargetId());
                        }
                        if (StringUtils.isNotBlank(item.getTargetState())) {
                            voBuilder.setTargetState(item.getTargetState());
                        }
                        if (item.getIsBroad() != null) {
                            voBuilder.setIsBroad(BoolValue.of(item.getIsBroad()));
                        }
                        if (item.getIsPhrase() != null) {
                            voBuilder.setIsPhrase(BoolValue.of(item.getIsPhrase()));
                        }
                        if (item.getIsExact() != null) {
                            voBuilder.setIsExact(BoolValue.of(item.getIsExact()));
                        }
                        if (item.getIsNegativeExact() != null) {
                            voBuilder.setIsNegativeExact(BoolValue.of(item.getIsNegativeExact()));
                        }
                        if (item.getIsNegativePhrase() != null) {
                            voBuilder.setIsNegativePhrase(BoolValue.of(item.getIsNegativePhrase()));
                        }
                        if (item.getIsTargetType() != null) {
                            voBuilder.setIsTargetType(BoolValue.of(item.getIsTargetType()));
                        }
                        if (item.getDefaultBid() != null) {
                            voBuilder.setDefaultBid(item.getDefaultBid());
                        }
                        if (item.getPortfolioId() != null) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (item.getPortfolioName() != null) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }
                        if (item.getType() != null) {
                            voBuilder.setType(item.getType());
                        }
                        if (item.getImgUrl() != null) {
                            voBuilder.setImgUrl(item.getImgUrl());
                        }
                        if (item.getTargetTitle()!= null) {
                            voBuilder.setTargetTitle(item.getTargetTitle());
                        }
                        if (item.getCategory()!= null) {
                            voBuilder.setCategory(item.getCategory());
                        }
                        if (item.getBrandName()!= null) {
                            voBuilder.setBrandName(item.getBrandName());
                        }
                        if (item.getCommodityPriceRange()!= null) {
                            voBuilder.setCommodityPriceRange(item.getCommodityPriceRange());
                        }
                        if (item.getCategoryRating()!= null) {
                            voBuilder.setCategoryRating(item.getCategoryRating());
                        }
                        if (item.getDistribution()!= null) {
                            voBuilder.setDistribution(item.getDistribution());
                        }
                        if (item.getLookBack()!= null) {
                            voBuilder.setLookBack(item.getLookBack());
                        }
                        //true 都不为T00030
                        fillDefaultBrandMessage(voBuilder, item, true);
                        // 广告策略标签
                        if(CollectionUtils.isNotEmpty(item.getStrategyList())){
                            voBuilder.addAllAdStrategys(buildStrategyList(item));
                        }
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        if (item.getAdSales() != null) {
                            voBuilder.setAdSales(DoubleValue.of(item.getAdSales().doubleValue()));
                        }
                        //本广告产品订单量
                        if (item.getAdSaleNum() != null) {
                            voBuilder.setAdSaleNum(Int32Value.of(item.getAdSaleNum()));
                        }
                        //本广告产品销量
                        if (item.getAdSelfSaleNum() != null) {
                            voBuilder.setAdSelfSaleNum(Int32Value.of(item.getAdSelfSaleNum()));
                        }
                        //其他产品广告订单量
                        if (item.getAdOtherOrderNum() != null) {
                            voBuilder.setAdOtherOrderNum(Int32Value.of(item.getAdOtherOrderNum()));
                        }
                        //其他产品广告销售额
                        if (item.getAdOtherSales() != null) {
                            voBuilder.setAdOtherSales(DoubleValue.of(item.getAdOtherSales().doubleValue()));
                        }
                        //其他产品广告销量
                        if (item.getAdOtherSaleNum() != null) {
                            voBuilder.setAdOtherSaleNum(Int32Value.of(item.getAdOtherSaleNum()));
                        }
                        // ABA搜索词排名
                        voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                        // ABA排名周变化率
                        if (item.getWeekRatio() != null) {
                            voBuilder.setWeekRatio(String.valueOf(Optional.ofNullable(item.getWeekRatio()).orElse(BigDecimal.ZERO)));
                        }
                        // 封装关键词实时排名参数
                        if (item.getRankParamVo() != null) {
                            RankParamVo.Builder rankVoBuilder = RankParamVo.newBuilder();
                            KeywordsRankParamVo rankParamVo = item.getRankParamVo();
                            if (rankParamVo.getId() != null) {
                                rankVoBuilder.setId(rankParamVo.getId());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getSiteId())) {
                                rankVoBuilder.setSiteId(rankParamVo.getSiteId());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getSiteName())) {
                                rankVoBuilder.setSiteName(rankParamVo.getSiteName());
                            }
                            if (CollectionUtils.isNotEmpty(rankParamVo.getProducts())) {
                                List<ProductRpcVo> productRpcVos = Lists.newArrayList();
                                rankParamVo.getProducts().stream().forEach(product -> {
                                    ProductRpcVo.Builder productRpcVo = ProductRpcVo.newBuilder();
                                    if (StringUtils.isNotBlank(product.getAsin())) {
                                        productRpcVo.setAsin(product.getAsin());
                                        productRpcVo.setAsinUrl(product.getAsinUrl());
                                    }
                                    if (StringUtils.isNotBlank(product.getMainImage())) {
                                        productRpcVo.setMainImage(product.getMainImage());
                                    }
                                    productRpcVos.add(productRpcVo.build());
                                });
                                rankVoBuilder.addAllProducts(productRpcVos);
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getKeyword())) {
                                rankVoBuilder.setKeyword(rankParamVo.getKeyword());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getKeywordId())) {
                                rankVoBuilder.setKeywordId(rankParamVo.getKeywordId());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getUrl())) {
                                rankVoBuilder.setUrl(rankParamVo.getUrl());
                            }
                            if (rankParamVo.getShopId() != null) {
                                rankVoBuilder.setShopId(rankParamVo.getShopId());
                            }
                            voBuilder.setRankVo(rankVoBuilder.build());
                        }
                        voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));


                        //填充环比数据
                        if (MapUtils.isNotEmpty(finalCompareQueryMap)) {
                            String mapKey = (StringUtils.isNotBlank(item.getKeywordId()) ? item.getKeywordId() : item.getTargetId()).concat("#").concat(Optional.ofNullable(item.getQuery()).orElse(""));
                            if (finalCompareQueryMap.containsKey(mapKey)) {
                                ReportVo compareItem = finalCompareQueryMap.get(mapKey);
                                voBuilder.setCompareCpc(DoubleValue.of(Optional.ofNullable(compareItem.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareRoas(DoubleValue.of(Optional.ofNullable(compareItem.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAsots(DoubleValue.of(Optional.ofNullable(compareItem.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcots(DoubleValue.of(Optional.ofNullable(compareItem.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                                voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                                voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                                voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                                voBuilder.setCompareSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getSaleNum()).orElse(0)));
                                voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                                voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                                voBuilder.setCompareClickRate(DoubleValue.of(Optional.ofNullable(compareItem.getClickRate()).orElse(0.0)));
                                voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdClickRatio(DoubleValue.of(Optional.ofNullable(compareItem.getAdClickRatio()).orElse(0.0)));
                                voBuilder.setCompareAdConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getAdConversionRate()).orElse(0.0)));
                                voBuilder.setCompareSales(DoubleValue.of(Optional.ofNullable(compareItem.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdOtherSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdOtherSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareSalesConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getSalesConversionRate()).orElse(0.0)));
                                voBuilder.setCompareCost(DoubleValue.of(Optional.ofNullable(compareItem.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcos(DoubleValue.of(Optional.ofNullable(compareItem.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                                // 花费占比
                                voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                                // 销售额占比
                                voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                // 订单量占比
                                voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                // 销量占比
                                voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrand14d()).orElse(0)));
                                voBuilder.setCompareSalesNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressionRank(Int32Value.of(Optional.ofNullable(compareItem.getImpressionRank()).orElse(0)));
                                voBuilder.setCompareImpressionShare(DoubleValue.of(Optional.ofNullable(compareItem.getImpressionShare()).orElse(0.0)));
                                voBuilder.setCompareOrderRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrderRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareSalesRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareOrdersNewToBrandPercentageFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrdersNewToBrandPercentage14d()).orElse(0.0)));
                                voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                                //环比值,计算
                                voBuilder.setCompareCpaRate(voBuilder.getCompareCpa().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpa().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCpcRate(voBuilder.getCompareCpc().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpc().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareRoasRate(voBuilder.getCompareRoas().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getRoas().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAsotsRate(voBuilder.getCompareAsots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAsots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcotsRate(voBuilder.getCompareAcots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressions().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClicks().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());


                                voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdOtherOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherOrderNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSaleNumRate(voBuilder.getCompareSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdOtherSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdSelfSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSelfSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSelfSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClickRateRate(voBuilder.getCompareClickRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClickRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdClickRatioRate(voBuilder.getCompareAdClickRatio().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdClickRatio().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdConversionRateRate(voBuilder.getCompareAdConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRate(voBuilder.getCompareSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOtherSalesRate(voBuilder.getCompareAdOtherSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdOtherSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
//
                                voBuilder.setCompareAdSalesRate(voBuilder.getCompareAdSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesConversionRateRate(voBuilder.getCompareSalesConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCostRate(voBuilder.getCompareCost().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCost().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCost().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCost().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcosRate(voBuilder.getCompareAcos().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcos().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesNewToBrandFTDRate(voBuilder.getCompareSalesNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionRankRate(voBuilder.getCompareImpressionRank().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionRank().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionShareRate(voBuilder.getCompareImpressionShare().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionShare().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderRateNewToBrandFTDRate(voBuilder.getCompareOrderRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRateNewToBrandFTDRate(voBuilder.getCompareSalesRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandPercentageFTDRate(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandPercentageFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 2), 100)));

                            }
                        }

                        return voBuilder.build();
                    }
            ).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        StopWatchUtil.remove();
        return AllQueryWordDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }

    private static List<com.meiyunji.sponsored.rpc.vo.AdStrategy> buildStrategyList(ReportVo item) {
        List<com.meiyunji.sponsored.rpc.vo.AdStrategy> strategyList = new ArrayList<>();
        for (AdStrategyVo strategyVo : item.getStrategyList()) {
            com.meiyunji.sponsored.rpc.vo.AdStrategy.Builder strategyBuilder = com.meiyunji.sponsored.rpc.vo.AdStrategy.newBuilder();
            strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
            strategyBuilder.setStatus(strategyVo.getStatus());
            strategyList.add(strategyBuilder.build());
        }
        return strategyList;
    }

    /**
     * 填充广告策略
     */
    public void fillAdStrategy(CpcQueryWordDto dto, List<ReportVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> queryIds = rows.stream().map(ReportVo::getQueryId).distinct().collect(Collectors.toList());
        List<String> groupIds = rows.stream().map(ReportVo::getAdGroupId).distinct().collect(Collectors.toList());
        // 根据投放id集合获取自动化规则受控对象
        List<AdvertiseAutoRuleStatus> autoRuleStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(), queryIds, AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString());
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleMap = StreamUtil.groupingBy(autoRuleStatuses, AdvertiseAutoRuleStatus::getItemId);
        List<AdvertiseAutoRuleStatus> autoRuleGroupStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(), groupIds, AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString());
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleGroupMap = StreamUtil.groupingBy(autoRuleGroupStatuses, AdvertiseAutoRuleStatus::getItemId);
        for (ReportVo vo : rows) {
            // 自动化规则标签
            List<AdStrategyVo> adstrategyList = new ArrayList<>();
            // key:标签 value:状态集合
            Map<String,List<String>> strategyMap = new HashMap<>();
            if(autoRuleMap.containsKey(vo.getQueryId())){
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleMap.get(vo.getQueryId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdQueryStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtil.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
            }
            if(autoRuleGroupMap.containsKey(vo.getAdGroupId())){
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleGroupMap.get(vo.getAdGroupId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdQueryStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtil.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
            }
            // 自动化规则标签
            for (String strategy : strategyMap.keySet()) {
                int status = 0;
                List<String> statusList = strategyMap.get(strategy);
                if(statusList.contains("ENABLED")){
                    status = 1;
                }
                AdStrategyVo strategyVo = new AdStrategyVo();
                strategyVo.setAdStrategyType(strategy);
                strategyVo.setStatus(status);
                adstrategyList.add(strategyVo);
            }
            if(SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(vo.getMatchType())){
                // 过滤主题投放
                vo.setStrategyList(new ArrayList<>());
            }else{
                vo.setStrategyList(adstrategyList);
            }
        }
    }

    /**
     * 出单搜索词
     * 填充 品牌细节 默认信息
     */
    private static void fillDefaultBrandMessage(SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder voBuilder, SearchTermBO item, boolean fillDefault) {
        if (TargetTypeEnum.category.name().equalsIgnoreCase(item.getMatchType())) {
            String brandName = org.apache.commons.lang3.StringUtils.isNotBlank(item.getBrandName()) ? item.getBrandName() : fillDefault ? BrandMessageConstants.DEFAULT_BRAND_NAME : null;
            String commodityPriceRange = org.apache.commons.lang3.StringUtils.isNotBlank(item.getCommodityPriceRange()) ? item.getCommodityPriceRange() : fillDefault ? BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE : null;
            String rating = org.apache.commons.lang3.StringUtils.isNotBlank(item.getCategoryRating()) ? item.getCategoryRating() : fillDefault ? BrandMessageConstants.DEFAULT_RATING : null;
            String distribution = org.apache.commons.lang3.StringUtils.isNotBlank(item.getDistribution()) ? item.getDistribution() : fillDefault ? BrandMessageConstants.DEFAULT_DISTRIBUTION : null;

            if (org.apache.commons.lang3.StringUtils.isNotBlank(brandName)) {
                voBuilder.setBrandName(brandName);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(commodityPriceRange)) {
                voBuilder.setCommodityPriceRange(commodityPriceRange);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(rating)) {
                voBuilder.setCategoryRating(rating);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(distribution)) {
                voBuilder.setDistribution(distribution);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getLookBack())) {
            voBuilder.setLookBack(item.getLookBack());
        }
    }

    /**
     * 填充 品牌细节 默认信息
     */
    private void fillDefaultBrandMessage(ReportRpcVo.Builder voBuilder, ReportVo item, boolean fillDefault) {
        if (TargetTypeEnum.category.name().equalsIgnoreCase(item.getMatchType())) {
            String brandName = StringUtils.isNotBlank(item.getBrandName()) ? item.getBrandName() : fillDefault ? BrandMessageConstants.DEFAULT_BRAND_NAME : null;
            String commodityPriceRange = StringUtils.isNotBlank(item.getCommodityPriceRange()) ? item.getCommodityPriceRange() : fillDefault ? BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE : null;
            String rating = StringUtils.isNotBlank(item.getCategoryRating()) ? item.getCategoryRating() : fillDefault ? BrandMessageConstants.DEFAULT_RATING : null;
            String distribution = StringUtils.isNotBlank(item.getDistribution()) ? item.getDistribution() : fillDefault ? BrandMessageConstants.DEFAULT_DISTRIBUTION : null;

            if (StringUtils.isNotBlank(brandName)) {
                voBuilder.setBrandName(brandName);
            }
            if (StringUtils.isNotBlank(commodityPriceRange)) {
                voBuilder.setCommodityPriceRange(commodityPriceRange);
            }
            if (StringUtils.isNotBlank(rating)) {
                voBuilder.setCategoryRating(rating);
            }
            if (StringUtils.isNotBlank(distribution)) {
                voBuilder.setDistribution(distribution);
            }
        }
        if (StringUtils.isNotBlank(item.getLookBack())) {
            voBuilder.setLookBack(item.getLookBack());
        }
    }

    @Override
    public AllSearchTermDataResponse.AllSearchTermData getAllSearchTermList(Integer puid, CpcQueryWordDto dto, Page page) {
        if (dto.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
        }
        Page pageVo = getDorisAllSearchTermPage(puid, dto, page);

        //处理分页
        AllSearchTermDataResponse.AllSearchTermData.Page.Builder pageBuilder = AllSearchTermDataResponse.AllSearchTermData.Page.newBuilder();
        pageBuilder.setPageNo(pageVo.getPageNo());
        pageBuilder.setPageSize(pageVo.getPageSize());
        pageBuilder.setTotalPage(pageVo.getTotalPage());
        pageBuilder.setTotalSize(pageVo.getTotalSize());
        List<ReportVo> rows = pageVo.getRows();

        if (CollectionUtils.isNotEmpty(rows)) {
            //环比数据
            Map<String, ReportVo> compareQueryMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());
                //通过asin精确查询
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).distinct().collect(Collectors.joining(StringUtil.SPECIAL_COMMA)).toLowerCase());
                // 查对比数据的时候不需要联表
                boolean isQueryJoinSearchTermsRank = dto.isQueryJoinSearchTermsRank();
                dto.setQueryJoinSearchTermsRank(false);
                Page<ReportVo> pageCompare = getDorisAllSearchTermPage(puid, dto, new Page(1, page.getPageSize()));
                // 还原字段
                dto.setQueryJoinSearchTermsRank(isQueryJoinSearchTermsRank);
                compareQueryMap = pageCompare.getRows().stream()
                        .collect(Collectors.toMap(k -> Optional.ofNullable(k.getQuery()).orElse(""), Function.identity(), (a, b) -> a));
            }
            Map<String, ReportVo> finalCompareQueryMap = compareQueryMap;


            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setAdTypes(item.getType());
                }
                if (StringUtils.isNotBlank(item.getQuery())) {
                    voBuilder.setQuery(item.getQuery());
                }
                if (StringUtils.isNotBlank(item.getQueryCn())) {
                    voBuilder.setQueryCn(item.getQueryCn());
                }
                if (Objects.nonNull(item.getOccurrenceNum())){
                    voBuilder.setOccurrenceNum(item.getOccurrenceNum());
                }
                voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
                // ABA搜索词排名
                voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                if (item.getAdSales() != null) {
                    voBuilder.setAdSales(DoubleValue.of(item.getAdSales().doubleValue()));
                }
                if (item.getAdOtherSales() != null) {
                    voBuilder.setAdOtherSales(DoubleValue.of(item.getAdOtherSales().doubleValue()));
                }
                voBuilder.setMainImage("");
                if (StringUtils.isNotBlank(item.getMainImage())) {
                    if(item.getMainImage().endsWith("S60_.jpg")){
                        item.setMainImage(item.getMainImage().replace("S60_.jpg","S600_.jpg"));
                    }
                    voBuilder.setMainImage(item.getMainImage());
                }
                voBuilder.setIsKeywordType(BoolValue.of(item.getIsKeywordType()));
                if (StringUtils.isNotBlank(item.getSearchTermIdStr())) {
                    voBuilder.setSearchTermIdStr(item.getSearchTermIdStr());
                    voBuilder.addAllSearchTermIds(Arrays.asList(item.getSearchTermIdStr().split(",")));
                }

                //填充环比数据
                if (MapUtils.isNotEmpty(finalCompareQueryMap)) {
                    String mapKey = Optional.ofNullable(item.getQuery()).orElse("");
                    if (finalCompareQueryMap.containsKey(mapKey)) {
                        ReportVo compareItem = finalCompareQueryMap.get(mapKey);
                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        voBuilder.setCompareSalesConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getSalesConversionRate()).orElse(0.0)));
                        voBuilder.setCompareCost(DoubleValue.of(Optional.ofNullable(compareItem.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        voBuilder.setCompareCpc(DoubleValue.of(Optional.ofNullable(compareItem.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAcos(DoubleValue.of(Optional.ofNullable(compareItem.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareRoas(DoubleValue.of(Optional.ofNullable(compareItem.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareSales(DoubleValue.of(Optional.ofNullable(compareItem.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAdSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAdOtherSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdOtherSales()).orElse(BigDecimal.ZERO).doubleValue()));

                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareSalesConversionRateRate(voBuilder.getCompareSalesConversionRate().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getSalesConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareCostRate(voBuilder.getCompareCost().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getCost().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCost().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCost().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getImpressions().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getClicks().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareCpaRate(voBuilder.getCompareCpa().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getCpa().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareCpcRate(voBuilder.getCompareCpc().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getCpc().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareRoasRate(voBuilder.getCompareRoas().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getRoas().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareAcosRate(voBuilder.getCompareAcos().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getAcos().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareSalesRate(voBuilder.getCompareSales().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSales().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareAdOtherSalesRate(voBuilder.getCompareAdOtherSales().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getAdOtherSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                        voBuilder.setCompareAdSalesRate(voBuilder.getCompareAdSales().getValue() == 0 ? "-" :
                                (BigDecimal.valueOf(voBuilder.getAdSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue())))
                                        .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    }
                }

                return voBuilder.build();
            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        return AllSearchTermDataResponse.AllSearchTermData.newBuilder()
                .setPage(pageBuilder.build()).build();
    }

    @Override
    public List<SearchTermSourceTargetDetailResponse.SourceTargetDetail> searchTermSourceTargetDetail(Integer puid, CpcQueryWordDto dto) {
        List<SearchTermBO> searchTermBOS = odsCpcQueryKeywordReportDao.searchTermSourceTargetDetail(dto);
        if (CollectionUtils.isEmpty(searchTermBOS)) {
            return Collections.emptyList();
        }

        List<String> campaignIds = searchTermBOS.stream().filter(Objects::nonNull).map(SearchTermBO::getCampaignId).distinct().collect(Collectors.toList());
        List<String> spGroupIds = searchTermBOS.stream().filter(Objects::nonNull).filter(i -> Constants.SP.equalsIgnoreCase(i.getAdType()))
                .map(SearchTermBO::getAdGroupId).distinct().collect(Collectors.toList());
        List<String> sbGroupIds = searchTermBOS.stream().filter(Objects::nonNull).filter(i -> Constants.SB.equalsIgnoreCase(i.getAdType()))
                .map(SearchTermBO::getAdGroupId).distinct().collect(Collectors.toList());

        List<AmazonAdCampaignAll> campaignList = Lists.newArrayList();
        Map<String, AmazonAdPortfolio> portfolioMap;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            campaignList = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid, dto.getShopIdList(), campaignIds);
            List<String> portfolioIds = campaignList.stream().filter(Objects::nonNull).map(AmazonAdCampaignAll::getPortfolioId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.listByShopId(puid, dto.getShopIdList(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            } else {
                portfolioMap = null;
            }
        } else {
            portfolioMap = null;
        }
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));

        Map<String, AmazonAdGroup> spGroupMap;
        List<AmazonAdGroup> spGroupList = null;
        if (CollectionUtils.isNotEmpty(spGroupIds)) {
            spGroupList = amazonAdGroupDao.listByGroupIdsAndShopIdList(puid, dto.getShopIdList(), spGroupIds);
        }
        if (CollectionUtils.isNotEmpty(spGroupList)) {
            spGroupMap = spGroupList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        } else {
            spGroupMap = null;
        }

        Map<String, AmazonSbAdGroup> sbGroupMap;
        List<AmazonSbAdGroup> sbGroupList = null;
        if (CollectionUtils.isNotEmpty(sbGroupIds)) {
            sbGroupList = amazonSbAdGroupDao.getListByShopIdsAndGroupIds(puid, dto.getShopIdList(), sbGroupIds);
        }
        if (CollectionUtils.isNotEmpty(sbGroupList)) {
            sbGroupMap = sbGroupList.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        } else {
            sbGroupMap = null;
        }

        List<String> targetIdList = new ArrayList<>();
        Map<String, AmazonAdTargeting> targetMap = new HashMap<>();
        List<String> asins = new ArrayList<>();
        Map<String, AsinImage> asinMap = new HashMap<>();
        searchTermBOS.stream().filter(Objects::nonNull).forEach(item -> {
            if (Constants.SP.equalsIgnoreCase(item.getAdType()) && !Constants.TARGETING_EXPRESSION_PREDEFINED.equals(item.getTargetingType())) {
                String matchType;
                matchType = getMatchType(item);
                if (!SpKeywordGroupValueEnum.getTheme().equalsIgnoreCase(matchType) && !Constants.TARGETING_EXPRESSION_PREDEFINED.equals(item.getTargetingType())) {
                    targetIdList.add(item.getSearchId());
                }
                if (Pattern.compile(Constants.ASIN_REGEX).matcher(item.getTargetingExpression()).matches()) {
                    String expression = item.getTargetingExpression();
                    if (expression.startsWith("asin=")) {
                        String keywords = expression.substring("asin=".length());
                        if (keywords.startsWith("\"") && keywords.endsWith("\"")) {
                            asins.add(keywords.substring(1, keywords.length() - 1).toUpperCase());
                        }
                    } else if (expression.startsWith("asin-expanded=")) {
                        String keywords = expression.substring("asin-expanded=".length());
                        if (keywords.startsWith("\"") && keywords.endsWith("\"")) {
                            asins.add(keywords.substring(1, keywords.length() - 1).toUpperCase());
                        }
                    } else {
                        asins.add(expression);
                    }
                }
            }
        });

        if (CollectionUtils.isNotEmpty(asins)) {
            List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(dto.getPuid(), dto.getMarketplaceId(), asins);
            asinMap = listByAsins.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
        }

        //查询商品投放的图片和title信息/类目的相关信息
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            targetMap = amazonAdTargetingShardingDao.getByAdTargetIds(puid, dto.getShopId(), targetIdList)
                    .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
        }
        Map<String, AmazonAdTargeting> finalTargetMap = targetMap;
        Map<String, AsinImage> finalAsinMap = asinMap;
        return searchTermBOS.stream().filter(Objects::nonNull).map(item -> {
            SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder = SearchTermSourceTargetDetailResponse.SourceTargetDetail.newBuilder();
            builder.setQuery(item.getQuery());
            builder.setType(item.getAdType());

            if (Constants.SP.equalsIgnoreCase(item.getAdType())) {
                //搜索词为ASIN类型
                if (Pattern.compile(Constants.ASIN_REGEX).matcher(item.getQuery()).matches()) {
                    builder.setUseTargeting(true);
                    String matchType;
                    matchType = getMatchType(item);
                    builder.setMatchType(matchType);
                    if (StringUtils.isNotBlank(item.getTargetingExpression())) {
                        builder.setTargetingExpression(item.getTargetingExpression().trim());
                    }
                    //自动投放-来源投放直接显示自动投放组
                    handleNonThemeMatch(item, builder, finalTargetMap, finalAsinMap);
                } else {
                    //搜索词为非ASIN类型
                    builder.setUseTargeting(false);
                    if (item.getType().equalsIgnoreCase("keyword")) {
                        builder.setMatchType(item.getMatchType());
                        if (StringUtils.isNotBlank(item.getKeywordText())) {
                            builder.setKeywordText(item.getKeywordText());
                        }
                        handleNonThemeMatch(item, builder, finalTargetMap, finalAsinMap);
                    } else {
                        //asin有自动和手动
                        // 兼容前端
                        String matchType;
                        matchType = getMatchType(item);
                        builder.setMatchType(matchType);
                        //匹配类型为主题投放
                        if (SpKeywordGroupValueEnum.getTheme().equalsIgnoreCase(matchType)) {
                            builder.setKeywordText(item.getTargetingExpression());
                        } else {
                            handleNonThemeMatch(item, builder, finalTargetMap, finalAsinMap);
                        }
                    }
                }
            } else {
                builder.setUseTargeting(false);
                builder.setMatchType(item.getMatchType());
                if (StringUtils.isNotBlank(item.getKeywordText())) {
                    if (SbMatchValueEnum.THEME.getMatchType().equalsIgnoreCase(item.getMatchType())) {
                        String keywordText = item.getKeywordText();
                        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(keywordText)
                                || Constants.keywords_related_to_your_brand.equalsIgnoreCase(keywordText)) {
                            builder.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(keywordText)
                                || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(keywordText)) {
                            builder.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                        } else {
                            builder.setKeywordText(keywordText);
                        }
                    } else {
                        builder.setKeywordText(item.getKeywordText());
                    }
                }
            }

            builder.setCampaignId(item.getCampaignId());
            if (MapUtils.isNotEmpty(campaignMap) && campaignMap.containsKey(item.getCampaignId())) {
                AmazonAdCampaignAll campaign = campaignMap.get(item.getCampaignId());
                builder.setShopId(campaign.getShopId());
                builder.setMarketplaceId(campaign.getMarketplaceId());
                builder.setCampaignName(campaign.getName());
                builder.setCampaignState(campaign.getState());
                if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                    builder.setPortfolioId(campaign.getPortfolioId());
                    if (portfolioMap != null && portfolioMap.containsKey(campaign.getPortfolioId())) {
                        AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                        builder.setPortfolioName(amazonAdPortfolio.getName());
                    } else {
                        builder.setPortfolioName("广告组合待同步");
                    }
                } else {
                    builder.setPortfolioName("-");
                }
            } else {
                builder.setCampaignName("-");
            }

            builder.setAdGroupId(item.getAdGroupId());
            if (Constants.SP.equalsIgnoreCase(item.getAdType())) {
                if (MapUtils.isNotEmpty(spGroupMap) && spGroupMap.containsKey(item.getAdGroupId())) {
                    AmazonAdGroup amazonAdGroup = spGroupMap.get(item.getAdGroupId());
                    builder.setAdGroupName(amazonAdGroup.getName());
                    builder.setAdGroupType(amazonAdGroup.getAdGroupType());
                    builder.setAdGroupState(amazonAdGroup.getState());
                } else {
                    builder.setAdGroupName("-");
                }
            }
            if (Constants.SB.equalsIgnoreCase(item.getAdType())) {
                if (MapUtils.isNotEmpty(sbGroupMap) && sbGroupMap.containsKey(item.getAdGroupId())) {
                    AmazonSbAdGroup sbAdGroup = sbGroupMap.get(item.getAdGroupId());
                    builder.setAdGroupName(sbAdGroup.getName());
                    builder.setAdGroupType(sbAdGroup.getAdGroupType());
                    builder.setAdGroupState(sbAdGroup.getState());
                } else {
                    builder.setAdGroupName("-");
                }
            }
            //true 都不为T00030
            fillDefaultBrandMessage(builder, item, true);
            builder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
            builder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
            builder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
            builder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
            builder.setSales(DoubleValue.of(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));
            builder.setAdSales(DoubleValue.of(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));
            builder.setAdOtherSales(DoubleValue.of(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));

            Double salesConversionRate = item.getClicks() == 0 ? 0.00 :
                    DoubleUtil.divide(Double.valueOf(item.getSaleNum()) * 100, Double.valueOf(item.getClicks()), 2);
            builder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(salesConversionRate).orElse(0.0)));

            BigDecimal cpc = BigDecimal.valueOf(item.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(item.getCost(), BigDecimal.valueOf(item.getClicks()));
            builder.setCpc(DoubleValue.of(Optional.ofNullable(cpc).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));

            BigDecimal cpa = BigDecimal.valueOf(item.getSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(item.getCost(), BigDecimal.valueOf(item.getSaleNum()));
            builder.setCpa(DoubleValue.of(Optional.ofNullable(cpa).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));

            BigDecimal acos = item.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    MathUtil.divide(item.getCost(), item.getTotalSales());
            acos = MathUtil.multiply(acos, BigDecimal.valueOf(100));
            builder.setAcos(DoubleValue.of(Optional.ofNullable(acos).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));

            BigDecimal roas = item.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    item.getTotalSales().divide(item.getCost(), 2, RoundingMode.HALF_UP);
            builder.setRoas(DoubleValue.of(Optional.ofNullable(roas).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).doubleValue()));

            return builder.build();
        }).collect(Collectors.toList());
    }

    private void handleNonThemeMatch(SearchTermBO item, SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder, Map<String, AmazonAdTargeting> finalTargetMap,Map<String, AsinImage> finalAsinMap) {
        if (Constants.TARGETING_EXPRESSION_PREDEFINED.equals(item.getTargetingType())) {
            builder.setKeywordText("自动投放组");
        } else {
            if (finalTargetMap.containsKey(item.getSearchId())) {
                AmazonAdTargeting amazonAdTargeting = finalTargetMap.get(item.getSearchId());
                handleTargeting(item, builder, amazonAdTargeting, finalAsinMap);
            }
        }
    }

    private void handleTargeting(SearchTermBO item, SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder, AmazonAdTargeting amazonAdTargeting, Map<String, AsinImage> finalAsinMap) {
        if (amazonAdTargeting != null) {
            if (TargetTypeEnum.category.name().equals(amazonAdTargeting.getType())) {
                handleCategoryTargeting(item, builder, amazonAdTargeting);
            } else if (TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
                handleAsinTargeting(item, builder, amazonAdTargeting);
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getResolvedExpression()) && TargetTypeEnum.category.name().equalsIgnoreCase(amazonAdTargeting.getType())) {
                handleResolvedExpression(builder, amazonAdTargeting, item);
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetingValue()) && (StringUtils.isBlank(amazonAdTargeting.getTitle()) || StringUtils.isBlank(amazonAdTargeting.getImgUrl()))) {
                AsinImage asinImage = finalAsinMap.get(amazonAdTargeting.getTargetingValue().toUpperCase());
                if (asinImage != null) {
                    Optional.ofNullable(asinImage.getTitle()).filter(StringUtils::isNotEmpty).ifPresent(t -> {
                        if (StringUtils.isEmpty(amazonAdTargeting.getTitle())) {
                            builder.setTargetTitle(t);
                        }
                    });
                    Optional.ofNullable(asinImage.getImage()).filter(StringUtils::isNotEmpty).ifPresent(m -> {
                        if (StringUtils.isEmpty(amazonAdTargeting.getImgUrl())) {
                            builder.setImageUrl(m);
                        }
                    });
                }
            }
        }
    }

    private void handleResolvedExpression(SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder, AmazonAdTargeting amazonAdTargeting, SearchTermBO item) {
        JSONArray jsonArray = JSONArray.parseArray(amazonAdTargeting.getResolvedExpression());
        if (jsonArray != null && !jsonArray.isEmpty()) {
            this.fillBrandMessage(builder, jsonArray, item);
        }
    }

    private static void handleAsinTargeting(SearchTermBO item, SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder, AmazonAdTargeting amazonAdTargeting) {
        builder.setMatchType(item.getMatchType());
        builder.setKeywordText(amazonAdTargeting.getTargetingValue().toUpperCase());
        if (StringUtils.isNotBlank(amazonAdTargeting.getImgUrl())) {
            builder.setImageUrl(amazonAdTargeting.getImgUrl());
        }
        if (StringUtils.isNotBlank(amazonAdTargeting.getTitle())) {
            builder.setTargetTitle(amazonAdTargeting.getTitle());
        }

    }

    private static void handleCategoryTargeting(SearchTermBO item, SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder, AmazonAdTargeting amazonAdTargeting) {
        builder.setCategory(amazonAdTargeting.getTargetingValue());
        builder.setMatchType(item.getMatchType());
        // 改版前路径是存在这个字段里的，因为要支持列表页模糊搜索改到了targeting_value
        if (StringUtils.isNotBlank(amazonAdTargeting.getCategoryPath())) {
            builder.setCategory(amazonAdTargeting.getCategoryPath());
        }
        //如果为数字ID,表明类目或品牌已经被amazon删除
        if (StringUtils.isNumeric(amazonAdTargeting.getCategoryPath())) {
            builder.setCategory("此类目亚马逊已删除");
        }
    }

    private static String getMatchType(SearchTermBO item) {
        String matchType;
        if ("close-match".equalsIgnoreCase(item.getTargetingExpression())) {
            matchType = "close_match";
        } else if ("loose-match".equalsIgnoreCase(item.getTargetingExpression())) {
            matchType = "loose_match";
        } else if ("substitutes".equalsIgnoreCase(item.getTargetingExpression())) {
            matchType = "substitutes";
        } else if ("complements".equalsIgnoreCase(item.getTargetingExpression())) {
            matchType = "complements";
        } else if (item.getTargetingExpression().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())) {
            matchType = "theme";
        } else {
            matchType = StringUtil.toStringSafe(item.getTargetingExpression());
        }
        return matchType;
    }

    public Page getDorisAllSearchTermPage(Integer puid, CpcQueryWordDto dto, Page page) {

        if (CollectionUtils.isNotEmpty(dto.getPortfolioIds()) || CollectionUtils.isNotEmpty(dto.getCampaignIds())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdByPortfolioIdCampaignId(puid, dto.getMarketplaceId(), dto.getShopIdList(), dto.getPortfolioIds(), dto.getCampaignIds());
            if (CollectionUtils.isEmpty(campaignIds)) {
                return page;
            }
            List<AmazonAdGroup> spGroups = amazonAdGroupDao.getGroupsByCampaignIdGroupId(puid, dto.getMarketplaceId(), dto.getShopIdList(), campaignIds, dto.getGroupIdList());
            List<AmazonSbAdGroup> sbGroups = amazonSbAdGroupDao.getGroupsByCampaignIdGroupId(puid, dto.getMarketplaceId(), dto.getShopIdList(), campaignIds, dto.getGroupIdList());
            List<String> groupIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(spGroups)) {
                groupIds.addAll(spGroups.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(sbGroups)) {
                groupIds.addAll(sbGroups.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(groupIds)) {
                return page;
            }
//            log.info("出单搜索词 检验之后的组id: {}", groupIds);
            dto.setGroupIdList(groupIds.stream().distinct().collect(Collectors.toList()));
        }

        // 筛选模板
        if (Boolean.TRUE.equals(dto.getOnlyCount())) {
            int count = odsCpcQueryKeywordReportDao.listCountSearchTermPageList(puid, dto, page);
            page.setTotalSize(count);
            return page;
        }

        page = odsCpcQueryKeywordReportDao.allSearchTermPageList(puid, dto, page);
        List<SearchTermBO> rowsList = page.getRows();
        if (CollectionUtils.isEmpty(rowsList)) {
            return page;
        }

        List<String> asins = rowsList.stream().filter(Objects::nonNull).map(SearchTermBO::getQuery)
                .filter(s -> Pattern.compile(Constants.ASIN_REGEX).matcher(s).matches()).distinct().collect(Collectors.toList());
        Map<String, List<AmazonAdProductMetadata>> metadataMap = null;
        if (CollectionUtils.isNotEmpty(asins)) {
            List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getByAsinSkus(puid, dto.getMarketplaceId(), null, asins);
            if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                Map<String, List<AmazonAdProductMetadata>> map = new HashMap<>();
                map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getAsin)));
                metadataMap = map;
            }
        }
        Map<String,List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;

        Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
        if (!dto.isQueryJoinSearchTermsRank()) {
            List<String> searchTerms = rowsList.stream().map(SearchTermBO::getQuery).distinct().collect(Collectors.toList());
            List<OdsWeekSearchTermsAnalysis> searchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, dto.getMarketplaceId());
            searchTermsAnalysisMap.putAll(searchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o, n) -> n)));
        }

        List<ReportVo> list = Lists.newArrayListWithExpectedSize(rowsList.size());
        rowsList.stream().filter(Objects::nonNull).forEach(e -> {
            ReportVo vo = new ReportVo();
            vo.setType(e.getAdType());
            vo.setQuery(e.getQuery());
            vo.setQueryCn(e.getQueryCn());
            vo.setOccurrenceNum(e.getOccurrenceNum());
            vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
            vo.setOrderNum(Optional.ofNullable(e.getSaleNum()).orElse(0));
            vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
            vo.setSales(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vo.setAdSales(Optional.ofNullable(e.getAdSales()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vo.setAdOtherSales(Optional.ofNullable(e.getAdOtherSales()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));

            Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
            vo.setSalesConversionRate(salesConversionRate);

            BigDecimal cpcRate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
            vo.setCpc(cpcRate.setScale(2, RoundingMode.HALF_UP));

            BigDecimal cpaRate = BigDecimal.valueOf(e.getSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getSaleNum()));
            vo.setCpa(cpaRate.setScale(2, RoundingMode.HALF_UP));

            BigDecimal acosRate = e.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getTotalSales());
            acosRate = MathUtil.multiply(acosRate, BigDecimal.valueOf(100));
            vo.setAcos(acosRate.setScale(2, RoundingMode.HALF_UP));

            if (e.getTotalSales() != null && e.getCost() != null) {
                vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().divide(e.getCost(), 2, RoundingMode.HALF_UP));
            }

            String mainImage = e.getMainImage();
            if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(vo.getQuery())) {
                AmazonAdProductMetadata metadata = finalMetadataMap.get(vo.getQuery()).get(0);
                if (metadata != null) {
                    mainImage = metadata.getImageUrl();
                }
            }
            if (StringUtils.isBlank(mainImage) && ("substitutes".equals(vo.getTargetingExpression()) || "complements".equals(vo.getTargetingExpression()) || (vo.getTargetingExpression() !=null && vo.getTargetingExpression().contains("asin")))) {
                mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
            } else if (StringUtils.isBlank(mainImage) && StringUtils.isNotBlank(vo.getQuery()) && Pattern.compile(Constants.ASIN_REGEX).matcher(vo.getQuery()).matches()) { //搜索词符合 b 开头，后面跟9位字母和数字的组合
                mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
            }
            if(mainImage != null && mainImage.endsWith("S60_.jpg")){
                mainImage = mainImage.replace("S60_.jpg","S600_.jpg");
            }
            vo.setMainImage(mainImage);

            // 联表时字段aba排名与周变化率肯定不为空
            if (dto.isQueryJoinSearchTermsRank()) {
                vo.setSearchFrequencyRank(e.getSearchFrequencyRank() == Integer.MAX_VALUE ? 0 : e.getSearchFrequencyRank());
            } else {
                OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(vo.getQuery().toLowerCase());
                if (searchTermsAnalysis != null) {
                    vo.setSearchFrequencyRank(searchTermsAnalysis.getSearchFrequencyRank());
                }
            }
            vo.setIsKeywordType(!(Pattern.compile(Constants.ASIN_REGEX).matcher(e.getQuery()).matches()));
            vo.setSearchTermIdStr(e.getIdStr());

            if (Pattern.compile(Constants.ASIN_REGEX).matcher(e.getQuery()).matches()) {
                vo.setQuery(e.getQuery().toUpperCase());
            }
            list.add(vo);
        });
        page.setRows(list);
        return page;
    }

    @Override
    public Page pageList(Integer puid, CpcQueryWordDto dto, Page page) {

        page = pageListHistoryOrNow(puid, dto, page);
        List<CpcQueryKeywordReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
            BigDecimal sumRange = shopSaleDto == null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
            poList.forEach(e -> {
                ReportVo vo = getVo(e, sumRange);
                vo.setQuery(e.getQuery());
                vo.setKeywordText(e.getKeywordText());
                vo.setMatchType(e.getMatchType());
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());
                vo.setKeywordId(e.getKeywordId());
                vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                //检查该搜索词是否已经加入到否定
                AmazonAdKeyword keyword = amazonAdKeywordDaoRoutingService.getByKeywordText(puid, dto.getShopId(), dto.getMarketplaceId(), e.getCampaignId(), e.getAdGroupId(), e.getQuery(), Constants.NEGATIVE);
                if (keyword != null && !Constants.ARCHIVED.equalsIgnoreCase(keyword.getState())) {
                    vo.setNegaType(keyword.getMatchType());
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            //获取列表页所有queryId
            List<String> queryIdList = cpcQueryKeywordReportDao.listQueryIdByQueryWordDto(dto);
            if (CollectionUtils.isEmpty(queryIdList)) {
                return page;
            }
            List<String> queryIds = wordRootKeywordSpDao.listQueryIdByWordRootAndQueryIdList(puid, dto.getShopId(), dto.getWordRoot(), queryIdList);
            if (CollectionUtils.isEmpty(queryIds)) {
                return page;
            }
            dto.setQueryIds(queryIds);
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            sumRange = BigDecimal.ZERO;
        } else {
            sumRange = shopSaleDto.getSumRange();
        }
        dto.setShopSales(sumRange);

        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getStatus()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getStatus(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcQueryKeywordReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                    matchTypeList.add(Constants.EXACT);
                    matchTypeList.add(Constants.BROAD);
                    matchTypeList.add(Constants.PHRASE);
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                    matchTypeList.add(Constants.NEGATIVEPHRASE);
                } else {
                    if ("isExact".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.EXACT);
                    }
                    if ("isBroad".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.BROAD);
                    }
                    if ("isPhrase".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.PHRASE);
                    }
                    if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.NEGATIVEEXACT);
                    }
                    if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                        matchTypeList.add(Constants.NEGATIVEPHRASE);
                    }
                }

            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonAdKeywordShardingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonAdNekeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        return page;
                    }
                } else {
                    return page;
                }
            } else {
                return page;
            }
        }

        page = pageManageListHistoryOrNow(puid, dto, page);
        AdMetricDto adMetricDto = getSumAdMetricData(puid, dto);
        List<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo> poList = page.getRows();
        if (poList != null && poList.size() > 0) {

            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getCampaignId).distinct().collect(Collectors.toList());
            List<String> groupIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getAdGroupId).distinct().collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            //批量查询广告活动和广告组
            List<AmazonAdCampaignAll> byCampaignIds = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
            }

            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(byCampaignIds)) {
                campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonAdGroup> groupMap = null;
            List<AmazonAdGroup> adGroupByIds = null;
            if (CollectionUtils.isNotEmpty(groupIds)) {
                adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
            }

            if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = new ReportVo();

                filterMetricData(e, vo, adMetricDto);

                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getSaleNum()).orElse(0));
                //广告销量(原来取saleNum字段，现在改成salesNum字段)
                vo.setSaleNum(Optional.ofNullable(e.getSalesNum()).orElse(0));
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setSales(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));

                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);

                BigDecimal rate2 = e.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getTotalSales());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getSaleNum()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getTotalSales() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));

                vo.setQuery(e.getQuery());
                vo.setQueryCn(e.getQueryCn());
                if (e.getType().equalsIgnoreCase("keyword")) {
                    vo.setKeywordText(e.getKeywordText());
                    vo.setMatchType(e.getMatchType());
                    vo.setKeywordId(e.getKeywordId());
                    vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                    vo.setIsTargetType(false);
                } else {
                    vo.setTargetId(e.getTargetId());
                    vo.setMatchType(e.getTargetingExpression());
                    vo.setKeywordText(Constants.TARGETING_EXPRESSION_PREDEFINED.equals(e.getTargetingType()) ? "自动投放组" : "商品投放组");
                    vo.setIsTargetType(true);
                    vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                }

                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());

                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());

                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }

                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setDefaultBid(finalGroupMap.get(e.getAdGroupId()).getDefaultBid());
                }

                /**
                 * TODO 广告报告重构
                 * 本广告产品销售额
                 */
                vo.setAdSales(e.getAdSales());
                //本广告产品订单量
                vo.setAdSaleNum(e.getAdSaleNum());
                //本广告产品销量
                vo.setAdSelfSaleNum(e.getAdOrderNum());
                //其他产品广告订单量
                int sumAdOtherOrderNum = e.getSaleNum() - e.getAdSaleNum();
                vo.setAdOtherOrderNum(sumAdOtherOrderNum);
                //其他产品广告销售额
                BigDecimal sumAdOtherSales = e.getTotalSales().subtract(e.getAdSales());
                vo.setAdOtherSales(sumAdOtherSales);
                //其他产品广告销量
                int sumAdOtherSaleNum = e.getSalesNum() - e.getAdOrderNum();
                vo.setAdOtherSaleNum(sumAdOtherSaleNum);
                // 广告笔单价
                vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page dorisPageExportList(Integer puid, CpcQueryWordDto dto, Page page) {
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            sumRange = BigDecimal.ZERO;
        } else {
            sumRange = shopSaleDto.getSumRange();
        }
        dto.setShopSales(sumRange);

        // 参数赋值
        if (setParam(puid, dto)) return page;

        if (dto.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
        }
        page = odsCpcQueryKeywordReportDao.pageManageList(puid, dto, page);
        AdMetricDto adMetricDto = odsCpcQueryKeywordReportDao.getSumAdMetricDto(puid, dto);
        List<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = poList.stream().map(e -> new WordTranslateQo(e.getMarketplaceId(), e.getQuery())).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, true);

            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getCampaignId).distinct().collect(Collectors.toList());
            List<String> groupIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getAdGroupId).distinct().collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            //批量查询广告活动和广告组
            List<AmazonAdCampaignAll> byCampaignIds = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
            }

            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(byCampaignIds)) {
                campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonAdGroup> groupMap = null;
            List<AmazonAdGroup> adGroupByIds = null;
            if (CollectionUtils.isNotEmpty(groupIds)) {
                adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
            }

            if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
            }

            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
            Map<String, Map<String, Set<String>>> groupKeywordMapList = this.getDorisKeywordData(puid, dto.getShopId(), groupIds);
            boolean supportAbaRankExport = GrayUtil.isHit(dto.getPuid(), dynamicRefreshConfiguration.getSupportAbaRankExportWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
            if (!dto.isQueryJoinSearchTermsRank() && supportAbaRankExport) {
                List<String> searchTerms = poList.stream().map(AdQueryOptionVo::getQuery).distinct().collect(Collectors.toList());
                List<OdsWeekSearchTermsAnalysis> searchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, dto.getMarketplaceId());
                searchTermsAnalysisMap.putAll(searchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o, n) -> n)));
            }

            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setQueryId(e.getQueryId());
                filterMetricData(e, vo, adMetricDto);

                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    Map<String, Set<String>> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    Set<String> matchTypeSet = amazonAdKeywords.get(e.getQuery().toLowerCase().trim());
                    if (CollectionUtils.isNotEmpty(matchTypeSet)) {
                        // vo.setIsExact(matchTypeSet.contains(Constants.EXACT.toLowerCase()));
                        if (matchTypeSet.contains(Constants.EXACT.toLowerCase())) {
                            vo.setIsExact(true);
                        }
                        if (matchTypeSet.contains(Constants.BROAD.toLowerCase())) {
                            vo.setIsBroad(true);
                        }
                        if (matchTypeSet.contains(Constants.PHRASE.toLowerCase())) {
                            vo.setIsPhrase(true);
                        }
                        if (matchTypeSet.contains(Constants.NEGATIVEEXACT.toLowerCase())) {
                            vo.setIsNegativeExact(true);
                        }
                        if (matchTypeSet.contains(Constants.NEGATIVEPHRASE.toLowerCase())) {
                            vo.setIsNegativePhrase(true);
                        }
                    }

                }

                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getSaleNum()).orElse(0));
                //广告销量(原来取saleNum字段，现在改成salesNum字段)
                vo.setSaleNum(Optional.ofNullable(e.getSalesNum()).orElse(0));
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setSales(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));

                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getSaleNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);

                BigDecimal rate2 = e.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getTotalSales());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getSaleNum()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getTotalSales() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));

                vo.setQuery(e.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(dto.getMarketplaceId(), e.getQuery())));
                if (e.getType().equalsIgnoreCase("keyword")) {
                    vo.setKeywordText(e.getKeywordText());
                    vo.setMatchType(e.getMatchType());
                    vo.setKeywordId(e.getKeywordId());
                    vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                    vo.setIsTargetType(false);
                } else {
                    vo.setTargetId(e.getTargetId());
                    vo.setMatchType(e.getTargetingExpression());
                    vo.setKeywordText(Constants.TARGETING_EXPRESSION_PREDEFINED.equals(e.getTargetingType()) ? "自动投放组" : "商品投放组");
                    vo.setIsTargetType(true);
                    vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                }

                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());

                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());

                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }

                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setDefaultBid(finalGroupMap.get(e.getAdGroupId()).getDefaultBid());
                }

                /**
                 * TODO 广告报告重构
                 * 本广告产品销售额
                 */
                vo.setAdSales(e.getAdSales());
                //本广告产品订单量
                vo.setAdSaleNum(e.getAdSaleNum());
                //本广告产品销量
                vo.setAdSelfSaleNum(e.getAdOrderNum());
                //其他产品广告订单量
                int sumAdOtherOrderNum = e.getSaleNum() - e.getAdSaleNum();
                vo.setAdOtherOrderNum(sumAdOtherOrderNum);
                //其他产品广告销售额
                BigDecimal sumAdOtherSales = e.getTotalSales().subtract(e.getAdSales());
                vo.setAdOtherSales(sumAdOtherSales);
                //其他产品广告销量
                int sumAdOtherSaleNum = e.getSalesNum() - e.getAdOrderNum();
                vo.setAdOtherSaleNum(sumAdOtherSaleNum);
                // 广告笔单价
                vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
                // 兼容特殊排序逻辑
                // 联表时字段aba排名与周变化率肯定不为空
                if (dto.isQueryJoinSearchTermsRank()) {
                    vo.setSearchFrequencyRank(e.getSearchFrequencyRank() == Integer.MAX_VALUE ? 0 : e.getSearchFrequencyRank());
                    if (vo.getSearchFrequencyRank() > 0) {
                        BigDecimal weekRatio = e.getWeekRatio().intValue() == Integer.MIN_VALUE ? BigDecimal.ZERO : e.getWeekRatio();
                        vo.setWeekRatio(weekRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                } else {
                    OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(vo.getQuery().toLowerCase());
                    if (searchTermsAnalysis != null) {
                        vo.setSearchFrequencyRank(searchTermsAnalysis.getSearchFrequencyRank());
                        vo.setWeekRatio(Optional.ofNullable(searchTermsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    /**
     * 构建关键词id和广告组合名字的map：campaignIds -> campaign entities -> portfolioIds -> portfolio entities -> portfolio name
     * 关键字最多会有100000个
     *
     * @param puid                 puid
     * @param poList               poList
     * @param shopIdCampaignIdsMap 店铺id -> campaignId列表
     * @return 关键词id和广告组合名字的map
     */
    private Map<String, String> buildKeywordIdPortfolioNameMap(int puid, List<AdQueryOptionVo> poList, Map<Integer, Set<String>> shopIdCampaignIdsMap) {
        //根据campaignIds查询一个任务中所有的广告活动列表
        List<AmazonAdCampaignAll> campaignList = new ArrayList<>();
        for (int shopId : shopIdCampaignIdsMap.keySet()) {
            List<List<String>> lists = Lists.partition(Lists.newArrayList(shopIdCampaignIdsMap.get(shopId)), 1000);
            for (List<String> campaignIdList : lists) {
                campaignList.addAll(amazonAdCampaignDao.getByCampaignIds(puid, shopId, campaignIdList));
            }
        }
        //根据portfolioIds查询一个任务中所有的广告组合列表
        Map<Integer, Set<String>> shopIdPortfolioIdsMap = campaignList.parallelStream().collect(Collectors.groupingBy(AmazonAdCampaignAll::getShopId, Collectors.mapping(AmazonAdCampaignAll::getPortfolioId, Collectors.toSet())));
        List<AmazonAdPortfolio> portfolioList = new ArrayList<>();
        for (int shopId : shopIdPortfolioIdsMap.keySet()) {
            List<List<String>> lists = Lists.partition(Lists.newArrayList(shopIdPortfolioIdsMap.get(shopId)), 1000);
            for (List<String> PortfolioIdList : lists) {
                portfolioList.addAll(amazonAdPortfolioDao.getByPortfolioIds(puid, shopId, PortfolioIdList));
            }
        }

        //通过得到的三个map，构建关键词id和广告组合名字的map
        //Map<String, String> keywordIdCampaignIdMap = poList.stream().collect(Collectors.toMap(vo -> (StringUtils.isEmpty(vo.getKeywordId()) ? vo.getTargetId() : vo.getKeywordId()) + vo.getCountDate(), AdQueryOptionVo::getCampaignId, (x, y) -> x));
        Map<String, String> keywordIdCampaignIdMap = new HashMap<>(poList.size());
        //为了排查问题方便打印日志做的操作，问题排查出来后必须进行相应处理
        for (AdQueryOptionVo adQueryOptionVo : poList) {
            if (adQueryOptionVo == null || (StringUtils.isEmpty(adQueryOptionVo.getKeywordId()) && StringUtils.isEmpty(adQueryOptionVo.getTargetId())) || adQueryOptionVo.getCampaignId() == null) {
                log.error("build keywordIdCampaignIdMap fail: puid: {}, adQueryOptionVo: {}", puid, JSON.toJSONString(adQueryOptionVo));
                throw new RuntimeException(String.format("build keywordIdCampaignIdMap fail: puid: %d, adQueryOptionVo: %s", puid, adQueryOptionVo == null ? "null" : JSON.toJSONString(adQueryOptionVo)));
            }
            String key = (StringUtils.isEmpty(adQueryOptionVo.getKeywordId()) ? adQueryOptionVo.getTargetId() : adQueryOptionVo.getKeywordId()) + adQueryOptionVo.getCountDate();
            if (keywordIdCampaignIdMap.containsKey(key)) {
                continue;
            }
            String value = adQueryOptionVo.getCampaignId();
            keywordIdCampaignIdMap.put(key, value);
        }
        Map<String, String> campaignIdportfolioIdMap = campaignList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, campaign -> StringUtils.isNotBlank(campaign.getPortfolioId()) ? campaign.getPortfolioId() : "", (x, y) -> x));
        Map<String, String> portfolioIdNameMap = portfolioList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, AmazonAdPortfolio::getName, (x, y) -> x));
        Map<String, String> keywordIdPortfolioNameMap = new HashMap<>(poList.size());
        keywordIdCampaignIdMap.forEach((k, v) -> {
            keywordIdPortfolioNameMap.put(k, portfolioIdNameMap.get(campaignIdportfolioIdMap.get(v)));
        });
        return keywordIdPortfolioNameMap;
    }

    @Override
    public Page pageManageListExport(Integer puid, SearchVo searchVo, Page page) {
        page = cpcQueryKeywordReportDao.pageManageListExport(puid, searchVo, page);

        List<AdQueryOptionVo> poList = page.getRows();

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());

            //查询基础数据
            Map<Integer, Set<String>> shopIdCampaignIdsMap = poList.parallelStream().collect(Collectors.groupingBy(AdQueryOptionVo::getShopId, Collectors.mapping(AdQueryOptionVo::getCampaignId, Collectors.toSet())));
            //查询广告活动基础数据
            Map<Integer, Map<String, DownloadCenterCampaignBaseDataBO>> campaignBaseDataMap =  reportFillBaseDataHelper.queryBaseData4DownloadByShopGroupIdList(puid, shopIdCampaignIdsMap, (a, b, c) -> amazonAdCampaignAllDao.queryBaseData4DownloadByCampaignIdList(a, b, c));
            //分组查询portfolioName
            Map<String, String> keywordIdPortfolioNameMap;
            try {
                keywordIdPortfolioNameMap = buildKeywordIdPortfolioNameMap(puid, poList, shopIdCampaignIdsMap);
            } catch (Exception e) {
                log.error("sp query portfolioName fail: ", e);
                keywordIdPortfolioNameMap = new HashMap<>();
            }
            Map<String, String> finalKeywordIdPortfolioNameMap = keywordIdPortfolioNameMap;
            poList.forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setShopId(e.getShopId());
                vo.setQuery(e.getQuery());
                vo.setTargetId(e.getTargetId());
                vo.setKeywordText(e.getKeywordText());
                if (StringUtils.isNotEmpty(e.getMatchType())) {
                    if (Constants.PHRASE.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("词组匹配");
                    } else if (Constants.EXACT.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("精确匹配");
                    } else if (Constants.BROAD.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("广泛匹配");
                    } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("紧密匹配");
                    } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(e.getMatchType())) {
                        vo.setMatchType("宽泛匹配");
                    }
                }

                vo.setPortfolioName(finalKeywordIdPortfolioNameMap.get((StringUtils.isEmpty(e.getKeywordId()) ? e.getTargetId() : e.getKeywordId()) + e.getCountDate()));
                ////为了排查问题方便打印日志做的操作，问题排查出来后必须进行相应处理
                try {
                    vo.setCampaignBudgetCurrencyCode(AmznEndpoint.getByMarketplaceId(e.getMarketplaceId()).getCurrencyCode().value());
                } catch (Exception exception) {
                    log.error("报告下载中心，搜索词报告数据查询失败，AdQueryOptionVo:{}", JSON.toJSONString(e), exception);
                    throw exception;
                }
                vo.setAdGroupId(e.getAdGroupId());
                vo.setCampaignId(e.getCampaignId());
                vo.setAdGroupName(e.getAdGroupName());
                vo.setCampaignName(e.getCampaignName());
                if (StringUtils.isNotBlank(e.getTargetingText())) {
                    if ("substitutes".equals(e.getTargetingText())) {
                        vo.setTargetingText("同类产品");
                    } else if ("close-match".equals(e.getTargetingText())) {
                        vo.setTargetingText("紧密匹配");
                    } else if ("loose-match".equals(e.getTargetingText())) {
                        vo.setTargetingText("宽泛匹配");
                    } else if ("complements".equals(e.getTargetingText())) {
                        vo.setTargetingText("关联产品");
                    } else if (e.getTargetingText().contains("category=")) {
                        String category = e.getTargetingText().substring(e.getTargetingText().indexOf("=") + 1,
                            e.getTargetingText().length() - 1);
                        vo.setTargetingText(category.replace("\"", ""));
                    } else if (e.getTargetingText().contains("asin=")) {
                        String asin = e.getTargetingText().substring(e.getTargetingText().indexOf("=") + 1,
                            e.getTargetingText().length() - 1);
                        vo.setTargetingText(asin.replace("\"", ""));
                    }
                }

                reportFillBaseDataHelper.fillCampaignBaseData4ReportVo(vo, e.getShopId(), e.getCampaignId(), campaignBaseDataMap);

                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setCpc(e.getCost() != null && e.getClicks() != null ? calculationRateBigDecimal(e.getCost(), BigDecimal.valueOf(e.getClicks()), false) : BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setClickRate(e.getClicks() != null && e.getImpressions() != null ? calculationRateDouble(Double.valueOf(e.getClicks()), Double.valueOf(e.getImpressions())) : 0.00);
                vo.setAcos(e.getCost() != null && e.getTotalSales() != null ? calculationRateBigDecimal(e.getCost(), e.getTotalSales(), true) : BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setRoas(getRoas(e.getTotalSales(), e.getCost()));
                vo.setSalesConversionRate(getSalesConversionRate(e.getClicks(), e.getOrderNum()));
                vo.setAttributedConversions7d(Optional.ofNullable(e.getOrderNum()).orElse(0));
                vo.setAttributedConversions7dSameSKU(Optional.ofNullable(e.getAdOrderNum()).orElse(0));
                if (e.getOrderNum() != null) {
                    if (e.getAdOrderNum() != null) {
                        vo.setAttributedConversions7dOtherSameSKU(e.getOrderNum() - e.getAdOrderNum());
                    } else {
                        vo.setAttributedConversions7dOtherSameSKU(e.getOrderNum());
                    }
                } else {
                    vo.setAttributedConversions7dOtherSameSKU(0);
                }
                vo.setAttributedSales7d(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setAttributedSales7dSameSKU(Optional.ofNullable(e.getAdSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                if (e.getTotalSales() != null) {
                    if (e.getAdSales() != null) {
                        vo.setAttributedSales7dOtherSameSKU(e.getTotalSales().subtract(e.getAdSales()));
                    } else {
                        vo.setAttributedSales7dOtherSameSKU(e.getTotalSales());
                    }
                } else {
                    vo.setAttributedSales7dOtherSameSKU(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
                }
                vo.setAttributedUnitsOrdered7d(Optional.ofNullable(e.getSaleNum()).orElse(0));
                vo.setAttributedUnitsOrdered7dSameSKU(Optional.ofNullable(e.getAdSaleNum()).orElse(0));
                if (e.getSaleNum() != null) {
                    if (e.getAdSaleNum() != null) {
                        vo.setAttributedUnitsOrdered7dOtherSameSKU(e.getSaleNum() - e.getAdSaleNum());
                    } else {
                        vo.setAttributedUnitsOrdered7dOtherSameSKU(e.getSaleNum());
                    }
                } else {
                    vo.setAttributedUnitsOrdered7dOtherSameSKU(0);
                }
                if (StringUtils.isNotBlank(e.getCountDate())) {
                    vo.setCountDate(e.getCountDate());
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    public Double getSalesConversionRate(Integer clicks, Integer orderNum) {
        if (clicks == null || orderNum == null) {
            return null;
        }
        return calculationRateDouble(Double.valueOf(orderNum), Double.valueOf(clicks));
    }

    public BigDecimal getRoas(BigDecimal totalSales, BigDecimal cost) {
        if (totalSales == null || cost == null) {
            return null;
        }
        return cost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalSales.divide(cost, 2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculationRateBigDecimal(BigDecimal value1, BigDecimal value2, Boolean per) {
        BigDecimal rate = value2.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(value1, value2);
        if (per) {
            rate = MathUtil.multiply(rate, BigDecimal.valueOf(100));
        }
        return rate.setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    private Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1 * 100, value2, 2);
    }

    @Override
    public ReportVo sumReport(Integer puid, CpcQueryWordDto dto) {
        CpcQueryKeywordReport report = sumReportHistoryOrNow(puid, dto);
        // 取店铺销售额
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange = shopSaleDto == null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
        return getVo(report, sumRange);
    }

    @Override
    public ReportVo sumManageReport(Integer puid, CpcQueryWordDto dto, BigDecimal sumRange) {
        CpcQueryKeywordReport report = sumManageReportHistoryOrNow(puid, dto);
        return getVo(report, sumRange);
    }

    @Override
    public Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page) {
        page = detailListHistoryOrNow(puid, dto, page);
        List<CpcQueryKeywordReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = getVoWithDateType(e, dto.getDateType(), null);
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public ReportVo sumDetailReport(Integer puid, CpcQueryWordDetailDto dto) {
        CpcQueryKeywordReport report = sumDetailReportHistoryOrNow(puid, dto);
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange = shopSaleDto == null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
        return getVo(report, sumRange);
    }

    @Override
    public List<ReportVo> detailListChart(int puid, CpcQueryWordDetailDto dto) {
        List<CpcQueryKeywordReport> poList = detailListChartHistoryOrNow(puid, dto);
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = getVoWithDateType(e, dto.getDateType(), null);
                list.add(vo);
            });
            return list;
        }
        return null;
    }

    @Override
    public AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());

        dto.setShopSales(shopSaleDto != null && shopSaleDto.getSumRange() != null ? shopSaleDto.getSumRange() : BigDecimal.ZERO);

        //组装vo
        long allKeywordTime = System.currentTimeMillis();
        Page pageVo = getAdQueryWordsPageVo(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 {} ==============================", System.currentTimeMillis() - allKeywordTime);
        //        if (pageVo.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
        //            int totalPage = Constants.TOTALSIZELIMIT / pageVo.getPageSize();
        //            pageVo.setTotalPage(totalPage);
        //            pageVo.setTotalSize(Constants.TOTALSIZELIMIT);
        //        }

        //处理分页
        AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.Builder pageBuilder = AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(pageVo.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(pageVo.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(pageVo.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(pageVo.getTotalSize()));
        List<ReportVo> rows = pageVo.getRows();

        //填充ABA展示信息（仅支持美国站）
        long t1 = Instant.now().toEpochMilli();
        fillABARankField(rows);
        log.info("填充ABA展示信息花费时间：{}", Instant.now().toEpochMilli() - t1);

        // 封装关键词实时排名参数
        //        long t2 = Instant.now().toEpochMilli();
        //        packageKeywordRankParam(puid, dto, rows);
        //        log.info("封装关键词实时排名参数耗时：{}", Instant.now().toEpochMilli() - t2);

        if (CollectionUtils.isNotEmpty(rows)) {

            //环比数据
            Map<String, ReportVo> compareQueryMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(),
                        dto.getCompareStartDate(), dto.getCompareEndDate());
                BigDecimal shopSalesByDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSalesByDataCompare = BigDecimal.ZERO;
                } else {
                    shopSalesByDataCompare = shopSaleDtoCompare.getSumRange();
                }
                dto.setShopSales(shopSalesByDataCompare);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());
                //通过asin精确查询本来
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).distinct().collect(Collectors.joining(StringUtil.SPECIAL_COMMA)));

                Page<ReportVo> pageCompare = getAdQueryWordsPageVo(puid, dto, new Page(1, page.getPageSize()));
                compareQueryMap = pageCompare.getRows().stream()
                        .collect(Collectors.toMap(k -> Optional.ofNullable(k.getKeywordId()).orElse("").concat("#")
                                .concat(Optional.ofNullable(k.getQuery()).orElse("")), Function.identity(), (a, b) -> a));
            }
            //调用运营工具服务接口
            getKeywordMonitor(puid, rows);
            Map<String, ReportVo> finalCompareQueryMap = compareQueryMap;
            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                        ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
                        if (org.apache.commons.lang.StringUtils.isNotBlank(item.getCountDate())) {
                            voBuilder.setCountDate(item.getCountDate());
                        }

                        if (item.getShopId() != null) {
                            voBuilder.setShopId(Int32Value.of(item.getShopId()));
                        }
                        if (item.getMarketplaceId() != null) {
                            voBuilder.setMarketplaceId(item.getMarketplaceId());
                        }
                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAsots(DoubleValue.of(Optional.ofNullable(item.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcots(DoubleValue.of(Optional.ofNullable(item.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
                        voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
                        voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
                        voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(item.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(item.getAdClickRatio()).orElse(0.0)));
                        voBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(item.getAdConversionRate()).orElse(0.0)));
                        voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
                        voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                        voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                        voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                        voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                        voBuilder.setNaturalClicks(org.apache.commons.lang.StringUtils.isNotBlank(item.getNaturalClicks()) ? item.getNaturalClicks() : "0");

                        if (item.getKeywordMonitor() != null) {
                            voBuilder.setKeywordMonitor(item.getKeywordMonitor());
                        } else {
                            voBuilder.setKeywordMonitor(0);
                        }
                        if (StringUtils.isNotBlank(item.getCampaignId())) {
                            voBuilder.setCampaignId(item.getCampaignId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupId())) {
                            voBuilder.setAdGroupId(item.getAdGroupId());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupType())) {
                            voBuilder.setAdGroupType(item.getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(item.getAdGroupName())) {
                            voBuilder.setAdGroupName(item.getAdGroupName());
                        }

                        if (StringUtils.isNotBlank(item.getCampaignName())) {
                            voBuilder.setCampaignName(item.getCampaignName());
                        }

                        if (StringUtils.isNotBlank(item.getKeywordText())) {
                            voBuilder.setKeywordText(item.getKeywordText());
                        }
                        if (StringUtils.isNotBlank(item.getMatchType())) {
                            voBuilder.setMatchType(item.getMatchType());
                        }
                        if (StringUtils.isNotBlank(item.getSku())) {
                            voBuilder.setSku(item.getSku());
                        }
                        if (StringUtils.isNotBlank(item.getAsin())) {
                            voBuilder.setAsin(item.getAsin());
                        }

                        if (StringUtils.isNotBlank(item.getQuery())) {
                            voBuilder.setQuery(item.getQuery());
                        }

                        if (StringUtils.isNotBlank(item.getQueryCn())) {
                            voBuilder.setQueryCn(item.getQueryCn());
                        }

                        if (StringUtils.isNotBlank(item.getParentAsin())) {
                            voBuilder.setParentAsin(item.getParentAsin());
                        }
                        if (StringUtils.isNotBlank(item.getTitle())) {
                            voBuilder.setTitle(item.getTitle());
                        }
                        if (StringUtils.isNotBlank(item.getMainImage())) {
                            voBuilder.setMainImage(item.getMainImage());
                        }
                        if (StringUtils.isNotBlank(item.getNegaType())) {
                            voBuilder.setNegaType(item.getNegaType());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingType())) {
                            voBuilder.setTargetingType(item.getTargetingType());
                        }
                        if (StringUtils.isNotBlank(item.getKeywordId())) {
                            voBuilder.setKeywordId(item.getKeywordId());
                        }
                        if (StringUtils.isNotBlank(item.getAdId())) {
                            voBuilder.setAdId(item.getAdId());
                        }
                        if (StringUtils.isNotBlank(item.getTargetingText())) {
                            voBuilder.setTargetingText(item.getTargetingText());
                        }
                        if (StringUtils.isNotBlank(item.getSpCampaignType())) {
                            voBuilder.setSpCampaignType(item.getSpCampaignType());
                        }
                        if (StringUtils.isNotBlank(item.getSpGroupType())) {
                            voBuilder.setSpGroupType(item.getSpGroupType());
                        }

                        if (StringUtils.isNotBlank(item.getSpTargetType())) {
                            voBuilder.setSpTargetType(item.getSpTargetType());
                        }

                        if (StringUtils.isNotBlank(item.getTargetId())) {
                            voBuilder.setTargetId(item.getTargetId());
                        }
                        if (item.getIsBroad() != null) {
                            voBuilder.setIsBroad(BoolValue.of(item.getIsBroad()));
                        }
                        if (item.getIsPhrase() != null) {
                            voBuilder.setIsPhrase(BoolValue.of(item.getIsPhrase()));
                        }
                        if (item.getIsExact() != null) {
                            voBuilder.setIsExact(BoolValue.of(item.getIsExact()));
                        }
                        if (item.getIsNegativeExact() != null) {
                            voBuilder.setIsNegativeExact(BoolValue.of(item.getIsNegativeExact()));
                        }
                        if (item.getIsNegativePhrase() != null) {
                            voBuilder.setIsNegativePhrase(BoolValue.of(item.getIsNegativePhrase()));
                        }
                        if (item.getIsTargetType() != null) {
                            voBuilder.setIsTargetType(BoolValue.of(item.getIsTargetType()));
                        }
                        if (item.getDefaultBid() != null) {
                            voBuilder.setDefaultBid(item.getDefaultBid());
                        }
                        if (item.getPortfolioId() != null) {
                            voBuilder.setPortfolioId(item.getPortfolioId());
                        }
                        if (item.getPortfolioName() != null) {
                            voBuilder.setPortfolioName(item.getPortfolioName());
                        }
                        if (item.getIsHidden() != null) {
                            voBuilder.setIsHidden(item.getIsHidden());
                        }
                        if (item.getType() != null) {
                            voBuilder.setType(item.getType());
                        }
                        /**
                         * TODO 广告报告重构
                         * 本广告产品销售额
                         */
                        if (item.getAdSales() != null) {
                            voBuilder.setAdSales(DoubleValue.of(item.getAdSales().doubleValue()));
                        }
                        //本广告产品订单量
                        if (item.getAdSaleNum() != null) {
                            voBuilder.setAdSaleNum(Int32Value.of(item.getAdSaleNum()));
                        }
                        //本广告产品销量
                        if (item.getAdSelfSaleNum() != null) {
                            voBuilder.setAdSelfSaleNum(Int32Value.of(item.getAdSelfSaleNum()));
                        }
                        //其他产品广告订单量
                        if (item.getAdOtherOrderNum() != null) {
                            voBuilder.setAdOtherOrderNum(Int32Value.of(item.getAdOtherOrderNum()));
                        }
                        //其他产品广告销售额
                        if (item.getAdOtherSales() != null) {
                            voBuilder.setAdOtherSales(DoubleValue.of(item.getAdOtherSales().doubleValue()));
                        }
                        //其他产品广告销量
                        if (item.getAdOtherSaleNum() != null) {
                            voBuilder.setAdOtherSaleNum(Int32Value.of(item.getAdOtherSaleNum()));
                        }
                        // ABA搜索词排名
                        voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                        // ABA排名周变化率
                        if (item.getWeekRatio() != null) {
                            voBuilder.setWeekRatio(String.valueOf(Optional.ofNullable(item.getWeekRatio()).orElse(BigDecimal.ZERO)));
                        }
                        // 封装关键词实时排名参数
                        if (item.getRankParamVo() != null) {
                            RankParamVo.Builder rankVoBuilder = RankParamVo.newBuilder();
                            KeywordsRankParamVo rankParamVo = item.getRankParamVo();
                            if (rankParamVo.getId() != null) {
                                rankVoBuilder.setId(rankParamVo.getId());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getSiteId())) {
                                rankVoBuilder.setSiteId(rankParamVo.getSiteId());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getSiteName())) {
                                rankVoBuilder.setSiteName(rankParamVo.getSiteName());
                            }
                            if (CollectionUtils.isNotEmpty(rankParamVo.getProducts())) {
                                List<ProductRpcVo> productRpcVos = Lists.newArrayList();
                                rankParamVo.getProducts().stream().forEach(product -> {
                                    ProductRpcVo.Builder productRpcVo = ProductRpcVo.newBuilder();
                                    if (StringUtils.isNotBlank(product.getAsin())) {
                                        productRpcVo.setAsin(product.getAsin());
                                        productRpcVo.setAsinUrl(product.getAsinUrl());
                                    }
                                    if (StringUtils.isNotBlank(product.getMainImage())) {
                                        productRpcVo.setMainImage(product.getMainImage());
                                    }
                                    productRpcVos.add(productRpcVo.build());
                                });
                                rankVoBuilder.addAllProducts(productRpcVos);
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getKeyword())) {
                                rankVoBuilder.setKeyword(rankParamVo.getKeyword());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getKeywordId())) {
                                rankVoBuilder.setKeywordId(rankParamVo.getKeywordId());
                            }
                            if (StringUtils.isNotBlank(rankParamVo.getUrl())) {
                                rankVoBuilder.setUrl(rankParamVo.getUrl());
                            }
                            if (rankParamVo.getShopId() != null) {
                                rankVoBuilder.setShopId(rankParamVo.getShopId());
                            }
                            voBuilder.setRankVo(rankVoBuilder.build());
                        }
                        voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                        //填充环比数据
                        if (MapUtils.isNotEmpty(finalCompareQueryMap)) {
                            String mapKey = Optional.ofNullable(item.getKeywordId()).orElse("").concat("#").concat(Optional.ofNullable(item.getQuery()).orElse(""));
                            if (finalCompareQueryMap.containsKey(mapKey)) {
                                ReportVo compareItem = finalCompareQueryMap.get(mapKey);
                                voBuilder.setCompareCpc(DoubleValue.of(Optional.ofNullable(compareItem.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareRoas(DoubleValue.of(Optional.ofNullable(compareItem.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAsots(DoubleValue.of(Optional.ofNullable(compareItem.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcots(DoubleValue.of(Optional.ofNullable(compareItem.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                                voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                                voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                                voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                                voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                                voBuilder.setCompareSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getSaleNum()).orElse(0)));
                                voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                                voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                                voBuilder.setCompareClickRate(DoubleValue.of(Optional.ofNullable(compareItem.getClickRate()).orElse(0.0)));
                                voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdClickRatio(DoubleValue.of(Optional.ofNullable(compareItem.getAdClickRatio()).orElse(0.0)));
                                voBuilder.setCompareAdConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getAdConversionRate()).orElse(0.0)));
                                voBuilder.setCompareSales(DoubleValue.of(Optional.ofNullable(compareItem.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdOtherSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdOtherSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAdSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdSales()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareSalesConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getSalesConversionRate()).orElse(0.0)));
                                voBuilder.setCompareCost(DoubleValue.of(Optional.ofNullable(compareItem.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareAcos(DoubleValue.of(Optional.ofNullable(compareItem.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                                // 花费占比
                                voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                                // 销售额占比
                                voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                                // 订单量占比
                                voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                                // 销量占比
                                voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                                voBuilder.setCompareOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(compareItem.getOrdersNewToBrand14d()).orElse(0)));
                                voBuilder.setCompareSalesNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO).doubleValue()));
                                voBuilder.setCompareImpressionRank(Int32Value.of(Optional.ofNullable(compareItem.getImpressionRank()).orElse(0)));
                                voBuilder.setCompareImpressionShare(DoubleValue.of(Optional.ofNullable(compareItem.getImpressionShare()).orElse(0.0)));
                                voBuilder.setCompareOrderRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrderRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareSalesRateNewToBrandFTD(DoubleValue.of(Optional.ofNullable(compareItem.getSalesRateNewToBrand14d()).orElse(0.0)));
                                voBuilder.setCompareOrdersNewToBrandPercentageFTD(DoubleValue.of(Optional.ofNullable(compareItem.getOrdersNewToBrandPercentage14d()).orElse(0.0)));
                                voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                                //环比值,计算
                                voBuilder.setCompareCpaRate(voBuilder.getCompareCpa().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpa().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCpcRate(voBuilder.getCompareCpc().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCpc().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareRoasRate(voBuilder.getCompareRoas().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getRoas().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAsotsRate(voBuilder.getCompareAsots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAsots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcotsRate(voBuilder.getCompareAcots().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressions().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClicks().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdOtherOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherOrderNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSaleNumRate(voBuilder.getCompareSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdOtherSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdSelfSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSelfSaleNum().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSelfSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareClickRateRate(voBuilder.getCompareClickRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getClickRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdClickRatioRate(voBuilder.getCompareAdClickRatio().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdClickRatio().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdConversionRateRate(voBuilder.getCompareAdConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRate(voBuilder.getCompareSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOtherSalesRate(voBuilder.getCompareAdOtherSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdOtherSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                //
                                voBuilder.setCompareAdSalesRate(voBuilder.getCompareAdSales().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAdSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesConversionRateRate(voBuilder.getCompareSalesConversionRate().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareCostRate(voBuilder.getCompareCost().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getCost().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCost().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCost().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAcosRate(voBuilder.getCompareAcos().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getAcos().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        (new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage())))
                                                .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesNewToBrandFTDRate(voBuilder.getCompareSalesNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionRankRate(voBuilder.getCompareImpressionRank().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionRank().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionRank().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareImpressionShareRate(voBuilder.getCompareImpressionShare().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getImpressionShare().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressionShare().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrderRateNewToBrandFTDRate(voBuilder.getCompareOrderRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrderRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareSalesRateNewToBrandFTDRate(voBuilder.getCompareSalesRateNewToBrandFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getSalesRateNewToBrandFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesRateNewToBrandFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                                voBuilder.setCompareOrdersNewToBrandPercentageFTDRate(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue() == 0 ? "-" :
                                        (BigDecimal.valueOf(voBuilder.getOrdersNewToBrandPercentageFTD().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue())))
                                                .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrdersNewToBrandPercentageFTD().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                                voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 2), 100)));

                            }
                        }

                        return voBuilder.build();
                    }
            ).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }
        StopWatchUtil.remove();
        return AllQueryWordDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }

    private void getKeywordMonitor(Integer puid, List<ReportVo> rows) {
        List<String> keywordTexts = rows.stream().map(ReportVo::getQuery).collect(Collectors.toList());
        CountByPuidAndKeywordTextParam param = new CountByPuidAndKeywordTextParam();
        param.setPuid(puid);
        param.setUid(puid);
        param.setMarketplaceId(rows.get(0).getMarketplaceId());
        param.setKeywordList(keywordTexts);
        List<MonitorKeywordVo> monitorKeywordVos = null;
        try {
            monitorKeywordVos = keywordRankMonitorFeignService.getCountByPuidAndKeywordText(param);
        } catch (Exception e) {
            log.error("广告定时任务调用工具服务异常:", e);
            return;
        }
        if (CollectionUtils.isEmpty(monitorKeywordVos)) {
            return;
        }
        Map<String, MonitorKeywordVo> monKeywordNumMap = monitorKeywordVos.stream().collect(Collectors.toMap(MonitorKeywordVo::getKeywordText, Function.identity(), (e1, e2) -> e1));
        rows.forEach(e -> {
            if (MapUtils.isNotEmpty(monKeywordNumMap) && monKeywordNumMap.containsKey(e.getQuery())) {
                e.setKeywordMonitor(monKeywordNumMap.get(e.getQuery()).getCount());
            }
        });
    }

    private void packageKeywordRankParam(Integer puid, CpcQueryWordDto param, List<ReportVo> rows) {
        if (CollectionUtils.isEmpty(rows) || Constants.SB.equalsIgnoreCase(param.getType())
                || Constants.SD.equalsIgnoreCase(param.getType())) {
            return;
        }
        String marketPlaceId = param.getMarketplaceId();
        AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(marketPlaceId);
        Map<String, List<AmazonAdProduct>> adGroupAndAsinsMap = new HashMap<>();
        List<String> adGroupIds = rows.stream().filter(item -> StringUtils.isNotBlank(item.getAdGroupId()))
                .map(ReportVo::getAdGroupId).distinct().collect(Collectors.toList());
        Map<String, AmazonAdProductMetadata> metadataMap = new HashMap<>();
        Map<String, ProductAdReportVo> asinImageMap = new HashMap<>();
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            List<AmazonAdProduct> amazonAdProducts = amazonAdProductDao.listValidByGroupIds(puid, param.getShopId(), adGroupIds);
            Set<String> skuSet = new HashSet<>();
            adGroupAndAsinsMap = amazonAdProducts.stream().filter(product -> StringUtils.isNotBlank(product.getAdGroupId())).peek(e -> skuSet.add(e.getSku())).collect(Collectors.groupingBy(AmazonAdProduct::getAdGroupId));
            if (CollectionUtils.isNotEmpty(skuSet)) {
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(puid, param.getShopId(), Lists.newArrayList(skuSet), null);
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    metadataMap = amazonAdProductMetadataList.stream().collect(Collectors.toMap(AmazonAdProductMetadata::getSku, Function.identity(), (e1, e2) -> e2));
                }
            }

            // 兼容老代码逻辑
            List<List<String>> partition = Lists.partition(Lists.newArrayList(skuSet), 100);
            for (List<String> batchSkus : partition) {
                List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(puid, param.getShopId(), "", batchSkus);
                if (CollectionUtils.isNotEmpty(asinBySkus)) {
                    asinImageMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductAdReportVo::getSku, Function.identity(), (e1, e2) -> e2)));
                }
            }
        }
        for (ReportVo vo : rows) {
            KeywordsRankParamVo keywordsRankParamVo = new KeywordsRankParamVo();
            //            keywordsRankParamVo.setId();
            keywordsRankParamVo.setSiteName(endpoint.getMarketplaceCN());
            keywordsRankParamVo.setSiteId(endpoint.getMarketplace());
            keywordsRankParamVo.setShopId(param.getShopId());
            if (CollectionUtils.isNotEmpty(adGroupAndAsinsMap.get(vo.getAdGroupId()))) {
                List<ProductVo> productVos = Lists.newArrayList();
                Map<String, AmazonAdProductMetadata> finalMetadataMap = metadataMap;
                adGroupAndAsinsMap.get(vo.getAdGroupId()).stream().forEach(item -> {
                    ProductVo productVo = new ProductVo();
                    if (StringUtils.isNotBlank(item.getAsin())) {
                        productVo.setAsin(item.getAsin());
                        productVo.setAsinUrl(StringUtil.getAmzProductUrlBySite(item.getAsin(), marketPlaceId));
                    }
                    if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(item.getSku())) {
                        AmazonAdProductMetadata metadata = finalMetadataMap.get(item.getSku());
                        productVo.setMainImage(metadata.getImageUrl());
                    }
                    if (MapUtils.isNotEmpty(asinImageMap) && asinImageMap.containsKey(item.getSku())) {
                        ProductAdReportVo productAdReportVo = asinImageMap.get(item.getSku());
                        productVo.setMainImage(productAdReportVo.getMainImage());
                    }
                    productVos.add(productVo);
                });
                keywordsRankParamVo.setProducts(productVos);
            }
            keywordsRankParamVo.setKeywordId(vo.getKeywordId());
            keywordsRankParamVo.setKeyword(vo.getQuery());
            String searchUrl = "http://" + endpoint.getDomain() + "/s?k=" + vo.getQuery();
            keywordsRankParamVo.setUrl(searchUrl);
            vo.setRankParamVo(keywordsRankParamVo);
        }
    }

    @Override
    public AdGroupKeywordRankResponse getAdGroupKeywordRank(Integer puid, AdGroupKeywordRankParam adGroupKeywordRankParam) {
        AdGroupKeywordRankResponse.Builder builder = AdGroupKeywordRankResponse.newBuilder();
        builder.setCode(Int32Value.of(Result.SUCCESS));
        if (!Constants.SP.equalsIgnoreCase(adGroupKeywordRankParam.getType())) {
            return builder.build();
        }
        String marketplaceId = getMarketplaceIdByShoId(adGroupKeywordRankParam.getShopId());
        AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
        KeywordRankParamVo.Builder keywordRank = buildKeywordRankParamVo(endpoint, adGroupKeywordRankParam);
        // 查询sku
        List<AmazonAdProduct> amazonAdProducts = amazonAdProductDao.listValidByGroupIds(puid, adGroupKeywordRankParam.getShopId(), adGroupKeywordRankParam.getGroupIds());
        if (CollectionUtils.isNotEmpty(amazonAdProducts)) {
            Set<String> skuSet = amazonAdProducts.stream().map(AmazonAdProduct::getSku).collect(Collectors.toSet());
            // 获取元数据sku
            Map<String, AmazonAdProductMetadata> metadataMap = getProductMetadata(skuSet, adGroupKeywordRankParam.getShopId(), puid);
            // 通过http获取
            Map<String, ProductAdReportVo> asinImageMap = getAsinHttpMap(skuSet, adGroupKeywordRankParam.getShopId(), puid);
            // 商品不为空
            if (CollectionUtils.isNotEmpty(amazonAdProducts)) {
                keywordRank.addAllProducts(buildProductRpcVo(amazonAdProducts, metadataMap, asinImageMap, endpoint));
            }
        }
        builder.setData(keywordRank.build());
        return builder.build();
    }

    /**
     * 通过http调用获取商品
     */
    private Map<String, ProductAdReportVo> getAsinHttpMap(Set<String> skuSet, Integer shopId, int puid) {
        Map<String, ProductAdReportVo> asinImageMap = new HashMap<>((int) (skuSet.size() / 0.75F) + 1);
        List<List<String>> partition = Lists.partition(Lists.newArrayList(skuSet), 100);
        for (List<String> batchSkus : partition) {
            List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(puid, shopId, "", batchSkus);
            if (CollectionUtils.isNotEmpty(asinBySkus)) {
                asinImageMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.toMap(ProductAdReportVo::getSku, Function.identity(), (e1, e2) -> e2)));
            }
        }
        return asinImageMap;
    }

    /**
     * 获取元数据
     */
    private Map<String, AmazonAdProductMetadata> getProductMetadata(Set<String> skuSet, Integer shopId, int puid) {
        Map<String, AmazonAdProductMetadata> metadataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(skuSet)) {
            List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(puid, shopId, Lists.newArrayList(skuSet), null);
            if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                metadataMap = amazonAdProductMetadataList.stream().collect(Collectors.toMap(AmazonAdProductMetadata::getSku, Function.identity(), (e1, e2) -> e2));
            }
        }
        return metadataMap;
    }

    /**
     * 构造商品基本信息
     */
    private List<ProductRpcVo> buildProductRpcVo(List<AmazonAdProduct> amazonAdProducts,
                                                 Map<String, AmazonAdProductMetadata> metadataMap,
                                                 Map<String, ProductAdReportVo> asinImageMap,
                                                 AmznEndpoint endpoint) {
        List<ProductRpcVo> rpcVos = new ArrayList<>(amazonAdProducts.size());
        for (AmazonAdProduct amazonAdProduct : amazonAdProducts) {
            ProductRpcVo.Builder builderProduct = ProductRpcVo.newBuilder();
            builderProduct.setAsin(StringUtil.toStringSafe(amazonAdProduct.getAsin()));
            builderProduct.setAsinUrl(StringUtil.getAmzProductUrlBySite(amazonAdProduct.getAsin(), endpoint));
            if (asinImageMap.containsKey(amazonAdProduct.getSku())) {
                builderProduct.setMainImage(StringUtil.toStringSafe(asinImageMap.get(amazonAdProduct.getSku()).getMainImage()));
                rpcVos.add(builderProduct.build());
                continue;
            }
            if (metadataMap.containsKey(amazonAdProduct.getSku())) {
                builderProduct.setMainImage(StringUtil.toStringSafe(metadataMap.get(amazonAdProduct.getSku()).getImageUrl()));
            }
            rpcVos.add(builderProduct.build());
        }
        return rpcVos;
    }

    /**
     * 构造基本信息
     */
    private KeywordRankParamVo.Builder buildKeywordRankParamVo(AmznEndpoint endpoint,
                                                               AdGroupKeywordRankParam adGroupKeywordRankParam) {
        KeywordRankParamVo.Builder builder = KeywordRankParamVo.newBuilder();
        builder.setSiteName(StringUtil.toStringSafe(endpoint.getMarketplaceCN()));
        builder.setSiteId(StringUtil.toStringSafe(endpoint.getMarketplace()));
        builder.setKeywordId(StringUtil.toStringSafe(adGroupKeywordRankParam.getKeywordId()));
        builder.setKeyword(StringUtil.toStringSafe(adGroupKeywordRankParam.getQuery()));
        String searchUrl = "http://" + endpoint.getDomain() + "/s?k=" + adGroupKeywordRankParam.getQuery();
        builder.setUrl(searchUrl);
        return builder;
    }

    /**
     * 校验和获取站点
     */
    private String getMarketplaceIdByShoId(Integer shopId) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        return shopAuth.getMarketplaceId();
    }

    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);

        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //按条件查询所有数据
        List<AdHomePerformancedto> list = new ArrayList<>();
        List<AdHomePerformancedto> listCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = new ArrayList<>();  //每日汇总数据

        int searchDataType = Optional.ofNullable(dto.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.Builder builder = AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder();

        boolean isNull = false;  // 查询的数据为空

        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            //获取列表页所有queryId
            List<String> queryIdList = cpcQueryKeywordReportDao.listQueryIdByQueryWordDto(dto);
            if (CollectionUtils.isEmpty(queryIdList)) {
                isNull = true;
            }
            List<String> queryIds = wordRootKeywordSpDao.listQueryIdByWordRootAndQueryIdList(puid, dto.getShopId(), dto.getWordRoot(), queryIdList);
            if (CollectionUtils.isEmpty(queryIds)) {
                isNull = true;
            }
            dto.setQueryIds(queryIds);
        }

        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcQueryKeywordReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if ("isExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.EXACT);
                }
                if ("isBroad".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.BROAD);
                }
                if ("isPhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.PHRASE);
                }
                if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                }
                if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEPHRASE);
                }
            }

            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonAdKeywordShardingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonAdNekeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        isNull = true;
                    }
                } else {
                    isNull = true;
                }
            } else {
                isNull = true;
            }
        }

        if (isNull) {  // 查询数据置为空
            list = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            List<AdHomePerformancedto> keywordList = getReportKeywordByDateOrHistory(puid, dto);
            if (CollectionUtils.isNotEmpty(keywordList)) {
                list.addAll(keywordList);
                List<String> keywordIdList = keywordList.stream().map(AdHomePerformancedto::getKeywordId).collect(Collectors.toList());
                if (searchChartData) {
                    List<AdHomePerformancedto> keywordDayList = getReportKeywordByKeywordIdListOrHistory(puid, dto, keywordIdList);
                    if (CollectionUtils.isNotEmpty(keywordDayList)) {
                        reportDayList.addAll(keywordDayList);
                    }
                }
            }
            if (dto.getIsCompare()) {
                CpcQueryWordDto compareDto = new CpcQueryWordDto();
                BeanUtils.copyProperties(dto, compareDto);
                compareDto.setStart(compareDto.getCompareStartDate());
                compareDto.setEnd(compareDto.getCompareEndDate());
                List<AdHomePerformancedto> keywordListCompare = getReportKeywordByDateOrHistory(puid, compareDto);
                listCompare.addAll(keywordListCompare);
            }

            List<AdHomePerformancedto> targetList = getReportTargetByDateOrHistory(puid, dto);
            if (CollectionUtils.isNotEmpty(targetList)) {
                list.addAll(targetList);
                List<String> targetIdList = targetList.stream().map(AdHomePerformancedto::getTargetId).collect(Collectors.toList());
                List<AdHomePerformancedto> targetDayList = getReportTargetByTargetIdListOrHistory(puid, dto, targetIdList);
                if (searchChartData) {
                    if (CollectionUtils.isNotEmpty(targetDayList)) {
                        reportDayList.addAll(targetDayList);
                    }
                }
            }

            if (dto.getIsCompare()) {
                CpcQueryWordDto compareDto = new CpcQueryWordDto();
                BeanUtils.copyProperties(dto, compareDto);
                compareDto.setStart(compareDto.getCompareStartDate());
                compareDto.setEnd(compareDto.getCompareEndDate());
                List<AdHomePerformancedto> targetListCompare = getReportTargetByDateOrHistory(puid, compareDto);
                listCompare.addAll(targetListCompare);
            }
        }

        //环比数据
        BigDecimal shopSaleDataCompare = BigDecimal.ZERO;
        if (dto.getIsCompare()) {
            ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate(), dto.getCompareEndDate());

            if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                shopSaleDataCompare = BigDecimal.ZERO;
            } else {
                shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
            }
        }
        if (searchChartData) {
            //处理chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, dto.getStart(), dto.getEnd(), reportDayList, shopSaleData);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSaleData);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSaleData);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos);
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryKeywordAggregateDataVo(list, listCompare, shopSaleData, shopSaleDataCompare, isVc);

        return builder
                .setAggregateDataVo(aggregateDataVo)
                .build();
    }

    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getDorisAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc =  ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        //店铺销售额
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);
        //获取币种
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        //按条件查询所有数据
        List<AdHomePerformancedto> listCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList;  //每日汇总数据
        boolean isNull = setParam(puid,dto);  // 查询的数据为空
        // 查询数据
        if (isNull) {
            reportDayList = new ArrayList<>();
        } else {
            if (dto.isQueryJoinSearchTermsRank()) {
                String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
                dto.setLastWeekSearchTermsRankDate(date);
            }
            //获取投放维度列表
            reportDayList = odsCpcQueryKeywordReportDao.getQueryKeywordReportAggregate(puid, dto, true, false);
            //获取对比数据
            if (dto.getIsCompare()) {
                listCompare.addAll(odsCpcQueryKeywordReportDao.getQueryKeywordReportAggregate(puid, dto, false, true));
            }
        }
        //环比数据
        BigDecimal shopSaleDataCompare = BigDecimal.ZERO;
        if (dto.getIsCompare()) {
            ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate(), dto.getCompareEndDate());
            if (shopSaleDtoCompare != null && shopSaleDtoCompare.getSumRange() != null) {  //店铺销售额
                shopSaleDataCompare = shopSaleDtoCompare.getSumRange();
            }
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryKeywordAggregateDataVo(reportDayList, listCompare, shopSaleData, shopSaleDataCompare, isVc);
        //处理chart数据
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, dto.getStart(), dto.getEnd(), reportDayList, shopSaleData);
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSaleData);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList, shopSaleData);
        return AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setAggregateDataVo(aggregateDataVo)
                .addAllDay(dayPerformanceVos)
                .addAllMonth(monthPerformanceVos)
                .addAllWeek(weekPerformanceVos)
                .build();
    }

    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getDorisAllSearchTermAggregateData(Integer puid, CpcQueryWordDto dto) {

        if (CollectionUtils.isNotEmpty(dto.getPortfolioIds()) || CollectionUtils.isNotEmpty(dto.getCampaignIds())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdByPortfolioIdCampaignId(puid, dto.getMarketplaceId(), dto.getShopIdList(), dto.getPortfolioIds(), dto.getCampaignIds());
            if (CollectionUtils.isEmpty(campaignIds)) {
                return AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder().build();
            }
            List<AmazonAdGroup> spGroups = amazonAdGroupDao.getGroupsByCampaignIdGroupId(puid, dto.getMarketplaceId(), dto.getShopIdList(), campaignIds, dto.getGroupIdList());
            List<AmazonSbAdGroup> sbGroups = amazonSbAdGroupDao.getGroupsByCampaignIdGroupId(puid, dto.getMarketplaceId(), dto.getShopIdList(), campaignIds, dto.getGroupIdList());
            List<String> groupIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(spGroups)) {
                groupIds.addAll(spGroups.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isNotEmpty(sbGroups)) {
                groupIds.addAll(sbGroups.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(groupIds)) {
                return AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder().build();
            }
            dto.setGroupIdList(groupIds.stream().distinct().collect(Collectors.toList()));
        }


        if (dto.isQueryJoinSearchTermsRank()) {
            String date = weekSearchTermsAnalysisService.getLatestDate(dto.getMarketplaceId());
            dto.setLastWeekSearchTermsRankDate(date);
        }
        AdHomeAggregateDataRpcVo.Builder aggregateDataBuilder = AdHomeAggregateDataRpcVo.newBuilder();
        SearchTermAggregateBO aggregateData = odsCpcQueryKeywordReportDao.allSearchTermAggregate(puid, dto, true);
        if (Objects.nonNull(aggregateData)){
            int clicks = Optional.ofNullable(aggregateData.getClicks()).orElse(0);
            int saleNum = Optional.ofNullable(aggregateData.getSaleNum()).orElse(0);
            BigDecimal cost = Optional.ofNullable(aggregateData.getCost()).orElse(BigDecimal.ZERO);
            Integer impressions = Optional.ofNullable(aggregateData.getImpressions()).orElse(0);
            BigDecimal totalSales = Optional.ofNullable(aggregateData.getTotalSales()).orElse(BigDecimal.ZERO);
            BigDecimal adSales = Optional.ofNullable(aggregateData.getAdSales()).orElse(BigDecimal.ZERO);
            BigDecimal adOtherSales = Optional.ofNullable(aggregateData.getAdOtherSales()).orElse(BigDecimal.ZERO);

            BigDecimal cvr = clicks == 0 ? BigDecimal.ZERO : new BigDecimal(saleNum).multiply(new BigDecimal("100")).divide(new BigDecimal(clicks), 2, RoundingMode.HALF_UP);
            BigDecimal cpc = clicks == 0 ? BigDecimal.ZERO : cost.divide(new BigDecimal(clicks), 2, RoundingMode.HALF_UP);
            BigDecimal cpa = cost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(saleNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(cost, BigDecimal.valueOf(saleNum));
            BigDecimal acos = totalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : cost.multiply(new BigDecimal("100")).divide(totalSales, 2, RoundingMode.HALF_UP);
            BigDecimal roas = cost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : totalSales.divide(cost, 2, RoundingMode.HALF_UP);

            aggregateDataBuilder
                    .setAdOrderNum(Int32Value.of(saleNum))
                    .setTotalSize(Optional.ofNullable(aggregateData.getTotalSize()).orElse(0))
                    .setCvr(cvr.stripTrailingZeros().toPlainString()) // 广告转化率
                    .setAdCost(cost.stripTrailingZeros().toPlainString())
                    .setImpressions(Int32Value.of(impressions))
                    .setClicks(Int32Value.of(clicks))
                    .setAdCostPerClick(cpc.stripTrailingZeros().toPlainString())
                    .setCpa(cpa.stripTrailingZeros().toPlainString())
                    .setAcos(acos.stripTrailingZeros().toPlainString())
                    .setRoas(roas.stripTrailingZeros().toPlainString())
                    .setAdSale(totalSales.stripTrailingZeros().toPlainString()) // 广告销售额
                    .setAdSales(adSales.stripTrailingZeros().toPlainString()) // 广告销售额
                    .setAdOtherSales(adOtherSales.stripTrailingZeros().toPlainString()) // 广告销售额
            ;

            if (dto.getIsCompare()) {
                List<String> queryList = odsCpcQueryKeywordReportDao.queryListAllSearchTermAggregateData(puid, dto);
                List<SearchTermAggregateBO> compareList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(queryList)) {
                    compareList.addAll(odsCpcQueryKeywordReportDao.allSearchTermAggregateDataByQueryList(puid, dto, dto.getCompareStartDate(), dto.getCompareEndDate(), queryList));
                    compareList.addAll(odsCpcQueryTargetingReportDao.allSearchTermAggregateDataByQueryList(puid, dto, dto.getCompareStartDate(), dto.getCompareEndDate(), queryList));
                    compareList.addAll(odsCpcSbQueryKeywordReportDao.allSearchTermAggregateDataByQueryList(puid, dto, dto.getCompareStartDate(), dto.getCompareEndDate(), queryList));
                    this.fillAllSearchTermAggregateDataCompare(aggregateDataBuilder, compareList);
                }
            }
        }
        return AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setAggregateDataVo(aggregateDataBuilder)
                .build();
    }

    /**
     * 填充对比数据
     * @param aggregateDataBuilder
     * @param compareAggregateData
     */
    private void fillAllSearchTermAggregateDataCompare(AdHomeAggregateDataRpcVo.Builder aggregateDataBuilder, List<SearchTermAggregateBO> compareAggregateDataList) {
        int compareClicks = compareAggregateDataList.stream().filter(e -> e.getClicks() != null).mapToInt(SearchTermAggregateBO::getClicks).sum();
        int compareSaleNum = compareAggregateDataList.stream().filter(e -> e.getSaleNum() != null).mapToInt(SearchTermAggregateBO::getSaleNum).sum();
        BigDecimal compareCost = compareAggregateDataList.stream().filter(item -> item.getCost() != null).map(SearchTermAggregateBO::getCost).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer compareImpressions = compareAggregateDataList.stream().filter(e -> e.getImpressions() != null).mapToInt(SearchTermAggregateBO::getImpressions).sum();
        BigDecimal compareTotalSales = compareAggregateDataList.stream().filter(item -> item.getTotalSales() != null).map(SearchTermAggregateBO::getTotalSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal compareAdSales = compareAggregateDataList.stream().filter(item -> item.getAdSales() != null).map(SearchTermAggregateBO::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal compareAdOtherSales = compareAggregateDataList.stream().filter(item -> item.getAdOtherSales() != null).map(SearchTermAggregateBO::getAdOtherSales).reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal compareCvr = compareClicks == 0 ? BigDecimal.ZERO : new BigDecimal(compareSaleNum).multiply(new BigDecimal("100")).divide(new BigDecimal(compareClicks), 2, RoundingMode.HALF_UP);
        BigDecimal compareCpc = compareClicks == 0 ? BigDecimal.ZERO : compareCost.divide(new BigDecimal(compareClicks), 2, RoundingMode.HALF_UP);
        BigDecimal compareCpa = compareCost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(compareSaleNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(compareCost, BigDecimal.valueOf(compareSaleNum));
        BigDecimal compareAcos = compareTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : compareCost.multiply(new BigDecimal("100")).divide(compareTotalSales, 2, RoundingMode.HALF_UP);
        BigDecimal compareRoas = compareCost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : compareTotalSales.divide(compareCost, 2, RoundingMode.HALF_UP);

        aggregateDataBuilder
                .setCompareAdOrderNum(Int32Value.of(compareSaleNum))
                .setCompareCvr(compareCvr.stripTrailingZeros().toPlainString()) // 广告转化率
                .setCompareAdCost(compareCost.stripTrailingZeros().toPlainString())
                .setCompareImpressions(Int32Value.of(compareImpressions))
                .setCompareClicks(Int32Value.of(compareClicks))
                .setCompareAdCostPerClick(compareCpc.stripTrailingZeros().toPlainString())
                .setCompareCpa(compareCpa.stripTrailingZeros().toPlainString())
                .setCompareAcos(compareAcos.stripTrailingZeros().toPlainString())
                .setCompareRoas(compareRoas.stripTrailingZeros().toPlainString())
                .setCompareAdSale(compareTotalSales.stripTrailingZeros().toPlainString()) // 广告销售额
                .setCompareAdSales(compareAdSales.stripTrailingZeros().toPlainString()) // 广告销售额
                .setCompareAdOtherSales(compareAdOtherSales.stripTrailingZeros().toPlainString()) // 广告销售额
        ;
        aggregateDataBuilder.setCompareAdOrderNumRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAdOrderNum().getValue(), aggregateDataBuilder.getCompareAdOrderNum().getValue()));
        aggregateDataBuilder.setCompareCvrRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getCvr(), aggregateDataBuilder.getCompareCvr()));
        aggregateDataBuilder.setCompareAdCostRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAdCost(), aggregateDataBuilder.getCompareAdCost()));
        aggregateDataBuilder.setCompareImpressionsRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getImpressions().getValue(), aggregateDataBuilder.getCompareImpressions().getValue()));
        aggregateDataBuilder.setCompareClicksRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getClicks().getValue(), aggregateDataBuilder.getCompareClicks().getValue()));
        aggregateDataBuilder.setCompareAdCostPerClickRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAdCostPerClick(), aggregateDataBuilder.getCompareAdCostPerClick()));
        aggregateDataBuilder.setCompareCpaRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getCpa(), aggregateDataBuilder.getCompareCpa()));
        aggregateDataBuilder.setCompareRoasRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getRoas(), aggregateDataBuilder.getCompareRoas()));
        aggregateDataBuilder.setCompareAcosRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAcos(), aggregateDataBuilder.getCompareAcos()));
        aggregateDataBuilder.setCompareAdSaleRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAdSale(), aggregateDataBuilder.getCompareAdSale()));
        aggregateDataBuilder.setCompareAdSalesRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAdSales(), aggregateDataBuilder.getCompareAdSales()));
        aggregateDataBuilder.setCompareAdOtherSalesRate(MathUtil.calculateCompareRateWithLine(aggregateDataBuilder.getAdOtherSales(), aggregateDataBuilder.getCompareAdOtherSales()));
    }

    /**
     * 汇总数据组装
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getQueryKeywordAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, boolean isVc) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal cpa = sumAdcost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(sumAdOrderNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(sumAdcost, BigDecimal.valueOf(sumAdOrderNum));
        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //本广告产品销量
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum;
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);
        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare, sumAdOrderNumCompare, 2);
        //每笔订单花费
        BigDecimal sumCpaCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP);
        //广告销量
        int sumSalesNumCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //本广告产品订单量
        int sumAdSaleNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNumCompare = sumAdOrderNumCompare - sumAdSaleNumCompare;
        //本广告产品销售额
        BigDecimal sumAdSalesCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSales() != null).map(AdHomePerformancedto::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他产品广告销售额
        BigDecimal sumAdOtherSalesCompare = sumAdSaleCompare.subtract(sumAdSalesCompare);
        //本广告产品销量
        int sumOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNumCompare = sumSalesNumCompare - sumOrderNumCompare;

        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toPlainString())
                .setAcots(acots.stripTrailingZeros().toPlainString())
                .setAsots(asots.stripTrailingZeros().toPlainString())
                .setRoas(roas.stripTrailingZeros().toPlainString())
                .setAdCost(sumAdcost.stripTrailingZeros().toPlainString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toPlainString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toPlainString())
                .setCtr(sumCtr.stripTrailingZeros().toPlainString())
                .setAdSale(sumAdSale.stripTrailingZeros().toPlainString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setCpa(cpa.stripTrailingZeros().toPlainString())
                .setAdSaleNum(Int32Value.of(sumAdSaleNum))
                .setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum))
                .setAdSales(sumAdSales.stripTrailingZeros().toPlainString())
                .setAdOtherSales(sumAdOtherSales.stripTrailingZeros().toPlainString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .setAdSelfSaleNum(Int32Value.of(sumOrderNum))
                .setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum))
                .setAdCostPercentage(sumAdCostPercentage)
                .setAdSalePercentage(sumAdSalePercentage)
                .setAdOrderNumPercentage(sumAdOrderNumPercentage)
                .setOrderNumPercentage(sumOrderNumPercentage)
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))
                .setCompareCpa(sumCpaCompare.toPlainString())
                .setCompareOrderNum(Int32Value.of(sumSalesNumCompare))
                .setCompareAdSaleNum(Int32Value.of(sumAdSaleNumCompare))
                .setCompareAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNumCompare))
                .setCompareAdSales(sumAdSalesCompare.toPlainString())
                .setCompareAdOtherSales(sumAdOtherSalesCompare.toPlainString())
                .setCompareAdSelfSaleNum(Int32Value.of(sumOrderNumCompare))
                .setCompareAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNumCompare))

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 2), 100)))
                .setCompareCpaRate(sumCpaCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (cpa.subtract(sumCpaCompare))
                        .multiply(new BigDecimal(100)).divide(sumCpaCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrderNumRate(sumSalesNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumSalesNum, sumSalesNumCompare, 4), 100)))
                .setCompareAdSaleNumRate(sumAdSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdSaleNum, sumAdSaleNumCompare, 4), 100)))
                .setCompareAdOtherOrderNumRate(sumAdOtherOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherOrderNum, sumAdOtherOrderNumCompare, 4), 100)))
                .setCompareAdSalesRate(sumAdSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSales.subtract(sumAdSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdOtherSalesRate(sumAdOtherSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdOtherSales.subtract(sumAdOtherSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdOtherSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdSelfSaleNumRate(sumOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrderNum, sumOrderNumCompare, 4), 100)))
                .setCompareAdOtherSaleNumRate(sumAdOtherSaleNumCompare == 0 ? "-" :

                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherSaleNum, sumAdOtherSaleNumCompare, 4), 100)));


        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setCompareAcots("-");
            builder.setAsots("-");
            builder.setCompareAsotsRate("-");
            builder.setCompareAsots("-");
        }

        return builder.build();

        //.build();
    }

    private AdHomeAggregateDataRpcVo getAdHomeAggregateDataVo(ReportVo reportVo, BigDecimal shopSales) {
        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (reportVo == null) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setAdCost("0")
                    .setRoas("0")
                    .setAsots("0")
                    .setAsots("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .build();
        }
        Integer clicks = Optional.ofNullable(reportVo.getClicks()).orElse(0);
        Integer impressions = Optional.ofNullable(reportVo.getImpressions()).orElse(0);
        BigDecimal acos = Optional.ofNullable(reportVo.getAcos()).orElse(BigDecimal.ZERO);
        BigDecimal adCost = Optional.ofNullable(reportVo.getCost()).orElse(BigDecimal.ZERO);
        Integer adOrderNum = Optional.ofNullable(reportVo.getOrderNum()).orElse(0);
        BigDecimal adSale = Optional.ofNullable(reportVo.getSales()).orElse(BigDecimal.ZERO);

        //平均点击费
        BigDecimal adCostPerClick = clicks == 0 ? BigDecimal.ZERO : adCost.divide(new BigDecimal(clicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal cvr = clicks == 0 ? BigDecimal.ZERO : new BigDecimal(adOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(clicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal ctr = impressions == 0 ? BigDecimal.ZERO : new BigDecimal(clicks).multiply(new BigDecimal("100")).divide(new BigDecimal(impressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = adCost.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adSale.divide(adCost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adCost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        return AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(acos.toString())
                .setRoas(roas.stripTrailingZeros().toString())
                .setAsots(asots.stripTrailingZeros().toString())
                .setAcots(acots.stripTrailingZeros().toString())
                .setAdCost(adCost.toString())
                .setClicks(Int32Value.of(clicks))
                .setAdOrderNum(Int32Value.of(adOrderNum))
                .setAdSale(adSale.toString())
                .setImpressions(Int32Value.of(impressions))
                .setCtr(ctr.toString())
                .setCvr(cvr.toString())
                .setAdCostPerClick(adCostPerClick.toString())
                .build();
    }

    public Page getAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page) {
        if (StringUtils.isNotBlank(dto.getWordRoot())) {
            //获取列表页所有queryId
            List<String> queryIdList = cpcQueryKeywordReportDao.listQueryIdByQueryWordDto(dto);
            if (CollectionUtils.isEmpty(queryIdList)) {
                page.setRows(new ArrayList());
                return page;
            }
            List<String> queryIds = wordRootKeywordSpDao.listQueryIdByWordRootAndQueryIdList(puid, dto.getShopId(), dto.getWordRoot(), queryIdList);
            if (CollectionUtils.isEmpty(queryIds)) {
                page.setRows(new ArrayList());
                return page;
            }
            dto.setQueryIds(queryIds);
        }
        //todo:干掉不合理参数,避免前端误传
        dto.setCampaignIds(null);
        //分页数据
        StopWatchUtil.start();
        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcQueryKeywordReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if ("isExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.EXACT);
                }
                if ("isBroad".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.BROAD);
                }
                if ("isPhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.PHRASE);
                }
                if ("isNegativeExact".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEEXACT);
                }
                if ("isNegativePhrase".equalsIgnoreCase(matchType)) {
                    matchTypeList.add(Constants.NEGATIVEPHRASE);
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = amazonAdKeywordShardingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    List<SearchQueryTagParam> searchQueryTagParams = amazonAdNekeywordDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        return page;
                    }
                } else {
                    return page;
                }
            } else {
                return page;
            }
        }
        PageThreadDto pageThreadDto = pageThreadManageList(puid, dto, page);
        page = pageThreadDto.getPage();
        AdMetricDto adMetricDto = pageThreadDto.getAdMetricDto();
        log.info("==============================查询所有搜索词关键词花费时间 分页 {} ==============================", StopWatchUtil.getAndStart());

        List<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo> poList = page.getRows();

        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getAdGroupId).distinct().collect(Collectors.toList());
//        List<String> targetIds = poList.stream().map(AdQueryOptionVo::getTargetId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
        }
        //批量查询广告活动和广告组
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
        }

        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
        }

        Map<String, AmazonAdGroup> groupMap = null;
        List<AmazonAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        }
//        Map<String,AmazonAdTargeting> targetMap = null;
//        if (CollectionUtils.isNotEmpty(targetIds)) {
//            targetMap = amazonAdTargetingShardingDao.getByAdTargetIds(puid, dto.getShopId(), targetIds)
//                    .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
//        }
        log.info("==============================查询所有搜索词关键词花费时间 批量查询数据 {} ==============================", StopWatchUtil.getAndStart());

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
//            Map<String, AmazonAdTargeting> finalTargetMap = targetMap;
            //            List<String> groupIdList = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getAdGroupId)
            //                    .distinct().collect(Collectors.toList());
            Map<String, Map<String, Set<String>>> groupKeywordMapList = getKeywordData(puid, dto.getShopId(), groupIds);
            log.info("==============================ad key word 查询数据 {} ==============================", StopWatchUtil.getAndStart());
            BigDecimal sumRange = dto.getShopSales();

            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setType(Constants.SP);
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getSaleNum()).orElse(0));
                //广告销量(原来取saleNum字段，现在改成salesNum字段)
                vo.setSaleNum(Optional.ofNullable(e.getSalesNum()).orElse(0));
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setSales(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);

                BigDecimal rate2 = e.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getTotalSales());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getSaleNum()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getTotalSales() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));

                vo.setQuery(e.getQuery());
                vo.setQueryCn(e.getQueryCn());
                if (e.getType().equalsIgnoreCase("keyword")) {
                    vo.setKeywordText(e.getKeywordText());
                    vo.setMatchType(e.getMatchType());
                    vo.setKeywordId(e.getKeywordId());
                    vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                    vo.setIsTargetType(false);
                } else {
                    //asin有自动和手动
                    vo.setTargetId(e.getTargetId());
                    // 兼容前端
                    if ("close-match".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("close_match");
                    } else if ("loose-match".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("loose_match");
                    } else if ("substitutes".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("substitutes");
                    } else if ("complements".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("complements");
                    } else {
                        vo.setMatchType(StringUtil.toStringSafe(e.getTargetingExpression()));
                    }
                    vo.setKeywordText(Constants.TARGETING_EXPRESSION_PREDEFINED.equals(e.getTargetingType()) ? "自动投放组" : "商品投放组");
                    vo.setIsTargetType(true);
                    vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                }

                filterMetricData(e, vo, adMetricDto);

                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    Map<String, Set<String>> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    Set<String> matchTypeSet = amazonAdKeywords.get(e.getQuery().toLowerCase().trim());
                    if (CollectionUtils.isNotEmpty(matchTypeSet)) {
                        // vo.setIsExact(matchTypeSet.contains(Constants.EXACT.toLowerCase()));
                        if (matchTypeSet.contains(Constants.EXACT.toLowerCase())) {
                            vo.setIsExact(true);
                        }
                        if (matchTypeSet.contains(Constants.BROAD.toLowerCase())) {
                            vo.setIsBroad(true);
                        }
                        if (matchTypeSet.contains(Constants.PHRASE.toLowerCase())) {
                            vo.setIsPhrase(true);
                        }
                        if (matchTypeSet.contains(Constants.NEGATIVEEXACT.toLowerCase())) {
                            vo.setIsNegativeExact(true);
                        }
                        if (matchTypeSet.contains(Constants.NEGATIVEPHRASE.toLowerCase())) {
                            vo.setIsNegativePhrase(true);
                        }
                    }

                }

                vo.setCampaignId(e.getCampaignId());
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignStatus(campaign.getState());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap != null && finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    AmazonAdGroup amazonAdGroup = finalGroupMap.get(e.getAdGroupId());
                    vo.setAdGroupName(amazonAdGroup.getName());
                    vo.setAdGroupType(amazonAdGroup.getAdGroupType());
                    vo.setDefaultBid(amazonAdGroup.getDefaultBid());
                    vo.setAdGroupState(amazonAdGroup.getState());

                } else {
                    vo.setAdGroupName(e.getAdGroupName());
                }
//                if (MapUtils.isNotEmpty(finalTargetMap) && finalTargetMap.containsKey(e.getTargetId())){
//                    AmazonAdTargeting amazonAdTargeting = finalTargetMap.get(e.getTargetId());
//                    vo.setTargetState(amazonAdTargeting.getState());
//                }
                /**
                 * TODO 广告报告重构
                 * 本广告产品销售额
                 */
                vo.setAdSales(e.getAdSales());
                //本广告产品订单量
                vo.setAdSaleNum(e.getAdSaleNum());
                //本广告产品销量
                vo.setAdSelfSaleNum(e.getAdOrderNum());
                //其他产品广告订单量
                int adOtherOrderNum = e.getSaleNum() - e.getAdSaleNum();
                vo.setAdOtherOrderNum(adOtherOrderNum);
                //其他产品广告销售额
                BigDecimal adOtherSales = e.getTotalSales().subtract(e.getAdSales());
                vo.setAdOtherSales(adOtherSales);
                //其他产品广告销量
                int adOtherSaleNum = MathUtil.subtractInteger(e.getSalesNum(), e.getAdOrderNum());
                vo.setAdOtherSaleNum(adOtherSaleNum);
                vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    /**
     * 获取分组下关键词
     *
     * @param puid
     * @param shopId
     * @param groupIds
     * @return
     */
    private Map<String, Map<String, Set<String>>> getKeywordData(Integer puid, Integer shopId, List<String> groupIds) {
        Map<String, Map<String, Set<String>>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<AmazonAdKeyword> keywordList = amazonAdKeywordDaoRoutingService.listKeyWordAndMatchTypeByGroupIdList(puid, shopId, groupIds);
            logger.info("getKeywordData size = {}", keywordList.size());
            if (CollectionUtils.isNotEmpty(keywordList)) {
                keywordList.stream()
                        .filter(k -> StringUtils.isNotBlank(k.getAdGroupId()) && StringUtils.isNotBlank(k.getKeywordText()))
                        .collect(Collectors.groupingBy(AmazonAdKeyword::getAdGroupId))
                        .forEach((key, value) ->
                                map.put(key, value.stream().collect(
                                        Collectors.groupingBy(k -> k.getKeywordText().trim().toLowerCase(),
                                                Collectors.mapping(k -> k.getMatchType().toLowerCase(), Collectors.toSet())))));
            }
        }
        return map;
    }

    /**
     * 获取分组下关键词
     */
    private Map<String, Map<String, Set<String>>> getDorisKeywordData(Integer puid, Integer shopId, List<String> groupIds) {
        Map<String, Map<String, Set<String>>> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<OdsAmazonAdNeKeyword> amazonAdNeKeywords = odsAmazonAdNeKeywordDao.listKeyWordAndMatchTypeByGroupIdList(puid, shopId, groupIds);
            List<OdsAmazonAdKeyword> keywordList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(amazonAdNeKeywords)) {
                keywordList.addAll(amazonAdNeKeywords.stream().map(e -> {
                    OdsAmazonAdKeyword keyword = new OdsAmazonAdKeyword();
                    keyword.setKeywordText(e.getKeywordText());
                    keyword.setMatchType(e.getMatchType());
                    keyword.setAdGroupId(e.getAdGroupId());
                    return keyword;
                }).collect(Collectors.toList()));
            }
            List<OdsAmazonAdKeyword> amazonAdKeywords = odsAmazonAdKeywordDao.listKeyWordAndMatchTypeByGroupIdList(puid, shopId, groupIds);
            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                keywordList.addAll(amazonAdKeywords);
            }
            logger.info("getDorisKeywordData size = {}", keywordList.size());
            if (CollectionUtils.isNotEmpty(keywordList)) {
                keywordList.stream()
                        .filter(k -> StringUtils.isNotBlank(k.getAdGroupId()) && StringUtils.isNotBlank(k.getKeywordText()))
                        .collect(Collectors.groupingBy(OdsAmazonAdKeyword::getAdGroupId))
                        .forEach((key, value) ->
                                map.put(key, value.stream().collect(
                                        Collectors.groupingBy(k -> k.getKeywordText().trim().toLowerCase(),
                                                Collectors.mapping(k -> k.getMatchType().toLowerCase(), Collectors.toSet())))));
            }
        }
        return map;
    }

    @Override
    public List<QueryReportVo> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        List<QueryReportVo> voList = new ArrayList<>();

        List<CpcQueryKeywordReport> reportList = getReportVoListByGroupIdsOrHistory(puid, spGroupIds, searchVo);
        if (CollectionUtils.isEmpty(reportList)) {
            return voList;
        }

        String marketplaceId = reportList.get(0).getMarketplaceId();
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId).getCurrencyCode();

        List<String> campaignIds = reportList.stream().distinct().map(CpcQueryKeywordReport::getCampaignId).collect(Collectors.toList());
        List<String> groupIds = reportList.stream().distinct().map(CpcQueryKeywordReport::getAdGroupId).collect(Collectors.toList());

        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignDao.getByCampaignIds(puid, searchVo.getShopId(), marketplaceId, campaignIds);
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<AmazonAdGroup> groupList = amazonAdGroupDao.getAdGroupByIds(puid, searchVo.getShopId(), marketplaceId, groupIds);
        Map<String, AmazonAdGroup> groupMap = groupList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));

        List<AmazonAdKeyword> keywordList = null;
        Map<String, List<AmazonAdKeyword>> groupKeywordMapList = new HashMap<>();

        getKeywordData(puid, searchVo.getShopId(), groupIds, keywordList, groupKeywordMapList);
        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = reportList.stream().map(CpcQueryKeywordReport::getQuery).filter(e -> !e.matches(Constants.ASIN_REGEX)).distinct()
                .map(e -> new WordTranslateQo(searchVo.getMarketplaceId(), e)).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);

        QueryReportVo vo;
        for (CpcQueryKeywordReport report : reportList) {
            vo = new QueryReportVo();
            vo.setShopId(report.getShopId());
            vo.setMarketplaceId(report.getMarketplaceId());
            if (StringUtils.isNotBlank(report.getQuery()) && report.getQuery().matches(Constants.ASIN_REGEX)) {
                vo.setQuery(report.getQuery().toUpperCase());
            } else {
                vo.setQuery(report.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(searchVo.getMarketplaceId(), report.getQuery())));
            }
            vo.setCampaignId(report.getCampaignId());
            vo.setCampaignName(report.getCampaignName());
            vo.setAdGroupId(report.getAdGroupId());
            vo.setAdGroupName(report.getAdGroupName());
            vo.setType("sp");
            vo.setTargetType("keyword");
            vo.setTargetId(report.getKeywordId());
            vo.setCurrency(currencyCode);
            if (campaignMap.containsKey(report.getCampaignId())) {
                vo.setCampaignName(campaignMap.get(report.getCampaignId()).getName());
                vo.setCampaignTargetingType(campaignMap.get(report.getCampaignId()).getTargetingType());
            }
            if (groupMap.containsKey(report.getAdGroupId())) {
                vo.setAdGroupName(groupMap.get(report.getAdGroupId()).getName());
            }
            if (StringUtils.isNotBlank(report.getMatchType())) {
                vo.setMatchType(report.getMatchType().toLowerCase());
            }

            //检查该搜索词是否添加过
            if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(report.getAdGroupId()) && StringUtils.isNotBlank(report.getQuery())) {
                List<AmazonAdKeyword> amazonAdKeywords = groupKeywordMapList.get(report.getAdGroupId());
                if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                    for (AmazonAdKeyword keyword : amazonAdKeywords) {
                        if (StringUtils.isNotBlank(report.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                            if (report.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                    vo.setIsExact(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                    vo.setIsBroad(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                    vo.setIsPhrase(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                    vo.setIsNegativeExact(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                    vo.setIsNegativePhrase(true);
                                }
                            }
                        }
                    }
                }
            }

            vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
            vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
            vo.setAdSale(report.getTotalSales() != null ? String.valueOf(report.getTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");

            // 点击率
            Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
            vo.setCtr(String.valueOf(ctr));
            //订单转化率
            Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
            vo.setCvr(String.valueOf(cvr));
            //acos
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setAcos("0");
            } else {
                Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()), 2);
                vo.setAcos(String.valueOf(acos));
            }
            //acots
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAcots("0");
            } else {
                Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue(), 2);
                vo.setAcots(String.valueOf(acots));
            }
            //asots
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAsots("0");
            } else {
                Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue(), 2);
                vo.setAsots(String.valueOf(asots));
            }
            //adCostPerClick
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
                vo.setAdCostPerClick("0");
            } else {
                Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
                vo.setAdCostPerClick(String.valueOf(adCostPerClick));
            }
            //roas
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setRoas("0");
            } else {
                Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
                vo.setRoas(String.valueOf(roas));
            }

            voList.add(vo);
        }

        return voList;
    }

    @Override
    public List<TargetQueryReportVo> getListByKeywordId(Integer puid, String targetId, TargetQuerySearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        List<TargetQueryReportVo> voList = new ArrayList<>();

        List<CpcQueryKeywordReport> reportList = getListByKeywordIdOrHistory(puid, targetId, searchVo);
        if (CollectionUtils.isEmpty(reportList)) {
            return voList;
        }

        String marketplaceId = reportList.get(0).getMarketplaceId();
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId).getCurrencyCode();

        List<String> campaignIds = reportList.stream().distinct().map(CpcQueryKeywordReport::getCampaignId).collect(Collectors.toList());
        List<String> groupIds = reportList.stream().distinct().map(CpcQueryKeywordReport::getAdGroupId).collect(Collectors.toList());

        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignDao.getByCampaignIds(puid, searchVo.getShopId(), marketplaceId, campaignIds);
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<AmazonAdGroup> groupList = amazonAdGroupDao.getAdGroupByIds(puid, searchVo.getShopId(), marketplaceId, groupIds);
        Map<String, AmazonAdGroup> groupMap = groupList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));

        List<AmazonAdKeyword> keywordList = null;
        Map<String, List<AmazonAdKeyword>> groupKeywordMapList = new HashMap<>();

        getKeywordData(puid, searchVo.getShopId(), groupIds, keywordList, groupKeywordMapList);

        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = reportList.stream().map(CpcQueryKeywordReport::getQuery).filter(e -> !e.matches(Constants.ASIN_REGEX)).distinct()
                .map(e -> new WordTranslateQo(searchVo.getMarketplaceId(), e)).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);
        TargetQueryReportVo vo;
        for (CpcQueryKeywordReport report : reportList) {
            vo = new TargetQueryReportVo();
            vo.setShopId(report.getShopId());
            vo.setMarketplaceId(report.getMarketplaceId());
            if (StringUtils.isNotBlank(report.getQuery()) && report.getQuery().matches(Constants.ASIN_REGEX)) {
                vo.setQuery(report.getQuery().toUpperCase());
            } else {
                vo.setQuery(report.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(searchVo.getMarketplaceId(), report.getQuery())));
            }
            vo.setCampaignId(report.getCampaignId());
            vo.setCampaignName(report.getCampaignName());
            vo.setAdGroupId(report.getAdGroupId());
            vo.setAdGroupName(report.getAdGroupName());
            vo.setType("sp");
            vo.setTargetType("keyword");
            vo.setTargetId(report.getKeywordId());
            vo.setCurrency(currencyCode);
            if (campaignMap.containsKey(report.getCampaignId())) {
                vo.setCampaignName(campaignMap.get(report.getCampaignId()).getName());
                vo.setCampaignTargetingType(campaignMap.get(report.getCampaignId()).getTargetingType());
            }
            if (groupMap.containsKey(report.getAdGroupId())) {
                vo.setAdGroupName(groupMap.get(report.getAdGroupId()).getName());
            }
            if (StringUtils.isNotBlank(report.getMatchType())) {
                vo.setMatchType(report.getMatchType().toLowerCase());
            }

            //检查该搜索词是否添加过
            if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(report.getAdGroupId()) && StringUtils.isNotBlank(report.getQuery())) {
                List<AmazonAdKeyword> amazonAdKeywords = groupKeywordMapList.get(report.getAdGroupId());
                if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                    for (AmazonAdKeyword keyword : amazonAdKeywords) {
                        if (StringUtils.isNotBlank(report.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                            if (report.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                    vo.setIsExact(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                    vo.setIsBroad(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                    vo.setIsPhrase(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                    vo.setIsNegativeExact(true);
                                }
                                if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                    vo.setIsNegativePhrase(true);
                                }
                            }
                        }
                    }
                }
            }

            vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
            vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
            vo.setAdSale(report.getTotalSales() != null ? String.valueOf(report.getTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");

            // 点击率
            Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
            vo.setCtr(String.valueOf(ctr));
            //订单转化率
            Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
            vo.setCvr(String.valueOf(cvr));
            //acos
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setAcos("0");
            } else {
                Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()), 2);
                vo.setAcos(String.valueOf(acos));
            }
            //acots
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAcots("0");
            } else {
                Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue(), 2);
                vo.setAcots(String.valueOf(acots));
            }
            //asots
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAsots("0");
            } else {
                Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue(), 2);
                vo.setAsots(String.valueOf(asots));
            }
            //adCostPerClick
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
                vo.setAdCostPerClick("0");
            } else {
                Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
                vo.setAdCostPerClick(String.valueOf(adCostPerClick));
            }
            //roas
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setRoas("0");
            } else {
                Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
                vo.setRoas(String.valueOf(roas));
            }

            voList.add(vo);
        }

        return voList;
    }

    @Override
    public void getDetailsSumVo(QueryReportDetailsVo detailsVo, CpcCommPageVo vo, BigDecimal sumShopSale) {
        CpcQueryKeywordReport report = getDetailsSumVoOrHistory(detailsVo);
        if (report == null || report.getShopId() == null) {
            return;
        }
        // 组装报告数据
        setReportData(report, vo, sumShopSale);
    }

    @Override
    public void getQueryDetailsDay(AdReportDetailsVo adReportDetailsVo, QueryReportDetailsVo detailsVo) {
        List<CpcQueryKeywordReport> reportList = getListQueryDetailsDayOrHistory(detailsVo);
        if (CollectionUtils.isEmpty(reportList)) {
            return;
        }
        List<CpcCommPageVo> list = new ArrayList<>(reportList.size());
        CpcCommPageVo vo;
        for (CpcQueryKeywordReport report : reportList) {
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), report.getCountDate(), report.getCountDate());
            BigDecimal sumShopSale = BigDecimal.ZERO;
            if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
                sumShopSale = shopSaleDto.getSumRange();
            }
            vo = new CpcCommPageVo();
            // 组装报告数据
            setReportData(report, vo, sumShopSale);
            vo.setCountDate(report.getCountDate());
            list.add(vo);
        }
        adReportDetailsVo.setList(list);
    }

    private void setReportData(CpcQueryKeywordReport report, CpcCommPageVo vo, BigDecimal sumShopSale) {
        vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
        vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
        vo.setAdSale(report.getTotalSales() != null ? String.valueOf(report.getTotalSales().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");

        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()), 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue(), 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue(), 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }
    }

    // 填充指标占比数据
    private void filterMetricData(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo report, ReportVo vo, AdMetricDto adMetricDto) {
        if (adMetricDto == null) {
            vo.setAdCostPercentage("0");
            vo.setAdSalePercentage("0");
            vo.setAdOrderNumPercentage("0");
            vo.setOrderNumPercentage("0");
            return;
        }
        computeMetricData(adMetricDto, report, vo);
    }

    private void computeMetricData(AdMetricDto adMetricDto, com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo report, ReportVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (report.getCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(report.getCost().toString(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (report.getTotalSales() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(report.getTotalSales().toString(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (report.getSaleNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(report.getSaleNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (report.getSalesNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(report.getSalesNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }

    }

    // 获取投放信息
    private void getKeywordData(Integer puid, Integer shopId, List<String> groupIds, List<AmazonAdKeyword> keywordList, Map<String, List<AmazonAdKeyword>> groupKeywordMapList) {
        if (CollectionUtils.isNotEmpty(groupIds)) {
            keywordList = amazonAdKeywordDaoRoutingService.listByGroupIdList(puid, shopId, groupIds);
        }

        if (CollectionUtils.isNotEmpty(keywordList)) {
            for (AmazonAdKeyword keyword : keywordList) {
                if (StringUtils.isBlank(keyword.getAdGroupId())) {
                    continue;
                }
                if (groupKeywordMapList.containsKey(keyword.getAdGroupId())) {
                    groupKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                } else {
                    groupKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                }
            }
        }
    }

    private Page pageListHistoryOrNow(int puid, CpcQueryWordDto dto, Page page) {
        return cpcQueryKeywordReportDao.pageList(puid, dto, page);
    }

    private Page pageManageListHistoryOrNow(int puid, CpcQueryWordDto dto, Page page) {
        return cpcQueryKeywordReportDao.pageManageList(puid, dto, page);
    }

    /**
     * 按照时间 异步分页
     *
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    private PageThreadDto pageThreadManageList(int puid, CpcQueryWordDto dto, Page page) {
        PageThreadDto pageThreadDto = new PageThreadDto();
        // 判断是否开启多线程查询
        if (!pageThreadConfig.getEnable()) {
            pageThreadDto.setPage(pageManageListHistoryOrNow(puid, dto, page));
            pageThreadDto.setAdMetricDto(getSumAdMetricData(puid, dto));
            return pageThreadDto;
        }
        Date startDate = DateUtil.strToDate(dto.getStart(), DateUtil.PATTERN_YYYYMMDD);
        Date endDate = DateUtil.strToDate(dto.getEnd(), DateUtil.PATTERN_YYYYMMDD);
        boolean vipFlag = false;
        AmazonAdActiveMonitor adActiveMonitor = amazonAdActiveMonitorDao.findByPuid(puid);
        if (adActiveMonitor != null && adActiveMonitor.getVipFlag() != null) {
            vipFlag = adActiveMonitor.getVipFlag();
        }
        //(大于80天 || vip 用户)
        if ((Math.abs(DateUtil.daysBetween(startDate, endDate)) > pageThreadConfig.getTime() || vipFlag)) {
            logger.info("start pageThreadManageList");
            pageThreadDto.setPage(page);
            pageThreadDto.setTotal(0);
            List<PageRequestThreadDto> requestThreadDtos = new ArrayList<>(QueryKeywordPageThreadEnum.values().length);
            for (QueryKeywordPageThreadEnum queryKeywordPageThreadEnum : QueryKeywordPageThreadEnum.values()) {
                requestThreadDtos.add(PageRequestThreadDto.getStaticPageRequestThreadDto(queryKeywordPageThreadEnum, dto, page, puid));
            }
            try {
                List<CompletableFuture<PageThreadDto>> futures = completableFuturesManageList(requestThreadDtos);
                CompletableFuture<Void> all = CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]));
                CompletableFuture<List<PageThreadDto>> results = all.thenApply(v -> futures.stream().map(CompletableFuture::join).collect(Collectors.toList()));
                List<PageThreadDto> collect = results.get().stream().filter(Objects::nonNull).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(collect)) {
                    for (PageThreadDto threadDto : collect) {
                        if (QueryKeywordPageThreadEnum.COUNT.equals(threadDto.getType())) {
                            pageThreadDto.setTotal(threadDto.getTotal());
                            continue;
                        }
                        if (QueryKeywordPageThreadEnum.SELECT.equals(threadDto.getType())) {
                            pageThreadDto.setPage(threadDto.getPage());
                            continue;
                        }
                        if (QueryKeywordPageThreadEnum.SUM_SALES.equals(threadDto.getType())) {
                            pageThreadDto.setAdMetricDto(threadDto.getAdMetricDto());
                        }
                    }
                }
                if (pageThreadDto.getTotal() == 0 && pageThreadDto.getPage() != null) {
                    pageThreadDto.getPage().setTotalSize(0);
                    pageThreadDto.getPage().setTotalPage(0);
                }
                // 重新设置总页数
                if (pageThreadDto.getTotal() > 0 && pageThreadDto.getPage() != null) {
                    Page pageList = pageThreadDto.getPage();
                    pageList.setTotalSize(pageThreadDto.getTotal());
                    int totalPage = (pageThreadDto.getTotal() - 1) / pageList.getPageSize() + 1;
                    pageList.setTotalPage(totalPage);
                    pageThreadDto.setPage(pageList);
                }
            } catch (Exception e) {
                logger.error("pageThreadManageList error = {}", e.getMessage());
            }
        } else {
            pageThreadDto.setPage(pageManageListHistoryOrNow(puid, dto, page));
            pageThreadDto.setAdMetricDto(getSumAdMetricData(puid, dto));
        }
        return pageThreadDto;
    }

    /**
     * 执行任务
     */
    private List<CompletableFuture<PageThreadDto>> completableFuturesManageList(List<PageRequestThreadDto> requestThreadDtos) {
        List<CompletableFuture<PageThreadDto>> futures = new ArrayList<>(requestThreadDtos.size());
        ThreadPoolExecutor threadPoolExecutor = ThreadPoolUtil.getCommodityImgPool();
        // 封装方法
        requestThreadDtos.forEach(k -> futures.add(CompletableFuture.supplyAsync(() -> {
            try {
                PageThreadDto pageThread = new PageThreadDto();
                pageThread.setType(k.getType());
                // count
                if (QueryKeywordPageThreadEnum.COUNT.equals(k.getType())) {
                    pageThread.setTotal(cpcQueryKeywordReportDao.countPageManageList(k.getPuid(), k.getDto()));
                    return pageThread;
                }
                // 汇总
                if (QueryKeywordPageThreadEnum.SUM_SALES.equals(k.getType())) {
                    pageThread.setAdMetricDto(cpcQueryKeywordReportDao.getSumAdMetricDto(k.getPuid(), k.getDto()));
                    return pageThread;
                }
                // 查询列表
                pageThread.setPage(cpcQueryKeywordReportDao.pageThreadManageList(k.getPuid(), k.getDto(), k.getPage()));
                return pageThread;
            } catch (Exception e) {
                log.error("多线程异常 completableFuturesManageList", e);
            }
            return null;
        }, threadPoolExecutor)));
        return futures;
    }

    private AdMetricDto getSumAdMetricData(Integer puid, CpcQueryWordDto dto) {

        return cpcQueryKeywordReportDao.getSumAdMetricDto(puid, dto);

    }

    private CpcQueryKeywordReport sumReportHistoryOrNow(Integer puid, CpcQueryWordDto dto) {
        return cpcQueryKeywordReportDao.sumReport(puid, dto);
    }

    private CpcQueryKeywordReport sumManageReportHistoryOrNow(Integer puid, CpcQueryWordDto dto) {

        return cpcQueryKeywordReportDao.sumManageReport(puid, dto);
    }

    private Page detailListHistoryOrNow(Integer puid, CpcQueryWordDetailDto dto, Page page) {
        return cpcQueryKeywordReportDao.detailList(puid, dto, page);
    }

    private CpcQueryKeywordReport sumDetailReportHistoryOrNow(Integer puid, CpcQueryWordDetailDto dto) {
        return cpcQueryKeywordReportDao.sumDetailReport(puid, dto);
    }

    private List<CpcQueryKeywordReport> detailListChartHistoryOrNow(int puid, CpcQueryWordDetailDto dto) {
        return cpcQueryKeywordReportDao.detailListChart(puid, dto);
    }

    private List<AdHomePerformancedto> getReportKeywordByDateOrHistory(Integer puid, CpcQueryWordDto dto) {

        return cpcQueryKeywordReportDao.getReportKeywordByDate(puid, dto);
    }

    private List<AdHomePerformancedto> getReportKeywordByKeywordIdListOrHistory(Integer puid, CpcQueryWordDto dto, List<String> keywordIdList) {

        return cpcQueryKeywordReportDao.getReportKeywordByKeywordIdList(puid, dto.getShopId(),
                dto.getStart(), dto.getEnd(), keywordIdList, dto);
    }

    private List<AdHomePerformancedto> getReportTargetByDateOrHistory(Integer puid, CpcQueryWordDto dto) {

        return cpcQueryKeywordReportDao.getReportTargetByDate(puid, dto);
    }

    private List<AdHomePerformancedto> getReportTargetByTargetIdListOrHistory(Integer puid, CpcQueryWordDto dto, List<String> targetIdList) {

        return cpcQueryKeywordReportDao.getReportTargetByTargetIdList(puid, dto.getShopId(), dto.getStart(),
                dto.getEnd(), targetIdList, dto);
    }

    private List<CpcQueryKeywordReport> getReportVoListByGroupIdsOrHistory(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo) {

        return cpcQueryKeywordReportDao.getReportVoListByGroupIds(puid, spGroupIds, searchVo);
    }

    private List<CpcQueryKeywordReport> getListByKeywordIdOrHistory(Integer puid, String targetId, TargetQuerySearchVo searchVo) {
        return cpcQueryKeywordReportDao.getListByKeywordId(puid, targetId, searchVo);
    }

    private CpcQueryKeywordReport getDetailsSumVoOrHistory(QueryReportDetailsVo detailsVo) {
        return cpcQueryKeywordReportDao.getDetailsSumVo(detailsVo.getPuid(), detailsVo);
    }

    private List<CpcQueryKeywordReport> getListQueryDetailsDayOrHistory(QueryReportDetailsVo detailsVo) {
        return cpcQueryKeywordReportDao.getListQueryDetailsDay(detailsVo.getPuid(), detailsVo);
    }

    private void fillABARankField(List<ReportVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        //只获取当前页关键词里的搜索词排名及周变化率字段
        List<String> searchTermList = rows.stream()
                .map(item -> item.getQuery().trim().toLowerCase())
                .distinct()
                .collect(Collectors.toList());
        String searchTerms = StringUtil.joinString(searchTermList, StringUtil.SPLIT_COMMA);

        // 调取stateV2接口获取搜索词排名字段
        List<SearchTermsAnalysis> searchTermsAnalysisList = Lists.newArrayList();
        // 抛异常不捕获，避免影响程序执行
        try {
            log.info("start request stateV2 searchTermsAnalysis api");
            long s1 = Instant.now().toEpochMilli();
            searchTermsAnalysisList = searchAnalysisStatsV2Client.getSearchTermAnalysis(searchTerms);
            log.info("调用stateV2接口获取搜索词排名及周变化率，共耗时：{}", Instant.now().toEpochMilli() - s1);
        } finally {
            log.info("end request stateV2 searchTermsAnalysis api");
        }
        if (CollectionUtils.isEmpty(searchTermsAnalysisList)) {
            return;
        }
        Map<String, SearchTermsAnalysis> searchTermsAnalysisMap = searchTermsAnalysisList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(item -> item.getSearchTerm().trim().toLowerCase(), item -> item, (e1, e2) -> e1));
        rows.stream().peek(item -> {
            if (StringUtils.isNotBlank(item.getQuery()) && searchTermsAnalysisMap.get(item.getQuery().trim().toLowerCase()) != null) {
                SearchTermsAnalysis termsAnalysis = searchTermsAnalysisMap.get(item.getQuery().trim().toLowerCase());
                item.setSearchFrequencyRank(termsAnalysis.getSearchFrequencyRank());
                item.setWeekRatio(Optional.ofNullable(termsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }).collect(Collectors.toList());
    }

    @Override
    public List<AutoRuleQueryReportDataRpcVo> getAutoRuleDataList(Integer puid, Integer shopId, AutoRuleQueryWordDto dto) {
        if (puid == null || puid <= 0 || shopId == null || shopId <= 0) {
            return null;
        }
        List<AdQueryAutoRuleVo> autoRuleDataList = null;
        if ("sp".equalsIgnoreCase(dto.getType())) {
            autoRuleDataList = cpcQueryKeywordReportDao.getAutoRuleDataList(puid, dto);
        } else if ("sb".equalsIgnoreCase(dto.getType())) {
            autoRuleDataList = cpcSbQueryKeywordReportDao.getAutoRuleDataList(puid, dto);
        }

        if (CollectionUtils.isEmpty(autoRuleDataList)) {
            return null;
        }
        return autoRuleDataList.stream().map(this::transitionAutoRuleQueryReportDataRpcVo).collect(Collectors.toList());
    }

    private AutoRuleQueryReportDataRpcVo transitionAutoRuleQueryReportDataRpcVo(AdQueryAutoRuleVo vo) {
        AutoRuleQueryReportDataRpcVo.Builder builder = AutoRuleQueryReportDataRpcVo.newBuilder();
        if (vo.getAdGroupId() != null) {
            builder.setAdGroupId(vo.getAdGroupId());
        }
        builder.setCampaignId(vo.getCampaignId());
        builder.setAdOrderNum(Int32Value.of(vo.getAdOrderNum()));
        builder.setQuery(vo.getQuery());
        builder.setAdSale(DoubleValue.of(vo.getAdSale().doubleValue()));
        builder.setClicks(Int64Value.of(vo.getClicks()));
        builder.setCost(DoubleValue.of(vo.getCost().doubleValue()));
        builder.setImpressions(Int64Value.of(vo.getImpressions()));
        if (vo.getOrderNum() != null) {
            builder.setOrderNum(Int32Value.of(vo.getOrderNum()));
        }
        builder.setTargetId(vo.getTargetId());
        builder.setKeywordId(vo.getKeywordId());
        builder.setIsAsin(vo.getIsAsin());
        return builder.build();

    }

    private Page getDorisAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page) {
        StopWatchUtil.start();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        // 参数赋值
        if (setParam(puid, dto)) return page;

        if (dto.getOnlyCount() != null && dto.getOnlyCount()) {
            int size = odsCpcQueryKeywordReportDao.pageManageCountAll(puid, dto);
            return new Page(page.getPageNo(), page.getPageSize(), 0 , size, Lists.newArrayList());
        }

        //查询doris数据
        page = odsCpcQueryKeywordReportDao.pageManageList(puid, dto, page);
        AdMetricDto adMetricDto = odsCpcQueryKeywordReportDao.getSumAdMetricDto(puid, dto);
        log.info("==============================查询所有搜索词关键词花费时间 分页 {} ==============================", StopWatchUtil.getAndStart());

        List<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo> poList = page.getRows();

        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getAdGroupId).distinct().collect(Collectors.toList());
        List<String> allTargetIds = poList.stream().map(AdQueryOptionVo::getTargetId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
        List<String> keywordIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getKeywordId).distinct().collect(Collectors.toList());

        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
        }
        //批量查询广告活动和广告组
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
        }

        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
        }

        Map<String, AmazonAdGroup> groupMap = null;
        List<AmazonAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        }

        Map<String,AmazonAdTargeting> allTargetMap = null;
        if (CollectionUtils.isNotEmpty(allTargetIds)) {
            allTargetMap = amazonAdTargetingShardingDao.getByAdTargetIds(puid, dto.getShopId(), allTargetIds)
                    .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
        }
        Map<String, AmazonAdKeyword> keywordMap = null;
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            List<AmazonAdKeyword> amazonAdKeywords = amazonAdKeywordDaoRoutingService.getByKeywordIds(puid, dto.getShopId(), keywordIds, Constants.BIDDABLE);
            keywordMap = amazonAdKeywords.stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, Function.identity(), (a, b) -> a));
        }
        log.info("==============================查询所有搜索词关键词花费时间 批量查询数据 {} ==============================", StopWatchUtil.getAndStart());


        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            Map<String,AmazonAdTargeting> finalAllTargetMap = allTargetMap;
            Map<String, AmazonAdKeyword> finalKeywordMap = keywordMap;

            List<String> targetIdList = new ArrayList<>();
            Map<String, AmazonAdTargeting> targetMap = new HashMap<>();
            Map<String, Map<String, Set<String>>> groupKeywordMapList = this.getDorisKeywordData(puid, dto.getShopId(), groupIds);
            log.info("==============================ad key word 查询数据 {} ==============================", StopWatchUtil.getAndStart());
            BigDecimal sumRange = dto.getShopSales();

            Map<String, OdsWeekSearchTermsAnalysis> searchTermsAnalysisMap = new HashMap<>();
            if (!dto.isQueryJoinSearchTermsRank()) {
                List<String> searchTerms = poList.stream().map(AdQueryOptionVo::getQuery).distinct().collect(Collectors.toList());
                List<OdsWeekSearchTermsAnalysis> searchTermsAnalyses = weekSearchTermsAnalysisService.queryRanks(searchTerms, dto.getMarketplaceId());
                searchTermsAnalysisMap.putAll(searchTermsAnalyses.stream().collect(Collectors.toMap(OdsWeekSearchTermsAnalysis::getSearchTerm, Function.identity(), (o, n) -> n)));
            }
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = poList.stream().map(e -> new WordTranslateQo(e.getMarketplaceId(), e.getQuery())).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);

            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setQueryId(e.getQueryId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setType(Constants.SP);
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getSaleNum()).orElse(0));
                //广告销量(原来取saleNum字段，现在改成salesNum字段)
                vo.setSaleNum(Optional.ofNullable(e.getSalesNum()).orElse(0));
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setSales(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);

                BigDecimal rate2 = e.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getTotalSales());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                BigDecimal rate3 = BigDecimal.valueOf(e.getSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(e.getSaleNum()));
                BigDecimal cpa = rate3.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpa(cpa);

                if (e.getTotalSales() != null && e.getCost() != null) {
                    vo.setRoas(e.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().divide(e.getCost(), 2, RoundingMode.HALF_UP));
                }
                //新加指标,需要获取广告
                BigDecimal shopTotalSales = Optional.ofNullable(sumRange).orElse(BigDecimal.ZERO);
                vo.setAcots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));
                vo.setAsots(shopTotalSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : e.getTotalSales().multiply(BigDecimal.valueOf(100)).divide(sumRange, 2, RoundingMode.HALF_UP));

                vo.setQuery(e.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(dto.getMarketplaceId(), e.getQuery())));
                if (e.getType().equalsIgnoreCase("keyword")) {
                    vo.setKeywordText(e.getKeywordText());
                    vo.setMatchType(e.getMatchType().toLowerCase());
                    vo.setKeywordId(e.getKeywordId());
                    vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                    vo.setIsTargetType(false);
                } else {
                    //asin有自动和手动
                    vo.setTargetId(e.getTargetId());
                    // 兼容前端
                    if ("close-match".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("close_match");
                    } else if ("loose-match".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("loose_match");
                    } else if ("substitutes".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("substitutes");
                    } else if ("complements".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("complements");
                    } else if (e.getTargetingExpression () != null && e.getTargetingExpression().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())) {
                        vo.setMatchType("theme");
                    } else {
                        vo.setMatchType(StringUtil.toStringSafe(e.getTargetingExpression()));
                    }
                    if (SpKeywordGroupValueEnum.getTheme().equalsIgnoreCase(vo.getMatchType())) {
                        vo.setKeywordText(e.getTargetingExpression());
                    } else {
                        if (Constants.TARGETING_EXPRESSION_PREDEFINED.equals(e.getTargetingType())) {
                            vo.setKeywordText("自动投放组");
                        } else {
                            //将手动投放的targetId放入到需要查询投放表的list中
                            //该类型为精确/拓展/类目
                            targetIdList.add(vo.getTargetId());
                            String expression = e.getTargetingExpression();
                            if (expression.startsWith("asin=")) {
                                String keywords = expression.substring("asin=".length());
                                vo.setKeywordText(keywords);
                                // 去除首尾双引号
                                if (vo.getKeywordText().startsWith("\"") && vo.getKeywordText().endsWith("\"")) {
                                    vo.setKeywordText(vo.getKeywordText().substring(1, vo.getKeywordText().length() - 1));
                                }
                            } else if (expression.startsWith("asin-expanded=")) {
                                String keywords = expression.substring("asin-expanded=".length());
                                vo.setKeywordText(keywords);
                                // 去除首尾双引号
                                if (vo.getKeywordText().startsWith("\"") && vo.getKeywordText().endsWith("\"")) {
                                    vo.setKeywordText(vo.getKeywordText().substring(1, vo.getKeywordText().length() - 1));
                                }
                            } else {
                                vo.setKeywordText(expression);
                            }
                        }
                    }

                    vo.setIsTargetType(true);
                    vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                }

                filterMetricData(e, vo, adMetricDto);

                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    Map<String, Set<String>> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    Set<String> matchTypeSet = amazonAdKeywords.get(e.getQuery().toLowerCase().trim());
                    if (CollectionUtils.isNotEmpty(matchTypeSet)) {
                        // vo.setIsExact(matchTypeSet.contains(Constants.EXACT.toLowerCase()));
                        if (matchTypeSet.contains(Constants.EXACT.toLowerCase())) {
                            vo.setIsExact(true);
                        }
                        if (matchTypeSet.contains(Constants.BROAD.toLowerCase())) {
                            vo.setIsBroad(true);
                        }
                        if (matchTypeSet.contains(Constants.PHRASE.toLowerCase())) {
                            vo.setIsPhrase(true);
                        }
                        if (matchTypeSet.contains(Constants.NEGATIVEEXACT.toLowerCase())) {
                            vo.setIsNegativeExact(true);
                        }
                        if (matchTypeSet.contains(Constants.NEGATIVEPHRASE.toLowerCase())) {
                            vo.setIsNegativePhrase(true);
                        }
                    }

                }

                vo.setCampaignId(e.getCampaignId());
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignStatus(campaign.getState());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap != null && finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    AmazonAdGroup amazonAdGroup = finalGroupMap.get(e.getAdGroupId());
                    vo.setAdGroupName(amazonAdGroup.getName());
                    vo.setAdGroupType(amazonAdGroup.getAdGroupType());
                    vo.setDefaultBid(amazonAdGroup.getDefaultBid());
                    vo.setAdGroupState(amazonAdGroup.getState());

                } else {
                    vo.setAdGroupName(e.getAdGroupName());
                }
                if (MapUtils.isNotEmpty(finalAllTargetMap) && finalAllTargetMap.containsKey(e.getTargetId())){
                    AmazonAdTargeting amazonAdTargeting = finalAllTargetMap.get(e.getTargetId());
                    vo.setTargetState(amazonAdTargeting.getState());
                }
                if (MapUtils.isNotEmpty(finalKeywordMap) && finalKeywordMap.containsKey(e.getKeywordId())) {
                    vo.setTargetState(finalKeywordMap.get(e.getKeywordId()).getState());
                }
                /**
                 * TODO 广告报告重构
                 * 本广告产品销售额
                 */
                vo.setAdSales(e.getAdSales());
                //本广告产品订单量
                vo.setAdSaleNum(e.getAdSaleNum());
                //本广告产品销量
                vo.setAdSelfSaleNum(e.getAdOrderNum());
                //其他产品广告订单量
                int adOtherOrderNum = e.getSaleNum() - e.getAdSaleNum();
                vo.setAdOtherOrderNum(adOtherOrderNum);
                //其他产品广告销售额
                BigDecimal adOtherSales = e.getTotalSales().subtract(e.getAdSales());
                vo.setAdOtherSales(adOtherSales);
                //其他产品广告销量
                int adOtherSaleNum = MathUtil.subtractInteger(e.getSalesNum(), e.getAdOrderNum());
                vo.setAdOtherSaleNum(adOtherSaleNum);
                vo.setAdvertisingUnitPrice(vo.getOrderNum() == 0 ? BigDecimal.ZERO :
                        MathUtil.divideOfObject(vo.getSales(), vo.getOrderNum(), 2));
                // 兼容特殊排序逻辑
                // 联表时字段aba排名与周变化率肯定不为空
                if (dto.isQueryJoinSearchTermsRank()) {
                    vo.setSearchFrequencyRank(e.getSearchFrequencyRank() == Integer.MAX_VALUE ? 0 : e.getSearchFrequencyRank());
                    if (vo.getSearchFrequencyRank() > 0) {
                        BigDecimal weekRatio = e.getWeekRatio().intValue() == Integer.MIN_VALUE ? BigDecimal.ZERO : e.getWeekRatio();
                        vo.setWeekRatio(weekRatio.setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                } else {
                    OdsWeekSearchTermsAnalysis searchTermsAnalysis = searchTermsAnalysisMap.get(vo.getQuery().toLowerCase());
                    if (searchTermsAnalysis != null) {
                        vo.setSearchFrequencyRank(searchTermsAnalysis.getSearchFrequencyRank());
                        vo.setWeekRatio(Optional.ofNullable(searchTermsAnalysis.getWeekRatio()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                    }
                }
                list.add(vo);
            });
            //查询商品投放的图片和title信息/类目的相关信息
            if (CollectionUtils.isNotEmpty(targetIdList)) {
                targetMap = amazonAdTargetingShardingDao.getByAdTargetIds(puid, dto.getShopId(), targetIdList)
                        .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
            }
            List<String> asins = list.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordText()) && Pattern.compile(Constants.ASIN_REGEX).matcher(e.getKeywordText()).matches() && StringUtils.isBlank(e.getImgUrl())).map(e -> e.getKeywordText().toUpperCase()).distinct().collect(Collectors.toList());
            Map<String, AsinImage> asinMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(asins)) {
                //填充标签数据
                long t4 = Instant.now().toEpochMilli();
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(), shopAuth.getMarketplaceId(), asins);
                log.info("获取图片花费时间 {}", Instant.now().toEpochMilli() - t4);
                asinMap = listByAsins.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
            }
            //设置商品投放的图片和title信息/类目的相关信息
            for (ReportVo vo : list) {
                if (targetMap.containsKey(vo.getTargetId())) {
                    AmazonAdTargeting amazonAdTargeting = targetMap.get(vo.getTargetId());
                    if (amazonAdTargeting != null && TargetTypeEnum.category.name().equals(amazonAdTargeting.getType())) {
                        vo.setMatchType(amazonAdTargeting.getType());
                        vo.setCategory(amazonAdTargeting.getTargetingValue());
                        // 改版前路径是存在这个字段里的，因为要支持列表页模糊搜索改到了targeting_value
                        if (StringUtils.isNotBlank(amazonAdTargeting.getCategoryPath())) {
                            vo.setCategory(amazonAdTargeting.getCategoryPath());
                        }
                        //如果为数字ID,表明类目或品牌已经被amazon删除
                        if (StringUtils.isNumeric(amazonAdTargeting.getCategoryPath())) {
                            vo.setCategory("此类目亚马逊已删除");
                        }
                    } else if (amazonAdTargeting != null && TargetTypeEnum.asin.name().equals(amazonAdTargeting.getType())) {
                        vo.setMatchType(amazonAdTargeting.getSelectType());
                        vo.setImgUrl(amazonAdTargeting.getImgUrl());
                        vo.setTargetTitle(amazonAdTargeting.getTitle());
                        vo.setKeywordText(amazonAdTargeting.getTargetingValue().toUpperCase());
                        if (StringUtils.isNotBlank(vo.getKeywordText()) && (StringUtils.isBlank(vo.getTitle()) || StringUtils.isBlank(vo.getImgUrl()))) {
                            AsinImage asinImage = asinMap.get(vo.getKeywordText().toUpperCase());
                            if (asinImage != null) {
                                Optional.ofNullable(asinImage.getTitle()).filter(StringUtils::isNotEmpty).ifPresent(t -> {
                                    if (StringUtils.isEmpty(vo.getTargetTitle())) {
                                        vo.setTargetTitle(t);
                                    }
                                });
                                Optional.ofNullable(asinImage.getImage()).filter(StringUtils::isNotEmpty).ifPresent(m -> {
                                    if (StringUtils.isEmpty(vo.getImgUrl())) {
                                        vo.setImgUrl(m);
                                    }
                                });
                            }
                        }
                    }
                    if (amazonAdTargeting != null && StringUtils.isNotBlank(amazonAdTargeting.getResolvedExpression()) && TargetTypeEnum.category.name().equalsIgnoreCase(amazonAdTargeting.getType())) {
                        JSONArray jsonArray = JSONArray.parseArray(amazonAdTargeting.getResolvedExpression());
                        if (jsonArray != null && !jsonArray.isEmpty()) {
                            this.fillBrandMessage(vo, jsonArray);
                        }
                    }
                }
            }
            page.setRows(list);
        }
        return page;
    }

    private boolean setParam(Integer puid, CpcQueryWordDto dto) {
        //todo:干掉不合理参数,避免前端误传
        dto.setCampaignIds(null);
        //分页数据
        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }
        //若只选择了标签asin，则直接返回空列表
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            Set<String> targetTagTypeSet = new HashSet<>(Lists.newArrayList(CpcQueryWordDto.QueryWordTagTypeEnum.TARGET_ASIN.getValue(), CpcQueryWordDto.QueryWordTagTypeEnum.NE_TARGET_ASIN.getValue()));
            if (targetTagTypeSet.containsAll(dto.getQueryWordTagTypeList())) {
                return true;
            }
        }
        // 自动化规则筛选投放id集合、组id集合
        List<Integer> operationTypeList = AdQueryStrategyTypeEnum.operationTypeList(dto.getAdStrategyTypeList());
        if (CollectionUtils.isNotEmpty(operationTypeList)) {
            String queryType = AmazonAd.AdQueryTypeEnum.SP_QUERY.getSearchField();
            List<String> targetIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(dto.getPuid(), cn.hutool.core.collection.CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, dto.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString(), null, queryType);
            dto.setAutoRuleIds(targetIdList);
            List<String> groupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, dto.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString(), null, null);
            dto.setAutoRuleGroupIds(groupIdList);
            if (CollectionUtils.isEmpty(targetIdList) && CollectionUtils.isEmpty(groupIdList) &&
                    !dto.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                return true;
            }
        }
        return false;
    }

    /**
     * 填充品牌细节信息
     *
     * @param vo
     * @param jsonArray
     */
    private void fillBrandMessage(ReportVo vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setCategoryRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookBack(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    /**
     * 填充品牌细节信息
     *
     * @param builder
     * @param jsonArray
     * @param item
     */
    private void fillBrandMessage(SearchTermSourceTargetDetailResponse.SourceTargetDetail.Builder builder, JSONArray jsonArray, SearchTermBO item) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(builder, valueArray, item);
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    builder.setBrandName(jsonObject.getString("value"));
                    item.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    builder.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                    item.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    builder.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                    item.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    builder.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                    item.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    builder.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                    item.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    builder.setCategoryRating("1~" + jsonObject.getString("value"));
                    item.setCategoryRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    builder.setCategoryRating(jsonObject.getString("value") + "~5");
                    item.setCategoryRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        builder.setDistribution("具有Prime资格");
                        item.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        builder.setDistribution("不具有Prime资格");
                        item.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    builder.setLookBack(jsonObject.getString("value"));
                    item.setLookBack(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }
}
