package com.meiyunji.sponsored.service.syncTask;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbGroupApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdGroupApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcAdGroupApiService;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.stream.enums.StreamConstants;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamLogService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamRedisCountService;
import com.meiyunji.sponsored.service.stream.service.IAmazonManagementStreamTaskRetryService;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorStreamTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.monitor.MonitorTypeEnum;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.syncTask.message.ManagementAdGroupStreamMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ManagementAdGroupConsumer {


    @Resource
    private IScVcShopAuthDao shopAuthDao;
    @Resource
    private CpcAdGroupApiService cpcAdGroupApiService;
    @Resource
    private CpcSbGroupApiService cpcSbGroupApiService;
    @Resource
    private CpcSdGroupApiService cpcSdGroupApiService;
    @Resource
    private IAmazonManagementStreamTaskRetryService amazonManagementStreamTaskRetryService;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private IAmazonManagementStreamLogService amazonManagementStreamLogService;
    @Autowired
    private IAmazonManagementStreamRedisCountService amazonManagementStreamRedisCountService;

    public void process(List<ManagementAdGroupStreamMessage> messages) throws Exception {
        log.info("adgroup stream data, {}", JSON.toJSONString(messages));
        Set<String> sellerIds = new HashSet<>();
        Set<String> marketplaceIds = new HashSet<>();
        Map<String, List<ManagementAdGroupStreamMessage>> collect = new HashMap<>();
        if (CollectionUtils.isEmpty(messages)) {
            log.info("ad stream group message empty");
            return;
        }
        for (ManagementAdGroupStreamMessage message : messages) {
            sellerIds.add(message.getAdvertiseId());
            marketplaceIds.add(message.getMarketplaceId());
            collect.computeIfAbsent(getKey(message), key -> new ArrayList<>()).add(message);
        }
        Date nowDate = new Date();
        amazonManagementStreamLogService.printAllManagementStreamCount(MonitorTypeEnum.all, MonitorStreamTypeEnum.ad_group, nowDate, messages.size());
        LocalDateTime localDateTime = LocalDateTime.now();
//        amazonManagementStreamRedisCountService.countAllAmazonManagementStreamHour(localDateTime, messages.size());
        List<ShopAuth> shopAuths = shopAuthDao.getBySellerIdsAndMarketplaceIds(Lists.newArrayList(sellerIds), Lists.newArrayList(marketplaceIds));
        if (CollectionUtils.isEmpty(shopAuths)) {
            log.info("adGroup stream not fund shopAuths,sellerId:{},marketplaceId:{}", StringUtils.join(sellerIds, ","), StringUtils.join(marketplaceIds, ","));
            return;
        }

        Map<String, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(this::getKey, Function.identity()));
        for (Map.Entry<String, List<ManagementAdGroupStreamMessage>> entry : collect.entrySet()) {
            ShopAuth shopAuth = shopAuthMap.get(entry.getKey());
            if (shopAuth == null) {
                continue;
            }
            List<ManagementAdGroupStreamMessage> managementAdGroupStreamMessages = entry.getValue();
            amazonManagementStreamLogService.printPuidManagementStreamCount(shopAuth.getPuid(), shopAuth.getId(), MonitorTypeEnum.puid, MonitorStreamTypeEnum.ad_group, nowDate, managementAdGroupStreamMessages.size());

            Map<String, List<ManagementAdGroupStreamMessage>> stringListMap = new HashMap<>();
            for (ManagementAdGroupStreamMessage managementAdGroupStreamMessage : managementAdGroupStreamMessages) {
                stringListMap.computeIfAbsent(managementAdGroupStreamMessage.getAdProduct(), key -> new ArrayList<>()).add(managementAdGroupStreamMessage);
            }
            //缺失字段比较多，广告活动层级数据量比较小，先尝试直接使同步的方式取回来
            //即使入库了也如需同步一遍活动服务状态，保持一直，可以考虑用异步；
            //查询profile和店铺逻辑可以考虑加缓存提供公共服务
            for (Map.Entry<String, List<ManagementAdGroupStreamMessage>> e : stringListMap.entrySet()) {
                String k = e.getKey();
                List<ManagementAdGroupStreamMessage> v = e.getValue();
                AdProductEnum adProductEnum = AdProductEnum.getAdProductEnum(k);
                if (adProductEnum == null) {
                    continue;
                }
                if (AdProductEnum.SPONSORED_PRODUCTS == adProductEnum) {
                    List<List<String>> partition = Lists.partition(v.stream().map(ManagementAdGroupStreamMessage::getAdGroupId).distinct().collect(Collectors.toList()), StreamConstants.SP_MAX_GROUP_IDS_COUNT);
                    for (List<String> ids : partition) {
                        try {
                            cpcAdGroupApiService.syncAdGroups(shopAuth, null, StringUtils.join(ids, ","), null,true , null, dynamicRefreshConfiguration.getSyncStreamManageProxy() );
                            List<AmazonAdGroup> amazonAdGroups = cpcAdSyncService.confirmAdGroupTypeByGroupIdList(shopAuth, ids);
                            if (CollectionUtils.isNotEmpty(amazonAdGroups)) {
                                List<String> retryIds = amazonAdGroups.stream().map(AmazonAdGroup::getAdGroupId).collect(Collectors.toList());
                                amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.GROUP, retryIds);
                                amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size() - retryIds.size());
                            } else {
                                amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size());
                            }
                        } catch (Exception exception) {
                            amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.GROUP, ids);
                        }
                    }
                } else if (AdProductEnum.SPONSORED_BRANDS == adProductEnum) {
                    List<List<String>> partition = Lists.partition(v.stream().map(ManagementAdGroupStreamMessage::getAdGroupId).distinct().collect(Collectors.toList()), StreamConstants.SB_MAX_GROUP_IDS_COUNT);
                    for (List<String> ids : partition) {
                        try {
                            cpcSbGroupApiService.syncAdGroups(shopAuth, null, StringUtils.join(ids, ","), true, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            List<AmazonSbAdGroup> amazonSbAdGroups = cpcAdSyncService.confirmAdGroupTargetTypeAndAdFormatAndLandingPage(shopAuth, null, ids);
                            if (CollectionUtils.isNotEmpty(amazonSbAdGroups)) {
                                List<String> retryIds = amazonSbAdGroups.stream().map(AmazonSbAdGroup::getAdGroupId).collect(Collectors.toList());
                                amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.GROUP, retryIds);
                                amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size() - retryIds.size());
                            } else {
                                amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size());
                            }
                        } catch (Exception exception) {
                            amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.GROUP, ids);
                        }
                    }

                } else if (AdProductEnum.SPONSORED_DISPLAY == adProductEnum) {
                    List<List<String>> partition = Lists.partition(v.stream().map(ManagementAdGroupStreamMessage::getAdGroupId).distinct().collect(Collectors.toList()), StreamConstants.SD_MAX_GROUP_IDS_COUNT);
                    for (List<String> ids : partition) {
                        try {
                            cpcSdGroupApiService.syncAdGroups(shopAuth, null, StringUtils.join(ids, ","), null, true, null, dynamicRefreshConfiguration.getSyncStreamManageProxy());
                            amazonManagementStreamRedisCountService.countMinutesSuccessAmazonManagementStreamHour(localDateTime, ids.size());
                        } catch (Exception exception) {
                            amazonManagementStreamTaskRetryService.saveTaskRetry(shopAuth.getPuid(), shopAuth.getId(), adProductEnum, AmazonStreamTaskTypeEnum.GROUP, ids);
                        }
                    }
                }
            }

        }


    }

    private String getKey(ManagementAdGroupStreamMessage groupStreamMessage) {
        return groupStreamMessage.getAdvertiseId() + "#" + groupStreamMessage.getMarketplaceId();
    }

    private String getKey(ShopAuth shopAuth) {
        return shopAuth.getSellingPartnerId() + "#" + shopAuth.getMarketplaceId();
    }



}
