package com.meiyunji.sponsored.service.util;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.ImmutableSet;
import org.apache.commons.lang.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2022/10/12 15:40
 */
public class AmazonErrorUtils {

    private static final String AMAZON_ERROR_MAP_WITH_KEYWORD = "Keyword is invalid";

    // 使用Map来存储允许的字符，键为字符，值可以随意设置，这里简单设为布尔值true
    private static final Map<Character, Boolean> ALLOWED_CHARS_MAP = new HashMap<>();

    static {
        // 初始化允许的字符到Map中
        String allowedChars = " .&\\+[]\t\n\r'\"0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ®ÁÉÍÑÓÚÜáéíñóúüÄÖŒßäöÀÂÆÇÈÊËÎÏÔÙÛŸàâæçèêëîïôùûÿœ";
        for (char c : allowedChars.toCharArray()) {
            ALLOWED_CHARS_MAP.put(c, true);
        }
    }

    public static final ImmutableMap<String, String> AMAZON_ERROR_MAP_WITH_PLACEHOLDER = new ImmutableMap.Builder<String, String>()
            .put("Keyword exceeds length constraints", "%s：单个否定词最多由%s个单词组成")
            .build();

    public static final ImmutableMap<String, String> AMAZON_ERROR_MAP = new ImmutableMap.Builder<String, String>()
            .put("This product is ineligible for advertising", "此产品不符合广告推广条件")
            .put("Product is ineligible for advertising", "广告产品不合法，无法添加")
            .put("number of targeting clause (keyword) non archived exceed limit per ad agroup", "未归档投放超出广告组投放数量上限")
            .put("An entity with that name already exists", "名称已存在")
            .put("Targetingclause (keyword) is invalid", "无效关键词，请检查关键词格式")
            .put("Keyword did not specify ad group id for create operation", "网络延迟，请稍后重试")
            .put("Cannot find entity with id", "网络延迟，请稍后重试")
            .put("Parent entity is archived", "已归档设置不可操作")
            .put("Please try again later", "网络延迟，请稍后重试")
            .put("Start date cannot be in the past", "开始日期不可设置为过去日期")
            .put("Rate exceeded for request authorization", "网络延迟，请稍后重试")
            .put("Entity ID does not exist", "网络延迟，请稍后重试")
            .put("Campaign type cannot be changed", "广告活动名称已存在")
            .put("Campaign type cannot be changed Cannot change targeting type", "广告活动名称已存在")
            .put("Request violates entity uniqueness constraint!", "广告对象已存在")
            .put("An entity with that name already exist", "广告对象已存在")
            .put("Campaign name invalid characters", "广告活动名称存在无效字符")
            .put("Archived entity cannot be modified", "已归档设置不可操作")
            .put("Ad group name already exists for campaign", "广告组名称已存在")
            .put("already exists", "历史已存在相同的投放，请检查")
            .put("Only negative keywords and negative product targets are allowed in auto-targeting campaigns", "亚马逊不支持将品牌否定投放到自动投放的广告组")
            .put("Bid is higher than the campaign budget.", "竞价高于广告活动预算")
            .put("Bid must be less than half the value of your budget.", "竞价必须小于每日预算的一半")
            .put("Number of targeting clauses per ad group exceeds the limit","该广告组的投放数量超出最大限制1000")
            .put("Keyword is invalid","关键词无效")
            .put("Ad group cannot have both keyword and product targets","广告组不能同时包含关键词和产品投放")
            .put("Keyword was specified with a wrong bid or empty value","关键词竞价超出限制范围或为空值")
            .put("Ad group cannot have both keyword(theme) and product attribute","广告组不能同时包含关键词和产品投放")
            .put("Cannot add keyword to SB ad group without ad","无法在没有广告产品的情况下向 SB 广告组添加/更新关键字")
            .put("Failed to create keyword","创建关键词失败")
            .put("Targeting expression does not conform to language specific rules","存在语法错误")
            .put("Targeting clause did not specify an expression for create operation","类目投放发生错误")
            .put("Cannot add/update keyword to SB ad group without ad","无法在没有广告产品的情况下向 SB 广告组添加/更新关键字")
            .put("Cannot add/update theme to SB ad group without ad.", "无法在没有广告产品的情况下向 SB 广告组添加/更新主题")
            .put("Keyword exceeds length constraints","关键词超出长度限制")
            .put("Target with invalid syntax or semantics expression because of the provided note","存在语法错误")
            .put("Unauthorized","未取得授权")
            .put("Cannot update bid for this targeting clause when the ad group has an enabled optimization rule.","无法手动调整竞价，因为广告组正在运行针对竞价和投放的效果优化方案")
            .build();


    /**
     * 用来判断是否重试
     */
    public static final String ARCHIVED_MESSAGE = "已归档设置不可操作";

    /**
     * 只要错误信息中包含枚举中的值就会转换中文信息
     *
     * @param msg
     * @return
     */
    public static String getError(String msg) {
        if (StringUtils.isBlank(msg)) {
            return msg;
        }
        ImmutableSet<Map.Entry<String, String>> entries = AMAZON_ERROR_MAP.entrySet();
        String k = msg.toLowerCase();
        for (Map.Entry<String, String> entry : entries) {
            if (k.contains(entry.getKey().toLowerCase())) {
                return entry.getValue();
            }
        }
        return msg;
    }

    /**
     * 搜索词、投放词 添加到关键词投放/添加到否定关键词投放异常信息校验映射
     * @param msg
     * @return
     */
    public static String getKeywordTargetError(String msg, String keyword) {
        if (StringUtils.isBlank(msg)) {
            return msg;
        }
        if (!msg.equals(AMAZON_ERROR_MAP_WITH_KEYWORD)) {
            ImmutableSet<Map.Entry<String, String>> entries = AMAZON_ERROR_MAP.entrySet();
            String k = msg.toLowerCase();
            for (Map.Entry<String, String> entry : entries) {
                if (k.contains(entry.getKey().toLowerCase())) {
                    return entry.getValue();
                }
            }
        }
        //校验开头和结尾是否存在空格
        if (keyword.startsWith(" ") || keyword.endsWith(" ")) {
            return "开头和结尾存在空格";
        }
        //校验双引号的个数是否为偶数
        int quoteCount = keyword.length() - keyword.replace("\"", "").length();
        if (quoteCount != 0) {
            if (quoteCount % 2 != 0) {
                return "缺失双引号";
            } else {
                return "双引号的使用不符合规范";
            }
        }
        //校验+/-的使用
        if (keyword.contains("+") || keyword.contains("-")) {
            String[] parts;
            if (keyword.contains("+")) {
                parts = keyword.split("\\+");
            } else {
                parts = keyword.split("-");
            }
            for (String part : parts) {
                if (part.startsWith(" ") && part.endsWith(" ")) {
                    return "“+”/“-”仅允许用于连接单词，请检查";
                }
            }
        }
        //非法字符校验
        List<Character> characters = new ArrayList<>();
        for (int i = 0; i < keyword.length(); i++) {
            char c = keyword.charAt(i);
            if (!ALLOWED_CHARS_MAP.containsKey(c)) {
                characters.add(c);
            }
        }
        if (CollectionUtils.isNotEmpty(characters)) {
            StringBuilder illegalCharsBuilder = new StringBuilder();
            for (Character c : characters) {
                illegalCharsBuilder.append(c);
            }
            return "含有非法字符："+ illegalCharsBuilder;
        }
        return "关键词无效";
    }



    /**
     * 只要错误信息中包含枚举中的值就会转换中文信息
     *
     * @param msg
     * @return
     */
    public static String getErrorWithPlaceHolder(String msg, String arg, String[] args) {
        if (StringUtils.isBlank(msg)) {
            return msg;
        }
        ImmutableSet<Map.Entry<String, String>> entries = AMAZON_ERROR_MAP_WITH_PLACEHOLDER.entrySet();
        String k = msg.toLowerCase();
        for (Map.Entry<String, String> entry : entries) {
            if (k.contains(entry.getKey().toLowerCase())) {
                return String.format(entry.getValue(), args);
            }
        }
        return getKeywordTargetError(msg, arg);
    }
}
