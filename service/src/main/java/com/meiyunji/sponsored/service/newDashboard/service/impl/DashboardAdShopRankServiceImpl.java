package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdShopRankResponseVo;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.ShopRankChartDto;
import com.meiyunji.sponsored.service.newDashboard.dto.ShopRankDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.ShopRankPageDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardRankQueryFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdShopRankService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardShopRankReqVo;
import com.meiyunji.sponsored.service.util.SummaryReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-22  16:20
 */
@Service
@Slf4j
public class DashboardAdShopRankServiceImpl implements IDashboardAdShopRankService {

    @Autowired
    private IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;

    @Autowired
    private IExcelService excelService;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcShopDataService cpCShopDataService;

    @Autowired
    private IVcShopAuthDao vcShopAuthDao;

    private static final List<String> baseHeaderList = Arrays.asList(
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate",
            "roas", "roasMomRate", "roasYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayShopSales", "shopSalesMomRate", "shopSalesYoyRate",
            "displayAcots", "acotsMomRate", "acotsYoyRate",
            "displayAsots", "asotsMomRate", "asotsYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");


    private static final Map<String, String> baseDataHeaderMap = new HashMap<String, String>(){
        {
            put(DashboardDataFieldEnum.COST.getCode(), "displayCost");
            put(DashboardDataFieldEnum.TOTAL_SALES.getCode(), "displayTotalSales");
            put(DashboardDataFieldEnum.IMPRESSIONS.getCode(), "impressions");
            put(DashboardDataFieldEnum.CLICKS.getCode(), "clicks");
            put(DashboardDataFieldEnum.ORDER_NUM.getCode(), "orderNum");
            put(DashboardDataFieldEnum.SALE_NUM.getCode(), "saleNum");
        }
    };

    private static final Map<String, List<String>> calDataHeaderMap = new HashMap<String, List<String>>(){
        {
            put(DashboardDataFieldEnum.ACOS.getCode(), Arrays.asList("displayAcos", "displayCost", "displayTotalSales"));
            put(DashboardDataFieldEnum.ROAS.getCode(), Arrays.asList("displayRoas", "displayTotalSales", "displayCost"));
            put(DashboardDataFieldEnum.CLICK_RATE.getCode(), Arrays.asList("displayClickRate", "clicks", "impressions"));
            put(DashboardDataFieldEnum.CONVERSION_RATE.getCode(), Arrays.asList("displayConversionRate", "orderNum", "clicks"));
            put(DashboardDataFieldEnum.CPC.getCode(), Arrays.asList("displayCpc", "displayCost", "clicks"));
            put(DashboardDataFieldEnum.CPA.getCode(), Arrays.asList("displayCpa", "displayCost", "orderNum"));
        }
    };

    @Override
    public DashboardAdShopRankResponseVo queryShopRank(DashboardShopRankReqVo req) {
        DashboardAdShopRankResponseVo.Builder voBuilder = DashboardAdShopRankResponseVo.newBuilder();
        ShopRankPageDto shopRankPageDto = this.getTopList(req, false);
        List<ShopRankDto> resultList = shopRankPageDto.getDtoList();
        ShopRankChartDto chartDto = shopRankPageDto.getChartDto();
        List<Integer> allShopId = vcShopAuthDao.getAllShopId(req.getPuid());
        boolean isVc = CollectionUtils.isNotEmpty(allShopId);
        //组装数据返回
        List<DashboardAdShopRankResponseVo.TopList> topList = resultList.stream().map(d -> {
            DashboardAdShopRankResponseVo.TopList.Builder vo = DashboardAdShopRankResponseVo.TopList.newBuilder();
            BeanUtils.copyProperties(d, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(d));
            if (DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(req.getQueryField()) && Objects.nonNull(d.getId())) {
                vo.setShopId(Integer.parseInt(d.getId()));
            }
            Optional.ofNullable(d.getCost()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCost);
            Optional.ofNullable(d.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(d.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(d.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(d.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(d.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(d.getShopSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setShopSales);
            Optional.ofNullable(d.getAcos()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(d.getRoas()).map(String::valueOf).ifPresent(vo::setRoas);
            Optional.ofNullable(d.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(d.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(d.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(d.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);
            if (!isVc) {
                Optional.ofNullable(d.getAcots()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcots);
                Optional.ofNullable(d.getAsots()).map(CalculateUtil::formatPercent).ifPresent(vo::setAsots);
            } else {
                vo.setAcots("-");
                vo.setAsots("-");

            }
            return vo.build();
        }).collect(Collectors.toList());
        voBuilder.addAllRows(topList);
        if (chartDto != null) {
            DashboardAdShopRankResponseVo.SumVo.Builder sumVo = DashboardAdShopRankResponseVo.SumVo.newBuilder();
            sumVo.setSum(chartDto.getSum());
            if (chartDto.getPercent() != null) {
                sumVo.setPercent(chartDto.getPercent());
            }
            Optional.ofNullable(chartDto.getTopSum()).ifPresent(sumVo::setTopSum);
            Optional.ofNullable(chartDto.getTopPercent()).ifPresent(sumVo::setTopPercent);
            voBuilder.setSumVo(sumVo.build());
        }
        return voBuilder.build();
    }

    @Override
    public List<String> exportShopRank(DashboardShopRankReqVo reqVo) {
        ShopRankPageDto shopRankPageDto = this.getTopList(reqVo, true);
        String url = this.writeExcelAndUpload(shopRankPageDto.getDtoList(), reqVo);
        log.info("dashboard export shop rank, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private ShopRankPageDto getTopList(DashboardShopRankReqVo req, boolean isExport) {
        ShopRankPageDto dto = new ShopRankPageDto();
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        Map<String, BigDecimal> salesMap = new HashMap<>();
        Map<String, BigDecimal> yoySalesMap = new HashMap<>();
        Map<String, BigDecimal> momSalesMap = new HashMap<>();
        BigDecimal[] sumShopSale = new BigDecimal[]{new BigDecimal(0)};
        //获取店铺或站点销售额
        this.fillSaleMap(req, salesMap, yoySalesMap, momSalesMap, sumShopSale);
        //查询当前日期时间段内广告活动图表信息
        List<Object> argsList = Lists.newArrayList();
        //当前时间段查询sql
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        String subSqlA = odsAmazonAdCampaignAllReportDao.rankQuerySql(puid, shopIdList, marketplaceIdList, null, currency, req.getStartDate(),
                req.getEndDate(), argsList, req.getQueryField(), siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField);
        String subSqlB;
        List<DashboardAdTopDataDto> currentAndSubList;
        List<ShopRankDto> resultList = Lists.newArrayList();
        //是否需要查询对比的汇总数据，用于获取选择除占比外其他的扇形图数据
        boolean isSelAllSub = !isExport && req.getIsChart() == 1 && Stream.of(DashboardDataFieldEnum.COST, DashboardDataFieldEnum.TOTAL_SALES,
                DashboardDataFieldEnum.IMPRESSIONS, DashboardDataFieldEnum.CLICKS,
                DashboardDataFieldEnum.ORDER_NUM, DashboardDataFieldEnum.SALE_NUM).anyMatch(d -> d == dataField)
                && !DashboardOrderByRateEnum.PERCENT.equals(orderField);

        //存储同比时间或者环比时间
        boolean isMomMap;
        String subSqlBStartDate;
        String subSqlBEndDate;
        String startDate;
        String endDate;
        //如果是按占比排序，即当前时间段值/汇总值,先计算同比,或按同比排序，即当前时间段值/同比时间段值
        if (DashboardOrderByRateEnum.PERCENT == orderField || Stream.of(DashboardOrderByRateEnum.YOY_RATE, DashboardOrderByRateEnum.YOY_VALUE).anyMatch(r -> r == orderField)) {
            subSqlBStartDate = req.getYoyStartDate();
            subSqlBEndDate = req.getYoyEndDate();
            startDate = req.getMomStartDate();
            endDate = req.getMomEndDate();
            isMomMap = true;
        } else {
            subSqlBStartDate = req.getMomStartDate();
            subSqlBEndDate = req.getMomEndDate();
            startDate = req.getYoyStartDate();
            endDate = req.getYoyEndDate();
            isMomMap = false;
        }
        
        subSqlB = odsAmazonAdCampaignAllReportDao.rankQuerySql(puid, shopIdList, marketplaceIdList, null, currency, subSqlBStartDate,
                subSqlBEndDate, argsList, req.getQueryField(), siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField);
        currentAndSubList = odsAmazonAdCampaignAllReportDao.queryAdRankYoyOrMomTop(subSqlA, subSqlB,
                argsList, dataField, orderField, req.getOrderBy(), req.getQueryField(), limit, isSelAllSub, req.getNoZero());
        List<String> idList = currentAndSubList.parallelStream().map(DashboardAdTopDataDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(idList)) {
            List<ShopRankDto> dtoList = odsAmazonAdCampaignAllReportDao.queryAdRankData(puid, shopIdList, marketplaceIdList, idList, currency,
                    startDate, endDate, req.getQueryField());
            Map<String, ShopRankDto> dtoMap = dtoList.parallelStream().collect(Collectors.toMap(ShopRankDto::getId, v1 -> v1));
            resultList = this.buildShopRankDtoList(currentAndSubList, dtoMap, isMomMap, req.getYoyOverLimit(),
                    salesMap, yoySalesMap, momSalesMap, sumShopSale);
        }

        //填充店铺、站点名称
        if (DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(req.getQueryField())) {
            Set<Integer> idSet = resultList.parallelStream().map(e -> Integer.parseInt(e.getId())).collect(Collectors.toSet());
            Map<String, ShopAuthBo> shopMap = shopAuthDao.getShopAuthBoByIds(puid, new ArrayList<>(idSet)).stream()
                    .collect(Collectors.toMap(e -> e.getId().toString(), v1 -> v1, (old, current) -> current));
            for (ShopRankDto result : resultList) {
                result.setShopName(Optional.ofNullable(shopMap.get(result.getId())).map(ShopAuthBo::getName).orElse(""));
                result.setMarketplaceId(Optional.ofNullable(shopMap.get(result.getId())).map(ShopAuthBo::getMarketplaceId).orElse(""));
            }
        } else if (DashboardRankQueryFieldEnum.MARKETPLACE_QUERY_TYPE.getCode().equals(req.getQueryField())) {
            Map<String, String> idNameMap = resultList.parallelStream().collect(Collectors.toMap(ShopRankDto::getId,
                    e -> Optional.ofNullable(AmznEndpoint.getByMarketplaceId(e.getId())).map(AmznEndpoint::getMarketplaceCN).orElse("")));
            for (ShopRankDto result : resultList) {
                result.setMarketplaceName(idNameMap.getOrDefault(result.getId(), ""));
            }
        }

        if (!isExport && req.getIsChart() == 1 && Stream.of(DashboardDataFieldEnum.COST, DashboardDataFieldEnum.TOTAL_SALES,
                DashboardDataFieldEnum.IMPRESSIONS, DashboardDataFieldEnum.CLICKS,
                DashboardDataFieldEnum.ORDER_NUM, DashboardDataFieldEnum.SALE_NUM).anyMatch(d -> d == dataField)) {
            ShopRankChartDto shopRankChartDto = this.computeShopRankChartDto(currentAndSubList, dataField, orderField);
            dto.setChartDto(shopRankChartDto);
            //兼容前端展示图的其他店铺，总值和top值不相同即有其他店铺
            if (shopRankChartDto.getOtherSum() != null) {
                ShopRankDto orderShopRankDto = new ShopRankDto();
                if (DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(req.getQueryField())) {
                    orderShopRankDto.setShopName("其他店铺");
                } else if (DashboardRankQueryFieldEnum.MARKETPLACE_QUERY_TYPE.getCode().equals(req.getQueryField())) {
                    orderShopRankDto.setMarketplaceName("其他站点");
                }
                if (DashboardDataFieldEnum.COST.equals(dataField)) {
                    orderShopRankDto.setCost(shopRankChartDto.getOtherSum());
                } else if (DashboardDataFieldEnum.TOTAL_SALES.equals(dataField)) {
                    orderShopRankDto.setTotalSales(shopRankChartDto.getOtherSum());
                } else if (DashboardDataFieldEnum.IMPRESSIONS.equals(dataField)) {
                    orderShopRankDto.setImpressions(shopRankChartDto.getOtherSum().longValue());
                } else if (DashboardDataFieldEnum.CLICKS.equals(dataField)) {
                    orderShopRankDto.setClicks(shopRankChartDto.getOtherSum().intValue());
                } else if (DashboardDataFieldEnum.ORDER_NUM.equals(dataField)) {
                    orderShopRankDto.setOrderNum(shopRankChartDto.getOtherSum().intValue());
                } else if (DashboardDataFieldEnum.SALE_NUM.equals(dataField)) {
                    orderShopRankDto.setSaleNum(shopRankChartDto.getOtherSum().intValue());
                }
                orderShopRankDto.setHasOther(true);
                resultList.add(orderShopRankDto);
            }
        }
        //是否需要排序
        if (StringUtils.isNotBlank(req.getListOrderField()) || StringUtils.isNotBlank(req.getListOrderType())) {
            OrderByUtil.sortedByOrderField(resultList, req.getListOrderField(), req.getListOrderType(), "id");
        }
        dto.setDtoList(resultList);
        return dto;
    }

    private List<ShopRankDto> buildShopRankDtoList(List<DashboardAdTopDataDto> currentAndSubList, Map<String, ShopRankDto> dtoMap,
                                                   boolean isMomMap, boolean yoyOverLimit,
                                                   Map<String, BigDecimal> salesMap, Map<String, BigDecimal> yoySalesMap,
                                                   Map<String, BigDecimal> momSalesMap, BigDecimal[] sumShopSales) {
        List<ShopRankDto> resultList = new ArrayList<>();
        for (DashboardAdTopDataDto dataDto : currentAndSubList) {
            //当前查询list已经包含了当期及同比的计算属性和汇总属性
            //还需要计算环比占比，同比占比
            ShopRankDto momDto = dtoMap.get(dataDto.getId());
            if (momDto == null) {
                momDto = new ShopRankDto();
                momDto.setId(dataDto.getId());
                CalculateAdDataUtil.buildZeroAdCalData(momDto);
            }
            ShopRankDto currentDto = new ShopRankDto();
                BeanUtils.copyProperties(dataDto, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(dataDto));
            ShopRankDto yoyDto = this.convertBasicAndCalData(dataDto);
            ShopRankDto summaryDto = this.convertSummaryData(dataDto);
            currentDto.setShopSales(salesMap.getOrDefault(dataDto.getId(), BigDecimal.ZERO));
            momDto.setShopSales(momSalesMap.getOrDefault(dataDto.getId(), BigDecimal.ZERO));
            yoyDto.setShopSales(yoySalesMap.getOrDefault(dataDto.getId(), BigDecimal.ZERO));
            CalculateAdDataUtil.calAdCalShopData(currentDto);
            if (isMomMap) {
                CalculateAdDataUtil.calAdCompareYearDataReflex(currentDto, yoyDto);
                CalculateAdDataUtil.calAdCompareMonthDataReflex(currentDto, momDto);
                this.computeData(currentDto, yoyDto, momDto, summaryDto, yoyOverLimit, sumShopSales);
            } else {
                CalculateAdDataUtil.calAdCompareYearDataReflex(currentDto, momDto);
                CalculateAdDataUtil.calAdCompareMonthDataReflex(currentDto, yoyDto);
                this.computeData(currentDto, momDto, yoyDto, summaryDto, yoyOverLimit, sumShopSales);
            }
            resultList.add(currentDto);
        }

        return resultList;
    }

    /**
     * 填充店铺、站点销售额
     */
    private void fillSaleMap(DashboardShopRankReqVo req, Map<String, BigDecimal> salesMap,
                             Map<String, BigDecimal> yoySalesMap, Map<String, BigDecimal> momSalesMap,
                             BigDecimal[] sumShopSale) {
        if (DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(req.getQueryField())) {
            salesMap.putAll(cpCShopDataService.shopSaleGroupByShop(req.getPuid(), req.getShopIdList(), req.getMarketplaceIdList(), req.getStartDate(), req.getEndDate(), req.getCurrency())
                    .stream().collect(Collectors.toMap(e -> String.valueOf(e.getShopId()), e -> {
                        sumShopSale[0] = sumShopSale[0].add(e.getSumRange());
                        return e.getSumRange();
                    })));
            yoySalesMap.putAll(cpCShopDataService.shopSaleGroupByShop(req.getPuid(), req.getShopIdList(), req.getMarketplaceIdList(), req.getYoyStartDate(), req.getYoyEndDate(), req.getCurrency())
                    .stream().collect(Collectors.toMap(e -> String.valueOf(e.getShopId()), e -> e.getSumRange())));
            momSalesMap.putAll(cpCShopDataService.shopSaleGroupByShop(req.getPuid(), req.getShopIdList(), req.getMarketplaceIdList(), req.getMomStartDate(), req.getMomEndDate(), req.getCurrency())
                    .stream().collect(Collectors.toMap(e -> String.valueOf(e.getShopId()), e -> e.getSumRange())));

        } else if (DashboardRankQueryFieldEnum.MARKETPLACE_QUERY_TYPE.getCode().equals(req.getQueryField())) {
            salesMap.putAll(cpCShopDataService.shopSaleGroupByMarketplace(req.getPuid(), req.getShopIdList(), req.getMarketplaceIdList(), req.getStartDate(), req.getEndDate(), req.getCurrency())
                    .stream().collect(Collectors.toMap(ShopSaleDto::getMarketplaceId, e -> {
                        sumShopSale[0] = sumShopSale[0].add(e.getSumRange());
                        return e.getSumRange();
                    })));
            yoySalesMap.putAll(cpCShopDataService.shopSaleGroupByMarketplace(req.getPuid(), req.getShopIdList(), req.getMarketplaceIdList(), req.getYoyStartDate(), req.getYoyEndDate(), req.getCurrency())
                    .stream().collect(Collectors.toMap(ShopSaleDto::getMarketplaceId, e -> e.getSumRange())));
            momSalesMap.putAll(cpCShopDataService.shopSaleGroupByMarketplace(req.getPuid(), req.getShopIdList(), req.getMarketplaceIdList(), req.getMomStartDate(), req.getMomEndDate(), req.getCurrency())
                    .stream().collect(Collectors.toMap(ShopSaleDto::getMarketplaceId, ShopSaleDto::getSumRange)));
        }
    }

    /**
     * 计算扇形图数据上面的汇总和top数据，会根据dataField和orderField两个下拉框变化
     */
    private ShopRankChartDto computeShopRankChartDto(List<DashboardAdTopDataDto> currentAndSubList,
                                                     DashboardDataFieldEnum dataField, DashboardOrderByRateEnum orderField) {
        ShopRankChartDto dto = new ShopRankChartDto();
        if (CollectionUtils.isEmpty(currentAndSubList)) {
            if (DashboardOrderByRateEnum.PERCENT.equals(orderField)) {
                dto.setSum("0");
                dto.setTopSum("0");
                dto.setTopPercent("0%");
            } else if (DashboardOrderByRateEnum.YOY_RATE.equals(orderField) || DashboardOrderByRateEnum.MOM_RATE.equals(orderField)) {
                dto.setSum("0%");
                dto.setPercent("0");
                dto.setTopSum("0%");
                dto.setPercent("0");
            } else if (DashboardOrderByRateEnum.YOY_VALUE.equals(orderField) || DashboardOrderByRateEnum.MOM_VALUE.equals(orderField)) {
                dto.setSum("0");
                dto.setPercent("0%");
                dto.setTopSum("0");
                dto.setPercent("0%");
            }
            return dto;
        }
        DashboardAdTopDataDto dashboardAdTopDataDto = currentAndSubList.get(0);
        //计算指标总值和top值
        BigDecimal sum = BigDecimal.ZERO;
        BigDecimal topSum = BigDecimal.ZERO;
        if (DashboardDataFieldEnum.COST.equals(dataField)) {
            sum = dashboardAdTopDataDto.getAllCost();
            topSum = currentAndSubList.stream().map(DashboardAdTopDataDto::getCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (DashboardDataFieldEnum.TOTAL_SALES.equals(dataField)) {
            sum = dashboardAdTopDataDto.getAllTotalSales();
            topSum = currentAndSubList.stream().map(DashboardAdTopDataDto::getTotalSales).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
        } else if (DashboardDataFieldEnum.IMPRESSIONS.equals(dataField)) {
            sum = dashboardAdTopDataDto.getAllImpressions();
            topSum = BigDecimal.valueOf(currentAndSubList.stream().mapToLong(DashboardAdTopDataDto::getImpressions).filter(Objects::nonNull).sum());
        } else if (DashboardDataFieldEnum.CLICKS.equals(dataField)) {
            sum = dashboardAdTopDataDto.getAllClicks();
            topSum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getClicks).filter(Objects::nonNull).sum());
        } else if (DashboardDataFieldEnum.ORDER_NUM.equals(dataField)) {
            sum = dashboardAdTopDataDto.getAllOrderNum();
            topSum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getOrderNum).filter(Objects::nonNull).sum());
        } else if (DashboardDataFieldEnum.SALE_NUM.equals(dataField)) {
            sum = dashboardAdTopDataDto.getAllSaleNum();
            topSum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getSaleNum).filter(Objects::nonNull).sum());
        }
        //计算其他店铺、站点的指标总值，用于扇形图的其他店铺、站点指标值
        dto.setOtherSum(sum.compareTo(topSum) == 0 ? null : MathUtil.subtract(sum, topSum).setScale(2, RoundingMode.HALF_UP));
        //计算扇形图上的4个指标
        if (DashboardOrderByRateEnum.PERCENT.equals(orderField)) {
            dto.setSum(CalculateUtil.formatDecimal(sum));
            dto.setTopSum(CalculateUtil.formatDecimal(topSum));
            dto.setTopPercent(CalculateUtil.calPercentStr4Decimal(sum, topSum));
        } else {
            BigDecimal allNum = BigDecimal.ZERO;
            BigDecimal topNum = BigDecimal.ZERO;
            BigDecimal topSubNum = BigDecimal.ZERO;
            if (DashboardDataFieldEnum.COST.equals(dataField)) {
                allNum = dashboardAdTopDataDto.getAllCost();
                topNum = currentAndSubList.stream().map(DashboardAdTopDataDto::getCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                topSubNum = currentAndSubList.stream().map(DashboardAdTopDataDto::getSubCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (DashboardDataFieldEnum.TOTAL_SALES.equals(dataField)) {
                allNum = dashboardAdTopDataDto.getAllTotalSales();
                topNum = currentAndSubList.stream().map(DashboardAdTopDataDto::getTotalSales).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
                topSubNum = currentAndSubList.stream().map(DashboardAdTopDataDto::getSubTotalSales).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add);
            } else if (DashboardDataFieldEnum.IMPRESSIONS.equals(dataField)) {
                allNum = dashboardAdTopDataDto.getAllImpressions();
                topNum = BigDecimal.valueOf(currentAndSubList.stream().mapToLong(DashboardAdTopDataDto::getImpressions).filter(Objects::nonNull).sum());
                topSubNum = BigDecimal.valueOf(currentAndSubList.stream().mapToLong(DashboardAdTopDataDto::getSubImpressions).filter(Objects::nonNull).sum());
            } else if (DashboardDataFieldEnum.CLICKS.equals(dataField)) {
                allNum = dashboardAdTopDataDto.getAllClicks();
                topNum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getClicks).filter(Objects::nonNull).sum());
                topSubNum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getSubClicks).filter(Objects::nonNull).sum());
            } else if (DashboardDataFieldEnum.ORDER_NUM.equals(dataField)) {
                allNum = dashboardAdTopDataDto.getAllOrderNum();
                topNum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getOrderNum).filter(Objects::nonNull).sum());
                topSubNum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getSubOrderNum).filter(Objects::nonNull).sum());
            } else if (DashboardDataFieldEnum.SALE_NUM.equals(dataField)) {
                allNum = dashboardAdTopDataDto.getAllSaleNum();
                topNum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getSaleNum).filter(Objects::nonNull).sum());
                topSubNum = BigDecimal.valueOf(currentAndSubList.stream().mapToInt(DashboardAdTopDataDto::getSubSaleNum).filter(Objects::nonNull).sum());
            }
            if (DashboardOrderByRateEnum.YOY_RATE.equals(orderField) || DashboardOrderByRateEnum.MOM_RATE.equals(orderField)) {
                dto.setSum(CalculateUtil.calRate4Decimal(allNum, dashboardAdTopDataDto.getAllSubNum()));
                dto.setPercent(CalculateUtil.calValueStr4Decimal(allNum, dashboardAdTopDataDto.getAllSubNum()));
                dto.setTopSum(CalculateUtil.calRate4Decimal(topNum, topSubNum));
                dto.setTopPercent(CalculateUtil.calValueStr4Decimal(topNum, topSubNum));
            } else {
                dto.setSum(CalculateUtil.calValueStr4Decimal(allNum, dashboardAdTopDataDto.getAllSubNum()));
                dto.setPercent(CalculateUtil.calRate4Decimal(allNum, dashboardAdTopDataDto.getAllSubNum()));
                dto.setTopSum(CalculateUtil.calValueStr4Decimal(topNum, topSubNum));
                dto.setTopPercent(CalculateUtil.calRate4Decimal(topNum, topSubNum));
            }
        }
        return dto;
    }

    private ShopRankDto convertBasicAndCalData(DashboardAdTopDataDto subInfo) {
        ShopRankDto dto = new ShopRankDto();
        dto.setCost(subInfo.getSubCost());
        dto.setTotalSales(subInfo.getSubTotalSales());
        dto.setImpressions(subInfo.getSubImpressions());
        dto.setClicks(subInfo.getSubClicks());
        dto.setOrderNum(subInfo.getSubOrderNum());
        dto.setSaleNum(subInfo.getSubSaleNum());
        dto.setAcos(subInfo.getSubAcos());
        dto.setRoas(subInfo.getSubRoas());
        dto.setClickRate(subInfo.getSubClickRate());
        dto.setConversionRate(subInfo.getSubConversionRate());
        dto.setCpc(subInfo.getSubCpc());
        dto.setCpa(subInfo.getSubCpa());
        return dto;
    }

    private ShopRankDto convertSummaryData(DashboardAdTopDataDto subInfo) {
        ShopRankDto dto = new ShopRankDto();
        dto.setCost(subInfo.getAllCost());
        dto.setTotalSales(subInfo.getAllTotalSales());
        Optional.ofNullable(subInfo.getAllImpressions()).map(BigDecimal::longValue).ifPresent(dto::setImpressions);
        Optional.ofNullable(subInfo.getAllClicks()).map(BigDecimal::intValue).ifPresent(dto::setClicks);
        Optional.ofNullable(subInfo.getAllOrderNum()).map(BigDecimal::intValue).ifPresent(dto::setOrderNum);
        Optional.ofNullable(subInfo.getAllSaleNum()).map(BigDecimal::intValue).ifPresent(dto::setSaleNum);
        return dto;
    }

    private void computeData(ShopRankDto dto, ShopRankDto yoyDto,
                             ShopRankDto momDto, ShopRankDto summary,
                             boolean yoyOverLimit, BigDecimal[] sumShopSales) {
        if (Objects.isNull(dto)) {
            return;
        }
        CalculateAdDataUtil.calAdCalData(dto);//不用数据库统计的计算指标，使用原始指标计算得到计算指标保持数据一致性

        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
            CalculateAdDataUtil.calAdYoyShopData(dto, null);
        } else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalData(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflex(dto, yoyDto);//填充环比增长值
            CalculateAdDataUtil.calAdYoyDataReflex(dto, yoyDto);//填充环比增长率
            CalculateAdDataUtil.calAdCalShopData(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyShopValueReflex(dto, yoyDto);//填充环比增长值
            CalculateAdDataUtil.calAdYoyShopDataReflex(dto, yoyDto);//填充环比增长率
        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdCalData(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomValueReflex(dto, momDto);//填充同比增长值
            CalculateAdDataUtil.calAdMomDataReflex(dto, momDto);//填充同比增长率
            CalculateAdDataUtil.calAdCalShopData(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomShopValueReflex(dto, momDto);//填充同比增长值
            CalculateAdDataUtil.calAdMomShopDataReflex(dto, momDto);//填充同比增长率
        }
        CalculateAdDataUtil.calAdPercentData(dto, summary);
        //设置店铺销售额占比
        log.info("total sales about all shop is:{}", sumShopSales[0]);
        dto.setShopSalesPercent(CalculateUtil.calPercentStr4Decimal(dto.getShopSales(), sumShopSales[0]));
    }

    private String writeExcelAndUpload(List<ShopRankDto> dtoList, DashboardShopRankReqVo reqVo) {
        dtoList.forEach(dataDto -> CalculateAdDataUtil.fillDisplay4Export(dataDto, reqVo.getCurrency()));
        if (CollectionUtils.isNotEmpty(dtoList)) {
            dtoList.forEach(e -> {
                CalculateAdDataUtil.calAdCalDataForExport(e, reqVo.getCurrency());
                CalculateAdDataUtil.calAdCalShopDataForExport(e, reqVo.getCurrency());
            });
        }
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(reqVo.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(reqVo.getDataField());
        List<String> headers = new ArrayList<>();
        headers.add(DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(reqVo.getQueryField()) ? "shopName" : "marketplaceName");
        if (reqVo.getIsChart() == 0) {
            headers.addAll(baseHeaderList);
            //计算导出列
            if (reqVo.getPercent() != 1) {
                headers = headers.stream().filter(x -> !x.contains(DashboardOrderByRateEnum.PERCENT.getSuffix())).collect(Collectors.toList());
            }
            if (reqVo.getMom() != 1) {
                headers = headers.stream().filter(x -> !x.contains(DashboardOrderByRateEnum.MOM_RATE.getSuffix())).collect(Collectors.toList());
            }
            if (reqVo.getYoy() != 1) {
                headers = headers.stream().filter(x -> !x.contains(DashboardOrderByRateEnum.YOY_RATE.getSuffix())).collect(Collectors.toList());
            }
        } else {
            if (Stream.of(DashboardDataFieldEnum.COST, DashboardDataFieldEnum.TOTAL_SALES,
                    DashboardDataFieldEnum.IMPRESSIONS, DashboardDataFieldEnum.CLICKS,
                    DashboardDataFieldEnum.ORDER_NUM, DashboardDataFieldEnum.SALE_NUM).anyMatch(d -> d == dataField)) {
                headers.add(baseDataHeaderMap.get(dataField.getCode()));
                headers.add(dataField.getCode() + orderField.getCode().substring(0, 1).toUpperCase() + orderField.getCode().substring(1));
            } else {
                headers.addAll(calDataHeaderMap.get(dataField.getCode()));
                if (DashboardOrderByRateEnum.PERCENT != orderField) {
                    headers.add(2, dataField.getCode() + orderField.getCode().substring(0, 1).toUpperCase() + orderField.getCode().substring(1));
                }
            }
        }

        String orderBy = Optional.ofNullable(DashboardOrderByEnum.orderByMap.get(reqVo.getOrderBy()))
                .map(DashboardOrderByEnum::getExportDesc).orElse("");
        String limitCnt = Optional.ofNullable(reqVo.getLimit()).map(String::valueOf).orElse("");
        //写excel并上传
        String fileName = (DashboardRankQueryFieldEnum.SHOP_QUERY_TYPE.getCode().equals(reqVo.getQueryField()) ? "店铺排行榜" : "站点排行榜");
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), dtoList, fileName + orderBy + limitCnt,
                ShopRankDto.class, headers, true);

    }
}
