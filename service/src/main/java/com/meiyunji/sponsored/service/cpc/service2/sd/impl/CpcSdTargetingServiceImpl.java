package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.sd.constant.TacticEnum;
import com.amazon.advertising.sd.constant.TargetingTypeEnum;
import com.amazon.advertising.sd.mode.*;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargetingSd;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.SDbidOptimizationEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/6
 */
@Service
@Slf4j
public class CpcSdTargetingServiceImpl implements ICpcSdTargetingService {

    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Autowired
    private CpcSdTargetingApiService cpcSdTargetingApiService;
    @Autowired
    private CpcSdRecommendApiService cpcSdRecommendApiService;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAdManageOperationLogService adOperationLogService;
    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;

    private final String bidOverstep = "Bid must be less than half the value of your budget";
    private static final String MIN_PRICE = "0";
    private static final String MAX_PRICE = "2147483647";

    @Override
    public Result pageList(TargetingPageParam param) {
        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(param.getPuid(), param.getShopId(), param.getGroupId());
        if (amazonSdAdGroup == null) {
            return ResultUtil.returnErr("对象不存在");
        }

        Page<TargetingPageVo> voPage = new Page<>();

        // 是否需要按广告表现指标排序, 需要的要取满足过滤条件的所有活动，排序后在用程序分页；
        // 不需要的直接走数据库分页
        boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
        List<AmazonSdAdTargeting> poList;
        if (isSorted) {
            poList = amazonSdAdTargetingDao.listByCondition(param.getPuid(), param);

            voPage.setPageNo(param.getPageNo());
            voPage.setPageSize(param.getPageSize());
        } else {
            Page<AmazonSdAdTargeting> page = amazonSdAdTargetingDao.pageList(param.getPuid(), param);
            BeanUtils.copyProperties(page, voPage);
            poList = page.getRows();
        }

        if (CollectionUtils.isNotEmpty(poList)) {
            // 按活动分组获取活动的汇总数据
            List<AmazonAdSdTargetingReport> reports = amazonAdSdTargetingReportDao.listSumReports(param.getPuid(), param.getShopId(), param.getStartDate(), param.getEndDate(),
                    poList.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));

            Map<String, ReportBase> targetingReportMap = reports.stream()
                    .collect(Collectors.toMap(AmazonAdSdTargetingReport::getTargetId, AmazonAdSdTargetingReport::getReportBase));

            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            List<TargetingPageVo> voList = new ArrayList<>(poList.size());
            voPage.setRows(voList);
            TargetingPageVo vo;
            for (AmazonSdAdTargeting amazonAdTargeting : poList) {
                vo = new TargetingPageVo();
                voList.add(vo);
                vo.setId(Long.valueOf(amazonAdTargeting.getId()));
                vo.setShopId(amazonAdTargeting.getShopId());
                vo.setTargetId(amazonAdTargeting.getTargetId());
                vo.setState(amazonAdTargeting.getState());
                if (amazonAdTargeting.getSuggested() != null) {
                    vo.setSuggestBid(DataFormatUtil.scale(amazonAdTargeting.getSuggested(), 2));
                }
                if (amazonAdTargeting.getRangeStart() != null) {
                    vo.setRangeStart(DataFormatUtil.scale(amazonAdTargeting.getRangeStart(), 2));
                }
                if (amazonAdTargeting.getRangeEnd() != null) {
                    vo.setRangeEnd(DataFormatUtil.scale(amazonAdTargeting.getRangeEnd(), 2));
                }

                if (amazonAdTargeting.getBid() != null) {
                    vo.setBid(amazonAdTargeting.getBid().toString());
                } else {
                    if (amazonSdAdGroup.getDefaultBid() != null) {
                        vo.setBid(amazonSdAdGroup.getDefaultBid().toString());
                    }
                }

                vo.setType(amazonAdTargeting.getType());
                if (SdTargetTypeEnum.category.getValue().equals(amazonAdTargeting.getType())) {
                    vo.setCategory(amazonAdTargeting.getTargetText());
                } else if (SdTargetTypeEnum.asin.getValue().equals(amazonAdTargeting.getType())) {
                    vo.setAsin(amazonAdTargeting.getTargetText());
                    vo.setTitle(amazonAdTargeting.getTitle());
                    vo.setImgUrl(amazonAdTargeting.getImgUrl());
                } else if (SdTargetTypeEnum.exactProduct.getValue().equals(amazonAdTargeting.getType())) {
                    vo.setTargetText(amazonAdTargeting.getTargetText());
                } else if (SdTargetTypeEnum.similarProduct.getValue().equals(amazonAdTargeting.getType())) {
                    vo.setTargetText(amazonAdTargeting.getTargetText());
                }

                // 填充报告数据
                cpcCommService.fillReportDataIntoPageVo(vo, targetingReportMap.get(amazonAdTargeting.getTargetId()), shopSaleDto);
            }

            // 需要程序排序的
            if (isSorted) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                PageUtil.getPage(voPage, voList);
            }
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result showTargetPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getTargetId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonSdAdTargeting amazonAdTargeting = amazonSdAdTargetingDao.getbyTargetId(puid, param.getShopId(), param.getTargetId());
        if (amazonAdTargeting == null) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdTargeting.getShopId());
        adPerformanceVo.setTargetId(amazonAdTargeting.getTargetId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdSdTargetingReport> reports = amazonAdSdTargetingReportDao.listReports(puid, param.getShopId(), param.getStartDate(),
                param.getEndDate(), param.getTargetId());
        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e.getReportBase(), shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public Result createTargeting(AddSdTargetingVo addTargetingVo) {
        // 校验参数
        String msg = checkAddTargetingVo(addTargetingVo.getTargetings());
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error(msg);
        }

        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(addTargetingVo.getPuid(), addTargetingVo.getShopId(), addTargetingVo.getGroupId());
        if (amazonSdAdGroup == null) {
            return ResultUtil.error("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(addTargetingVo.getShopId(), addTargetingVo.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(addTargetingVo.getPuid(), addTargetingVo.getShopId());
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<AmazonSdAdTargeting> amazonAdTargetings = convertAddTargetingVoToPO(addTargetingVo.getUid(), amazonSdAdGroup, addTargetingVo.getTargetings());

        Result result = cpcSdTargetingApiService.create(shop, profile, amazonAdTargetings);
        /**
         * 逻辑：amazonAdKeywords有targetId的表示成功,再调用获取商品定位日志方法
         * start
         */
        List<AdManageOperationLog> targetLogs = Lists.newArrayListWithExpectedSize(2);
        for(AmazonSdAdTargeting target : amazonAdTargetings){
            AdManageOperationLog targetLog = adOperationLogService.getSdTargetsLog(null, target);
            targetLog.setIp(addTargetingVo.getLoginIp());
            if (StringUtils.isNotBlank(target.getTargetId())) {
                targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                if (StringUtils.isNotBlank(target.getErrMsg())) {
                    targetLog.setResultInfo(target.getErrMsg());
                } else {
                    targetLog.setResultInfo(result.getMsg());
                }

            }
            targetLogs.add(targetLog);
        }
        if (!result.success()) {
            adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            return result;
        }

        List<AmazonSdAdTargeting> succList = amazonAdTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了: 重复添加同一个投放，接口不会报错，会返回其对应的targetId
            List<String> existInDB = amazonSdAdTargetingDao.listByTargetId(addTargetingVo.getPuid(), addTargetingVo.getShopId(), succList.stream()
                    .map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSdAdTargetingDao.batchAdd(addTargetingVo.getPuid(), succList);
            } catch (Exception e) {
                log.error("createSdTargeting:", e);
                return ResultUtil.returnErr("系统异常");
            }

            String campaignId = amazonSdAdGroup.getCampaignId();
            //创建成功, 需要在同步 获取投放状态
            if (StringUtils.isNotBlank(campaignId)) {
                cpcSdTargetingApiService.syncTargetings(shop, campaignId);
            }
            //写入doris
            saveDoris(addTargetingVo.getPuid(), addTargetingVo.getShopId(), succList.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));
        }

        // 没有失败的就代表全部成功了
        List<AmazonSdAdTargeting> failList = amazonAdTargetings.stream().filter(e -> StringUtils.isBlank(e.getTargetId())).collect(Collectors.toList());
        if (failList.size() == 0) {
            adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            return ResultUtil.success();
        }

        StringBuilder errMsg = new StringBuilder("创建失败的投放原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg().equalsIgnoreCase(bidOverstep) ? bidOverstep : e.getErrMsg());
            }
        });
        adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
        return ResultUtil.returnErr(errMsg.toString());
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> createTargetingNew(AddSdTargetingVo addTargetingVo, AmazonSdAdGroup amazonSdAdGroup,
                                     ShopAuth shop, AmazonAdProfile profile, Integer uid, String loginIp) {
        // 校验参数
        String msg = checkAddTargetingVoNew(addTargetingVo.getTargetings());
        if (StringUtils.isNotBlank(msg)) {
            log.error("check param error, msg:{}", msg);
            throw new ServiceException(msg);
        }
        AmazonSdAdGroup groupInfo;
        ShopAuth shopInfo;
        AmazonAdProfile profileInfo;
        if (Objects.isNull(amazonSdAdGroup)) {
            groupInfo = amazonSdAdGroupDao.getByGroupId(addTargetingVo.getPuid(), addTargetingVo.getShopId(), addTargetingVo.getGroupId());
            if (groupInfo == null) {
                log.error("group not exist, puid:{}, shopId:{}, groupId:{}", addTargetingVo.getPuid(), addTargetingVo.getShopId(), addTargetingVo.getGroupId());
                throw new ServiceException("group not exist");
            }
        } else {
            groupInfo = amazonSdAdGroup;
        }
        if (Objects.isNull(shop)) {
            shopInfo = shopAuthDao.getScAndVcByIdAndPuid(addTargetingVo.getShopId(), addTargetingVo.getPuid());
            if (shopInfo == null) {
                log.error("shop auth error, puid:{}, shopId:{}", addTargetingVo.getPuid(), addTargetingVo.getShopId());
                throw new ServiceException("没有CPC授权");
            }
        } else {
            shopInfo = shop;
        }

        if (Objects.isNull(profile)) {
            profileInfo = amazonAdProfileDao.getProfile(addTargetingVo.getPuid(), addTargetingVo.getShopId());
            if (profileInfo == null) {
                log.error("profile auth error, puid:{}, shopId:{}", addTargetingVo.getPuid(), addTargetingVo.getShopId());
                throw new ServiceException("没有站点对应的配置信息");
            }
        } else {
            profileInfo = profile;
        }

        List<AmazonSdAdTargeting> amazonAdTargetings = convertAddTargetingVoToPONew(addTargetingVo.getUid(), shopInfo,
                groupInfo.getCampaignId(), groupInfo.getAdGroupId(), groupInfo.getProfileId(), addTargetingVo.getTargetings());
        Result result = cpcSdTargetingApiService.create(shopInfo, profileInfo, amazonAdTargetings);

        List<AmazonSdAdTargeting> succList = amazonAdTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
        List<String> existInDB = new ArrayList<>();
        if (succList.size() > 0) {
            // 有可能已经添加过了: 重复添加同一个投放，接口不会报错，会返回其对应的targetId
            existInDB.addAll(amazonSdAdTargetingDao.listByTargetId(shopInfo.getPuid(), shopInfo.getId(), succList.stream()
                    .map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }

            // 入库
            try {
                amazonSdAdTargetingDao.batchAdd(shopInfo.getPuid(), succList);
            } catch (Exception e) {
                log.error("createSdTargeting:", e);
                throw new ServiceException(e.getMessage());
            }

            //创建成功, 需要在同步 获取投放状态
            if (StringUtils.isNotBlank(groupInfo.getAdGroupId())) {
                try {
//                    ThreadPoolUtil.getSDTargetCreateSyncPool().execute(() -> {
                        cpcSdTargetingApiService.syncTargetingsByGroupId(shopInfo, groupInfo.getAdGroupId());
//                    });
                } catch (Exception e) {
                    log.info("SD创建投放成功后，根据广告组id同步投放状态异常", e);
                    throw new ServiceException(e.getMessage());
                }
            }
            //写入doris
            saveDoris(shopInfo.getPuid(), shopInfo.getId(), succList.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));
        }
        /**
         * 逻辑：amazonAdKeywords有targetId的表示成功,再调用获取商品定位日志方法
         * start
         */
        List<AdManageOperationLog> targetLogs = Lists.newArrayListWithExpectedSize(2);
        for(AmazonSdAdTargeting target : amazonAdTargetings){
            AdManageOperationLog targetLog = adOperationLogService.getSdTargetsLog(null, target);
            targetLog.setIp(loginIp);
            if (existInDB.contains(target.getTargetId())) continue;
            if (StringUtils.isNotBlank(target.getTargetId())) {
                targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                if (StringUtils.isNotBlank(target.getErrMsg())) {
                    targetLog.setResultInfo(target.getErrMsg());
                } else {
                    targetLog.setResultInfo(result.getMsg());
                }

            }
            targetLogs.add(targetLog);
        }
        if (!result.success()) {
            adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
            targetingResult.setErrInfoList(Collections.singletonList(SBCommonErrorVo.getErrorVo("投放", String.valueOf(result.getMsg()))));
            return targetingResult;
        }

        NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
        targetingResult.setTargetingList(amazonAdTargetings.stream().map(s -> {
                    NewCreateTargetResultVo vo = new NewCreateTargetResultVo();
                    Optional.ofNullable(s.getTargetId()).ifPresent(vo::setTargetId);
                    Optional.ofNullable(s.getTargetText()).ifPresent(vo::setTargetText);
                    Optional.ofNullable(s.getType()).ifPresent(vo::setMatchType);
                    return vo;
                })
                .collect(Collectors.toList()));

        // 没有失败的就代表全部成功了
        List<AmazonSdAdTargeting> failList = amazonAdTargetings.stream().filter(e -> StringUtils.isBlank(e.getTargetId())).collect(Collectors.toList());
        if (failList.isEmpty()) {
            adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            return targetingResult;
        }
        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (org.apache.commons.lang.StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getTargetText(), e.getErrMsg()));
            }
        });
        targetingResult.setErrInfoList(errorList);

        //记录日志
        adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
        return targetingResult;
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> sdCreateTargetingNew(List<SdTargetingVo> targetingList, String campaignId,
                                                                         String adGroupId, ShopAuth shop,
                                                                         AmazonAdProfile profile, Integer uid,
                                                                         String loginId) {
        // 校验参数
        String msg = checkAddTargetingVoNew(targetingList);
        if (StringUtils.isNotBlank(msg)) {
            log.error("check param error, msg:{}", msg);
            throw new ServiceException(msg);
        }

        List<AmazonSdAdTargeting> amazonAdTargetings = convertAddTargetingVoToPONew(uid, shop, campaignId,
                adGroupId, profile.getProfileId(), targetingList);

        List<String> existInDB = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
            // 有可能已经添加过了: 重复添加同一个投放，接口不会报错，会返回其对应的targetId
            existInDB.addAll(amazonSdAdTargetingDao.listByTargetId(shop.getPuid(), shop.getId(), amazonAdTargetings.stream()
                    .map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList())).stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));
            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                amazonAdTargetings = amazonAdTargetings.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
            }
        }

        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            log.error("request target list is empty or exist already");
            NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
            targetingResult.setCampaignId(campaignId);
            targetingResult.setAdGroupId(adGroupId);
            return targetingResult;
        }

        Result result = cpcSdTargetingApiService.create(shop, profile, amazonAdTargetings);

        List<AmazonSdAdTargeting> succList = amazonAdTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
        if (succList.size() > 0) {
            // 入库
            try {
                amazonSdAdTargetingDao.batchAdd(shop.getPuid(), succList);
            } catch (Exception e) {
                log.error("createSdTargeting:", e);
                throw new ServiceException(e.getMessage());
            }

            //创建成功, 需要在同步 获取投放状态
            if (StringUtils.isNotBlank(campaignId)) {
                try {
                    ThreadPoolUtil.getSDTargetCreateSyncPool().execute(() -> {
                        cpcSdTargetingApiService.syncTargetings(shop, campaignId);
                    });
                } catch (Exception e) {
                    log.info("SD创建投放成功后，同步投放状态异常", e);
                    throw new ServiceException(e.getMessage());
                }
            }
            //写入doris
            saveDoris(shop.getPuid(), shop.getId(), succList.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));
        }
        /**
         * 逻辑：amazonAdKeywords有targetId的表示成功,再调用获取商品定位日志方法
         * start
         */
        List<AdManageOperationLog> targetLogs = Lists.newArrayListWithExpectedSize(2);
        for(AmazonSdAdTargeting target : amazonAdTargetings){
            AdManageOperationLog targetLog = adOperationLogService.getSdTargetsLog(null, target);
            targetLog.setIp(loginId);
            if (existInDB.contains(target.getTargetId())) continue;
            if (StringUtils.isNotBlank(target.getTargetId())) {
                targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                if (StringUtils.isNotBlank(target.getErrMsg())) {
                    targetLog.setResultInfo(target.getErrMsg());
                } else {
                    targetLog.setResultInfo(result.getMsg());
                }

            }
            targetLogs.add(targetLog);
        }
        if (!result.success()) {
            adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
            targetingResult.setCampaignId(campaignId);
            targetingResult.setAdGroupId(adGroupId);
            targetingResult.setErrInfoList(Collections.singletonList(SBCommonErrorVo.getErrorVo("投放", String.valueOf(result.getMsg()))));
            return targetingResult;
        }

        NewCreateResultResultVo<SBCommonErrorVo> targetingResult = new NewCreateResultResultVo();
        targetingResult.setCampaignId(campaignId);
        targetingResult.setAdGroupId(adGroupId);
        targetingResult.setTargetingList(amazonAdTargetings.stream().map(s -> {
                    NewCreateTargetResultVo vo = new NewCreateTargetResultVo();
                    Optional.ofNullable(s.getTargetId()).ifPresent(vo::setTargetId);
                    Optional.ofNullable(s.getTargetText()).ifPresent(vo::setTargetText);
                    Optional.ofNullable(s.getType()).ifPresent(vo::setMatchType);
                    return vo;
                })
                .collect(Collectors.toList()));

        // 没有失败的就代表全部成功了
        List<AmazonSdAdTargeting> failList = amazonAdTargetings.stream().filter(e -> StringUtils.isBlank(e.getTargetId())).collect(Collectors.toList());
        if (failList.isEmpty()) {
            adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
            return targetingResult;
        }
        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (org.apache.commons.lang.StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getTargetText(), e.getErrMsg()));
            }
        });
        targetingResult.setErrInfoList(errorList);

        //记录日志
        adOperationLogService.batchLogsMergeByAdGroup(targetLogs);
        return targetingResult;
    }

    @Override
    public Result update(UpdateSdTargetingVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonSdAdTargeting oldAmazonSdAdTargeting = amazonSdAdTargetingDao.getbyTargetId(puid, shopId, vo.getTargetId());
        if (oldAmazonSdAdTargeting == null) {
            return ResultUtil.returnErr("没有广告产品信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, oldAmazonSdAdTargeting.getAdGroupId());
        AmazonSdAdTargeting amazonSdAdTargeting = new AmazonSdAdTargeting();
        BeanUtils.copyProperties(oldAmazonSdAdTargeting, amazonSdAdTargeting);

        if (StringUtils.isNotBlank(vo.getState())) {
            amazonSdAdTargeting.setState(vo.getState());
        } else {
            amazonSdAdTargeting.setState(null);
        }
        if (StringUtils.isNotBlank(vo.getBid())) {
            amazonSdAdTargeting.setBid(new BigDecimal(vo.getBid()));
        } else {
            amazonSdAdTargeting.setBid(null);
        }
        if (Objects.nonNull(amazonSdAdGroup)) {
            amazonSdAdTargeting.setCampaignId(amazonSdAdGroup.getCampaignId());
            amazonSdAdTargeting.setTactic(amazonSdAdGroup.getTactic());
        }

        Result result = cpcSdTargetingApiService.update(shop, profile, Lists.newArrayList(amazonSdAdTargeting));

        amazonSdAdTargeting.setUpdateId(vo.getUid());
        List<AdManageOperationLog> targetsLogs = Lists.newArrayListWithExpectedSize(1);

        //只有一个投放
        //result失败
        if (result.error() || StringUtils.isNotBlank(result.getMsg())) {
            AdManageOperationLog errorTargetLog = adOperationLogService.getSdTargetsLog(oldAmazonSdAdTargeting, amazonSdAdTargeting);
            errorTargetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            errorTargetLog.setResultInfo(result.getMsg());
            errorTargetLog.setIp(vo.getIp());
            targetsLogs.add(errorTargetLog);
            adOperationLogService.batchLogsMergeByAdGroup(targetsLogs);
            return result;
        }

        //投放内部失败，此时result是success
        if (!amazonSdAdTargeting.getApiSucc()) {
            if (bidOverstep.equalsIgnoreCase(amazonSdAdTargeting.getErrMsg())) {
                amazonSdAdTargeting.setErrMsg("展示型广告的竞价必须少于预算金额的一半");
            }
            AdManageOperationLog errorTargetLog = adOperationLogService.getSdTargetsLog(oldAmazonSdAdTargeting, amazonSdAdTargeting);
            errorTargetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            errorTargetLog.setResultInfo(amazonSdAdTargeting.getErrMsg());
            errorTargetLog.setIp(vo.getIp());
            targetsLogs.add(errorTargetLog);
            adOperationLogService.batchLogsMergeByAdGroup(targetsLogs);
            return ResultUtil.error(amazonSdAdTargeting.getErrMsg());
        }

        //成功
        AdManageOperationLog successTargetLog = adOperationLogService.getSdTargetsLog(oldAmazonSdAdTargeting, amazonSdAdTargeting);
        successTargetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        successTargetLog.setIp(vo.getIp());
        targetsLogs.add(successTargetLog);

        // 入库
        try {
            amazonSdAdTargetingDao.updateByIdAndPuid(puid, amazonSdAdTargeting);
            //写入doris
            saveDoris(puid, shopId, Collections.singletonList(amazonSdAdTargeting.getTargetId()));
            adOperationLogService.batchLogsMergeByAdGroup(targetsLogs);
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("updateSdTargeting:", e);
        }
        //如果上边异常，也记录下成功的操作日志
        adOperationLogService.batchLogsMergeByAdGroup(targetsLogs);
        return ResultUtil.returnErr("系统异常");
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String targetId, String loginIp) {
        AmazonSdAdTargeting amazonSdAdTargeting = amazonSdAdTargetingDao.getbyTargetId(puid, shopId, targetId);
        if (amazonSdAdTargeting == null) {
            return ResultUtil.returnErr("没有投放定位信息");
        }

        AmazonSdAdTargeting targeting = new AmazonSdAdTargeting();
        BeanUtils.copyProperties(amazonSdAdTargeting, targeting);

        targeting.setState(CpcStatusEnum.archived.name());
        targeting.setUpdateId(uid);
        Result result = cpcSdTargetingApiService.archive(targeting);

        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, targeting.getAdGroupId());
        if (Objects.nonNull(amazonSdAdGroup)) {
            targeting.setCampaignId(amazonSdAdGroup.getCampaignId());
        }
        List<AdManageOperationLog> targetsLogs = Lists.newArrayListWithExpectedSize(2);
        AdManageOperationLog targetsLog = adOperationLogService.getSdTargetsLog(amazonSdAdTargeting, targeting);
        if (result.success()) {
            targeting.setUpdateId(uid);
            targeting.setState(CpcStatusEnum.archived.name());
            amazonSdAdTargetingDao.updateByIdAndPuid(puid, targeting);
            //写入doris
            saveDoris(Collections.singletonList(targeting), false, true);
            targetsLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            targetsLog.setIp(loginIp);
        }
        if (result.error()){
            targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            targetsLog.setResultInfo(result.getMsg());
            targetsLog.setIp(loginIp);
        }
        targetsLogs.add(targetsLog);
        adOperationLogService.printAdOperationLog(targetsLogs);
        return result;
    }

    @Override
    public Result suggestCategory(int puid, Integer shopId, String groupId) {
        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, groupId);
        if (amazonSdAdGroup == null) {
            return ResultUtil.error("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        List<SuggestCategoryVo> vos = new ArrayList<>();

        // 查出状态有效的asin
        List<String> asins = amazonSdAdProductDao.listValidAsin(puid, shopId, groupId)
                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(asins)) {
            Result<SDTargetingRecommendationsV31> result = cpcSdRecommendApiService.getRecommendations(shop, amazonSdAdGroup.getTactic(),
                    asins, Lists.newArrayList(TargetingTypeEnum.CATEGORY));
            if (result.success() && result.getData() != null) {
                SDTargetingRecommendationsV31 data = result.getData();
                List<TargetingRecommCategory> categories = data.getCategories();
                if (CollectionUtils.isNotEmpty(categories)) {
                    SuggestCategoryVo vo;
                    for (TargetingRecommCategory c : categories) {
                        vo = new SuggestCategoryVo();
                        vo.setId(c.getCategory());
                        vo.setName(c.getName());
                        vo.setPath(Joiner.on("/").join(c.getPath()));
                        vos.add(vo);
                    }
                }
            }
        }

        return ResultUtil.returnSucc(vos);
    }

    @Override
    public Result suggestCategoryNew(int puid, ShopAuth shop, List<String> asinList, String predicate) {
        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.error("请求建议分类对应asin为空");
        }

        if (Objects.isNull(shop)) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        List<SuggestCategoryVo> vos = new ArrayList<>();

        // 查出状态有效的asin
//        List<String> asins = amazonSdAdProductDao.listValidAsinByAsin(puid, shop.getId(), asinList)
//                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(asinList)) {
            Result<SDTargetingRecommendationsV35> result = cpcSdRecommendApiService.getRecommendationsNew(shop, SDTaticEnum.T00030.getCode(),
                    asinList, Lists.newArrayList(TargetingTypeEnum.CATEGORY), "");

            if (result.success() && result.getData() != null) {
                SDTargetingRecommendationsV35 data = result.getData();
                List<SDCategoryRecommendationV33> categories = data.getCategories();
                if (CollectionUtils.isNotEmpty(categories)) {
                    SuggestCategoryVo vo;
                    for (SDCategoryRecommendationV33 c : categories) {
                        vo = new SuggestCategoryVo();
                        Optional.ofNullable(c.getCategory()).map(String::valueOf).ifPresent(vo::setId);
                        vo.setName(c.getName());
                        vo.setPath(Joiner.on("/").join(c.getPath()));
                        vos.add(vo);
                    }
                }
            }
        }

        return ResultUtil.returnSucc(vos);
    }

    @Override
    public Result suggestProductNew(int puid, ShopAuth shop, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.error("请求建议产品对应asin为空");
        }

        if (Objects.isNull(shop)) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        List<SuggestProductVo> vos = new ArrayList<>();

        // 查出状态有效的asin
//        List<String> asins = amazonSdAdProductDao.listValidAsinByAsin(puid, shop.getId(), asinList)
//                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(asinList)) {
            Result<SDTargetingRecommendationsV35> result = cpcSdRecommendApiService.getRecommendationsNew(shop, SDTaticEnum.T00020.getCode(),
                    asinList, Lists.newArrayList(TargetingTypeEnum.PRODUCT), "");

            if (result.success() && result.getData() != null) {
                SDTargetingRecommendationsV35 data = result.getData();
                List<SDProductRecommendationV32> products = data.getProducts();
                if (CollectionUtils.isNotEmpty(products)) {
                    SuggestProductVo vo;
                    for (SDProductRecommendationV32 p : products) {
                        vo = new SuggestProductVo();
                        vo.setAsin(p.getAsin());
                        Optional.ofNullable(p.getAdvertisedAsins()).filter(CollectionUtils::isNotEmpty).ifPresent(vo::setAdvertisedAsins);
                        vos.add(vo);
                    }
                }
            }
        }

        return ResultUtil.returnSucc(vos);
    }

    @Override
    public Result suggestAudiences(int puid, ShopAuth shop, List<String> asinList) {
        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.error("请求建议产品对应asin为空");
        }

        if (Objects.isNull(shop)) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        List<SuggestAudiencesVo> vos = new ArrayList<>();

        // 查出状态有效的asin
//        List<String> asins = amazonSdAdProductDao.listValidAsinByAsin(puid, shop.getId(), asinList)
//                .stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(asinList)) {
            Result<SDTargetingRecommendationsV35> result = cpcSdRecommendApiService.getRecommendationsNew(shop, SDTaticEnum.T00030.getCode(),
                    asinList, Lists.newArrayList(TargetingTypeEnum.AUDIENCE), "");

            if (result.success() && result.getData() != null) {
                SDTargetingRecommendationsV35 data = result.getData();
                List<SDAudienceCategoryRecommendations> audiences = data.getAudiences();
                if (CollectionUtils.isNotEmpty(audiences)) {
                    SuggestAudiencesVo vo;
                    for (SDAudienceCategoryRecommendations list : audiences) {
                        vo = new SuggestAudiencesVo();
                        Optional.ofNullable(list.getAudiences()).filter(CollectionUtils::isNotEmpty).map(audienceInfo ->
                                audienceInfo.stream().map(a -> SuggestAudienceInfo.builder()
                                .audienceId(a.getAudience())
                                .name(a.getName())
                                .rank(a.getRank())
                                .build()).collect(Collectors.toList())).ifPresent(vo::setAudiences);
                        Optional.ofNullable(list.getCategory()).filter(StringUtils::isNotEmpty).ifPresent(vo::setCategory);
                        vos.add(vo);
                    }
                }
            }
        }

        return ResultUtil.returnSucc(vos);
    }

    @Override
    public Result getSuggestedBid(int puid, Integer shopId, List<String> targetIds) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<AmazonSdAdTargeting> targetingList = amazonSdAdTargetingDao.listByTargetId(puid, shopId, targetIds);
        if (CollectionUtils.isEmpty(targetingList)) {
            return ResultUtil.returnErr("没有投放定位信息");
        }
        List<String> groupIds = targetingList.stream().map(AmazonSdAdTargeting::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonSdAdGroup> byGroupIds = amazonSdAdGroupDao.getByGroupIds(puid, shopId, groupIds);
        if (CollectionUtils.isEmpty(byGroupIds)) {
            return ResultUtil.returnErr("没有找到广告组信息");
        }
        Map<String, List<AmazonSdAdGroup>> groupMap = byGroupIds.stream().collect(Collectors.groupingBy(e -> StringUtils.isBlank(e.getBidOptimization()) ? "" : e.getBidOptimization()));

        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        //新版sd获取建议竞价要根据不同costType 和bidOptimization 分别进行请求，
        //bidOptimization 
        //clicks	cpc	T00020 T00030	Optimize for page visits
        //conversions	cpc	T00020 T00030	Optimize for conversion
        //reach	vcpm	T00020 T00030	Optimize for viewable impressions. We recommend starting with $5 USD bids for each target to begin testing with this awareness strategy. $1 is the minimum bid for vCPM.
        Map<String, List<AmazonSdAdTargeting>> targetingMap = targetingList.stream().collect(Collectors.groupingBy(AmazonSdAdTargeting::getAdGroupId));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SdSuggestedTargetVo>>> completableFutures = new ArrayList<>();
        for (Map.Entry<String, List<AmazonSdAdGroup>> entry : groupMap.entrySet()) {
            String bidOptimization = entry.getKey();
            if (StringUtils.isBlank(bidOptimization)) {
                bidOptimization = Constants.SD_BID_OPTIMIZATION_CLICKS;
            }
            List<AmazonSdAdGroup> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                for (AmazonSdAdGroup group : value) {
                    List<AmazonSdAdTargeting> targetings = targetingMap.get(group.getAdGroupId());
                    if (CollectionUtils.isEmpty(targetings)) continue;
                    final String finalBidOptimization = bidOptimization;
                    completableFutures.add(CompletableFuture.supplyAsync(() -> handleSuggestedBid(puid, shopId, shop, group, targetings, Constants.SD_BID_OPTIMIZATION_REACH.equalsIgnoreCase(finalBidOptimization) ? "vcpm" : "cpc", finalBidOptimization), executor));
                }
            }
        }

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedTargetVos.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("sd targeting getSuggestedBid error, puid:{}, shopId:{}", puid, shop.getId(), e);
            }
        });

        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    @Override
    public Result<List<SdSuggestedTargetVo>> getSuggestedBidNew(List<String> asins, ShopAuth shop,
                                                                List<SdTargetingVo> targetVo, AmazonAdProfile profile,
                                                                String bidOptimization, String creativeType) {
        if (CollectionUtils.isEmpty(asins)) {
            return ResultUtil.error("获取竞价对应的广告产品不能为空");
        }
        if (Objects.isNull(shop)) {
            return ResultUtil.error("店铺不存在");
        }
        // 校验参数
        String msg = checkTargetingBidVo(targetVo);
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error(msg);
        }
        SDbidOptimizationEnum bidOptEnum = SDbidOptimizationEnum.getSDbidOptimizationEnumByCode(bidOptimization);
        if (Objects.isNull(bidOptEnum)) {
            return ResultUtil.error("对应的竞价优化不存在");
        }
        //根据投放内容组装调用亚马逊接口expression表达式对应的PO
        List<AmazonSdAdTargeting> amazonAdTargetList = convertBinRecommendTargetingVoToPONew(shop, targetVo);
        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        Optional.of(handleSuggestedBidNew(shop, profile, asins, amazonAdTargetList, bidOptEnum.getCostType(), bidOptEnum.getVal(), creativeType))
                .filter(CollectionUtils::isNotEmpty).ifPresent(suggestedTargetVos::addAll);

        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    @Override
    public Result batchGetSuggestedBid(int puid, Integer shopId, List<Integer> sourceShopIds, List<TargetSuggestedBidDetail> details, String targetingType) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<String> targetIds = details.stream().map(TargetSuggestedBidDetail::getTargetId).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        Map<String, CommonAmazonAdTargeting> targetingMap = new HashMap<>();
        // 搜索词与词库不传targetId
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(targetIds)) {
            List<CommonAmazonAdTargeting> targetingList = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
            if (org.apache.commons.collections.CollectionUtils.isEmpty(targetingList)) {
                return ResultUtil.returnErr("对象不存在");
            }
            targetingMap.putAll(targetingList.stream().collect(
                    Collectors.toMap(CommonAmazonAdTargeting::getTargetId, Function.identity(), (newVal, oldVal) -> newVal)));
        }

        List<String> groupIds = details.stream().map(TargetSuggestedBidDetail::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonSdAdGroup> byGroupIds = amazonSdAdGroupDao.getByGroupIds(puid, shopId, groupIds);
        if (CollectionUtils.isEmpty(byGroupIds)) {
            return ResultUtil.returnErr("没有找到广告组信息");
        }
        Map<String, List<AmazonSdAdGroup>> groupMap = byGroupIds.stream().collect(Collectors.groupingBy(e -> e.getShopId() + "#" + (StringUtils.isBlank(e.getBidOptimization()) ? "" : e.getBidOptimization())));

        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        //新版sd获取建议竞价要根据不同costType 和bidOptimization 分别进行请求，
        //bidOptimization
        //clicks	cpc	T00020 T00030	Optimize for page visits
        //conversions	cpc	T00020 T00030	Optimize for conversion
        //reach	vcpm	T00020 T00030	Optimize for viewable impressions. We recommend starting with $5 USD bids for each target to begin testing with this awareness strategy. $1 is the minimum bid for vCPM.
        Map<String, List<TargetSuggestedBidDetail>> detailMap = details.stream().collect(Collectors.groupingBy(TargetSuggestedBidDetail::getAdGroupId));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SdSuggestedTargetVo>>> completableFutures = new ArrayList<>();
        for (Map.Entry<String, List<AmazonSdAdGroup>> entry : groupMap.entrySet()) {
            String[] key = entry.getKey().split("#");
            Integer groupShopId = Integer.valueOf(key[0]);
            String bidOptimization = null;
            if (key.length > 1) {
                bidOptimization = key[1];
            }
            if (StringUtils.isBlank(bidOptimization)) {
                bidOptimization = Constants.SD_BID_OPTIMIZATION_CLICKS;
            }
            List<AmazonSdAdGroup> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                for (AmazonSdAdGroup group : value) {
                    List<TargetSuggestedBidDetail> targetings = detailMap.get(group.getAdGroupId());
                    if (CollectionUtils.isEmpty(targetings)) continue;
                    final String finalBidOptimization = bidOptimization;
                    String costType = Constants.SD_BID_OPTIMIZATION_REACH.equalsIgnoreCase(finalBidOptimization) ? "vcpm" : "cpc";
                    completableFutures.add(CompletableFuture.supplyAsync(() ->
                            batchHandleSuggestedBid(puid, groupShopId, shop, group, targetings, targetingMap, costType, finalBidOptimization, targetingType), executor));
                }
            }
        }

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedTargetVos.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("sd targeting getSuggestedBid error, puid:{}, shopId:{}", puid, shop.getId(), e);
            }
        });

        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    @Override
    public Result<List<SdSuggestedTargetVo>> getSuggestedBidMultiShop(int puid, List<TargetSuggestBidBatchQo> qoList) {
        List<Integer> shopIdList = qoList.stream().map(TargetSuggestBidBatchQo::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, shopIdList);
        if (shopIdList.size() != shopAuthList.size()) {
            return ResultUtil.error("店铺不存在，请刷新页面重试");
        }
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        List<Integer> shopIds = new ArrayList<>(shopAuthMap.keySet());

        List<AmazonSdAdTargeting> targetingList = amazonSdAdTargetingDao.getByTargetSuggestBidBatchQo(puid, qoList);
        if (CollectionUtils.isEmpty(targetingList)) {
            return ResultUtil.returnErr("没有投放定位信息");
        }
        List<String> groupIds = targetingList.stream().map(AmazonSdAdTargeting::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonSdAdGroup> byGroupIds = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(puid, shopIds, groupIds);
        if (CollectionUtils.isEmpty(byGroupIds)) {
            return ResultUtil.returnErr("没有找到广告组信息");
        }
        Map<String, List<AmazonSdAdGroup>> groupMap = byGroupIds.stream().collect(Collectors.groupingBy(e -> StringUtils.isBlank(e.getBidOptimization()) ? "" : e.getBidOptimization()));

        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        //新版sd获取建议竞价要根据不同costType 和bidOptimization 分别进行请求，
        //bidOptimization
        //clicks	cpc	T00020 T00030	Optimize for page visits
        //conversions	cpc	T00020 T00030	Optimize for conversion
        //reach	vcpm	T00020 T00030	Optimize for viewable impressions. We recommend starting with $5 USD bids for each target to begin testing with this awareness strategy. $1 is the minimum bid for vCPM.
        Map<String, List<AmazonSdAdTargeting>> targetingMap = targetingList.stream().collect(Collectors.groupingBy(AmazonSdAdTargeting::getAdGroupId));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SdSuggestedTargetVo>>> completableFutures = new ArrayList<>();
        for (Map.Entry<String, List<AmazonSdAdGroup>> entry : groupMap.entrySet()) {
            String bidOptimization = entry.getKey();
            if (StringUtils.isBlank(bidOptimization)) {
                bidOptimization = Constants.SD_BID_OPTIMIZATION_CLICKS;
            }
            List<AmazonSdAdGroup> value = entry.getValue();
            if (CollectionUtils.isNotEmpty(value)) {
                for (AmazonSdAdGroup group : value) {
                    List<AmazonSdAdTargeting> targetings = targetingMap.get(group.getAdGroupId());
                    if (CollectionUtils.isEmpty(targetings)) continue;
                    final String finalBidOptimization = bidOptimization;
                    completableFutures.add(CompletableFuture.supplyAsync(() -> handleSuggestedBid(puid, group.getShopId(), shopAuthMap.get(group.getShopId()), group, targetings, Constants.SD_BID_OPTIMIZATION_REACH.equalsIgnoreCase(finalBidOptimization) ? "vcpm" : "cpc", finalBidOptimization), executor));
                }
            }
        }

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedTargetVos.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info(String.format("sd targeting getSuggestedBid error, puid:%s", puid), e);
            }
        });

        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    private List<SdSuggestedTargetVo> handleSuggestedBid(Integer puid, Integer shopId, ShopAuth shop, AmazonSdAdGroup group, List<AmazonSdAdTargeting> targetingList, String costType, String bidOptimization) {
        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        // 过滤出有效的, 没有有效的也不返回报错
        targetingList = targetingList.stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(targetingList)) {
            Map<String, AmazonSdAdTargeting> targetingMap = targetingList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity()));

            List<String> asins = amazonSdAdProductDao.listValidAsin(puid, shopId, group.getAdGroupId());
            if (CollectionUtils.isNotEmpty(asins)) {
                for (AmazonSdAdTargeting e : targetingList) {
                    SdSuggestedTargetVo suggestedBidVo = new SdSuggestedTargetVo();
                    suggestedBidVo.setTargetId(e.getTargetId());
                    suggestedBidVo.setExpression(JSONUtil.jsonToArray(e.getExpression(), ExpressionNested.class));
                    suggestedBidVo.setSuggested(Optional.ofNullable(e.getSuggested()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                    suggestedBidVo.setRangeStart(Optional.ofNullable(e.getRangeStart()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                    suggestedBidVo.setRangeEnd(Optional.ofNullable(e.getRangeEnd()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                    suggestedTargetVos.add(suggestedBidVo);
                }

                Lists.partition(suggestedTargetVos, 100).forEach(subList -> {
                    Result<?> result = cpcSdRecommendApiService.getBidRecommendations(shop, asins, subList, costType, bidOptimization);
                    if (!result.success()) return;
                    subList.forEach((suggestedBidVo) -> {
                        AmazonSdAdTargeting amazonAdTargeting = targetingMap.get(suggestedBidVo.getTargetId());
                        if (amazonAdTargeting == null) return;
                        amazonAdTargeting.setSuggested(Optional.ofNullable(suggestedBidVo.getSuggested()).map(BigDecimal::new).orElse(amazonAdTargeting.getSuggested()));
                        amazonAdTargeting.setRangeStart(Optional.ofNullable(suggestedBidVo.getRangeStart()).map(BigDecimal::new).orElse(amazonAdTargeting.getRangeStart()));
                        amazonAdTargeting.setRangeEnd(Optional.ofNullable(suggestedBidVo.getRangeEnd()).map(BigDecimal::new).orElse(amazonAdTargeting.getRangeEnd()));
                    });
                });

                // 批量更新建议竞价
                amazonSdAdTargetingDao.batchUpdateSuggestValue(puid, Lists.newArrayList(targetingMap.values()));
            }
        }
        return suggestedTargetVos;
    }

    private List<SdSuggestedTargetVo> handleSuggestedBidNew(ShopAuth shop, AmazonAdProfile amazonAdProfile,
                                                            List<String> asins, List<AmazonSdAdTargeting> targetingList,
                                                            String costType, String bidOptimization,
                                                            String creativeType) {
        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        // 过滤出有效的, 没有有效的也不返回报错
        if (CollectionUtils.isNotEmpty(asins) && CollectionUtils.isNotEmpty(targetingList)) {

//            List<String> validAsinList = amazonSdAdProductDao.listValidAsinByAsin(shop.getPuid(), shop.getId(), asins);
            if (CollectionUtils.isNotEmpty(asins)) {
                Lists.partition(targetingList, 100).forEach(subList -> {
                    List<SdSuggestedTargetVo> result = cpcSdRecommendApiService.getBidRecommendationsNew(shop,
                            amazonAdProfile, asins, subList, costType, bidOptimization, creativeType);
                    if (CollectionUtils.isEmpty(result)) return;
                    suggestedTargetVos.addAll(result);
                });
            }
        }
        return suggestedTargetVos;
        }

    private List<SdSuggestedTargetVo> batchHandleSuggestedBid(Integer puid, Integer shopId, ShopAuth shop, AmazonSdAdGroup group, List<TargetSuggestedBidDetail> targetingList, Map<String, CommonAmazonAdTargeting> targetingMap, String costType, String bidOptimization, String targetingType) {
        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();
        List<String> asins = amazonSdAdProductDao.listValidAsin(puid, shopId, group.getAdGroupId());
        for (TargetSuggestedBidDetail e : targetingList) {
            SdSuggestedTargetVo suggestedBidVo = new SdSuggestedTargetVo();
            suggestedBidVo.setIndex(e.getIndex());
            if (StringUtils.isNotEmpty(e.getTargetId())) {
                String targetExpression = targetingMap.get(e.getTargetId()).getExpression();
                List<ExpressionNested> expressions = JSONUtil.jsonToArray(targetExpression, ExpressionNested.class);
                if (CollectionUtils.isNotEmpty(expressions)) {
                    for (ExpressionNested expression : expressions) {
                        String expressionType = expression.getType();
                        if (com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType)) {
                            SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expressionType);
                            if (expressionByValueV3 != null) {
                                expressionType = expressionByValueV3.getValue();
                            }
                        }
                        // 以用户选择的投放类型为准
                        if (ExpressionEnum.asinSameAs.value().equals(expressionType) || ExpressionEnum.asinExpandedFrom.value().equals(expressionType)) {
                            expressionType = e.getExpressionType();
                        }
                        expression.setType(expressionType);
                    }
                }
                suggestedBidVo.setExpression(expressions);
            } else {
                ExpressionNested expression = new ExpressionNested();
                expression.setType(e.getExpressionType());
                expression.setValue(e.getAsin());
                suggestedBidVo.setExpression(Collections.singletonList(expression));
            }
            suggestedTargetVos.add(suggestedBidVo);
        }
        if (CollectionUtils.isEmpty(asins)) {
            return suggestedTargetVos;
        }
        Lists.partition(suggestedTargetVos, 100).forEach(
                subList -> cpcSdRecommendApiService.getBidRecommendations(shop, asins, subList, costType, bidOptimization));
        return suggestedTargetVos;
    }

    @Override
    public Result getSuggestedBidByText(AddSdTargetingVo addTargetingVo) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(addTargetingVo.getShopId(), addTargetingVo.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(addTargetingVo.getPuid(), addTargetingVo.getShopId(), addTargetingVo.getGroupId());
        if (amazonSdAdGroup == null) {
            return ResultUtil.error("没有广告组信息");
        }

        List<SdSuggestedTargetVo> suggestedTargetVos = new ArrayList<>();

        // 广告组下有效的asin
        List<String> asins = amazonSdAdProductDao.listValidAsin(addTargetingVo.getPuid(), addTargetingVo.getShopId(), addTargetingVo.getGroupId());
        if (CollectionUtils.isEmpty(asins)) {
            return ResultUtil.returnSucc(suggestedTargetVos);
        }

        SdSuggestedTargetVo suggestedBidVo;
        ExpressionNested expressionNested;
        Expression expression;
        List<Expression> expressions;
        for (SdTargetingVo vo : addTargetingVo.getTargetings()) {
            expressions = new ArrayList<>();
            if (SdTargetTypeEnum.asin.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(vo.getAsin());
                expressions.add(expression);
            } else if (SdTargetTypeEnum.exactProduct.name().equals(vo.getType())
                    || SdTargetTypeEnum.similarProduct.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(vo.getType());
                expression.setValue(""); // 接口要求是""
                expressions.add(expression);
            } else {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinCategorySameAs.value());
                expression.setValue(vo.getCategoryId());
                expressions.add(expression);

                if (StringUtils.isNotBlank(vo.getBrand())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(vo.getBrand());
                    expressions.add(expression);
                }
                if (StringUtils.isBlank(vo.getMinPrice())) {
                    vo.setMinPrice(MIN_PRICE);
                }
                if (StringUtils.isBlank(vo.getMaxPrice())) {
                    vo.setMaxPrice(MAX_PRICE);
                }
                if (StringUtils.isNotBlank(vo.getMinPrice()) && StringUtils.isNotBlank(vo.getMaxPrice())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinPriceBetween.value());
                    expression.setValue(vo.getMinPrice() + "-" + vo.getMaxPrice());
                    expressions.add(expression);
                }
                if (vo.getMinReviewRating() != null && vo.getMaxReviewRating() != null) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                    expression.setValue(vo.getMinReviewRating() + "-" + vo.getMaxReviewRating());
                    expressions.add(expression);
                }
                if (vo.getPrimeShippingEligible() != null) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                    expression.setValue(vo.getPrimeShippingEligible().toString());
                    expressions.add(expression);
                }
            }

            List<ExpressionNested> expressionNesteds = new ArrayList<>();
            if (TacticEnum.T00030.name().equalsIgnoreCase(amazonSdAdGroup.getTactic())) {
                expressionNested = new ExpressionNested();
                expressionNested.setType("views");
                expressionNested.setValue(expressions);

                expression = new Expression();
                expression.setType("lookback");
                expression.setValue("30");
                expressions.add(expression);
            } else {
                for (Expression e : expressions) {
                    expressionNested = new ExpressionNested();
                    expressionNested.setType(e.getType());
                    expressionNested.setValue(e.getValue());
                    expressionNesteds.add(expressionNested);
                }
            }

            suggestedBidVo = new SdSuggestedTargetVo();
            suggestedBidVo.setExpression(expressionNesteds);
            suggestedTargetVos.add(suggestedBidVo);
        }

        // 接口要求：每次指定的关键词数量不能超过10个
        List<List<SdSuggestedTargetVo>> lists = Lists.partition(suggestedTargetVos, 100);
        String bidOptimization = amazonSdAdGroup.getBidOptimization();
        if (StringUtils.isBlank(bidOptimization)) {
            bidOptimization = Constants.SD_BID_OPTIMIZATION_CLICKS;
        }
        for (List<SdSuggestedTargetVo> subList : lists) {
            cpcSdRecommendApiService.getBidRecommendations(shop, asins, subList, Constants.SD_BID_OPTIMIZATION_REACH.equalsIgnoreCase(bidOptimization) ? "vcpm" : "cpc", bidOptimization);
        }
        return ResultUtil.returnSucc(suggestedTargetVos);
    }

    private String checkAddTargetingVo(List<SdTargetingVo> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return "对象不存在";
        }

        for (SdTargetingVo vo : targetings) {
            if (!SdTargetTypeEnum.asin.name().equals(vo.getType())
                    && !SdTargetTypeEnum.category.name().equals(vo.getType())
                    && !SdTargetTypeEnum.exactProduct.name().equals(vo.getType())
                    && !SdTargetTypeEnum.similarProduct.name().equals(vo.getType())) {
                return "cpc.type.not.support";
            }
            if ((TargetTypeEnum.asin.name().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin()))
                    || (TargetTypeEnum.category.name().equals(vo.getType()) && StringUtils.isBlank(vo.getCategoryId()))) {
                return "cpc.none.targeting";
            }
            if (StringUtils.isBlank(vo.getBid())) {
                return "cpc.none.targeting";
            }

        }
        return null;
    }

    private String checkAddTargetingVoNew(List<SdTargetingVo> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return "投放对象为空";
        }

        for (SdTargetingVo vo : targetings) {
            if (StringUtils.isEmpty(vo.getType()) || StringUtils.isEmpty(vo.getPredicate())) {
                return "对应类型/投放类型不存在";
            }
            if (!SdTargetTypeEnum.getMap().containsKey(vo.getType())) {
                return "cpc.predicate.type.not.support";
            }
            if (!SdTargetPredicateEnum.getMap().containsKey(vo.getPredicate())) {
                return "cpc.predicate.type.not.support";
            }
            if ((SdTargetTypeEnum.asin.getValue().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin()))
                    || (SdTargetTypeEnum.category.getValue().equals(vo.getType()) && StringUtils.isBlank(vo.getCategoryId()))
                    || (SdTargetTypeEnum.audience.getValue().equals(vo.getType()) && StringUtils.isBlank(vo.getAudienceId()))) {
                return "cpc.none.targeting";
            }
            if (StringUtils.isBlank(vo.getBid())) {
                return "cpc.none.targeting";
            }

        }
        return null;
    }

    private String checkTargetingBidVo(List<SdTargetingVo> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return "对象不存在";
        }

        for (SdTargetingVo vo : targetings) {
            if (StringUtils.isEmpty(vo.getType())) {
                return "对应类型/投放类型不存在";
            }
            if (!SdTargetTypeEnum.getMap().containsKey(vo.getType())) {
                return "cpc.predicate.type.not.support";
            }
            if ((SdTargetTypeEnum.asin.getValue().equals(vo.getType()) && StringUtils.isBlank(vo.getAsin()))
                    || (SdTargetTypeEnum.category.getValue().equals(vo.getType()) && StringUtils.isBlank(vo.getCategoryId()))
                    || (SdTargetTypeEnum.audience.getValue().equals(vo.getType()) && StringUtils.isBlank(vo.getAudienceId()))) {
                return "cpc.none.targeting";
            }
        }
        return null;
    }

    private List<AmazonSdAdTargeting> convertAddTargetingVoToPONew(Integer uid, ShopAuth shop,
                                                                   String campaignId, String adGroupId,
                                                                   String profileId , List<SdTargetingVo> targetList) {
        List<AmazonSdAdTargeting> amazonAdTargetings = new ArrayList<>(targetList.size());
        AmazonSdAdTargeting amazonAdTargeting;
        for (SdTargetingVo vo : targetList) {
            amazonAdTargeting = new AmazonSdAdTargeting();
            amazonAdTargetings.add(amazonAdTargeting);

            amazonAdTargeting.setPuid(shop.getPuid());
            amazonAdTargeting.setShopId(shop.getId());
            amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
            amazonAdTargeting.setProfileId(profileId);
            amazonAdTargeting.setCampaignId(campaignId);
            amazonAdTargeting.setAdGroupId(adGroupId);
            amazonAdTargeting.setExpressionType(Constants.MANUAL);
            amazonAdTargeting.setState(CpcStatusEnum.enabled.name());
            amazonAdTargeting.setType(vo.getType());
            amazonAdTargeting.setBid(new BigDecimal(vo.getBid()));
            amazonAdTargeting.setCreateId(uid);
            amazonAdTargeting.setCreateInAmzup(1);

            if (StringUtils.isNotBlank(vo.getSuggested())) {
                amazonAdTargeting.setSuggested(new BigDecimal(vo.getSuggested()));
            }
            if (StringUtils.isNotBlank(vo.getRangeStart())) {
                amazonAdTargeting.setRangeStart(new BigDecimal(vo.getRangeStart()));
            }
            if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                amazonAdTargeting.setRangeEnd(new BigDecimal(vo.getRangeEnd()));
            }

            //根据入参设置expression
            targetExpressionHandler(vo, amazonAdTargeting);

            //单独设置场内客群投放进行区分
            if (StringUtils.isNotEmpty(vo.getParentType()) && SdTargetTypeEnum.audience_InMarket.getValue().equals(vo.getParentType())) {
                amazonAdTargeting.setParentType(SdTargetTypeEnum.audience_InMarket.getValue());
            }
        }

        return amazonAdTargetings;
    }

    private List<AmazonSdAdTargeting> convertBinRecommendTargetingVoToPONew(ShopAuth shop, List<SdTargetingVo> targetList) {
        List<AmazonSdAdTargeting> amazonAdTargetings = new ArrayList<>(targetList.size());
        AmazonSdAdTargeting amazonAdTargeting;
        for (SdTargetingVo vo : targetList) {
            amazonAdTargeting = new AmazonSdAdTargeting();
            amazonAdTargetings.add(amazonAdTargeting);

            amazonAdTargeting.setPuid(shop.getPuid());
            amazonAdTargeting.setShopId(shop.getId());
            amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
            amazonAdTargeting.setExpressionType(Constants.MANUAL);
            amazonAdTargeting.setState(CpcStatusEnum.enabled.name());

            //根据入参设置expression
            targetExpressionHandler(vo, amazonAdTargeting);
        }

        return amazonAdTargetings;
    }

    private void targetExpressionHandler(SdTargetingVo vo, AmazonSdAdTargeting amazonAdTargeting) {
        Expression expression;
        List<Expression> expressions = new ArrayList<>();
        //内容相关投放-商品投放
        if (SdTargetTypeEnum.asin.getValue().equals(vo.getType())) {
            expression = new Expression();
            expression.setType(SdTargetTypeNewEnum.ASIN_SAME_AS.getCode());
            expression.setValue(vo.getAsin());
            expressions.add(expression);

            amazonAdTargeting.setTargetText(vo.getAsin());
            amazonAdTargeting.setImgUrl(vo.getImgUrl());
            amazonAdTargeting.setTitle(vo.getTitle());
        } else if (SdTargetTypeNewEnum.EXACT_PRODUCT.getCode().equals(vo.getType())
                || SdTargetTypeNewEnum.SIMILAR_PRODUCT.getCode().equals(vo.getType())
                || SdTargetTypeNewEnum.RELATEDP_RODUCT.getCode().equals(vo.getType())) {
            expression = new Expression();
            expression.setType(vo.getType());
//            if (!SdTargetPredicateEnum.VIEW.getCode().equals(vo.getPredicate())
//                    && !SdTargetPredicateEnum.PURCHASE.getCode().equals(vo.getPredicate())) {
//                expression.setValue(""); // 接口要求是""
//            }
            expressions.add(expression);
            amazonAdTargeting.setSuggestBidId(vo.getCategoryId());
            amazonAdTargeting.setTargetText(vo.getName());
        } else if (SdTargetTypeNewEnum.CONTENT_CATEGORY.getCode().equals(vo.getType())) {
            expression = new Expression();
            expression.setType(vo.getType());
            expression.setValue(vo.getAudienceId());
            expressions.add(expression);
            amazonAdTargeting.setTargetText(vo.getAudienceName());
            if (StringUtils.isEmpty(amazonAdTargeting.getTargetText()) && StringUtils.isNotEmpty(vo.getName())) {
                amazonAdTargeting.setTargetText(vo.getName());
            }
            amazonAdTargeting.setSuggestBidId(vo.getAudienceId());
        } else if (SdTargetTypeEnum.category.getValue().equals(vo.getType()) || SdTargetTypeEnum.audience.getValue().equals(vo.getType())) {
            if (SdTargetTypeEnum.category.getValue().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(SdTargetTypeNewEnum.ASIN_CATEGORY_SAME_AS.getCode());
                expression.setValue(vo.getCategoryId());
                expressions.add(expression);
                amazonAdTargeting.setSuggestBidId(vo.getCategoryId());
                amazonAdTargeting.setTargetText(vo.getName());
                if (StringUtils.isEmpty(amazonAdTargeting.getTargetText()) && StringUtils.isNotEmpty(vo.getAudienceName())) {
                    amazonAdTargeting.setTargetText(vo.getAudienceName());
                }
                amazonAdTargeting.setCategoryName(vo.getCategoryName());
            }

            if (SdTargetTypeEnum.audience.getValue().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(SdTargetTypeNewEnum.AUDIENCE_SAME_AS.getCode());
                expression.setValue(vo.getAudienceId());
                expressions.add(expression);
                amazonAdTargeting.setSuggestBidId(vo.getAudienceId());
                amazonAdTargeting.setTargetText(vo.getAudienceName());
                if (StringUtils.isEmpty(amazonAdTargeting.getTargetText()) && StringUtils.isNotEmpty(vo.getName())) {
                    amazonAdTargeting.setTargetText(vo.getName());
                }
            }


            if (StringUtils.isNotBlank(vo.getBrand())) {
                Optional.ofNullable(vo.getBrandName()).ifPresent(amazonAdTargeting::setBrandName);

                expression = new Expression();
                expression.setType(SdTargetTypeNewEnum.ASIN_BRAND_SAME_AS.getCode());
                expression.setValue(vo.getBrand());
                expressions.add(expression);
            }

            //minPrice和maxPrice都应该为double类型
            if (StringUtils.isNotBlank(vo.getMinPrice()) || StringUtils.isNotBlank(vo.getMaxPrice())) {
                Optional.ofNullable(vo.getMinPrice()).ifPresent(amazonAdTargeting::setMinPrice);
                Optional.ofNullable(vo.getMaxPrice()).ifPresent(amazonAdTargeting::setMaxPrice);

                expression = new Expression();
                if (StringUtils.isBlank(vo.getMinPrice())) {
                    expression.setType(SdTargetTypeNewEnum.ASIN_PRICE_LESS_THAN.getCode());
                    expression.setValue(vo.getMaxPrice());
                    expressions.add(expression);
                } else if (StringUtils.isBlank(vo.getMaxPrice())) {
                    expression.setType(SdTargetTypeNewEnum.ASIN_PRICE_GREATER_THAN.getCode());
                    expression.setValue(vo.getMinPrice());
                    expressions.add(expression);
                } else {
                    expression.setType(SdTargetTypeNewEnum.ASIN_PRICE_BETWEEN.getCode());
                    expression.setValue(vo.getMinPrice() + "-" + vo.getMaxPrice());
                    expressions.add(expression);
                }
            }
            if (vo.getMinReviewRating() != null || vo.getMaxReviewRating() != null) {
                Optional.ofNullable(vo.getMinReviewRating()).ifPresent(amazonAdTargeting::setMinReviewRating);
                Optional.ofNullable(vo.getMaxReviewRating()).ifPresent(amazonAdTargeting::setMaxReviewRating);
                expression = new Expression();
                if (vo.getMinReviewRating() == null) {
                    expression.setType(SdTargetTypeNewEnum.ASIN_REVIEW_RATING_LESS_THAN.getCode());
                    expression.setValue(String.valueOf(vo.getMaxReviewRating()));
                    expressions.add(expression);
                } else if (vo.getMaxReviewRating() == null) {
                    expression.setType(SdTargetTypeNewEnum.ASIN_REVIEW_RATING_GREATER_THAN.getCode());
                    expression.setValue(String.valueOf(vo.getMinReviewRating()));
                    expressions.add(expression);
                } else {
                    expression.setType(SdTargetTypeNewEnum.ASIN_REVIEW_RATING_BETWEEN.getCode());
                    expression.setValue(vo.getMinReviewRating() + "-" + vo.getMaxReviewRating());
                    expressions.add(expression);
                }
            }
            if (vo.getPrimeShippingEligible() != null) {
                Optional.ofNullable(vo.getPrimeShippingEligible()).ifPresent(amazonAdTargeting::setPrimeShippingEligible);
                expression = new Expression();
                expression.setType(SdTargetTypeNewEnum.ASIN_IS_PRIME_SHIPPING_ELIGIBLE.getCode());
                expression.setValue(vo.getPrimeShippingEligible().toString());
                expressions.add(expression);
            }
        }

        List<ExpressionNested> expressionNesteds = new ArrayList<>();
        ExpressionNested expressionNested;
        SdTargetPredicateEnum predicate = SdTargetPredicateEnum.getMap().get(vo.getPredicate());
        if (Objects.nonNull(predicate) && (SdTargetPredicateEnum.VIEW == predicate
                || SdTargetPredicateEnum.PURCHASE == predicate || SdTargetPredicateEnum.AUDIENCE == predicate)) {
            expressionNested = new ExpressionNested();
            expressionNested.setType(predicate.getCode());
            expressionNested.setValue(expressions);
            expressionNesteds.add(expressionNested);

            if (SdTargetPredicateEnum.AUDIENCE != predicate) {
                expression = new Expression();
                expression.setType("lookback");
                String lookBack = Optional.ofNullable(vo.getLookback()).filter(StringUtils::isNotEmpty).orElse(SdLookbackEnum.LOOKBACK_30.getValue());
                expression.setValue(lookBack);
                expressions.add(expression);
            }

            amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressionNesteds));
            return;
        }
        amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressions));
    }

    private List<AmazonSdAdTargeting> convertAddTargetingVoToPO(Integer uid, AmazonSdAdGroup amazonAdGroup, List<SdTargetingVo> targetings) {
        List<AmazonSdAdTargeting> amazonAdTargetings = new ArrayList<>(targetings.size());
        AmazonSdAdTargeting amazonAdTargeting;
        ExpressionNested expressionNested;
        Expression expression;
        List<Expression> expressions;
        for (SdTargetingVo vo : targetings) {
            amazonAdTargeting = new AmazonSdAdTargeting();
            amazonAdTargetings.add(amazonAdTargeting);

            amazonAdTargeting.setPuid(amazonAdGroup.getPuid());
            amazonAdTargeting.setShopId(amazonAdGroup.getShopId());
            amazonAdTargeting.setMarketplaceId(amazonAdGroup.getMarketplaceId());
            amazonAdTargeting.setProfileId(amazonAdGroup.getProfileId());
            amazonAdTargeting.setCampaignId(amazonAdGroup.getCampaignId());
            amazonAdTargeting.setAdGroupId(amazonAdGroup.getAdGroupId());
            amazonAdTargeting.setExpressionType(Constants.MANUAL);
            amazonAdTargeting.setState(CpcStatusEnum.enabled.name());
            amazonAdTargeting.setType(vo.getType());
            amazonAdTargeting.setBid(new BigDecimal(vo.getBid()));
            amazonAdTargeting.setCreateId(uid);
            amazonAdTargeting.setCreateInAmzup(1);

            if (StringUtils.isNotBlank(vo.getSuggested())) {
                amazonAdTargeting.setSuggested(new BigDecimal(vo.getSuggested()));
            }
            if (StringUtils.isNotBlank(vo.getRangeStart())) {
                amazonAdTargeting.setRangeStart(new BigDecimal(vo.getRangeStart()));
            }
            if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                amazonAdTargeting.setRangeEnd(new BigDecimal(vo.getRangeEnd()));
            }

            expressions = new ArrayList<>();
            if (SdTargetTypeEnum.asin.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinSameAs.value());
                expression.setValue(vo.getAsin());
                expressions.add(expression);

                amazonAdTargeting.setTargetText(vo.getAsin());
                amazonAdTargeting.setImgUrl(vo.getImgUrl());
                amazonAdTargeting.setTitle(vo.getTitle());
            } else if (SdTargetTypeEnum.exactProduct.name().equals(vo.getType())
                    || SdTargetTypeEnum.similarProduct.name().equals(vo.getType())) {
                expression = new Expression();
                expression.setType(vo.getType());
                expression.setValue(""); // 接口要求是""
                expressions.add(expression);

                amazonAdTargeting.setTargetText(vo.getTitle());
            } else {
                expression = new Expression();
                expression.setType(ExpressionEnum.asinCategorySameAs.value());
                expression.setValue(vo.getCategoryId());
                expressions.add(expression);
                amazonAdTargeting.setTargetText(vo.getCategoryName());
                amazonAdTargeting.setCategoryName(vo.getCategoryName());
                amazonAdTargeting.setBrandName(vo.getBrandName());
                amazonAdTargeting.setMinPrice(vo.getMinPrice());
                amazonAdTargeting.setMaxPrice(vo.getMaxPrice());
                amazonAdTargeting.setMinReviewRating(vo.getMinReviewRating());
                amazonAdTargeting.setMaxReviewRating(vo.getMaxReviewRating());
                amazonAdTargeting.setPrimeShippingEligible(vo.getPrimeShippingEligible());

                if (StringUtils.isNotBlank(vo.getBrand())) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(vo.getBrand());
                    expressions.add(expression);
                }
                if (StringUtils.isNotBlank(vo.getMinPrice()) || StringUtils.isNotBlank(vo.getMaxPrice())) {
                    expression = new Expression();
                    if (StringUtils.isBlank(vo.getMinPrice())) {
                        expression.setType(ExpressionEnum.asinPriceLessThan.value());
                        expression.setValue(vo.getMaxPrice());
                        expressions.add(expression);
                    } else if (StringUtils.isBlank(vo.getMaxPrice())) {
                        expression.setType(ExpressionEnum.asinPriceGreaterThan.value());
                        expression.setValue(vo.getMinPrice());
                        expressions.add(expression);
                    } else {
                        expression.setType(ExpressionEnum.asinPriceBetween.value());
                        expression.setValue(vo.getMinPrice() + "-" + vo.getMaxPrice());
                        expressions.add(expression);
                    }
                }
                if (vo.getMinReviewRating() != null || vo.getMaxReviewRating() != null) {
                    expression = new Expression();
                    if (vo.getMinReviewRating() == null) {
                        expression.setType(ExpressionEnum.asinReviewRatingLessThan.value());
                        expression.setValue(String.valueOf(vo.getMaxReviewRating()));
                        expressions.add(expression);
                    } else if (vo.getMaxReviewRating() == null) {
                        expression.setType(ExpressionEnum.asinReviewRatingGreaterThan.value());
                        expression.setValue(String.valueOf(vo.getMinReviewRating()));
                        expressions.add(expression);
                    } else {
                        expression.setType(ExpressionEnum.asinReviewRatingBetween.value());
                        expression.setValue(vo.getMinReviewRating() + "-" + vo.getMaxReviewRating());
                        expressions.add(expression);
                    }
                }
                if (vo.getPrimeShippingEligible() != null) {
                    expression = new Expression();
                    expression.setType(ExpressionEnum.asinIsPrimeShippingEligible.value());
                    expression.setValue(vo.getPrimeShippingEligible().toString());
                    expressions.add(expression);
                }
            }

            List<ExpressionNested> expressionNesteds = new ArrayList<>();
            if (TacticEnum.T00030.name().equalsIgnoreCase(amazonAdGroup.getTactic())) {
                expressionNested = new ExpressionNested();
                expressionNested.setType("views");
                expressionNested.setValue(expressions);
                expressionNesteds.add(expressionNested);

                expression = new Expression();
                expression.setType("lookback");
                expression.setValue("30");
                expressions.add(expression);
            } else {
                for (Expression e : expressions) {
                    expressionNested = new ExpressionNested();
                    expressionNested.setType(e.getType());
                    expressionNested.setValue(e.getValue());
                    expressionNesteds.add(expressionNested);
                }
            }

            amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressionNesteds));
        }

        return amazonAdTargetings;
    }

    @Override
    public Result updateBatchMultiShop(List<UpdateBatchTargetVo> vos, String type, String loginIp) {
        Map<Integer, List<UpdateBatchTargetVo>> voMap = vos.stream().collect(Collectors.groupingBy(UpdateBatchTargetVo::getShopId));
        BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting> batchData = new BatchResponseVo<>();
        Result<BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting>> result;
        BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting> data;
        for (Map.Entry<Integer, List<UpdateBatchTargetVo>> entry : voMap.entrySet()) {
            result = this.updateBatch(entry.getValue(), type, loginIp);
            if (result.error()) {
                return ResultUtil.error(result.getMsg());
            }

            data = result.getData();
            if (data != null) {
                batchData.addCountNum(data.getCountNum());
                batchData.addSuccessNum(data.getSuccessNum());
                batchData.addFailNum(data.getFailNum());
                batchData.addSuccessList(data.getSuccessList());
                batchData.addErrorList(data.getErrorList());
            }
        }
        return ResultUtil.success(batchData);
    }

    @Override
    public Result updateBatch(List<UpdateBatchTargetVo> vos, String type, String ip) {

        int puid = vos.get(0).getPuid();

        int uid = vos.get(0).getUid();
        int shopId = vos.get(0).getShopId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }


        List<UpdateBatchTargetVo> errorList = Lists.newArrayList();

        List<Long> ids = vos.stream().filter(e -> e.getId() != null && e.getId() > 0).map(e -> Long.valueOf(e.getId().toString())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return ResultUtil.error("参数错误");
        }

        List<AmazonSdAdTargeting> listByIdList = amazonSdAdTargetingDao.getListByLongIdList(puid, ids);
        List<AmazonSdAdTargeting> updateList = Lists.newArrayList();
        Map<Long, AmazonSdAdTargeting> amazonAdTargetingMap = listByIdList.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getId, e -> e));

        List<String> adGroupIds = listByIdList.stream().map(AmazonSdAdTargeting::getAdGroupId).distinct().collect(Collectors.toList());
        List<AmazonSdAdGroup> sdAdGroupList = amazonSdAdGroupDao.getByGroupIds(puid, shopId, adGroupIds);
        Map<String, AmazonSdAdGroup> adGroupMap = sdAdGroupList.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e, (v1, v2) -> v2));

        for (UpdateBatchTargetVo vo : vos) {
            checkBatchVo(vo, type);
            if (StringUtils.isNotBlank(vo.getFailReason())) {
                errorList.add(vo);
                continue;
            }
            AmazonSdAdTargeting oldAmazonAdTargeting = amazonAdTargetingMap.get(vo.getId());
            if (oldAmazonAdTargeting == null) {
                vo.setFailReason("对象不存在");
                errorList.add(vo);
                continue;
            }
            AmazonSdAdTargeting amazonAdTargeting = new AmazonSdAdTargeting();
            BeanUtils.copyProperties(oldAmazonAdTargeting, amazonAdTargeting);
            convertVoToBatchUpdatePo(amazonAdTargeting, vo, type);
            AmazonSdAdGroup sdAdGroup = adGroupMap.get(oldAmazonAdTargeting.getAdGroupId());
            if (Objects.nonNull(sdAdGroup)) {
                amazonAdTargeting.setCampaignId(sdAdGroup.getCampaignId());
                amazonAdTargeting.setTactic(sdAdGroup.getTactic());
            }
            updateList.add(amazonAdTargeting);
        }
        if (CollectionUtils.isEmpty(updateList)) {
            BatchResponseVo<UpdateBatchTargetVo, AmazonAdTargeting> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            data.setSuccessNum(0);
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<UpdateBatchTargetVo, AmazonSdAdTargeting>> result = cpcSdTargetingApiService.update(shop, profile, updateList, type);

        List<AdManageOperationLog> targetsLogs = Lists.newArrayList();
        for (AmazonSdAdTargeting targeting : updateList) {
            AdManageOperationLog targetsLog = adOperationLogService.getSdTargetsLog(amazonAdTargetingMap.get(targeting.getId()), targeting);
            targetsLog.setIp(ip);
            targetsLogs.add(targetsLog);
        }

        if (result.success()) {
            BatchResponseVo<UpdateBatchTargetVo, AmazonSdAdTargeting> data = result.getData();
            List<UpdateBatchTargetVo> keywordsError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                keywordsError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }
            List<AmazonSdAdTargeting> amazonAdKeywordsSuccess = data.getSuccessList();
            Map<String, AmazonSdAdTargeting> successTargetsMap = amazonAdKeywordsSuccess.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, v -> v, (v1, v2) -> v2));
            Map<String, UpdateBatchTargetVo> errorTargetsMap = keywordsError.stream().collect(Collectors.toMap(UpdateBatchTargetVo::getTargetId, v -> v, (v1, v2) -> v2));
            log.info("sd投放批量更新结果 successMap={}， errorMap={}", successTargetsMap, errorTargetsMap);
            for (AdManageOperationLog targetsLog : targetsLogs) {
                if (!StringUtil.isEmptyObject(successTargetsMap.get(targetsLog.getTargetId()))) {
                    targetsLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg = errorTargetsMap.containsKey(targetsLog.getTargetId()) ? errorTargetsMap.get(targetsLog.getTargetId()).getFailReason() : "操作失败";
                    targetsLog.setResultInfo(errMsg);
                }
            }

            //记操作日志
            if (CollectionUtils.isNotEmpty(amazonAdKeywordsSuccess)) {
                amazonSdAdTargetingDao.updateList(puid, amazonAdKeywordsSuccess, type);
                //写入doris
                saveDoris(puid, shopId, amazonAdKeywordsSuccess.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()));
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},更新成功数据：{}", type, uid, puid, shopId, JSONUtil.objectToJson(amazonAdKeywordsSuccess));
                //前端不需要展示成功消息，减少消耗移除成功数据
                data.getSuccessList().clear();
            }

        } else {
            for (AdManageOperationLog targetsLog : targetsLogs) {
                targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                targetsLog.setResultInfo(result.getMsg());
            }
        }
        adOperationLogService.batchLogsMergeByAdGroup(targetsLogs);
        return result;

    }

    /**
     * 根据更新类型进行数据校验
     */
    private UpdateBatchTargetVo checkBatchVo(UpdateBatchTargetVo updateBatchTargetVo, String type) {

        if (updateBatchTargetVo.getId() == null || updateBatchTargetVo.getId() < 1) {
            updateBatchTargetVo.setFailReason("参数错误");
            return updateBatchTargetVo;
        }

        if (Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)) {
            if (updateBatchTargetVo.getBid() == null || updateBatchTargetVo.getBid().isNaN() || updateBatchTargetVo.getBid() < 0.02) {
                updateBatchTargetVo.setFailReason("竞价错误 ");
            }
            return updateBatchTargetVo;
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            if (StringUtils.isEmpty(updateBatchTargetVo.getState())) {
                updateBatchTargetVo.setFailReason("参数错误");
            }
            return updateBatchTargetVo;
        } else {
            updateBatchTargetVo.setFailReason("参数错误");
            return updateBatchTargetVo;
        }

    }

    // 更新广告组时vo->po
    private void convertVoToBatchUpdatePo(AmazonSdAdTargeting amazonAdTargeting, UpdateBatchTargetVo vo, String type) {
        if (Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)) {
            amazonAdTargeting.setBid(BigDecimal.valueOf(vo.getBid()));
        }
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            amazonAdTargeting.setState(vo.getState());
        }
        amazonAdTargeting.setUpdateId(vo.getUid());
    }

    private List<Object> buildUpLogMessage(Map<Long, AmazonSdAdTargeting> oldList, List<AmazonSdAdTargeting> newList, String type) {
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if (Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)) {
            dataList.add("SP商品投放批量修改默认竞价");

        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            dataList.add("SP商品投放批量修改状态");

        }
        newList.forEach(e -> {
            AmazonSdAdTargeting old = oldList.get(e.getId());

            if (Constants.CPC_SD_TARGET_BATCH_UPDATE_BID.equals(type)) {

                builder.append("id:").append(e.getId());
                if (old != null) {
                    builder.append(",旧值:").append(old.getBid());
                }
                builder.append(",新值:").append(e.getBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {

                builder.append("id:").append(e.getId());
                if (old != null) {
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0, builder.length());
        });
        return dataList;
    }


    /**
     * 写入doris
     *
     * @param list
     */
    @Override
    public void saveDoris(List<AmazonSdAdTargeting> list, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdTargetingSd> collect = list.stream().map(x -> {
                OdsAmazonAdTargetingSd odsAmazonAdTargetingSd = new OdsAmazonAdTargetingSd();
                BeanUtils.copyProperties(x, odsAmazonAdTargetingSd);
                if (create) {
                    odsAmazonAdTargetingSd.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdTargetingSd.setUpdateTime(date);
                }
                if (StringUtils.isNotBlank(odsAmazonAdTargetingSd.getState())) {
                    odsAmazonAdTargetingSd.setState(odsAmazonAdTargetingSd.getState().toLowerCase());
                }
                return odsAmazonAdTargetingSd;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sd targeting save doris error", e);
        }
    }

    /**
     * 写入doris
     *
     * @param puid
     * @param shopId
     * @param targetIdList
     */
    @Override
    public void saveDoris(int puid, int shopId, List<String> targetIdList) {
        try {
            if (CollectionUtils.isEmpty(targetIdList)) {
                return;
            }
            List<AmazonSdAdTargeting> list = amazonSdAdTargetingDao.listByTargetId(puid, shopId, targetIdList);
            saveDoris(list, false, false);
        } catch (Exception e) {
            log.error("sd targeting save doris error", e);
        }
    }

}