package com.meiyunji.sponsored.service.searchTermsAnalysis;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.amc.dto.QueryAbaRankDto;
import com.meiyunji.sponsored.service.amc.dto.QueryWordAdSpaceReportDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsWeekSearchTermsAnalysisDao;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.searchTermsAnalysis.qo.SearchTermsAnalysisTrendQo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.SearchTermsListTrendsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.WeekFields;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-08-06 10:55
 */
@Service
public class WeekSearchTermsAnalysisService implements SearchTermsAnalysisInterface {
    @Resource
    private IOdsWeekSearchTermsAnalysisDao weekSearchTermsAnalysisDao;
    @Resource
    private WeekSearchTermsAnalysisCache weekSearchTermsAnalysisCache;

    public String getLatestDate(String marketplaceId) {
        return weekSearchTermsAnalysisCache.get(marketplaceId);
    }

    public Map<String, String> getLatestDateList(List<String> marketplaceIdList) {
        Map<String, String> resultMap = new HashMap<>();
        for (String marketplaceId : marketplaceIdList) {
            if (StringUtils.isBlank(marketplaceId)) {
                continue;
            }
            resultMap.put(marketplaceId, getLatestDate(marketplaceId));
        }
        return resultMap;
    }

    // 该方法由于doris有in的限制,所以会对词组进行分组循环查数据库,需要保证查询次数可控才可调用此方法
    public List<OdsWeekSearchTermsAnalysis> queryRanks(List<QueryWordAdSpaceReportDto> dtoList, Map<String, String> marketplaceIdLatestDateMap) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }
        // 按 marketplaceId 分组，并提取 customerSearchTerm
        Map<String, Set<String>> marketplaceIdToSearchTerms = dtoList.stream()
            .filter(Objects::nonNull)
            .filter(k->StringUtils.isNotBlank(k.getMarketplaceId()))
            .filter(k->StringUtils.isNotBlank(k.getCustomerSearchTerm()))
            .collect(Collectors.groupingBy(
                QueryWordAdSpaceReportDto::getMarketplaceId, // 按 marketplaceId 分组
                Collectors.mapping(
                    QueryWordAdSpaceReportDto::getCustomerSearchTerm, // 提取 customerSearchTerm
                    Collectors.toSet() // 收集为 Set
                )
            ));

        // 将分组结果转换为 List<QueryAbaRankDto>
        List<QueryAbaRankDto> collect = marketplaceIdToSearchTerms.entrySet().stream()
            .map(entry -> {
                QueryAbaRankDto dto = new QueryAbaRankDto();
                dto.setMarketplaceId(entry.getKey()); // 设置 marketplaceId
                dto.setSearchTerms(entry.getValue()); // 设置 searchTerms
                dto.setStartDate(marketplaceIdLatestDateMap.get(entry.getKey()));
                return dto;
            })
            .collect(Collectors.toList());

        List<OdsWeekSearchTermsAnalysis> resultList = new ArrayList<>();
        try {
            List<QueryAbaRankDto> eachMFlag = collect.stream().filter(k -> CollectionUtils.isNotEmpty(k.getSearchTerms()) && k.getSearchTerms().size() >= 10000).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(eachMFlag)) {
                //分站点
                for (QueryAbaRankDto each: collect) {
                    //拆分搜索词
                    List<List<String>> partition = Lists.partition(Lists.newArrayList(each.getSearchTerms()), 9000);
                    for (List<String> single: partition) {
                        each.setSearchTerms(new HashSet<>(single));
                        List<OdsWeekSearchTermsAnalysis> odsWeekSearchTermsAnalyses = weekSearchTermsAnalysisDao.queryRanks(Collections.singletonList(each));
                        resultList.addAll(odsWeekSearchTermsAnalyses);
                    }
                }
            } else {
                resultList = weekSearchTermsAnalysisDao.queryRanks(collect);
            }

        } catch (Exception e) {}

        return resultList;
    }


    // 该方法由于doris有in的限制,所以会对词组进行分组循环查数据库,需要保证查询次数可控才可调用此方法
    public List<OdsWeekSearchTermsAnalysis> queryRanks(List<String> searchTerms, String marketplaceId) {
        String date = getLatestDate(marketplaceId);

        if (StringUtils.isAnyBlank(marketplaceId, date)) {
            return Collections.emptyList();
        }

        List<OdsWeekSearchTermsAnalysis> resultList = new ArrayList<>();
        List<String> distinctSearchTerms = searchTerms.stream().map(String::toLowerCase).distinct().collect(Collectors.toList());
        for (List<String> subSearchTerms : Lists.partition(distinctSearchTerms, 9000)) {
            try {
                List<OdsWeekSearchTermsAnalysis> weekSearchTermsAnalyses = weekSearchTermsAnalysisDao.queryRanks(subSearchTerms, marketplaceId, date);
                resultList.addAll(weekSearchTermsAnalyses);
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (Exception e) {}
        }

        return resultList;
    }

    // 该方法由于doris有in的限制,所以会对词组进行分组循环查数据库,需要保证查询次数可控才可调用此方法
    public List<OdsWeekSearchTermsAnalysis> queryRanks(List<String> searchTerms, List<String> marketplaceIdList, List<String> startDateList, List<String> marketplaceStartDateList) {
        List<OdsWeekSearchTermsAnalysis> resultList = new ArrayList<>();
        List<String> distinctSearchTerms = searchTerms.stream().map(String::toLowerCase).distinct().collect(Collectors.toList());
        for (List<String> subSearchTerms : Lists.partition(distinctSearchTerms, 9000)) {
            resultList.addAll(weekSearchTermsAnalysisDao.queryRanks(subSearchTerms, marketplaceIdList, startDateList, marketplaceStartDateList));
        }
        return resultList;
    }

    public List<SearchTermsListTrendsVo> getTrend(SearchTermsAnalysisTrendQo param) {
        //获取13周
        Set<String> set = getLastWeeks(param.getEndDate(), 12);
        String minDay = set.stream().min(Comparator.naturalOrder()).get().split("#")[0];
        List<OdsWeekSearchTermsAnalysis> list = weekSearchTermsAnalysisDao.getTrend(param, minDay);
        Map<String, List<OdsWeekSearchTermsAnalysis>> map = list.stream().collect(Collectors.groupingBy(report ->
                report.getMarketplaceId() + "###" + report.getSearchTerm()));
        List<SearchTermsListTrendsVo> resultList = new ArrayList<>();
        map.forEach((key, value) -> {
            String[] arr = key.split("###", -1);
            String marketplaceId = arr[0];
            String searchTerm = arr[1];
            SearchTermsListTrendsVo vo = new SearchTermsListTrendsVo();
            vo.setSearchTerm(searchTerm);
            vo.setMarketplaceId(marketplaceId);
            List<SearchTermsListTrendsVo.SearchTermsTrendsVo> trendList = new ArrayList<>();
            Map<String, Integer> trendMap = value.stream().collect(Collectors.toMap(report ->
                            DateUtil.dateToStrWithTime(report.getStartDate(), DateUtil.PATTERN) + "#" + DateUtil.dateToStrWithTime(report.getEndDate(), DateUtil.PATTERN),
                    OdsWeekSearchTermsAnalysis::getSearchFrequencyRank, (report1, report2) -> report1));
            set.forEach(item -> {
                //否则50万名
                // Integer rank = trendMap.get(item) != null ? trendMap.get(item) : 500000;
                Integer rank = trendMap.get(item);
                SearchTermsListTrendsVo.SearchTermsTrendsVo trendVo = new SearchTermsListTrendsVo.SearchTermsTrendsVo();
                trendVo.setStartDate(item.split("#")[0]);
                trendVo.setEndDate(item.split("#")[1]);
                trendVo.setSearchFrequencyRank(rank);
                trendVo.setWeekStr(dealWeek(item.split("#")[1]));
                trendList.add(trendVo);
            });
            trendList.sort(Comparator.comparing(SearchTermsListTrendsVo.SearchTermsTrendsVo::getStartDate));
            vo.setList(trendList);
            resultList.add(vo);
        });
        return resultList;
    }

    private Set<String> getLastWeeks(String endStr, int num) {
        Set<String> set = new TreeSet<>();
        LocalDate endDay = LocalDate.parse(endStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        for (int i = 0; i <= num; i++) {
            LocalDate last = endDay.minusDays(7 * i);
            String startDate = last.minusDays(endDay.getDayOfWeek().getValue()).toString();
            String endDate = last.plusDays(6 - endDay.getDayOfWeek().getValue()).toString();
            set.add(startDate + "#" + endDate);
        }
        return set;
    }

    private String dealWeek(String day) {
        WeekFields weekFields = WeekFields.of(Locale.getDefault());
        LocalDate startDay = LocalDate.parse(day, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        int num = startDay.get(weekFields.weekOfYear());
        return day.substring(2, 4) + "年第" + num + "周";
    }

}
