package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdTargetTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTask;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-07 13:25
 */
@Repository
@Slf4j
public class AdTargetTaskDaoImpl extends AdBaseDaoImpl<AdTargetTask> implements IAdTargetTaskDao {
    @Override
    public Page<AdTargetTask> getPageByPuidAfterTargetDate(int puid, int sourceShopId, int pageNo, int pageSize, int targetPageType, String sourceAdCampaignId, Date date, int uid) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select * from ");
        sql.append(getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("select count(*) from ");
        countSql.append(getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid=? and target_page_type=? and source_ad_campaign_id=? ");
        argsList.add(puid);
        argsList.add(targetPageType);
        argsList.add(StringUtils.defaultString(sourceAdCampaignId));
//        if (sourceShopId > 0) {
//            whereSql.append("and source_shop_id=? ");
//            argsList.add(sourceShopId);
//        }
        if (uid > 0) {
            whereSql.append(" and uid = ? ");
            argsList.add(uid);
        }
        whereSql.append("and create_time>=? ");
        argsList.add(date);

        sql.append(whereSql);
        countSql.append(whereSql);
        sql.append(" order by create_time desc ");

        Object[] args = argsList.toArray();
        return this.getPageResult(pageNo, pageSize, countSql.toString(), args, sql.toString(), args, AdTargetTask.class);
    }

    @Override
    public List<AdTargetTask> getByPuidAndIds(int puid, List<Long> taskIds) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.in("id", taskIds.toArray());
        return listByCondition(conditionBuilder.build());
    }

    @Override
    public List<AdTargetTask> getListBeforeTargetDate(Date date) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.lessThanOrEqualTo("create_time", date);
        return listByCondition(conditionBuilder.build());
    }

    @Override
    public void deleteByIds(List<Long> ids) {
        StringBuilder sql = new StringBuilder("delete from ").append(getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where ");
        List<Object> argsList = Lists.newArrayList();
        whereSql.append(SqlStringUtil.dealInListNotAnd("id", ids, argsList));
        sql.append(whereSql);

        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AdTargetTask> getUnfinishedListBeforeTargetDate(Date date) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.in("status", new Object[]{AdTargetTaskStatusEnum.WAITING.getCode(), AdTargetTaskStatusEnum.RUNNING.getCode()});
        conditionBuilder.lessThanOrEqualTo("create_time", date);
        return listByCondition(conditionBuilder.build());
    }

    @Override
    public AdTargetTask getByPuid(int puid, long taskId) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("id", taskId);

        return getByCondition(conditionBuilder.build());
    }

    @Override
    public void updateStatus(int puid, long taskId, int status) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("update ").append(getJdbcHelper().getTable()).append(" set status=?,update_time=now() ");
        argsList.add(status);

        StringBuilder whereSql = new StringBuilder(" where puid=? and id=? ");
        argsList.add(puid);
        argsList.add(taskId);

        AdTargetTaskStatusEnum adTargetTaskStatusEnum = AdTargetTaskStatusEnum.findByCode(status);
        if (adTargetTaskStatusEnum == null) {
            log.warn("invalid request.puid:{} taskId:{} status:{}", puid, taskId, status);
            return;
        }
        // 防止状态回退
        whereSql.append(SqlStringUtil.dealInList("status", adTargetTaskStatusEnum.getValidPreStatus(), argsList));
        sql.append(whereSql);

        getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }
}
