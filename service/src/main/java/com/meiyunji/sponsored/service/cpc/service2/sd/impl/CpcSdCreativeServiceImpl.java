package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.assets.entity.assetsGet.AssetVersionList;
import com.amazon.advertising.assets.entity.assetsGet.GetAssetResult;
import com.amazon.advertising.assets.entity.assetsRegister.RegisterAssetResult;
import com.amazon.advertising.assets.entity.assetsUpload.UploadAssetResult;
import com.amazon.advertising.assets.enums.AssetSubTypeEnum;
import com.amazon.advertising.assets.enums.AssetTypeEnum;
import com.amazon.advertising.sd.entity.creatives.CreativesResult;
import com.amazon.advertising.sd.entity.creatives.PreviewResult;
import com.amazon.advertising.sd.mode.creative.Creative;
import com.amazon.advertising.sd.mode.creative.PreviewConfiguration;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.rpc.sd.upload.AssetProperties;
import com.meiyunji.sponsored.rpc.sd.upload.CroppingCoordinates;
import com.meiyunji.sponsored.rpc.sd.upload.ListCreativeResult;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdCreativeDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdCreative;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.assets.CpcAssetsApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdCreativeService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.assets.ImageAssetVo;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SDVideoCheckApprovedProgramsEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;


/**
 * Created by lm on 2021/7/31.
 */
@Service
@Slf4j
public class CpcSdCreativeServiceImpl implements ICpcSdCreativeService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSdCreativeApiService cpcSdCreativeApiService;
    @Autowired
    private IAmazonAdSdCreativeDao amazonAdSdCreativeDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private CpcAssetsApiService cpcAssetsApiService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    @Override
    public Result<ImageAssetVo> uploadImage(Integer puid, Integer shopId, String fileName, String imageType, byte[] bytes) {
        return null;
    }

    @Override
    public Result<ImageAssetVo> uploadVideo(Integer puid, Integer shopId, String fileName, String videoType, byte[] bytes) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<UploadAssetResult> urlResult = cpcAssetsApiService.getAssetUploadUrl(shop, profile, fileName);
        if (urlResult.error()) return ResultUtil.returnErr("网络超时,请稍后重试."+urlResult.getMsg());
        String uploadUrl = urlResult.getData().getUrl();
        Result<?> uploadResult = cpcAssetsApiService.uploadAsset(shop, uploadUrl, bytes, videoType);
        if (uploadResult.error()) return ResultUtil.returnErr("网络超时,请稍后重试."+urlResult.getMsg());
        Result<RegisterAssetResult> registerResult = cpcAssetsApiService.registerAsset(shop, profile, fileName, uploadUrl, AssetTypeEnum.VIDEO.name(),
                Collections.singletonList(AssetSubTypeEnum.BACKGROUND_VIDEO.name()), Collections.singletonList(CampaignTypeEnum.sd.getCampaignType()), null, null);
        if (registerResult.error()) return ResultUtil.returnErr("网络超时,请稍后重试."+registerResult.getMsg());
        RegisterAssetResult data = registerResult.getData();
        ImageAssetVo assetVo = new ImageAssetVo();
        assetVo.setAssetId(data.getAssetId());
        assetVo.setVersionId(data.getVersionId());
        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, profile, assetVo.getAssetId(), assetVo.getVersionId());
        if (assetResult.error()) return ResultUtil.returnSucc(assetVo);

        GetAssetResult asset;
        List<AssetVersionList> versions;
        if ((asset = assetResult.getData()) != null && CollectionUtils.isNotEmpty(versions = asset.getAssetVersionList())) {
            assetVo.setAssetId(asset.getAssetGlobal().getAssetId());
            AssetVersionList version = versions.get(0);

            //校验视频上传后的标签，检查是否符合创建SBV的要求
            List<String> checkApprovedList = version.getSpecCheckApprovedPrograms();
            if (CollectionUtils.isEmpty(checkApprovedList)) {
                return ResultUtil.returnErr("亚马逊上传请求超时，请稍后重试");
            }
            if (checkApprovedList.stream().noneMatch(approved ->
                    SDVideoCheckApprovedProgramsEnum.SPONSORED_DISPLAY_VIDEO.getApproved().equals(approved))) {
                return ResultUtil.returnErr("视频格式不符合要求，请检查后重新上传");
            }
            assetVo.setName(version.getName());
            assetVo.setUrl(version.getUrl());
            assetVo.setMediaType(version.getFileMetadata().getContentType());
            assetVo.setHeight(version.getFileMetadata().getHeight());
            assetVo.setWidth(version.getFileMetadata().getWidth());
        }
        return ResultUtil.returnSucc(assetVo);
    }

    @Override
    public Result<UploadAssetResult> creativeCreate(Integer puid, Integer shopId, String fileName) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<UploadAssetResult> resultResult = cpcAssetsApiService.getAssetUploadUrl(shop, profile,fileName);
        if (resultResult.success()) {
            return ResultUtil.returnSucc(resultResult.getData());
        }
        return ResultUtil.returnErr(resultResult.getMsg());
    }

    @Override
    public Result<CreativesResult> sdCreativeCreateNew(SdCreativeVo sdCreativeVo) {
        int puid = sdCreativeVo.getPuid();
        int shopId = sdCreativeVo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);

        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<CreativesResult> result = cpcSdCreativeApiService.createCreative(shop, profile, convertToCreativeList(sdCreativeVo));
        logSdCreativeGet(sdCreativeVo, profile, result);
        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }
        CreativesResult creativeResult = (CreativesResult) result.getData();
        String creativeId = creativeResult.getCreativeId().toString();

        String errMsg = null;
        // 入库
        try {
            errMsg = save(sdCreativeVo, creativeResult, profile);
        } catch (Exception e) {
            log.error("createCampaign save fail:", e);
        }
        // 创建成功 同步这个创意
        cpcAdSyncService.syncSdCreativeByShop(shop, null, creativeId);
        if (StringUtils.isNotBlank(errMsg)) {
            return ResultUtil.returnErr(errMsg);
        }
        return ResultUtil.returnSucc(creativeResult);
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> sdCreativeCreateNew(SdCreativeVo sdCreativeVo, String campaignId,
                                                                        String adGroupId, ShopAuth shop,
                                                                        AmazonAdProfile profile, Integer uid,
                                                                        String loginId) {
        Result<CreativesResult> result = cpcSdCreativeApiService.createCreative(shop, profile, convertToCreativeList(sdCreativeVo));
        logSdCreativeGet(sdCreativeVo, profile, result);
        if (!result.success()) {
            log.info("invoke remote amazon api error:{}", result);
            throw new ServiceException(StringUtils.isEmpty(result.getMsg()) ? "提交亚马逊异常" : result.getMsg());
        }
        CreativesResult creativeResult = result.getData();
        String creativeId = creativeResult.getCreativeId();

        String errMsg;
        // 入库
        try {
            errMsg = save(sdCreativeVo, creativeResult, profile);
        } catch (Exception e) {
            log.error("create creatives save fail:", e);
            throw new ServiceException("保存广告创意异常");
        }
        // 创建成功 同步这个创意
        cpcAdSyncService.syncSdCreativeByShop(shop, null, creativeId);
        if (StringUtils.isNotBlank(errMsg)) {
            throw new ServiceException(errMsg);
        }

        return NewCreateResultResultVo.<SBCommonErrorVo>builder()
                .campaignId(campaignId)
                .adGroupId(adGroupId)
                .creativeIds(Collections.singletonList(creativeId))
                .build();
    }

    @Override
    public Result<RegisterAssetResult> creativeComplete(Integer puid, Integer shopId, String fileName, String uploadLocation, String assetType, List<String> assetSubTypeList, List<String> tags) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result<RegisterAssetResult> resultResult = cpcAssetsApiService.registerAsset(shop, profile, fileName, uploadLocation, assetType, assetSubTypeList, tags, null, null);
        if (resultResult.success()) {
            return ResultUtil.returnSucc(resultResult.getData());
        }
        return ResultUtil.returnErr(resultResult.getMsg());
    }

    @Override
    public Result<CreativesResult> getSdCreative(SdCreativeVo sdCreativeVo) {
        int puid = sdCreativeVo.getPuid();
        int shopId = sdCreativeVo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);

        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<CreativesResult> result = cpcSdCreativeApiService.createCreative(shop, profile, convertToCreativeList(sdCreativeVo));
        logSdCreativeGet(sdCreativeVo, profile, result);
        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }
        CreativesResult creativeResult = (CreativesResult) result.getData();
        String creativeId = creativeResult.getCreativeId().toString();

        String errMsg = null;
        // 入库
        try {
            errMsg = save(sdCreativeVo, creativeResult, profile);
        } catch (Exception e) {
            log.error("createCampaign save fail:", e);
        }
        // 创建成功 同步这个创意
        cpcAdSyncService.syncSdCreativeByShop(shop, null, creativeId);
        if (StringUtils.isNotBlank(errMsg)) {
            return ResultUtil.returnErr(errMsg);
        }
        return ResultUtil.returnSucc(creativeResult);
    }

    private void logSdCreativeGet(SdCreativeVo vo, AmazonAdProfile profile, Result<CreativesResult> result) {
        try {
            Integer puid = vo.getPuid();
            Integer shopId = profile.getShopId();
            String adGroupId = vo.getAdGroupId();

            AmazonAdSdCreative sdCreative = new AmazonAdSdCreative();
            sdCreative.setPuid(puid);
            sdCreative.setShopId(shopId);
            sdCreative.setMarketplaceId(profile.getMarketplaceId());
            sdCreative.setAdGroupId(adGroupId);
            AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, adGroupId);
            if (Objects.nonNull(amazonSdAdGroup)) {
                sdCreative.setCampaignId(amazonSdAdGroup.getCampaignId());
            }
            if (Objects.nonNull(result.getData())){
                sdCreative.setCreativeId(result.getData().getCreativeId());
            }
            sdCreative.setCreateId(vo.getUid());
            if (Objects.nonNull(vo.getProperties())) {
                sdCreative.setCreativeProperties(JSONUtil.objectToJson(vo.getProperties()));
            }

            List<AdManageOperationLog> operationLogs = adManageOperationLogService.getSdCreativeLogs(null, sdCreative);
            operationLogs.forEach(item -> {
                item.setIp(vo.getLoginIp());
                if (result.success()) {
                    item.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    item.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    item.setResultInfo(result.getMsg());
                }
            });
            adManageOperationLogService.printAdOperationLog(operationLogs);

        } catch (Exception e) {
            log.error("logSdCreativeGet error", e);
        }
    }

    @Override
    public Result<CreativesResult> updateSdCreative(SdCreativeVo sdCreativeVo) {
        int puid = sdCreativeVo.getPuid();
        int shopId = sdCreativeVo.getShopId();
        String creativeId = sdCreativeVo.getCreativeId();

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonAdSdCreative sdCreative = amazonAdSdCreativeDao.getByCreativeId(puid, shopId,profile.getMarketplaceId(), creativeId);
        if (sdCreative == null) {
            return ResultUtil.error("该广告组没有创意信息");
        }

        Result<CreativesResult> result = cpcSdCreativeApiService.updateCreative(shop, profile, convertToCreativeList(sdCreativeVo));
        logSdCreativeUpdate(sdCreative, sdCreativeVo, result);
        if (result.success()) {
            BeanUtils.copyProperties(sdCreativeVo, sdCreative);
            sdCreative.setCreativeProperties(JSONUtil.objectToJson(sdCreativeVo.getProperties()));
            sdCreative.setUpdateId(sdCreativeVo.getUid());
            amazonAdSdCreativeDao.updateByIdAndPuid(sdCreativeVo.getPuid(), sdCreative);
            // 修改成功 同步这个创意
            cpcAdSyncService.syncSdCreativeByShop(shop, null, creativeId);
            return result;
        }

        return ResultUtil.error(result.getMsg());
    }

    private void logSdCreativeUpdate(AmazonAdSdCreative oldSdCreative, SdCreativeVo vo, Result<CreativesResult> result) {
        try {
            AmazonAdSdCreative sdCreative = new AmazonAdSdCreative();
            BeanUtils.copyProperties(oldSdCreative, sdCreative);
            sdCreative.setCreateId(vo.getUid());
            sdCreative.setUpdateId(vo.getUid());
            if (Objects.nonNull(vo.getProperties())) {
                sdCreative.setCreativeProperties(JSONUtil.objectToJson(vo.getProperties()));
            }
            AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(vo.getPuid(), vo.getShopId(), oldSdCreative.getAdGroupId());
            if (Objects.nonNull(amazonSdAdGroup)) {
                sdCreative.setCampaignId(amazonSdAdGroup.getCampaignId());
            }

            List<AdManageOperationLog> operationLogs = adManageOperationLogService.getSdCreativeLogs(oldSdCreative, sdCreative);
            operationLogs.forEach(item -> {
                item.setIp(vo.getLoginIp());
                if (result.success()) {
                    item.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    item.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    item.setResultInfo(result.getMsg());
                }
            });
            adManageOperationLogService.printAdOperationLog(operationLogs);
        } catch (Exception e) {
            log.error("logSdCreativeUpdate error", e);
        }
    }

    @Override
    public Result<List<AmazonAdSdCreative>> listCreative(SdCreativePageParam param) {
        int puid = param.getPuid();
        int shopId = param.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        List<AmazonAdSdCreative> list = amazonAdSdCreativeDao.getList(puid, param);

        return ResultUtil.success(list);
    }

    @Override
    public Result<List<ListCreativeResult>> listCreativeNew(SdCreativePageParam param) {
        int puid = param.getPuid();
        int shopId = param.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        List<AmazonAdSdCreative> list = amazonAdSdCreativeDao.getList(puid, param);
        //需要对Asset进行循环，调用亚马逊接口获取其对应的url
        return ResultUtil.success(packageRpcAssetProperties(shop, profile, list));
    }

    @Override
    public Result<PreviewResult> previewCreative(SdCreativeVo sdCreativeVo, SdCreativePreviewConfigVo configVo) {
        int puid = sdCreativeVo.getPuid();
        int shopId = sdCreativeVo.getShopId();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        PreviewConfiguration configuration = new PreviewConfiguration();
        BeanUtils.copyProperties(configVo, configuration);
        Result<PreviewResult> resultResult = cpcSdCreativeApiService.previewCreative(shop, profile, convertToCreative(sdCreativeVo), configuration);
        if (resultResult.success()) {
            return ResultUtil.returnSucc(resultResult.getData());
        }
        return ResultUtil.returnErr(resultResult.getMsg());
    }

    private List<Creative> convertToCreativeList(SdCreativeVo creativeVo) {
        List<Creative> creativeList = Lists.newArrayList();
        Creative creative = new Creative();
        BeanUtils.copyProperties(creativeVo, creative);
        if (creativeVo.getState() != null) {
            creative.setModerationStatus(creativeVo.getState());
        }
        creativeList.add(creative);
        return creativeList;
    }

    private Creative convertToCreative(SdCreativeVo creativeVo) {
        Creative creative = new Creative();
        BeanUtils.copyProperties(creativeVo, creative);
        if (creativeVo.getState() != null) {
            creative.setModerationStatus(creativeVo.getState());
        }
        return creative;
    }

    private String save(SdCreativeVo vo, CreativesResult creativeResult, AmazonAdProfile profile) {
        String errMsg = "";
        int puid = vo.getPuid();
        int shopId = profile.getShopId();
        String marketplaceId = profile.getMarketplaceId();
        String profileId = profile.getProfileId();
        String creativeId = creativeResult.getCreativeId().toString();

        AmazonAdSdCreative sdCreative = new AmazonAdSdCreative();
        sdCreative.setPuid(puid);
        sdCreative.setShopId(shopId);
        sdCreative.setMarketplaceId(marketplaceId);
        sdCreative.setProfileId(profileId);
        sdCreative.setCreativeId(creativeId);
        sdCreative.setCreativeType("IMAGE");
        if (vo.getProperties() != null) {
            sdCreative.setCreativeProperties(JSONUtil.objectToJson(vo.getProperties()));
        }

        if (StringUtils.isNotBlank(vo.getAdGroupId())) {
            sdCreative.setAdGroupId(vo.getAdGroupId());
        }

        sdCreative.setCreateId(vo.getUid());
        sdCreative.setCreateInAmzup(Constants.CREATE_IN_AMZUP);

        // 创意和着陆页 以同步过来的数据为准
        try {
            amazonAdSdCreativeDao.save(puid, sdCreative);
        } catch (Exception e) {
            log.error("create creative fail:", e);
            errMsg += "create creative fail:" + e.getMessage().toString();
        }
        return errMsg;
    }

    private List<ListCreativeResult> packageRpcAssetProperties(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonAdSdCreative> creativeList) {
        if (CollectionUtils.isEmpty(creativeList)) {
            return null;
        }
        List<ListCreativeResult> resultList = new ArrayList<>();
        for (AmazonAdSdCreative creative : creativeList) {
            if (Objects.isNull(creative)) {
                continue;
            }
            ListCreativeResult.Builder vo = ListCreativeResult.newBuilder();
            vo.setId(Int64Value.of(creative.getId()));
            vo.setShopId(Int32Value.of(creative.getShopId()));
            vo.setPuid(Int32Value.of(creative.getPuid()));
            vo.setAdGroupId(creative.getAdGroupId());
            vo.setCreativeId(creative.getCreativeId());
            Optional.ofNullable(creative.getCreativeType()).filter(StringUtils::isNotEmpty).ifPresent(vo::setCreativeType);
            Optional.ofNullable(creative.getState()).filter(StringUtils::isNotEmpty).ifPresent(vo::setState);
            String creativeProperties = creative.getCreativeProperties();
            if (StringUtils.isNotEmpty(creativeProperties)) {
                com.amazon.advertising.sd.mode.creative.CreativeProperties properties = JSONUtil.jsonToObject(creativeProperties, com.amazon.advertising.sd.mode.creative.CreativeProperties.class);
                com.meiyunji.sponsored.rpc.sd.upload.CreativeProperties.Builder rpcProperties = com.meiyunji.sponsored.rpc.sd.upload.CreativeProperties.newBuilder();
                if (Objects.nonNull(properties) && StringUtils.isNotBlank(properties.getHeadline())) {
                    rpcProperties.setHeadline(properties.getHeadline());
                }
                if (Objects.nonNull(properties) && properties.getBrandLogo() != null) {
                    com.amazon.advertising.sd.mode.creative.AssetProperties brandLogo = properties.getBrandLogo();
                    AssetProperties.Builder rpcBrandLogo = AssetProperties.newBuilder();
                    rpcBrandLogo.setAssetId(brandLogo.getAssetId());
                    rpcBrandLogo.setAssetVersion(brandLogo.getAssetVersion());
                    if (StringUtils.isNotEmpty(brandLogo.getAssetId())) {
                        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, amazonAdProfile, brandLogo.getAssetId(),
                                Optional.ofNullable(brandLogo.getAssetVersion()).filter(StringUtils::isNotEmpty).orElse(null));
                        Optional.ofNullable(assetResult).map(Result::getData)
                                .map(GetAssetResult::getAssetVersionList)
                                .filter(CollectionUtils::isNotEmpty)
                                .map(l -> l.get(0)).map(AssetVersionList::getUrl)
                                .ifPresent(rpcBrandLogo::setUrl);
                    }

                    com.amazon.advertising.sd.mode.creative.CroppingCoordinates brandLogoCord = brandLogo.getCroppingCoordinates();
                    if (brandLogoCord != null) {
                        CroppingCoordinates.Builder rpcBrandLogoCord = CroppingCoordinates.newBuilder();
                        rpcBrandLogoCord.setTop(Int32Value.of(brandLogoCord.getTop()));
                        rpcBrandLogoCord.setWidth(Int32Value.of(brandLogoCord.getWidth()));
                        rpcBrandLogoCord.setLeft(Int32Value.of(brandLogoCord.getLeft()));
                        rpcBrandLogoCord.setHeight(Int32Value.of(brandLogoCord.getHeight()));
                        rpcBrandLogo.setCroppingCoordinates(rpcBrandLogoCord);
                    }
                    rpcProperties.setBrandLogo(rpcBrandLogo);
                }
                if (Objects.nonNull(properties) && properties.getRectCustomImage() != null) {
                    com.amazon.advertising.sd.mode.creative.AssetProperties rectCustomImage = properties.getRectCustomImage();
                    AssetProperties.Builder rpcRectCustom = AssetProperties.newBuilder();
                    rpcRectCustom.setAssetId(rectCustomImage.getAssetId());
                    rpcRectCustom.setAssetVersion(rectCustomImage.getAssetVersion());
                    if (StringUtils.isNotEmpty(rectCustomImage.getAssetId())) {
                        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, amazonAdProfile, rectCustomImage.getAssetId(),
                                Optional.ofNullable(rectCustomImage.getAssetVersion()).filter(StringUtils::isNotEmpty).orElse(null));
                        Optional.ofNullable(assetResult).map(Result::getData)
                                .map(GetAssetResult::getAssetVersionList)
                                .filter(CollectionUtils::isNotEmpty)
                                .map(l -> l.get(0)).map(AssetVersionList::getUrl)
                                .ifPresent(rpcRectCustom::setUrl);
                    }

                    com.amazon.advertising.sd.mode.creative.CroppingCoordinates rectCustomCord = rectCustomImage.getCroppingCoordinates();
                    if (rectCustomCord != null) {
                        CroppingCoordinates.Builder rpcRectCustomCord = CroppingCoordinates.newBuilder();
                        rpcRectCustomCord.setTop(Int32Value.of(rectCustomCord.getTop()));
                        rpcRectCustomCord.setWidth(Int32Value.of(rectCustomCord.getWidth()));
                        rpcRectCustomCord.setLeft(Int32Value.of(rectCustomCord.getLeft()));
                        rpcRectCustomCord.setHeight(Int32Value.of(rectCustomCord.getHeight()));
                        rpcRectCustom.setCroppingCoordinates(rpcRectCustomCord);
                    }
                    rpcProperties.setRectCustomImage(rpcRectCustom);
                }

                if (Objects.nonNull(properties) && properties.getSquareCustomImage() != null) {
                    com.amazon.advertising.sd.mode.creative.AssetProperties squareCustomImage = properties.getSquareCustomImage();
                    AssetProperties.Builder rpcSquareCustom = AssetProperties.newBuilder();
                    rpcSquareCustom.setAssetId(squareCustomImage.getAssetId());
                    rpcSquareCustom.setAssetVersion(squareCustomImage.getAssetVersion());
                    if (StringUtils.isNotEmpty(squareCustomImage.getAssetId())) {
                        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, amazonAdProfile, squareCustomImage.getAssetVersion(),
                                Optional.ofNullable(squareCustomImage.getAssetVersion()).filter(StringUtils::isNotEmpty).orElse(null));
                        Optional.ofNullable(assetResult).map(Result::getData)
                                .map(GetAssetResult::getAssetVersionList)
                                .filter(CollectionUtils::isNotEmpty)
                                .map(l -> l.get(0)).map(AssetVersionList::getUrl)
                                .ifPresent(rpcSquareCustom::setUrl);
                    }

                    com.amazon.advertising.sd.mode.creative.CroppingCoordinates squareCustomCord = squareCustomImage.getCroppingCoordinates();
                    if (squareCustomCord != null) {
                        CroppingCoordinates.Builder rpcSquareCustomCord = CroppingCoordinates.newBuilder();
                        rpcSquareCustomCord.setTop(Int32Value.of(squareCustomCord.getTop()));
                        rpcSquareCustomCord.setWidth(Int32Value.of(squareCustomCord.getWidth()));
                        rpcSquareCustomCord.setLeft(Int32Value.of(squareCustomCord.getLeft()));
                        rpcSquareCustomCord.setHeight(Int32Value.of(squareCustomCord.getHeight()));
                        rpcSquareCustom.setCroppingCoordinates(rpcSquareCustomCord);
                    }
                    rpcProperties.setSquareCustomImage(rpcSquareCustom);
                }

                //视频创意素材
                if (Objects.nonNull(properties) && Objects.nonNull(properties.getVideo())) {
                    com.amazon.advertising.sd.mode.creative.AssetProperties videoProperties = properties.getVideo();
                    AssetProperties.Builder video = AssetProperties.newBuilder();
                    video.setAssetId(videoProperties.getAssetId());
                    video.setAssetVersion(videoProperties.getAssetVersion());
                    if (StringUtils.isNotEmpty(videoProperties.getAssetId())) {
                        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, amazonAdProfile, videoProperties.getAssetId(),
                                Optional.ofNullable(videoProperties.getAssetVersion()).filter(StringUtils::isNotEmpty).orElse(null));
                        Optional.ofNullable(assetResult).map(Result::getData)
                                .map(GetAssetResult::getAssetVersionList)
                                .filter(CollectionUtils::isNotEmpty)
                                .map(l -> l.get(0)).map(AssetVersionList::getUrl)
                                .ifPresent(video::setUrl);
                    }

                    rpcProperties.setVideo(video.build());
                }
                vo.setProperties(rpcProperties);
            }
            resultList.add(vo.build());
        }
        return resultList;
    }
}
