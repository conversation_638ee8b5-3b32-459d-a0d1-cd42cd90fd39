package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbGroupReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbGroupReport;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTypeDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * sb广告组报告(OdsAmazonAdSbGroupReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
@Repository
@Slf4j
public class OdsAmazonAdSbGroupReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdSbGroupReport> implements IOdsAmazonAdSbGroupReportDao {

    @Override
    public List<DashboardAdTypeDataDto> queryAdTypeCharts(Integer puid,
                                                          List<String> marketplaceIdList,
                                                          List<Integer> shopIdList,
                                                          String currency,
                                                          String startDate,
                                                          String endDate, List<String> siteToday, Boolean isSiteToday,
                                                          List<String> portfolioIds, List<String> campaignIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        sql.append("select g.ad_format groupType, g.ad_group_type targetType, ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ");
        sql.append("ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ifnull(sum(conversions14d), 0) orderNum, ifnull(sum(units_sold14d), 0) saleNum ");
        sql.append("from ").append(getJdbcHelper().getTable()).append("  report ");

        sql.append(" join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m ");
        sql.append(" on c.`from` = m.currency and c.puid= ? and `to` = ? and month >= ? and month <= ? ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and m.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        sql.append(" ) c on report.count_month = c.month and report.marketplace_id = c.marketplace_id ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));

        sql.append("join ods_t_amazon_ad_group_sb g on report.ad_group_id = g.ad_group_id ");
        sql.append(" and g.puid = ? ");
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(" and g.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append(" and g.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        sql.append("and report.puid = ? and g.ad_format in ('productCollection', 'video', 'storeSpotlight') ");
        sql.append("and g.ad_group_type in ('keyword', 'product') ");
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        argsList.add(puid);
        argsList.add(puid);


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sql.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }


        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sql.append(" where report.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }

        sql.append(" group by g.ad_format, g.ad_group_type ");

        log.info(sql.toString());
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(DashboardAdTypeDataDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTopDataDto> queryAdSbGroupYoyOrMomTop(String subSqlA, String subSqlB,
                                                                 List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                 DashboardOrderByRateEnum orderField, String orderBy,
                                                                 int limit, Boolean noZero) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.ad_group_id as groupId, ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.ad_group_id = subSqlB.ad_group_id ");
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" where " + getColumnSelect(dataField.getCode()) );
        }
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (limit == 0) {
            limit = 10;
        }
        sb.append(" LIMIT ").append(limit);
        return getJdbcTemplate().query(sb.toString(), new BeanPropertyRowMapper<>(DashboardAdTopDataDto.class), argsList.toArray());
    }

    @Override
    public List<CampaignOrGroupOrPortfolioDto> queryAdSbGroupCharts(Integer puid, List<Integer> shopIdList,
                                                                    List<String> marketplaceIdList, List<String> groupIdList,
                                                                    String currency, String startDate,
                                                                    String endDate) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = sbGroupQuerySql(puid, shopIdList, marketplaceIdList, groupIdList, currency, startDate, endDate, argsList, null, null, null, null, null, null);
        return getJdbcTemplate().query(querySql, new BeanPropertyRowMapper<>(CampaignOrGroupOrPortfolioDto.class), argsList.toArray());
    }

    @Override
    public String sbGroupQuerySql(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, List<String> groupIdList, String currency, String startDate, String endDate, List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT report.ad_group_id, ");
        sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(sales14d * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(impressions), 0) impressions, ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(conversions14d), 0) orderNum, ");
        sb.append(" ifnull(sum(units_sold14d), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(sales14d * c.rate), 0), 2), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(sales14d * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 2), 0) roas, ");
        sb.append(" ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 2) clickRate, ");//点击率
        sb.append(" ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 2) conversionRate, ");//转化率
        sb.append(" ROUND(ifnull(sum(cost * c.rate)/ sum(impressions), 0), 2) cpc, ");//cpc
        sb.append(" ROUND(ifnull(sum(cost * c.rate)/ sum(conversions14d), 0), 2) cpa ");//cpa
        sb.append(" from ").append(getJdbcHelper().getTable()).append("  report ");
        sb.append(" join (select * from dim_currency_rate where puid = ? and `to` = ? ) c on report.puid = c.puid and DATE_FORMAT(report.count_date, '%Y%m') = c.month ");
        sb.append(" join dim_marketplace_info m on m.marketplace_id = report.marketplace_id and c.`from` = m.currency ");
        sb.append(" where report.puid = ? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            sb.append("and report.ad_group_id in ('").append(StringUtils.join(groupIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }

        sb.append(" group by report.ad_group_id");
        return sb.toString();
    }

    /**
     * 用户排除为0 的字段处理
     *
     * @param orderByField
     * @return
     */
    private String getColumnSelect(String orderByField) {

        switch (orderByField) {
            case "impressions":
                return " ifnull(subSqlA.impressions, 0) <> 0 ";
            case "clicks":
                return " ifnull(subSqlA.clicks, 0) <> 0 ";
            case "cost":
                return " ifnull(subSqlA.cost, 0) <> 0 ";
            case "roas":
                return " ifnull(subSqlA.roas, 0) <> 0 ";
            case "acos":
                return " ifnull(subSqlA.acos, 0) <> 0 ";
            case "clickRate":
                return " ifnull(subSqlA.clickRate, 0) <> 0 ";
            case "conversionRate":
                return " ifnull(subSqlA.conversionRate, 0.0) <> 0 ";
            case "cpc":
                return " ifnull(subSqlA.cpc, 0) <> 0 ";
            case "cpa":
                return " ifnull(subSqlA.cpa, 0) <> 0 ";
            case "totalSales":
                return " ifnull(subSqlA.totalSales, 0) <> 0 ";
            case "orderNum":
                return " ifnull(subSqlA.orderNum, 0) <> 0 ";
            case "saleNum":
                return " ifnull(subSqlA.saleNum, 0) <> 0 ";
            default:
                return orderByField + " <> 0 ";
        }
    }
}

