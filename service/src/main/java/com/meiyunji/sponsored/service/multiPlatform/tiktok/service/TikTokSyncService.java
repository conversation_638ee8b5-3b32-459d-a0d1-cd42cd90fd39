package com.meiyunji.sponsored.service.multiPlatform.tiktok.service;

import com.alibaba.fastjson2.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sellfox.core.exception.BizServiceException;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.client.GmvMaxApiClient;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.*;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxCampaignSyncInfo;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.TikTokSyncRecoveryParam;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreInfo;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokSyncErrorRecord;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GmvMaxCampaignGetReq;
import com.tiktok.advertising.model.gmv_max.GmvMaxCampaign;
import com.tiktok.advertising.model.gmv_max.GmvMaxCampaignReport;
import com.tiktok.advertising.model.gmv_max.Identity;
import com.tiktok.advertising.model.gmv_max.ReportMetrics;
import com.tiktok.advertising.model.gmv_max.request.FilteringGmvMaxCampaignGet;
import com.tiktok.advertising.model.gmv_max.request.FilteringGmvMaxCampaignReport;
import com.tiktok.advertising.model.gmv_max.response.GmvMaxCampaignGetResponse;
import com.tiktok.advertising.model.gmv_max.response.GmvMaxCampaignInfo;
import com.tiktok.advertising.model.gmv_max.response.GmvMaxCampaignReportResponse;
import com.tiktok.advertising.model.gmv_max.response.IdentityGetResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class TikTokSyncService {

    private static final String INIT_GMV_MAX_CAMPAIGN = "initGmvMaxCampaign";
    private static final String INIT_GMV_MAX_CAMPAIGN_INFO = "initGmvMaxCampaignInfo";
    private static final String INIT_GMV_MAX_CAMPAIGN_REPORT = "initGmvMaxCampaignReport";

    private static final String GMV_MAX_CAMPAIGN = "gmvMaxCampaign";
    private static final String GMV_MAX_CAMPAIGN_INFO = "gmvMaxCampaignInfo";
    private static final String GMV_MAX_CAMPAIGN_REPORT = "gmvMaxCampaignReport";

    // 0待处理,1处理中,2处理完成
    private static final int STATUS_PENDING = 0;
    private static final int STATUS_PROCESSING = 1;
    private static final int STATUS_COMPLETED = 2;

    // 推广系列一级状态
    private static final List<String> PRIMARY_STATUS = Lists.newArrayList("STATUS_DELIVERY_OK", "STATUS_DISABLE", "STATUS_DELETE");

    @Autowired
    private GmvMaxApiClient apiClient;
    @Autowired
    private TikTokStoreInfoDao tiktokStoreInfoDao;
    @Autowired
    private TikTokStoreTokenDao tiktokStoreTokenDao;
    @Autowired
    private TikTokGmvMaxCampaignDao tiktokGmvMaxCampaignDao;
    @Autowired
    private TikTokGmvMaxCampaignReportDao tiktokGmvMaxCampaignReportDao;
    @Autowired
    private TikTokSyncErrorRecordDao tiktokSyncErrorRecordDao;
    @Autowired
    private RedisTemplate<String, Object> redisTemplateNew;

    public void initGmvMaxCampaignSync(Integer puid, Integer shopId, String advertiserId, String storeId) {
        String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, shopId);
        Map<String, Integer> storeShopMap = Maps.newHashMap();
        storeShopMap.put(storeId, shopId);
        for (String primaryStatus : PRIMARY_STATUS) {
            syncGmvMaxCampaignDataBatch(puid, accessToken, advertiserId, Lists.newArrayList(storeId), null, storeShopMap, primaryStatus, 1, INIT_GMV_MAX_CAMPAIGN,
                    null, 0);
        }
    }

    public void addOrUpdateGmvMaxCampaignSync(Integer puid, Integer shopId, String advertiserId, String storeId, List<String> campaignIds, List<String> primaryStatusList) {
        String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, shopId);
        Map<String, Integer> storeShopMap = Maps.newHashMap();
        storeShopMap.put(storeId, shopId);
        if (CollectionUtils.isNotEmpty(primaryStatusList)) {
            for (String primaryStatus : primaryStatusList) {
                syncGmvMaxCampaignDataBatch(puid, accessToken, advertiserId, Lists.newArrayList(storeId), campaignIds, storeShopMap, primaryStatus, 1, INIT_GMV_MAX_CAMPAIGN,
                        null, 0);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        } else {
            syncGmvMaxCampaignDataBatch(puid, accessToken, advertiserId, Lists.newArrayList(storeId), campaignIds, storeShopMap, null, 1, INIT_GMV_MAX_CAMPAIGN,
                    null, 0);
        }
    }

    public void initGmvMaxCampaignReport(Integer puid, Integer shopId, String advertiserId, String storeId) {
        String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, shopId);

        LocalDate endDate = LocalDate.now();
        LocalDate startDate = LocalDate.parse("2024-01-01");

        LocalDate currentEndDate = endDate;
        while (currentEndDate.isAfter(startDate)) {
            LocalDate currentStartDate = currentEndDate.minusDays(30);
            if (currentStartDate.isBefore(startDate)) {
                currentStartDate = startDate;
            }

            saveGmvMaxCampaignReportData(puid, shopId, accessToken, advertiserId, storeId,
                    currentStartDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), currentEndDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                    1, INIT_GMV_MAX_CAMPAIGN_REPORT, null, 0);

            currentEndDate = currentStartDate.minusDays(1);
        }
    }

    public void syncGmvMaxCampaignData(String params) {
        log.info("开始定时任务基础数据同步任务");
        int page = 0;
        int pageSize = 100;
        List<TikTokStoreInfo> stores;

        try {
            do {
                stores = tiktokStoreInfoDao.getStoreInfoToSync(page * pageSize, pageSize);
                if (CollectionUtils.isEmpty(stores)) {
                    break;
                }

                // 按puid + advertiserId分组
                Map<String, List<TikTokStoreInfo>> advertiserGroup = stores.stream()
                        .collect(Collectors.groupingBy(i -> i.getPuid() + StringUtil.SPECIAL_COMMA + i.getAdvertiserId()));

                for (Map.Entry<String, List<TikTokStoreInfo>> entry : advertiserGroup.entrySet()) {
                    Integer puid = Integer.valueOf(entry.getKey().split(StringUtil.SPECIAL_COMMA)[0]);
                    String advertiserId = entry.getKey().split(StringUtil.SPECIAL_COMMA)[1];
                    List<TikTokStoreInfo> storeList = entry.getValue();

                    // 每次处理10个storeId
                    for (int i = 0; i < storeList.size(); i += 10) {
                        Map<String, Integer> storeShopMap = Maps.newHashMap();
                        List<String> batchStoreIds = Lists.newArrayList();

                        storeList.stream().skip(i).limit(10)
                                .collect(Collectors.toList()).forEach(s -> {
                                    storeShopMap.put(s.getStoreId(), s.getShopId());
                                    batchStoreIds.add(s.getStoreId());
                                });
                        String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, storeList.get(0).getShopId());
                        for (String primaryStatus : PRIMARY_STATUS) {
                            syncGmvMaxCampaignDataBatch(puid, accessToken, advertiserId, batchStoreIds, null, storeShopMap, primaryStatus, 1, GMV_MAX_CAMPAIGN, null, 0);
                        }
                    }
                }
                page++;
            } while (CollectionUtils.size(stores) == pageSize);
            log.info("定时任务基础数据同步任务完成");
        } catch (Exception e) {
            log.error("定时任务基础数据同步任务 error", e);
        }
    }

    public void syncGmvMaxCampaignDataBatch(int puid, String accessToken, String advertiserId, List<String> storeIds, List<String> campaignIds,
                                            Map<String, Integer> storeShopMap, String primaryStatus, int startPage, String taskType,
                                            TikTokSyncErrorRecord record, int errorStartPage) {
        GmvMaxCampaignGetReq req;
        try {
            while (true) {
                req = new GmvMaxCampaignGetReq();
                req.setAdvertiserId(advertiserId);
                req.setPage(startPage);
                req.setPageSize(100);
                FilteringGmvMaxCampaignGet filtering = new FilteringGmvMaxCampaignGet();
                if (CollectionUtils.isNotEmpty(storeIds)) {
                    filtering.setStoreIds(storeIds);
                }
                filtering.setCampaignIds(campaignIds);
                filtering.setGmvMaxPromotionTypes(Lists.newArrayList("PRODUCT_GMV_MAX"));
                if (StringUtils.isNotBlank(primaryStatus)) {
                    filtering.setPrimaryStatus(primaryStatus);
                }
                req.setFiltering(filtering);
                GmvMaxCampaignGetResponse response = apiClient.campaignGet(accessToken, req);
                List<GmvMaxCampaign> list = response.getList();
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                log.info("syncGmvMaxCampaignDataBatch1231: advertiserId:" + advertiserId + "primaryStatus: " + primaryStatus + " 获取到数据条数: " + list.size());

                TikTokSyncRecoveryParam recoveryCampaignInfo = new TikTokSyncRecoveryParam();
                List<GmvMaxCampaignSyncInfo> insertList = new ArrayList<>();
                for (GmvMaxCampaign gmvMaxCampaign : list) {
                    recoveryCampaignInfo.setCampaignId(gmvMaxCampaign.getCampaignId());
                    try {
                        GmvMaxCampaignSyncInfo syncInfo = new GmvMaxCampaignSyncInfo();
                        syncInfo.buildFrom(gmvMaxCampaign, primaryStatus);
                        GmvMaxCampaignInfo gmvMaxCampaignInfo = apiClient.campaignInfo(accessToken, advertiserId, gmvMaxCampaign.getCampaignId());
                        if (CollectionUtils.isNotEmpty(gmvMaxCampaignInfo.getIdentityList())) {
                            //获取redis缓存的达人信息
                            IdentityGetResponse identityGetResponse = apiClient.identityGetByRedis(puid, accessToken, gmvMaxCampaignInfo.getAdvertiserId(), gmvMaxCampaignInfo.getStoreId(), gmvMaxCampaignInfo.getStoreAuthorizedBcId());
                            if (identityGetResponse != null && CollectionUtils.isNotEmpty(identityGetResponse.getIdentityList())) {
                                Map<String, Identity> identityMap = identityGetResponse.getIdentityList().stream().collect(Collectors.toMap(Identity::getIdentityId, Function.identity()));
                                gmvMaxCampaignInfo.setIdentityList(gmvMaxCampaignInfo.getIdentityList().stream().map(e -> {
                                    if (identityMap.containsKey(e.getIdentityId())) {
                                        return identityMap.get(e.getIdentityId());
                                    }
                                    return e;
                                }).collect(Collectors.toList()));
                            }
                        }
                        syncInfo.buildFrom(storeShopMap.get(gmvMaxCampaignInfo.getStoreId()), gmvMaxCampaignInfo);
                        insertList.add(syncInfo);
                    } catch (Exception e) {
                        log.error("syncGmvMaxCampaignDataBatch error", e);
                        // 记录错误，等待重试 这里只重试获取详情
                        String type = taskType.startsWith("init") ? INIT_GMV_MAX_CAMPAIGN_INFO : GMV_MAX_CAMPAIGN_INFO;
                        tiktokSyncErrorRecordDao.add(puid, type, advertiserId, e.getMessage(), JSON.toJSONString(recoveryCampaignInfo));
                    }
                }
                if (CollectionUtils.isNotEmpty(insertList)) {
                    tiktokGmvMaxCampaignDao.batchAddOrUpdate(puid, insertList);
                }
                //若有传入广告活动id，同步一页
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    break;
                }
                startPage++;
            }
        } catch (Exception e) {
            //  记录错误，等待重试
            if (Objects.nonNull(record)) {
                // 如果是重试任务又出错了
                if (errorStartPage == startPage) {
                    // 页码没变，说明直接报错了，重试次数加1，更新状态，等待下次重试
                    tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_PENDING);
                    tiktokSyncErrorRecordDao.incrementRetryCount(record.getId());
                } else {
                    // 页码变了，说明本次重试成功了
                    tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_COMPLETED);

                    // 但是又有错误产生，视为新的错误，生成新的记录
                    TikTokSyncRecoveryParam recoveryCampaign = new TikTokSyncRecoveryParam();
                    recoveryCampaign.setPage(startPage);
                    recoveryCampaign.setStoreIds(storeIds);
                    recoveryCampaign.setPrimaryStatus(primaryStatus);
                    tiktokSyncErrorRecordDao.add(puid, taskType, advertiserId, e.getMessage(), JSON.toJSONString(recoveryCampaign));
                }
            } else {
                // 非重试任务直接记录错误
                TikTokSyncRecoveryParam recoveryCampaign = new TikTokSyncRecoveryParam();
                recoveryCampaign.setPage(startPage);
                recoveryCampaign.setStoreIds(storeIds);
                recoveryCampaign.setPrimaryStatus(primaryStatus);
                tiktokSyncErrorRecordDao.add(puid, taskType, advertiserId, e.getMessage(), JSON.toJSONString(recoveryCampaign));
            }
            log.error("syncGmvMaxCampaignDataBatch error", e);
        }
    }

    public void syncGmvMaxCampaignReportData(String params) {
        log.info("开始定时任务报告数据同步任务");
        int page = 0;
        int pageSize = 100;
        List<TikTokStoreInfo> stores;
        LocalDate endDate = LocalDate.now();
        LocalDate startDate = endDate.minusDays(3);

        try {
            do {
                stores = tiktokStoreInfoDao.getStoreInfoToSync(page * pageSize, pageSize);
                if (CollectionUtils.isEmpty(stores)) {
                    break;
                }
                for (TikTokStoreInfo store : stores) {
                    Integer puid = store.getPuid();
                    Integer shopId = store.getShopId();
                    String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, shopId);
                    saveGmvMaxCampaignReportData(puid, store.getShopId(), accessToken, store.getAdvertiserId(), store.getStoreId(),
                            startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            1, GMV_MAX_CAMPAIGN_REPORT, null, 0);
                }
                page++;
            } while (CollectionUtils.size(stores) == pageSize);
            log.info("定时任务报告数据同步任务完成");
        } catch (Exception e) {
            log.error("定时任务报告数据同步任务 error", e);
        }
    }

    public void saveGmvMaxCampaignReportData(Integer puid, Integer shopId, String accessToken, String advertiserId, String storeId,
                                             String startDate, String endDate, int startPage, String taskType,
                                             TikTokSyncErrorRecord record, int errorStartPage) {
        try {
            while (true) {
                List<String> metrics = Lists.newArrayList("campaign_id", "cost", "orders", "cost_per_order", "gross_revenue", "roi", "net_cost");
                List<String> dimensions = Lists.newArrayList("campaign_id", "stat_time_day");
                FilteringGmvMaxCampaignReport filtering = new FilteringGmvMaxCampaignReport();
                filtering.setGmvMaxPromotionTypes(Lists.newArrayList("PRODUCT"));
                filtering.setCampaignStatuses(PRIMARY_STATUS);
                GmvMaxCampaignReportResponse response = apiClient.campaignReportGet(accessToken, advertiserId, Lists.newArrayList(storeId), startDate, endDate,
                        metrics, dimensions, filtering, "cost", "DESC", startPage, 1000);
                List<GmvMaxCampaignReport> list = response.getList().stream().filter(i -> {
                    // 均不为0才同步
                    ReportMetrics reportMetrics = i.getMetrics();
                    return MathUtil.safeDecimal(reportMetrics.getCost(), 4).compareTo(BigDecimal.ZERO) != 0
                            || MathUtil.safeDecimal(reportMetrics.getOrders(), 4).compareTo(BigDecimal.ZERO) != 0
                            || MathUtil.safeDecimal(reportMetrics.getCostPerOrder(), 4).compareTo(BigDecimal.ZERO) != 0
                            || MathUtil.safeDecimal(reportMetrics.getGrossRevenue(), 4).compareTo(BigDecimal.ZERO) != 0
                            || MathUtil.safeDecimal(reportMetrics.getNetCost(), 4).compareTo(BigDecimal.ZERO) != 0
                            || MathUtil.safeDecimal(reportMetrics.getRoi(), 4).compareTo(BigDecimal.ZERO) != 0;
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(list)) {
                    break;
                }
                tiktokGmvMaxCampaignReportDao.batchAddOrUpdate(puid, shopId, advertiserId, storeId, list);
                startPage++;
            }
        } catch (Exception e) {
            // 记录错误，等待重试
            log.error("syncGmvMaxCampaignReportDataBatch error", e);
            if (Objects.nonNull(record)) {
                // 如果是重试任务又出错了
                if (errorStartPage == startPage) {
                    // 页码没变，说明直接报错了，重试次数加1，更新状态，等待下次重试
                    tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_PENDING);
                    tiktokSyncErrorRecordDao.incrementRetryCount(record.getId());
                } else {
                    // 页码变了，说明本次重试成功了
                    tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_COMPLETED);

                    // 但是又有错误产生，视为新的错误，生成新的记录
                    TikTokSyncRecoveryParam recoveryCampaign = new TikTokSyncRecoveryParam();
                    recoveryCampaign.setPage(startPage);
                    recoveryCampaign.setShopId(shopId);
                    recoveryCampaign.setStoreId(storeId);
                    recoveryCampaign.setStartDate(startDate);
                    recoveryCampaign.setEndDate(endDate);
                    tiktokSyncErrorRecordDao.add(puid, taskType, advertiserId, e.getMessage(), JSON.toJSONString(recoveryCampaign));
                }
            } else {
                TikTokSyncRecoveryParam recoveryCampaign = new TikTokSyncRecoveryParam();
                recoveryCampaign.setPage(startPage);
                recoveryCampaign.setShopId(shopId);
                recoveryCampaign.setStoreId(storeId);
                recoveryCampaign.setStartDate(startDate);
                recoveryCampaign.setEndDate(endDate);
                tiktokSyncErrorRecordDao.add(puid, taskType, advertiserId, e.getMessage(), JSON.toJSONString(recoveryCampaign));
            }

        }
    }

    public void tiktokGmvMaxErrorRecoveryJob(String params) {
        log.info("开始GMV MAX错误恢复任务");

        // 每次100条 优先处理初始化的错误
        List<TikTokSyncErrorRecord> records = tiktokSyncErrorRecordDao.getInitErrorRecords();
        if (CollectionUtils.isEmpty(records)) {
            records = tiktokSyncErrorRecordDao.getErrorRecords();
        }
        if (CollectionUtils.isEmpty(records)) {
            log.info("没有需要恢复的错误记录");
            return;
        }

        for (TikTokSyncErrorRecord record : records) {
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_PROCESSING);
            // 活动详情重试
            if (StringUtils.equalsAnyIgnoreCase(record.getTaskType(), INIT_GMV_MAX_CAMPAIGN_INFO, GMV_MAX_CAMPAIGN_INFO)) {
                retryCampaignInfo(record);
            }
            // 活动报告重试
            if (StringUtils.equalsAnyIgnoreCase(record.getTaskType(), INIT_GMV_MAX_CAMPAIGN_REPORT, GMV_MAX_CAMPAIGN_REPORT)) {
                retryCampaignReport(record);
            }
            // 活动列表重试
            if (StringUtils.equalsAnyIgnoreCase(record.getTaskType(), INIT_GMV_MAX_CAMPAIGN, GMV_MAX_CAMPAIGN)) {
                retryCampaign(record);
            }
        }
    }

    public void retryCampaignInfo(TikTokSyncErrorRecord record) {
        try {
            Integer puid = record.getPuid();
            String advertiserId = record.getAdvertiserId();
            List<TikTokStoreInfo> tikTokStoreInfos = tiktokStoreInfoDao.listByAdvertiserId(puid, advertiserId);
            String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, tikTokStoreInfos.get(0).getShopId());

            TikTokSyncRecoveryParam recoveryParam = JSON.parseObject(record.getRequestParams(), TikTokSyncRecoveryParam.class);
            GmvMaxCampaignInfo gmvMaxCampaignInfo = apiClient.campaignInfo(accessToken, advertiserId, recoveryParam.getCampaignId());

            String storeId = gmvMaxCampaignInfo.getStoreId();

            TikTokStoreInfo store = tikTokStoreInfos.stream().filter(s -> StringUtils.equals(s.getStoreId(), storeId)).findFirst()
                    .orElseThrow(() -> new BizServiceException("未找到对应的店铺信息"));
            if (CollectionUtils.isNotEmpty(gmvMaxCampaignInfo.getIdentityList())) {
                //获取redis缓存的达人信息
                IdentityGetResponse response = apiClient.identityGetByRedis(puid, accessToken, gmvMaxCampaignInfo.getAdvertiserId(), gmvMaxCampaignInfo.getStoreId(), gmvMaxCampaignInfo.getStoreAuthorizedBcId());
                if (response != null && CollectionUtils.isNotEmpty(response.getIdentityList())) {
                    Map<String, Identity> identityMap = response.getIdentityList().stream().collect(Collectors.toMap(Identity::getIdentityId, Function.identity()));
                    gmvMaxCampaignInfo.setIdentityList(gmvMaxCampaignInfo.getIdentityList().stream().map(e -> {
                        if (identityMap.containsKey(e.getIdentityId())) {
                            return identityMap.get(e.getIdentityId());
                        }
                        return e;
                    }).collect(Collectors.toList()));
                }
            }
            tiktokGmvMaxCampaignDao.addOrUpdate(puid, store.getShopId(), gmvMaxCampaignInfo);
            // 标记为已处理
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_COMPLETED);
        } catch (Exception e) {
            log.error("retryCampaignInfo error", e);
            // 重试次数加1
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_PENDING);
            tiktokSyncErrorRecordDao.incrementRetryCount(record.getId());
        }
    }

    public void retryCampaign(TikTokSyncErrorRecord record) {
        try {
            Integer puid = record.getPuid();
            String advertiserId = record.getAdvertiserId();
            List<TikTokStoreInfo> tikTokStoreInfos = tiktokStoreInfoDao.listByAdvertiserId(puid, advertiserId);
            String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, tikTokStoreInfos.get(0).getShopId());

            TikTokSyncRecoveryParam recoveryParam = JSON.parseObject(record.getRequestParams(), TikTokSyncRecoveryParam.class);
            int startPage = recoveryParam.getPage();
            List<String> storeIds = recoveryParam.getStoreIds();
            Map<String, Integer> storeShopMap = Maps.newHashMap();
            for (TikTokStoreInfo storeInfo : tikTokStoreInfos) {
                if (storeIds.contains(storeInfo.getStoreId())) {
                    storeShopMap.put(storeInfo.getStoreId(), storeInfo.getShopId());
                }
            }
            syncGmvMaxCampaignDataBatch(puid, accessToken, advertiserId, storeIds, null, storeShopMap, recoveryParam.getPrimaryStatus(), startPage,
                    record.getTaskType(), record, startPage);
            // 标记为已处理
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_COMPLETED);
        } catch (Exception e) {
            // 重试次数加1
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_PENDING);
            tiktokSyncErrorRecordDao.incrementRetryCount(record.getId());
        }
    }

    public void retryCampaignReport(TikTokSyncErrorRecord record) {
        try {
            Integer puid = record.getPuid();
            String advertiserId = record.getAdvertiserId();
            TikTokSyncRecoveryParam recoveryParam = JSON.parseObject(record.getRequestParams(), TikTokSyncRecoveryParam.class);
            Integer startPage = recoveryParam.getPage();
            Integer shopId = recoveryParam.getShopId();
            String storeId = recoveryParam.getStoreId();
            String startDate = recoveryParam.getStartDate();
            String endDate = recoveryParam.getEndDate();

            String accessToken = tiktokStoreTokenDao.getAccessTokenByShopId(puid, shopId);
            saveGmvMaxCampaignReportData(puid, shopId, accessToken, advertiserId, storeId,
                    startDate, endDate, startPage, record.getTaskType(), record, startPage);

            // 标记为已处理
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_COMPLETED);
        } catch (Exception e) {
            // 重试次数加1
            tiktokSyncErrorRecordDao.updateStatus(record.getId(), STATUS_PENDING);
            tiktokSyncErrorRecordDao.incrementRetryCount(record.getId());
        }
    }


}
