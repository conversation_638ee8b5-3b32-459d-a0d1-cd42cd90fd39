package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.grpc.adTargetTask.AdTargetTaskOuterClass;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.*;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetTaskDto.AdTargetTaskDetailDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.IAdTargetTaskService;
import com.meiyunji.sponsored.service.cpc.service2.impl.targettask.TargetTaskHandler;
import com.meiyunji.sponsored.service.kafka.AdTargetTaskKafkaProducer;
import com.meiyunji.sponsored.service.kafka.message.AdTargetTaskMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-07 14:08
 */
@Service
@Slf4j
public class AdTargetTaskServiceImpl implements IAdTargetTaskService {

    @Autowired
    private IAdTargetTaskDao adTargetTaskDao;
    @Autowired
    private IAdTargetTaskDetailDao adTargetTaskDetailDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;
    @Autowired
    private AdTargetTaskKafkaProducer adTargetTaskKafkaProducer;
    @Autowired
    private Map<String, TargetTaskHandler> targetTaskHandlerMap;
    private static BigDecimal HUNDRED = new BigDecimal(100);

    @Override
    public long recordTargetTask(AdTargetTaskDto adTargetTaskDto) {
        List<AdTargetTaskDetailDto> taskDetails = adTargetTaskDto.getTaskDetails();
        List<String> targetIds = taskDetails.stream().map(AdTargetTaskDetailDto::getTargetId).distinct().collect(Collectors.toList());
        List<Integer> sourceShopIds = new ArrayList<>();
        //兼容单店铺传参，若里层有shopId则以里面为准
        if (taskDetails.get(0).getSourceShopId() != 0) {
            sourceShopIds.addAll(taskDetails.stream().map(AdTargetTaskDetailDto::getSourceShopId).filter(e -> !e.equals(0)).distinct().collect(Collectors.toList()));
        } else {
            sourceShopIds.add(adTargetTaskDto.getSourceShopId());
        }
        // 从投放页面进行投放只有targetId信息,需要通过targetId和sourceShopId对详细信息进行反查
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(
                targetIds, adTargetTaskDto.getType(), adTargetTaskDto.getPuid(), sourceShopIds, adTargetTaskDto.getTargetingType());

        // 创建异步任务
        AdTargetTask adTargetTask = buildAdTargetTask(adTargetTaskDto, taskDetails, targetDetailMap);
        adTargetTaskDao.createNew(adTargetTask);

        // 添加异步任务详情
        List<AdTargetTaskDetail> adTargetTaskDetails = taskDetails.stream()
                .map(each -> buildAdTargetTaskDetail(adTargetTask, each, targetDetailMap))
                .collect(Collectors.toList());
        adTargetTaskDetailDao.batchInsert(adTargetTaskDetails, adTargetTaskDto.getPuid());
        return adTargetTask.getId();
    }

    private Map<String, AdTargetDetailDto> buildTargetDetailMap(List<String> targetIds, int taskType, int puid, List<Integer> sourceShopIds, String targetingType) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return Collections.emptyMap();
        } else {
            AdTargetTaskTypeEnum taskTypeEnum = AdTargetTaskTypeEnum.enumMap.get(taskType);
            TargetTaskHandler targetTaskHandler = targetTaskHandlerMap.get(taskTypeEnum.getHandlerName());
            return targetTaskHandler.buildTargetDetailMap(puid, targetIds, sourceShopIds, targetingType);
        }
    }

    @Override
    public AdTargetTaskOuterClass.AdTargetTaskPage getAdTargetTaskPage(int puid, int sourceShopId, int pageNo, int pageSize, int targetPageType, String sourceAdCampaignId, int uid) {
        AdTargetTaskOuterClass.AdTargetTaskPage.Builder pageBuilder = AdTargetTaskOuterClass.AdTargetTaskPage.newBuilder();
        Date lowLimit = DateTime.now().minusWeeks(1).withTimeAtStartOfDay().toDate();
        Page<AdTargetTask> page = adTargetTaskDao.getPageByPuidAfterTargetDate(puid, sourceShopId, pageNo, pageSize, targetPageType, sourceAdCampaignId, lowLimit, uid);

        List<AdTargetTaskOuterClass.AdTargetTask> resultDataList = buildAdTargetTaskList(puid, page.getRows());
        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        pageBuilder.addAllRows(resultDataList);
        return pageBuilder.build();
    }

    @Override
    public List<AdTargetTaskOuterClass.AdTargetTask> getAdTargetTaskByTaskIds(int puid, List<Long> taskIds) {
        List<AdTargetTask> dataList = adTargetTaskDao.getByPuidAndIds(puid, taskIds);
        return buildAdTargetTaskList(puid, dataList);
    }

    private List<AdTargetTaskOuterClass.AdTargetTask> buildAdTargetTaskList(int puid, List<AdTargetTask> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<Long> taskIds = dataList.stream()
                .filter(each -> AdTargetTaskStatusEnum.RUNNING.getCode() == each.getStatus())
                .map(AdTargetTask::getId)
                .collect(Collectors.toList());
        Map<Long, Integer> targetTaskSuccessDetailMap = CollectionUtils.isEmpty(taskIds)
                ? Collections.emptyMap()
                : adTargetTaskDetailDao.getRunningTaskProcess(taskIds, puid).stream().filter(each -> each.getTaskId() != null)
                .collect(Collectors.toMap(AdTargetTaskSuccessDetail::getTaskId, AdTargetTaskSuccessDetail::getCount, (newVal, oldVal) -> newVal));

        List<Integer> shopIds = dataList.stream()
                .map(AdTargetTask::getShopId)
                .distinct()
                .collect(Collectors.toList());
        List<ShopAuth> shopAuths = CollectionUtils.isEmpty(shopIds) ? Collections.emptyList() : shopAuthDao.getScAndVcByIds(shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream()
                .collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (newVal, oldVal) -> newVal));

        return dataList.stream()
                .map(each -> buildAdTargetTaskView(each, targetTaskSuccessDetailMap, shopAuthMap)).collect(Collectors.toList());
    }

    @Override
    public AdTargetTaskOuterClass.AdTargetTaskDetailPage getFailAdTargetTaskDetailPage(long taskId, int puid, int pageNo, int pageSize) {
        AdTargetTaskOuterClass.AdTargetTaskDetailPage.Builder pageBuilder = AdTargetTaskOuterClass.AdTargetTaskDetailPage.newBuilder();
        Page<AdTargetTaskDetail> page = adTargetTaskDetailDao.getFailurePage(taskId, puid, pageNo, pageSize);

        List<AdTargetTaskOuterClass.AdTargetTaskDetail> resultDataList = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            AdTargetTask adTargetTask = adTargetTaskDao.getByPuid(puid, taskId);
            List<String> targetIds = page.getRows().stream().map(AdTargetTaskDetail::getTargetId)
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
            List<Integer> sourceShopIds = page.getRows().stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
            Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(
                    targetIds, adTargetTask.getType(), puid, sourceShopIds, adTargetTask.getTargetingType());

            int shopId = page.getRows().get(0).getShopId();
            String campaignType = AdTargetTaskTypeEnum.enumMap.get(adTargetTask.getType()).getAdCampaignType().toLowerCase();

            List<String> groupIds = page.getRows().stream().map(AdTargetTaskDetail::getAdGroupId).distinct().collect(Collectors.toList());
            Map<String, String> groupNameMap = buildGroupNameMap(groupIds, campaignType, puid, shopId);

            List<String> campaignIds = page.getRows().stream().map(AdTargetTaskDetail::getAdCampaignId).distinct().collect(Collectors.toList());
            List<AmazonAdCampaignAll> campaignList = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, null, campaignIds, campaignType);
            Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().filter(Objects::nonNull).
                    collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (newVal, oldVal) -> newVal));

            List<String> portfolioIds = campaignList.stream().map(AmazonAdCampaignAll::getPortfolioId).distinct().collect(Collectors.toList());
            List<AmazonAdPortfolio> portFolios = amazonAdPortfolioDao.getPortfolioList(puid, shopId, portfolioIds);
            Map<String, AmazonAdPortfolio> portFolioMap = portFolios.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (oldVal, newVal) -> newVal));

            ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
            resultDataList = page.getRows().stream()
                    .map(each -> buildFailAdTargetTaskDetailView(each, portFolioMap, campaignMap, groupNameMap, targetDetailMap, shopAuth, adTargetTask))
                    .collect(Collectors.toList());
        }

        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        pageBuilder.setTaskId(taskId);
        pageBuilder.addAllRows(resultDataList);
        return pageBuilder.build();
    }

    private Map<String, String> buildGroupNameMap(List<String> groupIds, String campaignType, int puid, int shopId) {
        Map<String, String> groupMap = Collections.emptyMap();
        if ("sp".equals(campaignType)) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(puid, shopId, null, groupIds);
            groupMap = amazonAdGroups.stream()
                    .collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, AmazonAdGroup::getName, (newVal, oldVal) -> newVal));
        } else if ("sb".equals(campaignType)) {
            List<AmazonSbAdGroup> amazonAdGroups = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, groupIds);
            groupMap = amazonAdGroups.stream()
                    .collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, AmazonSbAdGroup::getName, (newVal, oldVal) -> newVal));
        } else if ("sd".equals(campaignType)) {
            List<AmazonSdAdGroup> amazonAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, shopId, groupIds);
            groupMap = amazonAdGroups.stream()
                    .collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getName, (newVal, oldVal) -> newVal));
        }
        return groupMap;
    }

    @Override
    public AdTargetTaskOuterClass.TargetObjectDetailPage getTargetObjectDetailPage(long taskId, int puid, int pageNo, int pageSize) {
        AdTargetTask adTargetTask = adTargetTaskDao.getByPuid(puid, taskId);
        AdTargetTaskOuterClass.TargetObjectDetailPage.Builder pageBuilder = AdTargetTaskOuterClass.TargetObjectDetailPage.newBuilder();
        Page<AdTargetTaskDetail> page = adTargetTaskDetailDao.getTargetObjectDetailPage(taskId, puid, pageNo, pageSize);

        List<String> targetIds = page.getRows().stream().map(AdTargetTaskDetail::getTargetId)
                .filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<Integer> sourceShopIds = page.getRows().stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(
                targetIds, adTargetTask.getType(), puid, sourceShopIds, adTargetTask.getTargetingType());

        List<AdTargetTaskOuterClass.AdTargetTaskDetail> resultDataList = Collections.emptyList();
        if (CollectionUtils.isNotEmpty(page.getRows())) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcById(adTargetTask.getSourceShopId());
            resultDataList = page.getRows().stream().map(each -> buildTargetDetailView(each, targetDetailMap, shopAuth)).collect(Collectors.toList());
        }

        pageBuilder.setPageSize(page.getPageSize());
        pageBuilder.setPageNo(page.getPageNo());
        pageBuilder.setTotalPage(page.getTotalPage());
        pageBuilder.setTotalSize(page.getTotalSize());
        pageBuilder.addAllRows(resultDataList);
        return pageBuilder.build();
    }

    @Override
    public List<AdTargetTaskDetail> getAdTargetTaskDetailList(long taskId, int puid) {
        return adTargetTaskDetailDao.getListByTaskId(taskId, puid);
    }

    @Override
    public void deleteExpiredRecords() {
        Date lowLimit = DateTime.now().minusWeeks(1).withTimeAtStartOfDay().toDate();
        List<AdTargetTask> adTargetTasks = adTargetTaskDao.getListBeforeTargetDate(lowLimit);
        if (CollectionUtils.isEmpty(adTargetTasks)) {
            return;
        }
        // 先删除任务详情,再删除主任务信息
        // 如果任务详情删除了,主任务信息没删除,有另外的定时任务会对异常状态的任务进行删除
        Map<Integer, List<Long>> puid2TaskIds = adTargetTasks.stream()
                .collect(Collectors.groupingBy(AdTargetTask::getPuid, Collectors.mapping(AdTargetTask::getId, Collectors.toList())));
        for (Map.Entry<Integer, List<Long>> entry : puid2TaskIds.entrySet()) {
            int puid = entry.getKey();
            List<Long> taskIds = entry.getValue();
            try {
                adTargetTaskDetailDao.deleteByTaskIds(taskIds, puid);
                adTargetTaskDao.deleteByIds(taskIds);
                TimeUnit.MILLISECONDS.sleep(100);
            } catch (Exception e) {
                log.error("deleteExpiredRecords error.puid:{} taskIds:{}", puid, taskIds, e);
            }
        }
    }

    @Override
    public void removeOrRestartAbnormalRecords() {
        Date lowLimit = DateTime.now().minusMinutes(30).toDate();
        List<AdTargetTask> adTargetTasks = adTargetTaskDao.getUnfinishedListBeforeTargetDate(lowLimit);
        if (CollectionUtils.isEmpty(adTargetTasks)) {
            return;
        }
        for (AdTargetTask adTargetTask : adTargetTasks) {
            // 只处理未执行的部分
            List<AdTargetTaskDetail> adTargetTaskDetails = adTargetTaskDetailDao.getListByTaskId(adTargetTask.getId(), adTargetTask.getPuid()).stream()
                    .filter(each -> AdTargetTaskStatusEnum.WAITING.getCode() == each.getStatus()).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
                // 没有任务详情的任务视作异常任务,删除该任务
                adTargetTaskDao.deleteById(adTargetTask.getId());
            } else {
                executeTask(adTargetTask, adTargetTaskDetails);
            }
        }
    }

    @Override
    public void executeTask(long taskId) {
        AdTargetTask adTargetTask = adTargetTaskDao.getById(taskId);
        List<AdTargetTaskDetail> adTargetTaskDetails = adTargetTaskDetailDao.getListByTaskId(adTargetTask.getId(), adTargetTask.getPuid());
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            log.info("executeTask fail.no task detail.puid:{} taskId:{}", adTargetTask.getPuid(), taskId);
            return;
        }
        executeTask(adTargetTask, adTargetTaskDetails);
    }

    private void executeTask(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        int limit = dynamicRefreshConfiguration.getUseMultiThreadHandleTargetTaskSizeLimit();
        // 投放数量小于等于limit走线程池执行,大于limit通过kafka消费执行
        if (adTargetTaskDetails.size() <= limit) {
            ThreadPoolExecutor pool = ThreadPoolUtil.getExecuteTargetTaskPool();
            pool.execute(() -> doExecuteTask(adTargetTask, adTargetTaskDetails));
        } else {
            AdTargetTaskMessage message = new AdTargetTaskMessage();
            message.setTaskId(adTargetTask.getId());
            try {
                adTargetTaskKafkaProducer.send(adTargetTask.getShopId(), message);
            } catch (Exception e) {
                log.error("adTargetTaskKafkaProducer send error.taskId:{}", adTargetTask.getId(), e);
            }
        }
    }

    @Override
    public void doExecuteTask(long taskId) {
        AdTargetTask adTargetTask = adTargetTaskDao.getById(taskId);
        List<AdTargetTaskDetail> adTargetTaskDetails = adTargetTaskDetailDao.getListByTaskId(adTargetTask.getId(), adTargetTask.getPuid());
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            log.info("doExecuteTask fail.no task detail.puid:{} taskId:{}", adTargetTask.getPuid(), taskId);
            return;
        }
        doExecuteTask(adTargetTask, adTargetTaskDetails);
    }

    private void doExecuteTask(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        // 只处理未执行成功的任务
        adTargetTaskDetails = adTargetTaskDetails.stream()
                .filter(each -> AdTargetTaskStatusEnum.SUCCESS.getCode() != each.getStatus()).collect(Collectors.toList());
        int taskType = adTargetTask.getType();
        AdTargetTaskTypeEnum taskTypeEnum = AdTargetTaskTypeEnum.enumMap.get(taskType);
        TargetTaskHandler targetTaskHandler = targetTaskHandlerMap.get(taskTypeEnum.getHandlerName());
        adTargetTaskDao.updateStatus(adTargetTask.getPuid(), adTargetTask.getId(), AdTargetTaskStatusEnum.RUNNING.getCode());
        targetTaskHandler.handle(adTargetTask, adTargetTaskDetails);
    }

    @Override
    public String retry(int puid, long taskId) {
        AdTargetTask adTargetTask = adTargetTaskDao.getByPuid(puid, taskId);
        if (adTargetTask == null) {
            log.info("retry fail.no task detail.puid:{} taskId:{}", adTargetTask.getPuid(), taskId);
            return "任务不存在";
        }
        List<AdTargetTaskDetail> adTargetTaskDetails = adTargetTaskDetailDao.getListByTaskId(adTargetTask.getId(), adTargetTask.getPuid());
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            log.info("retry fail.no task detail.puid:{} taskId:{}", adTargetTask.getPuid(), taskId);
            return "任务详情不存在";
        }
        doExecuteTask(adTargetTask, adTargetTaskDetails);
        return StringUtils.EMPTY;
    }

    private AdTargetTask buildAdTargetTask(AdTargetTaskDto source, List<AdTargetTaskDetailDto> taskDetails, Map<String, AdTargetDetailDto> targetDetailMap) {
        AdTargetTask target = new AdTargetTask();
        target.setPuid(source.getPuid());
        target.setUid(source.getUid());
        target.setLoginIp(source.getLoginIp());
        target.setShopId(source.getShopId());
        target.setSourceShopId(source.getSourceShopId());
        target.setType(source.getType());
        target.setStatus(AdTargetTaskStatusEnum.WAITING.getCode());
        AdTargetTaskDetailDto firstTaskDetail = taskDetails.get(0);
        // 冗余字段
        if (targetDetailMap.isEmpty()) {
            target.setTargetObject(firstTaskDetail.getTargetObject());
            target.setTargetObjectSize((int) taskDetails.stream().map(AdTargetTaskDetailDto::getTargetObject).distinct().count());
        } else {
            AdTargetDetailDto adTargetDetail = targetDetailMap.get(firstTaskDetail.getTargetId());
            target.setTargetObject(adTargetDetail.getTargetObject());
            target.setTargetObjectSize((int) targetDetailMap.values().stream().map(AdTargetDetailDto::getTargetObject).distinct().count());
        }
        target.setTargetObjectType(firstTaskDetail.getTargetObjectType());
        target.setTargetSize(taskDetails.size());
        target.setTargetingType(StringUtils.defaultString(source.getTargetingType()));
        target.setTargetPageType(source.getTargetPageType());
        target.setSourceAdCampaignId(StringUtils.defaultString(source.getSourceAdCampaignId()));
        return target;
    }

    private AdTargetTaskDetail buildAdTargetTaskDetail(AdTargetTask adTargetTask, AdTargetTaskDetailDto source, Map<String, AdTargetDetailDto> targetDetailMap) {
        AdTargetTaskDetail target = new AdTargetTaskDetail();
        target.setPuid(adTargetTask.getPuid());
        target.setShopId(adTargetTask.getShopId());
        target.setTaskId(adTargetTask.getId());

        target.setTargetObject(source.getTargetObject());
        target.setTargetObjectDesc(StringUtils.defaultString(source.getTargetObjectDesc()));
        target.setTargetObjectType(source.getTargetObjectType());
        target.setAdCampaignId(source.getAdCampaignId());
        target.setAdGroupId(source.getAdGroupId());
        target.setMatchType(source.getMatchType());
        target.setStatus(AdTargetTaskStatusEnum.WAITING.getCode());
        target.setFailureReason(StringUtils.EMPTY);
        target.setFailureReasonDetail(StringUtils.EMPTY);
        target.setImgUrl(correctImgUrl(source.getImgUrl()));
        target.setBid(Optional.ofNullable(source.getBid()).orElse(BigDecimal.ZERO));
        target.setSuggested(source.getSuggested());
        target.setRangeStart(source.getRangeStart());
        target.setRangeEnd(source.getRangeEnd());
        target.setTargetId(StringUtils.defaultString(source.getTargetId()));
        target.setSourceShopId(source.getSourceShopId());
        if (MapUtils.isNotEmpty(targetDetailMap)) {
            AdTargetDetailDto adTargetDetail = targetDetailMap.get(source.getTargetId());
            target.setTargetObject(adTargetDetail.getTargetObject());
            target.setTargetObjectDesc(StringUtils.defaultString(adTargetDetail.getTargetObjectDesc()));
            target.setImgUrl(StringUtils.defaultString(adTargetDetail.getImgUrl()));
        }
        return target;
    }

    private static String correctImgUrl(String imgUrl) {
        if (StringUtils.isBlank(imgUrl)) {
            return StringUtils.EMPTY;
        }
        int index1 = imgUrl.indexOf("http:");
        int index2 = imgUrl.indexOf("https:");
        if (index1 == 0 || index2 == 0) {
            return imgUrl;
        }
        int index = Math.max(index1, index2);
        return imgUrl.substring(index);
    }

    private AdTargetTaskOuterClass.AdTargetTask buildAdTargetTaskView(AdTargetTask adTargetTask,
                                                                      Map<Long, Integer> targetTaskSuccessDetailMap,
                                                                      Map<Integer, ShopAuth> shopAuthMap) {
        AdTargetTaskOuterClass.AdTargetTask.Builder builder = AdTargetTaskOuterClass.AdTargetTask.newBuilder();
        builder.setId(adTargetTask.getId());
        builder.setTargetObject(adTargetTask.getTargetObject());
        builder.setTargetObjectSize(adTargetTask.getTargetObjectSize());
        builder.setTargetObjectType(adTargetTask.getTargetObjectType());
        builder.setTargetSize(adTargetTask.getTargetSize());
        builder.setStatus(adTargetTask.getStatus());
        Optional.ofNullable(adTargetTask.getType()).ifPresent(builder::setType);
        if (AdTargetTaskStatusEnum.RUNNING.getCode() == adTargetTask.getStatus()) {
            int finishedCount = targetTaskSuccessDetailMap.getOrDefault(adTargetTask.getId(), 0);
            int progress = MathUtil.divide(BigDecimal.valueOf(finishedCount), BigDecimal.valueOf(adTargetTask.getTargetSize()), 2)
                    .multiply(HUNDRED).intValue();
            builder.setProgress(progress);
        }
        AdTargetTaskTypeEnum adTargetTaskTypeEnum = AdTargetTaskTypeEnum.enumMap.get(adTargetTask.getType());
        builder.setTargetType(adTargetTaskTypeEnum.getDesc());
        builder.setAdCampaignType(adTargetTaskTypeEnum.getAdCampaignType());
        builder.setStartTime(DateUtil.getDateTime(adTargetTask.getCreateTime()));
        ShopAuth shopAuth = shopAuthMap.get(adTargetTask.getShopId());
        if (shopAuth != null) {
            builder.setShopName(shopAuth.getName());
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
        }
        return builder.build();
    }

    private AdTargetTaskOuterClass.AdTargetTaskDetail buildFailAdTargetTaskDetailView(AdTargetTaskDetail adTargetTaskDetail,
                                                                                      Map<String, AmazonAdPortfolio> portFolioMap,
                                                                                      Map<String, AmazonAdCampaignAll> campaignMap,
                                                                                      Map<String, String> groupNameMap,
                                                                                      Map<String, AdTargetDetailDto> targetDetailMap,
                                                                                      ShopAuth shopAuth, AdTargetTask adTargetTask) {
        AdTargetTaskOuterClass.AdTargetTaskDetail.Builder builder = AdTargetTaskOuterClass.AdTargetTaskDetail.newBuilder();
        builder.setTargetObject(adTargetTaskDetail.getTargetObject());
        if ("theme".equalsIgnoreCase(adTargetTaskDetail.getMatchType())) {
            if ("KEYWORDS_RELATED_TO_YOUR_BRAND".equalsIgnoreCase(adTargetTaskDetail.getTargetObject())) {
                builder.setTargetObject("与您的品牌相关的关键词");
            } else if ("KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES".equalsIgnoreCase(adTargetTaskDetail.getTargetObject())) {
                builder.setTargetObject("与您的落地页相关的关键词");
            }
        }
        builder.setTargetObjectType(adTargetTaskDetail.getTargetObjectType());
        builder.setFailureReason(adTargetTaskDetail.getFailureReason());
        Optional.ofNullable(adTargetTask.getType()).ifPresent(builder::setType);
        if (!AdTargetTaskMatchTypeEnum.notDisplayCodes.contains(adTargetTaskDetail.getMatchType())) {
            AdTargetTaskMatchTypeEnum matchTypeEnum = AdTargetTaskMatchTypeEnum.enumMap.get(adTargetTaskDetail.getMatchType());
            if (matchTypeEnum != null) {
                builder.setMatchType(matchTypeEnum.getDesc());
            }
        }
        AmazonAdCampaignAll amazonAdCampaignAll = campaignMap.get(adTargetTaskDetail.getAdCampaignId());
        if (amazonAdCampaignAll != null) {
            builder.setAdCampaign(amazonAdCampaignAll.getName());
            if (StringUtils.isNotBlank(amazonAdCampaignAll.getPortfolioId())) {
                AmazonAdPortfolio amazonAdPortfolio = portFolioMap.get(amazonAdCampaignAll.getPortfolioId());
                if (amazonAdPortfolio != null) {
                    builder.setAdPortfolio(amazonAdPortfolio.getName());
                }
            }
        }
        String groupName = groupNameMap.get(adTargetTaskDetail.getAdGroupId());
        if (StringUtils.isNotBlank(groupName)) {
            builder.setAdGroup(groupName);
        }
        if (shopAuth != null) {
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
        }

        fillTargetDetail(builder, adTargetTaskDetail, targetDetailMap);
        return builder.build();
    }

    private AdTargetTaskOuterClass.AdTargetTaskDetail buildTargetDetailView(AdTargetTaskDetail adTargetTaskDetail,
                                                                            Map<String, AdTargetDetailDto> targetDetailMap,
                                                                            ShopAuth shopAuth) {
        AdTargetTaskOuterClass.AdTargetTaskDetail.Builder builder = AdTargetTaskOuterClass.AdTargetTaskDetail.newBuilder();
        builder.setTargetObject(adTargetTaskDetail.getTargetObject());
        builder.setTargetObjectType(adTargetTaskDetail.getTargetObjectType());
        if (shopAuth != null) {
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
        }
        fillTargetDetail(builder, adTargetTaskDetail, targetDetailMap);
        return builder.build();
    }

    private void fillTargetDetail(AdTargetTaskOuterClass.AdTargetTaskDetail.Builder builder,
                                  AdTargetTaskDetail adTargetTaskDetail,
                                  Map<String, AdTargetDetailDto> targetDetailMap) {
        if (MapUtils.isEmpty(targetDetailMap)) {
            if (StringUtils.isNotBlank(adTargetTaskDetail.getTargetObjectDesc())) {
                builder.setTargetObjectDesc(adTargetTaskDetail.getTargetObjectDesc());
            }
            if (StringUtils.isNotBlank(adTargetTaskDetail.getImgUrl())) {
                builder.setImgUrl(adTargetTaskDetail.getImgUrl());
            }
        } else {
            if (AdTargetObjectTypeEnum.ASIN.getCode() == adTargetTaskDetail.getTargetObjectType()) {
                if (StringUtils.isNotBlank(adTargetTaskDetail.getTargetObjectDesc())) {
                    builder.setTargetObjectDesc(adTargetTaskDetail.getTargetObjectDesc());
                }
                if (StringUtils.isNotBlank(adTargetTaskDetail.getImgUrl())) {
                    builder.setImgUrl(adTargetTaskDetail.getImgUrl());
                }
            } else if (AdTargetObjectTypeEnum.CATEGORY.getCode() == adTargetTaskDetail.getTargetObjectType()) {
                String targetId = adTargetTaskDetail.getTargetId();
                AdTargetDetailDto targetDetail = targetDetailMap.get(targetId);

                builder.setBrandName(BrandMessageConstants.DEFAULT_BRAND_NAME);
                if (StringUtils.isNotBlank(targetDetail.getBrandName())) {
                    builder.setBrandName(targetDetail.getBrandName());
                }

                builder.setCommodityPriceRange(BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE);
                if (StringUtils.isNotBlank(targetDetail.getMaxPrice()) && StringUtils.isNotBlank(targetDetail.getMinPrice())) {
                    builder.setCommodityPriceRange(targetDetail.getMinPrice() + "-" + targetDetail.getMaxPrice());
                } else if (StringUtils.isNotBlank(targetDetail.getMaxPrice())) {
                    builder.setCommodityPriceRange(targetDetail.getMaxPrice() + " 及以下");
                } else if (StringUtils.isNotBlank(targetDetail.getMinPrice())) {
                    builder.setCommodityPriceRange(targetDetail.getMinPrice() + " 及更高");
                }

                builder.setRating(BrandMessageConstants.DEFAULT_RATING);
                if (StringUtils.isNotBlank(targetDetail.getMaxReviewRating()) && StringUtils.isNotBlank(targetDetail.getMinReviewRating())) {
                    builder.setRating(targetDetail.getMinReviewRating() + "~" + targetDetail.getMaxReviewRating());
                } else if (StringUtils.isNotBlank(targetDetail.getMaxReviewRating())) {
                    builder.setRating("1~" + targetDetail.getMaxReviewRating());
                } else if (StringUtils.isNotBlank(targetDetail.getMinReviewRating())) {
                    builder.setRating(targetDetail.getMinReviewRating() + "~5");
                }

                builder.setDistribution(BrandMessageConstants.DEFAULT_DISTRIBUTION);
                if (StringUtils.isNotBlank(targetDetail.getAsinIsPrimeShippingEligible())) {
                    if (BooleanUtils.toBoolean(targetDetail.getAsinIsPrimeShippingEligible())) {
                        builder.setDistribution("具有Prime资格");
                    } else {
                        builder.setDistribution("不具有Prime资格");
                    }
                }
            }
        }
    }
}





