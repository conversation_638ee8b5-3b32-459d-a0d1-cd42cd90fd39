package com.meiyunji.sponsored.service.reportHour.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sellfox.ams.api.entry.HourlyReportDataPb;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.MultiThreadQueryAndMergeUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSdProductReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.FeedHourlySelectDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSdProductReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.ProductHourParam;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdProductHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AmazonAdProductHourReportServiceImpl implements IAmazonAdProductHourReportService {

    private final IScVcShopAuthDao shopAuthDao;
    private final AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final IAmazonAdProductReportDao amazonAdProductReportDao;
    private final IAmazonAdSdProductReportDao amazonAdSdProductReportDao;
    private final IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    private final IAmazonAdFeedReportService amazonAdFeedReportService;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;

    public AmazonAdProductHourReportServiceImpl(
            IScVcShopAuthDao shopAuthDao,
            @Qualifier("adFeedBlockingStub") AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub,
            IAmazonAdProductDao amazonAdProductDao, IAmazonAdProductReportDao amazonAdProductReportDao,
            IAmazonAdSdProductReportDao amazonAdSdProductReportDao, IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao,
            IAmazonAdFeedReportService amazonAdFeedReportService) {
        this.shopAuthDao = shopAuthDao;
        this.adFeedBlockingStub = adFeedBlockingStub;
        this.amazonAdProductDao = amazonAdProductDao;
        this.amazonAdProductReportDao = amazonAdProductReportDao;
        this.amazonAdSdProductReportDao = amazonAdSdProductReportDao;
        this.amazonMarketingStreamDataDao = amazonMarketingStreamDataDao;
        this.amazonAdFeedReportService = amazonAdFeedReportService;
    }


    @Override
    public List<AdProductHourVo> getHourList(int puid, ProductHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStart(LocalDate.parse(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setWeekdayList(Optional.ofNullable(param.getWeeks()).filter(StringUtils::isNotBlank).
                map(w -> StringUtil.splitInt(param.getWeeks(), ",")).orElse(HourConvert.weeKs));
        if (StringUtils.isNotBlank(param.getAdId())) {
            builder.setAdIds(Lists.newArrayList(param.getAdId()));
        }
        List<AmazonMarketingStreamData> amazonMarketingStreamDataList = amazonMarketingStreamDataDao.statisticsProductHourlyReport(builder);

        //获取小时对比数据
        List<AmazonMarketingStreamData> compareResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            // todo 更换了查询方法
//            compareResponse = amazonMarketingStreamDataDao.listByKeywordHourly(builder);
            compareResponse = amazonMarketingStreamDataDao.statisticsProductHourlyReport(builder);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(amazonMarketingStreamDataList, hourlyReportDataMap);
        List<AdProductHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdProductHourVo adProductHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adProductHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdProductWeekDayVo> getWeeklySuperpositionList(int puid, ProductHourParam param) {
        List<AdProductHourVo> weeklySuperpositionDailyList = getWeeklySuperpositionDailyList(puid, param);
        return convertToHourOfWeekDayVos(weeklySuperpositionDailyList);
    }

    @Override
    public List<AdProductHourVo> getWeeklySuperpositionDailyList(int puid, ProductHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getAdId())) {
            selectDto.setAdIds(Collections.singletonList(param.getAdId()));
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsProductWeeklySuperpositionReport(selectDto);
        //pb对象转vo对象
        List<AdProductHourVo> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());

        Map<Integer, List<AdProductHourVo>> compareHourMap = Maps.newHashMap();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDateCompare()));
            selectDto.setEndDate(param.getEndDateCompare());
            List<AmazonMarketingStreamData> compareList = amazonMarketingStreamDataDao.statisticsProductWeeklySuperpositionReport(selectDto);
            if (CollectionUtils.isNotEmpty(compareList)) {
                List<AdProductHourVo> compareVOs = compareList.stream().map(this::convertTo).collect(Collectors.toList());
                compareHourMap.putAll(StreamUtil.groupingBy(compareVOs, AdProductHourVo::getWeekDay));
            }
        }

        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdProductHourVo::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdProductHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdProductHourVo::getWeekDay));
        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
        needFilledWeek.forEach(e -> {
            List<AdProductHourVo> adProductHourVos = new ArrayList<>();
            voMap.put(e, adProductHourVos);
        });
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<AdProductHourVo> compareHourVOs = compareHourMap.get(k);
            Map<String, AdProductHourVo> compareHourVOMap = StreamUtil.toMap(compareHourVOs, AdProductHourVo::getLabel);

            List<String> hourList = v.stream().map(AdProductHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setWeekDay(k);
                voList.add(vo);
                if (compareHourVOMap.containsKey(hour)) {
                    AdProductHourVo compareItem = compareHourVOMap.get(hour);
                    vo.compareDataSet(compareItem);
                }
            }
            for (AdProductHourVo vo : v) {
                if (compareHourVOMap.containsKey(vo.getLabel())) {
                    AdProductHourVo compareItem = compareHourVOMap.get(vo.getLabel());
                    vo.compareDataSet(compareItem);
                }
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdProductHourVo::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdProductHourVo> getMonthlyList(int puid, String adType, ProductHourParam param, List<AdProductHourVo> compares) {
        List<AdProductHourVo> reports = getAdProductDailyReports(puid, adType, param);
        reports = ReportChartUtil.getProductMonthReportVos(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdProductHourVo> compareList = getAdProductDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getProductMonthReportVos(compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingMonthCompare(param ,reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdProductHourVo> paddingMonthCompare(ProductHourParam param, List<AdProductHourVo> reports, List<AdProductHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdProductHourVo> resList = new ArrayList<>();
        Map<String, AdProductHourVo> map = StreamUtil.toMap(reports, AdProductHourVo::getLabel);
        Map<String, AdProductHourVo> compareMap = StreamUtil.toMap(compareList, AdProductHourVo::getLabel);
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end));
             start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdProductHourVo report = map.get(start.format(monthFormatter));
            AdProductHourVo compareReport = compareMap.get(startCompare.format(monthFormatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdProductHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdProductHourVo();
                vo.setLabel(start.format(monthFormatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    private List<AdProductHourVo> getAdProductDailyReports(int puid, String adType, ProductHourParam param) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), param.getPuid());
        //日,周,月报告数据
        if ("SP".equalsIgnoreCase(adType)) {
            List<AmazonAdProductReport> reports =
                    amazonAdProductReportDao.getReportByAdId(puid, param.getShopId(),
                            start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getAdId());
            return reports.stream().map(item -> {
                AdProductHourVo vo = new AdProductHourVo();

                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getTotalSales());
                vo.setAdSelfSale(item.getAdSales());
                vo.setAdOtherSale(item.getAdOtherSales());
                vo.setAdOrderNum(item.getSaleNum());
                vo.setSelfAdOrderNum(item.getAdSaleNum());
                vo.setOtherAdOrderNum(item.getAdOtherOrderNum());
                vo.setAdSaleNum(item.getOrderNum());
                vo.setAdSelfSaleNum(item.getAdOrderNum());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNum());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        } else if ("SD".equalsIgnoreCase(adType)) {
            List<AmazonAdSdProductReport> reports =
                    amazonAdSdProductReportDao.getReportByAdId(puid, param.getShopId(),
                            start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            shopAuth.getMarketplaceId(), param.getAdId());
            return reports.stream().map(item -> {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getSales14d());
                vo.setAdSelfSale(item.getSales14dSameSKU());
                vo.setAdOtherSale(item.getSales14d().subtract(item.getSales14dSameSKU()));
                vo.setAdOrderNum(item.getConversions14d());
                vo.setSelfAdOrderNum(item.getConversions14dSameSKU());
                vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSKU());
                vo.setAdSaleNum(item.getConversions14d());
                vo.setAdSelfSaleNum(item.getConversions14dSameSKU());
                vo.setAdOtherSaleNum(item.getConversions14d() - item.getConversions14dSameSKU());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<AdProductHourVo> getWeeklyList(int puid, String adType, ProductHourParam param, List<AdProductHourVo> compares) {
        List<AdProductHourVo> reports = getAdProductDailyReports(puid, adType, param);
        reports = ReportChartUtil.getProductWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdProductHourVo> compareList = getAdProductDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getProductWeekReportVos(param.getStartDate(), param.getEndDate(), compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdProductHourVo> paddingWeekCompare(List<AdProductHourVo> reports, List<AdProductHourVo> compareList) {
        if (CollectionUtils.isEmpty(reports)) {
            return reports;
        }
        for (int i = 0; i < reports.size() && i < compareList.size(); i++) {
            reports.get(i).compareDataSet(compareList.get(i));
        }
        return reports;
    }

    @Override
    public List<AdProductHourVo> getDailyList(int puid, String adType, ProductHourParam param, List<AdProductHourVo> compares) {
        List<AdProductHourVo> reports = getAdProductDailyReports(puid, adType, param);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdProductHourVo> compareList = getAdProductDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdProductHourVo> paddingDayCompare(ProductHourParam param, List<AdProductHourVo> reportList, List<AdProductHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdProductHourVo> resList = new ArrayList<>();
        Map<String, AdProductHourVo> map = StreamUtil.toMap(reportList, AdProductHourVo::getLabel);
        Map<String, AdProductHourVo> compareMap = StreamUtil.toMap(compareList, AdProductHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdProductHourVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdProductHourVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdProductHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdProductHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AdProductHourOfKeywordVo> getListOfAd(int puid, ProductHourParam param) {
        return convertToHourOfAdVos(getDetailListOfAd(puid, param));
    }

    @Override
    public List<AdProductHourVo> getDetailListOfAd(int puid, ProductHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getAdId())) {
            selectDto.setAdIds(Collections.singletonList(param.getAdId()));
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsProductHourlyReportOfTarget(selectDto);

        //pb对象转vo对象
        List<AdProductHourVo> hourVos = dataList.stream().map(this::convertTo).collect(Collectors.toList());
//        //查询所有对应的asin
//        List<String> adIds = hourVos.stream().map(AdKeywordAndTargetHourVo::getAdId).distinct().collect(Collectors.toList());
//        List<AmazonAdProduct> adProducts = amazonAdProductDao.getByAdIds(puid, shopAuth.getId(), adIds);
//        Map<String, AmazonAdProduct> adProductMap = adProducts.stream().collect(Collectors.toMap(
//                AmazonAdProduct::getAdId, Function.identity(), (a, b) -> a));
//        //填充asin信息
//        List<AdProductHourVo> voList = hourVos.stream().peek(item -> {
//            if (adProductMap.containsKey(item.getAdId())) {
//                item.setAsin(adProductMap.get(item.getAdId()).getAsin());
//            }
//        }).collect(Collectors.toList());
        //时段无数据填充0值
        Map<String, List<AdProductHourVo>> voMap =
                hourVos.stream().collect(Collectors.groupingBy(AdProductHourVo::getKeywordId));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdProductHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setKeywordId(k);
                vo.setKeywordText(v.get(0).getKeywordText());
                hourVos.add(vo);
            }
        });

        return hourVos.stream().sorted(Comparator.comparing(AdProductHourVo::getKeywordId)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdProductHourOfPlacementVo> getListOfPlacement(int puid, ProductHourParam param) {
        return convertToHourOfPlacementVos(getDetailListOfPlacement(puid, param));
    }

    @Override
    public List<AdProductHourVo> getDetailListOfPlacement(int puid, ProductHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getAdId())) {
            selectDto.setAdIds(Collections.singletonList(param.getAdId()));
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsProductHourlyReportOfPlacement(selectDto);

        //pb对象转vo对象
        List<AdProductHourVo> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdProductHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdProductHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdProductHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdProductHourVo::getPlacementOrder)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());

    }

    private List<AdProductHourOfKeywordVo> convertToHourOfAdVos(List<AdProductHourVo> hourVos) {
        //按asin汇总数据
        Map<String, List<AdProductHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductHourVo::getKeywordId));

        List<AdProductHourOfKeywordVo> adProductHourOfKeywordVos = new ArrayList<>(hourVoMap.size());

        for (Map.Entry<String, List<AdProductHourVo>> entry : hourVoMap.entrySet()) {
            AdProductHourOfKeywordVo adProductHourOfKeywordVo = new AdProductHourOfKeywordVo();
            adProductHourOfKeywordVo.setKeywordId(entry.getKey());
            adProductHourOfKeywordVo.setKeywordText(entry.getValue().get(0).getKeywordText());
            List<AdProductHourVo> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(asinHourVos, adMetricDto);
            filterMetricData(asinHourVos, adMetricDto);
            adProductHourOfKeywordVo.setDetails(asinHourVos);
            adProductHourOfKeywordVo.staticsFromHourVos(asinHourVos);
            adProductHourOfKeywordVos.add(adProductHourOfKeywordVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adProductHourOfKeywordVos, adMetricDto1);
        metricData(adProductHourOfKeywordVos, adMetricDto1);
        return adProductHourOfKeywordVos;
    }


    private List<AdProductHourOfPlacementVo> convertToHourOfPlacementVos(List<AdProductHourVo> hourVos) {
        //按广告位汇总数据
        Map<String, List<AdProductHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductHourVo::getPlacement));

        List<AdProductHourOfPlacementVo> adProductHourOfPlacementVos = new ArrayList<>(3);

        for (Map.Entry<String, List<AdProductHourVo>> entry : hourVoMap.entrySet()) {
            AdProductHourOfPlacementVo adProductHourOfPlacementVo = new AdProductHourOfPlacementVo();
            List<AdProductHourVo> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(asinHourVos, adMetricDto);
            filterMetricData(asinHourVos, adMetricDto);
            adProductHourOfPlacementVo.setDetails(asinHourVos);
            adProductHourOfPlacementVo.staticsFromHourVos(asinHourVos);
            adProductHourOfPlacementVo.setPlacement(entry.getKey());
            adProductHourOfPlacementVos.add(adProductHourOfPlacementVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adProductHourOfPlacementVos, adMetricDto1);
        metricData(adProductHourOfPlacementVos, adMetricDto1);
        return adProductHourOfPlacementVos.stream().collect(Collectors.toList());
    }

    private List<AdProductWeekDayVo> convertToHourOfWeekDayVos(List<AdProductHourVo> hourVos) {
        //按周汇总数据
        Map<Integer, List<AdProductHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductHourVo::getWeekDay));

        List<AdProductWeekDayVo> adProductWeekDayVos = new ArrayList<>(7);

        for (Map.Entry<Integer, List<AdProductHourVo>> entry : hourVoMap.entrySet()) {
            AdProductWeekDayVo adProductWeekDayVo = new AdProductWeekDayVo();
            List<AdProductHourVo> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(asinHourVos, adMetricDto);
            filterMetricData(asinHourVos, adMetricDto);
            adProductWeekDayVo.setDetails(asinHourVos);
            adProductWeekDayVo.staticsFromHourVos(asinHourVos);
            adProductWeekDayVo.calculateCompareRate();
            adProductWeekDayVo.setWeekDay(entry.getKey());
            adProductWeekDayVos.add(adProductWeekDayVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adProductWeekDayVos, adMetricDto1);
        metricData(adProductWeekDayVos, adMetricDto1);
        return adProductWeekDayVos.stream().collect(Collectors.toList());
    }

    private void filterSumMetricData(List<AdProductHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdProductHourVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdProductHourVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdProductHourVo> voList, AdMetricDto adMetricDto) {
        for (AdProductHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdProductHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private void sumMetricData(List<? extends AdProductHourBaseVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdProductHourBaseVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdProductHourBaseVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void metricData(List<? extends AdProductHourBaseVo> voList, AdMetricDto adMetricDto) {
        for (AdProductHourBaseVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            metricData(adMetricDto, vo);
        }
    }

    private void metricData(AdMetricDto adMetricDto, AdProductHourBaseVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private AdProductHourVo handleVo(Integer hour, HourlyReportDataPb.HourlyReportData data,
                                     HourlyReportDataPb.HourlyReportData dataCompare) {
        AdProductHourVo adProductHourVoCompare = convertTo(dataCompare);
        AdProductHourVo adProductHourVo = convertTo(data);
        AdProductHourVo vo = AdProductHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adProductHourVo.getAdSale())
                .adSelfSale(adProductHourVo.getAdSelfSale())
                .adOtherSale(adProductHourVo.getAdOtherSale())
                .adOrderNum(adProductHourVo.getAdOrderNum())
                .selfAdOrderNum(adProductHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adProductHourVo.getOtherAdOrderNum())
                .adSaleNum(adProductHourVo.getAdSaleNum())
                .adSelfSaleNum(adProductHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adProductHourVo.getAdOtherSaleNum())
                .adCost(adProductHourVo.getAdCost())
                .clicks(adProductHourVo.getClicks())
                .impressions(adProductHourVo.getImpressions())
                .adCostPerClick(adProductHourVo.getAdCostPerClick())
                .cpa(adProductHourVo.getCpa())
                .acos(adProductHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adProductHourVo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adProductHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adProductHourVo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adProductHourVo.getClicks())))
                .roas(adProductHourVo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adProductHourVo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adProductHourVo.getAdSale().divide(adProductHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adProductHourVoCompare.getClicks())
                .impressionsCompare(adProductHourVoCompare.getImpressions())
                .adSaleNumCompare(adProductHourVoCompare.getAdSaleNum())
                .adSaleCompare(adProductHourVoCompare.getAdSale())
                .adCostCompare(adProductHourVoCompare.getAdCost())
                .adOrderNumCompare(adProductHourVoCompare.getAdOrderNum())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    private AdProductHourVo handleVo(Integer hour, AmazonMarketingStreamData data,
                                     AmazonMarketingStreamData dataCompare) {
        AdProductHourVo adProductHourVoCompare = convertTo(dataCompare);
        AdProductHourVo adProductHourVo = convertTo(data);
        AdProductHourVo vo = AdProductHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adProductHourVo.getAdSale())
                .adSelfSale(adProductHourVo.getAdSelfSale())
                .adOtherSale(adProductHourVo.getAdOtherSale())
                .adOrderNum(adProductHourVo.getAdOrderNum())
                .selfAdOrderNum(adProductHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adProductHourVo.getOtherAdOrderNum())
                .adSaleNum(adProductHourVo.getAdSaleNum())
                .adSelfSaleNum(adProductHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adProductHourVo.getAdOtherSaleNum())
                .adCost(adProductHourVo.getAdCost())
                .clicks(adProductHourVo.getClicks())
                .impressions(adProductHourVo.getImpressions())
                .adCostPerClick(adProductHourVo.getAdCostPerClick())
                .cpa(adProductHourVo.getCpa())
                .acos(adProductHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adProductHourVo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adProductHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adProductHourVo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adProductHourVo.getClicks())))
                .roas(adProductHourVo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adProductHourVo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adProductHourVo.getAdSale().divide(adProductHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adProductHourVoCompare.getClicks())
                .impressionsCompare(adProductHourVoCompare.getImpressions())
                .adSaleNumCompare(adProductHourVoCompare.getAdSaleNum())
                .adSaleCompare(adProductHourVoCompare.getAdSale())
                .adCostCompare(adProductHourVoCompare.getAdCost())
                .adOrderNumCompare(adProductHourVoCompare.getAdOrderNum())
                .acosCompare(adProductHourVoCompare.getAcos())
                .adCostPerClickCompare(adProductHourVoCompare.getAdCostPerClick())
                .cvrCompare(adProductHourVoCompare.getCvr())
                .ctrCompare(adProductHourVoCompare.getCtr())
                .roasCompare(adProductHourVoCompare.getRoas())
                .build();
        vo.calculateCompareRate();//为各对比率属性设值
        return vo;
    }

    private AdProductHourVo convertTo(HourlyReportDataPb.HourlyReportData data) {
        AdProductHourVo vo = new AdProductHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setWeekDay(data.getWeekday());
        vo.setKeywordId(data.getKeywordId());
        vo.setKeywordText(data.getKeywordText());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdProductHourVo convertTo(AmazonMarketingStreamData data) {
        AdProductHourVo vo = new AdProductHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setWeekDay(data.getWeekday());
        vo.setKeywordId(data.getKeywordId());
        vo.setKeywordText(data.getKeywordText());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(
            ProductHourlyReportResponsePb.ProductHourlyReportResponse statisticsByHourResponse,
            Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }


    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMap(
            List<AmazonMarketingStreamData> statisticsByHourResponse,
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourResponse)) {
            hourlyReportDataMap = statisticsByHourResponse.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }


    @Override
    public List<AdProductHourVo> getHourListAll(int puid, ProductHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStart(LocalDate.parse(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setWeekdayList(Optional.ofNullable(param.getWeeks()).filter(StringUtils::isNotBlank).
                map(w -> StringUtil.splitInt(param.getWeeks(), ",")).orElse(HourConvert.weeKs));

        //获取小时对比数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getFeedHourlyReport(builder, param.getAdIds());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            hourlyReportDataCompareMap = getFeedHourlyReport(builder, param.getAdIds());
        }

        //组装数据

        List<AdProductHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdProductHourVo adProductHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adProductHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }


    private Map<Integer, HourlyReportDataPb.HourlyReportData> getFeedHourlyReport(ProductHourlyReportRequestPb.ProductHourlyReportRequest.Builder builder, List<String> adIds) {
        if (CollectionUtils.isEmpty(adIds)) {
            return new HashMap<>();
        }

        List<ProductHourlyReportResponsePb.ProductHourlyReportResponse> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> adIds, builder, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, HourlyReportDataPb.HourlyReportData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0).map(ProductHourlyReportResponsePb.ProductHourlyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }

        return reportDataMap;

    }

    private Map<Integer, AmazonMarketingStreamData> getFeedHourlyReport(FeedHourlySelectDTO builder, List<String> adIds) {
        if (CollectionUtils.isEmpty(adIds)) {
            return new HashMap<>();
        }

        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> adIds, builder, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(Objects::nonNull).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }

        return reportDataMap;

    }

    private ProductHourlyReportResponsePb.ProductHourlyReportResponse callFeedHourlyReport(ProductHourlyReportRequestPb.ProductHourlyReportRequest.Builder builder, List<String> adIds) {

        ProductHourlyReportRequestPb.ProductHourlyReportRequest.Builder sendBuilder = ProductHourlyReportRequestPb.ProductHourlyReportRequest.newBuilder();
        sendBuilder.setSellerId(builder.getSellerId());
        sendBuilder.setMarketplaceId(builder.getMarketplaceId());
        sendBuilder.setStartDate(builder.getStartDate());
        sendBuilder.setEndDate(builder.getEndDate());
        if (CollectionUtils.isNotEmpty(builder.getWeekdayList())) {
            sendBuilder.addAllWeekday(builder.getWeekdayList());
        }
        sendBuilder.addAllAdId(adIds);
        return adFeedBlockingStub.statisticsProductHourlyReport(sendBuilder.build());
    }

    private List<AmazonMarketingStreamData> callFeedHourlyReport(FeedHourlySelectDTO builder, List<String> adIds) {
        FeedHourlySelectDTO queryDto = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(builder, queryDto);
        queryDto.setAdIds(adIds);
        return amazonMarketingStreamDataDao.listBitmapByHourly(queryDto);
    }


    private HourlyReportDataPb.HourlyReportData aggregationHourlyReport(HourlyReportDataPb.HourlyReportData value1, HourlyReportDataPb.HourlyReportData value2) {
        HourlyReportDataPb.HourlyReportData.Builder newBuilder = HourlyReportDataPb.HourlyReportData.newBuilder();
        newBuilder.setKeywordId(value1.getKeywordId());
        newBuilder.setAdId(value1.getAdId());
        newBuilder.setAdGroupId(value1.getAdGroupId());
        newBuilder.setDate(value1.getDate());
        newBuilder.setKeywordText(value1.getKeywordText());
        newBuilder.setPlacement(value1.getPlacement());
        newBuilder.setTime(value1.getTime());


        newBuilder.setAttributedConversions1D(MathUtil.sumInteger(value1.getAttributedConversions1D(), value2.getAttributedConversions1D()));
        newBuilder.setAttributedConversions7D(MathUtil.sumInteger(value1.getAttributedConversions7D(), value2.getAttributedConversions7D()));
        newBuilder.setAttributedConversions14D(MathUtil.sumInteger(value1.getAttributedConversions14D(), value2.getAttributedConversions14D()));
        newBuilder.setAttributedConversions30D(MathUtil.sumInteger(value1.getAttributedConversions30D(), value2.getAttributedConversions30D()));
        newBuilder.setAttributedConversions1DSameSku(MathUtil.sumInteger(value1.getAttributedConversions1DSameSku(), value2.getAttributedConversions1DSameSku()));
        newBuilder.setAttributedConversions7DSameSku(MathUtil.sumInteger(value1.getAttributedConversions7DSameSku(), value2.getAttributedConversions7DSameSku()));
        newBuilder.setAttributedConversions14DSameSku(MathUtil.sumInteger(value1.getAttributedConversions14DSameSku(), value2.getAttributedConversions14DSameSku()));
        newBuilder.setAttributedConversions30DSameSku(MathUtil.sumInteger(value1.getAttributedConversions30DSameSku(), value2.getAttributedConversions30DSameSku()));

        newBuilder.setAttributedSales1D(MathUtil.sum(value1.getAttributedSales1D(), value2.getAttributedSales1D()));
        newBuilder.setAttributedSales7D(MathUtil.sum(value1.getAttributedSales7D(), value2.getAttributedSales7D()));
        newBuilder.setAttributedSales14D(MathUtil.sum(value1.getAttributedSales14D(), value2.getAttributedSales14D()));
        newBuilder.setAttributedSales30D(MathUtil.sum(value1.getAttributedSales30D(), value2.getAttributedSales30D()));
        newBuilder.setAttributedSales1DSameSku(MathUtil.sum(value1.getAttributedSales1DSameSku(), value2.getAttributedSales1DSameSku()));
        newBuilder.setAttributedSales7DSameSku(MathUtil.sum(value1.getAttributedSales7DSameSku(), value2.getAttributedSales7DSameSku()));
        newBuilder.setAttributedSales14DSameSku(MathUtil.sum(value1.getAttributedSales14DSameSku(), value2.getAttributedSales14DSameSku()));
        newBuilder.setAttributedSales30DSameSku(MathUtil.sum(value1.getAttributedSales30DSameSku(), value2.getAttributedSales30DSameSku()));

        newBuilder.setAttributedUnitsOrdered1D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered1D(), value2.getAttributedUnitsOrdered1D()));
        newBuilder.setAttributedUnitsOrdered7D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered7D(), value2.getAttributedUnitsOrdered7D()));
        newBuilder.setAttributedUnitsOrdered14D(MathUtil.sum(value1.getAttributedUnitsOrdered14D(), value2.getAttributedUnitsOrdered14D()));
        newBuilder.setAttributedUnitsOrdered30D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered30D(), value2.getAttributedUnitsOrdered30D()));
        newBuilder.setAttributedUnitsOrdered1DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered1DSameSku(), value2.getAttributedUnitsOrdered1DSameSku()));
        newBuilder.setAttributedUnitsOrdered7DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered7DSameSku(), value2.getAttributedUnitsOrdered7DSameSku()));
        newBuilder.setAttributedUnitsOrdered14DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered14DSameSku(), value2.getAttributedUnitsOrdered14DSameSku()));
        newBuilder.setAttributedUnitsOrdered30DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered30DSameSku(), value2.getAttributedUnitsOrdered30DSameSku()));


        newBuilder.setClicks(MathUtil.sumLong(value1.getClicks(), value2.getClicks()));
        newBuilder.setCost(MathUtil.sum(value1.getCost(), value2.getCost()));
        newBuilder.setImpressions(MathUtil.sumLong(value1.getImpressions(), value2.getImpressions()));

        return newBuilder.build();

    }


    @Override
    public List<AdProductHourVo> getDailyListAll(int puid, String adType, ProductHourParam param, List<AdProductHourVo> compares) {
        List<AdProductHourVo> reports = getAdProductDailyReportsAll(puid, adType, param);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdProductHourVo> compareList = getAdProductDailyReportsAll(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdProductHourVo> getAdProductDailyReportsAll(int puid, String adType, ProductHourParam param) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), param.getPuid());
        if (CollectionUtils.isEmpty(param.getAdIds())) {
            return new ArrayList<>();
        }
        //日,周,月报告数据
        if ("SP".equalsIgnoreCase(adType)) {
            List<AmazonAdProductReport> reports =
                    amazonAdProductReportDao.getReportByAdIdsGroupByCountDate(puid, param.getShopId(),
                            start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getAdIds());
            return reports.stream().map(item -> {
                AdProductHourVo vo = new AdProductHourVo();

                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getTotalSales());
                vo.setAdSelfSale(item.getAdSales());
                vo.setAdOtherSale(item.getAdOtherSales());
                vo.setAdOrderNum(item.getSaleNum());
                vo.setSelfAdOrderNum(item.getAdSaleNum());
                vo.setOtherAdOrderNum(item.getAdOtherOrderNum());
                vo.setAdSaleNum(item.getOrderNum());
                vo.setAdSelfSaleNum(item.getAdOrderNum());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNum());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        } else if ("SD".equalsIgnoreCase(adType)) {
            List<AmazonAdSdProductReport> reports =
                    amazonAdSdProductReportDao.listReportsByAdIdsGroupByCountDate(puid, param.getShopId(),
                            shopAuth.getMarketplaceId(),
                            start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            param.getAdIds());
            return reports.stream().map(item -> {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getSales14d());
                vo.setAdSelfSale(item.getSales14dSameSKU());
                vo.setAdOtherSale(item.getSales14d().subtract(item.getSales14dSameSKU()));
                vo.setAdOrderNum(item.getConversions14d());
                vo.setSelfAdOrderNum(item.getConversions14dSameSKU());
                vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSKU());
                vo.setAdSaleNum(item.getConversions14d());
                vo.setAdSelfSaleNum(item.getConversions14dSameSKU());
                vo.setAdOtherSaleNum(item.getConversions14d() - item.getConversions14dSameSKU());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        }
        return null;
    }


    @Override
    public List<AdProductHourVo> getWeeklyListAll(int puid, String adType, ProductHourParam param, List<AdProductHourVo> compares) {
        List<AdProductHourVo> reports = getAdProductDailyReportsAll(puid, adType, param);
        reports = ReportChartUtil.getProductWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdProductHourVo> compareList = getAdProductDailyReportsAll(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getProductWeekReportVos(param.getStartDate(), param.getEndDate(), compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    @Override
    public List<AdProductHourVo> getMonthlyListAll(int puid, String adType, ProductHourParam param, List<AdProductHourVo> compares) {
        List<AdProductHourVo> reports = getAdProductDailyReportsAll(puid, adType, param);
        reports = ReportChartUtil.getProductMonthReportVos(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdProductHourVo> compareList = getAdProductDailyReportsAll(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getProductMonthReportVos(compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingMonthCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }


    @Override
    public List<AdProductHourOfPlacementVo> getListOfPlacementAll(int puid, ProductHourParam param) {
        return convertToHourOfPlacementVos(getDetailListOfPlacementAll(puid, param));
    }


    @Override
    public List<AdProductHourVo> getDetailListOfPlacementAll(int puid, ProductHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        builder.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()));
        builder.setEndDate(param.getEndDate());

        //pb对象转vo对象
        List<AmazonMarketingStreamData> dataList = getFeedHourlyOfPlacementReport(builder, param.getAdIds());
        List<AdProductHourVo> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdProductHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdProductHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdProductHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdProductHourVo::getPlacementOrder)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());

    }


    private List<AmazonMarketingStreamData> getFeedHourlyOfPlacementReport(CampaignHourlyReportSelectDto builder, List<String> adIds) {
        if (CollectionUtils.isEmpty(adIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> adIds, builder, (x, y) -> callFeedHourlyOfPlacementReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getPlacement();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;


    }

    private List<AmazonMarketingStreamData> callFeedHourlyOfPlacementReport(CampaignHourlyReportSelectDto builder, List<String> adIds) {
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(builder, queryDto);
        queryDto.setAdIds(adIds);
        return amazonMarketingStreamDataDao.statisticsProductHourlyReportOfPlacement(queryDto);
    }

    @Override
    public List<AdProductWeekDayVo> getWeeklySuperpositionListAll(int puid, ProductHourParam param) {
        return convertToHourOfWeekDayVos(getWeeklySuperpositionDailyListAll(puid, param));
    }

    @Override
    public List<AdProductHourVo> getWeeklySuperpositionDailyListAll(int puid, ProductHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return Collections.emptyList();
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        queryDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        queryDto.setMarketplaceId(shopAuth.getMarketplaceId());
        queryDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()));
        queryDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getAdId())) {
            queryDto.setAdIds(Collections.singletonList(param.getAdId()));
        }
        //pb对象转vo对象
        List<AmazonMarketingStreamData> dataList = getFeedProductWeeklyReport(queryDto, param.getAdIds());
        List<AdProductHourVo> voList = dataList.stream().map(this::convertTo).collect(Collectors.toList());

        Map<Integer, List<AdProductHourVo>> compareHourMap = Maps.newHashMap();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            queryDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()));
            queryDto.setEndDate(param.getEndDateCompare());
            List<AmazonMarketingStreamData> compareList = getFeedProductWeeklyReport(queryDto, param.getAdIds());
            if (CollectionUtils.isNotEmpty(compareList)){
                List<AdProductHourVo> compareVOs = compareList.stream().map(this::convertTo).collect(Collectors.toList());
                compareHourMap.putAll(StreamUtil.groupingBy(compareVOs, AdProductHourVo::getWeekDay));
            }
        }

        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdProductHourVo::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdProductHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdProductHourVo::getWeekDay));
        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
        needFilledWeek.forEach(e -> {
            List<AdProductHourVo> adProductHourVos = new ArrayList<>();
            voMap.put(e, adProductHourVos);
        });
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<AdProductHourVo> compareHourVOs = compareHourMap.get(k);
            Map<String, AdProductHourVo> compareHourVOMap = StreamUtil.toMap(compareHourVOs, AdProductHourVo::getLabel);

            List<String> hourList = v.stream().map(AdProductHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdProductHourVo vo = new AdProductHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setWeekDay(k);
                voList.add(vo);
                if (compareHourVOMap.containsKey(hour)) {
                    AdProductHourVo compareItem = compareHourVOMap.get(hour);
                    vo.compareDataSet(compareItem);
                }
            }
            for (AdProductHourVo vo : v) {
                if (compareHourVOMap.containsKey(vo.getLabel())) {
                    AdProductHourVo compareItem = compareHourVOMap.get(vo.getLabel());
                    vo.compareDataSet(compareItem);
                }
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdProductHourVo::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }


    private List<AmazonMarketingStreamData> getFeedProductWeeklyReport(CampaignHourlyReportSelectDto queryDto, List<String> adIds) {
        if (CollectionUtils.isEmpty(adIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> adIds, queryDto, (x, y) -> callFeedProductWeeklyReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getWeekday();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;
    }

    private List<AmazonMarketingStreamData> callFeedProductWeeklyReport(CampaignHourlyReportSelectDto baseDto, List<String> adIds) {
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(baseDto, selectDto);
        selectDto.setAdIds(adIds);
        return amazonMarketingStreamDataDao.statisticsProductWeeklySuperpositionReport(selectDto);
    }

}
