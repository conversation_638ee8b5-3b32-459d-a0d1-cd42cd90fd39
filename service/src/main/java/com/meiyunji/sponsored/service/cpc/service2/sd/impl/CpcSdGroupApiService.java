package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.adGroups.AdGroupResult;
import com.amazon.advertising.sd.constant.TacticEnum;
import com.amazon.advertising.sd.entity.group.*;
import com.amazon.advertising.sd.mode.AdGroup;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SPadGroupVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.syncAd.enums.SdStateEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/7/7.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSdGroupApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Autowired
    private IDorisService dorisService;

    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public Result create(ShopAuth shop, AmazonAdProfile amazonAdProfile, AmazonSdAdGroup amazonSdAdGroup) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AdGroup adGroup = makeEntityForCreation(amazonSdAdGroup);

        CreateAdGroupsResponse response = cpcApiHelper.call(shop, () -> AdGroupsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Lists.newArrayList(adGroup)));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            AdGroupResult result = response.getResultList().get(0);
            if ("SUCCESS".equals(result.getCode())) {
                amazonSdAdGroup.setAdGroupId(result.getAdGroupId().toString());
                return ResultUtil.success();
            }

            if (StringUtils.isNotBlank(result.getDetails())) {
                errMsg = result.getDetails();
            } else if (StringUtils.isNotBlank(result.getDescription())) {
                errMsg = result.getDescription();
            }
        } else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SD广告权限，请到Amazon后台开通SD广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails();
            }
        }

        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }

    public Result update(ShopAuth shop, AmazonAdProfile amazonAdProfile, AmazonSdAdGroup amazonSdAdGroup) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AdGroup adGroup = makeEntityForUpdate(amazonSdAdGroup);

        UpdateAdGroupsResponse response = cpcApiHelper.call(shop, () -> AdGroupsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Lists.newArrayList(adGroup)));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            AdGroupResult result = response.getResultList().get(0);
            if ("SUCCESS".equals(result.getCode())) {
                return ResultUtil.success();
            }

            if (StringUtils.isNotBlank(result.getDetails())) {
                errMsg = result.getDetails();
            }
        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDetails())) {
            errMsg = response.getError().getDetails();
        }

        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }

    /**
     * 同步所有的活动
     *
     * @param shop：
     */
    public void syncAdGroups(ShopAuth shop, String campaignId, String adGroupIds, List<SdStateEnum> stateList, boolean isThrow, Consumer<List<AmazonSdAdGroup>> callback, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdGroup--配置信息为空");
            return;
        }

        //获取活动的基本信息
        AdGroupsClient client =  AdGroupsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = AdGroupsClient.getInstance(true);
        }
        int startIndex = 0;
        int count = 500;
        ListAdGroupsExResponse response;

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(SdStateEnum.values()).map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        }

        while (true) {
            int finalSartIndex = startIndex;
            AdGroupsClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, stateFilter, null, adGroupIds, campaignId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD adgroup rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                AdGroupsClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, stateFilter, null, null, campaignId));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && isThrow) {
                throw new ServiceException("请求接口异常");
            }

            if (response == null || CollectionUtils.isEmpty(response.getResultList())) {
                break;
            }

            int size = response.getResultList().size();
            AmazonSdAdGroup amazonSdAdGroup;
            List<AmazonSdAdGroup> amazonSdAdGroups = new ArrayList<>(size);
            for (AdGroup adGroup : response.getResultList()) {
                if (TacticEnum.T00030.name().equals(adGroup.getTactic())
                        || TacticEnum.T00020.name().equals(adGroup.getTactic())) {
                    amazonSdAdGroup = turnEntityToPO(adGroup);
                    if (StringUtils.isNotBlank(amazonSdAdGroup.getAdGroupId())) {
                        amazonSdAdGroup.setPuid(shop.getPuid());
                        amazonSdAdGroup.setShopId(shop.getId());
                        amazonSdAdGroup.setMarketplaceId(shop.getMarketplaceId());
                        amazonSdAdGroup.setProfileId(amazonAdProfile.getProfileId());
                        amazonSdAdGroups.add(amazonSdAdGroup);
                    }
                }
            }

            if (amazonSdAdGroups.size() > 0) {
                Map<String, AmazonSdAdGroup> groupMap = amazonSdAdGroupDao.listByGroupId(shop.getPuid(), shop.getId(),
                        amazonSdAdGroups.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList()))
                        .stream().filter(e -> StringUtils.isNotBlank(e.getAdGroupId()))
                        .collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity()));

                List<AmazonSdAdGroup> insertList = new ArrayList<>();
                List<AmazonSdAdGroup> updateList = new ArrayList<>();
                AmazonSdAdGroup old;

                for (AmazonSdAdGroup c : amazonSdAdGroups) {
                    if (groupMap.containsKey(c.getAdGroupId())) {
                        old = groupMap.get(c.getAdGroupId());
                        if (StringUtils.isNotBlank(c.getName())) {
                            old.setName(c.getName());
                        }
                        if (c.getDefaultBid() != null) {
                            old.setDefaultBid(c.getDefaultBid());
                        }
                        if (StringUtils.isNotBlank(c.getState())) {
                            old.setState(c.getState());
                        }
                        if (StringUtils.isNotBlank(c.getTactic())) {
                            old.setTactic(c.getTactic());
                        }
                        if (StringUtils.isNotBlank(c.getServingStatus())) {
                            old.setServingStatus(c.getServingStatus());
                        }
                        if (StringUtils.isNotBlank(c.getBidOptimization())) {
                            old.setBidOptimization(c.getBidOptimization());
                        }
                        if (c.getCreationDate() != null) {
                            old.setCreationDate(c.getCreationDate());
                        }
                        if (c.getLastUpdatedDate() != null) {
                            old.setLastUpdatedDate(c.getLastUpdatedDate());
                        }
                        if (StringUtils.isNotBlank(c.getCreativeType())) {
                            old.setCreativeType(c.getCreativeType());
                        }
                        updateList.add(old);
                    } else {
                        c.setCreateInAmzup(0);
                        insertList.add(c);
                    }
                }

                try {
                    amazonSdAdGroupDao.batchAdd(shop.getPuid(), insertList);
                    amazonSdAdGroupDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSdGroup:", e);
                }

                //执行回调
                if (callback != null) {
                    callback.accept(amazonSdAdGroups);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
    }

    public void syncAdGroups(ShopAuth shop, String campaignId, String adGroupIds, boolean isThrow) {
        syncAdGroups(shop, campaignId, adGroupIds, null, isThrow, null);
    }

    public void syncAdGroups(ShopAuth shop, String campaignId, String adGroupIds) {
        syncAdGroups(shop, campaignId, adGroupIds, false);
    }

    public void syncAdGroups(ShopAuth shop, String campaignId, String adGroupIds, List<SdStateEnum> stateList, boolean isThrow, Consumer<List<AmazonSdAdGroup>> callback) {
        syncAdGroups(shop, campaignId, adGroupIds, stateList, isThrow, callback, false);
    }


    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String groupIds){
        List<AmazonSdAdGroup> amazons = amazonSdAdGroupDao.listByGroupId(puid, shopId, StringUtil.stringToList(groupIds,","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
            key.setServingStatus(key.getServingStatus());
            return AmazonServingStatusDto.build(key.getAdGroupId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());}).collect(Collectors.toList());
    }

    /**
     * 归档
     *
     * @param amazonSdAdGroup：
     * @return ：Result
     */
    public Result archive(AmazonSdAdGroup amazonSdAdGroup) {
        if (amazonSdAdGroup == null) {
            return ResultUtil.error("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonSdAdGroup.getShopId(), amazonSdAdGroup.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        ArchiveAdGroupResponse response = cpcApiHelper.call(shop, () -> AdGroupsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop),
                amazonSdAdGroup.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonSdAdGroup.getAdGroupId())));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getAdGroupId() != null) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            msg = AmazonErrorUtils.getError(response.getResult().getDetails());
        }
        return ResultUtil.error(msg);
    }

    private AdGroup makeEntityForCreation(AmazonSdAdGroup amazonSdAdGroup) {
        AdGroup adGroup = new AdGroup();
        if (StringUtils.isNotBlank(amazonSdAdGroup.getCampaignId())) {
            adGroup.setCampaignId(Long.valueOf(amazonSdAdGroup.getCampaignId()));
        }
        adGroup.setName(amazonSdAdGroup.getName());
        if (amazonSdAdGroup.getDefaultBid() != null) {
            adGroup.setDefaultBid(amazonSdAdGroup.getDefaultBid().doubleValue());
        }
        if(StringUtils.isNotBlank(amazonSdAdGroup.getBidOptimization())){
            adGroup.setBidOptimization(amazonSdAdGroup.getBidOptimization());
        }
        adGroup.setState(amazonSdAdGroup.getState());
        adGroup.setTactic(amazonSdAdGroup.getTactic());
        Optional.ofNullable(amazonSdAdGroup.getCreativeType()).filter(StringUtils::isNotEmpty).ifPresent(adGroup::setCreativeType);
        return adGroup;
    }

    private AdGroup makeEntityForUpdate(AmazonSdAdGroup amazonSdAdGroup) {
        AdGroup adGroup = new AdGroup();
        if (StringUtils.isNotBlank(amazonSdAdGroup.getCampaignId())) {
            adGroup.setCampaignId(Long.valueOf(amazonSdAdGroup.getCampaignId()));
        }
        if (StringUtils.isNotBlank(amazonSdAdGroup.getAdGroupId())) {
            adGroup.setAdGroupId(Long.valueOf(amazonSdAdGroup.getAdGroupId()));
        }
        if (StringUtils.isNotBlank(amazonSdAdGroup.getName())) {
            adGroup.setName(amazonSdAdGroup.getName());
        }
        if (amazonSdAdGroup.getDefaultBid() != null) {
            adGroup.setDefaultBid(amazonSdAdGroup.getDefaultBid().doubleValue());
        }
        if (StringUtils.isNotBlank(amazonSdAdGroup.getState())) {
            adGroup.setState(amazonSdAdGroup.getState());
        }
        if(StringUtils.isNotBlank(amazonSdAdGroup.getBidOptimization())){
            adGroup.setBidOptimization(amazonSdAdGroup.getBidOptimization());
        }
        return adGroup;
    }

    // 把接口返回的dto转换成po
    private AmazonSdAdGroup turnEntityToPO(AdGroup adGroup) {
        AmazonSdAdGroup amazonSdAdGroup = new AmazonSdAdGroup();
        if (adGroup.getCampaignId() != null) {
            amazonSdAdGroup.setCampaignId(adGroup.getCampaignId().toString());
        }
        if (adGroup.getAdGroupId() != null) {
            amazonSdAdGroup.setAdGroupId(adGroup.getAdGroupId().toString());
        }
        amazonSdAdGroup.setName(adGroup.getName());

        if (adGroup.getDefaultBid() != null && adGroup.getDefaultBid() > 0) {
            amazonSdAdGroup.setDefaultBid(BigDecimal.valueOf(adGroup.getDefaultBid()));
        }

        amazonSdAdGroup.setState(adGroup.getState());
        amazonSdAdGroup.setTactic(adGroup.getTactic());

        amazonSdAdGroup.setServingStatus(adGroup.getServingStatus());
        amazonSdAdGroup.setBidOptimization(adGroup.getBidOptimization());

        if (StringUtils.isNotBlank(adGroup.getCreationDate())) {
            amazonSdAdGroup.setCreationDate(DateUtil.getDateByMillisecond(Long.valueOf(adGroup.getCreationDate())));
        }
        if (StringUtils.isNotBlank(adGroup.getLastUpdatedDate())) {
            amazonSdAdGroup.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(adGroup.getLastUpdatedDate())));
        }

        Optional.ofNullable(adGroup.getCreativeType()).filter(StringUtils::isNotEmpty).ifPresent(amazonSdAdGroup::setCreativeType);

        return amazonSdAdGroup;
    }

    public Result<BatchResponseVo<SPadGroupVo,AmazonSdAdGroup>> update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdGroup> amazonSdAdGroups,String type) {
        BatchResponseVo<SPadGroupVo,AmazonSdAdGroup> batchResponseVo = new BatchResponseVo<>();
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<AdGroup> campaigns = amazonSdAdGroups.stream().map(e -> {
            return convertGroup(e, type);
        }).collect(Collectors.toList());

        UpdateAdGroupsResponse response = cpcApiHelper.call(shop, () -> AdGroupsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), campaigns));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        Map<String, AmazonSdAdGroup> amazonSdAdGroupMap = amazonSdAdGroups.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
        List<AmazonSdAdGroup> successList = Lists.newArrayList();
        List<SPadGroupVo> errorList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getResultList() != null && response.getResultList().size() > 0) {

            List<AdGroupResult> resultList = response.getResultList();
            List<Long> successId = Lists.newArrayList();
            for (AdGroupResult adGroupResult : resultList) {

                if ("SUCCESS".equals(adGroupResult.getCode())) {
                    AmazonSdAdGroup amazonSdAdGroupSuccess = amazonSdAdGroupMap.remove(String.valueOf(adGroupResult.getAdGroupId()));
                    if (amazonSdAdGroupSuccess != null) {
                        successList.add(amazonSdAdGroupSuccess);
                    }
                    successId.add(amazonSdAdGroupSuccess.getId());
                } else {
                    AmazonSdAdGroup amazonSdAdGroupFail = amazonSdAdGroupMap.remove(String.valueOf(adGroupResult.getAdGroupId()));
                    if (amazonSdAdGroupFail != null) {
                        SPadGroupVo sPadGroupVoError = new SPadGroupVo();
                        sPadGroupVoError.setGroupId(amazonSdAdGroupFail.getAdGroupId());
                        sPadGroupVoError.setName(amazonSdAdGroupFail.getName());
                        sPadGroupVoError.setDxmGroupId(amazonSdAdGroupFail.getId());
                        sPadGroupVoError.setId(amazonSdAdGroupFail.getId());
                        //更新失败数据处理
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(adGroupResult.getDescription())) {
                            sPadGroupVoError.setFailReason(AmazonErrorUtils.getError(adGroupResult.getDescription()));
                        } else {
                            sPadGroupVoError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(sPadGroupVoError);
                    }

                }
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSdAdGroupMap)) {
                amazonSdAdGroupMap.forEach((k, v) -> {
                    SPadGroupVo sPadGroupVoError = new SPadGroupVo();
                    sPadGroupVoError.setCampaignId(v.getCampaignId());
                    sPadGroupVoError.setName(v.getName());
                    sPadGroupVoError.setId(v.getId());
                    sPadGroupVoError.setGroupId(v.getAdGroupId());
                    sPadGroupVoError.setDxmGroupId(v.getId());
                    sPadGroupVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sPadGroupVoError);
                });
            }

        } else if (response.getError() != null && org.apache.commons.lang3.StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSdAdGroupMap)) {
                amazonSdAdGroupMap.forEach((k, v) -> {
                    SPadGroupVo sPadGroupVoError = new SPadGroupVo();
                    sPadGroupVoError.setCampaignId(v.getCampaignId());
                    sPadGroupVoError.setName(v.getName());
                    sPadGroupVoError.setId(v.getId());
                    sPadGroupVoError.setGroupId(v.getAdGroupId());
                    sPadGroupVoError.setDxmGroupId(v.getId());
                    sPadGroupVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sPadGroupVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonSdAdGroups.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    private AdGroup convertGroup(AmazonSdAdGroup vo, String type){

        AdGroup group  = new AdGroup();
        if (StringUtils.isNotBlank(vo.getCampaignId())) {
            group.setCampaignId(Long.valueOf(vo.getCampaignId()));
        }
        if (StringUtils.isNotBlank(vo.getAdGroupId())) {
            group.setAdGroupId(Long.valueOf(vo.getAdGroupId()));
        }
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            group.setState(vo.getState());
        } else if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){
            group.setDefaultBid(vo.getDefaultBid().doubleValue());
        }
        return group;
    }

    /**
     * 同步所有的活动
     *
     * @param shop：
     */
    public List<AmazonSdAdGroup> syncSdAdGroups(ShopAuth shop, String adGroupId) {
        List<AmazonSdAdGroup> amazonSdAdGroups = new ArrayList<>();
        if (shop == null) {
            return new ArrayList<>();
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdGroup--配置信息为空");
            return new ArrayList<>();
        }

        //获取活动的基本信息
        AdGroupsClient client = AdGroupsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 500;
        ListAdGroupsExResponse response;

        while (true) {
            int finalSartIndex = startIndex;
            response = cpcApiHelper.call(shop, () -> client.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, null, null, adGroupId, null));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD adgroup rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = cpcApiHelper.call(shop, () -> client.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, null, null, adGroupId, null));
                retry++;
            }
            if (response == null || CollectionUtils.isEmpty(response.getResultList())) {
                break;
            }

            int size = response.getResultList().size();
            AmazonSdAdGroup amazonSdAdGroup;
            for (AdGroup adGroup : response.getResultList()) {
                if (TacticEnum.T00030.name().equals(adGroup.getTactic())
                        || TacticEnum.T00020.name().equals(adGroup.getTactic())) {
                    amazonSdAdGroup = turnEntityToPO(adGroup);
                    if (StringUtils.isNotBlank(amazonSdAdGroup.getAdGroupId())) {
                        amazonSdAdGroup.setPuid(shop.getPuid());
                        amazonSdAdGroup.setShopId(shop.getId());
                        amazonSdAdGroup.setMarketplaceId(shop.getMarketplaceId());
                        amazonSdAdGroup.setProfileId(amazonAdProfile.getProfileId());
                        amazonSdAdGroups.add(amazonSdAdGroup);
                    }
                }
            }


            if (size < count) {
                break;
            }

            startIndex += size;
        }
        return amazonSdAdGroups;
    }


}
