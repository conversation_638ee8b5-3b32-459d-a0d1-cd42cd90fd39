package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/3
 */
@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PortfolioPageParam {
    //任务redis存储的id
    private String uuid;
    @ApiModelProperty(value = "pageNo")
    private Integer pageNo;
    @ApiModelProperty(value = "pageSize")
    private Integer pageSize;
    @ApiModelProperty(value = "shopId",required = true)
    private Integer shopId;
    @ApiModelProperty(value = "puid")
    private Integer puid;
    private Integer uid;
    @ApiModelProperty(value = "是否隐藏 默认为false")
    private Boolean isHidden;
    @ApiModelProperty(value = "查询字段 默认是name")
    private String searchField;
    @ApiModelProperty(value = "查询字段值 输入的查询广告组合名字")
    private String searchValue;
    @ApiModelProperty(value = "查询类型 exact:精准查询（默认） blur:模糊查询")
    private String searchType;
    @ApiModelProperty(value = "广告组合状态 刚入页面可不传 enabled(已开启) paused(已暂停) enabled,paused(已开启和已暂停) archived(已存档)")
    private String status;
    @ApiModelProperty(value = "开始时间")
    private String startDate;
    @ApiModelProperty(value = "结束时间")
    private String endDate;
    @ApiModelProperty(value = "排序字段 当页面不做排序时可不传")
    private String orderField;
    @ApiModelProperty(value = "类型升序 asc 降序 desc 当页面不做排序时可不传")
    private String orderType;
    @ApiModelProperty(value = "广告组合Id 暂不需要")
    private String portfolioId;
    @ApiModelProperty(value = "广告组合Id集合,逗号拼接 ")
    private List<String> portfolioIds;
    @ApiModelProperty(value = "广告组合下的广告活动Id集合 ")
    private List<String> campaignIds;
    @ApiModelProperty(value = "店铺销售额 前端不需传")
    private BigDecimal shopSales;
    @ApiModelProperty("仅显示正在投放  勾选后传值 enabled")
    private String servingStatus;
    private List<Integer> shopIdList;
    //环比相关参数
    private Boolean isCompare;
    private String compareStartDate;
    private String compareEndDate;

    private List<String> searchValueList;

    private String pageSign;

    /** 查询数据类型 {@link com.meiyunji.sponsored.service.enums.SearchDataTypeEnum}*/
    @ApiModelProperty("查询数据类型")
    private Integer searchDataType;

    /**
     * 广告组合Id
     */
    private List<String> portfolioIdList;

    private Boolean isAdminUser;
}
