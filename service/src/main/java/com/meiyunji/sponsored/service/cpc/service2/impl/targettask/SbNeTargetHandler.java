package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.sb.entity.netargeting.CreateNeTargetResponse;
import com.amazon.advertising.sb.entity.netargeting.NeTargetClient;
import com.amazon.advertising.sb.entity.targeting.CreateTargetErrorResults;
import com.amazon.advertising.sb.entity.targeting.CreateTargetSuccessResults;
import com.amazon.advertising.sb.entity.targeting.SbCreateTargetResult;
import com.amazon.advertising.sb.mode.targeting.SBExpression;
import com.amazon.advertising.sb.mode.targeting.SBResolvedExpression;
import com.amazon.advertising.sb.mode.targeting.Targeting;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetObjectTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AdTargetDetailDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbNeTargetService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbNeTargetApiService;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.vo.SbBatchNeTargetsVo;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:23
 */
@Service(AdTargetTaskConstant.SB_NE_TARGET_HANDLER)
@Slf4j
public class SbNeTargetHandler implements TargetTaskHandler {

    @Autowired
    private ICommonAmazonAdTargetingService commonAmazonAdTargetingService;
    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonSbAdNeTargetingDao amazonSbAdNeTargetingDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private CpcSbNeTargetApiService cpcSbNeTargetApiService;
    @Autowired
    private ICpcSbNeTargetService cpcSbNeTargetService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    private static int MAX_SYNC_BATCH_CAMPAIGN_ID_LIMIT = 100;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        create(adTargetTask, adTargetTaskDetails);
    }

    private void create(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        if (CollectionUtils.isEmpty(adGroupIdSet)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonSbAdGroup> amazonSbAdGroups = amazonSbAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonSbAdGroup> amazonAdGroupMap = amazonSbAdGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        int originalTaskDetailSize = adTargetTaskDetails.size();
        List<SbBatchNeTargetsVo> amazonAdTargetings = convertBatchAddNeTargetingVo(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList, adTargetTask.getTargetingType());
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(adTargetTask.getPuid(), adTargetTask.getShopId());
        if (profile == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("没有站点对应的配置信息");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        int failureNum = 0;
        List<List<SbBatchNeTargetsVo>> amazonAdTargetingPartition = Lists.partition(amazonAdTargetings, AdTargetTaskConstant.MAX_SB_TARGET_SIZE);
        for (List<SbBatchNeTargetsVo> amazonAdTargetingList : amazonAdTargetingPartition) {
            Result result = create(amazonAdTargetingList, adTargetTaskDetailMap, shop, profile);
            for (SbBatchNeTargetsVo amazonSbAdTargeting : amazonAdTargetingList) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonSbAdTargeting.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNum++;
                }
            }
            if (result.success()) {
                List<AmazonSbAdNeTargeting> neTargetList = new ArrayList<>(amazonAdTargetingList.size());
                if (CollectionUtils.isNotEmpty(amazonAdTargetingList)) {
                    amazonAdTargetingList.forEach(item -> {
                        AmazonSbAdNeTargeting sbAdNeTargeting = new AmazonSbAdNeTargeting();
                        BeanUtils.copyProperties(item, sbAdNeTargeting);
                        sbAdNeTargeting.setState("enabled");
                        neTargetList.add(sbAdNeTargeting);
                    });
                }

                logSbNeTargetCreate(neTargetList, adTargetTask.getLoginIp(), result);

                List<AmazonSbAdNeTargeting> succList = neTargetList.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId()))
                        .collect(Collectors.toList());
                if (succList.size() > 0) {
                    // 有可能已经添加过了
                    List<String> existInDB = amazonSbAdNeTargetingDao.listByTargetId(adTargetTask.getPuid(), adTargetTask.getShopId(), succList.stream()
                                    .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList())).stream()
                            .map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());

                    // 排除掉已有的
                    if (CollectionUtils.isNotEmpty(existInDB)) {
                        succList = succList.stream().filter(e -> !existInDB.contains(e.getTargetId())).collect(Collectors.toList());
                    }

                    try {
                        amazonSbAdNeTargetingDao.batchAdd(adTargetTask.getPuid(), succList);
                        List<String> targetIds = succList.stream().map(AmazonSbAdNeTargeting::getTargetId).collect(Collectors.toList());
                        cpcSbNeTargetService.saveDoris(adTargetTask.getPuid(), adTargetTask.getShopId(), targetIds);
                    } catch (Exception e) {
                        log.error("createSbNeTargeting:", e);
                    }

                    List<String> adCampaignIds = succList.stream().map(AmazonSbAdNeTargeting::getCampaignId).distinct().collect(Collectors.toList());
                    for (List<String> subAdCampaignIds : Lists.partition(adCampaignIds, MAX_SYNC_BATCH_CAMPAIGN_ID_LIMIT)) {
                        ThreadPoolUtil.getNegativeSyncPool().execute(() -> {
                            try {
                                //创建成功, 需要在同步 获取投放状态
                                cpcSbNeTargetApiService.syncNeTargets(shop, String.join(",", subAdCampaignIds));
                            } catch (Exception e) {
                                log.info("添加成功后同步否定异常", e);
                            }
                        });
                    }

                    //同步广告组投放类型字段
                    try {
                        List<AmazonSbAdGroup> sbAdGroups = succList.stream().map(AmazonSbAdNeTargeting::getAdGroupId).distinct().map(amazonAdGroupMap::get)
                                .filter(each -> StringUtils.isNotBlank(each.getAdGroupType())).collect(Collectors.toList());
                        if (CollectionUtils.isNotEmpty(sbAdGroups)) {
                            sbAdGroups.forEach(each -> each.setAdGroupType("product"));
                            amazonSbAdGroupDao.batchUpdateAdGroupType(adTargetTask.getPuid(), sbAdGroups);
                        }
                    } catch (Exception e) {
                        log.error("updateGroupTargetType:", e);
                    }
                }
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNum += originalTaskDetailSize - amazonAdTargetings.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNum);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private void logSbNeTargetCreate(List<AmazonSbAdNeTargeting> neTargetList, String ip, Result result) {
        try {
            if (CollectionUtils.isEmpty(neTargetList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdNeTargeting neTargeting : neTargetList) {
                AdManageOperationLog targetLog = adManageOperationLogService.getSbNeTargetLog(null, neTargeting);
                targetLog.setIp(ip);
                if (StringUtils.isNotBlank(neTargeting.getTargetId())) {
                    targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    if (StringUtils.isNotBlank(neTargeting.getErrMsg())) {
                        targetLog.setResultInfo(neTargeting.getErrMsg());
                    } else {
                        targetLog.setResultInfo(result.getMsg());
                    }
                }
                operationLogs.add(targetLog);
            }
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("SB否定商品投放创建日志异常", e);
        }
    }

    private Result create(List<SbBatchNeTargetsVo> neTargetList, Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap, ShopAuth
            shop, AmazonAdProfile profile) {
        List<Targeting> targetingList = neTargetList.stream().map(e -> {
            Targeting targeting = new Targeting();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targeting.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            if (StringUtils.isNotBlank(e.getCampaignId())) {
                targeting.setCampaignId(Long.valueOf(e.getCampaignId()));
            }
            if (StringUtils.isNotBlank(e.getExpression())) {
                targeting.setExpressions(JSONUtil.jsonToArray(e.getExpression(), SBExpression.class));
            }
            return targeting;
        }).collect(Collectors.toList());

        CreateNeTargetResponse response = cpcApiHelper.call(shop, () -> NeTargetClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), profile.getProfileId(),
                shop.getMarketplaceId(), targetingList));

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        String formatErrMsg = errMsg;
        SbCreateTargetResult result = response.getResult();
        if (result != null) {
            List<CreateTargetSuccessResults> succList = result.getCreateTargetSuccessResults();
            List<CreateTargetErrorResults> failList = result.getCreateTargetErrorResults();

            if (CollectionUtils.isEmpty(succList) && CollectionUtils.isEmpty(failList)) {
                String returnErrMsg = errMsg;
                if (StringUtils.isNotBlank(result.getDetails())) {
                    returnErrMsg = response.getResult().getDetails();
                } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                    returnErrMsg = response.getResult().getDescription();
                }
                errMsg = AmazonErrorUtils.getError(returnErrMsg);
                formatErrMsg = targetTaskComponent.getError(errMsg, returnErrMsg);
            } else {
                if (CollectionUtils.isNotEmpty(succList)) {
                    for (CreateTargetSuccessResults results : succList) {
                        Integer index = results.getTargetRequestIndex();
                        SbBatchNeTargetsVo amazonAdTargeting = neTargetList.get(index);
                        amazonAdTargeting.setTargetId(String.valueOf(results.getTargetId()));
                        AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                        adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                    }
                }
                if (CollectionUtils.isNotEmpty(failList)) {
                    for (CreateTargetErrorResults results : failList) {
                        Integer index = results.getTargetRequestIndex();
                        String returnErrorMsg = StringUtils.defaultIfBlank(results.getDetails(), results.getDescription());
                        String errorMsg = AmazonErrorUtils.getError(returnErrorMsg);
                        SbBatchNeTargetsVo amazonAdTargeting = neTargetList.get(index);
                        amazonAdTargeting.setErrMsg(errorMsg);
                        AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
                        adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMsg, returnErrorMsg));
                        adTargetTaskDetail.setFailureReasonDetail(returnErrorMsg);
                        adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    }
                }
                return ResultUtil.success();
            }
        }

        for (SbBatchNeTargetsVo amazonAdTargeting : neTargetList) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdTargeting.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatErrMsg);
            adTargetTaskDetail.setFailureReasonDetail(errMsg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }

        return ResultUtil.returnErr(errMsg);
    }

    private List<SbBatchNeTargetsVo> convertBatchAddNeTargetingVo(
            Integer uid, Map<String, AmazonSbAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList, String targetingType) {
        List<SbBatchNeTargetsVo> batchNeTargetsVos = new ArrayList<>();
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        List<String> targetIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getTargetId).distinct().collect(Collectors.toList());
        AdTargetTaskDetail first = adTargetTaskDetails.get(0);
        List<Integer> sourceShopIds = adTargetTaskDetails.stream().map(AdTargetTaskDetail::getSourceShopId).distinct().collect(Collectors.toList());
        Map<String, AdTargetDetailDto> targetDetailMap = buildTargetDetailMap(first.getPuid(), targetIds, sourceShopIds, targetingType);
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonSbAdGroup amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                String type = AdTargetObjectTypeEnum.getTargetTypeByCode(adTargetTaskDetail.getTargetObjectType());
                SbBatchNeTargetsVo batchNeTargetsVo = new SbBatchNeTargetsVo();
                batchNeTargetsVo.setPuid(amazonAdGroup.getPuid());
                batchNeTargetsVo.setShopId(amazonAdGroup.getShopId());
                batchNeTargetsVo.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                batchNeTargetsVo.setProfileId(amazonAdGroup.getProfileId());
                batchNeTargetsVo.setAdGroupId(amazonAdGroup.getAdGroupId());
                batchNeTargetsVo.setCampaignId(amazonAdGroup.getCampaignId());
                batchNeTargetsVo.setType(type);
                batchNeTargetsVo.setCreateId(uid);
                batchNeTargetsVo.setCreateInAmzup(1);
                batchNeTargetsVo.setTargetTaskDetailId(adTargetTaskDetail.getId());
                List<SBResolvedExpression> expressions = new ArrayList<>();
                if (SdTargetTypeEnum.asin.name().equals(type)) {  //现在只有否asin功能, 否品牌暂不支持
                    SBResolvedExpression expression = new SBResolvedExpression();
                    expression.setType(ExpressionEnum.asinSameAs.value());
                    expressions.add(expression);

                    if (StringUtils.isEmpty(adTargetTaskDetail.getTargetId())) {
                        expression.setValue(adTargetTaskDetail.getTargetObject());
                        batchNeTargetsVo.setTargetText(adTargetTaskDetail.getTargetObject());
                        batchNeTargetsVo.setImgUrl(adTargetTaskDetail.getImgUrl());
                        batchNeTargetsVo.setTitle(adTargetTaskDetail.getTargetObjectDesc());
                    } else {
                        AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                        expression.setValue(adTargetDetail.getTargetObject());
                        batchNeTargetsVo.setTargetText(adTargetDetail.getTargetObject());
                        batchNeTargetsVo.setImgUrl(adTargetDetail.getImgUrl());
                        batchNeTargetsVo.setTitle(adTargetDetail.getTargetObjectDesc());
                    }
                }
                if (SdTargetTypeEnum.brand.name().equals(type)) {
                    // 品牌否定投放任务肯定包含targetId
                    AdTargetDetailDto adTargetDetail = targetDetailMap.get(adTargetTaskDetail.getTargetId());
                    SBResolvedExpression expression = new SBResolvedExpression();
                    expression.setType(ExpressionEnum.asinBrandSameAs.value());
                    expression.setValue(adTargetDetail.getBrandId());
                    batchNeTargetsVo.setTargetText(adTargetDetail.getBrandName());
                    expressions.add(expression);
                }
                batchNeTargetsVo.setExpression(JSONUtil.objectToJson(expressions));
                batchNeTargetsVos.add(batchNeTargetsVo);
            }
        }
        return batchNeTargetsVos;
    }

    @Override
    public Map<String, AdTargetDetailDto> buildTargetDetailMap(Integer puid, List<String> targetIds, List<Integer> sourceShopIds, String targetingType) {
        List<CommonAmazonAdTargeting> list = commonAmazonAdTargetingService.listByTargetIds(targetingType, puid, sourceShopIds, targetIds);
        return list.stream().map(each -> {
            AdTargetDetailDto adTargetDetail = new AdTargetDetailDto();
            adTargetDetail.setTargetId(each.getTargetId());
            boolean isSp = TargetingTypeEnum.SP_TARGETING_CODE_LIST.contains(targetingType);
            String type = each.getType();
            if (TargetTypeEnum.asin.name().equals(type)) {
                adTargetDetail.setTargetObject(each.getTargetingValue());
                adTargetDetail.setTargetObjectDesc(each.getTitle());
                adTargetDetail.setImgUrl(each.getImgUrl());
            } else if (TargetTypeEnum.brand.name().equals(type)) {
                targetTaskComponent.fillNeTargetDetail(each, adTargetDetail, isSp);
            }
            return adTargetDetail;
        }).collect(Collectors.toMap(AdTargetDetailDto::getTargetId, Function.identity(), (newVal, oldVal) -> newVal));
    }
}
