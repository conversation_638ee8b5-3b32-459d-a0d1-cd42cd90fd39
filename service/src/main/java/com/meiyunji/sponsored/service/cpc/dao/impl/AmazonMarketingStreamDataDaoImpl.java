package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.permission.util.PermissionSqlBuilder;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.*;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import com.meiyunji.sponsored.service.newDashboard.bo.EffectDataBo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Repository
@Slf4j
public class AmazonMarketingStreamDataDaoImpl extends DorisBaseDaoImpl<AmazonMarketingStreamData> implements IAmazonMarketingStreamDataDao {

    private static final String FEAD_DAY_DATA_TABLE = "t_amazon_marketing_stream_data_aggregation_day";
    private static final String FEAD_HOUR_DATA_TABLE = "t_amazon_marketing_stream_data";
    @Resource
    private IAdProductRightService adProductRightService;


    @Override
    public List<AmazonMarketingStreamData> listByHourly(FeedHourlySelectDTO feedHourlySelectDTO) {
        if (feedHourlySelectDTO.getSellerId() == null || feedHourlySelectDTO.getMarketplaceId() == null) {
            log.error("出现sellerId或marketplaceId为空查询小时级数据表");
            return new ArrayList<>();
        }
        StringBuilder selectSql = new StringBuilder("SELECT time, ");
        selectSql.append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(feedHourlySelectDTO.getSellerId());
        argsList.add(feedHourlySelectDTO.getMarketplaceId());
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", feedHourlySelectDTO.getExcludeCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getPlacements())) {
            whereSql.append(SqlStringUtil.dealNotInList("placements", feedHourlySelectDTO.getPlacements(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listByHourlyMultiple(FeedHourlySelectDTO feedHourlySelectDTO) {
        if (CollectionUtils.isEmpty(feedHourlySelectDTO.getSellerIdList()) || CollectionUtils.isEmpty(feedHourlySelectDTO.getMarketplaceIdList())) {
            log.error("出现sellerId或marketplaceId为空查询小时级数据表");
            return new ArrayList<>();
        }
        StringBuilder selectSql = new StringBuilder("SELECT time,currency,DATE_FORMAT(date, '%Y%m') month, ");
        selectSql.append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where 1=1 ");
        List<Object> argsList = Lists.newArrayList();
        whereSql.append(SqlStringUtil.dealInList("seller_id", feedHourlySelectDTO.getSellerIdList(), argsList));
        whereSql.append(SqlStringUtil.dealInList("marketplace_id", feedHourlySelectDTO.getMarketplaceIdList(), argsList));
        whereSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id)", feedHourlySelectDTO.getConcatWsList(), argsList));
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", feedHourlySelectDTO.getExcludeCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getPlacements())) {
            whereSql.append(SqlStringUtil.dealNotInList("placements", feedHourlySelectDTO.getPlacements(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" group by time,currency,DATE_FORMAT(date, '%Y%m')");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listByPlacementHourly(FeedHourlySelectDTO feedHourlySelectDTO) {
        StringBuilder selectSql = new StringBuilder("SELECT time, placement, ")
                .append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(feedHourlySelectDTO.getSellerId());
        argsList.add(feedHourlySelectDTO.getMarketplaceId());
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getPlacements())) {
            whereSql.append(SqlStringUtil.dealNotInList("placement", feedHourlySelectDTO.getPlacements(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" group by time, placement");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listWeekByHourly(FeedHourlySelectDTO feedHourlySelectDTO) {
        StringBuilder selectSql = new StringBuilder("SELECT weekday,time,")
                .append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(feedHourlySelectDTO.getSellerId());
        argsList.add(feedHourlySelectDTO.getMarketplaceId());
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", feedHourlySelectDTO.getExcludeCampaignIds(), argsList));
        }

        selectSql.append(whereSql);
        selectSql.append(" group by weekday, time ");

        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listWeekByHourlyMultiple(FeedHourlySelectDTO feedHourlySelectDTO) {
        if (CollectionUtils.isEmpty(feedHourlySelectDTO.getSellerIdList()) || CollectionUtils.isEmpty(feedHourlySelectDTO.getMarketplaceIdList())) {
            log.error("出现sellerId或marketplaceId为空查询小时级数据表");
            return new ArrayList<>();
        }
        StringBuilder selectSql = new StringBuilder("SELECT weekday,time,DATE_FORMAT(date, '%Y%m') month,currency, ");
        selectSql.append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where 1=1 ");
        List<Object> argsList = Lists.newArrayList();
        whereSql.append(SqlStringUtil.dealInList("seller_id", feedHourlySelectDTO.getSellerIdList(), argsList));
        whereSql.append(SqlStringUtil.dealInList("marketplace_id", feedHourlySelectDTO.getMarketplaceIdList(), argsList));
        whereSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id)", feedHourlySelectDTO.getConcatWsList(), argsList));
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", feedHourlySelectDTO.getExcludeCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" group by weekday,time,currency,DATE_FORMAT(date, '%Y%m') ");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listByKeywordHourly(FeedHourlySelectDTO feedHourlySelectDTO) {
        StringBuilder selectSql = new StringBuilder("SELECT time, keyword_id,")
                .append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(feedHourlySelectDTO.getSellerId());
        argsList.add(feedHourlySelectDTO.getMarketplaceId());
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }

        selectSql.append(whereSql);
        selectSql.append(" group by keyword_id, time ");

        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listBitmapByHourly(FeedHourlySelectDTO feedHourlySelectDTO) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(feedHourlySelectDTO.getSellerId());
        argsList.add(feedHourlySelectDTO.getMarketplaceId());
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", feedHourlySelectDTO.getExcludeCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" group by time ");

        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<ProductPerspectiveCountDto> getDiagnoseCount4Campaign(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("select ");
        selectSql.append("campaign_id                               id,");
        selectSql.append("sum(clicks)                               clicks,");
        selectSql.append("sum(cost)                                 cost,");
        selectSql.append("sum(impressions)                          impressions,");
        selectSql.append("sum(attributed_conversions7d)             attributed_conversions7d,");
        selectSql.append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,");
        selectSql.append("sum(attributed_sales7d)                   attributed_sales7d,");
        selectSql.append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,");
        selectSql.append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,");
        selectSql.append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");
        selectSql.append("from t_amazon_marketing_stream_data ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder("where marketplace_id = ? ");
        argsList.add(baseParam.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", baseParam.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(baseParam.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", baseParam.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(baseParam.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", baseParam.getAdIds(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(baseParam.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(baseParam.getEndDate());
        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(ProductPerspectiveCountDto.class), argsList.toArray());
        } catch (Exception e) {
            log.error("getDiagnoseCount4Campaign error.baseParam:{} shopSales:{} filterDto:{}", baseParam, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductPerspectiveCountDto> getDiagnoseCount4Keyword(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("select ");
        selectSql.append("keyword_id                                id,");
        selectSql.append("sum(clicks)                               clicks,");
        selectSql.append("sum(cost)                                 cost,");
        selectSql.append("sum(impressions)                          impressions,");
        selectSql.append("sum(attributed_conversions7d)             attributed_conversions7d,");
        selectSql.append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,");
        selectSql.append("sum(attributed_sales7d)                   attributed_sales7d,");
        selectSql.append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,");
        selectSql.append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,");
        selectSql.append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");
        selectSql.append("from t_amazon_marketing_stream_data ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder("where marketplace_id = ? ");
        argsList.add(baseParam.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", baseParam.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(baseParam.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", baseParam.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(baseParam.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", baseParam.getAdIds(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(baseParam.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(baseParam.getEndDate());
        selectSql.append(whereSql);
        selectSql.append(" group by keyword_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(ProductPerspectiveCountDto.class), argsList.toArray());
        } catch (Exception e) {
            log.error("getDiagnoseCount4Keyword error.baseParam:{} shopSales:{} filterDto:{}", baseParam, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductPerspectiveCountDto> getDiagnoseCount4Placement(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("select ");
        selectSql.append("campaign_id                               id,");
        selectSql.append("placement,");
        selectSql.append("sum(clicks)                               clicks,");
        selectSql.append("sum(cost)                                 cost,");
        selectSql.append("sum(impressions)                          impressions,");
        selectSql.append("sum(attributed_conversions7d)             attributed_conversions7d,");
        selectSql.append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,");
        selectSql.append("sum(attributed_sales7d)                   attributed_sales7d,");
        selectSql.append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,");
        selectSql.append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,");
        selectSql.append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");
        selectSql.append("from t_amazon_marketing_stream_data ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder("where marketplace_id = ? ");
        argsList.add(baseParam.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", baseParam.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(baseParam.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", baseParam.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(baseParam.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", baseParam.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(baseParam.getPlacements())) {
            whereSql.append(SqlStringUtil.dealInList("placement", baseParam.getPlacements(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(baseParam.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(baseParam.getEndDate());
        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id, placement ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(ProductPerspectiveCountDto.class), argsList.toArray());
        } catch (Exception e) {
            log.error("getDiagnoseCount4Placement error.baseParam:{} shopSales:{} filterDto:{}", baseParam, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductPerspectiveCountDto> getAllTypeDiagnoseCount4Campaign(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("select ");
        selectSql.append("campaign_id                               id,")
                .append(this.buildReportSelectSumSql());

        selectSql.append("from t_amazon_marketing_stream_data ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder("where marketplace_id = ? ");
        argsList.add(baseParam.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", baseParam.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(baseParam.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", baseParam.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(baseParam.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", baseParam.getAdIds(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(baseParam.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(baseParam.getEndDate());
        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSumSql(dto, shopSales, argsList, baseParam.getAdType()));
        try {
            return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(ProductPerspectiveCountDto.class), argsList.toArray());
        } catch (Exception e) {
            log.error("getDiagnoseCount4Campaign error.baseParam:{} shopSales:{} filterDto:{}", baseParam, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<ProductPerspectiveCountDto> getAllTypeDiagnoseCount4Keyword(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("select ");
        selectSql.append("keyword_id                                id,")
                .append(this.buildReportSelectSumSql());
        selectSql.append("from t_amazon_marketing_stream_data ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder("where marketplace_id = ? ");
        argsList.add(baseParam.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", baseParam.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(baseParam.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", baseParam.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(baseParam.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", baseParam.getAdIds(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(baseParam.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(baseParam.getEndDate());
        selectSql.append(whereSql);
        selectSql.append(" group by keyword_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSumSql(dto, shopSales, argsList, baseParam.getAdType()));
        try {
            return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(ProductPerspectiveCountDto.class), argsList.toArray());
        } catch (Exception e) {
            log.error("getDiagnoseCount4Keyword error.baseParam:{} shopSales:{} filterDto:{}", baseParam, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> aggregateSellersByHour(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, any(placement) placement, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(SqlStringUtil.dealInList("placement", queryDto.getPlacements(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdGroupList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", queryDto.getAdGroupList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("aggregateSellersByHour error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> aggregateSellersByHourAndType(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, any(placement) placement, ")
                .append(this.buildReportSelectSql());

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(SqlStringUtil.dealInList("placement", queryDto.getPlacements(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdGroupList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", queryDto.getAdGroupList(), argsList));
        }
        if (StringUtils.isNotBlank(queryDto.getType())) {
            List<String> typeList = StringUtil.splitStr(queryDto.getType()).stream().map(e -> e.toUpperCase()).collect(Collectors.toList());
            whereSql.append(SqlStringUtil.dealInList("type", typeList, argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("aggregateSellersByHourAndType error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsProductPerspectiveCampaignHourlyReport(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsProductPerspectiveCampaignHourlyReport error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsTargetHourlyReport(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append(buildSelectSql(queryDto.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getItemIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", queryDto.getItemIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsTargetHourlyReport error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsAllTypeTargetHourlyReport(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append(this.buildReportSelectSql());
        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(queryDto.getItemIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", queryDto.getItemIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsTargetHourlyReport error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> aggregatePlacementSellersByHour(CampaignHourlyReportSelectDto queryDto, List<CampaignPlacementDto> campaignIdPlacementList) {
        StringBuilder selectSql = new StringBuilder("SELECT time, any(placement) placement, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(campaignIdPlacementList)) {
            whereSql.append(SqlStringUtil.dealMultiInList(Arrays.asList("campaign_id", "placement"), campaignIdPlacementList, argsList, Arrays.asList(CampaignPlacementDto::getCampaignId, CampaignPlacementDto::getPlacement)));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(SqlStringUtil.dealInList("placement", queryDto.getPlacements(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdGroupList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", queryDto.getAdGroupList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("aggregatePlacementSellersByHour error.queryDto:{} campaignIdPlacementList:{}", queryDto, campaignIdPlacementList, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsByHour(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, any(placement) placement, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(SqlStringUtil.dealInList("placement", queryDto.getPlacements(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdGroupList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", queryDto.getAdGroupList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsByHour error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> groupStatisticsByHour(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdGroupList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", queryDto.getAdGroupList(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("groupStatisticsByHour error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> campaignStatisticsByHour(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", queryDto.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("campaignStatisticsByHour error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisCampaignView(CampaignHourlyReportSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT campaign_id, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }

        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisCampaignView error.queryDto:{} shopSales:{} filterDto:{}", queryDto, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisAllCampaignView(CampaignViewParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT campaign_id, type, ")
                .append(" clicks, cost, impressions, attributed_conversions7d, attributed_conversions7d_same_sku,")
                .append(" attributed_sales7d, attributed_sales7d_same_sku, attributed_units_ordered7d, attributed_units_ordered7d_same_sku, ")
                .append(" view_impressions, attributed_orders_new_to_brand14d, attributed_sales_new_to_brand14d, attributed_units_ordered_new_to_brand14d from (");
        List<Object> argsList = Lists.newArrayList();

        StringBuilder whereSql = new StringBuilder(" and marketplace_id = ? ");
        List<Object> whereArgsList = Lists.newArrayList();
        whereArgsList.add(param.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", param.getSellerIdList(), whereArgsList));
        whereSql.append(" and date >= ? ");
        whereArgsList.add(param.getStartDate());
        whereSql.append(" and date <= ? ");
        whereArgsList.add(param.getEndDate());

        //存放多个类型的sql
        StringJoiner sj = new StringJoiner(" UNION ALL ");
        List<String> typeList = StringUtil.splitStr(param.getType());
        StringBuilder sql;
        if (typeList.contains(Constants.SP) && CollectionUtils.isNotEmpty(param.getSpAdCamgaignIdList()) && CollectionUtils.isNotEmpty(param.getSpAdIdList())) {
            sql = new StringBuilder("SELECT campaign_id, any(type) type,")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SP))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("campaign_id", param.getSpAdCamgaignIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSpAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by campaign_id ");
            sj.add(sql.toString());
        }
        if (typeList.contains(Constants.SB) && CollectionUtils.isNotEmpty(param.getSbAdCamgaignIdList()) && CollectionUtils.isNotEmpty(param.getSbAdIdList())) {
            sql = new StringBuilder("SELECT campaign_id, any(type) type,")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SB))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("campaign_id", param.getSbAdCamgaignIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSbAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by campaign_id ");
            sj.add(sql.toString());
        }
        if (typeList.contains(Constants.SD) && CollectionUtils.isNotEmpty(param.getSdAdCamgaignIdList()) && CollectionUtils.isNotEmpty(param.getSdAdIdList())) {
            sql = new StringBuilder("SELECT campaign_id, any(type) type,")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SD))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("campaign_id", param.getSdAdCamgaignIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSdAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by campaign_id ");
            sj.add(sql.toString());
        }
        selectSql.append(sj).append(" ) c where 1=1 ")
                .append(this.buildProductPerspectiveAnalysisWhereFilterSql(dto, shopSales, argsList))
                .append("order by campaign_id");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisAllCampaignView error.param:{} shopSales:{} filterDto:{}", param, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public Page<AmazonMarketingStreamData> productPerspectiveAnalysisAllTargetViewPage(ViewBaseParam param, BigDecimal shopSales,
                                                                                   ProductPerspectiveAnalysisFilterDto dto) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = this.buildProductPerspectiveAnalysisAllTargetViewSql(param, shopSales, dto, argsList);
        String countSql = " select count(*) from (" + selectSql + " ) d ";
        String orderBySql = buildProductPerspectiveAnalysisOrderSql(param.getOrderField(), param.getOrderType());
        selectSql.append(StringUtils.isBlank(orderBySql) ? " order by keyword_id " : orderBySql);
        Object[] array = argsList.toArray();
        return this.getPageResult(param.getPageNo(), param.getPageSize(), countSql, array, selectSql.toString(), array, AmazonMarketingStreamData.class);
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisAllTargetViewList(ViewBaseParam param, BigDecimal shopSales,
                                                                                       ProductPerspectiveAnalysisFilterDto dto) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = this.buildProductPerspectiveAnalysisAllTargetViewSql(param, shopSales, dto, argsList);
        selectSql.append(" order by keyword_id ");
        Object[] array = argsList.toArray();
        return getJdbcTemplate().query(selectSql.toString(), array, new BeanPropertyRowMapper<>(AmazonMarketingStreamData.class));
    }

    private StringBuilder buildProductPerspectiveAnalysisAllTargetViewSql(ViewBaseParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto, List<Object> argsList) {
        StringBuilder selectSql = new StringBuilder("SELECT keyword_id, type, ")
                .append(" clicks, cost, impressions, attributed_conversions7d, attributed_conversions7d_same_sku,")
                .append(" attributed_sales7d, attributed_sales7d_same_sku, attributed_units_ordered7d, attributed_units_ordered7d_same_sku,")
                .append(" view_impressions, attributed_orders_new_to_brand14d, attributed_sales_new_to_brand14d, attributed_units_ordered_new_to_brand14d from (");

        StringBuilder whereSql = new StringBuilder(" and marketplace_id = ? ");
        List<Object> whereArgsList = Lists.newArrayList();
        whereArgsList.add(param.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", param.getSellerIdList(), whereArgsList));
        whereSql.append(" and date >= ? ");
        whereArgsList.add(param.getStartDate());
        whereSql.append(" and date <= ? ");
        whereArgsList.add(param.getEndDate());

        //存放多个类型的sql
        StringJoiner sj = new StringJoiner(" UNION ALL ");
        List<String> typeList = StringUtil.splitStr(param.getType());
        StringBuilder sql;
        if (typeList.contains(Constants.SP) && CollectionUtils.isNotEmpty(param.getSpAdTargetIdList()) && CollectionUtils.isNotEmpty(param.getSpAdIdList())) {
            sql = new StringBuilder("SELECT keyword_id, any(type) type, ")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SP))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("keyword_id", param.getSpAdTargetIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSpAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by keyword_id ");
            sj.add(sql.toString());
        }
        if (typeList.contains(Constants.SB) && CollectionUtils.isNotEmpty(param.getSbAdTargetIdList()) && CollectionUtils.isNotEmpty(param.getSbAdIdList())) {
            sql = new StringBuilder("SELECT keyword_id, any(type) type, ")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SB))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("keyword_id", param.getSbAdTargetIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSbAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by keyword_id ");
            sj.add(sql.toString());
        }
        if (typeList.contains(Constants.SD) && CollectionUtils.isNotEmpty(param.getSdAdTargetIdList()) && CollectionUtils.isNotEmpty(param.getSdAdIdList())) {
            sql = new StringBuilder("SELECT keyword_id, any(type) type, ")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SD))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("keyword_id", param.getSdAdTargetIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSdAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by keyword_id ");
            sj.add(sql.toString());
        }
        selectSql.append(sj).append(" ) c where 1=1 ")
                .append(this.buildProductPerspectiveAnalysisWhereFilterSql(dto, shopSales, argsList));
        return selectSql;
    }

    @Override
    public Page<AmazonMarketingStreamData> productPerspectiveAnalysisTargetView(ProductPerspectiveDiagnoseSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT keyword_id, ")
                .append("sum(clicks)                               clicks,")
                .append("ROUND(sum(cost), 2)                       cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", queryDto.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }

        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by keyword_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        int pageNo = Optional.ofNullable(queryDto.getPageNo()).orElse(0);
        int pageSize = Optional.ofNullable(queryDto.getPageSize()).orElse(0);
        // 不分页
        boolean noPagination = pageNo == 0 && pageSize == 0;
        Page<AmazonMarketingStreamData> resultPage = new Page<>();
        try {
            if (noPagination) {
                List<AmazonMarketingStreamData> resultList = getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
                resultPage.setTotalPage(1);
                resultPage.setTotalSize(resultList.size());
                resultPage.setRows(resultList);
            } else {
                List<AmazonMarketingStreamData> allList = getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
                int totalSize = allList.size();
                if (totalSize > 0) {
                    int totalPage = (totalSize - 1) / pageSize + 1;
                    pageNo = Math.min(pageNo, totalPage);
                    pageNo = Math.max(pageNo, 1);

                    selectSql.append(buildProductPerspectiveAnalysisOrderSql(queryDto, shopSales, argsList));
                    selectSql.append("limit ?,? ");
                    argsList.add((pageNo - 1) * pageSize);
                    argsList.add(pageSize);
                    List<AmazonMarketingStreamData> resultList = getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
                    resultPage.setTotalPage(totalPage);
                    resultPage.setTotalSize(allList.size());
                    resultPage.setRows(resultList);
                }
            }
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisTargetView error.queryDto:{} shopSales:{} filterDto:{}", queryDto, shopSales, dto, e);
        }
        return resultPage;
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisPlacementViewAggregate(ProductPerspectiveDiagnoseSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT campaign_id, placement, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(SqlStringUtil.dealInList("placement", queryDto.getPlacements(), argsList));
        }

        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id, placement ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisPlacementViewAggregate error.queryDto:{} shopSales:{} filterDto:{}", queryDto, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public Page<AmazonMarketingStreamData> productPerspectiveAnalysisPlacementView(ProductPerspectiveDiagnoseSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT campaign_id, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(" and placement = ? ");
            argsList.add(queryDto.getPlacements().get(0));
        }

        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        int pageNo = Optional.ofNullable(queryDto.getPageNo()).orElse(0);
        int pageSize = Optional.ofNullable(queryDto.getPageSize()).orElse(0);
        // 不分页
        boolean noPagination = pageNo == 0 && pageSize == 0;
        Page<AmazonMarketingStreamData> resultPage = new Page<>();
        resultPage.setRows(Lists.newArrayList());
        try {
            if (noPagination) {
                List<AmazonMarketingStreamData> resultList = getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
                resultPage.setTotalPage(1);
                resultPage.setTotalSize(resultList.size());
                resultPage.setRows(resultList);
            } else {
                List<AmazonMarketingStreamData> allList = getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
                int totalSize = allList.size();
                if (totalSize > 0) {
                    int totalPage = (totalSize - 1) / pageSize + 1;
                    pageNo = Math.min(pageNo, totalPage);
                    pageNo = Math.max(pageNo, 1);

                    selectSql.append(buildProductPerspectiveAnalysisOrderSql(queryDto, shopSales, argsList));
                    selectSql.append("limit ?,? ");
                    argsList.add((pageNo - 1) * pageSize);
                    argsList.add(pageSize);
                    List<AmazonMarketingStreamData> resultList = getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
                    resultPage.setTotalPage(totalPage);
                    resultPage.setTotalSize(allList.size());
                    resultPage.setRows(resultList);
                }
            }
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisPlacementView error.queryDto:{} shopSales:{} filterDto:{}", queryDto, shopSales, dto, e);
        }
        return resultPage;
    }

    @Override
    public AmazonMarketingStreamData sumAdStreamByKeywordIdsAndAdIds(AdKeywordStreamDataParam param) {

        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate splitDate = LocalDate.now().minusDays(65);

        // 结束时间小于65天的, 只查询天维度数据
        // 开始时间小于65天,结束时间大于65天, union all 查询日维度和小时维度数据
        // 开始时间大于65天只查询小时数据
        StringBuilder selectSql;
        List<Object> argsList = Lists.newArrayList();
        if (end.isBefore(splitDate)) {
            selectSql = buildSumAdStreamSql(param, start, end, FEAD_DAY_DATA_TABLE, argsList);
        } else if (start.isAfter(splitDate)) {
            selectSql = buildSumAdStreamSql(param, start, end, FEAD_HOUR_DATA_TABLE, argsList);
        } else {
            selectSql = new StringBuilder("SELECT ")
                    .append("sum(clicks)                               clicks,")
                    .append("ROUND(sum(cost), 2)                       cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales7d)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

            selectSql.append(" from ( ");
            StringBuilder unionAllSql = buildSumAdStreamSql(param, start, splitDate, FEAD_DAY_DATA_TABLE, argsList).append(" union all ")
                    .append(buildSumAdStreamSql(param, splitDate.plusDays(1), end, FEAD_HOUR_DATA_TABLE, argsList));
            selectSql.append(unionAllSql).append(" ) t");
        }

        List<AmazonMarketingStreamData> list = getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(AmazonMarketingStreamData.class), argsList.toArray());
        return CollectionUtils.isEmpty(list) ? new AmazonMarketingStreamData() : list.get(0);

    }

    private StringBuilder buildSumAdStreamSql(AdKeywordStreamDataParam param, LocalDate startDate, LocalDate endDate, String tableName, List<Object> argsList) {
        StringBuilder selectSql = new StringBuilder("SELECT ")
                .append("sum(clicks)                               clicks,")
                .append("ROUND(sum(cost), 2)                       cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from ").append(tableName);
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        argsList.add(param.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", param.getSellerIds(), argsList));

        //可能超过10000条, 拆成bitmap写法
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            String keywordIdStr = StringUtils.join(param.getKeywordIds(), ",");
            whereSql.append(" and bitmap_has_any(BITMAP_FROM_STRING(keyword_id),BITMAP_FROM_STRING(?)) ");
            argsList.add(keywordIdStr);
        }
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            String adIdStr = StringUtils.join(param.getAdIds(), ",");
            whereSql.append(" and bitmap_has_any(BITMAP_FROM_STRING(ad_id),BITMAP_FROM_STRING(?)) ");
            argsList.add(adIdStr);
        }

        if (StringUtils.isNotBlank(param.getType())) {
            whereSql.append("and type = ? ");
            argsList.add(param.getType());
        }

        whereSql.append(" and date >= ? ");
        argsList.add(startDate);
        whereSql.append(" and date <= ? ");
        argsList.add(endDate);

        selectSql.append(whereSql);

        return selectSql;
    }

    @Override
    public List<AmazonMarketingStreamData> queryAdStreamByKeywordIdsAndAdIds(AdKeywordStreamDataParam param) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        LocalDate splitDate = LocalDate.now().minusDays(65);
        // 结束时间小于65天的, 只查询天维度数据
        // 开始时间小于65天,结束时间大于65天, union all 查询日维度和小时维度数据
        // 开始时间大于65天只查询小时数据
        StringBuilder selectSql;
        List<Object> argsList = Lists.newArrayList();
        if (end.isBefore(splitDate)) {
            selectSql = builderQueryAdStreamSql(param, start, end, FEAD_DAY_DATA_TABLE, argsList, true);
        } else if (start.isAfter(splitDate)) {
            selectSql = builderQueryAdStreamSql(param, start, end, FEAD_HOUR_DATA_TABLE, argsList, true);
        } else {
            selectSql = new StringBuilder("SELECT any(date), keyword_id, ")
                    .append("any(adIdListStr)                               adIdListStr,")
                    .append("sum(clicks)                               clicks,")
                    .append("ROUND(sum(cost), 2)                       cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales7d)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");


            selectSql.append(" from (").append(builderQueryAdStreamSql(param, start, splitDate, FEAD_DAY_DATA_TABLE, argsList, false).append(" union all ")
                    .append(builderQueryAdStreamSql(param, splitDate.plusDays(1), end, FEAD_HOUR_DATA_TABLE, argsList, false))).append(") t");
            selectSql.append(" group by t.keyword_id ");
            //日期维度明细查询
            if (param.getGroupByDate() != null && param.getGroupByDate()) {
                selectSql.append(" ,t.date ");
            }
        }


        return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(AmazonMarketingStreamData.class), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> queryAdStreamByKeywordIdsAndAdIdsGroupByHour(AdKeywordStreamDataParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql;
        selectSql = new StringBuilder("SELECT seller_id, date_format(date, '%Y%m') dataMonth, time, marketplace_id,")
                .append("sum(clicks)                               clicks,")
                .append("ROUND(sum(cost), 2)                       cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");
        selectSql.append("  from t_amazon_marketing_stream_data ");

        StringBuilder whereSql = new StringBuilder(" where ");
        whereSql.append(SqlStringUtil.dealInListNotAnd("seller_id", param.getSellerIds(), argsList));
        whereSql.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceIdList(), argsList));

        {
            whereSql.append(" and ad_id in (select ad_id from ods_t_amazon_ad_product where puid = ? ");
            argsList.add(param.getPuid());
            whereSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), argsList));
            whereSql.append(SqlStringUtil.dealInList("asin", param.getAsinList(), argsList));

            if (StringUtils.isNotBlank(param.getCampaignId())) {
                whereSql.append(" and campaign_id = ? ");
                argsList.add(param.getCampaignId());
            }
            if (StringUtils.isNotBlank(param.getGroupId())) {
                whereSql.append(" and ad_group_id = ? ");
                argsList.add(param.getGroupId());
            }
            whereSql.append(")");
        }

        whereSql.append(" and date >= ? ");
        argsList.add(param.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(param.getEndDate());

        selectSql.append(whereSql);

        selectSql.append(" group by seller_id, marketplace_id, time, date_format(date, '%Y%m') ");

        return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(AmazonMarketingStreamData.class), argsList.toArray());
    }

    public StringBuilder builderQueryAdStreamSql(AdKeywordStreamDataParam param, LocalDate start, LocalDate end, String tableName, List<Object> argsList, boolean needGroupBy) {
        StringBuilder selectSql;
        if (needGroupBy) {
            selectSql = new StringBuilder("SELECT any(date) as date, keyword_id, ")
                    .append("array_join(collect_set(ad_id), ',')     adIdListStr,")
                    .append("sum(clicks)                               clicks,")
                    .append("ROUND(sum(cost), 2)                       cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales7d)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");
        } else {
            selectSql = new StringBuilder("SELECT keyword_id, date, ad_id as  adIdListStr, ")
                    .append("clicks,")
                    .append("cost,")
                    .append("impressions,")
                    .append("attributed_conversions7d,")
                    .append("attributed_conversions7d_same_sku,")
                    .append("attributed_sales7d,")
                    .append("attributed_sales7d_same_sku,")
                    .append("attributed_units_ordered7d,")
                    .append("attributed_units_ordered7d_same_sku ");
        }


        selectSql.append("  from  ").append(tableName);

        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");

        argsList.add(param.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", param.getSellerIds(), argsList));

        //可能超过10000条, 拆成bitmap写法
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            String keywordIdStr = StringUtils.join(param.getKeywordIds(), ",");
            whereSql.append(" and bitmap_has_any(BITMAP_FROM_STRING(keyword_id),BITMAP_FROM_STRING(?)) ");
            argsList.add(keywordIdStr);
        }
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            String adIdStr = StringUtils.join(param.getAdIds(), ",");
            whereSql.append(" and bitmap_has_any(BITMAP_FROM_STRING(ad_id),BITMAP_FROM_STRING(?)) ");
            argsList.add(adIdStr);
        }

        if (StringUtils.isNotBlank(param.getType())) {
            whereSql.append("and type = ? ");
            argsList.add(param.getType());
        }

        whereSql.append(" and date >= ? ");
        argsList.add(start);
        whereSql.append(" and date <= ? ");
        argsList.add(end);

        selectSql.append(whereSql);

        if (needGroupBy) {
            selectSql.append(" group by keyword_id ");
            if (param.getGroupByDate() != null && param.getGroupByDate()) {
                selectSql.append(",date ");
            }
        }

        return selectSql;
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisTargetViewSumAdMetric(ProductPerspectiveDiagnoseSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", queryDto.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }

        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by keyword_id ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisTargetViewSumAdMetric error.queryDto:{} shopSales:{} filterDto:{}", queryDto, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisAllTargetViewSumAdMetric(ViewBaseParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT ")
                .append(" sum(clicks) clicks, sum(cost) cost, sum(impressions) impressions, sum(attributed_conversions7d) attributed_conversions7d, sum(attributed_conversions7d_same_sku) attributed_conversions7d_same_sku,")
                .append(" sum(attributed_sales7d) attributed_sales7d, sum(attributed_sales7d_same_sku) attributed_sales7d, sum(attributed_units_ordered7d) attributed_units_ordered7d, sum(attributed_units_ordered7d_same_sku) attributed_units_ordered7d_same_sku from (")
                .append(" SELECT keyword_id, type, ")
                .append(" clicks, cost, impressions, attributed_conversions7d, attributed_conversions7d_same_sku,")
                .append(" attributed_sales7d, attributed_sales7d_same_sku, attributed_units_ordered7d, attributed_units_ordered7d_same_sku,")
                .append(" view_impressions, attributed_orders_new_to_brand14d, attributed_sales_new_to_brand14d, attributed_units_ordered_new_to_brand14d from (");
        List<Object> argsList = Lists.newArrayList();

        StringBuilder whereSql = new StringBuilder(" and marketplace_id = ? ");
        List<Object> whereArgsList = Lists.newArrayList();
        whereArgsList.add(param.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", param.getSellerIdList(), whereArgsList));
        whereSql.append(" and date >= ? ");
        whereArgsList.add(param.getStartDate());
        whereSql.append(" and date <= ? ");
        whereArgsList.add(param.getEndDate());

        //存放多个类型的sql
        StringJoiner sj = new StringJoiner(" UNION ALL ");
        List<String> typeList = StringUtil.splitStr(param.getType());
        StringBuilder sql;
        if (typeList.contains(Constants.SP) && CollectionUtils.isNotEmpty(param.getSpAdTargetIdList()) && CollectionUtils.isNotEmpty(param.getSpAdIdList())) {
            sql = new StringBuilder("SELECT keyword_id, any(type) type,")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SP))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("keyword_id", param.getSpAdTargetIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSpAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by keyword_id ");
            sj.add(sql.toString());
        }
        if (typeList.contains(Constants.SB) && CollectionUtils.isNotEmpty(param.getSbAdTargetIdList()) && CollectionUtils.isNotEmpty(param.getSbAdIdList())) {
            sql = new StringBuilder("SELECT keyword_id, any(type) type, ")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SB))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("keyword_id", param.getSbAdTargetIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSbAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by keyword_id ");
            sj.add(sql.toString());
        }
        if (typeList.contains(Constants.SD) && CollectionUtils.isNotEmpty(param.getSdAdTargetIdList()) && CollectionUtils.isNotEmpty(param.getSdAdIdList())) {
            sql = new StringBuilder("SELECT keyword_id, any(type) type, ")
                    .append(this.buildProductPerspectiveAllTypeSql(Constants.SD))
                    .append(" from t_amazon_marketing_stream_data ")
                    .append(" where ").append(SqlStringUtil.dealBitMapDorisInNoAndList("keyword_id", param.getSdAdTargetIdList(), argsList))
                    .append(SqlStringUtil.dealBitMapDorisInList("ad_id", param.getSdAdIdList(), argsList))
                    .append(whereSql);
            argsList.addAll(whereArgsList);
            sql.append(" group by keyword_id ");
            sj.add(sql.toString());
        }
        selectSql.append(sj).append(" ) c where 1=1 ")
                .append(this.buildProductPerspectiveAnalysisWhereFilterSql(dto, shopSales, argsList))
                .append(" ) s ");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisPlacementViewSumAdMetric(ProductPerspectiveDiagnoseSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto) {
        StringBuilder selectSql = new StringBuilder("SELECT ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));

        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getPlacements())) {
            whereSql.append(" and placement = ? ");
            argsList.add(queryDto.getPlacements().get(0));
        }

        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id, placement ");
        selectSql.append(this.buildProductPerspectiveAnalysisFilterSql(dto, shopSales, argsList));
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisPlacementViewSumAdMetric error.queryDto:{} shopSales:{} filterDto:{}", queryDto, shopSales, dto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsProductHourlyReportOfTarget(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT keyword_id, any(keyword_text) keyword_text, time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time, keyword_id ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsProductHourlyReportOfTarget error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsTargetHourlyReportOfAd(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT ad_id, time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(queryDto.getItemIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", queryDto.getItemIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time, ad_id ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsTargetHourlyReportOfAd error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsTargetHourlyReportOfPlacement(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT placement, time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(SqlStringUtil.dealInList("seller_id", queryDto.getSellerIds(), argsList));
        if (CollectionUtils.isNotEmpty(queryDto.getItemIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", queryDto.getItemIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time, placement ");
        // mysql 5.7会默认对group by字段进行隐式排序,doris不支持,需要手动排序
        selectSql.append(" order by time ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsTargetHourlyReportOfPlacement error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsTargetHourlyReportOfPlacementMultiple(FeedHourlySelectDTO feedHourlySelectDTO) {
        StringBuilder selectSql = new StringBuilder("SELECT time,placement,currency,DATE_FORMAT(date, '%Y%m') month, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where 1=1 ");
        List<Object> argsList = Lists.newArrayList();
        whereSql.append(SqlStringUtil.dealInList("seller_id", feedHourlySelectDTO.getSellerIdList(), argsList));
        whereSql.append(SqlStringUtil.dealInList("marketplace_id", feedHourlySelectDTO.getMarketplaceIdList(), argsList));
        whereSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id)", feedHourlySelectDTO.getConcatWsList(), argsList));
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", feedHourlySelectDTO.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", feedHourlySelectDTO.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", feedHourlySelectDTO.getExcludeCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", feedHourlySelectDTO.getKeywordIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getPlacements())) {
            whereSql.append(SqlStringUtil.dealNotInList("placements", feedHourlySelectDTO.getPlacements(), argsList));
        }
        selectSql.append(whereSql);
        selectSql.append(" group by time, placement,currency,DATE_FORMAT(date, '%Y%m')");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsProductHourlyReportOfPlacement(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT placement, time, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time, placement ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsProductHourlyReportOfPlacement error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsProductHourlyReport(FeedHourlySelectDTO feedHourlySelectDTO) {
        StringBuilder selectSql = new StringBuilder("SELECT time, ")
                .append(buildSelectSql(feedHourlySelectDTO.getType()));

        selectSql.append("  from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(feedHourlySelectDTO.getSellerId());
        argsList.add(feedHourlySelectDTO.getMarketplaceId());
        if (StringUtils.isNotBlank(feedHourlySelectDTO.getType())) {
            whereSql.append(" and type = ? ");
            argsList.add(feedHourlySelectDTO.getType());
        }
        if (feedHourlySelectDTO.getStart() != null) {
            whereSql.append(" and date >= ? ");
            argsList.add(feedHourlySelectDTO.getStart());
        }
        if (feedHourlySelectDTO.getEnd() != null) {
            whereSql.append(" and date <= ? ");
            argsList.add(feedHourlySelectDTO.getEnd());
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("weekday", feedHourlySelectDTO.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(feedHourlySelectDTO.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", feedHourlySelectDTO.getAdIds(), argsList));
        }

        selectSql.append(whereSql);
        selectSql.append(" group by time ");

        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsProductHourlyReport error.queryDto:{}", feedHourlySelectDTO, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> getHourReportByCampaignId(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time_window_start, campaign_id, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? and campaign_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        argsList.add(queryDto.getCampaignIds().get(0));
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id, time_window_start ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("getHourReportByCampaignId error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> productPerspectiveAnalysisGetHourReportByCampaignId(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time_window_start, campaign_id, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? and campaign_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        argsList.add(queryDto.getCampaignIds().get(0));
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by campaign_id, time_window_start ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisGetHourReportByCampaignId error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> getHourReportByKeywordId(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time_window_start, keyword_id, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? and keyword_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        argsList.add(queryDto.getItemIds().get(0));
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by keyword_id, time_window_start ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("productPerspectiveAnalysisGetHourReportByCampaignId error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsByWeek(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, weekday, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", queryDto.getCampaignIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time, weekday ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsByWeek error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsByWeek(CommonHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, weekday, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,");

        if (Constants.SB.equalsIgnoreCase(queryDto.getType())) {
            selectSql.append("sum(viewable_impressions)                 viewable_impressions,")
                    .append("sum(attributed_orders_new_to_brand14d)         attributed_orders_new_to_brand14d,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d)          attributed_sales_new_to_brand14d,");
        }
        if (Constants.SD.equalsIgnoreCase(queryDto.getType())) {
            selectSql.append("sum(view_impressions)                                view_impressions,")
                    .append("sum(attributed_orders_new_to_brand14d)               attributed_orders_new_to_brand14d,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)        attributed_units_ordered_new_to_brand14d,")
                    .append("sum(view_attributed_conversions14d)                  view_attributed_conversions14d,")
                    .append("sum(view_attributed_sales14d)                        view_attributed_sales14d,")
                    .append("sum(view_attributed_units_ordered14d)                view_attributed_units_ordered14d,")
                    .append("sum(view_attributed_orders_new_to_brand14d)          view_attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_units_ordered_new_to_brand14d)   view_attributed_units_ordered_new_to_brand14d,")
                    .append("sum(view_attributed_sales_new_to_brand14d)           view_attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d)                attributed_sales_new_to_brand14d,");
        }
        selectSql.append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku");
        selectSql.append(" from t_amazon_marketing_stream_data");

        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerId());
        argsList.add(queryDto.getMarketplaceId());
        whereSql.append(" AND date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" AND date <= ? ");
        argsList.add(queryDto.getEndDate());
        whereSql.append(" AND type = ? ");
        argsList.add(queryDto.getType());

        appendInClause(whereSql, "campaign_id", queryDto.getCampaignIds(), argsList);
        appendInClause(whereSql, "ad_group_id", queryDto.getGroupIds(), argsList);
        appendInClause(whereSql, "keyword_id", queryDto.getKeywordTargetingIds(), argsList);
        appendInClause(whereSql, "weekday", queryDto.getWeekdayList(), argsList);

        if (CollectionUtils.isNotEmpty(queryDto.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("campaign_id", queryDto.getExcludeCampaignIds(), argsList));
        }

        selectSql.append(whereSql);
        selectSql.append(" GROUP BY time, weekday");

        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsByWeek error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    private void appendInClause(StringBuilder sql, String columnName, List<?> values, List<Object> argsList) {
        if (CollectionUtils.isNotEmpty(values)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList(columnName, values, argsList));
        }
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsProductWeeklySuperpositionReport(CampaignHourlyReportSelectDto queryDto) {
        StringBuilder selectSql = new StringBuilder("SELECT time, weekday, ")
                .append("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                .append("sum(attributed_sales14d)                  attributed_sales14d,")
                .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                .append("sum(attributed_sales7d)                   attributed_sales7d,")
                .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku");

        selectSql.append(" from  t_amazon_marketing_stream_data");
        StringBuilder whereSql = new StringBuilder(" where seller_id = ? and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(queryDto.getSellerIds().get(0));
        argsList.add(queryDto.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(queryDto.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("ad_id", queryDto.getAdIds(), argsList));
        }
        whereSql.append(" and weekday in (1, 2, 3, 4, 5, 6, 7) ");
        whereSql.append(" and date >= ? ");
        argsList.add(queryDto.getStartDate());
        whereSql.append(" and date <= ? ");
        argsList.add(queryDto.getEndDate());

        selectSql.append(whereSql);
        selectSql.append(" group by time, weekday ");
        try {
            return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
        } catch (Exception e) {
            log.error("statisticsProductWeeklySuperpositionReport error.queryDto:{}", queryDto, e);
        }
        return Collections.emptyList();
    }

    private String buildProductPerspectiveAnalysisOrderSql(ProductPerspectiveDiagnoseSelectDto queryDto, BigDecimal shopSales, List<Object> argsList) {
        StringBuilder orderSql = new StringBuilder();
        if (queryDto.getOrderField() == null || queryDto.getOrderType() == null) {
            return orderSql.toString();
        }
        String orderField = queryDto.getOrderField();
        if ("adCost".equals(orderField)) {
            orderSql.append("order by cost ");
        } else if ("impressions".equals(orderField)) {
            orderSql.append("order by impressions ");
        } else if ("clicks".equals(orderField)) {
            orderSql.append("order by clicks ");
        } else if ("cpa".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/attributed_conversions7d, 0), 2) ");
        } else if ("adCostPerClick".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/clicks,0),2) ");
        } else if ("ctr".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(clicks/impressions*100,0),2) ");
        } else if ("cvr".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(attributed_conversions7d/clicks*100,0),2) ");
        } else if ("acos".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/attributed_sales7d*100,0),2) ");
        } else if ("roas".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(attributed_sales7d/cost,0),2) ");
        } else if ("acots".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/?*100,0),2) ");
            argsList.add(shopSales);
        } else if ("asots".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(attributed_sales7d/?*100,0),2) ");
            argsList.add(shopSales);
        } else if ("adOrderNum".equals(orderField)) {
            orderSql.append("order by attributed_conversions7d ");
        } else if ("adSaleNum".equals(orderField)) {
            orderSql.append("order by attributed_conversions7d_same_sku ");
        } else if ("adOtherOrderNum".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_conversions7d - attributed_conversions7d_same_sku, 0) ");
        } else if ("adSale".equals(orderField)) {
            orderSql.append("order by attributed_sales7d ");
        } else if ("adSales".equals(orderField)) {
            orderSql.append("order by attributed_sales7d_same_sku ");
        } else if ("adOtherSales".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales7d - attributed_sales7d_same_sku, 0) ");
        } else if ("orderNum".equals(orderField)) {
            orderSql.append("order by attributed_units_ordered7d ");
        } else if ("adSelfSaleNum".equals(orderField)) {
            orderSql.append("order by attributed_units_ordered7d_same_sku ");
        } else if ("adOtherSaleNum".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_units_ordered7d - attributed_units_ordered7d_same_sku, 0) ");
        } else if ("advertisingUnitPrice".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales7d/attributed_conversions7d, 0) ");
        }
        if (orderSql.length() > 0 && "desc".equals(queryDto.getOrderType())) {
            orderSql.append("desc ");
        }
        return orderSql.toString();
    }

    private String buildProductPerspectiveAnalysisOrderSql(String orderField, String orderType) {
        StringBuilder orderSql = new StringBuilder();
        if (orderField == null || orderType == null) {
            return orderSql.toString();
        }
        if ("adCost".equals(orderField)) {
            orderSql.append("order by cost ");
        } else if ("impressions".equals(orderField)) {
            orderSql.append("order by impressions ");
        } else if ("clicks".equals(orderField)) {
            orderSql.append("order by clicks ");
        } else if ("cpa".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/attributed_conversions7d, 0), 2) ");
        } else if ("adCostPerClick".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/clicks,0),2) ");
        } else if ("ctr".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(clicks/impressions*100,0),2) ");
        } else if ("cvr".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(attributed_conversions7d/clicks*100,0),2) ");
        } else if ("acos".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(cost/attributed_sales7d*100,0),2) ");
        } else if ("roas".equals(orderField)) {
            orderSql.append("order by ROUND(ifnull(attributed_sales7d/cost,0),2) ");
        } else if ("adOrderNum".equals(orderField)) {
            orderSql.append("order by attributed_conversions7d ");
        } else if ("adSaleNum".equals(orderField)) {
            orderSql.append("order by attributed_conversions7d_same_sku ");
        } else if ("adOtherOrderNum".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_conversions7d - attributed_conversions7d_same_sku, 0) ");
        } else if ("adSale".equals(orderField)) {
            orderSql.append("order by attributed_sales7d ");
        } else if ("adSales".equals(orderField)) {
            orderSql.append("order by attributed_sales7d_same_sku ");
        } else if ("adOtherSales".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales7d - attributed_sales7d_same_sku, 0) ");
        } else if ("orderNum".equals(orderField)) {
            orderSql.append("order by attributed_units_ordered7d ");
        } else if ("adSelfSaleNum".equals(orderField)) {
            orderSql.append("order by attributed_units_ordered7d_same_sku ");
        } else if ("adOtherSaleNum".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_units_ordered7d - attributed_units_ordered7d_same_sku, 0) ");
        } else if ("advertisingUnitPrice".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales7d/attributed_conversions7d, 0) ");
        } else if ("viewImpressions".equals(orderField)) {
            orderSql.append("order by ifnull(view_impressions, 0) ");
        } else if ("vcpm".equals(orderField)) {
            orderSql.append("order by ifnull(cost/view_impressions, 0) ");
        } else if ("acots".equals(orderField)) {
            orderSql.append("order by ifnull(cost, 0) ");
        } else if ("asots".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales7d, 0) ");
        } else if ("ordersNewToBrandFTD".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_orders_new_to_brand14d, 0) ");
        } else if ("orderRateNewToBrandFTD".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_orders_new_to_brand14d/attributed_conversions7d, 0) ");
        } else if ("salesNewToBrandFTD".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales_new_to_brand14d, 0) ");
        } else if ("salesRateNewToBrandFTD".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_sales_new_to_brand14d/attributed_sales7d, 0) ");
        } else if ("unitsOrderedNewToBrandFTD".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_units_ordered_new_to_brand14d, 0) ");
        } else if ("unitsOrderedRateNewToBrandFTD".equals(orderField)) {
            orderSql.append("order by ifnull(attributed_units_ordered_new_to_brand14d/attributed_units_ordered7d, 0) ");
        }
        if ("desc".equals(orderType)) {
            orderSql.append("desc ");
        }
        return orderSql.toString();
    }

    private String buildProductPerspectiveAnalysisFilterSql(ProductPerspectiveAnalysisFilterDto dto, BigDecimal shopSales, List<Object> argsList) {
        StringBuilder filterSql = new StringBuilder();
        if (dto == null) {
            return filterSql.toString();
        }
        filterSql.append(" having 1=1 ");
        if (dto.getCostMin() != null) {
            filterSql.append(" and sum(cost) >= ? ");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            filterSql.append(" and sum(cost) <= ? ");
            argsList.add(dto.getCostMax());
        }
        if (dto.getImpressionsMin() != null) {
            filterSql.append(" and sum(impressions) >= ? ");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            filterSql.append(" and sum(impressions) <= ? ");
            argsList.add(dto.getImpressionsMax());
        }
        if (dto.getClicksMin() != null) {
            filterSql.append(" and sum(clicks) >= ? ");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            filterSql.append(" and sum(clicks) <= ? ");
            argsList.add(dto.getClicksMax());
        }
        if (dto.getCpaMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(attributed_conversions7d), 0), 2) >= ? ");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(attributed_conversions7d), 0), 2) <= ? ");
            argsList.add(dto.getCpaMax());
        }
        if (dto.getCpcMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) >= ? ");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(clicks),0),2) <= ? ");
            argsList.add(dto.getCpcMax());
        }
        if (dto.getClickRateMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) >= ? ");
            argsList.add(dto.getClickRateMin());
        }
        if (dto.getClickRateMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(clicks)/sum(impressions)*100,0),2) <= ? ");
            argsList.add(dto.getClickRateMax());
        }
        if (dto.getSalesConversionRateMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(attributed_conversions7d)/sum(clicks)*100,0),2) >= ? ");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(attributed_conversions7d)/sum(clicks)*100,0),2) <= ? ");
            argsList.add(dto.getSalesConversionRateMax());
        }
        if (dto.getAcosMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(attributed_sales7d)*100,0),2) >= ? ");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(attributed_sales7d)*100,0),2) <= ? ");
            argsList.add(dto.getAcosMax());
        }
        if (dto.getRoasMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(attributed_sales7d)/sum(cost),0),2) >= ? ");
            argsList.add(dto.getRoasMin());
        }
        if (dto.getRoasMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(attributed_sales7d)/sum(cost),0),2) <= ? ");
            argsList.add(dto.getRoasMax());
        }
        if (dto.getAcotsMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/?*100,0),2) >= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAcotsMin());
        }
        if (dto.getAcotsMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/?*100,0),2) <= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAcotsMax());
        }
        if (dto.getAsotsMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(attributed_sales7d)/?*100,0),2) >= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAsotsMin());
        }
        if (dto.getAsotsMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(attributed_sales7d)/?*100,0),2) <= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAsotsMax());
        }
        if (dto.getOrderNumMin() != null) {
            filterSql.append(" and sum(attributed_conversions7d) >= ? ");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            filterSql.append(" and sum(attributed_conversions7d) <= ? ");
            argsList.add(dto.getOrderNumMax());
        }
        if (dto.getAdSaleNumMin() != null) {
            filterSql.append(" and sum(attributed_conversions7d_same_sku) >= ? ");
            argsList.add(dto.getAdSaleNumMin());
        }
        if (dto.getAdSaleNumMax() != null) {
            filterSql.append(" and sum(attributed_conversions7d_same_sku) <= ? ");
            argsList.add(dto.getAdSaleNumMax());
        }
        if (dto.getAdOtherOrderNumMin() != null) {
            filterSql.append(" and ifnull(sum(attributed_conversions7d) - sum(attributed_conversions7d_same_sku), 0) >= ? ");
            argsList.add(dto.getAdOtherOrderNumMin());
        }
        if (dto.getAdOtherOrderNumMax() != null) {
            filterSql.append(" and ifnull(sum(attributed_conversions7d) - sum(attributed_conversions7d_same_sku), 0) <= ? ");
            argsList.add(dto.getAdOtherOrderNumMax());
        }
        if (dto.getSalesMin() != null) {
            filterSql.append(" and sum(attributed_sales7d) >= ? ");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            filterSql.append(" and sum(attributed_sales7d) <= ? ");
            argsList.add(dto.getSalesMax());
        }
        if (dto.getAdSalesMin() != null) {
            filterSql.append(" and sum(attributed_sales7d_same_sku) >= ? ");
            argsList.add(dto.getAdSalesMin());
        }
        if (dto.getAdSalesMax() != null) {
            filterSql.append(" and sum(attributed_sales7d_same_sku) <= ? ");
            argsList.add(dto.getAdSalesMax());
        }
        if (dto.getAdOtherSalesMin() != null) {
            filterSql.append(" and ifnull(sum(attributed_sales7d) - sum(attributed_sales7d_same_sku), 0) >= ? ");
            argsList.add(dto.getAdOtherSalesMin());
        }
        if (dto.getAdOtherSalesMax() != null) {
            filterSql.append(" and ifnull(sum(attributed_sales7d) - sum(attributed_sales7d_same_sku), 0) <= ? ");
            argsList.add(dto.getAdOtherSalesMax());
        }
        if (dto.getAdSalesTotalMin() != null) {
            filterSql.append(" and sum(attributed_units_ordered7d) >= ? ");
            argsList.add(dto.getAdSalesTotalMin());
        }
        if (dto.getAdSalesTotalMax() != null) {
            filterSql.append(" and sum(attributed_units_ordered7d) <= ? ");
            argsList.add(dto.getAdSalesTotalMax());
        }
        if (dto.getAdSelfSaleNumMin() != null) {
            filterSql.append(" and sum(attributed_units_ordered7d_same_sku) >= ? ");
            argsList.add(dto.getAdSelfSaleNumMin());
        }
        if (dto.getAdSelfSaleNumMax() != null) {
            filterSql.append(" and sum(attributed_units_ordered7d_same_sku) <= ? ");
            argsList.add(dto.getAdSelfSaleNumMax());
        }
        if (dto.getAdOtherSaleNumMin() != null) {
            filterSql.append(" and ifnull(sum(attributed_units_ordered7d) - sum(attributed_units_ordered7d_same_sku), 0) >= ? ");
            argsList.add(dto.getAdOtherSaleNumMin());
        }
        if (dto.getAdOtherSaleNumMax() != null) {
            filterSql.append(" and ifnull(sum(attributed_units_ordered7d) - sum(attributed_units_ordered7d_same_sku), 0) <= ? ");
            argsList.add(dto.getAdOtherSaleNumMax());
        }
        if (dto.getAdvertisingUnitPriceMin() != null) {
            filterSql.append(" and ifnull(sum(attributed_sales7d)/sum(attributed_conversions7d), 0) >= ? ");
            argsList.add(dto.getAdvertisingUnitPriceMin());
        }
        if (dto.getAdvertisingUnitPriceMax() != null) {
            filterSql.append(" and ifnull(sum(attributed_sales7d)/sum(attributed_conversions7d), 0) <= ? ");
            argsList.add(dto.getAdvertisingUnitPriceMax());
        }
        if (dto.getViewImpressionsMax() != null) {
            filterSql.append(" and sum(view_impressions) >= ? ");
            argsList.add(dto.getViewImpressionsMax());
        }
        if (dto.getViewImpressionsMin() != null) {
            filterSql.append(" and sum(view_impressions) <= ? ");
            argsList.add(dto.getViewImpressionsMin());
        }
        if (dto.getVcpmMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(view_impressions)*1000,0),2) <= ? ");
            argsList.add(dto.getVcpmMax());
        }
        if (dto.getVcpmMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum(cost)/sum(view_impressions)*1000,0),2) <= ? ");
            argsList.add(dto.getVcpmMin());
        }
        if (dto.getOrdersNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(sum(view_attributed_orders_new_to_brand14d), 0) >= ? ");
            argsList.add(dto.getOrdersNewToBrandFTDMin());
        }
        if (dto.getOrdersNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(sum(view_attributed_orders_new_to_brand14d), 0) <= ? ");
            argsList.add(dto.getOrdersNewToBrandFTDMax());
        }
        if (dto.getOrderRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum(view_attributed_orders_new_to_brand14d)/sum(view_attributed_conversions14d)) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getOrderRateNewToBrandFTDMin());
        }
        if (dto.getOrderRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum(view_attributed_orders_new_to_brand14d)/sum(view_attributed_conversions14d)) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getOrderRateNewToBrandFTDMax());
        }
        if (dto.getSalesNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(sum(view_attributed_sales_new_to_brand14d), 0) >= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMin());
        }
        if (dto.getSalesNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(sum(view_attributed_sales_new_to_brand14d), 0) <= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMax());
        }
        if (dto.getSalesRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum(view_attributed_sales_new_to_brand14d)/sum(view_attributed_sales14d)) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getSalesRateNewToBrandFTDMin());
        }
        if (dto.getSalesRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum(view_attributed_sales_new_to_brand14d)/sum(view_attributed_sales14d)) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getSalesRateNewToBrandFTDMax());
        }
        if (dto.getUnitsOrderedNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(sum(attributed_units_ordered_new_to_brand14d), 0) >= ? ");
            argsList.add(dto.getUnitsOrderedNewToBrandFTDMin());
        }
        if (dto.getUnitsOrderedNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(sum(attributed_units_ordered_new_to_brand14d), 0) <= ? ");
            argsList.add(dto.getUnitsOrderedNewToBrandFTDMax());
        }
        if (dto.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum(view_attributed_units_ordered_new_to_brand14d)/sum(view_attributed_units_ordered14d)) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (dto.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum(view_attributed_units_ordered_new_to_brand14d)/sum(view_attributed_units_ordered14d)) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getUnitsOrderedRateNewToBrandFTDMax());
        }
        return filterSql.toString();
    }

    /**
     * 产品透视wheresql拼接，sd做本产品广告订单量和其他产品广告订单量有特殊逻辑处理
     */
    private String buildProductPerspectiveAnalysisWhereFilterSql(ProductPerspectiveAnalysisFilterDto dto, BigDecimal shopSales, List<Object> argsList) {
        StringBuilder filterSql = new StringBuilder();
        if (dto == null) {
            return filterSql.toString();
        }
        if (dto.getCostMin() != null) {
            filterSql.append(" and cost >= ? ");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            filterSql.append(" and cost <= ? ");
            argsList.add(dto.getCostMax());
        }
        if (dto.getImpressionsMin() != null) {
            filterSql.append(" and impressions >= ? ");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            filterSql.append(" and impressions <= ? ");
            argsList.add(dto.getImpressionsMax());
        }
        if (dto.getClicksMin() != null) {
            filterSql.append(" and clicks >= ? ");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            filterSql.append(" and clicks <= ? ");
            argsList.add(dto.getClicksMax());
        }
        if (dto.getCpaMin() != null) {
            filterSql.append(" and ROUND(ifnull(cost/attributed_conversions7d, 0), 2) >= ? ");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            filterSql.append(" and ROUND(ifnull(cost/attributed_conversions7d, 0), 2) <= ? ");
            argsList.add(dto.getCpaMax());
        }
        if (dto.getCpcMin() != null) {
            filterSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ? ");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            filterSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ? ");
            argsList.add(dto.getCpcMax());
        }
        if (dto.getClickRateMin() != null) {
            filterSql.append(" and ROUND(ifnull(clicks/impressions*100,0),2) >= ? ");
            argsList.add(dto.getClickRateMin());
        }
        if (dto.getClickRateMax() != null) {
            filterSql.append(" and ROUND(ifnull(clicks/impressions*100,0),2) <= ? ");
            argsList.add(dto.getClickRateMax());
        }
        if (dto.getSalesConversionRateMin() != null) {
            filterSql.append(" and ROUND(ifnull(attributed_conversions7d/clicks*100,0),2) >= ? ");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            filterSql.append(" and ROUND(ifnull(attributed_conversions7d/clicks*100,0),2) <= ? ");
            argsList.add(dto.getSalesConversionRateMax());
        }
        if (dto.getAcosMin() != null) {
            filterSql.append(" and ROUND(ifnull(cost/attributed_sales7d*100,0),2) >= ? ");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            filterSql.append(" and ROUND(ifnull(cost/attributed_sales7d*100,0),2) <= ? ");
            argsList.add(dto.getAcosMax());
        }
        if (dto.getRoasMin() != null) {
            filterSql.append(" and ROUND(ifnull(attributed_sales7d/cost,0),2) >= ? ");
            argsList.add(dto.getRoasMin());
        }
        if (dto.getRoasMax() != null) {
            filterSql.append(" and ROUND(ifnull(attributed_sales7d/cost,0),2) <= ? ");
            argsList.add(dto.getRoasMax());
        }
        if (dto.getAcotsMin() != null) {
            filterSql.append(" and ROUND(ifnull(cost/?*100,0),2) >= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAcotsMin());
        }
        if (dto.getAcotsMax() != null) {
            filterSql.append(" and ROUND(ifnull(cost/?*100,0),2) <= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAcotsMax());
        }
        if (dto.getAsotsMin() != null) {
            filterSql.append(" and ROUND(ifnull(attributed_sales7d/?*100,0),2) >= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAsotsMin());
        }
        if (dto.getAsotsMax() != null) {
            filterSql.append(" and ROUND(ifnull(attributed_sales7d/?*100,0),2) <= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAsotsMax());
        }
        if (dto.getOrderNumMin() != null) {
            filterSql.append(" and attributed_conversions7d >= ? ");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            filterSql.append(" and attributed_conversions7d <= ? ");
            argsList.add(dto.getOrderNumMax());
        }
        //sd不做sql过滤，因为cpc和vpcm对应的本产品广告订单量和其他产品广告订单量值不相同，后面做内存过滤
        if (dto.getAdSaleNumMin() != null) {
            filterSql.append(" and (type = 'SD' or (type != 'SD' and attributed_conversions7d_same_sku >= ?)) ");
            argsList.add(dto.getAdSaleNumMin());
        }
        if (dto.getAdSaleNumMax() != null) {
            filterSql.append(" and (type = 'SD' or (type != 'SD' and attributed_conversions7d_same_sku <= ?)) ");
            argsList.add(dto.getAdSaleNumMax());
        }
        if (dto.getAdOtherOrderNumMin() != null) {
            filterSql.append(" and (type = 'SD' or (type != 'SD' and ifnull(attributed_conversions7d - attributed_conversions7d_same_sku, 0) >= ?)) ");
            argsList.add(dto.getAdOtherOrderNumMin());
        }
        if (dto.getAdOtherOrderNumMax() != null) {
            filterSql.append(" and (type = 'SD' or (type != 'SD' and ifnull(attributed_conversions7d - attributed_conversions7d_same_sku, 0) <= ?)) ");
            argsList.add(dto.getAdOtherOrderNumMax());
        }
        if (dto.getSalesMin() != null) {
            filterSql.append(" and attributed_sales7d >= ? ");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            filterSql.append(" and attributed_sales7d <= ? ");
            argsList.add(dto.getSalesMax());
        }
        if (dto.getAdSalesMin() != null) {
            filterSql.append(" and attributed_sales7d_same_sku >= ? ");
            argsList.add(dto.getAdSalesMin());
        }
        if (dto.getAdSalesMax() != null) {
            filterSql.append(" and attributed_sales7d_same_sku <= ? ");
            argsList.add(dto.getAdSalesMax());
        }
        if (dto.getAdOtherSalesMin() != null) {
            filterSql.append(" and ifnull(attributed_sales7d - attributed_sales7d_same_sku, 0) >= ? ");
            argsList.add(dto.getAdOtherSalesMin());
        }
        if (dto.getAdOtherSalesMax() != null) {
            filterSql.append(" and ifnull(attributed_sales7d - attributed_sales7d_same_sku, 0) <= ? ");
            argsList.add(dto.getAdOtherSalesMax());
        }
        if (dto.getAdSalesTotalMin() != null) {
            filterSql.append(" and attributed_units_ordered7d >= ? ");
            argsList.add(dto.getAdSalesTotalMin());
        }
        if (dto.getAdSalesTotalMax() != null) {
            filterSql.append(" and attributed_units_ordered7d <= ? ");
            argsList.add(dto.getAdSalesTotalMax());
        }
        if (dto.getAdSelfSaleNumMin() != null) {
            filterSql.append(" and attributed_units_ordered7d_same_sku >= ? ");
            argsList.add(dto.getAdSelfSaleNumMin());
        }
        if (dto.getAdSelfSaleNumMax() != null) {
            filterSql.append(" and attributed_units_ordered7d_same_sku <= ? ");
            argsList.add(dto.getAdSelfSaleNumMax());
        }
        if (dto.getAdOtherSaleNumMin() != null) {
            filterSql.append(" and if(type = 'SP', ifnull(attributed_units_ordered7d - attributed_units_ordered7d_same_sku, 0), 0) >= ? ");
            argsList.add(dto.getAdOtherSaleNumMin());
        }
        if (dto.getAdOtherSaleNumMax() != null) {
            filterSql.append(" and if(type = 'SP', ifnull(attributed_units_ordered7d - attributed_units_ordered7d_same_sku, 0), 0) <= ? ");
            argsList.add(dto.getAdOtherSaleNumMax());
        }
        if (dto.getAdvertisingUnitPriceMin() != null) {
            filterSql.append(" and ifnull(attributed_sales7d/attributed_conversions7d, 0) >= ? ");
            argsList.add(dto.getAdvertisingUnitPriceMin());
        }
        if (dto.getAdvertisingUnitPriceMax() != null) {
            filterSql.append(" and ifnull(attributed_sales7d)/attributed_conversions7d, 0) <= ? ");
            argsList.add(dto.getAdvertisingUnitPriceMax());
        }
        if (dto.getViewImpressionsMin() != null) {
            filterSql.append(" and view_impressions >= ? ");
            argsList.add(dto.getViewImpressionsMin());
        }
        if (dto.getViewImpressionsMax() != null) {
            filterSql.append(" and view_impressions <= ? ");
            argsList.add(dto.getViewImpressionsMax());
        }
        if (dto.getVcpmMin() != null) {
            filterSql.append(" and ROUND(ifnull(cost/view_impressions*1000,0),2) >= ? ");
            argsList.add(dto.getVcpmMin());
        }
        if (dto.getVcpmMax() != null) {
            filterSql.append(" and ROUND(ifnull(cost/view_impressions*1000,0),2) <= ? ");
            argsList.add(dto.getVcpmMax());
        }
        if (dto.getOrdersNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(attributed_orders_new_to_brand14d, 0) >= ? ");
            argsList.add(dto.getOrdersNewToBrandFTDMin());
        }
        if (dto.getOrdersNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(attributed_orders_new_to_brand14d, 0) <= ? ");
            argsList.add(dto.getOrdersNewToBrandFTDMax());
        }
        if (dto.getOrderRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((attributed_orders_new_to_brand14d/attributed_conversions7d) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getOrderRateNewToBrandFTDMin());
        }
        if (dto.getOrderRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((attributed_orders_new_to_brand14d/attributed_conversions7d) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getOrderRateNewToBrandFTDMax());
        }
        if (dto.getSalesNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(attributed_sales_new_to_brand14d, 0) >= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMin());
        }
        if (dto.getSalesNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(attributed_sales_new_to_brand14d, 0) <= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMax());
        }
        if (dto.getSalesRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((attributed_sales_new_to_brand14d/attributed_sales7d) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getSalesRateNewToBrandFTDMin());
        }
        if (dto.getSalesRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((attributed_sales_new_to_brand14d/attributed_sales7d) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getSalesRateNewToBrandFTDMax());
        }
        if (dto.getUnitsOrderedNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(attributed_units_ordered_new_to_brand14d, 0) >= ? ");
            argsList.add(dto.getUnitsOrderedNewToBrandFTDMin());
        }
        if (dto.getUnitsOrderedNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(attributed_units_ordered_new_to_brand14d, 0) <= ? ");
            argsList.add(dto.getUnitsOrderedNewToBrandFTDMax());
        }
        if (dto.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((attributed_units_ordered_new_to_brand14d/attributed_units_ordered7d) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (dto.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((attributed_units_ordered_new_to_brand14d/attributed_units_ordered7d) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getUnitsOrderedRateNewToBrandFTDMax());
        }
        return filterSql.toString();
    }

    private String buildProductPerspectiveAnalysisFilterSumSql(ProductPerspectiveAnalysisFilterDto dto, BigDecimal shopSales, List<Object> argsList, String adType) {
        StringBuilder filterSql = new StringBuilder();
        if (dto == null) {
            return filterSql.toString();
        }
        filterSql.append(" having 1=1 ");
        if (dto.getCostMin() != null) {
            filterSql.append(" and sum_cost >= ? ");
            argsList.add(dto.getCostMin());
        }
        if (dto.getCostMax() != null) {
            filterSql.append(" and sum_cost <= ? ");
            argsList.add(dto.getCostMax());
        }
        if (dto.getImpressionsMin() != null) {
            filterSql.append(" and sum_impressions >= ? ");
            argsList.add(dto.getImpressionsMin());
        }
        if (dto.getImpressionsMax() != null) {
            filterSql.append(" and sum_impressions <= ? ");
            argsList.add(dto.getImpressionsMax());
        }
        if (dto.getClicksMin() != null) {
            filterSql.append(" and sum_clicks >= ? ");
            argsList.add(dto.getClicksMin());
        }
        if (dto.getClicksMax() != null) {
            filterSql.append(" and sum_clicks <= ? ");
            argsList.add(dto.getClicksMax());
        }
        if (dto.getCpaMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_attributed_conversions7d, 0), 2) >= ? ");
            argsList.add(dto.getCpaMin());
        }
        if (dto.getCpaMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_attributed_conversions7d, 0), 2) <= ? ");
            argsList.add(dto.getCpaMax());
        }
        if (dto.getCpcMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_clicks,0),2) >= ? ");
            argsList.add(dto.getCpcMin());
        }
        if (dto.getCpcMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_clicks,0),2) <= ? ");
            argsList.add(dto.getCpcMax());
        }
        if (dto.getClickRateMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_clicks/sum_impressions*100,0),2) >= ? ");
            argsList.add(dto.getClickRateMin());
        }
        if (dto.getClickRateMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_clicks/sum_impressions*100,0),2) <= ? ");
            argsList.add(dto.getClickRateMax());
        }
        if (dto.getSalesConversionRateMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_attributed_conversions7d/sum_clicks*100,0),2) >= ? ");
            argsList.add(dto.getSalesConversionRateMin());
        }
        if (dto.getSalesConversionRateMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_attributed_conversions7d/sum_clicks*100,0),2) <= ? ");
            argsList.add(dto.getSalesConversionRateMax());
        }
        if (dto.getAcosMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_attributed_sales7d*100,0),2) >= ? ");
            argsList.add(dto.getAcosMin());
        }
        if (dto.getAcosMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_attributed_sales7d*100,0),2) <= ? ");
            argsList.add(dto.getAcosMax());
        }
        if (dto.getRoasMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_attributed_sales7d/sum_cost,0),2) >= ? ");
            argsList.add(dto.getRoasMin());
        }
        if (dto.getRoasMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_attributed_sales7d/sum_cost,0),2) <= ? ");
            argsList.add(dto.getRoasMax());
        }
        if (dto.getAcotsMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/?*100,0),2) >= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAcotsMin());
        }
        if (dto.getAcotsMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/?*100,0),2) <= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAcotsMax());
        }
        if (dto.getAsotsMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_attributed_sales7d/?*100,0),2) >= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAsotsMin());
        }
        if (dto.getAsotsMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_attributed_sales7d/?*100,0),2) <= ? ");
            argsList.add(shopSales);
            argsList.add(dto.getAsotsMax());
        }
        if (dto.getOrderNumMin() != null) {
            filterSql.append(" and sum_attributed_conversions7d >= ? ");
            argsList.add(dto.getOrderNumMin());
        }
        if (dto.getOrderNumMax() != null) {
            filterSql.append(" and sum_attributed_conversions7d <= ? ");
            argsList.add(dto.getOrderNumMax());
        }
        //sd的情况内存过滤
        if (!Constants.SD.equalsIgnoreCase(adType)) {
            if (dto.getAdSaleNumMin() != null) {
                filterSql.append(" and sum_attributed_conversions7d_same_sku >= ? ");
                argsList.add(dto.getAdSaleNumMin());
            }
            if (dto.getAdSaleNumMax() != null) {
                filterSql.append(" and sum_attributed_conversions7d_same_sku <= ? ");
                argsList.add(dto.getAdSaleNumMax());
            }
            if (dto.getAdOtherOrderNumMin() != null) {
                filterSql.append(" and ifnull(sum_attributed_conversions7d - sum_attributed_conversions7d_same_sku, 0) >= ? ");
                argsList.add(dto.getAdOtherOrderNumMin());
            }
            if (dto.getAdOtherOrderNumMax() != null) {
                filterSql.append(" and ifnull(sum_attributed_conversions7d - sum_attributed_conversions7d_same_sku, 0) <= ? ");
                argsList.add(dto.getAdOtherOrderNumMax());
            }
        }
        if (dto.getSalesMin() != null) {
            filterSql.append(" and sum_attributed_sales7d >= ? ");
            argsList.add(dto.getSalesMin());
        }
        if (dto.getSalesMax() != null) {
            filterSql.append(" and sum_attributed_sales7d <= ? ");
            argsList.add(dto.getSalesMax());
        }
        if (dto.getAdSalesMin() != null) {
            filterSql.append(" and sum_attributed_sales7d_same_sku >= ? ");
            argsList.add(dto.getAdSalesMin());
        }
        if (dto.getAdSalesMax() != null) {
            filterSql.append(" and sum_attributed_sales7d_same_sku <= ? ");
            argsList.add(dto.getAdSalesMax());
        }
        if (dto.getAdOtherSalesMin() != null) {
            filterSql.append(" and ifnull(sum_attributed_sales7d - sum_attributed_sales7d_same_sku, 0) >= ? ");
            argsList.add(dto.getAdOtherSalesMin());
        }
        if (dto.getAdOtherSalesMax() != null) {
            filterSql.append(" and ifnull(sum_attributed_sales7d - sum_attributed_sales7d_same_sku, 0) <= ? ");
            argsList.add(dto.getAdOtherSalesMax());
        }
        if (dto.getAdSalesTotalMin() != null) {
            filterSql.append(" and sum_attributed_units_ordered7d >= ? ");
            argsList.add(dto.getAdSalesTotalMin());
        }
        if (dto.getAdSalesTotalMax() != null) {
            filterSql.append(" and sum_attributed_units_ordered7d <= ? ");
            argsList.add(dto.getAdSalesTotalMax());
        }
        if (dto.getAdSelfSaleNumMin() != null) {
            filterSql.append(" and sum_attributed_units_ordered7d_same_sku >= ? ");
            argsList.add(dto.getAdSelfSaleNumMin());
        }
        if (dto.getAdSelfSaleNumMax() != null) {
            filterSql.append(" and sum_attributed_units_ordered7d_same_sku <= ? ");
            argsList.add(dto.getAdSelfSaleNumMax());
        }
        if (dto.getAdOtherSaleNumMin() != null) {
            filterSql.append(" and if(adType = 'SP', ifnull(sum_attributed_units_ordered7d - sum_attributed_units_ordered7d_same_sku, 0), 0) >= ? ");
            argsList.add(dto.getAdOtherSaleNumMin());
        }
        if (dto.getAdOtherSaleNumMax() != null) {
            filterSql.append(" and if(adType = 'SP', ifnull(sum_attributed_units_ordered7d - sum_attributed_units_ordered7d_same_sku, 0), 0) <= ? ");
            argsList.add(dto.getAdOtherSaleNumMax());
        }
        if (dto.getAdvertisingUnitPriceMin() != null) {
            filterSql.append(" and ifnull(sum_attributed_sales7d/sum_attributed_conversions7d, 0) >= ? ");
            argsList.add(dto.getAdvertisingUnitPriceMin());
        }
        if (dto.getAdvertisingUnitPriceMax() != null) {
            filterSql.append(" and ifnull(sum_attributed_sales7d/sum_attributed_conversions7d, 0) <= ? ");
            argsList.add(dto.getAdvertisingUnitPriceMax());
        }
        if (dto.getViewImpressionsMin() != null) {
            filterSql.append(" and sum_view_impressions >= ? ");
            argsList.add(dto.getViewImpressionsMin());
        }
        if (dto.getViewImpressionsMax() != null) {
            filterSql.append(" and sum_view_impressions <= ? ");
            argsList.add(dto.getViewImpressionsMax());
        }
        if (dto.getVcpmMin() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_view_impressions*1000,0),2) >= ? ");
            argsList.add(dto.getVcpmMin());
        }
        if (dto.getVcpmMax() != null) {
            filterSql.append(" and ROUND(ifnull(sum_cost/sum_view_impressions*1000,0),2) <= ? ");
            argsList.add(dto.getVcpmMax());
        }
        if (dto.getOrdersNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(sum_attributed_orders_new_to_brand14d, 0) >= ? ");
            argsList.add(dto.getOrdersNewToBrandFTDMin());
        }
        if (dto.getOrdersNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(sum_attributed_orders_new_to_brand14d, 0) <= ? ");
            argsList.add(dto.getOrdersNewToBrandFTDMax());
        }
        if (dto.getOrderRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum_attributed_orders_new_to_brand14d/sum_attributed_conversions7d) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getOrderRateNewToBrandFTDMin());
        }
        if (dto.getOrderRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum_attributed_orders_new_to_brand14d/sum_attributed_conversions7d) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getOrderRateNewToBrandFTDMax());
        }
        if (dto.getSalesNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(sum_attributed_sales_new_to_brand14d, 0) >= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMin());
        }
        if (dto.getSalesNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(sum_attributed_sales_new_to_brand14d, 0) <= ? ");
            argsList.add(dto.getSalesNewToBrandFTDMax());
        }
        if (dto.getSalesRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum_attributed_sales_new_to_brand14d/sum_attributed_sales7d) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getSalesRateNewToBrandFTDMin());
        }
        if (dto.getSalesRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum_attributed_sales_new_to_brand14d/sum_attributed_sales7d) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getSalesRateNewToBrandFTDMax());
        }
        if (dto.getUnitsOrderedNewToBrandFTDMin() != null) {
            filterSql.append(" and ifnull(sum_attributed_units_ordered_new_to_brand14d, 0) >= ? ");
            argsList.add(dto.getUnitsOrderedNewToBrandFTDMin());
        }
        if (dto.getUnitsOrderedNewToBrandFTDMax() != null) {
            filterSql.append(" and ifnull(sum_attributed_units_ordered_new_to_brand14d, 0) <= ? ");
            argsList.add(dto.getUnitsOrderedNewToBrandFTDMax());
        }
        if (dto.getUnitsOrderedRateNewToBrandFTDMin() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum_attributed_units_ordered_new_to_brand14d/sum_attributed_units_ordered7d) * 100, 0), 4), 2) >= ? ");
            argsList.add(dto.getUnitsOrderedRateNewToBrandFTDMin());
        }
        if (dto.getUnitsOrderedRateNewToBrandFTDMax() != null) {
            filterSql.append(" and ROUND(ROUND(ifnull((sum_attributed_units_ordered_new_to_brand14d/sum_attributed_units_ordered7d) * 100, 0), 4), 2) <= ? ");
            argsList.add(dto.getUnitsOrderedRateNewToBrandFTDMax());
        }
        return filterSql.toString();
    }

    private String buildSelectSql(String adType) {
        StringBuilder selectSql;
        if (Constants.SP.equalsIgnoreCase(adType)) {
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales7d)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        } else if (Constants.SB.equalsIgnoreCase(adType)) {
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                    .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                    .append("sum(attributed_sales14d)                  attributed_sales14d,")
                    .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                    .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                    .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                    .append("sum(viewable_impressions)  viewable_impressions,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d)  attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_orders_new_to_brand14d)  attributed_orders_new_to_brand14d ");


        } else if (Constants.SD.equalsIgnoreCase(adType)) {
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                    .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                    .append("sum(attributed_sales14d)                  attributed_sales14d,")
                    .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                    .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                    .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                    .append("sum(view_impressions)  view_impressions,")
                    .append("sum(view_attributed_units_ordered_new_to_brand14d)  view_attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d)  attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_orders_new_to_brand14d)  attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_sales_new_to_brand14d)  view_attributed_sales_new_to_brand14d,")
                    .append("sum(view_attributed_units_ordered14d)  view_attributed_units_ordered14d,")
                    .append("sum(view_attributed_sales14d)  view_attributed_sales14d,")
                    .append("sum(view_attributed_orders_new_to_brand14d)  view_attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_conversions14d)  view_attributed_conversions14d ");
        } else {
            //没有传类型就剔除 1天，30天，必定不用的字段；
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                    .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales14d)                  attributed_sales14d,")
                    .append("sum(attributed_sales14d_same_sku)         attributed_sales14d_same_sku,")
                    .append("sum(attributed_sales7d)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                    .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku,")
                    .append("sum(viewable_impressions)  viewable_impressions,")
                    .append("sum(view_impressions)  view_impressions,")
                    .append("sum(view_attributed_units_ordered_new_to_brand14d)  view_attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d)  attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_orders_new_to_brand14d)  attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_sales_new_to_brand14d)  view_attributed_sales_new_to_brand14d,")
                    .append("sum(view_attributed_units_ordered14d)  view_attributed_units_ordered14d,")
                    .append("sum(view_attributed_sales14d)  view_attributed_sales14d,")
                    .append("sum(view_attributed_orders_new_to_brand14d)  view_attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_conversions14d)  view_attributed_conversions14d ");

        }
        return selectSql.toString();

    }

    private String buildSelectSqlMultiShop(String adType) {
        StringBuilder selectSql;
        if (Constants.SP.equalsIgnoreCase(adType)) {
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost * c.rate)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales7d * c.rate)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku * c.rate)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku ");

        } else if (Constants.SB.equalsIgnoreCase(adType)) {
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost * c.rate)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                    .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                    .append("sum(attributed_sales14d * c.rate)                  attributed_sales14d,")
                    .append("sum(attributed_sales14d_same_sku * c.rate)         attributed_sales14d_same_sku,")
                    .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                    .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                    .append("sum(viewable_impressions)  viewable_impressions,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d * c.rate)  attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_orders_new_to_brand14d)  attributed_orders_new_to_brand14d ");


        } else if (Constants.SD.equalsIgnoreCase(adType)) {
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost * c.rate)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                    .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                    .append("sum(attributed_sales14d * c.rate)                  attributed_sales14d,")
                    .append("sum(attributed_sales14d_same_sku * c.rate)         attributed_sales14d_same_sku,")
                    .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                    .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                    .append("sum(view_impressions)  view_impressions,")
                    .append("sum(view_attributed_units_ordered_new_to_brand14d)  view_attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d * c.rate)  attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_orders_new_to_brand14d)  attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_sales_new_to_brand14d * c.rate)  view_attributed_sales_new_to_brand14d,")
                    .append("sum(view_attributed_units_ordered14d)  view_attributed_units_ordered14d,")
                    .append("sum(view_attributed_sales14d * c.rate)  view_attributed_sales14d,")
                    .append("sum(view_attributed_orders_new_to_brand14d)  view_attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_conversions14d)  view_attributed_conversions14d ");
        } else {
            //没有传类型就剔除 1天，30天，必定不用的字段；
            selectSql = new StringBuilder()
                    .append("sum(clicks)                               clicks,")
                    .append("sum(cost * c.rate)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)            attributed_conversions14d,")
                    .append("sum(attributed_conversions14d_same_sku)   attributed_conversions14d_same_sku,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,")
                    .append("sum(attributed_sales14d * c.rate)                  attributed_sales14d,")
                    .append("sum(attributed_sales14d_same_sku * c.rate)         attributed_sales14d_same_sku,")
                    .append("sum(attributed_sales7d * c.rate)                   attributed_sales7d,")
                    .append("sum(attributed_sales7d_same_sku * c.rate)          attributed_sales7d_same_sku,")
                    .append("sum(attributed_units_ordered14d)          attributed_units_ordered14d,")
                    .append("sum(attributed_units_ordered14d_same_sku) attributed_units_ordered14d_same_sku,")
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku,")
                    .append("sum(viewable_impressions)  viewable_impressions,")
                    .append("sum(view_impressions)  view_impressions,")
                    .append("sum(view_attributed_units_ordered_new_to_brand14d)  view_attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d,")
                    .append("sum(attributed_sales_new_to_brand14d * c.rate)  attributed_sales_new_to_brand14d,")
                    .append("sum(attributed_orders_new_to_brand14d)  attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_sales_new_to_brand14d * c.rate)  view_attributed_sales_new_to_brand14d,")
                    .append("sum(view_attributed_units_ordered14d)  view_attributed_units_ordered14d,")
                    .append("sum(view_attributed_sales14d * c.rate)  view_attributed_sales14d,")
                    .append("sum(view_attributed_orders_new_to_brand14d)  view_attributed_orders_new_to_brand14d,")
                    .append("sum(view_attributed_conversions14d)  view_attributed_conversions14d ");

        }
        return selectSql.toString();

    }

    @Override
    public List<EffectDataBo> getEffectData(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency,
                                            String startDate, String endDate, String modelType, List<ShopAuth> shopAuths, List<String> siteToday, Boolean isSiteToday,
                                            List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = getEffectDataSql(puid, sellerList, marketplaceIdList, currency, startDate, endDate, argsList, false, shopAuths,
                siteToday, isSiteToday, portfolioIds, campaignIds, shopIds, null);
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(EffectDataBo.class));
    }


    @Override
    public List<EffectDataBo> getEffectData(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency,
                                            String startDate, String endDate, String modelType, List<ShopAuth> shopAuths, List<String> siteToday, Boolean isSiteToday,
                                            List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIds, Boolean isYesterday) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = getEffectDataSql(puid, sellerList, marketplaceIdList, currency, startDate, endDate, argsList, false, shopAuths,
                siteToday, isSiteToday, portfolioIds, campaignIds, shopIds, isYesterday);
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(EffectDataBo.class));
    }


    @Override
    public EffectDataBo getEffectAggregateData(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency, String startDate, String endDate, String modelType, List<String> siteToday, Boolean isSiteToday,
                                               List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = getEffectDataSql(puid, sellerList, marketplaceIdList, currency, startDate, endDate, argsList, true, Collections.emptyList(),
                siteToday, isSiteToday, portfolioIds, campaignIds, shopIds, null);
        List<EffectDataBo> list = getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(EffectDataBo.class));
        return CollectionUtils.isNotEmpty(list) && list.size() == 1 ? list.get(0) : new EffectDataBo();
    }


    @Override
    public List<AmazonMarketingStreamData> getHourlyDataTopFiveCampaign(List<CampaignLastDayHourlyReportDto> list,
                                                                        String dateTime, String orderBy, Map<String, Double> rateMap,
                                                                        Integer puid, List<Integer> shopIds) {
        List<String> sellerIds = list.stream().map(CampaignLastDayHourlyReportDto::getSellerId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerIds)) {
            return new ArrayList<>();
        }
        setSmId(list);
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" SELECT seller_id,marketplace_id,campaign_id,any(currency) currency,sum(attributed_conversions7d) attributed_conversions7d,sum(clicks)  clicks,sum(impressions) impressions, CASE any(currency)  ");
        List<Object> argsList1 = new ArrayList<>();
        StringBuilder rateBuild = new StringBuilder(" CASE  any(currency) ");
        rateMap.forEach((k, v) -> {
            selectSql.append(" WHEN '");
            selectSql.append(k);
            selectSql.append("' THEN  sum(cost) * ?  ");
            argsList.add(v);
            rateBuild.append(" WHEN '");
            rateBuild.append(k);
            rateBuild.append("' THEN  sum(attributed_sales7d) * ?  ");
            argsList1.add(v);
        });
        selectSql.append(" ELSE sum(cost) END as cost, ");
        selectSql.append(rateBuild);
        argsList.addAll(argsList1);
        selectSql.append(" ELSE sum(attributed_sales7d) END as attributed_sales7d from  t_amazon_marketing_stream_data  where ");
        selectSql.append(SqlStringUtil.dealInListNotAnd("seller_id", sellerIds, argsList));
        selectSql.append(" AND date >=  ?  ");
        argsList.add(dateTime);
        selectSql.append(SqlStringUtil.dealInList("marketplace_id", list.stream().map(CampaignLastDayHourlyReportDto::getMarketplaceId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id)", list.stream().map(CampaignLastDayHourlyReportDto::getSmId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(" AND utc_date_time >=  ?  ");
        argsList.add(dateTime);
        String checkProductRightSql = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(puid, shopIds, Collections.singletonList(CampaignTypeEnum.sp), argsList);
        if (StringUtils.isNotBlank(checkProductRightSql)) {
            selectSql.append(" AND " + checkProductRightSql);
        }
        // 存在SQL注入
        selectSql.append("  group by seller_id, marketplace_id, campaign_id  order by " + orderBy + " desc limit 5 ");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> getTopFiveCampaignIndicatorDoris(List<CampaignLastDayHourlyReportDto> list, String dateTime) {
        List<String> sellerIds = list.stream().map(CampaignLastDayHourlyReportDto::getSellerId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerIds)) {
            return new ArrayList<>();
        }
        setSmcId(list);
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" SELECT seller_id,marketplace_id,campaign_id,any(currency) currency, sum(cost) cost, sum(attributed_conversions7d)  attributed_conversions7d, sum(attributed_sales7d) attributed_sales7d, sum(clicks)  clicks, sum(impressions) impressions, utc_date_time utc_date_time from t_amazon_marketing_stream_data  where ");
        selectSql.append(SqlStringUtil.dealInListNotAnd("seller_id", sellerIds, argsList));
        selectSql.append(" AND date >=  ?  ");
        argsList.add(getNowDate());
        selectSql.append(SqlStringUtil.dealInList("marketplace_id", list.stream().map(CampaignLastDayHourlyReportDto::getMarketplaceId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(SqlStringUtil.dealInList("campaign_id", list.stream().map(CampaignLastDayHourlyReportDto::getCampaignId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id, campaign_id)", list.stream().map(CampaignLastDayHourlyReportDto::getSmcId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(" AND utc_date_time >=  ?  group by seller_id, marketplace_id, campaign_id, utc_date_time ");
        argsList.add(dateTime);
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsByDateTimeRangeAndCampaignIdDoris(List<CampaignLastDayHourlyReportDto> list, String dateTime, Integer puid, List<Integer> shopIds) {
        List<String> sellerIds = list.stream().map(CampaignLastDayHourlyReportDto::getSellerId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerIds)) {
            return new ArrayList<>();
        }
        setSmcId(list);
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" SELECT seller_id,marketplace_id,campaign_id,any(currency) currency, MAX(last_update_at) last_update_at, sum(clicks) clicks, sum(cost) cost, sum(impressions) impressions,  sum(attributed_conversions7d) attributed_conversions7d, sum(attributed_conversions7d_same_sku) attributed_conversions7d_same_sku,sum(attributed_sales7d) attributed_sales7d, sum(attributed_sales7d_same_sku) attributed_sales7d_same_sku, sum(attributed_units_ordered7d) attributed_units_ordered7d, sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku    from t_amazon_marketing_stream_data  where ");
        selectSql.append(SqlStringUtil.dealInListNotAnd("seller_id", sellerIds, argsList));
        selectSql.append(" AND date >=  ?  ");
        argsList.add(getNowDate());
        selectSql.append(SqlStringUtil.dealInList("marketplace_id", list.stream().map(CampaignLastDayHourlyReportDto::getMarketplaceId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(SqlStringUtil.dealInList("campaign_id", list.stream().map(CampaignLastDayHourlyReportDto::getCampaignId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id, campaign_id)", list.stream().map(CampaignLastDayHourlyReportDto::getSmcId).distinct().collect(Collectors.toList()), argsList));
        String checkProductRightSql = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(puid, shopIds, Collections.singletonList(CampaignTypeEnum.sp), argsList);
        if (StringUtils.isNotBlank(checkProductRightSql)) {
            selectSql.append(checkProductRightSql);
        }
        selectSql.append(" AND utc_date_time >=  ?  group by seller_id, marketplace_id ");
        argsList.add(dateTime);
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> statisticsByDateTimeRangeDoris(List<CampaignLastDayHourlyReportDto> list, String dateTime, Integer puid, List<Integer> shopIds) {
        List<String> sellerIds = list.stream().map(CampaignLastDayHourlyReportDto::getSellerId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sellerIds)) {
            return new ArrayList<>();
        }
        setSmId(list);
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" SELECT seller_id,marketplace_id,any(currency) currency, MAX(last_update_at) last_update_at, sum(clicks) clicks, sum(cost) cost, sum(impressions) impressions,  sum(attributed_conversions7d) attributed_conversions7d, sum(attributed_conversions7d_same_sku) attributed_conversions7d_same_sku,sum(attributed_sales7d) attributed_sales7d, sum(attributed_sales7d_same_sku) attributed_sales7d_same_sku, sum(attributed_units_ordered7d) attributed_units_ordered7d, sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku  from t_amazon_marketing_stream_data  where ");
        selectSql.append(SqlStringUtil.dealInListNotAnd("seller_id", sellerIds, argsList));
        selectSql.append(" AND date >=  ?  ");
        argsList.add(getNowDate());
        selectSql.append(SqlStringUtil.dealInList("marketplace_id", list.stream().map(CampaignLastDayHourlyReportDto::getMarketplaceId).distinct().collect(Collectors.toList()), argsList));
        selectSql.append(SqlStringUtil.dealInList("concat_ws(',',seller_id, marketplace_id)", list.stream().map(CampaignLastDayHourlyReportDto::getSmcId).distinct().collect(Collectors.toList()), argsList));
        String checkProductRightSql = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(puid, shopIds, Collections.singletonList(CampaignTypeEnum.sp), argsList);
        if (StringUtils.isNotBlank(checkProductRightSql)) {
            selectSql.append(" AND " + checkProductRightSql);
        }
        selectSql.append(" AND utc_date_time >=  ?  group by seller_id, marketplace_id ");
        argsList.add(dateTime);
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    private String getNowDate() {
        return LocalDate.now(ZoneId.of("UTC")).minusDays(2).toString();
    }

    private void setSmId(List<CampaignLastDayHourlyReportDto> list) {
        list.forEach(key -> key.setSmId(key.getSellerId() + "," + key.getMarketplaceId()));
    }

    private void setSmcId(List<CampaignLastDayHourlyReportDto> list) {
        list.forEach(key -> key.setSmcId(key.getSellerId() + "," + key.getMarketplaceId() + "," + key.getCampaignId()));
    }

    @Override
    public List<AmazonMarketingStreamData> listMultiShopByHourly(FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO) {
        // todo 注意这里doris的内存占用
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("SELECT time, ");
        selectSql.append(buildSelectSqlMultiShop(feedHourlySelectMultiShopDTO.getType()));
        selectSql.append("  from  t_amazon_marketing_stream_data r join ( select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
        argsList.add(feedHourlySelectMultiShopDTO.getPuid());
        argsList.add(feedHourlySelectMultiShopDTO.getTo());
        selectSql.append(" and c.month >= ").append(DateUtil.dateStringFormat(feedHourlySelectMultiShopDTO.getStart().toString(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        selectSql.append(" and c.month <= ").append(DateUtil.dateStringFormat(feedHourlySelectMultiShopDTO.getEnd().toString(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        selectSql.append(" where 1=1");
        if (CollectionUtils.isNotEmpty(feedHourlySelectMultiShopDTO.getMarketplaceIds())) {
            selectSql.append(SqlStringUtil.dealInList("m.marketplace_id", feedHourlySelectMultiShopDTO.getMarketplaceIds(), argsList));
        }
        selectSql.append(") c");
        selectSql.append(" on c.marketplace_id = r.marketplace_id ");
        selectSql.append(" and c.month = DATE_FORMAT(r.date, '%Y%m') ");
        selectSql.append(" where 1=1 ");
        if (CollectionUtils.isNotEmpty(feedHourlySelectMultiShopDTO.getSellerId())) {
            selectSql.append(SqlStringUtil.dealInList("r.seller_id", feedHourlySelectMultiShopDTO.getSellerId(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectMultiShopDTO.getMarketplaceIds())) {
            selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", feedHourlySelectMultiShopDTO.getMarketplaceIds(), argsList));
        }
        if (StringUtils.isNotBlank(feedHourlySelectMultiShopDTO.getType())) {
            selectSql.append(" and r.type = ? ");
            argsList.add(feedHourlySelectMultiShopDTO.getType());
        }
        if (feedHourlySelectMultiShopDTO.getStart() != null) {
            selectSql.append(" and r.date >= ? ");
            argsList.add(feedHourlySelectMultiShopDTO.getStart());
        }
        if (feedHourlySelectMultiShopDTO.getEnd() != null) {
            selectSql.append(" and r.date <= ? ");
            argsList.add(feedHourlySelectMultiShopDTO.getEnd());
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectMultiShopDTO.getCampaignIds())) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", feedHourlySelectMultiShopDTO.getCampaignIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectMultiShopDTO.getWeekdayList())) {
            selectSql.append(SqlStringUtil.dealInList("r.weekday", feedHourlySelectMultiShopDTO.getWeekdayList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(feedHourlySelectMultiShopDTO.getExcludeCampaignIds())) {
            selectSql.append(SqlStringUtil.dealBitMapDorisNotInList("r.campaign_id", feedHourlySelectMultiShopDTO.getExcludeCampaignIds(), argsList));
        }
        selectSql.append(" group by time ");
        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    @Override
    public List<AmazonMarketingStreamData> listWeekByHourlyMultiShop(FeedHourlySelectDTOMultiShop queryBuilder) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("SELECT weekday,time,")
                .append(buildSelectSqlMultiShop(queryBuilder.getType()));
        selectSql.append("  from  t_amazon_marketing_stream_data r join ( select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
        argsList.add(queryBuilder.getPuid());
        argsList.add(queryBuilder.getTo());
        selectSql.append(" and c.month >= ").append(DateUtil.dateStringFormat(queryBuilder.getStart().toString(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        selectSql.append(" and c.month <= ").append(DateUtil.dateStringFormat(queryBuilder.getEnd().toString(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
        selectSql.append(" where 1=1");
        if (CollectionUtils.isNotEmpty(queryBuilder.getMarketplaceIds())) {
            selectSql.append(SqlStringUtil.dealInList("m.marketplace_id", queryBuilder.getMarketplaceIds(), argsList));
        }
        selectSql.append(") c");
        selectSql.append(" on c.marketplace_id = r.marketplace_id");
        selectSql.append(" and c.month = DATE_FORMAT(r.date, '%Y%m') ");
        StringBuilder whereSql = new StringBuilder(" where 1=1 ");
        if (CollectionUtils.isNotEmpty(queryBuilder.getSellerIds())) {
            whereSql.append(SqlStringUtil.dealInList("r.seller_id", queryBuilder.getSellerIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryBuilder.getMarketplaceIds())) {
            whereSql.append(SqlStringUtil.dealInList("r.marketplace_id", queryBuilder.getMarketplaceIds(), argsList));
        }
        if (StringUtils.isNotBlank(queryBuilder.getType())) {
            whereSql.append(" and r.type = ? ");
            argsList.add(queryBuilder.getType());
        }
        if (queryBuilder.getStart() != null) {
            whereSql.append(" and r.date >= ? ");
            argsList.add(queryBuilder.getStart());
        }
        if (queryBuilder.getEnd() != null) {
            whereSql.append(" and r.date <= ? ");
            argsList.add(queryBuilder.getEnd());
        }
        if (CollectionUtils.isNotEmpty(queryBuilder.getCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", queryBuilder.getCampaignIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(queryBuilder.getAdGroupIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", queryBuilder.getAdGroupIds(), argsList));
        }

        if (CollectionUtils.isNotEmpty(queryBuilder.getWeekdayList())) {
            whereSql.append(SqlStringUtil.dealInList("r.weekday", queryBuilder.getWeekdayList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(queryBuilder.getAdIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_id", queryBuilder.getAdIds(), argsList));
        }
        if (CollectionUtils.isNotEmpty(queryBuilder.getExcludeCampaignIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisNotInList("r.campaign_id", queryBuilder.getExcludeCampaignIds(), argsList));
        }

        selectSql.append(whereSql);
        selectSql.append(" group by weekday, time ");

        return getJdbcTemplate().query(selectSql.toString(), getMapper(), argsList.toArray());
    }

    private StringBuilder getEffectDataSql(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency,
                                           String startDate, String endDate, List<Object> argsList, boolean isAggregate, List<ShopAuth> shopAuths, List<String> siteToday, Boolean isSiteToday,
                                           List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIdList, Boolean isYesterday) {


        StringBuilder sb = new StringBuilder("select ");
        if (!isAggregate) {
            sb.append(" time, ");
        }
        sb.append(" ifnull(sum(cost * c.rate), 0) cost, ifnull(sum(if(type='SP', attributed_sales7d, if(type='SB',attributed_sales14d, view_attributed_sales14d)) * c.rate), 0) totalSales, ")
                .append(" ifnull(sum(clicks), 0) clicks, ifnull(sum(impressions), 0) impressions, ifnull(sum(if(type='SP', attributed_conversions7d, if(type='SB',attributed_conversions14d, view_attributed_conversions14d))), 0) orderNum ");
        sb.append(", ifnull(sum(if(type = 'SP' , attributed_units_ordered7d, if(type = 'SB', attributed_units_ordered14d , view_attributed_units_ordered14d))), 0) saleNum ");
        sb.append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        sb.append(" join (select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m ");
        sb.append(" on c.`from` = m.currency and c.puid= ? and `to` = ? and month >= ? and month <= ? ");
        argsList.add(puid);
        argsList.add(currency);
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMM");
            LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
            if (Boolean.TRUE.equals(isYesterday)) {
                now = now.plusDays(-1);
            }
            argsList.add(now.plusHours(-12).format(formatter));
            argsList.add(now.plusHours(12).format(formatter));
        } else {
            argsList.add(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
            argsList.add(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM));
        }

        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(SqlStringUtil.dealInList("m.marketplace_id", marketplaceIdList, argsList));
        }
        sb.append(") c ");
        sb.append(" on r.marketplace_id = c.marketplace_id and DATE_FORMAT(r.date, '%Y%m') = c.month ");
        sb.append(SqlStringUtil.dealInList("r.seller_id", sellerList, argsList));
        sb.append(SqlStringUtil.dealInList("r.marketplace_id", marketplaceIdList, argsList));

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', r.marketplace_id, r.date)", siteToday, argsList));
            sb.append(" and r.date >= ? and r.date <= ? ");
            LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
            if (Boolean.TRUE.equals(isYesterday)) {
                now = now.plusDays(-1);
            }
            argsList.add(now.plusHours(-12).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusHours(12).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and r.date >= ? and r.date <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        sb.append(SqlStringUtil.dealMultiInList(Arrays.asList("r.seller_id", "r.marketplace_id"),
                shopAuths, argsList, Arrays.asList(ShopAuth::getSellingPartnerId, ShopAuth::getMarketplaceId)));

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIds, argsList));
        }else {
            String productRightCampaignIdsSqlFromGrpc = adProductRightService.getProductRightCampaignIdsSqlFromGrpc(puid, shopIdList, Lists.newArrayList(CampaignTypeEnum.sp, CampaignTypeEnum.sb, CampaignTypeEnum.sd), argsList, "r.campaign_id");
            if (StringUtils.isNotBlank(productRightCampaignIdsSqlFromGrpc)) {
                sb.append(" AND ").append(productRightCampaignIdsSqlFromGrpc);
            }
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" where r.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append(" )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        if (!isAggregate) {
            sb.append(" group by r.time ");
        }
        return sb;
    }

    private String buildProductPerspectiveAllTypeSql(String type) {
        StringBuilder sql = new StringBuilder();
        if (Constants.SP.equals(type)) {
            sql.append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions7d)             attributed_conversions7d,")  //广告订单量
                    .append("sum(attributed_conversions7d_same_sku)    attributed_conversions7d_same_sku,") //本产品广告订单量
                    .append("sum(attributed_sales7d)                   attributed_sales7d,")    //广告销售额
                    .append("sum(attributed_sales7d_same_sku)          attributed_sales7d_same_sku,")   //本产品广告销售额
                    .append("sum(attributed_units_ordered7d)           attributed_units_ordered7d,")    //广告销量
                    .append("sum(attributed_units_ordered7d_same_sku)  attributed_units_ordered7d_same_sku, ")  //本产品广告销量
                    .append("0                                         view_impressions, ")     //可见展示次数
                    .append("0                                         attributed_orders_new_to_brand14d, ")    //品牌新买家订单量
                    .append("0                                         attributed_sales_new_to_brand14d, ")     //品牌新买家销售额
                    .append("0                                         attributed_units_ordered_new_to_brand14d ");     //品牌新买家销量
        } else if (Constants.SB.equals(type)) {
            sql.append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(attributed_conversions14d)       attributed_conversions7d,")  //广告订单量
                    .append("sum(attributed_conversions14d_same_sku)    attributed_conversions7d_same_sku,") //本产品广告订单量
                    .append("sum(attributed_sales14d)                  attributed_sales7d,")    //广告销售额
                    .append("sum(attributed_sales14d_same_sku)         attributed_sales7d_same_sku,")   //本产品广告销售额
                    .append("sum(attributed_units_ordered14d)     attributed_units_ordered7d,")    //广告销量
                    .append("0                                         attributed_units_ordered7d_same_sku, ")  //本产品广告销量
                    .append("sum(viewable_impressions)                      view_impressions, ")    //可见展示次数
                    .append("sum(attributed_orders_new_to_brand14d)         attributed_orders_new_to_brand14d, ")    //品牌新买家订单量
                    .append("sum(attributed_sales_new_to_brand14d)          attributed_sales_new_to_brand14d, ")     //品牌新买家销售额
                    .append("sum(attributed_units_ordered_new_to_brand14d)  attributed_units_ordered_new_to_brand14d ");     //品牌新买家销量
        } else if (Constants.SD.equals(type)) {
            sql.append("sum(clicks)                               clicks,")
                    .append("sum(cost)                                 cost,")
                    .append("sum(impressions)                          impressions,")
                    .append("sum(view_attributed_conversions14d)       attributed_conversions7d,")  //广告订单量
                    .append("sum(attributed_conversions14d_same_sku)  attributed_conversions7d_same_sku,") //本产品广告订单量
                    .append("sum(view_attributed_sales14d)             attributed_sales7d,")    //广告销售额
                    .append("sum(attributed_sales14d_same_sku)         attributed_sales7d_same_sku,")   //本产品广告销售额
                    .append("sum(view_attributed_units_ordered14d)     attributed_units_ordered7d,")    //广告销量
                    .append("0                                         attributed_units_ordered7d_same_sku, ")  //本产品广告销量
                    .append("sum(view_impressions)                          view_impressions, ")    //可见展示次数
                    .append("sum(view_attributed_orders_new_to_brand14d)    attributed_orders_new_to_brand14d, ")    //品牌新买家订单量
                    .append("sum(view_attributed_sales_new_to_brand14d)     attributed_sales_new_to_brand14d, ")     //品牌新买家销售额
                    .append("sum(view_attributed_units_ordered_new_to_brand14d)     attributed_units_ordered_new_to_brand14d ");    //品牌新买家销量
        }
        return sql.toString();
    }

    /**
     * 多类型查feed数据公用sql
     *
     * @return
     */
    private String buildReportSelectSql() {
        StringBuilder selectSql = new StringBuilder("sum(clicks)                               clicks,")
                .append("sum(cost)                                 cost,")
                .append("sum(impressions)                          impressions,")
                .append("sum(if(type = 'SP' , attributed_conversions7d, if(type = 'SB', attributed_conversions14d , view_attributed_conversions14d)))             attributed_conversions7d,")
                .append("sum(if(type = 'SP' , attributed_conversions7d_same_sku, if(type = 'SB', attributed_conversions14d_same_sku , attributed_conversions14d_same_sku)))    attributed_conversions7d_same_sku,")
                .append("sum(if(type = 'SP' , attributed_sales7d, if(type = 'SB', attributed_sales14d , view_attributed_sales14d)))                   attributed_sales7d,")
                .append("sum(if(type = 'SP' , attributed_sales7d_same_sku, if(type = 'SB', attributed_sales14d_same_sku , attributed_sales14d_same_sku)))          attributed_sales7d_same_sku,")
                .append("sum(if(type = 'SP' , attributed_units_ordered7d, if(type = 'SB', attributed_units_ordered14d , view_attributed_units_ordered14d)))           attributed_units_ordered7d,")
                .append("sum(if(type = 'SP' , attributed_units_ordered7d_same_sku, 0))  attributed_units_ordered7d_same_sku, ")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', viewable_impressions , view_impressions)))           viewable_impressions,")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', attributed_orders_new_to_brand14d , view_attributed_orders_new_to_brand14d)))           attributed_orders_new_to_brand14d,")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', attributed_sales_new_to_brand14d , view_attributed_sales_new_to_brand14d)))           attributed_sales_new_to_brand14d,")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', attributed_units_ordered_new_to_brand14d , view_attributed_units_ordered_new_to_brand14d)))           attributed_units_ordered_new_to_brand14d ");
        return selectSql.toString();
    }

    /**
     * 多类型查feed数据公用sql，有重命名，可用于多选广告类型后需要高级筛选过滤
     *
     * @return
     */
    private String buildReportSelectSumSql() {
        StringBuilder selectSql = new StringBuilder(" any(type) adType, ")
                .append("sum(clicks)                               sum_clicks,")
                .append("sum(cost)                                 sum_cost,")
                .append("sum(impressions)                          sum_impressions,")
                .append("sum(if(type = 'SP' , attributed_conversions7d, if(type = 'SB', attributed_conversions14d , view_attributed_conversions14d)))             sum_attributed_conversions7d,")
                .append("sum(if(type = 'SP' , attributed_conversions7d_same_sku, if(type = 'SB', attributed_conversions14d_same_sku , attributed_conversions14d_same_sku)))    sum_attributed_conversions7d_same_sku,")
                .append("sum(if(type = 'SP' , attributed_sales7d, if(type = 'SB', attributed_sales14d , view_attributed_sales14d)))                   sum_attributed_sales7d,")
                .append("sum(if(type = 'SP' , attributed_sales7d_same_sku, if(type = 'SB', attributed_sales14d_same_sku , attributed_sales14d_same_sku)))          sum_attributed_sales7d_same_sku,")
                .append("sum(if(type = 'SP' , attributed_units_ordered7d, if(type = 'SB', attributed_units_ordered14d , view_attributed_units_ordered14d)))           sum_attributed_units_ordered7d,")
                .append("sum(if(type = 'SP' , attributed_units_ordered7d_same_sku, 0))  sum_attributed_units_ordered7d_same_sku, ")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', viewable_impressions , view_impressions)))           sum_view_impressions,")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', attributed_orders_new_to_brand14d , view_attributed_orders_new_to_brand14d)))           sum_attributed_orders_new_to_brand14d,")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', attributed_sales_new_to_brand14d , view_attributed_sales_new_to_brand14d)))           sum_attributed_sales_new_to_brand14d,")
                .append("sum(if(type = 'SP' , 0, if(type = 'SB', attributed_units_ordered_new_to_brand14d , view_attributed_units_ordered_new_to_brand14d)))           sum_attributed_units_ordered_new_to_brand14d ");
        return selectSql.toString();
    }
}
