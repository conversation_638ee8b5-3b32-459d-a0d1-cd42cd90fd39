package com.meiyunji.sponsored.service.cpc.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetDataResponse;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IWxCpcQueryTargetingReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@Service
public class WxCpcQueryTargetingReportServiceImpl extends ReportService<CpcQueryTargetingReport> implements IWxCpcQueryTargetingReportService {

    @Autowired
    private IWxCpcQueryTargetingReportDao wxCpcQueryTargetingReportDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;


    @Override
    public AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        //默认关闭高级选项
        dto.setUseAdvanced(false);
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);

        boolean isNull = false;

        if (StringUtils.isNotBlank(dto.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, dto.getShopId(), dto.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        //分页数据
        if (!isNull) {
            page = wxCpcQueryTargetingReportDao.pageKeywordAndTargetManageList(puid, dto, page);

            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / page.getPageSize();
                page.setTotalPage(totalPage);
                page.setTotalSize(Constants.TOTALSIZELIMIT);
            }
        }

        getPageReportVo(puid, dto, page);

        //处理分页
        AllQueryTargetDataResponse.AdQueryTargetingHomeVo.Page.Builder pageBuilder = AllQueryTargetDataResponse.AdQueryTargetingHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));
        List<ReportVo> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(this::convertToProtobufBean).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        ReportRpcVo reportRpcVo = ReportRpcVo.newBuilder().build();

        return AllQueryTargetDataResponse.AdQueryTargetingHomeVo.newBuilder()
                .setSum(reportRpcVo)
                .setPage(pageBuilder.build()).build();
    }

    @Override
    public AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto) {
        //默认关闭高级选项
        dto.setUseAdvanced(false);
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        boolean isNull = false;

        if (StringUtils.isNotBlank(dto.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, dto.getShopId(), dto.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        LocalDate startDate = LocalDate.parse(dto.getStart(),DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(dto.getEnd(), DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        String compareStartDate = startDate.minus(between).minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        String compareEndDate = startDate.minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));

        CpcQueryWordDto compareDto = new CpcQueryWordDto();
        BeanUtils.copyProperties(dto, compareDto);
        compareDto.setStart(compareStartDate);
        compareDto.setEnd(compareEndDate);

        //按条件查询所有数据
        List<AdHomePerformancedto> reportList;
        List<AdHomePerformancedto> compareReportList;
        if (isNull) {
            reportList = new ArrayList<>();
            compareReportList = new ArrayList<>();
        } else {

            reportList = wxCpcQueryTargetingReportDao.getKeywordAndTargetListAllTargetingReportByDate(puid, dto);
            compareReportList = wxCpcQueryTargetingReportDao.getKeywordAndTargetListAllTargetingReportByDate(puid, compareDto);

        }

        List<String> targetIdList = reportList.stream().map(AdHomePerformancedto::getTargetId).collect(Collectors.toList());

        //每日汇总数据
        List<AdHomePerformancedto> reportDayList = wxCpcQueryTargetingReportDao.getReportByTargetIdList(puid, dto.getShopId(), dto.getStart(), dto.getEnd(),targetIdList, dto);

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryTargetAggregateDataVo(reportList,shopSalesByData);
        AdHomeAggregateDataRpcVo compareAggregateDataVo = getQueryTargetAggregateDataVo(compareReportList,shopSalesByData);

        //处理环比数据
        AdHomeAggregateDataRpcVo adHomeAggregateDataRpcVo = getTargetingAggregateDataChainVo(aggregateDataVo, compareAggregateDataVo);

        //处理chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList,shopSalesByData);

        return AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo.newBuilder()
                .addAllDay(dayPerformanceVos)
                .setAggregateDataVo(adHomeAggregateDataRpcVo)
                .build();
    }



    private AdHomeAggregateDataRpcVo getQueryTargetAggregateDataVo(List<AdHomePerformancedto> rows, BigDecimal shopSales) {
        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (CollectionUtils.isEmpty(rows)) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setRoas("0")
                    .setAcots("0")
                    .setAsots("0")
                    .setAdCost("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .setCpa("0")
                    .setOrderNum(Int32Value.of(0))
                    .build();
        }
        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 4, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 4, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 4, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal cpa = sumAdcost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(sumAdOrderNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(sumAdcost, BigDecimal.valueOf(sumAdOrderNum));
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();

        return AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toString())
                .setAcots(acots.stripTrailingZeros().toString())
                .setAsots(asots.stripTrailingZeros().toString())
                .setRoas(roas.stripTrailingZeros().toString())
                .setAdCost(sumAdcost.stripTrailingZeros().toString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toString())
                .setCtr(sumCtr.stripTrailingZeros().toString())
                .setAdSale(sumAdSale.stripTrailingZeros().toString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setCpa(cpa.stripTrailingZeros().toString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .buildPartial();
    }


    private AdHomeAggregateDataRpcVo getTargetingAggregateDataChainVo(AdHomeAggregateDataRpcVo aggregateDataVo,AdHomeAggregateDataRpcVo compareAggregateDataVo){
        AdHomeAggregateDataRpcVo.Builder builder = aggregateDataVo.toBuilder();
        if (compareAggregateDataVo == null) {
            return builder
                    .setAcosCompare("0")
                    .setClicksCompare(0)
                    .setCtrCompare("0")
                    .setCvrCompare("0")
                    .setImpressionsCompare(0)
                    .setAdCostCompare("0")
                    .setAdSaleCompare("0")
                    .setAdOrderNumCompare(0)
                    .setAdCostPerClickCompare("0")
                    .setAcosChain("0")
                    .setClicksChain("0")
                    .setCtrChain("0")
                    .setCvrChain("0")
                    .setImpressionsChain("0")
                    .setAdCostChain("0")
                    .setAdSaleChain("0")
                    .setAdOrderNumChain("0")
                    .setAdCostPerClickChain("0")
                    .build();
        }

        return builder
                .setAcosCompare(compareAggregateDataVo.getAcos())
                .setClicksCompare(compareAggregateDataVo.getClicks().getValue())
                .setCtrCompare(compareAggregateDataVo.getCtr())
                .setCvrCompare(compareAggregateDataVo.getCvr())
                .setImpressionsCompare(compareAggregateDataVo.getImpressions().getValue())
                .setAdCostCompare(compareAggregateDataVo.getAdCost())
                .setAdSaleCompare(compareAggregateDataVo.getAdSale())
                .setAdOrderNumCompare(compareAggregateDataVo.getAdOrderNum().getValue())
                .setAdCostPerClickCompare(compareAggregateDataVo.getAdCostPerClick())
                .setAcosChain(calculationChain(new BigDecimal(aggregateDataVo.getAcos()),new BigDecimal(compareAggregateDataVo.getAcos())))
                .setClicksChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getClicks().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getClicks().getValue())))
                .setCtrChain(calculationChain(new BigDecimal(aggregateDataVo.getCtr()),new BigDecimal(compareAggregateDataVo.getCtr())))
                .setCvrChain(calculationChain(new BigDecimal(aggregateDataVo.getCvr()),new BigDecimal(compareAggregateDataVo.getCvr())))
                .setImpressionsChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getImpressions().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getImpressions().getValue())))
                .setAdCostChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCost()),new BigDecimal(compareAggregateDataVo.getAdCost())))
                .setAdSaleChain(calculationChain(new BigDecimal(aggregateDataVo.getAdSale()),new BigDecimal(compareAggregateDataVo.getAdSale())))
                .setAdOrderNumChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getAdOrderNum().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getAdOrderNum().getValue())))
                .setAdCostPerClickChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCostPerClick()),new BigDecimal(compareAggregateDataVo.getAdCostPerClick())))
                .build();
    }

    private String calculationChain(BigDecimal data,BigDecimal dataCompare) {
        if (dataCompare.compareTo(BigDecimal.ZERO) == 0 && data.compareTo(BigDecimal.ZERO) == 1) {
            return "100.0000";
        }
        return MathUtil.divideByZero(MathUtil.subtract(data, dataCompare).multiply(BigDecimal.valueOf(100)), dataCompare).toString();
    }


    private ReportRpcVo convertToProtobufBean(ReportVo item) {
        ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
        if (StringUtils.isNotBlank(item.getCountDate())) {
            voBuilder.setCountDate(item.getCountDate());
        }

        if (item.getShopId() != null) {
            voBuilder.setShopId(Int32Value.of(item.getShopId()));
        }
        voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
        voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
        voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
        voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));


        if (StringUtils.isNotBlank(item.getCampaignId())) {
            voBuilder.setCampaignId(item.getCampaignId());
        }
        if (StringUtils.isNotBlank(item.getAdGroupId())) {
            voBuilder.setAdGroupId(item.getAdGroupId());
        }
        if (StringUtils.isNotBlank(item.getAdGroupType())) {
            voBuilder.setAdGroupType(item.getAdGroupType());
        }
        if (StringUtils.isNotBlank(item.getAdGroupName())) {
            voBuilder.setAdGroupName(item.getAdGroupName());
        }
        if (StringUtils.isNotBlank(item.getCampaignName())) {
            voBuilder.setCampaignName(item.getCampaignName());
        }
        if (StringUtils.isNotBlank(item.getKeywordText())) {
            voBuilder.setKeywordText(item.getKeywordText());
        }
        if (StringUtils.isNotBlank(item.getMatchType())) {
            voBuilder.setMatchType(item.getMatchType());
        }
        if (StringUtils.isNotBlank(item.getSku())) {
            voBuilder.setSku(item.getSku());
        }
        if (StringUtils.isNotBlank(item.getAsin())) {
            voBuilder.setAsin(item.getAsin());
        }

        if (StringUtils.isNotBlank(item.getQuery())) {
            voBuilder.setQuery(item.getQuery());
        }

        if (StringUtils.isNotBlank(item.getParentAsin())) {
            voBuilder.setParentAsin(item.getParentAsin());
        }
        if (StringUtils.isNotBlank(item.getTitle())) {
            voBuilder.setTitle(item.getTitle());
        }
        if (StringUtils.isNotBlank(item.getMainImage())) {
            if(item.getMainImage().endsWith("S60_.jpg")){
                item.setMainImage(item.getMainImage().replace("S60_.jpg","S600_.jpg"));
            }
            voBuilder.setMainImage(item.getMainImage());
        }
        if (StringUtils.isNotBlank(item.getNegaType())) {
            voBuilder.setNegaType(item.getNegaType());
        }
        if (StringUtils.isNotBlank(item.getTargetingType())) {
            voBuilder.setTargetingType(item.getTargetingType());
        }
        if (StringUtils.isNotBlank(item.getTargetId())) {
            voBuilder.setTargetId(item.getTargetId());
            voBuilder.setIsTargetType(BoolValue.of(true));
        }

        if (StringUtils.isNotBlank(item.getAdId())) {
            voBuilder.setAdId(item.getAdId());
        }
        if (StringUtils.isNotBlank(item.getTargetingText())) {
            voBuilder.setTargetingText(item.getTargetingText());
        }
        if (StringUtils.isNotBlank(item.getSpCampaignType())) {
            voBuilder.setSpCampaignType(item.getSpCampaignType());
        }
        if (StringUtils.isNotBlank(item.getSpGroupType())) {
            voBuilder.setSpGroupType(item.getSpGroupType());
        }

        if (StringUtils.isNotBlank(item.getSpTargetType())) {
            voBuilder.setSpTargetType(item.getSpTargetType());
        }

        if (StringUtils.isNotBlank(item.getTargetingExpression())) {
            voBuilder.setTargetingExpression(item.getTargetingExpression());
        }
        if (item.getIsTargetAsin() != null) {
            voBuilder.setIsTargetAsin(BoolValue.of(item.getIsTargetAsin()));
        }
        if (item.getIsNeTargetAsin() != null) {
            voBuilder.setIsNeTargetAsin(BoolValue.of(item.getIsNeTargetAsin()));
        }
        if (item.getDefaultBid() != null) {
            voBuilder.setDefaultBid(item.getDefaultBid());
        }
        if (item.getPortfolioId() != null) {
            voBuilder.setPortfolioId(item.getPortfolioId());
        }
        if (item.getPortfolioName() != null) {
            voBuilder.setPortfolioName(item.getPortfolioName());
        }
        if (item.getIsHidden() != null) {
            voBuilder.setIsHidden(item.getIsHidden());
        }
        //广告类型
        if (StringUtils.isNotBlank(item.getType())) {
            voBuilder.setType(item.getType());
        }
        // 标记搜索词是否可添加商品投放
        if (item.getIsAdd() != null) {
            voBuilder.setIsAdd(item.getIsAdd());
        }
        // 标记搜索词是否可添加否定商品投放
        if (item.getIsNegativeAdd() != null) {
            voBuilder.setIsNegativeAdd(item.getIsNegativeAdd());
        }
        if (StringUtils.isNotBlank(item.getKeywordId())) {
            voBuilder.setKeywordId(item.getKeywordId());
            voBuilder.setIsTargetType(BoolValue.of(false));
        }
        return voBuilder.build();
    }


    private void getPageReportVo(Integer puid, CpcQueryWordDto dto, Page page) {
        List<CpcQueryTargetingReport> poList = page.getRows();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getAdGroupId).distinct().collect(Collectors.toList());
        List<String> asins =  poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getQuery).distinct().collect(Collectors.toList());

        //批量查询广告活动和广告组
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
        }


        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item->item,(a,b)->a));
        }

        Map<String, AmazonAdGroup> groupMap = null;

        List<AmazonAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item->item,(a,b)->a));
        }

        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;


            List<AmazonAdTargeting> targetingList = null;
            if (CollectionUtils.isNotEmpty(groupIds)) {
               targetingList = amazonAdTargetDaoRoutingService.listByGroupIdList(puid, dto.getShopId(), groupIds);
            }


            Map<String, List<AmazonAdTargeting>> groupTargetMapList =  new HashMap<>();
            if (CollectionUtils.isNotEmpty(targetingList)) {
                for (AmazonAdTargeting target : targetingList) {
                    if (StringUtils.isBlank(target.getAdGroupId())) {
                        continue;
                    }
                    if (groupTargetMapList.containsKey(target.getAdGroupId())) {
                        groupTargetMapList.get(target.getAdGroupId()).add(target);
                    } else {
                        groupTargetMapList.put(target.getAdGroupId(),  Lists.newArrayList(target));
                    }
                }
            }


            BigDecimal sumRange =  dto.getShopSales();  //店铺销售额

            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息

            Map<String,List<AmazonAdProductMetadata>> metadataMap = null;
            if (CollectionUtils.isNotEmpty(asins)) {
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(puid,dto.getShopId(),null,asins);
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    Map<String,List<AmazonAdProductMetadata>> map = new HashMap<>();
                    map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getAsin)));
                    metadataMap = map;
                }
            }
            Map<String,List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;
            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = getVo(e, sumRange);
                vo.setQuery(e.getQuery() != null ? e.getQuery().toUpperCase() : "");
                vo.setTargetingExpression(e.getTargetingExpression());
                vo.setSalesConversionRate(e.getSalesConversionRateSaleNum());
                vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                vo.setCampaignId(e.getCampaignId());
                if (StringUtils.isNotBlank(e.getKeywordId())) {
                    vo.setKeywordId(e.getKeywordId());
                }
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(e.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    if (Constants.MANUAL.equals(campaign.getTargetingType())) {
                        vo.setIsAdd(Constants.ARCHIVED.equals(campaign.getState()) ? 1 : 0);
                    } else {
                        vo.setIsAdd(1);
                    }
                    vo.setIsNegativeAdd(Constants.ARCHIVED.equals(campaign.getState()) ? 1 : 0);

                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                            if (amazonAdPortfolio != null) {
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                vo.setPortfolioName("广告组合待同步");
                            }
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                    vo.setIsAdd(1);
                    vo.setIsNegativeAdd(1);
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    AmazonAdGroup adGroup = finalGroupMap.get(e.getAdGroupId());
                    vo.setAdGroupName(adGroup.getName());
                    vo.setAdGroupType(adGroup.getAdGroupType());
                    vo.setDefaultBid(adGroup.getDefaultBid());
                    if (vo.getIsAdd() != 1 && Constants.GROUP_TYPE_TARGETING.equals(adGroup.getAdGroupType())) {
                        vo.setIsAdd(Constants.ARCHIVED.equals(adGroup.getState()) ? 1 : 0);
                    } else {
                        vo.setIsAdd(1);
                    }
                    if (vo.getIsNegativeAdd() != 1 && (Constants.GROUP_TYPE_TARGETING.equals(adGroup.getAdGroupType())) || Constants.AUTO.equals(adGroup.getAdGroupType())) {
                        vo.setIsNegativeAdd(Constants.ARCHIVED.equals(adGroup.getState()) ? 1 : 0);
                    } else {
                        vo.setIsNegativeAdd(1);
                    }
                } else {
                    vo.setAdGroupName(e.getAdGroupName());
                    vo.setIsAdd(1);
                    vo.setIsNegativeAdd(1);
                }
                vo.setTargetId(e.getTargetId());
                String mainImage = e.getMainImage();
                if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(vo.getQuery())) {
                    AmazonAdProductMetadata metadata = finalMetadataMap.get(vo.getQuery()).get(0);
                    if (metadata != null) {
                        mainImage = metadata.getImageUrl();
                    }
                }
                if (StringUtils.isBlank(mainImage) && ("substitutes".equals(vo.getTargetingExpression()) || "complements".equals(vo.getTargetingExpression()) || vo.getTargetingExpression().contains("asin"))) {
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                } else if (StringUtils.isBlank(mainImage) && StringUtils.isNotBlank(vo.getQuery()) && Pattern.compile("^[bB]+[A-Za-z0-9]{9}+$").matcher(vo.getQuery()).matches()) { //搜索词符合 b 开头，后面跟9位字母和数字的组合
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                }
                if(mainImage != null && mainImage.endsWith("S60_.jpg")){
                    mainImage = mainImage.replace("S60_.jpg","S600_.jpg");
                }
                vo.setMainImage(mainImage);

                if (groupTargetMapList.size() > 0 && groupTargetMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<AmazonAdTargeting> amazonAdTargetings = groupTargetMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                        for (AmazonAdTargeting targeting : amazonAdTargetings) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(targeting.getTargetingValue())) {
                                if (e.getQuery().trim().equalsIgnoreCase(targeting.getTargetingValue().trim())) {
                                    if ("asin".equalsIgnoreCase(targeting.getType())) {
                                        vo.setIsTargetAsin(true);
                                    }
                                    if ("negativeAsin".equalsIgnoreCase(targeting.getType())) {
                                        vo.setIsNeTargetAsin(true);
                                    }
                                }
                            }
                        }
                    }
                }
                //广告类型
                vo.setType(Constants.SP);
                list.add(vo);
            });
            page.setRows(list);
        }
    }


}
