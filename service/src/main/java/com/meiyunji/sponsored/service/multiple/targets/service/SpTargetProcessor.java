package com.meiyunji.sponsored.service.multiple.targets.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdTargetingExtendDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.multiple.targets.dto.*;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetSpOrderByEnum;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * sp关键词投放子类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class SpTargetProcessor extends AbstractTargetProcessor{

    @Resource
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    @Resource
    private IOdsAmazonAdTargetingDao odsAmazonAdTargetingDao;

    @Resource
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Resource
    private IAmazonAdTargetingExtendDao amazonAdTargetingExtendDao;

    @Override
    public void abstractCheckParam(TargetReqDto req) {

    }

    @Override
    public void abstractSetParam(TargetReqDto req) {

    }

    @Override
    public Boolean abstractFilterTargetIds(TargetReqDto req) {

        return false;
    }

    @Override
    public void abstractBuildWhereSql(TargetReqDto req, StringBuilder whereSql, List<Object> argsList) {
        whereSql.append(" and t.type != 'negativeAsin' ");
        // sp商品、自动投放刷选
        if (StringUtils.isNotBlank(req.getTargetType())) {
            if ("auto".equalsIgnoreCase(req.getTargetType())) {
                whereSql.append(" and t.type = 'auto' ");
            } else {
                whereSql.append(" and t.type in ('asin','category') ");
            }
        }
        // sp 定位类型筛选
        if (StringUtils.isNotBlank(req.getProductTargetType())) {
            if ("asin".equalsIgnoreCase(req.getProductTargetType())) {
                whereSql.append(" and t.type='asin' ");
            } else if ("category".equalsIgnoreCase(req.getProductTargetType())) {
                whereSql.append(" and t.type='category'  ");
            } else {
                whereSql.append(" and t.targeting_value = ? and t.type = 'auto' ");
                argsList.add(req.getProductTargetType());
            }
        }
        //
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(req.getSearchField())) {
                whereSql.append(" and lower(t.targeting_value) = ? and t.type = 'asin' ");
                argsList.add(req.getSearchValue().toLowerCase());
            } else if ("category".equalsIgnoreCase(req.getSearchField())) {
                whereSql.append(" and lower(t.targeting_value) like ? and t.type = 'category' ");
                argsList.add("%" + req.getSearchValue().toLowerCase() + "%");
            }
        }
    }

    @Override
    public void abstractPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto) {
        if (!export) {
            // 24小时竞价日志
            getOperationLogMap(req, dto);
            // 获取asin信息填充asin、图片信息
            dto.setAsinMap(getAsinMap(req, CollectionUtil.newArrayList(dto.getTargetMap().values())));
            //扩展信息-预估曝光量
            List<TargetExtendInfo> targetExtendInfoList = getTargetExtendInfoList(req);
            dto.setTargetExtendInfoMap(StreamUtil.toMap(targetExtendInfoList, TargetExtendInfo::getTargetId));
        }
    }

    @Override
    public void abstractBuildParam(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        if (StringUtils.isNotBlank(row.getServingStatus())) {
            AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(row.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
            if (byCode != null) {
                row.setServingStatusDec(byCode.getDescription());
                row.setServingStatusName(byCode.getName());
            } else {
                row.setServingStatusDec(row.getServingStatus());
                row.setServingStatusName(row.getServingStatus());
            }
        }
    }

    @Override
    public List<TargetInfo> abstractMysqlTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<AmazonAdTargeting> targetingList = amazonAdTargetDaoRoutingService.getListTargetByTargetIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (AmazonAdTargeting targeting : targetingList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(targeting, TargetInfo.class);
                    targetInfo.setTargetText(targeting.getTargetingValue());
                    targetInfo.setProductTargetType(targeting.getType());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    private List<TargetExtendInfo> getTargetExtendInfoList(TargetReqDto req) {
        List<TargetExtendInfo> targetExtendInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<TargetExtendInfo> keywordExtendList = amazonAdTargetingExtendDao.selectByShopIdAndKeywordIdList(req.getPuid(), req.getShopIdList(), targetIds);
                if (CollectionUtils.isNotEmpty(keywordExtendList)) {
                    targetExtendInfoList.addAll(keywordExtendList);
                }
            }
        }
        return targetExtendInfoList;
    }

    @Override
    public List<TargetInfo> abstractDorisTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 9000);
            for (List<String> targetIds : targetIdsList) {
                List<OdsAmazonAdTargeting> targetingList = odsAmazonAdTargetingDao.getByTargetingIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (OdsAmazonAdTargeting targeting : targetingList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(targeting, TargetInfo.class);
                    targetInfo.setTargetText(targeting.getTargetingValue());
                    targetInfo.setProductTargetType(targeting.getType());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<GroupInfo> abstractGroupInfoList(TargetReqDto req, List<String> adGroupIdList) {
        List<GroupInfo> groupInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(adGroupIdList)){
            // 分批获取
            List<List<String>> adGroupIdsList = Lists.partition(adGroupIdList, 10000);
            for (List<String> adGroupIds : adGroupIdsList) {
                List<AmazonAdGroup> adGroupInfoList = amazonAdGroupDao.getListByShopIdsAndGroupIds(req.getPuid(), req.getShopIdList(), adGroupIds);
                for (AmazonAdGroup adGroup : adGroupInfoList) {
                    GroupInfo groupInfo = BeanUtil.copyProperties(adGroup, GroupInfo.class);
                    groupInfoList.add(groupInfo);
                }
            }
        }
        return groupInfoList;
    }

    @Override
    public List<String> excludeFiledList(TargetReqDto req) {
        ArrayList<String> excludeFiledList = Lists.newArrayList("viewImpressions", "vcpm", "ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "ordersNewToBrandPercentageFTD",
                "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "video5SecondViews", "video5SecondViewRate", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews",
                "videoCompleteViews", "videoUnmutes", "viewabilityRate", "viewClickThroughRate", "brandedSearches", "detailPageViews",
                "keywordText", "matchType", "keywordTextCn", "searchFrequencyRank", "weekRatio");
        if("auto".equals(req.getTargetType())){
            excludeFiledList.add("selectType");
        }
       return excludeFiledList;
    }

}
