package com.meiyunji.sponsored.service.budgetUsage.impl;

import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.PredicateEnum;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.types.enumeration.CurrencyCode;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.budgetUsage.IAmazonAdBudgetAnalysisService;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisBudgetChartDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisBudgetChartQueryDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisPageDto;
import com.meiyunji.sponsored.service.budgetUsage.dto.BudgetAnalysisQueryRequest;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogEntityTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignDorisAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdOperationLogService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdBudgetUsageDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdBudgetUsageDayDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdBudgetUsage;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdBudgetUsageDay;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.missBudget.entity.AmazonAdMissBudget;
import com.meiyunji.sponsored.service.missBudget.service.AmazonAdMissBudgetService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AmazonAdBudgetAnalysisServiceImpl implements IAmazonAdBudgetAnalysisService {


    private final IOdsAmazonAdBudgetUsageDayDao odsAmazonAdBudgetUsageDayDao;
    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final IAmazonAdPortfolioDao amazonAdPortfolioDao;
    private final IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;
    private final AmazonAdMissBudgetService amazonAdMissBudgetService;
    private final IOdsAmazonAdBudgetUsageDao odsAmazonAdBudgetUsageDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final IAmazonAdOperationLogService amazonAdOperationLogService;

    private final StringRedisService stringRedisService;

    @Override
    public Page<BudgetAnalysisPageDto> listPage(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest) {
        dealBudgetAnalysisQueryRequest(budgetAnalysisQueryRequest);
        // 处理高级筛选参数
        Page<BudgetAnalysisPageDto> voPage = new Page<>();
        voPage.setPageNo(budgetAnalysisQueryRequest.getPageNo());
        voPage.setPageSize(budgetAnalysisQueryRequest.getPageSize());
        //过滤掉未授权的店铺
        List<Integer> shopIds = shopAuthDao.getAuthShopByShopIdList(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList()).stream().map(ShopAuth::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIds)) {
            voPage.setRows(new ArrayList<>());
            return voPage;
        }
        budgetAnalysisQueryRequest.setShopIdList(shopIds);
        // 处理广告组合和asin筛选
        List<String> campaignIds = getQueryIdsByPageFilter(budgetAnalysisQueryRequest);
        if (campaignIds != null && campaignIds.size() == 0) {
            voPage.setRows(new ArrayList<>());
            return voPage;
        }
        budgetAnalysisQueryRequest.setCampaignIdList(campaignIds);
        long time = System.currentTimeMillis();
        if (budgetAnalysisQueryRequest.getOnlyCount() != null && budgetAnalysisQueryRequest.getOnlyCount()) {
            voPage.setTotalSize(odsAmazonAdBudgetUsageDayDao.onlyCount(budgetAnalysisQueryRequest));
            voPage.setTotalPage(0);
            voPage.setRows(new ArrayList<>());
            return voPage;
        }
        Page<OdsAmazonAdBudgetUsageDay> page = odsAmazonAdBudgetUsageDayDao.listPage(budgetAnalysisQueryRequest);
        log.info("查询列表耗时 = {}", System.currentTimeMillis() - time);
        dealPageResult(page, voPage, budgetAnalysisQueryRequest, false);
        return voPage;
    }

    @Override
    public Page<BudgetAnalysisPageDto> listExport(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest) {
        dealBudgetAnalysisQueryRequest(budgetAnalysisQueryRequest);
        // 处理高级筛选参数
        Page<BudgetAnalysisPageDto> voPage = new Page<>();
        budgetAnalysisQueryRequest.setPageNo(1);
        budgetAnalysisQueryRequest.setPageSize(60000);
        voPage.setPageNo(budgetAnalysisQueryRequest.getPageNo());
        voPage.setPageSize(budgetAnalysisQueryRequest.getPageSize());
        //过滤掉未授权的店铺
        List<Integer> shopIds = shopAuthDao.getAuthShopByShopIdList(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList()).stream().map(ShopAuth::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIds)) {
            voPage.setRows(new ArrayList<>());
            return voPage;
        }
        budgetAnalysisQueryRequest.setShopIdList(shopIds);
        // 处理广告组合和asin筛选
        List<String> campaignIds = getQueryIdsByPageFilter(budgetAnalysisQueryRequest);
        if (campaignIds != null && campaignIds.size() == 0) {
            voPage.setRows(new ArrayList<>());
            return voPage;
        }
        budgetAnalysisQueryRequest.setCampaignIdList(campaignIds);
        Page<OdsAmazonAdBudgetUsageDay> page = odsAmazonAdBudgetUsageDayDao.listPage(budgetAnalysisQueryRequest);
        dealPageResult(page, voPage, budgetAnalysisQueryRequest, true);
        return voPage;
    }

    @Override
    public BudgetAnalysisBudgetChartDto budgetAnalysisTotalBudget(BudgetAnalysisBudgetChartQueryDto budgetAnalysisBudgetChartQueryDto) {
        Set<String> currency = new HashSet<>();
        for (String marketplaceId : budgetAnalysisBudgetChartQueryDto.getMarketplaceIdList()) {
//            Marketplace marketplace = Marketplace.fromId(marketplaceId);
//            currency.add(marketplace.getCurrencyCode().name());
            AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
            currency.add(amznEndpoint.getCurrencyCode().name());
        }
        List<AdHomePerformancedto> list;
        // 一种币种
        boolean isCurrency = currency.size() == 1;
        if (isCurrency) {
            list = amazonAdCampaignAllDorisDao.totalBudgetByType(budgetAnalysisBudgetChartQueryDto.getPuid(), budgetAnalysisBudgetChartQueryDto.getShopIdList(), budgetAnalysisBudgetChartQueryDto.getStatus());
        } else {
            list = amazonAdCampaignAllDorisDao.totalBudgetByTypeAndRate(budgetAnalysisBudgetChartQueryDto.getPuid(), budgetAnalysisBudgetChartQueryDto.getShopIdList(), budgetAnalysisBudgetChartQueryDto.getStatus(), LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMM")));
        }
        Map<String, BigDecimal> typeMap = list.stream().collect(Collectors.toMap(AdHomePerformancedto::getType, AdHomePerformancedto::getAdCost));
        BudgetAnalysisBudgetChartDto budgetAnalysisBudgetChartDto = new BudgetAnalysisBudgetChartDto();
        budgetAnalysisBudgetChartDto.setCurrencyCode(isCurrency ? currency.stream().findFirst().orElse(CurrencyCode.USD.name()) : CurrencyCode.USD.name());
        List<String> types = Lists.newArrayList(Constants.SP, Constants.SB, Constants.SD);
        BigDecimal totalAmount = BigDecimal.ZERO;
        List<BudgetAnalysisBudgetChartDto.BudgetAnalysisBudgetChartItemDto> budgetAnalysisBudgetChartItemDtos = new ArrayList<>(3);
        for (String type : types) {
            BudgetAnalysisBudgetChartDto.BudgetAnalysisBudgetChartItemDto budgetAnalysisBudgetChartItemDto = new BudgetAnalysisBudgetChartDto.BudgetAnalysisBudgetChartItemDto();
            budgetAnalysisBudgetChartItemDto.setType(type.toUpperCase());
            budgetAnalysisBudgetChartItemDto.setBudget(typeMap.getOrDefault(type, BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP).toString());
            totalAmount = totalAmount.add(typeMap.getOrDefault(type, BigDecimal.ZERO));
            budgetAnalysisBudgetChartItemDtos.add(budgetAnalysisBudgetChartItemDto);
        }
        budgetAnalysisBudgetChartDto.setTotalBudget(totalAmount.setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisBudgetChartDto.setItemDtoList(budgetAnalysisBudgetChartItemDtos);
        return budgetAnalysisBudgetChartDto;
    }

    private void dealPageResult(Page<OdsAmazonAdBudgetUsageDay> page, Page<BudgetAnalysisPageDto> voPage, BudgetAnalysisQueryRequest budgetAnalysisQueryRequest, Boolean isExport) {
        voPage.setTotalPage(page.getTotalPage());
        voPage.setTotalSize(page.getTotalSize());
        if (CollectionUtils.isEmpty(page.getRows())) {
            voPage.setRows(new ArrayList<>());
            return;
        }
        List<OdsAmazonAdBudgetUsageDay> odsAmazonAdBudgetUsageDays = page.getRows();
        // 活动Id|日期
        List<String> idDateList = odsAmazonAdBudgetUsageDays.stream().map(k -> k.getBudgetScopeId() + "|" + k.getUsageUpdatedSiteDate().toString()).collect(Collectors.toList());
        // 日期
        List<String> date = odsAmazonAdBudgetUsageDays.stream().map(k -> k.getUsageUpdatedSiteDate().toString()).distinct().collect(Collectors.toList());
        // 活动Id
        List<String> ids = odsAmazonAdBudgetUsageDays.stream().map(OdsAmazonAdBudgetUsageDay::getBudgetScopeId).distinct().collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), ids).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));
        List<String> portfolioIds = amazonAdCampaignAllMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).collect(Collectors.toList());
        // 店铺
        Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();
        List<Integer> shopId = amazonAdCampaignAllMap.values().stream().map(AmazonAdCampaignAll::getShopId).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shopId)) {
            shopAuthMap = shopAuthDao.getAuthShopByShopIdList(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList()).stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (e1, e2) -> e2));
            // 缩小店铺范围
            budgetAnalysisQueryRequest.setShopIdList(shopId);
        }
        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
        // 广告组合
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap = amazonAdPortfolioDao.listByShopId(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity()));
        }
        // 当日预算
        Map<String, OdsAmazonAdBudgetUsageDay> amazonAdBudgetUsageDays = odsAmazonAdBudgetUsageDayDao.listByBudgetScopeIdAndDate(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), ids, date).stream().collect(Collectors.toMap(k -> k.getBudgetScopeId() + "|" + k.getUsageUpdatedSiteDate().toString(), Function.identity(), (e1, e2) -> e2));
        // 活动报告
        Map<String, AmazonAdCampaignDorisAllReport> amazonAdCampaignDorisAllReportMap = amazonAdCampaignAllDorisDao.listByCampaignIdAndDate(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), ids, date, idDateList).stream().collect(Collectors.toMap(k -> k.getCampaignId() + "|" + k.getCountDate(), Function.identity(), (e1, e2) -> e2));
        // 建议预算
        Map<String, AmazonAdMissBudget> missBudgetMap = amazonAdMissBudgetService.listByCampaignIdsAndShopIds(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), ids).stream().collect(Collectors.toMap(AmazonAdMissBudget::getCampaignId, Function.identity()));
        Map<String, List<OdsAmazonAdBudgetUsage>> amazonAdBudgetUsage = new HashMap<>();
        Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = new HashMap<>();
        // 非导出才查询这个
        if (!isExport) {
            List<OdsAmazonAdBudgetUsage> budgetUsages = odsAmazonAdBudgetUsageDao.listCampaignBySiteDate(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), ids, date, idDateList);
            amazonAdBudgetUsage = budgetUsages.stream().collect(Collectors.groupingBy(k -> k.getBudgetScopeId() + "|" + k.getUsageUpdatedSiteDate().toString()));
            Date lastDate = DateUtil.addDay(new Date(), -1);
            amazonAdOperationLogMap = amazonAdOperationLogService.listGroupByTypeAndIdentifyIdAndShopIdList(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), Constants.SP, AmazonAdOperationLogEntityTypeEnum.CAMPAIGN.getCode(), AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), ids, lastDate);
        }
        List<BudgetAnalysisPageDto> budgetAnalysisPageDtos = new ArrayList<>();
        for (OdsAmazonAdBudgetUsageDay amazonAdBudgetUsageDay : odsAmazonAdBudgetUsageDays) {
            String key = amazonAdBudgetUsageDay.getBudgetScopeId() + "|" + amazonAdBudgetUsageDay.getUsageUpdatedSiteDate().toString();
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(amazonAdBudgetUsageDay.getBudgetScopeId());
            // 忽略脏数据
            if (amazonAdCampaignAll == null) {
                continue;
            }
            BudgetAnalysisPageDto budgetAnalysisPageDto = buildBudgetAnalysisPage(amazonAdBudgetUsageDays.get(key), portfolioMap,
                    amazonAdCampaignAll,
                    shopAuthMap.get(amazonAdCampaignAll.getShopId()),
                    amazonAdCampaignDorisAllReportMap.getOrDefault(key, new AmazonAdCampaignDorisAllReport()),
                    missBudgetMap.get(amazonAdBudgetUsageDay.getBudgetScopeId()), amazonAdBudgetUsage.get(key), amazonAdOperationLogMap.get(amazonAdBudgetUsageDay.getBudgetScopeId()), isExport);
            budgetAnalysisPageDtos.add(budgetAnalysisPageDto);
        }
        voPage.setRows(budgetAnalysisPageDtos);
    }

    private BudgetAnalysisPageDto buildBudgetAnalysisPage(OdsAmazonAdBudgetUsageDay amazonAdBudgetUsageDay, Map<String, AmazonAdPortfolio> portfolioMap,
                                                          AmazonAdCampaignAll amazonAdCampaignAll,
                                                          ShopAuth shopAuth,
                                                          AmazonAdCampaignDorisAllReport amazonAdCampaignDorisAllReport,
                                                          AmazonAdMissBudget amazonAdMissBudget,
                                                          List<OdsAmazonAdBudgetUsage> amazonAdBudgetUsages,
                                                          AmazonAdOperationLogBO amazonAdOperationLogBO,
                                                          Boolean export) {
        BudgetAnalysisPageDto budgetAnalysisPageDto = new BudgetAnalysisPageDto();
        budgetAnalysisPageDto.setName(amazonAdCampaignAll.getName());
        if (StringUtils.isNotBlank(amazonAdCampaignAll.getState())) {
            budgetAnalysisPageDto.setState(amazonAdCampaignAll.getState().toLowerCase());
            budgetAnalysisPageDto.setStateName(AmazonAdCampaignAll.stateEnum.valueOf(budgetAnalysisPageDto.getState()).getDescription());
        }
        budgetAnalysisPageDto.setShopId(shopAuth.getId());
        budgetAnalysisPageDto.setShopName(shopAuth.getName());
        budgetAnalysisPageDto.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
        budgetAnalysisPageDto.setMarketplaceId(shopAuth.getMarketplaceId());
        budgetAnalysisPageDto.setCampaignId(amazonAdBudgetUsageDay.getBudgetScopeId());
        budgetAnalysisPageDto.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
        AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonAdCampaignAll.getPortfolioId());
        budgetAnalysisPageDto.setPortfolioName(amazonAdPortfolio != null ? amazonAdPortfolio.getName() : "-");
        budgetAnalysisPageDto.setType(amazonAdBudgetUsageDay.getAdvertisingProductType());
        budgetAnalysisPageDto.setBudget(amazonAdBudgetUsageDay.getBudget().setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setBudgetUsagePercentage(amazonAdBudgetUsageDay.getBudgetUsagePercentage().setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setDate(amazonAdBudgetUsageDay.getUsageUpdatedSiteDate().toString());
        budgetAnalysisPageDto.setBudgetRemaining(amazonAdBudgetUsageDay.getBudgetRemaining().setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setSpent(amazonAdBudgetUsageDay.getBudgetUsage().setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setBudgetTime(getBudgetTimeFormat(amazonAdBudgetUsageDay.getBudgetTime()));
        budgetAnalysisPageDto.setBudgetAdjustmentTime(amazonAdBudgetUsageDay.getBudgetAdjustmentTime());
        budgetAnalysisPageDto.setImpressions(StringUtil.getSafeValue(amazonAdCampaignDorisAllReport.getImpressions()));
        budgetAnalysisPageDto.setClicks(StringUtil.getSafeValue(amazonAdCampaignDorisAllReport.getClicks()));
        budgetAnalysisPageDto.setBudgetAdjustmentNum(amazonAdBudgetUsageDay.getBudgetAdjustmentNum());
        budgetAnalysisPageDto.setDailyBudget(amazonAdCampaignAll.getBudget().setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setBudgetType(amazonAdCampaignAll.getBudgetType());
        budgetAnalysisPageDto.setAdOrderNum(StringUtil.getSafeValue(amazonAdCampaignDorisAllReport.getOrderNum()));
        budgetAnalysisPageDto.setOrderNum(StringUtil.getSafeValue(amazonAdCampaignDorisAllReport.getSaleNum()));
        budgetAnalysisPageDto.setCtr(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getClicks(), amazonAdCampaignDorisAllReport.getImpressions(), true));
        budgetAnalysisPageDto.setCvr(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getOrderNum(), amazonAdCampaignDorisAllReport.getClicks(), true));
        budgetAnalysisPageDto.setAdCost(StringUtil.getSafeValue(amazonAdCampaignDorisAllReport.getCost()).setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setAdSale(StringUtil.getSafeValue(amazonAdCampaignDorisAllReport.getTotalSales()).setScale(2, RoundingMode.HALF_UP));
        budgetAnalysisPageDto.setAcos(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getCost(), amazonAdCampaignDorisAllReport.getTotalSales(), true));
        budgetAnalysisPageDto.setRoas(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getTotalSales(), amazonAdCampaignDorisAllReport.getCost(), false));
        budgetAnalysisPageDto.setAdCostPerClick(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getCost(), amazonAdCampaignDorisAllReport.getClicks(), false));
        budgetAnalysisPageDto.setCpa(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getCost(), amazonAdCampaignDorisAllReport.getOrderNum(), false));
        budgetAnalysisPageDto.setAdvertisingUnitPrice(StringUtil.getSafeDivide(amazonAdCampaignDorisAllReport.getTotalSales(), amazonAdCampaignDorisAllReport.getOrderNum(), false));
        // 已花费  / （广告花费/ 点击次数) 优化为 已花费  * 点击 / 广告花费
        budgetAnalysisPageDto.setEstimateClicks(StringUtil.getSafeDivide(amazonAdBudgetUsageDay.getBudgetUsage().multiply(new BigDecimal(amazonAdCampaignDorisAllReport.getClicks() == null ? 0 : amazonAdCampaignDorisAllReport.getClicks())), amazonAdCampaignDorisAllReport.getCost(), false).setScale(0, RoundingMode.HALF_UP).intValue());
        budgetAnalysisPageDto.setId(amazonAdCampaignAll.getId());
        budgetAnalysisPageDto.setCampaignTargetingType(amazonAdCampaignAll.getAdTargetType());
        if (CampaignTypeEnum.sp.getCampaignType().equals(amazonAdCampaignAll.getType())) {
            budgetAnalysisPageDto.setCampaignTargetingType(amazonAdCampaignAll.getTargetingType());
        }
        if (CampaignTypeEnum.sb.getCampaignType().equals(amazonAdCampaignAll.getType())) {
            budgetAnalysisPageDto.setCampaignTargetingType(Constants.MANUAL);
            budgetAnalysisPageDto.setTargetingType(amazonAdCampaignAll.getIsMultiAdGroupsEnabled() ? "true" : "false");
        }
        if (CampaignTypeEnum.sd.getCampaignType().equals(amazonAdCampaignAll.getType())) {
            budgetAnalysisPageDto.setCampaignTargetingType(amazonAdCampaignAll.getTactic());
        }
        // 组装页面需要数据
        if (CollectionUtils.isNotEmpty(amazonAdBudgetUsages)) {
            amazonAdBudgetUsages.sort(Comparator.comparing(OdsAmazonAdBudgetUsage::getUsageUpdatedSiteTime));
            budgetAnalysisPageDto.setOverBudgetTime(getOverBudgetTime(amazonAdBudgetUsages));
            budgetAnalysisPageDto.setBudgetRemainingChart(amazonAdBudgetUsages.stream().map(this::build).collect(Collectors.toList()));
        }
        amazonAdCampaignAll.setServingStatus(amazonAdCampaignAll.getServingStatus());
        budgetAnalysisPageDto.setServingStatus(amazonAdCampaignAll.getServingStatus());
        budgetAnalysisPageDto.setServingStatusName(amazonAdCampaignAll.getServingStatusName());
        budgetAnalysisPageDto.setServingStatusDec(amazonAdCampaignAll.getServingStatusDec());
        budgetAnalysisPageDto.setStrategy(amazonAdCampaignAll.getStrategy());
        // 预算错过
        if (amazonAdMissBudget != null) {
            budgetAnalysisPageDto.setSuggestedBudget(amazonAdMissBudget.getSuggestedBudget());
            budgetAnalysisPageDto.setSuggestedBudgetIncreasePercent(StringUtil.getSafeValueStr(amazonAdMissBudget.getSuggestedBudgetIncreasePercent()));
            BudgetAnalysisPageDto.MissBudgetRecommendation missBudgetRecommendation = build(amazonAdMissBudget);
            budgetAnalysisPageDto.setMissBudgetRecommendation(missBudgetRecommendation);
        }
        // 设置预算小黄点
        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), amazonAdCampaignAll.getCampaignId());
        if (amazonAdOperationLogBO != null) {
            BudgetAnalysisPageDto.BudgetAdOperationLog budgetAdOperationLog = new BudgetAnalysisPageDto.BudgetAdOperationLog();
            if (StringUtils.isNotBlank(amazonAdOperationLogBO.getContent())) {
                OperationContent operationContent = JSONUtil.jsonToObject(amazonAdOperationLogBO.getContent(), OperationContent.class);
                if (operationContent != null) {
                    budgetAdOperationLog.setCount(amazonAdOperationLogBO.getCount());
                    if (StringUtils.isNotBlank(operationContent.getPreviousValue())) {
                        budgetAdOperationLog.setPreviousValue(operationContent.getPreviousValue());
                    }
                    if (StringUtils.isNotBlank(operationContent.getNewValue())) {
                        budgetAdOperationLog.setNewValue(operationContent.getNewValue());
                    }
                    budgetAdOperationLog.setSiteOperationTime(amazonAdOperationLogBO.getSiteOperationTime());
                    budgetAnalysisPageDto.setBudgetLog(budgetAdOperationLog);
                }
            }
            budgetAnalysisPageDto.setIsUpdateBudget(true);
        } else {
            if (!export) {
                budgetAnalysisPageDto.setIsUpdateBudget(stringRedisService.get(logKey) != null);
            }
        }

        if (AmazonAdCampaignAll.stateEnum.enabled.getCode().equals(amazonAdCampaignAll.getState())) {
            // 设置超预算时间
            if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaignAll.getServingStatus()) || AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode().equalsIgnoreCase(amazonAdCampaignAll.getServingStatus())) {
                if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaignAll.getServingStatus())) {
                    budgetAnalysisPageDto.setServingStatus(AmazonAdCampaignAll.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode());
                }
                //如果状态是超过预算
                String outOfTimeStr = "";
                if (amazonAdCampaignAll.getOutOfBudgetTime() != null) {
                    try {
                        LocalDateTime localDateTime = LocalDateTimeUtil.ofEpochSecondToDateTime(amazonAdCampaignAll.getOutOfBudgetTime());
                        ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(amazonAdCampaignAll.getMarketplaceId());
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        Date date = LocalDateTimeUtil.convertLDTToDate(localDateTime);
                        outOfTimeStr = DateUtil.dateToStrWithFormat(date, "HH:mm");
                    } catch (Exception e) {
                        log.error("转换超预算时间错误", e);
                    }
                }
                budgetAnalysisPageDto.setServingStatusName(budgetAnalysisPageDto.getServingStatusName() + " " + outOfTimeStr);
            }
        }

        if (StringUtils.isNotBlank(amazonAdCampaignAll.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(amazonAdCampaignAll.getAdjustments(), new TypeReference<List<Adjustment>>() {
                });
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            budgetAnalysisPageDto.setPlacementProductPage("0");
            budgetAnalysisPageDto.setPlacementTop("0");
            budgetAnalysisPageDto.setPlacementRestOfSearch("0");
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        budgetAnalysisPageDto.setPlacementProductPage(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        budgetAnalysisPageDto.setPlacementTop(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        budgetAnalysisPageDto.setPlacementRestOfSearch(String.valueOf(adjustment.getPercentage()));
                    }
                }
            }
        }
        return budgetAnalysisPageDto;
    }

    private BudgetAnalysisPageDto.BudgetRemainingChart build(OdsAmazonAdBudgetUsage amazonAdBudgetUsage) {
        BudgetAnalysisPageDto.BudgetRemainingChart budgetRemainingChart = new BudgetAnalysisPageDto.BudgetRemainingChart();
        budgetRemainingChart.setCurrentBudget(amazonAdBudgetUsage.getBudget().setScale(2, RoundingMode.HALF_UP).toString());
        budgetRemainingChart.setPercent(amazonAdBudgetUsage.getBudgetUsagePercentage().setScale(2, RoundingMode.HALF_UP).toString());
        budgetRemainingChart.setUpdateAt(amazonAdBudgetUsage.getUsageUpdatedSiteTime().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_DATE_TIME)));
        return budgetRemainingChart;
    }

    private BudgetAnalysisPageDto.MissBudgetRecommendation build(AmazonAdMissBudget succ) {
        BudgetAnalysisPageDto.MissBudgetRecommendation builder = new BudgetAnalysisPageDto.MissBudgetRecommendation();
        if (succ.getEstimatedMissedSalesLower() != null) {
            builder.setEstimatedMissedSalesLower(succ.getEstimatedMissedSalesLower().toString());
        }
        if (succ.getEstimatedMissedSalesUpper() != null) {
            builder.setEstimatedMissedSalesUpper(succ.getEstimatedMissedSalesUpper().toString());
        }
        if (succ.getEstimatedMissedImpressionsLower() != null) {
            builder.setEstimatedMissedImpressionsLower(succ.getEstimatedMissedImpressionsLower().toString());
        }
        if (succ.getEstimatedMissedImpressionsUpper() != null) {
            builder.setEstimatedMissedImpressionsUpper(succ.getEstimatedMissedImpressionsUpper().toString());
        }
        if (succ.getEstimatedMissedClicksLower() != null) {
            builder.setEstimatedMissedClicksLower(succ.getEstimatedMissedClicksLower().toString());
        }
        if (succ.getEstimatedMissedClicksUpper() != null) {
            builder.setEstimatedMissedClicksUpper(succ.getEstimatedMissedClicksUpper().toString());
        }
        if (succ.getPercentTimeInBudget() != null) {
            builder.setPercentTimeInBudget(succ.getPercentTimeInBudget().toString());
        }
        if (StringUtils.isNotBlank(succ.getStartDate())) {
            builder.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(succ.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        }
        if (StringUtils.isNotBlank(succ.getEndDate())) {
            builder.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(succ.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        }
        if (StringUtils.isNotBlank(succ.getRuleId())) {
            builder.setRuleId(succ.getRuleId());
        }
        if (StringUtils.isNotBlank(succ.getRuleName())) {
            builder.setRuleName(succ.getRuleName());
        }
        if (succ.getSuggestedBudgetIncreasePercent() != null) {
            builder.setSuggestedBudgetIncreasePercent(succ.getSuggestedBudgetIncreasePercent().toString());
        }
        return builder;
    }

    private List<List<String>> getOverBudgetTime(List<OdsAmazonAdBudgetUsage> amazonAdBudgetUsages) {
        List<List<String>> list = new ArrayList<>();
        int length = amazonAdBudgetUsages.size();
        boolean isBudgetOver = false;
        LocalDateTime localDateTime = null;
        for (int i = 0; i < length; i++) {
            OdsAmazonAdBudgetUsage amazonAdBudgetUsage = amazonAdBudgetUsages.get(i);
            if (isBudgetOver) {
                // 代表这段时间 是超预算的
                list.add(Lists.newArrayList(LocalDateTimeUtil.getHourTime(localDateTime), LocalDateTimeUtil.getHourTime(amazonAdBudgetUsage.getUsageUpdatedSiteTime())));
            }
            isBudgetOver = amazonAdBudgetUsage.getBudgetUsagePercentage().compareTo(BigDecimal.valueOf(100L)) >= 0;
            if (isBudgetOver) {
                localDateTime = amazonAdBudgetUsage.getUsageUpdatedSiteTime();
            }
            // 代表最后结束了
            if (i == (length - 1)) {
                if (isBudgetOver) {
                    // 代表这段时间 是超预算的
                    list.add(Lists.newArrayList(LocalDateTimeUtil.getHourTime(amazonAdBudgetUsage.getUsageUpdatedSiteTime()), "24:00"));
                }
            }
        }
        return list;
    }

    private static String getBudgetTimeFormat(BigDecimal time) {
        if (time == null || time.compareTo(BigDecimal.ZERO) <= 0) {
            return "00小时00分";
        }
        // 获取整数部分
        BigDecimal integerPart = time.setScale(0, RoundingMode.DOWN);
        String timeStr = String.valueOf(integerPart.intValue());
        if (timeStr.length() == 1) {
            timeStr = "0" + timeStr;
        }
        timeStr = timeStr + "小时";
        BigDecimal part = time.subtract(integerPart);
        if (part.compareTo(BigDecimal.ZERO) >= 0) {
            String partStr = String.valueOf(part.multiply(BigDecimal.valueOf(60L)).setScale(0, RoundingMode.HALF_UP).intValue());
            if (partStr.length() == 1) {
                partStr = "0" + partStr;
            }
            timeStr = timeStr + partStr + "分";
        }
        return timeStr;
    }

    /**
     * 处理请求消息
     */
    private void dealBudgetAnalysisQueryRequest(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest) {
        if (budgetAnalysisQueryRequest.getSiteToday()) {
            // 站点今天 无论啥结果设置开始时间 减少三天
            budgetAnalysisQueryRequest.setStartDate(LocalDate.now().minusDays(3).toString());
            // 未来日期 + 2天
            budgetAnalysisQueryRequest.setEndDate(LocalDate.now().minusDays(-2).toString());
            // 站点不为空 才设置站点今日
            if (CollectionUtils.isNotEmpty(budgetAnalysisQueryRequest.getMarketplaceIdList())) {
                budgetAnalysisQueryRequest.setSiteTodayDate(CalculateAdDataUtil.getSiteToday(budgetAnalysisQueryRequest.getMarketplaceIdList()));
            }
        }
        budgetAnalysisQueryRequest.setClickRateMin(budgetAnalysisQueryRequest.getClickRateMin() != null ? MathUtil.divide(budgetAnalysisQueryRequest.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        budgetAnalysisQueryRequest.setClickRateMax(budgetAnalysisQueryRequest.getClickRateMax() != null ? MathUtil.divide(budgetAnalysisQueryRequest.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        budgetAnalysisQueryRequest.setAcosMin(budgetAnalysisQueryRequest.getAcosMin() != null ? MathUtil.divide(budgetAnalysisQueryRequest.getAcosMin(), BigDecimal.valueOf(100)) : null);
        budgetAnalysisQueryRequest.setAcosMax(budgetAnalysisQueryRequest.getAcosMax() != null ? MathUtil.divide(budgetAnalysisQueryRequest.getAcosMax(), BigDecimal.valueOf(100)) : null);
        budgetAnalysisQueryRequest.setSalesConversionRateMin(budgetAnalysisQueryRequest.getSalesConversionRateMin() != null ? MathUtil.divide(budgetAnalysisQueryRequest.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        budgetAnalysisQueryRequest.setSalesConversionRateMax(budgetAnalysisQueryRequest.getSalesConversionRateMax() != null ? MathUtil.divide(budgetAnalysisQueryRequest.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
    }

    /**
     * 组合和商品的拦截
     */
    private List<String> getQueryIdsByPageFilter(BudgetAnalysisQueryRequest budgetAnalysisQueryRequest) {
        List<String> campaignIds = null;
        if (StringUtils.isNotBlank(budgetAnalysisQueryRequest.getPortfolioIds())) {
            //广告组合id不为空
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdAndShopIds(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), budgetAnalysisQueryRequest.getPortfolioIds(), budgetAnalysisQueryRequest.getType(), null, null);
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                budgetAnalysisQueryRequest.setCampaignIdList(campaignIds);
            } else {
                campaignIds = new ArrayList<>();
            }
        }
        if ((Objects.isNull(campaignIds) || CollectionUtils.isNotEmpty(campaignIds)) && StringUtils.isNotBlank(budgetAnalysisQueryRequest.getProductType()) && Constants.CAMPAIGN_PRODUCT_SELECT.contains(budgetAnalysisQueryRequest.getProductType()) && StringUtils.isNotBlank(budgetAnalysisQueryRequest.getProductValue())) {
            List<String> listProductValues = budgetAnalysisQueryRequest.getListProductValues();
            //查询asin/msku不为空
            campaignIds = amazonAdProductDao.getCampaignIdByAsinOrMskuShopList(budgetAnalysisQueryRequest.getPuid(), budgetAnalysisQueryRequest.getShopIdList(), budgetAnalysisQueryRequest.getMarketplaceIdList(), budgetAnalysisQueryRequest.getProductType(), listProductValues, budgetAnalysisQueryRequest.getType(), campaignIds);
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 为空直接返回结果
                budgetAnalysisQueryRequest.setCampaignIdList(campaignIds);
            } else {
                campaignIds = new ArrayList<>();
            }
        }
        if (campaignIds == null && StringUtils.isNotBlank(budgetAnalysisQueryRequest.getCampaignIds())) {
            campaignIds = StringUtil.splitStr(budgetAnalysisQueryRequest.getCampaignIds());
        } else if (CollectionUtils.isNotEmpty(campaignIds) && StringUtils.isNotBlank(budgetAnalysisQueryRequest.getCampaignIds())) {
            campaignIds.retainAll(StringUtil.splitStr(budgetAnalysisQueryRequest.getCampaignIds()));
        }
        return campaignIds;
    }
}
