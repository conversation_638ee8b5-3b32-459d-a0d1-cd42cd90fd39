package com.meiyunji.sponsored.service.multiple.targets.dto;

import com.meiyunji.sponsored.service.enums.SDCreativeTypeEnum;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 投放基础信息
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
public class GroupInfo {

    /**
     * 广告组id
     */
    private String adGroupId;

    /**
     * 广告组名称
     */
    private String name;

    /**
     * 广告组状态
     */
    private String state;

    /**
     * sb广告类型 product keyword
     */
    private String adGroupType;

    /**
     * 是否组分时竞价
     */
    private Integer isStateBidding;

    /**
     * 组分时竞价状态
     */
    private Integer pricingStateBidding;

    /**
     * 默认竞价
     */
    private BigDecimal defaultBid;

    /**
     * 'sb广告组活动类型 商品集 视频集'
     */
    private String adFormat;

    /**
     * 广告格式 {@link SDCreativeTypeEnum}
     */
    private String creativeType;
}
