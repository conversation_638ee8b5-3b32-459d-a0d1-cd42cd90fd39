package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdInvoiceDetailsDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdInvoiceDetails;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class AmazonAdInvoiceDetailsDaoImpl extends BaseShardingDaoImpl<AmazonAdInvoiceDetails> implements IAmazonAdInvoiceDetailsDao {

    @Override
    public void insertOrUpdateList(int puid, List<AmazonAdInvoiceDetails> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_invoice_details`(`puid`,`shop_id`,`marketplace_id`,`invoice_id`,`promotions`,")
                .append("`government_invoice_information`,`payer_contact_information`,`tax_detail`,`adjustments`,`invoice_lines`,")
                .append("`issuer_contact_information`,`third_party_contact_information`,`payments`,`portfolios`,`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdInvoiceDetails details : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(details.getShopId());
            argsList.add(details.getMarketplaceId());
            argsList.add(details.getInvoiceId());
            argsList.add(details.getPromotions());
            argsList.add(details.getGovernmentInvoiceInformation());
            argsList.add(details.getPayerContactInformation());
            argsList.add(details.getTaxDetail());
            argsList.add(details.getAdjustments());
            argsList.add(details.getInvoiceLines());
            argsList.add(details.getIssuerContactInformation());
            argsList.add(details.getThirdPartyContactInformation());
            argsList.add(details.getPayments());
            argsList.add(details.getPortfolios());
        }
        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `promotions`=values(promotions),`government_invoice_information`=values(government_invoice_information),`payer_contact_information`=values(payer_contact_information),");
        sql.append("`tax_detail`=values(tax_detail),`adjustments`=values(adjustments),`invoice_lines`=values(invoice_lines),`issuer_contact_information`=values(issuer_contact_information),");
        sql.append("`third_party_contact_information`=values(third_party_contact_information),`payments`=values(payments),`portfolios`=values(portfolios)");
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public AmazonAdInvoiceDetails getInvoiceDetailsByInvoiceId(Integer puid, Integer shopId, String invoiceId) {

        String sql = "select * from t_amazon_ad_invoice_details where puid=? and shop_id=? and invoice_id = ? ";
        List<Object> args = Lists.newArrayList(puid, shopId,invoiceId);
        List<AmazonAdInvoiceDetails> query = getJdbcTemplate(puid).query(sql, args.toArray(), getMapper());
        if(CollectionUtils.isEmpty(query)){
            return null;
        }
        return query.size() == 1 ? query.get(0) : null;

    }


    @Override
    public List<AmazonAdInvoiceDetails> getInvoiceDetailsByInvoiceIds(Integer puid, List<Integer> shopIds, List<String> invoiceIds) {

        StringBuilder sql =  new StringBuilder(" select * from t_amazon_ad_invoice_details where puid=? ");

        List<Object> args = Lists.newArrayList(puid);
        if(CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,args));
        }
        if(CollectionUtils.isNotEmpty(invoiceIds)){
            sql.append(SqlStringUtil.dealInList("invoice_id",invoiceIds,args));
        }

        List<AmazonAdInvoiceDetails> query = getJdbcTemplate(puid).query(sql.toString(), args.toArray(), getMapper());
        return query;


    }


    @Override
    public void deleteByInvoiceId(Integer puid, Integer shopId, String invoiceId) {
        String sql = "DELETE FROM t_amazon_ad_invoice_details  where puid=? and shop_id=? and invoice_id = ? ";
        List<Object> args = Lists.newArrayList(puid, shopId, invoiceId);
        getJdbcTemplate(puid).update(sql, args.toArray());
    }

}

