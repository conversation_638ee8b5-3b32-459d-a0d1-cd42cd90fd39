package com.meiyunji.sponsored.service.wordFrequency.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTopBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.dao.IAmazonAdWordRootKeywordSpDao;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordRootTopDto;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSp;
import com.meiyunji.sponsored.service.wordFrequency.po.WordRootKeywordSp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-20  22:44
 */
@Repository
public class AmazonAdWordRootKeywordSpDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdWordRootKeywordSp> implements IAmazonAdWordRootKeywordSpDao {

    @Override
    public List<WordRootTranslatorBo> listTranslatorBoByShopId(Integer puid, Integer shopId, String start, String end, int limit) {
        StringBuilder sb = new StringBuilder("select id, word_root wordRoot from " + this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and create_time >= ? and create_time <= ? and word_root_cn is null limit " + limit);
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(start);
        argsList.add(end);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTranslatorBo.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchUpdateWordRootCn(Integer puid, List<WordRootTranslatorBo> updateList) {
        StringBuilder sql = new StringBuilder("update " + this.getJdbcHelper().getTable() + " set word_root_cn = ? where id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (WordRootTranslatorBo bo : updateList) {
            batchArg = new Object[]{bo.getWordRootCn(), bo.getId()};
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> listKeywordIdByWordRootAndKeywordIdList(Integer puid, Integer shopId, String wordRoot, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select distinct keyword_id from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and word_root = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(wordRoot);
        if (CollectionUtils.isNotEmpty(keywordIds)) {
            sb.append(SqlStringUtil.dealInList("keyword_id", keywordIds, argsList));
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void batchInsertOrUpdateSpKeyword(Integer puid, List<AmazonAdWordRootKeywordSp> amazonAdWordRootKeywordSpList) {
        StringBuilder sql = new StringBuilder("INSERT INTO "+ getJdbcHelper().getTable() +" (`puid`,`shop_id`,`word_frequency_type`,`keyword_text`,")
                .append("`word_root`,`keyword_id`, `campaign_id`, `ad_group_id`, `match_type`, `create_time`,`update_time`) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdWordRootKeywordSp amazonAdWordRootKeywordSp : amazonAdWordRootKeywordSpList) {
            sql.append(" (?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(amazonAdWordRootKeywordSp.getShopId());
            argsList.add(amazonAdWordRootKeywordSp.getWordFrequencyType());
            argsList.add(amazonAdWordRootKeywordSp.getKeywordText());
            argsList.add(amazonAdWordRootKeywordSp.getWordRoot());
            argsList.add(amazonAdWordRootKeywordSp.getKeywordId());
            argsList.add(amazonAdWordRootKeywordSp.getCampaignId());
            argsList.add(amazonAdWordRootKeywordSp.getAdGroupId());
            argsList.add(amazonAdWordRootKeywordSp.getMatchType());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `puid`=values(puid),`shop_id`=values(shop_id),")
                .append("word_frequency_type=values(word_frequency_type),keyword_text=values(keyword_text)," +
                        "`word_root`=values(word_root),`keyword_id`=values(keyword_id),`campaign_id`=values(campaign_id),`ad_group_id`=values(ad_group_id),`match_type`=values(match_type)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append(" and shop_id = ? ");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }
}
