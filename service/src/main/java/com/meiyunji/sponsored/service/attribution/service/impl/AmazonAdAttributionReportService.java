package com.meiyunji.sponsored.service.attribution.service.impl;

import com.amazon.advertising.attribution.AttributionReports;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.attribution.api.AttributionApiClient;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionReportDao;
import com.meiyunji.sponsored.service.attribution.dao.IAmazonAdAttributionReportSyncStatusDao;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionReport;
import com.meiyunji.sponsored.service.attribution.entity.AmazonAdAttributionReportSyncStatus;
import com.meiyunji.sponsored.service.attribution.service.IAmazonAdAttributionReportService;
import com.meiyunji.sponsored.service.cache.UserPlanTypeCacheService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2022/3/12 11:55
 * @describe:
 */
@Service
@Slf4j
public class AmazonAdAttributionReportService implements IAmazonAdAttributionReportService {

    private final IAmazonAdProfileDao amazonAdProfileDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final AttributionApiClient attributionApiClient;
    private final IAmazonAdAttributionReportDao amazonAdAttributionReportDao;
    private final IAmazonAdAttributionReportSyncStatusDao amazonAdAttributionReportSyncStatusDao;
    @Autowired
    private UserPlanTypeCacheService userPlanTypeCacheService;

    public AmazonAdAttributionReportService(
            IAmazonAdProfileDao amazonAdProfileDao,
            IScVcShopAuthDao shopAuthDao, AttributionApiClient attributionApiClient,
            IAmazonAdAttributionReportDao amazonAdAttributionReportDao, IAmazonAdAttributionReportSyncStatusDao amazonAdAttributionReportSyncStatusDao) {
        this.amazonAdProfileDao = amazonAdProfileDao;
        this.shopAuthDao = shopAuthDao;
        this.attributionApiClient = attributionApiClient;
        this.amazonAdAttributionReportDao = amazonAdAttributionReportDao;
        this.amazonAdAttributionReportSyncStatusDao = amazonAdAttributionReportSyncStatusDao;
    }

    public List<AmazonAdAttributionReport> countByCreativeIds(
            Integer puid, List<String> creativeIds, String startDate, String endDate) {
        return amazonAdAttributionReportDao.countByCreativeIds(puid, creativeIds, startDate, endDate);
    }


    public List<AmazonAdAttributionReport> countByDate(
            Integer puid, String creativeId, String startDate, String endDate) {
        return amazonAdAttributionReportDao.countByDate(puid, creativeId, startDate, endDate);
    }

    @Override
    public void syncAttributionReport(Integer puid, Integer shopId, String startDate, String endDate) {
        List<AmazonAdAttributionReportSyncStatus> statusList = amazonAdAttributionReportSyncStatusDao.getNeedSyncAttributionReportShop(puid,shopId);
        if (CollectionUtils.isEmpty(statusList)) {
            return;
        }

        List<AmazonAdAttributionReportSyncStatus> statuses = new ArrayList<>();
        for (AmazonAdAttributionReportSyncStatus status : statusList) {
            if (userPlanTypeCacheService.puidExpire(status.getPuid())) {
                log.warn("同步attribution报告数据, shopId: {}, puid: {}, 状态为-99, 停止同步", status.getShopId(), status.getPuid());
                continue;
            }
            statuses.add(status);
        }

        if (CollectionUtils.isEmpty(statuses)) {
            return;
        }

        List<Integer> shopIds = statuses.stream().map(AmazonAdAttributionReportSyncStatus::getShopId).collect(Collectors.toList());
        List<ShopAuth> shopAuths = shopAuthDao.getScAndVcByIds(shopIds);
        Map<Integer, ShopAuth> shopAuthMap = shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

        for (AmazonAdAttributionReportSyncStatus status : statuses) {
            List<AmazonAdAttributionReport> reportEntities = Lists.newArrayList();
            if (shopAuthMap.containsKey(status.getShopId())) {
                String cursorId = null;
                do {
                    AttributionReports attributionReports = attributionApiClient.syncAttributionReport(shopAuthMap.get(status.getShopId()), status.getProfileId(),
                            status.getMarketplaceId(), startDate, endDate, cursorId);
                    if (attributionReports != null) {
                        cursorId = attributionReports.getCursorId();
                        if (CollectionUtils.isNotEmpty(attributionReports.getReports())) {
                            List<AttributionReports.Reports> reports = attributionReports.getReports().stream()
                                    .filter(item-> StringUtils.isNotBlank(item.getCreativeId())).collect(Collectors.toList());
                            for (AttributionReports.Reports report : reports) {
                                reportEntities.add(AmazonAdAttributionReport.builder().countDate(report.getDate())
                                        .puid(status.getPuid()).shopId(status.getShopId())
                                        .campaignId(StringUtils.isBlank(report.getCampaignId()) ? StringUtils.EMPTY : report.getCampaignId())
                                        .adGroupId(StringUtils.isBlank(report.getAdGroupId()) ? StringUtils.EMPTY : report.getAdGroupId())
                                        .creativeId(report.getCreativeId())
                                        .advertiserName(report.getAdvertiserName()).marketplaceId(status.getMarketplaceId())
                                        .advertiserId(status.getAdvertiserId())
                                        .click(StringUtils.isNotBlank(report.getClickThroughs()) ? Long.parseLong(report.getClickThroughs()) : 0L)
                                        .views(StringUtils.isNotBlank(report.getAttributedDetailPageViewsClicks14d()) ?
                                                Long.parseLong(report.getAttributedDetailPageViewsClicks14d()) : 0L)
                                        .totalViews(StringUtils.isNotBlank(report.getAttributedTotalDetailPageViewsClicks14d()) ?
                                                Long.parseLong(report.getAttributedTotalDetailPageViewsClicks14d()) : 0L)
                                        .sales(StringUtils.isNotBlank(report.getAttributedSales14d()) ?
                                                new BigDecimal(report.getAttributedSales14d()) : BigDecimal.ZERO)
                                        .totalSales(StringUtils.isNotBlank(report.getTotalAttributedSales14d()) ?
                                                new BigDecimal(report.getTotalAttributedSales14d()) : BigDecimal.ZERO)
                                        .purchases(StringUtils.isNotBlank(report.getAttributedPurchases14d()) ?
                                                Long.parseLong(report.getAttributedPurchases14d()) : 0L)
                                        .totalPurchases(StringUtils.isNotBlank(report.getAttributedTotalPurchases14d()) ?
                                                Long.parseLong(report.getAttributedTotalPurchases14d()) : 0L)
                                        .sold(StringUtils.isNotBlank(report.getUnitsSold14d()) ?
                                                Long.parseLong(report.getUnitsSold14d()) : 0L)
                                        .totalSold(StringUtils.isNotBlank(report.getTotalUnitsSold14()) ?
                                                Long.parseLong(report.getTotalUnitsSold14()) : 0L)
                                        .addToCartNum(StringUtils.isNotBlank(report.getAttributedAddToCartClicks14d()) ?
                                                Long.parseLong(report.getAttributedAddToCartClicks14d()) : 0L)
                                        .totalAddToCartNum(StringUtils.isNotBlank(report.getAttributedTotalAddToCartClicks14d()) ?
                                                Long.parseLong(report.getAttributedTotalAddToCartClicks14d()) : 0L)
                                        .publisherId(report.getPublisher()).publisherName(report.getPublisher())
                                        .createTime(LocalDateTime.now()).updateTime(LocalDateTime.now())
                                        .build());
                            }
                        }
                    } else {
                        cursorId = null;
                    }
                } while (StringUtils.isNotBlank(cursorId));
            }
            if (CollectionUtils.isNotEmpty(reportEntities)) {
                //批量入库 最大200
                List<List<AmazonAdAttributionReport>> partition = Lists.partition(reportEntities, 200);
                for (List<AmazonAdAttributionReport> reports : partition) {
                    amazonAdAttributionReportDao.insertOrUpdate(status.getPuid(), reports);
                }
            }
            //更新同步时间
            amazonAdAttributionReportSyncStatusDao.updateById(AmazonAdAttributionReportSyncStatus.builder()
                    .id(status.getId()).lastSyncTime(LocalDateTime.now()).nextSyncTime(LocalDateTime.now().plusDays(1)).build());
        }

    }

    @Override
    public AmazonAdAttributionReport summaryByCreativeIds(Integer puid, List<String> creativeIds, String startDate, String endDate) {
        return amazonAdAttributionReportDao.summaryByCreativeIds(puid,creativeIds,startDate,endDate);
    }
}
