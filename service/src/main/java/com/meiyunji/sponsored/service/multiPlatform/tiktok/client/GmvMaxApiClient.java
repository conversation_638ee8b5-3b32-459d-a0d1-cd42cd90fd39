package com.meiyunji.sponsored.service.multiPlatform.tiktok.client;

import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GmvMaxCampaignGetReq;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.GmvMaxVideoGetParam;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request.ShopProductParam;
import com.tiktok.advertising.api.GmvMaxApi;
import com.tiktok.advertising.base.Constants;
import com.tiktok.advertising.exception.TikTokSDKException;
import com.tiktok.advertising.model.gmv_max.Identity;
import com.tiktok.advertising.model.gmv_max.Store;
import com.tiktok.advertising.model.gmv_max.request.*;
import com.tiktok.advertising.model.gmv_max.response.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class GmvMaxApiClient {

    private static final GmvMaxApi gmvMaxApi = GmvMaxApi.getInstance();

    @Autowired
    private ReportRateLimiter reportRateLimiter;
    @Autowired
    private CampaignRateLimiter campaignRateLimiter;
    @Autowired
    private RedisTemplate<String, Object> redisTemplateNew;

    private static Map<String, String> campaignStatusUpdateReturnMsgTransfer = new HashMap<String, String>() {
        {
            put("This ad can't be published because the product is already part of an active GMV Max campaign.", "无法发布此广告，因为该产品已经是一个状态为active的GMV Max推广系列的一部分");
        }
    };


    public List<Store> gmvStoreList(String accessToken, String advertiserId) {
        StoreListResponse response = gmvMaxApi.storeList(accessToken, advertiserId);
        log.info("gmv max api gmvStoreList response :{}", response);
        return response.getStoreList();
    }

    public Boolean shopAdUsageCheck(String advertiserId, String storeId, String accessToken) {
        ShopAdUsageCheckResp response = gmvMaxApi.shopAdUsageCheck(accessToken, advertiserId, storeId);
        log.info("gmv max api shopAdUsageCheck response :{}", response);
        return Boolean.TRUE.equals(response.getPromoteAllProductsAllowed());
    }

    public StoreProductResponse storeProduct(ShopProductParam param, String accessToken, String bcId, String storeId, FilteringStoreProductGet filtering) {
        String sortField = "";
        String sortType = "";
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())) {
            if ("price".equalsIgnoreCase(param.getOrderField())) {
                sortField = "min_price";
            } else if ("sales".equalsIgnoreCase(param.getOrderField())) {
                sortField = "historical_sales";
            }
        }
        if (StringUtils.isNotBlank(sortField)) {
            sortType = "ASC".equalsIgnoreCase(param.getOrderValue()) ? "ASC" : "DESC";
        }

        StoreProductResponse response = gmvMaxApi.storeProduct(accessToken, param.getAdvertiserId(), bcId, storeId, filtering, sortField, sortType, param.getPageNo(), param.getPageSize());
        log.info("gmv max api storeProduct response :{}", response);
        return response;
    }

    public IdentityGetResponse identityGet(String accessToken, String advertiserId, String storeId, String storeAuthorizedBcId) {
        IdentityGetResponse response = gmvMaxApi.identityGet(accessToken, advertiserId, storeId, storeAuthorizedBcId);
        log.info("gmv max api identityGet response :{}", response);
        return response;
    }

    public IdentityGetResponse identityGetByRedis(Integer puid, String accessToken, String advertiserId, String storeId, String storeAuthorizedBcId) {
        String key = String.format(RedisConstant.TIKTOK_IDENTITY_GET, puid, advertiserId, storeId, storeAuthorizedBcId);
        IdentityGetResponse response = (IdentityGetResponse) redisTemplateNew.opsForValue().get(key);
        if (response == null) {
            response = gmvMaxApi.identityGet(accessToken, advertiserId, storeId, storeAuthorizedBcId);
            if (Objects.isNull(response)) {
                return null;
            }
            redisTemplateNew.opsForValue().set(key, response, 5, TimeUnit.MINUTES);
        }
        log.info("gmv max api identityGetByRedis response :{}", response);
        return response;
    }

    public VideoGetResponse videoGet(String accessToken, GmvMaxVideoGetParam param, String storeId, String storeAuthorizedBcId,
                                     Boolean needAuthCodeVideo, List<Identity> identityList) {

        String sortField = Boolean.TRUE.equals(param.getCustomPostsEligible()) ? "" : param.getOrderField();
        String sortType = OrderTypeEnum.asc.getType().equalsIgnoreCase(param.getOrderValue()) ? OrderTypeEnum.asc.getType().toUpperCase() : OrderTypeEnum.desc.getType().toUpperCase();

        VideoGetResponse response = gmvMaxApi.videoGet(
                accessToken, param.getAdvertiserId(), storeId, storeAuthorizedBcId,
                param.getSpuIdList(), Boolean.TRUE.equals(param.getCustomPostsEligible()), param.getKeyword(),
                needAuthCodeVideo, identityList,
                sortField, sortType, param.getPageNo(), param.getPageSize());
        log.info("gmv max api videoGet response :{}", response);
        return response;
    }

    public GmvMaxCampaignInfo campaignCreate(String accessToken, GmvMaxCampaignCreateRequest createRequest) {
        GmvMaxCampaignInfo response = gmvMaxApi.campaignCreate(accessToken, createRequest);
        log.info("gmv max api campaignCreate response :{}", response);
        return response;
    }

    public CampaignStatusUpdateResponse campaignStatusUpdate(String accessToken, CampaignStatusUpdateRequest updateRequest) {
        CampaignStatusUpdateResponse response = null;
        try {
            response = gmvMaxApi.campaignStatusUpdate(accessToken, updateRequest);
        } catch (TikTokSDKException e) {
            if (Constants.PARAM_FORMAT_ERR.equals(e.getCode()) && campaignStatusUpdateReturnMsgTransfer.containsKey(e.getErrorMessage())) {
                throw new SponsoredBizException(campaignStatusUpdateReturnMsgTransfer.get(e.getErrorMessage()));
            } else {
                throw e;
            }
        } catch (Exception e) {
            throw e;
        }
        log.info("gmv max api campaignStatusUpdate response :{}", response);
        return response;
    }

    public GmvMaxCampaignInfo campaignUpdate(String accessToken, GmvMaxCampaignUpdateRequest updateRequest) {
        GmvMaxCampaignInfo response = gmvMaxApi.campaignUpdate(accessToken, updateRequest);
        log.info("gmv max api campaignUpdate response :{}", response);
        return response;
    }

    public GmvMaxCampaignGetResponse campaignGet(String accessToken, GmvMaxCampaignGetReq req) {
        campaignRateLimiter.acquire();
        GmvMaxCampaignGetResponse response = gmvMaxApi.campaignGet(accessToken, req.getAdvertiserId(), req.getFiltering(), req.getPage(), req.getPageSize());
        log.info("gmv max api campaignGet response :{}", response);
        return response;
    }

    public GmvMaxCampaignInfo campaignInfo(String accessToken, String advertiserId, String campaignId) {
        GmvMaxCampaignInfo response = gmvMaxApi.campaignInfo(accessToken, advertiserId, campaignId);
        log.info("gmv max api campaignInfo response :{}", response);
        return response;
    }

    public GmvMaxCampaignReportResponse campaignReportGet(String accessToken, String advertiserId, List<String> storeIds,
                                                          String startDate, String endDate, List<String> metrics, List<String> dimensions,
                                                          FilteringGmvMaxCampaignReport filtering, String sortField, String sortType,
                                                          int page, int pageSize) {
        reportRateLimiter.acquire();
        GmvMaxCampaignReportResponse response = gmvMaxApi.campaignReportGet(accessToken, advertiserId, storeIds,
                startDate, endDate, metrics, dimensions, filtering, sortField, sortType, page, pageSize);
        log.info("gmv max api campaignReportGet response :{}", response);
        return response;
    }

}
