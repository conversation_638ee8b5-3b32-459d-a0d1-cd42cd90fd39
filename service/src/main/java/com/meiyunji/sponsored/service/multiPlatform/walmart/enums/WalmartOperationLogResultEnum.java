package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

import java.util.List;

@Getter
public enum WalmartOperationLogResultEnum {

    /**
     * 操作类型
     */
    SUCCESS("success", 0, new Integer[]{0}),
    FAIL("fail", 1, new Integer[]{1}),
    ALL("all", 2, new Integer[]{0,1})
    ;

    private String result;

    private Integer resultValue;

    private Integer[] queryValList;

    WalmartOperationLogResultEnum(String result, Integer resultValue, Integer[] queryValList) {
        this.result = result;
        this.resultValue = resultValue;
        this.queryValList = queryValList;

    }

    public static WalmartOperationLogResultEnum getEnumByResult(String result) {
        for (WalmartOperationLogResultEnum resultEnum : WalmartOperationLogResultEnum.values()) {
            if (resultEnum.getResult().equals(result)) {
                return resultEnum;
            }
        }
        return null;
    }

    public static WalmartOperationLogResultEnum getEnumByResultVal(String resultVal) {
        for (WalmartOperationLogResultEnum resultEnum : WalmartOperationLogResultEnum.values()) {
            if (resultEnum.getResultValue().equals(Integer.valueOf(resultVal))) {
                return resultEnum;
            }
        }
        return null;
    }
}
