package com.meiyunji.sponsored.service.cpc.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbTargetingReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdSbTargetingReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.ReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.cpc.vo.SumReportDataVo;
import com.meiyunji.sponsored.service.cpc.vo.export.SbQueryTargetExportVo;
import com.meiyunji.sponsored.service.enums.AdReportExportAdFormatEnum;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Service
public class AmazonAdSbTargetingReportServiceImpl implements IAmazonAdSbTargetingReportService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private IAmazonAdSbTargetingReportDao targetingReportDao;

    @Override
    public Page pageList(int puid, SearchVo search, Page page) {
        page = targetingReportDao.getPageList(puid,search,page);

        if (page != null && page.getRows().size() > 0) {
            List<AmazonAdSbTargetingReport> poList = page.getRows();
            if(poList!= null && poList.size()>0) {
                List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
                ReportDataVo vo;
                for (AmazonAdSbTargetingReport report : poList) {
                    vo = new ReportDataVo();
                    vo.setShopId(report.getShopId());
                    vo.setCampaignName(report.getCampaignName());
                    vo.setTargetId(report.getTargetId());
                    vo.setTargetingExpression(report.getTargetingExpression());
                    vo.setCost(report.getCost());
                    vo.setSales(report.getSales14d());
                    vo.setImpressions(report.getImpressions());
                    vo.setClicks(report.getClicks());
                    //attributedConversions14d  字段作为订单量更为准确
                    vo.setOrderNum(report.getConversions14d());
                    vo.setAcos(getAcos(report.getCost(),report.getSales14d()));
                    vo.setRoas(getRoas(report.getSales14d(),report.getCost()));
                    vo.setCpc(report.getCost() != null && report.getClicks() != null? calculationRateBigDecimal(report.getCost(),BigDecimal.valueOf(report.getClicks()),false):BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
                    vo.setClickRate(report.getClicks() != null && report.getImpressions() != null? calculationRateDouble(Double.valueOf(report.getClicks()),Double.valueOf(report.getImpressions())):0.00);
                    vo.setSalesConversionRate(getSalesConversionRate(report.getClicks(),report.getConversions14d()));

                    //广告组
                    vo.setAdGroupName(report.getAdGroupName());

                    //广告销量
                    vo.setViewAttributedConversions14d(report.getUnitsSold14d());

                    if(StringUtils.isNotBlank(report.getMatchType())){
                        if(Constants.PHRASE.equalsIgnoreCase(report.getMatchType())){
                            vo.setMatchType("词组匹配");
                        }else if(Constants.EXACT.equalsIgnoreCase(report.getMatchType())){
                            vo.setMatchType("精确匹配");
                        }else if(Constants.BROAD.equalsIgnoreCase(report.getMatchType())){
                            vo.setMatchType("广泛匹配");
                        } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(report.getMatchType())) {
                            vo.setMatchType("紧密匹配");
                        } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(report.getMatchType())) {
                            vo.setMatchType("宽泛匹配");
                        }
                    }
                    if (StringUtils.isNotBlank(report.getTargetingText())) {
                        if (report.getTargetingText().equals("substitutes")) {
                            vo.setTargetingText("同类产品");
                        } else if (report.getTargetingText().equals("close-match")) {
                            vo.setTargetingText("紧密匹配");
                        } else if (report.getTargetingText().equals("loose-match")) {
                            vo.setTargetingText("宽泛匹配");
                        } else if (report.getTargetingText().equals("complements")) {
                            vo.setTargetingText("关联产品");
                        } else if (report.getTargetingText().contains("category=")) {
                            String category = report.getTargetingText().substring(report.getTargetingText().indexOf("=")+1,
                                    report.getTargetingText().length()-1);
                            vo.setTargetingText(category.replace("\"",""));
                        } else if (report.getTargetingText().contains("asin=")) {
                            String asin = report.getTargetingText().substring(report.getTargetingText().indexOf("=")+1,
                                    report.getTargetingText().length()-1);
                            vo.setTargetingText(asin.replace("\"",""));
                        }
                    }
                    //新版报告下载中心
                    vo.setAttributedConversions14d(Optional.ofNullable(report.getConversions14d()).orElse(0));
                    vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getConversions14dSameSKU()).orElse(0));
                    if (report.getConversions14d() != null) {
                        if (report.getConversions14dSameSKU() != null) {
                            vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d() - report.getConversions14dSameSKU());
                        } else {
                            vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d());
                        }
                    }else {
                        vo.setAttributedConversions14dOtherSameSKU(0);
                    }
                    vo.setAttributedSales14d(Optional.ofNullable(report.getSales14d()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
                    vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getSales14dSameSKU()).orElse(BigDecimal.ZERO).setScale(2,BigDecimal.ROUND_HALF_UP));
                    if (report.getSales14d() != null) {
                        if (report.getSales14dSameSKU() != null) {
                            vo.setAttributedSales14dOtherSameSKU(report.getSales14d().subtract(report.getSales14dSameSKU()));
                        } else {
                            vo.setAttributedSales14dOtherSameSKU(report.getSales14d());
                        }
                    }else {
                        vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP));
                    }
                    vo.setAttributedOrderRateNewToBrand14d(Optional.ofNullable(report.getOrderRateNewToBrand14d()).orElse(0.00));
                    vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
                    vo.setAttributedOrdersNewToBrandPercentage14d(Optional.ofNullable(report.getOrdersNewToBrandPercentage14d()).orElse(0.00));
                    vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO.setScale(2,BigDecimal.ROUND_HALF_UP)));
                    vo.setAttributedSalesNewToBrandPercentage14d(Optional.ofNullable(report.getSalesNewToBrandPercentage14d()).orElse(0.00));
                    vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
                    vo.setAttributedUnitsOrderedNewToBrandPercentage14d(Optional.ofNullable(report.getUnitsOrderedNewToBrandPercentage14d()).orElse(0.00));

                    //sbv专属字段
                    if ("video".equals(search.getAdFormat())) {
                        vo.setVctr(Optional.ofNullable(report.getVctr()).orElse(0.00));
                        vo.setVideo5SecondViewRate(Optional.ofNullable(report.getVideo5SecondViewRate()).orElse(0.00));
                        vo.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0));
                        vo.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0));
                        vo.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0));
                        vo.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0));
                        vo.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0));
                        vo.setViewableImpressions(Optional.ofNullable(report.getViewableImpressions()).orElse(0));
                        vo.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0));
                        vo.setVtr(Optional.ofNullable(report.getVtr()).orElse(0.00));
                    }
                    if (StringUtils.isNotBlank(report.getCountDate())) {
                        vo.setCountDate(report.getCountDate());
                    }
                    list.add(vo);
                }
                page.setRows(list);
            }
        }
        return page;
    }

    public BigDecimal getAcos(BigDecimal cost,BigDecimal totalSales) {
        if(cost == null || totalSales == null){
            return null;
        }
        return calculationRateBigDecimal(cost,totalSales,true);
    }

    public Double getSalesConversionRate(Integer clicks,Integer orderNum) {
        if(clicks == null || orderNum == null){
            return null;
        }
        return calculationRateDouble(Double.valueOf(orderNum),Double.valueOf(clicks));
    }

    public BigDecimal getRoas(BigDecimal totalSales,BigDecimal cost) {
        if(totalSales == null || cost == null){
            return null;
        }
        return cost.compareTo(BigDecimal.ZERO)==0?BigDecimal.ZERO : totalSales.divide(cost,2, RoundingMode.HALF_UP);
    }

    private BigDecimal calculationRateBigDecimal(BigDecimal value1, BigDecimal value2,Boolean per) {
        BigDecimal rate = value2.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(value1,value2);
        if(per){
            rate = MathUtil.multiply(rate,BigDecimal.valueOf(100));
        }
        return rate.setScale(2,BigDecimal.ROUND_HALF_UP);
    }

    private Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1*100,value2,2);
    }

    @Override
    public Page detailPageList(int puid, ReportParam param, Page page) {
        param.setStartDate(DateUtil.dateToStrWithFormat(param.getStart(),"yyyyMMdd"));
        param.setEndDate(DateUtil.dateToStrWithFormat(param.getEnd(),"yyyyMMdd"));

        page = targetingReportDao.detailPageList(puid, param.getShopId(), param.getMarketplaceId(), param, page);

        List<AmazonAdSbTargetingReport> poList = page.getRows();
        if(poList!= null && poList.size()>0) {
            List<ReportDataVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            ReportDataVo vo;
            for (AmazonAdSbTargetingReport report : poList) {
                vo = new ReportDataVo();
                String countDate = report.getCountDate();
                if(StringUtils.isNotEmpty(report.getCountDate())){
                    countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMMdd"),"yyyy-MM-dd");
                    vo.setCountDate(countDate);
                }
                vo.setShopId(report.getShopId());
                vo.setCampaignName(report.getCampaignName());
                vo.setTargetId(report.getTargetId());
                vo.setTargetingText(report.getTargetingText());
                vo.setCost(report.getCost());
                vo.setSales(report.getSales14d());
                vo.setImpressions(report.getImpressions());
                vo.setClicks(report.getClicks());
                vo.setOrderNum(report.getConversions14d());
                vo.setAcos(vo.getAcos());
                vo.setCpc(vo.getCpc());
                vo.setClickRate(vo.getClickRate());
                vo.setSalesConversionRate(vo.getSalesConversionRate());
                list.add(vo);
            }
            page.setRows(list);
        }
        return page;
    }

    @Override
    public SumReportDataVo getSumReport(int puid, Integer shopId, String marketplaceId, ReportParam param) {
        SumReportDataVo vo = new SumReportDataVo();
        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(param.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(param.getEnd(),"yyyyMMdd");
        AmazonAdSbTargetingReport sumReport = targetingReportDao.getSumReportByTargetId(puid,shopId,marketplaceId,startStr,endStr,param.getTargetId());

        ReportDataVo dataVo = null;
        if (sumReport != null) {
            dataVo = new ReportDataVo();
            dataVo.setCost(sumReport.getCost());
            dataVo.setSales(sumReport.getSales14d());
            dataVo.setImpressions(sumReport.getImpressions());
            dataVo.setClicks(sumReport.getClicks());
            dataVo.setOrderNum(sumReport.getConversions14d());
            dataVo.setAcos(dataVo.getAcos());
            dataVo.setCpc(dataVo.getCpc());
            dataVo.setClickRate(dataVo.getClickRate());
            dataVo.setSalesConversionRate(dataVo.getSalesConversionRate());
        }
        vo.setReportVo(dataVo);
        //获取上个时间段的数据
        Map<String,String> map = getLastTime(param.getStart(),param.getEnd(),param.getLastMonth());
        AmazonAdSbTargetingReport lastSumReport = targetingReportDao.getSumReportByTargetId(puid,shopId,marketplaceId,map.get("startStr"),map.get("endStr"),param.getCampaignId());

        ReportDataVo lastDataVo = null;
        if (lastSumReport != null) {
            lastDataVo = new ReportDataVo();
            lastDataVo.setCost(lastSumReport.getCost());
            lastDataVo.setSales(lastSumReport.getSales14d());
            lastDataVo.setImpressions(lastSumReport.getImpressions());
            lastDataVo.setClicks(lastSumReport.getClicks());
            lastDataVo.setOrderNum(lastSumReport.getConversions14d());
            lastDataVo.setAcos(lastDataVo.getAcos());
            lastDataVo.setCpc(lastDataVo.getCpc());
            lastDataVo.setClickRate(lastDataVo.getClickRate());
            lastDataVo.setSalesConversionRate(lastDataVo.getSalesConversionRate());
        }
        vo.setLastReportVo(lastDataVo);
        vo.calculationGrow();
        return vo;
    }

    @Override
    public List<ReportDataVo> getChartList(int puid, Integer shopId, String marketplaceId, ReportParam param) {
        String startStr = DateUtil.dateToStrWithFormat(param.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(param.getEnd(),"yyyyMMdd");
        List<AmazonAdSbTargetingReport> poList = targetingReportDao.getChartList(puid,shopId,marketplaceId,startStr,endStr,param.getTargetId());

        List<ReportDataVo> list = null;
        if (poList != null && poList.size() > 0) {
            list = Lists.newArrayListWithExpectedSize(poList.size());
            ReportDataVo vo;
            for (AmazonAdSbTargetingReport report : poList) {
                vo = new ReportDataVo();
                String countDate = report.getCountDate();
                if(StringUtils.isNotEmpty(report.getCountDate())){
                    countDate = DateUtil.dateToStrWithFormat(DateUtil.strToDate(countDate,"yyyyMMdd"),"yyyy-MM-dd");
                    vo.setCountDate(countDate);
                }
                vo.setShopId(report.getShopId());
                vo.setCampaignName(report.getCampaignName());
                vo.setTargetId(report.getTargetId());
                vo.setTargetingText(report.getTargetingText());
                vo.setCost(report.getCost());
                vo.setSales(report.getSales14d());
                vo.setImpressions(report.getImpressions());
                vo.setClicks(report.getClicks());
                vo.setOrderNum(report.getConversions14d());
                vo.setAcos(vo.getAcos());
                vo.setCpc(vo.getCpc());
                vo.setClickRate(vo.getClickRate());
                vo.setSalesConversionRate(vo.getSalesConversionRate());
                list.add(vo);
            }
        }
        return list;
    }

    @Override
    public Map<String, String> getAdCampaignMap(int puid, Integer shopId, String marketplaceId) {
        List<Map<String, Object>> list =  targetingReportDao.getAdCampaignMap(puid, shopId, marketplaceId);
        return getItemIdAndNameMap(list);
    }

    /**
     * 获取汇总报告对比时间段
     * @param start
     * @param end
     * @param lastMonth
     * @return
     */
    private Map<String,String> getLastTime(Date start, Date end, Integer lastMonth){
        Map<String,String> map = Maps.newHashMapWithExpectedSize(2);
        //获取上个时间段的数据
        if(lastMonth!=null&&lastMonth == 1){ //获取上一个月的同时间段的数据
            map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addMonth(start,-1),"yyyyMMdd"));
            map.put("endStr",DateUtil.dateToStrWithFormat(DateUtil.addMonth(end,-1),"yyyyMMdd"));
        }else {
            //获取时间差
            int days = DateUtil.getDayBetween(start,end);
            end = DateUtil.addDay(start,-1);
            map.put("endStr",DateUtil.dateToStrWithFormat(end,"yyyyMMdd"));
            map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addDay(end,-days),"yyyyMMdd"));
        }
        return map;
    }

    private Map<String, String> getItemIdAndNameMap(List<Map<String, Object>> list) {
        if (list != null && list.size() > 0) {
            Map<String, String> map = Maps.newHashMapWithExpectedSize(list.size());
            list.forEach(temp -> {
                map.put(String.valueOf(temp.get("id")), String.valueOf(temp.get("name")));
            });
            return map;
        }
        return null;
    }


    @Override
    public ReportDataVo convertSbQueryTargetExportVo2ReportDataVo(SbQueryTargetExportVo report, String adFormat) {
        ReportDataVo vo = new ReportDataVo();
        vo.setAdFormat(report.getAdFormat());
        vo.setShopId(report.getShopId());
        vo.setCampaignName(report.getCampaignName());
        vo.setTargetId(report.getTargetId());
        vo.setTargetingExpression(report.getTargetingExpression());
        vo.setCost(report.getCost());
        vo.setSales(report.getSales14d());
        vo.setImpressions(report.getImpressions());
        vo.setClicks(report.getClicks());
        //attributedConversions14d  字段作为订单量更为准确
        vo.setOrderNum(report.getConversions14d());
        vo.setAcos(getAcos(report.getCost(), report.getSales14d()));
        vo.setRoas(getRoas(report.getSales14d(), report.getCost()));
        vo.setCpc(report.getCost() != null && report.getClicks() != null ? calculationRateBigDecimal(report.getCost(), BigDecimal.valueOf(report.getClicks()), false) : BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
        vo.setClickRate(report.getClicks() != null && report.getImpressions() != null ? calculationRateDouble(Double.valueOf(report.getClicks()), Double.valueOf(report.getImpressions())) : 0.00);
        vo.setSalesConversionRate(getSalesConversionRate(report.getClicks(), report.getConversions14d()));

        //广告组
        vo.setAdGroupName(report.getAdGroupName());

        //广告销量
        vo.setViewAttributedConversions14d(report.getUnitsSold14d());

        if (StringUtils.isNotBlank(report.getTargetingText())) {
            if (report.getTargetingText().contains("category=")) {
                String category = report.getTargetingText().substring(report.getTargetingText().indexOf("=") + 1, report.getTargetingText().length() - 1);
                vo.setTargetingText(category.replace("\"", ""));
                vo.setMatchType("类目定位");
            } else if (report.getTargetingText().contains("asin=")) {
                String asin = report.getTargetingText().substring(report.getTargetingText().indexOf("=") + 1, report.getTargetingText().length() - 1);
                vo.setTargetingText(asin.replace("\"", ""));
                vo.setMatchType("ASIN定位");
            }
        }
        //新版报告下载中心
        vo.setAttributedConversions14d(Optional.ofNullable(report.getConversions14d()).orElse(0));
        vo.setAttributedConversions14dSameSKU(Optional.ofNullable(report.getConversions14dSameSKU()).orElse(0));
        if (report.getConversions14d() != null) {
            if (report.getConversions14dSameSKU() != null) {
                vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d() - report.getConversions14dSameSKU());
            } else {
                vo.setAttributedConversions14dOtherSameSKU(report.getConversions14d());
            }
        } else {
            vo.setAttributedConversions14dOtherSameSKU(0);
        }
        vo.setAttributedSales14d(Optional.ofNullable(report.getSales14d()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
        vo.setAttributedSales14dSameSKU(Optional.ofNullable(report.getSales14dSameSKU()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
        if (report.getSales14d() != null) {
            if (report.getSales14dSameSKU() != null) {
                vo.setAttributedSales14dOtherSameSKU(report.getSales14d().subtract(report.getSales14dSameSKU()));
            } else {
                vo.setAttributedSales14dOtherSameSKU(report.getSales14d());
            }
        } else {
            vo.setAttributedSales14dOtherSameSKU(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP));
        }
        vo.setAttributedOrderRateNewToBrand14d(Optional.ofNullable(report.getOrderRateNewToBrand14d()).orElse(0.00));
        vo.setAttributedOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand14d()).orElse(0));
        vo.setAttributedOrdersNewToBrandPercentage14d(Optional.ofNullable(report.getOrdersNewToBrandPercentage14d()).orElse(0.00));
        vo.setAttributedSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand14d()).orElse(BigDecimal.ZERO.setScale(2, BigDecimal.ROUND_HALF_UP)));
        vo.setAttributedSalesNewToBrandPercentage14d(Optional.ofNullable(report.getSalesNewToBrandPercentage14d()).orElse(0.00));
        vo.setAttributedUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand14d()).orElse(0));
        vo.setAttributedUnitsOrderedNewToBrandPercentage14d(Optional.ofNullable(report.getUnitsOrderedNewToBrandPercentage14d()).orElse(0.00));

        //sbv专属字段
        if (AdReportExportAdFormatEnum.video.getAdFormat().equals(adFormat)) {
            vo.setVctr(Optional.ofNullable(report.getVctr()).orElse(0.00));
            vo.setVideo5SecondViewRate(Optional.ofNullable(report.getVideo5SecondViewRate()).orElse(0.00));
            vo.setVideo5SecondViews(Optional.ofNullable(report.getVideo5SecondViews()).orElse(0));
            vo.setVideoFirstQuartileViews(Optional.ofNullable(report.getVideoFirstQuartileViews()).orElse(0));
            vo.setVideoMidpointViews(Optional.ofNullable(report.getVideoMidpointViews()).orElse(0));
            vo.setVideoThirdQuartileViews(Optional.ofNullable(report.getVideoThirdQuartileViews()).orElse(0));
            vo.setVideoUnmutes(Optional.ofNullable(report.getVideoUnmutes()).orElse(0));
            vo.setViewableImpressions(Optional.ofNullable(report.getViewableImpressions()).orElse(0));
            vo.setVideoCompleteViews(Optional.ofNullable(report.getVideoCompleteViews()).orElse(0));
            vo.setVtr(Optional.ofNullable(report.getVtr()).orElse(0.00));
        }
        if (StringUtils.isNotBlank(report.getCountDate())) {
            vo.setCountDate(report.getCountDate());
        }

        return vo;
    }
}
