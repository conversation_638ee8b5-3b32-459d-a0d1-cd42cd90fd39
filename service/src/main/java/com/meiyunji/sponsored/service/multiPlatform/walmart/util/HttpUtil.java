package com.meiyunji.sponsored.service.multiPlatform.walmart.util;


import com.google.common.collect.Maps;
import com.meiyunji.sponsored.service.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.http.HttpHost;
import org.apache.http.client.methods.*;



import java.io.*;


@Slf4j
public class HttpUtil {


	private static final int TIME_OUT_WAIT = 1500;
	private static final HttpUtil httpUtil = new HttpUtil();

	private static final OkHttpClient client = new OkHttpClient().newBuilder().build();
	private static final MediaType JSON = MediaType.parse("application/json");

	public static HttpUtil getInstance() {
		return httpUtil;
	}


	/**
	 * 获取wish的代理
	 * @return HttpHost
	 */
	private HttpHost getWalmartProxy(){
		HttpHost proxy = null;
		String useProxy = SpringContextUtil.getProperty("walmartProxy.switch");
		if("true".equals(useProxy)){
			String wishProxyHost = SpringContextUtil.getProperty("walmartProxy.proxyHost");
			int wishProxyPort = Integer.valueOf(SpringContextUtil.getProperty("walmartProxy.proxyPort"));
			proxy = new HttpHost(wishProxyHost, wishProxyPort, "http");
		}
		return proxy;
	}


	public InputStream getInputStream(HttpGet gm, String url){
		log.info("get() begin...  url:" + url);
		try {
			HttpUtilFinal http = HttpUtilFinal.getInstance();
			// 是否启用代理
			HttpHost proxy = getWalmartProxy();
			log.info("get() end...  url:" + url);
			return http.doGetForStream(gm, url, Maps.newHashMap(), proxy,null);
		} catch (Exception e) {
			log.error("get::" + url, e);
		}
		return null;
	}




}