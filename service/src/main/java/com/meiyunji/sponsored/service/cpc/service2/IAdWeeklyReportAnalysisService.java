package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sellfox.ams.api.service.AdReportWeeklyRequestPb;
import com.meiyunji.sellfox.ams.api.service.AdReportWeeklyResponsePb;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.grpc.common.AdWeekReportResponsePb;
import com.meiyunji.sponsored.grpc.common.HourlyWeeklySuperpositionCommonReq;
import com.meiyunji.sponsored.grpc.common.HourlyWeeklySuperpositionTargetReq;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.CommonHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.reportHour.vo.AdReportWeeklyDayVO;

import java.util.List;
import java.util.function.Function;

/**
 * @author: ys
 * @date: 2023/12/26 20:27
 * @describe:
 */
public interface IAdWeeklyReportAnalysisService {
    AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getSuperpositionReport(AdHourReportRequest param, Function<AdReportWeeklyRequestPb.AdReportWeeklyRequest,
            AdReportWeeklyResponsePb.AdReportWeeklyResponse> weeklyFunc);

    AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getSuperpositionReportFromDoris(AdHourReportRequest param, Function<CampaignHourlyReportSelectDto,
            List<AmazonMarketingStreamData>> weeklyFunc);

    List<AdReportWeeklyDayVO> getSuperpositionList(AdHourReportRequest param, Function<AdReportWeeklyRequestPb.AdReportWeeklyRequest,
            AdReportWeeklyResponsePb.AdReportWeeklyResponse> weeklyFunc);

    /**
     * 通用查询广告活动、广告组合、投放的周叠加数据
     * @param param param
     * @return AdWeek
     */
    AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getCommonWeeklySuperpositionReport(HourlyWeeklySuperpositionCommonReq param);

    AdWeekReportResponsePb.AdWeekReportResponse.AdWeek getTargetWeeklySuperpositionReport(HourlyWeeklySuperpositionTargetReq param);

    /**
     * 通用导出周叠加数据
     * @param param param
     * @return downloadUrl
     */
    String exportCommonWeekData(HourlyWeeklySuperpositionCommonReq param);

    /**
     * 多店铺投放导出周叠加数据
     * @param param param
     * @return downloadUrl
     */
    String exportTargetWeekData(HourlyWeeklySuperpositionTargetReq param);
}
