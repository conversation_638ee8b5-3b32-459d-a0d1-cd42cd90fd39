package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.permission.annotation.AdProductPermissionFilter;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdCampaignOrGroupOrPortfolioResponseVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdCampaignAllBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.CampaignOrGroupOrPortfolioDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdCampaignService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardCampaignOrGroupOrPortfolioReqVo;
import com.meiyunji.sponsored.service.util.SummaryReportUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: ys
 * @date: 2024/4/9 15:52
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdCampaignServiceImpl implements IDashboardAdCampaignService {

    @Autowired
    private IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;

    @Autowired
    private IExcelService excelService;

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IVcShopAuthDao vcShopAuthDao;

    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#reqVo.shopIdList",
            strategy = PermissionFilterStrategy.FILTER
    )
    public DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page queryCampaignCharts(DashboardCampaignOrGroupOrPortfolioReqVo req) {
        List<CampaignOrGroupOrPortfolioDto> resultList = getCampaignTopList(req);
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("get top campaign chars data list is null, queryField:{}, dataField:{}", req.getQueryField(), req.getDataField());
            return null;
        }
        List<Integer> allIdByPuid = vcShopAuthDao.getAllIdByPuid(req.getPuid());
        boolean isVc = CollectionUtils.isNotEmpty(allIdByPuid);
        //组装数据返回
        List<DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList> topList = resultList.stream().map(d -> {
            DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList.Builder vo =
                    DashboardAdCampaignOrGroupOrPortfolioResponseVo.Page.TopList.newBuilder();
            BeanUtils.copyProperties(d, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(d));
            vo.setCost(CalculateUtil.formatDecimal(d.getCost()));
            Optional.ofNullable(d.getShopId()).map(Integer::valueOf).ifPresent(vo::setShopId);
            Optional.ofNullable(d.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(d.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(d.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(d.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(d.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(d.getAcos()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(d.getRoas()).map(String::valueOf).ifPresent(vo::setRoas);
            Optional.ofNullable(d.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(d.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(d.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(d.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);
            Optional.ofNullable(d.getCostPercent()).ifPresent(vo::setCostPercent);
            Optional.ofNullable(d.getTotalSalesPercent()).ifPresent(vo::setTotalSalesPercent);
            Optional.ofNullable(d.getImpressionsPercent()).ifPresent(vo::setImpressionsPercent);
            Optional.ofNullable(d.getClicksPercent()).ifPresent(vo::setClicksPercent);
            Optional.ofNullable(d.getOrderNumPercent()).ifPresent(vo::setOrderNumPercent);
            Optional.ofNullable(d.getSaleNumPercent()).ifPresent(vo::setSaleNumPercent);
            return vo.build();
        }).collect(Collectors.toList());
        return PageUtils.getPageInfo(topList, req.getPageSize(), req.getPageNo());
    }

    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#reqVo.shopIdList",
            strategy = PermissionFilterStrategy.FILTER
    )
    public List<String> exportCampaignCharts(DashboardCampaignOrGroupOrPortfolioReqVo reqVo) {
        List<CampaignOrGroupOrPortfolioDto> campaignList = getCampaignTopList(reqVo);
        String url = writeExcelAndUpload(campaignList, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export campaign charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private List<CampaignOrGroupOrPortfolioDto> getCampaignTopList(DashboardCampaignOrGroupOrPortfolioReqVo req) {
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        List<String> portfolioIds = req.getPortfolioIds();
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        //查询当前日期时间段内广告活动图表信息
        List<Object> argsList = Lists.newArrayList();
        //当前时间段查询sql
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        String subSqlA = odsAmazonAdCampaignAllReportDao.campaignQuerySql(puid, shopIdList, marketplaceIdList, req.getCampaignIds(), currency, req.getStartDate(),
                req.getEndDate(), argsList, siteToday, req.getSiteToday(), portfolioIds, req.getNoZero(), dataField);
        String subSqlB;
        List<DashboardAdTopDataDto> currentAndSubList;
        List<CampaignOrGroupOrPortfolioDto> resultList = Lists.newArrayList();
        //如果是按占比排序，即当前时间段值/汇总值,先计算同比,或按同比排序，即当前时间段值/同比时间段值
        if (DashboardOrderByRateEnum.PERCENT == orderField || Stream.of(DashboardOrderByRateEnum.YOY_RATE,
                DashboardOrderByRateEnum.YOY_VALUE).anyMatch(r -> r == orderField)) {
            //同比sql
            subSqlB = odsAmazonAdCampaignAllReportDao
                    .campaignQuerySql(puid, shopIdList, marketplaceIdList, req.getCampaignIds(), currency, req.getYoyStartDate(),
                            req.getYoyEndDate(), argsList, null, null, portfolioIds, null, null);
            currentAndSubList = odsAmazonAdCampaignAllReportDao.queryAdCampaignYoyOrMomTop(subSqlA, subSqlB,
                    argsList, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
            List<String> campaignIdList = currentAndSubList.parallelStream().map(DashboardAdTopDataDto::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                //还需要查询环比
                List<CampaignOrGroupOrPortfolioDto> momCamapignList = odsAmazonAdCampaignAllReportDao.
                        queryAdCampaignCharts(puid, shopIdList, marketplaceIdList, campaignIdList, currency,
                                req.getMomStartDate(), req.getMomEndDate());
                Map<String, CampaignOrGroupOrPortfolioDto> momCampaignMap = momCamapignList.parallelStream()
                        .collect(Collectors.toMap(CampaignOrGroupOrPortfolioDto::getCampaignId, v1 -> v1, (old, current) -> current));
                resultList = currentAndSubList.stream().map(c -> {
                    //当前查询list已经包含了当期及同比的计算属性和汇总属性
                    //还需要计算环比占比，同比占比
                    CampaignOrGroupOrPortfolioDto momCampaign = Optional.ofNullable(momCampaignMap.get(c.getCampaignId())).orElseGet(() -> {
                        CampaignOrGroupOrPortfolioDto tempDto = new CampaignOrGroupOrPortfolioDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    CampaignOrGroupOrPortfolioDto currentDto = new CampaignOrGroupOrPortfolioDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    CampaignOrGroupOrPortfolioDto yoyDto = convertBasicAndCalData(c);
                    CampaignOrGroupOrPortfolioDto summaryDto = convertSummaryData(c);
                    computeCampaignData(currentDto, yoyDto, momCampaign, summaryDto, req.getYoyOverLimit());
                    return currentDto;
                }).collect(Collectors.toList());
            }
        }

        //如果是按环比排序，即当前时间段值/环比时间段值
        if (Stream.of(DashboardOrderByRateEnum.MOM_RATE,
                DashboardOrderByRateEnum.MOM_VALUE).anyMatch(r -> r == orderField)) {
            subSqlB = odsAmazonAdCampaignAllReportDao
                    .campaignQuerySql(puid, shopIdList, marketplaceIdList, req.getCampaignIds(), currency, req.getMomStartDate(),
                            req.getMomEndDate(), argsList,  null, null, portfolioIds, null, null);
            currentAndSubList = odsAmazonAdCampaignAllReportDao.queryAdCampaignYoyOrMomTop(subSqlA, subSqlB,
                    argsList, dataField, orderField, req.getOrderBy(), limit, req.getNoZero());
            List<String> campaignIdList = currentAndSubList.parallelStream().map(DashboardAdTopDataDto::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(campaignIdList)) {

                //还需要查同比
                List<CampaignOrGroupOrPortfolioDto> yoyList = odsAmazonAdCampaignAllReportDao.
                        queryAdCampaignCharts(puid, shopIdList, marketplaceIdList, campaignIdList, currency,
                                req.getYoyStartDate(), req.getYoyEndDate());
                Map<String, CampaignOrGroupOrPortfolioDto> yoyMap = yoyList.parallelStream()
                        .collect(Collectors.toMap(CampaignOrGroupOrPortfolioDto::getCampaignId, v1 -> v1, (old, current) -> current));

                resultList = currentAndSubList.stream().map(c -> {
                    CampaignOrGroupOrPortfolioDto yoyCampaign = Optional.ofNullable(yoyMap.get(c.getCampaignId())).orElseGet(() -> {
                        CampaignOrGroupOrPortfolioDto tempDto = new CampaignOrGroupOrPortfolioDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    CampaignOrGroupOrPortfolioDto currentDto = new CampaignOrGroupOrPortfolioDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    CampaignOrGroupOrPortfolioDto monDto = convertBasicAndCalData(c);
                    CampaignOrGroupOrPortfolioDto summaryDto = convertSummaryData(c);
                    computeCampaignData(currentDto, yoyCampaign, monDto, summaryDto, req.getYoyOverLimit());
                    return currentDto;
                }).collect(Collectors.toList());
            }
        }

        //填充广告活动名称,店铺名称，站点名称
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        Set<String> campaignIdSet = resultList.parallelStream().map(CampaignOrGroupOrPortfolioDto::getCampaignId).collect(Collectors.toSet());
        List<AmazonAdCampaignAllBo> campaignInfoList = amazonAdCampaignAllDao.listBoByCampaignIds(req.getPuid(), req.getShopIdList(), new ArrayList<>(campaignIdSet));
        if (CollectionUtils.isEmpty(campaignInfoList)) {
            return Collections.emptyList();
        }
        Map<String, AmazonAdCampaignAllBo> campaignMap = campaignInfoList.parallelStream().collect(Collectors.toMap(AmazonAdCampaignAllBo::getCampaignId, v1 -> v1, (oldVal, newVal) -> newVal));
        Set<Integer> shopIdSet = campaignInfoList.parallelStream().map(AmazonAdCampaignAllBo::getShopId).collect(Collectors.toSet());

        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIdSet));
        Map<Integer, String> shopNameMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, ShopAuth::getName));
        resultList.forEach(c -> {
            AmazonAdCampaignAllBo campaignInfo = campaignMap.get(c.getCampaignId());
            if (Objects.nonNull(campaignInfo)) {
                Optional.ofNullable(campaignInfo.getName()).ifPresent(c::setCampaignName);
                Optional.ofNullable(campaignInfo.getShopId()).map(String::valueOf).ifPresent(c::setShopId);
                Optional.ofNullable(campaignInfo.getType()).map(String::valueOf).ifPresent(c::setType);
                Optional.ofNullable(shopNameMap.get(campaignInfo.getShopId())).ifPresent(c::setShopName);
                Optional.ofNullable(SummaryReportUtil.getByMarketplaceId(campaignInfo.getMarketplaceId()))
                        .map(SummaryReportUtil::getMarketplaceCN).ifPresent(c::setMarketplaceName);
            }
        });
        //是否需要排序
        if (StringUtils.isNotBlank(req.getListOrderField()) || StringUtils.isNotBlank(req.getListOrderType())) {
            OrderByUtil.sortedByOrderField(resultList, req.getListOrderField(), req.getListOrderType(), "campaignId");
        }
        return resultList;
    }

    private String writeExcelAndUpload(List<CampaignOrGroupOrPortfolioDto> campaignList, DashboardCampaignOrGroupOrPortfolioReqVo reqVo) {
        campaignList.forEach(dataDto -> CalculateAdDataUtil.fillDisplay4Export(dataDto, reqVo.getCurrency()));
        List<String> headers = new ArrayList<>(baseHeaderList);
        //计算导出列
        if (reqVo.getPercent() != 1) {
            headers = headers.stream().filter(x -> !x.contains(DashboardOrderByRateEnum.PERCENT.getSuffix())).collect(Collectors.toList());
        }
        if (reqVo.getMom() != 1) {
            headers = headers.stream().filter(x -> !x.contains(DashboardOrderByRateEnum.MOM_RATE.getSuffix())).collect(Collectors.toList());
        }
        if (reqVo.getYoy() != 1) {
            headers = headers.stream().filter(x -> !x.contains(DashboardOrderByRateEnum.YOY_RATE.getSuffix())).collect(Collectors.toList());
        }

        String orderBy = Optional.ofNullable(DashboardOrderByEnum.orderByMap.get(reqVo.getOrderBy()))
                .map(DashboardOrderByEnum::getExportDesc).orElse("");
        String limitCnt = Optional.ofNullable(reqVo.getLimit()).map(String::valueOf).orElse("");
        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), campaignList, fileName + orderBy + limitCnt,
                CampaignOrGroupOrPortfolioDto.class, headers, true);

    }

    private List<String> baseHeaderList = Arrays.asList("campaignName", "marketplaceName", "shopName",
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate", "displayRoas", "roasMomRate", "roasYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");

    private static final String fileName = "广告活动";

    private CampaignOrGroupOrPortfolioDto convertBasicAndCalData(DashboardAdTopDataDto subInfo) {
        CampaignOrGroupOrPortfolioDto dto = new CampaignOrGroupOrPortfolioDto();
        dto.setCost(subInfo.getSubCost());
        dto.setTotalSales(subInfo.getSubTotalSales());
        dto.setImpressions(subInfo.getSubImpressions());
        dto.setClicks(subInfo.getSubClicks());
        dto.setOrderNum(subInfo.getSubOrderNum());
        dto.setSaleNum(subInfo.getSubSaleNum());
        dto.setAcos(subInfo.getSubAcos());
        dto.setRoas(subInfo.getSubRoas());
        dto.setClickRate(subInfo.getSubClickRate());
        dto.setConversionRate(subInfo.getSubConversionRate());
        dto.setCpc(subInfo.getSubCpc());
        dto.setCpa(subInfo.getSubCpa());
        return dto;
    }

    private CampaignOrGroupOrPortfolioDto convertSummaryData(DashboardAdTopDataDto subInfo) {
        CampaignOrGroupOrPortfolioDto dto = new CampaignOrGroupOrPortfolioDto();
        dto.setCost(subInfo.getAllCost());
        dto.setTotalSales(subInfo.getAllTotalSales());
        Optional.ofNullable(subInfo.getAllImpressions()).map(BigDecimal::longValue).ifPresent(dto::setImpressions);
        Optional.ofNullable(subInfo.getAllClicks()).map(BigDecimal::intValue).ifPresent(dto::setClicks);
        Optional.ofNullable(subInfo.getAllOrderNum()).map(BigDecimal::intValue).ifPresent(dto::setOrderNum);
        Optional.ofNullable(subInfo.getAllSaleNum()).map(BigDecimal::intValue).ifPresent(dto::setSaleNum);
        return dto;
    }

    private void computeCampaignData(CampaignOrGroupOrPortfolioDto dto, CampaignOrGroupOrPortfolioDto yoyDto,
                                     CampaignOrGroupOrPortfolioDto momDto, CampaignOrGroupOrPortfolioDto summary,
                                     boolean yoyOverLimit) {
        if (Objects.isNull(dto)) {
            return;
        }
        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
        } else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalData(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflex(dto, yoyDto);//填充同比增长值
            CalculateAdDataUtil.calAdYoyDataReflex(dto, yoyDto);//填充同比增长率
        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdCalData(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomData(dto, momDto);//填充环比增长率
            CalculateAdDataUtil.calAdMomValueReflex(dto, momDto);//填充环比增长值
        }
        CalculateAdDataUtil.calAdPercentData(dto, summary);
    }
}
