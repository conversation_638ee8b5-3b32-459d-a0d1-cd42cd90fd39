package com.meiyunji.sponsored.service.multiple.targets.enums;

import com.meiyunji.sponsored.service.multiple.targets.dto.TargetExportDto;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 多店铺投放层级导出枚举
 * <AUTHOR>
 * @see TargetExportDto
 */
@Getter
public enum TargetExportFieldEnum {
    STATE("enable", "state", "有效状态", false),
    SHOP_NAME("shopName", "shopName","店铺名称", false),
    POSITIONING("positioning","target", "定位", false),
    SELECT_TYPE("selectType", "selectType","筛选条件", false),
    KEYWORD_TEXT("keywordText", "keywordText", "关键词", false),
    KEYWORD_TEXT_CN("keywordTextCn", "keywordTextCn", "关键词翻译", false),
    MATCH_TYPE("matchType", "matchType", "匹配类型", false),
    SEARCH_FREQUENCY_RANK("searchFrequencyRank","searchFrequencyRank", "ABA搜索词排名", false),
    WEEK_RATIO("weekRatio", "weekRatio", "排名周变化率", false),
    CAMPAIGN_STATE("campaignState", "servingStatusName", "广告活动状态", false),
    TYPE("type", "type", "推广类型", false),
    TARGETING_TYPE("TargetingType", "campaignTargetingType", "投放类型", false),
    PORTFOLIO_NAME("portfolioName", "portfolioName","广告组合", false),
    GROUP_NAME("groupName", "advertisingGroup", "所属广告组", false),
    SUGGEST_BID("suggestBid", "suggestBid", "建议竞价", true),
    SUGGEST_BID_RANGE("suggestBidRange", "suggestBidScope", "建议竞价范围", true),
    CAMPAIGN_NAME("campaignName", "advertisingActivities", "所属广告活动", false),
    BID("bid", "bid", "竞价", true),
    AD_COST("adCost", "adCost", "广告花费", true),
    TOP_IMPRESSION_SHARE("topImpressionShare", "topImpressionShare", "搜索结果首页首位IS", false),
    AD_COST_PER_CLICK("adCostPerClick", "adCostPerClick", "CPC", true),
    ACOS("acos", "acos", "ACoS", false),
    ASOTS("asots", "asots", "ASoTS", false),
    AD_ORDER_NUM_PERCENTAGE("adOrderNumPercentage", "adOrderNumPercentage", "广告订单量占比", false),
    AD_SALE("adSale", "adSale", "广告销售额", true),
    AD_OTHER_SALES("adOtherSales", "adOtherSales", "其他产品广告销售额", true),
    AD_SELF_SALE_NUM("adSelfSaleNum", "adSelfSaleNum", "本广告产品销量", false),
    AD_COST_PERCENTAGE("adCostPercentage","adCostPercentage", "广告花费占比", false),
    CLICKS("clicks", "clicks","广告点击量", false),
    CTR("ctr", "ctr","广告点击率", false),
    ROAS("roas", "roas", "ROAS", false),
    ADVERTISING_UNIT_PRICE("advertisingUnitPrice","advertisingUnitPrice", "广告笔单价", true),
    SELF_AD_ORDER_NUM("selfAdOrderNum","adSaleNum", "本广告产品订单量", false),
    AD_SALE_PERCENTAGE("adSalePercentage", "adSalePercentage","广告销售额占比", false),
    AD_SALE_NUM("adSaleNum","orderNum", "广告销量", false),
    AD_OTHER_SALE_NUM("adOtherSaleNum", "adOtherSaleNum","其他产品广告销量", false),
    IMPRESSIONS("impressions", "impressions", "广告曝光量", false),
    CPA("cpa", "cpa", "CPA", true),
    CVR("cvr", "cvr", "广告转化率", false),
    ACOTS("acots", "acots", "ACoTS", false),
    AD_ORDER_NUM("adOrderNum", "adOrderNum", "广告订单量", false),
    OTHER_AD_ORDER_NUM("otherAdOrderNum", "adOtherOrderNum", "其他产品广告订单量", false),
    AD_SELF_SALE("adSelfSale", "adSales", "本广告产品销售额", true),
    AD_SALE_NUM_PERCENTAGE("adSaleNumPercentage", "orderNumPercentage", "广告销量占比", false),
    VCPM("vcpm", "vcpm","VCPM", false),
    ORDER_RATE_NEW_TO_BRAND_FTD("orderRateNewToBrandFTD", "orderRateNewToBrandFTD","“品牌新买家”订单百分比", false),
    ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD("ordersNewToBrandPercentageFTD","ordersNewToBrandPercentageFTD", "“品牌新买家”订单转化率", false),
    ORDERS_NEW_TO_BRAND_FTD("ordersNewToBrandFTD", "ordersNewToBrandFTD","“品牌新买家”订单量", false),
    SALES_NEW_TO_BRAND_FTD("salesNewToBrandFTD", "salesNewToBrandFTD","“品牌新买家”销售额", true),
    SALES_RATE_NEW_TO_BRAND_FTD("salesRateNewToBrandFTD", "salesRateNewToBrandFTD","“品牌新买家”销售额百分比", false),
    VIDEO_5_SECOND_VIEWS("video5SecondViews", "video5SecondViews","5秒观看次数", false),
    VIDEO_MIDPOINT_VIEWS("videoMidpointViews", "videoMidpointViews","视频播至1/2次数", false),
    VIDEO_UNMUTES("videoUnmutes", "videoUnmutes","视频取消静音", false),
    BRANDED_SEARCHES("brandedSearches", "brandedSearches","品牌搜索次数", false),
    VIDEO_5_SECOND_VIEW_RATE("video5SecondViewRate", "video5SecondViewRate","5秒观看率", false),
    VIDEO_THIRD_QUARTILE_VIEWS("videoThirdQuartileViews","videoThirdQuartileViews", "视频播至3/4次数", false),
    VIEWABILITY_RATE("viewabilityRate", "viewabilityRate", "观看率", false),
    VIDEO_FIRST_QUARTILE_VIEWS("videoFirstQuartileViews","videoFirstQuartileViews", "视频播至1/4次数", false),
    VIDEO_COMPLETE_VIEWS("videoCompleteViews", "videoCompleteViews","视频完整播放次数", false),
    VIEW_CLICK_THROUGH_RATE("viewClickThroughRate", "viewClickThroughRate", "观看点击率", false),
    DETAIL_PAGE_VIEWS("detailPageViews","detailPageViews", "DPV", false),
    NEW_TO_BRAND_DETAIL_PAGE_VIEWS("newToBrandDetailPageViews", "newToBrandDetailPageViews","“品牌新买家”观看量", false),
    ADD_TO_CART("addToCart","addToCart", "加购次数", false),
    ADD_TO_CART_RATE("addToCartRate","addToCartRate", "加购率", false),
    VIEW_IMPRESSIONS("viewImpressions", "viewImpressions", "可见展示次数", false),
    ECP_ADD_TO_CART("eCPAddToCart", "ecpAddToCart","单次加购花费", true),
    AD_TAGS("adTags", "adTag","标签", false),
    AD_STRATEGY_TAG("adStrategyTag", "adStrategyTag","广告策略类型标签", false),
    ;

    // Static variable to hold the list of poParamKey values
    private static final List<String> PO_PARAM_KEY_LIST = Collections.unmodifiableList(
            Arrays.stream(values())
                    .map(TargetExportFieldEnum::getPoParamKey)
                    .collect(Collectors.toList())
    );
    /**
     * 后端po属性名
     */
    private final String poParamKey;
    private final String voName;
    private final String tableColName;
    private final boolean currencyStyle;

    TargetExportFieldEnum(String poParamKey, String voName, String tableColName, boolean currencyStyle) {
        this.poParamKey = poParamKey;
        this.voName = voName;
        this.tableColName = tableColName;
        this.currencyStyle = currencyStyle;
    }

    // Method to get the list of poParamKey values
    public static List<String> getPoParamKeyList() {
        return PO_PARAM_KEY_LIST;
    }

    // Static method to get enum by poParamKey, returns null if not found
    public static TargetExportFieldEnum fromPoParamKey(String key) {
        for (TargetExportFieldEnum value : values()) {
            if (value.poParamKey.equals(key)) {
                return value;
            }
        }
        return null; // Return null if no match is found
    }

}
