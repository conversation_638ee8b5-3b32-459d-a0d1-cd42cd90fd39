package com.meiyunji.sponsored.service.adProductRight.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISellfoxRoleUserDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adProductRight.request.*;
import com.meiyunji.sponsored.service.adProductRight.response.*;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.adProductRight.vo.*;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonIAdProductPermissionDao;
import com.meiyunji.sponsored.service.cpc.manager.DataDomainManager;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductPermission;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProductPermission;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductPermissionDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.service.impl.DorisServiceImpl;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.post.response.GetUserInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 广告产品权限service
 *
 * @Author: heqiwen
 * @Date: 2025/05/22 13:56
 */
@Service
@Slf4j
public class AdProductRightService implements IAdProductRightService {

    @Autowired
    private IAmazonIAdProductPermissionDao amazonIAdProductPermissionDao;
    @Autowired
    private IOdsAmazonAdProductPermissionDao odsAmazonAdProductPermissionDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private DorisServiceImpl dorisServiceImpl;
    @Autowired
    private IScVcShopAuthDao scVcShopAuthDao;
    @Autowired
    private ISellfoxRoleUserDao sellfoxRoleUserDao;
    @Autowired
    private IOdsAmazonAdCampaignAllDao odsAmazonAdCampaignAllDao;
    @Autowired
    private DataDomainManager dataDomainManager;
    @Autowired
    private IOdsAmazonAdPortfolioDao odsAmazonAdPortfolioDao;

    @Override
    public List<ShopSitesResponse> getShopSiteList(int puid, Boolean isAdd, List<Integer> shopIdList) {
        List<ShopSitesResponse> shopSites = new ArrayList<>();
        try {
            // 查询该puid下所配置广告产品权限的ASIN所在的站点/店铺
            List<Integer> shopIds = new ArrayList<>();
            List<ShopAuth> shopAuths = new ArrayList<>();
            if (Boolean.FALSE.equals(isAdd)) {
                List<ShopSiteVo> shopSiteVos = amazonIAdProductPermissionDao.getAllShopSiteByPuid(puid);
                shopIds = shopSiteVos.stream().map(ShopSiteVo::getShopId).distinct().collect(Collectors.toList());
                if (CollectionUtils.isEmpty(shopIds)) {
                    return shopSites;
                }
                shopAuths = scVcShopAuthDao.getAuthShopByShopIdList(puid, shopIds);
            } else {
                // 添加页查该用户下的所有店铺
                shopAuths = scVcShopAuthDao.getAuthShopByShopIdList(puid, shopIdList);
            }
            // 查询店铺的相关数据

            Map<String, List<ShopAuth>> shopAuthMap = StreamUtil.groupingBy(shopAuths, ShopAuth::getMarketplaceId);
            // sc店铺
            for (Map.Entry<String, List<ShopAuth>> entry : shopAuthMap.entrySet()) {
                ShopSitesResponse shopSite = new ShopSitesResponse();
                String marketplaceId = entry.getKey();
                AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
                List<ShopAuth> shopAuth = entry.getValue();
                shopSite.setMarketplaceId(marketplaceId);
                shopSite.setMarketplaceCN(amznEndpoint.getMarketplaceCN());
                shopSite.setRegion(shopAuth.get(0).getRegion());
                shopSite.setMarketplace(amznEndpoint.getMarketplace().toLowerCase());
                List<ShopVo> shopVos = new ArrayList<>();
                shopAuth.forEach(shop -> {
                    ShopVo shopVo = new ShopVo();
                    shopVo.setShopId(shop.getId());
                    shopVo.setShopName(shop.getName());
                    shopVo.setType(shop.getType());
                    shopVos.add(shopVo);
                });
                shopSite.setShopVos(shopVos);
                shopSites.add(shopSite);
            }
        } catch (Exception e) {
            log.error("产品权限列表站点店铺下拉查询异常：puid:{}",puid, e);
        }
        return shopSites;
    }

    @Override
    public List<GetUserInfoResponse.UserInfo> getUserList(int puid, UserListRequest request) {
        List<GetUserInfoResponse.UserInfo> userInfos = new ArrayList<>();
        try {
            // 查询该puid下所配置广告产品权限的ASIN所属的账号
            List<Integer> uids = amazonIAdProductPermissionDao.getAllUser(puid, request.getShopIdList(), request.getMarketplaceIdList());
            // 补充账号名称 过滤主账号的账号
            userInfos = userDao.getUserInfoByIds(puid, new HashSet<>(uids));
        } catch (Exception e) {
            log.error("产品权限列表账号下拉查询异常：puid:{}",puid, e);
        }
        return userInfos;
    }

    @Override
    public List<GetUserInfoResponse.UserInfo> getUser(int puid) {
        List<GetUserInfoResponse.UserInfo> userInfos = new ArrayList<>();
        try {
            // 查询该puid下非主账号且过滤已禁用和已归档的用户
            userInfos = userDao.getUserInfoFilterStatus(puid, null);
            // 查询子管理员账号
            List<Integer> adminUserIdList = sellfoxRoleUserDao.getAdminUserIdList(puid);
            // 过滤子管理员的账号
            userInfos = userInfos.stream().filter(i -> !adminUserIdList.contains(i.getUid())).distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("产品权限列表账号下拉查询异常：puid:{}",puid, e);
        }
        return userInfos;
    }

    @Override
    public GetRightListResponse pageList(int puid, GetRightListRequest request) {
        GetRightListResponse response = new GetRightListResponse();
        try {
            // 根据筛选分页查询基础数据
            Page<GetRightListResponse.DataVo> pageRightVo = odsAmazonAdProductPermissionDao.pageList(puid, request);
            List<GetRightListResponse.DataVo> rows = pageRightVo.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                return response;
            }
            Set<Integer> shopIds = new HashSet<>();
            Set<Integer> uids = new HashSet<>();
            Set<String> marketplaceIds = new HashSet<>();
            Set<Integer> scShopIds = new HashSet<>();
            Set<Integer> vcShopIds = new HashSet<>();
            Set<String> scAsins = new HashSet<>();
            Set<String> vcAsins = new HashSet<>();
            for (GetRightListResponse.DataVo row : rows) {
                shopIds.add(row.getShopId());
                uids.add(row.getUid());
                marketplaceIds.add(row.getMarketplaceId());
                List<String> asins = StringUtil.splitStr(row.getAsins(), ",");
                if ("SC".equals(row.getType())) {
                    scShopIds.add(row.getShopId());
                    scAsins.addAll(asins);
                } else {
                    vcShopIds.add(row.getShopId());
                    vcAsins.addAll(asins);
                }
            }
            // 根据分页后的内容查询产品详情
            // sc店铺
            List<AsinInfoNoBuilderVo> scProducts = new ArrayList<>();
            List<AsinInfoNoBuilderVo> vcProducts = new ArrayList<>();
            Map<String, AsinInfoNoBuilderVo> scProductMap = new HashMap<>();
            Map<String, AsinInfoNoBuilderVo> vcProductMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(scShopIds) && CollectionUtils.isNotEmpty(scAsins)) {
                scProducts = odsProductDao.listScProducts(puid, new ArrayList<>(scShopIds), new ArrayList<>(scAsins));
                scProductMap = StreamUtil.toMap(scProducts, i -> i.getAsin() + i.getShopId());
            }
            // vc店铺
            if (CollectionUtils.isNotEmpty(vcShopIds)) {
                vcProducts = odsProductDao.listVcProducts(puid, new ArrayList<>(vcShopIds), new ArrayList<>(vcAsins));
                vcProductMap = StreamUtil.toMap(vcProducts, i -> i.getAsin() + i.getShopId());
            }
            // 查询账号信息
            Map<Integer, GetUserInfoResponse.UserInfo> userInfoMap = StreamUtil.toMap(userDao.getUserInfoByIds(puid, uids), GetUserInfoResponse.UserInfo::getUid);
            // 查询商品数量
            List<CountProductVo> countVo = odsAmazonAdProductPermissionDao.countProducts(puid, new ArrayList<>(shopIds), new ArrayList<>(uids), new ArrayList<>(marketplaceIds));
            HashMap<String, CountProductVo> map = countVo.stream().collect(Collectors.toMap(vo -> vo.getShopId() + "#" + vo.getUid(), vo -> vo, (existing, replacement) -> replacement, HashMap::new));
            // 组装数据
            Map<String, AsinInfoNoBuilderVo> finalScProductMap = scProductMap;
            Map<String, AsinInfoNoBuilderVo> finalVcProductMap = vcProductMap;
            rows.forEach(
                    row -> {
                        //设置账户信息
                        GetUserInfoResponse.UserInfo userInfo = userInfoMap.get(row.getUid());
                        if (Objects.nonNull(userInfo)) {
                            row.setName(userInfo.getName());
                        }
                        // 设置站点信息
                        AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(row.getMarketplaceId());
                        row.setMarketplaceCN(amznEndpoint.getMarketplaceCN());
                        // 设置商品信息
                        List<String> asins = StringUtil.splitStr(row.getAsins(), ",");
                        List<AsinInfoNoBuilderVo> asinInfos = new ArrayList<>();
                        if (ShopTypeEnum.SC.getCode().equals(row.getType())) {
                            for (String asin : asins) {
                                AsinInfoNoBuilderVo asinInfo = finalScProductMap.get(asin + row.getShopId());
                                if (Objects.nonNull(asinInfo)) {
                                    List<String> mskus = new ArrayList<>();
                                    if (StringUtils.isNotBlank(asinInfo.getMsku())) {
                                        mskus = StringUtil.splitStr(asinInfo.getMsku(), ",");
                                        List<String> list = mskus.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
                                        asinInfo.setMskus(list);
                                    } else {
                                        mskus.add("-");
                                        asinInfo.setMskus(mskus);
                                    }
                                    asinInfos.add(asinInfo);
                                } else {
                                    AsinInfoNoBuilderVo vo = new AsinInfoNoBuilderVo();
                                    List<String> mskus = new ArrayList<>();
                                    mskus.add("-");
                                    vo.setAsin(asin);
                                    vo.setMskus(mskus);
                                    asinInfos.add(vo);
                                }
                            }
                            row.setAsinInfos(asinInfos);
                        } else {
                            for (String asin : asins) {
                                AsinInfoNoBuilderVo asinInfo = finalVcProductMap.get(asin + row.getShopId());
                                if (Objects.nonNull(asinInfo)) {
                                    List<String> mskus = new ArrayList<>();
                                    mskus.add("-");
                                    asinInfo.setMskus(mskus);
                                    asinInfo.setMsku(null);
                                    asinInfos.add(asinInfo);
                                }
                            }
                            row.setAsinInfos(asinInfos);
                        }
                        row.setAsins(null);
                        // 设置商品数量
                        CountProductVo countProductVo = map.get(row.getShopId() + "#" + row.getUid());
                        if (Objects.nonNull(countProductVo)) {
                            row.setCount(countProductVo.getCount());
                        }
                    }
            );
            response.setPage(pageRightVo);
        } catch (Exception e) {
            log.error("产品权限列表页查询失败：puid:{}", puid, e);
        }
        return response;
    }

    @Override
    public AsinDetailResponse getAsinDetail(int puid, AsinDetailRequest request) {
        AsinDetailResponse response = new AsinDetailResponse();
        List<AsinDetailResponse.AsinDetail> asinDetails = new ArrayList<>();
        Page<AsinDetailResponse.AsinDetail> page = new Page<>();
        try {
            Page<AsinInfo> asinDetailPage = odsAmazonAdProductPermissionDao.getAsinDetail(puid, request.getShopId(), request.getMarketplaceId(),
                    request.getUid(), request.getType(), request.getPageNo(), request.getPageSize());
            List<AsinInfo> rows = asinDetailPage.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                return response;
            }
            page.setRows(asinDetails);
            page.setPageNo(asinDetailPage.getPageNo());
            page.setPageSize(asinDetailPage.getPageSize());
            page.setTotalPage(asinDetailPage.getTotalPage());
            page.setTotalSize(asinDetailPage.getTotalSize());
            for (AsinInfo row : rows) {
                AsinDetailResponse.AsinDetail asinDetail = new AsinDetailResponse.AsinDetail();
                asinDetail.setAsin(row.getAsin());
                asinDetail.setTitle(row.getTitle());
                asinDetail.setImgUrl(row.getImgUrl());
                List<String> mskus = new ArrayList<>();
                if (StringUtils.isNotBlank(row.getMsku())) {
                    mskus = StringUtil.splitStr(row.getMsku(), ",");
                    asinDetail.setMskus(mskus);
                } else {
                    mskus.add("-");
                    asinDetail.setMskus(mskus);
                }
                asinDetails.add(asinDetail);
            }
        } catch (Exception e) {
            log.error("广告产品权限 - 商品详情接口查询异常：puid:{}", puid, e);
        }
        response.setPage(page);
        return response;
    }

    @Override
    public Result<String> deleteRight(int puid, int uid, DeleteRightRequest request) {
        try {
            // 删除mysql数据
            int deleteCount = amazonIAdProductPermissionDao.delete(puid, request.getShopId(), uid);
            // 删除doris数据
            if (deleteCount > 0) {
                odsAmazonAdProductPermissionDao.delete(puid, request.getShopId(), uid);
            }
        } catch (Exception e) {
            log.error("产品权限一处失败：", e);
            return ResultUtil.error("删除失败");
        }
        return ResultUtil.success();
    }

    @Override
    public Result<String> addRight(int puid, AddRightRequest request) {
        //组装数据
        List<AmazonAdProductPermission> list = new ArrayList<>();
        List<OdsAmazonAdProductPermission> odsList = new ArrayList<>();
        List<AddRightRequest.Asins> products = request.getProduct();
        for (AddRightRequest.Asins product : products) {
            for (Integer uid : request.getUidList()) {
                AmazonAdProductPermission amazonAdProductPermission = new AmazonAdProductPermission();
                amazonAdProductPermission.setPuid(puid);
                amazonAdProductPermission.setShopId(product.getShopId());
                amazonAdProductPermission.setUid(uid);
                amazonAdProductPermission.setAsin(product.getAsin());
                amazonAdProductPermission.setMarketplaceId(product.getMarketplaceId());
                list.add(amazonAdProductPermission);
            }
        }
        // 防止数量太多，分批插入
        List<List<AmazonAdProductPermission>> partitions = Lists.partition(list, 9000);
        // 插入mysql
        int flag = 0;
        try {
            for (List<AmazonAdProductPermission> partition : partitions) {
                flag = amazonIAdProductPermissionDao.addRight(partition);
            }
            // 同步doris
            if (flag > 0) {
                for (AmazonAdProductPermission adProductPermission : list) {
                    OdsAmazonAdProductPermission odsAdProductPermission = new OdsAmazonAdProductPermission();
                    odsAdProductPermission.setPuid(puid);
                    odsAdProductPermission.setUid(adProductPermission.getUid());
                    odsAdProductPermission.setAsin(adProductPermission.getAsin());
                    odsAdProductPermission.setShopId(adProductPermission.getShopId());
                    odsAdProductPermission.setMarketplaceId(adProductPermission.getMarketplaceId());
                    odsAdProductPermission.setCreateTime(new Date());
                    odsAdProductPermission.setUpdateTime(new Date());
                    odsList.add(odsAdProductPermission);
                }
                List<List<OdsAmazonAdProductPermission>> odsPartitions = Lists.partition(odsList, 9000);
                for (List<OdsAmazonAdProductPermission> odsPartition : odsPartitions) {
                    dorisServiceImpl.saveDoris(odsPartition);
                }
            }
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("保存权限失败！", e);
            return ResultUtil.error("保存权限失败！");
        }
    }

    @Override
    public AsinListResponse getAsinInfo(int puid, AsinInfoRequest request) {
        AsinListResponse response = new AsinListResponse();
        Page<AsinListResponse.AsinInfo> page = new Page<>();
        List<AsinListResponse.AsinInfo> list = new ArrayList<>();
        try {
            // 分页查询asin
            Page<AsinInfo> asinInfoPage = odsAmazonAdProductPermissionDao.getAsinDetail(puid, request.getShopId(),  request.getMarketplaceId(), request.getUid(), request.getType(), request.getPageNo(), request.getPageSize());
            List<AsinInfo> rows = asinInfoPage.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                return response;
            }
            // 查询店铺信息
            List<Integer> shopIds = rows.stream().map(AsinInfo::getShopId).distinct().collect(Collectors.toList());
            Map<Integer, ShopAuth> map = StreamUtil.toMap(scVcShopAuthDao.getAuthShopByShopIdList(puid, shopIds), ShopAuth::getId);
            rows.forEach(row -> {
                AsinListResponse.AsinInfo asinInfo = new AsinListResponse.AsinInfo();
                asinInfo.setAsin(row.getAsin());
                asinInfo.setTitle(row.getTitle());
                asinInfo.setImgUrl(row.getImgUrl());
                List<String> mskus = new ArrayList<>();
                if (StringUtils.isNotBlank(row.getMsku())) {
                    mskus = StringUtil.splitStr(row.getMsku(), ",");
                    asinInfo.setMskus(mskus);
                } else {
                    mskus.add("-");
                    asinInfo.setMskus(mskus);
                }
                asinInfo.setShopId(row.getShopId());
                ShopAuth shopAuth = map.get(row.getShopId());
                asinInfo.setShopName(shopAuth.getName());
                asinInfo.setMarketplaceId(shopAuth.getMarketplaceId());
                asinInfo.setMarketplaceCN(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
                list.add(asinInfo);
            });
            page.setRows(list);
            page.setPageNo(asinInfoPage.getPageNo());
            page.setPageSize(asinInfoPage.getPageSize());
            page.setTotalPage(asinInfoPage.getTotalPage());
            page.setTotalSize(asinInfoPage.getTotalSize());
        } catch (Exception e) {
            log.error("编辑广告产品权限 - 右侧产品详情接口异常：puid:{}", puid, e);
        }
        response.setPage(page);
        return response;
    }

    @Override
    public Result<String> updateRight(int puid, AddRightRequest request) {
        // 先查出该账号的所有产品
        List<String> removeAsins = request.getRemoveProducts();
        List<List<String>> removeAsinList = null;
        // 组装需要添加的产品
        List<AmazonAdProductPermission> list = new ArrayList<>();
        List<List<AmazonAdProductPermission>> addPartitions = null;
        List<String> products = request.getAddProducts();
        Integer uid = request.getUid();
        Integer shopId = request.getShopId();
        String marketplaceId = request.getMarketplaceId();
        if (CollectionUtils.isNotEmpty(products)) {
            for (String product : products) {
                AmazonAdProductPermission amazonAdProductPermission = new AmazonAdProductPermission();
                amazonAdProductPermission.setPuid(puid);
                amazonAdProductPermission.setShopId(shopId);
                amazonAdProductPermission.setUid(uid);
                amazonAdProductPermission.setAsin(product);
                amazonAdProductPermission.setMarketplaceId(marketplaceId);
                list.add(amazonAdProductPermission);
            }
            // 防止数量太多，分批插入和删除
            addPartitions = Lists.partition(list, 9000);
        }
        try {
            if (CollectionUtils.isNotEmpty(removeAsins)) {
                removeAsinList = Lists.partition(removeAsins, 9000);
            }
            // 删除广告产品
            if (CollectionUtils.isNotEmpty(removeAsinList)) {
                // 删除mysql数据
                int deleteCount = 0;
                for (List<String> partition : removeAsinList) {
                    deleteCount = amazonIAdProductPermissionDao.deleteByAsin(puid, shopId, uid, partition);
                }
                // 删除Doris数据
                if (deleteCount > 0) {
                    for (List<String> partition : removeAsinList) {
                        odsAmazonAdProductPermissionDao.deleteByAsin(puid, shopId, uid, partition);
                    }
                }
            }
            // 移除全部
            if (Objects.nonNull(request.getIsRemoveAll()) && Boolean.TRUE.equals(request.getIsRemoveAll())) {
                // 删除mysql数据
                int deleteCount = 0;
                deleteCount = amazonIAdProductPermissionDao.deleteByUid(puid, shopId, uid);
                // 删除Doris数据
                if (deleteCount > 0) {
                    odsAmazonAdProductPermissionDao.deleteByUid(puid, shopId, uid);
                }
            }
            // 添加广告产品
            if (addPartitions != null) {
                List<OdsAmazonAdProductPermission> odsList = new ArrayList<>();
                int flag = 0;
                // 插入mysql
                for (List<AmazonAdProductPermission> partition : addPartitions) {
                    flag = amazonIAdProductPermissionDao.addRight(partition);
                }
                // 同步到Doris
                if (flag > 0) {
                    for (AmazonAdProductPermission adProductPermission : list) {
                        OdsAmazonAdProductPermission odsAdProductPermission = new OdsAmazonAdProductPermission();
                        odsAdProductPermission.setPuid(puid);
                        odsAdProductPermission.setUid(adProductPermission.getUid());
                        odsAdProductPermission.setAsin(adProductPermission.getAsin());
                        odsAdProductPermission.setShopId(adProductPermission.getShopId());
                        odsAdProductPermission.setMarketplaceId(adProductPermission.getMarketplaceId());
                        odsAdProductPermission.setCreateTime(new Date());
                        odsAdProductPermission.setUpdateTime(new Date());
                        odsList.add(odsAdProductPermission);
                    }
                    List<List<OdsAmazonAdProductPermission>> odsPartitions = Lists.partition(odsList, 9000);
                    for (List<OdsAmazonAdProductPermission> odsPartition : odsPartitions) {
                        dorisServiceImpl.saveDoris(odsPartition);
                    }
                }
            }
        } catch (Exception e) {
            log.error("编辑广告产品保存失败 puid:{} shopId:{}：", puid, shopId, e);
            return ResultUtil.error("编辑广告产品保存失败");
        }
        return ResultUtil.success("保存成功");
    }

    @Override
    public AsinListResponse getAsinList(int puid, AsinListRequest request) {
        AsinListResponse response = new AsinListResponse();
        List<AsinListResponse.AsinInfo> list = new ArrayList<>();
        Page<AsinListResponse.AsinInfo> page = new Page<>(request.getPageNo(), request.getPageSize());
        List<String> asins = new ArrayList<>();
        List<Integer> shopIds = new ArrayList<>();
        try {
            if (ShopTypeEnum.SC.getCode().equals(request.getType())) {
                GetAsinInfoResponse asinInfoResponse = new GetAsinInfoResponse();
                // 如果是SC店铺：
                // 调用数据组接口，分页查询asin
                asinInfoResponse = dataDomainManager.getAsinInfo(puid, request.getShopIdList(), request.getMarketplaceIdList(), request);
                if (Objects.isNull(asinInfoResponse.getData())) {
                    response.setPage(page);
                    return response;
                }
                List<GetAsinInfoResponse.AsinInfo> asinInfos = asinInfoResponse.getData().getRows();
                shopIds = asinInfos.stream().map(GetAsinInfoResponse.AsinInfo::getShopId).distinct().collect(Collectors.toList());
                Map<Integer, ShopAuth> map = StreamUtil.toMap(scVcShopAuthDao.getScAndVcAuthShopByShopIdList(puid, shopIds), ShopAuth::getId);
                for (GetAsinInfoResponse.AsinInfo asinInfo : asinInfos) {
                    AsinListResponse.AsinInfo info = new AsinListResponse.AsinInfo();
                    asins.add(asinInfo.getAsin());
                    info.setAsin(asinInfo.getAsin());
                    info.setTitle(asinInfo.getTitle());
                    info.setImgUrl(asinInfo.getMainImage());
                    info.setShopId(asinInfo.getShopId());
                    info.setShopName(map.get(asinInfo.getShopId()).getName());
                    info.setMarketplaceId(asinInfo.getMarketplaceId());
                    info.setMarketplaceCN(AmznEndpoint.getByMarketplaceId(asinInfo.getMarketplaceId()).getMarketplaceCN());
                    List<String> mskuList = new ArrayList<>();
                    if (StringUtils.isNotBlank(asinInfo.getMskuStr())) {
                        mskuList = StringUtil.splitStr(asinInfo.getMskuStr(), StringUtil.SPECIAL_COMMA);
                    } else {
                        mskuList.add("-");
                    }
                    info.setMskus(mskuList);
                    list.add(info);
                }
                page.setTotalPage(asinInfoResponse.getData().getTotalPage());
                page.setTotalSize(asinInfoResponse.getData().getTotalSize());
                page.setRows(list);
            } else {
                // 如果是VC店铺：
                // 查询自己的vc产品表
                page = odsProductDao.getAsinInfo(puid, request.getShopIdList(), request.getMarketplaceIdList(), request.getPageNo(), request.getPageSize());
                if (CollectionUtils.isEmpty(page.getRows())) {
                    response.setPage(page);
                    return response;
                }
                shopIds = page.getRows().stream().map(AsinListResponse.AsinInfo::getShopId).collect(Collectors.toList());
                Map<Integer, ShopAuth> map = StreamUtil.toMap(scVcShopAuthDao.getScAndVcAuthShopByShopIdList(puid, shopIds), ShopAuth::getId);
                for (AsinListResponse.AsinInfo asinInfo : page.getRows()) {
                    asins.add(asinInfo.getAsin());
                    asinInfo.setShopName(map.get(asinInfo.getShopId()).getName());
                    asinInfo.setMarketplaceCN(AmznEndpoint.getByMarketplaceId(asinInfo.getMarketplaceId()).getMarketplaceCN());
                }
            }
            // 如果是编辑页 反查一遍产品权限表反显已添加字段
            if (Boolean.TRUE.equals(request.getIsEdit())) {
                if (CollectionUtils.isNotEmpty(asins)) {
                    List<String> filterAsin = odsAmazonAdProductPermissionDao.filterAsin(puid, request.getUid(), request.getShopIdList().get(0), asins);
                    page.getRows().forEach(row -> {
                        if (filterAsin.contains(row.getAsin())) {
                            row.setIsAdd(true);
                        }
                    });
                }
            }
        } catch (Exception e) {
            log.error("添加广告产品 - 左侧产品详情接口 puid:{}", puid, e);
        }
        response.setPage(page);
        return response;
    }


    /**
     *
     * 检查用户是否需要校验产品权限
     * @param puid
     * @param uid GRPC 接口可以通过   MDC.get(Constants.UID)  需要注意异步调用，如果不能拿从主项目透传过来
     * @param isAdminUser GRPC 接口可以通过  MDC.get(Constants.ADMIN_USER)  需要注意异步调用，如果不能拿从主项目透传过来
     * @return false 不需要检查权限， true 需要检查权限
     */
    @Override
    public boolean checkUidNeedProductRight(Integer puid, Integer uid, List<Integer> shopIds, boolean isAdminUser) {
        //1.判断用户是否是管理员或者主账号
        if (puid.equals(uid) || isAdminUser) {
            return false;
        }
        Integer countByUidAndShopIds = odsAmazonAdProductPermissionDao.getCountByUidAndShopIds(puid, uid, shopIds);
        if (countByUidAndShopIds > 0) {
            return true;
        }
        return false;
    }

    /**
     * 检查用户是否需要校验产品权限，用户信息和是否管理员是通过DMC获取，该方法不能用于多线程调用
     * @param puid puid
     * @param shopIds 店铺id集合
     * @return false 不需要检查权限， true 需要检查权限
     */
    @Override
    public boolean checkUidNeedProductRight(Integer puid, List<Integer> shopIds) {
        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        log.info("uid:{}, isAdminUser:{}", uid, isAdminUser);
        return checkUidNeedProductRight(puid, uid, shopIds, isAdminUser);
    }

    /**
     *
     * 查询有设置权限的店铺
     * @param puid
     * @return
     */
    @Override
    public List<Integer> getUidNeedProductRightShopIds(Integer puid, List<Integer> shopIds) {
        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        log.info("uid:{}, isAdminUser:{}", uid, isAdminUser);
        return getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
    }

    /**
     *
     * 查询有设置权限的店铺
     * @param puid
     * @param uid GRPC 接口可以通过   MDC.get(Constants.UID)  需要注意异步调用，如果不能拿从主项目透传过来
     * @param isAdminUser GRPC 接口可以通过  MDC.get(Constants.ADMIN_USER)  需要注意异步调用，如果不能拿从主项目透传过来
     * @return
     */
    @Override
    public List<Integer> getUidNeedProductRightShopIds(Integer puid, Integer uid, List<Integer> shopIds, boolean isAdminUser) {
        //1.判断用户是否是管理员或者主账号
        if (puid.equals(uid) || isAdminUser) {
            return Lists.newArrayList();
        }
        return odsAmazonAdProductPermissionDao.getShopIdsByUidAndShopIds(puid, uid, shopIds);
    }


    /**
     *
     * 按活动拼接sql ( shop_id in (无需过滤权限店铺id) or  campaign_in (需要过滤权限店铺拼接权限sql) )形式
     *
     * @param campaignIdFieldName 活动id 字段名称
     * @param puid
     * @param uid GRPC 接口可以通过   MDC.get(Constants.UID)  需要注意异步调用，如果不能拿从主项目透传过来
     * @param isAdminUser GRPC 接口可以通过  MDC.get(Constants.ADMIN_USER)  需要注意异步调用，如果不能拿从主项目透传过
     * @param shopIds 店铺id
     * @param types com.meiyunji.sponsored.service.enums.CampaignTypeEnum
     * @param args sql 参数
     * @return
     */
    @Override
    public String getProductRightSql(String shopIdFieldName, String campaignIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {
        List<Integer> uidNeedProductRightShopIds = getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
        if (CollectionUtils.isEmpty(uidNeedProductRightShopIds)) {
            return "";
        }
        ArrayList<Integer> nonSet = Lists.newArrayList(shopIds);
        nonSet.removeAll(uidNeedProductRightShopIds);
        StringBuilder sql = new StringBuilder();
        sql.append("(");
        if (CollectionUtils.isNotEmpty(nonSet)) {
            sql.append(SqlStringUtil.dealInListNotAnd(shopIdFieldName, nonSet, args));
            sql.append(" or ");
        }
        sql.append(campaignIdFieldName).append(" in ").append(builderProductRightSql(puid, uid, uidNeedProductRightShopIds, args, types));

        sql.append(")");
        return sql.toString();
    }

    /**
     * 查询所有没有设置产品店铺活动id
     * @param puid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private String getNonSetProductSql (int puid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {
        StringBuilder sql = new StringBuilder(" SELECT " +
                " a.campaign_id campaign_id " +
                " FROM " +
                " ods_t_amazon_ad_campaign_all a " +
                " WHERE a.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("a.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealDorisInList("a.type", types.stream().map(CampaignTypeEnum::getCampaignType).collect(Collectors.toList()), args));
        return sql.toString();
    }




    private String getSpProductRightSql(int puid, List<Integer> shopIds, List<Object> args) {
        StringBuilder sql = new StringBuilder(" select " +
                "  puid, shop_id, campaign_id, asin  from ods_t_amazon_ad_product sp ");
        sql.append("  where sp.puid =? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("sp.shop_id", shopIds, args));
        sql.append(" group by puid, shop_id, campaign_id, asin ");
        return sql.toString();
    }


    private String getSbProductRightSql(int puid, List<Integer> shopIds, List<Object> args) {
        StringBuilder sql = new StringBuilder(" select " +
                " puid,shop_id shop_id,campaign_id,trim(split_asins.asin) AS asin  from ods_t_amazon_sb_ads ads " +
                " LATERAL VIEW EXPLODE(split_by_string(ads.asins,',')) split_asins AS asin ");
        sql.append("  where ads.puid = ?  ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("ads.shop_id", shopIds, args));
        sql.append(" group by puid, shop_id, campaign_id, trim(split_asins.asin) ");
        return sql.toString();
    }

    private String getSdProductRightSql(int puid, List<Integer> shopIds, List<Object> args) {
        StringBuilder sql = new StringBuilder(" select " +
                "  puid, shop_id, campaign_id, asin  from ods_t_amazon_ad_product_sd sd ");
        sql.append("  where sd.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("sd.shop_id", shopIds, args));
        sql.append(" group by  puid, shop_id, campaign_id, asin ");
        return sql.toString();
    }


    /**
     * 组装产品权限sql
     * @param puid
     * @param uid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private String getProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {

        if (CollectionUtils.isEmpty(types) || !CampaignTypeEnum.campaignTypeEnumList.containsAll(types)) {
            throw new RuntimeException("系统错误，未知广告类型，请尝试刷新页面重试");
        }

        List<String> sql = types.stream().map(e -> {
            if (CampaignTypeEnum.sp == e) {
                return getSpProductRightSql(puid, shopIds, args);
            }
            if (CampaignTypeEnum.sb == e) {
                return getSbProductRightSql(puid, shopIds, args);
            }
            if (CampaignTypeEnum.sd == e) {
                return getSdProductRightSql(puid, shopIds, args);
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        String s = String.join(" union ", sql);
        StringBuilder sqlBuilder = new StringBuilder(" select distinct campaign_id  campaign_id from ( ");
        sqlBuilder.append(s).append(" ) a  join ods_t_ad_product_permission pp on a.puid = pp.puid ");
        sqlBuilder.append(" and a.shop_id = pp.shop_id and a.asin = pp.asin and pp.puid= ? and pp.uid = ? ");
        args.add(puid);
        args.add(uid);
        return sqlBuilder.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args)).toString();
    }



    /**
     * 拼接权限查询sql
     * @param puid
     * @param uid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private  String builderProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {
        StringBuilder sqlBuilder = new StringBuilder();
        //权限sql
        sqlBuilder.append(getProductRightSql(puid, uid, shopIds, args, types));
        //如果是sb 的要查出活动下面一个asin 都没有的数据
        if (types.contains(CampaignTypeEnum.sb)) {
            sqlBuilder.append(" union  ").append(getSbNonProductSql(puid, shopIds, args));
        }
        //如果是sd 的要查出活动下面一个asin 都没有的数据
        if (types.contains(CampaignTypeEnum.sd)) {
            sqlBuilder.append(" union   ").append(getSdNonProductSql(puid, shopIds, args));
        }
        //查询所有没有产品的活动id
        sqlBuilder.append(" union ").append(getNonCreateProductSql(puid, shopIds, args, types, false));

        return sqlBuilder.toString();

    }

    /**
     * 广告活动未创建广告产品数据
     * @param puid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private String getNonCreateProductSql(int puid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types, boolean isPortfolio) {
        StringBuilder sql = new StringBuilder(" SELECT " +
                " a.campaign_id campaign_id " +
                " FROM " +
                " ods_t_amazon_ad_campaign_all a " +
                " WHERE a.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("a.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealDorisInList("a.type", types.stream().map(CampaignTypeEnum::getCampaignType).collect(Collectors.toList()), args));
        if (isPortfolio) {
            sql.append(" and a.portfolio_id is not null and a.portfolio_id <> '' ");
        }
        if (types.contains(CampaignTypeEnum.sp)) {
            sql.append(" AND NOT EXISTS ( " +
                    " SELECT " +
                    " 1 " +
                    " FROM " +
                    " ods_t_amazon_ad_product b " +
                    " WHERE b.puid = ? ");
            args.add(puid);
            sql.append(SqlStringUtil.dealDorisInList("b.shop_id", shopIds, args));
            sql.append(" and " +
                    " a.puid = b.puid " +
                    " AND a.shop_id = b.shop_id " +
                    " AND a.campaign_id = b.campaign_id  and a.type = 'sp' " +
                    " ) ");
        }
        if (types.contains(CampaignTypeEnum.sb)) {
            sql.append(" AND NOT EXISTS ( " +
                    " SELECT " +
                    " 1 " +
                    " FROM " +
                    " ods_t_amazon_sb_ads c " +
                    " WHERE c.puid = ? ");
            args.add(puid);
            sql.append(SqlStringUtil.dealDorisInList("c.shop_id", shopIds, args));
            sql.append(" and " +
                    " a.puid = c.puid " +
                    " AND a.shop_id = c.shop_id " +
                    " AND a.campaign_id = c.campaign_id  and a.type = 'sb' " +
                    " ) ");
        }
        if (types.contains(CampaignTypeEnum.sd)) {
            sql.append(" AND NOT EXISTS ( " +
                    " SELECT " +
                    " 1 " +
                    " FROM " +
                    " ods_t_amazon_ad_product_sd d " +
                    " WHERE d.puid = ? ");
            args.add(puid);
            sql.append(SqlStringUtil.dealDorisInList("d.shop_id", shopIds, args));
            sql.append(" and " +
                    " a.puid = d.puid " +
                    " AND a.shop_id = d.shop_id " +
                    " AND a.campaign_id = d.campaign_id  and a.type = 'sd' " +
                    " ) ");
        }
        return sql.toString();
    }


    /**
     * sb 广告活动下所有的产品都是空
     * @param puid
     * @param shopIds
     * @param args
     * @return
     */
    private String getSbNonProductSql(int puid, List<Integer> shopIds, List<Object> args) {
        StringBuilder sql = new StringBuilder(" select " +
                "  campaign_id  from ods_t_amazon_sb_ads ");
        sql.append("  where puid = ?  ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        sql.append(" GROUP BY puid,shop_id,campaign_id ");
        sql.append(" HAVING COUNT(CASE WHEN asins IS NOT NULL AND asins != '' THEN 1 END) = 0 ");
        return sql.toString();
    }


    /**
     * sd 广告活动下所有的产品都是空
     * @param puid
     * @param shopIds
     * @param args
     * @return
     */
    private String getSdNonProductSql(int puid, List<Integer> shopIds, List<Object> args) {
        StringBuilder sql = new StringBuilder(" select " +
                " campaign_id  from ods_t_amazon_ad_product_sd ");
        sql.append("  where puid = ?  ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        sql.append(" GROUP BY puid,shop_id,campaign_id ");
        sql.append(" HAVING COUNT(CASE WHEN asin IS NOT NULL AND asin != '' THEN 1 END) = 0 ");
        return sql.toString();
    }


    /**
     * 按活动拼接sql: and campaign_id in ()
     * @param campaignIdFieldName
     * @param puid
     * @param shopIds
     * @param types
     * @return
     */
    @Override
    public String getProductRightCampaignIdsSqlAnd(String campaignIdFieldName, Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {
        String campaignIdsSql = getProductRightCampaignIdsSql(campaignIdFieldName, puid, shopIds, types, args);
        if (StringUtils.isBlank(campaignIdsSql)) {
            return "";
        }

        return " and " + campaignIdsSql + " ";
    }

    /**
     * 按活动拼接sql，不拼接and: campaign_id in ()
     * @param campaignIdFieldName
     * @param puid
     * @param shopIds
     * @param types
     * @return
     */
    @Override
    public String getProductRightCampaignIdsSql(String campaignIdFieldName, Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {
        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        log.info("uid:{}, isAdminUser:{}", uid, isAdminUser);
        return getProductRightCampaignIdsSql(campaignIdFieldName, puid, uid, isAdminUser, shopIds, types, args);
    }


    /**
     * 按活动拼接sql ( campaign_in (用户所有有权限的活动) )形式
     * @param campaignIdFieldName
     * @param puid
     * @param uid
     * @param isAdminUser
     * @param shopIds
     * @param types
     * @return
     */
    @Override
    public String getProductRightCampaignIdsSql(String campaignIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {
        List<Integer> uidNeedProductRightShopIds = getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
        if (CollectionUtils.isEmpty(uidNeedProductRightShopIds)) {
            return "";
        }
        ArrayList<Integer> nonSet = Lists.newArrayList(shopIds);
        nonSet.removeAll(uidNeedProductRightShopIds);
        StringBuilder sql = new StringBuilder();
        sql.append("(");
        sql.append(campaignIdFieldName).append(" in (select campaign_id from (");
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(nonSet)) {
            sql.append(getNonSetProductSql(puid, nonSet, args, types));
            flag = true;
        }
        if (CollectionUtils.isNotEmpty(uidNeedProductRightShopIds)) {
            if (flag) {
                sql.append(" union ");
            }
            sql.append(builderProductRightSql(puid, uid, uidNeedProductRightShopIds, args, types));
        }
        sql.append(")s))");
        return sql.toString();
    }


    /**
     * 获取有权限的活动id  先判断一下用户是否需要过滤权限，并且这个最好是只用在单店铺查询
     * @param puid
     * @param uid
     * @param isAdminUser
     * @param shopIds
     * @param types
     * @return
     */
    @Override
    public List<String> getProductRightCampaignIds(Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types) {
        List<Object> args = new ArrayList<>();
        List<Integer> uidNeedProductRightShopIds = getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
        ArrayList<Integer> nonSet = Lists.newArrayList(shopIds);
        nonSet.removeAll(uidNeedProductRightShopIds);
        StringBuilder sql = new StringBuilder();
        sql.append("select campaign_id from ods_t_amazon_ad_campaign_all ");
        sql.append(" where puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("type", types.stream().map(CampaignTypeEnum::getCampaignType).collect(Collectors.toList()), args));
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        sql.append(" and campaign_id ").append(" in ( select campaign_id from ( ");
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(nonSet)) {
            sql.append(getNonSetProductSql(puid, nonSet, args, types));
            flag = true;
        }
        if (CollectionUtils.isNotEmpty(uidNeedProductRightShopIds)) {
            if (flag) {
                sql.append(" union ");
            }
            sql.append(builderProductRightSql(puid, uid, uidNeedProductRightShopIds, args, types));
        }
        sql.append(")s)");
        sql.append(" limit " + Constants.TOTALSIZELIMIT);
        return odsAmazonAdCampaignAllDao.queryProductRightSqlGetCampaignIds(args, sql.toString());
    }

    @Override
    public String getProductRightCampaignIdsSqlFromGrpc(Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {
        return getProductRightCampaignIdsSqlFromGrpc(puid, shopIds, types, args, "campaign_id");
    }

    /**
     * 注意不要在多线程任务中使用该方法，MDC底层由ThreadLocal实现，grpc调用携带的值会丢失，这种情况下只能先提前把值取出来
     * @param puid
     * @param shopIds
     * @param types
     * @param args
     * @param campaignIdFieldName
     * @return
     */
    @Override
    public String getProductRightCampaignIdsSqlFromGrpc(Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args, String campaignIdFieldName) {
        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        log.info("uid:{}, isAdminUser:{}", uid, isAdminUser);
        return getProductRightCampaignIdsSql(campaignIdFieldName, puid, uid, isAdminUser, shopIds, types, args);
    }

    @Override
    public Pair<Boolean, List<String>> getProductRightCampaignIdsFromGrpc(Integer puid, Integer shopId, CampaignTypeEnum type) {
        return getProductRightCampaignIdsFromGrpc(puid, Collections.singletonList(shopId), Collections.singletonList(type));
    }


//
//    private String getSpProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args) {
//      StringBuilder sql = new StringBuilder(" select " +
//              "  DISTINCT ads.campaign_id campaign_id  from ods_t_amazon_ad_product sp" +
//              "  join ods_t_ad_product_permission pp on sp.puid = pp.puid " +
//              "  and sp.shop_id = pp.shop_id and sp.asin = pp.asin and sp.puid = ? and pp.puid = ? and pp.uid = ?  ");
//      args.add(puid);
//      args.add(puid);
//      args.add(uid);
//      sql.append(SqlStringUtil.dealDorisInList("sp.shop_id", shopIds, args));
//      sql.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args));
//      sql.append("  where sp.puid = ? and pp.puid = ? and pp.uid = ?  ");
//      sql.append(SqlStringUtil.dealDorisInList("sp.shop_id", shopIds, args));
//      sql.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args));
//      return sql.toString();
//    }
//
//
//    private String getSbProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args) {
//
//        StringBuilder sql = new StringBuilder(" select " +
//                "  DISTINCT ads.campaign_id campaign_id  from ods_t_amazon_sb_ads ads " +
//                " LATERAL VIEW EXPLODE(split_by_string(ads.asins,',')) split_asins AS asin " +
//                "  join ods_t_ad_product_permission pp on sp.puid = pp.puid " +
//                "  and sp.shop_id = pp.shop_id and trim(split_asins.asin) = pp.asin and ads.puid = ? and pp.puid = ? and pp.uid = ?  ");
//        args.add(puid);
//        args.add(puid);
//        args.add(uid);
//        sql.append(SqlStringUtil.dealDorisInList("ads.shop_id", shopIds, args));
//        sql.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args));
//        sql.append("  where ads.puid = ? and pp.puid = ? and pp.uid = ?  ");
//        sql.append(SqlStringUtil.dealDorisInList("ads.shop_id", shopIds, args));
//        sql.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args));
//        return sql.toString();
//    }
//
//    private String getSdProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args) {
//        StringBuilder sql = new StringBuilder(" select " +
//                "  DISTINCT campaign_id campaign_id  from ods_t_amazon_ad_product_sd sd" +
//                "  join ods_t_ad_product_permission pp on sd.puid = pp.puid " +
//                "  and sd.shop_id = pp.shop_id and sd.asin = pp.asin and sd.puid = ? and pp.puid = ? and pp.uid = ?  ");
//        args.add(puid);
//        args.add(puid);
//        args.add(uid);
//        sql.append(SqlStringUtil.dealDorisInList("sd.shop_id", shopIds, args));
//        sql.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args));
//        sql.append("  where sd.puid = ? and pp.puid = ? and pp.uid = ?  ");
//        sql.append(SqlStringUtil.dealDorisInList("sd.shop_id", shopIds, args));
//        sql.append(SqlStringUtil.dealDorisInList("pp.shop_id", shopIds, args));
//        return sql.toString();
//    }
    /**
     * 获取有权限的活动id
     * 注意不要在多线程任务中使用该方法，MDC底层由ThreadLocal实现，grpc调用携带的值会丢失，这种情况下只能先提前把值取出来
     * @param puid
     * @param shopIds
     * @param types
     * @return null表示无需校验，空列表表示无权限
     */
    @Override
    public Pair<Boolean, List<String>> getProductRightCampaignIdsFromGrpc(Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types) {
        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        boolean needCheck = checkUidNeedProductRight(puid, uid, shopIds, isAdminUser);
        log.info("uid:{}, isAdminUser:{}", uid, isAdminUser);
        if (!needCheck) {
            return Pair.of(false, null);
        }
        return Pair.of(true, getProductRightCampaignIds(puid, uid, isAdminUser, shopIds, types));
    }



    @Override
    public String getProductRightSqlFromGrpc(int puid, String shopIdFieldName, String campaignIdFieldName, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {

        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean b = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        log.info("uid:{}, isAdminUser:{}", uid, b);
        return getProductRightSql(shopIdFieldName, campaignIdFieldName, puid, uid, b, shopIds, types, args);
    }

    @Override
    public void putUidAndAdminUserToMDC(Integer uid, Boolean adminUser) {
        if (Objects.nonNull(uid)) {
            MDC.put(Constants.UID, String.valueOf(uid));
        }
        if (Objects.nonNull(adminUser)) {
            MDC.put(Constants.ADMIN_USER, String.valueOf(adminUser));
        }
    }




    @Override
    public Pair<Boolean, List<String>> getProductRightPortfolioIdsFromGrpc(Integer puid, List<Integer> shopIds, List<String> portfolioIds) {
        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        boolean needCheck = checkUidNeedProductRight(puid, uid, shopIds, isAdminUser);
        log.info("uid:{}, isAdminUser:{}", uid, isAdminUser);
        if (!needCheck) {
            return Pair.of(false, null);
        }
        return Pair.of(true, getProductRightPortfolioIds(puid, uid, isAdminUser, shopIds, portfolioIds));
    }


    /**
     * 获取有权限的广告组合id  先判断一下用户是否需要过滤权限，并且这个最好是只用在单店铺查询
     *
     * @param puid
     * @param uid
     * @param isAdminUser
     * @param shopIds
     * @return
     */
    @Override
    public List<String> getProductRightPortfolioIds(Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<String> portfolioIds) {
        List<Object> args = new ArrayList<>();
        List<Integer> uidNeedProductRightShopIds = getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
        ArrayList<Integer> nonSet = Lists.newArrayList(shopIds);
        nonSet.removeAll(uidNeedProductRightShopIds);
        StringBuilder sql = new StringBuilder();
        sql.append("select portfolio_id from ods_t_amazon_ad_portfolio ");
        sql.append(" where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isEmpty(portfolioIds)) {
            if (portfolioIds.size() > 10000) {
                SqlStringUtil.dealBitMapDorisInList("portfolio_id", portfolioIds, args);
            }  else {
                SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, args);
            }
        }

        List<CampaignTypeEnum> types = Lists.newArrayList(CampaignTypeEnum.values());
        sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIds, args));
        sql.append(" and portfolio_id ").append(" in ( select portfolio_id from ( ");
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(nonSet)) {
            sql.append(getNonSetProductPortfolioSql(puid, nonSet, args, types));
            flag = true;
        }
        if (CollectionUtils.isNotEmpty(uidNeedProductRightShopIds)) {
            if (flag) {
                sql.append(" union ");
            }
            sql.append(getPortfolioProductRightSql(puid, uid, uidNeedProductRightShopIds, args, types));
        }
        sql.append(")s)");
        sql.append(" limit " + Constants.TOTALSIZELIMIT);
        return odsAmazonAdPortfolioDao.queryProductRightSqlGetPortfolioIds(args, sql.toString());
    }




    /**
     * 按活动拼接sql ( portfolio_id (用户所有有权限的活动) )形式
     * @param portfolioIdFieldName
     * @param puid
     * @param uid
     * @param isAdminUser
     * @param shopIds
     * @return
     */
    @Override
    public String getProductRightPortfolioIdsSql(String portfolioIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<Object> args) {
        List<Integer> uidNeedProductRightShopIds = getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
        if (CollectionUtils.isEmpty(uidNeedProductRightShopIds)) {
            return "";
        }
        //广告组合查所有类型数据
        List<String> typesValues = Arrays.stream(CampaignTypeEnum.values()).map(CampaignTypeEnum::getCampaignType).collect(Collectors.toList());
        List<CampaignTypeEnum> types = Lists.newArrayList(CampaignTypeEnum.values());
        ArrayList<Integer> nonSet = Lists.newArrayList(shopIds);
        nonSet.removeAll(uidNeedProductRightShopIds);
        StringBuilder sql = new StringBuilder();
        sql.append("(");
        sql.append(portfolioIdFieldName).append(" in (select portfolio_id from (");
        boolean flag = false;
        if (CollectionUtils.isNotEmpty(nonSet)) {
            sql.append(getNonSetProductPortfolioSql(puid, nonSet, args, types));
            flag = true;
        }
        if (CollectionUtils.isNotEmpty(uidNeedProductRightShopIds)) {
            if (flag) {
                sql.append(" union ");
            }
            sql.append(getPortfolioProductRightSql(puid, uid, uidNeedProductRightShopIds, args, types));
        }
        sql.append(")s))");
        return sql.toString();
    }



    /**
     * 查询所有没有设置产品店铺活动id
     * @param puid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private String getNonSetProductPortfolioSql (int puid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {
        StringBuilder sql = new StringBuilder(" SELECT " +
                " a.portfolio_id portfolio_id " +
                " FROM " +
                " ods_t_amazon_ad_portfolio a " +
                " WHERE a.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("a.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealDorisInList("a.type", types.stream().map(CampaignTypeEnum::getCampaignType).collect(Collectors.toList()), args));
        return sql.toString();
    }


    /**
     * 拼接权限查询sql
     * @param puid
     * @param uid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private  String builderPortfolioProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {
        StringBuilder sqlBuilder = new StringBuilder();
        //权限sql
        sqlBuilder.append(getProductRightSql(puid, uid, shopIds, args, types));
        //如果是sb 的要查出活动下面一个asin 都没有的数据
        if (types.contains(CampaignTypeEnum.sb)) {
            sqlBuilder.append(" union  ").append(getSbNonProductSql(puid, shopIds, args));
        }
        //如果是sd 的要查出活动下面一个asin 都没有的数据
        if (types.contains(CampaignTypeEnum.sd)) {
            sqlBuilder.append(" union   ").append(getSdNonProductSql(puid, shopIds, args));
        }
        //查询所有没有产品的活动id
        sqlBuilder.append(" union ").append(getNonCreateProductSql(puid, shopIds, args, types, true));

        return sqlBuilder.toString();

    }

    private String getPortfolioProductRightSql(int puid, int uid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {
        StringBuilder sql = new StringBuilder();
        sql.append(getPortfolioNoneCampaignSql(puid, uid, shopIds, args, types));
        sql.append(" union ");
        sql.append(" select " +
                " DISTINCT ac.portfolio_id portfolio_id " +
                " from " +
                " ods_t_amazon_ad_campaign_all ac ");
        sql.append(" where ac.puid = ?  and ac.portfolio_id is not null " +
                " and ac.portfolio_id <> '' ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("ac.shop_id", shopIds, args));
        sql.append(" and ac.campaign_id in ( select campaign_id from ( ");
        sql.append(builderPortfolioProductRightSql(puid, uid, shopIds, args, types));
        sql.append(")a ) ");
        return sql.toString();
    }


    /**
     * 没有设置活动的广告组合
     * @param puid
     * @param uid
     * @param shopIds
     * @param args
     * @param types
     * @return
     */
    private String getPortfolioNoneCampaignSql(int puid, int uid, List<Integer> shopIds, List<Object> args, List<CampaignTypeEnum> types) {
        StringBuilder sql = new StringBuilder(" select " +
                " p.portfolio_id portfolio_id " +
                " FROM " +
                " ods_t_amazon_ad_portfolio p " +
                " LEFT JOIN ods_t_amazon_ad_campaign_all c  " +
                " ON " +
                " p.puid = c.puid " +
                " AND p.shop_id = c.shop_id " +
                " AND p.portfolio_id = c.portfolio_id ");
        sql.append(" and c.puid = ? ");
        args.add(puid);
        sql.append(" and p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("c.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealDorisInList("p.shop_id", shopIds, args));
        sql.append(" and c.portfolio_id is not null and c.portfolio_id <> '' ");
        sql.append(" WHERE p.puid= ? ");
        args.add(puid);
        sql.append(" and c.puid= ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealDorisInList("c.shop_id", shopIds, args));
        sql.append(SqlStringUtil.dealDorisInList("p.shop_id", shopIds, args));
        sql.append(" and c.puid IS NULL ");
        return sql.toString();
    }


    @Override
    public String getPortfolioRightSqlFromGrpc(int puid, String shopIdFieldName, String portfolioIdFieldName, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {

        int uid = NumberUtils.toInt(MDC.get(Constants.UID));
        boolean b = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));
        log.info("uid:{}, isAdminUser:{}", uid, b);
        return getPortfolioProductRightSql(shopIdFieldName, portfolioIdFieldName, puid, uid, b, shopIds, types, args);
    }



    @Override
    public String getPortfolioProductRightSql(String shopIdFieldName, String portfolioIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args) {
        List<Integer> uidNeedProductRightShopIds = getUidNeedProductRightShopIds(puid, uid, shopIds, isAdminUser);
        if (CollectionUtils.isEmpty(uidNeedProductRightShopIds)) {
            return "";
        }
        ArrayList<Integer> nonSet = Lists.newArrayList(shopIds);
        nonSet.removeAll(uidNeedProductRightShopIds);
        StringBuilder sql = new StringBuilder();
        sql.append("(");
        if (CollectionUtils.isNotEmpty(nonSet)) {
            sql.append(SqlStringUtil.dealInListNotAnd(shopIdFieldName, nonSet, args));
            sql.append(" or ");
        }
        sql.append(portfolioIdFieldName).append(" in ( select portfolio_id from( ").append(getPortfolioProductRightSql(puid, uid, uidNeedProductRightShopIds, args, types));
        sql.append(")s)");
        sql.append(")");
        return sql.toString();
    }



}
