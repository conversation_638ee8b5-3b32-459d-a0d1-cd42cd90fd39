package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdWordRootKeywordSbDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdWordRootKeywordSb;
import com.meiyunji.sponsored.service.enums.TargetMatchValueEnum;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordFrequencyAdMetricDto;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootDataQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootAggregateDataVo;
import com.meiyunji.sponsored.service.wordFrequency.vo.GetWordRootDataVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-29  17:15
 */
@Repository
public class OdsAmazonAdWordRootKeywordSbDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdWordRootKeywordSb> implements IOdsAmazonAdWordRootKeywordSbDao {

    @Override
    public Page<GetWordRootDataVo> pageList(Integer puid, GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder countSqlSb = new StringBuilder("select count(*) from ( ");
        StringBuilder sqlSb = new StringBuilder("select word_root wordRoot, count(distinct w.keyword_id) `frequency` ")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" w ")
                .append(" join ods_t_amazon_ad_sb_keyword_report r ")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.keyword_id = r.keyword_id ")
                .append(this.getAndSqlByPageList(puid, qo, argsList))
                .append(this.getWhereSqlByPageList(puid, qo, argsList))
                .append(" group by word_root ")
                .append(this.getHavingSqlByPageList(qo, argsList));
        countSqlSb.append(sqlSb).append(" ) s ");
        //排序
        this.getOrderByPageList(qo, sqlSb);

        Object[] array = argsList.toArray();
        return getPageResultByClass(qo.getPageNo(), qo.getPageSize(), countSqlSb.toString(), array, sqlSb.toString(), array, GetWordRootDataVo.class);
    }

    @Override
    public List<GetWordRootDataVo> pageListByWordRoots(Integer puid, GetWordRootDataQo qo, List<String> wordRoots) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select word_root wordRoot, count(distinct w.keyword_id) `frequency`, ")
                .append("SUM(cost) adCost,SUM(impressions) impressions,SUM(clicks) clicks,SUM(conversions14d) adOrderNum,")
                .append("SUM(conversions14d_same_sku) `adSaleNum`,SUM(sales14d) adSale, SUM(sales14d_same_sku) `adSales`, SUM(units_sold14d) orderNum")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" w ")
                .append(" join ods_t_amazon_ad_sb_keyword_report r ")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.keyword_id = r.keyword_id ")
                .append(this.getAndSqlByPageList(puid, qo, argsList))
                .append(this.getWhereSqlByPageList(puid, qo, argsList))
                .append(SqlStringUtil.dealInList("word_root", wordRoots, argsList))
                .append(" group by word_root ")
                .append(SqlStringUtil.orderByField("word_root", wordRoots, argsList));

        Object[] array = argsList.toArray();
        return getJdbcTemplate().query(sqlSb.toString(), array, new BeanPropertyRowMapper<>(GetWordRootDataVo.class));
    }

    @Override
    public WordFrequencyAdMetricDto getPageListAdMetricDto(Integer puid, GetWordRootDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(frequency) sumFrequency, SUM(adCost) sumCost, SUM(sales14d) sumAdSale, SUM(conversions14d) sumAdOrderNum, SUM(unitsSold14d) sumOrderNum from (");
        sqlSb.append("select word_root wordRoot, count(distinct w.keyword_id) `frequency`, ")
                .append(" SUM(cost) adCost, SUM(sales14d) sales14d, SUM(conversions14d) conversions14d, SUM(units_sold14d) unitsSold14d ")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" w ")
                .append(" join ods_t_amazon_ad_sb_keyword_report r ")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.keyword_id = r.keyword_id ")
                .append(this.getAndSqlByPageList(puid, qo, argsList))
                .append(this.getWhereSqlByPageList(puid, qo, argsList))
                .append(" group by word_root ")
                .append(this.getHavingSqlByPageList(qo, argsList));
        sqlSb.append(" ) s");
        List<WordFrequencyAdMetricDto> list = getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordFrequencyAdMetricDto.class));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    @Override
    public GetWordRootAggregateDataVo getPageListAggregateData(Integer puid, GetWordRootAggregateDataQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select SUM(`frequency`) `frequency`, ")
                .append("SUM(adCost) adCost,SUM(impressions) impressions,SUM(clicks) clicks,SUM(adOrderNum) adOrderNum,")
                .append("SUM(adSaleNum) `adSaleNum`,SUM(adSale) adSale, SUM(adSales) `adSales`, SUM(orderNum) orderNum")
                .append(" from (");
        sqlSb.append("select word_root wordRoot, count(distinct w.keyword_id) `frequency`, ")
                .append("SUM(cost) adCost,SUM(impressions) impressions,SUM(clicks) clicks,SUM(conversions14d) adOrderNum,")
                .append("SUM(conversions14d_same_sku) `adSaleNum`,SUM(sales14d) adSale, SUM(sales14d_same_sku) `adSales`, SUM(units_sold14d) orderNum")
                .append(" from ").append(this.getJdbcHelper().getTable()).append(" w ")
                .append(" join ods_t_amazon_ad_sb_keyword_report r ")
                .append(" on w.puid = r.puid and w.shop_id = r.shop_id and w.keyword_id = r.keyword_id ")
                .append(this.getAndSqlByPageList(puid, qo, argsList))
                .append(this.getWhereSqlByPageList(puid, qo, argsList))
                .append(" group by word_root ")
                .append(this.getHavingSqlByPageList(qo, argsList));
        sqlSb.append(" ) s");
        List<GetWordRootAggregateDataVo> list = getJdbcTemplate().query(sqlSb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(GetWordRootAggregateDataVo.class));
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : new GetWordRootAggregateDataVo();
    }

    private String getAndSqlByPageList(Integer puid, GetWordRootAggregateDataQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(qo.getShopId());
        argsList.add(qo.getStartDate());
        argsList.add(qo.getEndDate());

        List<String> matchTypeList = StringUtil.stringToList(qo.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(TargetMatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                sb.append(" and 1=0 ");
                return sb.toString();
            }
        }
        if (CollectionUtils.isNotEmpty(matchTypes)) {
            //doris不区分大小写
            sb.append(SqlStringUtil.dealInList("lower(r.match_type)", matchTypes, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupIds())) {
            List<String> list = StringUtil.splitStr(qo.getGroupIds());
            sb.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", qo.getGroupIdList(), argsList));
        }
        return sb.toString();
    }

    private String getWhereSqlByPageList(Integer puid, GetWordRootAggregateDataQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where ");
        //在where条件中使用1=0进行中断查询，1=0需要放在所有查询条件最前面，Alibaba durid才不会报错
        List<String> matchTypeList = StringUtil.stringToList(qo.getMatchType(), StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(TargetMatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件不是('紧密匹配'，'宽泛匹配'),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)) {
                sb.append(" 1=0 ");
                return sb.toString();
            }
        }
        sb.append(" w.puid = ? and w.shop_id = ? ");
        argsList.add(puid);
        argsList.add(qo.getShopId());

        if (CollectionUtils.isNotEmpty(matchTypes)) {
            //doris不区分大小写
            sb.append(SqlStringUtil.dealInList("lower(w.match_type)", matchTypes, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getCampaignIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("w.campaign_id", qo.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupIds())) {
            List<String> list = StringUtil.splitStr(qo.getGroupIds());
            sb.append(SqlStringUtil.dealBitMapDorisInList("w.ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("w.ad_group_id", qo.getGroupIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getSearchValue()) && StringUtils.isNotBlank(qo.getSearchField()) && StringUtils.isNotBlank(qo.getSearchType())) {
            GetWordRootDataQo.SearchFieldEnum searchFieldEnum = GetWordRootDataQo.SearchFieldEnum.getSearchField(qo.getSearchField());
            if (searchFieldEnum != null) {
                if (SearchTypeEnum.BLUR.getValue().equals(qo.getSearchType())) { //模糊搜索
                    sb.append(" and ").append(searchFieldEnum.getField()).append(" like ? ");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(qo.getSearchValue().trim()) + "%");
                } else {//默认精确
                    List<String> searchValueList = StringUtil.splitStr(qo.getSearchValue().trim(), StringUtil.SPECIAL_COMMA);
                    if (searchValueList.size() > 1) {
                        sb.append(SqlStringUtil.dealInList(searchFieldEnum.getField(), searchValueList, argsList));
                    } else {
                        sb.append(" and ").append(searchFieldEnum.getField()).append(" = ?");
                        argsList.add(qo.getSearchValue().trim());
                    }
                }
            }
        }

        if (StringUtils.isNotBlank(qo.getWordFrequencyType())) {
            List<Integer> wordFrequencyTypeList = new ArrayList<>();
            for (String wordFrequencyType : qo.getWordFrequencyType().split(StringUtil.SPLIT_COMMA)) {
                WordRoot.WordFrequencyType wordFrequencyTypeEnum = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(wordFrequencyType));
                if (wordFrequencyTypeEnum != null) {
                    wordFrequencyTypeList.add(wordFrequencyTypeEnum.getType());
                }
            }
            if (CollectionUtils.isNotEmpty(wordFrequencyTypeList)) {
                sb.append(SqlStringUtil.dealInList("w.word_frequency_type", wordFrequencyTypeList, argsList));
            }
        }
        return sb.toString();
    }

    private String getHavingSqlByPageList(GetWordRootAggregateDataQo qo, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        if (!qo.getUseAdvanced()) {
            return "";
        }

        BigDecimal shopSales = qo.getShopSales() != null ? qo.getShopSales() : BigDecimal.valueOf(0);

        sb.append(" having 1=1 ");
        //频率
        if (qo.getFrequencyMin() != null) {
            sb.append(" and frequency >= ? ");
            argsList.add(qo.getFrequencyMin());
        }
        if (qo.getFrequencyMax() != null) {
            sb.append(" and frequency <= ? ");
            argsList.add(qo.getFrequencyMax());
        }
        //花费
        if (qo.getCostMin() != null) {
            sb.append(" and SUM(cost) >= ? ");
            argsList.add(qo.getCostMin());
        }
        if (qo.getCostMax() != null) {
            sb.append(" and SUM(cost) <= ? ");
            argsList.add(qo.getCostMax());
        }
        //展示量
        if (qo.getImpressionsMin() != null) {
            sb.append(" and SUM(impressions) >= ? ");
            argsList.add(qo.getImpressionsMin());
        }
        if (qo.getImpressionsMax() != null) {
            sb.append(" and SUM(impressions) <= ? ");
            argsList.add(qo.getImpressionsMax());
        }
        //点击量
        if (qo.getClicksMin() != null) {
            sb.append(" and SUM(clicks) >= ? ");
            argsList.add(qo.getClicksMin());
        }
        if (qo.getClicksMax() != null) {
            sb.append(" and SUM(clicks) <= ? ");
            argsList.add(qo.getClicksMax());
        }
        //广告订单量
        if (qo.getOrderNumMin() != null) {
            sb.append(" and SUM(conversions14d) >= ? ");
            argsList.add(qo.getOrderNumMin());
        }
        if (qo.getOrderNumMax() != null) {
            sb.append(" and SUM(conversions14d) <= ? ");
            argsList.add(qo.getOrderNumMax());
        }
        //本广告产品订单量
        if (qo.getAdSaleNumMin() != null) {
            sb.append(" and ifnull(SUM(conversions14d_same_sku), 0) >= ? ");
            argsList.add(qo.getAdSaleNumMin());
        }
        if (qo.getAdSaleNumMax() != null) {
            sb.append(" and ifnull(SUM(conversions14d_same_sku), 0) <= ? ");
            argsList.add(qo.getAdSaleNumMax());
        }
        //广告销售额
        if (qo.getSalesMin() != null) {
            sb.append(" and SUM(sales14d) >= ? ");
            argsList.add(qo.getSalesMin());
        }
        if (qo.getSalesMax() != null) {
            sb.append(" and SUM(sales14d) <= ? ");
            argsList.add(qo.getSalesMax());
        }
        //本广告产品销售额
        if (qo.getAdSalesMin() != null) {
            sb.append(" and ifnull(SUM(sales14d_same_sku), 0) >= ? ");
            argsList.add(qo.getAdSalesMin());
        }
        if (qo.getAdSalesMax() != null) {
            sb.append(" and ifnull(SUM(sales14d_same_sku), 0) <= ? ");
            argsList.add(qo.getAdSalesMax());
        }
        //广告销量
        if (qo.getAdSalesTotalMin() != null) {
            sb.append(" and ifnull(SUM(units_sold14d), 0) >= ? ");
            argsList.add(qo.getAdSalesTotalMin());
        }
        if (qo.getAdSalesTotalMax() != null) {
            sb.append(" and ifnull(SUM(units_sold14d), 0) <= ? ");
            argsList.add(qo.getAdSalesTotalMax());
        }
        //本广告产品销量
        if (qo.getAdSelfSaleNumMin() != null) {
            sb.append(" and ifnull(SUM(ad_units_sold14d), 0) >= ? ");
            argsList.add(qo.getAdSelfSaleNumMin());
        }
        if (qo.getAdSelfSaleNumMax() != null) {
            sb.append(" and ifnull(SUM(ad_units_sold14d), 0) <= ? ");
            argsList.add(qo.getAdSelfSaleNumMax());
        }
        //ctr点击率（clicks/impressions）
        if (qo.getClickRateMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) >= ?");
            argsList.add(qo.getClickRateMin());
        }
        if (qo.getClickRateMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(clicks)/SUM(impressions),0),4) <= ?");
            argsList.add(qo.getClickRateMax());
        }
        //cvr订单转化率
        if (qo.getSalesConversionRateMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(conversions14d)/SUM(clicks),0),4) >= ?");
            argsList.add(qo.getSalesConversionRateMin());
        }
        if (qo.getSalesConversionRateMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(conversions14d)/SUM(clicks),0),4) <= ?");
            argsList.add(qo.getSalesConversionRateMax());
        }
        //CPA
        if (qo.getCpaMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(conversions14d),0), 2) >= ?");
            argsList.add(qo.getCpaMin());
        }
        if (qo.getCpaMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(conversions14d),0), 2) <= ?");
            argsList.add(qo.getCpaMax());
        }
        //cpc  平均点击费用
        if (qo.getCpcMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) >= ?");
            argsList.add(qo.getCpcMin());
        }
        if (qo.getCpcMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(clicks),0),2) <= ?");
            argsList.add(qo.getCpcMax());
        }
        //acos
        if (qo.getAcosMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(sales14d),0),4) >= ?");
            argsList.add(qo.getAcosMin());
        }
        if (qo.getAcosMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(cost)/SUM(sales14d),0),4) <= ?");
            argsList.add(qo.getAcosMax());
        }
        // roas
        if (qo.getRoasMin() != null) {
            sb.append(" and ROUND(ifnull(SUM(sales14d)/SUM(cost),0),2) >= ?");
            argsList.add(qo.getRoasMin());
        }
        if (qo.getRoasMax() != null) {
            sb.append(" and ROUND(ifnull(SUM(sales14d)/SUM(cost),0),2) <= ?");
            argsList.add(qo.getRoasMax());
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAcotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // acots  需要乘以店铺销售额
        if (qo.getAcotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(cost),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAcotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // asots 需要乘以店铺销售额
        if (qo.getAsotsMin() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(sales14d),0) / ").append(shopSales).append(" ),4) >= ? ");
                argsList.add(qo.getAsotsMin());
            } else {
                sb.append(" and 0 >= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        // asots  需要乘以店铺销售额
        if (qo.getAsotsMax() != null) {
            if (shopSales.doubleValue() > 0) {
                sb.append(" and ROUND((ifnull(SUM(sales14d),0) / ").append(shopSales).append(" ),4) <= ? ");
                argsList.add(qo.getAsotsMax());
            } else {
                sb.append(" and 0 <= ? ");
                argsList.add(qo.getAcotsMin());
            }
        }
        //其他产品广告订单量
        if (qo.getAdOtherOrderNumMin() != null) {
            sb.append(" and ifnull(SUM(conversions14d) - SUM(conversions14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherOrderNumMin());
        }
        if (qo.getAdOtherOrderNumMax() != null) {
            sb.append(" and ifnull(SUM(conversions14d) - SUM(conversions14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherOrderNumMax());
        }
        //其他产品广告销售额
        if (qo.getAdOtherSalesMin() != null) {
            sb.append(" and ifnull(SUM(sales14d) - SUM(sales14d_same_sku), 0) >= ?");
            argsList.add(qo.getAdOtherSalesMin());
        }
        if (qo.getAdOtherSalesMax() != null) {
            sb.append(" and ifnull(SUM(sales14d) - SUM(sales14d_same_sku), 0) <= ?");
            argsList.add(qo.getAdOtherSalesMax());
        }
        //其他产品广告销量
//        if (qo.getAdOtherSaleNumMin() != null) {
//            sb.append(" and ifnull(SUM(units_sold14d) - SUM(ad_order_num), 0) >= ?");
//            argsList.add(qo.getAdOtherSaleNumMin());
//        }
//        if (qo.getAdOtherSaleNumMax() != null) {
//            sb.append(" and ifnull(SUM(units_sold14d) - SUM(ad_order_num), 0) <= ?");
//            argsList.add(qo.getAdOtherSaleNumMax());
//        }
        return sb.toString();
    }

    private void getOrderByPageList(GetWordRootDataQo qo, StringBuilder sb) {
        if (StringUtils.isNotBlank(qo.getOrderField()) && StringUtils.isNotBlank(qo.getOrderValue())) {
            String orderField = this.getOrderField(qo.getOrderField());
            if (StringUtils.isNotBlank(orderField)) {
                sb.append(" order by ").append(orderField);
                if (OrderTypeEnum.desc.getType().equals(qo.getOrderValue())) {
                    sb.append(" desc ");
                }
            }
        } else {
            sb.append(" order by frequency desc ");
        }
    }

    public String getOrderField(String field) {
        switch (field) {
            case "wordRoot":
                return " wordRoot ";
            case "adCost":
                return " SUM(cost) ";
            case "impressions":
                return " SUM(impressions) ";
            case "clicks":
                return " SUM(clicks) ";
            //广告订单量
            case "adOrderNum":
                return " SUM(conversions14d) ";
            //本广告产品订单量
            case "adSaleNum":
                return " SUM(conversions14d_same_sku) ";
            //广告销售额
            case "adSale":
                return " SUM(sales14d) ";
            //本广告产品销售额
            case "adSales":
                return " SUM(sales14d_same_sku) ";
            //广告销量
            case "orderNum":
                return " SUM(units_ordered14d) ";
            //本广告产品销量
//            case "adSelfSaleNum":
//                return " SUM(ad_order_num) ";
            //广告点击率ctr
            case "ctr":
                return " ifnull(SUM(clicks)/SUM(impressions),0) ";
            //广告转化率cvr
            case "cvr":
                return " ifnull(SUM(conversions14d)/SUM(clicks),0) ";
            case "cpa":
                return " ifnull(SUM(cost)/SUM(conversions14d),0) ";
            case "adCostPerClick":
                return " ifnull(SUM(cost)/SUM(clicks),0) ";
            case "acos":
                return " ifnull(SUM(cost)/SUM(sales14d),0) ";
            case "roas":
                return " ifnull(SUM(sales14d)/SUM(cost),0) ";
            case "acots":
                return " SUM(cost) ";
            case "asots":
                return " SUM(sales14d) ";
            //其他产品广告订单量
            case "adOtherOrderNum":
                return " ifnull(SUM(conversions14d) - SUM(conversions14d_same_sku),0) ";
            //其他产品广告销售额
            case "adOtherSales":
                return " ifnull(SUM(sales14d) - SUM(sales14d_same_sku),0) ";
            //其他产品广告销量
//            case "adOtherSaleNum":
//                return " ifnull(SUM(units_ordered14d) - SUM(ad_order_num),0) ";
            default:
                return " frequency ";
        }
    }
}
