package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdSalesmanDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdSalesmanTopDataDto;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dto.GroupCampaignDto;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdProductDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdProductTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryFieldEnum;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProductReport;


import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

import java.util.ArrayList;
import java.util.List;

import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * amazon广告产品报告表(OdsAmazonAdProductReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
@Repository
public class OdsAmazonAdProductReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdProductReport> implements IOdsAmazonAdProductReportDao {

    @Override
    public List<GroupCampaignDto> getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                                                String searchType, String searchValue, String startDate, String endDate) {
        //不带查询条件 直接返回空数组
        if (StringUtils.isBlank(searchType) || StringUtils.isBlank(searchValue)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(" select 'sp' as type, r.ad_group_id ad_group_id, any(r.campaign_id) as campaign_id from ods_t_product p ")
                .append(" join ods_t_amazon_ad_product_report r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ")
                .append(" and r.puid = ? and r.count_day >= ? and r.count_day <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
            sql.append(SqlStringUtil.dealInList("p.asin", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
            sql.append(SqlStringUtil.dealInList("p.sku", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
            sql.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, args))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, args)).append(")")
                    .append(") ");
        }

        sql.append(" group by r.ad_group_id ");
        return getJdbcTemplate().query(sql.toString(), (re, i) -> GroupCampaignDto.builder()
                .type(re.getString("type"))
                .adGroupId(re.getString("ad_group_id"))
                .campaignId(re.getString("campaign_id"))
                .build(), args.toArray());
    }


    @Override
    public List<String> getGroupIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                        String searchType, String searchValue, String startDate, String endDate) {

        //不带查询条件 直接返回空数组
        if (StringUtils.isBlank(searchType) || StringUtils.isBlank(searchValue)) {
            return new ArrayList<>();
        }
        StringBuilder sql = new StringBuilder(" select distinct r.ad_group_id ad_group_id from ods_t_product p ")
                .append(" join ods_t_amazon_ad_product_report r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ")
                .append(" and r.puid = ? and r.count_day >= ? and r.count_day <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", campaignIds, args));
        }
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
            sql.append(SqlStringUtil.dealInList("p.asin", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
            sql.append(SqlStringUtil.dealInList("p.sku", searchValues, args));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
            sql.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, args))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, args)).append(")")
                    .append(") ");
        }
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<String> getAsinByParentAsinAndGroupIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> groupIds,
                                                                  String parentAsin, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder(" select distinct p.asin from ods_t_product p ")
                .append(" join ods_t_amazon_ad_product_report r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ")
                .append(" and r.puid = ? and r.count_day >= ? and r.count_day <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?)) ");
        args.add(parentAsin);
        args.add(parentAsin);
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<AsinListDto> getAsinByParentAsinList(Integer puid, List<Integer> shopIds, List<String> groupIds, List<String> parentAsinList, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder(" select distinct p.shop_id shopId, p.asin asin, p.sku msku, p.parent_asin parentAsin  from ods_t_product p ")
                .append(" join ods_t_amazon_ad_product_report r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ")
                .append(" and r.puid = ? and r.count_day >= ? and r.count_day <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" where p.puid = ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("p.shop_id", shopIds, args));
        sql.append(" and (")
                .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", parentAsinList, args))
                .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", parentAsinList, args)).append(")")
                .append(") ");
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(AsinListDto.class));
    }

    @Override
    public List<String> getAsinByMskuAndGroupIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> groupIds,
                                                                     String msku, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder(" select distinct r.asin from ods_t_amazon_ad_product_report r ")
                .append(" where r.puid = ? and r.count_day >= ? and r.count_day <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealInList("r.shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("r.ad_group_id", groupIds, args));
        }
        sql.append(" and r.sku = ? ");
        args.add(msku);
        return getJdbcTemplate().queryForList(sql.toString(), String.class, args.toArray());
    }

    @Override
    public List<AsinListDto> getAsinByMskuList(Integer puid, List<Integer> shopIds, List<String> groupIds, List<String> mskuList, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder(" select shop_id shopId, asin, sku msku from ods_t_amazon_ad_product_report ")
                .append(" where puid = ? and count_day >= ? and count_day <= ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        if (CollectionUtils.isNotEmpty(groupIds)) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupIds, args));
        }
        sql.append(SqlStringUtil.dealInList("sku", mskuList, args));
        sql.append(" group by shop_id, asin, sku ");
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(AsinListDto.class));
    }

    @Override
    public List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByProduct(Integer puid, List<Integer> shopIdList, String marketPlaceId,
                                                                             String searchType, String searchValue, String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select 'sp' as type, r.shop_id shopId, r.campaign_id campaignId, r.ad_group_id adGroupId, r.ad_id adId from ods_t_product p")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ");
        sb.append(" and r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("r.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and r.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        if (StringUtils.isNotBlank(startDate)) {
            sb.append(" and r.count_day >= ? ");
            argsList.add(startDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            sb.append(" and r.count_day <= ? ");
            argsList.add(endDate);
        }
        sb.append(" where p.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and p.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
            sb.append(SqlStringUtil.dealInList("p.asin", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
            sb.append(SqlStringUtil.dealInList("p.sku", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
            sb.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, argsList))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, argsList)).append(")")
                    .append(") ");
        }
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdProductPerspectiveBO.class));
    }

    @Override
    public List<String> adIdListByProduct(Integer puid, List<Integer> shopIdList, String marketPlaceId,
                                          String searchType, String searchValue, String startDate, String endDate) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select distinct r.ad_id from ods_t_product p")
                .append(" join ").append(this.getJdbcHelper().getTable()).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id and p.asin = r.asin and p.sku = r.sku ");
        sb.append(" and r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("r.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and r.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }
        if (StringUtils.isNotBlank(startDate)) {
            sb.append(" and r.count_day >= ? ");
            argsList.add(startDate);
        }
        if (StringUtils.isNotBlank(endDate)) {
            sb.append(" and r.count_day <= ? ");
            argsList.add(endDate);
        }
        sb.append(" where p.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("p.shop_id", shopIdList, argsList));
        }
        if (StringUtils.isNotBlank(marketPlaceId)) {
            sb.append(" and p.marketplace_id = ? ");
            argsList.add(marketPlaceId);
        }

        List<String> searchValues = StringUtil.splitStr(searchValue, StringUtil.SPECIAL_COMMA);
        if (ViewBaseParam.SearchTypeEnum.ASIN.getValue().equals(searchType)) {
//            sb.append(" and p.asin = ? ");
            sb.append(SqlStringUtil.dealInList("p.asin", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.MSKU.getValue().equals(searchType)) {
//            sb.append(" and p.sku = ? ");
            sb.append(SqlStringUtil.dealInList("p.sku", searchValues, argsList));
        } else if (ViewBaseParam.SearchTypeEnum.PARENT_ASIN.getValue().equals(searchType)) {
//            sb.append(" and (p.parent_asin = ? or (p.id = p.parent_id and p.asin = ?)) ");
//            argsList.add(searchValue);
            sb.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("p.parent_asin", searchValues, argsList))
                    .append(" or (p.id = p.parent_id ").append(SqlStringUtil.dealInList("p.asin", searchValues, argsList)).append(")")
                    .append(") ");
        }
//        argsList.add(searchValue);
        return getJdbcTemplate().queryForList(sb.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<String> getGroupIdByAsin(Integer puid, GetWordRootAggregateDataQo qo, String start, String end) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select distinct ad_group_id from ").append(this.getJdbcHelper().getTable());
        sql.append(" where puid = ? and shop_id = ? ");
        argsList.add(qo.getPuid());
        argsList.add(qo.getShopId());
        if (StringUtils.isNotEmpty(start)) {
            sql.append(" and count_day >= ? ");
            argsList.add(start);
        }
        if (StringUtils.isNotEmpty(end)) {
            sql.append(" and count_day <= ? ");
            argsList.add(end);
        }
        if (StringUtils.isNotBlank(qo.getCampaignIds())) {
            List<String> campaignIdList = StringUtil.splitStr(qo.getCampaignIds(), StringUtil.SPLIT_COMMA);
            sql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignIdList, argsList));
        }
        if (StringUtils.isNotBlank(qo.getGroupIds())) {
            List<String> groupIdList = StringUtil.splitStr(qo.getGroupIds(), StringUtil.SPLIT_COMMA);
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", groupIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(qo.getGroupIdList())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", qo.getGroupIdList(), argsList));
        }
        if (StringUtils.isNotBlank(qo.getAsin())) {
            List<String> asinList = StringUtil.splitStr(qo.getAsin(), StringUtil.SPECIAL_COMMA);
            sql.append(SqlStringUtil.dealInList("asin", asinList, argsList));
        }
        return this.getJdbcTemplate().queryForList(sql.toString(), argsList.toArray(), String.class);
    }



    @Override
    public String adProductByAsinOrSkuQuerySql(Integer puid, List<Integer> shopIdList,
                                               List<String> marketplaceIdList, List<String> ads,
                                               String currency, String startDate, String endDate,
                                               List<Object> argsList, boolean isAsin, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds,
                                               Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        StringBuilder sb = new StringBuilder();

        sb.append(" select ");
        sb.append(" GROUP_CONCAT(DISTINCT t3.shop_ids) shop_ids, ");
        sb.append(" ifnull(sum(t3.cost * c.rate), 0) cost, ");
        sb.append(" ifnull(sum(t3.total_sales * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(t3.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(t3.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t3.order_num), 0) orderNum, ");
        sb.append(" ifnull(sum(t3.sale_num), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t3.cost * c.rate), 0)/ ifnull(sum(t3.total_sales * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t3.total_sales * c.rate), 0)/ ifnull(sum(t3.cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ROUND(ifnull(sum(t3.clicks)/ sum(t3.impressions), 0), 4) clickRate, ");
        sb.append(" ROUND(ifnull(sum(t3.order_num)/ sum(t3.clicks), 0), 4) conversionRate, ");
        sb.append(" ROUND(ifnull(sum(t3.cost * c.rate)/ sum(t3.clicks), 0), 4) cpc, ");
        sb.append(" ROUND(ifnull(sum(t3.cost * c.rate)/ sum(t3.order_num), 0), 4) cpa, ");
        if (isAsin) {
            sb.append(" t3.asin label , ");
        } else {
            sb.append(" t3.sku label, ");
        }
        sb.append(" any(t3.asin) asin ");
        sb.append(" from ( ");
        sb.append(" SELECT ");
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t1.shop_id AS CHAR)) shop_ids, ");
        sb.append(" t1.marketplace_id AS marketplace_id, ");
        sb.append(" ifnull(sum(t1.cost), 0) cost, ");
        sb.append(" ifnull(sum(t1.total_sales), 0) total_sales, ");
        sb.append(" ifnull(sum(t1.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t1.order_num), 0) sale_num , ");
        sb.append(" ifnull(sum(t1.sale_num), 0) order_num , ");
        if (isAsin) {
            sb.append("   t1.ASIN asin,  ");
            sb.append("   any(t1.sku) sku,  ");
        } else {
            sb.append("  t1.sku sku,  ");
            sb.append("   any(t1.ASIN) asin,  ");
        }

        sb.append(" DATE_FORMAT(t1.count_date, '%Y%m') AS MONTH ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_product_report t1 ");
        sb.append(" WHERE  ");
        sb.append(" t1.puid = ?  ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t1.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t1.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(ads)) {
            if (isAsin) {
                sb.append(" and  t1.asin  ");
            } else {
                sb.append(" and t1.sku  ");
            }
            sb.append(" in ('").append(StringUtils.join(ads, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', t1.marketplace_id, t1.count_day)", siteToday, argsList));
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and t1.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("t1.campaign_id", campaignIds, argsList));
        }

        sb.append(" group by ");
        if (isAsin) {
            sb.append("   asin  ");
        } else {
            sb.append("  sku  ");
        }
        sb.append("  , marketplace_id, MONTH ");

        sb.append(" union all ");


        sb.append(" SELECT ");
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t2.shop_id AS CHAR)) shop_ids, ");
        sb.append(" t2.marketplace_id AS marketplace_id, ");
        sb.append(" ifnull(sum(t2.cost), 0) cost, ifnull(sum(t2.sales14d), 0) total_sales, ");
        sb.append(" ifnull(sum(t2.impressions), 0) impressions, ifnull(sum(t2.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t2.conversions14d), 0) order_num, ");
        sb.append(" ifnull(sum(t2.units_ordered14d), 0) sale_num, ");


        if (isAsin) {
            sb.append("   t2.ASIN asin,  ");
            sb.append("   any(t2.sku) sku,  ");
        } else {
            sb.append("  t2.sku sku,  ");
            sb.append("   any(t2.ASIN) asin,  ");
        }

        sb.append(" DATE_FORMAT(t2.count_date, '%Y%m') AS MONTH ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_sd_product_report t2 ");
        sb.append(" WHERE  ");
        sb.append(" t2.puid = ?  and t2.asin is not null and t2.asin != '' ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t2.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t2.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(ads)) {
            if (isAsin) {
                sb.append(" and  t2.asin  ");
            } else {
                sb.append(" and t2.sku  ");
            }
            sb.append(" in ('").append(StringUtils.join(ads, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', t2.marketplace_id, t2.count_day)", siteToday, argsList));
            sb.append(" and t2.count_day >= ? and t2.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and t2.count_day >= ? and t2.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and t2.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("t2.campaign_id", campaignIds, argsList));
        }


        sb.append(" group by ");
        if (isAsin) {
            sb.append("   asin  ");
        } else {
            sb.append("  sku  ");
        }
        sb.append("  , marketplace_id, MONTH  ) t3 ");

        sb.append(" JOIN (SELECT puid,`month`,`from`,rate FROM dim_currency_rate WHERE " );
        sb.append(" puid = ? ");
        argsList.add(puid);
        sb.append(" AND `to` = ? ) c ");
        argsList.add(currency);
        sb.append(" ON t3.month = c.month ");
        sb.append(" JOIN dim_marketplace_info m ON m.marketplace_id = t3.marketplace_id AND c.`from` = m.currency " );
        if (isAsin) {
            sb.append(" group by  t3.asin  ");
        } else {
            sb.append(" group by t3.sku  ");
        }
        return sb.toString();
    }

    @Override
    public String adProductQuerySql(Integer puid, List<Integer> shopIdList,
                                    List<String> marketplaceIdList, List<String> adIds,
                                    String currency, String startDate, String endDate,
                                    List<Object> argsList, DashboardQueryFieldEnum dashboardQueryFieldEnum,
                                    List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds,
                                    Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, LocalDate categoryQueryDate, List<String> noFind){
        if (DashboardQueryFieldEnum.ASIN_QUERY_TYPE == dashboardQueryFieldEnum) {
            return adProductByAsinOrSkuQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList, true, siteToday,
                    isSiteToday, portfolioIds, campaignIds, noZero, dashboardDataFieldEnum);
        } else if (DashboardQueryFieldEnum.SKU_QUERY_TYPE == dashboardQueryFieldEnum) {
            return adProductByAsinOrSkuQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList, false, siteToday,
                    isSiteToday, portfolioIds, campaignIds, noZero, dashboardDataFieldEnum);
        } else if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE == dashboardQueryFieldEnum) {
            return adProductGroupByParentAsinQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList, siteToday,
                    isSiteToday, portfolioIds, campaignIds, noZero, dashboardDataFieldEnum);
        } else if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE == dashboardQueryFieldEnum) {
            return adProductGroupByCategoryQuerySql(puid, shopIdList, marketplaceIdList, adIds,
                    currency, startDate, endDate, argsList, siteToday,
                    isSiteToday, portfolioIds, campaignIds, noZero, dashboardDataFieldEnum, categoryQueryDate, noFind);
        }  else {
            return null;
        }

    }


    @Override
    public String adProductGroupByParentAsinQuerySql(Integer puid, List<Integer> shopIdList,
                                                     List<String> marketplaceIdList, List<String> adIds,
                                                     String currency, String startDate, String endDate,
                                                     List<Object> argsList, List<String> siteToday, Boolean isSiteToday,
                                                     List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum) {
        StringBuilder sb = new StringBuilder();

        sb.append(" select ");
        sb.append(" GROUP_CONCAT(DISTINCT t3.shop_ids) shop_ids, ");
        sb.append(" ifnull(sum(t3.cost * c.rate), 0) cost, ");
        sb.append(" ifnull(sum(t3.total_sales * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(t3.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(t3.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t3.order_num), 0) orderNum, ");
        sb.append(" ifnull(sum(t3.sale_num), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t3.cost * c.rate), 0)/ ifnull(sum(t3.total_sales * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t3.total_sales * c.rate), 0)/ ifnull(sum(t3.cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ROUND(ifnull(sum(t3.clicks)/ sum(t3.impressions), 0), 4) clickRate, ");
        sb.append(" ROUND(ifnull(sum(t3.order_num)/ sum(t3.clicks), 0), 4) conversionRate, ");
        sb.append(" ROUND(ifnull(sum(t3.cost * c.rate)/ sum(t3.clicks), 0), 4) cpc, ");
        sb.append(" ROUND(ifnull(sum(t3.cost * c.rate)/ sum(t3.order_num), 0), 4) cpa, ");
        sb.append(" GROUP_CONCAT(DISTINCT t3.asin) ASIN, ");
        sb.append(" t4.parent_asin label ");
        sb.append(" from ( ");
        sb.append(" SELECT ");
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t1.shop_id AS CHAR)) shop_ids, ");
        sb.append(" t1.marketplace_id AS marketplace_id, ");
        sb.append(" ifnull(sum(t1.cost), 0) cost, ");
        sb.append(" ifnull(sum(t1.total_sales), 0) total_sales, ");
        sb.append(" ifnull(sum(t1.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t1.order_num), 0) sale_num, ");
        sb.append(" ifnull(sum(t1.sale_num), 0) order_num, ");
        sb.append(" t1.ASIN, ");
        sb.append(" DATE_FORMAT(t1.count_date, '%Y%m') AS MONTH ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_product_report t1 ");
        sb.append(" WHERE  ");
        sb.append(" t1.puid = ?  ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t1.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t1.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(adIds)) {
            sb.append("and t1.asin in ('").append(StringUtils.join(adIds, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', t1.marketplace_id, t1.count_day)", siteToday, argsList));
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and t1.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("t1.campaign_id", campaignIds, argsList));
        }


        sb.append(" GROUP BY  ASIN, marketplace_id, MONTH ");

        sb.append(" union all ");


        sb.append(" SELECT ");
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t2.shop_id AS CHAR)) shop_ids, ");
        sb.append(" t2.marketplace_id AS marketplace_id, ");
        sb.append(" ifnull(sum(t2.cost), 0) cost, ifnull(sum(t2.sales14d), 0) total_sales, ");
        sb.append(" ifnull(sum(t2.impressions), 0) impressions, ifnull(sum(t2.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t2.conversions14d), 0) order_num, ");
        sb.append(" ifnull(sum(t2.units_ordered14d), 0) sale_num, ");
        sb.append(" t2.ASIN asin, ");
        sb.append(" DATE_FORMAT(t2.count_date, '%Y%m') AS MONTH ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_sd_product_report t2  ");
        sb.append(" WHERE  ");
        sb.append(" t2.puid = ?  and t2.asin is not null and t2.asin != '' ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t2.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t2.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(adIds)) {
            sb.append("and t2.asin in ('").append(StringUtils.join(adIds, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', t2.marketplace_id, t2.count_day)", siteToday, argsList));
            sb.append(" and t2.count_day >= ? and t2.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and t2.count_day >= ? and t2.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and t2.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("t2.campaign_id", campaignIds, argsList));
        }

        sb.append(" GROUP BY  ASIN, marketplace_id, MONTH ) t3 ");

        sb.append(" JOIN (");
        sb.append(" select asin, max ( if(parent_asin IS NULL OR parent_asin = '', asin, parent_asin ) )  parent_asin  from ods_t_product where ");
        sb.append(" puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(adIds)) {
            sb.append("and asin in ('").append(StringUtils.join(adIds, "','")).append("') ");
        }
        sb.append(" group by asin ) t4 ON t3.asin = t4.asin ");
        sb.append(" JOIN (SELECT puid,`month`,`from`,rate FROM dim_currency_rate WHERE " );
        sb.append(" puid = ? ");
        argsList.add(puid);
        sb.append(" AND `to` = ? ) c ");
        argsList.add(currency);
        sb.append(" ON t3.month = c.month ");
        sb.append(" JOIN dim_marketplace_info m ON m.marketplace_id = t3.marketplace_id AND c.`from` = m.currency " );
        sb.append(" GROUP by t4.parent_asin ");

        return sb.toString();
    }



    @Override
    public String adProductGroupByCategoryQuerySql(Integer puid, List<Integer> shopIdList,
                                                   List<String> marketplaceIdList, List<String> adIds,
                                                   String currency, String startDate, String endDate,
                                                   List<Object> argsList, List<String> siteToday, Boolean isSiteToday,
                                                   List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, LocalDate categoryQueryDate, List<String> noFind) {
        StringBuilder sb = new StringBuilder();
        LocalDate now = LocalDate.now();
        sb.append(" select ");
        sb.append(" GROUP_CONCAT(DISTINCT t3.shop_ids) shop_ids, ");
        sb.append(" ifnull(sum(t3.cost * c.rate), 0) cost, ");
        sb.append(" ifnull(sum(t3.total_sales * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(t3.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(t3.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t3.order_num), 0) orderNum, ");
        sb.append(" ifnull(sum(t3.sale_num), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t3.cost * c.rate), 0)/ ifnull(sum(t3.total_sales * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t3.total_sales * c.rate), 0)/ ifnull(sum(t3.cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ROUND(ifnull(sum(t3.clicks)/ sum(t3.impressions), 0), 4) clickRate, ");
        sb.append(" ROUND(ifnull(sum(t3.order_num)/ sum(t3.clicks), 0), 4) conversionRate, ");
        sb.append(" ROUND(ifnull(sum(t3.cost * c.rate)/ sum(t3.clicks), 0), 4) cpc, ");
        sb.append(" ROUND(ifnull(sum(t3.cost * c.rate)/ sum(t3.order_num), 0), 4) cpa, ");
        sb.append(" t3.full_cid label  ");
        sb.append(" from ( ");
        sb.append(" SELECT ");
        sb.append(" t1.puid puid, ");
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t1.shop_id AS CHAR)) shop_ids, ");
        sb.append(" t1.marketplace_id AS marketplace_id, ");
        sb.append(" ifnull(sum(t1.cost), 0) cost, ");
        sb.append(" ifnull(sum(t1.total_sales), 0) total_sales, ");
        sb.append(" ifnull(sum(t1.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t1.sale_num), 0) order_num, ");
        sb.append(" ifnull(sum(t1.order_num), 0) sale_num, ");
        sb.append(" t2.full_cid full_cid, ");
        sb.append(" t1.count_month count_month ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_product_report t1 ");
        sb.append(" join (select puid, shop_id, sku, full_cid from ods_t_product_commodity_rela ");
        sb.append(" where puid = ? and month = ? and  pt = ? and full_cid != '-' ");
        argsList.add(puid);
        argsList.add(LocalDateTimeUtil.formatDate(categoryQueryDate, "yyyyMM"));
        argsList.add(LocalDateTimeUtil.formatDate(categoryQueryDate.withDayOfMonth(1), "yyyy-MM-dd"));
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(adIds)) {
            sb.append(SqlStringUtil.dealDorisInList("full_cid", adIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(noFind)) {
            sb.append(SqlStringUtil.dealDorisNotInList("full_cid", noFind, argsList));
        }
        sb.append(") t2 on t1.puid = t2.puid and t1.shop_id = t2.shop_id and t1.sku = t2.sku ");
        sb.append(" WHERE  ");
        sb.append(" t1.puid = ?  ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t1.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t1.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', t1.marketplace_id, t1.count_day)", siteToday, argsList));
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and t1.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("t1.campaign_id", campaignIds, argsList));
        }


        sb.append(" group by puid,marketplace_id,count_month ,full_cid ");

        sb.append(" union all ");


        sb.append(" SELECT ");
        sb.append(" t1.puid puid, ");
        sb.append(" GROUP_CONCAT(DISTINCT CAST(t1.shop_id AS CHAR)) shop_ids, ");
        sb.append(" t1.marketplace_id AS marketplace_id, ");
        sb.append(" ifnull(sum(t1.cost), 0) cost, ifnull(sum(t1.sales14d), 0) total_sales, ");
        sb.append(" ifnull(sum(t1.impressions), 0) impressions, ifnull(sum(t1.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t1.conversions14d), 0) order_num, ");
        sb.append(" ifnull(sum(t1.units_ordered14d), 0) sale_num, ");
        sb.append(" t2.full_cid full_cid, ");
        sb.append(" t1.count_month count_month ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_sd_product_report t1 ");
        sb.append(" join (select puid, shop_id, sku, full_cid from ods_t_product_commodity_rela ");
        sb.append(" where puid = ? and month = ? and  pt = ? and full_cid != '-' ");
        argsList.add(puid);
        argsList.add(LocalDateTimeUtil.formatDate(categoryQueryDate, "yyyyMM"));
        argsList.add(LocalDateTimeUtil.formatDate(categoryQueryDate.withDayOfMonth(1), "yyyy-MM-dd"));
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        if (CollectionUtils.isNotEmpty(adIds)) {
            sb.append(SqlStringUtil.dealDorisInList("full_cid", adIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(noFind)) {
            sb.append(SqlStringUtil.dealDorisNotInList("full_cid", noFind, argsList));
        }
        sb.append(") t2 on t1.puid = t2.puid and t1.shop_id = t2.shop_id and t1.sku = t2.sku ");
        sb.append(" WHERE  ");
        sb.append(" t1.puid = ?  ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and t1.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t1.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', t1.marketplace_id, t1.count_day)", siteToday, argsList));
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and t1.count_day >= ? and t1.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and t1.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("t1.campaign_id", campaignIds, argsList));

        }
        sb.append(" GROUP BY  puid,marketplace_id,count_month ,full_cid ) t3 ");
        sb.append(" JOIN (SELECT puid,`month`,`from`,rate FROM dim_currency_rate WHERE " );
        sb.append(" puid = ? ");
        argsList.add(puid);
        sb.append(" AND `to` = ? ) c ");
        argsList.add(currency);
        sb.append(" ON t3.count_month = c.month ");
        sb.append(" JOIN dim_marketplace_info m ON m.marketplace_id = t3.marketplace_id AND c.`from` = m.currency " );
        sb.append(" GROUP by  t3.full_cid ");
        return sb.toString();
    }



    @Override
    public List<DashboardAdProductTopDataDto> queryAdProductYoyOrMomTop(String subSqlA, String subSqlB,
                                                                        List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                        DashboardOrderByRateEnum orderField, String orderBy,
                                                                        Integer limit, Boolean noZero, DashboardQueryFieldEnum dashboardQueryFieldEnum) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.label as label, ");
        if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE != dashboardQueryFieldEnum) {
            sb.append(" subSqlA.asin asin , ");
        }
        sb.append(" subSqlA.shop_ids as shopIds, ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.label = subSqlB.label ");
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" where " + getColumnSelect(dataField.getCode()) );
        }
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdProductTopDataDto.class), argsList.toArray());
    }


    @Override
    public List<DashboardAdProductDto> queryAdProductCharts(Integer puid, List<Integer> shopIdList,
                                                            List<String> marketplaceIdList, List<String> ads,
                                                            String currency, String startDate, String endDate, DashboardQueryFieldEnum dashboardQueryFieldEnum,
                                                            List<String> siteToday, Boolean isSiteToday,
                                                            List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, LocalDate categoryQueryDate, List<String> noFind) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = adProductQuerySql(puid, shopIdList, marketplaceIdList, ads, currency, startDate, endDate, argsList, dashboardQueryFieldEnum,
                siteToday, isSiteToday, portfolioIds, campaignIds, noZero, dashboardDataFieldEnum, categoryQueryDate, noFind);
        return getJdbcTemplate().query(querySql, new BeanPropertyRowMapper<>(DashboardAdProductDto.class), argsList.toArray());
    }


    /**
     * 用户排除为0 的字段处理
     *
     * @param orderByField
     * @return
     */
    private String getColumnSelect(String orderByField) {

        switch (orderByField) {
            case "impressions":
                return " ifnull(subSqlA.impressions, 0) <> 0 ";
            case "clicks":
                return " ifnull(subSqlA.clicks, 0) <> 0 ";
            case "cost":
                return " ifnull(subSqlA.cost, 0) <> 0 ";
            case "roas":
                return " ifnull(subSqlA.roas, 0) <> 0 ";
            case "acos":
                return " ifnull(subSqlA.acos, 0) <> 0 ";
            case "clickRate":
                return " ifnull(subSqlA.clickRate, 0) <> 0 ";
            case "conversionRate":
                return " ifnull(subSqlA.conversionRate, 0.0) <> 0 ";
            case "cpc":
                return " ifnull(subSqlA.cpc, 0) <> 0 ";
            case "cpa":
                return " ifnull(subSqlA.cpa, 0) <> 0 ";
            case "totalSales":
                return " ifnull(subSqlA.totalSales, 0) <> 0 ";
            case "orderNum":
                return " ifnull(subSqlA.orderNum, 0) <> 0 ";
            case "saleNum":
                return " ifnull(subSqlA.saleNum, 0) <> 0 ";
            default:
                return orderByField + " <> 0 ";
        }
    }

    @Override
    public Page<AsinListDto> getAsinSkuPageByPuidAndShopId(AsinListReqVo reqVo) {
        AsinListReqVo.SearchTypeEnum searchTypeEnum;
        if (AsinListReqVo.SearchTypeEnum.ASIN.getType().equals(reqVo.getSearchType())) {
            searchTypeEnum = AsinListReqVo.SearchTypeEnum.ASIN;
        } else if (AsinListReqVo.SearchTypeEnum.MSKU.getType().equals(reqVo.getSearchType())) {
            searchTypeEnum = AsinListReqVo.SearchTypeEnum.MSKU;
        } else {
            return new Page<>(reqVo.getPageNo(), reqVo.getPageSize(), 0, 0, new ArrayList<>());
        }
        List<Object> argsList = Lists.newArrayList();
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        StringBuilder selectSql = new StringBuilder("select asin, parentAsin, msku, shopId, marketplaceId, mainImage from (");
        List<String> sql = new ArrayList<>(3);
        List<String> typeList = StringUtil.splitStr(reqVo.getAdType());
        if (typeList.contains(Constants.SP)) {
            sql.add(this.buildSelAsinSkuPageSql(reqVo, searchTypeEnum, argsList, "ods_t_amazon_ad_product_report", Constants.SP));
        }
        if (typeList.contains(Constants.SB)) {
            sql.add(this.buildSelAsinSkuPageSql(reqVo, searchTypeEnum, argsList, "ods_t_amazon_sb_ads", Constants.SB));
        }
        if (typeList.contains(Constants.SD)) {
            sql.add(this.buildSelAsinSkuPageSql(reqVo, searchTypeEnum, argsList, "ods_t_amazon_ad_sd_product_report", Constants.SD));
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(") c");
        countSql.append(allSql).append(") c");
        selectSql.append(" order by ").append(searchTypeEnum.getType());
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(reqVo.getPageNo(), reqVo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AsinListDto.class);
    }

    @Override
    public Page<AsinListDto> getParentAsinIdPage(AsinListReqVo reqVo) {
        if (!AsinListReqVo.SearchTypeEnum.PARENT_ASIN.getType().equals(reqVo.getSearchType())) {
            return new Page<>(reqVo.getPageNo(), reqVo.getPageSize(), 0, 0, new ArrayList<>());
        }
        List<Object> argsList = Lists.newArrayList();
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        StringBuilder selectSql = new StringBuilder("select shop_id shopId, any_value(marketplace_id) marketplaceId, asin, group_concat(CAST(id as CHAR)) ids from ods_t_product ")
                .append(" where puid = ? ");
        argsList.add(reqVo.getPuid());
        selectSql.append(SqlStringUtil.dealInList("shop_id", reqVo.getShopIdList(), argsList));
        selectSql.append(" and marketplace_id = ? ");
        argsList.add(reqVo.getMarketplaceId());
        selectSql.append(" and id in (select parentId from ( ");
        List<String> sql = new ArrayList<>(3);
        List<String> typeList = StringUtil.splitStr(reqVo.getAdType());
        if (typeList.contains(Constants.SP)) {
            sql.add(this.buildSelParentAsinIdPageSql(reqVo, argsList, "ods_t_amazon_ad_product_report", Constants.SP));
        }
        if (typeList.contains(Constants.SB)) {
            sql.add(this.buildSelParentAsinIdPageSql(reqVo, argsList, "ods_t_amazon_sb_ads", Constants.SB));
        }
        if (typeList.contains(Constants.SD)) {
            sql.add(this.buildSelParentAsinIdPageSql(reqVo, argsList, "ods_t_amazon_ad_sd_product_report", Constants.SD));
        }
        String allSql = String.join(" union ", sql);
        selectSql.append(allSql).append(") s) ");

        if (Boolean.TRUE.equals(reqVo.getSearchFlag())) {
            if (CollectionUtils.isNotEmpty(reqVo.getSearchList())) {
                List<String> list = reqVo.getSearchList().stream().map(String::toLowerCase).collect(Collectors.toList());
                selectSql.append(SqlStringUtil.dealInList("lower(asin)", list, argsList));
            }
        } else {
            if (StringUtils.isNotBlank(reqVo.getSearchValue())) {
                selectSql.append(" and lower(asin) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(reqVo.getSearchValue().toLowerCase()) + "%");
            }
        }

        selectSql.append("group by shop_id, asin");
        countSql.append(selectSql).append(") c");
        selectSql.append(" order by asin");
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(reqVo.getPageNo(), reqVo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AsinListDto.class);
    }

    private String buildSelAsinSkuPageSql(AsinListReqVo reqVo, AsinListReqVo.SearchTypeEnum searchTypeEnum, List<Object> argsList, String tableName, String type) {
        StringBuilder selectSql = new StringBuilder("select any(p.asin) asin, any(p.parent_asin) parentAsin, any(p.sku) msku, any(p.shop_id) shopId, any(p.marketplace_id) marketplaceId, any(p.main_image) mainImage ")
                .append(" from ods_t_product p ").append(" join ").append(tableName).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id ");
        if (Constants.SB.equalsIgnoreCase(type)) {
            selectSql.append(" and p.asin = r.asins ");
        } else {
            selectSql.append(" and p.asin = r.asin and p.sku = r.sku ");
        }
        selectSql.append(" and r.puid = ? ");
        argsList.add(reqVo.getPuid());
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", reqVo.getShopIdList(), argsList));
        selectSql.append(" and r.marketplace_id = ? ");
        argsList.add(reqVo.getMarketplaceId());
        if (!Constants.SB.equalsIgnoreCase(type)) {
            if (StringUtils.isNotEmpty(reqVo.getStart())) {
                selectSql.append(" and r.count_day >= ? ");
                argsList.add(reqVo.getStart());
            }
            if (StringUtils.isNotEmpty(reqVo.getEnd())) {
                selectSql.append(" and r.count_day <= ? ");
                argsList.add(reqVo.getEnd());
            }
        }
        selectSql.append(" where p.puid = ? ");
        argsList.add(reqVo.getPuid());
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", reqVo.getShopIdList(), argsList));
        if (Boolean.TRUE.equals(reqVo.getSearchFlag())) {
            if (CollectionUtils.isNotEmpty(reqVo.getSearchList())) {
                List<String> list = reqVo.getSearchList().stream().map(String::toLowerCase).collect(Collectors.toList());
                selectSql.append(SqlStringUtil.dealInList("lower(p." + searchTypeEnum.getField() + ")", list, argsList));
            }
        } else {
            if (StringUtils.isNotBlank(reqVo.getSearchValue())) {
                selectSql.append(" and lower(p.").append(searchTypeEnum.getField()).append(") like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(reqVo.getSearchValue().toLowerCase()) + "%");
            }
        }
        selectSql.append(" group by p.shop_id, p.").append(searchTypeEnum.getField());
        return selectSql.toString();
    }

    private String buildSelParentAsinIdPageSql(AsinListReqVo reqVo, List<Object> argsList, String tableName, String type) {
        StringBuilder selectSql = new StringBuilder("select p.parent_id parentId from ods_t_product p ").append(" join ").append(tableName).append(" r ")
                .append(" on p.puid = r.puid and p.shop_id = r.shop_id ");
        if (Constants.SB.equalsIgnoreCase(type)) {
            selectSql.append(" and p.asin = r.asins ");
        } else {
            selectSql.append(" and p.asin = r.asin and p.sku = r.sku ");
        }
        selectSql.append(" and r.puid = ? ");
        argsList.add(reqVo.getPuid());
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", reqVo.getShopIdList(), argsList));
        selectSql.append(" and r.marketplace_id = ? ");
        argsList.add(reqVo.getMarketplaceId());
        if (!Constants.SB.equalsIgnoreCase(type)) {
            if (StringUtils.isNotEmpty(reqVo.getStart())) {
                selectSql.append(" and r.count_day >= ? ");
                argsList.add(reqVo.getStart());
            }
            if (StringUtils.isNotEmpty(reqVo.getEnd())) {
                selectSql.append(" and r.count_day <= ? ");
                argsList.add(reqVo.getEnd());
            }
        }
        selectSql.append(" where p.puid = ? ");
        argsList.add(reqVo.getPuid());
        selectSql.append(SqlStringUtil.dealInList("p.shop_id", reqVo.getShopIdList(), argsList));
        selectSql.append(" and p.parent_id is not null and p.parent_id != '' ");
        selectSql.append(" group by p.shop_id, p.parent_id");
        return selectSql.toString();
    }







    @Override
    public String adProductGroupByDevIdQuerySql(Integer puid, List<Integer> shopIdList,
                                                List<String> marketplaceIdList, List<Integer> devIds,
                                                String currency, String startDate, String endDate,
                                                List<Object> argsList, List<String> siteToday, Boolean isSiteToday,
                                                List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, boolean isSummary) {
        StringBuilder sb = new StringBuilder();
        sb.append(" select ");
        sb.append(" ifnull(sum(t.cost * c.rate), 0) cost, ");
        sb.append(" ifnull(sum(t.total_sales * c.rate), 0) totalSales, ");
        sb.append(" ifnull(sum(t.impressions), 0) impressions, ");
        sb.append(" ifnull(sum(t.clicks), 0) clicks, ");
        sb.append(" ifnull(sum(t.order_num), 0) orderNum, ");
        sb.append(" ifnull(sum(t.sale_num), 0) saleNum, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t.cost * c.rate), 0)/ ifnull(sum(t.total_sales * c.rate), 0), 4), 0) acos, ");
        sb.append(" ifnull(ROUND(ifnull(sum(t.total_sales * c.rate), 0)/ ifnull(sum(t.cost * c.rate), 0), 4), 0) roas, ");
        sb.append(" ROUND(ifnull(sum(t.clicks)/ sum(t.impressions), 0), 4) clickRate, ");
        sb.append(" ROUND(ifnull(sum(t.order_num)/ sum(t.clicks), 0), 4) conversionRate, ");
        sb.append(" ROUND(ifnull(sum(t.cost * c.rate)/ sum(t.clicks), 0), 4) cpc, ");
        sb.append(" ROUND(ifnull(sum(t.cost * c.rate)/ sum(t.order_num), 0), 4) cpa, ");
        if (!isSummary) {
            sb.append(" dev_id  devId ");
        } else {
            sb.append(" count_day  countDay ");
        }

        sb.append(" from ( ");



        //sp sql
        sb.append(" SELECT puid, shop_id, marketplace_id , count_day, ");
        sb.append(" cost, ");
        sb.append(" total_sales, ");
        sb.append(" impressions, ");
        sb.append(" clicks, ");
        sb.append(" order_num as sale_num, ");
        sb.append(" sale_num as order_num, ");
        sb.append(" asin, ");
        sb.append(" sku, ");
        sb.append(" count_month ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_product_report  ");
        sb.append(" WHERE  ");
        sb.append(" puid = ?  ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, count_day)", siteToday, argsList));
            sb.append(" and count_day >= ? and count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, argsList));
        }


        sb.append(" union all ");

        //sb sql
        sb.append(" SELECT puid, shop_id, marketplace_id ,  count_day, ");
        sb.append("  cost, sales14d total_sales, ");
        sb.append("  impressions,  clicks, ");
        sb.append(" conversions14d order_num, ");
        sb.append(" units_ordered14d sale_num, ");
        sb.append(" asin, ");
        sb.append(" sku, ");
        sb.append(" count_month ");
        sb.append(" FROM ");
        sb.append(" ods_t_amazon_ad_sd_product_report  ");
        sb.append(" WHERE  ");
        sb.append(" puid = ? and asin is not null and asin != ''  ");

        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, count_day)", siteToday, argsList));
            sb.append(" and count_day >= ? and count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("campaign_id", campaignIds, argsList));
        }

        sb.append(" ) t ");

        sb.append(" JOIN ");

        sb.append(" ods_t_product t1 on t1.puid = t.puid and t.shop_id = t1.shop_id  and t.asin = t1.asin and t.sku = t1.sku  ");
        sb.append(" and t1.dev_id is not null and t1.dev_id != '' and t1.dev_id != ' ' ");
        sb.append(" and t1.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and t1.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(devIds)) {
            sb.append("and dev_id in ('").append(StringUtils.join(devIds, "','")).append("') ");
        }

        sb.append(" join  dim_marketplace_info m on m.marketplace_id = t.marketplace_id ");
        sb.append(" join (select * from dim_currency_rate where  puid = ? and `to` =  ? ) c ");
        argsList.add(puid);
        argsList.add(currency);
        sb.append("  on t.puid = c.puid and t.count_month = c.month and c.`from` = m.currency ");
        if (!isSummary) {
            sb.append(" group by dev_id ");
        } else {
            sb.append(" group by count_day ");
        }
        return sb.toString();
    }




    /**
     * 用户排除为0 的字段处理
     *
     * @param orderByField
     * @return
     */
    private String getDevIdColumnSelect(String orderByField) {

        switch (orderByField) {
            case "impressions":
                return " ifnull(t.impressions, 0) <> 0 ";
            case "clicks":
                return " ifnull(t.clicks, 0) <> 0 ";
            case "cost":
                return " ifnull(t.cost, 0) <> 0 ";
            case "roas":
                return " ifnull(t.roas, 0) <> 0 ";
            case "acos":
                return " ifnull(t.acos, 0) <> 0 ";
            case "clickRate":
                return " ifnull(t.clickRate, 0) <> 0 ";
            case "conversionRate":
                return " ifnull(t.conversionRate, 0.0) <> 0 ";
            case "cpc":
                return " ifnull(t.cpc, 0) <> 0 ";
            case "cpa":
                return " ifnull(t.cpa, 0) <> 0 ";
            case "totalSales":
                return " ifnull(t.totalSales, 0) <> 0 ";
            case "orderNum":
                return " ifnull(t.orderNum, 0) <> 0 ";
            case "saleNum":
                return " ifnull(t.saleNum, 0) <> 0 ";
            default:
                return orderByField + " <> 0 ";
        }
    }

    @Override
    public List<DashboardAdSalesmanTopDataDto> queryAdSalesmanYoyOrMomTop(String subSqlA, String subSqlB,
                                                                          List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                          DashboardOrderByRateEnum orderField, String orderBy,
                                                                          Integer limit, Boolean noZero) {
        List<Object> argsList = Lists.newArrayList();
        argsList.addAll(queryParam);
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT  subSqlA.devId devId , ifnull(subSqlA.cost, 0) as cost, ");
        sb.append(" ifnull(subSqlA.totalSales, 0) as totalSales, ifnull(subSqlA.impressions, 0) as impressions,  ");
        sb.append(" ifnull(subSqlA.clicks, 0) as clicks, ifnull(subSqlA.orderNum, 0) as orderNum, ");
        sb.append(" ifnull(subSqlA.saleNum, 0) as saleNum, ifnull(subSqlA.acos, 0) as acos, ifnull(subSqlA.roas, 0) as roas, ");
        sb.append(" ifnull(subSqlA.clickRate, 0) as clickRate, ifnull(subSqlA.conversionRate, 0) as conversionRate, ifnull(subSqlA.cpc, 0) as cpc, ifnull(subSqlA.cpa, 0) as cpa, ");
        sb.append(" ifnull(subSqlB.cost, 0) as subCost, ");
        sb.append(" ifnull(subSqlB.totalSales, 0) as subTotalSales, ifnull(subSqlB.impressions, 0) as subImpressions,  ");
        sb.append(" ifnull(subSqlB.clicks, 0) as subClicks, ifnull(subSqlB.orderNum, 0) as subOrderNum, ");
        sb.append(" ifnull(subSqlB.saleNum, 0) as subSaleNum, ifnull(subSqlB.acos, 0) as subAcos, ifnull(subSqlB.roas, 0) as subRoas, ");
        sb.append(" ifnull(subSqlB.clickRate, 0) as subClickRate, ifnull(subSqlB.conversionRate, 0) as subConversionRate, ifnull(subSqlB.cpc, 0) as subCpc, ifnull(subSqlB.cpa, 0) as subCpa, ");
        sb.append(" SUM(subSqlA.cost) OVER () as allCost, ");
        sb.append(" SUM(subSqlA.totalSales) OVER () as allTotalSales, ");
        sb.append(" SUM(subSqlA.impressions) OVER () as allImpressions, ");
        sb.append(" SUM(subSqlA.clicks) OVER () as allClicks, ");
        sb.append(" SUM(subSqlA.orderNum) OVER () as allOrderNum, ");
        sb.append(" SUM(subSqlA.saleNum) OVER () as allSaleNum ");
        sb.append(" From ");
        sb.append(" (").append(subSqlA).append(")").append(" subSqlA ");
        sb.append(" left join ");
        sb.append(" (").append(subSqlB).append(")").append(" subSqlB ");
        sb.append(" on subSqlA.devId = subSqlB.devId ");
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" where " + getColumnSelect(dataField.getCode()) );
        }
        if (Objects.nonNull(orderField) && DashboardOrderByRateEnum.PERCENT == orderField) {
            //以上几个计算占比时，是按绝对值进行排序的
            sb.append(" ORDER BY ").append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        }  else if (Objects.nonNull(orderField) && Stream.of(DashboardOrderByRateEnum.YOY_VALUE, DashboardOrderByRateEnum.MOM_VALUE)
                .anyMatch(d -> d == orderField)) {//计算增长值
            sb.append(" ORDER BY ").append(" (");
            sb.append("ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(") ");
        }else {
            sb.append(" ORDER BY ").append(" (");
            sb.append("if(ifnull(subSqlB.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", if(ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" = 0 ").append(", 0").append(", 1)");
            sb.append(", (ifnull(subSqlA.").append(dataField.getCode()).append(", 0)").append(" - ").append(" subSqlB.").append(dataField.getCode()).append(" ) ");
            sb.append(" / ").append(" subSqlB.").append(dataField.getCode()).append(" )");
            sb.append(" )");
        }
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        sb.append(", ifnull(subSqlA.").append(dataField.getCode()).append(", 0)");
        if (StringUtils.isNotEmpty(orderBy)) {
            sb.append(" ").append(orderBy);
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdSalesmanTopDataDto.class), argsList.toArray());
    }


    @Override
    public List<DashboardAdSalesmanDto> queryAdSalesmanCharts(Integer puid, List<Integer> shopIdList,
                                                                     List<String> marketplaceIdList, List<Integer> devIds,
                                                                     String currency, String startDate, String endDate,
                                                                     List<String> siteToday, Boolean isSiteToday,
                                                                     List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, boolean isSummary) {
        List<Object> argsList = Lists.newArrayList();
        String querySql = adProductGroupByDevIdQuerySql(puid, shopIdList, marketplaceIdList, devIds, currency, startDate, endDate, argsList,
                siteToday, isSiteToday, portfolioIds, campaignIds, noZero, dashboardDataFieldEnum, isSummary);
        return getJdbcTemplate().query(querySql, new BeanPropertyRowMapper<>(DashboardAdSalesmanDto.class), argsList.toArray());
    }



}

