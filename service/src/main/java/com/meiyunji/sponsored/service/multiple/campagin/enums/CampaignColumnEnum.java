package com.meiyunji.sponsored.service.multiple.campagin.enums;

import com.meiyunji.sponsored.common.util.StringUtil;
import lombok.Getter;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 活动列表页-报告统计枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum CampaignColumnEnum {
    COST("costDoris", " ,IFNULL(sum(r.cost),0) `costDoris`",
            ",IFNULL(sum(r.cost*d.rate),0) `costDoris` "),
    TOTAL_SALES("totalSalesDoris", " ,IFNULL(sum(if (r.type = 'sp', sales7d,sales14d)),0) totalSalesDoris",
            ",IFNULL(sum(if (r.type = 'sp', sales7d,sales14d)*d.rate),0) totalSalesDoris"),
    AD_SALES("adSalesDoris", " ,IFNULL(sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)),0) adSalesDoris",
            " ,IFNULL(sum(if (r.type = 'sp', sales7d_same_sku,sales14d_same_sku)*d.rate),0) adSalesDoris"),
    IMPRESSIONS("impressionsDoris", " ,IFNULL(sum(`impressions`),0) impressionsDoris",""),
    CLICKS("clicksDoris", " ,IFNULL(sum(`clicks`),0) clicksDoris",""),
    ORDER_NUM("orderNumDoris", " ,IFNULL(sum(if (r.type = 'sp' , conversions7d,conversions14d)),0) orderNumDoris",""),
    AD_ORDER_NUM("adOrderNumDoris", " ,IFNULL(sum(if (r.type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`)),0) adOrderNumDoris",""),
    SALE_NUM("saleNumDoris", " ,IFNULL(sum(if (r.type = 'sp' ,`units_ordered7d`,if (r.type = 'sb' ,`units_sold14d`,`units_ordered14d`))),0) saleNumDoris",""),
    AD_SALE_NUM("adSaleNumDoris", " ,IFNULL(sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`)),0) adSaleNumDoris",""),
    VIEW_IMPRESSIONS("viewImpressionsDoris", " ,IFNULL(sum(if (r.type = 'sb', `viewable_impressions`,`view_impressions`)),0) viewImpressionsDoris",""),
    SALES_NEW_TO_BRAND("salesNewToBrand14dDoris", " ,IFNULL(sum(`sales_new_to_brand14d`),0) salesNewToBrand14dDoris",
            " ,IFNULL(sum(`sales_new_to_brand14d`*d.rate),0) salesNewToBrand14dDoris"),
    ORDERS_NEW_TO_BRAND("ordersNewToBrand14dDoris", " ,IFNULL(sum(orders_new_to_brand14d),0) ordersNewToBrand14dDoris",""),
    UNITS_ORDERED_NEW_TO_BRAND("unitsOrderedNewToBrand14dDoris", " ,IFNULL(sum(units_ordered_new_to_brand14d),0) unitsOrderedNewToBrand14dDoris",""),
    NEW_TO_BRAND_DETAIL_PAGE_VIEWS("newToBrandDetailPageViewsDoris", " ,IFNULL(sum(`new_to_brand_detail_page_views`),0) AS newToBrandDetailPageViewsDoris",""),
    ADD_TO_CART("addToCartDoris", " ,IFNULL(sum(`add_to_cart`),0) AS addToCartDoris",""),
    VIDEO5SECOND_VIEWS("video5secondViewsDoris", " ,IFNULL(sum(`video5second_views`),0) AS video5secondViewsDoris",""),
    VIDEO_FIRST_QUARTILE_VIEWS("videoFirstQuartileViewsDoris", " ,IFNULL(sum(`video_first_quartile_views`),0) AS `videoFirstQuartileViewsDoris`",""),
    VIDEO_MIDPOINT_VIEWS("videoMidpointViewsDoris", " ,IFNULL(sum(`video_Midpoint_Views`),0) AS `videoMidpointViewsDoris`",""),
    VIDEO_THIRD_QUARTILE_VIEWS("videoThirdQuartileViewsDoris", " ,IFNULL(sum(`video_third_quartile_views`),0) AS `videoThirdQuartileViewsDoris`",""),
    VIDEO_COMPLETE_VIEWS("videoCompleteViewsDoris", " ,IFNULL(sum(`video_complete_views`),0) AS `videoCompleteViewsDoris`",""),
    VIDEO_UNMUTES("videoUnmutesDoris", " ,IFNULL(sum(`video_unmutes`),0) AS `videoUnmutesDoris`",""),
    BRANDED_SEARCHES("brandedSearches14dDoris", " ,IFNULL(sum(`branded_searches14d`),0) AS `brandedSearches14dDoris`",""),
    DETAIL_PAGE_VIEW("detailPageView14dDoris", " ,IFNULL(sum(`detail_page_view14d`),0) AS `detailPageView14dDoris`",""),
    MIN_TOP_IS("minTopIsDoris", " ,IFNULL(min(top_of_search_is),0) minTopIsDoris",""),
    MAX_TOP_IS("maxTopIsDoris", " ,max(top_of_search_is) maxTopIsDoris",""),
    CUMULATIVE_REACH("cumulativeReachDoris", " ,IFNULL(max(`cumulative_reach`),0) AS `cumulativeReachDoris`",""),
    IMPRESSIONS_FREQUENCY_AVERAGE("impressionsFrequencyAverageDoris", " ,IFNULL(max(`impressions_frequency_average`),0) AS `impressionsFrequencyAverageDoris`",""),
    AD_OTHER_SALE_NUM("adOtherSaleNumDoris", " ,ifnull(sum(if (r.type = 'sp',units_ordered7d - units_ordered7d_same_sku,0)),0) as adOtherSaleNumDoris ",""),
    SB_SALE_NUM("sbSaleNumDoris", " ,IFNULL(sum(if (r.type = 'sb' ,`units_sold14d`,0)), 0) sbSaleNumDoris ",""),
    SD_SALE_NUM("sdSaleNumDoris", " ,IFNULL(sum(if (r.type = 'sd' ,`units_ordered14d`,0)), 0) sdSaleNumDoris ",""),
    SD_AD_SALE_NUM("spAdSaleNumDoris", " ,IFNULL(sum(if (r.type = 'sp' ,`units_ordered7d_same_sku`,0)), 0) spAdSaleNumDoris ",""),
    SB_VIEW_IMPRESSIONS("sbViewImpressionsDoris", " ,IFNULL(sum(if (r.type = 'sb', `viewable_impressions`,0)),0) sbViewImpressionsDoris ",""),
    SD_VIEW_IMPRESSIONS("sdViewImpressionsDoris", " ,IFNULL(sum(if (r.type = 'sd', `view_impressions`,0)),0) sdViewImpressionsDoris ",""),
    SB_COST("sbCostDoris", " ,IFNULL(sum(if (r.type = 'sb', r.cost,0)),0) sbCostDoris ",
            " ,IFNULL(sum(if (r.type = 'sb', r.cost*d.rate,0)),0) sbCostDoris "),
    SD_COST("sdCostDoris", " ,IFNULL(sum(if (r.type = 'sd', r.cost,0)),0) sdCostDoris ",
            " ,IFNULL(sum(if (r.type = 'sd', r.cost*d.rate,0)),0) sdCostDoris "),
    ;

    // 排序字段
    private final String code;
    // 字段
    private final String column;
    // 汇率字段
    private final String rateColumn;

    CampaignColumnEnum(String code, String column, String rateColumn) {
        this.code = code;
        this.column = column;
        this.rateColumn = rateColumn;
    }

    /**
     * 根据code获取统计字段
     */
    public static String getColumnByCode(String code,Boolean changeRate) {
        for (CampaignColumnEnum columnEnum : CampaignColumnEnum.values()) {
            if (columnEnum.getCode().equals(code)) {
                if(changeRate && StringUtil.isNotEmpty(columnEnum.getRateColumn())){
                    return columnEnum.getRateColumn();
                }else{
                    return columnEnum.getColumn();
                }
            }
        }
        return null;
    }

    public static Set<String> getAllCode() {
        Set<String> list = new HashSet<>();
        for (CampaignColumnEnum columnEnum : CampaignColumnEnum.values()) {
            list.add(columnEnum.getCode());
        }
        return list;
    }
}
