package com.meiyunji.sponsored.service.multiple.targets.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Constants;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingSdDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargetingSd;
import com.meiyunji.sponsored.service.enums.SBTargetingAudienceTypeEnum;
import com.meiyunji.sponsored.service.multiple.targets.dto.GroupInfo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetDataDto;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetInfo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetSdOrderByEnum;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * sp关键词投放子类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Slf4j
@Service
public class SdTargetProcessor extends AbstractTargetProcessor{

    @Resource
    private IAmazonSdAdTargetingDao sdAdTargetingDao;

    @Resource
    private IOdsAmazonAdTargetingSdDao odsTargetingSdDao;

    @Resource
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    @Override
    public void abstractCheckParam(TargetReqDto req) {

    }

    @Override
    public void abstractSetParam(TargetReqDto req) {
        // 受众投放类型转换
        if (StringUtils.isNotBlank(req.getTargetType())) {
            if ("T00020".equals(req.getTargetType())) {
                req.setTargetType(Constants.PRODUCT_TARGET);
            } else if ("T00030".equals(req.getTargetType())) {
                req.setTargetType(Constants.AUDIENCE_TARGET);
            }
        }
    }

    @Override
    public Boolean abstractFilterTargetIds(TargetReqDto req) {

        return false;
    }

    @Override
    public void abstractBuildWhereSql(TargetReqDto req, StringBuilder whereSql, List<Object> argsList) {
        // 投放类型刷选
        if (StringUtils.isNotEmpty(req.getTargetType())) {
            whereSql.append(" and t.tactic_type = ? ");
            argsList.add(req.getTargetType());
        }
        // 投放搜索
        if (StringUtils.isNotBlank(req.getSearchField()) && StringUtils.isNotBlank(req.getSearchValue())) {  //搜索值
            String lowerSearchValue = req.getSearchValue().toLowerCase();
            if ("asin".equalsIgnoreCase(req.getSearchField())) {
                whereSql.append(" and lower(t.target_text) = ? and t.type = 'asin' ");
                argsList.add(lowerSearchValue);
            } else if ("category".equalsIgnoreCase(req.getSearchField())) {
                whereSql.append(" and lower(t.target_text) like ? and t.type = 'category' ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(lowerSearchValue) + "%");
            } else {
                whereSql.append(" and lower(t.target_text) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(lowerSearchValue) + "%");
            }
        }
        if (req.getAdvanceFilter() != null && (req.getAdvanceFilter().getVcpmMin() != null || req.getAdvanceFilter().getVcpmMax() != null)) {
            // vcpm高级筛选只筛选费用类型为vcpm的
            whereSql.append(" and c.cost_type = 'vcpm' ");
        }
        // 匹配新版SD投放融合
        List<String> tacticTargetList = Arrays.asList(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode(),
                SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode(),
                SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
        List<String> filterTargetTypeList = CollectionUtil.newArrayList(req.getProductTargetType());
        if (StringUtils.isNotBlank(req.getProductTargetType()) && CollectionUtils.isNotEmpty(filterTargetTypeList)) {
            List<String> conditionList = Lists.newArrayList();
            if (tacticTargetList.parallelStream().anyMatch(filterTargetTypeList::contains)) {
                // 受众投放筛选
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode())) {
                    conditionList.add("t.target_type in ('views', 'purchases')");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode())) {
                    conditionList.add("t.type in ('In-market')");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode())){
                    //兴趣及生活方式受众(兴趣：Interest ; 内容分类：contentCategorySameAs ; 生活事件：Life event ; 生活方式：Lifestyle; 受众：audienceSameAs ; 受众： Audience)
                    conditionList.add("t.type in ('Interest', 'contentCategorySameAs', 'Life event', 'Lifestyle', 'audienceSameAs', 'Audience')");
                }
            } else {
                // 商品投放筛选
                if (filterTargetTypeList.contains("asin")) {
                    conditionList.add("t.type='asin'");
                } else if (filterTargetTypeList.contains("category")) {
                    conditionList.add("t.target_type = 'asinCategorySameAs'");
                } else if (filterTargetTypeList.contains("similarProduct")) {
                    //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                    conditionList.add("t.target_type = 'similarProduct'");
                }
            }
            if (CollectionUtils.isNotEmpty(conditionList)) {
                whereSql.append(" and ( ").append(String.join(" or ", conditionList)).append(" ) ");
            }
        }
    }

    @Override
    public void abstractPrepareData(TargetReqDto req, Boolean export, TargetDataDto dto) {
        if (!export) {
            // 获取asin信息填充asin、图片信息
            dto.setAsinMap(getAsinMap(req, CollectionUtil.newArrayList(dto.getTargetMap().values())));
        }
    }

    @Override
    public void abstractBuildParam(TargetResp row, TargetDataDto dto, TargetReqDto req) {
        if (StringUtils.isNotBlank(row.getServingStatus())) {
            AmazonAdTargeting.servingStatusEnum byCode = UCommonUtil.getByCode(row.getServingStatus(), AmazonAdTargeting.servingStatusEnum.class);
            if (byCode != null) {
                row.setServingStatusDec(byCode.getDescription());
                row.setServingStatusName(byCode.getName());
            } else {
                row.setServingStatusDec(row.getServingStatus());
                row.setServingStatusName(row.getServingStatus());
            }
        }

        SBTargetingAudienceTypeEnum targetType = SBTargetingAudienceTypeEnum.fromValue(row.getAudienceTargetType());
        row.setTitle(targetType != null ? targetType.getDesc() : null);
        row.setAudienceTargetType(targetType != null ? targetType.getType() : null);
    }

    @Override
    public List<TargetInfo> abstractMysqlTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 10000);
            for (List<String> targetIds : targetIdsList) {
                List<AmazonSdAdTargeting> targetingList = sdAdTargetingDao.listByShopIdsAndTargetIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (AmazonSdAdTargeting targeting : targetingList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(targeting, TargetInfo.class);
                    targetInfo.setProductTargetType(targeting.getType());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<TargetInfo> abstractDorisTargetList(TargetReqDto req) {
        List<TargetInfo> targetList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(req.getTargetIds())){
            // 分批获取
            List<List<String>> targetIdsList = Lists.partition(req.getTargetIds(), 9000);
            for (List<String> targetIds : targetIdsList) {
                List<OdsAmazonAdTargetingSd> targetingList = odsTargetingSdDao.getByTargetingIds(req.getPuid(), req.getShopIdList(), targetIds);
                for (OdsAmazonAdTargetingSd targeting : targetingList) {
                    TargetInfo targetInfo = BeanUtil.copyProperties(targeting, TargetInfo.class);
                    targetInfo.setProductTargetType(targeting.getType());
                    targetList.add(targetInfo);
                }
            }
        }
        return targetList;
    }

    @Override
    public List<GroupInfo> abstractGroupInfoList(TargetReqDto req, List<String> adGroupIdList) {
        List<GroupInfo> groupInfoList = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(adGroupIdList)){
            // 分批获取
            List<List<String>> adGroupIdsList = Lists.partition(adGroupIdList, 10000);
            for (List<String> adGroupIds : adGroupIdsList) {
                List<AmazonSdAdGroup> adGroupInfoList = amazonSdAdGroupDao.listByGroupId(req.getPuid(), req.getShopIdList(), adGroupIds);
                for (AmazonSdAdGroup adGroup : adGroupInfoList) {
                    GroupInfo groupInfo = BeanUtil.copyProperties(adGroup, GroupInfo.class);
                    groupInfoList.add(groupInfo);
                }
            }
        }
        return groupInfoList;
    }

    @Override
    public List<String> excludeFiledList(TargetReqDto req) {
        return Lists.newArrayList("selectType", "adSelfSaleNum", "adOtherSaleNum", "topImpressionShare", "video5SecondViews", "video5SecondViewRate",
                "keywordText","matchType", "keywordTextCn", "searchFrequencyRank", "weekRatio");
    }
}
