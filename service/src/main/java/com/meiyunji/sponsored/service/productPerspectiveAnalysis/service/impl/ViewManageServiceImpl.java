package com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.productPerspectiveAnalysis.viewManage.*;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.rpc.vo.ProductRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsRankParamVo;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregateIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregatePlacementIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-08-28  16:48
 */
@Service
@Slf4j
public class ViewManageServiceImpl implements IViewManageService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService CpCShopDataService;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    @Autowired
    private ICampaignViewService campaignViewService;
    @Autowired
    private IPlacementViewService placementViewService;
    @Autowired
    private IKeywordViewService keywordViewService;
    @Autowired
    private IAutoTargetViewService autoTargetViewService;
    @Autowired
    private ICategoryTargetViewService categoryTargetViewService;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private ISearchTermsViewService searchTermsViewService;
    @Autowired
    private IAudienceTargetViewService audienceTargetViewService;

    @Override
    public CampaignViewResponse.CampaignVo getCampaignView(Integer puid, CampaignViewParam param) {
        CampaignViewResponse.CampaignVo.Builder campaignVo = CampaignViewResponse.CampaignVo.newBuilder();
        CampaignViewResponse.CampaignVo.Page.Builder campaignVoPage = CampaignViewResponse.CampaignVo.Page.newBuilder();
        campaignVoPage.setPageNo(param.getPageNo());
        campaignVoPage.setPageSize(param.getPageSize());
        campaignVo.setPage(campaignVoPage.build());
        //总汇总初始化
        campaignVo.setAggregateViewVo(this.buildAggregateViewVo(null));

        Page<CampaignViewVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            this.addAggregateCampaignIdsTemporarySynchronize(param);
            return campaignVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adCamgaignIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adCamgaignIdList.add(bo.getCampaignId());
            adIdList.add(bo.getAdId());
        }
        param.setAdCamgaignIdList(adCamgaignIdList);
        param.setAdIdList(adIdList);

        //获取广告活动视图列表页与汇总
        CampaignViewPageVo campaignViewPageVo = campaignViewService.getCampaignViewPageVoList(puid, param, voPage);
        Page<CampaignViewVo> page = campaignViewPageVo.getPage();
        List<CampaignViewVo> rows = page.getRows();
        campaignVoPage.setTotalPage(page.getTotalPage());
        campaignVoPage.setTotalSize(page.getTotalSize());
        campaignVo.setAggregateViewVo(this.buildAggregateViewVo(campaignViewPageVo.getAggregateViewVo()));

        if (campaignViewPageVo.getAggregateViewVo() == null) {
            campaignVo.setPage(campaignVoPage.build());
            return campaignVo.build();
        }

        campaignVo.setAutoAggregateViewVo(this.buildAggregateViewVo(campaignViewPageVo.getAutoAggregateViewVo()));
        campaignVo.setManualAggregateViewVo(this.buildAggregateViewVo(campaignViewPageVo.getManualAggregateViewVo()));
        if (CollectionUtils.isEmpty(rows)) {
            campaignVo.setPage(campaignVoPage.build());
            return campaignVo.build();
        }

        //查询环比指标
        Map<String, StreamDataViewVo> compareCampaignMap = null;

        if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
            //对比时无须高级搜索条件
            param.setUseAdvanced(false);

            List<String> campaignIdList = rows.stream().map(CampaignViewVo::getCampaignId).collect(Collectors.toList());
            compareCampaignMap = campaignViewService.getStreamDataByCompare(param, campaignIdList);
        }

        Map<String, StreamDataViewVo> finalCompareCampaignMap = compareCampaignMap;
        List<CampaignViewResponse.CampaignVo.Page.CampaignPageVo> vos = rows.stream()
                .filter(Objects::nonNull).map(item -> {
                    CampaignViewResponse.CampaignVo.Page.CampaignPageVo.Builder voBuilder =
                            CampaignViewResponse.CampaignVo.Page.CampaignPageVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(item.getId());
                    }
                    if (item.getShopId() != null) {
                        voBuilder.setShopId(item.getShopId());
                    }
                    if (StringUtils.isNotBlank(item.getCampaignId())) {
                        voBuilder.setCampaignId(item.getCampaignId());
                    }
                    if (StringUtils.isNotBlank(item.getType())) {
                        voBuilder.setType(item.getType());
                    }
                    if (StringUtils.isNotBlank(item.getPortfolioId())) {
                        voBuilder.setPortfolioId(item.getPortfolioId());
                    }
                    if (StringUtils.isNotBlank(item.getPortfolioName())) {
                        voBuilder.setPortfolioName(item.getPortfolioName());
                    }
                    if (item.getIsHidden() != null) {
                        voBuilder.setIsHidden(item.getIsHidden());
                    }
                    if (StringUtils.isNotBlank(item.getName())) {
                        voBuilder.setName(item.getName());
                    }
                    if (StringUtils.isNotBlank(item.getState())) {
                        voBuilder.setState(item.getState());
                    }
                    if (StringUtils.isNotBlank(item.getServingStatus())) {
                        voBuilder.setServingStatus(item.getServingStatus());
                    }
                    if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                        voBuilder.setServingStatusDec(item.getServingStatusDec());
                    }
                    if (item.getOutOfBudget() != null) {
                        voBuilder.setOutOfBudget(item.getOutOfBudget());
                    }
                    if (StringUtils.isNotBlank(item.getDailyBudget())) {
                        voBuilder.setDailyBudget(item.getDailyBudget());
                    }
                    if (item.getBudgetLog() != null) {
                        DataLogVo budgetLog = item.getBudgetLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(budgetLog.getCount());
                        dataLog.setPreviousValue(budgetLog.getPreviousValue());
                        dataLog.setNewValue(budgetLog.getNewValue());
                        dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                        voBuilder.setBudgetLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getBudgetType())) {
                        voBuilder.setBudgetType(item.getBudgetType());
                    }
                    if (item.getBudgetUsage() != null) {
                        voBuilder.setBudgetUsage(converCampaignBudgetUsage(item.getBudgetUsage()));
                    }
                    if (CollectionUtils.isNotEmpty(item.getBudgetUsages())) {
                        List<CampaignViewResponse.CampaignVo.Page.BudgetUsage> budgetUsages = item.getBudgetUsages().stream().filter(Objects::nonNull).map(this::converCampaignBudgetUsage).collect(Collectors.toList());
                        voBuilder.addAllBudgetUsages(budgetUsages);
                    }
                    if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                        voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                    } else {
                        voBuilder.setTopImpressionShare("-");
                    }
                    if (item.getMissBudgets() != null) {
                        CampaignViewResponse.CampaignVo.Page.MissBudgetRecommendation.Builder missBudgetsVo = CampaignViewResponse.CampaignVo.Page.MissBudgetRecommendation.newBuilder();
                        CampaignViewVo.MissBudgetRecommendation missBudget = item.getMissBudgets();
                        if (StringUtils.isNotBlank(missBudget.getCampaignId())) {
                            missBudgetsVo.setCampaignId(missBudget.getCampaignId());
                        }
                        if (missBudget.getSuggestedBudget() != null) {
                            missBudgetsVo.setSuggestedBudget(missBudget.getSuggestedBudget());
                        }
                        if (missBudget.getEstimatedMissedSalesLower() != null) {
                            missBudgetsVo.setEstimatedMissedSalesLower(missBudget.getEstimatedMissedSalesLower());
                        }
                        if (missBudget.getEstimatedMissedSalesUpper() != null) {
                            missBudgetsVo.setEstimatedMissedSalesUpper(missBudget.getEstimatedMissedSalesUpper());
                        }
                        if (missBudget.getEstimatedMissedImpressionsLower() != null) {
                            missBudgetsVo.setEstimatedMissedImpressionsLower(missBudget.getEstimatedMissedImpressionsLower());
                        }
                        if (missBudget.getEstimatedMissedImpressionsUpper() != null) {
                            missBudgetsVo.setEstimatedMissedImpressionsUpper(missBudget.getEstimatedMissedImpressionsUpper());
                        }
                        if (missBudget.getEstimatedMissedClicksLower() != null) {
                            missBudgetsVo.setEstimatedMissedClicksLower(missBudget.getEstimatedMissedClicksLower());
                        }
                        if (missBudget.getEstimatedMissedClicksUpper() != null) {
                            missBudgetsVo.setEstimatedMissedClicksUpper(missBudget.getEstimatedMissedClicksUpper());
                        }
                        if (missBudget.getPercentTimeInBudget() != null) {
                            missBudgetsVo.setPercentTimeInBudget(missBudget.getPercentTimeInBudget());
                        }
                        if (StringUtils.isNotBlank(missBudget.getStartDate())) {
                            missBudgetsVo.setStartDate(missBudget.getStartDate());
                        }
                        if (StringUtils.isNotBlank(missBudget.getEndDate())) {
                            missBudgetsVo.setEndDate(missBudget.getEndDate());
                        }
                        if (StringUtils.isNotBlank(missBudget.getRuleId())) {
                            missBudgetsVo.setRuleId(missBudget.getRuleId());
                        }
                        if (StringUtils.isNotBlank(missBudget.getRuleName())) {
                            missBudgetsVo.setRuleName(missBudget.getRuleName());
                        }
                        if (missBudget.getSuggestedBudgetIncreasePercent() != null) {
                            missBudgetsVo.setSuggestedBudgetIncreasePercent(missBudget.getSuggestedBudgetIncreasePercent());
                        }
                        voBuilder.setMissBudget(missBudgetsVo.build());
                    }
                    if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                        voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                    }
                    if (StringUtils.isNotBlank(item.getTargetingType())) {
                        voBuilder.setTargetingType(item.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(item.getStrategy())) {
                        voBuilder.setStrategy(item.getStrategy());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementProductPage())) {
                        voBuilder.setPlacementProductPage(item.getPlacementProductPage());
                    } else {
                        voBuilder.setPlacementProductPage("0");
                    }
                    if (item.getPlacementProductPageLog() != null) {
                        DataLogVo placementProductPageLog = item.getPlacementProductPageLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(placementProductPageLog.getCount());
                        dataLog.setPreviousValue(placementProductPageLog.getPreviousValue());
                        dataLog.setNewValue(placementProductPageLog.getNewValue());
                        dataLog.setSiteOperationTime(placementProductPageLog.getSiteOperationTime());
                        voBuilder.setPlacementProductPageLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementTop())) {
                        voBuilder.setPlacementTop(item.getPlacementTop());
                    } else {
                        voBuilder.setPlacementTop("0");
                    }
                    if (item.getPlacementTopLog() != null) {
                        DataLogVo placementTopLog = item.getPlacementTopLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(placementTopLog.getCount());
                        dataLog.setPreviousValue(placementTopLog.getPreviousValue());
                        dataLog.setNewValue(placementTopLog.getNewValue());
                        dataLog.setSiteOperationTime(placementTopLog.getSiteOperationTime());
                        voBuilder.setPlacementTopLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementRestOfSearch())) {
                        voBuilder.setPlacementRestOfSearch(item.getPlacementRestOfSearch());
                    } else {
                        voBuilder.setPlacementRestOfSearch("0");
                    }
                    if (item.getPlacementRestOfSearchLog() != null) {
                        DataLogVo placementRestOfSearchLog = item.getPlacementRestOfSearchLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(placementRestOfSearchLog.getCount());
                        dataLog.setPreviousValue(placementRestOfSearchLog.getPreviousValue());
                        dataLog.setNewValue(placementRestOfSearchLog.getNewValue());
                        dataLog.setSiteOperationTime(placementRestOfSearchLog.getSiteOperationTime());
                        voBuilder.setPlacementRestOfSearchLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementSiteAmazonBusiness())) {
                        voBuilder.setPlacementSiteAmazonBusiness(item.getPlacementSiteAmazonBusiness());
                    } else {
                        voBuilder.setPlacementSiteAmazonBusiness("0");
                    }
                    if (item.getPlacementSiteAmazonBusinessLog() != null) {
                        DataLogVo placementSiteAmazonBusinessLog = item.getPlacementSiteAmazonBusinessLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(placementSiteAmazonBusinessLog.getCount());
                        dataLog.setPreviousValue(placementSiteAmazonBusinessLog.getPreviousValue());
                        dataLog.setNewValue(placementSiteAmazonBusinessLog.getNewValue());
                        dataLog.setSiteOperationTime(placementSiteAmazonBusinessLog.getSiteOperationTime());
                        voBuilder.setPlacementSiteAmazonBusinessLog(dataLog.build());
                    }
                    if (item.getIsUpdateBudget() != null) {
                        voBuilder.setIsUpdateBudget(item.getIsUpdateBudget());
                    }
                    if (item.getIsUpdatePlacementTop() != null) {
                        voBuilder.setIsUpdatePlacementTop(item.getIsUpdatePlacementTop());
                    }
                    if (item.getIsUpdatePlacementProductPage() != null) {
                        voBuilder.setIsUpdatePlacementProductPage(item.getIsUpdatePlacementProductPage());
                    }
                    if (item.getIsUpdatePlacementRestOfSearch() != null) {
                        voBuilder.setIsUpdatePlacementRestOfSearch(item.getIsUpdatePlacementRestOfSearch());
                    }
                    if (item.getIsUpdatePlacementSiteAmazonBusiness() != null) {
                        voBuilder.setIsUpdatePlacementSiteAmazonBusiness(item.getIsUpdatePlacementSiteAmazonBusiness());
                    }
                    if (StringUtils.isNotBlank(item.getStartDate())) {
                        voBuilder.setStartDate(item.getStartDate());
                    }
                    if (StringUtils.isNotBlank(item.getEndDate())) {
                        voBuilder.setEndDate(item.getEndDate());
                    }
                    if (StringUtils.isNotBlank(item.getType())) {
                        voBuilder.setType(item.getType());
                    }
                    if (item.getTargetType() != null) {
                        voBuilder.setTargetType(item.getTargetType());
                    }

                    voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                    voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                    //广告订单量
                    voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                    voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                    voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                    voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                    voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                    voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                    voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                    voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                    voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                    //广告销售额
                    voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");


                    //分时调价设置
                    if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                        voBuilder.setMarketplaceId(item.getMarketplaceId());
                    }
                    if (item.getIsBudgetPricing() != null) {
                        voBuilder.setIsBudgetPricing(item.getIsBudgetPricing());
                    }
                    if (item.getIsStatePricing() != null) {
                        voBuilder.setIsStatePricing(item.getIsStatePricing());
                    }
                    if (item.getPricingStartStopState() != null) {
                        voBuilder.setPricingStartStopState(item.getPricingStartStopState());
                    }
                    if (item.getPricingBudgetState() != null) {
                        voBuilder.setPricingBudgetState(item.getPricingBudgetState());
                    }
                    if (item.getIsSpacePricing() != null) {
                        voBuilder.setIsSpacePricing(item.getIsSpacePricing());
                    }
                    if (item.getPricingSpaceState() != null) {
                        voBuilder.setPricingSpaceState(item.getPricingSpaceState());
                    }

                    if (item.getCostType() != null) {
                        voBuilder.setCostType(item.getCostType());
                    }
                    if (item.getBrandEntityId() != null) {
                        voBuilder.setBrandEntityId(item.getBrandEntityId());
                    }
                    if (item.getBidOptimization() != null) {
                        voBuilder.setBidOptimization(item.getBidOptimization());
                    }
                    if (item.getBidMultiplier() != null) {
                        voBuilder.setBidMultiplier(item.getBidMultiplier());
                    }
                    if (item.getServingStatusName() != null) {
                        voBuilder.setServingStatusName(item.getServingStatusName());
                    }
                    if (CampaignTypeEnum.sp.getCampaignType().equals(item.getType())) {
                        voBuilder.setCostType("cpc");
                    }
                    voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");

                    voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                    voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                    voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));

                    //其他广告产品订单量
                    voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                    //本广告产品销售额
                    voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                    //其他广告产品销售额
                    voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                    //广告销量
                    voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                    // 花费占比
                    voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                    // 销售额占比
                    voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                    // 订单量占比
                    voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                    // 销量占比
                    voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                    //sp无数据，先保留
                    voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                    voBuilder.setOrdersNewToBrandFTD(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0));
                    voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                    voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                    voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                    voBuilder.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0));
                    voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                    voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                    //环比指标数据
                    if (MapUtils.isNotEmpty(finalCompareCampaignMap)) {
                        if (finalCompareCampaignMap.containsKey(item.getCampaignId())) {
                            StreamDataViewVo compareItem = finalCompareCampaignMap.get(item.getCampaignId());

                            voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                            //曝光环比值
                            long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                            voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                    new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                            //点击量环比值
                            long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                            voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                    new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                            //ctr环比值
                            BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                            voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                            //cvr环比值
                            BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                            voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                            //Acos环比值
                            BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                            voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                            //Acots环比值
                            BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                            voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                            //Asots环比值
                            BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                            voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                            //AdOrderNum环比值
                            int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                            voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                    new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                            //AdCost环比值
                            BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                            voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                            //AdCostPerClick环比值
                            BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                            voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                            //AdSale环比值
                            BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                            voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                            //Roas环比值
                            BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                            voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                            //Cpa环比值
                            BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                            voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                            //Vcpm环比值
                            BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                            voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toString());


                            voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                            //AdSaleNum比值
                            int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                            voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                    new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                            //AdOtherOrderNum比值
                            int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                            voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                    new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                            //AdSales环比值
                            BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                            voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                            voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                            //AdOtherSales环比值
                            BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                            voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                            //OrderNum比值
                            int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                            voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                    new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                            //AdSelfSaleNum比值
                            int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                            voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                    new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                            //AdOtherSaleNum比值
                            int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                            voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                    new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                            //AdCostPercentage环比值
                            BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                            voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                            //AdSalePercentage环比值
                            BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                            voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                            //AdOrderNumPercentage环比值
                            BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                            voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                            //OrderNumPercentage环比值
                            BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                            voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                            // AdvertisingUnitPrice 环比
                            voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                            voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));

                        }
                    }

                    return voBuilder.build();

                }).collect(Collectors.toList());
        campaignVoPage.addAllRows(vos);
        campaignVo.setPage(campaignVoPage.build());
        return campaignVo.build();
    }

    @Override
    public PlacementViewResponse.PlacementVo getPlacementView(Integer puid, PlacementViewParam param) {
        PlacementViewResponse.PlacementVo.Builder placementVo = PlacementViewResponse.PlacementVo.newBuilder();
        PlacementViewResponse.PlacementVo.Page.Builder placementVoPage = PlacementViewResponse.PlacementVo.Page.newBuilder();
        placementVoPage.setPageNo(param.getPageNo());
        placementVoPage.setPageSize(param.getPageSize());
        placementVo.setPage(placementVoPage.build());

        if (param.getUseAdvanced() &&
                (Objects.nonNull(param.getVcpmMin()) || Objects.nonNull(param.getVcpmMax()))) {
            return placementVo.build();
        }

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            return placementVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);


        List<String> adCamgaignIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adCamgaignIdList.add(bo.getCampaignId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            List<String> campaignIds = Arrays.asList(param.getCampaignIds().split(","));
            adCamgaignIdList.retainAll(campaignIds);
            if (CollectionUtils.isEmpty(adCamgaignIdList)) {
                return placementVo.build();
            }
        }
        param.setAdCamgaignIdList(adCamgaignIdList);
        param.setAdIdList(adIdList);

        //获取广告位视图列表页
        Page<PlacementViewVo> voPage = placementViewService.getPlacementViewPageVoList(puid, param);
        placementVoPage.setTotalPage(voPage.getTotalPage());
        placementVoPage.setTotalSize(voPage.getTotalSize());
        List<PlacementViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> comparePlacementMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> campaignIdList = rows.stream().map(PlacementViewVo::getCampaignId).collect(Collectors.toList());
                comparePlacementMap = placementViewService.getStreamDataByCompare(param, campaignIdList);
            }

            Map<String, StreamDataViewVo> finalComparePlacementMap = comparePlacementMap;
            List<PlacementViewResponse.PlacementVo.Page.PlacementViewVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                PlacementViewResponse.PlacementVo.Page.PlacementViewVo.Builder voBuilder = PlacementViewResponse.PlacementVo.Page.PlacementViewVo.newBuilder();
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (StringUtils.isNotBlank(item.getPredicate())) {
                    voBuilder.setPredicate(item.getPredicate());
                }
                if (StringUtils.isNotBlank(item.getPercentage())) {
                    voBuilder.setPercentage(item.getPercentage());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }
                if (StringUtils.isNotBlank(item.getStrategy())) {
                    voBuilder.setStrategy(item.getStrategy());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }

                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }

                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }

                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalComparePlacementMap)) {
                    if (finalComparePlacementMap.containsKey(item.getCampaignId())) {
                        StreamDataViewVo compareItem = finalComparePlacementMap.get(item.getCampaignId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                    }
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            placementVoPage.addAllRows(vos);
        }
        placementVo.setPage(placementVoPage.build());
        return placementVo.build();
    }

    @Override
    public PlacementViewAggregateResponse.AggregateVo getPlacementViewAggregate(Integer puid, PlacementViewParam param) {
        PlacementViewAggregateResponse.AggregateVo.Builder aggregateVo = PlacementViewAggregateResponse.AggregateVo.newBuilder();
        //总汇总初始化
        aggregateVo.setAggregateVo(this.buildAggregateViewVo(null));

        if (param.getUseAdvanced() &&
                (Objects.nonNull(param.getVcpmMin()) || Objects.nonNull(param.getVcpmMax()))) {
            ThreadPoolUtil.getPerspectiveAggregatePlacementSyncPool().execute(() -> cpcPageIdsHandler.addAggregatePlacementIdsTemporarySynchronize(new AggregatePlacementIdsTemporary(), param.getPageSign(), ""));
            return aggregateVo.build();
        }

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            ThreadPoolUtil.getPerspectiveAggregatePlacementSyncPool().execute(() -> {
                cpcPageIdsHandler.addAggregatePlacementIdsTemporarySynchronize(new AggregatePlacementIdsTemporary(), param.getPageSign(), "");
            });
            return aggregateVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adCamgaignIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adCamgaignIdList.add(bo.getCampaignId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getCampaignIds())) {
            List<String> campaignIds = Arrays.asList(param.getCampaignIds().split(","));
            adCamgaignIdList.retainAll(campaignIds);
            if (CollectionUtils.isEmpty(adCamgaignIdList)) {
                //存储列表页的所有id到数据库中，用于展示汇总趋势图
                ThreadPoolUtil.getPerspectiveAggregatePlacementSyncPool().execute(() -> {
                    cpcPageIdsHandler.addAggregatePlacementIdsTemporarySynchronize(new AggregatePlacementIdsTemporary(), param.getPageSign(), "");
                });
                return aggregateVo.build();
            }
        }
        param.setAdCamgaignIdList(adCamgaignIdList);
        param.setAdIdList(adIdList);

        PlacementViewAggregateVo placementViewAggregateVo = placementViewService.getPlacementViewAggregateVoList(puid, param);
        aggregateVo.setAggregateVo(this.buildAggregateViewVo(placementViewAggregateVo.getAggregateVo()));
        if (placementViewAggregateVo.getAggregateVo() != null) {
            aggregateVo.setProductAggregateVo(this.buildAggregateViewVo(placementViewAggregateVo.getProductAggregateVo()));
            aggregateVo.setTopAggregateVo(this.buildAggregateViewVo(placementViewAggregateVo.getTopAggregateVo()));
            aggregateVo.setOtherAggregateVo(this.buildAggregateViewVo(placementViewAggregateVo.getOtherAggregateVo()));
            aggregateVo.setOffAmazonAggregateVo(this.buildAggregateViewVo(placementViewAggregateVo.getOffAmazonAggregateVo()));
        }
        return aggregateVo.build();
    }

    @Override
    public KeywordViewResponse.KeywordVo getKeywordView(Integer puid, KeywordViewParam param) {
        KeywordViewResponse.KeywordVo.Builder keywordVo = KeywordViewResponse.KeywordVo.newBuilder();
        KeywordViewResponse.KeywordVo.Page.Builder keywordVoPage = KeywordViewResponse.KeywordVo.Page.newBuilder();
        keywordVoPage.setPageNo(param.getPageNo());
        keywordVoPage.setPageSize(param.getPageSize());
        keywordVo.setPage(keywordVoPage.build());

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            return keywordVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adGroupIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adGroupIdList.add(bo.getAdGroupId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return keywordVo.build();
            }
        }
        param.setAdGroupIdList(adGroupIdList);
        param.setAdIdList(adIdList);

        Page<KeywordViewVo> voPage;
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && KeywordViewParam.OrderFieldEnum.BID.getField().equals(param.getOrderField())) {
            voPage = keywordViewService.getVoPageByInternalMemory(puid, param);
        } else {
            voPage = keywordViewService.getVoPageByFeedPage(puid, param);
        }
        keywordVoPage.setTotalPage(voPage.getTotalPage());
        keywordVoPage.setTotalSize(voPage.getTotalSize());
        List<KeywordViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> keywordIdList = rows.stream().map(KeywordViewVo::getKeywordId).collect(Collectors.toList());
                compareKeywordMap = keywordViewService.getStreamDataByCompare(param, keywordIdList);
            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List<KeywordViewResponse.KeywordVo.Page.KeywordPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                KeywordViewResponse.KeywordVo.Page.KeywordPageVo.Builder voBuilder = KeywordViewResponse.KeywordVo.Page.KeywordPageVo.newBuilder();
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getKeywordId())) {
                    voBuilder.setKeywordId(item.getKeywordId());
                }
                if (StringUtils.isNotBlank(item.getKeywordText())) {
                    voBuilder.setKeywordText(item.getKeywordText());
                }
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    List<AdTagVo> adTagVos = new ArrayList<>();
                    AdTagVo.Builder adTagVo;
                    for (AdTag adTag : item.getAdTags()) {
                        adTagVo = AdTagVo.newBuilder();
                        adTagVo.setId(Int64Value.of(adTag.getId()));
                        adTagVo.setName(adTag.getName());
                        adTagVo.setColor(adTag.getColor());
                        adTagVos.add(adTagVo.build());
                    }
                    voBuilder.addAllAdTags(adTagVos);
                }
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (StringUtils.isNotBlank(item.getMatchType())) {
                    voBuilder.setMatchType(item.getMatchType());
                }
                if (item.getIsPricing() != null) {
                    voBuilder.setIsPricing(item.getIsPricing());
                }
                if (item.getPricingState() != null) {
                    voBuilder.setPricingState(item.getPricingState());
                }
                if (StringUtils.isNotBlank(item.getAdvRank())) {
                    voBuilder.setAdvRank(item.getAdvRank());
                }
                if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                    voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                } else {
                    voBuilder.setTopImpressionShare("-");
                }
                // 封装关键词实时排名参数
                if (item.getRankVo() != null) {
                    KeywordViewResponse.KeywordVo.Page.KeywordPageVo.KeywordRankParamVo.Builder rankVoBuilder = KeywordViewResponse.KeywordVo.Page.KeywordPageVo.KeywordRankParamVo.newBuilder();
                    KeywordsRankParamVo rankParamVo = item.getRankVo();
                    if (rankParamVo.getId() != null) {
                        rankVoBuilder.setId(rankParamVo.getId());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getSiteId())) {
                        rankVoBuilder.setSiteId(rankParamVo.getSiteId());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getSiteName())) {
                        rankVoBuilder.setSiteName(rankParamVo.getSiteName());
                    }
                    if (CollectionUtils.isNotEmpty(rankParamVo.getProducts())) {
                        List<ProductRpcVo> productRpcVos = Lists.newArrayList();
                        rankParamVo.getProducts().stream().forEach(product -> {
                            ProductRpcVo.Builder productRpcVo = ProductRpcVo.newBuilder();
                            if (StringUtils.isNotBlank(product.getAsin())) {
                                productRpcVo.setAsin(product.getAsin());
                                productRpcVo.setAsinUrl(product.getAsinUrl());
                            }
                            if (StringUtils.isNotBlank(product.getMainImage())) {
                                productRpcVo.setMainImage(product.getMainImage());
                            }
                            productRpcVos.add(productRpcVo.build());
                        });
                        rankVoBuilder.addAllProducts(productRpcVos);
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getKeyword())) {
                        rankVoBuilder.setKeyword(rankParamVo.getKeyword());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getKeywordId())) {
                        rankVoBuilder.setKeywordId(rankParamVo.getKeywordId());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getUrl())) {
                        rankVoBuilder.setUrl(rankParamVo.getUrl());
                    }
                    voBuilder.setRankVo(rankVoBuilder.build());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }
                if (StringUtils.isNotBlank(item.getSuggestBid())) {
                    voBuilder.setSuggestBid(item.getSuggestBid());
                }
                if (StringUtils.isNotBlank(item.getRangeStart())) {
                    voBuilder.setRangeStart(item.getRangeStart());
                }
                if (StringUtils.isNotBlank(item.getRangeEnd())) {
                    voBuilder.setRangeEnd(item.getRangeEnd());
                }
                if (StringUtils.isNotBlank(item.getBid())) {
                    voBuilder.setBid(item.getBid());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }
                voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                if (item.getWeekRatio() != null) {
                    voBuilder.setWeekRatio(item.getWeekRatio().toString());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }
                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
                    if (finalCompareKeywordMap.containsKey(item.getKeywordId())) {
                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getKeywordId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                    }
                }
                Optional.ofNullable(item.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setCostType);
                Optional.ofNullable(item.getGoal()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setGoal);
                Optional.ofNullable(item.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setAdFormat);
                return voBuilder.build();
            }).collect(Collectors.toList());
            keywordVoPage.addAllRows(vos);
        }
        keywordVo.setPage(keywordVoPage.build());
        return keywordVo.build();
    }

    @Override
    public KeywordViewAggregateResponse.AggregateVo getKeywordViewAggregate(Integer puid, KeywordViewParam param) {
        KeywordViewAggregateResponse.AggregateVo.Builder aggregateVo = KeywordViewAggregateResponse.AggregateVo.newBuilder();
        //总汇总初始化
        aggregateVo.setAggregateVo(this.buildKeywordAggregateViewVo(null));
        if (StringUtils.isNotBlank(param.getQueryValue())) {
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(param.getQueryValue());
            if (keywordGroupValueEnumByTextCn != null) {
                param.setQueryValue(keywordGroupValueEnumByTextCn.getKeywordText());
            }
        }

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateKeyWordSyncPool(), param.getPageSign(), null);
            return aggregateVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adGroupIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adGroupIdList.add(bo.getAdGroupId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                //存储列表页的所有id到数据库中，用于展示汇总趋势图
                this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateKeyWordSyncPool(), param.getPageSign(), null);
                return aggregateVo.build();
            }
        }
        param.setAdGroupIdList(adGroupIdList);
        param.setAdIdList(adIdList);

        //获取汇总数据
        KeywordViewAggregatePageVo keywordViewAggregatePageVo = keywordViewService.getKeywordViewAggregatePageVo(puid, param);
        aggregateVo.setAggregateVo(this.buildKeywordAggregateViewVo(keywordViewAggregatePageVo.getAggregateVo()));
        KeywordViewAggregateResponse.AggregateVo.Page.Builder page = KeywordViewAggregateResponse.AggregateVo.Page.newBuilder();
        Page<KeywordViewAggregateVo> voPage = keywordViewAggregatePageVo.getPage();
        if (voPage != null && CollectionUtils.isNotEmpty(voPage.getRows())) {
            List<KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo> keywordAggregateVoList = new ArrayList<>();
            KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo keywordAggregateVo;
            for (KeywordViewAggregateVo vo : voPage.getRows()) {
                keywordAggregateVo = this.buildKeywordAggregateViewVo(vo);
                keywordAggregateVoList.add(keywordAggregateVo);
            }
            page.setTotalPage(voPage.getTotalPage());
            page.setTotalSize(voPage.getTotalSize());
            page.addAllRow(keywordAggregateVoList);
        }

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        aggregateVo.setPage(page.build());
        return aggregateVo.build();
    }

    @Override
    public AutoTargetViewResponse.TargetVo getAutoTargetView(Integer puid, TargetViewParam param) {
        AutoTargetViewResponse.TargetVo.Builder targetVo = AutoTargetViewResponse.TargetVo.newBuilder();
        AutoTargetViewResponse.TargetVo.Page.Builder targetVoPage = AutoTargetViewResponse.TargetVo.Page.newBuilder();
        //总汇总初始化
        targetVo.setAggregateVo(this.buildAggregateViewVo(null));
        targetVoPage.setPageNo(param.getPageNo());
        targetVoPage.setPageSize(param.getPageSize());

        // 处理vcpm高级筛选 后端没有这个筛选 但是由于前端问题 还是会传这个参数！！ FK
        if (param.getUseAdvanced() &&
                (Objects.nonNull(param.getVcpmMin()) || Objects.nonNull(param.getVcpmMax()))) {
            this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool(), param.getPageSign(), null);
            targetVoPage.setTotalPage(0);
            targetVoPage.setTotalSize(0);
            targetVo.setPage(targetVoPage.build());
            return targetVo.build();
        }

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool(), param.getPageSign(), null);
            targetVoPage.setTotalPage(0);
            targetVoPage.setTotalSize(0);
            targetVo.setPage(targetVoPage.build());
            return targetVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adGroupIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adGroupIdList.add(bo.getAdGroupId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                //存储列表页的所有id到数据库中，用于展示汇总趋势图
                this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool(), param.getPageSign(), null);
                return targetVo.build();
            }
        }
        param.setAdGroupIdList(adGroupIdList);
        param.setAdIdList(adIdList);

        AutoTargetViewPageVo autoTargetViewPageVo = autoTargetViewService.getAutoTargetViewPageVoList(puid, param);
        targetVo.setAggregateVo(this.buildAggregateViewVo(autoTargetViewPageVo.getAggregateViewVo()));

        Page<CategoryTargetViewVo> voPage = autoTargetViewPageVo.getPage();
        targetVoPage.setTotalPage(voPage.getTotalPage());
        targetVoPage.setTotalSize(voPage.getTotalSize());
        List<CategoryTargetViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> targetIdList = rows.stream().map(CategoryTargetViewVo::getTargetId).collect(Collectors.toList());
                compareKeywordMap = keywordViewService.getStreamDataByCompare(param, targetIdList);
            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List<AutoTargetViewResponse.TargetVo.Page.TargetingPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                AutoTargetViewResponse.TargetVo.Page.TargetingPageVo.Builder voBuilder = AutoTargetViewResponse.TargetVo.Page.TargetingPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    voBuilder.setTargetId(item.getTargetId());
                }
                if (StringUtils.isNotBlank(item.getTargetText())) {
                    voBuilder.setTargetText(item.getTargetText());
                }
                if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                    voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                } else {
                    voBuilder.setTopImpressionShare("-");
                }
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    List<AdTagVo> adTagVos = new ArrayList<>();
                    AdTagVo.Builder adTagVo;
                    for (AdTag adTag : item.getAdTags()) {
                        adTagVo = AdTagVo.newBuilder();
                        adTagVo.setId(Int64Value.of(adTag.getId()));
                        adTagVo.setName(adTag.getName());
                        adTagVo.setColor(adTag.getColor());
                        adTagVos.add(adTagVo.build());
                    }
                    voBuilder.addAllAdTags(adTagVos);
                }
                if (item.getIsPricing() != null) {
                    voBuilder.setIsPricing(item.getIsPricing());
                }
                if (item.getPricingState() != null) {
                    voBuilder.setPricingState(item.getPricingState());
                }
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }
                if (StringUtils.isNotBlank(item.getBid())) {
                    voBuilder.setBid(item.getBid());
                }
                if (StringUtils.isNotBlank(item.getSuggestBid())) {
                    voBuilder.setSuggestBid(item.getSuggestBid());
                }
                if (StringUtils.isNotBlank(item.getRangeStart())) {
                    voBuilder.setRangeStart(item.getRangeStart());
                }
                if (StringUtils.isNotBlank(item.getRangeEnd())) {
                    voBuilder.setRangeEnd(item.getRangeEnd());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }
                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
                    if (finalCompareKeywordMap.containsKey(item.getTargetId())) {
                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getTargetId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                    }
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            targetVoPage.addAllRows(vos);
        }
        targetVo.setPage(targetVoPage.build());
        return targetVo.build();
    }

    @Override
    public CategoryTargetViewResponse.TargetVo getCategoryTargetView(Integer puid, TargetViewParam param) {
        CategoryTargetViewResponse.TargetVo.Builder targetVo = CategoryTargetViewResponse.TargetVo.newBuilder();
        CategoryTargetViewResponse.TargetVo.Page.Builder targetVoPage = CategoryTargetViewResponse.TargetVo.Page.newBuilder();
        //总汇总初始化
        targetVoPage.setPageNo(param.getPageNo());
        targetVoPage.setPageSize(param.getPageSize());

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            targetVoPage.setTotalPage(0);
            targetVoPage.setTotalSize(0);
            targetVo.setPage(targetVoPage.build());
            return targetVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adGroupIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adGroupIdList.add(bo.getAdGroupId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return targetVo.build();
            }
        }
        param.setAdGroupIdList(adGroupIdList);
        param.setAdIdList(adIdList);

        Page<CategoryTargetViewVo> voPage;
        //根据筛选条件判断是否走内存分页
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && TargetViewParam.OrderFieldEnum.BID.getField().equals(param.getOrderField())) {
            voPage = categoryTargetViewService.getVoPageByInternalMemory(puid, param);
        } else {
            voPage = categoryTargetViewService.getVoPageByFeedPage(puid, param);
        }
        targetVoPage.setTotalPage(voPage.getTotalPage());
        targetVoPage.setTotalSize(voPage.getTotalSize());
        List<CategoryTargetViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> targetIdList = rows.stream().map(CategoryTargetViewVo::getTargetId).collect(Collectors.toList());
                compareKeywordMap = keywordViewService.getStreamDataByCompare(param, targetIdList);
            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List<CategoryTargetViewResponse.TargetVo.Page.TargetingPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                CategoryTargetViewResponse.TargetVo.Page.TargetingPageVo.Builder voBuilder = CategoryTargetViewResponse.TargetVo.Page.TargetingPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getTargetType())) {
                    voBuilder.setTargetType(item.getTargetType());
                }
                if (StringUtils.isNotBlank(item.getTitle())) {
                    voBuilder.setTitle(item.getTitle());
                }
                if (StringUtils.isNotBlank(item.getBrandName())) {
                    voBuilder.setBrandName(item.getBrandName());
                }
                if (StringUtils.isNotBlank(item.getCommodityPriceRange())) {
                    voBuilder.setCommodityPriceRange(item.getCommodityPriceRange());
                }
                if (StringUtils.isNotBlank(item.getRating())) {
                    voBuilder.setRating(item.getRating());
                }
                if (StringUtils.isNotBlank(item.getDistribution())) {
                    voBuilder.setDistribution(item.getDistribution());
                }
                if (StringUtils.isNotBlank(item.getSelectType())) {
                    voBuilder.setSelectType(item.getSelectType());
                }
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    voBuilder.setTargetId(item.getTargetId());
                }
                if (StringUtils.isNotBlank(item.getTargetText())) {
                    voBuilder.setTargetText(item.getTargetText());
                }
                if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                    voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                } else {
                    voBuilder.setTopImpressionShare("-");
                }
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    List<AdTagVo> adTagVos = new ArrayList<>();
                    AdTagVo.Builder adTagVo;
                    for (AdTag adTag : item.getAdTags()) {
                        adTagVo = AdTagVo.newBuilder();
                        adTagVo.setId(Int64Value.of(adTag.getId()));
                        adTagVo.setName(adTag.getName());
                        adTagVo.setColor(adTag.getColor());
                        adTagVos.add(adTagVo.build());
                    }
                    voBuilder.addAllAdTags(adTagVos);
                }
                if (item.getIsPricing() != null) {
                    voBuilder.setIsPricing(item.getIsPricing());
                }
                if (item.getPricingState() != null) {
                    voBuilder.setPricingState(item.getPricingState());
                }
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }
                if (StringUtils.isNotBlank(item.getBid())) {
                    voBuilder.setBid(item.getBid());
                }
                if (StringUtils.isNotBlank(item.getSuggestBid())) {
                    voBuilder.setSuggestBid(item.getSuggestBid());
                }
                if (StringUtils.isNotBlank(item.getRangeStart())) {
                    voBuilder.setRangeStart(item.getRangeStart());
                }
                if (StringUtils.isNotBlank(item.getRangeEnd())) {
                    voBuilder.setRangeEnd(item.getRangeEnd());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }

                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
                    if (finalCompareKeywordMap.containsKey(item.getTargetId())) {
                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getTargetId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                    }
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            targetVoPage.addAllRows(vos);
        }
        targetVo.setPage(targetVoPage.build());
        return targetVo.build();
    }

    @Override
    public CategoryTargetViewAggregateResponse.AggregateVo getCategoryTargetViewAggregate(Integer puid, TargetViewParam param) {
        CategoryTargetViewAggregateResponse.AggregateVo.Builder aggregateVo = CategoryTargetViewAggregateResponse.AggregateVo.newBuilder();
        //总汇总初始化
        aggregateVo.setAggregateVo(this.buildTargetAggregateViewVo(null));

        //根据所选的ASIN查询广告产品表t_amazon_ad_product_report查出ad_id列表和广告活动列表
        List<AmazonAdProductPerspectiveBO> productBoList = odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(
                puid, param.getShopIdList(), param.getMarketplaceId(), param.getSearchType(), param.getSearchValue(),
                param.getStartDate(), param.getEndDate());
        if (CollectionUtils.isEmpty(productBoList)) {
            //存储列表页的所有id到数据库中，用于展示汇总趋势图
            this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool(), param.getPageSign(), null);
            return aggregateVo.build();
        }

        //填充店铺数据与销售额
        //过滤无效店铺
        List<Integer> dataShopIdList = productBoList.stream().map(AmazonAdProductPerspectiveBO::getShopId).distinct().collect(Collectors.toList());
        param.setShopIdList(dataShopIdList);
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        List<String> adGroupIdList = new ArrayList<>();
        List<String> adIdList = new ArrayList<>();
        for (AmazonAdProductPerspectiveBO bo : productBoList) {
            adGroupIdList.add(bo.getAdGroupId());
            adIdList.add(bo.getAdId());
        }
        //与筛选的广告组对比，若无交集则直接返回空
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = Arrays.asList(param.getGroupId().split(","));
            adGroupIdList.retainAll(groupIds);
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                //存储列表页的所有id到数据库中，用于展示汇总趋势图
                this.addAggregateIdsTemporarySynchronize(ThreadPoolUtil.getPerspectiveAggregateTargetSyncPool(), param.getPageSign(), null);
                return aggregateVo.build();
            }
        }
        param.setAdGroupIdList(adGroupIdList);
        param.setAdIdList(adIdList);

        //获取汇总数据
        CategoryTargetViewAggregatePageVo aggregatePageVo = categoryTargetViewService.getCategoryTargetViewAggregatePageVo(puid, param);
        aggregateVo.setAggregateVo(this.buildTargetAggregateViewVo(aggregatePageVo.getAggregateVo()));
        CategoryTargetViewAggregateResponse.AggregateVo.Page.Builder page = CategoryTargetViewAggregateResponse.AggregateVo.Page.newBuilder();
        Page<CategoryTargetViewAggregateVo> voPage = aggregatePageVo.getPage();
        if (voPage != null && CollectionUtils.isNotEmpty(voPage.getRows())) {
            List<CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo> targetAggregateVoList = new ArrayList<>();
            CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo targetAggregateVo;
            for (CategoryTargetViewAggregateVo vo : voPage.getRows()) {
                targetAggregateVo = this.buildTargetAggregateViewVo(vo);
                targetAggregateVoList.add(targetAggregateVo);
            }
            page.setTotalPage(voPage.getTotalPage());
            page.setTotalSize(voPage.getTotalSize());
            page.addAllRows(targetAggregateVoList);
        }

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        aggregateVo.setPage(page.build());
        return aggregateVo.build();
    }

    /**
     * 填充店铺数据
     */
    @Override
    public void fillShopSellerId(Integer puid, ViewBaseParam param) {
        //获取店铺
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuthList)) {
            AssertUtil.fail("获取店铺异常，请联系管理员");
        }
        param.setShopIdList(shopAuthList.stream().map(ShopAuth::getId).collect(Collectors.toList()));
        param.setSellerIdList(shopAuthList.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList()));
    }

    /**
     * 填充店铺销售额
     */
    @Override
    public void fillShopSale(Integer puid, ViewBaseParam param) {
        //获取店铺销售额
        List<ShopSaleDto> shopSaleDtoList = CpCShopDataService.getListShopSale(puid, param.getShopIdList(), param.getStartDate(), param.getEndDate());
        BigDecimal shopSales = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(shopSaleDtoList)) {
            shopSales = shopSaleDtoList.stream().filter(item -> item != null && item.getSumRange() != null).map(ShopSaleDto::getSumRange).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        param.setShopSales(shopSales);
    }

    /**
     * 广告活动、广告位、自动投放视图转换AggregateViewVo
     */
    @Override
    public AggregateViewVo buildAggregateViewVo(StreamDataViewVo aggregateViewVo) {
        AggregateViewVo.Builder builder = AggregateViewVo.newBuilder();
        builder.setAdCost("0");
        builder.setImpressions(0);
        builder.setClicks(0);
        builder.setCpa("0");
        builder.setAdCostPerClick("0");
        builder.setCtr("0");
        builder.setCvr("0");
        builder.setAcos("0");
        builder.setRoas("0");
        builder.setAdOrderNum(0);
        builder.setAdSaleNum(0);
        builder.setAdOtherOrderNum(0);
        builder.setAdSale("0");
        builder.setAdSales("0");
        builder.setAdOtherSales("0");
        builder.setOrderNum(0);
        builder.setAdSelfSaleNum(0);
        builder.setAdOtherSaleNum(0);
        builder.setAcots("0");
        builder.setAsots("0");
        builder.setVcpm("0");
        builder.setViewImpressions(0);
        builder.setOrdersNewToBrandFTD(0);
        builder.setOrderRateNewToBrandFTD("0");
        builder.setSalesNewToBrandFTD("0");
        builder.setSalesRateNewToBrandFTD("0");
        builder.setUnitsOrderedNewToBrandFTD(0);
        builder.setUnitsOrderedRateNewToBrandFTD("0");
        builder.setUnitsOrderedRateNewToBrandFTD("0");
        builder.setAdCostPercentage("0");
        builder.setAdSalePercentage("0");
        builder.setAdOrderNumPercentage("0");
        builder.setOrderNumPercentage("0");
        builder.setAdvertisingUnitPrice("0");
        builder.setOrdersNewToBrandFTD(0);
        builder.setOrderRateNewToBrandFTD("0");
        builder.setSalesNewToBrandFTD("0");
        builder.setSalesRateNewToBrandFTD("0");
        builder.setUnitsOrderedNewToBrandFTD(0);
        builder.setUnitsOrderedRateNewToBrandFTD("0");
        builder.setViewImpressions(0);
        builder.setVcpm("0");
        if (aggregateViewVo == null) {
            return builder.build();
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCost())) {
            builder.setAdCost(aggregateViewVo.getAdCost());
        }
        if (aggregateViewVo.getImpressions() != null) {
            builder.setImpressions(aggregateViewVo.getImpressions());
        }
        if (aggregateViewVo.getClicks() != null) {
            builder.setClicks(aggregateViewVo.getClicks());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCpa())) {
            builder.setCpa(aggregateViewVo.getCpa());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPerClick())) {
            builder.setAdCostPerClick(aggregateViewVo.getAdCostPerClick());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCtr())) {
            builder.setCtr(aggregateViewVo.getCtr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCvr())) {
            builder.setCvr(aggregateViewVo.getCvr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcos())) {
            builder.setAcos(aggregateViewVo.getAcos());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getRoas())) {
            builder.setRoas(aggregateViewVo.getRoas());
        }
        if (aggregateViewVo.getAdOrderNum() != null) {
            builder.setAdOrderNum(aggregateViewVo.getAdOrderNum());
        }
        if (aggregateViewVo.getAdSaleNum() != null) {
            builder.setAdSaleNum(aggregateViewVo.getAdSaleNum());
        }
        if (aggregateViewVo.getAdOtherOrderNum() != null) {
            builder.setAdOtherOrderNum(aggregateViewVo.getAdOtherOrderNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSale())) {
            builder.setAdSale(aggregateViewVo.getAdSale());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSales())) {
            builder.setAdSales(aggregateViewVo.getAdSales());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOtherSales())) {
            builder.setAdOtherSales(aggregateViewVo.getAdOtherSales());
        }
        if (aggregateViewVo.getOrderNum() != null) {
            builder.setOrderNum(aggregateViewVo.getOrderNum());
        }
        if (aggregateViewVo.getAdSelfSaleNum() != null) {
            builder.setAdSelfSaleNum(aggregateViewVo.getAdSelfSaleNum());
        }
        if (aggregateViewVo.getAdOtherSaleNum() != null) {
            builder.setAdOtherSaleNum(aggregateViewVo.getAdOtherSaleNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcots())) {
            builder.setAcots(aggregateViewVo.getAcots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAsots())) {
            builder.setAsots(aggregateViewVo.getAsots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPercentage())) {
            builder.setAdCostPercentage(aggregateViewVo.getAdCostPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSalePercentage())) {
            builder.setAdSalePercentage(aggregateViewVo.getAdSalePercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOrderNumPercentage())) {
            builder.setAdOrderNumPercentage(aggregateViewVo.getAdOrderNumPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderNumPercentage())) {
            builder.setOrderNumPercentage(aggregateViewVo.getOrderNumPercentage());
        }
        builder.setAdvertisingUnitPrice(Optional.ofNullable(aggregateViewVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        if (aggregateViewVo.getOrdersNewToBrandFTD() != null) {
            builder.setOrdersNewToBrandFTD(aggregateViewVo.getOrdersNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderRateNewToBrandFTD())) {
            builder.setOrderRateNewToBrandFTD(aggregateViewVo.getOrderRateNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesNewToBrandFTD())) {
            builder.setSalesNewToBrandFTD(aggregateViewVo.getSalesNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesRateNewToBrandFTD())) {
            builder.setSalesRateNewToBrandFTD(aggregateViewVo.getSalesRateNewToBrandFTD());
        }
        if (aggregateViewVo.getUnitsOrderedNewToBrandFTD() != null) {
            builder.setUnitsOrderedNewToBrandFTD(aggregateViewVo.getUnitsOrderedNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD())) {
            builder.setUnitsOrderedRateNewToBrandFTD(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD());
        }
        if (aggregateViewVo.getViewImpressions() != null) {
            builder.setViewImpressions(aggregateViewVo.getViewImpressions());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getVcpm())) {
            builder.setVcpm(aggregateViewVo.getVcpm());
        }
        return builder.build();
    }

    /**
     * 关键词投放视图转换AggregateViewVo
     */
    @Override
    public KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo buildKeywordAggregateViewVo(KeywordViewAggregateVo aggregateViewVo) {
        KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo.Builder builder = KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo.newBuilder();
        builder.setAdCost("0");
        builder.setImpressions(0);
        builder.setClicks(0);
        builder.setCpa("0");
        builder.setAdCostPerClick("0");
        builder.setCtr("0");
        builder.setCvr("0");
        builder.setAcos("0");
        builder.setRoas("0");
        builder.setAdOrderNum(0);
        builder.setAdSaleNum(0);
        builder.setAdOtherOrderNum(0);
        builder.setAdSale("0");
        builder.setAdSales("0");
        builder.setAdOtherSales("0");
        builder.setOrderNum(0);
        builder.setAdSelfSaleNum(0);
        builder.setAdOtherSaleNum(0);
        builder.setAcots("0");
        builder.setAsots("0");
        builder.setAdCostPercentage("0");
        builder.setAdSalePercentage("0");
        builder.setAdOrderNumPercentage("0");
        builder.setOrderNumPercentage("0");
        builder.setOrdersNewToBrandFTD(0);
        builder.setOrderRateNewToBrandFTD("0");
        builder.setSalesNewToBrandFTD("0");
        builder.setSalesRateNewToBrandFTD("0");
        builder.setUnitsOrderedNewToBrandFTD(0);
        builder.setUnitsOrderedRateNewToBrandFTD("0");
        builder.setViewImpressions(0);
        builder.setVcpm("0");
        if (aggregateViewVo == null) {
            return builder.build();
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getKeywordText())) {
            builder.setKeywordText(aggregateViewVo.getKeywordText());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getKeywordTextCn())) {
            builder.setKeywordTextCn(aggregateViewVo.getKeywordTextCn());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getMatchType())) {
            builder.setMatchType(aggregateViewVo.getMatchType());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCost())) {
            builder.setAdCost(aggregateViewVo.getAdCost());
        }
        if (aggregateViewVo.getImpressions() != null) {
            builder.setImpressions(aggregateViewVo.getImpressions());
        }
        if (aggregateViewVo.getClicks() != null) {
            builder.setClicks(aggregateViewVo.getClicks());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCpa())) {
            builder.setCpa(aggregateViewVo.getCpa());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPerClick())) {
            builder.setAdCostPerClick(aggregateViewVo.getAdCostPerClick());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCtr())) {
            builder.setCtr(aggregateViewVo.getCtr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCvr())) {
            builder.setCvr(aggregateViewVo.getCvr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcos())) {
            builder.setAcos(aggregateViewVo.getAcos());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getRoas())) {
            builder.setRoas(aggregateViewVo.getRoas());
        }
        if (aggregateViewVo.getAdOrderNum() != null) {
            builder.setAdOrderNum(aggregateViewVo.getAdOrderNum());
        }
        if (aggregateViewVo.getAdSaleNum() != null) {
            builder.setAdSaleNum(aggregateViewVo.getAdSaleNum());
        }
        if (aggregateViewVo.getAdOtherOrderNum() != null) {
            builder.setAdOtherOrderNum(aggregateViewVo.getAdOtherOrderNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSale())) {
            builder.setAdSale(aggregateViewVo.getAdSale());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSales())) {
            builder.setAdSales(aggregateViewVo.getAdSales());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOtherSales())) {
            builder.setAdOtherSales(aggregateViewVo.getAdOtherSales());
        }
        if (aggregateViewVo.getOrderNum() != null) {
            builder.setOrderNum(aggregateViewVo.getOrderNum());
        }
        if (aggregateViewVo.getAdSelfSaleNum() != null) {
            builder.setAdSelfSaleNum(aggregateViewVo.getAdSelfSaleNum());
        }
        if (aggregateViewVo.getAdOtherSaleNum() != null) {
            builder.setAdOtherSaleNum(aggregateViewVo.getAdOtherSaleNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcots())) {
            builder.setAcots(aggregateViewVo.getAcots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAsots())) {
            builder.setAsots(aggregateViewVo.getAsots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPercentage())) {
            builder.setAdCostPercentage(aggregateViewVo.getAdCostPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSalePercentage())) {
            builder.setAdSalePercentage(aggregateViewVo.getAdSalePercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOrderNumPercentage())) {
            builder.setAdOrderNumPercentage(aggregateViewVo.getAdOrderNumPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderNumPercentage())) {
            builder.setOrderNumPercentage(aggregateViewVo.getOrderNumPercentage());
        }
        if (aggregateViewVo.getOrdersNewToBrandFTD() != null) {
            builder.setOrdersNewToBrandFTD(aggregateViewVo.getOrdersNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderRateNewToBrandFTD())) {
            builder.setOrderRateNewToBrandFTD(aggregateViewVo.getOrderRateNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesNewToBrandFTD())) {
            builder.setSalesNewToBrandFTD(aggregateViewVo.getSalesNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesRateNewToBrandFTD())) {
            builder.setSalesRateNewToBrandFTD(aggregateViewVo.getSalesRateNewToBrandFTD());
        }
        if (aggregateViewVo.getUnitsOrderedNewToBrandFTD() != null) {
            builder.setUnitsOrderedNewToBrandFTD(aggregateViewVo.getUnitsOrderedNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD())) {
            builder.setUnitsOrderedRateNewToBrandFTD(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD());
        }
        if (aggregateViewVo.getViewImpressions() != null) {
            builder.setViewImpressions(aggregateViewVo.getViewImpressions());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getVcpm())) {
            builder.setVcpm(aggregateViewVo.getVcpm());
        }
        builder.setAdvertisingUnitPrice(Optional.ofNullable(aggregateViewVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        return builder.build();
    }


    /**
     * 商品投放视图转换AggregateViewVo
     */
    @Override
    public CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo buildTargetAggregateViewVo(CategoryTargetViewAggregateVo aggregateViewVo) {
        CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo.Builder builder = CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo.newBuilder();
        builder.setAdCost("0");
        builder.setImpressions(0);
        builder.setClicks(0);
        builder.setCpa("0");
        builder.setAdCostPerClick("0");
        builder.setCtr("0");
        builder.setCvr("0");
        builder.setAcos("0");
        builder.setRoas("0");
        builder.setAdOrderNum(0);
        builder.setAdSaleNum(0);
        builder.setAdOtherOrderNum(0);
        builder.setAdSale("0");
        builder.setAdSales("0");
        builder.setAdOtherSales("0");
        builder.setOrderNum(0);
        builder.setAdSelfSaleNum(0);
        builder.setAdOtherSaleNum(0);
        builder.setAcots("0");
        builder.setAsots("0");
        builder.setAdCostPercentage("0");
        builder.setAdSalePercentage("0");
        builder.setAdOrderNumPercentage("0");
        builder.setOrderNumPercentage("0");
        builder.setOrdersNewToBrandFTD(0);
        builder.setOrderRateNewToBrandFTD("0");
        builder.setSalesNewToBrandFTD("0");
        builder.setSalesRateNewToBrandFTD("0");
        builder.setUnitsOrderedNewToBrandFTD(0);
        builder.setUnitsOrderedRateNewToBrandFTD("0");
        builder.setViewImpressions(0);
        builder.setVcpm("0");
        if (aggregateViewVo == null) {
            return builder.build();
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getTargetType())) {
            builder.setTargetType(aggregateViewVo.getTargetType());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getTitle())) {
            builder.setTitle(aggregateViewVo.getTitle());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAsin())) {
            builder.setAsin(aggregateViewVo.getAsin());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getImgUrl())) {
            builder.setImgUrl(aggregateViewVo.getImgUrl());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCategory())) {
            builder.setCategory(aggregateViewVo.getCategory());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getDomain())) {
            builder.setDomain(aggregateViewVo.getDomain());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getTargetText())) {
            builder.setTargetText(aggregateViewVo.getTargetText());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCost())) {
            builder.setAdCost(aggregateViewVo.getAdCost());
        }
        if (aggregateViewVo.getImpressions() != null) {
            builder.setImpressions(aggregateViewVo.getImpressions());
        }
        if (aggregateViewVo.getClicks() != null) {
            builder.setClicks(aggregateViewVo.getClicks());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCpa())) {
            builder.setCpa(aggregateViewVo.getCpa());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPerClick())) {
            builder.setAdCostPerClick(aggregateViewVo.getAdCostPerClick());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCtr())) {
            builder.setCtr(aggregateViewVo.getCtr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCvr())) {
            builder.setCvr(aggregateViewVo.getCvr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcos())) {
            builder.setAcos(aggregateViewVo.getAcos());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getRoas())) {
            builder.setRoas(aggregateViewVo.getRoas());
        }
        if (aggregateViewVo.getAdOrderNum() != null) {
            builder.setAdOrderNum(aggregateViewVo.getAdOrderNum());
        }
        if (aggregateViewVo.getAdSaleNum() != null) {
            builder.setAdSaleNum(aggregateViewVo.getAdSaleNum());
        }
        if (aggregateViewVo.getAdOtherOrderNum() != null) {
            builder.setAdOtherOrderNum(aggregateViewVo.getAdOtherOrderNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSale())) {
            builder.setAdSale(aggregateViewVo.getAdSale());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSales())) {
            builder.setAdSales(aggregateViewVo.getAdSales());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOtherSales())) {
            builder.setAdOtherSales(aggregateViewVo.getAdOtherSales());
        }
        if (aggregateViewVo.getOrderNum() != null) {
            builder.setOrderNum(aggregateViewVo.getOrderNum());
        }
        if (aggregateViewVo.getAdSelfSaleNum() != null) {
            builder.setAdSelfSaleNum(aggregateViewVo.getAdSelfSaleNum());
        }
        if (aggregateViewVo.getAdOtherSaleNum() != null) {
            builder.setAdOtherSaleNum(aggregateViewVo.getAdOtherSaleNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcots())) {
            builder.setAcots(aggregateViewVo.getAcots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAsots())) {
            builder.setAsots(aggregateViewVo.getAsots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPercentage())) {
            builder.setAdCostPercentage(aggregateViewVo.getAdCostPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSalePercentage())) {
            builder.setAdSalePercentage(aggregateViewVo.getAdSalePercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOrderNumPercentage())) {
            builder.setAdOrderNumPercentage(aggregateViewVo.getAdOrderNumPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderNumPercentage())) {
            builder.setOrderNumPercentage(aggregateViewVo.getOrderNumPercentage());
        }
        if (aggregateViewVo.getOrdersNewToBrandFTD() != null) {
            builder.setOrdersNewToBrandFTD(aggregateViewVo.getOrdersNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderRateNewToBrandFTD())) {
            builder.setOrderRateNewToBrandFTD(aggregateViewVo.getOrderRateNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesNewToBrandFTD())) {
            builder.setSalesNewToBrandFTD(aggregateViewVo.getSalesNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesRateNewToBrandFTD())) {
            builder.setSalesRateNewToBrandFTD(aggregateViewVo.getSalesRateNewToBrandFTD());
        }
        if (aggregateViewVo.getUnitsOrderedNewToBrandFTD() != null) {
            builder.setUnitsOrderedNewToBrandFTD(aggregateViewVo.getUnitsOrderedNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD())) {
            builder.setUnitsOrderedRateNewToBrandFTD(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD());
        }
        if (aggregateViewVo.getViewImpressions() != null) {
            builder.setViewImpressions(aggregateViewVo.getViewImpressions());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getVcpm())) {
            builder.setVcpm(aggregateViewVo.getVcpm());
        }
        builder.setAdvertisingUnitPrice(Optional.ofNullable(aggregateViewVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        return builder.build();
    }

    /**
     * 商品投放视图转换AggregateViewVo
     */
    @Override
    public AudienceTargetViewAggregateResponse.AggregateVo.TargetAggregateVo buildAudienceTargetAggregateViewVo(AudienceTargetViewAggregateVo aggregateViewVo) {
        AudienceTargetViewAggregateResponse.AggregateVo.TargetAggregateVo.Builder builder = AudienceTargetViewAggregateResponse.AggregateVo.TargetAggregateVo.newBuilder();
        builder.setAdCost("0");
        builder.setImpressions(0);
        builder.setClicks(0);
        builder.setCpa("0");
        builder.setAdCostPerClick("0");
        builder.setCtr("0");
        builder.setCvr("0");
        builder.setAcos("0");
        builder.setRoas("0");
        builder.setAdOrderNum(0);
        builder.setAdSaleNum(0);
        builder.setAdOtherOrderNum(0);
        builder.setAdSale("0");
        builder.setAdSales("0");
        builder.setAdOtherSales("0");
        builder.setOrderNum(0);
        builder.setAdSelfSaleNum(0);
        builder.setAdOtherSaleNum(0);
        builder.setAcots("0");
        builder.setAsots("0");
        builder.setAdCostPercentage("0");
        builder.setAdSalePercentage("0");
        builder.setAdOrderNumPercentage("0");
        builder.setOrderNumPercentage("0");
        builder.setOrdersNewToBrandFTD(0);
        builder.setOrderRateNewToBrandFTD("0");
        builder.setSalesNewToBrandFTD("0");
        builder.setSalesRateNewToBrandFTD("0");
        builder.setUnitsOrderedNewToBrandFTD(0);
        builder.setUnitsOrderedRateNewToBrandFTD("0");
        builder.setViewImpressions(0);
        builder.setVcpm("0");
        if (aggregateViewVo == null) {
            return builder.build();
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getTargetType())) {
            builder.setTargetType(aggregateViewVo.getTargetType());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getTitle())) {
            builder.setTitle(aggregateViewVo.getTitle());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getTargetText())) {
            builder.setTargetText(aggregateViewVo.getTargetText());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCost())) {
            builder.setAdCost(aggregateViewVo.getAdCost());
        }
        if (aggregateViewVo.getImpressions() != null) {
            builder.setImpressions(aggregateViewVo.getImpressions());
        }
        if (aggregateViewVo.getClicks() != null) {
            builder.setClicks(aggregateViewVo.getClicks());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCpa())) {
            builder.setCpa(aggregateViewVo.getCpa());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPerClick())) {
            builder.setAdCostPerClick(aggregateViewVo.getAdCostPerClick());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCtr())) {
            builder.setCtr(aggregateViewVo.getCtr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getCvr())) {
            builder.setCvr(aggregateViewVo.getCvr());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcos())) {
            builder.setAcos(aggregateViewVo.getAcos());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getRoas())) {
            builder.setRoas(aggregateViewVo.getRoas());
        }
        if (aggregateViewVo.getAdOrderNum() != null) {
            builder.setAdOrderNum(aggregateViewVo.getAdOrderNum());
        }
        if (aggregateViewVo.getAdSaleNum() != null) {
            builder.setAdSaleNum(aggregateViewVo.getAdSaleNum());
        }
        if (aggregateViewVo.getAdOtherOrderNum() != null) {
            builder.setAdOtherOrderNum(aggregateViewVo.getAdOtherOrderNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSale())) {
            builder.setAdSale(aggregateViewVo.getAdSale());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSales())) {
            builder.setAdSales(aggregateViewVo.getAdSales());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOtherSales())) {
            builder.setAdOtherSales(aggregateViewVo.getAdOtherSales());
        }
        if (aggregateViewVo.getOrderNum() != null) {
            builder.setOrderNum(aggregateViewVo.getOrderNum());
        }
        if (aggregateViewVo.getAdSelfSaleNum() != null) {
            builder.setAdSelfSaleNum(aggregateViewVo.getAdSelfSaleNum());
        }
        if (aggregateViewVo.getAdOtherSaleNum() != null) {
            builder.setAdOtherSaleNum(aggregateViewVo.getAdOtherSaleNum());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAcots())) {
            builder.setAcots(aggregateViewVo.getAcots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAsots())) {
            builder.setAsots(aggregateViewVo.getAsots());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdCostPercentage())) {
            builder.setAdCostPercentage(aggregateViewVo.getAdCostPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdSalePercentage())) {
            builder.setAdSalePercentage(aggregateViewVo.getAdSalePercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getAdOrderNumPercentage())) {
            builder.setAdOrderNumPercentage(aggregateViewVo.getAdOrderNumPercentage());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderNumPercentage())) {
            builder.setOrderNumPercentage(aggregateViewVo.getOrderNumPercentage());
        }
        if (aggregateViewVo.getOrdersNewToBrandFTD() != null) {
            builder.setOrdersNewToBrandFTD(aggregateViewVo.getOrdersNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getOrderRateNewToBrandFTD())) {
            builder.setOrderRateNewToBrandFTD(aggregateViewVo.getOrderRateNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesNewToBrandFTD())) {
            builder.setSalesNewToBrandFTD(aggregateViewVo.getSalesNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getSalesRateNewToBrandFTD())) {
            builder.setSalesRateNewToBrandFTD(aggregateViewVo.getSalesRateNewToBrandFTD());
        }
        if (aggregateViewVo.getUnitsOrderedNewToBrandFTD() != null) {
            builder.setUnitsOrderedNewToBrandFTD(aggregateViewVo.getUnitsOrderedNewToBrandFTD());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD())) {
            builder.setUnitsOrderedRateNewToBrandFTD(aggregateViewVo.getUnitsOrderedRateNewToBrandFTD());
        }
        if (aggregateViewVo.getViewImpressions() != null) {
            builder.setViewImpressions(aggregateViewVo.getViewImpressions());
        }
        if (StringUtils.isNotBlank(aggregateViewVo.getVcpm())) {
            builder.setVcpm(aggregateViewVo.getVcpm());
        }
        builder.setAdvertisingUnitPrice(Optional.ofNullable(aggregateViewVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        return builder.build();
    }

    private CampaignViewResponse.CampaignVo.Page.BudgetUsage converCampaignBudgetUsage(CampaignViewVo.BudgetUsage budgetUsage) {
            CampaignViewResponse.CampaignVo.Page.BudgetUsage.Builder budgetUsageVo = CampaignViewResponse.CampaignVo.Page.BudgetUsage.newBuilder();
            if (budgetUsage.getIsNoData() != null) {
                budgetUsageVo.setIsNoData(budgetUsage.getIsNoData());
            }
            if (budgetUsage.getPercent() != null) {
                budgetUsageVo.setPercent(budgetUsage.getPercent());
            }
            if (StringUtils.isNotBlank(budgetUsage.getLastUpdateAt())) {
                budgetUsageVo.setLastUpdateAt(budgetUsage.getLastUpdateAt());
            }
            if (budgetUsage.getCurrentBudget() != null) {
                budgetUsageVo.setCurrentBudget(budgetUsage.getCurrentBudget());
            }
            if (budgetUsage.getDayType() != null) {
                budgetUsageVo.setDayType(budgetUsage.getDayType());
            }
            if (budgetUsage.getDate() != null) {
                budgetUsageVo.setDate(budgetUsage.getDate());
            }
            if (CollectionUtils.isNotEmpty(budgetUsage.getItemList())) {
                List<CampaignViewResponse.CampaignVo.Page.BudgetUsage.Item> budgetUsageItemVoList = new ArrayList<>();
                CampaignViewResponse.CampaignVo.Page.BudgetUsage.Item.Builder budgetUsageItemVo;
                for (CampaignViewVo.BudgetUsageItem budgetUsageItem : budgetUsage.getItemList()) {
                    budgetUsageItemVo = CampaignViewResponse.CampaignVo.Page.BudgetUsage.Item.newBuilder();
                    if (StringUtils.isNotBlank(budgetUsageItem.getUpdateAt())) {
                        budgetUsageItemVo.setUpdateAt(budgetUsageItem.getUpdateAt());
                    }
                    if (budgetUsageItem.getPercent() != null) {
                        budgetUsageItemVo.setPercent(budgetUsageItem.getPercent());
                    }
                    if (budgetUsageItem.getCurrentBudget() != null) {
                        budgetUsageItemVo.setCurrentBudget(budgetUsageItem.getCurrentBudget());
                    }
                    budgetUsageItemVoList.add(budgetUsageItemVo.build());
                }
                budgetUsageVo.addAllItem(budgetUsageItemVoList);
            }
           return budgetUsageVo.build();
    }

    //存储列表页的所有id到数据库中，用于展示汇总趋势图
    private void addAggregateIdsTemporarySynchronize(ThreadPoolExecutor executor, String pageSign, String searchParamStr) {
        executor.execute(() -> {
            cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(new AggregateIdsTemporary(), pageSign, searchParamStr);
        });
    }

    private void addAggregateCampaignIdsTemporarySynchronize(CampaignViewParam param) {
        ThreadPoolUtil.getPerspectiveAggregateCampaignSyncPool().execute(() -> {
            AggregateIdsTemporary aggregateIdsTemporary = new AggregateIdsTemporary();
            cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(aggregateIdsTemporary, param.getPageSign(), Constants.AUTO);
            cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(aggregateIdsTemporary, param.getPageSign(), Constants.MANUAL);
            cpcPageIdsHandler.addAggregateIdsTemporarySynchronize(aggregateIdsTemporary, param.getPageSign(), "");
        });
    }


    @Override
    public SearchTermsViewResponse.SearchTermsVo getSearchTermsView(Integer puid, SearchTermsViewParam param) {
        SearchTermsViewResponse.SearchTermsVo.Builder searchTermsVo = SearchTermsViewResponse.SearchTermsVo.newBuilder();
        SearchTermsViewResponse.SearchTermsVo.Page.Builder searchTermsVoPage = SearchTermsViewResponse.SearchTermsVo.Page.newBuilder();
        //总汇总初始化
        searchTermsVo.setAggregateVo(this.buildAggregateViewVo(null));
        searchTermsVoPage.setPageNo(param.getPageNo());
        searchTermsVoPage.setPageSize(param.getPageSize());
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        SearchTermsViewPageVo  searchTermsViewPageVo = searchTermsViewService.getSearchTermsViewPageVoList(puid, param);
        searchTermsVo.setAggregateVo(this.buildAggregateViewVo(searchTermsViewPageVo.getAggregateViewVo()));

        Page<SearchTermsViewVo> voPage = searchTermsViewPageVo.getPage();
        searchTermsVoPage.setTotalPage(voPage.getTotalPage());
        searchTermsVoPage.setTotalSize(voPage.getTotalSize());
        List<SearchTermsViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

//            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
//                //对比时无须高级搜索条件
//                param.setUseAdvanced(false);
//
//                List<String> targetIdList = rows.stream().map(SearchTermsViewVo::getTargetId).collect(Collectors.toList());
//                compareKeywordMap = keywordViewService.getStreamDataByCompare(param, targetIdList);
//            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List< SearchTermsViewResponse.SearchTermsVo.Page.SearchTermsPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                SearchTermsViewResponse.SearchTermsVo.Page.SearchTermsPageVo.Builder voBuilder = SearchTermsViewResponse.SearchTermsVo.Page.SearchTermsPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (StringUtils.isNotBlank(item.getQuery())) {
                    voBuilder.setQuery(item.getQuery());
                }
                if (StringUtils.isNotBlank(item.getQueryCn())) {
                    voBuilder.setQueryCn(item.getQueryCn());
                }
                if (StringUtils.isNotBlank(item.getMainImage())) {
                    voBuilder.setMainImage(item.getMainImage());
                }
                if (item.getIsAsin() != null) {
                    voBuilder.setIsAsin(item.getIsAsin());
                }
                //前端需要marketplace_id, 而且marketplace_id 是必传字段，这边直接返回给前端；
                voBuilder.setMarketplaceId(param.getMarketplaceId());
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

//                //环比指标数据
//                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
//                    if (finalCompareKeywordMap.containsKey(item.getTargetId())) {
//                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getTargetId());
//
//                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
//                        //曝光环比值
//                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
//                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
//                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
//                        //点击量环比值
//                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
//                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
//                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
//                        //ctr环比值
//                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
//                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
//                        //cvr环比值
//                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
//                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
//                        //Acos环比值
//                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
//                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
//                        //Acots环比值
//                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
//                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
//                        //Asots环比值
//                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
//                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
//                        //AdOrderNum环比值
//                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
//                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
//                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
//                        //AdCost环比值
//                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
//                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
//                        //AdCostPerClick环比值
//                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
//                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
//                        //AdSale环比值
//                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
//                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
//                        //Roas环比值
//                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
//                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
//                        //AdSaleNum比值
//                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
//                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
//                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
//                        //AdOtherOrderNum比值
//                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
//                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
//                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
//                        //AdSales环比值
//                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
//                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());
//
//
//                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
//                        //AdOtherSales环比值
//                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
//                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
//                        //OrderNum比值
//                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
//                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
//                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
//                        //AdSelfSaleNum比值
//                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
//                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
//                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
//                        //AdOtherSaleNum比值
//                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
//                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
//                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
//                        //Cpa环比值
//                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
//                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
//                        // AdvertisingUnitPrice 环比
//                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
//                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
//                    }
//                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            searchTermsVoPage.addAllRows(vos);
        }
        searchTermsVo.setPage(searchTermsVoPage.build());
        return searchTermsVo.build();
    }

    @Override
    public SearchTermsViewResponse.SearchTermsVo getAllSearchTermsView(Integer puid, SearchTermsViewParam param) {
        SearchTermsViewResponse.SearchTermsVo.Builder searchTermsVo = SearchTermsViewResponse.SearchTermsVo.newBuilder();
        SearchTermsViewResponse.SearchTermsVo.Page.Builder searchTermsVoPage = SearchTermsViewResponse.SearchTermsVo.Page.newBuilder();
        //总汇总初始化
        searchTermsVo.setAggregateVo(this.buildAggregateViewVo(null));
        searchTermsVoPage.setPageNo(param.getPageNo());
        searchTermsVoPage.setPageSize(param.getPageSize());
        this.fillShopSellerId(puid, param);
        this.fillShopSale(puid, param);

        SearchTermsViewPageVo  searchTermsViewPageVo = searchTermsViewService.getAllSearchTermsViewPageVoList(puid, param);
        searchTermsVo.setAggregateVo(this.buildAggregateViewVo(searchTermsViewPageVo.getAggregateViewVo()));

        Page<SearchTermsViewVo> voPage = searchTermsViewPageVo.getPage();
        searchTermsVoPage.setTotalPage(voPage.getTotalPage());
        searchTermsVoPage.setTotalSize(voPage.getTotalSize());
        List<SearchTermsViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

//            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
//                //对比时无须高级搜索条件
//                param.setUseAdvanced(false);
//
//                List<String> targetIdList = rows.stream().map(SearchTermsViewVo::getTargetId).collect(Collectors.toList());
//                compareKeywordMap = keywordViewService.getStreamDataByCompare(param, targetIdList);
//            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List< SearchTermsViewResponse.SearchTermsVo.Page.SearchTermsPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                SearchTermsViewResponse.SearchTermsVo.Page.SearchTermsPageVo.Builder voBuilder = SearchTermsViewResponse.SearchTermsVo.Page.SearchTermsPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (StringUtils.isNotBlank(item.getQuery())) {
                    voBuilder.setQuery(item.getQuery());
                }
                if (StringUtils.isNotBlank(item.getQueryCn())) {
                    voBuilder.setQueryCn(item.getQueryCn());
                }
                if (StringUtils.isNotBlank(item.getMainImage())) {
                    voBuilder.setMainImage(item.getMainImage());
                }
                if (item.getIsAsin() != null) {
                    voBuilder.setIsAsin(item.getIsAsin());
                }
                //前端需要marketplace_id, 而且marketplace_id 是必传字段，这边直接返回给前端；
                voBuilder.setMarketplaceId(param.getMarketplaceId());
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                //sb才有
                voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0L));
                voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                voBuilder.setOrdersNewToBrandFTD(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0));
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
//                //环比指标数据
//                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
//                    if (finalCompareKeywordMap.containsKey(item.getTargetId())) {
//                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getTargetId());
//
//                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
//                        //曝光环比值
//                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
//                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
//                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
//                        //点击量环比值
//                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
//                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
//                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
//                        //ctr环比值
//                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
//                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
//                        //cvr环比值
//                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
//                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
//                        //Acos环比值
//                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
//                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
//                        //Acots环比值
//                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
//                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
//                        //Asots环比值
//                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
//                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
//                        //AdOrderNum环比值
//                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
//                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
//                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
//                        //AdCost环比值
//                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
//                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
//                        //AdCostPerClick环比值
//                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
//                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
//                        //AdSale环比值
//                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
//                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
//                        //Roas环比值
//                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
//                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
//                        //AdSaleNum比值
//                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
//                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
//                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
//                        //AdOtherOrderNum比值
//                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
//                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
//                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
//                        //AdSales环比值
//                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
//                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());
//
//
//                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
//                        //AdOtherSales环比值
//                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
//                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
//                        //OrderNum比值
//                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
//                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
//                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
//                        //AdSelfSaleNum比值
//                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
//                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
//                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
//                        //AdOtherSaleNum比值
//                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
//                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
//                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
//                                        2, RoundingMode.HALF_UP).toString());
//
//                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
//                        //Cpa环比值
//                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
//                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
//                        // AdvertisingUnitPrice 环比
//                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
//                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
//                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
//                    }
//                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            searchTermsVoPage.addAllRows(vos);
        }
        searchTermsVo.setPage(searchTermsVoPage.build());
        return searchTermsVo.build();
    }

    @Override
    public CampaignViewResponse.CampaignVo getAllCampaignView(Integer puid, CampaignViewParam param) {
        CampaignViewResponse.CampaignVo.Builder campaignVo = CampaignViewResponse.CampaignVo.newBuilder();
        CampaignViewResponse.CampaignVo.Page.Builder campaignVoPage = CampaignViewResponse.CampaignVo.Page.newBuilder();
        campaignVoPage.setPageNo(param.getPageNo());
        campaignVoPage.setPageSize(param.getPageSize());
        campaignVo.setPage(campaignVoPage.build());
        //总汇总初始化
        campaignVo.setAggregateViewVo(this.buildAggregateViewVo(null));

        Page<CampaignViewVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());

        //获取广告活动视图列表页与汇总
        CampaignViewPageVo campaignViewPageVo = campaignViewService.getAllCampaignViewPageVoList(puid, param, voPage);
        Page<CampaignViewVo> page = campaignViewPageVo.getPage();
        List<CampaignViewVo> rows = page.getRows();
        campaignVoPage.setTotalPage(page.getTotalPage());
        campaignVoPage.setTotalSize(page.getTotalSize());
        campaignVo.setAggregateViewVo(this.buildAggregateViewVo(campaignViewPageVo.getAggregateViewVo()));

        if (campaignViewPageVo.getAggregateViewVo() == null) {
            campaignVo.setPage(campaignVoPage.build());
            return campaignVo.build();
        }

        campaignVo.setAutoAggregateViewVo(this.buildAggregateViewVo(campaignViewPageVo.getAutoAggregateViewVo()));
        campaignVo.setManualAggregateViewVo(this.buildAggregateViewVo(campaignViewPageVo.getManualAggregateViewVo()));
        if (CollectionUtils.isEmpty(rows)) {
            campaignVo.setPage(campaignVoPage.build());
            return campaignVo.build();
        }

        //查询环比指标
        Map<String, StreamDataViewVo> compareCampaignMap = null;

        if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
            //对比时无须高级搜索条件
            param.setUseAdvanced(false);

            List<String> campaignIdList = rows.stream().map(CampaignViewVo::getCampaignId).collect(Collectors.toList());
            compareCampaignMap = campaignViewService.getAllStreamDataByCompare(param, campaignIdList);
        }

        Map<String, StreamDataViewVo> finalCompareCampaignMap = compareCampaignMap;
        List<CampaignViewResponse.CampaignVo.Page.CampaignPageVo> vos = rows.stream()
                .filter(Objects::nonNull).map(item -> {
                    CampaignViewResponse.CampaignVo.Page.CampaignPageVo.Builder voBuilder =
                            CampaignViewResponse.CampaignVo.Page.CampaignPageVo.newBuilder();
                    if (item.getId() != null) {
                        voBuilder.setId(item.getId());
                    }
                    if (item.getShopId() != null) {
                        voBuilder.setShopId(item.getShopId());
                    }
                    if (StringUtils.isNotBlank(item.getCampaignId())) {
                        voBuilder.setCampaignId(item.getCampaignId());
                    }
                    if (StringUtils.isNotBlank(item.getType())) {
                        voBuilder.setType(item.getType());
                    }
                    if (StringUtils.isNotBlank(item.getPortfolioId())) {
                        voBuilder.setPortfolioId(item.getPortfolioId());
                    }
                    if (StringUtils.isNotBlank(item.getPortfolioName())) {
                        voBuilder.setPortfolioName(item.getPortfolioName());
                    }
                    if (item.getIsHidden() != null) {
                        voBuilder.setIsHidden(item.getIsHidden());
                    }
                    if (StringUtils.isNotBlank(item.getName())) {
                        voBuilder.setName(item.getName());
                    }
                    if (StringUtils.isNotBlank(item.getState())) {
                        voBuilder.setState(item.getState());
                    }
                    if (StringUtils.isNotBlank(item.getServingStatus())) {
                        voBuilder.setServingStatus(item.getServingStatus());
                    }
                    if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                        voBuilder.setServingStatusDec(item.getServingStatusDec());
                    }
                    if (item.getOutOfBudget() != null) {
                        voBuilder.setOutOfBudget(item.getOutOfBudget());
                    }
                    if (StringUtils.isNotBlank(item.getDailyBudget())) {
                        voBuilder.setDailyBudget(item.getDailyBudget());
                    }
                    if (item.getBudgetLog() != null) {
                        DataLogVo budgetLog = item.getBudgetLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(budgetLog.getCount());
                        dataLog.setPreviousValue(budgetLog.getPreviousValue());
                        dataLog.setNewValue(budgetLog.getNewValue());
                        dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                        voBuilder.setBudgetLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getBudgetType())) {
                        voBuilder.setBudgetType(item.getBudgetType());
                    }
                    if (item.getBudgetUsage() != null) {
                        voBuilder.setBudgetUsage(converCampaignBudgetUsage(item.getBudgetUsage()));
                    }
                    if (CollectionUtils.isNotEmpty(item.getBudgetUsages())) {
                        List<CampaignViewResponse.CampaignVo.Page.BudgetUsage> budgetUsages = item.getBudgetUsages().stream().filter(Objects::nonNull).map(this::converCampaignBudgetUsage).collect(Collectors.toList());
                        voBuilder.addAllBudgetUsages(budgetUsages);
                    }
                    if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                        voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                    } else {
                        voBuilder.setTopImpressionShare("-");
                    }
                    if (item.getMissBudgets() != null) {
                        CampaignViewResponse.CampaignVo.Page.MissBudgetRecommendation.Builder missBudgetsVo = CampaignViewResponse.CampaignVo.Page.MissBudgetRecommendation.newBuilder();
                        CampaignViewVo.MissBudgetRecommendation missBudget = item.getMissBudgets();
                        if (StringUtils.isNotBlank(missBudget.getCampaignId())) {
                            missBudgetsVo.setCampaignId(missBudget.getCampaignId());
                        }
                        if (missBudget.getSuggestedBudget() != null) {
                            missBudgetsVo.setSuggestedBudget(missBudget.getSuggestedBudget());
                        }
                        if (missBudget.getEstimatedMissedSalesLower() != null) {
                            missBudgetsVo.setEstimatedMissedSalesLower(missBudget.getEstimatedMissedSalesLower());
                        }
                        if (missBudget.getEstimatedMissedSalesUpper() != null) {
                            missBudgetsVo.setEstimatedMissedSalesUpper(missBudget.getEstimatedMissedSalesUpper());
                        }
                        if (missBudget.getEstimatedMissedImpressionsLower() != null) {
                            missBudgetsVo.setEstimatedMissedImpressionsLower(missBudget.getEstimatedMissedImpressionsLower());
                        }
                        if (missBudget.getEstimatedMissedImpressionsUpper() != null) {
                            missBudgetsVo.setEstimatedMissedImpressionsUpper(missBudget.getEstimatedMissedImpressionsUpper());
                        }
                        if (missBudget.getEstimatedMissedClicksLower() != null) {
                            missBudgetsVo.setEstimatedMissedClicksLower(missBudget.getEstimatedMissedClicksLower());
                        }
                        if (missBudget.getEstimatedMissedClicksUpper() != null) {
                            missBudgetsVo.setEstimatedMissedClicksUpper(missBudget.getEstimatedMissedClicksUpper());
                        }
                        if (missBudget.getPercentTimeInBudget() != null) {
                            missBudgetsVo.setPercentTimeInBudget(missBudget.getPercentTimeInBudget());
                        }
                        if (StringUtils.isNotBlank(missBudget.getStartDate())) {
                            missBudgetsVo.setStartDate(missBudget.getStartDate());
                        }
                        if (StringUtils.isNotBlank(missBudget.getEndDate())) {
                            missBudgetsVo.setEndDate(missBudget.getEndDate());
                        }
                        if (StringUtils.isNotBlank(missBudget.getRuleId())) {
                            missBudgetsVo.setRuleId(missBudget.getRuleId());
                        }
                        if (StringUtils.isNotBlank(missBudget.getRuleName())) {
                            missBudgetsVo.setRuleName(missBudget.getRuleName());
                        }
                        if (missBudget.getSuggestedBudgetIncreasePercent() != null) {
                            missBudgetsVo.setSuggestedBudgetIncreasePercent(missBudget.getSuggestedBudgetIncreasePercent());
                        }
                        voBuilder.setMissBudget(missBudgetsVo.build());
                    }
                    if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                        voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                    }
                    if (StringUtils.isNotBlank(item.getTargetingType())) {
                        voBuilder.setTargetingType(item.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(item.getStrategy())) {
                        voBuilder.setStrategy(item.getStrategy());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementProductPage())) {
                        voBuilder.setPlacementProductPage(item.getPlacementProductPage());
                    } else {
                        voBuilder.setPlacementProductPage("0");
                    }
                    if (item.getPlacementProductPageLog() != null) {
                        DataLogVo placementProductPageLog = item.getPlacementProductPageLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(placementProductPageLog.getCount());
                        dataLog.setPreviousValue(placementProductPageLog.getPreviousValue());
                        dataLog.setNewValue(placementProductPageLog.getNewValue());
                        dataLog.setSiteOperationTime(placementProductPageLog.getSiteOperationTime());
                        voBuilder.setPlacementProductPageLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementTop())) {
                        voBuilder.setPlacementTop(item.getPlacementTop());
                    } else {
                        voBuilder.setPlacementTop("0");
                    }
                    if (item.getPlacementTopLog() != null) {
                        DataLogVo placementTopLog = item.getPlacementTopLog();
                        DataLog.Builder dataLog = DataLog.newBuilder();
                        dataLog.setCount(placementTopLog.getCount());
                        dataLog.setPreviousValue(placementTopLog.getPreviousValue());
                        dataLog.setNewValue(placementTopLog.getNewValue());
                        dataLog.setSiteOperationTime(placementTopLog.getSiteOperationTime());
                        voBuilder.setPlacementTopLog(dataLog.build());
                    }
                    if (StringUtils.isNotBlank(item.getPlacementRestOfSearch())) {
                        voBuilder.setPlacementRestOfSearch(item.getPlacementRestOfSearch());
                    } else {
                        voBuilder.setPlacementRestOfSearch("0");
                    }
                    if (item.getIsUpdateBudget() != null) {
                        voBuilder.setIsUpdateBudget(item.getIsUpdateBudget());
                    }
                    if (item.getIsUpdatePlacementTop() != null) {
                        voBuilder.setIsUpdatePlacementTop(item.getIsUpdatePlacementTop());
                    }
                    if (item.getIsUpdatePlacementProductPage() != null) {
                        voBuilder.setIsUpdatePlacementProductPage(item.getIsUpdatePlacementProductPage());
                    }
                    if (StringUtils.isNotBlank(item.getStartDate())) {
                        voBuilder.setStartDate(item.getStartDate());
                    }
                    if (StringUtils.isNotBlank(item.getEndDate())) {
                        voBuilder.setEndDate(item.getEndDate());
                    }
                    if (StringUtils.isNotBlank(item.getType())) {
                        voBuilder.setType(item.getType());
                    }
                    if (item.getTargetType() != null) {
                        voBuilder.setTargetType(item.getTargetType());
                    }

                    voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                    voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                    //广告订单量
                    voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                    voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                    voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                    voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                    voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                    voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                    voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                    voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                    voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                    //广告销售额
                    voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");


                    //分时调价设置
                    if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                        voBuilder.setMarketplaceId(item.getMarketplaceId());
                    }
                    if (item.getIsBudgetPricing() != null) {
                        voBuilder.setIsBudgetPricing(item.getIsBudgetPricing());
                    }
                    if (item.getIsStatePricing() != null) {
                        voBuilder.setIsStatePricing(item.getIsStatePricing());
                    }
                    if (item.getPricingStartStopState() != null) {
                        voBuilder.setPricingStartStopState(item.getPricingStartStopState());
                    }
                    if (item.getPricingBudgetState() != null) {
                        voBuilder.setPricingBudgetState(item.getPricingBudgetState());
                    }
                    if (item.getIsSpacePricing() != null) {
                        voBuilder.setIsSpacePricing(item.getIsSpacePricing());
                    }
                    if (item.getPricingSpaceState() != null) {
                        voBuilder.setPricingSpaceState(item.getPricingSpaceState());
                    }
                    if (item.getCostType() != null) {
                        voBuilder.setCostType(item.getCostType());
                    }
                    if (CampaignTypeEnum.sp.getCampaignType().equals(item.getType())) {
                        voBuilder.setCostType("cpc");
                    }
                    if (item.getBrandEntityId() != null) {
                        voBuilder.setBrandEntityId(item.getBrandEntityId());
                    }
                    if (item.getBidOptimization() != null) {
                        voBuilder.setBidOptimization(item.getBidOptimization());
                    }
                    if (item.getBidMultiplier() != null) {
                        voBuilder.setBidMultiplier(item.getBidMultiplier());
                    }
                    if (item.getServingStatusName() != null) {
                        voBuilder.setServingStatusName(item.getServingStatusName());
                    }
                    voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");

                    voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                    voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                    voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));

                    //其他广告产品订单量
                    voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                    //本广告产品销售额
                    voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                    //其他广告产品销售额
                    voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                    //广告销量
                    voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                    // 花费占比
                    voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                    // 销售额占比
                    voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                    // 订单量占比
                    voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                    // 销量占比
                    voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

                    //sb、sd才有
                    voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0L));
                    if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                        voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                    }else{
                        voBuilder.setVcpm("-");
                    }
                    voBuilder.setOrdersNewToBrandFTD(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0));
                    voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                    voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                    voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                    voBuilder.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0));
                    voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
                    voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                    //环比指标数据
                    if (MapUtils.isNotEmpty(finalCompareCampaignMap)) {
                        if (finalCompareCampaignMap.containsKey(item.getCampaignId())) {
                            StreamDataViewVo compareItem = finalCompareCampaignMap.get(item.getCampaignId());

                            voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                            //曝光环比值
                            long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                            voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                    new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                            //点击量环比值
                            long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                            voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                    new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                            //ctr环比值
                            BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                            voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                            //cvr环比值
                            BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                            voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                            //Acos环比值
                            BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                            voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                            //Acots环比值
                            BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                            voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                            //Asots环比值
                            BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                            voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                            //AdOrderNum环比值
                            int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                            voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                    new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                            //AdCost环比值
                            BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                            voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                            //AdCostPerClick环比值
                            BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                            voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                            //AdSale环比值
                            BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                            voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                            //Roas环比值
                            BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                            voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                            //Cpa环比值
                            BigDecimal CpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                            voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    CpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                            //AdSaleNum比值
                            int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                            voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                    new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                            //AdOtherOrderNum比值
                            int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                            voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                    new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                            //AdSales环比值
                            BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                            voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                            voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                            //AdOtherSales环比值
                            BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                            voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                            //OrderNum比值
                            int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                            voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                    new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                            //AdSelfSaleNum比值
                            int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                            voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                    new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                            //AdOtherSaleNum比值
                            int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                            voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                    new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                            2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                            //AdCostPercentage环比值
                            BigDecimal adCostPercentageDiff = new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage()));
                            voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adCostPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                            //AdSalePercentage环比值
                            BigDecimal adSalePercentageDiff = new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage()));
                            voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getCompareAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adSalePercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSalePercentage()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                            //AdOrderNumPercentage环比值
                            BigDecimal adOrderNumPercentageDiff = new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()));
                            voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    adOrderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                            voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                            //OrderNumPercentage环比值
                            BigDecimal orderNumPercentageDiff = new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage()));
                            voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    orderNumPercentageDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toString());

                            // AdvertisingUnitPrice 环比
                            voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                            voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                            //可见展示环比值
                            voBuilder.setCompareViewImpressions(compareItem.getViewImpressions() == null ? "0" : String.valueOf(compareItem.getViewImpressions()));
                            long viewImpressionDiff = voBuilder.getViewImpressions() - Long.parseLong(voBuilder.getCompareViewImpressions());
                            voBuilder.setCompareViewImpressionsRate(Integer.parseInt(voBuilder.getCompareViewImpressions()) == 0 ? "-" :
                                    new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions()),
                                            2, RoundingMode.HALF_UP).toString());
                            //Vcpm环比值
                            voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                            if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                                BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                                voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                        vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toPlainString());
                            }else{
                                voBuilder.setCompareVcpmRate("-");
                            }
                            //品牌新买家订单量环比值
                            voBuilder.setCompareOrdersNewToBrandFTD(compareItem.getOrdersNewToBrandFTD() != null ? compareItem.getOrdersNewToBrandFTD() : 0);
                            int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD() - voBuilder.getCompareOrdersNewToBrandFTD();
                            voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD() == 0 ? "-" :
                                    new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                            //品牌新买家订单百分比环比值
                            voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ? compareItem.getOrderRateNewToBrandFTD() : "0");
                            BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                            voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                            //品牌新买家销售额环比值
                            voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ? compareItem.getSalesNewToBrandFTD() : "0");
                            BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                            voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                            //品牌新买家销售额百分比环比值
                            voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ? compareItem.getSalesRateNewToBrandFTD() : "0");
                            BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                            voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                            //品牌新买家销量环比值
                            voBuilder.setCompareUnitsOrderedNewToBrandFTD(compareItem.getUnitsOrderedNewToBrandFTD() != null ? compareItem.getUnitsOrderedNewToBrandFTD() : 0);
                            int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD() - voBuilder.getCompareUnitsOrderedNewToBrandFTD();
                            voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD() == 0 ? "-" :
                                    new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                            //品牌新买家销量百分比环比值
                            voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ? compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                            BigDecimal unitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                            voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    unitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        }
                    }

                    return voBuilder.build();

                }).collect(Collectors.toList());
        campaignVoPage.addAllRows(vos);
        campaignVo.setPage(campaignVoPage.build());
        return campaignVo.build();
    }

    @Override
    public KeywordViewResponse.KeywordVo getAllKeywordView(Integer puid, KeywordViewParam param) {
        KeywordViewResponse.KeywordVo.Builder keywordVo = KeywordViewResponse.KeywordVo.newBuilder();
        KeywordViewResponse.KeywordVo.Page.Builder keywordVoPage = KeywordViewResponse.KeywordVo.Page.newBuilder();
        keywordVoPage.setPageNo(param.getPageNo());
        keywordVoPage.setPageSize(param.getPageSize());
        keywordVo.setPage(keywordVoPage.build());

        Page<KeywordViewVo> voPage = keywordViewService.getAllKeywordViewPageVoList(puid, param);
        keywordVoPage.setTotalPage(voPage.getTotalPage());
        keywordVoPage.setTotalSize(voPage.getTotalSize());
        List<KeywordViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> keywordIdList = rows.stream().map(KeywordViewVo::getKeywordId).collect(Collectors.toList());
                compareKeywordMap = keywordViewService.getAllStreamDataByCompare(param, keywordIdList);
            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List<KeywordViewResponse.KeywordVo.Page.KeywordPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                KeywordViewResponse.KeywordVo.Page.KeywordPageVo.Builder voBuilder = KeywordViewResponse.KeywordVo.Page.KeywordPageVo.newBuilder();
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getKeywordId())) {
                    voBuilder.setKeywordId(item.getKeywordId());
                }
                if (StringUtils.isNotBlank(item.getKeywordText())) {
                    voBuilder.setKeywordText(item.getKeywordText());
                }
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    List<AdTagVo> adTagVos = new ArrayList<>();
                    AdTagVo.Builder adTagVo;
                    for (AdTag adTag : item.getAdTags()) {
                        adTagVo = AdTagVo.newBuilder();
                        adTagVo.setId(Int64Value.of(adTag.getId()));
                        adTagVo.setName(adTag.getName());
                        adTagVo.setColor(adTag.getColor());
                        adTagVos.add(adTagVo.build());
                    }
                    voBuilder.addAllAdTags(adTagVos);
                }
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (StringUtils.isNotBlank(item.getMatchType())) {
                    voBuilder.setMatchType(item.getMatchType());
                }
                if (item.getIsPricing() != null) {
                    voBuilder.setIsPricing(item.getIsPricing());
                }
                if (item.getPricingState() != null) {
                    voBuilder.setPricingState(item.getPricingState());
                }
                if (StringUtils.isNotBlank(item.getAdvRank())) {
                    voBuilder.setAdvRank(item.getAdvRank());
                }
                if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                    voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                } else {
                    voBuilder.setTopImpressionShare("-");
                }
                // 封装关键词实时排名参数
                if (item.getRankVo() != null) {
                    KeywordViewResponse.KeywordVo.Page.KeywordPageVo.KeywordRankParamVo.Builder rankVoBuilder = KeywordViewResponse.KeywordVo.Page.KeywordPageVo.KeywordRankParamVo.newBuilder();
                    KeywordsRankParamVo rankParamVo = item.getRankVo();
                    if (rankParamVo.getId() != null) {
                        rankVoBuilder.setId(rankParamVo.getId());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getSiteId())) {
                        rankVoBuilder.setSiteId(rankParamVo.getSiteId());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getSiteName())) {
                        rankVoBuilder.setSiteName(rankParamVo.getSiteName());
                    }
                    if (CollectionUtils.isNotEmpty(rankParamVo.getProducts())) {
                        List<ProductRpcVo> productRpcVos = Lists.newArrayList();
                        rankParamVo.getProducts().stream().forEach(product -> {
                            ProductRpcVo.Builder productRpcVo = ProductRpcVo.newBuilder();
                            if (StringUtils.isNotBlank(product.getAsin())) {
                                productRpcVo.setAsin(product.getAsin());
                                productRpcVo.setAsinUrl(product.getAsinUrl());
                            }
                            if (StringUtils.isNotBlank(product.getMainImage())) {
                                productRpcVo.setMainImage(product.getMainImage());
                            }
                            productRpcVos.add(productRpcVo.build());
                        });
                        rankVoBuilder.addAllProducts(productRpcVos);
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getKeyword())) {
                        rankVoBuilder.setKeyword(rankParamVo.getKeyword());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getKeywordId())) {
                        rankVoBuilder.setKeywordId(rankParamVo.getKeywordId());
                    }
                    if (StringUtils.isNotBlank(rankParamVo.getUrl())) {
                        rankVoBuilder.setUrl(rankParamVo.getUrl());
                    }
                    voBuilder.setRankVo(rankVoBuilder.build());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }
                if (StringUtils.isNotBlank(item.getSuggestBid())) {
                    voBuilder.setSuggestBid(item.getSuggestBid());
                }
                if (StringUtils.isNotBlank(item.getRangeStart())) {
                    voBuilder.setRangeStart(item.getRangeStart());
                }
                if (StringUtils.isNotBlank(item.getRangeEnd())) {
                    voBuilder.setRangeEnd(item.getRangeEnd());
                }
                if (StringUtils.isNotBlank(item.getBid())) {
                    voBuilder.setBid(item.getBid());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }
                voBuilder.setSearchFrequencyRank(item.getSearchFrequencyRank());
                if (item.getWeekRatio() != null) {
                    voBuilder.setWeekRatio(item.getWeekRatio().toString());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }
                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                //sb、sd才有
                voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0L));
                if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                    voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                }else{
                    voBuilder.setVcpm("-");
                }
                voBuilder.setOrdersNewToBrandFTD(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0));
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                voBuilder.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0));
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
                    if (finalCompareKeywordMap.containsKey(item.getKeywordId())) {
                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getKeywordId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                        //可见展示环比值
                        voBuilder.setCompareViewImpressions(compareItem.getViewImpressions() == null ? "0" : String.valueOf(compareItem.getViewImpressions()));
                        long viewImpressionDiff = voBuilder.getViewImpressions() - Long.parseLong(voBuilder.getCompareViewImpressions());
                        voBuilder.setCompareViewImpressionsRate(Integer.parseInt(voBuilder.getCompareViewImpressions()) == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions()),
                                        2, RoundingMode.HALF_UP).toString());
                        //Vcpm环比值
                        voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                            BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                            voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toPlainString());
                        }else{
                            voBuilder.setCompareVcpmRate("-");
                        }
                        //品牌新买家订单量环比值
                        voBuilder.setCompareOrdersNewToBrandFTD(compareItem.getOrdersNewToBrandFTD() != null ? compareItem.getOrdersNewToBrandFTD() : 0);
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD() - voBuilder.getCompareOrdersNewToBrandFTD();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家订单百分比环比值
                        voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ? compareItem.getOrderRateNewToBrandFTD() : "0");
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销售额环比值
                        voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ? compareItem.getSalesNewToBrandFTD() : "0");
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销售额百分比环比值
                        voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ? compareItem.getSalesRateNewToBrandFTD() : "0");
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销量环比值
                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(compareItem.getUnitsOrderedNewToBrandFTD() != null ? compareItem.getUnitsOrderedNewToBrandFTD() : 0);
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD() - voBuilder.getCompareUnitsOrderedNewToBrandFTD();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销量百分比环比值
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ? compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        BigDecimal unitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                unitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                    }
                }

                Optional.ofNullable(item.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setCostType);
                Optional.ofNullable(item.getGoal()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setGoal);
                Optional.ofNullable(item.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setAdFormat);

                return voBuilder.build();
            }).collect(Collectors.toList());
            keywordVoPage.addAllRows(vos);
        }
        keywordVo.setPage(keywordVoPage.build());
        return keywordVo.build();
    }

    @Override
    public KeywordViewAggregateResponse.AggregateVo getAllKeywordViewAggregate(Integer puid, KeywordViewParam param) {
        KeywordViewAggregateResponse.AggregateVo.Builder aggregateVo = KeywordViewAggregateResponse.AggregateVo.newBuilder();
        //总汇总初始化
        aggregateVo.setAggregateVo(this.buildKeywordAggregateViewVo(null));

        //获取汇总数据
        KeywordViewAggregatePageVo keywordViewAggregatePageVo = keywordViewService.getAllKeywordViewAggregatePageVo(puid, param);
        aggregateVo.setAggregateVo(this.buildKeywordAggregateViewVo(keywordViewAggregatePageVo.getAggregateVo()));
        KeywordViewAggregateResponse.AggregateVo.Page.Builder page = KeywordViewAggregateResponse.AggregateVo.Page.newBuilder();
        Page<KeywordViewAggregateVo> voPage = keywordViewAggregatePageVo.getPage();
        if (voPage != null && CollectionUtils.isNotEmpty(voPage.getRows())) {
            List<KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo> keywordAggregateVoList = new ArrayList<>();
            KeywordViewAggregateResponse.AggregateVo.KeywordAggregateVo keywordAggregateVo;
            for (KeywordViewAggregateVo vo : voPage.getRows()) {
                keywordAggregateVo = this.buildKeywordAggregateViewVo(vo);
                keywordAggregateVoList.add(keywordAggregateVo);
            }
            page.setTotalPage(voPage.getTotalPage());
            page.setTotalSize(voPage.getTotalSize());
            page.addAllRow(keywordAggregateVoList);
        }

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        aggregateVo.setPage(page.build());
        return aggregateVo.build();
    }

    @Override
    public CategoryTargetViewResponse.TargetVo getAllCategoryTargetView(Integer puid, TargetViewParam param) {
        CategoryTargetViewResponse.TargetVo.Builder targetVo = CategoryTargetViewResponse.TargetVo.newBuilder();
        CategoryTargetViewResponse.TargetVo.Page.Builder targetVoPage = CategoryTargetViewResponse.TargetVo.Page.newBuilder();
        //总汇总初始化
        targetVoPage.setPageNo(param.getPageNo());
        targetVoPage.setPageSize(param.getPageSize());

        //获取列表页数据
        Page<CategoryTargetViewVo> voPage = categoryTargetViewService.getAllTargetViewPageVoList(puid, param);
        targetVoPage.setTotalPage(voPage.getTotalPage());
        targetVoPage.setTotalSize(voPage.getTotalSize());
        List<CategoryTargetViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> targetIdList = rows.stream().map(CategoryTargetViewVo::getTargetId).collect(Collectors.toList());
                compareKeywordMap = keywordViewService.getAllStreamDataByCompare(param, targetIdList);
            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List<CategoryTargetViewResponse.TargetVo.Page.TargetingPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                CategoryTargetViewResponse.TargetVo.Page.TargetingPageVo.Builder voBuilder = CategoryTargetViewResponse.TargetVo.Page.TargetingPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getTargetType())) {
                    voBuilder.setTargetType(item.getTargetType());
                }
                if (StringUtils.isNotBlank(item.getTitle())) {
                    voBuilder.setTitle(item.getTitle());
                }
                if (StringUtils.isNotBlank(item.getBrandName())) {
                    voBuilder.setBrandName(item.getBrandName());
                }
                if (StringUtils.isNotBlank(item.getCommodityPriceRange())) {
                    voBuilder.setCommodityPriceRange(item.getCommodityPriceRange());
                }
                if (StringUtils.isNotBlank(item.getRating())) {
                    voBuilder.setRating(item.getRating());
                }
                if (StringUtils.isNotBlank(item.getDistribution())) {
                    voBuilder.setDistribution(item.getDistribution());
                }
                if (StringUtils.isNotBlank(item.getSelectType())) {
                    voBuilder.setSelectType(item.getSelectType());
                }
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    voBuilder.setTargetId(item.getTargetId());
                }
                if (StringUtils.isNotBlank(item.getTargetText())) {
                    voBuilder.setTargetText(item.getTargetText());
                }
                if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                    voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                } else {
                    voBuilder.setTopImpressionShare("-");
                }
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    List<AdTagVo> adTagVos = new ArrayList<>();
                    AdTagVo.Builder adTagVo;
                    for (AdTag adTag : item.getAdTags()) {
                        adTagVo = AdTagVo.newBuilder();
                        adTagVo.setId(Int64Value.of(adTag.getId()));
                        adTagVo.setName(adTag.getName());
                        adTagVo.setColor(adTag.getColor());
                        adTagVos.add(adTagVo.build());
                    }
                    voBuilder.addAllAdTags(adTagVos);
                }
                if (item.getIsPricing() != null) {
                    voBuilder.setIsPricing(item.getIsPricing());
                }
                if (item.getPricingState() != null) {
                    voBuilder.setPricingState(item.getPricingState());
                }
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getDailyBudget())) {
                    voBuilder.setDailyBudget(item.getDailyBudget());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }
                if (StringUtils.isNotBlank(item.getBid())) {
                    voBuilder.setBid(item.getBid());
                }
                if (StringUtils.isNotBlank(item.getSuggestBid())) {
                    voBuilder.setSuggestBid(item.getSuggestBid());
                }
                if (StringUtils.isNotBlank(item.getRangeStart())) {
                    voBuilder.setRangeStart(item.getRangeStart());
                }
                if (StringUtils.isNotBlank(item.getRangeEnd())) {
                    voBuilder.setRangeEnd(item.getRangeEnd());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }

                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                //sb、sd才有
                voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0L));
                if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                    voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                }else{
                    voBuilder.setVcpm("-");
                }
                voBuilder.setOrdersNewToBrandFTD(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0));
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                voBuilder.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0));
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
                    if (finalCompareKeywordMap.containsKey(item.getTargetId())) {
                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getTargetId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                        //可见展示环比值
                        voBuilder.setCompareViewImpressions(compareItem.getViewImpressions() == null ? "0" : String.valueOf(compareItem.getViewImpressions()));
                        long viewImpressionDiff = voBuilder.getViewImpressions() - Long.parseLong(voBuilder.getCompareViewImpressions());
                        voBuilder.setCompareViewImpressionsRate(Integer.parseInt(voBuilder.getCompareViewImpressions()) == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions()),
                                        2, RoundingMode.HALF_UP).toString());
                        //Vcpm环比值
                        voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                            BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                            voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toPlainString());
                        }else{
                            voBuilder.setCompareVcpmRate("-");
                        }
                        //品牌新买家订单量环比值
                        voBuilder.setCompareOrdersNewToBrandFTD(compareItem.getOrdersNewToBrandFTD() != null ? compareItem.getOrdersNewToBrandFTD() : 0);
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD() - voBuilder.getCompareOrdersNewToBrandFTD();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家订单百分比环比值
                        voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ? compareItem.getOrderRateNewToBrandFTD() : "0");
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销售额环比值
                        voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ? compareItem.getSalesNewToBrandFTD() : "0");
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销售额百分比环比值
                        voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ? compareItem.getSalesRateNewToBrandFTD() : "0");
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销量环比值
                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(compareItem.getUnitsOrderedNewToBrandFTD() != null ? compareItem.getUnitsOrderedNewToBrandFTD() : 0);
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD() - voBuilder.getCompareUnitsOrderedNewToBrandFTD();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销量百分比环比值
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ? compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        BigDecimal unitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                unitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                    }
                }

                Optional.ofNullable(item.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setCostType);
                Optional.ofNullable(item.getGoal()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setGoal);
                Optional.ofNullable(item.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setAdFormat);

                return voBuilder.build();
            }).collect(Collectors.toList());
            targetVoPage.addAllRows(vos);
        }
        targetVo.setPage(targetVoPage.build());
        return targetVo.build();
    }

    @Override
    public CategoryTargetViewAggregateResponse.AggregateVo getAllCategoryTargetViewAggregate(Integer puid, TargetViewParam param) {
        CategoryTargetViewAggregateResponse.AggregateVo.Builder aggregateVo = CategoryTargetViewAggregateResponse.AggregateVo.newBuilder();
        //总汇总初始化
        aggregateVo.setAggregateVo(this.buildTargetAggregateViewVo(null));

        //获取汇总数据
        CategoryTargetViewAggregatePageVo aggregatePageVo = categoryTargetViewService.getAllCategoryTargetViewAggregatePageVo(puid, param);
        aggregateVo.setAggregateVo(this.buildTargetAggregateViewVo(aggregatePageVo.getAggregateVo()));
        CategoryTargetViewAggregateResponse.AggregateVo.Page.Builder page = CategoryTargetViewAggregateResponse.AggregateVo.Page.newBuilder();
        Page<CategoryTargetViewAggregateVo> voPage = aggregatePageVo.getPage();
        if (voPage != null && CollectionUtils.isNotEmpty(voPage.getRows())) {
            List<CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo> targetAggregateVoList = new ArrayList<>();
            CategoryTargetViewAggregateResponse.AggregateVo.TargetAggregateVo targetAggregateVo;
            for (CategoryTargetViewAggregateVo vo : voPage.getRows()) {
                targetAggregateVo = this.buildTargetAggregateViewVo(vo);
                targetAggregateVoList.add(targetAggregateVo);
            }
            page.setTotalPage(voPage.getTotalPage());
            page.setTotalSize(voPage.getTotalSize());
            page.addAllRows(targetAggregateVoList);
        }

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        aggregateVo.setPage(page.build());
        return aggregateVo.build();
    }

    @Override
    public AudienceTargetViewResponse.TargetVo getAllAudienceTargetView(Integer puid, AudienceTargetViewParam param) {
        AudienceTargetViewResponse.TargetVo.Builder targetVo = AudienceTargetViewResponse.TargetVo.newBuilder();
        AudienceTargetViewResponse.TargetVo.Page.Builder targetVoPage = AudienceTargetViewResponse.TargetVo.Page.newBuilder();
        //总汇总初始化
        targetVoPage.setPageNo(param.getPageNo());
        targetVoPage.setPageSize(param.getPageSize());

        //获取列表页数据
        Page<AudienceTargetViewVo> voPage = audienceTargetViewService.getAllAudienceTargetViewPageVoList(puid, param);
        targetVoPage.setTotalPage(voPage.getTotalPage());
        targetVoPage.setTotalSize(voPage.getTotalSize());
        List<AudienceTargetViewVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询环比指标
            Map<String, StreamDataViewVo> compareKeywordMap = null;

            if (param.getIsCompare() && StringUtils.isNotBlank(param.getCompareStartDate()) && StringUtils.isNotBlank(param.getCompareEndDate())) {
                //对比时无须高级搜索条件
                param.setUseAdvanced(false);

                List<String> targetIdList = rows.stream().map(AudienceTargetViewVo::getTargetId).collect(Collectors.toList());
                compareKeywordMap = keywordViewService.getAllStreamDataByCompare(param, targetIdList);
            }

            Map<String, StreamDataViewVo> finalCompareKeywordMap = compareKeywordMap;
            List<AudienceTargetViewResponse.TargetVo.Page.TargetingPageVo> vos = rows.stream().filter(Objects::nonNull).map(item -> {
                AudienceTargetViewResponse.TargetVo.Page.TargetingPageVo.Builder voBuilder = AudienceTargetViewResponse.TargetVo.Page.TargetingPageVo.newBuilder();
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                if (item.getId() != null) {
                    voBuilder.setId(item.getId());
                }
                if (item.getShopId() != null) {
                    voBuilder.setShopId(item.getShopId());
                }
                if (StringUtils.isNotBlank(item.getMarketplaceId())) {
                    voBuilder.setMarketplaceId(item.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getTargetType())) {
                    voBuilder.setTargetType(item.getTargetType());
                }
                if (StringUtils.isNotBlank(item.getTitle())) {
                    voBuilder.setTitle(item.getTitle());
                }
                if (StringUtils.isNotBlank(item.getBrandName())) {
                    voBuilder.setBrandName(item.getBrandName());
                }
                if (StringUtils.isNotBlank(item.getCommodityPriceRange())) {
                    voBuilder.setCommodityPriceRange(item.getCommodityPriceRange());
                }
                if (StringUtils.isNotBlank(item.getRating())) {
                    voBuilder.setRating(item.getRating());
                }
                if (StringUtils.isNotBlank(item.getDistribution())) {
                    voBuilder.setDistribution(item.getDistribution());
                }
                if (StringUtils.isNotBlank(item.getLookback())) {
                    voBuilder.setLookback(item.getLookback());
                }
                if (StringUtils.isNotBlank(item.getTargetId())) {
                    voBuilder.setTargetId(item.getTargetId());
                }
                if (StringUtils.isNotBlank(item.getTargetText())) {
                    voBuilder.setTargetText(item.getTargetText());
                }
                if (StringUtils.isNotBlank(item.getTopImpressionShare())) {
                    voBuilder.setTopImpressionShare(item.getTopImpressionShare());
                } else {
                    voBuilder.setTopImpressionShare("-");
                }
                if (CollectionUtils.isNotEmpty(item.getAdTags())) {
                    List<AdTagVo> adTagVos = new ArrayList<>();
                    AdTagVo.Builder adTagVo;
                    for (AdTag adTag : item.getAdTags()) {
                        adTagVo = AdTagVo.newBuilder();
                        adTagVo.setId(Int64Value.of(adTag.getId()));
                        adTagVo.setName(adTag.getName());
                        adTagVo.setColor(adTag.getColor());
                        adTagVos.add(adTagVo.build());
                    }
                    voBuilder.addAllAdTags(adTagVos);
                }
                if (item.getIsPricing() != null) {
                    voBuilder.setIsPricing(item.getIsPricing());
                }
                if (item.getPricingState() != null) {
                    voBuilder.setPricingState(item.getPricingState());
                }
                if (StringUtils.isNotBlank(item.getServingStatus())) {
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if (StringUtils.isNotBlank(item.getServingStatusDec())) {
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if (StringUtils.isNotBlank(item.getServingStatusName())) {
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getDailyBudget())) {
                    voBuilder.setDailyBudget(item.getDailyBudget());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }
                if (StringUtils.isNotBlank(item.getBid())) {
                    voBuilder.setBid(item.getBid());
                }
                if (StringUtils.isNotBlank(item.getSuggestBid())) {
                    voBuilder.setSuggestBid(item.getSuggestBid());
                }
                if (StringUtils.isNotBlank(item.getRangeStart())) {
                    voBuilder.setRangeStart(item.getRangeStart());
                }
                if (StringUtils.isNotBlank(item.getRangeEnd())) {
                    voBuilder.setRangeEnd(item.getRangeEnd());
                }
                if (item.getIsStateBidding() != null) {
                    voBuilder.setIsAdGroupBidding(item.getIsStateBidding());
                }
                if (item.getPricingStateBidding() != null) {
                    voBuilder.setPricingAdGroupBidding(item.getPricingStateBidding());
                }
                if (item.getBidLog() != null) {
                    DataLogVo budgetLog = item.getBidLog();
                    DataLog.Builder dataLog = DataLog.newBuilder();
                    dataLog.setCount(budgetLog.getCount());
                    dataLog.setPreviousValue(budgetLog.getPreviousValue());
                    dataLog.setNewValue(budgetLog.getNewValue());
                    dataLog.setSiteOperationTime(budgetLog.getSiteOperationTime());
                    voBuilder.setBidLog(dataLog.build());
                }
                if (item.getIsUpdateBid() != null) {
                    voBuilder.setIsUpdateBid(item.getIsUpdateBid());
                }
                Optional.ofNullable(item.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(voBuilder::setCostType);

                //指标参数
                voBuilder.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0L));
                voBuilder.setClicks(Optional.ofNullable(item.getClicks()).orElse(0L));
                voBuilder.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
                voBuilder.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                //广告位新增cpa字段
                voBuilder.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
                //本广告产品订单量
                voBuilder.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
                //本广告产品销售额
                voBuilder.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
                //其他产品广告销售额
                voBuilder.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
                //广告销量
                voBuilder.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
                // 花费占比
                voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
                // 销售额占比
                voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
                // 订单量占比
                voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
                // 销量占比
                voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
                // 广告笔单价
                voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                //sb、sd才有
                voBuilder.setViewImpressions(Optional.ofNullable(item.getViewImpressions()).orElse(0L));
                if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                    voBuilder.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0.00");
                }else{
                    voBuilder.setVcpm("-");
                }
                voBuilder.setOrdersNewToBrandFTD(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0));
                voBuilder.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
                voBuilder.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
                voBuilder.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
                voBuilder.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0));
                voBuilder.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");

                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareKeywordMap)) {
                    if (finalCompareKeywordMap.containsKey(item.getTargetId())) {
                        StreamDataViewVo compareItem = finalCompareKeywordMap.get(item.getTargetId());

                        voBuilder.setCompareImpressions(Optional.ofNullable(compareItem.getImpressions()).orElse(0L));
                        //曝光环比值
                        long impressionDiff = voBuilder.getImpressions() - voBuilder.getCompareImpressions();
                        voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions() == 0 ? "-" :
                                new BigDecimal(impressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareImpressions()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareClicks(Optional.ofNullable(compareItem.getClicks()).orElse(0L));
                        //点击量环比值
                        long clicksDiff = voBuilder.getClicks() - voBuilder.getCompareClicks();
                        voBuilder.setCompareClicksRate(voBuilder.getCompareClicks() == 0 ? "-" :
                                new BigDecimal(clicksDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareClicks()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCtr(StringUtils.isNotBlank(compareItem.getCtr()) ? compareItem.getCtr() : "0");
                        //ctr环比值
                        BigDecimal ctrDiff = new BigDecimal(voBuilder.getCtr()).subtract(new BigDecimal(voBuilder.getCompareCtr()));
                        voBuilder.setCompareCtrRate(new BigDecimal(voBuilder.getCompareCtr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                ctrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCtr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCvr(StringUtils.isNotBlank(compareItem.getCvr()) ? compareItem.getCvr() : "0");
                        //cvr环比值
                        BigDecimal cvrDiff = new BigDecimal(voBuilder.getCvr()).subtract(new BigDecimal(voBuilder.getCompareCvr()));
                        voBuilder.setCompareCvrRate(new BigDecimal(voBuilder.getCompareCvr()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cvrDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCvr()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcos(StringUtils.isNotBlank(compareItem.getAcos()) ? compareItem.getAcos() : "0");
                        //Acos环比值
                        BigDecimal acosDiff = new BigDecimal(voBuilder.getAcos()).subtract(new BigDecimal(voBuilder.getCompareAcos()));
                        voBuilder.setCompareAcosRate(new BigDecimal(voBuilder.getCompareAcos()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acosDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcos()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAcots(StringUtils.isNotBlank(compareItem.getAcots()) ? compareItem.getAcots() : "0");
                        //Acots环比值
                        BigDecimal acotsDiff = new BigDecimal(voBuilder.getAcots()).subtract(new BigDecimal(voBuilder.getCompareAcots()));
                        voBuilder.setCompareAcotsRate(new BigDecimal(voBuilder.getCompareAcots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                acotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAcots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAsots(StringUtils.isNotBlank(compareItem.getAsots()) ? compareItem.getAsots() : "0");
                        //Asots环比值
                        BigDecimal asotsDiff = new BigDecimal(voBuilder.getAsots()).subtract(new BigDecimal(voBuilder.getCompareAsots()));
                        voBuilder.setCompareAsotsRate(new BigDecimal(voBuilder.getCompareAsots()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                asotsDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAsots()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOrderNum(Optional.ofNullable(compareItem.getAdOrderNum()).orElse(0));
                        //AdOrderNum环比值
                        int adOrderNumDiff = voBuilder.getAdOrderNum() - voBuilder.getCompareAdOrderNum();
                        voBuilder.setCompareAdOrderNumRate(voBuilder.getCompareAdOrderNum() == 0 ? "-" :
                                new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCost(StringUtils.isNotBlank(compareItem.getAdCost()) ? compareItem.getAdCost() : "0");
                        //AdCost环比值
                        BigDecimal adCostDiff = new BigDecimal(voBuilder.getAdCost()).subtract(new BigDecimal(voBuilder.getCompareAdCost()));
                        voBuilder.setCompareAdCostRate(new BigDecimal(voBuilder.getCompareAdCost()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCost()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdCostPerClick(StringUtils.isNotBlank(compareItem.getAdCostPerClick()) ? compareItem.getAdCostPerClick() : "0");
                        //AdCostPerClick环比值
                        BigDecimal adCostPerClickDiff = new BigDecimal(voBuilder.getAdCostPerClick()).subtract(new BigDecimal(voBuilder.getCompareAdCostPerClick()));
                        voBuilder.setCompareAdCostPerClickRate(new BigDecimal(voBuilder.getCompareAdCostPerClick()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adCostPerClickDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPerClick()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSale(StringUtils.isNotBlank(compareItem.getAdSale()) ? compareItem.getAdSale() : "0");
                        //AdSale环比值
                        BigDecimal adSaleDiff = new BigDecimal(voBuilder.getAdSale()).subtract(new BigDecimal(voBuilder.getCompareAdSale()));
                        voBuilder.setCompareAdSaleRate(new BigDecimal(voBuilder.getCompareAdSale()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSaleDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSale()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareRoas(StringUtils.isNotBlank(compareItem.getRoas()) ? compareItem.getRoas() : "0");
                        //Roas环比值
                        BigDecimal roasDiff = new BigDecimal(voBuilder.getRoas()).subtract(new BigDecimal(voBuilder.getCompareRoas()));
                        voBuilder.setCompareRoasRate(new BigDecimal(voBuilder.getCompareRoas()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                roasDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareRoas()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSaleNum(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0));
                        //AdSaleNum比值
                        int adSaleNumDiff = voBuilder.getAdSaleNum() - voBuilder.getCompareAdSaleNum();
                        voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum() == 0 ? "-" :
                                new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherOrderNum(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0));
                        //AdOtherOrderNum比值
                        int adOtherOrderNumDiff = voBuilder.getAdOtherOrderNum() - voBuilder.getCompareAdOtherOrderNum();
                        voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum() == 0 ? "-" :
                                new BigDecimal(adOtherOrderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSales(StringUtils.isNotBlank(compareItem.getAdSales()) ? compareItem.getAdSales() : "0");
                        //AdSales环比值
                        BigDecimal adSalesDiff = new BigDecimal(voBuilder.getAdSales()).subtract(new BigDecimal(voBuilder.getCompareAdSales()));
                        voBuilder.setCompareAdSalesRate(new BigDecimal(voBuilder.getCompareAdSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSales()), 2, RoundingMode.HALF_UP).toString());


                        voBuilder.setCompareAdOtherSales(StringUtils.isNotBlank(compareItem.getAdOtherSales()) ? compareItem.getAdOtherSales() : "0");
                        //AdOtherSales环比值
                        BigDecimal adOtherSalesDiff = new BigDecimal(voBuilder.getAdOtherSales()).subtract(new BigDecimal(voBuilder.getCompareAdOtherSales()));
                        voBuilder.setCompareAdOtherSalesRate(new BigDecimal(voBuilder.getCompareAdOtherSales()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                adOtherSalesDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSales()), 2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareOrderNum(Optional.ofNullable(compareItem.getOrderNum()).orElse(0));
                        //OrderNum比值
                        int orderNumDiff = voBuilder.getOrderNum() - voBuilder.getCompareOrderNum();
                        voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum() == 0 ? "-" :
                                new BigDecimal(orderNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdSelfSaleNum(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0));
                        //AdSelfSaleNum比值
                        int adSelfSaleNumDiff = voBuilder.getAdSelfSaleNum() - voBuilder.getCompareAdSelfSaleNum();
                        voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum() == 0 ? "-" :
                                new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdSelfSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareAdOtherSaleNum(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0));
                        //AdOtherSaleNum比值
                        int adOtherSaleNumDiff = voBuilder.getAdOtherSaleNum() - voBuilder.getCompareAdOtherSaleNum();
                        voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum() == 0 ? "-" :
                                new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOtherSaleNum()),
                                        2, RoundingMode.HALF_UP).toString());

                        voBuilder.setCompareCpa(StringUtils.isNotBlank(compareItem.getCpa()) ? compareItem.getCpa() : "0");
                        //Cpa环比值
                        BigDecimal cpaDiff = new BigDecimal(voBuilder.getCpa()).subtract(new BigDecimal(voBuilder.getCompareCpa()));
                        voBuilder.setCompareCpaRate(new BigDecimal(voBuilder.getCompareCpa()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                cpaDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareCpa()), 2, RoundingMode.HALF_UP).toString());
                        // AdvertisingUnitPrice 环比
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                        voBuilder.setCompareAdvertisingUnitPriceRate(compareItem.getAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(item.getAdvertisingUnitPrice(), compareItem.getAdvertisingUnitPrice(), 2), 100)));
                        //可见展示环比值
                        voBuilder.setCompareViewImpressions(compareItem.getViewImpressions() == null ? "0" : String.valueOf(compareItem.getViewImpressions()));
                        long viewImpressionDiff = voBuilder.getViewImpressions() - Long.parseLong(voBuilder.getCompareViewImpressions());
                        voBuilder.setCompareViewImpressionsRate(Integer.parseInt(voBuilder.getCompareViewImpressions()) == 0 ? "-" :
                                new BigDecimal(viewImpressionDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareViewImpressions()),
                                        2, RoundingMode.HALF_UP).toString());
                        //Vcpm环比值
                        voBuilder.setCompareVcpm(StringUtils.isNotBlank(compareItem.getVcpm()) ? compareItem.getVcpm() : "0");
                        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
                            BigDecimal vcpmDiff = new BigDecimal(voBuilder.getVcpm()).subtract(new BigDecimal(voBuilder.getCompareVcpm()));
                            voBuilder.setCompareVcpmRate(new BigDecimal(voBuilder.getCompareVcpm()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                    vcpmDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareVcpm()), 2, RoundingMode.HALF_UP).toPlainString());
                        } else{
                            voBuilder.setCompareVcpmRate("-");
                        }
                        //品牌新买家订单量环比值
                        voBuilder.setCompareOrdersNewToBrandFTD(compareItem.getOrdersNewToBrandFTD() != null ? compareItem.getOrdersNewToBrandFTD() : 0);
                        int ordersNewToBrandFTDDiff = voBuilder.getOrdersNewToBrandFTD() - voBuilder.getCompareOrdersNewToBrandFTD();
                        voBuilder.setCompareOrdersNewToBrandFTDRate(voBuilder.getCompareOrdersNewToBrandFTD() == 0 ? "-" :
                                new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrdersNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家订单百分比环比值
                        voBuilder.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getOrderRateNewToBrandFTD()) ? compareItem.getOrderRateNewToBrandFTD() : "0");
                        BigDecimal orderRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getOrderRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()));
                        voBuilder.setCompareOrderRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销售额环比值
                        voBuilder.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesNewToBrandFTD()) ? compareItem.getSalesNewToBrandFTD() : "0");
                        BigDecimal salesNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()));
                        voBuilder.setCompareSalesNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销售额百分比环比值
                        voBuilder.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getSalesRateNewToBrandFTD()) ? compareItem.getSalesRateNewToBrandFTD() : "0");
                        BigDecimal salesRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getSalesRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()));
                        voBuilder.setCompareSalesRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareSalesRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销量环比值
                        voBuilder.setCompareUnitsOrderedNewToBrandFTD(compareItem.getUnitsOrderedNewToBrandFTD() != null ? compareItem.getUnitsOrderedNewToBrandFTD() : 0);
                        int unitsOrderedNewToBrandFTDDiff = voBuilder.getUnitsOrderedNewToBrandFTD() - voBuilder.getCompareUnitsOrderedNewToBrandFTD();
                        voBuilder.setCompareUnitsOrderedNewToBrandFTDRate(voBuilder.getCompareUnitsOrderedNewToBrandFTD() == 0 ? "-" :
                                new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                        //品牌新买家销量百分比环比值
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareItem.getUnitsOrderedRateNewToBrandFTD()) ? compareItem.getUnitsOrderedRateNewToBrandFTD() : "0");
                        BigDecimal unitsOrderedRateNewToBrandFTDDiff = new BigDecimal(voBuilder.getUnitsOrderedRateNewToBrandFTD()).subtract(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()));
                        voBuilder.setCompareUnitsOrderedRateNewToBrandFTDRate(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                                unitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareUnitsOrderedRateNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
                    }
                }
                return voBuilder.build();
            }).collect(Collectors.toList());
            targetVoPage.addAllRows(vos);
        }
        targetVo.setPage(targetVoPage.build());
        return targetVo.build();
    }

    @Override
    public AudienceTargetViewAggregateResponse.AggregateVo getAllAudienceTargetViewAggregate(Integer puid, AudienceTargetViewParam param) {
        AudienceTargetViewAggregateResponse.AggregateVo.Builder aggregateVo = AudienceTargetViewAggregateResponse.AggregateVo.newBuilder();
        //总汇总初始化
        aggregateVo.setAggregateVo(this.buildAudienceTargetAggregateViewVo(null));

        //获取汇总数据
        AudienceTargetViewAggregatePageVo aggregatePageVo = audienceTargetViewService.getAllAudienceTargetViewAggregatePageVo(puid, param);
        aggregateVo.setAggregateVo(this.buildAudienceTargetAggregateViewVo(aggregatePageVo.getAggregateVo()));
        AudienceTargetViewAggregateResponse.AggregateVo.Page.Builder page = AudienceTargetViewAggregateResponse.AggregateVo.Page.newBuilder();
        Page<AudienceTargetViewAggregateVo> voPage = aggregatePageVo.getPage();
        if (voPage != null && CollectionUtils.isNotEmpty(voPage.getRows())) {
            List<AudienceTargetViewAggregateResponse.AggregateVo.TargetAggregateVo> targetAggregateVoList = new ArrayList<>();
            AudienceTargetViewAggregateResponse.AggregateVo.TargetAggregateVo targetAggregateVo;
            for (AudienceTargetViewAggregateVo vo : voPage.getRows()) {
                targetAggregateVo = this.buildAudienceTargetAggregateViewVo(vo);
                targetAggregateVoList.add(targetAggregateVo);
            }
            page.setTotalPage(voPage.getTotalPage());
            page.setTotalSize(voPage.getTotalSize());
            page.addAllRows(targetAggregateVoList);
        }

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        aggregateVo.setPage(page.build());
        return aggregateVo.build();
    }
    
}
