package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.bo.AdKeywordOrderBo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbKeywordReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.wordFrequency.qo.KeywordTopQo;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;

import java.util.List;

/**
 * sb关键词报告(OdsAmazonAdSbKeywordReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
public interface IOdsAmazonAdSbKeywordReportDao extends IDorisBaseDao<OdsAmazonAdSbKeywordReport> {
    /**
     * 生成广告看板查询投放关键词数据的查询sql
     * @param puid
     * @param marketplaceIdList
     * @param shopIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @param argsList
     * @return
     */
    String buildQueryAdKeywordChartsSql(Integer puid,
                                        List<String> marketplaceIdList,
                                        List<Integer> shopIdList,
                                        String currency,
                                        String startDate,
                                        String endDate,
                                        List<Object> argsList, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);

    List<DashboardAdTargetingMatrixTopDto> getKeywordTopList(Integer puid, List<String> marketplaceIdList,
                                                             List<Integer> shopIdList, String currency,
                                                             String startDate, String endDate,
                                                             DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                             Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                             List<String> campaignIds, Boolean noZero);

    List<DashboardAdTargetingMatrixTopDto> getKeywordTopInfoList(Integer puid, List<String> marketplaceIdList,
                                                             List<Integer> shopIdList, String currency,
                                                             String startDate, String endDate,
                                                             DashboardDataFieldEnum dataField, List<String> keywordIdList,
                                                                 DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                 List<String> campaignIds, Boolean noZero);

    /**
     * 获取某词根下关键词列表页的所有keywordId;
     */
    List<String> getKeywordPageIdList(Integer puid, KeywordsPageParam param);

    /**
     * 关键词投放高级筛选个数
     */
    int getKeywordAllCount(Integer puid, KeywordsPageParam param);

    /**
     * 关键词投放列表页
     */
    Page<KeywordPageVo> getKeywordPage(Integer puid, KeywordsPageParam param);

    /**
     * 关键词投放汇总查询所有keywordId
     */
    List<String> getKeywordIdListByPage(Integer puid, String startStr, String endStr, KeywordsPageParam param);

    /**
     * 获取关键词投放页面词根top
     */
    List<WordRootTopVo> getWordRootTopList(Integer puid, KeywordTopQo qo);

    /**
     * 根据keywordIds查询时间内的报告汇总数据
     */
    List<KeywordPageVo> getReportListByKeywordIds(Integer puid, Integer shopId, List<String> keywordIds, String startStr, String endStr);

    /**
     * 关键词投放列表页占比数据
     */
    AdMetricDto getKeywordPageSumMetricData(Integer puid, KeywordsPageParam param);

    /**
     * 关键词投放汇总根据keywordId查询按天维度数据
     */
    List<AdHomePerformancedto> getReportAggregateByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr,
                                                                 KeywordsPageParam param, boolean isGroupByDate);

    AdHomePerformancedto getReportAggregateCompareDataByKeywordIdList(Integer puid, KeywordsPageParam param, String compareStartData, String compareEndData);

    /**
     * 关键词库列表页报告数据-SB
     *
     * @param puid
     * @param shopIds
     * @param param
     * @param keywordTexts
     * @return
     */
    List<AdKeywordOrderBo> getSbKeywordTexts(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts);

    /**
     * 关键词库列表页-报告数据查询-SB
     *
     * @param puid
     * @param shopIds
     * @param param
     * @param keywordTexts
     * @return
     */
    List<KeywordLibsVo> getSbKeywordReportData(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts);

    List<KeywordLibsDetailVo> getSbKeywordReportData(Integer puid, KeywordLibsPageParam param, List<String> keywordIds);

    List<KeywordLibsVo> getSbCompKeywordReportData(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywords);

    /**
     * 查询sb关键词投放报告数据
     * 默认排序
     * @param puid
     * @param vo
     * @param sbKeywordIdList
     * @return
     */
    List<RepeatTargetingCountVo> getSbKeywordReportData(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> sbKeywordIdList);

    /**
     * 查询sb关键词投放环比报告数据
     * @param puid
     * @param vo
     * @param sbKeywordList
     * @return
     */
    List<RepeatTargetingCountVo> getSbCompKeywordReportData(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> sbKeywordList);

    /**
     * 查询sb关键词投放报告数据
     * 指标值排序
     *
     * @param puid
     * @param vo
     * @param
     * @return
     */
    Page<RepeatTargetingCountVo> getSbKeywordReportDataOrderByIndex(Integer puid, RepeatTargetingCountPageVo vo);

    /**
     * 查询sb关键词投放环比数据
     * 指标值排序
     *
     * @param puid
     * @param vo
     * @param sbKeywordIdList
     * @return
     */
    List<RepeatTargetingCountVo> getSbCompKeywordReportDataOrderByIndex(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> sbKeywordIdList);

    /**
     * 查询sb关键词投放报告数据
     *
     * @param puid
     * @param detailPageVo
     * @param keywordIds
     * @return
     */
    List<RepeatTargetingDetailVo> getSbReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds);

    /**
     * 查询sb关键词投放环比数据
     *
     * @param puid
     * @param detailPageVo
     * @param keywordIds
     * @return
     */
    List<RepeatTargetingDetailVo> getSbCompReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds);

    List<RepeatTargetingTotalVo> getRepeatTargetingReportTotalData(Integer puid, RepeatTargetingDetailPageVo totalDataVo, List<String> keywordIds);

    Page<RepeatTargetingDetailVo> getIndexReportData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds);

    List<OdsAmazonAdSbKeywordReport> getReportByKeywordIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startDate, String endDate,
                                                                           List<String> keywordIds, boolean changeRate, String currency);
}

