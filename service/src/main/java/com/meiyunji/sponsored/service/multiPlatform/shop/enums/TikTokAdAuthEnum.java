package com.meiyunji.sponsored.service.multiPlatform.shop.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/2/24 13:38
 * @describe:
 */
@Getter
public enum TikTokAdAuthEnum {
    NO_AD_AUTH(0, "未授权"),

    AD_AUTH(1, "已授权"),

    AD_EXIT_FAILURE(4, "存在失效")
    ;
    private Integer status;
    private String msg;

    TikTokAdAuthEnum(Integer status, String msg) {
        this.status = status;
        this.msg = msg;
    }
}
