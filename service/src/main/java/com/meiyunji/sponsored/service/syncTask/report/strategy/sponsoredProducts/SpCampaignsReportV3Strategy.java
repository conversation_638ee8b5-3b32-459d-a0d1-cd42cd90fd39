package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdShopReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdShopReport;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductCampaigns;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpCampaignsReportV3Strategy extends AbstractReportProcessStrategy {
    // TODO 删除多余dao
    private final IAmazonAdShopReportDao amazonAdShopReportDao;
    private final IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;
    public SpCampaignsReportV3Strategy(CosBucketClient dataBucketClient,
                                       IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao,
                                       IAmazonAdShopReportDao amazonAdShopReportDao,
                                       PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdCampaignAllReportDao = amazonAdCampaignAllReportDao;
        this.amazonAdShopReportDao = amazonAdShopReportDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3
                && notification.getV3Type() == AmazonReportV3Type.sp_campaigns;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath()))));JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductCampaigns> reports = Lists.newArrayListWithExpectedSize(batchSize);
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductCampaigns report = new SponsoredProductCampaigns();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                reports.add(report);
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= batchSize) {
                    dealReports(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports, shopType);
            }

            //汇总一次门店数据
            sumShopReport(notification);
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        //入库新表***
        List<AmazonAdCampaignAllReport> poAllList = getPoByAllReportCampaign(notification, reports, shopType);
        //分批入库
        List<List<AmazonAdCampaignAllReport>> partitionAll = Lists.partition(poAllList, batchSize);
        for (List<AmazonAdCampaignAllReport> campaignReports : partitionAll) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), campaignReports, 0, amazonAdCampaignAllReportDao::insertOrUpdateList);
            if (DateUtil.checkDateRange(notification.getV3EndDate(), 5L)) {
                amazonAdCampaignAllReportDao.insertDorisList(campaignReports);
            }
        }

    }

    private List<AmazonAdCampaignAllReport> getPoByAllReportCampaign(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        List<AmazonAdCampaignAllReport> list = Lists.newArrayListWithExpectedSize(reports.size());
        AmazonAdCampaignAllReport amazonAdCampaignReport;
        for (SponsoredProductCampaigns report : reports) {
            amazonAdCampaignReport = new AmazonAdCampaignAllReport();
            amazonAdCampaignReport.setPuid(notification.getSellerIdentifier());
            amazonAdCampaignReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdCampaignReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdCampaignReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            amazonAdCampaignReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdCampaignReport.setType(CampaignTypeEnum.sp.getCampaignType());
            amazonAdCampaignReport.setCampaignType(Constants.PLACEMENT_ALL); //All表示汇总信息
            amazonAdCampaignReport.setPlacement(Constants.PLACEMENT_ALL);
            amazonAdCampaignReport.setIsSummary(1);
            amazonAdCampaignReport.setCampaignName(report.getCampaignName());
            amazonAdCampaignReport.setCampaignStatus(report.getCampaignStatus().toLowerCase());
            amazonAdCampaignReport.setCampaignBudget(report.getCampaignBudgetAmount());
            //由于vc店铺都是取14天归因数，为了不改变查询代码，所以这里将入库字段进行对调
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                amazonAdCampaignReport.setConversions7d(report.getPurchases14d());
                amazonAdCampaignReport.setConversions14d(report.getPurchases7d());
                amazonAdCampaignReport.setConversions7dSameSKU(report.getPurchasesSameSku14d());
                amazonAdCampaignReport.setConversions14dSameSKU(report.getPurchasesSameSku7d());
                amazonAdCampaignReport.setSales7d(report.getSales14d() == null ? null : report.getSales14d());
                amazonAdCampaignReport.setSales14d(report.getSales7d() == null ? null : report.getSales7d());
                amazonAdCampaignReport.setSales7dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
                amazonAdCampaignReport.setSales14dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
                amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks14d());
                amazonAdCampaignReport.setUnitsOrdered14d(report.getUnitsSoldClicks7d());
                amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku14d());
                amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku7d());
            } else {
                amazonAdCampaignReport.setConversions7d(report.getPurchases7d());
                amazonAdCampaignReport.setConversions14d(report.getPurchases14d());
                amazonAdCampaignReport.setConversions7dSameSKU(report.getPurchasesSameSku7d());
                amazonAdCampaignReport.setConversions14dSameSKU(report.getPurchasesSameSku14d());
                amazonAdCampaignReport.setSales7d(report.getSales7d() == null ? null : report.getSales7d());
                amazonAdCampaignReport.setSales14d(report.getSales14d() == null ? null : report.getSales14d());
                amazonAdCampaignReport.setSales7dSameSKU(report.getAttributedSalesSameSku7d() == null ? null : report.getAttributedSalesSameSku7d());
                amazonAdCampaignReport.setSales14dSameSKU(report.getAttributedSalesSameSku14d() == null ? null : report.getAttributedSalesSameSku14d());
                amazonAdCampaignReport.setUnitsOrdered7d(report.getUnitsSoldClicks7d());
                amazonAdCampaignReport.setUnitsOrdered14d(report.getUnitsSoldClicks14d());
                amazonAdCampaignReport.setUnitsOrdered7dSameSKU(report.getUnitsSoldSameSku7d());
                amazonAdCampaignReport.setUnitsOrdered14dSameSKU(report.getUnitsSoldSameSku14d());
            }

            amazonAdCampaignReport.setConversions1d(report.getPurchases1d());
            amazonAdCampaignReport.setConversions1dSameSKU(report.getPurchasesSameSku1d());
            amazonAdCampaignReport.setConversions30d(report.getPurchases30d());
            amazonAdCampaignReport.setConversions30dSameSKU(report.getPurchasesSameSku30d());


            amazonAdCampaignReport.setSales1d(report.getSales1d() == null ? null : report.getSales1d());
            amazonAdCampaignReport.setSales1dSameSKU(report.getAttributedSalesSameSku1d() == null ? null : report.getAttributedSalesSameSku1d());
            amazonAdCampaignReport.setSales30d(report.getSales30d() == null ? null : report.getSales30d());
            amazonAdCampaignReport.setSales30dSameSKU(report.getAttributedSalesSameSku30d() == null ? null : report.getAttributedSalesSameSku30d());

            amazonAdCampaignReport.setUnitsOrdered1d(report.getUnitsSoldClicks1d());
            amazonAdCampaignReport.setUnitsOrdered1dSameSKU(report.getUnitsSoldSameSku1d());
            amazonAdCampaignReport.setUnitsOrdered30d(report.getUnitsSoldClicks30d());
            amazonAdCampaignReport.setUnitsOrdered30dSameSKU(report.getUnitsSoldSameSku30d());

            amazonAdCampaignReport.setClicks(report.getClicks());
            amazonAdCampaignReport.setCost(report.getCost());
            amazonAdCampaignReport.setImpressions(report.getImpressions());
            amazonAdCampaignReport.setTopOfSearchIs(report.getTopOfSearchImpressionShare());
            list.add(amazonAdCampaignReport);
        }
        return list;
    }



    private void sumShopReport(ReportReadyNotification notification) {
        LocalDate startDate = notification.getV3StartDate();
        LocalDate endDate = notification.getV3EndDate();
        // 取店铺下所有活动的汇总
        List<AmazonAdCampaignAllReport> sumReports =
                amazonAdCampaignAllReportDao.getSumDailyReportByDateRange(notification.getSellerIdentifier(),
                        notification.getMarketplaceIdentifier(), notification.getMarketplace().getId(),
                        startDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")), endDate.format(DateTimeFormatter.ofPattern("yyyyMMdd")), "sp");


        List<AmazonAdShopReport> shopReports = sumReports.stream().map(sumReport -> {
            AmazonAdShopReport shopReport = new AmazonAdShopReport();
            shopReport.setPuid(notification.getSellerIdentifier());
            shopReport.setShopId(notification.getMarketplaceIdentifier());
            shopReport.setMarketplaceId(notification.getMarketplace().getId());
            shopReport.setCountDate(sumReport.getCountDate());

            shopReport.setCost(sumReport.getCost());
            shopReport.setCostRmb(sumReport.getCostRmb());
            shopReport.setCostUsd(sumReport.getCostUsd());
            shopReport.setTotalSales(sumReport.getTotalSales());
            shopReport.setTotalSalesRmb(sumReport.getTotalSalesRmb());
            shopReport.setTotalSalesUsd(sumReport.getTotalSalesUsd());
            shopReport.setAdSales(sumReport.getAdSales());
            shopReport.setAdSalesRmb(sumReport.getAdSalesRmb());
            shopReport.setAdSalesUsd(sumReport.getAdSalesUsd());
            shopReport.setImpressions(sumReport.getImpressions());
            shopReport.setClicks(sumReport.getClicks());
            shopReport.setOrderNum(sumReport.getOrderNum());
            shopReport.setAdOrderNum(sumReport.getAdOrderNum());
            shopReport.setSaleNum(sumReport.getSaleNum());
            shopReport.setAdSaleNum(sumReport.getAdSaleNum());
            return shopReport;
        }).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shopReports)) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), shopReports,0, amazonAdShopReportDao::batchInsertOrUpdate);
        }

    }
}
