package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.sb.neKeyword.NeKeywordResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbNeKeywordService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;

import com.meiyunji.sponsored.service.enums.MarketplaceTimeZoneEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogTargetEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbNeKeyword;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/8/5.
 */

@Service
@Slf4j
public class CpcSbNeKeywordServiceImpl implements ICpcSbNeKeywordService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSbAdGroupDao groupDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao neKeywordDao;
    @Autowired
    private CpcSbNeKeywordApiService cpcSbNeKeywordApiService;
    @Autowired
    private ICpcSbGroupService cpcSbGroupService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IDorisService dorisService;

    @Override
    public Result<Page<NeKeywordsPageVo>> pageList(SbNeKeywordsPageParam param) {

        Page<AmazonSbAdNeKeyword> page = neKeywordDao.neKeywordsPageList(param.getPuid(), param);
        Page<NeKeywordsPageVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);

        if (CollectionUtils.isNotEmpty(page.getRows())) {
            List<NeKeywordsPageVo> list = new ArrayList<>(page.getRows().size());
            voPage.setRows(list);
            NeKeywordsPageVo vo;
            for (AmazonSbAdNeKeyword amazonAdKeyword : page.getRows()) {
                vo = new NeKeywordsPageVo();
                list.add(vo);
                vo.setId(amazonAdKeyword.getId());
                vo.setShopId(amazonAdKeyword.getShopId());
                vo.setState(amazonAdKeyword.getState());
                vo.setMatchType(amazonAdKeyword.getMatchType());
                vo.setKeywordText(amazonAdKeyword.getKeywordText());
                vo.setCreateTime(DateUtil.dateToStrWithTime(amazonAdKeyword.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
            }
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result<List<NeKeywordResponse.Data>> createNeKeywords(AddSbNeKeywordsVo addNeKeywordsVo) {
        AmazonSbAdGroup adGroup = groupDao.getByGroupId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getGroupId());
        if (adGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }
        for (NeKeywordsVo vo : addNeKeywordsVo.getNeKeywords()) {
            if (StringUtils.isBlank(vo.getKeywordText()) || vo.getMatchType() == null) {
                return ResultUtil.error("对象不存在");
            }
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(addNeKeywordsVo.getShopId(), addNeKeywordsVo.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId());
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        // 排除已存在的否定关键词
        Iterator<NeKeywordsVo> it = addNeKeywordsVo.getNeKeywords().iterator();
        NeKeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (neKeywordDao.exist(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), adGroup.getAdGroupId(), next.getKeywordText(), next.getMatchType())) {
                it.remove();
            }
        }

        if (addNeKeywordsVo.getNeKeywords().size() == 0) {
            return ResultUtil.success();
        }

        List<AmazonSbAdNeKeyword> amazonAdNeKeywords = convertAddNeKeywordsVoToPo(addNeKeywordsVo.getUid(), adGroup, addNeKeywordsVo.getNeKeywords());

        Result result = cpcSbNeKeywordApiService.createNeKeywords(shop, profile, amazonAdNeKeywords);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdNeKeyword> succList = amazonAdNeKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = neKeywordDao.listByKeywordId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), succList.stream()
                    .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }

            logSbNeKeywordsCreate(succList, addNeKeywordsVo.getIp(), result);

            // 入库
            try {
                neKeywordDao.batchAdd(addNeKeywordsVo.getPuid(), succList);
                List<String> keywordIds = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList());
                saveDoris(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), keywordIds);

            } catch (Exception e) {
                log.error("createSbNeKeyword:", e);
            }

            List<String> keywordIds = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            //创建成功, 需要在同步 获取否定关键词状态
            ThreadPoolUtil.getNegativeSyncPool().execute(()->{
                try {
                    cpcSbNeKeywordApiService.syncNeKeywords(shop, addNeKeywordsVo.getCampaignId(), keywordIds);
                    saveDoris(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), keywordIds);
                } catch (Exception e) {
                    log.info("添加成功后同步否定异常", e);
                }
            });
            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getGroupId());
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("keyword");
                    groupDao.updateByIdAndPuid(addNeKeywordsVo.getPuid(), sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }

            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
            }
        }

        List<AmazonSbAdNeKeyword> failList = amazonAdNeKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (failList.size() == 0) {
            return ResultUtil.success();
        }

        StringBuilder errMsg = new StringBuilder("创建失败的否定关键词原因：");
        List<NeKeywordResponse.Data> errorList = new ArrayList<>();

        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
            if (StringUtils.isNotBlank(e.getKeywordText())) {
                NeKeywordResponse.Data.Builder builder = NeKeywordResponse.Data.newBuilder();
                builder.setKeywordText(e.getKeywordText());
                builder.setMatchType(e.getMatchType());
                errorList.add(builder.build());
            }
        });

        if (CollectionUtils.isNotEmpty(errorList) && errorList.size() > 0 && succList.size() > 0) {
            result.setData(errorList.stream().distinct().collect(Collectors.toList()));
        } else {
            result.setCode(-1);
            result.setData(null);
        }
        result.setMsg(errMsg.toString());

        return result;
    }

    private void logSbNeKeywordsCreate(List<AmazonSbAdNeKeyword> keywordList, String ip, Result result) {
        try {
            if (CollectionUtils.isEmpty(keywordList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdNeKeyword keyword : keywordList) {
                AdManageOperationLog operationLog = adManageOperationLogService.getSbNeKeywordLog(null, keyword);
                operationLog.setIp(ip);
                if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    operationLog.setResultInfo(StringUtils.isNotBlank(keyword.getErrMsg()) ? "targetValue:" + keyword.getKeywordText() + ",desc:" + keyword.getErrMsg() : result.getMsg());
                }
                operationLogs.add(operationLog);
            }
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("sb否定关键词日志异常", e);
        }
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> createNeKeywords(AddSbNeKeywordsVo addNeKeywordsVo, ShopAuth shop, AmazonAdProfile profile) {
        AmazonSbAdGroup adGroup = groupDao.getByGroupId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getGroupId());
        if (adGroup == null) {
            throw new ServiceException(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg());
        }
        for (NeKeywordsVo vo : addNeKeywordsVo.getNeKeywords()) {
            if (StringUtils.isBlank(vo.getKeywordText()) || vo.getMatchType() == null) {
                throw new ServiceException(SBCreateErrorEnum.NEKEYWORD_INFO_ERROR.getMsg());
            }
        }

        // 排除已存在的否定关键词
        Iterator<NeKeywordsVo> it = addNeKeywordsVo.getNeKeywords().iterator();
        NeKeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (neKeywordDao.exist(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), adGroup.getAdGroupId(), next.getKeywordText(), next.getMatchType())) {
                it.remove();
            }
        }

        if (addNeKeywordsVo.getNeKeywords().isEmpty()) {
            NewCreateResultResultVo<SBCommonErrorVo> response = new NewCreateResultResultVo<>();
            response.setCampaignId(addNeKeywordsVo.getCampaignId());
            response.setAdGroupId(addNeKeywordsVo.getGroupId());
            return response;
        }

        List<AmazonSbAdNeKeyword> amazonAdNeKeywords = convertAddNeKeywordsVoToPo(addNeKeywordsVo.getUid(), adGroup, addNeKeywordsVo.getNeKeywords());

        Result result = cpcSbNeKeywordApiService.createNeKeywordsNew(shop, profile, amazonAdNeKeywords);
        if (!result.success()) {
            NewCreateResultResultVo<SBCommonErrorVo> response = new NewCreateResultResultVo<>();
            response.setCampaignId(addNeKeywordsVo.getCampaignId());
            response.setAdGroupId(addNeKeywordsVo.getGroupId());
            return response;
        }

        List<AmazonSbAdNeKeyword> succList = amazonAdNeKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (!succList.isEmpty()) {
            // 有可能已经添加过了
            List<String> existInDB = neKeywordDao.listByKeywordId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), succList.stream()
                            .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                neKeywordDao.batchAdd(addNeKeywordsVo.getPuid(), succList);

            } catch (Exception e) {
                log.error("createSbNeKeyword:", e);
                throw new ServiceException(e.getMessage());
            }

            List<String> keywordIds = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            //创建成功, 需要在同步 获取否定关键词状态
            ThreadPoolUtil.getNegativeSyncPool().execute(()->{
                try {
                    cpcSbNeKeywordApiService.syncNeKeywords(shop, addNeKeywordsVo.getCampaignId(), keywordIds);
                } catch (Exception e) {
                    log.info("添加成功后同步否定异常", e);
                    throw new ServiceException(e.getMessage());
                }
            });
            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getGroupId());
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("keyword");
                    groupDao.updateByIdAndPuid(addNeKeywordsVo.getPuid(), sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
                throw new ServiceException(e.getMessage());
            }
        }

        NewCreateResultResultVo<SBCommonErrorVo> response = new NewCreateResultResultVo<>();
        response.setCampaignId(addNeKeywordsVo.getCampaignId());
        response.setAdGroupId(addNeKeywordsVo.getGroupId());
        response.setNeKeywordIdList(amazonAdNeKeywords.stream().map(s -> {
            NewCreateNeTargetResultVo vo = new NewCreateNeTargetResultVo();
            Optional.ofNullable(s.getKeywordId()).ifPresent(vo::setNeTargetId);
            Optional.ofNullable(s.getKeywordText()).ifPresent(vo::setNeTargetText);
            Optional.ofNullable(s.getMatchType()).ifPresent(vo::setNeMatchType);
            return vo;
        }).collect(Collectors.toList()));

        List<AmazonSbAdNeKeyword> failList = amazonAdNeKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (failList.isEmpty()) {
            return response;
        }

        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (org.apache.commons.lang.StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getKeywordText(), e.getErrMsg()));
            }
        });
        response.setErrInfoList(errorList);
        return response;
    }

    @Override
    public Result createCampaignIdsNeKeywords(AddSbCampaignIdsNeKeywordsVo addNeKeywordsVo) {

        for (NeKeywordsVo vo : addNeKeywordsVo.getNeKeywords()) {
            if (StringUtils.isBlank(vo.getKeywordText()) || vo.getMatchType() == null) {
                return ResultUtil.error("对象不存在");
            }
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(addNeKeywordsVo.getShopId(), addNeKeywordsVo.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId());
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<String,List<Integer>> resultMap = new HashMap<>();
        Set<Integer> success = new HashSet<>();
        Set<Integer> fail = new HashSet<>();

        List<String> groupIds = addNeKeywordsVo.getNeKeywords().stream().map(CampaignIdNeKeywordsVo::getGroupId).distinct().collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getAdGroupByIds(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), groupIds);
        if (CollectionUtils.isEmpty(adGroups)) {
            return ResultUtil.returnErr("没有广告组信息");
        }
        Iterator<CampaignIdNeKeywordsVo> iterator = addNeKeywordsVo.getNeKeywords().iterator();
        while (iterator.hasNext()) {
            CampaignIdNeKeywordsVo e = iterator.next();
            if (org.apache.commons.lang.StringUtils.isBlank(e.getKeywordText()) || org.apache.commons.lang.StringUtils.isBlank(e.getMatchType())
                    || org.apache.commons.lang.StringUtils.isBlank(e.getCampaignId())) {

                fail.add(e.getIndex());
                iterator.remove();
            }
        }


        if (CollectionUtils.isEmpty(addNeKeywordsVo.getNeKeywords())) {
            return ResultUtil.returnErr("参数请求错误");
        }

        Map<String, AmazonSbAdGroup> sbAdGroupMap = adGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e, (e1, e2) -> e2));
        // 排除已存在的否定关键词
        Iterator<CampaignIdNeKeywordsVo> it = addNeKeywordsVo.getNeKeywords().iterator();
        CampaignIdNeKeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            //移除不存在广告组信息的数据；
            AmazonSbAdGroup amazonSbAdGroup = sbAdGroupMap.get(next.getGroupId());

            if(amazonSbAdGroup == null){
                fail.add(next.getIndex());
                it.remove();
                continue;
            }
            if (neKeywordDao.exist(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), amazonSbAdGroup.getAdGroupId(), next.getKeywordText(), next.getMatchType())) {
                success.add(next.getIndex());
                it.remove();
            }
        }

        if (addNeKeywordsVo.getNeKeywords().size() == 0) {
            resultMap.put("success",Lists.newArrayList(success));
            resultMap.put("fail",Lists.newArrayList(fail));
            return ResultUtil.success(resultMap);
        }

        List<AmazonSbAdNeKeyword> amazonAdNeKeywords = convertAddNeKeywordsVoToPo(addNeKeywordsVo.getUid(), sbAdGroupMap, addNeKeywordsVo.getNeKeywords());

        Result result = cpcSbNeKeywordApiService.createNeKeywords(shop, profile, amazonAdNeKeywords);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdNeKeyword> succList = amazonAdNeKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = neKeywordDao.listByKeywordId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), succList.stream()
                            .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdNeKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                neKeywordDao.batchAdd(addNeKeywordsVo.getPuid(), succList);
                List<String> keywordIds = succList.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList());
                saveDoris(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), keywordIds);
            } catch (Exception e) {
                log.error("createSbNeKeyword:", e);
            }

            List<String> keywordIds = new ArrayList<>();
            //创建成功, 需要在同步 获取否定关键词状态
            List<String> campaignIdList = succList.stream().peek(e -> {

                if (org.apache.commons.lang.StringUtils.isNotBlank(e.getKeywordId())) {
                    keywordIds.add(e.getKeywordId());
                }
            }).map(AmazonSbAdNeKeyword::getCampaignId).distinct().collect(Collectors.toList());
            ThreadPoolUtil.getNegativeSyncPool().execute(()->{
                try {
                    cpcSbNeKeywordApiService.syncNeKeywords(shop, StringUtil.joinString(campaignIdList,","), keywordIds);
                    saveDoris(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), keywordIds);
                } catch (Exception e) {
                    log.info("添加成功后同步否定异常", e);
                }
            });

        }

        List<AmazonSbAdNeKeyword> failList = amazonAdNeKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(succList)){
            Set<Integer> collect = succList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdNeKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                success.addAll(collect);
            }
        }
        if(CollectionUtils.isNotEmpty(failList)){
            Set<Integer> collect = failList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdNeKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                fail.addAll(collect);
            }
        }
        resultMap.put("success", Lists.newArrayList(success));
        resultMap.put("fail",Lists.newArrayList(fail));
        if (failList.size() == 0) {
            return ResultUtil.success(resultMap);
        }

        StringBuilder errMsg = new StringBuilder("创建失败的否定关键词原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });
        Result<Object> objectResult = ResultUtil.returnErr(errMsg.toString());
        objectResult.setData(resultMap);
        return objectResult;
    }



    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, Long id, String ip) {
        AmazonSbAdNeKeyword neKeyword = neKeywordDao.getByPuidAndId(puid, id);
        if(neKeyword == null){
            return ResultUtil.error("没有关键词信息");
        }

        String keywordId = neKeyword.getKeywordId();
        if (StringUtils.isBlank(keywordId)) {
            return ResultUtil.error("平台keyword id 为空, 请同步该活动在操作");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Result result = cpcSbNeKeywordApiService.archive(shop, profile, keywordId);
        AmazonSbAdNeKeyword oldNeKeyword = new AmazonSbAdNeKeyword();
        BeanUtils.copyProperties(neKeyword, oldNeKeyword);
        neKeyword.setUpdateId(uid);
        neKeyword.setState(CpcStatusEnum.archived.name());
        logSbNeKeywordsArchive(oldNeKeyword, neKeyword, ip, result);
        if (result.success()) {
            neKeywordDao.updateByIdAndPuid(puid, neKeyword);
            saveDoris(neKeyword.getPuid(), neKeyword.getShopId(), Lists.newArrayList(keywordId));
        }
        return result;
    }

    private void logSbNeKeywordsArchive(AmazonSbAdNeKeyword oldKeyword, AmazonSbAdNeKeyword keyword, String ip, Result result) {
        try {
            AdManageOperationLog operationLog = adManageOperationLogService.getSbNeKeywordLog(oldKeyword, keyword);
            operationLog.setIp(ip);
            if (result.success()){
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }else{
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            adManageOperationLogService.printAdOperationLog(Lists.newArrayList(operationLog));
        }catch (Exception e){
            log.error("sb 否定关键词更新日志异常", e);
        }
    }

    @Override
    public Result<BatchResponseVo<BatchNeKeywordVo, AmazonSbAdNeKeyword>> batchArchive(Integer puid, Integer shopId, Integer uid, List<Long> idList, String ip) {
        List<AmazonSbAdNeKeyword> neSbKeywordList = neKeywordDao.getListByLongIdList(puid, idList);
        if (CollectionUtils.isEmpty(neSbKeywordList)) {
            return ResultUtil.returnErr("没有否定关键词信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<Long, AmazonSbAdNeKeyword> amazonSbAdNeKeywordMap = neSbKeywordList.stream().collect(Collectors.toMap(AmazonSbAdNeKeyword::getId, e -> e));
        List<BatchNeKeywordVo> errorList = new ArrayList<>();
        List<AmazonSbAdNeKeyword> archiveList = new ArrayList<>();

        for (Long id : idList) {
            BatchNeKeywordVo vo = new BatchNeKeywordVo();
            AmazonSbAdNeKeyword neKeyword = amazonSbAdNeKeywordMap.get(id);
            vo.setIsFail(false);
            vo.setUid(uid);
            if (neKeyword == null) {
                vo.setIsFail(true);
                vo.setFailReason("关键词不存在");
                errorList.add(vo);
                continue;
            }
            String keywordId = neKeyword.getKeywordId();
            if (StringUtils.isBlank(keywordId)) {
                vo.setIsFail(true);
                vo.setFailReason("平台keyword id 为空, 请同步该活动再操作");
                errorList.add(vo);
                continue;
            }
            AmazonSbAdNeKeyword amazonSbAdNeKeyword = new AmazonSbAdNeKeyword();
            BeanUtils.copyProperties(neKeyword, amazonSbAdNeKeyword);
            convertVoToBatchUpdatePo(amazonSbAdNeKeyword, vo);
            archiveList.add(amazonSbAdNeKeyword);
        }

        if (CollectionUtils.isEmpty(archiveList)) {
            BatchResponseVo<BatchNeKeywordVo, AmazonSbAdNeKeyword> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(idList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<BatchNeKeywordVo, AmazonSbAdNeKeyword>> result = cpcSbNeKeywordApiService.update(shop, profile, archiveList);

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        for (AmazonSbAdNeKeyword neKeyword : archiveList) {
            AmazonSbAdNeKeyword oldNeKeyword = amazonSbAdNeKeywordMap.get(neKeyword.getId());
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getSbNeKeywordLog(oldNeKeyword, neKeyword);
            adManageOperationLog.setIp(ip);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            BatchResponseVo<BatchNeKeywordVo, AmazonSbAdNeKeyword> data = result.getData();
            List<BatchNeKeywordVo> amazonAdSbNeKeywordError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                amazonAdSbNeKeywordError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }
            List<AmazonSbAdNeKeyword> successList = data.getSuccessList();

            Map<String, AmazonSbAdNeKeyword> successDataMap = successList.stream().collect(Collectors.toMap(AmazonSbAdNeKeyword::getKeywordId, e -> e));
            Map<String, BatchNeKeywordVo> errorDataMap = amazonAdSbNeKeywordError.stream().collect(Collectors.toMap(BatchNeKeywordVo::getKeywordId, e -> e));
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getTargetId()).getFailReason());
                }
            }

            if (CollectionUtils.isNotEmpty(successList)) {
                neKeywordDao.batchUpdate(puid, successList);
                List<String> keywordIds = successList.stream().map(AmazonSbAdNeKeyword::getKeywordId).collect(Collectors.toList());
                saveDoris(puid, shopId, keywordIds);
                data.getSuccessList().clear();
            }
        } else {
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(adManageOperationLogs);
        return result;
    }

    private void convertVoToBatchUpdatePo(AmazonSbAdNeKeyword amazonSbAdNeKeyword, BatchNeKeywordVo vo){
        amazonSbAdNeKeyword.setCreateInAmzup(Constants.UPDATE_IN_AMZUP);
        amazonSbAdNeKeyword.setUpdateId(vo.getUid());
    }

    // vo -> po
    private List<AmazonSbAdNeKeyword> convertAddNeKeywordsVoToPo(Integer uid, AmazonSbAdGroup adGroup, List<NeKeywordsVo> neKeywords) {
        List<AmazonSbAdNeKeyword> keywordList = new ArrayList<>(neKeywords.size());
        AmazonSbAdNeKeyword neKeyword;
        for (NeKeywordsVo vo : neKeywords) {
            neKeyword = new AmazonSbAdNeKeyword();
            neKeyword.setPuid(adGroup.getPuid());
            neKeyword.setShopId(adGroup.getShopId());
            neKeyword.setMarketplaceId(adGroup.getMarketplaceId());
            neKeyword.setProfileId(adGroup.getProfileId());
            neKeyword.setAdGroupId(adGroup.getAdGroupId());
            neKeyword.setCampaignId(adGroup.getCampaignId());
            neKeyword.setKeywordText(vo.getKeywordText());
            neKeyword.setMatchType(vo.getMatchType());
            neKeyword.setState("enabled");
            neKeyword.setCreateId(uid);
            neKeyword.setCreateInAmzup(1);
            keywordList.add(neKeyword);
        }

        return keywordList;
    }

    // vo -> po
    private List<AmazonSbAdNeKeyword> convertAddNeKeywordsVoToPo(Integer uid, Map<String,AmazonSbAdGroup> adGroup, List<CampaignIdNeKeywordsVo> neKeywords) {
        List<AmazonSbAdNeKeyword> keywordList = new ArrayList<>(neKeywords.size());
        AmazonSbAdNeKeyword neKeyword;
        for (CampaignIdNeKeywordsVo vo : neKeywords) {
            neKeyword = new AmazonSbAdNeKeyword();
            neKeyword.setIndex(vo.getIndex());
            AmazonSbAdGroup amazonSbAdGroup = adGroup.get(vo.getGroupId());
            neKeyword.setPuid(amazonSbAdGroup.getPuid());
            neKeyword.setShopId(amazonSbAdGroup.getShopId());
            neKeyword.setMarketplaceId(amazonSbAdGroup.getMarketplaceId());
            neKeyword.setProfileId(amazonSbAdGroup.getProfileId());
            neKeyword.setAdGroupId(amazonSbAdGroup.getAdGroupId());
            neKeyword.setCampaignId(amazonSbAdGroup.getCampaignId());
            neKeyword.setKeywordText(vo.getKeywordText());
            neKeyword.setState("enabled");
            neKeyword.setMatchType(vo.getMatchType());
            neKeyword.setCreateId(uid);
            neKeyword.setCreateInAmzup(1);
            keywordList.add(neKeyword);
        }

        return keywordList;
    }


    /**
     * 写入doris
     * @param keywordList
     */
    @Override
    public void saveDoris(List<AmazonSbAdNeKeyword> keywordList) {
        try {
            List<OdsAmazonAdSbNeKeyword> collect = keywordList.stream().map(x -> {
                OdsAmazonAdSbNeKeyword po = new OdsAmazonAdSbNeKeyword();
                BeanUtils.copyProperties(x, po);
                if (x.getCreateTime() != null) {
                    //sb 没有亚马逊创建时间创建时间使用 create_time 时间
                    LocalDateTime localDateTime = LocalDateTimeUtil.convertDateToLDT(x.getCreateTime());
                    po.setCreationDate(localDateTime);
                    //产品要求还是按北京时间转换，这个地方先不改，后面再说
                    po.setAmazonCreateTime(localDateTime);
                    po.setCreationAfterDate(localDateTime.plusDays(30L).toLocalDate());
                    po.setCreationBeforeDate(localDateTime.plusDays(-30L).toLocalDate());
                }
                if (StringUtils.isNotBlank(po.getState())) {
                    po.setState(po.getState().toLowerCase());
                }
                return po;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb ne keyword save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param keywordIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> keywordIdList) {
        try {
            if (CollectionUtils.isEmpty(keywordIdList)) {
                return;
            }
            List<AmazonSbAdNeKeyword> amazonSbAdNeKeywords = neKeywordDao.listByKeywordId(puid, shopId, keywordIdList);
            saveDoris(amazonSbAdNeKeywords);
        } catch (Exception e) {
            log.error("sp ne keyword save doris error", e);
        }
    }

    @Override
    public List<AmazonSbAdNeKeyword> listByKeywordText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList) {
        return neKeywordDao.listByKeywordText(puid, shopId, infoList);
    }
}
