package com.meiyunji.sponsored.service.cpc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告产品列表页
 */
@Data
@ApiModel
public class AdProductPageVo extends CpcCommPageVo {

    @ApiModelProperty("类型 sp sb sd")
    private String type;
    private Long id;
    @ApiModelProperty("店铺ID")
    private Integer shopId;
    private Integer dxmAdGroupId;
    @ApiModelProperty("活动ID")
    private String campaignId;
    @ApiModelProperty("活动状态")
    private String campaignState;
    @ApiModelProperty("活动名称")
    private String campaignName;
    @ApiModelProperty("活动投放类型")
    private String campaignTargetingType;
    @ApiModelProperty("广告组ID")
    private String adGroupId;
    @ApiModelProperty("广告组类型")
    private String adGroupType;
    @ApiModelProperty("广告组名称")
    private String adGroupName;
    @ApiModelProperty("广告组状态")
    private String adGroupState;
    @ApiModelProperty("广告ID")
    private String adId;
    @ApiModelProperty("状态")
    private String state;
    @ApiModelProperty("asin")
    private String asin;
    @ApiModelProperty("sku")
    private String sku;
    @ApiModelProperty("标题")
    private String title;
    @ApiModelProperty("图片URL")
    private String imgUrl;
    @ApiModelProperty("价格")
    private String price;
    @ApiModelProperty("站点")
    private String domain;
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date updateTime;
    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty("广告组合名称")
    private String portfolioName;
    @ApiModelProperty("广告组合隐藏状态 1:隐藏 0:可见")
    private Integer isHidden;
    @ApiModelProperty("二级状态")
    private String servingStatus;

    @ApiModelProperty("二级状态描述")
    private String servingStatusDec;

    @ApiModelProperty("二级状态名称")
    private String servingStatusName;

    @ApiModelProperty("标签")
    private List<AdTag> adTags;

    private String parentAsin;

    @ApiModelProperty("花费类型 vcpm/cpc")
    private String costType;
}
