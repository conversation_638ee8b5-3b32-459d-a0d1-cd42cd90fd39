package com.meiyunji.sponsored.service.multiple.common.utils;

import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.service.cpc.po.ReportNewBase;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.CpcCommPageNewVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportChartBase;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportDorisBase;
import com.meiyunji.sponsored.service.multiple.common.vo.*;
import com.meiyunji.sponsored.service.reportHour.vo.AdAnalysisAndCompareVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 多店铺报告工具类
 */
@Slf4j
public class ReportUtils {

    /**
     * 填充占比数据
     */
    public static void filterAdMetricData(AdMetricDto adMetricDto, CpcCommPageNewVo vo) {
        if (adMetricDto == null) {
            vo.setSumCost(BigDecimal.ZERO);
            vo.setSumAdSale(BigDecimal.ZERO);
            vo.setSumAdOrderNum(BigDecimal.ZERO);
            vo.setSumOrderNum(BigDecimal.ZERO);
            return;
        }
        vo.setSumCost(adMetricDto.getSumCost() == null ? BigDecimal.ZERO : adMetricDto.getSumCost());
        vo.setSumAdSale(adMetricDto.getSumAdSale() == null ? BigDecimal.ZERO : adMetricDto.getSumAdSale());
        vo.setSumAdOrderNum(adMetricDto.getSumAdOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumAdOrderNum());
        vo.setSumOrderNum(adMetricDto.getSumOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumOrderNum());
    }

    /**
     * 转公用报告基础数据对象
     */
    public static ReportNewBase buildReportBase(ReportDorisBase report, String type) {
        ReportNewBase reportBase = new ReportNewBase();
        reportBase.setCountDate("");
        reportBase.setCost(report.getCostDoris());
        reportBase.setTotalSales(report.getTotalSalesDoris());
        reportBase.setAdSales(report.getAdSalesDoris());
        reportBase.setImpressions(report.getImpressionsDoris());
        reportBase.setClicks(report.getClicksDoris());
        reportBase.setOrderNum(report.getSaleNumDoris());
        reportBase.setSaleNum(report.getOrderNumDoris());
        reportBase.setAdSaleNum(report.getAdOrderNumDoris());
        reportBase.setAdOrderNum(report.getAdSaleNumDoris());
        reportBase.setSalesNewToBrand14d(report.getSalesNewToBrand14dDoris());
        reportBase.setOrdersNewToBrand14d(report.getOrdersNewToBrand14dDoris());
        reportBase.setUnitsOrderedNewToBrand14d(report.getUnitsOrderedNewToBrand14dDoris());
        reportBase.setViewImpressions(report.getViewImpressionsDoris());
        reportBase.setType(type);
        reportBase.setMaxTopIs(report.getMaxTopIsDoris());
        reportBase.setMinTopIs(report.getMinTopIsDoris());
        reportBase.setNewToBrandDetailPageViews(report.getNewToBrandDetailPageViewsDoris());
        reportBase.setAddToCart(report.getAddToCartDoris());
        reportBase.setVideo5SecondViews(report.getVideo5secondViewsDoris());
        reportBase.setVideoFirstQuartileViews(report.getVideoFirstQuartileViewsDoris());
        reportBase.setVideoMidpointViews(report.getVideoMidpointViewsDoris());
        reportBase.setVideoThirdQuartileViews(report.getVideoThirdQuartileViewsDoris());
        reportBase.setVideoCompleteViews(report.getVideoCompleteViewsDoris());
        reportBase.setVideoUnmutes(report.getVideoUnmutesDoris());
        reportBase.setViewableImpressions(report.getViewImpressionsDoris());
        reportBase.setBrandedSearches(report.getBrandedSearches14dDoris());
        reportBase.setDetailPageViews(report.getDetailPageView14dDoris());
        reportBase.setCumulativeReach(report.getCumulativeReachDoris());
        reportBase.setImpressionsFrequencyAverage(report.getImpressionsFrequencyAverageDoris());
        return reportBase;
    }

    /**
     * 填充报告数据
     */
    public static <e extends CommonReport> void buildReport(e vo, CpcCommPageNewVo campaignVo) {
        vo.setImpressions(Optional.ofNullable(campaignVo.getImpressions()).orElse(0L));
        vo.setClicks(Optional.ofNullable(campaignVo.getClicks()).orElse(0L));
        vo.setAdOrderNum(Optional.ofNullable(campaignVo.getAdOrderNum()).orElse(0L));
        vo.setAdCostPerClick(StringUtils.isNotBlank(campaignVo.getAdCostPerClick()) ? campaignVo.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(campaignVo.getCtr()) ? campaignVo.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(campaignVo.getCvr()) ? campaignVo.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(campaignVo.getAcos()) ? campaignVo.getAcos() : "0");
        vo.setRoas(StringUtils.isNotBlank(campaignVo.getRoas()) ? campaignVo.getRoas() : "0");
        vo.setAdCost(StringUtils.isNotBlank(campaignVo.getAdCost()) ? campaignVo.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(campaignVo.getAcots()) ? campaignVo.getAcots() : "0");
        vo.setAsots(StringUtils.isNotBlank(campaignVo.getAsots()) ? campaignVo.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(campaignVo.getAdSale()) ? campaignVo.getAdSale() : "0");
        vo.setViewImpressions(Optional.ofNullable(campaignVo.getViewImpressions()).orElse(0L));
        vo.setCpa(StringUtils.isNotBlank(campaignVo.getCpa()) ? campaignVo.getCpa() : "0");
        vo.setVcpm(StringUtils.isNotBlank(campaignVo.getVcpm()) ? campaignVo.getVcpm() : "0.00");
        vo.setAdSelfSaleNum(Optional.ofNullable(campaignVo.getAdSelfSaleNum()).orElse(0L));
        vo.setAdOtherSaleNum(Optional.ofNullable(campaignVo.getAdOtherSaleNum()).orElse(0L));
        vo.setAdSaleNum(Optional.ofNullable(campaignVo.getAdSaleNum()).orElse(0L));
        vo.setAdSaleNum(Optional.ofNullable(campaignVo.getAdSaleNum()).orElse(0L));
        vo.setAdOtherOrderNum(Optional.ofNullable(campaignVo.getAdOtherOrderNum()).orElse(0L));
        vo.setAdSales(StringUtils.isNotBlank(campaignVo.getAdSales()) ? campaignVo.getAdSales() : "0");
        vo.setAdOtherSales(StringUtils.isNotBlank(campaignVo.getAdOtherSales()) ? campaignVo.getAdOtherSales() : "0");
        vo.setOrderNum(Optional.ofNullable(campaignVo.getOrderNum()).orElse(0L));
        vo.setOrdersNewToBrandFTD(Optional.ofNullable(campaignVo.getOrdersNewToBrandFTD()).orElse(0L));
        vo.setOrdersNewToBrandPercentageFTD(Optional.ofNullable(campaignVo.getOrdersNewToBrandPercentageFTD()).orElse( "0"));
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getOrderRateNewToBrandFTD()) ? campaignVo.getOrderRateNewToBrandFTD() : "0");
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getSalesNewToBrandFTD()) ? campaignVo.getSalesNewToBrandFTD() : "0");
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getSalesRateNewToBrandFTD()) ? campaignVo.getSalesRateNewToBrandFTD() : "0");
        vo.setUnitsOrderedNewToBrandFTD(Optional.ofNullable(campaignVo.getUnitsOrderedNewToBrandFTD()).orElse(0L));
        vo.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(campaignVo.getUnitsOrderedRateNewToBrandFTD()) ? campaignVo.getUnitsOrderedRateNewToBrandFTD() : "0");
        vo.setAdCostPercentage(StringUtils.isNotBlank(campaignVo.getAdCostPercentage()) ? campaignVo.getAdCostPercentage() : "0");
        vo.setAdSalePercentage(StringUtils.isNotBlank(campaignVo.getAdSalePercentage()) ? campaignVo.getAdSalePercentage() : "0");
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(campaignVo.getAdOrderNumPercentage()) ? campaignVo.getAdOrderNumPercentage() : "0");
        vo.setOrderNumPercentage(StringUtils.isNotBlank(campaignVo.getOrderNumPercentage()) ? campaignVo.getOrderNumPercentage() : "0");
        vo.setNewToBrandDetailPageViews(Optional.ofNullable(campaignVo.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAddToCart(Optional.ofNullable(campaignVo.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setAddToCartRate(Optional.ofNullable(campaignVo.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setECPAddToCart(Optional.ofNullable(campaignVo.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViews(Optional.ofNullable(campaignVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setVideo5SecondViewRate(Optional.ofNullable(campaignVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(campaignVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(campaignVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(campaignVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(campaignVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(campaignVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewabilityRate(Optional.ofNullable(campaignVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(campaignVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(campaignVo.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setDetailPageViews(Optional.ofNullable(campaignVo.getDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCumulativeReach(Optional.ofNullable(campaignVo.getCumulativeReach()).map(String::valueOf).orElse("0"));
        vo.setImpressionsFrequencyAverage(Optional.ofNullable(campaignVo.getImpressionsFrequencyAverage()).map(it -> it.setScale(2, RoundingMode.HALF_UP).toString()).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(campaignVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        vo.setTopImpressionShare(Optional.ofNullable(campaignVo.getTopImpressionShare()).orElse("-"));
    }

    /**
     * 填充环比值
     */
    public static <E extends CommonCompareReport> void buildCompareValue(E vo, CpcCommPageNewVo compareVo) {
        vo.setCompareImpressions(Optional.ofNullable(compareVo.getImpressions()).orElse(0L));
        vo.setCompareClicks(Optional.ofNullable(compareVo.getClicks()).orElse(0L));
        vo.setCompareCtr(StringUtils.isNotBlank(compareVo.getCtr()) ? compareVo.getCtr() : "0");
        vo.setCompareCvr(StringUtils.isNotBlank(compareVo.getCvr()) ? compareVo.getCvr() : "0");
        vo.setCompareAcos(StringUtils.isNotBlank(compareVo.getAcos()) ? compareVo.getAcos() : "0");
        vo.setCompareRoas(StringUtils.isNotBlank(compareVo.getRoas()) ? compareVo.getRoas() : "0");
        vo.setCompareAcots(StringUtils.isNotBlank(compareVo.getAcots()) ? compareVo.getAcots() : "0");
        vo.setCompareAsots(StringUtils.isNotBlank(compareVo.getAsots()) ? compareVo.getAsots() : "0");
        vo.setCompareAdOrderNum(Optional.ofNullable(compareVo.getAdOrderNum()).orElse(0L));
        vo.setCompareAdCost(StringUtils.isNotBlank(compareVo.getAdCost()) ? compareVo.getAdCost() : "0");
        vo.setCompareAdCostPerClick(StringUtils.isNotBlank(compareVo.getAdCostPerClick()) ? compareVo.getAdCostPerClick() : "0");
        vo.setCompareAdSale(StringUtils.isNotBlank(compareVo.getAdSale()) ? compareVo.getAdSale() : "0");
        vo.setCompareViewImpressions(compareVo.getViewImpressions() == null ? "0" : String.valueOf(compareVo.getViewImpressions()));
        vo.setCompareCpa(StringUtils.isNotBlank(compareVo.getCpa()) ? compareVo.getCpa() : "0");
        vo.setCompareVcpm(StringUtils.isNotBlank(compareVo.getVcpm()) ? compareVo.getVcpm() : "0");
        vo.setCompareAdSaleNum(Optional.ofNullable(compareVo.getAdSaleNum()).orElse(0L));
        vo.setCompareAdOtherOrderNum(Optional.ofNullable(compareVo.getAdOtherOrderNum()).orElse(0L));
        vo.setCompareAdSales(StringUtils.isNotBlank(compareVo.getAdSales()) ? compareVo.getAdSales() : "0");
        vo.setCompareAdOtherSales(StringUtils.isNotBlank(compareVo.getAdOtherSales()) ? compareVo.getAdOtherSales() : "0");
        vo.setCompareOrderNum(Optional.ofNullable(compareVo.getOrderNum()).orElse(0L));
        vo.setCompareAdSelfSaleNum(Optional.ofNullable(compareVo.getAdSelfSaleNum()).orElse(0L));
        vo.setCompareAdOtherSaleNum(Optional.ofNullable(compareVo.getAdOtherSaleNum()).orElse(0L));
        vo.setCompareOrdersNewToBrandFTD(Optional.ofNullable(compareVo.getOrdersNewToBrandFTD()).orElse(0L));
        vo.setCompareOrdersNewToBrandPercentageFTD(Optional.ofNullable(compareVo.getOrdersNewToBrandPercentageFTD()).orElse( "0"));
        vo.setCompareOrderRateNewToBrandFTD(StringUtils.isNotBlank(compareVo.getOrderRateNewToBrandFTD()) ? compareVo.getOrderRateNewToBrandFTD() : "0");
        vo.setCompareSalesNewToBrandFTD(StringUtils.isNotBlank(compareVo.getSalesNewToBrandFTD()) ? compareVo.getSalesNewToBrandFTD() : "0");
        vo.setCompareSalesRateNewToBrandFTD(StringUtils.isNotBlank(compareVo.getSalesRateNewToBrandFTD()) ? compareVo.getSalesRateNewToBrandFTD() : "0");
        vo.setCompareUnitsOrderedNewToBrandFTD(Optional.ofNullable(compareVo.getUnitsOrderedNewToBrandFTD()).orElse(0L));
        vo.setCompareUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(compareVo.getUnitsOrderedRateNewToBrandFTD()) ? compareVo.getUnitsOrderedRateNewToBrandFTD() : "0");
        vo.setCompareNewToBrandDetailPageViews(Optional.ofNullable(compareVo.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCompareAdCostPercentage(StringUtils.isNotBlank(compareVo.getAdCostPercentage()) ? compareVo.getAdCostPercentage() : "0");
        vo.setCompareAdSalePercentage(StringUtils.isNotBlank(compareVo.getAdSalePercentage()) ? compareVo.getAdSalePercentage() : "0");
        vo.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareVo.getAdOrderNumPercentage()) ? compareVo.getAdOrderNumPercentage() : "0");
        vo.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareVo.getOrderNumPercentage()) ? compareVo.getOrderNumPercentage() : "0");
        vo.setCompareAddToCart(Optional.ofNullable(compareVo.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setCompareAddToCartRate(Optional.ofNullable(compareVo.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setCompareECPAddToCart(Optional.ofNullable(compareVo.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setCompareVideo5SecondViews(Optional.ofNullable(compareVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideo5SecondViewRate(Optional.ofNullable(compareVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoFirstQuartileViews(Optional.ofNullable(compareVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoMidpointViews(Optional.ofNullable(compareVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoThirdQuartileViews(Optional.ofNullable(compareVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoCompleteViews(Optional.ofNullable(compareVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setCompareVideoUnmutes(Optional.ofNullable(compareVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setCompareViewabilityRate(Optional.ofNullable(compareVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setCompareViewClickThroughRate(Optional.ofNullable(compareVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setCompareBrandedSearches(Optional.ofNullable(compareVo.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setCompareImpressionsFrequencyAverage(Optional.ofNullable(compareVo.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
        vo.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        vo.setCompareDetailPageViews(Optional.ofNullable(compareVo.getDetailPageViews()).map(String::valueOf).orElse("0"));
    }

    /**
     * 填充环比增长率
     */
    public static <E extends CommonCompareReportRate> void buildCompareRate(E vo) {
        vo.setCompareImpressionsRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getImpressions()), BigDecimal.valueOf(vo.getCompareImpressions())).toString());
        vo.setCompareClicksRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(vo.getCompareClicks())).toString());
        vo.setCompareCtrRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getCtr()), new BigDecimal(vo.getCompareCtr())).toString());
        vo.setCompareCvrRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getCvr()), new BigDecimal(vo.getCompareCvr())).toString());
        vo.setCompareAcosRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAcos()), new BigDecimal(vo.getCompareAcos())).toString());
        vo.setCompareRoasRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getRoas()), new BigDecimal(vo.getCompareRoas())).toString());
        vo.setCompareAcotsRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAcots()), new BigDecimal(vo.getCompareAcots())).toString());
        vo.setCompareAsotsRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAsots()), new BigDecimal(vo.getCompareAsots())).toString());
        vo.setCompareAdOrderNumRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(vo.getCompareAdOrderNum())).toString());
        vo.setCompareAdCostRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAdCost()), new BigDecimal(vo.getCompareAdCost())).toString());
        vo.setCompareAdCostPerClickRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAdCostPerClick()), new BigDecimal(vo.getCompareAdCostPerClick())).toString());
        vo.setCompareAdSaleRate(AdAnalysisAndCompareVo.calculateCompareRete(new BigDecimal(vo.getAdSale()), new BigDecimal(vo.getCompareAdSale())).toString());
        vo.setCompareViewImpressionsRate(AdAnalysisAndCompareVo.calculateCompareRete(BigDecimal.valueOf(vo.getViewImpressions()), BigDecimal.valueOf(Integer.parseInt(vo.getCompareViewImpressions()))).toString());
        vo.setCompareCpaRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getCpa()), new BigDecimal(vo.getCompareCpa())));
        vo.setCompareVcpmRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVcpm()), new BigDecimal(vo.getCompareVcpm())));
        vo.setCompareAdSaleNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSaleNum()), new BigDecimal(vo.getCompareAdSaleNum())));
        vo.setCompareAdOtherOrderNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOtherOrderNum()), new BigDecimal(vo.getCompareAdOtherOrderNum())));
        vo.setCompareAdSalesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSales()), new BigDecimal(vo.getCompareAdSales())));
        vo.setCompareAdOtherSalesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOtherSales()), new BigDecimal(vo.getCompareAdOtherSales())));
        vo.setCompareOrderNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrderNum()), new BigDecimal(vo.getCompareOrderNum())));
        vo.setCompareAdSelfSaleNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSelfSaleNum()), new BigDecimal(vo.getCompareAdSelfSaleNum())));
        vo.setCompareAdOtherSaleNumRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOtherSaleNum()), new BigDecimal(vo.getCompareAdOtherSaleNum())));
        vo.setCompareOrdersNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrdersNewToBrandFTD()), new BigDecimal(vo.getCompareOrdersNewToBrandFTD())));
        vo.setCompareOrdersNewToBrandPercentageFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrdersNewToBrandPercentageFTD()), new BigDecimal(vo.getCompareOrdersNewToBrandPercentageFTD())));
        vo.setCompareOrderRateNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrderRateNewToBrandFTD()), new BigDecimal(vo.getCompareOrderRateNewToBrandFTD())));
        vo.setCompareSalesNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getSalesNewToBrandFTD()), new BigDecimal(vo.getCompareSalesNewToBrandFTD())));
        vo.setCompareSalesRateNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getSalesRateNewToBrandFTD()), new BigDecimal(vo.getCompareSalesRateNewToBrandFTD())));
        vo.setCompareUnitsOrderedNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getUnitsOrderedNewToBrandFTD()), new BigDecimal(vo.getCompareUnitsOrderedNewToBrandFTD())));
        vo.setCompareUnitsOrderedRateNewToBrandFTDRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getUnitsOrderedRateNewToBrandFTD()), new BigDecimal(vo.getCompareUnitsOrderedRateNewToBrandFTD())));
        vo.setCompareNewToBrandDetailPageViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getNewToBrandDetailPageViews()), new BigDecimal(vo.getCompareNewToBrandDetailPageViews())));
        vo.setCompareAdCostPercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdCostPercentage()), new BigDecimal(vo.getCompareAdCostPercentage())));
        vo.setCompareAdSalePercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdSalePercentage()), new BigDecimal(vo.getCompareAdSalePercentage())));
        vo.setCompareAdOrderNumPercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdOrderNumPercentage()), new BigDecimal(vo.getCompareAdOrderNumPercentage())));
        vo.setCompareOrderNumPercentageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getOrderNumPercentage()), new BigDecimal(vo.getCompareOrderNumPercentage())));
        vo.setCompareAddToCartRates(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAddToCart()), new BigDecimal(vo.getCompareAddToCart())));
        vo.setCompareAddToCartRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAddToCartRate()), new BigDecimal(vo.getCompareAddToCartRate())));
        vo.setCompareECPAddToCartRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getECPAddToCart()), new BigDecimal(vo.getCompareECPAddToCart())));
        vo.setCompareVideo5SecondViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideo5SecondViews()), new BigDecimal(vo.getCompareVideo5SecondViews())));
        vo.setCompareVideo5SecondViewRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideo5SecondViewRate()), new BigDecimal(vo.getCompareVideo5SecondViewRate())));
        vo.setCompareVideoFirstQuartileViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoFirstQuartileViews()), new BigDecimal(vo.getCompareVideoFirstQuartileViews())));
        vo.setCompareVideoMidpointViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoMidpointViews()), new BigDecimal(vo.getCompareVideoMidpointViews())));
        vo.setCompareVideoThirdQuartileViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoThirdQuartileViews()), new BigDecimal(vo.getCompareVideoThirdQuartileViews())));
        vo.setCompareVideoCompleteViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoCompleteViews()), new BigDecimal(vo.getCompareVideoCompleteViews())));
        vo.setCompareVideoUnmutesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getVideoUnmutes()), new BigDecimal(vo.getCompareVideoUnmutes())));
        vo.setCompareViewabilityRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getViewabilityRate()), new BigDecimal(vo.getCompareViewabilityRate())));
        vo.setCompareViewClickThroughRateRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getViewClickThroughRate()), new BigDecimal(vo.getCompareViewClickThroughRate())));
        vo.setCompareBrandedSearchesRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getBrandedSearches()), new BigDecimal(vo.getCompareBrandedSearches())));
        vo.setCompareImpressionsFrequencyAverageRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getImpressionsFrequencyAverage()), new BigDecimal(vo.getCompareImpressionsFrequencyAverage())));
        vo.setCompareAdvertisingUnitPriceRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getAdvertisingUnitPrice()), new BigDecimal(vo.getCompareAdvertisingUnitPrice())));
        vo.setCompareDetailPageViewsRate(AdAnalysisAndCompareVo.calculateCompareReteWithLine(new BigDecimal(vo.getDetailPageViews()), new BigDecimal(vo.getCompareDetailPageViews())));
    }

    /**
     * 图表参数转化
     */
    public static List<AdHomePerformanceNewDto> getChartDtoList(List<ReportChartBase> sumReportDayList) {
        List<AdHomePerformanceNewDto> dtoList = new ArrayList<>();
        // 转换图表参数
        for (ReportChartBase dto : sumReportDayList) {
            AdHomePerformanceNewDto chartDto = new AdHomePerformanceNewDto();
            chartDto.setCountDate(DateUtil.dateStringFormat(dto.getCountDay(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMMDD));
            chartDto.setClicks(dto.getClicksDoris());
            chartDto.setImpressions(dto.getImpressionsDoris());
            chartDto.setAdOrderNum(dto.getOrderNumDoris());
            chartDto.setAdCost(dto.getCostDoris());
            chartDto.setAdSale(dto.getTotalSalesDoris());
            chartDto.setAdSaleNum(dto.getAdOrderNumDoris());
            chartDto.setAdSales(dto.getAdSalesDoris());
            chartDto.setSalesNum(dto.getSaleNumDoris());
            chartDto.setOrderNum(dto.getAdSaleNumDoris());
            chartDto.setViewImpressions(dto.getViewImpressionsDoris());
            chartDto.setOrdersNewToBrand14d(dto.getOrdersNewToBrand14dDoris());
            chartDto.setSalesNewToBrand14d(dto.getSalesNewToBrand14dDoris());
            chartDto.setUnitsOrderedNewToBrand14d(dto.getUnitsOrderedNewToBrand14dDoris());
            dtoList.add(chartDto);
        }
        return dtoList;
    }

    /**
     * grpc图表转成vo图表
     */
    public static List<AdHomeChartVo> chartGrpcToVo(List<AdHomeChartRpcVo> grpcVoList){
        List<AdHomeChartVo> chartVoList = new ArrayList<>();
        for (AdHomeChartRpcVo grpcVo : grpcVoList) {
            AdHomeChartVo vo = new AdHomeChartVo();
            vo.setDescription(grpcVo.getDescription());
            vo.setCurrency(grpcVo.getCurrency());
            vo.setName(grpcVo.getName());
            vo.setTotal(grpcVo.getTotal().getValue());
            List<AdHomeChartVo.ChartRecord> chartRecords = new ArrayList<>();
            for (AdHomeChartRpcVo.ChartRpcRecord chartRpcRecord : grpcVo.getRecordsList()) {
                AdHomeChartVo.ChartRecord record = new AdHomeChartVo.ChartRecord();
                record.setDate(chartRpcRecord.getDate());
                record.setValue(chartRpcRecord.getValue());
                chartRecords.add(record);
            }
            vo.setRecords(chartRecords);
            chartVoList.add(vo);
        }
        return chartVoList;
    }

    /**
     * 广告管理列表页处理高级筛选百分比参数
     */
    public static void handlePercentParam(AdvanceFilter advanceFilter) {
        advanceFilter.setClickRateMin(advanceFilter.getClickRateMin() != null ? MathUtil.divide(advanceFilter.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setClickRateMax(advanceFilter.getClickRateMax() != null ? MathUtil.divide(advanceFilter.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setAcosMin(advanceFilter.getAcosMin() != null ? MathUtil.divide(advanceFilter.getAcosMin(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setAcosMax(advanceFilter.getAcosMax() != null ? MathUtil.divide(advanceFilter.getAcosMax(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setSalesConversionRateMin(advanceFilter.getSalesConversionRateMin() != null ? MathUtil.divide(advanceFilter.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setSalesConversionRateMax(advanceFilter.getSalesConversionRateMax() != null ? MathUtil.divide(advanceFilter.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setAcotsMin(advanceFilter.getAcotsMin() != null ? MathUtil.divide(advanceFilter.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setAcotsMax(advanceFilter.getAcotsMax() != null ? MathUtil.divide(advanceFilter.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setAsotsMin(advanceFilter.getAsotsMin() != null ? MathUtil.divide(advanceFilter.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setAsotsMax(advanceFilter.getAsotsMax() != null ? MathUtil.divide(advanceFilter.getAsotsMax(), BigDecimal.valueOf(100)) : null);
        advanceFilter.setOrderRateNewToBrandFTDMin(advanceFilter.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(advanceFilter.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setOrderRateNewToBrandFTDMax(advanceFilter.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(advanceFilter.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setSalesRateNewToBrandFTDMin(advanceFilter.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(advanceFilter.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setSalesRateNewToBrandFTDMax(advanceFilter.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(advanceFilter.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setUnitsOrderedRateNewToBrandFTDMin(advanceFilter.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(advanceFilter.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setUnitsOrderedRateNewToBrandFTDMax(advanceFilter.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(advanceFilter.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setBrandNewBuyerOrderConversionRateMin(advanceFilter.getBrandNewBuyerOrderConversionRateMin() != null ? MathUtil.divide(advanceFilter.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setBrandNewBuyerOrderConversionRateMax(advanceFilter.getBrandNewBuyerOrderConversionRateMax() != null ? MathUtil.divide(advanceFilter.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setViewClickThroughRateMin(advanceFilter.getViewClickThroughRateMin() != null ? MathUtil.divide(advanceFilter.getViewClickThroughRateMin(), BigDecimal.valueOf(100), 6) : null);
        advanceFilter.setViewClickThroughRateMax(advanceFilter.getViewClickThroughRateMax() != null ? MathUtil.divide(advanceFilter.getViewClickThroughRateMax(), BigDecimal.valueOf(100), 6) : null);
    }

    /**
     * 高级筛选反射转换成集合 方便后续通过枚举获取去掉if else判断
     */
    public static List<AdvanceFilterVo> advanceFilterChange(AdvanceFilter advanceFilter) {
        List<AdvanceFilterVo> advanceFilterVoList = new ArrayList<>();
        Class<?> clazz = advanceFilter.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object value = field.get(advanceFilter);
                if (value != null) {
                    AdvanceFilterVo filterVo = new AdvanceFilterVo();
                    filterVo.setCode(field.getName());
                    if(value instanceof BigDecimal) {
                        filterVo.setValue((BigDecimal)value);
                    }else if(value instanceof Integer){
                        filterVo.setValue(new BigDecimal((Integer)value));
                    }
                    advanceFilterVoList.add(filterVo);
                }
            } catch (IllegalAccessException e) {
                log.error("通过反射获取高级筛选参数异常");
            }
        }
        return advanceFilterVoList;
    }
}
