package com.meiyunji.sponsored.service.multiple.targets.dto;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.multiple.common.vo.AdvanceFilter;
import com.meiyunji.sponsored.service.multiple.common.vo.AdvanceFilterVo;
import com.meiyunji.sponsored.service.multiple.targets.enums.TargetTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 投放层级多店铺 前端请求参数+内部转换参数
 * @Author: zzh
 * @Date: 2025/4/17 13:24
 */
@Data
public class TargetReqDto {

    /**
     * 页码
     */
    private Integer pageNo = 1;

    /**
     * 分页大小
     */
    private Integer pageSize = 20;

    /**
     * 店铺id集合
     */
    private List<Integer> shopIdList;

    /**
     * 根据店铺id集合得到的店铺信息
     */
    private List<ShopAuth> shopAuthList;

    /**
     * 根据店铺id集合得到的站点id集合
     */
    private List<String> marketplaceIdList;

    /**
     * 站点最近的周排名日期
     */
    private List<String> abaRankDateList;

    /**
     * 站点id+|+周排名日期拼接
     */
    private List<String> marketplaceAbaRankDateList;

    /**
     * 投放类型
     */
    private String targetType;

    /**
     * 商品投放类型
     */
    private String productTargetType;

    /**
     * 商户ID
     */
    private Integer puid;

    /**
     * 子用户ID
     */
    private Integer uid;

    /**
     * redis中任务ID
     */
    private String uuid;

    /**
     * 广告组Id集合
     */
    private List<String> groupIdList;

    /**
     * 搜索字段 目前支持 name 关键字
     */
    private String searchField;

    /**
     * 搜索值
     */
    private String searchValue;

    /**
     * 状态集合
     */
    private List<String> statusList;

    /**
     * 匹配类型 EXACT->精确匹配  BROAD->广泛匹配  PHRASE->词组匹配
     */
    private String matchType;

    /**
     * 开始时间
     */
    @NotBlank(message = "开始日期不能为空")
    private String startDate;

    /**
     * 结束时间
     */
    @NotBlank(message = "结束日期不能为空")
    private String endDate;

    /**
     * 排序字段
     */
    private String orderField;

    /**
     * 升降序 desc asc
     */
    private String orderType;

    /**
     * 广告类型 sp sb sd
     */
    private String adType;

    /**
     * 广告标签id集合
     */
    private List<Long> adTagIdList;

    /**
     * 广告标签刷选后的投放id集合
     */
    private List<String> targetIds;

    /**
     * 服务状态集合
     */
    private List<String> servingStatusList;

    /**
     * 词根
     */
    private String wordRoot;

    /**
     * 词根刷选后的投放id集合
     */
    private List<String> wordRootKeywordIds;

    /**
     * 导出排序字段
     */
    private String exportSortField;

    /**
     * 冻结前num列
     */
    private Integer freezeNum;

    /**
     * 是否开启高级搜索
     */
    private Boolean useAdvanced;

    /**
     * 高级刷选参数
     */
    private AdvanceFilter advanceFilter;

    /**
     * advanceFilter 转化成集合
     */
    private List<AdvanceFilterVo> advanceFilterVoList = new ArrayList<>();

    /**
     * 广告组合ID集合
     */
    private List<String> portfolioIdList;

    /**
     * 广告活动id集合
     */
    private List<String> campaignIdList;

    /**
     * 搜索类型
     */
    private String searchType;

    /**
     * 是否环比
     */
    private Boolean isCompare;

    /**
     * 环比开始日期
     */
    private String compareStartDate;

    /**
     * 环比结束日期
     */
    private String compareEndDate;

    /**
     * pageSign
     */
    private String pageSign;

    /**
     * 查询是否与aba排名表联表查询
     */
    private boolean queryJoinSearchTermsRank;

    /**
     * 广告策略类型
     */
    private List<String> adStrategyTypeList;

    /**
     * 经过广告策略筛选后的广告投放id集合
     */
    private List<String> autoRuleIds;

    /**
     * 经过广告策略筛选后的广告组id集合
     */
    private List<String> autoRuleGroupIds;

    /**
     * 商品投放筛选类型
     */
    private String selectType;

    /**
     * 枚举
     */
    private TargetTypeEnum targetTypeEnum;

    /**
     * 多店铺判断是否需要汇率换算（多店铺币种不同时为true）
     */
    private Boolean changeRate;

    /**
     * 汇总接口店铺销售额
     */
    private BigDecimal shopSale;

    /**
     * top数量
     */
    private Integer top;

    /**
     * 词根类型（0:显示高频词; 1:仅显示高频单词; 2:仅显示高频词组）
     */
    private String wordFrequencyType;
}
