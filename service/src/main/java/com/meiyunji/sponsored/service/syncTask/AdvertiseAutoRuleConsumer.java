package com.meiyunji.sponsored.service.syncTask;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.sd.mode.ExpressionNested;
import com.amazon.advertising.spV3.enumeration.SpV3NegativeMatchTypeEnum;
import com.google.api.client.util.Lists;
import com.meiyunji.sellfox.aadras.types.enumeration.AddNegativeTargetType;
import com.meiyunji.sellfox.aadras.types.enumeration.AdvertiseRuleTaskResultCode;
import com.meiyunji.sellfox.aadras.types.enumeration.AdvertiseRuleTaskType;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordMessage;
import com.meiyunji.sellfox.aadras.types.schedule.entity.NegativeKeywordExtendEntity;
import com.meiyunji.sellfox.aadras.types.schedule.entity.NegativeTargetExtendEntity;
import com.meiyunji.sellfox.aadras.types.schedule.entity.NegativeTargetingExpression;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.autoRule.dao.AdvertiseAutoRuleExecuteRecordSequenceDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleExecuteRecordDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleTemplateDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleExecuteRecord;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleTemplate;
import com.meiyunji.sponsored.service.autoRule.vo.*;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.AutoRuleTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoTargetTypeEnum;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class AdvertiseAutoRuleConsumer {

    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAdvertiseAutoRuleTemplateDao advertiseAutoRuleTemplateDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IAdvertiseAutoRuleExecuteRecordDao advertiseAutoRuleExecuteRecordDao;
    @Autowired
    private AdvertiseAutoRuleExecuteRecordSequenceDao advertiseAutoRuleExecuteRecordSequenceDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;

    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    public void process(AdvertiseRuleTaskExecuteRecordMessage message) throws Exception {
        log.info("自动化规则计算服务 message: {}",message);
        try {
            List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>();
            List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>();
            List<AmazonSbAdNeKeyword> amazonSbAdNeKeywords = new ArrayList<>();
            //自动化规则回写执行后和待确认消息数据入表
            List<AdvertiseAutoRuleExecuteRecord> list = Lists.newArrayList();
            AdvertiseAutoRuleTemplate advertiseAutoRuleTemplate = advertiseAutoRuleTemplateDao.selectByPrimaryKey(message.getPuid(), message.getTemplateId());
            AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord = new AdvertiseAutoRuleExecuteRecord();
            advertiseAutoRuleExecuteRecord.setId(advertiseAutoRuleExecuteRecordSequenceDao.genId());
            advertiseAutoRuleExecuteRecord.setPuid(message.getPuid());
            advertiseAutoRuleExecuteRecord.setShopId(message.getShopId());
            advertiseAutoRuleExecuteRecord.setMarketplaceId(message.getMarketplaceId());
            advertiseAutoRuleExecuteRecord.setRuleType(message.getRuleType());
            advertiseAutoRuleExecuteRecord.setTaskId(message.getTaskId());
            advertiseAutoRuleExecuteRecord.setProfileId(message.getProfileId());
            advertiseAutoRuleExecuteRecord.setItemType(message.getItemType().name());
            advertiseAutoRuleExecuteRecord.setItemId(message.getItemId());
            advertiseAutoRuleExecuteRecord.setRecordId(message.getRecordId());
            advertiseAutoRuleExecuteRecord.setAdType(message.getAdType().name());
            if (message.getExecuteAt() != null) {
                advertiseAutoRuleExecuteRecord.setExecuteAt(convertDateFromSite(message.getExecuteAt(),message.getMarketplaceId()));
                advertiseAutoRuleExecuteRecord.setExecuteSiteDate(message.getExecuteAt());
            }
            if (message.getTriggerAt() != null) {
                advertiseAutoRuleExecuteRecord.setTriggerAt(message.getTriggerAt().plusHours(8));
            }
            advertiseAutoRuleExecuteRecord.setExecuteType(message.getExecuteType().name());
            advertiseAutoRuleExecuteRecord.setCode(message.getCode().name());
            List<AutoRuleJson> ruleJsonList = Lists.newArrayList();
            List<AdvertiseRuleTaskExecuteRecordMessage.Item> rules = message.getRule();
            List<RuleIndexJson> ruleIndexJsonList = Lists.newArrayList();
            AdvertiseRuleTaskExecuteRecordMessage.PerformOperation performOperation = message.getPerformOperation().get(0);
            rules.forEach(e -> {
                AutoRuleJson ruleJson = new AutoRuleJson();
                RuleIndexJson ruleIndexJson = new RuleIndexJson();
                ruleJson.setDay(e.getDay());
                ruleIndexJson.setDay(e.getDay());
                ruleIndexJson.setExcludeDay(e.getExcludeDay());
                ruleJson.setExcludeDay(e.getExcludeDay());
                ruleJson.setRuleIndex(e.getRuleIndex().name());
                ruleJson.setRuleOperator(e.getRuleOperator().name());
                ruleJson.setRuleStatisticalModeType(e.getRuleStatisticalMode().name());
                ruleJson.setRuleValue(e.getRuleValue());
                ruleIndexJson.setRuleIndex(e.getRuleIndex().name());
                ruleIndexJson.setRuleIndexValue(e.getTriggerValue());
                ruleIndexJsonList.add(ruleIndexJson);
                ruleJsonList.add(ruleJson);
            });
            List<PerformOperationJson> performOperationJsonList = Lists.newArrayList();
            PerformOperationJson performOperationJson = new PerformOperationJson();
            performOperationJson.setRuleAction(performOperation.getRuleAction().name());
            performOperationJson.setRuleAdjust(performOperation.getRuleAdjust().name());
            performOperationJson.setAdJustValue(performOperation.getAdJustValue());
            performOperationJson.setLimitValue(performOperation.getLimitValue());
            performOperationJsonList.add(performOperationJson);
            DataDetailJson dataDetailJson = new DataDetailJson();
            dataDetailJson.setOriginalValue(performOperation.getOriginalValue());
            dataDetailJson.setExecuteValue(performOperation.getExecuteValue());
            dataDetailJson.setRuleIndexList(ruleIndexJsonList);
            if (message.getNegativeTargetType() != null) {
                dataDetailJson.setNegativeTargetType(message.getNegativeTargetType().name());
            }
            advertiseAutoRuleExecuteRecord.setRule(JSONUtil.objectToJson(ruleJsonList));
            advertiseAutoRuleExecuteRecord.setPerformOperation(JSONUtil.objectToJson(performOperationJsonList));
            advertiseAutoRuleExecuteRecord.setDataDetail(JSONUtil.objectToJson(dataDetailJson));
            advertiseAutoRuleExecuteRecord.setTemplateId(message.getTemplateId());
            advertiseAutoRuleExecuteRecord.setTimeType("WEEKLY");
            advertiseAutoRuleExecuteRecord.setStartDate(message.getStart());
            advertiseAutoRuleExecuteRecord.setEndDate(message.getEnd());
            List<AdvertiseRuleTaskExecuteRecordMessage.TimeRuleItem> timeRuleItems = message.getTimeRule();
            if (CollectionUtils.isNotEmpty(timeRuleItems)) {
                List<TimeRuleJson> timeRuleJsonList = new ArrayList<>();
                timeRuleItems.forEach(t->{
                    TimeRuleJson timeRuleJson = new TimeRuleJson();
                    timeRuleJson.setSiteDate(t.getSiteDate());
                    timeRuleJson.setStartTimeSite(t.getStartTimeSite());
                    timeRuleJson.setEndTimeSite(t.getEndTimeSite());
                    timeRuleJsonList.add(timeRuleJson);
                });
                advertiseAutoRuleExecuteRecord.setTimeRule(JSONUtil.objectToJson(timeRuleJsonList));
            }
            if (advertiseAutoRuleTemplate == null) {
                advertiseAutoRuleExecuteRecord.setTemplateName("-");
            } else {
                advertiseAutoRuleExecuteRecord.setTemplateName(advertiseAutoRuleTemplate.getTemplateName());
            }
            advertiseAutoRuleExecuteRecord.setExecuteValue(performOperation.getExecuteValue());
            advertiseAutoRuleExecuteRecord.setOriginalValue(performOperation.getOriginalValue());
            advertiseAutoRuleExecuteRecord.setRuleActionType(performOperation.getRuleAction().name());
            advertiseAutoRuleExecuteRecord.setStateErrMsg(message.getStateErrMsg());
            if ("CAMPAIGN".equals(message.getItemType().name())) {
                AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(message.getPuid(), message.getShopId(), message.getItemId());
                advertiseAutoRuleExecuteRecord.setItemName(amazonAdCampaignAll.getName());
                advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdCampaignAll.getName());
                advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
                advertiseAutoRuleExecuteRecord.setItemOperateType("CAMPAIGN");
                advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdCampaignAll.getCampaignId());
            } else if ("KEYWORD".equals(message.getItemType().name())) {
                fillRecordInfosForKeyword(message, advertiseAutoRuleExecuteRecord);
            } else if ("TARGET".equals(message.getItemType().name())) {
                fillRecordInfosForTarget(message, advertiseAutoRuleExecuteRecord);
            } else if ("GROUP_SEARCH_QUERY".equals(message.getItemType().name())) {
                AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
                AmazonSbAdGroup amazonSbAdGroup = new AmazonSbAdGroup();
                if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                    amazonAdGroup = amazonAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), message.getItemId());
                    advertiseAutoRuleExecuteRecord.setItemName(amazonAdGroup.getName());
                    advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdGroup.getAdGroupId());
                    advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdGroup.getCampaignId());
                } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                    amazonSbAdGroup = amazonSbAdGroupDao.getByGroupId(message.getPuid(), message.getShopId(), message.getItemId());
                    advertiseAutoRuleExecuteRecord.setItemName(amazonSbAdGroup.getName());
                    advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdGroup.getAdGroupId());
                    advertiseAutoRuleExecuteRecord.setCampaignId(amazonSbAdGroup.getCampaignId());
                }
                advertiseAutoRuleExecuteRecord.setAdGroupId(message.getItemId());
                dataDetailJson.setNegativeTargetType(performOperation.getNegativeType().name());
                advertiseAutoRuleExecuteRecord.setItemOperateName(performOperation.getQuery());
                advertiseAutoRuleExecuteRecord.setItemOperateType("SEARCH_QUERY");
                advertiseAutoRuleExecuteRecord.setItemOperateId(message.getTargetId());
                advertiseAutoRuleExecuteRecord.setNegativeTargetType(message.getNegativeTargetType().name());
                if ("targeting".equals(message.getNegativeTargetType().name())) {
                    NegativeTargetExtendEntity negativeTargetExtendEntity = message.getNegativeTargetExtendEntity();
                    if (negativeTargetExtendEntity != null) {
                        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
                        amazonAdTargeting.setTargetId(negativeTargetExtendEntity.getTargetId());
                        amazonAdTargeting.setPuid(amazonAdGroup.getPuid());
                        amazonAdTargeting.setShopId(amazonAdGroup.getShopId());
                        amazonAdTargeting.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                        amazonAdTargeting.setAdGroupId(amazonAdGroup.getAdGroupId());
                        amazonAdTargeting.setDxmGroupId(amazonAdGroup.getId());
                        amazonAdTargeting.setCampaignId(amazonAdGroup.getCampaignId());
                        amazonAdTargeting.setProfileId(amazonAdGroup.getProfileId());
                        amazonAdTargeting.setExpressionType(Constants.MANUAL);
                        amazonAdTargeting.setState(negativeTargetExtendEntity.getState().toLowerCase());
                        amazonAdTargeting.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
                        amazonAdTargeting.setTargetingValue(performOperation.getQuery());
                        if (CollectionUtils.isNotEmpty(negativeTargetExtendEntity.getExpression())) {
                            List<Expression> expressions = new ArrayList<>();
                            List<NegativeTargetingExpression> negativeTargetingExpressions = negativeTargetExtendEntity.getExpression();
                            for (NegativeTargetingExpression negativeTargetingExpression : negativeTargetingExpressions) {
                                Expression expression = new Expression();
                                expression.setType(negativeTargetingExpression.getType());
                                expression.setValue(negativeTargetingExpression.getValue());
                                expressions.add(expression);
                            }
                            amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressions));
                        }
                        if (CollectionUtils.isNotEmpty(negativeTargetExtendEntity.getResolvedExpression())) {
                            List<Expression> resolveExpressions = new ArrayList<>();
                            List<NegativeTargetingExpression> negativeTargetingExpressions1 = negativeTargetExtendEntity.getResolvedExpression();
                            for (NegativeTargetingExpression negativeTargetingExpression : negativeTargetingExpressions1) {
                                Expression expression = new Expression();
                                expression.setType(negativeTargetingExpression.getType());
                                expression.setValue(negativeTargetingExpression.getValue());
                                resolveExpressions.add(expression);
                            }
                            amazonAdTargeting.setResolvedExpression(JSONUtil.objectToJson(resolveExpressions));
                        }
                        amazonAdTargetings.add(amazonAdTargeting);
                    }
                } else if ("keyword".equals(message.getNegativeTargetType().name())) {
                    NegativeKeywordExtendEntity negativeKeywordExtendEntity = message.getNegativeKeywordExtendEntity();
                    if (negativeKeywordExtendEntity != null) {
                        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
                            amazonAdKeyword.setPuid(message.getPuid());
                            amazonAdKeyword.setKeywordId(negativeKeywordExtendEntity.getKeywordId());
                            amazonAdKeyword.setShopId(message.getShopId());
                            amazonAdKeyword.setMarketplaceId(message.getMarketplaceId());
                            amazonAdKeyword.setProfileId(message.getProfileId());
                            amazonAdKeyword.setAdGroupId(negativeKeywordExtendEntity.getAdGroupId());
                            amazonAdKeyword.setDxmGroupId(amazonAdGroup.getId());
                            amazonAdKeyword.setCampaignId(amazonAdGroup.getCampaignId());
                            amazonAdKeyword.setKeywordText(negativeKeywordExtendEntity.getKeywordText());
                            amazonAdKeyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValueV3(negativeKeywordExtendEntity.getMatchType()).value());
                            amazonAdKeyword.setType(Constants.NEGATIVE);
                            amazonAdKeyword.setState(negativeKeywordExtendEntity.getState().toLowerCase());
                            amazonAdKeywords.add(amazonAdKeyword);
                        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
                            if (!amazonSbAdNeKeywordDao.exist(message.getPuid(), message.getShopId(), negativeKeywordExtendEntity.getKeywordId())) {
                                AmazonSbAdNeKeyword amazonSbAdNeKeyword = new AmazonSbAdNeKeyword();
                                amazonSbAdNeKeyword.setPuid(message.getPuid());
                                amazonSbAdNeKeyword.setKeywordId(negativeKeywordExtendEntity.getKeywordId());
                                amazonSbAdNeKeyword.setShopId(message.getShopId());
                                amazonSbAdNeKeyword.setMarketplaceId(message.getMarketplaceId());
                                amazonSbAdNeKeyword.setProfileId(message.getProfileId());
                                amazonSbAdNeKeyword.setAdGroupId(negativeKeywordExtendEntity.getAdGroupId());
                                amazonSbAdNeKeyword.setCampaignId(amazonSbAdGroup.getCampaignId());
                                amazonSbAdNeKeyword.setKeywordText(negativeKeywordExtendEntity.getKeywordText());
                                amazonSbAdNeKeyword.setMatchType(negativeKeywordExtendEntity.getMatchType());
                                amazonSbAdNeKeyword.setState(negativeKeywordExtendEntity.getState().toLowerCase());
                                amazonSbAdNeKeywords.add(amazonSbAdNeKeyword);
                            }
                        }
                    }
                }
            }
            list.add(advertiseAutoRuleExecuteRecord);
            advertiseAutoRuleExecuteRecordDao.batchInsert(message.getPuid(), list);
            //写入ES日志并同步最新数据到广告管理
            if (AdvertiseRuleTaskResultCode.SUCCESS.equals(message.getCode())) {
                try {
                    if (AdvertiseRuleTaskType.CAMPAIGN.equals(message.getItemType())) {
                        cpcCampaignService.autoRuleSyncCampaign(message.getPuid(), message.getShopId(), message.getAdType().getReportType(), message.getItemId());
                    } else if (AdvertiseRuleTaskType.KEYWORD.equals(message.getItemType())) {
                        cpcCampaignService.autoRuleSyncKeyword(message.getPuid(), message.getShopId(), message.getAdType().getReportType(), message.getItemId());
                    } else if (AdvertiseRuleTaskType.TARGET.equals(message.getItemType())) {
                        cpcCampaignService.autoRuleSyncTarget(message.getPuid(), message.getShopId(), message.getAdType().getReportType(), message.getItemId());
                    } else if (AdvertiseRuleTaskType.GROUP_SEARCH_QUERY.equals(message.getItemType())) {
                        if (AddNegativeTargetType.targeting.equals(message.getNegativeTargetType())) {
                            amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), amazonAdTargetings, Constants.TARGETING_TYPE_ASIN);
                        } else if (AddNegativeTargetType.keyword.equals(message.getNegativeTargetType())) {
                            if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(message.getPuid(), amazonAdKeywords, Constants.BIDDABLE);
                            }
                            if (CollectionUtils.isNotEmpty(amazonSbAdNeKeywords)) {
                                amazonSbAdNeKeywordDao.batchAdd(message.getPuid(),amazonSbAdNeKeywords);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("puid={} shopId={} itemType={} recordId={} 同步广告管理数据失败:", message.getPuid(), message.getShopId(), message.getItemType(),message.getRecordId(), e);
                }
                adManageOperationLogService.getAutoRuleTaskFinishedLog(advertiseAutoRuleExecuteRecord);
            } else if ("FAILURE".equals(message.getCode().name())) {
                adManageOperationLogService.getAutoRuleTaskFinishedLog(advertiseAutoRuleExecuteRecord);
            }
        } catch (Exception e) {
            log.error("Auto rule pulsar error:",e);
        }
    }

    private LocalDateTime convertDateFromSite(LocalDateTime localDateTime,String marketplaceId) {
        if (localDateTime == null) {
            return null;
        }
        MarketTimezoneAndCurrencyEnum cpcTime = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId);
        if(cpcTime == null){
            return null;
        }
        ZoneId srcZone = ZoneId.of(cpcTime.getTimezone());
        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime,srcZone,ZoneId.systemDefault());
        return localDateTime;
    }

    private void fillRecordInfosForKeyword(AdvertiseRuleTaskExecuteRecordMessage message,
                                           AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord) {
        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonAdKeyword amazonAdKeyword = amazonAdKeywordDaoRoutingService.getByKeywordId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdKeyword.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdKeyword.getAdGroupId());
        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSbAdKeyword amazonAdKeyword = amazonSbAdKeywordDao.getByKeywordId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdKeyword.getKeywordText());
            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdKeyword.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdKeyword.getAdGroupId());
        }

        advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
        advertiseAutoRuleExecuteRecord.setItemOperateType("TARGET");
        advertiseAutoRuleExecuteRecord.setItemType("TARGET");
        advertiseAutoRuleExecuteRecord.setTargetType("keywordTarget");
    }

    private void fillRecordInfosForTarget(AdvertiseRuleTaskExecuteRecordMessage message,
                                          AdvertiseAutoRuleExecuteRecord advertiseAutoRuleExecuteRecord) {
        if ("sp".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonAdTargeting amazonAdTargeting = amazonAdTargetDaoRoutingService.getByAdTargetId(message.getPuid(), message.getShopId(), message.getItemId());
            if ("auto".equals(amazonAdTargeting.getType())) {
                advertiseAutoRuleExecuteRecord.setItemName(AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue()));
                advertiseAutoRuleExecuteRecord.setItemOperateName(AutoTargetTypeEnum.getAutoTargetValue(amazonAdTargeting.getTargetingValue()));
                advertiseAutoRuleExecuteRecord.setTargetType("autoTarget");
            } else {
                advertiseAutoRuleExecuteRecord.setItemName(amazonAdTargeting.getTargetingValue());
                advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdTargeting.getTargetingValue());
                advertiseAutoRuleExecuteRecord.setTargetType("productTarget");
            }

            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdTargeting.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdTargeting.getAdGroupId());
        } else if ("sb".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSbAdTargeting amazonAdTargeting = amazonSbAdTargetingDao.getByTargetId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setTargetType("productTarget");

            advertiseAutoRuleExecuteRecord.setCampaignId(amazonAdTargeting.getCampaignId());
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdTargeting.getAdGroupId());
        } else if ("sd".equalsIgnoreCase(advertiseAutoRuleExecuteRecord.getAdType())) {
            AmazonSdAdTargeting amazonAdTargeting = amazonSdAdTargetingDao.getbyTargetId(message.getPuid(), message.getShopId(), message.getItemId());
            advertiseAutoRuleExecuteRecord.setItemName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setItemOperateName(amazonAdTargeting.getTargetText());
            advertiseAutoRuleExecuteRecord.setTargetType(AutoRuleTargetTypeEnum.productTarget.getTargetType());
            List<ExpressionNested> array = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), ExpressionNested.class);
            if (CollectionUtils.isNotEmpty(array)){
                // 设置为受众投放
                if (com.google.common.collect.Lists.newArrayList("views", "audience", "purchases", "contentCategorySameAs").contains(array.get(0).getType())){
                    advertiseAutoRuleExecuteRecord.setTargetType(AutoRuleTargetTypeEnum.audienceTarget.getTargetType());
                }
            }
            advertiseAutoRuleExecuteRecord.setAdGroupId(amazonAdTargeting.getAdGroupId());
            AmazonSdAdGroup sdGroup = amazonSdAdGroupDao.getByAdGroupId(message.getPuid(), message.getShopId(), amazonAdTargeting.getAdGroupId());
            //受众投放表没有活动id，需要从组上查
            if (Objects.nonNull(sdGroup)) {
                advertiseAutoRuleExecuteRecord.setCampaignId(sdGroup.getCampaignId());
            }
        }

        advertiseAutoRuleExecuteRecord.setItemOperateId(message.getItemId());
        advertiseAutoRuleExecuteRecord.setItemOperateType("TARGET");
    }
}
