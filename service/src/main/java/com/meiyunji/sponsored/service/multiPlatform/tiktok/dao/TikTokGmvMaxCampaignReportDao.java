package com.meiyunji.sponsored.service.multiPlatform.tiktok.dao;

import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokGmvMaxCampaignReport;
import com.tiktok.advertising.model.gmv_max.GmvMaxCampaignReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class TikTokGmvMaxCampaignReportDao extends BaseShardingDaoImpl<TikTokGmvMaxCampaignReport> {

    public void addOrUpdate(Integer puid, Integer shopId, String advertiserId, String storeId,
                            GmvMaxCampaignReport gmvMaxCampaignReport) {
        JdbcTemplate jdbcTemplate = getJdbcTemplate(puid);

        String statTimeDay = gmvMaxCampaignReport.getDimensions().getStatTimeDay();
        String campaignId = gmvMaxCampaignReport.getDimensions().getCampaignId();

        StringBuilder sb = new StringBuilder();
        sb.append(" INSERT INTO t_tiktok_gmv_max_campaign_report " +
                        "(puid, shop_id, advertiser_id, store_id, campaign_id, stat_time_day, " +
                        "cost, orders, cost_per_order, gross_revenue, roi, net_cost) ")
                .append(" VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ")
                .append(" ON DUPLICATE KEY UPDATE ")
                .append(" store_id = VALUES(store_id), ")
                .append(" cost = VALUES(cost), ")
                .append(" orders = VALUES(orders), ")
                .append(" cost_per_order = VALUES(cost_per_order), ")
                .append(" gross_revenue = VALUES(gross_revenue), ")
                .append(" roi = VALUES(roi), ")
                .append(" net_cost = VALUES(net_cost) ");

        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        argsList.add(storeId);
        argsList.add(campaignId);
        argsList.add(statTimeDay);
        argsList.add(gmvMaxCampaignReport.getMetrics().getCost());
        argsList.add(gmvMaxCampaignReport.getMetrics().getOrders());
        argsList.add(gmvMaxCampaignReport.getMetrics().getCostPerOrder());
        argsList.add(gmvMaxCampaignReport.getMetrics().getGrossRevenue());
        argsList.add(gmvMaxCampaignReport.getMetrics().getRoi());
        argsList.add(gmvMaxCampaignReport.getMetrics().getNetCost());

        update(jdbcTemplate, sb.toString(), argsList.toArray());
    }

    public int batchAddOrUpdate(Integer puid, Integer shopId, String advertiserId, String storeId,
                            List<GmvMaxCampaignReport> list) {
        StringBuilder sb = new StringBuilder();
        sb.append(" INSERT INTO ").append(this.getJdbcHelper().getTable())
                .append("(puid, shop_id, advertiser_id, store_id, campaign_id, stat_time_day, ")
                .append("cost, orders, cost_per_order, gross_revenue, roi, net_cost) VALUES ");

        List<Object> argsList = new ArrayList<>();
        for (GmvMaxCampaignReport gmvMaxCampaignReport : list) {
            sb.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?),");
            argsList.add(puid);
            argsList.add(shopId);
            argsList.add(advertiserId);
            argsList.add(storeId);
            argsList.add(gmvMaxCampaignReport.getDimensions().getCampaignId());
            argsList.add(gmvMaxCampaignReport.getDimensions().getStatTimeDay());
            argsList.add(gmvMaxCampaignReport.getMetrics().getCost());
            argsList.add(gmvMaxCampaignReport.getMetrics().getOrders());
            argsList.add(gmvMaxCampaignReport.getMetrics().getCostPerOrder());
            argsList.add(gmvMaxCampaignReport.getMetrics().getGrossRevenue());
            argsList.add(gmvMaxCampaignReport.getMetrics().getRoi());
            argsList.add(gmvMaxCampaignReport.getMetrics().getNetCost());
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(" ON DUPLICATE KEY UPDATE ")
                .append(" store_id = VALUES(store_id), ")
                .append(" cost = VALUES(cost), ")
                .append(" orders = VALUES(orders), ")
                .append(" cost_per_order = VALUES(cost_per_order), ")
                .append(" gross_revenue = VALUES(gross_revenue), ")
                .append(" roi = VALUES(roi), ")
                .append(" net_cost = VALUES(net_cost) ");
        return getJdbcTemplate(puid).update(sb.toString(), argsList.toArray());
    }

}
