package com.meiyunji.sponsored.service.batchCreate.task;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.UpdateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.entity.PutTargetEntityV3;
import com.amazon.advertising.spV3.targeting.entity.TargetExpression;
import com.amazon.advertising.spV3.targeting.entity.TargetSuccessResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchTargetingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.BatchCreateReturnDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchTargeting;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdTargetingTypeBo;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcTargetingApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 自动投放批量处理任务
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-27  16:20
 */
@Component
@Slf4j
public class AutoTargetCallAmazonApiTask extends AbstractCallAmazonApiTask {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdBatchTargetingDao amazonAdBatchTargetingDao;
    @Autowired
    private CpcTargetingApiService cpcTargetingApiService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    @Autowired
    private TaskStatusHelper taskStatusHelper;

    @Autowired
    private IDorisService dorisService;

    public void call(CallAmazonApiTaskResultDto resultDto) {
        log.info("sp batch create, create auto target, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            return ;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        String traceId = resultDto.getTraceDto().getTraceId();
        //1、获取redisson分布式锁，锁标识为："sp_batch_group_"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_AUTO_TARGETING.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("auto target task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }
        if (!b) {
            log.info("auto target task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }

        List<AmazonAdBatchTargeting> amazonAdBatchTargetings = null;
        try {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //根据传入的广告组id获取需要创建的自动投放数据
            List<Long> ids = resultDto.getSuccessIdList();

            if (resultDto.isCurrentLevel()) {
                amazonAdBatchTargetings = amazonAdBatchTargetingDao.listByIdList(puid, shopId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()), Constants.AUTO);
            } else {
                amazonAdBatchTargetings = amazonAdBatchTargetingDao.listByGroupIdList(puid, shopId, taskId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()), Constants.AUTO);
            }
            if (CollectionUtils.isEmpty(amazonAdBatchTargetings)) {
                return ;
            }
            //2、查询出task下成功的自动投放广告组
            Set<String> groupIdSet = new HashSet<>();
            Set<String> campaignIdSet = new HashSet<>();
            for (AmazonAdBatchTargeting target : amazonAdBatchTargetings) {
                groupIdSet.add(target.getAmazonAdGroupId());
                campaignIdSet.add(target.getAmazonCampaignId());
            }

            //3、同步广告组的自动投放数据
            cpcTargetingApiService.syncTargetings(shop, String.join(",", campaignIdSet), String.join(",", groupIdSet), null);

            //4、获取投放id，并修改状态为执行成功
            List<AmazonAdTargetingTypeBo> amazonAdTargetings = amazonAdTargetDaoRoutingService.listTargetTypeBoByGroupIds(puid, shopId, new ArrayList<>(groupIdSet));
            Map<String, String> targetIdMap = amazonAdTargetings.stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "#" + e.getExpression(), AmazonAdTargetingTypeBo::getTargetId));

            //从db中获取state状态为pause的自动投放，即需要更新自动投放状态
            List<AmazonAdBatchTargeting> autoTargetingPauseList = amazonAdBatchTargetings.stream().filter(t -> {
                String targetingId;
                List<TargetExpression> targetExpressions = Lists.newArrayList();
                List<Expression> expressions = JSONUtil.jsonToArray(t.getExpression(), Expression.class);
                if (expressions != null) {
                    for (Expression expression : expressions){
                        TargetExpression e = new TargetExpression();
                        e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                        e.setValue(expression.getValue());
                        targetExpressions.add(e);
                    }
                }
                if ((targetingId = targetIdMap.get(t.getAmazonAdGroupId() + "#" + JSONUtil.objectToJson(targetExpressions))) == null) {
                    return false;
                }
                t.setAmazonAdTargetId(targetingId);
                return Constants.AUTO_TARGETING_STATE_PAUSE.equals(t.getState());
            }).collect(Collectors.toList());
            List<List<AmazonAdBatchTargeting>> autoTargetParti = Lists.partition(autoTargetingPauseList, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (int i = 0; i < autoTargetParti.size(); i++) {
                updateAutoTargetExpression(traceId,  puid, taskId, shop,
                        autoTargetParti.get(i), i * SpBatchConstants.REQUEST_PARTITION_SIZE, resultDto.getLoginIp());
            }

            //写入doris
            saveDoris(puid, shopId, new ArrayList<>(targetIdMap.values()));

            //分片执行,最后批量更新投放状态
            List<List<AmazonAdBatchTargeting>> targetPartition = Lists.partition(amazonAdBatchTargetings, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (List<AmazonAdBatchTargeting> targets : targetPartition) {
                Map<Long, String> idTargetIdMap = new HashMap<>();
                for (AmazonAdBatchTargeting amazonAdBatchTargeting : targets) {
                    //转换为v3的expression兼容
                    List<TargetExpression> targetExpressions = new ArrayList<>();
                    List<Expression> expressions = JSONUtil.jsonToArray(amazonAdBatchTargeting.getExpression(), Expression.class);
                    if (expressions != null) {
                        for (Expression expression : expressions){
                            TargetExpression e = new TargetExpression();
                            e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                            e.setValue(expression.getValue());
                            targetExpressions.add(e);
                        }
                    }
                    idTargetIdMap.put(amazonAdBatchTargeting.getId(), targetIdMap.getOrDefault(amazonAdBatchTargeting.getAmazonAdGroupId() + "#" + JSONUtil.objectToJson(targetExpressions), ""));
                }
                amazonAdBatchTargetingDao.updateSuccTaskStatusByIdList(puid, idTargetIdMap);
            }
        } catch (Exception e) {
            log.error(String.format("sp batch create auto target, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //有异常将本层级全部改为重试状态
            if (CollectionUtils.isNotEmpty(amazonAdBatchTargetings)) {
                List<Long> idList = amazonAdBatchTargetings.stream().map(AmazonAdBatchTargeting::getId).collect(Collectors.toList());
                amazonAdBatchTargetingDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //7、释放redission锁
            lock.unlock();
        }
    }


    public void callStopAutoTarget(CallAmazonApiTaskResultDto resultDto) {
        log.info("sp batch create, create stop auto target, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            return ;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        String traceId = resultDto.getTraceDto().getTraceId();
        //1、获取redisson分布式锁，锁标识为："sp_batch_group_"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_AUTO_TARGETING.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("auto target task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }
        if (!b) {
            log.info("auto target task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }

        List<AmazonAdBatchTargeting> amazonAdBatchTargetings = new ArrayList<>();
        try {

            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //根据传入的广告组id获取需要创建的自动投放数据
            List<Long> ids = resultDto.getSuccessIdList();

            if (resultDto.isCurrentLevel()) {
                amazonAdBatchTargetings = amazonAdBatchTargetingDao.listByIdList(puid, shopId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode(), SpBatchCreateAdLevelStatusEnum.STOP.getCode()), Constants.AUTO);
            } else {
                amazonAdBatchTargetings = amazonAdBatchTargetingDao.listByGroupIdList(puid, shopId, taskId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode(), SpBatchCreateAdLevelStatusEnum.STOP.getCode()), Constants.AUTO);
            }
            if (CollectionUtils.isEmpty(amazonAdBatchTargetings)) {
                return ;
            }

            //2、查询出task下成功的自动投放广告组
            Set<String> groupIdSet = new HashSet<>();
            Set<String> campaignIdSet = new HashSet<>();
            for (AmazonAdBatchTargeting target : amazonAdBatchTargetings) {
                groupIdSet.add(target.getAmazonAdGroupId());
                campaignIdSet.add(target.getAmazonCampaignId());
            }
            //3、同步广告组的自动投放数据
            cpcTargetingApiService.syncTargetings(shop, String.join(",", campaignIdSet), String.join(",", groupIdSet), null);
            //4、获取投放id，并修改状态为执行成功
            List<AmazonAdTargetingTypeBo> amazonAdTargetings = amazonAdTargetDaoRoutingService.listTargetTypeBoByGroupIds(puid, shopId, new ArrayList<>(groupIdSet));
            Map<String, String> targetIdMap = amazonAdTargetings.stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "#" + e.getExpression(), AmazonAdTargetingTypeBo::getTargetId));
            //写入doris
            saveDoris(puid, shopId, new ArrayList<>(targetIdMap.values()));
            //分片执行
            List<List<AmazonAdBatchTargeting>> targetPartition = Lists.partition(amazonAdBatchTargetings, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (List<AmazonAdBatchTargeting> targets : targetPartition) {
                Map<Long, String> idTargetIdMap = new HashMap<>();
                for (AmazonAdBatchTargeting amazonAdBatchTargeting : targets) {
                    //转换为v3的expression兼容
                    List<TargetExpression> targetExpressions = new ArrayList<>();
                    List<Expression> expressions = JSONUtil.jsonToArray(amazonAdBatchTargeting.getExpression(), Expression.class);
                    if (expressions != null) {
                        for (Expression expression : expressions){
                            TargetExpression e = new TargetExpression();
                            e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                            e.setValue(expression.getValue());
                            targetExpressions.add(e);
                        }
                    }
                    idTargetIdMap.put(amazonAdBatchTargeting.getId(), targetIdMap.getOrDefault(amazonAdBatchTargeting.getAmazonAdGroupId() + "#" + JSONUtil.objectToJson(targetExpressions), ""));
                }
                amazonAdBatchTargetingDao.updateSuccTaskStatusByIdList(puid, idTargetIdMap);
            }
        } catch (Exception e) {
            log.error(String.format("sp batch create auto target, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //有异常将本层级全部改为重试状态
            if (CollectionUtils.isNotEmpty(amazonAdBatchTargetings)) {
                List<Long> idList = amazonAdBatchTargetings.stream().map(AmazonAdBatchTargeting::getId).collect(Collectors.toList());
                amazonAdBatchTargetingDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //7、释放redission锁
            lock.unlock();
        }
    }

    private void updateAutoTargetExpression(String traceId, Integer puid,
                                            Long taskId, ShopAuth shop,
                                            List<AmazonAdBatchTargeting> targets, int offset,
                                            String loginIp) {
        BatchCreateReturnDto dto = new BatchCreateReturnDto();
        Map<Integer, String> succMap = new HashMap<>();
        Map<Integer, String> errMap = new HashMap<>();
        List<Integer> retryList = new ArrayList<>();
        dto.setSuccMap(succMap);
        dto.setErrMap(errMap);
        dto.setRetryList(retryList);
        List<PutTargetEntityV3> targetEntityV3List = Lists.newArrayList();
        for (AmazonAdBatchTargeting amazonAdBatchTargeting : targets) {
            targetEntityV3List.add(this.batchTarget2TargetEntityV3(amazonAdBatchTargeting));
        }
        //批量将这些数据提交给亚马逊。
        String profileId = targets.get(0).getProfileId();
        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance().putTargets(shop.getAdAccessToken(), profileId,
                shop.getMarketplaceId(), targetEntityV3List, Boolean.TRUE);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            log.info("sp batch create auto target, response is null or http code not 200, batch traceId: {}, taskId: {}", traceId, taskId);
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance().putTargets(shop.getAdAccessToken(), profileId,
                    shop.getMarketplaceId(), targetEntityV3List, Boolean.TRUE);
        }

        //异常返回，全部加入到重试列表
        if (response == null || response.getData() == null || response.getStatusCode() == -1 || response.getStatusCode() == 429 || response.getStatusCode() == 500) {
            log.info("sp batch create, create auto target response none data, fail all, batch traceId: {}, taskId: {}", traceId, taskId);
            for (int i = 0; i < targets.size(); i++) {
                retryList.add(i + offset);
            }
        } else {
            //活动提交给亚马逊之后，需要处理其响应，将成功的和失败的分成2组。
            String errMsg = "更新自动广告投放失败";
            if (response.getData() != null && response.getData().getTargetingClauses() != null) {
                List<TargetSuccessResultV3> success = response.getData().getTargetingClauses().getSuccess();
                List<ErrorItemResultV3> errorItemResultV3s = response.getData().getTargetingClauses().getError();
                //成功的商品投放，批量和单个商品投放都设置投放id
                for (TargetSuccessResultV3 targetSuccessResultV3 : success) {
                    succMap.put(offset + targetSuccessResultV3.getIndex(), targetSuccessResultV3.getTargetId());
                }
                //失败的商品投放，批量和单个商品投放都设置错误信息
                for (ErrorItemResultV3 targetResult : errorItemResultV3s) {
                    errMap.put(offset + targetResult.getIndex(), targetResult.getErrors().get(0).getErrorMessage());
                }
            } else if (response.getError() != null) {
                if (StringUtils.isNotBlank(response.getError().getMessage())) {
                    errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
                } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                    errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
                }
                for (int i = 0; i < targets.size(); i++) {
                    errMap.put(offset + i, errMsg);
                }
            }
        }
        //成功处理
        if (succMap.size() > 0) {
            List<AmazonAdTargeting> succTargetings = new ArrayList<>();
            for (Map.Entry<Integer, String> succMapEntry : succMap.entrySet()) {
                AmazonAdBatchTargeting amazonAdBatchTargeting = targets.get(succMapEntry.getKey());
                //构建日志的po
                AmazonAdTargeting amazonAdTargeting = this.batchTarget2Target(amazonAdBatchTargeting);
                amazonAdTargeting.setTargetId(succMapEntry.getValue());
                succTargetings.add(amazonAdTargeting);
            }
            //记录成功日志
            this.collectSuccLog(succTargetings, loginIp);
            //同步投放表达式resolvedExpression
            String campaignIds = succTargetings.stream().map(AmazonAdTargeting::getCampaignId).collect(Collectors.joining(","));
            String groupIds = succTargetings.stream().map(AmazonAdTargeting::getAdGroupId).collect(Collectors.joining(","));
            String targetIds = succTargetings.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.joining(","));
            cpcTargetingApiService.syncTargetings(shop, campaignIds, groupIds, targetIds);
            log.info("sp batch create, update auto-target success, batch traceId: {}, taskId:{}", traceId, taskId);
        }

        //失败处理
        List<AmazonAdTargeting> errTargetings = new ArrayList<>();
        if (errMap.size() > 0) {
            Map<Long, String> idMsgMap = new HashMap<>();
            for (Map.Entry<Integer, String> errMapEntry : errMap.entrySet()) {
                AmazonAdBatchTargeting amazonAdBatchTargeting = targets.get(errMapEntry.getKey());
                idMsgMap.put(amazonAdBatchTargeting.getId(), errMapEntry.getValue());
                //构建日志po
                AmazonAdTargeting amazonAdTargeting = this.batchTarget2Target(amazonAdBatchTargeting);
                amazonAdTargeting.setError(errMapEntry.getValue());
                errTargetings.add(amazonAdTargeting);
            }
            //修改当前层级任务状态为失败
            taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_AUTO_TARGETING,  null, true);
            log.info("sp batch create, update auto-target error, batch traceId: {}, taskId: {}, idMsgMap: {}, taskId:{}", traceId, taskId);
        }

        //重试处理
        if (retryList.size() > 0) {
            List<Long> retryIdList = new ArrayList<>();
            Map<Long, String> errIdMap = new HashMap<>();
            for (Integer index : retryList) {
                AmazonAdBatchTargeting amazonAdBatchTargeting = targets.get(index);
                AmazonAdTargeting amazonAdTargeting = this.batchTarget2Target(amazonAdBatchTargeting);
                //若重试超过三次则直接置为失败
                if (amazonAdBatchTargeting.getExecuteCount() + 1 >= SpBatchConstants.EXECUTE_COUNT_LIMIT) {
                    errIdMap.put(amazonAdBatchTargeting.getId(), SpBatchConstants.RETRY_ERROR_MSG);
                    amazonAdTargeting.setError(SpBatchConstants.RETRY_ERROR_MSG);
                } else {
                    retryIdList.add(amazonAdBatchTargeting.getId());
                    amazonAdTargeting.setError(SpBatchConstants.RETRY_MSG);
                }
                errTargetings.add(amazonAdTargeting);
            }
            if (CollectionUtils.isNotEmpty(retryIdList)) {
                amazonAdBatchTargetingDao.updateRetryTaskStatusByIdList(puid, retryIdList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
                log.info("sp batch create, auto-target retry, batch traceId: {}, taskId: {}, retryIdList: {}", traceId, taskId, StringUtils.join(retryIdList, ","));
            }
            if (!errIdMap.isEmpty()) {
                amazonAdBatchTargetingDao.updateErrTaskStatusByIdList(puid, errIdMap, true);
                log.info("sp batch create, auto-target error, batch traceId: {}, taskId: {}, errIdMap: {}", traceId, taskId, errIdMap);
            }
        }
        //记录失败日志
        this.collectFailLog(errTargetings, loginIp);
    }

    private PutTargetEntityV3 batchTarget2TargetEntityV3(AmazonAdBatchTargeting amazonAdBatchTargeting) {
        PutTargetEntityV3 targetingClause = new PutTargetEntityV3();
        if (StringUtils.isEmpty(amazonAdBatchTargeting.getAmazonAdTargetId())) {
            throw new ServiceException("对应的投放ID不能为空");
        }
        targetingClause.setTargetId(amazonAdBatchTargeting.getAmazonAdTargetId());
        targetingClause.setExpressionType(amazonAdBatchTargeting.getExpressionType());
        targetingClause.setState(SpV3StateEnum.PAUSED.valueV3());
        targetingClause.setBid(amazonAdBatchTargeting.getBid() != null ? amazonAdBatchTargeting.getBid().doubleValue() : null);
        if (StringUtils.isNotBlank(amazonAdBatchTargeting.getExpression())) {
            List<Expression> expressions = JSONUtil.jsonToArray(amazonAdBatchTargeting.getExpression(), Expression.class);
            if (expressions != null) {
                List<TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions){
                    TargetExpression e = new TargetExpression();
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
        }
        return targetingClause;
    }

    private AmazonAdTargeting batchTarget2Target(AmazonAdBatchTargeting amazonAdBatchTargeting) {
        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
        amazonAdTargeting.setId(amazonAdBatchTargeting.getId());
        amazonAdTargeting.setPuid(amazonAdBatchTargeting.getPuid());
        amazonAdTargeting.setShopId(amazonAdBatchTargeting.getShopId());
        amazonAdTargeting.setMarketplaceId(amazonAdBatchTargeting.getMarketplaceId());
        amazonAdTargeting.setAdGroupId(amazonAdBatchTargeting.getAmazonAdGroupId());
        amazonAdTargeting.setCampaignId(amazonAdBatchTargeting.getAmazonCampaignId());
        amazonAdTargeting.setProfileId(amazonAdBatchTargeting.getProfileId());
        amazonAdTargeting.setExpressionType(Constants.AUTO);
        amazonAdTargeting.setState(CpcStatusEnum.paused.name());
        amazonAdTargeting.setType(amazonAdBatchTargeting.getType());
        amazonAdTargeting.setTargetingValue(amazonAdBatchTargeting.getTargetingValue());
        amazonAdTargeting.setBid(amazonAdBatchTargeting.getBid() != null ? amazonAdBatchTargeting.getBid().doubleValue() : null);
        amazonAdTargeting.setCreateId(amazonAdBatchTargeting.getCreateId());

        if (amazonAdBatchTargeting.getSuggested() != null) {
            amazonAdTargeting.setSuggested(amazonAdBatchTargeting.getSuggested());
        }
        if (amazonAdBatchTargeting.getRangeStart() != null) {
            amazonAdTargeting.setRangeStart(amazonAdBatchTargeting.getRangeStart());
        }
        if (amazonAdBatchTargeting.getRangeEnd() != null) {
            amazonAdTargeting.setRangeEnd(amazonAdBatchTargeting.getRangeEnd());
        }
        amazonAdTargeting.setExpression(amazonAdBatchTargeting.getExpression());
        return amazonAdTargeting;
    }

    private void collectSuccLog(List<AmazonAdTargeting> amazonAdTargetings, String loginIp) {
        List<AdManageOperationLog> targetLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ;
        }
        for (AmazonAdTargeting taraget: amazonAdTargetings) {
            AdManageOperationLog targetLog = adManageOperationLogService.getTargetsLog(null, taraget);
            targetLog.setIp(loginIp);
            targetLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            targetLogs.add(targetLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(targetLogs);
    }

    /**
     * 构建失败的日志（异常为广告组维度）
     */
    private void collectFailLog(List<AmazonAdTargeting> amazonAdTargetings, String loginIp) {
        List<AdManageOperationLog> targetLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ;
        }
        Map<String, List<AmazonAdTargeting>> targetGroupMap = amazonAdTargetings.stream().collect(Collectors.groupingBy(AmazonAdTargeting::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdTargeting>> entry : targetGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdTargeting> targetingList = entry.getValue();
            for (AmazonAdTargeting targeting : targetingList) {
                msgSb.append("targetValue:").append(targeting.getTargetingValue()).append(",desc:").append(targeting.getError()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建商品投放失败";
        for (AmazonAdTargeting target: amazonAdTargetings) {
            AdManageOperationLog targetLog = adManageOperationLogService.getTargetsLog(null, target);
            targetLog.setIp(loginIp);
            targetLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            targetLog.setResultInfo(groupMsgMap.getOrDefault(target.getAdGroupId(), err));
            targetLogs.add(targetLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(targetLogs);
    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param targetIdList
     */
    public void saveDoris(Integer puid, Integer shopId, List<String> targetIdList) {
        try {
            Date date = new Date();
            List<AmazonAdTargeting> list = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, shopId, targetIdList);
            List<OdsAmazonAdTargeting> collect = list.stream().map(x -> {
                OdsAmazonAdTargeting odsAmazonAdTargeting = new OdsAmazonAdTargeting();
                BeanUtils.copyProperties(x, odsAmazonAdTargeting);
                odsAmazonAdTargeting.setCreateTime(date);
                odsAmazonAdTargeting.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdTargeting.getState())) {
                    odsAmazonAdTargeting.setState(odsAmazonAdTargeting.getState().toLowerCase());
                }
                return odsAmazonAdTargeting;
            }).collect(Collectors.toList());
            dorisService.saveDorisByRoutineLoad(null, collect);
        } catch (Exception e) {
            log.error("sp targeting save doris error", e);
        }
    }
}
