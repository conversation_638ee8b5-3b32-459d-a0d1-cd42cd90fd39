package com.meiyunji.sponsored.service.strategyTask.vo;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import lombok.Data;

import java.util.List;

/**
 * @author: chenzimeng
 * @email: <EMAIL>
 * @date: 2024-01-15  16:39
 */
@Data
public class ProcessTaskParam {
    private Integer puid;
    private Integer shopId;
    private Long oldTemplateId;
    private Long newTemplateId;
    private Integer taskAction;
    private Integer operation;
    private String loginIp;
    private Integer uid;
    private String status;
    private String addWayType;
    private Long preTaskId;
    private List<Long> excludedStatusIdList;
    private List<StatusVo> statusVoList;
    private List<String> portfolioIds;
    private String searchValue;
    private String itemType;
    private List<String> adTypeList;
    private List<String> targetTypeList;
    private String campaignName;
    private String groupName;
    private List<String> campaignIds;
    private List<String> groupIds;
    private String state;
    private List<String> targetIdList;
    private String matchType;
    private String traceId;
    private String adType;
    private List<String> servingStatusList;
    private String adGroupScreen;
    private List<String> itemIdList;

    private String searchField;
}
