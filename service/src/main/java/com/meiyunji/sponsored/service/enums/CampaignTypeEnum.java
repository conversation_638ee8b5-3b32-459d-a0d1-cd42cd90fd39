package com.meiyunji.sponsored.service.enums;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: wade
 * @date: 2021/9/8 17:18
 * @describe:
 */
public enum CampaignTypeEnum {

    /**
     * 广告活动 推广类型
     * */
    sp("sp","产品推广"),

    sb("sb","品牌推广"),

    sd("sd","展示型推广");

    private String campaignType;

    private String campaignValue;

    public static String getCampaignValue(String campaignType){
        CampaignTypeEnum[] values = values();
        for (CampaignTypeEnum value : values) {
            if(value.getCampaignType().equals(campaignType)){
                return value.getCampaignValue();
            }
        }
        return "";
    }

    public static List<String> campaignTypeList = Arrays.stream(CampaignTypeEnum.values()).map(CampaignTypeEnum::getCampaignType).collect(Collectors.toList());

    CampaignTypeEnum(String campaignType, String campaignValue) {
        this.campaignType = campaignType;
        this.campaignValue = campaignValue;
    }

    public String getCampaignType() {
        return campaignType;
    }


    public String getCampaignValue() {
        return campaignValue;
    }

}

