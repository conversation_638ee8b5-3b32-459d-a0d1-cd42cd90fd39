package com.meiyunji.sponsored.service.multiPlatform.shop.service.impl;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.MultiPlatformTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.TikTokAdAuthEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.WalmartAdAuthEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import com.meiyunji.sponsored.service.multiPlatform.shop.service.IMultiPlatformShopService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2025/2/24 11:33
 * @describe:
 */
@Service
public class MultiPlatformShopServiceImpl implements IMultiPlatformShopService {

    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;
    @Override
    public List<MultiPlatformShopAuth> getWalmartAdShopList(Integer puid) {
        List<MultiPlatformShopAuth> shopList = multiPlatformShopAuthDao.listByPuidAndPlatform(puid, MultiPlatformTypeEnum.WALMART.getCode());
        return shopList.stream()
                .filter(shop -> Objects.equals(WalmartAdAuthEnum.AD_AUTH.getStatus(), shop.getAdStatus()))
                .collect(Collectors.toList());
    }
    @Override
    public MultiPlatformShopAuth getTiktokAdShopAuth(Integer puid, Integer shopId) {
        return multiPlatformShopAuthDao.listByIdAndPlatform(puid, shopId, MultiPlatformTypeEnum.TIKTOK.getCode());
    }

    @Override
    public List<Integer> getTiktokAdShopAuthList(Integer puid, List<Integer> shopIdList) {
        return multiPlatformShopAuthDao.listAdAuthIdByPuidAndPlatform(puid, shopIdList,
                MultiPlatformTypeEnum.TIKTOK.getCode(), Arrays.asList(TikTokAdAuthEnum.AD_AUTH.getStatus(), TikTokAdAuthEnum.AD_EXIT_FAILURE.getStatus()));
    }

    @Override
    public List<Integer> getAdAuthShopIdList(Integer puid, List<Integer> idList, String multiPlatformType) {
        return multiPlatformShopAuthDao.listAdAuthIdByPuidAndPlatform(puid, idList, multiPlatformType);
    }



    @Override
    public MultiPlatformShopAuth getMultiPlatformShopById(Integer puid, Integer shopId) {
        MultiPlatformShopAuth multiPlatformShopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        return multiPlatformShopAuth;
    }

    @Override
    public Map<Integer, String> getShopName(Integer puid, List<Integer> shopIds) {
        Map<Integer, String> nameMap = new HashMap<>();
        List<MultiPlatformShopAuth> multiPlatformNameList = multiPlatformShopAuthDao.listNameByIds(puid, shopIds);
        for (MultiPlatformShopAuth multiPlatformShopAuth : multiPlatformNameList) {
            nameMap.put(multiPlatformShopAuth.getId(), multiPlatformShopAuth.getName());
        }
        return nameMap;
    }

    @Override
    public MultiPlatformShopAuth checkAdShopAuth(Integer puid, Integer shopId) throws ServiceException {
        MultiPlatformShopAuth shopAuth = multiPlatformShopAuthDao.getByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            throw new ServiceException("未找到授权店铺账户！");
        }
        if (!Objects.equals(WalmartAdAuthEnum.AD_AUTH.getStatus(), shopAuth.getAdStatus())) {
            throw new ServiceException("没有该店铺权限！");
        }
        return shopAuth;
    }

    @Override
        public List<Integer> getWalmartAdAuthByShopIdList(Integer puid, List<Integer> shopIdList) {
        List<MultiPlatformShopAuth> walmartShopList = this.getWalmartAdShopList(puid);
        return walmartShopList.stream()
                .map(MultiPlatformShopAuth::getId)
                .filter(Objects::nonNull)
                .filter(shopIdList::contains)
                .collect(Collectors.toList());
    }


    @Override
    public List<MultiPlatformShopAuth> listAdAuthByPuidAndPlatform(Integer puid, List<Integer> shopId, String multiPlatformType) {
        return multiPlatformShopAuthDao.listAdAuthByPuidAndPlatform(puid, shopId, multiPlatformType);
    }
}
