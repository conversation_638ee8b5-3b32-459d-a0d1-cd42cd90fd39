package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_tiktok_sync_error_record")
public class TikTokSyncErrorRecord extends BasePo {

    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "task_type")
    private String taskType;

    @DbColumn(value = "advertiser_id")
    private String advertiserId;

    @DbColumn(value = "error_message")
    private String errorMessage;

    @DbColumn(value = "request_params")
    private String requestParams;

    @DbColumn(value = "status")
    private Integer status;

    @DbColumn(value = "retry_count")
    private Integer retryCount;

}
