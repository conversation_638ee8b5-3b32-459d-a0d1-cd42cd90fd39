package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.common.base.BaseQo;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: wade
 * @date: 2021/8/20 10:38
 * @describe: sp广告活动-否定商品投放查询参数
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class CampaignNeTargetingSpParam extends BaseQo {

    private Integer uid;

    private String uuid;

    @ApiModelProperty(value = "店铺ID",required = true)
    private Integer shopId;

   @ApiModelProperty(value = "puid",required = true)
    private Integer puid;

    /**
     * 活动id
     */
    @ApiModelProperty("活动id")
    private String campaignId;

    /**
     * asin
     */
    @ApiModelProperty("asin")
    private String asin;

    @ApiModelProperty("搜索字段")
    private String searchField;

    @ApiModelProperty("搜索字段")
    private String searchValue;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("活动状态")
    private String state;

    @ApiModelProperty("服务状态")
    private String servingStatus;

   @ApiModelProperty("广告组合ID")
   private String portfolioId;

   @ApiModelProperty(value = "广告组合下的活动id")
   private List<String> campaignIdList;

    /**
     * 否定投放报告筛选dto
     */
    private NeTargetReportFilterDto neTargetReportFilterDto;

}
