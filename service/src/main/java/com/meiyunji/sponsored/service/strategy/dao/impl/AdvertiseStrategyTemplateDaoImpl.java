package com.meiyunji.sponsored.service.strategy.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.autoRule.enums.AutoRuleEnableStatusEnum;
import com.meiyunji.sponsored.service.log.enums.ItemTypeEnum;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.TemplatePageListRequestApiTypeEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.strategy.vo.AdvertiseStrategyTemplateRequest;
import com.meiyunji.sponsored.service.util.PbUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class AdvertiseStrategyTemplateDaoImpl extends BaseShardingDaoImpl<AdvertiseStrategyTemplate> implements AdvertiseStrategyTemplateDao {

    @Override
    public AdvertiseStrategyTemplate selectByPrimaryKey(int puid, Long id) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("id",id).build();
        return getByCondition(puid,conditionBuilder);
    }

    @Override
    public int updateByPrimaryKey(int puid, AdvertiseStrategyTemplate record) {
        return updateById(puid, record);
    }

    @Override
    public int batchInsert(int puid, List<AdvertiseStrategyTemplate> list) {
        return 0;
    }

    @Override
    public Page<AdvertiseStrategyTemplate> pageList(int puid, AdvertiseStrategyTemplateRequest param) {
        StringBuilder sql = new StringBuilder("select t.id,t.puid,t.shop_id,t.marketplace_id,t.return_value,");
        sql.append("t.item_type,t.type,t.rule,t.version,t.template_name,t.create_name,t.update_name,");
        sql.append("t.create_at,t.last_update_at,t.create_uid,t.update_uid,t.children_item_type,");
        sql.append("s.start_stop_item_type as startStopItemType,count(s.id) as usageAmount,s.add_way_type as addWayType, t.status from t_advertise_strategy_template t ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select count(t.id) from t_advertise_strategy_template t ");
        String joinSql = " left join t_advertise_strategy_status s on t.puid = s.puid and t.shop_id = s.shop_id and t.id = s.template_id ";
        sql.append(joinSql);
        countSql.append(joinSql);

        StringBuilder whereSql = new StringBuilder();
        List<Object> args = new ArrayList<>();
        whereSql.append("where t.puid = ? and t.item_type = ? ");
        args.add(puid);
        args.add(param.getItemType());

        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            whereSql.append(SqlStringUtil.dealInList("t.shop_id", param.getShopIdList(), args));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.template_name like ?");
            args.add("%"+param.getSearchValue()+"%");
        }
        if (param.getTemplateId() != null) {
            whereSql.append(" and t.id != ?");
            args.add(param.getTemplateId());
        }
        if (CollectionUtils.isNotEmpty(param.getTemplateIdList())) {
            whereSql.append(SqlStringUtil.dealNotInList("t.id", param.getTemplateIdList(), args));
        }

        if (StringUtils.isNotBlank(param.getChildrenItemType())) {
            if (ItemTypeEnum.PORTFOLIO.getItemType().equals(param.getItemType())) {
                if (PbUtil.isPortfolioHour(param.getChildrenItemType())) {
                    whereSql.append(" and t.children_item_type = ? ");
                    args.add(param.getChildrenItemType());
                } else {
                    whereSql.append(" and t.children_item_type is null ");
                }
            } else {
                whereSql.append(" and t.children_item_type = ?");
                args.add(param.getChildrenItemType());
            }
        }

        if (Objects.nonNull(param.getUpdateTimeStart())) {
            whereSql.append(" and t.last_update_at >= ?");
            args.add(param.getUpdateTimeStart());
        }
        if (Objects.nonNull(param.getUpdateTimeEnd())) {
            whereSql.append(" and t.last_update_at <= ?");
            args.add(param.getUpdateTimeEnd());
        }
        if (CollectionUtils.isNotEmpty(param.getUpdateUidList())) {
            whereSql.append(SqlStringUtil.dealInList("t.update_uid", param.getUpdateUidList(), args));
        }
        if (CollectionUtils.isNotEmpty(param.getCreateUidList())) {
            whereSql.append(SqlStringUtil.dealInList("t.create_uid", param.getCreateUidList(), args));
        }
        if (StringUtils.isNotBlank(param.getStartStopItemType())) {
            //启停转移时，可以查询出受控对象数为0的模板
            if (TemplatePageListRequestApiTypeEnum.TRANSFER.getValue().equals(param.getApiType())) {
                whereSql.append(" and (s.start_stop_item_type = ? or s.start_stop_item_type is null) ");
            } else {
                //否则只能查询出对应启停类型的模板
                whereSql.append(" and s.start_stop_item_type = ? ");
            }
            args.add(param.getStartStopItemType());
        }
        whereSql.append(" group by t.id ");
        countSql.append(whereSql).append(" ) c");
        sql.append(whereSql).append(" ORDER BY t.last_update_at desc ");

        return this.getPageResult(puid, param.getPageNo(), param.getPageSize(), countSql.toString(),
                args.toArray(), sql.toString(), args.toArray(), AdvertiseStrategyTemplate.class);
    }

    @Override
    public Integer insertTemplate(Integer puid, AdvertiseStrategyTemplate template) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_advertise_strategy_template`(`id`,`puid`,`shop_id`,`marketplace_id`,");
        sql.append("`item_type`,`type`,`template_name`,`rule`,`version`,`create_uid`,`update_uid`,`create_name`,");
        sql.append("`update_name`,`children_item_type`,`return_value`,`status`,`create_at`,`last_update_at`) values");
        List<Object> argsList = Lists.newArrayList();
        sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
        argsList.add(template.getId());
        argsList.add(puid);
        argsList.add(template.getShopId());
        argsList.add(template.getMarketplaceId());
        argsList.add(template.getItemType());
        argsList.add(template.getType());
        argsList.add(template.getTemplateName());
        argsList.add(template.getRule());
        argsList.add(template.getVersion());
        argsList.add(template.getCreateUid());
        argsList.add(template.getUpdateUid());
        argsList.add(template.getCreateName());
        argsList.add(template.getUpdateName());
        argsList.add(template.getChildrenItemType());
        argsList.add(template.getReturnValue());
        argsList.add(template.getStatus());
        sql.deleteCharAt(sql.length()-1);
        return getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public Integer updateTemplate(Integer puid, AdvertiseStrategyTemplate template) {

        String sql = "update t_advertise_strategy_template set template_name = ? ,`type` = ?,rule = ?,update_uid = ?,update_name=?,return_value=?, " + (StringUtils.isNotBlank(template.getChildrenItemType()) ? "children_item_type=?, " : "") +
                " last_update_at = now(),version = ? where puid = ? and id = ?";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(template.getTemplateName());
        argsList.add(template.getType());
        argsList.add(template.getRule());
        argsList.add(template.getUpdateUid());
        argsList.add(template.getUpdateName());
        argsList.add(template.getReturnValue());
        if (StringUtils.isNotBlank(template.getChildrenItemType())) {
            argsList.add(template.getChildrenItemType());
        }
        argsList.add(template.getVersion());
        argsList.add(puid);
        argsList.add(template.getId());
        return getJdbcTemplate(puid).update(sql, argsList.toArray());
    }

    @Override
    public boolean existByName(Integer puid, String name,String itemType) {
        String sql = "select count(*) from t_advertise_strategy_template where puid = ? and `template_name`=? and `item_type` = ?";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, name,itemType);
    }

    @Override
    public void updateExecutePreviewOrNumber(Integer puid, Integer usageAmount,Integer executePreview,Long id) {
        String sql = "update t_advertise_strategy_template set usage_amount = ? , execute_preview = ? where puid = ? and id = ? ";
        getJdbcTemplate(puid).queryForObject(sql, Integer.class, usageAmount,executePreview,puid, id);
    }

    @Override
    public List<AdvertiseStrategyTemplate> getList(Integer puid, String templateName,String itemType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid",puid);
        if (StringUtils.isNotBlank(templateName)) {
            builder.equalTo("template_name",templateName);
        }
        if (StringUtils.isNotBlank(itemType)) {
            builder.equalTo("item_type",itemType);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public void deleteTemplateId(Integer puid, Long id) {
        String sql = "delete from t_advertise_strategy_template  where puid = ? and id = ? ";
        getJdbcTemplate(puid).update(sql,puid, id);
    }

    @Override
    public boolean verifyTemplateCount(Integer puid, String itemType) {
        String sql = "select count(*) from t_advertise_strategy_template where puid = ? and item_type = ?";
        return getJdbcTemplate(puid).queryForObject(sql, Boolean.class, puid, itemType);
    }

    @Override
    public List<AdvertiseStrategyTemplate> getListByTemplateId(Integer puid, List<Long> templateIdList) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .in("id",templateIdList.toArray()).build();
        return listByCondition(puid,conditionBuilder);
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateStrategyTemplateStatus(int puid, List<Long> templateIdList, String status, Long updateUid) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("update ").append(this.getJdbcHelper().getTable()).append(" set status = ?, last_update_at = now() ");
        argsList.add(status);
        if (Objects.nonNull(updateUid)) {
            sql.append(", update_uid = ? ");
            argsList.add(updateUid);
        }
        sql.append(" where puid = ? ");
        argsList.add(puid);
        sql.append(SqlStringUtil.dealInList("id", templateIdList, argsList));
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AdvertiseStrategyTemplate> getEnabledTemplate(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (Objects.nonNull(shopId)) {
            builder.equalTo("shop_id", shopId);
        }
        builder.equalTo("status", AutoRuleEnableStatusEnum.ENABLED.getCode());
        return listByCondition(puid, builder.build());
    }

    @Override
    public AdvertiseStrategyTemplate getTemplateByTaskId(Integer puid, Integer shopId, Long taskId) {
        String sql = "select b.* from t_advertise_strategy_status a join t_advertise_strategy_template b on a.template_id=b.id where a.puid=? and a.shop_id=? and a.task_id=?";
        List<AdvertiseStrategyTemplate> list = getJdbcTemplate(puid).query(sql, new Object[]{puid, shopId, taskId}, getRowMapper());
        return CollectionUtils.isEmpty(list) ? null : list.get(0);
    }
}
