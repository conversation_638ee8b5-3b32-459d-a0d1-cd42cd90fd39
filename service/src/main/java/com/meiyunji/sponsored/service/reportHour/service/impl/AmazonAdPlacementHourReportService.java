package com.meiyunji.sponsored.service.reportHour.service.impl;

import com.google.api.client.util.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.sellfox.ams.api.entry.HourlyReportDataPb;
import com.meiyunji.sellfox.ams.api.service.AmsApiGrpc;
import com.meiyunji.sellfox.ams.api.service.PlacementHourlyReportResponsePb;
import com.meiyunji.sellfox.ams.api.service.PlacementWeeklyReportResponsePb;
import com.meiyunji.sellfox.ams.api.service.PlacementWeeklyRequestPb;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.GetPlacementHourReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.CampaignPlacementDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service2.IAdCampaignPlacementReportRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.PlacementAggregateHourVo;
import com.meiyunji.sponsored.service.cpc.vo.PlacementHourParam;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.dashboard.dto.MultiThreadQueryParamDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.AggregatePlacementIdsTemporary;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementAggregateHourParam;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdPlacementHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementHourBaseVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdPlacementWeekDayVo;
import com.meiyunji.sponsored.service.util.PbUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-07-19  14:27
 */
@Service
@Slf4j
public class AmazonAdPlacementHourReportService implements IAmazonAdPlacementHourReportService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Qualifier("adFeedBlockingStub")
    @Autowired
    private AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonAdFeedReportService amazonAdFeedReportService;
    @Autowired
    private IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    @Autowired
    private IOdsAmazonAdProductDao odsAmazonAdProductDao;
    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;
    @Resource
    private IAdCampaignPlacementReportRoutingService adCampaignPlacementReportRoutingService;

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getListGrpc(int puid, PlacementHourParam param, ReportDateModelPb.ReportDateModel dateModel) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }

        //取店铺销售额
        List<AdPlacementHourVo> list = Lists.newArrayList();
        List<AdPlacementHourVo> compares = Lists.newArrayList();
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getList(puid, param);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getDayList(param.getPuid(), param, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getWeekList(param.getPuid(), param, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getMonthList(param.getPuid(), param, compares);
        }

        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour.Builder placementHourBuilder = GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdPlacementHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
            }
        }
        placementHourBuilder.addAllList(list.stream().filter(Objects::nonNull)
                .map(PbUtil::toPlacementHourReportPb).collect(Collectors.toList()));
        AdPlacementHourVo summaryVO = summary(list);
        if (Integer.valueOf(1).equals(param.getIsCompare()) && dateModel != ReportDateModelPb.ReportDateModel.HOURLY) {
            AdPlacementHourVo compareVO = summary(compares);
            summaryVO.compareDataSet(compareVO);
        }
        placementHourBuilder.setSummary(PbUtil.toPlacementHourReportPb(summaryVO));
        placementHourBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(list, false));

        //对比数据,chart图数据
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdPlacementHourVo> placementHourVos = list.stream().map(item -> {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(item.getLabel());
                vo.setAdCost(item.getAdCostCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setClicks(item.getClicksCompare());
                vo.setCpa(item.getCpaCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcots(item.getAcotsCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setSelfAdOrderNum(item.getSelfAdOrderNumCompare());
                vo.setOtherAdOrderNum(item.getOtherAdOrderNumCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdSelfSale(item.getAdSelfSaleCompare());
                vo.setAdOtherSale(item.getAdOtherSaleCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdSelfSaleNum(item.getAdSelfSaleNumCompare());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNumCompare());
                return vo;
            }).collect(Collectors.toList());
            placementHourBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(placementHourVos, true));
        }

        return placementHourBuilder.build();
    }

    @Override
    public List<AdPlacementHourVo> getList(int puid, PlacementHourParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        selectDto.setWeekdayList(weekdayList);
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            selectDto.setCampaignIds(Collections.singletonList(param.getCampaignId()));
        }
        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
        if (predicateEnum != null) {
            selectDto.setPlacements(Collections.singletonList(predicateEnum.getContent()));
        }
        List<AmazonMarketingStreamData> statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.statisticsByHour(selectDto));

        //获取小时对比数据
        List<AmazonMarketingStreamData> placementResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(param.getIsCompare())) {
            selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()));
            selectDto.setEndDate(param.getEndDateCompare());
            boolean returnCompareEmptyList = false;
            if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
                List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
                if (CollectionUtils.isEmpty(adIdList)) {
                    returnCompareEmptyList = true;
                }
                selectDto.setAdIds(adIdList);
            }
            placementResponse = (returnCompareEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.statisticsByHour(selectDto));
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(placementResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdPlacementHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdPlacementHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdPlacementHourVo> getAggregateHourList(ShopAuth shopAuth, int puid, List<String> idsList, PlacementAggregateHourVo param) {
        if (shopAuth == null) {
            return null;
        }
        AdPageBasicData pageBasicInfo = param.getAdPageBasicData();

        //获取小时级数据
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        builder.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), pageBasicInfo.getStartDate()));
        builder.setEndDate(pageBasicInfo.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        builder.setWeekdayList(weekdayList);
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);

        if (predicateEnum != null) {
            builder.setPlacements(Collections.singletonList(predicateEnum.getContent()));
        } else {
            builder.setPlacements(Arrays.asList(
                    PlacementPageParam.placementPredicateEnum.placementProductPage.getContent(),
                    PlacementPageParam.placementPredicateEnum.placementTop.getContent(),
                    PlacementPageParam.placementPredicateEnum.Other.getContent()));
        }

        List<List<AmazonMarketingStreamData>> resultList = multiThreadQueryAndMergeUtil.
                multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(), () -> idsList, builder, this::multiQueryPlacementResult);
        //merge result
        List<AmazonMarketingStreamData> mergeResult = mergePlacementHourResultResp(resultList);

        //获取小时对比数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(pageBasicInfo.getIsCompare().getValue())) {
            builder.setStartDate(pageBasicInfo.getStartDateCompare());
            builder.setEndDate(pageBasicInfo.getEndDateCompare());

            List<List<AmazonMarketingStreamData>> compareResultList = multiThreadQueryAndMergeUtil.
                    multiThreadQuery(() -> idsList, builder, this::multiQueryPlacementResult);
            List<AmazonMarketingStreamData> compareMergeResult = mergePlacementHourResultResp(compareResultList);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareMergeResult, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(mergeResult, hourlyReportDataMap);
        List<AdPlacementHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdPlacementHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdPlacementHourVo> getAggregateHourList(List<ShopAuth> shopAuths, int puid, PlacementAggregateHourParam param) {
        if (CollectionUtils.isEmpty(shopAuths)) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        selectDto.setSellerIds(sellerIds);
        selectDto.setMarketplaceId(param.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        AggregatePlacementIdsTemporary aggregatePlacementIdsTemporary = param.getAggregatePlacementIdsTemporary();
        if (CollectionUtils.isNotEmpty(aggregatePlacementIdsTemporary.getAdIdList())) {
            selectDto.setAdIds(aggregatePlacementIdsTemporary.getAdIdList());
        }
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        selectDto.setWeekdayList(weekdayList);
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getAggregateType(), PlacementPageParam.placementPredicateEnum.class);
        if (predicateEnum != null) {
            selectDto.setPlacements(Collections.singletonList(predicateEnum.getContent()));
        } else {
            selectDto.setPlacements(Arrays.asList(PlacementPageParam.placementPredicateEnum.placementProductPage.getContent(),
                    PlacementPageParam.placementPredicateEnum.placementTop.getContent(),
                    PlacementPageParam.placementPredicateEnum.Other.getContent(),
                    PlacementPageParam.placementPredicateEnum.offAmazon.getContent()));
        }

        List<List<AmazonMarketingStreamData>> resultList = multiThreadQueryAndMergeUtil.
                multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(), aggregatePlacementIdsTemporary::getCampaignIdPlacements, selectDto, this::multiQueryPlacementResultBySellers);
        //merge result
        List<AmazonMarketingStreamData> mergeResult = mergeCampaignHourResultResponse(resultList);

        //获取小时对比数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(param.getIsCompare())) {
            selectDto.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDateCompare()));
            selectDto.setEndDate(param.getEndDateCompare());

            List<List<AmazonMarketingStreamData>> compareResultList = multiThreadQueryAndMergeUtil.
                    multiThreadQuery(aggregatePlacementIdsTemporary::getCampaignIdPlacements, selectDto, this::multiQueryPlacementResultBySellers);
            List<AmazonMarketingStreamData> compareMergeResult = mergeCampaignHourResultResponse(compareResultList);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareMergeResult, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(mergeResult, hourlyReportDataMap);
        List<AdPlacementHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdPlacementHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdPlacementHourVo> getDayList(int puid, PlacementHourParam param, List<AdPlacementHourVo> compares) {
        List<AdPlacementHourVo> reports = getAdPlacementDailyReports(puid, param);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdPlacementHourVo> compareList = getAdPlacementDailyReports(puid, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdPlacementHourVo> paddingDayCompare(PlacementHourParam param, List<AdPlacementHourVo> reports, List<AdPlacementHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdPlacementHourVo> resList = new ArrayList<>();
        Map<String, AdPlacementHourVo> map = StreamUtil.toMap(reports, AdPlacementHourVo::getLabel);
        Map<String, AdPlacementHourVo> compareMap = StreamUtil.toMap(compareList, AdPlacementHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdPlacementHourVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdPlacementHourVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdPlacementHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdPlacementHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AdPlacementHourVo> getAggregateHourDayList(int puid, List<String> idsList, PlacementAggregateHourVo param, List<AdPlacementHourVo> compares) {
        List<AdPlacementHourVo> reports = getAggregatePlacementDailyReports(puid, idsList, param, false);
        reports = reports.stream().filter(Objects::nonNull).sorted((r1, r2) ->
                        DateUtil.compareDate(DateUtil.strToDate(r1.getDate(), "yyyy-MM-dd"), DateUtil.strToDate(r2.getDate(), "yyyy-MM-dd")))
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }

        AdPageBasicData adPageBasicData = param.getAdPageBasicData();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(adPageBasicData.getIsCompare().getValue())) {
            List<AdPlacementHourVo> compareList = getAggregatePlacementDailyReports(puid, idsList, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            PlacementHourParam tmpParam  =  PlacementHourParam.builder()
                    .startDate(adPageBasicData.getStartDate())
                    .endDate(adPageBasicData.getEndDate())
                    .startDateCompare(adPageBasicData.getStartDateCompare())
                    .build();
            return paddingDayCompare(tmpParam, reports, compareList);
        }else {
            return reports;
        }
    }

    @Override
    public List<AdPlacementHourVo> getWeekList(int puid, PlacementHourParam param, List<AdPlacementHourVo> compares) {
        List<AdPlacementHourVo> reports = getAdPlacementDailyReports(puid, param);
        reports = ReportChartUtil.getPlacementWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdPlacementHourVo> compareList = getAdPlacementDailyReports(puid, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getPlacementWeekReportVos(param.getStartDate(), param.getEndDate(), compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingWeekCompare(reports, compareList);
        }else {
            return reports;
        }
    }

    private List<AdPlacementHourVo> paddingWeekCompare(List<AdPlacementHourVo> reports, List<AdPlacementHourVo> compareList) {
        if (CollectionUtils.isEmpty(reports)) {
            return reports;
        }
        for (int i = 0; i < reports.size() && i < compareList.size(); i++) {
            reports.get(i).compareDataSet(compareList.get(i));
        }
        return reports;
    }

    @Override
    public List<AdPlacementHourVo> getAggregateHourWeekList(int puid, List<String> idsList, PlacementAggregateHourVo param, List<AdPlacementHourVo> compares) {
        List<AdPlacementHourVo> reports = getAggregatePlacementDailyReports(puid, idsList, param, false);
        AdPageBasicData basicPageInfo = param.getAdPageBasicData();
        reports = ReportChartUtil.getPlacementWeekReportVos(basicPageInfo.getStartDate(), basicPageInfo.getEndDate(), reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }

        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(basicPageInfo.getIsCompare().getValue())) {
            List<AdPlacementHourVo> compareList = getAggregatePlacementDailyReports(puid, idsList, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getPlacementWeekReportVos(basicPageInfo.getStartDateCompare(), basicPageInfo.getEndDateCompare(), compareList);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    @Override
    public List<AdPlacementHourVo> getMonthList(int puid, PlacementHourParam param, List<AdPlacementHourVo> compares) {
        List<AdPlacementHourVo> reports = getAdPlacementDailyReports(puid, param);
        reports = ReportChartUtil.getPlacementMonthReportVos(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdPlacementHourVo> compareList = getAdPlacementDailyReports(puid, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getPlacementMonthReportVos(compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingMonthCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdPlacementHourVo> paddingMonthCompare(PlacementHourParam param, List<AdPlacementHourVo> reports, List<AdPlacementHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdPlacementHourVo> resList = new ArrayList<>();
        Map<String, AdPlacementHourVo> map = StreamUtil.toMap(reports, AdPlacementHourVo::getLabel);
        Map<String, AdPlacementHourVo> compareMap = StreamUtil.toMap(compareList, AdPlacementHourVo::getLabel);
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end));
             start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdPlacementHourVo report = map.get(start.format(monthFormatter));
            AdPlacementHourVo compareReport = compareMap.get(startCompare.format(monthFormatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdPlacementHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdPlacementHourVo();
                vo.setLabel(start.format(monthFormatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }


    @Override
    public List<AdPlacementHourVo> getAggregateHourMonthList(int puid, List<String> idsList, PlacementAggregateHourVo param, List<AdPlacementHourVo> compares) {
        return getDailyWeeklyOrMonth(puid, param, idsList, ReportChartUtil::getPlacementMonthReportVos, compares);
    }

    private List<AdPlacementHourVo> getDailyWeeklyOrMonth(int puid, PlacementAggregateHourVo param, List<String> idsList,
                                                          Function<List<AdPlacementHourVo>, List<AdPlacementHourVo>> handleFuc,
                                                          List<AdPlacementHourVo> compares) {
        List<AdPlacementHourVo> reports = getAggregatePlacementDailyReports(puid, idsList, param, false);
        reports = handleFuc.apply(reports);
        if (CollectionUtils.isNotEmpty(reports)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(reports, adMetricDto);
            filterMetricData(reports, adMetricDto);
        }

        AdPageBasicData adPageBasicData = param.getAdPageBasicData();
        if (PlacementHourParam.CompareEnum.TRUE.getCode().equals(adPageBasicData.getIsCompare().getValue())) {
            List<AdPlacementHourVo> compareList = getAggregatePlacementDailyReports(puid, idsList, param, true);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = handleFuc.apply(compareList);
            PlacementHourParam tmpParam  =  PlacementHourParam.builder()
                    .startDate(adPageBasicData.getStartDate())
                    .endDate(adPageBasicData.getEndDate())
                    .startDateCompare(adPageBasicData.getStartDateCompare())
                    .build();
            return paddingMonthCompare(tmpParam, reports, compareList);
        }else {
            return reports;
        }
    }

    @Override
    public List<AdPlacementWeekDayVo> getWeeklySuperpositionList(int puid, PlacementHourParam param) {
        return convertToHourOfWeekDayVos(getWeeklySuperpositionDailyList(puid, param));
    }

    @Override
    public List<AdPlacementWeekDayVo> getPlacementWeeklySuperpositionList(int puid, PlacementHourParam param) {
        //在这里通过pageSign获取idList
        if (StringUtils.isEmpty(param.getPageSign())) {
            log.info("placement weekly superposition pageSign is null, param:{}", param);
        }
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(param.getPuid(), param.getPageSign(),
                "", param.getShopId(), new PlacementHourParam[]{param});
        if (CollectionUtils.isEmpty(idList)) {
            log.info("placement weekly superposition idList is null, param:{}", param);
        }
        return convertToHourOfWeekDayVos(getPlacementWeeklySuperpositionDailyList(puid, idList, param));
    }

    @Override
    public List<AdPlacementHourVo> getWeeklySuperpositionDailyList(int puid, PlacementHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        PlacementWeeklyRequestPb.PlacementWeeklyRequest.Builder builder =
                PlacementWeeklyRequestPb.PlacementWeeklyRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(param.getStartDate());
        builder.setEndDate(param.getEndDate());
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
        if (predicateEnum != null) {
            builder.addPlacement(predicateEnum.getContent());
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.addCampaignId(param.getCampaignId());
        }
        PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse response =
                adFeedBlockingStub.statisticsPlacementWeeklyReport(builder.build());

        //pb对象转vo对象
        List<HourlyReportDataPb.HourlyReportData> dataList = response.getDataList();
        List<AdPlacementHourVo> voList = dataList.stream().map(this::convertHourlyReportDataToVo).collect(Collectors.toList());
        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdPlacementHourVo::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdPlacementHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdPlacementHourVo::getWeekDay));
        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
        needFilledWeek.forEach(e -> {
            List<AdPlacementHourVo> adCampaignHourVos = new ArrayList<>();
            voMap.put(e, adCampaignHourVos);
        });
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdPlacementHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setWeekDay(k);
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdPlacementHourVo::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    @Override
    public List<AdPlacementHourVo> getPlacementWeeklySuperpositionDailyList(int puid, List<String> idList, PlacementHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        PlacementWeeklyRequestPb.PlacementWeeklyRequest.Builder builder =
                PlacementWeeklyRequestPb.PlacementWeeklyRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStartDate(param.getStartDate());
        builder.setEndDate(param.getEndDate());
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);
        Optional.ofNullable(predicateEnum).map(PlacementPageParam.placementPredicateEnum::getContent).ifPresent(builder::addPlacement);

        //需要分片查询数据,这个地方需要将param中获取的idList进行分片
        List<PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse> multiQueryResult = multiThreadQueryAndMergeUtil.
                multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(),
                        () -> idList, builder, this::multiQueryPlacementWeeklySuperposition);
        PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse response = mergeMultiQueryResult(multiQueryResult);

        //pb对象转vo对象
        List<HourlyReportDataPb.HourlyReportData> dataList = response.getDataList();
        List<AdPlacementHourVo> voList = dataList.stream().map(this::convertHourlyReportDataToVo).collect(Collectors.toList());
        //填充无数据的时间段
        List<Integer> allWeeks = HourConvert.weeKs;
        List<Integer> weekList = voList.stream().map(AdPlacementHourVo::getWeekDay).collect(Collectors.toList());
        Map<Integer, List<AdPlacementHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdPlacementHourVo::getWeekDay));
        List<Integer> needFilledWeek = allWeeks.stream().filter(item -> !weekList.contains(item)).collect(Collectors.toList());
        needFilledWeek.forEach(e -> {
            List<AdPlacementHourVo> adCampaignHourVos = new ArrayList<>();
            voMap.put(e, adCampaignHourVos);
        });
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdPlacementHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setWeekDay(k);
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdPlacementHourVo::getWeekDay)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }

    private PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse multiQueryPlacementWeeklySuperposition(List<String> idList, PlacementWeeklyRequestPb.PlacementWeeklyRequest.Builder builder) {
        PlacementWeeklyRequestPb.PlacementWeeklyRequest.Builder queryBuilder = PlacementWeeklyRequestPb.PlacementWeeklyRequest.newBuilder();
        BeanUtils.copyProperties(builder, queryBuilder);
        Optional.ofNullable(builder.getWeekdayList()).map(queryBuilder::addAllWeekday);
        Optional.ofNullable(builder.getPlacementList()).map(queryBuilder::addAllPlacement);
        queryBuilder.addAllCampaignId(idList);
        PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse response =
                adFeedBlockingStub.statisticsPlacementWeeklyReport(queryBuilder.build());
        return response;
    }

    private PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse mergeMultiQueryResult(List<PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse> partitionResult) {
        PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse.Builder builder = PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse.newBuilder();
        if (CollectionUtils.isEmpty(partitionResult)) return builder.build();
        List<HourlyReportDataPb.HourlyReportData> result = new ArrayList<>();
        //聚合数据
        Collection<HourlyReportDataPb.HourlyReportData> values = partitionResult.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0)
                .map(PlacementWeeklyReportResponsePb.PlacementWeeklyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour() + "&&" + e1.getWeekday();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        builder.addAllData(result);
        return builder.build();
    }

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getAggregateList(int puid, List<String> idList,
                                                                                                          PlacementAggregateHourVo param, ReportDateModelPb.ReportDateModel dateModel) {
        AdPageBasicData pageBasic = param.getAdPageBasicData();
        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElseGet(null);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return null;
        }

        //取店铺销售额
        List<AdPlacementHourVo> list = Lists.newArrayList();
        List<AdPlacementHourVo> compares = Lists.newArrayList();
        if (dateModel == ReportDateModelPb.ReportDateModel.HOURLY) {
            list = getAggregateHourList(shopAuth, puid, idList, param);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.DAILY) {
            list = getAggregateHourDayList(puid, idList, param, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.WEEKLY) {
            list = getAggregateHourWeekList(puid, idList, param, compares);
        } else if (dateModel == ReportDateModelPb.ReportDateModel.MONTHLY) {
            list = getAggregateHourMonthList(puid, idList, param, compares);
        }

        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour.Builder placementHourBuilder = GetPlacementHourReportResponsePb.
                GetPlacementHourReportResponse.PlacementHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(pageBasic.getOrderField()) &&
                    Constants.isADOrderField(pageBasic.getOrderField(), AdPlacementHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, pageBasic.getOrderField(), pageBasic.getOrderType());
            }
        }
        placementHourBuilder.addAllList(list.stream().filter(Objects::nonNull)
                .map(PbUtil::toPlacementHourReportPb).collect(Collectors.toList()));
        AdPlacementHourVo summaryVO = summary(list);
        if (pageBasic.getIsCompare().getValue() == 1 && dateModel != ReportDateModelPb.ReportDateModel.HOURLY) {
            AdPlacementHourVo compareVO = summary(compares);
            summaryVO.compareDataSet(compareVO);
        }
        placementHourBuilder.setSummary(PbUtil.toPlacementHourReportPb(summaryVO));
        placementHourBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(list, false));

        //对比数据,chart图数据
        if (pageBasic.getIsCompare().getValue() == 1) {
            List<AdPlacementHourVo> placementHourVos = list.stream().map(item -> {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(item.getLabel());
                vo.setAdCost(item.getAdCostCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setClicks(item.getClicksCompare());
                vo.setCpa(item.getCpaCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcots(item.getAcotsCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setSelfAdOrderNum(item.getSelfAdOrderNumCompare());
                vo.setOtherAdOrderNum(item.getOtherAdOrderNumCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdSelfSale(item.getAdSelfSaleCompare());
                vo.setAdOtherSale(item.getAdOtherSaleCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdSelfSaleNum(item.getAdSelfSaleNumCompare());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNumCompare());
                return vo;
            }).collect(Collectors.toList());
            placementHourBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(placementHourVos, true));
        }

        return placementHourBuilder.build();
    }

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getAggregateList(int puid, PlacementAggregateHourParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return null;
        }
        AggregatePlacementIdsTemporary aggregateIdsTemporary = cpcPageIdsHandler.getAggregatePlacementIdsTemporary(param.getPageSign(), null);
        if (aggregateIdsTemporary == null) {
            return null;
        }
        param.setAggregatePlacementIdsTemporary(aggregateIdsTemporary);

        //取店铺销售额
        List<AdPlacementHourVo> list = Lists.newArrayList();
        if (ReportDateModelPb.ReportDateModel.HOURLY == param.getDateModel()) {
            list = getAggregateHourList(shopAuths, puid, param);
        }

        GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour.Builder placementHourBuilder = GetPlacementHourReportResponsePb.
                GetPlacementHourReportResponse.PlacementHour.newBuilder();
        if (CollectionUtils.isNotEmpty(list)) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) &&
                    Constants.isADOrderField(param.getOrderField(), AdPlacementHourVo.class);
            if (isSorted) {
                PageUtil.sortedByOrderField(list, param.getOrderField(), param.getOrderType());
            }
        }
        placementHourBuilder.addAllList(list.stream().filter(Objects::nonNull)
                .map(PbUtil::toPlacementHourReportPb).collect(Collectors.toList()));
        placementHourBuilder.setSummary(PbUtil.toPlacementHourReportPb(summary(list)));
        placementHourBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(list, false));

        //对比数据,chart图数据
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            List<AdPlacementHourVo> placementHourVos = list.stream().map(item -> {
                AdPlacementHourVo vo = new AdPlacementHourVo();
                vo.setLabel(item.getLabel());
                vo.setAdCost(item.getAdCostCompare());
                vo.setImpressions(item.getImpressionsCompare());
                vo.setClicks(item.getClicksCompare());
                vo.setCpa(item.getCpaCompare());
                vo.setAdCostPerClick(item.getAdCostPerClickCompare());
                vo.setCtr(item.getCtrCompare());
                vo.setCvr(item.getCvrCompare());
                vo.setAcots(item.getAcotsCompare());
                vo.setRoas(item.getRoasCompare());
                vo.setAdOrderNum(item.getAdOrderNumCompare());
                vo.setSelfAdOrderNum(item.getSelfAdOrderNumCompare());
                vo.setOtherAdOrderNum(item.getOtherAdOrderNumCompare());
                vo.setAdSale(item.getAdSaleCompare());
                vo.setAdSelfSale(item.getAdSelfSaleCompare());
                vo.setAdOtherSale(item.getAdOtherSaleCompare());
                vo.setAdSaleNum(item.getAdSaleNumCompare());
                vo.setAdSelfSaleNum(item.getAdSelfSaleNumCompare());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNumCompare());
                return vo;
            }).collect(Collectors.toList());
            placementHourBuilder.addAllChart(ReportChartUtil.getPlacementHourChartData(placementHourVos, true));
        }

        return placementHourBuilder.build();
    }

    private List<AmazonMarketingStreamData> multiQueryPlacementResult(List<String> idList, CampaignHourlyReportSelectDto builder) {
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(builder, queryDto);
        queryDto.setCampaignIds(idList);
        return amazonMarketingStreamDataDao.statisticsByHour(queryDto);
    }

    private List<AmazonMarketingStreamData> multiQueryPlacementResultBySellers(List<AggregatePlacementIdsTemporary.CampaignIdPlacement> campaignIdPlacements,
                                                                               CampaignHourlyReportSelectDto selectDto) {
        if (CollectionUtils.isEmpty(campaignIdPlacements) || CollectionUtils.isEmpty(selectDto.getAdIds())) {
            return Collections.emptyList();
        }
        CampaignHourlyReportSelectDto queryBuilder = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(selectDto, queryBuilder);
        Optional.ofNullable(selectDto.getWeekdayList()).map(queryBuilder::setWeekdayList);
        Optional.ofNullable(selectDto.getPlacements()).map(queryBuilder::setPlacements);

        List<CampaignPlacementDto> campaignIdPlacementList = new ArrayList<>();
        campaignIdPlacements.forEach(e -> {
            CampaignPlacementDto campaignIdPlacement = new CampaignPlacementDto();
            campaignIdPlacement.setCampaignId(e.getCampaignId());
            campaignIdPlacement.setPlacement(e.getPlacement());
            campaignIdPlacementList.add(campaignIdPlacement);
        });
        queryBuilder.setAdIds(selectDto.getAdIds());
        queryBuilder.setSellerIds(selectDto.getSellerIds());
        return amazonMarketingStreamDataDao.aggregatePlacementSellersByHour(queryBuilder, campaignIdPlacementList);
    }

    private List<AmazonMarketingStreamData> mergePlacementHourResultResp(List<List<AmazonMarketingStreamData>> resultList) {
        List<AmazonMarketingStreamData> result = Collections.emptyList();
        if (CollectionUtils.isEmpty(resultList)) {
            return result;
        }
        //聚合数据
        Collection<AmazonMarketingStreamData> values = resultList.stream().filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                    LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                    return localTime.getHour();
                }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        return result;
    }

    private List<AmazonMarketingStreamData> mergeCampaignHourResultResponse(List<List<AmazonMarketingStreamData>> resultList) {
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(resultList)) {
            return result;
        }
        //聚合数据
        Collection<AmazonMarketingStreamData> values = resultList.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
            LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
            return localTime.getHour();
        }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
        if (CollectionUtils.isNotEmpty(values)) {
            result = com.google.common.collect.Lists.newArrayList(values);
        }
        return result;
    }

    private List<AdPlacementWeekDayVo> convertToHourOfWeekDayVos(List<AdPlacementHourVo> hourVos) {
        //按周汇总数据
        Map<Integer, List<AdPlacementHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdPlacementHourVo::getWeekDay));

        List<AdPlacementWeekDayVo> adPlacementWeekDayVos = new ArrayList<>(7);

        for (Map.Entry<Integer, List<AdPlacementHourVo>> entry : hourVoMap.entrySet()) {
            AdPlacementWeekDayVo adPlacementWeekDayVo = new AdPlacementWeekDayVo();
            List<AdPlacementHourVo> asinHourVos = entry.getValue();
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(asinHourVos, adMetricDto);
            filterMetricData(asinHourVos, adMetricDto);
            adPlacementWeekDayVo.setDetails(asinHourVos);
            adPlacementWeekDayVo.staticsFromHourVos(asinHourVos);
            adPlacementWeekDayVo.setWeekDay(entry.getKey());
            adPlacementWeekDayVos.add(adPlacementWeekDayVo);
        }
        AdMetricDto adMetricDto1 = new AdMetricDto();
        sumMetricData(adPlacementWeekDayVos, adMetricDto1);
        metricData(adPlacementWeekDayVos, adMetricDto1);
        return adPlacementWeekDayVos.stream().collect(Collectors.toList());
    }

    private void sumMetricData(List<? extends AdPlacementWeekDayVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdPlacementHourBaseVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdPlacementHourBaseVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void metricData(List<? extends AdPlacementWeekDayVo> voList, AdMetricDto adMetricDto) {
        for (AdPlacementWeekDayVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            metricData(adMetricDto, vo);
        }
    }

    private void metricData(AdMetricDto adMetricDto, AdPlacementWeekDayVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private List<AdPlacementHourVo> getAdPlacementDailyReports(int puid, PlacementHourParam param) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), param.getPuid());
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);

        //日,周,月报告数据
        List<AmazonAdCampaignAllReport> reports =
                adCampaignPlacementReportRoutingService.getReportByCampaignIdAndPlacement(puid, param.getShopId(),
                        start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                        end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getCampaignId(), predicateEnum.getContent(), param.getCampaignSite());

        return reports.stream().map(item -> {
            AdPlacementHourVo vo = new AdPlacementHourVo();

            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
            vo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
            vo.setAdOtherSaleNum(MathUtil.subtractInteger(vo.getAdSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
            return vo;
        }).collect(Collectors.toList());
    }

    private List<AdPlacementHourVo> getAggregatePlacementDailyReports(int puid, List<String> idList, PlacementAggregateHourVo param, boolean isCompare) {
        AdPageBasicData pageBasic = param.getAdPageBasicData();
        LocalDate start = Optional.ofNullable(pageBasic).map(AdPageBasicData::getStartDate).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
        LocalDate end = Optional.ofNullable(pageBasic).map(AdPageBasicData::getEndDate).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
        if (isCompare) {
            start = Optional.ofNullable(pageBasic).map(AdPageBasicData::getStartDateCompare).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
            end = Optional.ofNullable(pageBasic).map(AdPageBasicData::getEndDateCompare).filter(StringUtils::isNotBlank).map(LocalDate::parse).orElse(null);
        }
        if (Objects.isNull(start)) {
            return null;
        }

        Integer shopId = Optional.of(pageBasic.getShopId()).map(Int32Value::getValue).orElse(null);
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        PlacementPageParam.placementPredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), PlacementPageParam.placementPredicateEnum.class);

        MultiThreadQueryParamDto multiParam = MultiThreadQueryParamDto.builder()
                .shopIds(Collections.singletonList(shopId))
                .puid(puid)
                .marketplaceId(Optional.ofNullable(shopAuth).map(ShopAuth::getMarketplaceId).orElse(""))
                .startDate(start)
                .endDate(end)
                .predicate(Optional.ofNullable(predicateEnum).map(PlacementPageParam.placementPredicateEnum::getCode).orElse(null))
                .campaignSite(param.getCampaignSite())
                .build();

        List<List<AmazonAdCampaignAllReport>> multiResult = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getAggregatePlacementSyncPool(),
                () -> idList, multiParam, this::multiQueryPlacement);
        List<AmazonAdCampaignAllReport> reports = mergerDailyQueryPlacement(multiResult);
        //日,周,月报告数据
        return reports.stream().map(item -> {
            AdPlacementHourVo vo = new AdPlacementHourVo();
            vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(Optional.ofNullable(item.getTotalSales()).orElse(BigDecimal.ZERO));
            vo.setAdSelfSale(Optional.ofNullable(item.getAdSales()).orElse(BigDecimal.ZERO));
            vo.setAdOtherSale(Optional.ofNullable(item.getAdOtherSales()).orElse(BigDecimal.ZERO));
            vo.setAdOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
            vo.setSelfAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
            //sub other
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(Optional.ofNullable(item.getOrderNum()).orElse(0),
                    Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
            vo.setAdSaleNum(Optional.ofNullable(item.getSaleNum()).orElse(0));
            vo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
            vo.setAdOtherSaleNum(MathUtil.subtractInteger(vo.getAdSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdCost(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO));
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
            return vo;
        }).collect(Collectors.toList());
    }

    private List<AmazonAdCampaignAllReport> multiQueryPlacement(List<String> idsList, MultiThreadQueryParamDto dto) {
        //日,周,月报告数据
        return adCampaignPlacementReportRoutingService.getReportByCampaignIdListAndPlacement(dto.getPuid(), dto.getShopIds().get(0),
                dto.getStartDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                dto.getEndDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")), dto.getMarketplaceId(), idsList, dto.getPredicate(), dto.getCampaignSite());
    }

    private List<AmazonAdCampaignAllReport> mergerDailyQueryPlacement(List<List<AmazonAdCampaignAllReport>> reports) {
        List<AmazonAdCampaignAllReport> result = new ArrayList<>();
        //需要按日聚合
        Map<String, List<AmazonAdCampaignAllReport>> resultMap = reports.stream().flatMap(Collection::stream).
                collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getCountDate));
        for (List<AmazonAdCampaignAllReport> dataList : resultMap.values()) {
            AmazonAdCampaignAllReport reducePO = new AmazonAdCampaignAllReport();
            if (CollectionUtils.isEmpty(dataList)) continue;
            reducePO.setCostType(dataList.get(0).getCostType());
            reducePO.setShopId(dataList.get(0).getShopId());
            reducePO.setCountDate(dataList.get(0).getCountDate());
            reducePO.setMarketplaceId(dataList.get(0).getMarketplaceId());
            reducePO.setType(dataList.get(0).getType());
            reducePO.setCampaignId(dataList.get(0).getCampaignId());
            reducePO.setCampaignName(dataList.get(0).getCampaignName());
            result.add(dataList.stream().reduce(reducePO, AggregationDataUtil::getReduce));
        }
        return result;
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(PlacementHourlyReportResponsePb.PlacementHourlyReportResponse statisticsByHourResponse, Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private void filterSumMetricData(List<AdPlacementHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> item.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> item.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdPlacementHourVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdPlacementHourVo::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdPlacementHourVo> voList, AdMetricDto adMetricDto) {
        for (AdPlacementHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdPlacementHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private AdPlacementHourVo handleVo(Integer hour, HourlyReportDataPb.HourlyReportData data,
                                       HourlyReportDataPb.HourlyReportData dataCompare) {
        AdPlacementHourVo adCampaignHourVoCompare = convertHourlyReportDataToVo(dataCompare);
        AdPlacementHourVo adCampaignHourVo = convertHourlyReportDataToVo(data);
        AdPlacementHourVo vo = AdPlacementHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adCost(adCampaignHourVo.getAdCost())
                .impressions(adCampaignHourVo.getImpressions())
                .clicks(adCampaignHourVo.getClicks())
                .cpa(adCampaignHourVo.getCpa())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .ctr(adCampaignHourVo.getCtr())
                .cvr(adCampaignHourVo.getCvr())
                .acos(adCampaignHourVo.getAcos())
                .roas(adCampaignHourVo.getRoas())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .acots(adCampaignHourVo.getAcots())
                .build();
        //对比值
        if (adCampaignHourVoCompare != null) {
            vo.setAdCostCompare(adCampaignHourVoCompare.getAdCost());
            vo.setImpressionsCompare(adCampaignHourVoCompare.getImpressions());
            vo.setClicksCompare(adCampaignHourVoCompare.getClicks());
            vo.setCpaCompare(adCampaignHourVoCompare.getCpa());
            vo.setAdCostPerClickCompare(adCampaignHourVoCompare.getAdCostPerClick());
            vo.setCtrCompare(adCampaignHourVoCompare.getCtr());
            vo.setCvrCompare(adCampaignHourVoCompare.getCvr());
            vo.setAcosCompare(adCampaignHourVoCompare.getAcos());
            vo.setRoasCompare(adCampaignHourVoCompare.getRoas());
            vo.setAdOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum());
            vo.setSelfAdOrderNumCompare(adCampaignHourVoCompare.getSelfAdOrderNum());
            vo.setOtherAdOrderNumCompare(adCampaignHourVoCompare.getOtherAdOrderNum());
            vo.setAdSaleCompare(adCampaignHourVoCompare.getAdSale());
            vo.setAdSelfSaleCompare(adCampaignHourVoCompare.getAdSelfSale());
            vo.setAdOtherSaleCompare(adCampaignHourVoCompare.getAdOtherSale());
            vo.setAdSaleNumCompare(adCampaignHourVoCompare.getAdSaleNum());
            vo.setAdSelfSaleNumCompare(adCampaignHourVoCompare.getAdSelfSaleNum());
            vo.setAdOtherSaleNumCompare(adCampaignHourVoCompare.getAdOtherSaleNum());
            vo.setAcotsCompare(adCampaignHourVoCompare.getAcots());
            vo.setAsotsCompare(adCampaignHourVoCompare.getAsots());
            vo.afterPropertiesSet();
        }
        return vo;
    }

    private AdPlacementHourVo handleVo(Integer hour, AmazonMarketingStreamData data,
                                       AmazonMarketingStreamData dataCompare) {
        AdPlacementHourVo adCampaignHourVoCompare = convertHourlyReportDataToVo(dataCompare);
        AdPlacementHourVo adCampaignHourVo = convertHourlyReportDataToVo(data);
        AdPlacementHourVo vo = AdPlacementHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adCost(adCampaignHourVo.getAdCost())
                .impressions(adCampaignHourVo.getImpressions())
                .clicks(adCampaignHourVo.getClicks())
                .cpa(adCampaignHourVo.getCpa())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .ctr(adCampaignHourVo.getCtr())
                .cvr(adCampaignHourVo.getCvr())
                .acos(adCampaignHourVo.getAcos())
                .roas(adCampaignHourVo.getRoas())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .acots(adCampaignHourVo.getAcots())
                .build();
        //对比值
        if (adCampaignHourVoCompare != null) {
            vo.setAdCostCompare(adCampaignHourVoCompare.getAdCost());
            vo.setImpressionsCompare(adCampaignHourVoCompare.getImpressions());
            vo.setClicksCompare(adCampaignHourVoCompare.getClicks());
            vo.setCpaCompare(adCampaignHourVoCompare.getCpa());
            vo.setAdCostPerClickCompare(adCampaignHourVoCompare.getAdCostPerClick());
            vo.setCtrCompare(adCampaignHourVoCompare.getCtr());
            vo.setCvrCompare(adCampaignHourVoCompare.getCvr());
            vo.setAcosCompare(adCampaignHourVoCompare.getAcos());
            vo.setRoasCompare(adCampaignHourVoCompare.getRoas());
            vo.setAdOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum());
            vo.setSelfAdOrderNumCompare(adCampaignHourVoCompare.getSelfAdOrderNum());
            vo.setOtherAdOrderNumCompare(adCampaignHourVoCompare.getOtherAdOrderNum());
            vo.setAdSaleCompare(adCampaignHourVoCompare.getAdSale());
            vo.setAdSelfSaleCompare(adCampaignHourVoCompare.getAdSelfSale());
            vo.setAdOtherSaleCompare(adCampaignHourVoCompare.getAdOtherSale());
            vo.setAdSaleNumCompare(adCampaignHourVoCompare.getAdSaleNum());
            vo.setAdSelfSaleNumCompare(adCampaignHourVoCompare.getAdSelfSaleNum());
            vo.setAdOtherSaleNumCompare(adCampaignHourVoCompare.getAdOtherSaleNum());
            vo.setAcotsCompare(adCampaignHourVoCompare.getAcots());
            vo.setAsotsCompare(adCampaignHourVoCompare.getAsots());
            vo.afterPropertiesSet();
        }
        return vo;
    }

    private AdPlacementHourVo convertHourlyReportDataToVo(HourlyReportDataPb.HourlyReportData data) {
        AdPlacementHourVo vo = new AdPlacementHourVo();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdPlacementHourVo convertHourlyReportDataToVo(AmazonMarketingStreamData data) {
        AdPlacementHourVo vo = new AdPlacementHourVo();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdPlacementHourVo summary(List<AdPlacementHourVo> list) {
        AdPlacementHourVo vo = new AdPlacementHourVo();
        if (CollectionUtils.isEmpty(list)) {
            return vo;
        }
        for (AdPlacementHourVo ad : list) {
            vo.setAdCost(MathUtil.add(ad.getAdCost(), vo.getAdCost()));
            vo.setAdCostCompare(MathUtil.add(vo.getAdCostCompare(), ad.getAdCostCompare()));
            vo.setImpressions(MathUtil.add(vo.getImpressions(), ad.getImpressions()));
            vo.setImpressionsCompare(MathUtil.add(vo.getImpressionsCompare(), ad.getImpressionsCompare()));
            vo.setClicks(MathUtil.add(ad.getClicks(), vo.getClicks()));
            vo.setClicksCompare(MathUtil.add(vo.getClicksCompare(), ad.getClicksCompare()));
            vo.setAdOrderNum(MathUtil.add(ad.getAdOrderNum(), vo.getAdOrderNum()));
            vo.setAdOrderNumCompare(MathUtil.add(vo.getAdOrderNumCompare(), ad.getAdOrderNumCompare()));
            vo.setSelfAdOrderNum(MathUtil.add(ad.getSelfAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setSelfAdOrderNumCompare(MathUtil.add(ad.getSelfAdOrderNumCompare(), vo.getSelfAdOrderNumCompare()));
            vo.setOtherAdOrderNum(MathUtil.add(ad.getOtherAdOrderNum(), vo.getOtherAdOrderNum()));
            vo.setOtherAdOrderNumCompare(MathUtil.add(ad.getOtherAdOrderNumCompare(), vo.getOtherAdOrderNumCompare()));
            vo.setAdSale(MathUtil.add(ad.getAdSale(), vo.getAdSale()));
            vo.setAdSaleCompare(MathUtil.add(vo.getAdSaleCompare(), ad.getAdSaleCompare()));
            vo.setAdSelfSale(MathUtil.add(ad.getAdSelfSale(), vo.getAdSelfSale()));
            vo.setAdSelfSaleCompare(MathUtil.add(ad.getAdSelfSaleCompare(), vo.getAdSelfSaleCompare()));
            vo.setAdOtherSale(MathUtil.add(ad.getAdOtherSale(), vo.getAdOtherSale()));
            vo.setAdOtherSaleCompare(MathUtil.add(ad.getAdOtherSaleCompare(), vo.getAdOtherSaleCompare()));
            vo.setAdSaleNum(MathUtil.add(ad.getAdSaleNum(), vo.getAdSaleNum()));
            vo.setAdSaleNumCompare(MathUtil.add(vo.getAdSaleNumCompare(), ad.getAdSaleNumCompare()));
            vo.setAdSelfSaleNum(MathUtil.add(ad.getAdSelfSaleNum(), vo.getAdSelfSaleNum()));
            vo.setAdSelfSaleNumCompare(MathUtil.add(ad.getAdSelfSaleNumCompare(), vo.getAdSelfSaleNumCompare()));
            vo.setAdOtherSaleNum(MathUtil.add(ad.getAdOtherSaleNum(), vo.getAdOtherSaleNum()));
            vo.setAdOtherSaleNumCompare(MathUtil.add(ad.getAdOtherSaleNumCompare(), vo.getAdOtherSaleNumCompare()));
        }
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 2, RoundingMode.HALF_UP));
        vo.setCpaCompare(vo.getAdOrderNumCompare() == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().divide(BigDecimal.valueOf(vo.getAdOrderNumCompare()), 2, RoundingMode.HALF_UP));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks()), 2));
        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare()), 2));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions()), 2));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare()), 2));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks()), 2));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare()), 2));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 2, RoundingMode.HALF_UP));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 2, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 2, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 2, RoundingMode.HALF_UP));
        vo.afterPropertiesSet();//为各对比率属性设值
        if (vo.getAdCostPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdCostPercentage()) == 0) {
            vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdCostPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdSalePercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdSalePercentage()) == 0) {
            vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdSalePercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getAdOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getAdOrderNumPercentage()) == 0) {
            vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setAdOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        if (vo.getOrderNumPercentage() == null || BigDecimal.ZERO.compareTo(vo.getOrderNumPercentage()) == 0) {
            vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2));
        } else {
            vo.setOrderNumPercentage(BigDecimal.valueOf(100).setScale(2));
        }
        return vo;
    }

    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMap(List<AmazonMarketingStreamData> statisticsByHourResponse, Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourResponse)) {
            hourlyReportDataMap = statisticsByHourResponse.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }
}
