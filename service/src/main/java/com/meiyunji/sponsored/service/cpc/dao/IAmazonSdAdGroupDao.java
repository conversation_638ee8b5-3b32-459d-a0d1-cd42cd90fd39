package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.autoRule.vo.CampaignIdWithAdTypeVo;
import com.meiyunji.sponsored.service.cpc.bo.AllGroupOrderBo;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.vo.GroupInfoPageVo;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.strategy.vo.AdStrategyGroupParam;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> on 2021/7/6
 */
public interface IAmazonSdAdGroupDao extends IBaseShardingDao<AmazonSdAdGroup> {

    void batchAdd(int puid, List<AmazonSdAdGroup> list);

    void batchUpdate(int puid, List<AmazonSdAdGroup> list);

    List<AmazonSdAdGroup> listByGroupId(int puid, int shopId, List<String> groupIds);

    List<AmazonSdAdGroup> listByGroupId(int puid, List<Integer> shopIdList, List<String> groupIds);

    AmazonSdAdGroup getByGroupId(int puid, Integer shopId, String groupId);

    List<AmazonSdAdGroup> getByGroupIds(int puid, Integer shopId, List<String> groupIds);

    List<AmazonSdAdGroup> getByCampaignIds(int puid, Integer shopId, List<String> campaignIds);

    List<AmazonSdAdGroup> listAmazonSdAdGroup(int puid, Integer shopId, List<String> campaignIds, String status, String servingStatus);

    /**
     * 根据tactic获取SD广告的受众投放/商品投放类型数据
     *
     * @param puid
     * @param shopId
     * @param tactic
     * @return
     */
    List<String> getGroupIdsByTactic(int puid, Integer shopId, String tactic);

    Page<AmazonSdAdGroup> pageList(int puid, GroupPageParam param);

    List<AmazonSdAdGroup> listByCondition(int puid, GroupPageParam param);

    boolean exist(Integer puid, Integer shopId, String campaignId, String name);

    boolean exist(Integer puid, Integer shopId, String adGroupId);

    Page getPageList(Integer puid, GroupPageParam param, Page page);

    List<AmazonSdAdGroup> getList(Integer puid, GroupPageParam param);

    /**
     * 修改报告数据最新更新时间
     *
     * @param puid
     * @param shopId
     * @param groupId
     * @param localDate
     */
    void updateDataUpdateTime(Integer puid, Integer shopId, String groupId, LocalDate localDate);

    void batchUpdateAmazonSdAdGroup(Integer puid, List<AmazonSdAdGroup> list, String type);

    List<String> getAdGroupIdsByGroup(Integer puid, GroupPageParam param);

    List<String> getAdGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupId);

    List<AmazonSdAdGroup> listByPuidAndCampaignIds(int puid, List<String> campaignIds);

    List<AmazonSdAdGroup> listByPuidAndCampaignId(int puid, int shopId, String campaignId);
    List<AmazonSdAdGroup> getInfoByCampaignIdAndGroupId(int puid, int shopId, String campaignId, String groupId);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);

    Page<AmazonSdAdGroup> getPageListByStrategy(AdStrategyGroupParam param);

    List<String> queryAdGroupIdList(ControlledObjectParam param);

    void updatePricing(Integer puid, Integer shopId, String adGroupId, Integer isPricing, Integer pricingState, int updateId);

    List<String> getSdGroupIdListByParamAndIds(Integer puid, GroupPageParam param, List<String> adGroupIdList);

    List<AllGroupOrderBo> getSdGroupIdAndOrderFieldList(Integer puid, GroupPageParam param, List<String> adGroupIdList, String orderField);

    Page<GroupInfoPageVo> getAllSdGroupPage(Integer puid, GroupPageParam param);

    List<GroupInfoPageVo> getSdGroupPageVoListByGroupIdList(Integer puid, GroupPageParam param, List<String> adGroupIdList);

    List<String> getAdCampaignIdsByGroupIds(Integer puid, Integer shopId, String marketPlaceId, List<String> adGroupIds);

    List<String> getAdCampaignIdsByGroupIds(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, List<String> adGroupIds);

    AmazonSdAdGroup getByAdGroupId(Integer puid, Integer shopId, String adGroupId);

    List<String> queryAdGroupIdList(ProcessTaskParam param);

    List<AmazonSdAdGroup> getByShopIdsAndGroupIds(Integer puid, List<Integer> shopIds, List<String> adGroupIds);

    List<AmazonSdAdGroup> getByAdGroupIds(int puid, List<String> groupIds);

    List<AmazonSdAdGroup> getGroupByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> adGroupIds);

    List<AmazonSdAdGroup> autoRuleAdGroup(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIdList, String state, String searchValue, List<String> servingStatusList);

    void autoRuleUpdate(AdvertiseRuleTaskExecuteRecordV2Message message);

    List<String> queryAutoRuleAdGroupIdList(com.meiyunji.sponsored.service.autoRuleTask.vo.ProcessTaskParam param);

    List<String> getGroupIdByCampaignId(Integer puid, Integer shopId, List<CampaignIdWithAdTypeVo> sdCampaignIdList);

    List<String> getIdListByCampaignIds(Integer puid, List<Integer> shopIds, List<String> campaignIdList);

    List<AmazonSdAdGroup> getNameByShopIdsAndGroupIds(int puid, List<Integer> shopIds, List<String> campaignList, List<String> groupIds, String groupName);

    List<Map<String, Object>> getCampaignIdsByGroupIds(Integer puid, List<Integer> shopIds, Set<String> sdAdgroupIds);

    List<AmazonSdAdGroup> getAdGroupByIds(Integer puid, Integer shopId, List<String> groupIds);

    List<DownloadCenterBaseDataBO> queryBaseData4DownloadByShopAdGroup(Integer puid, Integer shopId, List<String> adGroupIdList);
}