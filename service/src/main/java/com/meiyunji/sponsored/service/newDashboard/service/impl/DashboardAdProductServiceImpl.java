package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdProductResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdSalesmanResponseVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSdProductReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.*;
import com.meiyunji.sponsored.service.newDashboard.dto.base.DashboardAdCalDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.*;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdProductService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.util.PageUtils;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdProductReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: ys
 * @date: 2024/4/9 15:52
 * @describe:
 */
@Service
@Slf4j
public class DashboardAdProductServiceImpl implements IDashboardAdProductService {



    @Autowired
    private IExcelService excelService;


    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Resource
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;

    @Resource
    private IOdsAmazonAdSdProductReportDao odsAmazonAdSdProductReportDao;

    @Resource
    private IOdsProductDao odsProductDao;

    @Autowired
    private CpcShopDataService cpCShopDataService;

    @Resource
    private IVcShopAuthDao vcShopAuthDao;

    @Override
    public DashboardAdProductResponseVo.Page queryAdProductCharts(DashboardAdProductReqVo req) {

        if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(req.getShopIdList());
            req.setShopIdList(Lists.newArrayList(req.getShopIdList()));
            if (CollectionUtils.isNotEmpty(listByIdList)) {
                req.getShopIdList().removeAll(listByIdList.stream().map(VcShopAuth::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(req.getShopIdList())) {
                log.info("剔除vc店铺后无数据puid:{}", req.getPuid());
                return null;
            }
        }
        List<DashboardAdProductDto> resultList = getAdProductTopList(req);
        if (CollectionUtils.isEmpty(resultList)) {
            log.info("get top campaign chars data list is null, queryField:{}, dataField:{}", req.getQueryField(), req.getDataField());
            return null;
        }
        //组装数据返回
        List<DashboardAdProductResponseVo.Page.TopList> topList = resultList.stream().map(d -> {
            DashboardAdProductResponseVo.Page.TopList.Builder vo =
                    DashboardAdProductResponseVo.Page.TopList.newBuilder();
            BeanUtils.copyProperties(d, vo, ParamCopyUtil.checkPropertiesNullOrEmptySuper(d));
            vo.setCost(CalculateUtil.formatDecimal(d.getCost()));
            Optional.ofNullable(d.getMainImage()).ifPresent(vo::setImgUrl);
            Optional.ofNullable(d.getLabel()).ifPresent(vo::setLabel);
            Optional.ofNullable(d.getAsin()).ifPresent(vo::setAsin);
            if (CollectionUtils.isNotEmpty(d.getShopInfos())) {
                List<DashboardAdProductResponseVo.Page.ShopDto> shopDtos = d.getShopInfos().stream().map(e -> {
                    DashboardAdProductResponseVo.Page.ShopDto.Builder builder = DashboardAdProductResponseVo.Page.ShopDto.newBuilder();
                    builder.setShopId(e.getShopId());
                    builder.setShopName(e.getShopName());
                    builder.setMarketplaceName(e.getMarketplaceName());
                    builder.setMarketplaceId(e.getMarketplaceId());
                    return builder.build();
                }).collect(Collectors.toList());
                vo.addAllShops(shopDtos);
            }

            if (StringUtils.isNotBlank(d.getShopIds())) {
                vo.addAllShopIds(StringUtil.splitStr(d.getShopIds(), ",").stream().filter(StringUtils::isNotBlank).map(String::trim).filter(StringUtils::isNotBlank).map(Integer::valueOf).collect(Collectors.toList()));
            }
            Optional.ofNullable(d.getTotalSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setTotalSales);
            Optional.ofNullable(d.getImpressions()).map(String::valueOf).ifPresent(vo::setImpressions);
            Optional.ofNullable(d.getClicks()).map(String::valueOf).ifPresent(vo::setClicks);
            Optional.ofNullable(d.getOrderNum()).map(String::valueOf).ifPresent(vo::setOrderNum);
            Optional.ofNullable(d.getSaleNum()).map(String::valueOf).ifPresent(vo::setSaleNum);
            Optional.ofNullable(d.getAcos()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcos);
            Optional.ofNullable(d.getRoas()).map(String::valueOf).ifPresent(vo::setRoas);
            Optional.ofNullable(d.getClickRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setClickRate);
            Optional.ofNullable(d.getConversionRate()).map(CalculateUtil::formatPercent).ifPresent(vo::setConversionRate);
            Optional.ofNullable(d.getCpc()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpc);
            Optional.ofNullable(d.getCpa()).map(CalculateUtil::formatDecimal).ifPresent(vo::setCpa);
            Optional.ofNullable(d.getCostPercent()).ifPresent(vo::setCostPercent);
            Optional.ofNullable(d.getTotalSalesPercent()).ifPresent(vo::setTotalSalesPercent);
            Optional.ofNullable(d.getImpressionsPercent()).ifPresent(vo::setImpressionsPercent);
            Optional.ofNullable(d.getClicksPercent()).ifPresent(vo::setClicksPercent);
            Optional.ofNullable(d.getOrderNumPercent()).ifPresent(vo::setOrderNumPercent);
            Optional.ofNullable(d.getSaleNumPercent()).ifPresent(vo::setSaleNumPercent);
            Optional.ofNullable(d.getShopSales()).map(CalculateUtil::formatDecimal).ifPresent(vo::setShopSales);
            Optional.ofNullable(d.getShopSalesYoyRate()).ifPresent(vo::setShopSalesYoyRate);
            Optional.ofNullable(d.getShopSalesMomRate()).ifPresent(vo::setShopSalesMomRate);
            Optional.ofNullable(d.getShopSalesYoyValue()).ifPresent(vo::setShopSalesYoyValue);
            Optional.ofNullable(d.getShopSalesMomValue()).ifPresent(vo::setShopSalesMomValue);
            Optional.ofNullable(d.getShopSalesPercent()).ifPresent(vo::setShopSalesPercent);


            Optional.ofNullable(d.getShopSaleNum()).map(String::valueOf).ifPresent(vo::setShopSaleNum);
            Optional.ofNullable(d.getShopSaleNumYoyRate()).ifPresent(vo::setShopSaleNumYoyRate);
            Optional.ofNullable(d.getShopSaleNumMomRate()).ifPresent(vo::setShopSaleNumMomRate);
            Optional.ofNullable(d.getShopSaleNumYoyValue()).ifPresent(vo::setShopSaleNumYoyValue);
            Optional.ofNullable(d.getShopSaleNumMomValue()).ifPresent(vo::setShopSaleNumMomValue);
            Optional.ofNullable(d.getShopSaleNumPercent()).ifPresent(vo::setShopSaleNumPercent);
            Optional.ofNullable(d.getAcots()).map(CalculateUtil::formatPercent).ifPresent(vo::setAcots);
            Optional.ofNullable(d.getAsots()).map(CalculateUtil::formatPercent).ifPresent(vo::setAsots);

            return vo.build();
        }).collect(Collectors.toList());
        return PageUtils.getAdProductPageInfo(topList, req.getPageSize(), req.getPageNo());
    }


    private List<DashboardAdProductDto> getAdProductTopList(DashboardAdProductReqVo req) {
        //查询广告组top图表数据，且需要区分sp,sb,sd
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        DashboardOrderByEnum orderByEn = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //先获取sp的图表数据
        List<DashboardAdProductDto> resultList = queryTopList(req, puid, shopIdList, marketplaceIdList, currency, limit, orderField, dataField);


        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        //还需要对结果进行排序，取前limit行数据
        Class<DashboardAdProductDto> cla = DashboardAdProductDto.class;
        Field[] fields = getAllField(cla);
        Map<String, Field> fieldMap = Arrays.stream(fields).peek(f -> f.setAccessible(true))
                .filter(e-> !e.isSynthetic()).collect(Collectors.toMap(Field::getName, v1 -> v1));
        StringBuilder compareKey = new StringBuilder(dataField.getCode());
        compareKey.append(orderField.getSuffix());
        resultList = resultList.stream().sorted((o1, o2) -> {
            try {
                int result = 0;
                Field compareField = fieldMap.get(compareKey.toString());
                if (compareField == null) {
                    compareField = fieldMap.get(dataField.getCode());
                }
                if (Objects.nonNull(compareField)) {
                    Object val1 = compareField.get(o1);
                    Object val2 = compareField.get(o2);
                    if(Objects.isNull(val1) && Objects.isNull(val2) ){
                        result = 0;
                    } else if(Objects.nonNull(val1) && Objects.isNull(val2) ){
                        result = 1;
                    } else if(Objects.isNull(val1)){
                        result = -1;
                    } else {
                        if(String.class.isAssignableFrom(compareField.getType())){
                            String rateValue1 = val1.toString().replace("%", "");
                            String rateValue2 = val2.toString().replace("%", "");
                            if("--".equalsIgnoreCase(rateValue2) && "--".equalsIgnoreCase(rateValue1)) {
                                result = 0;
                            } else if(!"--".equalsIgnoreCase(rateValue1) && "--".equalsIgnoreCase(rateValue2) ){
                                result = 1;
                            } else  if("--".equalsIgnoreCase(rateValue1)){
                                result = -1;
                            } else {
                                result = new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                            }
                        }
                        if(Integer.class.isAssignableFrom(compareField.getType())){
                            Integer compareVal1 = (Integer) val1;
                            Integer compareVal2 = (Integer) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(BigDecimal.class.isAssignableFrom(compareField.getType())){
                            BigDecimal compareVal1 = (BigDecimal) val1;
                            BigDecimal compareVal2 = (BigDecimal) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(Long.class.isAssignableFrom(compareField.getType())){
                            Long compareVal1 = (Long) val1;
                            Long compareVal2 = (Long) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                    }
                }
                if (result == 0) {
                    if(Objects.isNull(o1.getLabel()) && Objects.isNull(o2.getLabel()) ){
                        result = 0;
                    } else if(Objects.nonNull(o1.getLabel()) && Objects.isNull(o2.getLabel()) ){
                        result = 1;
                    } else if(Objects.isNull(o1.getLabel())){
                        result = -1;
                    } else {
                        result = o1.getLabel().compareTo(o2.getLabel());
                        if (result > 1) {
                            result = 1;
                        }
                        if (result < 0) {
                            result = -1;
                        }
                    }
                }
                return result;
            } catch (IllegalAccessException e) {
                log.error("compare product list data error", e);
            }
            return 0;
        }).collect(Collectors.toList());
        int subLimit = Math.min(limit, resultList.size());

        //按请求字段进行升降序
        //cpc，cpa， 环比不排序， 值排序，  acos 环比不排序   值排序
        if ( DashboardOrderByEnum.DESC == orderByEn) {
            Collections.reverse(resultList);
        }
        resultList = resultList.subList(0, subLimit);
        if (StringUtils.isNotBlank(req.getListOrderField()) || StringUtils.isNotBlank(req.getListOrderType())) {
            OrderByUtil.sortedByOrderField(resultList, req.getListOrderField(), req.getListOrderType(), "label");
        }
        return resultList;
    }


    private List<String> baseHeaderList = Arrays.asList(
            "marketplaceName", "shopName",
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate",
//            "displayRoas", "roasMomRate", "roasYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayShopSales", "shopSalesPercent", "shopSalesMomRate", "shopSalesYoyRate",
            "displayAcots", "acotsMomRate", "acotsYoyRate",
            "displayAsots", "asotsMomRate", "asotsYoyRate",
            "shopSaleNum", "shopSaleNumPercent", "shopSaleNumMomRate", "shopSaleNumYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");



    private DashboardAdProductDto convertBasicAndCalData(DashboardAdProductTopDataDto subInfo) {
        DashboardAdProductDto dto = new DashboardAdProductDto();
        dto.setCost(subInfo.getSubCost());
        dto.setTotalSales(subInfo.getSubTotalSales());
        dto.setImpressions(subInfo.getSubImpressions());
        dto.setClicks(subInfo.getSubClicks());
        dto.setOrderNum(subInfo.getSubOrderNum());
        dto.setSaleNum(subInfo.getSubSaleNum());
        dto.setAcos(subInfo.getSubAcos());
        dto.setRoas(subInfo.getSubRoas());
        dto.setClickRate(subInfo.getSubClickRate());
        dto.setConversionRate(subInfo.getSubConversionRate());
        dto.setCpc(subInfo.getSubCpc());
        dto.setCpa(subInfo.getSubCpa());
        return dto;
    }

    private DashboardAdProductDto convertSummaryData(DashboardAdProductTopDataDto subInfo) {
        DashboardAdProductDto dto = new DashboardAdProductDto();
        dto.setCost(subInfo.getAllCost());
        dto.setTotalSales(subInfo.getAllTotalSales());
        Optional.ofNullable(subInfo.getAllImpressions()).map(BigDecimal::longValue).ifPresent(dto::setImpressions);
        Optional.ofNullable(subInfo.getAllClicks()).map(BigDecimal::intValue).ifPresent(dto::setClicks);
        Optional.ofNullable(subInfo.getAllOrderNum()).map(BigDecimal::intValue).ifPresent(dto::setOrderNum);
        Optional.ofNullable(subInfo.getAllSaleNum()).map(BigDecimal::intValue).ifPresent(dto::setSaleNum);
        return dto;
    }

    private void computeProductData(DashboardAdProductDto dto, DashboardAdProductDto yoyDto,
                                     DashboardAdProductDto momDto, DashboardAdProductDto summary,
                                     boolean yoyOverLimit) {
        if (Objects.isNull(dto)) {
            return;
        }
        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
        }else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalData(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflex(dto, yoyDto);//填充同比增长值
            CalculateAdDataUtil.calAdYoyDataReflex(dto, yoyDto);//填充同比增长率

        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdCalData(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomData(dto, momDto);//填充环比增长率
            CalculateAdDataUtil.calAdMomValueReflex(dto, momDto);//填充环比增长值
        }
        CalculateAdDataUtil.calAdPercentData(dto, summary);
    }

    private List<DashboardAdProductDto> queryTopList(DashboardAdProductReqVo req, Integer puid,
                                                       List<Integer> shopIdList, List<String> marketplaceIdList,
                                                       String currency, int limit,
                                                       DashboardOrderByRateEnum orderField, DashboardDataFieldEnum dataField) {
        List<Object> spArgsListFirst = Lists.newArrayList();
        List<Object> spArgsListSecond = Lists.newArrayList();
        DashboardQueryFieldEnum dashboardQueryField = DashboardQueryFieldEnum.getDashboardCampaignQueryFieldEnumByCode(req.getQueryField());
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(req.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(marketplaceIdList);
        }
        List<String> categoryId = new ArrayList<>();
        if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE.getCode().equals(req.getQueryField())) {
            List<CategoryNameDto> category = odsProductDao.getCategoryByName(puid, Lists.newArrayList("未分类"));
            if (CollectionUtils.isNotEmpty(category)) {
                categoryId = category.stream().map(CategoryNameDto::getFullCid).distinct().collect(Collectors.toList());
            }
        }
        LocalDate categoryQueryDate = LocalDate.now();
        Integer productCommodityRelaNowMonthCount = odsProductDao.getProductCommodityRelaNowMonthCount(puid);
        if (productCommodityRelaNowMonthCount == null || productCommodityRelaNowMonthCount < 1) {
            categoryQueryDate = categoryQueryDate.minusMonths(1);
        }
        String spSubSqlA = odsAmazonAdProductReportDao.adProductQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getStartDate(), req.getEndDate(), spArgsListFirst, dashboardQueryField,
                siteToday, req.getSiteToday(), req.getPortfolioIds(), req.getCampaignIds(), req.getNoZero(), dataField, categoryQueryDate, categoryId);
        spArgsListSecond.addAll(spArgsListFirst);
        String spSubSqlB = odsAmazonAdProductReportDao.adProductQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getYoyStartDate(), req.getYoyEndDate(), spArgsListFirst, dashboardQueryField, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, categoryQueryDate, categoryId);
        Supplier<List<DashboardAdProductTopDataDto>> currentAndYoyList = () -> odsAmazonAdProductReportDao.queryAdProductYoyOrMomTop(spSubSqlA, spSubSqlB,
                spArgsListFirst, dataField, orderField, req.getOrderBy(), limit, req.getNoZero(), dashboardQueryField);
        List<String> groupYoyListSup = Lists.newArrayList();
        LocalDate finalCategoryQueryDate = categoryQueryDate;
        List<String> finalCategoryId = categoryId;
        Supplier<List<DashboardAdProductDto>> momList = () -> odsAmazonAdProductReportDao
                .queryAdProductCharts(puid, shopIdList, marketplaceIdList, groupYoyListSup, currency, req.getMomStartDate(),
                        req.getMomEndDate(), dashboardQueryField, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, finalCategoryQueryDate, finalCategoryId);
        String spSubSqlC = odsAmazonAdProductReportDao.adProductQuerySql(puid, shopIdList, marketplaceIdList, null,
                currency, req.getMomStartDate(),req.getMomEndDate(), spArgsListSecond, dashboardQueryField, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, categoryQueryDate, categoryId);
        Supplier<List<DashboardAdProductTopDataDto>> currentAndMomList = () -> odsAmazonAdProductReportDao.queryAdProductYoyOrMomTop(spSubSqlA, spSubSqlC,
                spArgsListSecond, dataField, orderField, req.getOrderBy(), limit, req.getNoZero(), dashboardQueryField);
        List<String> groupMomIdList = Lists.newArrayList();
        //还需要查同比
        LocalDate finalCategoryQueryDate1 = categoryQueryDate;
        List<String> finalCategoryId1 = categoryId;
        Supplier<List<DashboardAdProductDto>> yoyList = () -> odsAmazonAdProductReportDao.queryAdProductCharts(puid, shopIdList,
                marketplaceIdList, groupMomIdList, currency, req.getYoyStartDate(),req.getYoyEndDate(), dashboardQueryField, null, null, req.getPortfolioIds(), req.getCampaignIds(), null, null, finalCategoryQueryDate1, finalCategoryId1);
        Function<List<String>, Map<String, AsinMainImageDto>> groupNameMapFunc = asinList -> {
            if (CollectionUtils.isEmpty(asinList)) {
                return new HashMap<>();
            }
            List<OdsProduct> odsProductList = odsProductDao.getMainImgByAsinOrSku(puid, shopIdList, DashboardQueryFieldEnum.SKU_QUERY_TYPE == dashboardQueryField ? null : asinList, DashboardQueryFieldEnum.SKU_QUERY_TYPE == dashboardQueryField ? asinList : null);
            return odsProductList.parallelStream().collect(Collectors.toMap(e-> DashboardQueryFieldEnum.SKU_QUERY_TYPE == dashboardQueryField ? e.getSku() : e.getAsin() , g -> AsinMainImageDto.builder()
                    .asin(g.getAsin())
                    .sku(g.getSku())
                    .mainImage(g.getMainImage())
                    .build(),(e1, e2)-> e2));
        };
        return getAdProductTopList(req, req.getPuid(), orderField, currentAndYoyList, groupYoyListSup, momList, currentAndMomList,
                groupMomIdList, yoyList, req.getYoyOverLimit(), groupNameMapFunc);
    }


    private List<DashboardAdProductDto> getAdProductTopList(DashboardAdProductReqVo req, Integer puid, DashboardOrderByRateEnum orderField,
                                                                Supplier<List<DashboardAdProductTopDataDto>> currentAndYoyListSup,List<String> groupYoyListSup,
                                                                Supplier<List<DashboardAdProductDto>> momListSup, Supplier<List<DashboardAdProductTopDataDto>> currentAndMomListSup,
                                                                List<String> groupMomListSup, Supplier<List<DashboardAdProductDto>> yoyListSup,
                                                                boolean yoyOverLimit, Function<List<String>, Map<String, AsinMainImageDto>> asinMainImageMapFunc) {
        //查询当前日期时间段内广告活动图表信息
        //当前时间段查询sql
        List<DashboardAdProductTopDataDto> currentAndSubList;
        List<DashboardAdProductDto> resultList = Lists.newArrayList();
        //如果是按占比排序，即当前时间段值/汇总值,先计算同比,或按同比排序，即当前时间段值/同比时间段值
        if (DashboardOrderByRateEnum.PERCENT == orderField || Stream.of(DashboardOrderByRateEnum.YOY_RATE,
                DashboardOrderByRateEnum.YOY_VALUE).anyMatch(r -> r == orderField)) {
            //同比sql
//            subSqlB = subBSup.get();
            currentAndSubList = currentAndYoyListSup.get();
            List<String> asinList = null;
            if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE.getCode().equals(req.getQueryField())) {
                asinList = currentAndSubList.parallelStream().map(DashboardAdProductTopDataDto::getAsin).map(StringUtil::splitStr)
                        .flatMap(Collection::stream).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
            } else {
                asinList = currentAndSubList.parallelStream().map(DashboardAdProductTopDataDto::getLabel).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(asinList)) {
                groupYoyListSup.addAll(asinList);
                //还需要查询环比
                List<DashboardAdProductDto> momGroupList = momListSup.get();
                Map<String, DashboardAdProductDto> momAsinMap = momGroupList.parallelStream()
                        .collect(Collectors.toMap(DashboardAdProductDto::getLabel, v1 -> v1));
                List<Integer> shopIds = currentAndSubList.stream().map(DashboardAdProductTopDataDto::getShopIds)
                        .filter(StringUtils::isNotBlank).map(StringUtil::splitStr).flatMap(Collection::stream).filter(StringUtils::isNotBlank).map(String::trim).map(Integer::valueOf).distinct().collect(Collectors.toList());
                Map<String, BigDecimal> salesMap = new HashMap<>();
                Map<String, BigDecimal> yoySalesMap = new HashMap<>();
                Map<String, BigDecimal> momSalesMap = new HashMap<>();
                BigDecimal[] sumShopSale = new BigDecimal[]{new BigDecimal(0)};
                Integer[] sumShopSaleNum = new Integer[]{new Integer(0)};
                Map<String, Integer> saleNumMap = new HashMap<>();
                Map<String, Integer> yoySaleNumMap = new HashMap<>();
                Map<String, Integer> momSaleNumMap = new HashMap<>();
                //获取店铺或站点销售额

                if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
                    this.fillSaleMap(req.getShopIdList(), req, salesMap, saleNumMap, yoySalesMap, yoySaleNumMap, momSalesMap, momSaleNumMap, sumShopSale, sumShopSaleNum);
                }

                resultList = currentAndSubList.stream().map(c -> {
                    //当前查询list已经包含了当期及同比的计算属性和汇总属性
                    //还需要计算环比占比，同比占比
                    DashboardAdProductDto momGroup = Optional.ofNullable(momAsinMap.get(c.getLabel())).orElseGet(() -> {
                        DashboardAdProductDto tempDto = new DashboardAdProductDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    DashboardAdProductDto currentDto = new DashboardAdProductDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    DashboardAdProductDto yoyDto = convertBasicAndCalData(c);
                    DashboardAdProductDto summaryDto = convertSummaryData(c);
                    if (StringUtils.isNotBlank(c.getShopIds())) {
                        List<Integer> sids = StringUtil.splitStr(c.getShopIds(), ",").stream().filter(StringUtils::isNotBlank).map(String::trim).filter(StringUtils::isNotBlank).map(Integer::valueOf).distinct().collect(Collectors.toList());
                        sids.forEach(e->{
                            currentDto.setShopSales(MathUtil.add(currentDto.getShopSales(), salesMap.getOrDefault(e.toString(), BigDecimal.ZERO)));
                            currentDto.setShopSaleNum(MathUtil.add(currentDto.getShopSaleNum(), saleNumMap.getOrDefault(e.toString(), 0)));
                            momGroup.setShopSales(MathUtil.add(momGroup.getShopSales(), momSalesMap.getOrDefault(e.toString(), BigDecimal.ZERO)));
                            momGroup.setShopSaleNum(MathUtil.add(momGroup.getShopSaleNum(), momSaleNumMap.getOrDefault(e.toString(), 0)));
                            yoyDto.setShopSales(MathUtil.add(yoyDto.getShopSales(), yoySalesMap.getOrDefault(e.toString(), BigDecimal.ZERO)));
                            yoyDto.setShopSaleNum(MathUtil.add(yoyDto.getShopSaleNum(), yoySaleNumMap.getOrDefault(e.toString(), 0)));
                        });
                    }
                    computeData(currentDto, yoyDto, momGroup, summaryDto, yoyOverLimit, sumShopSale, sumShopSaleNum);
                    return currentDto;
                }).collect(Collectors.toList());
            }
        }

        //如果是按环比排序，即当前时间段值/环比时间段值
        if (Stream.of(DashboardOrderByRateEnum.MOM_RATE,
                DashboardOrderByRateEnum.MOM_VALUE).anyMatch(r -> r == orderField)) {
            currentAndSubList = currentAndMomListSup.get();
            List<String> asinList = null;
            if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE.getCode().equals(req.getQueryField())) {
                asinList = currentAndSubList.parallelStream().map(DashboardAdProductTopDataDto::getAsin).map(StringUtil::splitStr)
                        .flatMap(Collection::stream).filter(StringUtils::isNotBlank).map(String::trim).collect(Collectors.toList());
            } else {
                asinList = currentAndSubList.parallelStream().map(DashboardAdProductTopDataDto::getLabel).collect(Collectors.toList());
            }

            if (CollectionUtils.isNotEmpty(asinList)) {
                groupMomListSup.addAll(asinList);
                //还需要查同比
                List<DashboardAdProductDto> yoyList = yoyListSup.get();
                List<Integer> shopIds = currentAndSubList.stream().map(DashboardAdProductTopDataDto::getShopIds)
                        .filter(StringUtils::isNotBlank).map(StringUtil::splitStr).flatMap(Collection::stream).filter(StringUtils::isNotBlank).map(String::trim).map(Integer::valueOf).distinct().collect(Collectors.toList());
                Map<String, BigDecimal> salesMap = new HashMap<>();
                Map<String, BigDecimal> yoySalesMap = new HashMap<>();
                Map<String, BigDecimal> momSalesMap = new HashMap<>();
                BigDecimal[] sumShopSale = new BigDecimal[]{new BigDecimal(0)};
                Integer[] sumShopSaleNum = new Integer[]{new Integer(0)};
                Map<String, Integer> saleNumMap = new HashMap<>();
                Map<String, Integer> yoySaleNumMap = new HashMap<>();
                Map<String, Integer> momSaleNumMap = new HashMap<>();
                //获取店铺或站点销售额

                if (CollectionUtils.isNotEmpty(req.getShopIdList())) {
                    this.fillSaleMap(req.getShopIdList(), req, salesMap, saleNumMap, yoySalesMap, yoySaleNumMap, momSalesMap, momSaleNumMap, sumShopSale, sumShopSaleNum);
                }

                Map<String, DashboardAdProductDto> yoyMap = yoyList.parallelStream()
                        .collect(Collectors.toMap(DashboardAdProductDto::getLabel, v1 -> v1));
                resultList = currentAndSubList.stream().map(c -> {
                    DashboardAdProductDto yoyGroup = Optional.ofNullable(yoyMap.get(c.getLabel())).orElseGet(() -> {
                        DashboardAdProductDto tempDto = new DashboardAdProductDto();
                        CalculateAdDataUtil.buildZeroAdCalData(tempDto);
                        return tempDto;
                    });
                    DashboardAdProductDto currentDto = new DashboardAdProductDto();
                    BeanUtils.copyProperties(c, currentDto, ParamCopyUtil.checkPropertiesNullOrEmptySuper(c));
                    DashboardAdProductDto monDto = convertBasicAndCalData(c);
                    DashboardAdProductDto summaryDto = convertSummaryData(c);
                    if (StringUtils.isNotBlank(c.getShopIds())) {
                        List<Integer> sids = StringUtil.splitStr(c.getShopIds().trim(), ",").stream().filter(StringUtils::isNotBlank).map(String::trim).filter(StringUtils::isNotBlank).map(Integer::valueOf).distinct().collect(Collectors.toList());
                        sids.forEach(e->{
                            currentDto.setShopSales(MathUtil.add(currentDto.getShopSales(), salesMap.getOrDefault(e.toString(), BigDecimal.ZERO)));
                            currentDto.setShopSaleNum(MathUtil.add(currentDto.getShopSaleNum(), saleNumMap.getOrDefault(e.toString(), 0)));
                            monDto.setShopSales(MathUtil.add(monDto.getShopSales(), momSalesMap.getOrDefault(e.toString(), BigDecimal.ZERO)));
                            monDto.setShopSaleNum(MathUtil.add(monDto.getShopSaleNum(), momSaleNumMap.getOrDefault(e.toString(), 0)));
                            yoyGroup.setShopSales(MathUtil.add(yoyGroup.getShopSales(), yoySalesMap.getOrDefault(e.toString(), BigDecimal.ZERO)));
                            yoyGroup.setShopSaleNum(MathUtil.add(yoyGroup.getShopSaleNum(), yoySaleNumMap.getOrDefault(e.toString(), 0)));
                        });
                    }
                    computeData(currentDto, yoyGroup, monDto, summaryDto, yoyOverLimit, sumShopSale, sumShopSaleNum);
                    return currentDto;
                }).collect(Collectors.toList());
            }
        }

        //设置广告组名称
        List<String> asin = resultList.parallelStream().map(DashboardAdProductDto::getLabel)
                .filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, AsinMainImageDto> asinMap = asinMainImageMapFunc.apply(asin);
        //填充结果中的店铺名称和站点名称
        List<Integer> shopIds = resultList.stream().map(e -> StringUtil.splitStr(e.getShopIds().trim(), ",")).flatMap(Collection::stream).filter(StringUtils::isNotBlank).map(e->Integer.valueOf(e.trim())).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIds));
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        Map<String,String> categoryMap = new HashMap<>();
        if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE.getCode().equals(req.getQueryField())) {
            categoryMap = odsProductDao.getCategoryName(puid, asin).stream().collect(Collectors.toMap(CategoryNameDto::getFullCid, CategoryNameDto::getName, (e1, e2)-> e2));
        }
        Map<String, String> finalCategoryMap = categoryMap;
        resultList.forEach(r -> {
            AsinMainImageDto dto = asinMap.get(r.getLabel());
            List<Integer> list = StringUtil.splitStr(r.getShopIds().trim(), ",").stream().map(String::trim).filter(StringUtils::isNotBlank).map(Integer::valueOf).distinct().collect(Collectors.toList());
            if (Objects.nonNull(dto)) {
                Optional.ofNullable(dto.getMainImage()).ifPresent(r::setMainImage);
            }
            if (CollectionUtils.isNotEmpty(list)) {
                List<DashboardAdProductDto.ShopInfoDto> shopInfoDtos = list.stream().map(e -> {
                    ShopAuth shopAuth = shopAuthMap.get(e);
                    DashboardAdProductDto.ShopInfoDto shopInfoDto = new DashboardAdProductDto.ShopInfoDto();
                    if (shopAuth != null) {
                        shopInfoDto.setShopName(shopAuth.getName());
                        shopInfoDto.setMarketplaceName(shopAuth.getMarketplaceName());
                        shopInfoDto.setMarketplaceId(shopAuth.getMarketplaceId());
                    }
                    shopInfoDto.setShopId(e);
                    return shopInfoDto;
                }).collect(Collectors.toList());
                r.setShopInfos(shopInfoDtos);
            }
            if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE.getCode().equals(req.getQueryField())) {
                r.setLabel(finalCategoryMap.getOrDefault(r.getLabel(), r.getLabel()));
            }
        });
        return resultList;
    }

    private <T> Field[] getAllField(Class<T> cla) {
        Class clazz = cla;
        List<Field> fieldList = new ArrayList<>();
        while (clazz != null){
            fieldList.addAll(new ArrayList<>(Arrays.asList(clazz.getDeclaredFields())));
            clazz = clazz.getSuperclass();
        }
        Field[] fields = new Field[fieldList.size()];
        fieldList.toArray(fields);
        return fields;
    }



    private List<DashboardAdProductDto> getAdProductInfoList(DashboardAdProductReqVo req) {
        //查询广告组top图表数据，且需要区分sp,sb,sd
        Integer puid = req.getPuid();
        List<String> marketplaceIdList = req.getMarketplaceIdList();
        List<Integer> shopIdList = req.getShopIdList();
        String currency = req.getCurrency();
        DashboardOrderByRateEnum orderField = DashboardOrderByRateEnum.rateMap.get(req.getOrderByField());
        DashboardDataFieldEnum dataField = DashboardDataFieldEnum.fieldMap.get(req.getDataField());
        int limit = Optional.ofNullable(req.getLimit()).orElse(0);
        DashboardOrderByEnum orderByEn = DashboardOrderByEnum.orderByMap.get(req.getOrderBy());
        //先获取sp的图表数据
        List<DashboardAdProductDto> resultList = queryTopList(req, puid, shopIdList, marketplaceIdList, currency, limit, orderField, dataField);


        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }

        //填充结果中的店铺名称和站点名称
        List<Integer> shopIds = resultList.stream().map(e -> StringUtil.splitStr(e.getShopIds().trim(), ",")).flatMap(Collection::stream).filter(StringUtils::isNotBlank).map(e->Integer.valueOf(e.trim())).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, new ArrayList<>(shopIds));
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.parallelStream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        Map<String, BigDecimal> salesMap = new HashMap<>();
        Map<String, BigDecimal> yoySalesMap = new HashMap<>();
        Map<String, BigDecimal> momSalesMap = new HashMap<>();
        BigDecimal[] sumShopSale = new BigDecimal[]{new BigDecimal(0)};

        resultList.forEach(r -> {
            List<Integer> list = StringUtil.splitStr(r.getShopIds().trim(), ",").stream().map(String::trim).filter(StringUtils::isNotBlank).map(Integer::valueOf).distinct().collect(Collectors.toList());
            if (DashboardQueryFieldEnum.SKU_QUERY_TYPE.getCode().equals(req.getQueryField())) {
                r.setMsku(r.getLabel());
            } else if (DashboardQueryFieldEnum.ASIN_QUERY_TYPE.getCode().equals(req.getQueryField())) {
                r.setAsin(r.getLabel());
            } else if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE.getCode().equals(req.getQueryField())) {
                r.setParentAsin(r.getLabel());
            } else if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE.getCode().equals(req.getQueryField())) {
                r.setCategory(r.getLabel());
            }
            if (CollectionUtils.isNotEmpty(list)) {
                List<String> shopNames = new ArrayList<>();
                List<String> marketplaceName = new ArrayList<>();
                List<DashboardAdProductDto.ShopInfoDto> shopInfoDtos = list.stream().map(e -> {
                    ShopAuth shopAuth = shopAuthMap.get(e);
                    DashboardAdProductDto.ShopInfoDto shopInfoDto = new DashboardAdProductDto.ShopInfoDto();
                    if (shopAuth != null) {
                        shopNames.add(shopAuth.getName());
                        marketplaceName.add(shopAuth.getMarketplaceName());
                        shopInfoDto.setShopName(shopAuth.getName());
                        shopInfoDto.setMarketplaceName(shopAuth.getMarketplaceName());
                        shopInfoDto.setMarketplaceId(shopAuth.getMarketplaceId());
                    }
                    shopInfoDto.setShopId(e);
                    return shopInfoDto;
                }).collect(Collectors.toList());
                r.setShopInfos(shopInfoDtos);
                r.setShopName(StringUtil.joinString(shopNames));
                r.setMarketplaceName(StringUtil.joinString(marketplaceName));
            }
        });

        //还需要对结果进行排序，取前limit行数据
        Class<DashboardAdProductDto> cla = DashboardAdProductDto.class;
        Field[] fields = getAllField(cla);
        Map<String, Field> fieldMap = Arrays.stream(fields).peek(f -> f.setAccessible(true))
                .filter(e-> !e.isSynthetic()).collect(Collectors.toMap(Field::getName, v1 -> v1));
        StringBuilder compareKey = new StringBuilder(dataField.getCode());
        compareKey.append(orderField.getSuffix());
        resultList = resultList.stream().sorted((o1, o2) -> {
            try {
                int result = 0;
                Field compareField = fieldMap.get(compareKey.toString());
                if (compareField == null) {
                    compareField = fieldMap.get(dataField.getCode());
                }
                if (Objects.nonNull(compareField)) {
                    Object val1 = compareField.get(o1);
                    Object val2 = compareField.get(o2);
                    if(Objects.isNull(val1) && Objects.isNull(val2) ){
                        result = 0;
                    } else if(Objects.nonNull(val1) && Objects.isNull(val2) ){
                        result = 1;
                    } else if(Objects.isNull(val1)){
                        result = -1;
                    } else {
                        if(String.class.isAssignableFrom(compareField.getType())){
                            String rateValue1 = val1.toString().replace("%", "");
                            String rateValue2 = val2.toString().replace("%", "");
                            if ("--".equalsIgnoreCase(rateValue2) || "--".equalsIgnoreCase(rateValue1)) {
                                return 0;
                            } else if("null".equalsIgnoreCase(rateValue2) && "null".equalsIgnoreCase(rateValue1)) {
                                result = 0;
                            } else if(!"null".equalsIgnoreCase(rateValue1) && "null".equalsIgnoreCase(rateValue2) ){
                                result = 1;
                            } else  if("null".equalsIgnoreCase(rateValue1)){
                                result = -1;
                            } else {
                                result = new BigDecimal(rateValue1).compareTo(new BigDecimal(rateValue2));
                            }
                        }
                        if(Integer.class.isAssignableFrom(compareField.getType())){
                            Integer compareVal1 = (Integer) val1;
                            Integer compareVal2 = (Integer) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(BigDecimal.class.isAssignableFrom(compareField.getType())){
                            BigDecimal compareVal1 = (BigDecimal) val1;
                            BigDecimal compareVal2 = (BigDecimal) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                        if(Long.class.isAssignableFrom(compareField.getType())){
                            Long compareVal1 = (Long) val1;
                            Long compareVal2 = (Long) val2;
                            result = compareVal1.compareTo(compareVal2);
                        }
                    }
                }
                if (result == 0) {
                    if(Objects.isNull(o1.getLabel()) && Objects.isNull(o2.getLabel()) ){
                        result = 0;
                    } else if(Objects.nonNull(o1.getLabel()) && Objects.isNull(o2.getLabel()) ){
                        result = 1;
                    } else if(Objects.isNull(o1.getLabel())){
                        result = -1;
                    } else {
                        result = o1.getLabel().compareTo(o2.getLabel());
                        if (result > 1) {
                            result = 1;
                        }
                        if (result < 0) {
                            result = -1;
                        }
                    }

                }
                return result;
            } catch (IllegalAccessException e) {
                log.error("compare product list data error", e);
            }
            return 0;
        }).collect(Collectors.toList());
        int subLimit = Math.min(limit, resultList.size());

        //按请求字段进行升降序
        if ( DashboardOrderByEnum.DESC == orderByEn) {
            Collections.reverse(resultList);
        }
        resultList = resultList.subList(0, subLimit);
        if (StringUtils.isNotBlank(req.getListOrderField()) || StringUtils.isNotBlank(req.getListOrderType())) {
            OrderByUtil.sortedByOrderField(resultList, req.getListOrderField(), req.getListOrderType(), "label");
        }
        return resultList;
    }

    @Override
    public List<String> exportAdProductCharts(DashboardAdProductReqVo reqVo) {
        if (CollectionUtils.isNotEmpty(reqVo.getShopIdList())) {
            List<VcShopAuth> listByIdList = vcShopAuthDao.getListByIdList(reqVo.getShopIdList());
            reqVo.setShopIdList(Lists.newArrayList(reqVo.getShopIdList()));
            if (CollectionUtils.isNotEmpty(listByIdList)) {
                reqVo.getShopIdList().removeAll(listByIdList.stream().map(VcShopAuth::getId).collect(Collectors.toList()));
            }
            if (CollectionUtils.isEmpty(reqVo.getShopIdList())) {
                log.info("剔除vc店铺后无数据puid:{}", reqVo.getPuid());
                return null;
            }
        }
        List<DashboardAdProductDto> groupList = getAdProductInfoList(reqVo);
        groupList.forEach(dataDto ->{
            CalculateAdDataUtil.fillDisplay5Export(dataDto, reqVo.getCurrency());
            CalculateAdDataUtil.calAdCalShopDataForExport(dataDto, reqVo.getCurrency());
        });
        String url = writeExcelAndUpload(groupList, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export product charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private String writeExcelAndUpload(List<DashboardAdProductDto> groupList, DashboardAdProductReqVo reqVo) {
        List<String> headers = new ArrayList<>(baseHeaderList);
        //计算导出列
        if (reqVo.getPercent() != 1) {
            headers = headers.stream().filter(x -> !x.contains("Percent")).collect(Collectors.toList());
        }
        if (reqVo.getMom() != 1) {
            headers = headers.stream().filter(x -> !x.contains("MomRate")).collect(Collectors.toList());
        }
        if (reqVo.getYoy() != 1) {
            headers = headers.stream().filter(x -> !x.contains("YoyRate")).collect(Collectors.toList());
        }

        String fileName = "";
        if (DashboardQueryFieldEnum.SKU_QUERY_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "msku");
            fileName = "MSKU";
        } else if (DashboardQueryFieldEnum.ASIN_QUERY_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "asin");
            fileName = "ASIN";
        } else if (DashboardQueryFieldEnum.PARENT_ASIN_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "parentAsin");
            fileName = "父ASIN";
        } else if (DashboardQueryFieldEnum.PRODUCT_CLASSIFY_TYPE.getCode().equals(reqVo.getQueryField())) {
            headers.add(0, "category");
            fileName = "商品分类";
        }

        String orderBy = Optional.ofNullable(DashboardOrderByEnum.orderByMap.get(reqVo.getOrderBy()))
                .map(DashboardOrderByEnum::getExportDesc).orElse("");
        String limitCnt = Optional.ofNullable(reqVo.getLimit()).map(String::valueOf).orElse("");
        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), groupList, fileName + orderBy + limitCnt,
                DashboardAdProductDto.class, headers, true);

    }

    /**
     * 填充店铺、站点销售额
     */
    private void fillSaleMap(List<Integer> shopIds, DashboardAdProductReqVo req, Map<String, BigDecimal> salesMap,
                             Map<String, Integer> saleNumMap,
                             Map<String, BigDecimal> yoySalesMap, Map<String, Integer> yoySaleNumMap,
                             Map<String, BigDecimal> momSalesMap, Map<String, Integer> momSaleNumMap,
                             BigDecimal[] sumShopSale, Integer[] sumShopSaleNum) {

        List<ShopSaleDto> saleDtos = cpCShopDataService.shopSaleGroupByShop(req.getPuid(), shopIds, null, req.getStartDate(), req.getEndDate(), req.getCurrency());

        List<ShopSaleDto> yoySaleDtos = cpCShopDataService.shopSaleGroupByShop(req.getPuid(), shopIds, null, req.getYoyStartDate(), req.getYoyEndDate(), req.getCurrency());

        List<ShopSaleDto> momSaleDtos = cpCShopDataService.shopSaleGroupByShop(req.getPuid(), shopIds, null, req.getMomStartDate(), req.getMomEndDate(), req.getCurrency());
        saleDtos.forEach(e->{
            salesMap.put(e.getShopId().toString(), e.getSumRange());
            saleNumMap.put(e.getShopId().toString(), e.getSaleNum());
            sumShopSale[0] = MathUtil.sum(sumShopSale[0], e.getSumRange());
            sumShopSaleNum[0] = MathUtil.sum(sumShopSaleNum[0], e.getSaleNum());
        });
        yoySaleDtos.forEach(e->{
            yoySalesMap.put(e.getShopId().toString(), e.getSumRange());
            yoySaleNumMap.put(e.getShopId().toString(), e.getSaleNum());

        });
        momSaleDtos.forEach(e->{
            momSalesMap.put(e.getShopId().toString(), e.getSumRange());
            momSaleNumMap.put(e.getShopId().toString(), e.getSaleNum());
        });

    }



    private void computeData(DashboardAdProductDto dto, DashboardAdProductDto yoyDto,
                             DashboardAdProductDto momDto, DashboardAdProductDto summary,
                             boolean yoyOverLimit, BigDecimal[] sumShopSales, Integer[] sumShopSaleNum) {
        if (Objects.isNull(dto)) {
            return;
        }

        CalculateAdDataUtil.calAdCalShopData(dto);
        if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(dto, null);
            CalculateAdDataUtil.calAdYoyShopData(dto, null);
        } else if (Objects.nonNull(yoyDto)) {
            CalculateAdDataUtil.calAdCalDataScale4(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdYoyValueReflex(dto, yoyDto);//填充环比增长值
            CalculateAdDataUtil.calAdYoyDataReflex(dto, yoyDto);//填充环比增长率
            CalculateAdDataUtil.calAdCalShopDataScale4(yoyDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            calAdYoyShopValueReflex(dto, yoyDto);//填充环比增长值
            calAdYoyShopDataReflex(dto, yoyDto);//填充环比增长率
        }
        if (Objects.nonNull(momDto)) {
            CalculateAdDataUtil.calAdCalDataScale4(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            CalculateAdDataUtil.calAdMomValueReflex(dto, momDto);//填充同比增长值
            CalculateAdDataUtil.calAdMomDataReflex(dto, momDto);//填充同比增长率
            CalculateAdDataUtil.calAdCalShopDataScale4(momDto);//同比数据只包含基本数据查结构，需要先计算出计算属性值
            calAdMomShopValueReflex(dto, momDto);//填充同比增长值
            calAdMomShopDataReflex(dto, momDto);//填充同比增长率
        }
        CalculateAdDataUtil.calAdPercentData(dto, summary);
        //设置店铺销售额占比
        log.info("total sales about all shop is:{}", sumShopSales[0]);
        dto.setShopSalesPercent(CalculateUtil.calPercentStr4Decimal(dto.getShopSales(), sumShopSales[0]));
        dto.setShopSaleNumPercent(CalculateUtil.calPercentStr4Int(dto.getShopSaleNum(), sumShopSaleNum[0]));
    }

    private void calAdMomShopDataReflex(DashboardAdProductDto cur, DashboardAdProductDto mom) {
        cur.setShopSalesMomRate(CalculateUtil.calRate4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsMomRate(CalculateUtil.calRate4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAsotsMomRate(CalculateUtil.calRate4Decimal(cur.getAsots(), mom.getAsots()));
        cur.setShopSaleNumMomRate(CalculateUtil.calRate4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));
    }


    private void calAdMomShopValueReflex(DashboardAdProductDto cur, DashboardAdProductDto mom) {
        cur.setShopSalesMomValue(CalculateUtil.calValueStr4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsMomValue(CalculateUtil.calValueStr4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAcotsMomValue(CalculateUtil.calValueStr4Decimal(cur.getAsots(), mom.getAsots()));
        cur.setShopSaleNumMomValue(CalculateUtil.calValueStr4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));

    }


    private void calAdYoyShopValueReflex(DashboardAdProductDto cur, DashboardAdProductDto mom) {
        cur.setShopSalesYoyValue(CalculateUtil.calValueStr4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsYoyValue(CalculateUtil.calValueStr4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAcotsYoyValue(CalculateUtil.calValueStr4Decimal(cur.getAsots(), mom.getAsots()));
        cur.setShopSaleNumYoyValue(CalculateUtil.calValueStr4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));

    }


    private void calAdYoyShopDataReflex(DashboardAdProductDto cur, DashboardAdProductDto mom) {
        cur.setShopSalesYoyRate(CalculateUtil.calRate4Decimal(cur.getShopSales(), mom.getShopSales()));
        cur.setAcotsYoyRate(CalculateUtil.calRate4Decimal(cur.getAcots(), mom.getAcots()));
        cur.setAsotsYoyRate(CalculateUtil.calRate4Decimal(cur.getAsots(), mom.getAsots()));
        cur.setShopSaleNumYoyRate(CalculateUtil.calRate4Int(cur.getShopSaleNum(), mom.getShopSaleNum()));
    }


}
