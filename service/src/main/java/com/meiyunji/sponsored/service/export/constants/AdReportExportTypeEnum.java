package com.meiyunji.sponsored.service.export.constants;

/**
 * @author: sun<PERSON><PERSON>
 * @email: <EMAIL>
 * @date: 2023-09-12  11:04
 */


public enum AdReportExportTypeEnum {

    SP_CAMPAIGN("sp_campaign", "SP_广告活动报告", ConvertBeanIdConstant.SP_CAMPAIGN),
    SP_ADGROUP("sp_adgroup", "SP_广告组报告", ConvertBeanIdConstant.SP_ADGROUP),
    SP_PRODUCT("sp_product", "SP_广告产品报告", ConvertBeanIdConstant.SP_PRODUCT),
    SP_TARGETING("sp_targeting", "SP_投放报告", ConvertBeanIdConstant.SP_TARGETING),
    SP_SPACE("sp_space", "SP_广告位报告", ConvertBeanIdConstant.SP_SPACE),
    AMAZON_BUSINESS("amazon_business", "SP_企业购报告", ConvertBeanIdConstant.AMAZON_BUSINESS),
    SP_SEARCHTERM("sp_searchterm", "SP_搜索词报告", ConvertBeanIdConstant.SP_SEARCHTERM),
    SP_PURCHASED("sp_purchased", "SP_已购买商品报告", ConvertBeanIdConstant.SP_PURCHASED),
    AD_FLOW("ad_flow", "ad_总流量与无效流量报告", ConvertBeanIdConstant.AD_FLOW),

    SB_CAMPAIGN("sb_campaign", "SB_广告活动报告", ConvertBeanIdConstant.SB_CAMPAIGN),
    SB_TARGETING("sb_targeting", "SB_投放报告", ConvertBeanIdConstant.SB_TARGETING),
    SB_SPACE("sb_space", "SB_广告位报告", ConvertBeanIdConstant.SB_SPACE),
    SB_SEARCHTERM("sb_searchterm", "SB_搜索词报告", ConvertBeanIdConstant.SB_SEARCHTERM),

    SBV_CAMPAIGN("sbv_campaign", "SBV_广告活动报告", ConvertBeanIdConstant.SBV_CAMPAIGN),
    SBV_TARGETING("sbv_targeting", "SBV_投放报告", ConvertBeanIdConstant.SBV_TARGETING),
    SBV_SPACE("sbv_space", "SBV_广告位报告", ConvertBeanIdConstant.SBV_SPACE),
    SBV_SEARCHTERM("sbv_searchterm", "SBV_搜索词报告", ConvertBeanIdConstant.SBV_SEARCHTERM),

    SD_CAMPAIGN("sd_campaign", "SD_广告活动报告", ConvertBeanIdConstant.SD_CAMPAIGN),
    SD_ADGROUP("sd_adgroup", "SD_广告组报告", ConvertBeanIdConstant.SD_ADGROUP),
    SD_PRODUCT("sd_product", "SD_广告产品报告", ConvertBeanIdConstant.SD_PRODUCT),
    SD_PURCHASED("sd_purchased", "SD_已购买商品报告", ConvertBeanIdConstant.SD_PURCHASED),

    SD_TARGETING("sd_targeting", "SD_投放报告", ConvertBeanIdConstant.SD_TARGETING),
    SD_MATCHED("sd_searchterm", "SD_匹配的目标报告", ConvertBeanIdConstant.SD_MATCHED),


    SD_TARGETING_LIST("sd_targeting_list", "SD_投放报告", ""),
    ;


    private String reportType;

    private String reportName;

    private String convertBeanName;

    AdReportExportTypeEnum(String reportType, String reportName, String convertBeanName) {
        this.reportType = reportType;
        this.reportName = reportName;
        this.convertBeanName = convertBeanName;
    }

    public String getReportType() {
        return reportType;
    }

    public String getReportName() {
        return reportName;
    }

    public String getConvertBeanName() {
        return convertBeanName;
    }
}
