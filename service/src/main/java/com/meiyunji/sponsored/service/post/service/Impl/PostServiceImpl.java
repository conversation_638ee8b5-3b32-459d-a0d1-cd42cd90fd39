package com.meiyunji.sponsored.service.post.service.Impl;

import com.amazon.advertising.mode.post.*;
import com.amazon.advertising.posts.*;
import com.amazon.advertising.posts.enums.PostListFilterFieldEnum;
import com.amazon.advertising.posts.enums.PostListFilterTypeEnum;
import com.amazon.advertising.posts.enums.PostListSortFieldEnum;
import com.amazon.advertising.posts.enums.PostListSortOrderEnum;
import com.amazon.advertising.sb.entity.brands.BrandResult;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.common.vo.grap.GrayModule;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveUserDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cache.UserPlanTypeCacheService;
import com.meiyunji.sponsored.service.cpc.constants.AdManagePageExportTaskTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CpcProductDto;
import com.meiyunji.sponsored.service.cpc.dto.GetFbaReviewInfoReq;
import com.meiyunji.sponsored.service.cpc.dto.GetFbaReviewInfoResp;
import com.meiyunji.sponsored.service.cpc.dto.ProductStatusDto;
import com.meiyunji.sponsored.service.cpc.manager.DataDomainManager;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.IAdManagePageExportTaskService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcProductApiService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbStoreServiceImpl;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.export.handler.PostPageExportTaskHandler;
import com.meiyunji.sponsored.service.post.enums.PostProductStatusEnum;
import com.meiyunji.sponsored.service.post.enums.PostStatusEnum;
import com.meiyunji.sponsored.service.post.po.BrandsPo;
import com.meiyunji.sponsored.service.post.po.ChildAsin;
import com.meiyunji.sponsored.service.post.po.MediaPo;
import com.meiyunji.sponsored.service.post.po.ProductPo;
import com.meiyunji.sponsored.service.post.request.UnpublishPostRequest;
import com.meiyunji.sponsored.service.post.request.*;
import com.meiyunji.sponsored.service.post.response.*;
import com.meiyunji.sponsored.service.post.service.ICpcPostApiService;
import com.meiyunji.sponsored.service.post.service.IPostService;
import com.meiyunji.sponsored.service.post.utils.PostChartDataProcessUtil;
import com.meiyunji.sponsored.service.post.vo.AsinProductVo;
import com.meiyunji.sponsored.service.post.vo.PostChartVo;
import com.meiyunji.sponsored.service.post.vo.PostsAggregateVo;
import com.meiyunji.sponsored.service.util.GrayUtil;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static cn.hutool.core.thread.ThreadUtil.sleep;

/**
 * 帖子服务
 *
 * @Author: heqiwen
 * @Date: 2025/04/02 09:35
 */
@Service
@Slf4j
public class PostServiceImpl implements IPostService {

    @Autowired
    private IAmazonPostProfilesDao amazonPostProfilesDao;
    @Autowired
    private IAmazonPostsProductsDao amazonPostsProductsDao;
    @Autowired
    private IAmazonPostsDao amazonPostsDao;
    @Autowired
    private IAmazonPostsReportDao amazonPostsReportDao;
    @Autowired
    private PostChartDataProcessUtil postChartDataProcessUtil;
    @Autowired
    private IAdManagePageExportTaskService adManagePageExportTaskService;
    @Autowired
    private IShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private ICpcPostApiService cpcPostApiService;
    @Autowired
    private IAmazonPostsMediaDao amazonPostsMediaDao;
    @Autowired
    private ISlaveUserDao slaveUserDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private UserPlanTypeCacheService userPlanTypeCacheService;
    @Autowired
    private IAmazonAdPostReportSyncRecordDao amazonAdPostReportSyncRecordDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private DataDomainManager dataDomainManager;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private ICpcProductApiService cpcProductApiService;
    @Autowired
    private CpcSbStoreServiceImpl cpcSbStoreService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplateNew;
    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;

    private static final int BATCH_QUERY_SIZE = 100;
    private static final int BATCH_INSERT_SIZE = 100;
    // 同步最小开始时间
    private static final LocalDateTime MIN_START_TIME = LocalDateTime.of(2025, 1, 1, 0, 0, 0);
    // 首次同步，同步近2年数据
    private static final int FIRST_SYNC_YEAR_DURATION = 2;
    // 增量同步，同步最近2天数据
    private static final int INCREMENT_SYNC_DAY_DURATION = 2;

    /**
     * 列表页品牌下拉查询
     *
     * @param req
     * @return
     */
    public GetBrandsResponse multiShopGetBrands(GetBrandsRequest req) {
        GetBrandsResponse response = new GetBrandsResponse();
        try {
            List<GetBrandsResponse.BrandsVo> brandsVos = amazonPostProfilesDao.multiShopGetBrands(req.getPuid(), req.getShopIdList());
            if (CollectionUtils.isEmpty(brandsVos)) {
                log.info("品牌列表为空");
                return response;
            }
            response.setBrandsVo(brandsVos);
            return response;
        } catch (Exception e) {
            log.error("puid:{} shopId:{},获取品牌列表异常:{}", req.getPuid(), req.getShopIdList(), e);
            return response;
        }
    }

    /**
     * 创建帖子接口品牌查询
     *
     * @param req
     * @return
     */
    public GetBrandsResponse getBrands(GetBrandsRequest req) {
        GetBrandsResponse response = new GetBrandsResponse();
        try {
            List<GetBrandsResponse.BrandsVo> brandsVos = amazonPostProfilesDao.getBrands(req.getPuid(), req.getShopIdList());
            if (CollectionUtils.isEmpty(brandsVos)) {
                log.info("品牌列表为空");
                return response;
            }
            Result<List<BrandResult>> result = cpcSbStoreService.getBrand(req.getPuid(), req.getShopIdList().get(0));
            Map<String, BrandResult> map = StreamUtil.toMap(result.getData(), BrandResult::getBrandRegistryName);
            for (GetBrandsResponse.BrandsVo brandsVo : brandsVos) {
                if (map.containsKey(brandsVo.getName())) {
                    BrandResult brandResult = map.get(brandsVo.getName());
                    brandsVo.setBrandEntityId(brandResult.getBrandEntityId());
                }
            }
            response.setBrandsVo(brandsVos);
            return response;
        } catch (Exception e) {
            log.error("puid:{} shopId:{},获取品牌列表异常:", req.getPuid(), req.getShopIdList(), e);
            return response;
        }
    }

    /**
     * 帖子列表页查询
     *
     * @param req
     * @return
     */
    public GetPostsResponse pageList(GetPostsRequest req) {
        GetPostsResponse response = new GetPostsResponse();
        Page<GetPostsResponse.Posts> page = new Page<>(req.getPageNo(), req.getPageSize());
        List<BrandsPo> brandsPos = new ArrayList<>();
        GetPostsResponse responseFilter = new GetPostsResponse();
        try {
            // 筛选模板直接过滤 不继续走下面的逻辑
            responseFilter = filterPosts(req, response, page);
            if (Objects.nonNull(req.getFilterDto().getOnlyCount()) && Boolean.TRUE.equals(req.getFilterDto().getOnlyCount())) {
                Integer count = amazonPostsDao.listAllByConditions(req.getFilterDto());
                page.setTotalSize(count);
                page.setTotalPage(0);
                response.setPage(page);
                return response;
            }
            responseFilter = filterPosts(req, response, page);
            if (responseFilter != null) return responseFilter;
            // 分页查询
            Page<GetPostsResponse.Posts> pageList = amazonPostsDao.pageList(req.getPageNo(), req.getPageSize(), req.getFilterDto());
            List<GetPostsResponse.Posts> posts = pageList.getRows();
            if (CollectionUtils.isEmpty(posts)) {
                response.setPage(page);
                return response;
            }
            List<String> postIds = new ArrayList<>();
            Set<String> postProfileIds = new HashSet<>();
            Set<Integer> uids = new HashSet<>();
            Set<Integer> shopIds = new HashSet<>();
            posts.forEach(post -> {
                postIds.add(post.getPostId());
                postProfileIds.add(post.getPostProfileId());
                uids.add(post.getUid());
                shopIds.add(post.getShopId());
            });
            // 根据分页查询的数据去查询基础数据
            List<GetPostsResponse.Posts> baseList = amazonPostsDao.getPostDetailByPostId(req.getFilterDto().getPuid(), req.getFilterDto().getShopIdList(),  new ArrayList<>(postProfileIds), postIds);
            Map<String, GetPostsResponse.Posts> baseDataMap = StreamUtil.toMap(baseList, i -> i.getPostProfileId() + i.getPostId());
            // 根据分页查询的数据去查询报告数据
            List<GetPostsResponse.Posts> reportList = amazonPostsReportDao.getReportByPostIds(req.getFilterDto(), new ArrayList<>(postProfileIds), postIds);
            Map<String, GetPostsResponse.Posts> reportMap = StreamUtil.toMap(reportList, i -> i.getPostProfileId() + i.getPostId());
            // 查询店铺数据
            Map<Integer, ShopAuthBo> shopMap = StreamUtil.toMap(shopAuthDao.getShopAuthBoByIds(req.getFilterDto().getPuid(), new ArrayList<>(shopIds)), ShopAuthBo::getId);
            //查询品牌数据
            brandsPos = amazonPostProfilesDao.getBrandsByPostProfileIds(req.getFilterDto().getPuid(), new ArrayList<>(shopIds), postProfileIds, postIds);
            Map<String, BrandsPo> brandMap = StreamUtil.toMap(brandsPos, BrandsPo::getPostId);
            // 查询产品数据
            List<ProductPo> asins = amazonPostsProductsDao.getAsinsByPostIds(req.getFilterDto().getPuid(), new ArrayList<>(shopIds), postProfileIds, postIds);
            Map<String, List<ProductPo>> asinMap = StreamUtil.groupingBy(asins, ProductPo::getPostId);
            // 查询媒体数据
            List<MediaPo> medias = amazonPostsMediaDao.getMediaInfo(req.getFilterDto().getPuid(), new ArrayList<>(shopIds), postProfileIds, postIds);
            Map<String, MediaPo> mediaMap = StreamUtil.toMap(medias, MediaPo::getPostId);
            // 查询创建人名称信息
            List<GetUserInfoResponse.UserInfo> userInfos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(uids)) {
                userInfos = userDao.getUserInfoByIds(req.getFilterDto().getPuid(), uids);
            }
            Map<Integer, GetUserInfoResponse.UserInfo> userMap = StreamUtil.toMap(userInfos, GetUserInfoResponse.UserInfo::getUid);
            // 根据post_id 组装基础数据和报告数据
            posts.forEach(post -> {
                String postId = post.getPostId();
                String postProfileId = post.getPostProfileId();
                String key = postProfileId + postId;
                Integer shopId = post.getShopId();
                Integer uid = post.getUid();
                // 基础数据
                GetPostsResponse.Posts baseData = baseDataMap.get(key);
                if (baseData != null) {
                    post.setId(baseData.getId());
                    post.setLiveDate(baseData.getLiveDate());
                    post.setPostCreatedDate(baseData.getPostCreatedDate());
                    post.setRemark(baseData.getRemark());
                    post.setStatus(baseData.getStatus());
                    post.setMediaUrl(baseData.getMediaUrl());
                    post.setCaption(baseData.getCaption());
                    post.setStatusMetadata(baseData.getStatusMetadata());
                    // 设置拒绝原因
                    if ("REJECTED".equals(baseData.getStatus()) && Objects.nonNull(baseData.getStatusMetadata())) {
                        StatusMetadata statusMetadata = JSONUtil.jsonToObject(baseData.getStatusMetadata(), StatusMetadata.class);
                        if (Objects.nonNull(statusMetadata)) {
                            String reason = "";
                            for (RejectionReason rejectionReason : statusMetadata.getRejectionReasons()) {
                                reason = rejectionReason.getDetail() + ";";
                            }
                            post.setRejectionReasons(reason);
                        }
                    }
                }
                // 报告数据
                GetPostsResponse.Posts postData = reportMap.get(key);
                if (postData != null) {
                    post.setImpressions(postData.getImpressions());
                    post.setClicksToFollow(postData.getClicksToFollow());
                    post.setClicksToDetailPage(postData.getClicksToDetailPage());
                    post.setClicks(postData.getClicks());
                    post.setClicksToBrandStore(postData.getClicksToBrandStore());
                    post.setReach(postData.getReach());
                    if (!"0".equals(postData.getCtr()) && postData.getCtr() != null) {
                        BigDecimal bdCtr = new BigDecimal(postData.getCtr()).multiply(new BigDecimal("100").setScale(-2, RoundingMode.DOWN));
                        post.setCtr(bdCtr.toPlainString());
                    } else {
                        post.setCtr("0");
                    }
                } else {
                    post.setImpressions("0");
                    post.setClicksToFollow("0");
                    post.setClicksToDetailPage("0");
                    post.setClicks("0");
                    post.setClicksToBrandStore("0");
                    post.setReach("0");
                    post.setCtr("0");
                }
                // 品牌数据
                BrandsPo brandsPo = brandMap.get(postId);
                if (brandsPo != null) {
                    post.setBrandName(brandsPo.getBrandName());
                }
                // 产品信息
                List<ProductPo> productPos = asinMap.get(postId);
                if (productPos != null) {
                    List<String> asinList = productPos.stream().map(ProductPo::getAsin).collect(Collectors.toList());
                    post.setAsinList(asinList);
                }
                // 媒体信息
                MediaPo mediaPos = mediaMap.get(postId);
                if (mediaPos != null) {
                    post.setMediaId(mediaPos.getMediaId());
                    post.setMediaType(mediaPos.getMediaType());
                }
                // 店铺信息
                ShopAuthBo shopAuthBo = shopMap.get(shopId);
                if (shopAuthBo != null) {
                    post.setShopId(shopId);
                    post.setShopName(shopAuthBo.getName());
                }
                // 创建人信息
                GetUserInfoResponse.UserInfo userInfo = userMap.get(uid);
                if (userInfo != null) {
                    post.setCreator(userInfo.getName());
                } else {
                    post.setCreator("-");
                }
            });
            page.setRows(posts);
            page.setTotalSize(pageList.getTotalSize());
            page.setTotalPage(pageList.getTotalPage());
            response.setPage(page);
            return response;
        } catch (Exception e) {
            log.error("puid:{} shopId:{},获取帖子列表页查询异常:", req.getFilterDto().getPuid(), req.getFilterDto().getShopIdList(), e);
            response.setPage(page);
            return response;
        }
    }

    /**
     * 帖子列表页图表数据查询
     *
     * @param req
     * @return
     */
    public GetPostsChartResponse getChartData(GetPostsRequest req) {
        GetPostsChartResponse response = new GetPostsChartResponse();
        PostsAggregateVo aggregateVo = new PostsAggregateVo();
        List<PostChartVo> dayList = new ArrayList<>();
        List<PostChartVo> weekList = new ArrayList<>();
        List<PostChartVo> monthList = new ArrayList<>();
        // 前置过滤postId
        try {
            filterPosts(req, response);
            List<ProductPo> posts = amazonPostsDao.getPostIds(req.getPageNo(), req.getPageSize(), req.getFilterDto());
            List<String> postIds = new ArrayList<>();
            Set<String> postProfileIds = new HashSet<>();
            if (CollectionUtils.isNotEmpty(posts)) {
                posts.forEach(post -> {
                    postProfileIds.add(post.getPostProfileId());
                    postIds.add(post.getPostId());
                });
            } else {
                postProfileIds.add("0");
                postIds.add("0");
            }
            req.getFilterDto().setPostIds(postIds);
            req.getFilterDto().setPostProfileIds(new ArrayList<>(postProfileIds));
            // 组装图表数据
            buildChartData(response, req);
            // 查询汇总数据
            buildAggregateData(response, req);
            return response;
        } catch (Exception e) {
            log.error("puid:{} shopId:{},获取帖子列表页图表数据查询异常:", req.getFilterDto().getPuid(), req.getFilterDto().getShopIdList(), e);
            response.setAggregateVo(aggregateVo);
            response.setDay(dayList);
            response.setWeek(weekList);
            response.setMonth(monthList);
            return response;
        }
    }

    /**
     * 列表页导出
     * {@link PostPageExportTaskHandler#export(AdManagePageExportTask)}
     * @param req
     * @return
     */
    public Result<String> pageExport(GetPostsRequest req) {
        try {
            PostPageFilterParam filterDto = req.getFilterDto();
            Long id = adManagePageExportTaskService.saveExportTask(filterDto.getPuid(), filterDto.getExportUid(), 0, AdManagePageExportTaskTypeEnum.POST, filterDto.getStartDate(), filterDto.getEndDate(), req);
            if (Objects.isNull(id)) {
                throw new SponsoredBizException("帖子导出新建任务异常，请联系管理员");
            }
            return ResultUtil.success("success", filterDto.getUuid());
        } catch (Exception e) {
            throw new SponsoredBizException("帖子导出新建任务异常，请联系管理员");
        }
    }

    /**
     * 帖子备注更新
     *
     * @param req
     * @return
     */
    public Result<String> updateRemark(UpdateRemarkRequest req) {
        try {
            int updateCount = amazonPostsDao.updateRemark(req.getPuid(), req.getId(), req.getContent(), req);
            if (updateCount > 0) {
                return ResultUtil.success("success");
            } else {
                return ResultUtil.error("更新失败");
            }
        } catch (Exception e) {
            throw new SponsoredBizException("帖子帖子修改备注失败，请联系管理员");
        }
    }

    /**
     * 汇总图表前置过滤
     *
     * @param req
     * @param response
     * @return
     */
    private void filterPosts(GetPostsRequest req, GetPostsChartResponse response) {
        List<BrandsPo> brandsPos;
        PostsAggregateVo aggregateVo = new PostsAggregateVo();
        List<PostChartVo> day = new ArrayList<>();
        List<PostChartVo> week = new ArrayList<>();
        List<PostChartVo> month = new ArrayList<>();
        response.setAggregateVo(aggregateVo);
        response.setDay(day);
        response.setWeek(week);
        response.setMonth(month);
        List<String> postProfileIds = new ArrayList<>();
        List<String> postIds = new ArrayList<>();
        // 如果有品牌筛选
        if (CollectionUtils.isNotEmpty(req.getFilterDto().getBrandNameList()) && CollectionUtils.isNotEmpty(req.getFilterDto().getBrandIdList())) {
            List<BrandsPo> brandsPoList = new ArrayList<>();
            for (int i = 0; i < req.getFilterDto().getBrandNameList().size(); i++) {
                BrandsPo brandsPo = new BrandsPo();
                brandsPo.setBrandName(req.getFilterDto().getBrandNameList().get(i));
                brandsPo.setBrandId(req.getFilterDto().getBrandIdList().get(i));
                brandsPoList.add(brandsPo);
            }
            brandsPos = amazonPostProfilesDao.getProfileByBrand(req, brandsPoList);
            if (CollectionUtils.isEmpty(brandsPos)) {
                postProfileIds.add("0");
                postIds.add("0");
                req.getFilterDto().setPostIds(postIds);
                req.getFilterDto().getPostProfileIds().retainAll(postProfileIds);
                return;
            }
            req.getFilterDto().setPostProfileIds(brandsPos.stream().map(BrandsPo::getPostProfileId).collect(Collectors.toList()));
        }
        // 如果有ASIN筛选
        List<ProductPo> productPos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getFilterDto().getAsinList())) {
            productPos = amazonPostsProductsDao.getProfileByAsins(req, req.getFilterDto().getAsinList());
            if (CollectionUtils.isEmpty(productPos)) {
                postProfileIds.add("0");
                postIds.add("0");
                req.getFilterDto().setPostIds(postIds);
                req.getFilterDto().setPostProfileIds(postProfileIds);
                return;
            }

            productPos.forEach(productPo -> {
                postIds.add(productPo.getPostId());
                postProfileIds.add(productPo.getPostProfileId());
            });
            req.getFilterDto().setPostIds(postIds);
            req.getFilterDto().setPostProfileIds(postProfileIds);
        }
    }

    /**
     * 列表页前置过滤
     *
     * @param req
     * @param response
     * @param page
     * @return
     */
    private GetPostsResponse filterPosts(GetPostsRequest req, GetPostsResponse response, Page<GetPostsResponse.Posts> page) {
        List<BrandsPo> brandsPos;
        // 如果有品牌筛选
        if (CollectionUtils.isNotEmpty(req.getFilterDto().getBrandNameList()) && CollectionUtils.isNotEmpty(req.getFilterDto().getBrandIdList())) {
            List<BrandsPo> brandsPoList = new ArrayList<>();
            for (int i = 0; i < req.getFilterDto().getBrandNameList().size(); i++) {
                BrandsPo brandsPo = new BrandsPo();
                brandsPo.setBrandName(req.getFilterDto().getBrandNameList().get(i));
                brandsPo.setBrandId(req.getFilterDto().getBrandIdList().get(i));
                brandsPoList.add(brandsPo);
            }
            brandsPos = amazonPostProfilesDao.getProfileByBrand(req, brandsPoList);
            if (CollectionUtils.isEmpty(brandsPos)) {
                response.setPage(page);
                return response;
            }
            req.getFilterDto().setPostProfileIds(brandsPos.stream().map(BrandsPo::getPostProfileId).collect(Collectors.toList()));
        }
        // 如果有ASIN筛选
        List<ProductPo> productPos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(req.getFilterDto().getAsinList())) {
            productPos = amazonPostsProductsDao.getProfileByAsins(req, req.getFilterDto().getAsinList());
            if (CollectionUtils.isEmpty(productPos)) {
                response.setPage(page);
                return response;
            }
            List<String> profileIds = new ArrayList<>();
            List<String> postIds = new ArrayList<>();
            productPos.forEach(productPo -> {
                postIds.add(productPo.getPostId());
                profileIds.add(productPo.getPostProfileId());
            });
            req.getFilterDto().setPostIds(postIds);
            req.getFilterDto().setPostProfileIds(profileIds);
        }
        return null;
    }

    /**
     * 组装图表数据
     *
     * @param response
     * @param req
     */
    private void buildChartData(GetPostsChartResponse response, GetPostsRequest req) {
        // 查询图表数据
        List<PostsAggregateVo> reportDayList = amazonPostsReportDao.getChartData(req.getFilterDto().getPuid(), req);
        // 日期格式转换
        for (PostsAggregateVo dto : reportDayList) {
            dto.setCountDate(DateUtil.dateStringFormat(dto.getCountDate(), DateUtil.PATTERN, DateUtil.PATTERN_YYYYMMDD));
        }
        // 获取chart数据
        List<PostChartVo> dayPerformanceVos = postChartDataProcessUtil.getDayPerformanceVos(reportDayList);
        List<PostChartVo> weekPerformanceVos = postChartDataProcessUtil.getWeekPerformanceVos(req.getFilterDto().getStartDate(), req.getFilterDto().getEndDate(), reportDayList);
        List<PostChartVo> monthPerformanceVos = postChartDataProcessUtil.getMonthPerformanceVos(reportDayList);
        response.setDay(dayPerformanceVos);
        response.setWeek(weekPerformanceVos);
        response.setMonth(monthPerformanceVos);
    }

    /**
     * 组装汇总数据
     *
     * @param response
     * @param req
     */
    private void buildAggregateData(GetPostsChartResponse response, GetPostsRequest req) {
        // 查询统计数据
        PostsAggregateVo aggregateVo = amazonPostsReportDao.getAggregateReport(req.getFilterDto());
//        aggregateVo.setCurrency(CurrencyType.USD.code());
        aggregateVo.setCtr(aggregateVo.getImpressions() == 0 || aggregateVo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(aggregateVo.getClicks()).multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(aggregateVo.getImpressions()), 2, BigDecimal.ROUND_HALF_UP));
        response.setAggregateVo(aggregateVo);
    }

    /**
     * 创建页面 - 保存草稿
     *
     * @param req
     * @return
     */
    public Result<AmazonPost> submitForDraft(SubmitForDraftPostRequest req) {
        try {
            int puid = req.getPuid();
            int shopId = req.getShopId();
            // 校验店铺权限
            ShopAuth shop = shopAuthDao.getByIdAndPuid(shopId, puid);
            if (Objects.isNull(shop)) {
                return ResultUtil.error("没有CPC授权");
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
            if (Objects.isNull(profile)) {
                return ResultUtil.error("没有站点对应的配置信息");
            }
            //校验商品信息
            Result<String> msg = checkProducts(req.getProducts(), shop, profile, req.getPuid());
            if (msg != null)  ResultUtil.error(msg);
            CreatePostResponse response = cpcPostApiService.createPost(req, shop, profile);
            //数据入库
            if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && Objects.nonNull(response.getData().getPost()) && response.getStatusCode() == 200) {
                List<AmazonPost> amazonPosts = new ArrayList<>();
                List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                AmazonPostsProfiles postsProfiles = new AmazonPostsProfiles();
                postsProfiles.setPostProfileId(req.getPostProfileId());
                AmazonPost post = buildAmazonPostByPost(response.getData().getPost(), req.getPuid(), shop, postsProfiles, req.getUid(), false);
                amazonPosts.add(post);
                try {
                    // 帖子信息入库
                    amazonPostsDao.upsertPostsForCreate(amazonPosts);
                    // asin信息入库
                    replacePostProducts(response.getData().getPost(), req.getPuid(), shop, postsProfiles);
                    // 帖子媒体信息入库
                    amazonPostMedias.addAll(buildPostMedias(response.getData().getPost(), puid, shop, postsProfiles));
                    amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                    return ResultUtil.success("success", post);
                } catch (Exception e) {
                    log.error("puid:{} shopId:{},保存草稿异常:", req.getPuid(), req.getShopId(), e);
                    return ResultUtil.error("帖子保存草稿失败");
                }
            } else if (Objects.nonNull(response) && response.getStatusCode() == 429) {
                return ResultUtil.error("当前亚马逊请求过多，请稍后重试");
            } else {
                return ResultUtil.error(response.getStatusMessage());
            }
        } catch (Exception e) {
            log.error("puid:{} shopId:{},保存草稿异常:{}", req.getPuid(), req.getShopId(), e);
            throw new SponsoredBizException("帖子保存草稿失败，请联系管理员");
        }
    }

    /**
     * 创建页面 - 提交帖子
     *
     * @param req
     * @return
     */
    @Override
    public Result<String> submitForReview(SubmitForDraftPostRequest req) {
        int puid = req.getPuid();
        int shopId = req.getShopId();
        try {
            // 校验店铺权限
            ShopAuth shop = shopAuthDao.getByIdAndPuid(shopId, puid);
            if (Objects.isNull(shop)) {
                return ResultUtil.error("没有CPC授权");
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
            if (Objects.isNull(profile)) {
                return ResultUtil.error("没有站点对应的配置信息");
            }
            if (CollectionUtils.isEmpty(req.getProducts())) {
                return ResultUtil.error("未选择广告产品");
            }
            //校验商品信息
            Result<String> msg = checkProducts(req.getProducts(), shop, profile, req.getPuid());
            if (msg != null) return msg;
            //创建帖子
            Result<AmazonPost> result = submitForDraft(req);
            // 创建成功，且数据已入库
            if (Objects.nonNull(result.getData())) {
                // 获取帖子version字段
                GetPostResponse response = cpcPostApiService.getPostVersion(req.getPuid(), shop, profile, result.getData().getPostId());
                if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && Objects.nonNull(response.getData().getPost()) && response.getStatusCode() == 429) {
                    return ResultUtil.error("当前亚马逊请求过多，请稍后重试");
                } else if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && Objects.nonNull(response.getData().getPost()) && response.getStatusCode() != 200) {
                    return ResultUtil.error("获取帖子版本失败：" + response.getStatusMessage());
                }
                SubmitPostForReviewResponse submitPostForReviewResponse = cpcPostApiService.submitPost(req.getPuid(), shop, profile, result.getData().getPostId(), response.getData().getPost().getVersion());
                if (Objects.nonNull(submitPostForReviewResponse) && submitPostForReviewResponse.getStatusCode() == 429) {
                    return ResultUtil.error("创建帖子成功，提交帖子失败，当前亚马逊请求过多，请稍后重试");
                } else if (Objects.nonNull(result.getData()) && submitPostForReviewResponse.getStatusCode() != 200) {
                    return ResultUtil.error("创建帖子成功，提交帖子失败：" + submitPostForReviewResponse.getStatusMessage());
                } else if (Objects.nonNull(submitPostForReviewResponse) && Objects.nonNull(submitPostForReviewResponse.getData()) && Objects.nonNull(submitPostForReviewResponse.getData().getPost()) && submitPostForReviewResponse.getStatusCode() == 200) {
                    //数据再次入库 覆盖创建之后的入库的数据
                    AmazonPostsProfiles postsProfiles = new AmazonPostsProfiles();
                    List<AmazonPost> amazonPosts = new ArrayList<>();
                    List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                    postsProfiles.setPostProfileId(req.getPostProfileId());
                    AmazonPost post = buildAmazonPostByPost(submitPostForReviewResponse.getData().getPost(), req.getPuid(), shop, postsProfiles, req.getUid(), false);
                    amazonPosts.add(post);
                    // asin信息入库
                    replacePostProducts(submitPostForReviewResponse.getData().getPost(), req.getPuid(), shop, postsProfiles);
                    // 帖子信息入库
                    amazonPostsDao.upsertPostsForCreate(amazonPosts);
                    // 帖子媒体信息入库
                    amazonPostMedias.addAll(buildPostMedias(submitPostForReviewResponse.getData().getPost(), puid, shop, postsProfiles));
                    amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                    return ResultUtil.success("success");
                } else {
                    return ResultUtil.error(submitPostForReviewResponse.getStatusMessage());
                }
            } else {
                return ResultUtil.error("提交帖子失败");
            }
        } catch (Exception e) {
            log.error("puid:{} shopId:{},提交帖子异常:", req.getPuid(), req.getShopId(), e);
            throw new SponsoredBizException("提交帖子失败，请联系管理员");
        }
    }

    private Result<String> checkProducts(List<String> products, ShopAuth shop, AmazonAdProfile profile, Integer puid) {
        GetProductsInfoResponse checkResponse = cpcPostApiService.checkProducts(products, shop, profile, puid);
        List<IneligibleProducts> ineligibleProducts = checkResponse.getData().getIneligibleProducts();
        if (CollectionUtils.isNotEmpty(ineligibleProducts)) {
            StringBuilder msg = new StringBuilder();
            for (IneligibleProducts ineligibleProduct : ineligibleProducts) {
                if (PostProductStatusEnum.INVALID.getStatus().equals(ineligibleProduct.getIneligibilityCode())) {
                    msg.append("广告产品{").append(ineligibleProduct.getAsin()).append("}").append(PostProductStatusEnum.INVALID.getName()).append(";");
                } else if (PostProductStatusEnum.SCHEDULED.getStatus().equals(ineligibleProduct.getIneligibilityCode())) {
                    msg.append("广告产品{").append(ineligibleProduct.getAsin()).append("}").append(PostProductStatusEnum.SCHEDULED.getName()).append(";");;
                } else if (PostProductStatusEnum.UNAUTHORIZED.getStatus().equals(ineligibleProduct.getIneligibilityCode())) {
                    msg.append("广告产品{").append(ineligibleProduct.getAsin()).append("}").append(PostProductStatusEnum.UNAUTHORIZED.getName()).append(";");;
                }
            }
            return ResultUtil.error(msg.toString());
        }
        return null;
    }

    /**
     * 编辑页面 - 保存帖子为草稿
     */
    @Override
    public Result<String> updatePost(SubmitPostRequest req) {
        int puid = req.getPuid();
        int shopId = req.getShopId();
        try {
            // 校验店铺权限
            ShopAuth shop = shopAuthDao.getByIdAndPuid(shopId, puid);
            if (Objects.isNull(shop)) {
                return ResultUtil.error("没有CPC授权");
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
            if (Objects.isNull(profile)) {
                return ResultUtil.error("没有站点对应的配置信息");
            }
            //校验商品信息
            Result<String> msg = checkProducts(req.getProducts(), shop, profile, req.getPuid());
            if (msg != null) return msg;
            // 查询出原来的帖子基础数据
            AmazonPost oldPost = amazonPostsDao.getPostByPostId(req.getPuid(), req.getShopId(), req.getPostProfileId(), req.getPostId());
            // 获取帖子version字段
            GetPostResponse response = cpcPostApiService.getPostVersion(req.getPuid(), shop, profile, oldPost.getPostId());
            if (Objects.nonNull(response) && response.getStatusCode() != 200) {
                return ResultUtil.error("亚马逊请求获取版本号异常：" + response.getStatusMessage());
            }
            // 对帖子进行修改
            UpdatePostRequestParam param = buildAmazonPostForUpdate(oldPost, req, response);
            UpdatePostResponse updatePostResponse = cpcPostApiService.submitPostForUpdate(param, shop, profile, oldPost);
            if (Objects.nonNull(updatePostResponse) && Objects.nonNull(updatePostResponse.getData()) && Objects.nonNull(updatePostResponse.getData().getPost()) && updatePostResponse.getStatusCode() == 200) {
                //数据再次入库 覆盖创建之后的入库的数据
                AmazonPostsProfiles postsProfiles = new AmazonPostsProfiles();
                List<AmazonPost> amazonPosts = new ArrayList<>();
                List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                postsProfiles.setPostProfileId(req.getPostProfileId());
                AmazonPost post = buildAmazonPostByPost(updatePostResponse.getData().getPost(), req.getPuid(), shop, postsProfiles, req.getUid(), true);
                amazonPosts.add(post);
                // asin信息入库
                replacePostProducts(updatePostResponse.getData().getPost(), req.getPuid(), shop, postsProfiles);
                // 帖子信息入库
                if (CollectionUtils.isNotEmpty(amazonPosts)) {
                    amazonPostsDao.upsertPostsForCreate(amazonPosts);
                }
                // 帖子媒体信息入库
                amazonPostMedias.addAll(buildPostMedias(updatePostResponse.getData().getPost(), puid, shop, postsProfiles));
                if (CollectionUtils.isNotEmpty(amazonPostMedias)) {
                    amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                }
                return ResultUtil.success("保存成功");
            } else if (Objects.nonNull(updatePostResponse) && updatePostResponse.getStatusCode() == 429) {
                return ResultUtil.error("当前亚马逊请求过多，请稍后重试");
            } else {
                return ResultUtil.error(updatePostResponse.getStatusMessage());
            }
        } catch (Exception e) {
            log.error("puid:{} shopId:{},编辑页面保存帖子为草稿异常:", req.getPuid(), req.getShopId(), e);
            throw new SponsoredBizException("帖子提交失败，请联系管理员");
        }
    }

    /**
     * 编辑页面 - 提交帖子
     *
     * @param req
     * @return
     */
    @Override
    public Result<String> submitForUpdate(SubmitPostRequest req) {
        int puid = req.getPuid();
        int shopId = req.getShopId();
        try {
            // 校验店铺权限
            ShopAuth shop = shopAuthDao.getByIdAndPuid(shopId, puid);
            if (Objects.isNull(shop)) {
                return ResultUtil.error("没有CPC授权");
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
            if (Objects.isNull(profile)) {
                return ResultUtil.error("没有站点对应的配置信息");
            }
            //校验商品信息
            Result<String> msg = checkProducts(req.getProducts(), shop, profile, req.getPuid());
            if (msg != null) return msg;
            // 查询出原来的帖子基础数据
            AmazonPost oldPost = amazonPostsDao.getPostByPostId(req.getPuid(), req.getShopId(), req.getPostProfileId(), req.getPostId());
            // 获取帖子version字段
            GetPostResponse response = cpcPostApiService.getPostVersion(req.getPuid(), shop, profile, oldPost.getPostId());
            if (Objects.nonNull(response) && response.getStatusCode() != 200) {
                return ResultUtil.error("亚马逊请求获取版本号异常：" + response.getStatusMessage());
            }
            // 对帖子进行修改
            UpdatePostRequestParam param = buildAmazonPostForUpdate(oldPost, req, response);
            UpdatePostResponse updatePostResponse = cpcPostApiService.submitPostForUpdate(param, shop, profile, oldPost);
            if (Objects.nonNull(updatePostResponse) && updatePostResponse.getStatusCode() == 429) {
                return ResultUtil.error("当前亚马逊请求过多，请稍后重试");
            }
            if (Objects.nonNull(updatePostResponse) && updatePostResponse.getStatusCode() != 200) {
                return ResultUtil.error(updatePostResponse.getStatusMessage());
            }
            // 再次获取帖子version字段
            GetPostResponse versionResponse = cpcPostApiService.getPostVersion(req.getPuid(), shop, profile, oldPost.getPostId());
            if (Objects.nonNull(versionResponse) && versionResponse.getStatusCode() != 200) {
                return ResultUtil.error("亚马逊请求获取版本号异常：" + response.getStatusMessage());
            }
            // 对修改后的帖子进行提交
            SubmitPostForReviewResponse submitPostForReviewResponse = cpcPostApiService.submitPost(req.getPuid(), shop, profile, oldPost.getPostId(), versionResponse.getData().getPost().getVersion());
            if (Objects.nonNull(submitPostForReviewResponse) && Objects.nonNull(submitPostForReviewResponse.getData()) && Objects.nonNull(submitPostForReviewResponse.getData().getPost()) && submitPostForReviewResponse.getStatusCode() == 200) {
                //数据再次入库 覆盖创建之后的入库的数据
                AmazonPostsProfiles postsProfiles = new AmazonPostsProfiles();
                List<AmazonPost> amazonPosts = new ArrayList<>();
                List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                postsProfiles.setPostProfileId(req.getPostProfileId());
                AmazonPost post = buildAmazonPostByPost(submitPostForReviewResponse.getData().getPost(), req.getPuid(), shop, postsProfiles, req.getUid(), true);
                amazonPosts.add(post);
                // asin信息入库
                replacePostProducts(submitPostForReviewResponse.getData().getPost(), req.getPuid(), shop, postsProfiles);
                // 帖子信息入库
                if (CollectionUtils.isNotEmpty(amazonPosts)) {
                    amazonPostsDao.upsertPostsForCreate(amazonPosts);
                }
                // 帖子媒体信息入库
                amazonPostMedias.addAll(buildPostMedias(submitPostForReviewResponse.getData().getPost(), puid, shop, postsProfiles));
                if (CollectionUtils.isNotEmpty(amazonPostMedias)) {
                    amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                }
                return ResultUtil.success("success");
            } else if (Objects.nonNull(submitPostForReviewResponse) && submitPostForReviewResponse.getStatusCode() == 429) {
                return ResultUtil.error("当前亚马逊请求过多，请稍后重试");
            } else {
                return ResultUtil.error(submitPostForReviewResponse.getStatusMessage());
            }
        } catch (Exception e) {
            log.error("puid:{} shopId:{},提交帖子异常:", req.getPuid(), req.getShopId(), e);
            throw new SponsoredBizException("帖子提交失败，请联系管理员");
        }
    }

    /**
     * 获取帖子详情
     *
     * @param req
     * @return
     */
    @Override
    public PostDetailResponse getPostDetail(GetPostDetailRequest req) {
        PostDetailResponse response = new PostDetailResponse();
        try {
            // 校验店铺权限
            ShopAuth shop = shopAuthDao.getByIdAndPuid(req.getShopId(), req.getPuid());
            if (Objects.isNull(shop)) {
                return response;
            }
            AmazonAdProfile profile = amazonAdProfileDao.getProfile(req.getPuid(), req.getShopId());
            if (Objects.isNull(profile)) {
                return response;
            }
            // 查询帖子基础信息
            response = amazonPostsDao.getPostDetail(req);
            // 调用亚马逊接口获取帖子状态，实时更新一下帖子信息
            GetPostResponse postResponse = cpcPostApiService.getPostVersion(req.getPuid(), shop, profile, response.getPostId());
            if (Objects.nonNull(postResponse) && Objects.nonNull(postResponse.getData()) && Objects.nonNull(postResponse.getData().getPost()) && postResponse.getStatusCode() != 200) {
                log.info("调用亚马逊接口获取帖子状态 429 puid:{}, shopId:{}", req.getPuid(), req.getShopId());
                return response;
            }
            // 数据入库
            if (Objects.nonNull(postResponse) && Objects.nonNull(postResponse.getData()) && Objects.nonNull(postResponse.getData().getPost()) && !postResponse.getData().getPost().getStatus().equals(response.getStatus()) && postResponse.getStatusCode() == 200) {
                AmazonPostsProfiles postsProfiles = new AmazonPostsProfiles();
                List<AmazonPost> amazonPosts = new ArrayList<>();
                List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                postsProfiles.setPostProfileId(postResponse.getData().getPost().getProfileId());
                AmazonPost post = buildAmazonPostByPost(postResponse.getData().getPost(), req.getPuid(), shop, postsProfiles, req.getUid(), true);
                amazonPosts.add(post);
                // asin信息入库
                replacePostProducts(postResponse.getData().getPost(), req.getPuid(), shop, postsProfiles);
                // 帖子信息入库
                if (CollectionUtils.isNotEmpty(amazonPosts)) {
                    amazonPostsDao.upsertPostsForUpdate(amazonPosts);
                }
                // 帖子媒体信息入库
                amazonPostMedias.addAll(buildPostMedias(postResponse.getData().getPost(), req.getPuid(), shop, postsProfiles));
                if (CollectionUtils.isNotEmpty(amazonPostMedias)) {
                    amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                }
                response = amazonPostsDao.getPostDetail(req);
            }
            ArrayList<Integer> list = new ArrayList<>();
            list.add(req.getShopId());
            // 查询品牌信息
            List<GetBrandsResponse.BrandsVo> brands = amazonPostProfilesDao.getBrands(req.getPuid(), list);
            if (brands == null) {
                log.error("puid:{} shopId:{},查询帖子品牌信息为空", req.getPuid(), req.getShopId());
                return response;
            }
            response.setBrandId(brands.get(0).getBrandId());
            response.setBrandName(brands.get(0).getName());
            // 查询帖子下的推广产品信息
            buildPostProductsInfo(response, req);
            // 查询帖子媒体信息
            buildPostMediasInfo(response, req);
            // 查询帖子下的店铺和品牌信息
            buildPostShopAndBrandInfo(response, req);
            return response;
        } catch (Exception e) {
            log.error("puid:{} shopId:{},获取帖子详情异常:", req.getPuid(), req.getShopId(), e);
        }
        return response;
    }

    /**
     * 撤回帖子
     *
     * @param req
     * @return
     */
    @Override
    public Result<String> unpublish(UnpublishPostRequest req) {
        int puid = req.getPuid();
        int shopId = req.getShopId();
        // 校验店铺权限
        ShopAuth shop = shopAuthDao.getByIdAndPuid(shopId, puid);
        if (Objects.isNull(shop)) {
            return ResultUtil.error("没有CPC授权");
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (Objects.isNull(profile)) {
            return ResultUtil.error("没有站点对应的配置信息");
        }
        try {
            // 获取帖子版本号
            GetPostResponse getPostResponse = cpcPostApiService.getPostVersion(req.getPuid(), shop, profile, req.getPostId());
            String status = getPostResponse.getData().getPost().getStatus();
            if (status.equals(PostStatusEnum.LIVE.getStatus()) || status.equals(PostStatusEnum.SCHEDULED.getStatus())) {
                UnpublishPostResponse response = cpcPostApiService.unpublishPost(req.getPuid(), shop, profile, req.getPostId(), getPostResponse.getData().getPost().getVersion());
                // 数据入库
                if (Objects.nonNull(response) && Objects.nonNull(response.getData()) && Objects.nonNull(response.getData().getPost()) && response.getStatusCode() == 200) {
                    AmazonPostsProfiles postsProfiles = new AmazonPostsProfiles();
                    List<AmazonPost> amazonPosts = new ArrayList<>();
                    List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                    postsProfiles.setPostProfileId(response.getData().getPost().getProfileId());
                    AmazonPost post = buildAmazonPostByPost(response.getData().getPost(), req.getPuid(), shop, postsProfiles, req.getUid(), true);
                    amazonPosts.add(post);
                    // asin信息入库
                    replacePostProducts(response.getData().getPost(), req.getPuid(), shop, postsProfiles);
                    // 帖子信息入库
                    if (CollectionUtils.isNotEmpty(amazonPosts)) {
                        amazonPostsDao.upsertPostsForUpdate(amazonPosts);
                    }
                    // 帖子媒体信息入库
                    amazonPostMedias.addAll(buildPostMedias(response.getData().getPost(), puid, shop, postsProfiles));
                    if (CollectionUtils.isNotEmpty(amazonPostMedias)) {
                        amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                    }
                    return ResultUtil.success("success");
                } else if (Objects.nonNull(response) && response.getStatusCode() == 429) {
                    return ResultUtil.error("当前亚马逊请求过多，请稍后重试");
                } else {
                    return ResultUtil.error(response.getStatusMessage());
                }
            } else {
                return ResultUtil.error("状态不允许修改，请手动同步数据");
            }
        } catch (Exception e) {
            log.error("puid:{} shopId:{},撤回帖子异常:{}", req.getPuid(), req.getShopId(), e);
            throw new SponsoredBizException("帖子撤回失败，请联系管理员");
        }
    }

    /**
     * 创建人下拉框
     */
    @Override
    public List<GetUserInfoResponse.UserInfo> getUser(Integer puid, Integer uid, GetUserInfoRequest request) {
        List<GetUserInfoResponse.UserInfo> resultList = new ArrayList<>();
        // 查询创建过帖子的所有用户
        List<Integer> postUid = amazonPostsDao.getPostUid(puid);
        if (CollectionUtils.isEmpty(postUid)) {
            return resultList;
        }
        // 查询和对搜索结果进行过滤
        List<User> userInfos = userDao.listAllUserInfo(puid, request.getSearchValue(), null, postUid);
        for (User userInfo : userInfos) {
            GetUserInfoResponse.UserInfo user = new GetUserInfoResponse.UserInfo();
            user.setUid(userInfo.getId());
            user.setName(userInfo.getNickname());
            resultList.add(user);
        }
        return resultList;
    }

    /**
     * 手动同步帖子
     *
     * @param puid
     * @param shopId
     */
    @Override
    public void manualSyncUserPosts(Integer puid, Integer shopId) {
        // 限制单个店铺五小时内只能同步一次
        String syncKey = String.format(RedisConstant.SELLFOX_POST_SYNC_KEY, puid, shopId);
        try {
            Boolean locked = redisTemplateNew.opsForValue().setIfAbsent(syncKey, "同步中", 3600, TimeUnit.SECONDS);
            if (locked != null && locked) {
                // 同步帖子配置数据和基础数据
                log.info("syncData puid:{} shopId:{}", puid, shopId);
                CompletableFuture.runAsync(() -> {
                    try {
                        syncUserPostProfiles(puid);
                        String msg = doSyncUserPosts(puid, shopId, null);
                        Long time = redisTemplateNew.getExpire(syncKey);
                        if ("success".equals(msg)) {
                            if (Objects.nonNull(time) && time > 0) {
                                redisTemplateNew.opsForValue().set(syncKey, "同步完成", time, TimeUnit.SECONDS);
                            }
                        } else {
                            if (Objects.nonNull(time) && time > 0) {
                                redisTemplateNew.opsForValue().set(syncKey, "同步失败", time, TimeUnit.SECONDS);
                            }
                            // 失败删除key
                            redisTemplateNew.delete(syncKey);
                        }
                    } catch (Exception e) {
                        log.error("Async doSyncUserPosts error, puid: {}, shopId: {}", puid, shopId, e);
                    }
                }, ThreadPoolUtil.getManualSyncPostsPool());
            }
        } catch (Exception e) {
            redisTemplateNew.delete(syncKey);
            log.error("syncPost error, puid: {}, shopId: {}", puid, shopId, e);
        }
    }

    /**
     * 检查同步是否完成
     *
     * @param puid
     * @param shopId
     * @return
     */
    @Override
    public String checkSyncData(int puid, Integer shopId) {
        // 修改同步的key
        String syncKey = String.format(RedisConstant.SELLFOX_POST_SYNC_KEY, puid, shopId);
        String msg = "同步完成";
        if (Boolean.TRUE.equals(redisTemplateNew.hasKey(syncKey))) {
            msg = (String) redisTemplateNew.opsForValue().get(syncKey);
        }
        return msg;
    }

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceId
     * @return
     */
    @Override
    public ShopSitesResponse getShopInfo(int puid, String marketplaceId, List<Integer> shopIdList) {
        ShopSitesResponse response = new ShopSitesResponse();
        try {
            List<ShopSitesResponse.ShopSite> shopAuth = shopAuthDao.getAuthShopListByPuid(puid, marketplaceId, shopIdList);
            List<Integer> shopIds = amazonPostProfilesDao.getShopIdByPuid(puid, shopIdList).stream().distinct().collect(Collectors.toList());
            // 过滤出有品牌帖子的店铺，设置店铺信息
            shopAuth = shopAuth.stream()
                    .filter(shop -> shopIds.contains(shop.getShopId()))
                    .collect(Collectors.toList());
            for (ShopSitesResponse.ShopSite shopSite : shopAuth) {
                AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(shopSite.getMarketplaceId());
                shopSite.setMarketplace(amznEndpoint.getMarketplace());
                shopSite.setMarketplaceCN(amznEndpoint.getMarketplaceCN());
                shopSite.setDomain(amznEndpoint.getDomain());
                shopSite.setLowCostStore(shopSite.getSupportLowCostStore());
            }
            response.setShopSites(shopAuth);
        } catch (Exception e) {
            log.error("帖子列表页获取店铺信息异常 puid:{} error:{}", puid, e);
        }
        return response;
    }

    /**
     * 获取建议ASIN
     * @param req
     * @return
     */
    @Override
    public GetSuggestAsinResponse getSuggestAsin(GetSuggestAsinRequest req) {
        GetSuggestAsinResponse response = new GetSuggestAsinResponse();
        List<GetSuggestAsinResponse.SuggestAsinVo> suggestAsinVos = new ArrayList<>();
        List<GetSuggestAsinResponse.SuggestAsinVo> suggestAsins = new ArrayList<>();
        try {
            // 调用数据组接口获取前100个建议asin
            List<String> marketplaceIdList = new ArrayList<>();
            List<Integer> shopIds = new ArrayList<>();
            marketplaceIdList.add(Constants.US);
            shopIds.add(req.getShopId());
            suggestAsinVos = dataDomainManager.getSuggestAsin(req.getPuid(), shopIds, marketplaceIdList, req);
            log.info("suggestAsinVos:{}", suggestAsinVos);
            // 将获取到的建议asin进行过滤（未发过帖子的过滤掉）
            List<String> asinList = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(suggestAsinVos)) {
                asinList = amazonPostsProductsDao.getProductByAsins(req.getPuid(), req.getShopId());
                if (CollectionUtils.isNotEmpty(asinList)) {
                    List<String> finalAsinList = asinList;
                    suggestAsins = suggestAsinVos.stream()
                            .filter(item -> !finalAsinList.contains(item.getAsin()))
                            .collect(Collectors.toList());
                } else {
                    suggestAsins = suggestAsinVos;
                }
            }
            // 如果有名称查询，进一步过滤
            if (StringUtils.isNotBlank(req.getSearchValue()) && "asin".equals(req.getSearchField())) {
                suggestAsins = suggestAsins.stream()
                        .filter(item -> req.getSearchValue().equals(item.getAsin()))
                        .collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(req.getSearchValue()) && "title".equals(req.getSearchField())) {
                suggestAsins = suggestAsins.stream()
                        .filter(item -> item.getTitle().contains(req.getSearchValue()))
                        .collect(Collectors.toList());
            } else if (StringUtils.isNotBlank(req.getSearchValue()) && "msku".equals(req.getSearchField())) {
                suggestAsins = suggestAsins.stream()
                        .filter(item -> item.getMsku().contains(req.getSearchValue()))
                        .collect(Collectors.toList());
            }
            // 对过滤完的asin查询它的变体
            // 查询asin对应的parentId，根据parentId去查询asin对应的所有变体
            List<OdsProduct> products = new ArrayList<>();
            Set<Long> parentIdList = new HashSet<>();
            List<OdsProduct> sameParentIdList = new ArrayList<>();
            List<String> asins = suggestAsins.stream().map(GetSuggestAsinResponse.SuggestAsinVo::getAsin).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(asins)) {
                // 查询asin的parentId以及相关信息
                products = odsProductDao.getAsinParentId(req.getPuid(), req, asins);
                //获取产品资格状态
                List<AsinPageResponse.AsinInfo> list = new ArrayList<>();
                products.forEach(e -> {
                    AsinPageResponse.AsinInfo asinInfo = new AsinPageResponse.AsinInfo();
                    asinInfo.setAsin(e.getAsin());
                    asinInfo.setMsku(e.getSku());
                    list.add(asinInfo);
                });
                Map<String, ProductStatusDto> productEligibilityStatusMap = this.getProductEligibilityStatusMap(req.getPuid(), req.getShopId(), Constants.SB, list);
                parentIdList = products.stream().map(OdsProduct::getId).collect(Collectors.toSet());
                // 根据parentId去查询变体信息
                sameParentIdList = odsProductDao.getListByParentId(req.getPuid(), req.getShopId(), Constants.US, Lists.newArrayList(parentIdList));
                Map<Long, List<OdsProduct>> sameParentMap = sameParentIdList.stream()
                        .collect(Collectors.groupingBy(OdsProduct::getParentId));
                List<String> finalProducts = products.stream().map(OdsProduct::getAsin).collect(Collectors.toList());
                suggestAsins = suggestAsins.stream()
                        .filter(item -> finalProducts.contains(item.getAsin()))
                        .collect(Collectors.toList());
                // 构造变体信息
                for (OdsProduct product : products) {
                    for (GetSuggestAsinResponse.SuggestAsinVo suggestAsin : suggestAsins) {
                        String key = getKey(suggestAsin.getAsin(), suggestAsin.getMsku());
                        convertPoToPageVo(product, suggestAsin, sameParentMap.get(product.getParentId()));
                        //产品资格状态
                        suggestAsin.setIsMeetConditions(!productEligibilityStatusMap.containsKey(key));
                        suggestAsin.setOnlineStatus(product.getOnlineStatus());
                        suggestAsin.setImgUrl(suggestAsin.getImageUrl());
                        suggestAsin.setId(product.getId());
                        if (Objects.nonNull(suggestAsin.getPrice())) {
                            BigDecimal num = new BigDecimal(suggestAsin.getPrice());
                            DecimalFormat df = new DecimalFormat("#0.00");
                            String result = df.format(num);
                            suggestAsin.setPrice(result);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("getSuggestAsin error, puid: {}, shopId:{}", req.getPuid(), req.getShopId(), e);
        }
        response.setSuggestAsinVo(suggestAsins);
        return response;
    }

    /**
     * 搜索框查询
     * @param req
     * @return
     */
    @Override
    public AsinPageResponse getAsinPageList(GetSuggestAsinRequest req) {
        //需要查询所有数据,不根据parentId分组.
        AsinPageResponse response = new AsinPageResponse();
        try {
            // 分页查询产品信息
            Page<AsinPageResponse.AsinInfo> page = odsProductDao.getPageList(req.getPuid(), req);
            if (Boolean.TRUE.equals(req.getSearch())) {
                response.setPage(page);
            }
            if (CollectionUtils.isNotEmpty(page.getRows())) {
                //获取产品资格状态
                Map<String, ProductStatusDto> productEligibilityStatusMap = this.getProductEligibilityStatusMap(req.getPuid(), req.getShopId(), req.getType(), page.getRows());
                String domain = AmznEndpoint.getByMarketplaceId(page.getRows().get(0).getMarketplaceId()).getDomain();
                Set<Long> parentIdList = Optional.of(page).map(Page::getRows).filter(CollectionUtils::isNotEmpty)
                        .map(rows -> rows.stream().map(AsinPageResponse.AsinInfo::getParentId).collect(Collectors.toSet()))
                        .orElse(Collections.emptySet());
                // 根据分页查询得到的aisn的parentId去查询所有的变体
                List<OdsProduct> sameParentIdList = odsProductDao.getListByParentId(req.getPuid(), req.getShopId(), Constants.US, Lists.newArrayList(parentIdList));
                Map<Long, List<OdsProduct>> sameParentMap = sameParentIdList.stream()
                        .collect(Collectors.groupingBy(OdsProduct::getParentId));
                // 组装变体数据返回
                for (AsinPageResponse.AsinInfo product : page.getRows()) {
                    String key = getKey(product.getAsin(), product.getMsku());
                    AsinPageResponse.AsinInfo vo = new AsinPageResponse.AsinInfo();
                    vo.setDomain(domain);
                    convertToPageVo(product, vo, sameParentMap.get(product.getParentId()));
                    //产品资格状态
                    vo.setIsMeetConditions(!productEligibilityStatusMap.containsKey(key));
                    BeanUtils.copyProperties(vo, product);
                }
                response.setPage(page);
            }
        } catch (Exception e) {
            log.info("搜索框查询asin异常：puid{}, shopId:{}", req.getPuid(), req.getShopId(), e);
        }
        return response;
    }

    private void convertToPageVo(AsinPageResponse.AsinInfo product, AsinPageResponse.AsinInfo vo, List<OdsProduct> sameParentList) {
        if (product.getIsVariation() == 2) {
            vo.setId(product.getId());
            vo.setShopId(product.getShopId());
            vo.setMsku(product.getMsku());
            vo.setAsin(product.getAsin());
            vo.setOnlineStatus(product.getOnlineStatus());
            vo.setTitle(product.getTitle());
            vo.setImgUrl(product.getImgUrl());
            if (Objects.nonNull(product.getPrice())) {
                BigDecimal num = new BigDecimal(product.getPrice());
                DecimalFormat df = new DecimalFormat("#0.00");
                String result = df.format(num);
                vo.setPrice(result);
            }
            vo.setRating(product.getRating());
            vo.setRatingCount(product.getRatingCount());
            vo.setFbaAvailable(product.getFbaAvailable());
            if (CollectionUtils.isNotEmpty(sameParentList)) {
                // 过滤掉删除的
                sameParentList = sameParentList.stream().filter(e -> e.getDxmPublishState() == null || !e.getDxmPublishState().equals("delete")).collect(Collectors.toList());
                if (sameParentList.size() > 1) {
                    List<ChildAsin> childList = new ArrayList<>(sameParentList.size());
                    ChildAsin childVo;
                    for (OdsProduct p : sameParentList) {
                        //原来的逻辑是排除父msku:p.getId()不等于product.getId().
                        //改版要包含本身数据,前端需要.
                        childVo = new ChildAsin();
                        childVo.setId(p.getId());
                        childVo.setShopId(p.getShopId());
                        childVo.setMsku(p.getSku());
                        childVo.setAsin(p.getAsin());
                        childVo.setOnlineStatus(p.getOnlineStatus());
                        childVo.setTitle(p.getTitle());
                        childVo.setImgUrl(p.getMainImage());
                        childVo.setDomain(vo.getDomain());
                        if (Objects.nonNull(vo.getPrice())) {
                            BigDecimal num = new BigDecimal(vo.getPrice());
                            DecimalFormat df = new DecimalFormat("#0.00");
                            String result = df.format(num);
                            vo.setPrice(result);
                        }
                        childVo.setPrice(p.getStandardPrice());
                        childVo.setRating(p.getRating());
                        childVo.setRatingCount(p.getRatingCount());
                        childVo.setFbaAvailable(p.getQuantity());
                        childList.add(childVo);
                    }
                    vo.setChildList(childList);
                }
            }
        } else {
            List<ChildAsin> childList = new ArrayList<>();
            vo.setId(product.getId());
            vo.setShopId(product.getShopId());
            vo.setMsku(product.getMsku());
            vo.setAsin(product.getAsin());
            vo.setOnlineStatus(product.getOnlineStatus());
            vo.setTitle(product.getTitle());
            vo.setImgUrl(product.getImgUrl());
            if (Objects.nonNull(product.getPrice())) {
                BigDecimal num = new BigDecimal(product.getPrice());
                DecimalFormat df = new DecimalFormat("#0.00");
                String result = df.format(num);
                vo.setPrice(result);
            }
            vo.setRating(product.getRating());
            vo.setRatingCount(product.getRatingCount());
            vo.setFbaAvailable(product.getFbaAvailable());
            vo.setChildList(childList);
        }
    }

    public Map<String, ProductStatusDto> getProductEligibilityStatusMap(Integer puid, Integer shopId, String adType, List<AsinPageResponse.AsinInfo> rows) {
        List<CpcProductDto> dtoList = rows.stream().map(e -> new CpcProductDto(e.getAsin(), e.getMsku())).collect(Collectors.toList());
        List<ProductStatusDto> resultDtoList = cpcProductApiService.productEligibility(puid, shopId, adType, null, dtoList);
        Map<String, ProductStatusDto> asinResultMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(resultDtoList)) {
            asinResultMap = resultDtoList.stream().collect(Collectors.toMap(e-> getKey(e.getAsin(), e.getSku()), e -> e, (v1, v2) -> v1));
        }
        return asinResultMap;
    }

    public String getKey (String asin, String sku) {
        return asin + "$#$" + sku;
    }

    public void convertPoToPageVo(OdsProduct product, GetSuggestAsinResponse.SuggestAsinVo suggestAsinVo, List<OdsProduct> sameParentList) {
        if (product.getIsVariation() == 2) {
            suggestAsinVo.setId(product.getId());
            if (CollectionUtils.isNotEmpty(sameParentList)) {
                // 过滤掉删除的
                sameParentList = sameParentList.stream().filter(e -> e.getDxmPublishState() == null || !e.getDxmPublishState().equals("delete")).collect(Collectors.toList());
                if (sameParentList.size() > 1) {
                    List<ChildAsin> childAsins = new ArrayList<>();
                    ChildAsin childAsin;
                    for (OdsProduct p : sameParentList) {
                        //原来的逻辑是排除父msku:p.getId()不等于product.getId().
                        //改版要包含本身数据,前端需要.
                        childAsin = new ChildAsin();
                        childAsin.setId(p.getId());
                        childAsin.setShopId(p.getShopId());
                        childAsin.setMsku(p.getSku());
                        childAsin.setAsin(p.getAsin());
                        childAsin.setOnlineStatus(p.getOnlineStatus());
                        childAsin.setTitle(p.getTitle());
                        childAsin.setImgUrl(p.getMainImage());
                        if (Objects.nonNull(p.getStandardPrice())) {
                            String price = p.getStandardPrice().toString();
                            BigDecimal num = new BigDecimal(price);
                            DecimalFormat df = new DecimalFormat("#0.00");
                            String result = df.format(num);
                            childAsin.setPrice(result);
                        }
                        childAsins.add(childAsin);
                    }
                    suggestAsinVo.setChildList(childAsins);
                }
            }
        }
    }

    /**
     * 查询媒体信息
     * @param response
     * @param req
     */
    private void buildPostMediasInfo(PostDetailResponse response, GetPostDetailRequest req) {
        // 查询帖子下的媒体信息
        Set<String> postProfileId = new HashSet<>(Collections.singleton(req.getPostProfileId()));
        List<MediaPo> mediaInfo = amazonPostsMediaDao.getMediaInfo(req.getPuid(), Arrays.asList(req.getShopId()), postProfileId, Arrays.asList(req.getPostId()));
        MediaPo mediaPo = mediaInfo.get(0);
        if (Objects.nonNull(mediaPo)) {
            response.setMediaId(mediaPo.getMediaId());
            response.setMediaType(mediaPo.getMediaType());
        }
    }

    /**
     * 构建商品信息
     * @param response
     * @param req
     */
    private void buildPostProductsInfo(PostDetailResponse response, GetPostDetailRequest req) {
        // 查询帖子下的推广产品信息
        List<String> asins = amazonPostsProductsDao.getPostProducts(req.getPuid(), req.getShopId(), req.getPostProfileId(), req.getPostId());
        // 查询该商品对应的其他信息
        List<AsinProductVo> asinInfo = getAsinInfo(req.getPuid(), Arrays.asList(req.getShopId()), Constants.US, asins, "asin");
        // 如果从在线产品表中没有查询到数据，也要组装剩余数据返回给前端
        if (CollectionUtils.isNotEmpty(asinInfo)) {
            Map<String, AsinProductVo> productVoMap = StreamUtil.toMap(asinInfo, AsinProductVo::getAsin);
            for (String asin : asins) {
                if (productVoMap.containsKey(asin)) {
                    continue;
                }
                AsinProductVo asinProductVo = new AsinProductVo();
                asinProductVo.setShopId(req.getShopId());
                asinProductVo.setAsin(asin);
                asinProductVo.setHasAsinInfo(false);
                asinInfo.add(asinProductVo);
            }
        } else {
            for (String asin : asins) {
                AsinProductVo asinProductVo = new AsinProductVo();
                asinProductVo.setShopId(req.getShopId());
                asinProductVo.setAsin(asin);
                asinProductVo.setHasAsinInfo(false);
                asinInfo.add(asinProductVo);
            }
        }
        response.setProducts(asinInfo);
    }

    /**
     * 构建店铺和品牌信息
     * @param response
     * @param req
     */
    private void buildPostShopAndBrandInfo(PostDetailResponse response, GetPostDetailRequest req) {
        // 查询店铺信息
        String marketplaceId = Constants.US;
        List<ShopSitesResponse.ShopSite> shopAuth = shopAuthDao.getAuthShopListByPuid(req.getPuid(), marketplaceId, Arrays.asList(req.getShopId()));
        if (CollectionUtils.isNotEmpty(shopAuth)) {
            response.setShopInfo(shopAuth.get(0));
            response.setShopId(null);
        }
        // 查询品牌信息
        Result<List<BrandResult>> result = cpcSbStoreService.getBrand(req.getPuid(), req.getShopId());
        Map<String, BrandResult> map = StreamUtil.toMap(result.getData(), BrandResult::getBrandRegistryName);
        if (Objects.nonNull(map.get(response.getBrandName()))) {
            response.setBrandEntityId(map.get(response.getBrandName()).getBrandEntityId());
        }
    }

    /**
     * 查询产品信息
     *
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param asins
     * @param type          类型：asin\msku\parentAsin
     * @return
     */
    private List<AsinProductVo> getAsinInfo(Integer puid, List<Integer> shopId, String marketplaceId, List<String> asins, String type) {
        // 查询帖子下的商品标题、父ASIN、msku、价格、星级、评分数等信息
        List<AsinProductVo> productsVo = new ArrayList<>();
        if ("asin".equals(type)) {
            productsVo = odsProductDao.listProductInfoByAsins(puid, shopId, marketplaceId, asins);
        }
        if (org.springframework.util.CollectionUtils.isEmpty(productsVo)) {
            log.error("asin query sku is empty, puid: {}, shopId:{}", puid, shopId);
            return productsVo;
        }
        // 按照可售状态排序，再按照价格排序
        String status = "Active";
        productsVo.sort((a, b) -> {
            if (status.equals(a.getOnlineStatus()) && status.equalsIgnoreCase(b.getOnlineStatus())) {
                // 都为可售状态，按照价格倒序
                return b.getPrice().compareTo(a.getPrice());
            } else if (!status.equalsIgnoreCase(a.getOnlineStatus()) && !status.equalsIgnoreCase(b.getOnlineStatus())) {
                // 都为不可售状态，按照价格倒序
                return b.getPrice().compareTo(a.getPrice());
            } else {
                // 按照可售状态排序
                if (status.equalsIgnoreCase(a.getOnlineStatus())) {
                    return -1;
                } else {
                    return 1;
                }
            }
        });
        // 去重，当 asin 相同时，取排序靠前的
        LinkedHashMap<String, AsinProductVo> map = new LinkedHashMap<>();
        for (AsinProductVo product : productsVo) {
            map.putIfAbsent(product.getAsin(), product);
        }
        List<AsinProductVo> distinctList = new ArrayList<>(map.values());
        // 查询asin对应的fba可售数
        List<GetFbaReviewInfoReq.MskuShopId> mskuShopIds = new ArrayList<>(distinctList.size());
        Set<String> repeatMskuShopId = new HashSet<>();
        for (AsinProductVo list : distinctList) {
            if (repeatMskuShopId.contains(list.getShopId() + list.getSku())) {
                continue;
            }
            repeatMskuShopId.add(list.getShopId() + list.getSku());
            GetFbaReviewInfoReq.MskuShopId mskuShopId = new GetFbaReviewInfoReq.MskuShopId();
            mskuShopId.setMsku(list.getSku());
            mskuShopId.setShopId(list.getShopId());
            mskuShopIds.add(mskuShopId);
        }
        List<GetFbaReviewInfoResp.FbaInfo> fbaReviewInfo = dataDomainManager.getFbaReviewInfo(puid, mskuShopIds);
        for (GetFbaReviewInfoResp.FbaInfo fbaInfo : fbaReviewInfo) {
            if (Objects.isNull(fbaInfo)) {
                continue;
            }
            for (AsinProductVo productVo : distinctList) {
                if (productVo.getSku().equalsIgnoreCase(fbaInfo.getMsku()) && productVo.getShopId().equals(fbaInfo.getShopId())) {
                    productVo.setAvailable(fbaInfo.getAvailable());
                }
            }
        }
        return distinctList;
    }

    /**
     * 构建请求参数
     *
     * @param oldPost
     * @param req
     * @return
     */
    private UpdatePostRequestParam buildAmazonPostForUpdate(AmazonPost oldPost, SubmitPostRequest req, GetPostResponse postResponse) {
        UpdatePostRequestParam param = new UpdatePostRequestParam();
        param.setProfileId(oldPost.getPostProfileId());
        param.setProducts(req.getProducts());
        param.setVersion(postResponse.getData().getPost().getVersion());
        Media media = new Media();
        String mediaId = removeVersionSuffix(req.getMediaId());
        media.setMediaId(mediaId);
        media.setMediaUrl(req.getMediaUrl());
        media.setMediaType(req.getMediaType());
        MediaMetadata mediaMetadata = new MediaMetadata();
        mediaMetadata.setAssetId(mediaId);
        media.setMediaMetadata(mediaMetadata);
        List<Media> medias = new ArrayList<>();
        medias.add(media);
        param.setMedias(medias);
        if (req.getScheduledWithdrawalDate() != null) {
            String UTCScheduledWithdrawalDate = LocalDateTimeUtil.siteTimeStrToUtcStr(req.getScheduledWithdrawalDate(), Constants.US);
            param.setScheduledWithdrawalDate(UTCScheduledWithdrawalDate);
        }
        if (req.getCaption() != null) {
            param.setCaption(req.getCaption());
        }
        if (req.getScheduledLiveDate() != null) {
            String UTCScheduledLiveDate = LocalDateTimeUtil.siteTimeStrToUtcStr(req.getScheduledLiveDate(), Constants.US);
            param.setScheduledLiveDate(UTCScheduledLiveDate);
        }
        return param;
    }

    private String removeVersionSuffix(String mediaId) {
        int colonIndex = mediaId.indexOf(':');
        if (colonIndex != -1) {
            return mediaId.substring(0, colonIndex);
        }
        return mediaId; // 若没找到冒号，就返回原字符串
    }

    @Override
    public void syncUserPostProfiles(Integer index, Integer total, List<Integer> puids) {
        List<Integer> puidList;
        int limit = 1000;
        int startIndex = 0;
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        while (true) {
            if (CollectionUtils.isNotEmpty(puids)) {
                //根据传入的puid进行扫描
                puidList = puids;
            } else {
//                puidList = dynamicRefreshNacosConfiguration.getPostWhitelist();
                //遍历扫描赛狐所有的puid
//                puidList = slaveAmazonAdProfileDao.getAllPuidByLimit(startIndex, limit);
                // 根据百分比获取puid
                puidList = getGrayPuidList();
            }
            log.info("puidList:{}",puidList);
            int count = puidList.size();
            //xxl-job采用分片广播策略，对puid取模分到不同的服务器上进行执行
            for (int puid : puidList) {
                if (puid % total == index) {
                    try {
                        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> syncUserPostProfiles(puid), ThreadPoolUtil.getSyncPostProfilesPool()).exceptionally(ex->{
                            log.error("syncUserPostProfiles error puid: {}", puid, ex);
                            return null;
                        });
                        futures.add(future);
                    } catch (Exception e) {
                        log.error("syncUserPostProfiles error puid: {}", puid, e);
                    }
                }
            }
            if (count < limit) {
                break;
            }
            startIndex += limit;
        }
        // 等待所有任务执行完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    public void syncUserPostProfiles(Integer puid) {
        List<AmazonPostsProfiles> postsProfilesList = new ArrayList<>();

        Integer isPlay = 0;

        //判断该用户是否为付费用户
        Integer planType = slaveUserDao.getPlanTypeByPuid(puid);

        if (planType == 0 || planType == 1) {
            isPlay = 0;
        } else {
            isPlay = 1;
        }

        List<ShopAuth> shopAuths = shopAuthDao.getListByPuid(puid);

        if (CollectionUtils.isEmpty(shopAuths)) {
            return;
        }
        //遍历店铺,获取店铺的posts使用情况
        for (ShopAuth shopAuth : shopAuths) {
            if (!isValidShop(shopAuth)) {
                continue;
            }
            String nextToken = null;
            do {
                AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
                if (amazonAdProfile == null) {
                    log.info("syncUserPostProfiles puid:{}, shopId{}, 没有站点对应的配置信息", puid, shopAuth.getId());
                    break;
                }
                GetPostProfilesListResponse response =
                        PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                getPostProfilesListResponse(shopAuthService.getAdToken(shopAuth),
                                        shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), nextToken, BATCH_QUERY_SIZE);
                if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                    //token过期，刷新token
                    shopAuthService.refreshCpcAuth(shopAuth);
                    response =
                            PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                    getPostProfilesListResponse(shopAuthService.getAdToken(shopAuth),
                                            shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), nextToken, BATCH_QUERY_SIZE);
                }
                if (response != null && response.getStatusCode() != null &&
                        (response.getStatusCode() == 429 || response.getStatusCode() == 403 && "Forbidden".equals(response.getStatusMessage()))) {
                    log.info("syncUserPostProfilesLimit puid:{}, shopId:{}", puid, shopAuth.getId());
                    int postSyncRetry = dynamicRefreshNacosConfiguration.getPostProfileSyncRetry();
                    for (int i = 0; i < postSyncRetry;i++) {
                        try {
                            TimeUnit.SECONDS.sleep(2);
                        } catch (Exception e) {
                            log.error("syncUserPostProfiles sleep error.puid:{}, shopId:{}", puid, shopAuth.getId(), e);
                        }
                        response =
                                PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                        getPostProfilesListResponse(shopAuthService.getAdToken(shopAuth),
                                                shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), nextToken, BATCH_QUERY_SIZE);
                        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 200) {
                            break;
                        }
                    }
                    log.info("syncUserPostProfiles puid:{} retryCount:{}", puid, postSyncRetry);
                }
                if (response == null || response.getData() == null || response.getStatusCode() != 200) {
                    if (response != null) {
                        log.warn("syncUserPostProfilesLimitCodeeError puid:{}, shopId:{} code:{}", puid, shopAuth.getId(), response.getStatusCode());
                    } else {
                        log.warn("syncUserPostProfilesLimitCodeeError puid:{}, shopId:{}", puid, shopAuth.getId());
                    }
                    break;
                }
                log.info("syncUserPostProfiles puid:{}, shopId:{}, result:{}", puid, shopAuth.getId(), response.getData());
                GetPostProfilesListResult result = response.getData();
                List<com.amazon.advertising.mode.PostsProfiles> postsProfiles = result.getProfiles();
                if (CollectionUtils.isEmpty(postsProfiles)) {
                    break;
                }
                for (com.amazon.advertising.mode.PostsProfiles postsProfile : postsProfiles) {
                    AmazonPostsProfiles profiles = new AmazonPostsProfiles();
                    BeanUtils.copyProperties(postsProfile, profiles);
                    profiles.setPuid(puid);
                    profiles.setShopId(shopAuth.getId());
                    profiles.setIsPay(isPlay);
                    // null 当做 "" 处理
                    profiles.setBrandId(StringUtils.defaultString(postsProfile.getBrandId()));
                    postsProfilesList.add(profiles);
                }
                nextToken = result.getNextToken();
                sleep(50);
            } while (StringUtils.isNotBlank(nextToken));
        }
        if (CollectionUtils.isEmpty(postsProfilesList)) {
            return;
        }
        //批量插入插入到数据库
        amazonPostProfilesDao.upsertPostProfiles(postsProfilesList);
        log.info("puid:{} insert success:{}", puid, postsProfilesList);
    }

    @Override
    public void syncUserPosts(Integer index, Integer total, List<Integer> puids) {
        List<Integer> puidList;
        int limit = 1000;
        int startIndex = 0;
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        while (true) {
            if (CollectionUtils.isNotEmpty(puids)) {
                //根据传入的puid进行扫描
                puidList = puids;
            } else {
//                puidList = dynamicRefreshNacosConfiguration.getPostWhitelist();
                //遍历扫描赛狐所有的puid
//                puidList = slaveAmazonAdProfileDao.getAllPuidByLimit(startIndex, limit);
                // 根据百分比获取puid
                puidList = getGrayPuidList();
            }
            int count = puidList.size();
            //xxl-job采用分片广播策略，对puid取模分到不同的服务器上进行执行
            for (int puid : puidList) {
                if (puid % total == index) {
                    try {
                        doSyncUserPosts(puid, null, futures);
                    } catch (Exception e) {
                        log.error("syncUserPosts error puid: {}", puid, e);
                    }
                }
            }
            if (count < limit) {
                break;
            }
            startIndex += limit;
        }
        // 等待所有任务执行完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }


    private String doSyncUserPosts(Integer puid, Integer shopId, List<CompletableFuture<Void>> futures) {
        List<ShopAuth> shopAuths = Collections.emptyList();
        if (shopId != null) {
            ShopAuth shopAuth = shopAuthDao.getById(shopId);
            if (shopAuth == null) {
                return "店铺未授权";
            }
            shopAuths = Collections.singletonList(shopAuth);
        } else {
            shopAuths = shopAuthDao.getListByPuid(puid);
        }

        if (CollectionUtils.isEmpty(shopAuths)) {
            return "店铺为空";
        }
        AtomicReference<String> msg = new AtomicReference<>("");
        for (ShopAuth shopAuth : shopAuths) {
            if (futures != null) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                     msg.set(doSyncUserPosts(puid, shopAuth));
                }, ThreadPoolUtil.getSyncPostPool());
                futures.add(future);
            } else {
                msg.set(doSyncUserPosts(puid, shopAuth));

            }
        }
        return msg.get();
    }

    private String doSyncUserPosts(Integer puid, ShopAuth shopAuth) {
        if (!isValidShop(shopAuth)) {
            return "店铺未授权";
        }

        List<AmazonPostsProfiles> postsProfiles = amazonPostProfilesDao.listByShopId(puid, shopAuth.getId());
        if (CollectionUtils.isEmpty(postsProfiles)) {
            log.warn("no post post profiles.puid:{}, shopId:{}", puid, shopAuth.getId());
            return "no post post profiles.puid:" + puid + ", shopId:" + shopAuth.getId();
        }
        GetPostListRequestParam requestParam = new GetPostListRequestParam();
        requestParam.setMaxResults(BATCH_QUERY_SIZE);
        PostListSortCriterion postListSortCriterion = new PostListSortCriterion();
        postListSortCriterion.setSortField(PostListSortFieldEnum.createdDate.name());
        postListSortCriterion.setSortOrder(PostListSortOrderEnum.ASC.name());
        requestParam.setSortCriterion(postListSortCriterion);
        PostListFilter postListFilter = new PostListFilter();
        TimeZone timeZone = Marketplace.fromId(shopAuth.getMarketplaceId()).getTimeZone();
        postListFilter.setStartDate(LocalDateTimeUtil.toISOOffsetDateTimeStr(MIN_START_TIME, timeZone.toZoneId()));
        postListFilter.setFilterType(PostListFilterTypeEnum.RANGE.name());
        postListFilter.setFieldName(PostListFilterFieldEnum.createdDate.name());
        requestParam.setFilters(Collections.singletonList(postListFilter));

        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
        if (amazonAdProfile == null) {
            log.info("syncUserPosts puid:{}, shopId{}, 没有站点对应的配置信息", puid, shopAuth.getId());
            return "syncUserPosts puid:" + puid + ", shopId:" + shopAuth.getId() + ", 没有站点对应的配置信息";
        }

        Date date = new Date();
        List<AmazonAdPostReportSyncRecord> postReportSyncRecords = new ArrayList<>();
        int postSyncRetry = dynamicRefreshNacosConfiguration.getPostSyncRetry();
        for (AmazonPostsProfiles postsProfile : postsProfiles) {
            String nextToken = null;
            requestParam.setProfileId(postsProfile.getPostProfileId());
            boolean hasValidPosts = false;
            int retryCount = 0;
            do {
                List<AmazonPost> amazonPosts = new ArrayList<>();
                List<AmazonPostMedia> amazonPostMedias = new ArrayList<>();
                GetPostListResponse response =
                        PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                getPostList(shopAuthService.getAdToken(shopAuth),
                                        shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), requestParam);
                if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                    //token过期，刷新token
                    shopAuthService.refreshCpcAuth(shopAuth);
                    response =
                            PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                    getPostList(shopAuthService.getAdToken(shopAuth),
                                            shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), requestParam);
                }
                // 基础信息，一直同步到有数据为止
                if (response != null && response.getStatusCode() != null &&
                        (response.getStatusCode() == 429 || response.getStatusCode() == 403 && "Forbidden".equals(response.getStatusMessage()))) {
                    try {
                        TimeUnit.SECONDS.sleep(10);
                    } catch (Exception e) {
                        log.error("syncUserPosts sleep error.puid:{}, shopId:{}, postProfileId:{}", puid, shopAuth.getId(), postsProfile.getPostProfileId(), e);
                    }
                    retryCount++;
                    if (retryCount >= postSyncRetry) {
                        log.warn("syncUserPosts retry puid:{}, shopId:{}, postProfileId:{} retryCount:{}", puid, shopAuth.getId(), postsProfile.getPostProfileId(), retryCount);
                        break;
                    } else {
                        continue;
                    }
                }
                // 统计重试次数
                log.warn("syncUserPosts retry puid:{}, shopId:{}, postProfileId:{} retryCount:{}", puid, shopAuth.getId(), postsProfile.getPostProfileId(), retryCount);
                if (response == null || response.getData() == null || response.getStatusCode() != 200) {
                    log.warn("response is invalid.puid:{}, shopId:{}, postProfileId:{}", puid, shopAuth.getId(), postsProfile.getPostProfileId());
                    break;
                }
                GetPostListResult result = response.getData();
                List<Post> posts = result.getPosts();
                if (CollectionUtils.isEmpty(posts)) {
                    break;
                }
                for (Post post : posts) {
                    AmazonPost amazonPost = buildAmazonPostByPost(post, puid, shopAuth, postsProfile, null, false);
                    amazonPosts.add(amazonPost);
                    amazonPostMedias.addAll(buildPostMedias(post, puid, shopAuth, postsProfile));
                    // asin信息入库
                    replacePostProducts(post, puid, shopAuth, postsProfile);
                }
                nextToken = result.getNextToken();
                requestParam.setNextToken(nextToken);
                // 帖子信息入库
                if (CollectionUtils.isNotEmpty(amazonPosts)) {
                    log.info("amazonPosts record.puid:{}, shopId:{}, postProfileId:{}", puid, shopAuth.getId(), postsProfile.getPostProfileId());
                    amazonPostsDao.syncUpsertPosts(amazonPosts);
                    hasValidPosts = true;
                }
                // 帖子媒体信息入库
                if (CollectionUtils.isNotEmpty(amazonPostMedias)) {
                    amazonPostsMediaDao.upsertPostMedias(amazonPostMedias);
                }
                sleep(50);
                retryCount = 0;
            // 有下一页或者第一页并且正在重试可以继续执行
            } while (StringUtils.isNotBlank(nextToken) || (StringUtils.isBlank(nextToken) && retryCount > 0));
            // 没有同步到帖子信息就不需要创建同步记录了
            if (hasValidPosts) {
                AmazonAdPostReportSyncRecord postReportSyncRecord =
                        buildAmazonPostReportSyncRecord(puid, shopAuth, postsProfile, date);
                postReportSyncRecords.add(postReportSyncRecord);
            }
        }
        // 同步任务信息入库
        amazonAdPostReportSyncRecordDao.upsertPostReportSyncRecords(postReportSyncRecords);
        return "success";
    }

    public List<Integer> getGrayPuidList() {
        List<Integer> puidList;
        // 获取灰度模板
        List<Integer> whitePuidList = dynamicRefreshNacosConfiguration.getPostWhitelist();
        // 查询所有授权店铺的puid
        puidList = slaveShopAuthDao.getAllAdAuthShopPuid();
        // 进行过滤得到百分比中的puid
        puidList = puidList.stream()
                .filter(Objects::nonNull)
                .filter(puid -> GrayUtil.isHit(puid, null, dynamicRefreshNacosConfiguration.getPostPercentage()))
                .collect(Collectors.toList());
        // 将百分比中的puid和白名单中的puid取并集处理
        List<Integer> puids = Stream.concat(whitePuidList.stream(), puidList.stream())
                .distinct()
                .collect(Collectors.toList());
        return puids;
    }

    private AmazonPost buildAmazonPostByPost(Post post, Integer puid, ShopAuth shopAuth, AmazonPostsProfiles postsProfile, Integer uid, Boolean isUpdate) {
        AmazonPost amazonPost = new AmazonPost();
        amazonPost.setPuid(puid);
        amazonPost.setShopId(shopAuth.getId());
        amazonPost.setPostProfileId(postsProfile.getPostProfileId());
        amazonPost.setPostId(post.getId());
        if (Objects.nonNull(uid) && Boolean.FALSE.equals(isUpdate)) {
            amazonPost.setCreateUid(uid);
        } else if (Objects.nonNull(uid) && Boolean.TRUE.equals(isUpdate)) {
            amazonPost.setUpdateUid(uid);
        }
        amazonPost.setCaption(post.getCaption());
        amazonPost.setMediaUrl(post.getMedias().get(0).getMediaUrl());
        amazonPost.setStatus(post.getStatus());
        amazonPost.setStatusMetadata(JSONUtil.objectToJson2(post.getStatusMetadata()));
        amazonPost.setIsFlaggedForQuality(post.getIsFlaggedForQuality());
        amazonPost.setPostCreatedDate(LocalDateTimeUtil.utcToSiteTime2(post.getCreatedDate(), shopAuth.getMarketplaceId()));
        amazonPost.setLiveDate(LocalDateTimeUtil.utcToSiteTime2(post.getLiveDate(), shopAuth.getMarketplaceId()));
        amazonPost.setScheduledLiveDate(LocalDateTimeUtil.utcToSiteTime2(post.getScheduledLiveDate(), shopAuth.getMarketplaceId()));
        amazonPost.setScheduledWithdrawalDate(LocalDateTimeUtil.utcToSiteTime2(post.getScheduledWithdrawalDate(), shopAuth.getMarketplaceId()));
        amazonPost.setLastModified(LocalDateTimeUtil.utcToSiteTime2(post.getLastModified(), shopAuth.getMarketplaceId()));
        amazonPost.setPromotionMetadata(JSONUtil.objectToJson2(post.getPromotionMetadata()));
        return amazonPost;
    }

    private List<AmazonPostMedia> buildPostMedias(Post post, Integer puid, ShopAuth shopAuth, AmazonPostsProfiles postsProfile) {
        List<Media> medias = post.getMedias();
        if (CollectionUtils.isEmpty(medias)) {
            return Collections.emptyList();
        }
        List<AmazonPostMedia> postMedias = new ArrayList<>();
        for (Media media : medias) {
            AmazonPostMedia postMedia = new AmazonPostMedia();
            postMedia.setPuid(puid);
            postMedia.setShopId(shopAuth.getId());
            postMedia.setPostProfileId(postsProfile.getPostProfileId());
            postMedia.setPostId(post.getId());
            postMedia.setMediaId(media.getMediaId());
            postMedia.setMediaUrl(medias.get(0).getMediaUrl());
            postMedia.setMediaType(media.getMediaType());
            MediaMetadata mediaMetadata = media.getMediaMetadata();
            if (mediaMetadata != null) {
                postMedia.setAiGenerated(mediaMetadata.getAiGenerated());
                postMedia.setAiFeaturedAsins(mediaMetadata.getAiFeaturedAsins());
                postMedia.setCreatorContent(mediaMetadata.getCreatorContent());
                postMedia.setEntityHasAssetPermission(mediaMetadata.getEntityHasAssetPermission());
            }
            postMedias.add(postMedia);
        }
        return postMedias;
    }

    private void replacePostProducts(Post post, Integer puid, ShopAuth shopAuth, AmazonPostsProfiles postsProfile) {
        List<AmazonPostProduct> amazonPostProducts = new ArrayList<>();
        List<String> products = post.getProducts();
        // product为空表明没有产品信息，清空该帖子下的所有数据
        if (CollectionUtils.isEmpty(products)) {
            amazonPostsProductsDao.deleteByUniqueKey(puid, shopAuth.getId(), postsProfile.getPostProfileId(), post.getId());
            return;
        }
        for (String product : products) {
            AmazonPostProduct amazonPostProduct = new AmazonPostProduct();
            amazonPostProduct.setPuid(puid);
            amazonPostProduct.setShopId(shopAuth.getId());
            amazonPostProduct.setPostProfileId(postsProfile.getPostProfileId());
            amazonPostProduct.setPostId(post.getId());
            amazonPostProduct.setAsin(product);
            amazonPostProducts.add(amazonPostProduct);
        }
        amazonPostsProductsDao.upsertProducts(amazonPostProducts);
        amazonPostsProductsDao.deleteNotInAsins(puid, shopAuth.getId(), postsProfile.getPostProfileId(), post.getId(), products);
    }

    private AmazonAdPostReportSyncRecord buildAmazonPostReportSyncRecord(Integer puid, ShopAuth shopAuth, AmazonPostsProfiles postsProfile, Date excuteDate) {
        AmazonAdPostReportSyncRecord reportSyncRecord = new AmazonAdPostReportSyncRecord();
        reportSyncRecord.setPuid(puid);
        reportSyncRecord.setShopId(shopAuth.getId());
        reportSyncRecord.setPostProfileId(postsProfile.getPostProfileId());
        // 初始化同步开始时间
        LocalDateTime startDateTime = buildLastSyncDateTime();
        reportSyncRecord.setLastSyncDay(LocalDateTimeUtil.formatTime(startDateTime, LocalDateTimeUtil.YMD_DATE_FORMAT));
        reportSyncRecord.setNextExecuteDate(excuteDate);
        return reportSyncRecord;
    }

    private LocalDateTime buildLastSyncDateTime() {
        LocalDateTime endTime = LocalDateTime.now();
        LocalDateTime startTime = LocalDateTimeUtil.getDayStart(endTime.minusYears(FIRST_SYNC_YEAR_DURATION));
        if (startTime.isBefore(MIN_START_TIME)) {
            startTime = MIN_START_TIME;
        }
        return startTime;
    }

    @Override
    public void syncPostReportIncrementData(Integer index, Integer total, Integer puid, Integer shopId, String postProfileId) {
        syncPostReport(index, total, puid, shopId, postProfileId, false);
    }

    @Override
    public void syncPostReportAllData(Integer index, Integer total, Integer puid, Integer shopId, String postProfileId) {
        syncPostReport(index, total, puid, shopId, postProfileId, true);
    }

    private void syncPostReport(Integer index, Integer total, Integer targetPuid, Integer targetShopId, String postProfileId, boolean syncAllData) {
        // 增量同步与全量同步区分线程池
        ThreadPoolExecutor executor = ThreadPoolUtil.getSyncPostReportIncrementPool();
        // 全量同步的记录的占少数，所以此处初始化为null，增量同步在查出符合条件的记录后再在内存中对该标志位做过滤
        Boolean isInitFinish = null;
        if (syncAllData) {
            executor = ThreadPoolUtil.getSyncPostReportAllPool();
            isInitFinish = false;
        }
        Date date = new Date();
        List<AmazonAdPostReportSyncRecord> postReportSyncRecords =
                amazonAdPostReportSyncRecordDao.listByUniqueKeyAndExecuteTime(targetPuid, targetShopId, postProfileId, date, isInitFinish);
        if (CollectionUtils.isEmpty(postReportSyncRecords)) {
            return;
        }
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (AmazonAdPostReportSyncRecord postReportSyncRecord : postReportSyncRecords) {
            int puid = postReportSyncRecord.getPuid();
            if (puid % total != index) {
                continue;
            }
            int shopId = postReportSyncRecord.getShopId();
            ShopAuth shopAuth = shopAuthDao.getById(shopId);
            if (shopAuth == null) {
                continue;
            }
            if (!isValidShop(shopAuth)) {
                continue;
            }
            List<AmazonAdPostReportSyncRecord> validRecords = amazonAdPostReportSyncRecordDao.listByPuidAndShopId(puid, shopId);
            if (!syncAllData) {
                validRecords.removeIf(each -> !each.getIsInitFinish());
            }
            if (CollectionUtils.isEmpty(validRecords)) {
                continue;
            }
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> doSyncPostReport(validRecords, shopAuth), executor);
            futures.add(future);
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    private void doSyncPostReport(List<AmazonAdPostReportSyncRecord> records, ShopAuth shopAuth) {
        DateTime todayStart = DateTime.now();
        for (AmazonAdPostReportSyncRecord record : records) {
            try {
                int puid = record.getPuid();
                DateTime lastSyncDay = DateTime.parse(record.getLastSyncDay(), DateTimeFormat.forPattern(DateUtil.PATTERN));
                boolean needSkipDays = true;
                // 初始化标志位为1表示为增量同步
                if (BooleanUtils.isTrue(record.getIsInitFinish())) {
                    lastSyncDay = lastSyncDay.minusDays(INCREMENT_SYNC_DAY_DURATION);
                    needSkipDays = false;
                }
                // 是否全部同步成功
                boolean allSyncSuccess = true;
                do {
                    String metricDate = lastSyncDay.toString(DateUtil.PATTERN);
                    AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
                    if (amazonAdProfile == null) {
                        log.info("doSyncPostReport puid:{}, shopId{}, 没有站点对应的配置信息", puid, shopAuth.getId());
                        allSyncSuccess = false;
                        break;
                    }
                    DownloadPostMetricReportResponse response =
                            PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                    downloadPostMetricReport(shopAuthService.getAdToken(shopAuth),
                                            shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), record.getPostProfileId(), metricDate, metricDate);
                    if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                        //token过期，刷新token
                        shopAuthService.refreshCpcAuth(shopAuth);
                        response =
                                PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                        downloadPostMetricReport(shopAuthService.getAdToken(shopAuth),
                                                shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), record.getPostProfileId(), metricDate, metricDate);
                    }
                    if (response != null && response.getStatusCode() != null &&
                            (response.getStatusCode() == 429 || response.getStatusCode() == 403 && "Forbidden".equals(response.getStatusMessage()))) {
                        log.info("syncUserPostProfilesLimit puid:{}, shopId:{}", puid, shopAuth.getId());
                        int postSyncRetry = dynamicRefreshNacosConfiguration.getPostReportSyncRetry();
                        for (int i = 0; i < postSyncRetry;i++) {
                            try {
                                TimeUnit.SECONDS.sleep(2);
                            } catch (Exception e) {
                                log.error("syncUserPostProfiles sleep error.puid:{}, shopId:{}", puid, shopAuth.getId(), e);
                            }
                            response =
                                    PostsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).
                                            downloadPostMetricReport(shopAuthService.getAdToken(shopAuth),
                                                    shopAuth.getMarketplaceId(), amazonAdProfile.getProfileId(), record.getPostProfileId(), metricDate, metricDate);
                            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 200) {
                                break;
                            }
                        }
                    }
                    if (response == null || response.getData() == null || response.getStatusCode() != 200) {
                        if (response != null) {
                            log.warn("syncPostReportLimitCodeeError puid:{}, shopId:{} code:{}", puid, shopAuth.getId(), response.getStatusCode());
                        } else {
                            log.warn("syncPostReportLimitCodeeError puid:{}, shopId:{}", puid, shopAuth.getId());
                        }
                        allSyncSuccess = false;
                        break;
                    }
                    String reportDownloadUrl = response.getData().getReportDownloadUrl();
                    if (StringUtils.isBlank(reportDownloadUrl)) {
                        log.warn("no report url.puid:{}, shopId:{}, postProfileId:{}", puid, shopAuth.getId(), record.getPostProfileId());
                        allSyncSuccess = false;
                        break;
                    }
                    // 根据所有符合条件的帖子创建时间，跳过时间早于最早创建帖子的报告数据请求
                    Date reportMinStartDate = parsePostReportAndReturnReportMinStartDate(record, reportDownloadUrl, shopAuth, metricDate, needSkipDays);
                    // 只有初始化并且首次获取报告数据的时候才需要跳过
                    if (BooleanUtils.isFalse(record.getIsInitFinish()) && needSkipDays) {
                        needSkipDays = false;
                        if (reportMinStartDate != null) {
                            lastSyncDay = new DateTime(reportMinStartDate).withTimeAtStartOfDay();
                        }
                    }
                    if (reportMinStartDate == null) {
                        lastSyncDay = lastSyncDay.plusDays(1);
                    }
                    // 增量同步会同步近3天的数据，如果同步的日期大于数据库记录的同步的日期，才去更新最近同步时间，避免最近同步时间回退
                    if (metricDate.compareTo(record.getLastSyncDay()) > 0) {
                        // 更新数据的最新同步时间
                        amazonAdPostReportSyncRecordDao.updateLastSyncDate(record.getId(), metricDate);
                    }
                } while (lastSyncDay.isBefore(todayStart));
                if (allSyncSuccess) {
                    // 更新下次同步时间，并把初始化完成标志位置为1
                    amazonAdPostReportSyncRecordDao.updateNextExecuteTimeAndInitFinish(record.getId(), DateTime.now().plusHours(3).toDate());
                }
            } catch (Exception e) {
                log.error("syncPostReport error puid:{}, shopId:{}, postProfileId:{}", record.getPuid(), shopAuth.getId(), record.getPostProfileId(), e);
            }
        }
    }

    private Date parsePostReportAndReturnReportMinStartDate(AmazonAdPostReportSyncRecord record,
                                                            String reportDownloadUrl,
                                                            ShopAuth shopAuth,
                                                            String countDay,
                                                            boolean needSkipDays) throws Exception {
        URL url = new URL(reportDownloadUrl);
        InputStream inputStream = url.openStream();
        XSSFWorkbook workbook = new XSSFWorkbook(inputStream);
        XSSFSheet sheet = workbook.getSheetAt(0);
        List<AmazonPostReport> postReports = new ArrayList<>();
        Date validMinStartDate = LocalDateTimeUtil.convertSiteTimeToDate(MIN_START_TIME, shopAuth.getMarketplaceId());
        Date reportMinStartDate = null;
        for (int i = 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            // UTC时间
            Date date = row.getCell(0).getDateCellValue();
            if (date.compareTo(validMinStartDate) < 0) {
                continue;
            }
            if (needSkipDays) {
                if (reportMinStartDate == null) {
                    reportMinStartDate = date;
                } else if (reportMinStartDate.compareTo(date) > 0) {
                    reportMinStartDate = date;
                }
            }

            Integer impressions = (int) row.getCell(5).getNumericCellValue();
            Integer engagement = (int) row.getCell(6).getNumericCellValue();
            Integer clicksToBrandStore = (int) row.getCell(8).getNumericCellValue();
            Integer clicksToFollow = (int) row.getCell(9).getNumericCellValue();
            Integer clicksToDetailPage = (int) row.getCell(10).getNumericCellValue();
            Integer reach = CellType.STRING.equals(row.getCell(11).getCellTypeEnum()) ? 0 : (int) row.getCell(11).getNumericCellValue();
            // 没有指标数据，忽略
            if (impressions == 0 && engagement == 0 && clicksToBrandStore == 0 && clicksToFollow == 0 && clicksToDetailPage == 0 && reach == 0) {
                continue;
            }

            AmazonPostReport postReport = new AmazonPostReport();
            postReport.setPuid(record.getPuid());
            postReport.setShopId(record.getShopId());
            postReport.setPostProfileId(record.getPostProfileId());
            postReport.setPostId(row.getCell(1).getStringCellValue());
            postReport.setImpressions(impressions);
            postReport.setEngagement(engagement);
            postReport.setClicksToBrandStore(clicksToBrandStore);
            postReport.setClicksToFollow(clicksToFollow);
            postReport.setClicksToDetailPage(clicksToDetailPage);
            postReport.setReach(reach);
            postReport.setCountDay(countDay);
            postReports.add(postReport);
            // 攒够100条，批量插入
            if (postReports.size() >= BATCH_INSERT_SIZE) {
                amazonPostsReportDao.upsertPostReports(postReports);
                postReports.clear();
            }
        }
        if (CollectionUtils.isNotEmpty(postReports)) {
            amazonPostsReportDao.upsertPostReports(postReports);
            postReports.clear();
        }
        if (reportMinStartDate == null) {
            return null;
        } else {
            String reportMinStartDateStr = new DateTime(reportMinStartDate).toString(DateUtil.PATTERN);
            // 报告中所有帖子中最早的创建时间大于第一次请求报告的时间才需要跳过
            return reportMinStartDateStr.compareTo(countDay) > 0 ? reportMinStartDate : null;
        }
    }

    private boolean isValidShop(ShopAuth shopAuth) {
        if (!ShopAdStatusEnum.AUTH.getName().equals(shopAuth.getAdStatus())) {
            log.debug("[PostsServiceImpl] no ad auth.puid:{}, shopId:{}", shopAuth.getPuid(), shopAuth.getId());
            return false;
        }
        if (userPlanTypeCacheService.puidExpire(shopAuth.getPuid())) {
            log.debug("[PostsServiceImpl] user plan expire.puid:{}, shopId:{}", shopAuth.getPuid(), shopAuth.getId());
            return false;
        }
        if (!Marketplace.USA.getId().equals(shopAuth.getMarketplaceId())) {
            log.debug("[PostsServiceImpl] not usa shop.puid:{}, shopId:{}", shopAuth.getPuid(), shopAuth.getId());
            return false;
        }
        return true;
    }
}
