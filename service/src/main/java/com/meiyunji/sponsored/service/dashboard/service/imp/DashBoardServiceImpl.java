package com.meiyunji.sponsored.service.dashboard.service.imp;

import com.alibaba.nacos.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonMarketingStreamDataDao;
import com.meiyunji.sponsored.service.cpc.dto.CampaignLastDayHourlyReportDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.dashboard.dao.IDashboardDao;
import com.meiyunji.sponsored.service.dashboard.dto.*;
import com.meiyunji.sponsored.service.dashboard.service.DashBoardService;
import com.meiyunji.sponsored.service.strategy.enums.AdType;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DashBoardServiceImpl implements DashBoardService {

    private final AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    private final IDashboardDao dashboardDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;

    public DashBoardServiceImpl(AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub,
                                IScVcShopAuthDao shopAuthDao, IDashboardDao dashboardDao, IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao) {
        this.adFeedBlockingStub = adFeedBlockingStub;
        this.shopAuthDao = shopAuthDao;
        this.dashboardDao = dashboardDao;
        this.amazonMarketingStreamDataDao = amazonMarketingStreamDataDao;
    }

    /**
     * 查询所有广告活动信息
     *
     * @param puid
     * @param shopIds
     * @param adType
     * @param name
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public List<DashBoardCampaignDto> listCampaignsByPuidAndShopId(
            Integer puid, List<Integer> shopIds, String adType, String name, String portfolioId, String status,
            Integer pageNo, Integer pageSize) {
        return dashboardDao.listCampaignsByPuidAndShopId(puid, shopIds, adType, name, portfolioId, status,pageNo, pageSize);
    }

    @Override
    public Integer countAllCampaigns(Integer puid, List<Integer> shopIds, String adType, String name, String portfolioId,
                                     String status) {
        return dashboardDao.countAllCampaigns(puid, shopIds, adType, name, portfolioId, status);
    }

    @Override
    public DashboardHourReportDto realTimeHourlyData(List<CampaignInfoDto> dtos, MultiThreadQueryParamDto param) {
        return this.realTimeHourlyData(param.getPuid(), param.getShopIds(), param.getHour(), param.getCurrency(), dtos);
    }

    /**
     * 实时播报接口数据
     *
     * @param puid
     * @param shopIds
     * @param hour
     * @param currency
     * @param dtos
     * @return
     */
    @Override
    public DashboardHourReportDto realTimeHourlyData(Integer puid, List<Integer> shopIds, Integer hour, String currency,
                                                     List<CampaignInfoDto> dtos) {
        //查询店铺信息(选择了活动时,只查询活动信息上的shopId)
        List<Integer> shopIdList = dtos.stream().map(CampaignInfoDto::getShopId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopIdList)) {
            shopIdList = shopIds;
        }
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIdList);
        Map<Integer, ShopAuth> shopAuthMap =
                shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));

        LocalDateTime dateTime = LocalDateTime.now(ZoneId.of("UTC"))
                .minusHours(hour).withMinute(0).withSecond(0).withNano(0);
        String selectDateTime = LocalDateTimeUtil.formatTime(dateTime, LocalDateTimeUtil.YMDHMS_DATE_FORMAT);


        List<String> campaignIds = dtos.stream().map(CampaignInfoDto::getCampaignId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<AmazonMarketingStreamData> itemList;
        if (CollectionUtils.isEmpty(campaignIds)) {
            itemList = statisticsByDateTimeRange(shopAuths, selectDateTime);
        } else {
            List<CampaignLastDayHourlyReportDto> campaignLastDayHourlyReportDtos = dtos.stream().map(item -> {
                        CampaignLastDayHourlyReportDto campaignLastDayHourlyReportDto = new CampaignLastDayHourlyReportDto();
                        ShopAuth shopAuth = shopAuthMap.get(item.getShopId());
                        campaignLastDayHourlyReportDto.setSellerId(shopAuth.getSellingPartnerId());
                        campaignLastDayHourlyReportDto.setMarketplaceId(shopAuth.getMarketplaceId());
                        campaignLastDayHourlyReportDto.setCampaignId(item.getCampaignId());
                        return campaignLastDayHourlyReportDto;
                    }).collect(Collectors.toList());
            itemList = statisticsByDateTimeRangeAndCampaignIdPartition(campaignLastDayHourlyReportDtos, selectDateTime);
        }

        //查询当月汇率
        LocalDateTime now = LocalDateTime.now(ZoneOffset.of("+8"));
        if(now.getDayOfMonth() == 1){
            now = now.minusMonths(1);
        }
        String month = now.format(DateTimeFormatter.ofPattern("yyyyMM"));
        List<UserCurrencyRateDto> rates = listCurrencyRateByPuidAndMonth(puid, month, month);

        Map<String, UserCurrencyRateDto> rateMap = rates.stream()
                .collect(Collectors.toMap(k -> k.getMonth().concat("#").concat(k.getFrom()).concat("#")
                .concat(k.getTo()), Function.identity()));

        //转换成vo
        List<DashboardHourReportDto> list = new ArrayList<>(itemList.size());
        for (AmazonMarketingStreamData amazonMarketingStreamData : itemList) {
            //币种
            String marketCurrency = amazonMarketingStreamData.getCurrency();
            //获取转换汇率
            UserCurrencyRateDto rate = rateMap.get(month.concat("#").concat(marketCurrency).concat("#").concat(currency));
            if (rate == null) {
                continue;
            }
            DashboardHourReportDto dto = new DashboardHourReportDto();
            dto.setClicks(amazonMarketingStreamData.getClicks());
            dto.setImpressions(amazonMarketingStreamData.getImpressions());
            dto.setCost(rate.getRate().multiply(BigDecimal.valueOf(amazonMarketingStreamData.getCost())).doubleValue());
            dto.setAdOrder(amazonMarketingStreamData.getAttributedConversions7d());
            dto.setAdSales(rate.getRate().multiply(amazonMarketingStreamData.getAttributedSales7d()).doubleValue());
            dto.setLastUpdateAt(DateUtil.format(amazonMarketingStreamData.getUpdateTime(), DateUtil.PATTERN_DATE_TIME));
            list.add(dto);
        }
        //汇总数据
        return sumHourReport(list);
    }

    private List<AmazonMarketingStreamData> statisticsByDateTimeRange(List<ShopAuth> shopAuths, String selectDateTime) {
        List<CampaignLastDayHourlyReportDto> shopDtos = shopAuths.stream().map(item -> {
            CampaignLastDayHourlyReportDto dto = new CampaignLastDayHourlyReportDto();
            dto.setSellerId(item.getSellingPartnerId());
            dto.setMarketplaceId(item.getMarketplaceId());
            return dto;
        }).collect(Collectors.toList());
        return amazonMarketingStreamDataDao.statisticsByDateTimeRangeDoris(shopDtos, selectDateTime);
    }

    public List<AmazonMarketingStreamData> statisticsByDateTimeRangeAndCampaignIdPartition(List<CampaignLastDayHourlyReportDto> list, String dateTime) {
        if (list.size() < 9000) {
            return amazonMarketingStreamDataDao.statisticsByDateTimeRangeAndCampaignIdDoris(list, dateTime);
        }
        List<AmazonMarketingStreamData> sponsoredProductsCampaignLastDayHourlyReports = new ArrayList<>();
        List<List<CampaignLastDayHourlyReportDto>> listList = Lists.partition(list, 9000);
        for (List<CampaignLastDayHourlyReportDto> campaignLastDayHourlyReportDtos : listList) {
            sponsoredProductsCampaignLastDayHourlyReports.addAll(amazonMarketingStreamDataDao.statisticsByDateTimeRangeAndCampaignIdDoris(campaignLastDayHourlyReportDtos, dateTime));
        }
        return sponsoredProductsCampaignLastDayHourlyReports;
    }



    /**
     * 广告贡献接口
     *
     * @param puid
     * @param shopIds
     * @param adType
     * @param currency
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public AdContributesDataDto getAdContributes(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate, List<String> campaigns,String portfolioId,String status) {
        //当期数据
        List<AdContributesDto> list = dashboardDao.getAdContributes(
                puid, shopIds, adType, currency, startDate, endDate, campaigns,portfolioId,status);
        //获取门店销售额
        List<AdContributesDto> shopSalesList = dashboardDao.getShopSales(
                puid, shopIds, currency, startDate, endDate);

        //上期数据
        LocalDate perEndDate = startDate.minusDays(1);
        Period between = Period.between(startDate, endDate);
        LocalDate perStartDate = perEndDate.minus(between);

        List<AdContributesDto> perList = dashboardDao.getAdContributes(
                puid, shopIds, adType, currency, perStartDate, perEndDate, campaigns,portfolioId,status);
        //获取门店销售额
        List<AdContributesDto> perShopSalesList = dashboardDao.getShopSales(
                puid, shopIds, currency, perStartDate, perEndDate);


        //汇总数据
        //花费
        BigDecimal cost = list.stream().map(AdContributesDto::getCost).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //销售额
        BigDecimal sales = list.stream().map(AdContributesDto::getSales).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //店铺销售额
        BigDecimal shopSales = shopSalesList.stream().map(AdContributesDto::getShopSale)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        //asots
        BigDecimal aSots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sales
                .multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP);
        //acots
        BigDecimal aCots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : cost
                .multiply(BigDecimal.valueOf(100)).divide(shopSales, 2, RoundingMode.HALF_UP);

        //汇总上期数据
        //花费
        BigDecimal perCost = perList.stream().map(AdContributesDto::getCost)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //销售额
        BigDecimal perSales = perList.stream().map(AdContributesDto::getSales)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //店铺销售额
        BigDecimal perShopSales = perShopSalesList.stream().map(AdContributesDto::getShopSale)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //asots
        BigDecimal perASots = perShopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : perSales
                .multiply(BigDecimal.valueOf(100)).divide(perShopSales, 2, RoundingMode.HALF_UP);
        //acots
        BigDecimal perACots = perShopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : perCost
                .multiply(BigDecimal.valueOf(100)).divide(perShopSales, 2, RoundingMode.HALF_UP);


        Map<String, BigDecimal> shopSalesMap = shopSalesList.stream()
                .collect(Collectors.toMap(AdContributesDto::getCountDate, AdContributesDto::getShopSale, (a, b) -> a));
        //chart数据填充asots, acots
        for (AdContributesDto vo : list) {
            //查询对应日期的店铺销售额
            vo.setShopSale(shopSalesMap.getOrDefault(vo.getCountDate(), BigDecimal.ZERO));
            //ASoTS ，广告销售额占店铺销售额的比例，广告销售额占比=广告销售额/店铺销售额100% ；
            vo.setASoTS(vo.getShopSale().compareTo(BigDecimal.ZERO) == 0 ?
                    BigDecimal.ZERO : vo.getSales().multiply(BigDecimal.valueOf(100))
                    .divide(vo.getShopSale(), 2, RoundingMode.HALF_UP));
            //.ACoTS，广告花费占店铺销售额的比例，广告花费占比=广告花费/店铺销售额100%*
            vo.setACoTS(vo.getShopSale().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost()
                    .multiply(BigDecimal.valueOf(100)).divide(vo.getShopSale(), 2, RoundingMode.HALF_UP));
        }

        AdContributesDataDto adContributesDataDto = new AdContributesDataDto();
        adContributesDataDto.setCost(cost);
        adContributesDataDto.setSales(sales);
        adContributesDataDto.setShopSale(shopSales);
        adContributesDataDto.setACoTS(aCots);
        adContributesDataDto.setASoTS(aSots);
        adContributesDataDto.setList(list);
        //上期数据
        adContributesDataDto.setPerShopSale(perShopSales);
        adContributesDataDto.setPerSales(perSales);
        adContributesDataDto.setPerCost(perCost);
        adContributesDataDto.setPerACoTS(perACots);
        adContributesDataDto.setPerASoTS(perASots);

        adContributesDataDto.setShopSaleGrowRate(perShopSales.compareTo(BigDecimal.ZERO) == 0 ?
                BigDecimal.ZERO : shopSales.subtract(perShopSales)
                .multiply(BigDecimal.valueOf(100)).divide(perShopSales, 2, RoundingMode.HALF_UP));
        adContributesDataDto.setACoTSGrowRate(aCots.subtract(perACots));
        adContributesDataDto.setASoTSGrowRate(aSots.subtract(perASots));

        return adContributesDataDto;
    }

    @Override
    public List<AdvertisingDataDto> getAdvertisingData(
            Integer puid, List<Integer> shopIds, String adType, String currency,
            LocalDate startDate, LocalDate endDate,  List<String> campaignIds, String portfolioId, String status) {
        List<AdvertisingDataDto> list = dashboardDao.getAdvertisingData(
                puid, shopIds, adType, currency, startDate, endDate,  campaignIds, portfolioId, status);

        //填充计算指标数据
        for (AdvertisingDataDto vo : list) {
            //acos 广告费/销售额*100%
            vo.setAcos(BigDecimal.ZERO.compareTo(vo.getSales()) == 0 ? BigDecimal.ZERO : vo.getCost()
                    .multiply(new BigDecimal("100")).divide(vo.getSales(),
                            2, RoundingMode.HALF_UP));
            //roas 销售额/广告费(非百分比数据)
            vo.setRoas(BigDecimal.ZERO.compareTo(vo.getCost()) == 0 ? BigDecimal.ZERO : vo.getSales()
                    .divide(vo.getCost(), 2, RoundingMode.HALF_UP));
            //广告转化率  广告订单量/点击量 * 100%
            vo.setSalesConversionRate(vo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getAdOrder())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getClicks()),
                            2, RoundingMode.HALF_UP));
        }
        return list;
    }

    /**
     * 广告效果
     *
     * @param puid
     * @param shopIds
     * @param currency
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public AdPerformanceDataDto getAdPerformance(
            Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
            LocalDate endDate,  List<String> campaigns, String portfolioId, String status) {
        //查询列表数据
        List<AdPerformanceDto> list = dashboardDao.listCampaignReport(
                puid, shopIds, adType, currency, startDate, endDate, campaigns,portfolioId, status);

        //查询环比汇总数据
        LocalDate perEndDate = startDate.minusDays(1);
        Period between = Period.between(startDate, endDate);
        LocalDate perStartDate = perEndDate.minus(between);

        List<AdPerformanceDto> sumList = dashboardDao.sumCampaignReport(
                puid, shopIds, adType, currency, perStartDate, perEndDate, campaigns,portfolioId, status);

        //填充6个计算指标数据
        for (AdPerformanceDto vo : list) {
            //acos 广告费/销售额*100%
            vo.setAcos(BigDecimal.ZERO.compareTo(vo.getAdSales()) == 0 ? BigDecimal.ZERO : vo.getCost()
                    .multiply(new BigDecimal("100")).divide(vo.getAdSales(),
                            2, RoundingMode.HALF_UP));
            //roas 销售额/广告费(非百分比数据)
            vo.setRoas(BigDecimal.ZERO.compareTo(vo.getCost()) == 0 ? BigDecimal.ZERO : vo.getAdSales()
                    .divide(vo.getCost(),
                            2, RoundingMode.HALF_UP));
            //广告点击率   点击量/曝光量 * 100%
            vo.setClickRate(vo.getImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getClicks())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getImpressions()),
                            2, RoundingMode.HALF_UP));
            //CPC 广告花费/广告点击量(非百分比数据)
            vo.setCpc(vo.getClicks() == 0 ? BigDecimal.ZERO : vo.getCost()
                    .divide(BigDecimal.valueOf(vo.getClicks()),
                            2, RoundingMode.HALF_UP));
            //广告转化率  广告订单量/点击量 * 100%
            vo.setSalesConversionRate(vo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getAdOrder())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getClicks()),
                            2, RoundingMode.HALF_UP));
        }

        //汇总数据
        //曝光
        Long totalImpressions = list.stream().map(AdPerformanceDto::getImpressions).reduce(Long::sum).orElse(0L);
        //点击
        Long totalClicks = list.stream().map(AdPerformanceDto::getClicks).reduce(Long::sum).orElse(0L);
        //订单量
        Integer totalAdOrders = list.stream().map(AdPerformanceDto::getAdOrder).reduce(Integer::sum).orElse(0);
        //花费
        BigDecimal totalCost = list.stream().map(AdPerformanceDto::getCost)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //销售额
        BigDecimal totalSales = list.stream().map(AdPerformanceDto::getAdSales)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        AdPerformanceDataDto vo = new AdPerformanceDataDto();
        vo.setCost(totalCost);
        vo.setAdSales(totalSales);
        vo.setAdOrder(totalAdOrders);
        vo.setClicks(totalClicks);
        vo.setImpressions(totalImpressions);

        //acos 广告费/销售额*100%
        vo.setAcos(BigDecimal.ZERO.compareTo(totalSales) == 0 ? BigDecimal.ZERO : totalCost
                .multiply(new BigDecimal("100")).divide(totalSales,
                        2, RoundingMode.HALF_UP));
        //roas 销售额/广告费(非百分比数据)
        vo.setRoas(BigDecimal.ZERO.compareTo(totalCost) == 0 ? BigDecimal.ZERO : totalSales
                .divide(totalCost,
                        2, RoundingMode.HALF_UP));
        //广告点击率   点击量/曝光量 * 100%
        vo.setClickRate(totalImpressions == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(totalClicks)
                .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(totalImpressions),
                        2, RoundingMode.HALF_UP));
        //CPC 广告花费/广告点击量(非百分比数据)
        vo.setCpc(totalClicks == 0 ? BigDecimal.ZERO : totalCost
                .divide(BigDecimal.valueOf(totalClicks),
                        2, RoundingMode.HALF_UP));
        //广告转化率  广告订单量/点击量 * 100%
        vo.setSalesConversionRate(totalClicks == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(totalAdOrders)
                .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(totalClicks),
                        2, RoundingMode.HALF_UP));

        //环比数据填充
        if (CollectionUtils.isNotEmpty(sumList)) {
            AdPerformanceDto perVo = sumList.get(0);
            vo.setPerImpressions(perVo.getImpressions());
            vo.setPerClicks(perVo.getClicks());
            vo.setPerCost(perVo.getCost());
            vo.setPerAdSales(perVo.getAdSales());
            vo.setPerAdOrder(perVo.getAdOrder());
            //acos 广告费/销售额*100%
            vo.setPerAcos(BigDecimal.ZERO.compareTo(vo.getPerAdSales()) == 0 ? BigDecimal.ZERO : vo.getPerCost()
                    .multiply(new BigDecimal("100")).divide(vo.getPerAdSales(),
                            2, RoundingMode.HALF_UP));
            //roas 销售额/广告费(非百分比数据)
            vo.setPerRoas(BigDecimal.ZERO.compareTo(vo.getPerCost()) == 0 ? BigDecimal.ZERO : vo.getPerAdSales()
                    .divide(vo.getPerCost(),
                            2, RoundingMode.HALF_UP));
            //广告点击率   点击量/曝光量 * 100%
            vo.setPerClickRate(vo.getPerImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getPerClicks())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getPerImpressions()),
                            2, RoundingMode.HALF_UP));
            //CPC 广告花费/广告点击量(非百分比数据)
            vo.setPerCpc(vo.getPerClicks() == 0 ? BigDecimal.ZERO : vo.getPerCost()
                    .divide(BigDecimal.valueOf(vo.getPerClicks()),
                            2, RoundingMode.HALF_UP));
            //广告转化率  广告订单量/点击量 * 100%
            vo.setPerSalesConversionRate(vo.getPerClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getPerAdOrder())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getPerClicks()),
                            2, RoundingMode.HALF_UP));


            //增加率
            vo.setImpressionsGrowRate(perVo.getImpressions() == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf(totalImpressions - perVo.getImpressions()).multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(perVo.getImpressions()), 2, RoundingMode.HALF_UP));

            vo.setClicksGrowRate(perVo.getClicks() == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf(totalClicks - perVo.getClicks()).multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(perVo.getClicks()), 2, RoundingMode.HALF_UP));

            vo.setAdOrderGrowRate(perVo.getAdOrder() == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf(totalAdOrders - perVo.getAdOrder()).multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf(perVo.getAdOrder()), 2, RoundingMode.HALF_UP));

            vo.setAdSalesGrowRate(perVo.getAdSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    (totalSales.subtract(perVo.getAdSales())).multiply(BigDecimal.valueOf(100))
                            .divide(perVo.getAdSales(), 2, RoundingMode.HALF_UP));

            vo.setCostGrowRate(perVo.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    (totalCost.subtract(perVo.getCost())).multiply(BigDecimal.valueOf(100))
                            .divide(perVo.getCost(), 2, RoundingMode.HALF_UP));


            vo.setClickRateGrowRate(vo.getClickRate().subtract(vo.getPerClickRate()));
            vo.setSalesConversionRateGrowRate(vo.getSalesConversionRate().subtract(vo.getPerSalesConversionRate()));
            vo.setAcosGrowRate(vo.getAcos().subtract(vo.getPerAcos()));
            vo.setRoasGrowRate(vo.getRoas().subtract(vo.getPerRoas()));

            vo.setCpcGrowRate(vo.getPerCpc().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    (vo.getCpc().subtract(vo.getPerCpc())).multiply(BigDecimal.valueOf(100))
                            .divide(vo.getPerCpc(), 2, RoundingMode.HALF_UP));
        }

        vo.setList(list);

        return vo;

    }

    /**
     * 广告活动TOP 10数据
     *
     * @param puid
     * @param shopIds
     * @param adType
     * @param currency
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<CampaignTopTenDto> getCampaignTopTen(
            Integer puid, List<Integer> shopIds, String adType, String currency,
            String orderBy, LocalDate startDate, LocalDate endDate,List<String> campaignIds, String portfolioId,String status) {


        //本期按条件查询top 10活动
        List<CampaignTopTenDto> list = dashboardDao.getCampaignTopTen(
                puid, shopIds, adType, campaignIds, portfolioId,status, currency, orderBy, startDate, endDate);

        //根据top查询所属店铺数据
        List<Integer> shopIdList = list.stream().map(CampaignTopTenDto::getShopId).distinct().collect(Collectors.toList());
        List<CampaignTopTenDto> sumData = dashboardDao.listSumCampaignPerformance(
                puid, shopIdList, currency, startDate, endDate);
        //店铺对应花费和广告销售额
        Map<Integer, CampaignTopTenDto> sumMap = sumData.stream().collect(Collectors.toMap(CampaignTopTenDto::getShopId,
                Function.identity(), (a, b) -> a));


        campaignIds = list.stream().map(CampaignTopTenDto::getCampaignId).collect(Collectors.toList());

        LocalDate perEndDate = startDate.minusDays(1);
        Period between = Period.between(startDate, endDate);
        LocalDate perStartDate = perEndDate.minus(between);

        //上期按条件查询top 10活动
        Map<String, CampaignTopTenDto> perCampaignTop10Map = null;
        Map<Integer, CampaignTopTenDto> perSumMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<CampaignTopTenDto> perList = dashboardDao.getCampaignTopTen(
                    puid, shopIdList, adType, campaignIds, currency, orderBy, perStartDate, perEndDate);
            if (CollectionUtils.isNotEmpty(perList)) {
                List<CampaignTopTenDto> perSumData = dashboardDao.listSumCampaignPerformance(
                        puid, shopIdList, currency, perStartDate, perEndDate);
                //上期对应店铺数据
                perSumMap = perSumData.stream().collect(Collectors.toMap(CampaignTopTenDto::getShopId,
                        Function.identity(), (a, b) -> a));
                perCampaignTop10Map = perList.stream().collect(Collectors.toMap(k -> String.valueOf(k.getShopId())
                        .concat("#").concat(k.getCampaignId()), Function.identity(), (a, b) -> a));
            }
        }

        //查询店铺名称
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIdList);
        Map<Integer, ShopAuth> shopAuthMap =
                shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));

        //填充占比数据
        for (CampaignTopTenDto vo : list) {
            //本期指标
            CampaignTopTenDto shopTotalData = sumMap.getOrDefault(vo.getShopId(), new CampaignTopTenDto());
            if (vo.getShopId() == null) {
                log.error(String.format("shopId is null, puid: %s, campaignId: %s", puid, vo.getCampaignId()));
                continue;
            }
            if (shopAuthMap.get(vo.getShopId()) == null) {
                log.error(String.format("shopId is null, puid: %s, shopId: %s campaignId: %s", puid, vo.getShopId(), vo.getCampaignId()));
                continue;
            }

            vo.setShopName(shopAuthMap.get(vo.getShopId()).getName());
            vo.setCostRate(shopTotalData.getTotalCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getCost()
                    .multiply(BigDecimal.valueOf(100)).divide(shopTotalData.getTotalCost(), 2, RoundingMode.HALF_UP));
            vo.setSalesRate(shopTotalData.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getSales()
                    .multiply(BigDecimal.valueOf(100)).divide(shopTotalData.getTotalSales(), 2, RoundingMode.HALF_UP));
            if (MapUtils.isNotEmpty(perCampaignTop10Map)) {
                //上期指标
                CampaignTopTenDto perShopTotalData = perSumMap.getOrDefault(vo.getShopId(), new CampaignTopTenDto());
                String key = String.valueOf(vo.getShopId()).concat("#").concat(vo.getCampaignId());
                if (perCampaignTop10Map.containsKey(key)) {
                    vo.setPerSalesRate(perShopTotalData.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ?
                            BigDecimal.ZERO : perCampaignTop10Map.get(key).getSales().multiply(BigDecimal.valueOf(100))
                            .divide(perShopTotalData.getTotalSales(), 2, RoundingMode.HALF_UP));
                    vo.setPerCostRate(perShopTotalData.getTotalCost().compareTo(BigDecimal.ZERO) == 0 ?
                            BigDecimal.ZERO : perCampaignTop10Map.get(key).getCost().multiply(BigDecimal.valueOf(100))
                            .divide(perShopTotalData.getTotalCost(), 2, RoundingMode.HALF_UP));
                }
            }
        }

        return list;
    }

    /**
     * 查询搜索词接口
     *
     * @param puid
     * @param shopIds
     * @param currency
     * @param orderBy
     * @param startDate
     * @param endDate
     * @param campaigns
     * @return
     */
    @Override
    public List<AdQueryWordDto> listQueryWord(
            Integer puid, List<Integer> shopIds, String currency, String orderBy,
            LocalDate startDate, LocalDate endDate, List<CampaignInfoDto> campaigns) {

        //过滤掉广告列表中,不包含的店铺
        List<CampaignInfoDto> campaignInfos =
                campaigns.stream().filter($ -> shopIds.contains($.getShopId())).collect(Collectors.toList());

        //按类型分组
        Map<AdType, List<CampaignInfoDto>> campaignMap = campaignInfos.stream()
                .collect(Collectors.groupingBy(CampaignInfoDto::getAdType));

        List<AdQueryWordDto> list = new ArrayList<>();

        //SP 查询query-keyword和 查询query-target两张表
        if (campaignMap.containsKey(AdType.SP)) {
            List<AdQueryTop5Dto> spList = campaignMap.get(AdType.SP).stream().map($ -> {
                AdQueryTop5Dto adQueryTop5Dto = new AdQueryTop5Dto();
                adQueryTop5Dto.setCampaignId($.getCampaignId());
                adQueryTop5Dto.setShopId($.getShopId());
                return adQueryTop5Dto;
            }).collect(Collectors.toList());
            List<AdQueryWordDto> spQueryKeywords = dashboardDao
                    .listSpQueryWord(puid, startDate, endDate, currency, orderBy, spList);
            list.addAll(spQueryKeywords);
            //过滤已经查询出结果的活动ID,再查询query_targeting表
            List<String> campaignsOfKeyword = spQueryKeywords.stream().map(AdQueryWordDto::getCampaignId)
                    .collect(Collectors.toList());
            List<AdQueryTop5Dto> campaignsOfTarget = spList.stream()
                    .filter($ -> !campaignsOfKeyword.contains($.getCampaignId())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(campaignsOfTarget)) {
                List<AdQueryWordDto> spQueryTargets = dashboardDao
                        .listSpQueryTargeting(puid, startDate, endDate, currency, orderBy, campaignsOfTarget);
                list.addAll(spQueryTargets);
            }
        }

        if (campaignMap.containsKey(AdType.SB)) {
            List<AdQueryTop5Dto> sbList = campaignMap.get(AdType.SB).stream().map($ -> {
                AdQueryTop5Dto adQueryTop5Dto = new AdQueryTop5Dto();
                adQueryTop5Dto.setCampaignId($.getCampaignId());
                adQueryTop5Dto.setShopId($.getShopId());
                return adQueryTop5Dto;
            }).collect(Collectors.toList());
            List<AdQueryWordDto> sbQueryKeywords = dashboardDao
                    .listSbQueryWord(puid, startDate, endDate, currency, orderBy, sbList);
            list.addAll(sbQueryKeywords);
        }

        //填充指标数据
        for (AdQueryWordDto dto : list) {
            //acos 广告费/销售额*100%
            dto.setAcos(BigDecimal.ZERO.compareTo(dto.getAdSales()) == 0 ? BigDecimal.ZERO : dto.getCost()
                    .multiply(new BigDecimal("100")).divide(dto.getAdSales(),
                            2, RoundingMode.HALF_UP));
            //roas 销售额/广告费(非百分比数据)
            dto.setRoas(BigDecimal.ZERO.compareTo(dto.getCost()) == 0 ? BigDecimal.ZERO : dto.getAdSales()
                    .divide(dto.getCost(),
                            2, RoundingMode.HALF_UP));
            //广告点击率   点击量/曝光量 * 100%
            dto.setClickRate(dto.getImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(dto.getClicks())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(dto.getImpressions()),
                            2, RoundingMode.HALF_UP));
            //CPC 广告花费/广告点击量(非百分比数据)
            dto.setCpc(dto.getClicks() == 0 ? BigDecimal.ZERO : dto.getCost()
                    .divide(BigDecimal.valueOf(dto.getClicks()),
                            2, RoundingMode.HALF_UP));
            //广告转化率  广告订单量/点击量 * 100%
            dto.setSalesConversionRate(dto.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(dto.getAdOrder())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(dto.getClicks()),
                            2, RoundingMode.HALF_UP));
        }

        return list;
    }

/** =================================================================  服务号数据看板接口 ====================================================================   **/
    /**
     * 获取全部广告活动本期以及上期(效果，转化，展示)数据
     */
    public AdAllCampDashboradDataDto getWxAllCampIndicatorData(Integer puid, List<Integer> shopIds, String adType, String currency, LocalDate startDate,
                                                             LocalDate endDate) {
        // 获取ben期汇总数据
        List<AdPerformanceDto> sumList = dashboardDao.sumCampaignReport(puid, shopIds, adType, currency, startDate, endDate, null);
        // 获取上期汇总数据
        LocalDate perEndDate = startDate.minusDays(1);
        Period period = Period.between(startDate, endDate);
        LocalDate perStartDate = perEndDate.minus(period);
        List<AdPerformanceDto> perSumList = dashboardDao.sumCampaignReport(puid, shopIds, adType, currency, perStartDate, perEndDate, null);
        AdAllCampDashboradDataDto vo = new AdAllCampDashboradDataDto();
        if (CollectionUtils.isNotEmpty(sumList)) {
            AdPerformanceDto sumVo = sumList.get(0);
            vo.setCost(sumVo.getCost());
            vo.setAdSales(sumVo.getAdSales());
            vo.setAdOrder(sumVo.getAdOrder());
            vo.setClicks(sumVo.getClicks());
            vo.setImpressions(sumVo.getImpressions());

            //acos 广告费/销售额*100%
            vo.setAcos(BigDecimal.ZERO.compareTo(sumVo.getAdSales()) == 0 ? BigDecimal.ZERO : sumVo.getCost()
                    .multiply(new BigDecimal("100")).divide(sumVo.getAdSales(),
                            2, RoundingMode.HALF_UP));
            //roas 销售额/广告费(非百分比数据)
            vo.setRoas(BigDecimal.ZERO.compareTo(sumVo.getCost()) == 0 ? BigDecimal.ZERO : sumVo.getAdSales()
                    .divide(sumVo.getCost(),
                            2, RoundingMode.HALF_UP));
            //广告点击率   点击量/曝光量 * 100%
            vo.setClickRate(sumVo.getImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(sumVo.getClicks())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(sumVo.getImpressions()),
                            2, RoundingMode.HALF_UP));
            //CPC 广告花费/广告点击量(非百分比数据)
            vo.setCpc(sumVo.getClicks() == 0 ? BigDecimal.ZERO : sumVo.getCost()
                    .divide(BigDecimal.valueOf(sumVo.getClicks()),
                            2, RoundingMode.HALF_UP));
            //广告转化率  广告订单量/点击量 * 100%
            vo.setSalesConversionRate(sumVo.getClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(sumVo.getAdOrder())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(sumVo.getClicks()),
                            2, RoundingMode.HALF_UP));
        }
        if (CollectionUtils.isNotEmpty(perSumList)) {
            AdPerformanceDto perVo = perSumList.get(0);
            vo.setPerImpressions(perVo.getImpressions());
            vo.setPerClicks(perVo.getClicks());
            vo.setPerCost(perVo.getCost());
            vo.setPerAdSales(perVo.getAdSales());
            vo.setPerAdOrder(perVo.getAdOrder());
            //acos 广告费/销售额*100%
            vo.setPerAcos(BigDecimal.ZERO.compareTo(vo.getPerAdSales()) == 0 ? BigDecimal.ZERO : vo.getPerCost()
                    .multiply(new BigDecimal("100")).divide(vo.getPerAdSales(),
                            2, RoundingMode.HALF_UP));
            //roas 销售额/广告费(非百分比数据)
            vo.setPerRoas(BigDecimal.ZERO.compareTo(vo.getPerCost()) == 0 ? BigDecimal.ZERO : vo.getPerAdSales()
                    .divide(vo.getPerCost(),
                            2, RoundingMode.HALF_UP));
            //广告点击率   点击量/曝光量 * 100%
            vo.setPerClickRate(vo.getPerImpressions() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getPerClicks())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getPerImpressions()),
                            2, RoundingMode.HALF_UP));
            //CPC 广告花费/广告点击量(非百分比数据)
            vo.setPerCpc(vo.getPerClicks() == 0 ? BigDecimal.ZERO : vo.getPerCost()
                    .divide(BigDecimal.valueOf(vo.getPerClicks()),
                            2, RoundingMode.HALF_UP));
            //广告转化率  广告订单量/点击量 * 100%
            vo.setPerSalesConversionRate(vo.getPerClicks() == 0 ? BigDecimal.ZERO : BigDecimal.valueOf(vo.getPerAdOrder())
                    .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(vo.getPerClicks()),
                            2, RoundingMode.HALF_UP));
        }
        return vo;
    }

    /**
     * 广告活动TOP 10数据
     * 服务号
     * @param puid
     * @param shopIds
     * @param adType
     * @param currency
     * @param startDate
     * @param endDate
     * @return
     */
    @Override
    public List<WxCampaignTopTenDto> getWxCampaignTopTen(
            Integer puid, List<Integer> shopIds, String adType, String currency,
            String orderBy, LocalDate startDate, LocalDate endDate) {
        //本期按条件查询top 10活动
        List<WxCampaignTopTenDto> list = dashboardDao.getWxCampaignTopTen(puid, shopIds, adType, currency, orderBy, startDate, endDate);
        //查询店铺名称
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIds);
        Map<Integer, ShopAuth> shopAuthMap =
                shopAuths.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity(), (a, b) -> a));
        list.stream().filter(item -> item.getShopId() != null).peek(item -> {
            ShopAuth shopAuth = shopAuthMap.get(item.getShopId());
            if (shopAuth != null && StringUtils.isNotBlank(shopAuth.getName())) {
                item.setShopName(shopAuth.getName());
            }
        }).collect(Collectors.toList());
        return list;
    }

    /**
     * 展示每小时维度的各指标数据
     * @param puid
     * @param shopIds
     * @param hour
     * @param currency
     * @return
     */
    @Override
    public List<WxCampaignTopTenDto> getHourlyDataIndicator(
            Integer puid, List<Integer> shopIds, Integer hour, String currency, String orderBy) {

        //查询当月汇率
        String month = LocalDateTime.now(ZoneOffset.of("+8")).format(DateTimeFormatter.ofPattern("yyyyMM"));
        List<UserCurrencyRateDto> rates = coverRateListByCurrency(puid, month, month, currency);
        Map<String, Double> rateMap = rates.stream()
                .collect(Collectors.toMap(UserCurrencyRateDto::getFrom, item -> item.getRate().doubleValue(), (a, b) -> a));
        LocalDateTime dateTime = LocalDateTime.now(ZoneId.of("UTC"))
                .minusHours(hour).withMinute(0).withSecond(0).withNano(0);
        String selectDateTime = LocalDateTimeUtil.formatTime(dateTime, LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(puid, shopIds);
        // 查询fead服务接口获取每个店铺orderBy降序前5条广告活动
        List<CampaignLastDayHourlyReportDto> shopDtos = shopAuths.stream().map(item -> {
            CampaignLastDayHourlyReportDto dto = new CampaignLastDayHourlyReportDto();
            dto.setSellerId(item.getSellingPartnerId());
            dto.setMarketplaceId(item.getMarketplaceId());
            return dto;
        }).collect(Collectors.toList());
//        // -- start
        List<AmazonMarketingStreamData> itemList = amazonMarketingStreamDataDao.getHourlyDataTopFiveCampaign(shopDtos, selectDateTime, orderBy, rateMap);
        int index = 0;
        for (AmazonMarketingStreamData amazonMarketingStreamData : itemList) {
            amazonMarketingStreamData.setIndex(index);
            index++;
        }
        Map<String, Integer> campaignIndMap = itemList.stream().filter(Objects::nonNull)
                .filter(item -> StringUtils.isNotBlank(item.getCampaignId())).collect(Collectors.toMap(AmazonMarketingStreamData::getCampaignId, AmazonMarketingStreamData::getIndex));
        if (CollectionUtils.isEmpty(itemList)) {
            return null;
        }
        // -- end
        // 查询fead服务接口获取orderBy降序前5条广告活动时间范围内分组小时数据
        // 获取utc时间
        LocalDateTime utcDateTime = LocalDateTime.now(ZoneId.of("UTC"));
        List<CampaignLastDayHourlyReportDto> dtos = itemList.stream().map(item -> {
            CampaignLastDayHourlyReportDto dto = new CampaignLastDayHourlyReportDto();
            dto.setSellerId(item.getSellerId());
            dto.setMarketplaceId(item.getMarketplaceId());
            dto.setCampaignId(item.getCampaignId());
            return dto;
        }).collect(Collectors.toList());
        List<AmazonMarketingStreamData> amazonMarketingStreamData = getTopFiveCampaignIndicatorPartition(dtos, selectDateTime);
        //转换成vo
        // --end
        return coverRateList(amazonMarketingStreamData, rateMap, campaignIndMap, utcDateTime);
    }

    private List<AmazonMarketingStreamData> getTopFiveCampaignIndicatorPartition(List<CampaignLastDayHourlyReportDto> list, String dateTime){
        if (list.size() < 9000) {
            return amazonMarketingStreamDataDao.getTopFiveCampaignIndicatorDoris(list, dateTime);
        }
        List<AmazonMarketingStreamData> sponsoredProductsCampaignLastDayHourlyReports = new ArrayList<>();
        List<List<CampaignLastDayHourlyReportDto>> listList = Lists.partition(list, 9000);
        for (List<CampaignLastDayHourlyReportDto> campaignLastDayHourlyReportDtos : listList) {
            sponsoredProductsCampaignLastDayHourlyReports.addAll(amazonMarketingStreamDataDao.getTopFiveCampaignIndicatorDoris(campaignLastDayHourlyReportDtos, dateTime));
        }
        return sponsoredProductsCampaignLastDayHourlyReports;
    }

    /**
     * 累加所有小时级汇总数据
     *
     * @param list
     * @return
     */
    private DashboardHourReportDto sumHourReport(List<DashboardHourReportDto> list) {
        //曝光
        Long totalImpressions = list.stream().map(DashboardHourReportDto::getImpressions).reduce(Long::sum).orElse(0L);
        //点击
        Long totalClicks = list.stream().map(DashboardHourReportDto::getClicks).reduce(Long::sum).orElse(0L);
        //订单量
        Integer totalAdOrders = list.stream().map(DashboardHourReportDto::getAdOrder).reduce(Integer::sum).orElse(0);
        //花费
        BigDecimal totalCost = list.stream().map(item -> BigDecimal.valueOf(item.getCost()))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //销售额
        BigDecimal totalSales = list.stream().map(item -> BigDecimal.valueOf(item.getAdSales()))
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //最近更新小时
        String lastUpdateAt = list.stream().map(DashboardHourReportDto::getLastUpdateAt)
                .max(Comparator.comparing(String::valueOf)).orElse(LocalDateTime.now(ZoneId.of("UTC"))
                        .minusMinutes(20)
                        .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        DashboardHourReportDto dto = new DashboardHourReportDto();
        dto.setClicks(totalClicks);
        dto.setImpressions(totalImpressions);
        dto.setCost(totalCost.setScale(2, RoundingMode.HALF_UP).doubleValue());
        dto.setAdSales(totalSales.setScale(2, RoundingMode.HALF_UP).doubleValue());
        dto.setAdOrder(totalAdOrders);

        //acos 广告费/销售额*100%
        dto.setAcos(BigDecimal.ZERO.compareTo(totalSales) == 0 ? 0.0 : totalCost
                .multiply(new BigDecimal("100")).divide(totalSales,
                        2, RoundingMode.HALF_UP).doubleValue());
        //roas 销售额/广告费(非百分比数据)
        dto.setRoas(BigDecimal.ZERO.compareTo(totalCost) == 0 ? 0.0 : totalSales
                .divide(totalCost,
                        2, RoundingMode.HALF_UP).doubleValue());
        //广告点击率   点击量/曝光量 * 100%
        dto.setClickRate(totalImpressions == 0 ? 0.0 : BigDecimal.valueOf(totalClicks)
                .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(totalImpressions),
                        2, RoundingMode.HALF_UP).doubleValue());
        //CPC 广告花费/广告点击量(非百分比数据)
        dto.setCpc(totalClicks == 0 ? 0.0 : totalCost
                .divide(BigDecimal.valueOf(totalClicks),
                        2, RoundingMode.HALF_UP).doubleValue());
        //广告转化率  广告订单量/点击量 * 100%
        dto.setSalesConversionRate(totalClicks == 0 ? 0.0 : BigDecimal.valueOf(totalAdOrders)
                .multiply(new BigDecimal("100")).divide(BigDecimal.valueOf(totalClicks),
                        2, RoundingMode.HALF_UP).doubleValue());
        dto.setLastUpdateAt(lastUpdateAt);
        return dto;
    }


    /**
     * 查询汇率(数仓)
     *
     * @param puid
     * @param startMonth
     * @param endMonth
     * @return
     */
    private List<UserCurrencyRateDto> listCurrencyRateByPuidAndMonth(int puid, String startMonth, String endMonth) {
        return dashboardDao.listCurrencyRateByPuidAndMonth(puid, startMonth, endMonth);
    }

    /**
     * 查询币种汇率(数仓)
     * @param puid
     * @param startMonth
     * @param endMonth
     * @return
     */
    private List<UserCurrencyRateDto> coverRateListByCurrency(int puid, String startMonth, String endMonth, String currency) {
        return dashboardDao.coverRateListByCurrency(puid, startMonth, endMonth, currency);
    }

    /**
     * 转换统一币种封装list
     */
    private List<WxCampaignTopTenDto> coverRateList(List<AmazonMarketingStreamData> itemList, Map<String, Double> rateMap, Map<String, Integer> orderCampaignMap, LocalDateTime utcDateTime) {
        List<WxCampaignTopTenDto> list = itemList.stream().map($ -> {
            Double rate = rateMap.getOrDefault($.getCurrency(), 1.00);
            WxCampaignTopTenDto dto = new WxCampaignTopTenDto();
            dto.setIndex(orderCampaignMap.get($.getCampaignId()));
            dto.setSellerId($.getSellerId());
            dto.setMarketplaceId($.getMarketplaceId());
            dto.setCampaignId($.getCampaignId());
            dto.setHour(Duration.between($.getUtcDateTime(), utcDateTime).toHours());
            dto.setCost(BigDecimal.valueOf($.getCost())
                    .multiply(BigDecimal.valueOf(rate)).setScale(2, RoundingMode.HALF_UP));
            dto.setAdSales($.getAttributedSales7d()
                    .multiply(BigDecimal.valueOf(rate)).setScale(2, RoundingMode.HALF_UP));
            dto.setAdOrder($.getAttributedConversions7d());
            dto.setClicks($.getClicks());
            dto.setImpressions($.getImpressions());
            dto.setAcos($.getAttributedSales7d().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf($.getCost()).multiply(BigDecimal.valueOf(rate)).multiply(BigDecimal.valueOf(100))
                            .divide($.getAttributedSales7d().multiply(BigDecimal.valueOf(rate)), 2, RoundingMode.HALF_UP));

            dto.setSpc($.getClicks() == 0 ? BigDecimal.ZERO :
                    $.getAttributedSales7d().multiply(BigDecimal.valueOf(rate))
                            .divide(BigDecimal.valueOf($.getClicks()), 2, RoundingMode.HALF_UP));

            dto.setCpc($.getClicks() == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf($.getCost()).multiply(BigDecimal.valueOf(rate))
                            .divide(BigDecimal.valueOf($.getClicks()), 2, RoundingMode.HALF_UP));
            dto.setSalesConversionRate($.getClicks() == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf($.getAttributedConversions7d()).multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf($.getClicks()), 2, RoundingMode.HALF_UP));

            dto.setClickRate($.getImpressions() == 0 ? BigDecimal.ZERO :
                    BigDecimal.valueOf($.getClicks()).multiply(BigDecimal.valueOf(100))
                            .divide(BigDecimal.valueOf($.getImpressions()), 2, RoundingMode.HALF_UP));
            return dto;
        }).collect(Collectors.toList());
        return list;
    }
}
