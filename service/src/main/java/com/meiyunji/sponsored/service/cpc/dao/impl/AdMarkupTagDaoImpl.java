package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.po.AdMarkupTag;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

/**
 * 标签管理
 * <AUTHOR>
 */
@Repository
public class AdMarkupTagDaoImpl extends BaseShardingDaoImpl<AdMarkupTag> implements IAdMarkupTagDao {




    @Override
    public void insertList(List<AdMarkupTag> list) {
        StringBuilder sql = new StringBuilder("insert into `t_ad_markup_tag` (`puid`, `shop_id`, `marketplace_id`, `tag_id`, `relation_id`, " +
                "`type`, `ad_type`,`target_type`, `create_id`, `update_id`, `create_time`, `update_time`) values");
        List<Object> argsList = Lists.newArrayList();
        for (AdMarkupTag e : list) {
            sql.append(" (?, ?,?,?, ?,?, ?,?, ?,?,  now(3),now(3)),");
            argsList.add(e.getPuid());
            argsList.add(e.getShopId());
            argsList.add(e.getMarketplaceId());
            argsList.add(e.getTagId());
            argsList.add(e.getRelationId());
            argsList.add(e.getType());
            argsList.add(e.getAdType());
            argsList.add(e.getTargetType());
            argsList.add(e.getCreateId());
            argsList.add(e.getUpdateId());

        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `update_id`=values(update_id),update_time=now(3)");
        getJdbcTemplate(list.get(0).getPuid()).update(sql.toString(), argsList.toArray());
    }


    @Override
    public List<String> getRelationIds(Integer puid, Integer shopId, String type, Long tagId, String adType,String targetType) {
        StringBuilder sql = new StringBuilder("select DISTINCT relation_id from t_ad_markup_tag where puid = ? ");
        List<Object> args = Lists.newArrayList(puid);
        if(shopId != null){
            sql.append(" and shop_id = ? ");
            args.add(shopId);
        }
        if(tagId != null){
            sql.append(" and tag_id = ? ");
            args.add(tagId);
        }
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        if(StringUtils.isNotBlank(adType)){
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if(StringUtils.isNotBlank(targetType)){
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(),String.class,args.toArray());
    }

    @Override
    public List<String> getRelationIds(Integer puid, Integer shopId, String type, List<Long> tagIds, String adType, String targetType) {
        StringBuilder sql = new StringBuilder("select DISTINCT relation_id from t_ad_markup_tag where puid = ? ");
        List<Object> args = Lists.newArrayList(puid);
        if(shopId != null){
            sql.append(" and shop_id = ? ");
            args.add(shopId);
        }
        if(CollectionUtils.isNotEmpty(tagIds)){
            sql.append(SqlStringUtil.dealInList("tag_id", tagIds, args));
        }
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        if(StringUtils.isNotBlank(adType)){
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if(StringUtils.isNotBlank(targetType)){
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(),String.class,args.toArray());
    }

    @Override
    public List<String> getMultipleRelationIds(Integer puid, List<Integer> shopIdList, String type, List<Long> tagIds, String adType, String targetType) {
        StringBuilder sql = new StringBuilder("select DISTINCT relation_id from t_ad_markup_tag where puid = ? ");
        List<Object> args = Lists.newArrayList(puid);
        sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        if(CollectionUtils.isNotEmpty(tagIds)){
            sql.append(SqlStringUtil.dealInList("tag_id", tagIds, args));
        }
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        if(StringUtils.isNotBlank(adType)){
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if(StringUtils.isNotBlank(targetType)){
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(),String.class,args.toArray());
    }

    /**
     * 删除 指定指定标签id数据
     * @param puid
     * @param tagId
     * @return
     */
    @Override
    public int deleteByTagId(Integer puid, Long tagId){
        String sql = "delete from t_ad_markup_tag where puid = ? and tag_id = ? ";
        return getJdbcTemplate(puid).update(sql,puid,tagId);
    }


    /**
     * 删除指点关联id的数据
     * @param puid
     * @param shopId
     * @param type
     * @param adType
     * @param relationId
     * @return
     */
    @Override
    public int deleteByRelationId(Integer puid, Integer shopId, String type, String adType, String targetType, List<String> relationId,Long adTagId){
        StringBuilder sql = new StringBuilder("delete from t_ad_markup_tag where puid = ? and shop_id = ?  ");
        List<Object> args = Lists.newArrayList(puid,shopId);
        sql.append(SqlStringUtil.dealInList("relation_id",relationId,args));
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        if(StringUtils.isNotBlank(adType)){
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if(adTagId != null){
            sql.append(" and tag_id = ? ");
            args.add(adTagId);
        }
        if(StringUtils.isNotBlank(targetType)){
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }

        return getJdbcTemplate(puid).update(sql.toString(),args.toArray());
    }

    @Override
    public List<AdMarkupTagVo> getRelationVos(Integer puid, Integer shopId, String type, String adType, String targetType,Long tagId, List<String> relationIds){
        StringBuilder sql = new StringBuilder("select puid, shop_id,relation_id,GROUP_CONCAT(DISTINCT tag_id) tagIdsStr from t_ad_markup_tag where puid = ? and shop_id = ? ");
        List<Object> args = Lists.newArrayList(puid,shopId);

        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (StringUtils.isNotBlank(adType) && !AdTagTypeEnum.CAMPAIGN.getType().equalsIgnoreCase(type)) {
            sql.append(SqlStringUtil.dealInList("ad_type", StringUtil.splitStr(adType), args));
        }
        if(tagId != null){
            sql.append(" and tag_id = ? ");
            args.add(tagId);
        }
        if(StringUtils.isNotBlank(targetType)){
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        if(CollectionUtils.isNotEmpty(relationIds)){
            sql.append(SqlStringUtil.dealInList( "relation_id",relationIds,args));
        }
        sql.append( " group by relation_id ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdMarkupTagVo>() {
            @Override
            public AdMarkupTagVo mapRow(ResultSet re, int i) throws SQLException {
                AdMarkupTagVo dto = AdMarkupTagVo.builder()
                        .relationId(re.getString("relation_id"))
                        .shopId(re.getInt("shop_id"))
                        .puid(re.getInt("puid"))
                        .tagIdsStr(re.getString("tagIdsStr"))
                        .build();
                dto.setTagIdsList(dto.getTagIdsStr());
                return dto;
            }
        }, args.toArray());
    }

    @Override
    public List<AdMarkupTagVo> getRelationVosByShopIdList(Integer puid, List<Integer> shopIdList, String type, String adType, String targetType, Long tagId, List<String> relationIds) {
        StringBuilder sql = new StringBuilder("select puid, shop_id, relation_id, GROUP_CONCAT(DISTINCT tag_id) tagIdsStr from t_ad_markup_tag where puid = ?");
        List<Object> args = Lists.newArrayList(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (StringUtils.isNotBlank(adType)) {
            sql.append(" and ad_type = ? ");
            args.add(adType);
        }
        if (tagId != null) {
            sql.append(" and tag_id = ? ");
            args.add(tagId);
        }
        if (StringUtils.isNotBlank(targetType)) {
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        if (CollectionUtils.isNotEmpty(relationIds)) {
            sql.append(SqlStringUtil.dealInList("relation_id", relationIds, args));
        }
        sql.append(" group by relation_id ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdMarkupTagVo>() {
            @Override
            public AdMarkupTagVo mapRow(ResultSet re, int i) throws SQLException {
                AdMarkupTagVo dto = AdMarkupTagVo.builder()
                        .relationId(re.getString("relation_id"))
                        .shopId(re.getInt("shop_id"))
                        .puid(re.getInt("puid"))
                        .tagIdsStr(re.getString("tagIdsStr"))
                        .build();
                dto.setTagIdsList(dto.getTagIdsStr());
                return dto;
            }
        }, args.toArray());
    }

    @Override
    public List<AdMarkupTagVo> getRelationVosByShopIdList(Integer puid, List<Integer> shopIdList, String type, List<String> adTypeList, String targetType, Long tagId, List<String> relationIds) {
        StringBuilder sql = new StringBuilder("select puid, shop_id, relation_id, GROUP_CONCAT(DISTINCT tag_id) tagIdsStr from t_ad_markup_tag where puid = ?");
        List<Object> args = Lists.newArrayList(puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, args));
        }
        if (StringUtils.isNotBlank(type)) {
            sql.append(" and type = ? ");
            args.add(type);
        }
        if (CollectionUtils.isNotEmpty(adTypeList)) {
            sql.append(SqlStringUtil.dealInList("ad_type", adTypeList, args));
        }
        if (tagId != null) {
            sql.append(" and tag_id = ? ");
            args.add(tagId);
        }
        if (StringUtils.isNotBlank(targetType)) {
            sql.append(" and target_type = ? ");
            args.add(targetType);
        }
        if (CollectionUtils.isNotEmpty(relationIds)) {
            sql.append(SqlStringUtil.dealInList("relation_id", relationIds, args));
        }
        sql.append(" group by relation_id ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdMarkupTagVo>() {
            @Override
            public AdMarkupTagVo mapRow(ResultSet re, int i) throws SQLException {
                AdMarkupTagVo dto = AdMarkupTagVo.builder()
                        .relationId(re.getString("relation_id"))
                        .shopId(re.getInt("shop_id"))
                        .puid(re.getInt("puid"))
                        .tagIdsStr(re.getString("tagIdsStr"))
                        .build();
                dto.setTagIdsList(dto.getTagIdsStr());
                return dto;
            }
        }, args.toArray());
    }

    @Override
    public List<Long> getAdTagId(Integer puid, String type, List<String> relationIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("type",type)
                .in("relation_id", relationIds.toArray())
                .build();
        return listDistinctFieldByCondition(puid, "tag_id", builder, Long.class);
    }

    @Override
    public List<String> getCampaignIds(Integer puid, Integer shopId, String type, List<Long> tagIds, List<String> campaignIdList) {
        StringBuilder sql = new StringBuilder("select DISTINCT relation_id from t_ad_markup_tag where puid = ? ");
        List<Object> args = Lists.newArrayList(puid);
        if(shopId != null){
            sql.append(" and shop_id = ? ");
            args.add(shopId);
        }
        if(CollectionUtils.isNotEmpty(tagIds)){
            sql.append(SqlStringUtil.dealInList("tag_id", tagIds, args));
        }
        if(StringUtils.isNotBlank(type)){
            sql.append(" and type = ? ");
            args.add(type);
        }
        if(CollectionUtils.isNotEmpty(campaignIdList)){
            sql.append(SqlStringUtil.dealInList("relation_id", campaignIdList, args));
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(),String.class,args.toArray());
    }

    @Override
    public List<AdMarkupTag> listAllByPuidAndType(Integer puid, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("type",type)
                .build();
        return listByCondition(puid, builder);
    }
}