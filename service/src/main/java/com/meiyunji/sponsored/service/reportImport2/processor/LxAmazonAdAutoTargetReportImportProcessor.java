package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdAutoTargetReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdProductReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdAutoTargetReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdProductReport> {



    private final ICpcTargetingReportDao cpcTargetingReportDao;
    private final IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;


    protected LxAmazonAdAutoTargetReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                        IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                        IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                        ICpcTargetingReportDao cpcTargetingReportDao,
                                                        IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);

        this.cpcTargetingReportDao = cpcTargetingReportDao;
        this.amazonAdTargetDaoRoutingService = amazonAdTargetDaoRoutingService;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdAutoTargetReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdAutoTargetReport report = new LxAmazonAdAutoTargetReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdAutoTargetReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> targetIds = reports.stream().map(LxAmazonAdAutoTargetReport::getTargetId).collect(Collectors.toList());


        Map<String, AmazonAdTargeting> adTargetingMap = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, shopId, targetIds).stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));


        List<CpcTargetingReport> spReports = new ArrayList<>();

        reports.forEach(e -> {
            AmazonAdTargeting adTargeting = adTargetingMap.get(e.getTargetId());
            if (adTargeting == null) {
                log.error("pxq-report-import puid : {} shop_id : {} target_id : {} 不存在", puid, shopId, e.getTargetId());
                return;
            }

            CpcTargetingReport spReport = buildSpAdAutoTargetReport(importMessage.getCountDate(), e, adTargeting, shopAuth);
            spReports.add(spReport);

        });


        //持久数据到数据库
        if (CollectionUtils.isNotEmpty(spReports)) {
            cpcTargetingReportDao.insertList(puid, spReports);
        }

    }


    /**
     * 构建sp广告组报告
     *
     * @param report 报告
     * @return {@link CpcTargetingReport}
     */
    private CpcTargetingReport buildSpAdAutoTargetReport(String countDate, LxAmazonAdAutoTargetReport report, AmazonAdTargeting amazonAdTargeting, ShopAuth shopAuth) {


        CpcTargetingReport po = new CpcTargetingReport();
        po.setCountDate(countDate);
        po.setPuid(shopAuth.getPuid());
        po.setShopId(shopAuth.getId());
        po.setMarketplaceId(shopAuth.getMarketplaceId());
        po.setCampaignId(report.getCampaignId());
        po.setAdGroupId(report.getAdGroupId());
        po.setTargetId(amazonAdTargeting.getTargetId());
        po.setAdGroupName(report.getAdGroupName());
        po.setCampaignName(report.getCampaignName());
        po.setTotalSales(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        po.setAdSales(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        po.setOrderNum(report.getAdUnits() != null ? report.getAdUnits() : 0);
        po.setAdOrderNum(report.getDirectUnits() != null ? report.getDirectUnits() : 0);
        po.setSaleNum(report.getOrders() != null ? report.getOrders() : 0);
        po.setAdSaleNum(report.getDirectOrders() != null ? report.getDirectOrders() : 0);

        po.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        po.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        po.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);

        return po;
    }


}
