package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@DbTable(value = "t_tiktok_gmv_max_campaign")
public class TikTokGmvMaxCampaign extends BasePo {


    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    @DbColumn("puid")
    private Integer puid;

    @DbColumn("shop_id")
    private Integer shopId;

    @DbColumn("advertiser_id")
    private String advertiserId;

    @DbColumn("store_id")
    private String storeId;

    @DbColumn("store_authorized_bc_id")
    private String storeAuthorizedBcId;

    @DbColumn("campaign_id")
    private String campaignId;

    @DbColumn("campaign_name")
    private String campaignName;

    @DbColumn("operation_status")
    private String operationStatus;

    @DbColumn("shopping_ads_type")
    private String shoppingAdsType;

    @DbColumn("product_specific_type")
    private String productSpecificType;

    @DbColumn("item_group_ids")
    private String itemGroupIds;

    @DbColumn("optimization_goal")
    private String optimizationGoal;

    @DbColumn("deep_bid_type")
    private String deepBidType;

    @DbColumn("roas_bid")
    private BigDecimal roasBid;

    @DbColumn("budget")
    private BigDecimal budget;

    @DbColumn("schedule_type")
    private String scheduleType;

    @DbColumn("schedule_start_time")
    private LocalDateTime scheduleStartTime;

    @DbColumn("schedule_end_time")
    private LocalDateTime scheduleEndTime;

    @DbColumn("placements")
    private String placements;

    @DbColumn("location_ids")
    private String locationIds;

    @DbColumn("age_groups")
    private String ageGroups;

    @DbColumn("product_video_specific_type")
    private String productVideoSpecificType;

    @DbColumn("identity_list")
    private String identityList;

    @DbColumn("affiliate_posts_enabled")
    private Boolean affiliatePostsEnabled;

    @DbColumn("item_list")
    private String itemList;

    @DbColumn("campaign_custom_anchor_video_id")
    private String campaignCustomAnchorVideoId;

    @DbColumn("custom_anchor_video_list")
    private String customAnchorVideoList;

    @DbColumn("create_time_utc")
    private LocalDateTime createTimeUtc;

    @DbColumn("modify_time_utc")
    private LocalDateTime modifyTimeUtc;

    @DbColumn("objective_type")
    private String objectiveType;

    @DbColumn("primary_status")
    private String primaryStatus;

    @DbColumn("secondary_status")
    private String secondaryStatus;

}
