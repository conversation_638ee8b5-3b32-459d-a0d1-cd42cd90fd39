package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.mode.targeting.Brand;
import com.amazon.advertising.mode.targeting.CategoryRecommendations;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.TargetingClause;
import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.amazon.advertising.spV3.enumeration.SpV3State;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.enumeration.SpV3TargetingTypeEnum;
import com.amazon.advertising.spV3.negativetargeting.CreateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.NegativeTargetSpV3Client;
import com.amazon.advertising.mode.targeting.*;
import com.amazon.advertising.spV3.enumeration.*;
import com.amazon.advertising.spV3.keyword.KeywordSpV3Client;
import com.amazon.advertising.spV3.keyword.UpdateSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.entity.KeywordExtendEntityV3;
import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.keyword.entity.PutKeywordEntityV3;
import com.amazon.advertising.spV3.negativetargeting.UpdateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.entity.*;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazon.advertising.spV3.targeting.CreateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.ListSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.UpdateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.entity.*;
import com.amazon.advertising.spV3.negativetargeting.CreateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.NegativeTargetSpV3Client;
import com.amazon.advertising.spV3.targeting.CreateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.entity.TargetExpression;
import com.amazon.advertising.spV3.targeting.entity.TargetResolveExpression;
import com.amazon.advertising.spV3.targeting.entity.*;
import com.amazon.advertising.targeting.GetBrandRecommendationsNewResponse;
import com.amazon.advertising.targeting.GetRefinementsResponse;
import com.amazon.advertising.targeting.GetTargetingCategoriesRecommendationsResponse;
import com.amazon.advertising.targeting.ProductTargetingClient;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdKeywordShardingDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.Keyword;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SpNeTargetingVo;
import com.meiyunji.sponsored.service.cpc.vo.UpdateBatchTargetVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.AdStateV3;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.enums.TargetingExpressionPredicate;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/15.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcTargetingApiService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IDorisService dorisService;

    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;

    /**
     * 同步所有投放
     *
     * @param shop：
     */
    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId) {
        syncTargetings(shop, campaignId, groupId, targetId, false);
    }

    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, boolean nullResThrowException) {
        syncTargetings(shop, campaignId, groupId, targetId, null, nullResThrowException);
    }

    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<SpV3State> stateList, boolean nullResThrowException) {
        syncTargetings(shop, campaignId, groupId, targetId, stateList, nullResThrowException, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String ids){
        List<AmazonAdTargeting> amazons = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, shopId, StringUtil.stringToList(ids, ","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                    key.setServingStatus(key.getServingStatus());
                    return AmazonServingStatusDto.build(key.getTargetId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());
                }
        ).collect(Collectors.toList());
    }

    /**
     * 同步所有投放
     *
     * @param shop：
     */
    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<SpV3State> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }

        List<String> campaignIdList = null;
        List<String> groupIdList = null;
        List<String> targetIdList = null;

        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.splitStr(campaignId,",");
        }

        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }

        if(StringUtils.isNotBlank(targetId)){
            targetIdList = StringUtil.splitStr(targetId,",");
        }

        //获取活动的基本信息
        TargetSpV3Client client = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());

        if (isProxy) {
            client = TargetSpV3Client.getInstance(true);
        }

        int count = 2000;
        ListSpTargetV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listTarget(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                campaignIdList, groupIdList, targetIdList, stateList, null, null,
                null, Boolean.TRUE, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP targetings rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listTarget(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList, groupIdList, targetIdList, stateList, null, null,
                    null, Boolean.TRUE, nextToken, count);
                retry++;
            }
            if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401
                && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sp syncTargetings error");
            }

            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getTargetingClauses())) {
                break;
            }

            int size = response.getData().getTargetingClauses().size();
            AmazonAdTargeting amazonAdTargeting;
            List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>(size);
            for (TargetExtendEntityV3 targetingClause : response.getData().getTargetingClauses()) {
                amazonAdTargeting = turnToPO(targetingClause);
                if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                    amazonAdTargeting.setPuid(shop.getPuid());
                    amazonAdTargeting.setShopId(shop.getId());
                    amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdTargeting.setProfileId(amazonAdProfile.getProfileId());

                    // 确定投放的类型
                    if (CollectionUtils.isNotEmpty(targetingClause.getResolvedExpression())) {
                        for (TargetResolveExpression expression : targetingClause.getResolvedExpression()) {
                            if (SpV3ExpressionEnum.asinSameAs.getValueV3().equalsIgnoreCase(expression.getType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                                amazonAdTargeting.setTargetingValue(expression.getValue());
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                                amazonAdTargeting.setSelectType(expressionByValueV3 == null ? expression.getType() :expressionByValueV3.getValue());
                                break;
                            }
                            if (SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_CATEGORY);
                                amazonAdTargeting.setTargetingValue(expression.getValue());
                                break;
                            }
                            if (SpV3ExpressionEnum.queryHighRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.queryBroadRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.asinSubstituteRelated.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.asinAccessoryRelated.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_AUTO);
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                                amazonAdTargeting.setTargetingValue(expressionByValueV3 == null ? expression.getType() : expressionByValueV3.getValue());
                                break;
                            }
                        }
                    }

                    amazonAdTargetings.add(amazonAdTargeting);
                }
            }
            Map<String, List<TargetExtendEntityV3>> collect = response.getData().getTargetingClauses().stream().collect(Collectors.groupingBy(e -> "KEYWORD_GROUP_SAME_AS".equals(e.getExpression().get(0).getType()) ? "keyword" : "target"));
            saveKeyword(shop, amazonAdProfile, collect.get("keyword"));
            saveTarget(shop, amazonAdProfile, collect.get("target"));
            if (StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
    }


    /**
     * 同步所有投放
     *
     * @param shop：
     */
    public void syncTargetingsWithAuthed(AmazonAdProfile amazonAdProfile, ShopAuth shop, String campaignId, String groupId, String targetId, List<SpV3State> stateList, boolean nullResThrowException, boolean isProxy) {

        List<String> campaignIdList = null;
        List<String> groupIdList = null;
        List<String> targetIdList = null;

        if (StringUtils.isNotBlank(campaignId)) {
            campaignIdList = StringUtil.splitStr(campaignId, ",");
        }

        if (StringUtils.isNotBlank(groupId)) {
            groupIdList = StringUtil.splitStr(groupId, ",");
        }

        if (StringUtils.isNotBlank(targetId)) {
            targetIdList = StringUtil.splitStr(targetId, ",");
        }

        //获取活动的基本信息
        //获取活动的基本信息
        TargetSpV3Client client = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());

        if (isProxy) {
            client = TargetSpV3Client.getInstance(true);
        }
        ListSpTargetV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            int count = 2000;
            response = client.listTarget(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                campaignIdList, groupIdList, targetIdList, stateList, null, null,
                null, Boolean.TRUE, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP targetings rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if (retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listTarget(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList, groupIdList, targetIdList, stateList, null, null,
                    null, Boolean.TRUE, nextToken, count);
                retry++;
            }
            if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401
                && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sp syncTargetings error");
            }

            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getTargetingClauses())) {
                break;
            }

            int size = response.getData().getTargetingClauses().size();
            AmazonAdTargeting amazonAdTargeting;
            List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>(size);
            for (TargetExtendEntityV3 targetingClause : response.getData().getTargetingClauses()) {
                amazonAdTargeting = turnToPO(targetingClause);
                if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                    amazonAdTargeting.setPuid(shop.getPuid());
                    amazonAdTargeting.setShopId(shop.getId());
                    amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdTargeting.setProfileId(amazonAdProfile.getProfileId());

                    // 确定投放的类型
                    if (CollectionUtils.isNotEmpty(targetingClause.getResolvedExpression())) {
                        for (TargetResolveExpression expression : targetingClause.getResolvedExpression()) {
                            if (SpV3ExpressionEnum.asinSameAs.getValueV3().equalsIgnoreCase(expression.getType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                                amazonAdTargeting.setTargetingValue(expression.getValue());
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                                amazonAdTargeting.setSelectType(expressionByValueV3 == null ? expression.getType() : expressionByValueV3.getValue());
                                break;
                            }
                            if (SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_CATEGORY);
                                amazonAdTargeting.setTargetingValue(expression.getValue());
                                break;
                            }
                            if (SpV3ExpressionEnum.queryHighRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.queryBroadRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.asinSubstituteRelated.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.asinAccessoryRelated.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_AUTO);
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                                amazonAdTargeting.setTargetingValue(expressionByValueV3 == null ? expression.getType() : expressionByValueV3.getValue());
                                break;
                            }
                        }
                    }

                    amazonAdTargetings.add(amazonAdTargeting);
                }
            }

            Map<String, List<TargetExtendEntityV3>> collect = response.getData().getTargetingClauses().stream().collect(Collectors.groupingBy(e -> "KEYWORD_GROUP_SAME_AS".equals(e.getExpression().get(0).getType()) ? "keyword" : "target"));
            saveKeyword(shop, amazonAdProfile, collect.get("keyword"));
            saveTarget(shop, amazonAdProfile, collect.get("target"));

            if (StringUtils.isNotBlank(response.getData().getNextToken())) {
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
    }

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId) {
        syncNeTargetings(shop, campaignId, groupId, targetId, false);
    }

    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, boolean nullResThrowException) {
        syncNeTargetings(shop, campaignId, groupId, targetId, null, nullResThrowException);
    }

    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<String> stateList, boolean nullResThrowException) {
        syncNeTargetings(shop, campaignId, groupId, targetId, null, nullResThrowException, false);
    }

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncNeTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<String> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }

        List<String> campaignIds = null;
        List<String> groupIdList = null;
        List<String> targetIdList = null;

        if(StringUtils.isNotBlank(campaignId)){
            campaignIds = StringUtil.splitStr(campaignId,",");
        }

        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }

        if(StringUtils.isNotBlank(targetId)){
            targetIdList = StringUtil.splitStr(targetId,",");
        }

        NegativeTargetSpV3Client client = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client =  NegativeTargetSpV3Client.getInstance(true);
        }

        //获取活动的基本信息
        int maxResults = 500;
        ListSpNegativeTargetV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = "";
        while (true) {
            response = client.getNegativeTargetList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                    shop.getMarketplaceId(), targetIdList, campaignIds, stateList, null, groupIdList, maxResults, nextToken, true);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP neTargetings rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.getNegativeTargetList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                        shop.getMarketplaceId(), targetIdList, campaignIds, stateList, null, groupIdList, maxResults, nextToken, true);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sp syncNeTargetings error");
            }

            if (response == null || response.getResult() == null || response.getResult().getNegativeTargetingClauses().isEmpty()) {
                break;
            }

            int size = response.getResult().getNegativeTargetingClauses().size();
            AmazonAdTargeting amazonAdTargeting;
            List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>(size);
            for (ListNegativeTargetSuccessResultV3 targetingClause : response.getResult().getNegativeTargetingClauses()) {
                amazonAdTargeting = negativeTargetV3TurnToPO(targetingClause);
                if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                    amazonAdTargeting.setPuid(shop.getPuid());
                    amazonAdTargeting.setShopId(shop.getId());
                    amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdTargeting.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdTargeting.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);

                    // 确定投放的类型
                    // 之前是通过expression获取投放名称，改成resolvedExpression
                    if (CollectionUtils.isNotEmpty(targetingClause.getResolvedExpression())) {
                        for (com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression expression : targetingClause.getResolvedExpression()) {
                            amazonAdTargeting.setTargetingValue(expression.getValue());
                            break;
                        }
                    }

                    amazonAdTargetings.add(amazonAdTargeting);
                }
            }
            try {
                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(shop.getPuid(), amazonAdTargetings, Constants.TARGETING_TYPE_NEGATIVEASIN);
            } catch (Exception e) {
                log.error("error:", e);
            }

            if (StringUtils.isNotBlank(response.getResult().getNextToken())) {
                nextToken = response.getResult().getNextToken();
                continue;
            } else {
                break;
            }
        }
    }

    /**
     * 修改
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<List<AmazonAdTargeting>> updateStateAndBid(List<AmazonAdTargeting> amazonAdTargetings) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<PutTargetEntityV3> targetingList = makePutTargetingClausesBidAndState(amazonAdTargetings);
        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<ErrorItemResultV3> errorList = response.getData().getTargetingClauses().getError();
            List<TargetSuccessResultV3> successList = response.getData().getTargetingClauses().getSuccess();

            List<AmazonAdTargeting> succList = new ArrayList<>(errorList.size() + successList.size());
            for (TargetSuccessResultV3 TargetSuccessResultV3 : successList) {
                succList.add(amazonAdTargetings.get(TargetSuccessResultV3.getIndex()));
            }
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdTargetings.get(errorItemResultV3.getIndex()).getTargetingValue())
                        .append(",desc:").append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");

            }

            if (succList.size() > 0) {
                Result<List<AmazonAdTargeting>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.returnErr(msg);
    }

    /**
     * 修改状态
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<List<AmazonAdTargeting>> updateTargetState(List<AmazonAdTargeting> amazonAdTargetings) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<PutTargetEntityV3> targetingList = makePutTargetingClausesState(amazonAdTargetings);


        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<ErrorItemResultV3> errorList = response.getData().getTargetingClauses().getError();
            List<TargetSuccessResultV3> successList = response.getData().getTargetingClauses().getSuccess();

            List<AmazonAdTargeting> succList = new ArrayList<>(errorList.size() + successList.size());
            for (TargetSuccessResultV3 TargetSuccessResultV3 : successList) {
                succList.add(amazonAdTargetings.get(TargetSuccessResultV3.getIndex()));
            }
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdTargetings.get(errorItemResultV3.getIndex()).getTargetingValue())
                        .append(",desc:").append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");

            }

            if (succList.size() > 0) {
                Result<List<AmazonAdTargeting>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.returnErr(msg);
    }
/**
     * 修改竞价
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<List<AmazonAdTargeting>> updateTargetBid(List<AmazonAdTargeting> amazonAdTargetings) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<PutTargetEntityV3> targetingList = makePutTargetingClausesBid(amazonAdTargetings);


        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<ErrorItemResultV3> errorList = response.getData().getTargetingClauses().getError();
            List<TargetSuccessResultV3> successList = response.getData().getTargetingClauses().getSuccess();

            List<AmazonAdTargeting> succList = new ArrayList<>(errorList.size() + successList.size());
            for (TargetSuccessResultV3 TargetSuccessResultV3 : successList) {
                succList.add(amazonAdTargetings.get(TargetSuccessResultV3.getIndex()));
            }
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdTargetings.get(errorItemResultV3.getIndex()).getTargetingValue())
                        .append(",desc:").append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");

            }

            if (succList.size() > 0) {
                Result<List<AmazonAdTargeting>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.returnErr(msg);
    }

    public Result archive(List<AmazonAdTargeting> amazonAdTargetings) {
        AmazonAdTargeting amazonAdTargeting = amazonAdTargetings.get(0);
        if (amazonAdTargeting == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdTargeting.getShopId(), amazonAdTargeting.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delTargets(shopAuthService.getAdToken(shop),
                amazonAdTargeting.getProfileId(), shop.getMarketplaceId(),
                amazonAdTargetings.stream().map(AmazonAdTargeting::getTargetId)
                        .collect(Collectors.toList()), false);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delTargets(shopAuthService.getAdToken(shop),
                    amazonAdTargeting.getProfileId(), shop.getMarketplaceId(),
                    amazonAdTargetings.stream().map(AmazonAdTargeting::getTargetId)
                            .collect(Collectors.toList()), false);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getTargetingClauses().getSuccess())) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);
    }


    /**
     * 修改
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<List<AmazonAdTargeting>> updateNeTargets(ArrayList<AmazonAdTargeting> amazonAdTargetings) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<String> states = amazonAdTargetings.stream().map(AmazonAdTargeting::getState).distinct().collect(Collectors.toList());
        if(states.size() >1){
            return ResultUtil.returnErr("调整状态错误");
        }
        List<String> targetId = amazonAdTargetings.stream().map(AmazonAdTargeting::getTargetId).collect(Collectors.toList());
        List<PutNegativeTargetEntityV3> targetingList = makePutNeTargetingClauses(amazonAdTargetings);

        boolean isArchived = SpV3StateEnum.ARCHIVED.value().equalsIgnoreCase(states.get(0));
        UpdateSpNegativeTargetV3Response response = null;
        if(isArchived){
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).deleteNegativeTarget(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(),targetId ,true);
        } else {
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putNegativeTarget(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(),targetingList ,true);
        }
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            if(isArchived){
                response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).deleteNegativeTarget(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(),targetId ,true);
            } else {
                response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putNegativeTarget(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(),targetingList ,true);
            }
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null && response.getData().getNegativeTargetingClauses() != null) {
            StringBuilder error = new StringBuilder();
            List<NegativeTargetSuccessResultV3> success = response.getData().getNegativeTargetingClauses().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getNegativeTargetingClauses().getError();
            int index = 0;
            List<AmazonAdTargeting> succList = new ArrayList<>(targetingList.size());
            for (NegativeTargetSuccessResultV3 targetingClauseResult : success) {
                succList.add(amazonAdTargetings.get(targetingClauseResult.getIndex()));
            }
            for (ErrorItemResultV3 targetingClauseResult : errorItemResultV3s) {
                error.append("targetValue:").append(amazonAdTargetings.get(targetingClauseResult.getIndex()).getTargetingValue()).append(",desc:")
                        .append(AmazonErrorUtils.getError(targetingClauseResult.getErrors().get(0).getErrorMessage())).append(";");
            }

            if (succList.size() > 0) {
                Result<List<AmazonAdTargeting>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null ) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }
        return ResultUtil.returnErr(msg);
    }

    public Result archiveNeTargeting(AmazonAdTargeting amazonAdTargeting) {
        if (amazonAdTargeting == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdTargeting.getShopId(), amazonAdTargeting.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpNegativeTargetV3Response response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).deleteNegativeTarget(shopAuthService.getAdToken(shop),
                amazonAdTargeting.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdTargeting.getTargetId()),true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).deleteNegativeTarget(shopAuthService.getAdToken(shop),
                    amazonAdTargeting.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdTargeting.getTargetId()),true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && response.getData().getNegativeTargetingClauses() != null && CollectionUtils.isNotEmpty(response.getData().getNegativeTargetingClauses().getSuccess())) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if(response.getData() != null && response.getData().getNegativeTargetingClauses() != null && CollectionUtils.isNotEmpty(response.getData().getNegativeTargetingClauses().getError())){
            msg = AmazonErrorUtils.getError(response.getData().getNegativeTargetingClauses().getError().get(0).getErrors().get(0).getErrorMessage());
        }

        if (response.getError() != null) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors()) && StringUtils.isNotBlank(response.getError().getErrors().get(0).getErrorMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }
        return ResultUtil.error(msg);
    }

    /**
     * 获取建议分类
     *
     * @param shopId：
     * @param asinList：
     */
    public Result<List<CategoryRecommendations>> getCategoriesByAsin(Integer shopId, String profileId, List<String> asinList) {
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        boolean includeAncestor = false;

        GetTargetingCategoriesRecommendationsResponse response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getTargetingCategoriesRecommendationsResponse(shopAuthService.getAdToken(shop),
                profileId, shop.getMarketplaceId(), asinList, includeAncestor);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getTargetingCategoriesRecommendationsResponse(shopAuthService.getAdToken(shop),
                    profileId, shop.getMarketplaceId(), asinList, includeAncestor);
        }


        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        if (response.getCategories() != null) {
            return ResultUtil.returnSucc(response.getCategories());
        }

        //处理返回结果中的错误信息
        String msg = null;
        if (response.getDetails() != null) {
            msg = response.getDetails();
        } else if (response.getDescription() != null) {
            msg = response.getDescription();
        } else if (response.getStatusMessage() != null) {
            msg = response.getStatusMessage();
        }
        if (StringUtils.isBlank(msg)) {
            msg = "网络延迟，请稍后重试";
        }

        return ResultUtil.returnErr(msg);
    }

    public Result<List<Brand>> getBandsByCategory(ShopAuth shop, String categoryId) {
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        GetRefinementsResponse response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getRefinementsNewByCategoryId(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), Long.valueOf(categoryId));
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getRefinementsNewByCategoryId(shopAuthService.getAdToken(shop),
                    amazonAdProfile.getProfileId(), shop.getMarketplaceId(), Long.valueOf(categoryId));
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getRefinement() != null) {
            if (response.getRefinement().getBrands() != null) {
                return ResultUtil.returnSucc(response.getRefinement().getBrands());
            }
        }

        //处理返回结果中的错误信息
        String msg = null;
        if (response.getError() != null) {
            msg = response.getError().getDetails();
        }
        if (StringUtils.isBlank(msg)) {
            msg = "网络延迟，请稍后重试";
        }

        return ResultUtil.returnErr(msg);
    }

    private List<TargetingClause> makeTargetingClauses(List<AmazonAdTargeting> list) {
        List<TargetingClause> targetingList = Lists.newArrayListWithCapacity(list.size());
        TargetingClause targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(Long.valueOf(amazonAdTargeting.getTargetId()));
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getCampaignId())) {
                targetingClause.setCampaignId(Long.valueOf(amazonAdTargeting.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(amazonAdTargeting.getAdGroupId()));
            }
            targetingClause.setExpressionType(amazonAdTargeting.getExpressionType());
            targetingClause.setState(amazonAdTargeting.getState());
            targetingClause.setBid(amazonAdTargeting.getBid());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                targetingClause.setExpressions(expressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }


    private List<PutNegativeTargetEntityV3> makePutNeTargetingClauses(List<AmazonAdTargeting> list) {
        List<PutNegativeTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutNegativeTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new PutNegativeTargetEntityV3();

            targetingClause.setTargetId(amazonAdTargeting.getTargetId());

            targetingClause.setState(amazonAdTargeting.getState());

            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private List<PutTargetEntityV3> makePutTargetingClausesState(List<AmazonAdTargeting> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getState())) {
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdTargeting.getState()).getValue());
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }


    private List<PutTargetEntityV3> makePutTargetingClausesBidAndState(List<AmazonAdTargeting> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getState())) {
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdTargeting.getState()).getValue());
            }
            if (amazonAdTargeting.getBid() != null) {
                targetingClause.setBid(amazonAdTargeting.getBid());
            }

            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private List<PutTargetEntityV3> makePutTargetingClausesBid(List<AmazonAdTargeting> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }
            if (amazonAdTargeting.getBid() != null) {
                targetingClause.setBid(amazonAdTargeting.getBid());
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private AmazonAdTargeting turnToPO(TargetExtendEntityV3 targetingClause) {
        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
        if (targetingClause.getCampaignId() != null) {
            amazonAdTargeting.setCampaignId(targetingClause.getCampaignId());
        }
        if (targetingClause.getAdGroupId() != null) {
            amazonAdTargeting.setAdGroupId(targetingClause.getAdGroupId());
        }
        if (targetingClause.getTargetId() != null) {
            amazonAdTargeting.setTargetId(targetingClause.getTargetId());
        }
        amazonAdTargeting.setExpressionType(targetingClause.getExpressionType());
        amazonAdTargeting.setState(targetingClause.getState().toLowerCase());
        amazonAdTargeting.setServingStatus(targetingClause.getExtendedData().getServingStatus());
        amazonAdTargeting.setBid(targetingClause.getBid());
        amazonAdTargeting.setExpression(JSONUtil.objectToJson(targetingClause.getExpression()));
        amazonAdTargeting.setResolvedExpression(JSONUtil.objectToJson(targetingClause.getResolvedExpression()));
        return amazonAdTargeting;
    }

    /**
     * 修改
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<BatchResponseVo<UpdateBatchTargetVo,AmazonAdTargeting>> update(List<AmazonAdTargeting> amazonAdTargetings , String type) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        UpdateSpTargetV3Response response;

        //新版v3接口,归档操作有指定接口,不走状态字段
        if (StateEnum.archived.name().equalsIgnoreCase(amazonAdTargetings.get(0).getState())) {
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), amazonAdTargetings.stream()
                            .map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()), true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delTargets(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), amazonAdTargetings.stream()
                                .map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()), true);
            }
        } else {
            //其它修改操作
            List<PutTargetEntityV3> targetingList = makeTargetingsV3(amazonAdTargetings,type);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), targetingList, true);
            }
        }

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        Map<String, AmazonAdTargeting> amazonAdTargetingMap = amazonAdTargetings.stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, e -> e));
        BatchResponseVo<UpdateBatchTargetVo,AmazonAdTargeting> batchResponseVo = new BatchResponseVo<>();
        List<UpdateBatchTargetVo> errorList = Lists.newArrayList();
        List<AmazonAdTargeting> successList = Lists.newArrayList();

        if (response.getData() != null) {
            List<ErrorItemResultV3> errorResults= response.getData().getTargetingClauses().getError();
            List<TargetSuccessResultV3> successResults = response.getData().getTargetingClauses().getSuccess();

            for (TargetSuccessResultV3 TargetSuccessResultV3 : successResults) {
                AmazonAdTargeting amazonAdTargetingSuccess = amazonAdTargetingMap.remove(TargetSuccessResultV3.getTargetId());
                if (amazonAdTargetingSuccess != null) {
                    successList.add(amazonAdTargetingSuccess);
                }
            }
            for (ErrorItemResultV3 errorItemResultV3 : errorResults) {
                //通过index查询targetId
                String targetId = amazonAdTargetings.get(errorItemResultV3.getIndex()).getTargetId();

                AmazonAdTargeting amazonAdTargetingFail = amazonAdTargetingMap.remove(targetId);
                if (amazonAdTargetingFail != null) {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(amazonAdTargetingFail.getId());
                    spKeywordsVoError.setTargetId(amazonAdTargetingFail.getTargetId());
                    //更新失败数据处理
                    if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors())) {
                        spKeywordsVoError.setFailReason(String.valueOf(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    } else {
                        spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(spKeywordsVoError);
                }
            }

            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    spKeywordsVoError.setTargetId(v.getTargetId());
                    errorList.add(spKeywordsVoError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getMessage())) {
            //授权失败
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                String message = response.getError().getMessage();
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason(message);
                    spKeywordsVoError.setTargetId(v.getTargetId());
                    errorList.add(spKeywordsVoError);
                });
            }
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    spKeywordsVoError.setTargetId(v.getTargetId());
                    errorList.add(spKeywordsVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdTargetings.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    private List<TargetingClause> makeTargetings(List<AmazonAdTargeting> amazonAdTargetingList, String type) {
        List<TargetingClause> list = Lists.newArrayListWithCapacity(amazonAdTargetingList.size());

        for (AmazonAdTargeting amazonAdTargeting : amazonAdTargetingList) {
            TargetingClause targetingClause = new TargetingClause();

            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(Long.valueOf(amazonAdTargeting.getTargetId()));
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getCampaignId())) {
                targetingClause.setCampaignId(Long.valueOf(amazonAdTargeting.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(amazonAdTargeting.getAdGroupId()));
            }

            if(Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)){
                targetingClause.setBid(amazonAdTargeting.getBid());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                targetingClause.setState(amazonAdTargeting.getState());
            }
            list.add(targetingClause);
        }
        return list;
    }


    private List<PutTargetEntityV3> makeTargetingsV3(List<AmazonAdTargeting> amazonAdTargetingList, String type) {
        List<PutTargetEntityV3> list = Lists.newArrayListWithCapacity(amazonAdTargetingList.size());

        for (AmazonAdTargeting amazonAdTargeting : amazonAdTargetingList) {
            PutTargetEntityV3 targetingClause = new PutTargetEntityV3();

            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }
            if(Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)){
                targetingClause.setBid(amazonAdTargeting.getBid());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdTargeting.getState()).getValue());
            }
            list.add(targetingClause);
        }
        return list;
    }

    public Result<List<Brand>> getBandsByKeyword(ShopAuth shop, String keyword) {
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        GetBrandRecommendationsNewResponse response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBrandRecommendationsNewByKeyword(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keyword);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBrandRecommendationsNewByKeyword(shopAuthService.getAdToken(shop),
                    amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keyword);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getBrands() != null) {
            if (response.getBrands() != null) {
                return ResultUtil.returnSucc(response.getBrands());
            }
        }

        //处理返回结果中的错误信息
        String msg = null;
        if (response.getError() != null) {
            msg = response.getError().getDetails();
        }
        if (StringUtils.isBlank(msg)) {
            msg = "网络延迟，请稍后重试";
        }

        return ResultUtil.returnErr(msg);
    }

    /**
     * 创建定位
     *
     * @param targetings：
     * @return ：Result
     */
    Result createTargetV3(List<AmazonAdTargeting> targetings) {
        if (CollectionUtils.isEmpty(targetings)) {
            return ResultUtil.error("请求参数错误");
        }

        AmazonAdTargeting one = targetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<CreateTargetEntityV3> targetingList = makeCreateTargetingClausesV3(targetings);
        CreateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            TargetApiResponseV3 data = response.getData();
            if(CollectionUtils.isNotEmpty(data.getTargetingClauses().getSuccess())){
                for (TargetSuccessResultV3 successResultV3 : data.getTargetingClauses().getSuccess()){
                    AmazonAdTargeting amazonAdKeyword = targetings.get(successResultV3.getIndex());
                    amazonAdKeyword.setTargetId(successResultV3.getTargetId());
                    Optional.of(successResultV3).map(TargetSuccessResultV3::getTargetingClause)
                            .map(TargetExtendEntityV3::getResolvedExpression)
                            .map(expressionList -> expressionList.parallelStream().filter(e -> TargetingExpressionPredicate.ASIN_CATEGORY_SAME_AS.getValue().equals(e.getType())).findFirst())
                            .filter(Optional::isPresent)
                            .map(Optional::get)
                            .map(TargetResolveExpression::getValue)
                            .filter(StringUtils::isNotEmpty)
                            .ifPresent(amazonAdKeyword::setTargetingValue);
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getTargetingClauses().getError())){
                for (ErrorItemResultV3 errorItemResultV3 : data.getTargetingClauses().getError()){
                    error.append("targetValue:").append(targetings.get(errorItemResultV3.getIndex()).getTargetingValue()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
                    AmazonAdTargeting amazonAdKeyword = targetings.get(errorItemResultV3.getIndex());
                    amazonAdKeyword.setError(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);

    }

    public List<CreateTargetEntityV3> makeCreateTargetingClausesV3(List<AmazonAdTargeting> list) {
        List<CreateTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        CreateTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new CreateTargetEntityV3();
            targetingClause.setCampaignId(amazonAdTargeting.getCampaignId());
            targetingClause.setAdGroupId(amazonAdTargeting.getAdGroupId());
            targetingClause.setExpressionType(SpV3TargetingTypeEnum.getSpV3TargetingTypeEnumByValue(amazonAdTargeting.getExpressionType()).valueV3());
            targetingClause.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()).valueV3());
            targetingClause.setBid(amazonAdTargeting.getBid());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                List<TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions){
                    TargetExpression e = new TargetExpression();
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }


    /**
     * 创建定位
     *
     * @param targetings：
     * @return ：Result
     */
    Result createNeTargetV3(List<SpNeTargetingVo> targetings) {
        Result result = ResultUtil.success();
        if (CollectionUtils.isEmpty(targetings)) {
            return ResultUtil.error("请求参数错误");
        }

        SpNeTargetingVo one = targetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        List<CreateNegativeTargetEntityV3> targetingList = makeCreateNegativeTargetingClausesV3(targetings);
        CreateSpNegativeTargetV3Response response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response =  NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            NegativeTargetApiResponseV3 data = response.getData();
            if(CollectionUtils.isNotEmpty(data.getNegativeTargetingClauses().getSuccess())){
                for (NegativeTargetSuccessResultV3 successResultV3:data.getNegativeTargetingClauses().getSuccess()){
                    SpNeTargetingVo amazonAdKeyword = targetings.get(successResultV3.getIndex());
                    amazonAdKeyword.setTargetId(successResultV3.getTargetId());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getNegativeTargetingClauses().getError())){
                for (ErrorItemResultV3 errorItemResultV3 : data.getNegativeTargetingClauses().getError()){
                    error.append("targetValue:").append(targetings.get(errorItemResultV3.getIndex()).getTargetingValue()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
                    SpNeTargetingVo amazonAdKeyword = targetings.get(errorItemResultV3.getIndex());
                    amazonAdKeyword.setError(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);
    }

    public List<CreateNegativeTargetEntityV3> makeCreateNegativeTargetingClausesV3(List<SpNeTargetingVo> list) {
        List<CreateNegativeTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        CreateNegativeTargetEntityV3 targetingClause;
        for (SpNeTargetingVo amazonAdTargeting : list) {
            targetingClause = new CreateNegativeTargetEntityV3();
            targetingClause.setCampaignId(amazonAdTargeting.getCampaignId());
            targetingClause.setAdGroupId(amazonAdTargeting.getAdGroupId());
            targetingClause.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()).valueV3());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                List<com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions){
                    com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression e = new com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression();
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private AmazonAdTargeting negativeTargetV3TurnToPO(ListNegativeTargetSuccessResultV3 targetingClause) {
        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
        if (targetingClause.getCampaignId() != null) {
            amazonAdTargeting.setCampaignId(targetingClause.getCampaignId());
        }
        if (targetingClause.getAdGroupId() != null) {
            amazonAdTargeting.setAdGroupId(targetingClause.getAdGroupId());
        }
        if (targetingClause.getTargetId() != null) {
            amazonAdTargeting.setTargetId(targetingClause.getTargetId());
        }
        // 同步大写转小写
        if (targetingClause.getExpression() != null) {
            com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression expression = targetingClause.getExpression().get(0);
            expression.setType(SpV3ExpressionEnum.getExpressionByValueV3(expression.getType()).getValue());
        }
        if (targetingClause.getResolvedExpression() != null) {
            com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression expression = targetingClause.getResolvedExpression().get(0);
            expression.setType(SpV3ExpressionEnum.getExpressionByValueV3(expression.getType()).getValue());
        }
        amazonAdTargeting.setState(SpV3StateEnum.getSpV3StateEnumByValueV3(targetingClause.getState()).value());
        amazonAdTargeting.setServingStatus(targetingClause.getExtendedData().getServingStatus());
        amazonAdTargeting.setExpression(JSONUtil.objectToJson(targetingClause.getExpression()));
        amazonAdTargeting.setResolvedExpression(JSONUtil.objectToJson(targetingClause.getResolvedExpression()));
        //平台创建时间
        if (targetingClause.getExtendedData().getCreationDateTime() != null) {
            amazonAdTargeting.setCreationDate(LocalDateTimeUtil.convertSiteTimeToChina(targetingClause.getExtendedData().getCreationDateTime()));
        }
        return amazonAdTargeting;
    }


    /**
     * 修改
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<BatchResponseVo<UpdateBatchTargetVo,AmazonAdTargeting>> batchUpdateNeTarget(List<AmazonAdTargeting> amazonAdTargetings , String type) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        UpdateSpNegativeTargetV3Response response;

        //新版v3接口,归档操作有指定接口,不走状态字段
        if (StateEnum.archived.name().equalsIgnoreCase(amazonAdTargetings.get(0).getState())) {
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).deleteNegativeTarget(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), amazonAdTargetings.stream()
                            .map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()), true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).deleteNegativeTarget(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), amazonAdTargetings.stream()
                                .map(AmazonAdTargeting::getTargetId).collect(Collectors.toList()), true);
            }
        } else {
            //其它修改操作
            List<PutNegativeTargetEntityV3> targetingList = makeNeTargetingsV3(amazonAdTargetings,type);
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putNegativeTarget(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), targetingList, true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putNegativeTarget(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), targetingList, true);
            }
        }

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        Map<String, AmazonAdTargeting> amazonAdTargetingMap = amazonAdTargetings.stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, e -> e));
        BatchResponseVo<UpdateBatchTargetVo,AmazonAdTargeting> batchResponseVo = new BatchResponseVo<>();
        List<UpdateBatchTargetVo> errorList = Lists.newArrayList();
        List<AmazonAdTargeting> successList = Lists.newArrayList();

        if (response.getData() != null) {
            List<Long> successId = Lists.newArrayList();
            List<ErrorItemResultV3> errorResults= response.getData().getNegativeTargetingClauses().getError();
            List<NegativeTargetSuccessResultV3> successResults = response.getData().getNegativeTargetingClauses().getSuccess();

            for (NegativeTargetSuccessResultV3 TargetSuccessResultV3 : successResults) {
                AmazonAdTargeting amazonAdTargetingSuccess = amazonAdTargetingMap.remove(TargetSuccessResultV3.getTargetId());
                if (amazonAdTargetingSuccess != null) {
                    successList.add(amazonAdTargetingSuccess);
                }
                successId.add(amazonAdTargetingSuccess.getId());
            }
            for (ErrorItemResultV3 errorItemResultV3 : errorResults) {
                //通过index查询targetId
                String targetId = amazonAdTargetings.get(errorItemResultV3.getIndex()).getTargetId();

                AmazonAdTargeting amazonAdTargetingFail = amazonAdTargetingMap.remove(targetId);
                if (amazonAdTargetingFail != null) {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(amazonAdTargetingFail.getId());
                    spKeywordsVoError.setTargetId(amazonAdTargetingFail.getTargetId());
                    spKeywordsVoError.setTargetingValue(amazonAdTargetingFail.getTargetingValue());
                    //更新失败数据处理
                    if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors())) {
                        spKeywordsVoError.setFailReason(String.valueOf(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    } else {
                        spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(spKeywordsVoError);
                }
            }

            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    spKeywordsVoError.setTargetId(v.getTargetId());
                    spKeywordsVoError.setTargetingValue(v.getTargetingValue());
                    errorList.add(spKeywordsVoError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getMessage())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getMessage()));
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    spKeywordsVoError.setTargetId(v.getTargetId());
                    spKeywordsVoError.setTargetingValue(v.getTargetingValue());
                    errorList.add(spKeywordsVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdTargetings.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    /**
     * 同步所有投放
     *
     * @param shop：
     */
    public List<AmazonAdTargeting> syncTargeting(ShopAuth shop, String campaignId, String groupId, String targetId) {
        List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>();
        if (shop == null) {
            return amazonAdTargetings;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return amazonAdTargetings;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return amazonAdTargetings;
        }

        List<String> campaignIdList = null;
        List<String> groupIdList = null;
        List<String> targetIdList = null;

        if(StringUtils.isNotBlank(campaignId)){
            campaignIdList = StringUtil.splitStr(campaignId,",");
        }

        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }

        if(StringUtils.isNotBlank(targetId)){
            targetIdList = StringUtil.splitStr(targetId,",");
        }

        //获取活动的基本信息
        TargetSpV3Client client = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int count = 2000;
        ListSpTargetV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listTarget(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList, groupIdList, targetIdList, null, null, null,
                    null, Boolean.TRUE, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP targetings rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listTarget(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList, groupIdList, targetIdList, null, null, null,
                        null, Boolean.TRUE, nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getTargetingClauses())) {
                break;
            }

            int size = response.getData().getTargetingClauses().size();
            AmazonAdTargeting amazonAdTargeting;
            for (TargetExtendEntityV3 targetingClause : response.getData().getTargetingClauses()) {
                amazonAdTargeting = turnToPO(targetingClause);
                if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                    amazonAdTargeting.setPuid(shop.getPuid());
                    amazonAdTargeting.setShopId(shop.getId());
                    amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdTargeting.setProfileId(amazonAdProfile.getProfileId());

                    // 确定投放的类型
                    if (CollectionUtils.isNotEmpty(targetingClause.getResolvedExpression())) {
                        for (TargetResolveExpression expression : targetingClause.getResolvedExpression()) {
                            if (SpV3ExpressionEnum.asinSameAs.getValueV3().equalsIgnoreCase(expression.getType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                                amazonAdTargeting.setTargetingValue(expression.getValue());
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                                amazonAdTargeting.setSelectType(expressionByValueV3 == null ? expression.getType() :expressionByValueV3.getValue());
                                break;
                            }
                            if (SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_CATEGORY);
                                amazonAdTargeting.setTargetingValue(expression.getValue());
                                break;
                            }
                            if (SpV3ExpressionEnum.queryHighRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                    || SpV3ExpressionEnum.queryBroadRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                    || SpV3ExpressionEnum.asinSubstituteRelated.getValueV3().equalsIgnoreCase(expression.getType())
                                    || SpV3ExpressionEnum.asinAccessoryRelated.getValueV3().equalsIgnoreCase(expression.getType())) {
                                amazonAdTargeting.setType(Constants.TARGETING_TYPE_AUTO);
                                SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                                amazonAdTargeting.setTargetingValue(expressionByValueV3 == null ? expression.getType() : expressionByValueV3.getValue());
                                break;
                            }
                        }
                    }

                    amazonAdTargetings.add(amazonAdTargeting);
                }
            }
            Map<String, List<TargetExtendEntityV3>> collect = response.getData().getTargetingClauses().stream().collect(Collectors.groupingBy(e -> "KEYWORD_GROUP_SAME_AS".equals(e.getExpression().get(0).getType()) ? "keyword" : "target"));
            saveKeyword(shop, amazonAdProfile, collect.get("keyword"));
            saveTarget(shop, amazonAdProfile, collect.get("target"));

            if (StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return amazonAdTargetings;
    }




    private List<PutNegativeTargetEntityV3> makeNeTargetingsV3(List<AmazonAdTargeting> amazonAdTargetingList, String type) {
        List<PutNegativeTargetEntityV3> list = Lists.newArrayListWithCapacity(amazonAdTargetingList.size());

        for (AmazonAdTargeting amazonAdTargeting : amazonAdTargetingList) {
            PutNegativeTargetEntityV3 targetingClause = new PutNegativeTargetEntityV3();

            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }

            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdTargeting.getState()).getValue());
            }
            list.add(targetingClause);
        }
        return list;
    }


    private void saveTarget(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<TargetExtendEntityV3> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        int size = data.size();
        AmazonAdTargeting amazonAdTargeting;
        List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>(size);
        for (TargetExtendEntityV3 targetingClause : data) {
            amazonAdTargeting = turnToPO(targetingClause);
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                amazonAdTargeting.setPuid(shop.getPuid());
                amazonAdTargeting.setShopId(shop.getId());
                amazonAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                amazonAdTargeting.setProfileId(amazonAdProfile.getProfileId());

                // 确定投放的类型
                if (CollectionUtils.isNotEmpty(targetingClause.getResolvedExpression())) {
                    for (TargetResolveExpression expression : targetingClause.getResolvedExpression()) {
                        if (SpV3ExpressionEnum.asinSameAs.getValueV3().equalsIgnoreCase(expression.getType()) || SpV3ExpressionEnum.asinExpandedFrom.getValueV3().equalsIgnoreCase(expression.getType())) {
                            amazonAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                            amazonAdTargeting.setTargetingValue(expression.getValue());
                            SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                            amazonAdTargeting.setSelectType(expressionByValueV3 == null ? expression.getType() :expressionByValueV3.getValue());
                            break;
                        }
                        if (SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equalsIgnoreCase(expression.getType())) {
                            amazonAdTargeting.setType(Constants.TARGETING_TYPE_CATEGORY);
                            amazonAdTargeting.setTargetingValue(expression.getValue());
                            break;
                        }
                        if (SpV3ExpressionEnum.queryHighRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.queryBroadRelMatches.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.asinSubstituteRelated.getValueV3().equalsIgnoreCase(expression.getType())
                                || SpV3ExpressionEnum.asinAccessoryRelated.getValueV3().equalsIgnoreCase(expression.getType())) {
                            amazonAdTargeting.setType(Constants.TARGETING_TYPE_AUTO);
                            SpV3ExpressionEnum expressionByValueV3 = SpV3ExpressionEnum.getExpressionByValueV3(expression.getType());
                            amazonAdTargeting.setTargetingValue(expressionByValueV3 == null ? expression.getType() : expressionByValueV3.getValue());
                            break;
                        }
                    }
                }

                amazonAdTargetings.add(amazonAdTargeting);
            }
        }
        amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(shop.getPuid(), amazonAdTargetings, Constants.TARGETING_TYPE_ASIN);
    }

    private void saveKeyword(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<TargetExtendEntityV3> data) {
        if (CollectionUtils.isEmpty(data)) {
            return;
        }
        int size = data.size();
        AmazonAdKeyword amazonAdKeyword;
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(size);
        for (TargetExtendEntityV3 keyword : data) {
            amazonAdKeyword = turnToKeywordGroupPO(keyword);
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                amazonAdKeyword.setPuid(shop.getPuid());
                amazonAdKeyword.setShopId(shop.getId());
                amazonAdKeyword.setMarketplaceId(shop.getMarketplaceId());
                amazonAdKeyword.setProfileId(amazonAdProfile.getProfileId());
                amazonAdKeywords.add(amazonAdKeyword);
            }
        }
        amazonAdKeywordShardingDao.insertOnDuplicateKeyUpdate(shop.getPuid(), amazonAdKeywords);

    }

    private AmazonAdKeyword turnToKeywordGroupPO(TargetExtendEntityV3 targetingClause) {
        AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
        if (targetingClause.getTargetId() != null) {
            amazonAdKeyword.setKeywordId(targetingClause.getTargetId());
        }
        if (targetingClause.getCampaignId() != null) {
            amazonAdKeyword.setCampaignId(targetingClause.getCampaignId());
        }
        if (targetingClause.getAdGroupId() != null) {
            amazonAdKeyword.setAdGroupId(targetingClause.getAdGroupId());
        }
        amazonAdKeyword.setKeywordText(SpKeywordGroupValueEnum.getKeywordText(targetingClause.getExpression().get(0).getValue()));
        amazonAdKeyword.setState(targetingClause.getState().toLowerCase());
        amazonAdKeyword.setMatchType("theme");
        amazonAdKeyword.setBid(targetingClause.getBid());
        amazonAdKeyword.setType(Constants.BIDDABLE);
        amazonAdKeyword.setServingStatus(targetingClause.getExtendedData().getServingStatus());
        return amazonAdKeyword;
    }


    /**
     * 由于sp关键词组投放接口时用的商品投放的接口，但是他是归属于关键词投放，所以独立一个接口提供给关键词调用
     * 更新关键词词组状态
     * @param amazonAdKeywords
     * @return
     */
    public Result<List<AmazonAdKeyword>> updateKeywordGroupState(List<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }



        List<PutTargetEntityV3> keywords = makePutKeywordGroupClausesState(amazonAdKeywords);
        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<TargetSuccessResultV3> successList = response.getData().getTargetingClauses().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getTargetingClauses().getError();

            List<AmazonAdKeyword> succList = new ArrayList<>(successList.size() + errorList.size());
            //处理成功结果
            for (TargetSuccessResultV3 KeywordSuccessResultV3 : successList) {
                succList.add(amazonAdKeywords.get(KeywordSuccessResultV3.getIndex()));
            }
            //处理失败结果
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:")
                        .append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");
            }

            if (succList.size() > 0) {
                Result<List<AmazonAdKeyword>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = response.getError().getMessage();
        }
        return ResultUtil.returnErr(msg);
    }
    /**
     * 由于sp关键词组投放接口时用的商品投放的接口，但是他是归属于关键词投放，所以独立一个接口提供给关键词调用
     * 更新关键词词组竞价
     * @param list
     * @return
     */
    private List<PutTargetEntityV3> makePutKeywordGroupClausesState(List<AmazonAdKeyword> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdKeyword amazonAdKeyword : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                targetingClause.setTargetId(amazonAdKeyword.getKeywordId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdKeyword.getState()).getValue());
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    public Result<List<AmazonAdKeyword>> updateKeywordGroupBid(List<AmazonAdKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        List<PutTargetEntityV3> keywords = makePutKeywordGroupBid(amazonAdKeywords);
        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<TargetSuccessResultV3> successList = response.getData().getTargetingClauses().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getTargetingClauses().getError();

            List<AmazonAdKeyword> succList = new ArrayList<>(successList.size() + errorList.size());
            //处理成功结果
            for (TargetSuccessResultV3 KeywordSuccessResultV3 : successList) {
                succList.add(amazonAdKeywords.get(KeywordSuccessResultV3.getIndex()));
            }
            //处理失败结果
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:")
                        .append(errorItemResultV3.getErrors().get(0).getErrorMessage()).append(";");
            }

            if (succList.size() > 0) {
                Result<List<AmazonAdKeyword>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = response.getError().getMessage();
        }
        return ResultUtil.returnErr(msg);
    }

    private List<PutTargetEntityV3> makePutKeywordGroupBid(List<AmazonAdKeyword> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdKeyword amazonAdKeyword : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                targetingClause.setTargetId(amazonAdKeyword.getKeywordId());
            }
            if (amazonAdKeyword.getBid() != null) {
                targetingClause.setBid(amazonAdKeyword.getBid());
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }


    public Result archiveKeywordGroup(AmazonAdKeyword amazonAdKeyword) {
        if (amazonAdKeyword == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdKeyword.getShopId(), amazonAdKeyword.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delTargets(shopAuthService.getAdToken(shop),
                amazonAdKeyword.getProfileId(), shop.getMarketplaceId(),
                Collections.singletonList(amazonAdKeyword.getKeywordId()), false);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delTargets(shopAuthService.getAdToken(shop),
                    amazonAdKeyword.getProfileId(), shop.getMarketplaceId(),
                    Collections.singletonList(amazonAdKeyword.getKeywordId()), false);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getTargetingClauses().getSuccess())) {
            return ResultUtil.success();
        } else if(response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getTargetingClauses().getError()) &&   CollectionUtils.isNotEmpty(response.getData().getTargetingClauses().getError().get(0).getErrors())) {
            String errorMessage = response.getData().getTargetingClauses().getError().get(0).getErrors().get(0).getErrorMessage();
            if(StringUtils.isNotBlank(errorMessage)){
                return ResultUtil.error(AmazonErrorUtils.getError(errorMessage));
            }
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);
    }
}
