package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/3/11 15:48
 * @describe:
 */
@Getter
public enum WalmartAdTargetTypeEnum {
    AUTO("auto", "自动投放"),
    MANUAL("manual", "手动投放")
    ;
    private String code;
    private String msg;

    WalmartAdTargetTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static WalmartAdTargetTypeEnum getWalmartAdTargetTypeEnumByCode (String code) {
        for (WalmartAdTargetTypeEnum en : WalmartAdTargetTypeEnum.values()) {
            if (en.getCode().equals(code)) {
                return en;
            }
        }
        return null;
    }
}
