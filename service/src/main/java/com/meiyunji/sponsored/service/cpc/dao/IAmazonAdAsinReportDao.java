package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinReport;
import com.meiyunji.sponsored.service.cpc.vo.AdAsinDto;
import com.meiyunji.sponsored.service.cpc.vo.AdAsinPageParam;
import com.meiyunji.sponsored.service.cpc.vo.AsinReportPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;

import java.util.List;

/**
 * <AUTHOR> on 2021/4/29
 */
public interface IAmazonAdAsinReportDao extends IBaseShardingDao<AmazonAdAsinReport> {

    /**
     * insert ... on duplicate key update
     * @param list:
     */
    void insertList(Integer puid, List<AmazonAdAsinReport> list);

    /**
     * 分页查询
     * @param puid:
     * @param param:
     * @return :
     */
    Page<AmazonAdAsinReport> pageList(Integer puid, AsinReportPageParam param);

    /**
     * 分页查询
     *
     * @param puid
     * @param search
     * @param page
     * @return
     */
    Page<AmazonAdAsinReport> getPageList(Integer puid, SearchVo search, Page page);

    /**
     * 查出没有asin图片的
     * @param puid：
     * @param shopId：
     * @param campaignId：也可以指定活动id查
     * @param offset：主键偏移量
     */
    List<AmazonAdAsinReport> listNoAsinImage(Integer puid, Integer shopId, String startDate, String campaignId, long offset, int limit);

    /**
     * 查出没有otherAsin图片的
     * @param puid：
     * @param shopId：
     * @param campaignId：也可以指定活动id查
     * @param offset：主键偏移量
     */
    List<AmazonAdAsinReport> listNoOtherAsinImage(Integer puid, Integer shopId, String startDate, String campaignId, long offset, int limit);

    /**
     * 批量回填asin图片url
     * @param needUpdateList：
     */
    void batchSetAsinImage(Integer puid, List<AmazonAdAsinReport> needUpdateList);

    /**
     * 批量回填otherAsin图片url
     * @param needUpdateList：
     */
    void batchSetOtherAsinImage(Integer puid, List<AmazonAdAsinReport> needUpdateList);

    /**
     * 查询Asin信息
     * @param puid
     * @param param
     * @return
     */
    List<String> getAsinListByDate(Integer puid, AdAsinPageParam param);


    /**
     * 建立准确页面信息对象
     * @param puid
     * @param param
     * @param page
     * @return
     */
    Page getAsinSizeData(Integer puid, AdAsinPageParam param, Page page);


    /**
     * 查询Asin分页信息
     * @param puid
     * @param param
     * @param asinList
     * @return
     */
    List<AdAsinDto> getAsinPageList(Integer puid, AdAsinPageParam param, List<String> asinList);

    /**
     * 通过Asin获取Asin报告信息
     * @param puid
     * @param param
     * @return
     */
    List<AdAsinDto> getAsinDataList(Integer puid, AdAsinPageParam param);

    /**
     * 通过Asin获取Asin报告信息
     * @param puid
     * @param param
     * @return
     */
    List<AdAsinDto> getAsinDataListByDate(Integer puid, AdAsinPageParam param);

}