package com.meiyunji.sponsored.service.config;

import com.meiyunji.sponsored.service.kafka.DorisRoutineLoadProducer;
import com.meiyunji.sponsored.service.kafka.SyncServerStatusKafkaProducer;
import com.meiyunji.sponsored.service.properties.KafkaProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.record.CompressionType;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

@Configuration
@EnableConfigurationProperties({KafkaProperties.class})
@ConditionalOnProperty(name = "spring.kafka.sync-ad-manage-task.enabled", havingValue = "true")
@Slf4j
public class SyncServerStatusKafkaConfiguration {

    @Value("${spring.kafka.sync-ad-manage-task.bootstrap-servers}")
    private String bootstrapServers;

    private ProducerFactory<String,  String> producerFactory() {
        return new DefaultKafkaProducerFactory<>(producerConfigs());
    }
    private Map<String, Object> producerConfigs() {
        Map<String, Object> props = new HashMap<>();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        props.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, CompressionType.LZ4.name);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG,33554432);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        return props;
    }

    @Bean
    public KafkaTemplate<String,  String> syncServerStatusKafkaTemplate() {
        return new KafkaTemplate<>(producerFactory());
    }


    @Bean(name = "syncServerStatusKafkaProducer")
    @ConditionalOnProperty(name = "kafka.producers.sync-ad-manage-task.enabled", havingValue = "true")
    public SyncServerStatusKafkaProducer dorisCampaignAllProducer(
            KafkaProperties kafkaProperties, KafkaTemplate<String, String> syncServerStatusKafkaTemplate) {
        KafkaProperties.ProducerProperties producerProperty = kafkaProperties.getProducers().get("sync-ad-manage-task");
        return new SyncServerStatusKafkaProducer(producerProperty.getTopic(), syncServerStatusKafkaTemplate);
    }


}
