package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.permission.annotation.AdProductPermissionFilter;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.grpc.common.GetGroupHourReportResponsePb;
import com.meiyunji.sponsored.grpc.common.GetPlacementHourReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.CampaignNeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.CampaignNeTargetingSpRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementAggregateHourParam;

import java.util.List;

/**
 * @author: wade
 * @date: 2021/9/13 20:11
 * @describe: 广告通用接口(联合查询sp sb sd) 主要包含列表页面和指标数据和chart图数据接口
 */
 public interface ICpcCommonService {


    /**
     * 所有广告活动和广告组
     * @param puid
     * @param shopId
     * @param type
     * @param campaignType
     * @param groupType
     * @param modular
     * @return
     */
     List<AllTypeCampAndGroupResponse.CampAndGroupVo> getAllTypeCampAndGroup(Integer puid, Integer shopId, String type, String campaignType, String groupType, String modular);

    List<AllTypeCampAndGroupResponse.CampAndGroupVo> getAllTypeCampAndGroupProductRight(Integer puid, Integer shopId, String type, String campaignType, String groupType, String modular);

    /**
     * 所有广告活动数据
     * @param puid
     * @param param
     * @return
     */

    AllCampaignDataResponse.CampaignHomeVo getAllCampaignData(Integer puid, CampaignPageParam param);


    AllCampaignDataResponse.CampaignHomeVo getAllWxCampaignData(Integer puid, CampaignPageParam param);

    AllCampaignAggregateDataResponse.CampaignHomeVo getAllCampaignAggregateData(Integer puid, CampaignPageParam param);

    AllCampaignAggregateDataResponse.CampaignHomeVo getAllWxCampaignAggregateData(Integer puid, CampaignPageParam param);

    /**
     * 所有广告位数据
     * @param puid
     * @param param
     * @return
     */
    AllPlacementDataResponse.AdPlacementHomeVo getAllPlacementData(Integer puid,PlacementPageParam param);

    AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllPlacementAggregateData(Integer puid,PlacementPageParam param);

    /**
     * 所有广告组合数据
     * @param puid
     * @param param
     * @return
     */
    AllPortfolioDataListResponse.PortfolioHomeVo getAllPortfolioDataList(Integer puid, PortfolioPageParam param);

    AllPortfolioAggregateDataListResponse.PortfolioHomeVo getAllPortfolioAggregateDataList(Integer puid, PortfolioPageParam param);

    /**
     * 所有已购买Asin数据
     * @param puid
     * @param param
     * @return
     */
    AllAsinDataResponse.AsinHomeVo getAllAsinData(Integer puid, AdAsinPageParam param);

    /**
     * 所有已购买Asin汇总数据
     * @param puid
     * @param param
     * @return
     */
    AllAsinAggregateDataResponse.AsinHomeVo getAllAsinAggregateData(Integer puid, AdAsinPageParam param);

    /**
     * 已购买Asin关联数据
     * @param puid
     * @param param
     * @return
     */
    AllAssociationAsinDataResponse.AsinHomeVo getAllAssociationAsinData(Integer puid, AdAsinPageParam param);


    /**
     * 获取关键词商品
     * @param puid
     * @param adGroupKeywordRankParam
     * @return
     */
    AdGroupKeywordRankResponse getAdGroupKeywordRank(Integer puid, AdGroupKeywordRankParam adGroupKeywordRankParam);

    /**
     * 所有广告组数据
     * @param puid
     * @param param
     * @return
     */
    AllGroupDataResponse.GroupHomeVo getAllGroupData(Integer puid,GroupPageParam param);

    AllGroupDataResponse.GroupHomeVo getAllWxGroupData(Integer puid, GroupPageParam param);

    AllGroupAggregateDataResponse.GroupHomeVo getAllGroupAggregateData(Integer puid, GroupPageParam param);

    AllGroupAggregateDataResponse.GroupHomeVo getAllWxGroupAggregateData(Integer puid, GroupPageParam param);

    /**
     * 所有广告产品数据
     * @param puid
     * @param param
     * @return
     */
    AllProductDataResponse.AdProductHomeVo getAllProductData(Integer puid,AdProductPageParam param);

    AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(Integer puid,AdProductPageParam param);
    /**
     * 所有搜索词(投放)数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page);

    AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto);


    /**
     * 所有搜索词(关键词)数据
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid,CpcQueryWordDto dto, Page page);

    AllSearchTermDataResponse.AllSearchTermData allSearchTermList(Integer puid,CpcQueryWordDto dto, Page page);

    List<SearchTermSourceTargetDetailResponse.SourceTargetDetail> searchTermSourceTargetDetail(Integer puid,CpcQueryWordDto dto);

    AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo allSearchTermAggregateData(Integer puid,CpcQueryWordDto dto);

    AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid,CpcQueryWordDto dto);

    /**
     * 所有否定关键词分页(活动层)数据，不包括报告数据，
     * @param puid
     * @param param
     * @return
     */
    @Deprecated
    Page<CampaignNeKeywordsPageRpcVo> neKeywordsPageList(Integer puid, CampaignNeKeywordsPageParam param);

    /**
     * 所有活动否定商品数据
     * @param param
     * @return
     */
    @Deprecated
     Page<CampaignNeTargetingSpRpcVo> neTargetingPageList(CampaignNeTargetingSpParam param);


    /**
     * 所有广告组否定关键词数据
     * @param param
     * @return
     */
    @Deprecated
     Page<NeKeywordsPageRpcVo> allNekeywordPageList(NeKeywordsPageParam param);

    /**
     * 所有否定投放数据
     * @param param
     * @return
     */
    @Deprecated
     Page<NeTargetingPageRpcVo> getAllNeTargeting(NeTargetingPageParam param);


    /**
     * 所有关键词数据
     * @param puid
     * @param param
     * @return
     */
     AllKeyWordDataResponse.AdkeywordHomeRpcVo getAllKeyWordData(Integer puid, KeywordsPageParam param);

    List<KeywordsPageVo> getExportAllKeyWordData(ShopAuth shopAuth, Integer puid, KeywordsPageParam param);

    AllKeyWordDataResponse.AdkeywordHomeRpcVo getAllWxKeyWordData(Integer puid, KeywordsPageParam param);

    AllKeyWordAggregateDataResponse.AdkeywordHomeRpcVo getAllKeyWordAggregateData(Integer puid, KeywordsPageParam param);

    AllKeyWordAggregateDataResponse.AdkeywordHomeRpcVo getAllWxKeyWordAggregateData(Integer puid, KeywordsPageParam param);

    /**
     * 所有投放数据
     * @param puid
     * @param param
     * @return
     */
    AllTargetDataResponse.AdTargetingHomeRpcVo getAllTargetData(Integer puid,TargetingPageParam param);

    List<TargetingPageVo> getExportAllTargetData(ShopAuth shopAuth, Integer puid, TargetingPageParam param);

    AllTargetDataResponse.AdTargetingHomeRpcVo getAllWxTargetData(Integer puid, TargetingPageParam param);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getAllTargetAggregateData(Integer puid, TargetingPageParam param);

    AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getAllWxTargetAggregateData(Integer puid, TargetingPageParam param);

    List<CampaignBeyondBudgetvo> getCampaignBeyondBudget(AdCampaignChangeHistoryParam param);

   List<CampaignBeyondBudgetHourVo> getCampaignBeyondBudgetHour(AdCampaignChangeHistoryParam param);

    CampaignBeyondBudgetPageResponse getCampaignBeyondBudgetPage(AdCampaignChangeHistoryParam param);

    List<String> getAsinByCampaignId(Integer puid, Integer shopId, String campaignId, String type);

    /**
     * 获取sd广告组最先创建的asin
     * @param puid
     * @param shopId
     * @param campaignId
     * @param adGroupId
     * @return
     */
    String getAsinByAdGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId);

    List<String> getKeywordByCampaignId(Integer puid, Integer shopId, String campaignId, String type);

    Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroupPage(CampaignAndGroupPageListParam param);

    Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroupPageProductRight(CampaignAndGroupPageListParam param);

    Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPage(CampaignAndGroupPageListParam param);

    Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageProductRight(CampaignAndGroupPageListParam param);

    Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageNew(CampaignAndGroupPageListParam param);

    Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageNewProductRight(CampaignAndGroupPageListParam param);

    Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPage(CampaignAndGroupPageParam param);

    Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPageProductRight(CampaignAndGroupPageParam param);

    OperationLogPageResponse.Page operationLogPageList(OperationLogQo qo);

    RowData operationLogDayList(OperationLogQo qo);

    AllCampaignOrGroupAggregateDataAndOperationResponse.CampaignOrGroup getAllGroupOrCampaignAggregateData(Integer puid, CampaignOrGroupParam param);


    GetCampaignHourReportResponse.CampaignHour getCampaignHourReport(Integer puid, CampaignHourParam param, ReportDateModelPb.ReportDateModel dateModel);

    GetCampaignHourReportResponse.CampaignHour getAggregateCampaignHourReport(Integer puid, List<String> temporaryIds, CampaignAggregateHourParamVO param);

    GetCampaignHourReportResponse.CampaignHour getAggregateDailyWeeklyAndMonthly(Integer puid, List<String> temporaryIds,
                                                                                 CampaignAggregateHourParamVO param, ReportDateModelPb.ReportDateModel dateModel);

    GetCampaignBudgetHourReportResponse.CampaignHour getCampaignBudgetHourReport(Integer puid, CampaignHourParam param, ReportDateModelPb.ReportDateModel dateModel);

    AllAdsDataResponse.AdCreateHomeVo getAllAdsData(Integer puid, AdAdsPageParam param);

    AllAdsAggregateDataResponse.AdCreateHomeVo getAllAdsAggregateData(Integer puid, AdAdsPageParam param);

    Result updateKeywordRank(Integer puid, Integer shopId, String keywordId, String advRank, Integer uid);

    Result<List<AmazonAdPortfolio>> queryPortfolio(Integer puid, Integer shopId,String marketplaceId,String portfolioName,Integer pageSize,Integer pageNo);

    Result<Page<AmazonAdCampaignAll>> queryCampaign(Integer puid, Integer shopId, List<String> types, List<String> portfolioIds,Integer pageSize,Integer pageNo,String campaignName,String queryType,String targetingType,String campaignId, List<String> statusList);

    Result<Page<AmazonAdGroup>> queryGroup(Integer puid, Integer shopId, List<String> types, List<String> campaignIds, Integer pageSize, Integer pageNo, String groupName,String queryType,List<String> adGroupTypes, List<String> statusList);

    Result<List<String>> queryGroupType(Integer puid, Integer shopId, String campaignId);
    Result<CampaignAndGroupTargetTypeVo> queryCampaignAndGroupType(Integer puid, Integer shopId, String campaignId, String groupId);

    GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementHourReport(Integer puid, PlacementHourParam param, ReportDateModelPb.ReportDateModel dateModel);

    GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementAggregateHourReport(Integer puid, PlacementAggregateHourVo param);
    GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementDailyWeeklyAndMonthly(Integer puid, PlacementAggregateHourVo param, ReportDateModelPb.ReportDateModel dateModel);

    GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour getGroupAggregateHourReport(Integer puid, GroupAggregateHourVo param);
    GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour getGroupDailyWeeklyAndMonthly(Integer puid, GroupAggregateHourVo param, ReportDateModelPb.ReportDateModel dateModel);

    GetCampaignHourReportResponse.CampaignHour getAggregateCampaignHourReport(Integer puid, CampaignAggregateHourParam param);
    GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementAggregateHourReport(Integer puid, PlacementAggregateHourParam param);

    GetCampaignHourReportResponse.CampaignHour getAggregateDailyWeeklyAndMonthlyMultiShop(int puid, List<String> relationIds, CampaignAggregateHourMultiShopParamVO param, ReportDateModelPb.ReportDateModel dateModel);

    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression = "#param.adType",
            strategy = PermissionFilterStrategy.FILTER
    )
    Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPageDoris(CampaignAndGroupPageParam param);
}
