package com.meiyunji.sponsored.service.cpc.service.impl;

import com.amazon.advertising.keywords.biddable.*;
import com.amazon.advertising.keywords.negative.ListNegativeKeywordsResponse;
import com.amazon.advertising.keywords.negative.NegativeKeywordsClient;
import com.amazon.advertising.keywords.negative.UpdateNegativeKeywordsResponse;
import com.amazon.advertising.keywords.suggested.BulkGetAsinSuggestedKeywordsResponse;
import com.amazon.advertising.keywords.suggested.CreateKeywordBidRecommendationsResponse;
import com.amazon.advertising.keywords.suggested.SuggestedKeywordsClient;
import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.Bidding;
import com.amazon.advertising.mode.Profile;
import com.amazon.advertising.mode.adGroups.AdGroup;
import com.amazon.advertising.mode.campaigns.Campaign;
import com.amazon.advertising.mode.keywords.BidRecommendationSimple;
import com.amazon.advertising.mode.keywords.Keyword;
import com.amazon.advertising.mode.keywords.KeywordResult;
import com.amazon.advertising.mode.productAd.ProductAd;
import com.amazon.advertising.mode.targeting.*;
import com.amazon.advertising.profiles.GetProfileListResponse;
import com.amazon.advertising.profiles.ProfileClient;
import com.amazon.advertising.spV3.keyword.KeywordSpV3Client;
import com.amazon.advertising.spV3.keyword.UpdateSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.keyword.entity.PutKeywordEntityV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazon.advertising.spV3.targeting.UpdateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.entity.PutTargetEntityV3;
import com.amazon.advertising.spV3.enumeration.*;
import com.amazon.advertising.spV3.keyword.CreateSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.entity.CreateKeywordEntityV3;
import com.amazon.advertising.spV3.negativekeyword.CreateSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.NegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.negativekeyword.entity.CreateNegativeKeywordEntityV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordSuccessResultV3;
import com.amazon.advertising.spV3.negativetargeting.CreateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.NegativeTargetSpV3Client;
import com.amazon.advertising.spV3.negativetargeting.entity.CreateNegativeTargetEntityV3;
import com.amazon.advertising.spV3.negativetargeting.entity.NegativeTargetSuccessResultV3;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.amazon.advertising.spV3.targeting.CreateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.entity.CreateTargetEntityV3;
import com.amazon.advertising.spV3.targeting.entity.TargetExpression;
import com.amazon.advertising.spV3.targeting.entity.TargetSuccessResultV3;
import com.amazon.advertising.targeting.*;
import com.amazon.advertising.targeting.negative.ListNegativeTargetingClauseResponse;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.ICpcSyncService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.AdStateV3;
import com.meiyunji.sponsored.service.enums.TargetingExpressionPredicate;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.util.Constant;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName CpcServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/4/23 17:12
 **/
@Service
public class CpcSyncServiceImpl implements ICpcSyncService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    @Override
    public Result syncProfile(int puid, Integer shopId) {
        //根据shopid获取到token信息
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        //检查授权
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            return ResultUtil.error("没有CPC授权");
        }
        ProfileClient client = ProfileClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        GetProfileListResponse response;
        int retry0 = 0;
        String token;
        do {
            token = shopAuthService.getAdToken(shop);
            response = client.getProfileListResponse(token, shop.getMarketplaceId());
            if (response.getError() != null && Constants.UNAUTHORIZED.equals(response.getError().getCode())) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        //将获取的配置信息保存
        Result result = ResultUtil.error();
        if (response.getProfiles() != null && response.getProfiles().size() > 0) {
            //校验广告授权店铺是否和店铺一致
            List<Profile> profiles = response.getProfiles().stream()
                    .filter(profile -> shop.getSellingPartnerId().equals(profile.getAccountInfo().getId())
                            && shop.getMarketplaceId().equals(profile.getAccountInfo().getMarketplaceStringId())).collect(Collectors.toList());
            if (!profiles.isEmpty()) {
                insertOrUpdateProfiles(puid, shopId, profiles);
                return ResultUtil.success();
            } else {
                result.setMsg("请确认基础授权店铺和广告授权店铺是同一个店铺");
            }
        } else {
            result.setMsg("获取CPC配置信息失败");
        }
        return result;
    }

    /**
     * 将配置信息保存
     *
     * @param puid
     * @param shopId
     * @param profiles
     */
    private void insertOrUpdateProfiles(Integer puid, Integer shopId, List<Profile> profiles) {
        AmazonAdProfile amazonAdProfile;
        for (Profile profile : profiles) {
            amazonAdProfile = new AmazonAdProfile();
            amazonAdProfile.setPuid(puid);
            amazonAdProfile.setShopId(shopId);
            amazonAdProfile.setProfileId(String.valueOf(profile.getProfileId()));
            if (profile.getAccountInfo() == null) {
                continue;
            }
            amazonAdProfile.setAccountId(profile.getAccountInfo().getId());
            amazonAdProfile.setMarketplaceId(profile.getAccountInfo().getMarketplaceStringId());
            amazonAdProfile.setType(profile.getAccountInfo().getType());
            amazonAdProfile.setCountryCode(profile.getCountryCode());
            amazonAdProfile.setCurrencyCode(profile.getCurrencyCode());
            amazonAdProfile.setDailyBudget(profile.getDailyBudget());
            amazonAdProfile.setTimezone(profile.getTimeZone());
            Date date = new Date();
            amazonAdProfile.setCreateTime(date);
            amazonAdProfile.setUpdateTime(date);
            insertOrUpdateProfile(amazonAdProfile);
        }
    }

    private void insertOrUpdateProfile(AmazonAdProfile amazonAdProfile) {
        if (amazonAdProfileDao.exist(amazonAdProfile.getPuid(), "profile_id", amazonAdProfile.getProfileId())) {
            amazonAdProfileDao.updateByIdAndPuid(amazonAdProfile);
        } else {
            try {
                amazonAdProfileDao.save(amazonAdProfile);
            } catch (Exception e) {
                logger.error(e.getMessage());
            }
        }
    }


    @Override
    public Result getSuggestKeywordByAsin(Integer shopId, String profileId, String marketPlaceId, List<String> asinList) {
        Result result = ResultUtil.error("网络延迟，请稍后重试");
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        //获取推荐关键词
        SuggestedKeywordsClient client = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        BulkGetAsinSuggestedKeywordsResponse response;
        //2.获得token
        String token;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.bulkGetAsinSuggestedKeywordsResponse(token, profileId, marketPlaceId, Constants.MAX_SIZE, asinList);
            if (response.getError() != null && Constants.UNAUTHORIZED.equals(response.getError().getCode())) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        //处理返回结果中的错误信息
        if (response.getResultList() != null) {
            result = ResultUtil.success(response.getResultList());
        } else if (response.getError() != null) {
            result = ResultUtil.error(response.getError().getDetails());
        }
        return result;
    }
    
    @Override
    public Result getBandsByCategory(Integer shopId, String profileId, String marketPlaceId, String categoryId) {
        Result result = ResultUtil.error("网络延迟，请稍后重试");
        //根据shopid获取到token信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        String token;
        //        //获取推荐关键词
        ProductTargetingClient client = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        GetRefinementsResponse response;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.getRefinementsNewByCategoryId(token, profileId, marketPlaceId, Long.valueOf(categoryId));
            if (response.getError() != null && Constants.UNAUTHORIZED.equals(response.getError().getCode())) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        //处理返回结果中的错误信息
        if (response.getRefinement() != null) {
            if (response.getRefinement().getBrands() != null) {
                result = ResultUtil.success(response.getRefinement().getBrands());
            }
        } else if (response.getError() != null) {
            result = ResultUtil.error(response.getError().getDetails());
        }
        return result;
    }



    @Override
    public Result updateBiddableKeyword(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> list) {
        Result result = ResultUtil.success();
        //根据shopid获取到token信息
        //1.获取店铺信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        //2.获得token
        String token;
        //创建竞价关键词
        List<PutKeywordEntityV3> keywordList = getPutKeywordsByPo(list);
        UpdateSpKeywordV3Response response;

        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putKeywords(token, profileId, marketPlaceId, keywordList, true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        //处理返回结果中的错误信息
        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<KeywordSuccessResultV3> success = response.getData().getKeywords().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getKeywords().getError();
            List<KeywordResult> resultList = new ArrayList<>();

            for (KeywordSuccessResultV3 KeywordSuccessResultV3 : success) {
                KeywordResult keywordResult = new KeywordResult();
                keywordResult.setCode("SUCCESS");
                keywordResult.setKeywordId(Long.valueOf(KeywordSuccessResultV3.getKeywordId()));
            }

            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("keywordText:").append(list.get(errorItemResultV3.getIndex()).getKeywordText())
                        .append(",desc:").append(errorItemResultV3.getErrors().get(0)
                        .getErrorValue().get("message")).append(";");
            }

            if (error.length() > 0) {
                result = ResultUtil.error(error.toString());
            }
            result.setData(resultList);

        } else if (response.getError() != null) {
            result = ResultUtil.error(response.getError().getMessage());
        }
        return result;
    }








    @Override
    public Result updateTarget(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdTargeting> list) {
        Result result = ResultUtil.success();
        //根据shopid获取到token信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        //2.获得token
        String token;
        //创建竞价关键词
        List<PutTargetEntityV3> targetingList = getTargetingClauseByPoV3(list);
        UpdateSpTargetV3Response response;

        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
                    .putTargets(token, profileId, marketPlaceId, targetingList, true);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        //处理返回结果中的错误信息
        if (response.getData() != null) {
            StringBuilder error = new StringBuilder();
            List<ErrorItemResultV3> errorList = response.getData().getTargetingClauses().getError();
            List<TargetSuccessResultV3> successList = response.getData().getTargetingClauses().getSuccess();

            List<AmazonAdTargeting> succList = new ArrayList<>(errorList.size() + successList.size());

            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                error.append("targetValue:").append(list.get(errorItemResultV3.getIndex()).getTargetingValue())
                        .append(",desc:").append(errorItemResultV3.getErrors().get(0).getErrorValue().get("message")).append(";");
            }

            List<TargetingClauseResult> resultList = new ArrayList<>();
            for (TargetSuccessResultV3 TargetSuccessResultV3 : successList) {
                TargetingClauseResult targetingClauseResult = new TargetingClauseResult();
                targetingClauseResult.setTargetId(Long.valueOf(TargetSuccessResultV3.getTargetId()));
                targetingClauseResult.setCode("SUCCESS");
                resultList.add(targetingClauseResult);
            }

            if (error.length() > 0) {
                result = ResultUtil.error(1, error.toString());
            }
            result.setData(resultList);
        } else if (response.getError() != null) {
            result = ResultUtil.error(response.getError().getMessage());
        }
        return result;
    }




    @Override
    public Result getSuggestBidByKeyword(Integer shopId, String profileId, String marketPlaceId, String adGroupId, List<AmazonAdKeyword> keywordlist) {
        Result result = ResultUtil.success();
        //1.获取店铺信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        String token;
        SuggestedKeywordsClient client = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        List<BidRecommendationSimple> keywords = getBidRecommendationSimpleByAmazonAdKeyword(keywordlist);
        CreateKeywordBidRecommendationsResponse response;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.createKeywordBidRecommendationsResponse(token, profileId, marketPlaceId, Long.valueOf(adGroupId), keywords);
            if (response.getResult() != null && Constants.UNAUTHORIZED.equals(response.getResult().getCode())) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            logger.info("getSuggestBidByKeyword获取竞价建议失败,amazon接口出错");
        }
        //处理返回结果中的错误信息
        if (response.getResult() != null && response.getResult().getRecommendations() != null && response.getResult().getRecommendations().size() > 0) {
            result.setData(response.getResult().getRecommendations());
        } else if (response.getResult() != null) {
            result = ResultUtil.error(response.getResult().getDetails());
        }
        return result;
    }




    //将AmazonAdKeyword转为BidRecommendationSimple，用于获取竞价建议
    private List<BidRecommendationSimple> getBidRecommendationSimpleByAmazonAdKeyword(List<AmazonAdKeyword> keywordlist) {
        List<BidRecommendationSimple> list = Lists.newArrayList();
        BidRecommendationSimple keyword;
        for (AmazonAdKeyword amazonAdKeyword : keywordlist) {
            keyword = new BidRecommendationSimple();
            keyword.setKeyword(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(amazonAdKeyword.getMatchType());
            list.add(keyword);
        }
        return list;
    }

    private List<ProductAd> getProductAdsByAmazonAdProduct(String campaignId, String adGroupId, List<AmazonAdProduct> amazonAdProductList) {
        List<ProductAd> list = Lists.newArrayList();
        ProductAd productAd;
        for (AmazonAdProduct amazonAdProduct : amazonAdProductList) {
            productAd = new ProductAd();
            if (StringUtils.isNotBlank(amazonAdProduct.getAdId())) {
                productAd.setAdId(Long.valueOf(amazonAdProduct.getAdId()));
            }
            productAd.setCampaignId(Long.valueOf(campaignId));
            productAd.setAdGroupId(Long.valueOf(adGroupId));
            //seller只能用sku，vendor才可以用asin
            productAd.setSku(amazonAdProduct.getSku());
            //productAd.setAsin(amazonAdProduct.getAsin());
            if (StringUtils.isBlank(amazonAdProduct.getAdId()) && StringUtils.isBlank(amazonAdProduct.getState())) {//创建的时候默认为启动状态
                productAd.setState(Constants.ENABLED);
            } else {
                productAd.setState(amazonAdProduct.getState());
            }
            list.add(productAd);
        }
        return list;
    }

    private List<PutTargetEntityV3> getTargetingClauseByPoV3(List<AmazonAdTargeting> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpressionType())) {
                targetingClause.setExpressionType(amazonAdTargeting.getExpressionType().toUpperCase());
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getState())) {
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdTargeting.getState()).getValue());
            }
            if (amazonAdTargeting.getBid() != null) {
                targetingClause.setBid(amazonAdTargeting.getBid());
            }

            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);

                ArrayList<TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions) {
                    TargetExpression targetExpression = new TargetExpression();
                    targetExpression.setValue(expression.getValue());
                    targetExpression.setType(TargetingExpressionPredicate.fromOldValue(expression.getValue()).getValue());
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }
    private List<TargetingClause> getTargetingClauseByPo(List<AmazonAdTargeting> list) {
        List<TargetingClause> targetingList = Lists.newArrayListWithCapacity(list.size());
        TargetingClause targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(Long.valueOf(amazonAdTargeting.getTargetId()));
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getCampaignId())) {
                targetingClause.setCampaignId(Long.valueOf(amazonAdTargeting.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(amazonAdTargeting.getAdGroupId()));
            }
            targetingClause.setExpressionType(amazonAdTargeting.getExpressionType());
            if (StringUtils.isBlank(amazonAdTargeting.getTargetId())
                    && StringUtils.isBlank(amazonAdTargeting.getState())) {//创建的时候默认为启动状态
                targetingClause.setState(Constants.ENABLED);
            } else {
                targetingClause.setState(amazonAdTargeting.getState());
            }
            targetingClause.setBid(amazonAdTargeting.getBid());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                targetingClause.setExpressions(expressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private List<Keyword> getKeywordsByPo(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        Keyword keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(Long.valueOf(amazonAdKeyword.getKeywordId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(amazonAdKeyword.getCampaignId()));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(amazonAdKeyword.getAdGroupId()));
            }
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(amazonAdKeyword.getMatchType());
            if (StringUtils.isBlank(amazonAdKeyword.getKeywordId())
                    && StringUtils.isBlank(amazonAdKeyword.getState())) {//创建的时候默认为启动状态
                keyword.setState(Constants.ENABLED);
            } else {
                keyword.setState(amazonAdKeyword.getState());
            }
            keyword.setBid(amazonAdKeyword.getBid());
            list.add(keyword);
        }
        return list;
    }


    private List<PutKeywordEntityV3> getPutKeywordsByPo(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<PutKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        PutKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new PutKeywordEntityV3();
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordId())) {
                keyword.setKeywordId(amazonAdKeyword.getKeywordId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                keyword.setState(AdStateV3.fromOldValue(amazonAdKeyword.getState()).getValue());
            }
            if (amazonAdKeyword.getBid() != null) {
                keyword.setBid(amazonAdKeyword.getBid());
            }
            list.add(keyword);
        }
        return list;
    }

    private AdGroup getAdGroupByPo(AmazonAdGroup amazonAdGroup) {
        AdGroup adGroup = new AdGroup();
        if (StringUtils.isNotBlank(amazonAdGroup.getAdGroupId())) {
            adGroup.setAdGroupId(Long.valueOf(amazonAdGroup.getAdGroupId()));
        }
        adGroup.setName(amazonAdGroup.getName());
        if (StringUtils.isNotBlank(amazonAdGroup.getCampaignId())) {
            adGroup.setCampaignId(Long.valueOf(amazonAdGroup.getCampaignId()));
        }
        adGroup.setDefaultBid(amazonAdGroup.getDefaultBid());
        if (StringUtils.isBlank(amazonAdGroup.getAdGroupId())
                && StringUtils.isBlank(amazonAdGroup.getState())) {//创建的时候默认为启动状态
            adGroup.setState(Constants.ENABLED);
        } else {
            adGroup.setState(amazonAdGroup.getState());
        }
        return adGroup;
    }




    private void dealAmazonAdTargeting(Integer puid, Integer shopId, String profileId, String marketPlaceId, String type, List<TargetingClause> targetingClauseList, Map<String, Double> defaultBidMap, Map<String, Long> idMap, Set<String> adGroupIdSet) {
        //需要新增的活动list
        final List<AmazonAdTargeting> insertList = Lists.newArrayList();
        //需要更新的活动list
        final List<AmazonAdTargeting> updateList = Lists.newArrayList();
        //获取本地的数据去判断是需要更新还是新增
        //1.获取本地的产品信息
        List<String> onlineTargetId = targetingClauseList.stream().map(e -> String.valueOf(e.getTargetId())).collect(Collectors.toList());
        List<String> targetIds = amazonAdTargetDaoRoutingService.getTargetIds(puid, shopId, marketPlaceId, onlineTargetId);
        if (targetIds == null || targetIds.isEmpty()) {
            //表示都是新增的,将平台的关键词转为dxm的对象
            targetingClauseList.forEach(e -> {
                if (adGroupIdSet != null) {
                    adGroupIdSet.add(String.valueOf(e.getAdGroupId()));
                }
                insertList.add(getAmazonAdTargetingByTargeting(puid, shopId, profileId, marketPlaceId, type, defaultBidMap, idMap.get(String.valueOf(e.getAdGroupId())), e));
            });
        } else {
            Set<String> targetIdSet = targetIds.stream().collect(Collectors.toSet());
            targetingClauseList.forEach(e -> {
                if (adGroupIdSet != null) {
                    adGroupIdSet.add(String.valueOf(e.getAdGroupId()));
                }
                if (targetIdSet.contains(String.valueOf(e.getTargetId()))) {
                    updateList.add(getAmazonAdTargetingByTargeting(puid, shopId, profileId, marketPlaceId, type, defaultBidMap, idMap.get(String.valueOf(e.getAdGroupId())), e));
                } else {
                    insertList.add(getAmazonAdTargetingByTargeting(puid, shopId, profileId, marketPlaceId, type, defaultBidMap, idMap.get(String.valueOf(e.getAdGroupId())), e));
                }
            });
        }
        if (insertList != null && insertList.size() > 0) {
            //处理需要新增的定位(没有下一层级,不需要判断是否归档,直接新增就好了)
            amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(puid, insertList, Constants.TARGETING_TYPE_ASIN);
        }
        if (updateList != null && updateList.size() > 0) {
            //处理需要更新的定位(没有下一层级,不需要判断是否归档,直接更新就好了)
            amazonAdTargetDaoRoutingService.updateList(puid, updateList);
        }

    }

    private AmazonAdTargeting getAmazonAdTargetingByTargeting(Integer puid, Integer shopId, String profileId, String marketPlaceId, String type, Map<String, Double> defaultBidMap, Long dxmGroupId, TargetingClause targeting) {
        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
        amazonAdTargeting.setPuid(puid);
        amazonAdTargeting.setShopId(shopId);
        amazonAdTargeting.setMarketplaceId(marketPlaceId);
        amazonAdTargeting.setAdGroupId(String.valueOf(targeting.getAdGroupId()));
        amazonAdTargeting.setCampaignId(String.valueOf(targeting.getCampaignId()));
        amazonAdTargeting.setProfileId(profileId);
        amazonAdTargeting.setDxmGroupId(dxmGroupId);
        amazonAdTargeting.setTargetId(String.valueOf(targeting.getTargetId()));
        amazonAdTargeting.setExpressionType(targeting.getExpressionType());
        List<Expression> expressions = targeting.getExpressions();
        if (expressions != null && expressions.size() > 0) {
            if (Constants.AUTO.equals(targeting.getExpressionType())) {//auto是固定的四条(这个需要做唯一键)
                amazonAdTargeting.setTargetingValue(expressions.get(0).getType());
            } else {
                Map<String, String> map = expressions.stream().collect(Collectors.toMap(Expression::getType, e -> StringUtils.isBlank(e.getValue()) ? "" : e.getValue()));
                if (map.containsKey(ExpressionEnum.asinCategorySameAs.value())) {
                    List<Expression> resolvedExpressions = targeting.getResolvedExpressions();
                    Map<String, String> resolvedMap = resolvedExpressions.stream().collect(Collectors.toMap(Expression::getType, e -> StringUtils.isBlank(e.getValue()) ? "" : e.getValue()));
                    amazonAdTargeting.setType(Constants.TARGETING_TYPE_CATEGORY);
                    amazonAdTargeting.setTargetingValue(map.get(ExpressionEnum.asinCategorySameAs.value()));
                    amazonAdTargeting.setCategoryPath(resolvedMap.get(ExpressionEnum.asinCategorySameAs.value()));
                } else if (Constants.TARGETING_TYPE_NEGATIVEASIN.equals(type) && map.containsKey(ExpressionEnum.asinSameAs.value())) {
                    amazonAdTargeting.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
                    amazonAdTargeting.setTargetingValue(map.get(ExpressionEnum.asinSameAs.value()));
                } else if (!Constants.TARGETING_TYPE_NEGATIVEASIN.equals(type) && map.containsKey(ExpressionEnum.asinSameAs.value())) {
                    amazonAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                    amazonAdTargeting.setTargetingValue(map.get(ExpressionEnum.asinSameAs.value()));
                } else if (!Constants.TARGETING_TYPE_NEGATIVEASIN.equals(type) && map.containsKey(ExpressionEnum.asinExpandedFrom.value())) {
                    amazonAdTargeting.setType(Constants.TARGETING_TYPE_ASIN);
                    amazonAdTargeting.setTargetingValue(map.get(ExpressionEnum.asinExpandedFrom.value()));
                }
            }
        }
        amazonAdTargeting.setExpression(JSONUtil.objectToJson(expressions));
        amazonAdTargeting.setResolvedExpression(JSONUtil.objectToJson(targeting.getResolvedExpressions()));
        if (Constants.BIDDABLE.equals(type) && (targeting.getBid() == null || DoubleUtil.sub(targeting.getBid(), 0d) == 0)) {
            amazonAdTargeting.setBid((defaultBidMap != null && defaultBidMap.get(String.valueOf(targeting.getAdGroupId())) != null) ? defaultBidMap.get(String.valueOf(targeting.getAdGroupId())) : 0d);
        } else {
            amazonAdTargeting.setBid(targeting.getBid());
        }
        amazonAdTargeting.setState(targeting.getState());
        if (dxmGroupId != null) {
            amazonAdTargeting.MD5UniqueKey();
        }
        return amazonAdTargeting;
    }



    private void dealAmazonAdKeyword(Integer puid, Integer shopId, String profileId, String marketPlaceId, String type, List<Keyword> resultList, Map<String, Double> defaultBidMap, Map<String, Long> idMap, final Set<String> adGroupIdSet) {
        //需要新增的活动list
        final List<AmazonAdKeyword> insertList = Lists.newArrayList();
        //需要更新的活动list
        final List<AmazonAdKeyword> updateList = Lists.newArrayList();
        //获取本地的数据去判断是需要更新还是新增
        //1.获取本地的产品信息
        List<String> onlineKeywordId = resultList.stream().map(e -> String.valueOf(e.getKeywordId())).collect(Collectors.toList());
        List<String> keywordIds = amazonAdKeywordDaoRoutingService.getKeywordIds(puid, shopId, marketPlaceId, onlineKeywordId);
        if (keywordIds == null || keywordIds.isEmpty()) {
            //表示都是新增的,将平台的关键词转为dxm的对象
            resultList.forEach(e -> {
                if (adGroupIdSet != null) {
                    adGroupIdSet.add(String.valueOf(e.getAdGroupId()));
                }
                insertList.add(getAmazonAdKeywordByKeyword(puid, shopId, profileId, marketPlaceId, type, defaultBidMap, idMap.get(String.valueOf(e.getAdGroupId())), e));
            });
        } else {
            Set<String> keywordIdSet = keywordIds.stream().collect(Collectors.toSet());
            resultList.forEach(e -> {
                if (adGroupIdSet != null) {
                    adGroupIdSet.add(String.valueOf(e.getAdGroupId()));
                }
                if (keywordIdSet.contains(String.valueOf(e.getKeywordId()))) {
                    updateList.add(getAmazonAdKeywordByKeyword(puid, shopId, profileId, marketPlaceId, type, defaultBidMap, null, e));
                } else {
                    insertList.add(getAmazonAdKeywordByKeyword(puid, shopId, profileId, marketPlaceId, type, defaultBidMap, idMap.get(String.valueOf(e.getAdGroupId())), e));
                }
            });
        }
        if (insertList != null && insertList.size() > 0) {
            //处理需要新增的关键词(没有下一层级,不需要判断是否归档,直接新增就好了)
            amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(puid, insertList, Constants.BIDDABLE);
        }
        if (updateList != null && updateList.size() > 0) {
            //处理需要更新的关键词(没有下一层级,不需要判断是否归档,直接更新就好了)
            amazonAdKeywordDaoRoutingService.updateList(puid, updateList, Constants.BIDDABLE);
        }
    }

    private AmazonAdKeyword getAmazonAdKeywordByKeyword(Integer puid, Integer shopId, String profileId, String marketPlaceId, String type, Map<String, Double> defaultBidMap, Long dxmGroupId, Keyword keyword) {
        AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
        amazonAdKeyword.setPuid(puid);
        amazonAdKeyword.setShopId(shopId);
        amazonAdKeyword.setKeywordId(String.valueOf(keyword.getKeywordId()));
        amazonAdKeyword.setAdGroupId(String.valueOf(keyword.getAdGroupId()));
        amazonAdKeyword.setCampaignId(String.valueOf(keyword.getCampaignId()));
        amazonAdKeyword.setProfileId(profileId);
        amazonAdKeyword.setMarketplaceId(marketPlaceId);
        amazonAdKeyword.setKeywordText(keyword.getKeywordText());
        amazonAdKeyword.setDxmGroupId(dxmGroupId);
        amazonAdKeyword.setMatchType(keyword.getMatchType());
        if (Constants.BIDDABLE.equals(type) && (keyword.getBid() == null || DoubleUtil.sub(keyword.getBid(), 0d) == 0)) {
            amazonAdKeyword.setBid((defaultBidMap != null && defaultBidMap.get(String.valueOf(keyword.getAdGroupId())) != null) ? defaultBidMap.get(String.valueOf(keyword.getAdGroupId())) : 0d);
        } else {
            amazonAdKeyword.setBid(keyword.getBid());
        }
        amazonAdKeyword.setType(type);
        amazonAdKeyword.setState(keyword.getState());
        if (dxmGroupId != null) {
            amazonAdKeyword.MD5UniqueKey();
        }
        return amazonAdKeyword;
    }


    private void dealAmazonAdProduct(Integer puid, Integer shopId, String profileId, String marketPlaceId, List<ProductAd> productAdList, Map<String, Long> idMap) {
        //需要新增的活动list
        final List<AmazonAdProduct> insertList = Lists.newArrayList();
        //需要更新的活动list
        final List<AmazonAdProduct> updateList = Lists.newArrayList();
        //1.获取本地的产品信息
        List<String> onlineAdId = productAdList.stream().map(e -> String.valueOf(e.getAdId())).collect(Collectors.toList());
        List<String> adIds = amazonAdProductDao.getAdIds(puid, shopId, marketPlaceId, onlineAdId);
        //获取广告组对应的dxmGroupId

        if (adIds == null || adIds.size() <= 0) {
            //表示都是新增的,将平台的广告组转为dxm的对象
            productAdList.forEach(e -> {
                insertList.add(getAmazonAdProductByProductAd(puid, shopId, profileId, marketPlaceId, idMap.get(String.valueOf(e.getAdGroupId())), e));
            });
        } else {
            //将AmazonAdGroup转为以广告组Id为key的map，方便对比
            Set<String> adIdSet = adIds.stream().collect(Collectors.toSet());
            productAdList.forEach(e -> {
                if (adIdSet.contains(String.valueOf(e.getAdId()))) {
                    updateList.add(getAmazonAdProductByProductAd(puid, shopId, profileId, marketPlaceId, null, e));
                } else {
                    insertList.add(getAmazonAdProductByProductAd(puid, shopId, profileId, marketPlaceId, idMap.get(String.valueOf(e.getAdGroupId())), e));
                }
            });
        }
        if (insertList != null && insertList.size() > 0) {
            //处理需要新增的活动
            amazonAdProductDao.insertOnDuplicateKeyUpdate(puid, insertList);
        }
        if (updateList != null && updateList.size() > 0) {
            //处理需要更新的的活动
            amazonAdProductDao.updateList(puid,updateList);
        }

    }

    private AmazonAdProduct getAmazonAdProductByProductAd(Integer puid, Integer shopId, String profileId, String marketPlaceId, Long dxmGroupId, ProductAd productAd) {
        AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
        amazonAdProduct.setPuid(puid);
        amazonAdProduct.setShopId(shopId);
        amazonAdProduct.setAdId(String.valueOf(productAd.getAdId()));
        amazonAdProduct.setAdGroupId(String.valueOf(productAd.getAdGroupId()));
        amazonAdProduct.setCampaignId(String.valueOf(productAd.getCampaignId()));
        amazonAdProduct.setProfileId(profileId);
        amazonAdProduct.setMarketplaceId(marketPlaceId);
        amazonAdProduct.setSku(productAd.getSku());
        amazonAdProduct.setAsin(productAd.getAsin());
        amazonAdProduct.setState(productAd.getState());
        if (dxmGroupId != null) {
            amazonAdProduct.setDxmGroupId(dxmGroupId);
            amazonAdProduct.MD5UniqueKey();
        }
        return amazonAdProduct;
    }

    /**
     * 将从amazon获取过来的广告信息转换为dxm需要的信息
     *
     * @param puid
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param targetingType
     * @param adGroup
     * @return
     */
    private AmazonAdGroup getAmazonAdGroupByAdGroup(Integer puid, Integer shopId, String profileId, String marketPlaceId, String targetingType, AdGroup adGroup) {
        AmazonAdGroup amazonAdGroup = new AmazonAdGroup();
        amazonAdGroup.setPuid(puid);
        amazonAdGroup.setShopId(shopId);
        amazonAdGroup.setProfileId(profileId);
        amazonAdGroup.setMarketplaceId(marketPlaceId);
        amazonAdGroup.setAdGroupId(String.valueOf(adGroup.getAdGroupId()));
        amazonAdGroup.setCampaignId(String.valueOf(adGroup.getCampaignId()));
        amazonAdGroup.setAdGroupType(Constants.AUTO.equals(targetingType) ? Constants.AUTO : null);
        amazonAdGroup.setName(adGroup.getName());
        amazonAdGroup.setDefaultBid(adGroup.getDefaultBid());
        amazonAdGroup.setState(adGroup.getState());
        amazonAdGroup.MD5UniqueKey();
        return amazonAdGroup;
    }

    /**
     * 将从amazon获取过来的活动信息转换为dxm需要的信息(同步单个活动)
     *
     * @param puid
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param campaign
     */
    private AmazonAdCampaign getAmazonAdCampaignByCampaign(Integer puid, Integer shopId, String profileId, String marketPlaceId, Campaign campaign) {
        AmazonAdCampaign amazonAdCampaign;
        amazonAdCampaign = new AmazonAdCampaign();
        amazonAdCampaign.setPuid(puid);
        amazonAdCampaign.setShopId(shopId);
        amazonAdCampaign.setCampaignId(String.valueOf(campaign.getCampaignId()));
        amazonAdCampaign.setProfileId(profileId);
        amazonAdCampaign.setMarketplaceId(marketPlaceId);
        amazonAdCampaign.setName(campaign.getName());
        amazonAdCampaign.setCampaignType(campaign.getCampaignType());
        amazonAdCampaign.setTargetingType(campaign.getTargetingType());
        amazonAdCampaign.setState(campaign.getState());
        amazonAdCampaign.setDailyBudget(campaign.getDailyBudget());
        if (StringUtils.isNotBlank(campaign.getStartDate())) {
            amazonAdCampaign.setStartDate(DateUtil.strToDate(campaign.getStartDate(), "yyyyMMdd"));
        }
        if (StringUtils.isNotBlank(campaign.getEndDate())) {//活动的结束时间可以为空
            amazonAdCampaign.setEndDate(DateUtil.strToDate(campaign.getEndDate(), "yyyyMMdd"));
        }
        if (campaign.getBidding() != null) { //竞价策略和广告位置
            amazonAdCampaign.setStrategy(campaign.getBidding().getStrategy());
            if (campaign.getBidding().getAdjustments() != null) {
                amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(campaign.getBidding().getAdjustments()));
            }
        }
        amazonAdCampaign.setServingStatus(campaign.getServingStatus());
        if (StringUtils.isNotBlank(campaign.getCreationDate())) {
            amazonAdCampaign.setCreationDate(DateUtil.getDateByMillisecond(Long.valueOf(campaign.getCreationDate())));
        }
        if (StringUtils.isNotBlank(campaign.getLastUpdatedDate())) {
            amazonAdCampaign.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(campaign.getLastUpdatedDate())));
        }
        return amazonAdCampaign;
    }

    /**
     * 将从amazon获取过来的活动信息更新到dxm的对象中
     *
     * @param amazonAdCampaign
     * @param campaign
     */
    private void updateAmazonAdCampaignByCampaign(AmazonAdCampaign amazonAdCampaign, Campaign campaign) {
        if (amazonAdCampaign == null || campaign == null) {
            return;
        }
        if (StringUtils.isBlank(campaign.getStartDate())) {//活动必须要有开始时间
            return;
        }
        amazonAdCampaign.setName(campaign.getName());
        amazonAdCampaign.setCampaignType(campaign.getCampaignType());
        amazonAdCampaign.setTargetingType(campaign.getTargetingType());
        amazonAdCampaign.setState(campaign.getState());
        amazonAdCampaign.setDailyBudget(campaign.getDailyBudget());
        amazonAdCampaign.setStartDate(DateUtil.strToDate(campaign.getStartDate(), "yyyyMMdd"));
        if (StringUtils.isNotBlank(campaign.getEndDate())) {//活动的结束时间可以为空
            amazonAdCampaign.setEndDate(DateUtil.strToDate(campaign.getEndDate(), "yyyyMMdd"));
        } else {
            amazonAdCampaign.setEndDate(null);
        }
        if (campaign.getBidding() != null) { //竞价策略和广告位置
            amazonAdCampaign.setStrategy(campaign.getBidding().getStrategy());
            if (campaign.getBidding().getAdjustments() != null) {
                amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(campaign.getBidding().getAdjustments()));
            }
        } else { //表示没有设置进价策略
            amazonAdCampaign.setStrategy(null);
            amazonAdCampaign.setAdjustments("[]");
        }
        amazonAdCampaign.setServingStatus(campaign.getServingStatus());
        if (StringUtils.isNotBlank(campaign.getLastUpdatedDate())) {
            amazonAdCampaign.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(campaign.getLastUpdatedDate())));
        }
    }

    private Campaign getCampaignByCampaignPo(AmazonAdCampaign amazonAdCampaign) {
        Campaign campaign = new Campaign();
        if (StringUtils.isNotBlank(amazonAdCampaign.getCampaignId())) {
            campaign.setCampaignId(Long.valueOf(amazonAdCampaign.getCampaignId()));
        } else {
            if (StringUtils.isBlank(amazonAdCampaign.getCampaignType())) {//创建的时候默认
                campaign.setCampaignType(Constants.SPONSORED_PRODUCTS);
            } else {
                campaign.setCampaignType(amazonAdCampaign.getCampaignType());
            }
            campaign.setTargetingType(amazonAdCampaign.getTargetingType());
        }
        campaign.setName(amazonAdCampaign.getName());
        if (StringUtils.isBlank(amazonAdCampaign.getCampaignId())
                && StringUtils.isBlank(amazonAdCampaign.getState())) {//创建的时候默认为启动状态
            campaign.setState(Constants.ENABLED);
        } else {
            campaign.setState(amazonAdCampaign.getState());
        }
        campaign.setDailyBudget(amazonAdCampaign.getDailyBudget());
        if (amazonAdCampaign.getStartDate() != null) {
            campaign.setStartDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getStartDate(), "yyyyMMdd"));
        }
        if (amazonAdCampaign.getEndDate() != null) {
            campaign.setEndDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getEndDate(), "yyyyMMdd"));
        }
        if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy())) { //如果竞价策略为空就不用设置
            Bidding bidding = new Bidding();
            bidding.setStrategy(amazonAdCampaign.getStrategy());
            List<Adjustment> adjustments = null;
            if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
            }
            bidding.setAdjustments(adjustments);
            campaign.setBidding(bidding);
        }
        return campaign;
    }


    @Override
    public Result<ApiResponseV3<KeywordSuccessResultV3>> createBiddableKeywordV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> biddableKeywordList) {
        Result result = ResultUtil.success();
        //1.获取店铺信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        String token;
        //创建竞价关键词
        KeywordSpV3Client client = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        List<CreateKeywordEntityV3> keywordList = getCreateV3KeywordsByPo(biddableKeywordList);
        CreateSpKeywordV3Response response;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.createKeywords(token, profileId, marketPlaceId, keywordList,Boolean.TRUE);
            if (response.getStatusCode() != null && 401 == response.getStatusCode()) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            logger.info("createBiddableKeyword创建竞价关键词失败,amazon接口出错");
        }

        String msg = "网络延迟，请稍后重试";

        //处理返回结果中的错误信息

        if (response.getData() != null && response.getData().getKeywords() != null) {
            StringBuilder error = new StringBuilder();


            List<KeywordSuccessResultV3> success = response.getData().getKeywords().getSuccess();
            if(CollectionUtils.isNotEmpty(success)){
                for (KeywordSuccessResultV3 keywordResult : success) {
                    AmazonAdKeyword amazonAdKeyword = biddableKeywordList.get(keywordResult.getIndex());
                    amazonAdKeyword.setKeywordId(keywordResult.getKeywordId());
                }
            }
            List<ErrorItemResultV3> errorItemResultV3 = response.getData().getKeywords().getError();
            if (CollectionUtils.isNotEmpty(errorItemResultV3)) {
                for (ErrorItemResultV3 keywordResult : errorItemResultV3) {
                    String e =  "添加失败";
                    if(CollectionUtils.isNotEmpty(keywordResult.getErrors()) && StringUtils.isNotBlank(keywordResult.getErrors().get(0).getErrorMessage())){
                        e = keywordResult.getErrors().get(0).getErrorMessage();
                    }
                    AmazonAdKeyword amazonAdKeyword = biddableKeywordList.get(keywordResult.getIndex());
                    amazonAdKeyword.setError(e);

                    error.append("keywordText:").append(biddableKeywordList.get(keywordResult.getIndex()).getKeywordText()).append(",desc:").append(e).append(";");
                }

            }
            if (error.length() > 0) {
                result.setCode(1);
                result.setMsg(error.toString());
            }
            result.setData(response.getData().getKeywords());
        } else if (response.getError() != null) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getMessage()));
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage()));
            }
        } else {
            result = ResultUtil.error(msg);
        }
        return result;
    }

    private List<CreateKeywordEntityV3> getCreateV3KeywordsByPo(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CreateKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CreateKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CreateKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            keyword.setMatchType(SpV3MatchTypeEnum.getSpV3MatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            if(StringUtils.isBlank(amazonAdKeyword.getState())){
                amazonAdKeyword.setState(Constants.ENABLED);
            }
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            keyword.setBid(amazonAdKeyword.getBid());
            list.add(keyword);
        }
        return list;
    }


    @Override
    public Result<ApiResponseV3<KeywordSuccessResultV3>> createNegativeKeywordV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> negativeKeywordList) {
        Result result = ResultUtil.success();
        //1.获取店铺信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        String token;
        //创建竞价关键词
        NegativeKeywordSpV3Client client = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        List<CreateNegativeKeywordEntityV3> keywordList = getCreateNegativeKeywordsV3ByPo(negativeKeywordList);
        CreateSpNegativeKeywordV3Response response;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.createNegativeKeywords(token, profileId, marketPlaceId, keywordList,true);
            if (response.getStatusCode() != null && 401 == response.getStatusCode()) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            logger.info("createNegativeKeyword创建否定关键词失败,amazon接口出错");
        }
        String msg = "网络延迟，请稍后重试";

        //处理返回结果中的错误信息

        if (response.getData() != null && response.getData().getNegativeKeywords() != null) {
            StringBuilder error = new StringBuilder();


            List<NegativeKeywordSuccessResultV3> success = response.getData().getNegativeKeywords().getSuccess();
            if(CollectionUtils.isNotEmpty(success)){
                for (NegativeKeywordSuccessResultV3 keywordResult : success) {
                    AmazonAdKeyword amazonAdKeyword = negativeKeywordList.get(keywordResult.getIndex());
                    amazonAdKeyword.setKeywordId(keywordResult.getNegativeKeywordId());
                }
            }
            List<ErrorItemResultV3> errorItemResultV3 = response.getData().getNegativeKeywords().getError();
            if (CollectionUtils.isNotEmpty(errorItemResultV3)) {
                for (ErrorItemResultV3 keywordResult : errorItemResultV3) {
                    String e =  "添加失败";
                    if(CollectionUtils.isNotEmpty(keywordResult.getErrors()) && StringUtils.isNotBlank(keywordResult.getErrors().get(0).getErrorMessage())){
                        e = keywordResult.getErrors().get(0).getErrorMessage();
                    }
                    AmazonAdKeyword amazonAdKeyword = negativeKeywordList.get(keywordResult.getIndex());
                    amazonAdKeyword.setError(e);

                    error.append("keywordText:").append(negativeKeywordList.get(keywordResult.getIndex()).getKeywordText()).append(",desc:").append(e).append(";");
                }

            }
            if (error.length() > 0) {
                result.setCode(1);
                result.setMsg(error.toString());
            }
            result.setData(response.getData().getNegativeKeywords());
        } else if (response.getError() != null) {

            if(StringUtils.isNotBlank(response.getError().getMessage())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getMessage()));
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage()));
            }
        } else {
            result = ResultUtil.error(msg);
        }
        return result;
    }


    private List<CreateNegativeKeywordEntityV3> getCreateNegativeKeywordsV3ByPo(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CreateNegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CreateNegativeKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CreateNegativeKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            SpV3NegativeMatchTypeEnum spV3NegativeMatchTypeEnumByValue = SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType());
            if (spV3NegativeMatchTypeEnumByValue == null) {
                keyword.setMatchType(amazonAdKeyword.getMatchType());
            } else {
                keyword.setMatchType(spV3NegativeMatchTypeEnumByValue.valueV3());
            }

            if(StringUtils.isBlank(amazonAdKeyword.getState())){
                amazonAdKeyword.setState(Constants.ENABLED);
            }
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            list.add(keyword);
        }
        return list;
    }


    @Override
    public Result<ApiResponseV3<TargetSuccessResultV3>> createTargetV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdTargeting> list) {
        Result result = ResultUtil.success();
        //根据shopid获取到token信息
        //1.获取店铺信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        //2.获得token
        String token;
        //创建竞价关键词
        TargetSpV3Client client = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        List<CreateTargetEntityV3> targetingList = getCreateTargetingClauseByPo(list);
        CreateSpTargetV3Response response;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.createTargets(token, profileId, marketPlaceId, targetingList,Boolean.TRUE);
            if (response.getStatusCode() != null && 401 == response.getStatusCode()) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        String msg = "网络延迟，请稍后重试";

        //处理返回结果中的错误信息

        if (response.getData() != null && response.getData().getTargetingClauses() != null) {
            StringBuilder error = new StringBuilder();


            List<TargetSuccessResultV3> success = response.getData().getTargetingClauses().getSuccess();
            if(CollectionUtils.isNotEmpty(success)){
                for (TargetSuccessResultV3 successResult : success) {
                    AmazonAdTargeting amazonAdTargeting = list.get(successResult.getIndex());
                    amazonAdTargeting.setTargetId(successResult.getTargetId());
                }
            }
            List<ErrorItemResultV3> errorItemResultV3 = response.getData().getTargetingClauses().getError();
            if (CollectionUtils.isNotEmpty(errorItemResultV3)) {
                for (ErrorItemResultV3 keywordResult : errorItemResultV3) {
                    String e =  "添加失败";
                    if(CollectionUtils.isNotEmpty(keywordResult.getErrors()) && StringUtils.isNotBlank(keywordResult.getErrors().get(0).getErrorMessage())){
                        e = keywordResult.getErrors().get(0).getErrorMessage();
                    }
                    AmazonAdTargeting amazonAdTargeting = list.get(keywordResult.getIndex());
                    amazonAdTargeting.setError(e);

                    error.append("targetValue:").append(list.get(keywordResult.getIndex()).getError()).append(",desc:").append(e).append(";");
                }

            }
            if (error.length() > 0) {
                result.setCode(1);
                result.setMsg(error.toString());
            }
            result.setData(response.getData().getTargetingClauses());
        } else if (response.getError() != null) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getMessage()));
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage()));
            }
        } else {
            result = ResultUtil.error(msg);
        }
        return result;
    }


    private List<CreateTargetEntityV3> getCreateTargetingClauseByPo(List<AmazonAdTargeting> list) {
        List<CreateTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        CreateTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new CreateTargetEntityV3();
            targetingClause.setCampaignId(amazonAdTargeting.getCampaignId());
            targetingClause.setAdGroupId(amazonAdTargeting.getAdGroupId());
            targetingClause.setExpressionType(SpV3TargetingTypeEnum.getSpV3TargetingTypeEnumByValue(amazonAdTargeting.getExpressionType()).valueV3());

            if (StringUtils.isBlank(amazonAdTargeting.getState())) {//创建的时候默认为启动状态
                amazonAdTargeting.setState(Constants.ENABLED);
            }
            targetingClause.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()).valueV3());
            targetingClause.setBid(amazonAdTargeting.getBid());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                List<TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions){
                    TargetExpression e = new TargetExpression();
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }


    @Override
    public Result<ApiResponseV3<NegativeTargetSuccessResultV3>> createNegativeTargetV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdTargeting> list) {
        Result result = ResultUtil.success();
        //根据shopid获取到token信息
        ShopAuth shop = shopAuthDao.getScAndVcById(shopId);
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        String re = Constant.MARKETPLACEID_REGION_MAP.get(marketPlaceId);
        if (StringUtils.isBlank(re)) {
            return ResultUtil.error("站点没有对应区域信息");
        }
        String token;
        //创建竞价关键词
        NegativeTargetSpV3Client client = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        List<CreateNegativeTargetEntityV3> targetingList = getCreateV3NegativeTargetingClauseByPo(list);
        CreateSpNegativeTargetV3Response response;
        int retry0 = 0;
        do {
            token = shopAuthService.getAdToken(shop);
            //如果response为空就在请求一次
            response = client.createNegativeTargets(token, profileId, marketPlaceId, targetingList,Boolean.TRUE);
            if (response.getStatusCode() != null && 401 == response.getStatusCode()) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                if (retry0 < 2) {
                    response = null;
                }
            }
        } while (response == null & retry0++ < 3);
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        String msg = "网络延迟，请稍后重试";

        //处理返回结果中的错误信息

        if (response.getData() != null && response.getData().getNegativeTargetingClauses() != null) {
            StringBuilder error = new StringBuilder();


            List<NegativeTargetSuccessResultV3> success = response.getData().getNegativeTargetingClauses().getSuccess();
            if(CollectionUtils.isNotEmpty(success)){
                for (NegativeTargetSuccessResultV3 successResult : success) {
                    AmazonAdTargeting amazonAdTargeting = list.get(successResult.getIndex());
                    amazonAdTargeting.setTargetId(successResult.getTargetId());
                }
            }
            List<ErrorItemResultV3> errorItemResultV3 = response.getData().getNegativeTargetingClauses().getError();
            if (CollectionUtils.isNotEmpty(errorItemResultV3)) {
                for (ErrorItemResultV3 keywordResult : errorItemResultV3) {
                    String e =  "添加失败";
                    if(CollectionUtils.isNotEmpty(keywordResult.getErrors()) && StringUtils.isNotBlank(keywordResult.getErrors().get(0).getErrorMessage())){
                        e = keywordResult.getErrors().get(0).getErrorMessage();
                    }
                    AmazonAdTargeting amazonAdTargeting = list.get(keywordResult.getIndex());
                    amazonAdTargeting.setError(e);

                    error.append("targetValue:").append(list.get(keywordResult.getIndex()).getError()).append(",desc:").append(e).append(";");
                }

            }
            if (error.length() > 0) {
                result.setCode(1);
                result.setMsg(error.toString());
            }
            result.setData(response.getData().getNegativeTargetingClauses());
        } else if (response.getError() != null ) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getMessage()));
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                result = ResultUtil.error(AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage()));
            }
        } else {
            result = ResultUtil.error(msg);
        }
        return result;
    }


    private List<CreateNegativeTargetEntityV3> getCreateV3NegativeTargetingClauseByPo(List<AmazonAdTargeting> list) {
        List<CreateNegativeTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        CreateNegativeTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new CreateNegativeTargetEntityV3();
            targetingClause.setCampaignId(amazonAdTargeting.getCampaignId());
            targetingClause.setAdGroupId(amazonAdTargeting.getAdGroupId());
            if ( StringUtils.isBlank(amazonAdTargeting.getState())) {//创建的时候默认为启动状态
                amazonAdTargeting.setState(Constants.ENABLED);
            }
            targetingClause.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()).valueV3());

            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                List<com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions){
                    com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression e = new com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression();
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }


}
