package com.meiyunji.sponsored.service.common.service.impl;

import com.amazon.advertising.localization.keyword.KeywordsLocalizationClient;
import com.amazon.advertising.localization.keyword.KeywordsLocalizationResponse;
import com.amazon.advertising.localization.keyword.SourceDetail;
import com.amazon.advertising.localization.keyword.TargetDetail;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.common.dao.IWordTranslateDao;
import com.meiyunji.sponsored.service.common.po.WordTranslate;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.common.vo.WordTranslateVo;
import com.meiyunji.sponsored.service.cpc.constants.WordTranslateConstant;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-03-13  20:25
 */
@Slf4j
@Service
public class WordTranslateServiceImpl implements IWordTranslateService {

    @Autowired
    private IWordTranslateDao wordTranslateDao;
    @Autowired
    private RedisService redisService;
    @Autowired
    private IShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IShopAuthService shopAuthService;

    public static final String WORD_TRANSLATE_KEY = "WORD_TRANSLATE_KEY:";
    @Override
    public List<WordTranslateVo> wordTranslate(Integer puid, List<WordTranslateQo> words, boolean isExport) {
        Map<String, String> wordCnMap = new HashMap<>();
        List<WordTranslateQo> noTranslateWords = new ArrayList<>();
        //1、查询缓存翻译
        for (WordTranslateQo wordQo : words) {
            String value = redisService.getString(WORD_TRANSLATE_KEY + this.getWordTranslateKey(wordQo.getMarketplaceId(), wordQo.getWord()));
            if (value != null) {
                wordCnMap.put(this.getWordTranslateKey(wordQo.getMarketplaceId(), wordQo.getWord()), value);
            } else {
                noTranslateWords.add(wordQo);
            }
        }
        if (CollectionUtils.isEmpty(noTranslateWords)) {
            return this.buildWordTranslateVoList(words, wordCnMap);
        }
        //2、查询数据库翻译
        Map<String, String> needCacheSelectMap = wordTranslateDao.listByMarketplaceIdAndWord(noTranslateWords).stream()
                .collect(Collectors.toMap(e -> this.getWordTranslateKey(e.getMarketplaceId(), e.getWord()), WordTranslate::getWordCn));
        wordCnMap.putAll(needCacheSelectMap);
        noTranslateWords = words.stream().filter(e -> !wordCnMap.containsKey(this.getWordTranslateKey(e.getMarketplaceId(), e.getWord()))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(noTranslateWords)) {
            if (!isExport) {
                this.asyncPutWordTranslateRedis(puid, needCacheSelectMap);
            }
            return this.buildWordTranslateVoList(words, wordCnMap);
        }
        //3、查询亚马逊接口翻译，用户测才查，导出不走
        if (!isExport) {
            ShopAuth shopAuth = shopAuthDao.getOneByAdStatus(puid);
            if (shopAuth != null) {
                AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
                Map<String, Set<String>> marketplaceIdWordSetMap = noTranslateWords.stream().collect(Collectors.groupingBy(WordTranslateQo::getMarketplaceId, Collectors.mapping(WordTranslateQo::getWord, Collectors.toSet())));
                for (Map.Entry<String, Set<String>> entry : marketplaceIdWordSetMap.entrySet()) {
                    Map<String, String> getApiMap = this.getWordTranslateByApi(shopAuth, entry.getKey(), profile.getProfileId(), shopAuthService.getAdToken(shopAuth), entry.getValue());
                    needCacheSelectMap.putAll(getApiMap);
                    wordCnMap.putAll(getApiMap);
                }
            }
            this.asyncPutWordTranslateRedis(puid, needCacheSelectMap);
            this.asyncInsertWordTranslateTable(puid, needCacheSelectMap);
        }
        return this.buildWordTranslateVoList(words, wordCnMap);
    }

    @Override
    public Map<String, String> getWordTranslateMap(Integer puid, List<WordTranslateQo> words, boolean isExport) {
        return wordTranslate(puid, words, isExport).stream()
                .collect(Collectors.toMap(e -> getWordTranslateKey(e.getMarketplaceId(), e.getWord()), WordTranslateVo::getWordCn, (v1, v2) -> v2));
    }

    /**
     * 异步将翻译结果放入redis
     * @param wordCnMap
     */
    public void asyncPutWordTranslateRedis(Integer puid, Map<String, String> wordCnMap) {
        if (MapUtils.isEmpty(wordCnMap)) {
            return;
        }
        CompletableFuture.runAsync(() -> {
            wordCnMap.forEach((k, v) -> {
                redisService.set(WORD_TRANSLATE_KEY + k, v, 60 * 60);
            });
        }, ThreadPoolUtil.getWordTranslateSetRedisExecutor()).exceptionally(e -> {
            log.error(String.format("asyncPutWordTranslateRedis error, puid: %d", puid), e);
            return null;
        });
    }

    /**
     * 异步将翻译结果放入翻译表
     * @param wordCnMap
     */
    public void asyncInsertWordTranslateTable(Integer puid, Map<String, String> wordCnMap) {
        if (MapUtils.isEmpty(wordCnMap)) {
            return;
        }
        CompletableFuture.runAsync(() -> {
            List<WordTranslate> wordTranslates = new ArrayList<>();
            wordCnMap.forEach((k, v) -> {
                String[] keySplit = k.split(StringUtil.SPECIAL_COMMA);
                if (keySplit[1].length() <= 250 && v.length() <= 250) {
                    wordTranslates.add(new WordTranslate(null, keySplit[0], keySplit[1], v));
                }
            });
            if (CollectionUtils.isNotEmpty(wordTranslates)) {
                Map<String, List<WordTranslate>> wordTranslateMap = wordTranslates.stream().collect(Collectors.groupingBy(WordTranslate::getMarketplaceId, Collectors.toList()));
                for (Map.Entry<String, List<WordTranslate>> entry : wordTranslateMap.entrySet()) {
                    List<List<WordTranslate>> partitionList = Lists.partition(entry.getValue(), 1000);
                    for (List<WordTranslate> list : partitionList) {
                        wordTranslateDao.batchInsert(entry.getKey(), list);
                    }
                }
            }
        }, ThreadPoolUtil.getWordTranslateInsertTableExecutor()).exceptionally(e -> {
            log.error(String.format("asyncInsertWordTranslateTable error, puid: %d", puid), e);
            return null;
        });
    }

    /**
     * 构建翻译返回实体
     * @param words
     * @param wordCnMap
     * @return
     */
    private List<WordTranslateVo> buildWordTranslateVoList(List<WordTranslateQo> words, Map<String, String> wordCnMap) {
        return words.stream().map(e -> {
            WordTranslateVo vo = new WordTranslateVo();
            vo.setWord(e.getWord());
            vo.setMarketplaceId(e.getMarketplaceId());
            vo.setWordCn(wordCnMap.getOrDefault(this.getWordTranslateKey(e.getMarketplaceId(), e.getWord()), ""));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 调用亚马逊翻译接口获取词翻译
     * @param shopAuth
     * @param marketplaceId 站点可以和shopAuth不同站点
     * @param profileId
     * @param accessToken
     * @param words
     * @return
     */
    private Map<String, String> getWordTranslateByApi(ShopAuth shopAuth, String marketplaceId, String profileId, String accessToken, Set<String> words) {
        Map<String, String> map = new HashMap<>();
        SourceDetail sourceDetail = new SourceDetail();
        sourceDetail.setMarketplaceId(marketplaceId);
        TargetDetail targetDetail = new TargetDetail();
        targetDetail.setLocales(Collections.singletonList("zh_CN"));
        KeywordsLocalizationResponse response = KeywordsLocalizationClient.getInstance().getKeywordsLocalize(accessToken, profileId,
                shopAuth.getMarketplaceId(), new ArrayList<>(words), sourceDetail, targetDetail);
        if (response.getStatusCode() != null && response.getStatusCode() == 200 && response.getResult() != null && response.getResult().getLocalizedKeywordResponses() != null) {
            response.getResult().getLocalizedKeywordResponses().forEach(e -> {
                if (e.getSourceKeyword() != null && e.getSourceKeyword().getKeyword() != null &&
                        e.getLocalizedKeywordResults() != null && e.getLocalizedKeywordResults().get("zh_CN") != null && e.getLocalizedKeywordResults().get("zh_CN").getKeyword() != null) {
                    if (WordTranslateConstant.FORBIDDEN_WORD.stream().noneMatch(w -> e.getLocalizedKeywordResults().get("zh_CN").getKeyword().getKeyword().contains(w))) {
                        map.put(this.getWordTranslateKey(marketplaceId, e.getSourceKeyword().getKeyword()), e.getLocalizedKeywordResults().get("zh_CN").getKeyword().getKeyword());
                    } else {
                        map.put(this.getWordTranslateKey(marketplaceId, e.getSourceKeyword().getKeyword()), "");
                    }
                } else {
                    log.info("amazon word translators return error e:{}", JSONUtil.objectToJson(e));
                }
            });
            return map;
        } else {
            log.warn("Request amazon word translators error, status: {} message: {}", response.getStatusCode(), response.getStatusMessage());
        }
        return map;
    }
    @Override
    public String getWordTranslateKey(String marketplaceId, String word) {
        return marketplaceId + StringUtil.SPECIAL_COMMA + StringUtil.trim(word);
    }
}
