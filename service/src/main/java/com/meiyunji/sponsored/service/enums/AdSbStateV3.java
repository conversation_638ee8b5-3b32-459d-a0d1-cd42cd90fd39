package com.meiyunji.sponsored.service.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2024/1/16 20:33
 * @describe:
 */
@Getter
public enum AdSbStateV3 {
    ARCHIVED("ARCHIVED", "archived"),
    ENABLED("ENABLED", "enabled"),
    PAUSED ("PAUSED", "paused")
    ;
    private String value;
    private String oldValue;

    AdSbStateV3(String value, String oldValue) {
        this.value = value;
        this.oldValue = oldValue;
    }

    public static AdSbStateV3 fromValue(String value){
        for (AdSbStateV3 typeV3 : values()) {
            if (typeV3.getValue().equals(value)) {
                return typeV3;
            }
        }
        return null;
    }

    public static AdSbStateV3 fromOldValue(String oldValue){
        for (AdSbStateV3 typeV3 : values()) {
            if (typeV3.getOldValue().equals(oldValue)) {
                return typeV3;
            }
        }
        return null;
    }
}
