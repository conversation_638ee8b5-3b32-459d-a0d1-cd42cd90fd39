package com.meiyunji.sponsored.service.account.po;

import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopSpStatusEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * ShopAuth
 *
 * <AUTHOR>
 */
@DbTable(value = "t_shop_auth")
public class ScAndVcShopAuth extends BasePo {
    private static final long serialVersionUID = -3228256338058760474l;
    /**
     * id
     */
    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Integer id;
    /**
     * 用户id
     */
    @DbColumn(value = "puid")
    private Integer puid;
    /**
     * 店铺名称
     */
    @DbColumn(value = "name")
    private String name;
    /**
     * 亚马逊卖家编号
     */
    @DbColumn(value = "selling_partner_id")
    private String sellingPartnerId;
    /**
     * 大区，na,eu,fe
     */
    @DbColumn(value = "region")
    private String region;
    /**
     * 站点的marketplace id
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;
    /**
     * mws token
     */
    @DbColumn(value = "mws_auth_token")
    private String mwsAuthToken;
    /**
     * seller partner api refresh token
     */
    @DbColumn(value = "sp_refresh_token")
    private String spRefreshToken;
    /**
     * seller partner api access token
     */
    @DbColumn(value = "sp_access_token")
    private String spAccessToken;
    /**
     * seller partner api 授权时间
     */
    @DbColumn(value = "sp_auth_time")
    private Date spAuthTime;
    /**
     * 店铺授权状态：未授权，已授权，已过期
     */
    @DbColumn(value = "sp_status")
    private String spStatus;
    /**
     * Advertising api refresh token
     */
    @DbColumn(value = "ad_refresh_token")
    private String adRefreshToken;
    /**
     * Advertising api access token
     */
    @DbColumn(value = "ad_access_token")
    private String adAccessToken;
    /**
     * Advertising api 授权时间
     */
    @DbColumn(value = "ad_auth_time")
    private Date adAuthTime;
    /**
     * 广告授权状态：未授权，已授权，已过期
     */
    @DbColumn(value = "ad_status")
    private String adStatus;
    /**
     * 店铺状态,0默认,1授权失效,2 SP授权失效
     */
    @DbColumn(value = "status")
    private Integer status;
    /**
     * 创建人id
     */
    @DbColumn(value = "create_id")
    private Integer createId;
    /**
     * 修改人id
     */
    @DbColumn(value = "update_id")
    private Integer updateId;
    /**
     * 扣费时间，默认等于店铺授权时间
     * 充值后店铺小于等于3个，按充值时间，否则不变
     */
    @DbColumn(value = "deduct_time")
    private Date deductTime;

    /**
     * 广告授权刷新时间
     */
    @DbColumn(value = "ad_auth_sync_next_time")
    private LocalDateTime adAuthSyncNextTime;

    private Integer emailId;                //邮箱id
    private String email;                    //邮箱地址
    private Boolean emailValid;                //邮箱授权是否有效
    private String spAuthTimeStr;            //店铺授权时间字符串
    private String adAuthTimeStr;            //广告授权时间字符串

    private String taxNumber;               // 税号，2021年7月1号，欧洲税改，需要传税号。

    private String type;

    /**
     * shopauth转成json时需要字段
     */
    private String warehouseName;
    private String marketplaceName;
    private String marketplace;
    private String regionName;
    private String spStatusName;
    private String adStatusName;

    public LocalDateTime getAdAuthSyncNextTime() {
        return adAuthSyncNextTime;
    }

    public void setAdAuthSyncNextTime(LocalDateTime adAuthSyncNextTime) {
        this.adAuthSyncNextTime = adAuthSyncNextTime;
    }


    public void setId(Integer value) {
        this.id = value;
    }

    public Integer getId() {
        return this.id;
    }

    public void setPuid(Integer value) {
        this.puid = value;
    }

    public Integer getPuid() {
        return this.puid;
    }

    public void setName(String value) {
        this.name = value;
    }

    public String getName() {
        return this.name;
    }

    public void setSellingPartnerId(String value) {
        this.sellingPartnerId = value;
    }

    public String getSellingPartnerId() {
        return this.sellingPartnerId;
    }

    public void setRegion(String value) {
        this.region = value;
    }

    public String getRegion() {
        return this.region;
    }

    public void setMarketplaceId(String value) {
        this.marketplaceId = value;
    }

    public String getMarketplaceId() {
        return this.marketplaceId;
    }

    public void setMwsAuthToken(String value) {
        this.mwsAuthToken = value;
    }

    public String getMwsAuthToken() {
        return this.mwsAuthToken;
    }

    public void setSpRefreshToken(String value) {
        this.spRefreshToken = value;
    }

    public String getSpRefreshToken() {
        return this.spRefreshToken;
    }

    public void setSpAccessToken(String value) {
        this.spAccessToken = value;
    }

    public String getSpAccessToken() {
        return this.spAccessToken;
    }

    public void setSpAuthTime(Date value) {
        this.spAuthTime = value;
    }

    public Date getSpAuthTime() {
        return this.spAuthTime;
    }

    public void setAdRefreshToken(String value) {
        this.adRefreshToken = value;
    }

    public String getAdRefreshToken() {
        return this.adRefreshToken;
    }

    public void setAdAccessToken(String value) {
        this.adAccessToken = value;
    }

    /**
     * 后续不再直接使用
     *
     * @return
     */
    @Deprecated
    public String getAdAccessToken() {
        return this.adAccessToken;
    }

    public void setAdAuthTime(Date value) {
        this.adAuthTime = value;
    }

    public Date getAdAuthTime() {
        return this.adAuthTime;
    }

    public void setCreateId(Integer value) {
        this.createId = value;
    }

    public Integer getCreateId() {
        return this.createId;
    }

    public void setUpdateId(Integer value) {
        this.updateId = value;
    }

    public Integer getUpdateId() {
        return this.updateId;
    }

    public String getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(String spStatus) {
        this.spStatus = spStatus;
    }

    public String getAdStatus() {
        return adStatus;
    }

    public void setAdStatus(String adStatus) {
        this.adStatus = adStatus;
    }

    public Integer getEmailId() {
        return emailId;
    }

    public void setEmailId(Integer emailId) {
        this.emailId = emailId;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getEmailValid() {
        return emailValid;
    }

    public void setEmailValid(Boolean emailValid) {
        this.emailValid = emailValid;
    }

    public String getSpAuthTimeStr() {
        return spAuthTimeStr;
    }

    public void setSpAuthTimeStr(String spAuthTimeStr) {
        this.spAuthTimeStr = spAuthTimeStr;
    }

    public String getAdAuthTimeStr() {
        return adAuthTimeStr;
    }

    public void setAdAuthTimeStr(String adAuthTimeStr) {
        this.adAuthTimeStr = adAuthTimeStr;
    }

    public String getTaxNumber() {
        return taxNumber;
    }

    public void setTaxNumber(String taxNumber) {
        this.taxNumber = taxNumber;
    }

    public String getSpStatusName() {
        if (spStatus != null) {
            return ShopSpStatusEnum.getSpTypeByName(spStatus).getChineseName();
        }
        return null;
    }

    public String getAdStatusName() {
        if (adStatus != null) {
            return ShopAdStatusEnum.getAdTypeByName(adStatus).getChineseName();
        }
        return null;
    }

    public String getRegionName() {
        if (region != null) {
            switch (region) {
                case "na":
                    return "北美区";
                case "eu":
                    return "欧洲区";
                case "fe":
                    return "远东区";
                default:
                    return "";
            }
        }
        return null;
    }


    public String getWarehouseName() {
        if (StringUtils.isBlank(marketplaceId)) {
            return "";
        }
        AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
        if (endpoint != null) {
            return this.name + "-" + endpoint.getMarketplaceCN() + "仓";
        }
        return this.name + "仓";
    }

    public String getMarketplace() {
        if (marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplace().toLowerCase();
        }
        return null;
    }

    public String getMarketplaceName() {
        if (marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplaceCN();
        }
        return null;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getDeductTime() {
        return deductTime;
    }

    public void setDeductTime(Date deductTime) {
        this.deductTime = deductTime;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }
}