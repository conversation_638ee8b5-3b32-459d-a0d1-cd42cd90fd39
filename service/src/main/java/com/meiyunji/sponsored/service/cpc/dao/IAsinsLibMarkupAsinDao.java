package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.rpc.asins.AsinPageVo;
import com.meiyunji.sponsored.service.cpc.bo.KeywordLibMarkupAsinBo;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinLibMarkupAsin;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdAsinsLib;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibAsinPageListQo;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibAsinTagPageListVo;

import java.util.List;

/**
 * @author: ys
 * @date: 2024/12/3 15:24
 * @describe:
 */
public interface IAsinsLibMarkupAsinDao extends IBaseShardingDao<AmazonAdAsinLibMarkupAsin> {
    int batchInsert(List<AmazonAdAsinLibMarkupAsin> list);

    List<AmazonAdAsinLibMarkupAsin> listByUidAndAsinIds(Integer puid, List<Integer> uidList, List<Long> asinIdList);

    List<Long> listAsinIdByAsin(Integer puid, List<Integer> uid, String marketplaceId, String asin);

    int deleteByAsinLibId(int puid, int uid, List<Long> ids);

    List<AmazonAdAsinLibMarkupAsin> getListByAsinsLibId(Integer puid, List<Integer> uidList, List<Long> idList);

    Page<AmazonAdAsinLibMarkupAsin> getAsinTagPageList(Integer puid, List<Integer> uidList, List<String> marketplaceIdList, AsinLibAsinTagPageListVo req);

    int deleteByKeywordsLibIdAndAsin(int puid, List<Integer> uidList, List<Long> idList, String marketplaceId, String asin);
}
