package com.meiyunji.sponsored.service.newDashboard.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-04-22  16:20
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DashboardShopRankReqVo extends DashboardBaseReqVo {
    @ApiModelProperty("是否勾选占比，1是0否")
    private Integer percent = 0;
    @ApiModelProperty("是否勾选同比，1是0否")
    private Integer yoy = 0;
    @ApiModelProperty("是否勾选环比，1是0否")
    private Integer mom = 0;
    @ApiModelProperty("查询条件")
    private Integer queryField;
    @ApiModelProperty("指标条件")
    private String dataField;
    @ApiModelProperty("排序指标条件")
    private String orderByField;
    @ApiModelProperty("排序方式")
    private String orderBy;
    @ApiModelProperty("排序取名次数量")
    private Integer limit;
    @ApiModelProperty("是否图表数据，1是0否，默认否")
    private Integer isChart = 0;

    /**
     * 列表排序字段
     */
    private String  listOrderField;
    /**
     * 列表排序类型 desc，asc
     */
    private String  listOrderType;
}
