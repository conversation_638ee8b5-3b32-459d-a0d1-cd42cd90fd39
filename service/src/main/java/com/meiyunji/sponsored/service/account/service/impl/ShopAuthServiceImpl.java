package com.meiyunji.sponsored.service.account.service.impl;

import com.amazon.advertising.auth.AmazonAdvertisingAuthClient;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.meiyunji.amazon.sellerpartner.auth.AmazonAuthClient;
import com.meiyunji.amazon.sellerpartner.auth.AmazonAuthResponse;
import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sellfox.aadas.api.enumeration.RegionPb;
import com.meiyunji.sellfox.aadas.api.service.AadasApiGrpc;
import com.meiyunji.sellfox.aadas.api.service.GetAccessTokenRequestPb;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.StatusCode;
import com.meiyunji.sponsored.common.springjdbc.BaseServiceImpl;
import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.vo.ShopAuthSellerInfoVo;
import io.grpc.ManagedChannel;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.shade.com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.meiyunji.sellfox.aadas.api.service.GetAccessTokenResponsePb.*;

/**
 * <AUTHOR>
 */
@Service
public class ShopAuthServiceImpl extends BaseServiceImpl<ShopAuth> implements IShopAuthService {

    @Autowired
    private IShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;

    @Autowired
    private RedisService redisService;

    @Qualifier("taskManagedChannel")
    @Autowired
    private ManagedChannel taskManagedChannel;
    @Autowired
    private IVcShopAuthDao vcShopAuthDao;


    // 创建一个缓存实例，设置最大容量为 500000，写入后 2 天过期
    private final Cache<Integer, String> shopTypeCache = CacheBuilder.newBuilder()
            .maximumSize(500000)
            .expireAfterWrite(2, TimeUnit.DAYS)
            .build();


    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;



    @Override
    public int update(ShopAuth shopAuth) {
        return shopAuthDao.updateById(shopAuth);
    }


    @Override
    public String refreshCpcAuth(ShopAuth shop) {
        String accessToken = refreshAdToken(shop);
        if (StringUtils.isNotEmpty(accessToken)) {
            shop.setAdAccessToken(accessToken);
        }
        return accessToken;
    }

    @Override
    public int getCpcAuth(int puid, Integer shopId, String marketplaceId) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop != null && StringUtils.isNotBlank(shop.getAdRefreshToken())) {
            //检查是否有对应站点的配置文件
            if (amazonAdProfileDao.exist(puid, shopId, marketplaceId)) {
                return 1;
            }
        }
        return 0;
    }

    private String refreshAndGetAdAccessToken(Integer puid, Integer id, String oldAccessToken) {
        for (int i = 0; i < 3; i++) {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(id, puid);
            if (shop == null) {
                return null;
            }
            String newAccessToken = getAdAccessToken(shop, oldAccessToken);
            if (null != newAccessToken) {
                return newAccessToken;
            }
            String key = String.format(RedisConstant.SHOP_AD_REFRESH_TOKEN, shop.getSellingPartnerId(), shop.getRegion());
            // 先拿到锁
            if (redisService.lock(key, 60 * 1000)) {
                try {
                    // 验证
                    shop = shopAuthDao.getScAndVcByIdAndPuid(id, shop.getPuid());
                    newAccessToken = getAdAccessToken(shop, oldAccessToken);
                    if (null != newAccessToken) {
                        return newAccessToken;
                    }
                    //刷新token
                    AmazonAdvertisingAuthClient clinet = AmazonAdvertisingAuthClient.getInstance();
                    com.amazon.advertising.auth.AmazonAuthResponse response = clinet.refreshAccessToken(shop.getAdRefreshToken());
                    if (response != null && response.getStatusCode() == 200 && StringUtils.isNotBlank(response.getAccessToken())) {
                        newAccessToken = response.getAccessToken();
                        if (ShopTypeEnum.VC.getCode().equals(shop.getType())) {
                            vcShopAuthDao.updateAdAccessToken(shop.getPuid(), shop.getSellingPartnerId(), shop.getRegion(), response.getAccessToken());
                        } else {
                            shopAuthDao.updateAdAccessToken(shop.getPuid(), shop.getSellingPartnerId(), shop.getRegion(), response.getAccessToken());
                        }
                        return newAccessToken;
                    }
                } finally {
                    // 释放锁
                    redisService.releaseLock(key);
                }
            } else {
                try {
                    Thread.sleep(500);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return null;
    }

    private String getAdAccessToken(ShopAuth shop, String oldAccessToken) {
        String newAccessToken = shop.getAdAccessToken();
        if (!oldAccessToken.equals(newAccessToken)) {
            return newAccessToken;
        }
        return null;
    }

    private String refreshSpAccessToken(Integer puid, String sellingPartnerId, RegionEnum region, String spRefreshToken) {
        AmazonAuthClient client = new AmazonAuthClient(region);
        AmazonAuthResponse response = client.refreshAccessToken(spRefreshToken);
        if (response.getStatusCode() == StatusCode.success) {
            //刷新token成功，更新店铺数据
            logger.info("puid={} sellingPartnerId={} client.refreshAndGetSpAccessToken refresh success", puid, sellingPartnerId);
            shopAuthDao.updateSpAccessToken(puid, sellingPartnerId, region, response.getAccessToken());
            return response.getAccessToken();
        } else {
            return null;
        }
    }

    private String getNewAccessToken(ShopAuth shop, String oldAccessToken) {
        String newAccessToken = shop.getSpAccessToken();

        if (!oldAccessToken.equals(newAccessToken)) {
            return newAccessToken;
        }
        return null;
    }

    @Override
    public String getAdToken(ShopAuth shopAuth) {

        if (shopAuth == null) {
            logger.info("get ad token shop is null");
            return null;
        }
        StopWatch stopWatch = new StopWatch();
        String logKey = "get ad token puid:" + shopAuth.getPuid() + "shopId" + shopAuth.getId();
        stopWatch.start(logKey + ",total time");
        if (!dynamicRefreshConfiguration.verifyAdTokenGray(shopAuth.getPuid())) {
            return shopAuth.getAdAccessToken();
        }
        stopWatch.stop();
        logger.info("get ad token total time {} ", stopWatch.prettyPrint());
        String token = getAadasAdToken(shopAuth, false);
        if (StringUtils.isBlank(token)) {
            logger.error("shopId ={}, puid ={}, error token is null", shopAuth.getId(), shopAuth.getPuid());
        }
        return token;
    }

    @Override
    public Map<Integer, String> getShopNameMap(Integer puid, List<Integer> shopIds) {
        List<ShopAuth> shops = shopAuthDao.listAllByIds(puid, shopIds);
        if (shops != null && shops.size() > 0) {
            return shops.stream().collect(Collectors.toMap(ShopAuth::getId, ShopAuth::getName, (e1, e2) -> e2));
        }
        return new HashMap<>();
    }

    private String refreshAdToken(ShopAuth shopAuth) {

        if (shopAuth == null) {
            logger.info("get ad token shop is null");
            return null;
        }
        StopWatch stopWatch = new StopWatch();
        String logKey = "get refresh ad token puid:" + shopAuth.getPuid() + "shopId" + shopAuth.getId();
        stopWatch.start(logKey + ",total time");
        if (!dynamicRefreshConfiguration.verifyAdTokenGray(shopAuth.getPuid())) {
            return refreshAndGetAdAccessToken(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getAdAccessToken());
        }
        stopWatch.stop();
        logger.info("get refresh ad token total time {} ", stopWatch.prettyPrint());
        String token = getAadasAdToken(shopAuth, true);
        if (StringUtils.isBlank(token)) {
            logger.error("shopId ={}, puid ={}, error token is null", shopAuth.getId(), shopAuth.getPuid());
        }
        return token;
    }

    private String getAadasAdToken(ShopAuth shopAuth, boolean isForceRefreshToken) {
        String cacheKey = String.format(RedisConstant.SPONSORED_AD_TOKEN_SELLER_REGION_CACHE_KEY, shopAuth.getSellingPartnerId(), shopAuth.getRegion());
        StopWatch stopWatch = new StopWatch();
        String logKey = "get ad token puid:" + shopAuth.getPuid() + "shopId" + shopAuth.getId();
        stopWatch.start(logKey + ",redis time");
        if (!isForceRefreshToken) {
            try {
                String token = redisService.getString(cacheKey);
                if (StringUtils.isNotBlank(token)) {
                    stopWatch.stop();
                    logger.info("get ad token elapsed processing time {} ", stopWatch.prettyPrint());
                    return token;
                }
            } catch (Exception e) {
                logger.info("get ad token from redis fail",e);
            }
        }
        stopWatch.stop();
        stopWatch.start(logKey + ", call aadas time");
        AadasApiGrpc.AadasApiBlockingStub aadasApiBlockingStub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
        GetAccessTokenRequestPb.GetAccessTokenRequest.Builder builder = GetAccessTokenRequestPb.GetAccessTokenRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setRegion(RegionPb.Region.valueOf(Marketplace.fromId(shopAuth.getMarketplaceId()).getRegion().name()));
        builder.setIsForceRefresh(false);
        GetAccessTokenResponse accessToken = null;
        try {
            accessToken = aadasApiBlockingStub.getAccessToken(builder.build());
        } catch (Exception e) {
            logger.info(logKey + " error {}", e);
        }
        stopWatch.stop();
        logger.info("get ad token elapsed processing time {} ", stopWatch.prettyPrint());
        if (accessToken == null) {
            String amazonAdToken = getAmazonAdToken(shopAuth);
            if (StringUtils.isNotBlank(amazonAdToken)) {
                try {
                    redisService.set(cacheKey, amazonAdToken, 3000, TimeUnit.SECONDS);
                } catch (Exception e) {
                    logger.error(logKey + "，save redis fail ", e);
                }
                return amazonAdToken;
            }
            logger.info("get ad token failure puid : {}, shopId : {}", shopAuth.getPuid(), shopAuth.getId());
            return null;
        }
        LocalDateTime utc = LocalDateTime.now(ZoneId.of("UTC"));
        long toEpochSecond = utc.toEpochSecond(ZoneOffset.UTC);
        long l = accessToken.getExpireTime() - toEpochSecond - (5L * 60L);
        if (l > 0) {
            try {
                redisService.set(cacheKey, accessToken.getAccessToken(), l, TimeUnit.SECONDS);
            } catch (Exception e) {
                logger.error(logKey + "，save redis fail ", e);
            }
        }
        return accessToken.getAccessToken();
    }

    private String getAmazonAdToken(ShopAuth shopAuth){
        AmazonAdvertisingAuthClient clinet = AmazonAdvertisingAuthClient.getInstance();
        com.amazon.advertising.auth.AmazonAuthResponse response = clinet.refreshAccessToken(shopAuth.getAdRefreshToken());
        if (response != null && response.getStatusCode() == 200 && StringUtils.isNotBlank(response.getAccessToken())) {
            return response.getAccessToken();
        }
        return null;
    }

    @Override
    public List<Integer> checkAuthByShopIds(Integer puid, List<Integer> shopIds) {
        return shopAuthDao.listAllValidOrExpireAdShopByIds(puid, shopIds);
    }

    @Override
    public ShopAuth getScAndVcById(int shopId) {
        return shopAuthDao.getScAndVcById(shopId);
    }


    /**
     *
     * @param shopIds
     * @return
     */
    @Override
    public Map<Integer, String> getShopType(List<Integer> shopIds) {
        if (CollectionUtils.isNotEmpty(shopIds)) {
            HashSet<Integer> shopIdSet = Sets.newHashSet(shopIds);
            ImmutableMap<Integer, String> allPresent = shopTypeCache.getAllPresent(shopIdSet);
            if (MapUtils.isNotEmpty(allPresent) && allPresent.size() == shopIdSet.size()) {
                return Maps.newHashMap(allPresent);
            } else {
                HashMap<Integer, String> integerStringHashMap = new HashMap<>();
                if (MapUtils.isNotEmpty(allPresent)) {
                    shopIdSet.removeAll(allPresent.keySet());
                    integerStringHashMap = Maps.newHashMap(allPresent);
                }
                if (CollectionUtils.isNotEmpty(shopIdSet)) {
                    List<ShopAuth> scAndVcByIds = shopAuthDao.getScAndVcByIds(Lists.newArrayList(shopIdSet));
                    if (CollectionUtils.isNotEmpty(scAndVcByIds)) {
                        HashMap<Integer, String> finalIntegerStringHashMap = integerStringHashMap;
                        scAndVcByIds.forEach(e-> {
                            shopTypeCache.put(e.getId(), e.getType());
                            finalIntegerStringHashMap.put(e.getId(), e.getType());
                        });
                    }
                }
                return integerStringHashMap;
            }
        }
        return Maps.newHashMap();
    }

    @Override
    public ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid) {
        return shopAuthDao.getVcAndScByIdAndPuid(id, puid);
    }

    @Override
    public List<Integer> getShopByMid(int puid, String marketplaceId) {
        return shopAuthDao.getScAndVcShopByMid(puid, marketplaceId);
    }

    @Override
    public List<ShopAuth> getListByPuid(int puid) {
        return shopAuthDao.listScAndVcAllByPuid(puid);
    }

    @Override
    public List<ShopAuth> getAllId() {
        return shopAuthDao.getScAndVcAllId();
    }

    @Override
    public List<Integer> getAllShopId() {
        return shopAuthDao.getScAndVcAllShopId();
    }

    @Override
    public List<Integer> getAllShopId(int puid) {
        return shopAuthDao.getScAndVcAllShopId(puid);
    }

    @Override
    public List<Integer> getAllAdShopId(int puid) {
        return shopAuthDao.getScAndVcAllAdShopId(puid);
    }

    @Override
    public List<String> getAllSellerId(Integer puid) {
        return shopAuthDao.getScAndVcAllSellerId(puid);
    }

    @Override
    public List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds) {
        return shopAuthDao.listScAndVcValidByIds(puid, shopIds);
    }

    @Override
    public List<ShopAuth> listAllValidAdShop(Integer puid) {
        return shopAuthDao.listScAndVcAllValidAdShop(puid);
    }

    @Override
    public List<String> getSellerIdByPuid(Integer puid) {
        return shopAuthDao.getScAndVcSellerIdByPuid(puid);
    }

    @Override
    public List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList) {
        return shopAuthDao.listScAndVcByPuidAndMarketplace(puid, mkList, shopList);
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        return shopAuthDao.getScAndVcAllValidAdShopByLimit(puid, shopId, start, limit);
    }

    @Override
    public List<ShopAuth> getByPuidAndMarketplaceIds(int puid, String[] marketplaceIds) {
        return shopAuthDao.getScAndVcByPuidAndMarketplaceIds(puid, marketplaceIds);
    }

    @Override
    public List<String> marketplaceIdListByShopIds(List<Integer> shopIdList) {
        return shopAuthDao.marketplaceIdScAndVcListByShopIds(shopIdList);
    }

    @Override
    public List<Integer> IdListByShopIds(List<Integer> shopIdList) {
        return shopAuthDao.IdListScAndVcByShopIds(shopIdList);
    }

    @Override
    public List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList) {
        return shopAuthDao.getScAndVcAuthShopByShopIdList(puid, shopIdList);
    }

    @Override
    public List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        return shopAuthDao.getScAndVcBySellerIdsAndMarketplaceIds(sellerIds, marketplaceIds);
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puid) {
        return shopAuthDao.getScAndVcAdAuthShopByShopIdList(puid);
    }

    @Override
    public List<ShopAuth> getAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId) {
        return shopAuthDao.getScAndVcAdAuthShopByShopId(puid, shopId);
    }

    @Override
    public List<ShopAuthBo> getShopAuthBoByIds(int puid, List<Integer> idList) {
        return shopAuthDao.getScAndVcShopAuthBoByIds(puid, idList);
    }

    @Override
    public List<ShopAuthBo> getShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        return shopAuthDao.getScAndVcShopAuthBoByMarketPlaceAndIds(puid, idList, marketplaceIdList);
    }

    @Override
    public List<ShopAuthBo> getAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        return shopAuthDao.getScAndVcAuthShopBoByMarketPlaceAndIds(puid, idList, marketplaceIdList);
    }

    @Override
    public List<Integer> queryRandomSequentList() {
        return shopAuthDao.queryScAndVcRandomSequentList();
    }


    @Override
    public List<ShopAuthSellerInfoVo> getSellerInfoByIdList(ArrayList<Integer> integers) {
        return shopAuthDao.getScAndVcSellerInfoByIdList(integers);
    }

    @Override
    public List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus) {
        return shopAuthDao.getScAndVcShopList(puid, shopIds, adStatus);
    }

    @Override
    public List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        return shopAuthDao.getScAndVcBySellerIdsAndMarketplaceIds(sellerIds, marketplaceIds);
    }

    @Override
    public List<ShopAuth> getScAndVcShopListByShopIdsAndAdStatus(List<Integer> shopIds, String adStatus) {
        return shopAuthDao.getScAndVcShopListByShopIdsAndAdStatus(shopIds, adStatus);
    }

    @Override
    public List<ShopAuth> getScAndVcByIds(List<Integer> ids) {
        return shopAuthDao.getScAndVcByIds(ids);
    }

    @Override
    public ShopAuth getScAndVcByIdAndPuid(int id, int puid) {
        return shopAuthDao.getScAndVcByIdAndPuid(id, puid);
    }

    @Override
    public List<ShopAuthBo> getScAndVcShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList) {
        return shopAuthDao.getScAndVcShopAuthBoByMarketPlaceAndIds(puid, idList, marketplaceIdList);
    }

    @Override
    public List<ShopAuth> listAllByIds(int puid, List<Integer> shopIds) {
        return shopAuthDao.listScAndVcAllByIds(puid, shopIds);
    }

    @Override
    public List<ShopAuth> getScAndVcAuthShopByShopIdList(int puid, List<Integer> shopIdList) {
        return shopAuthDao.getScAndVcAuthShopByShopIdList(puid, shopIdList);
    }

    @Override
    public ShopAuth getBySellerIdAndMarketplaceId(String sellerId, String marketplaceId) {
        return shopAuthDao.getScAndVcBySellerIdAndMarketplaceId(sellerId, marketplaceId);
    }

    @Override
    public List<ShopAuth> listScAndVcAllByPuid(int puid) {
        return shopAuthDao.listScAndVcAllByPuid(puid);
    }

    @Override
    public ShopAuth getByIdAndPuid(Object id, Integer puid) {
        return shopAuthDao.getVcAndScByIdAndPuid(id, puid);
    }




    @Override
    public ShopAuth getById(Object id){
        return shopAuthDao.getScAndVcById((Integer) id);
    }

    @Override
    public List<ShopAuth> listByPuid(Integer puid) {
        return shopAuthDao.listScAndVcAllByPuid(puid);
    }



}