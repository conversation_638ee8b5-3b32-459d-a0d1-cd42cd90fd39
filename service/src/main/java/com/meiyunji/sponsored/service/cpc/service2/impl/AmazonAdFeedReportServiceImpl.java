package com.meiyunji.sponsored.service.cpc.service2.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.grpc.common.HourlyWeeklySuperpositionCommonReq;
import com.meiyunji.sponsored.grpc.common.HourlyWeeklySuperpositionTargetReq;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.bo.FeedTargetCampaignDto;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.function.FourFunction;
import com.meiyunji.sponsored.service.function.ThFunction;
import com.meiyunji.sponsored.service.function.TwFunction;
import com.meiyunji.sponsored.service.doris.dao.IDimCurrencyRateDao;
import com.meiyunji.sponsored.service.doris.po.DimCurrencyRate;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.handler.ViewManageHandler;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.*;
import com.meiyunji.sponsored.service.util.HourlyAndWeeklyDataHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AmazonAdFeedReportServiceImpl implements IAmazonAdFeedReportService {

    private final IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;

    private final IAmazonAdProductDao amazonAdProductDao;

    private final MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;

    private final IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    private final IAmazonSdAdGroupDao amazonSdAdGroupDao;

    private final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;

    private final IAmazonMarketingStreamSubscriberDao amazonMarketingStreamSubscriberDao;

    private final IScVcShopAuthDao shopAuthDao;

    private final ViewManageHandler viewManageHandler;

    private final IDimCurrencyRateDao dimCurrencyRateDao;

    @Override
    public List<AdReportHourlyVO> listByAdHourCampaignTypeAndCostType(ShopAuth shopAuth, AdHourReportRequest param, String campaignType, String costType) {
        if (shopAuth == null) {
            return new ArrayList<>();
        }
        log.info("listByAdHourCampaignTypeAndCostType type = {}", param);
        //产品透视需要查adid再过滤一层
        List<String> adIds = new ArrayList<>();
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            ViewBaseParam viewBaseParam = new ViewBaseParam();
            viewBaseParam.setPuid(shopAuth.getPuid());
            viewBaseParam.setShopIdList(Collections.singletonList(shopAuth.getId()));
            viewBaseParam.setMarketplaceId(shopAuth.getMarketplaceId());
            viewBaseParam.setSearchType(param.getFindType());
            viewBaseParam.setSearchValue(param.getFindValue());
            viewBaseParam.setType(campaignType);
            adIds = viewManageHandler.queryProductBoLists(viewBaseParam.getPuid(), viewBaseParam)
                    .stream().map(AmazonAdProductPerspectiveBO::getAdId).distinct().collect(Collectors.toList());
        }
        AdPageBasicData pageBasicInfo = param.getPageBasic();
        FeedHourlySelectDTO feedHourlySelectDTO = build(LocalDate.parse(pageBasicInfo.getStartDate()),
                LocalDate.parse(pageBasicInfo.getEndDate()),
                campaignType,
                shopAuth.getSellingPartnerId(),
                shopAuth.getMarketplaceId(),
                param.getCampaignId(),
                param.getWeeks(),
                param.getGroupId(),
                adIds,
                shopAuth);
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listByHourly(feedHourlySelectDTO);
        List<AmazonMarketingStreamData> streamDataCompare = new ArrayList<>();
        // 查看是否比较
        if (Integer.valueOf(1).equals(pageBasicInfo.getIsCompare().getValue())) {
            feedHourlySelectDTO.setStart(LocalDate.parse(pageBasicInfo.getStartDateCompare()));
            feedHourlySelectDTO.setEnd(LocalDate.parse(pageBasicInfo.getEndDateCompare()));
            streamDataCompare = amazonMarketingStreamDataDao.listByHourly(feedHourlySelectDTO);
        }
        List<AdReportHourlyVO> voList = new ArrayList<>();
        if (Constants.SB.equalsIgnoreCase(campaignType)) {
            buildReportHourlyVO(streamData, streamDataCompare, voList, true, this::buildSbAdReportHourlyVOBasic, false);
        }
        if (Constants.SD.equalsIgnoreCase(campaignType)) {
            if (Constants.SD_REPORT_CPC.equalsIgnoreCase(costType)) {
                buildReportHourlyVO(streamData, streamDataCompare, voList, true, this::buildSdCpcAdReportHourlyVOBasic, false);
            }
            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(costType)) {
                buildReportHourlyVO(streamData, streamDataCompare, voList, false, this::buildSdVcpmAdReportHourlyVOBasic, true);
            }
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            HourlyAndWeeklyDataHandler.filterSumMetricData(voList, adMetricDto);
            HourlyAndWeeklyDataHandler.filterMetricData(voList, adMetricDto);
            voList.sort(Comparator.comparing(AdReportHourlyVO::getHour));
        }
        return voList;
    }

    @Override
    public List<AdReportWeeklyDayVO> listAdReportWeeklyDayVOByAdHourReportRequest(ShopAuth shopAuth, AdHourReportRequest param, String campaignType, String costType) {
        if (shopAuth == null) {
            return new ArrayList<>();
        }
        log.info("listAdReportWeeklyDayVOByAdHourReportRequest type = {}", param);
        AdPageBasicData pageBasicInfo = param.getPageBasic();
        FeedHourlySelectDTO feedHourlySelectDTO = build(LocalDate.parse(pageBasicInfo.getStartDate()),
                LocalDate.parse(pageBasicInfo.getEndDate()),
                campaignType,
                shopAuth.getSellingPartnerId(),
                shopAuth.getMarketplaceId(),
                param.getCampaignId(),
                null,
                param.getGroupId(),
                null,
                shopAuth);
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listWeekByHourly(feedHourlySelectDTO);
        // 是否比较
        List<AmazonMarketingStreamData> streamDataCompare = Lists.newArrayList();
        if (pageBasicInfo.getIsCompare().getValue() == 1) {
            feedHourlySelectDTO.setStart(LocalDate.parse(pageBasicInfo.getStartDateCompare()));
            feedHourlySelectDTO.setEnd(LocalDate.parse(pageBasicInfo.getEndDateCompare()));
            streamDataCompare = amazonMarketingStreamDataDao.listWeekByHourly(feedHourlySelectDTO);
        }

        List<AdReportWeeklyDayVO> voList = new ArrayList<>();
        if (Constants.SB.equalsIgnoreCase(campaignType)) {
            buildAdWeekReportHourlyVO(streamData, streamDataCompare, voList, true, this::buildSbAdReportHourlyVOBasic);
        }
        if (Constants.SD.equalsIgnoreCase(campaignType)) {
            if (Constants.SD_REPORT_CPC.equalsIgnoreCase(costType)) {
                buildAdWeekReportHourlyVO(streamData, streamDataCompare, voList, true, this::buildSdCpcAdReportHourlyVOBasic);
            }
            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(costType)) {
                buildAdWeekReportHourlyVO(streamData, streamDataCompare, voList, false, this::buildSdVcpmAdReportHourlyVOBasic);
            }
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            sumMetricData(voList, adMetricDto);
            metricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdCampaignHourVo> listAdCampaignHourByAdHourCampaignTypeAndCostType(ShopAuth shopAuth, CampaignHourParam param, String campaignType, String costType) {
        if (shopAuth == null) {
            return new ArrayList<>();
        }
        FeedHourlySelectDTO feedHourlySelectDTO = build(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                campaignType,
                shopAuth.getSellingPartnerId(),
                shopAuth.getMarketplaceId(),
                param.getCampaignId(),
                param.getWeeks(),
                null,
                null,
                shopAuth);
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listByHourly(feedHourlySelectDTO);
        List<AmazonMarketingStreamData> streamDataCompare = new ArrayList<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            feedHourlySelectDTO.setStart(LocalDate.parse(param.getStartDateCompare()));
            feedHourlySelectDTO.setEnd(LocalDate.parse(param.getEndDateCompare()));
            streamDataCompare = amazonMarketingStreamDataDao.listByHourly(feedHourlySelectDTO);
        }
        List<AdCampaignHourVo> voList = new ArrayList<>();
        if (Constants.SB.equalsIgnoreCase(campaignType)) {
            buildExportHourlyVO(streamData, streamDataCompare, voList, true, this::buildAdCampaignHourVoSb);
        }
        if (Constants.SD.equalsIgnoreCase(campaignType)) {
            if (Constants.SD_REPORT_CPC.equalsIgnoreCase(costType)) {
                buildExportHourlyVO(streamData, streamDataCompare, voList, true, this::buildAdCampaignHourVoSdCpc);
            }
            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(costType)) {
                buildExportHourlyVO(streamData, streamDataCompare, voList, false, this::buildAdCampaignHourVoSdVcpm);
            }
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
            voList.sort(Comparator.comparing(AdCampaignHourVo::getHour));
        }
        return voList;
    }

    @Override
    public List<AdCampaignHourVo> listAggregateWeekList(List<ShopAuth> shopAuths, List<String> aggregateIds, CampaignHourParam param) {
        FeedHourlySelectDTO feedHourlySelectDTO = buildMultiple(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                null,
                param.getWeeks(),
                shopAuths);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            compareBuild(feedHourlySelectDTO, LocalDate.parse(param.getStartDateCompare()), LocalDate.parse(param.getEndDateCompare()));
        }
        List<List<AdCampaignHourVo>> list = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                () -> aggregateIds, feedHourlySelectDTO, this::multiThreadQueryWeekAms);
        return ReportChartUtil.getCampaignWeekReportVos(list);
    }

    @Override
    public List<AdCampaignHourVo> listAggregateHourList(List<ShopAuth> shopAuths, List<String> aggregateIds, CampaignAggregateHourParamVO param) {
        FeedHourlySelectDTO feedHourlySelectDTO = buildMultiple(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                param.getCampaignId(),
                param.getWeeks(),
                shopAuths
                );
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            compareBuild(feedHourlySelectDTO, LocalDate.parse(param.getStartDateCompare()), LocalDate.parse(param.getEndDateCompare()));
        }
        // 并发查询
        List<List<AdCampaignHourVo>> list = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                () -> aggregateIds, feedHourlySelectDTO, this::multiThreadQueryAms);
        List<AdCampaignHourVo> adCampaignHourVos = ReportChartUtil.getCampaignHourReportVos(list);
        if (CollectionUtils.isNotEmpty(adCampaignHourVos)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(adCampaignHourVos, adMetricDto);
            filterMetricData(adCampaignHourVos, adMetricDto);
            adCampaignHourVos.sort(Comparator.comparing(AdCampaignHourVo::getHour));
        }
        return adCampaignHourVos;
    }

    @Override
    public List<AdCampaignHourVo> listAggregateHourList(List<ShopAuth> shopAuths, CampaignAggregateHourParam param) {
        //获取小时级数据
        CampaignHourlyReportSelectDto campaignHourlyReportSelectDto = buildCampaignHourlyReportSelectDto(shopAuths, param);
        List<List<AmazonMarketingStreamData>> datas = multiThreadQueryAndMergeUtil.multiThreadQuery(
                ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                param::getCampaignIdList,
                campaignHourlyReportSelectDto,
                this::multiThreadQueryAmsBySellerIds);
        List<AmazonMarketingStreamData> resultList = mergeAggregateCampaignHourlyReportResult(datas);
        List<List<AmazonMarketingStreamData>> compareResult;
        List<AmazonMarketingStreamData> compareResponse;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            campaignHourlyReportSelectDto.setStartDate(getSellerIdsDataStartTime(campaignHourlyReportSelectDto.getSellerIds(), param.getMarketplaceId(), param.getStartDateCompare()));
            campaignHourlyReportSelectDto.setEndDate(param.getEndDateCompare());
            //对比汇总数据页需要多线程分片查询
            compareResult = multiThreadQueryAndMergeUtil.multiThreadQuery(
                    ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                    param::getCampaignIdList,
                    campaignHourlyReportSelectDto,
                    this::multiThreadQueryAmsBySellerIds);
            compareResponse = mergeAggregateCampaignHourlyReportResult(compareResult);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(resultList, hourlyReportDataMap);
        List<AdCampaignHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdCampaignHourVo adCampaignHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adCampaignHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }

        return voList;
    }

    private AdCampaignHourVo handleVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare) {
        AdCampaignHourVo adCampaignHourVoCompare = convertTo(dataCompare);
        AdCampaignHourVo adCampaignHourVo = convertTo(data);
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adCampaignHourVo.getAdSale())
                .adSelfSale(adCampaignHourVo.getAdSelfSale())
                .adOtherSale(adCampaignHourVo.getAdOtherSale())
                .adOrderNum(adCampaignHourVo.getAdOrderNum())
                .selfAdOrderNum(adCampaignHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adCampaignHourVo.getOtherAdOrderNum())
                .adSaleNum(adCampaignHourVo.getAdSaleNum())
                .adSelfSaleNum(adCampaignHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adCampaignHourVo.getAdOtherSaleNum())
                .adCost(adCampaignHourVo.getAdCost())
                .clicks(adCampaignHourVo.getClicks())
                .impressions(adCampaignHourVo.getImpressions())
                .adCostPerClick(adCampaignHourVo.getAdCostPerClick())
                .cpa(adCampaignHourVo.getCpa())
                .acos(adCampaignHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getClicks()), BigDecimal.valueOf(100)),
                        BigDecimal.valueOf(adCampaignHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVo.getAdOrderNum()),
                        BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignHourVo.getClicks())))
                .roas(Objects.isNull(adCampaignHourVo.getAdSale()) || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdSale()) == 0 || BigDecimal.ZERO.compareTo(adCampaignHourVo.getAdCost()) == 0 ?
                        BigDecimal.ZERO : adCampaignHourVo.getAdSale().divide(adCampaignHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .viewableImpressions(adCampaignHourVo.getViewableImpressions())
                .ordersNewToBrand(adCampaignHourVo.getOrdersNewToBrand())
                .unitsOrderedNewToBrand(adCampaignHourVo.getUnitsOrderedNewToBrand())
                .salesNewToBrand(adCampaignHourVo.getSalesNewToBrand())
                .ordersNewToBrandPercentage(adCampaignHourVo.getOrdersNewToBrandPercentage())
                .unitsOrderedNewToBrandPercentage(adCampaignHourVo.getUnitsOrderedNewToBrandPercentage())
                .salesNewToBrandPercentage(adCampaignHourVo.getSalesNewToBrandPercentage())
                .vcpm(adCampaignHourVo.getVcpm())
                .vrt(adCampaignHourVo.getVrt())
                .vCtr(adCampaignHourVo.getVCtr())
                .advertisingUnitPrice(adCampaignHourVo.getAdvertisingUnitPrice())
                .advertisingProductUnitPrice(adCampaignHourVo.getAdvertisingProductUnitPrice())
                .advertisingOtherProductUnitPrice(adCampaignHourVo.getAdvertisingOtherProductUnitPrice())
                .clicksCompare(adCampaignHourVoCompare.getClicks())
                .impressionsCompare(adCampaignHourVoCompare.getImpressions())
                .adSaleNumCompare(adCampaignHourVoCompare.getAdSaleNum())
                .adSaleCompare(adCampaignHourVoCompare.getAdSale())
                .adCostCompare(adCampaignHourVoCompare.getAdCost())
                .adOrderNumCompare(adCampaignHourVoCompare.getAdOrderNum())
                .adCostPerClickCompare(adCampaignHourVoCompare.getAdCostPerClick())
                .acosCompare(adCampaignHourVoCompare.getAcos())
                .ctrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVoCompare.getClicks()), BigDecimal.valueOf(100)),
                        BigDecimal.valueOf(adCampaignHourVoCompare.getImpressions())))
                .cvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adCampaignHourVoCompare.getAdOrderNum()),
                        BigDecimal.valueOf(100)), BigDecimal.valueOf(adCampaignHourVoCompare.getClicks())))
                .roasCompare(Objects.isNull(adCampaignHourVoCompare.getAdSale()) || BigDecimal.ZERO.compareTo(adCampaignHourVoCompare.getAdSale()) == 0 || BigDecimal.ZERO.compareTo(adCampaignHourVoCompare.getAdCost()) == 0 ?
                        BigDecimal.ZERO : adCampaignHourVoCompare.getAdSale().divide(adCampaignHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP))
                .vcpmImpressions(adCampaignHourVo.getVcpmImpressions())
                .vcpmCost(adCampaignHourVo.getAdCost())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    private AdCampaignHourVo convertTo(AmazonMarketingStreamData data) {
        AdCampaignHourVo vo = new AdCampaignHourVo();
        if (data == null) {
            return vo;
        }
        vo.setWeekDay(data.getWeekday());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(getDefaultInteger(data.getViewableImpressions()).intValue());
        vo.setOrdersNewToBrand(getDefaultInteger(data.getAttributedOrdersNewToBrand14d()));
        vo.setUnitsOrderedNewToBrand(getDefaultInteger(data.getAttributedUnitsOrderedNewToBrand14d()));
        vo.setSalesNewToBrand(getDefaultBigDecimal(data.getAttributedSalesNewToBrand14d()));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum()), 2));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getOtherAdOrderNum())));
        vo.setVcpmImpressions(vo.getViewableImpressions() == null ? 0L : Long.valueOf(vo.getViewableImpressions()));
        vo.setVcpmCost(vo.getAdCost());
        return vo;
    }

    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMap(List<AmazonMarketingStreamData> statisticsByHourDataList,
                                                                                  Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourDataList)) {
            hourlyReportDataMap = statisticsByHourDataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private List<AmazonMarketingStreamData> mergeAggregateCampaignHourlyReportResult(List<List<AmazonMarketingStreamData>> resultList) {
        if (CollectionUtils.isEmpty(resultList)) {
            return Collections.emptyList();
        }
        return new ArrayList<>(resultList.stream().filter(CollectionUtils::isNotEmpty)
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(e -> LocalTime.parse(e.getTime(), DateTimeFormatter.ISO_TIME).getHour(), e -> e, AggregationDataUtil::aggregationHourlyReport)).values());
    }

    private CampaignHourlyReportSelectDto buildCampaignHourlyReportSelectDto(List<ShopAuth> shopAuths, CampaignAggregateHourParam param) {
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        return new CampaignHourlyReportSelectDto()
                .setSellerIds(sellerIds)
                .setMarketplaceId(param.getMarketplaceId())
                .setStartDate(getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()))
                .setEndDate(param.getEndDate())
                .setAdIds(ListUtils.emptyIfNull(param.getAdIdList()))
                .setType(param.getAdType())
                .setWeekdayList(Optional.ofNullable(param.getWeeks()).filter(StringUtils::isNotBlank).
                        map(w -> StringUtil.splitInt(param.getWeeks(), ",")).orElse(HourConvert.weeKs));
    }

    private List<AdCampaignHourVo> multiThreadQueryAms(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) {
        FeedHourlySelectDTO queryBuilder = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        // 分为四种情况
        // sp  sb  sd cpc sd vcpm
        List<AmazonAdCampaignAll> list = amazonAdCampaignAllDao.getByCampaignIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), null, ids, null);
        List<AdCampaignHourVo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            // sp类型
            List<String> listSp = list.stream().filter(key -> Constants.SP.equalsIgnoreCase(key.getType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSp)) {
                queryBuilder.setCampaignIds(listSp);
                buildAggregateHour(voList, queryBuilder, this::buildAdCampaignHourVoSp);
            }
            // sb类型 且 isMultiAdGroupsEnabled   || sd类型 且 cpcs
            List<String> listSb = list.stream().filter(key -> (Constants.SB.equalsIgnoreCase(key.getType()) && key.getIsMultiAdGroupsEnabled())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSb)) {
                queryBuilder.setCampaignIds(listSb);
                buildAggregateHour(voList, queryBuilder, this::buildAdCampaignHourVoSb);
            }
            // sd类型 且 cpc
            List<String> listSdCpc = list.stream().filter(key -> Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_CPC.equalsIgnoreCase(key.getCostType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSdCpc)) {
                queryBuilder.setCampaignIds(listSdCpc);
                buildAggregateHour(voList, queryBuilder, this::buildAdCampaignHourVoSdCpc);
            }
            // sd类型 且 vcpm
            List<String> listSdVcpm = list.stream().filter(key -> Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_VCPM.equalsIgnoreCase(key.getCostType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSdVcpm)) {
                queryBuilder.setCampaignIds(listSdVcpm);
                buildAggregateHour(voList, queryBuilder, this::buildAdCampaignHourVoSdVcpm);
            }
        }
        return voList;
    }

    private List<AmazonMarketingStreamData> multiThreadQueryAmsBySellerIds(List<String> ids,
                                                                           CampaignHourlyReportSelectDto campaignHourlyReportSelectDto) {
        if (CollectionUtils.isEmpty(ids) || CollectionUtils.isEmpty(campaignHourlyReportSelectDto.getAdIds())) {
            return Collections.emptyList();
        }
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(campaignHourlyReportSelectDto, queryDto);
        queryDto.setCampaignIds(ids);
        //新版支持类型多选，后续删除旧分支即可
//        if (Constants.SP.equalsIgnoreCase(queryDto.getType())) {
//            return amazonMarketingStreamDataDao.aggregateSellersByHour(queryDto);
//        } else {
            return amazonMarketingStreamDataDao.aggregateSellersByHourAndType(queryDto);
//        }
    }

    private void buildAggregateHour(List<AdCampaignHourVo> voList, FeedHourlySelectDTO feedHourlySelectDTO, ThFunction<String, AmazonMarketingStreamData, AmazonMarketingStreamData, AdCampaignHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = getHourStreamData(feedHourlySelectDTO);
        List<AmazonMarketingStreamData> streamDataCompare = new ArrayList<>();
        if (Integer.valueOf(1).equals(feedHourlySelectDTO.getIsCompare())) {
            streamDataCompare = getHourStreamData(copyFeedHourlySelectDTO(feedHourlySelectDTO));
        }
        Map<String, AmazonMarketingStreamData> streamDataMap = streamData.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        Map<String, AmazonMarketingStreamData> streamDataCompareMap = streamDataCompare.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        HourConvert.HOUR_FEED_MAP.forEach((key, value) -> {
            AmazonMarketingStreamData streamData1 = streamDataMap.getOrDefault(key, new AmazonMarketingStreamData());
            AmazonMarketingStreamData marketingStreamDataCompare = streamDataCompareMap.getOrDefault(key, new AmazonMarketingStreamData());
            AdCampaignHourVo vo = hourlyFunc.apply(key, streamData1, marketingStreamDataCompare);
            voList.add(vo);
        });
    }

    /**
     * 获取小时级汇总数据
     */
    @Override
    public List<AmazonMarketingStreamData> getHourStreamData(FeedHourlySelectDTO dto) {
        // 根据小时+币种+月份分组
        List<AmazonMarketingStreamData> streamDataList = amazonMarketingStreamDataDao.listByHourlyMultiple(dto);
        // 汇率换算
        changeRate(dto, streamDataList);
        // 根据小时+月份累加
        List<AmazonMarketingStreamData> sumStreamList = new ArrayList<>();
        Map<String, List<AmazonMarketingStreamData>> timeMap = StreamUtil.groupingBy(streamDataList, AmazonMarketingStreamData::getTime);
        HourConvert.HOUR_FEED_MAP.forEach((key, value) -> {
            List<AmazonMarketingStreamData> amazonMarketingStreamData = timeMap.get(key);
            if(CollectionUtil.isNotEmpty(amazonMarketingStreamData)){
                sumStreamList.add(buildSumStreamData(key, null, amazonMarketingStreamData));
            }
        });
        return sumStreamList;
    }

    /**
     * 获取小时级汇总数据
     */
    @Override
    public List<AmazonMarketingStreamData> getTargetOfPlacementStreamData(FeedHourlySelectDTO dto) {
        // 根据小时+币种+月份分组
        List<AmazonMarketingStreamData> streamDataList = amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfPlacementMultiple(dto);
        // 汇率换算
        changeRate(dto, streamDataList);
        // 根据小时+广告位累加
        List<AmazonMarketingStreamData> sumStreamList = new ArrayList<>();
        Map<String, List<AmazonMarketingStreamData>> timeMap = StreamUtil.groupingBy(streamDataList, e -> e.getTime() + "&&" + e.getPlacement());
        timeMap.forEach((k, v) -> {
            String[] key = k.split("&&");
            if (CollectionUtil.isNotEmpty(v)) {
                AmazonMarketingStreamData data = buildSumStreamData(key[0], null, v);
                data.setPlacement(key[1]);
                sumStreamList.add(data);
            }
        });
        return sumStreamList;
    }

    /**
     * 构建小时级汇总数据 根据小时+月份累加
     */
    private AmazonMarketingStreamData buildSumStreamData(String time, Integer weekDay, List<AmazonMarketingStreamData> amazonMarketingStreamData) {
        AmazonMarketingStreamData streamData = new AmazonMarketingStreamData();
        streamData.setTime(time);
        streamData.setWeekday(weekDay);
        for (AmazonMarketingStreamData data : amazonMarketingStreamData) {
            streamData.setCost(MathUtil.add(data.getCost(), streamData.getCost()));
            streamData.setClicks(MathUtil.add(data.getClicks(), streamData.getClicks()));
            streamData.setImpressions(MathUtil.add(data.getImpressions(), streamData.getImpressions()));
            streamData.setAttributedSales14d(MathUtil.add(data.getAttributedSales14d(), streamData.getAttributedSales14d()));
            streamData.setAttributedSales14dSameSku(MathUtil.add(data.getAttributedSales14dSameSku(), streamData.getAttributedSales14dSameSku()));
            streamData.setAttributedSales7d(MathUtil.add(data.getAttributedSales7d(), streamData.getAttributedSales7d()));
            streamData.setAttributedSales7dSameSku(MathUtil.add(data.getAttributedSales7dSameSku(), streamData.getAttributedSales7dSameSku()));
            streamData.setAttributedConversions7d(MathUtil.add(data.getAttributedConversions7d(), streamData.getAttributedConversions7d()));
            streamData.setAttributedConversions14d(MathUtil.add(data.getAttributedConversions14d(), streamData.getAttributedConversions14d()));
            streamData.setAttributedConversions7dSameSku(MathUtil.add(data.getAttributedConversions7dSameSku(), streamData.getAttributedConversions7dSameSku()));
            streamData.setAttributedConversions14dSameSku(MathUtil.add(data.getAttributedConversions14dSameSku(), streamData.getAttributedConversions14dSameSku()));
            streamData.setAttributedUnitsOrdered7d(MathUtil.add(data.getAttributedUnitsOrdered7d(), streamData.getAttributedUnitsOrdered7d()));
            streamData.setAttributedUnitsOrdered14d(MathUtil.add(data.getAttributedUnitsOrdered14d(), streamData.getAttributedUnitsOrdered14d()));
            streamData.setAttributedUnitsOrdered7dSameSku(MathUtil.add(data.getAttributedUnitsOrdered7dSameSku(), streamData.getAttributedUnitsOrdered7dSameSku()));
            streamData.setAttributedUnitsOrdered14dSameSku(MathUtil.add(data.getAttributedUnitsOrdered14dSameSku(), streamData.getAttributedUnitsOrdered14dSameSku()));
            streamData.setViewImpressions(MathUtil.add(data.getViewImpressions(), streamData.getViewImpressions()));
            streamData.setViewableImpressions(MathUtil.add(data.getViewableImpressions(), streamData.getViewableImpressions()));
            streamData.setViewAttributedUnitsOrderedNewToBrand14d(MathUtil.add(data.getViewAttributedUnitsOrderedNewToBrand14d(), streamData.getViewAttributedUnitsOrderedNewToBrand14d()));
            streamData.setAttributedUnitsOrderedNewToBrand14d(MathUtil.add(data.getAttributedUnitsOrderedNewToBrand14d(), streamData.getAttributedUnitsOrderedNewToBrand14d()));
            streamData.setAttributedSalesNewToBrand14d(MathUtil.add(data.getAttributedSalesNewToBrand14d(), streamData.getAttributedSalesNewToBrand14d()));
            streamData.setAttributedOrdersNewToBrand14d(MathUtil.add(data.getAttributedOrdersNewToBrand14d(), streamData.getAttributedOrdersNewToBrand14d()));
            streamData.setViewAttributedSalesNewToBrand14d(MathUtil.add(data.getViewAttributedSalesNewToBrand14d(), streamData.getViewAttributedSalesNewToBrand14d()));
            streamData.setViewAttributedUnitsOrdered14d(MathUtil.add(data.getViewAttributedUnitsOrdered14d(), streamData.getViewAttributedUnitsOrdered14d()));
            streamData.setViewAttributedSales14d(MathUtil.add(data.getViewAttributedSales14d(), streamData.getViewAttributedSales14d()));
            streamData.setViewAttributedOrdersNewToBrand14d(MathUtil.add(data.getViewAttributedOrdersNewToBrand14d(), streamData.getViewAttributedOrdersNewToBrand14d()));
            streamData.setViewAttributedConversions14d(MathUtil.add(data.getViewAttributedConversions14d(), streamData.getViewAttributedConversions14d()));
        }
        return streamData;
    }

    /**
     * 汇率换算
     */
    private void changeRate(FeedHourlySelectDTO dto, List<AmazonMarketingStreamData> streamDataList) {
        if(MultipleUtils.changeRate(dto.getShopAuths()) && CollectionUtil.isNotEmpty(streamDataList)){
            List<Integer> monthList = StreamUtil.toListDistinct(streamDataList, AmazonMarketingStreamData::getMonth);
            List<String> fromList = StreamUtil.toListDistinct(streamDataList, AmazonMarketingStreamData::getCurrency);
            List<DimCurrencyRate> rateList = dimCurrencyRateDao.getRateList(dto.getPuid(), AmznEndpoint.US.getCurrencyCode().value(), fromList, monthList);
            Map<String, DimCurrencyRate> rateMap = StreamUtil.toMap(rateList, it -> it.getFrom() + ":" + it.getMonth());
            for (AmazonMarketingStreamData streamData : streamDataList) {
                DimCurrencyRate rate = rateMap.get(streamData.getCurrency() + ":" + streamData.getMonth());
                if(rate == null){
                    continue;
                }
                // 金额*汇率
                streamData.setCost(streamData.getCost() == null ? null : rateValue(BigDecimal.valueOf(streamData.getCost()), rate.getRate()).doubleValue());
                streamData.setAttributedSales7d(rateValue(streamData.getAttributedSales7d(), rate.getRate()));
                streamData.setAttributedSales7dSameSku(rateValue(streamData.getAttributedSales7dSameSku(), rate.getRate()));
                streamData.setAttributedSales14d(rateValue(streamData.getAttributedSales14d(), rate.getRate()));
                streamData.setAttributedSales14dSameSku(rateValue(streamData.getAttributedSales14dSameSku(), rate.getRate()));
                streamData.setAttributedSalesNewToBrand14d(rateValue(streamData.getAttributedSalesNewToBrand14d(), rate.getRate()));
                streamData.setViewAttributedSalesNewToBrand14d(rateValue(streamData.getViewAttributedSalesNewToBrand14d(), rate.getRate()));
                streamData.setViewAttributedSales14d(rateValue(streamData.getViewAttributedSales14d(), rate.getRate()));
            }
        }
    }

    /**
     * 计算汇率换算后的值 保留两位小数
     */
    private static BigDecimal rateValue(BigDecimal value, BigDecimal rate) {
        if(value == null){
            return null;
        }
        return value.multiply(rate).setScale(3, RoundingMode.HALF_UP);
    }

    private void buildAggregateWeek(List<AdCampaignHourVo> voList, FeedHourlySelectDTO feedHourlySelectDTO, FourFunction<Integer, AmazonMarketingStreamData, AmazonMarketingStreamData, Integer, AdCampaignHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = getWeekStreamData(feedHourlySelectDTO);
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamData.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        Map<Integer, List<AmazonMarketingStreamData>> streamDataCompareMap = Maps.newHashMap();
        if (Integer.valueOf(1).equals(feedHourlySelectDTO.getIsCompare())) {
            List<AmazonMarketingStreamData> streamDataCompare = getWeekStreamData(copyFeedHourlySelectDTO(feedHourlySelectDTO));
            streamDataCompareMap.putAll(streamDataCompare.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday)));
        }
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamData1 = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            Map<String, AmazonMarketingStreamData> marketingStreamDataCompare = streamDataCompareMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                AdCampaignHourVo vo = hourlyFunc.apply(key, streamData1.getOrDefault(k, new AmazonMarketingStreamData()), marketingStreamDataCompare.getOrDefault(k, new AmazonMarketingStreamData()), value);
                voList.add(vo);
            });
        });
    }

    /**
     * 获取周叠加数据 汇率换算
     */
    @Override
    public List<AmazonMarketingStreamData> getWeekStreamData(FeedHourlySelectDTO dto) {
        List<AmazonMarketingStreamData> streamDataList = amazonMarketingStreamDataDao.listWeekByHourlyMultiple(dto);
        // 判断是否需要汇率换算
        changeRate(dto,streamDataList);
        // 根据小时+月份累加
        List<AmazonMarketingStreamData> sumStreamList = new ArrayList<>();
        Map<String, List<AmazonMarketingStreamData>> timeMap = StreamUtil.groupingBy(streamDataList, it-> it.getWeekday()+":"+it.getTime());
        HourConvert.weeKs.forEach(key -> {
            HourConvert.HOUR_FEED_MAP.forEach((k, value) -> {
                List<AmazonMarketingStreamData> amazonMarketingStreamData = timeMap.get(key+":"+k);
                if (CollectionUtil.isNotEmpty(amazonMarketingStreamData)) {
                    sumStreamList.add(buildSumStreamData(k, key, amazonMarketingStreamData));
                }
            });
        });
        return sumStreamList;
    }

    private FeedHourlySelectDTO copyFeedHourlySelectDTO(FeedHourlySelectDTO feedHourlySelectDTO) {
        FeedHourlySelectDTO queryBuilder = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        queryBuilder.setStart(feedHourlySelectDTO.getStartCompare());
        queryBuilder.setEnd(feedHourlySelectDTO.getEndCompare());
        return queryBuilder;
    }

    private FeedHourlySelectDTOMultiShop copyFeedHourlySelectDTOMultiShop(FeedHourlySelectDTOMultiShop feedHourlySelectDTO) {
        FeedHourlySelectDTOMultiShop queryBuilder = new FeedHourlySelectDTOMultiShop();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        queryBuilder.setStart(feedHourlySelectDTO.getStartCompare());
        queryBuilder.setEnd(feedHourlySelectDTO.getEndCompare());
        return queryBuilder;
    }

    private AdCampaignHourVo buildAdCampaignWeekVoSb(Integer key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare, Integer hour) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .weekDay(key)
                .hour(hour)
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .otherAdOrderNum(MathUtil.subtractInteger(streamData1.getAttributedConversions14d(), streamData1.getAttributedConversions14dSameSku()))
                .vcpmImpressions(0L)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .build();
        if (marketingStreamDataCompare != null) {
            vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
            vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
            vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
            vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
            vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
            vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        }
        return vo;
    }

    private AdCampaignHourVo buildAdCampaignWeekVoSdCpc(Integer key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare, Integer hour) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .weekDay(key)
                .hour(hour)
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .otherAdOrderNum(MathUtil.subtractInteger(streamData1.getAttributedConversions14d(), streamData1.getAttributedConversions14dSameSku()))
                .vcpmImpressions(0L)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(0L)
                .totalClicks(0L)
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .build();
        if (marketingStreamDataCompare != null) {
            vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
            vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
            vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
            vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
            vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
            vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        }

        return vo;
    }

    private AdCampaignHourVo buildAdCampaignHourVoSb(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .vcpmImpressions(0L)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }

    private AdCampaignHourVo buildAdCampaignHourVoSdCpc(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .vcpmImpressions(0L)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(0L)
                .totalClicks(0L)
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }

    private AdCampaignHourVo buildAdCampaignHourVoSdVcpm(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getViewAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getViewAttributedConversions14d()))
                .selfAdOrderNum(0)
                .adSaleNum(getDefaultInteger(streamData1.getViewAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getViewAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getViewAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getViewAttributedSalesNewToBrand14d()))
                .vcpmImpressions(getDefaultInteger(streamData1.getViewImpressions()))
                .vcpmCost(getDefaultBigDecimal(streamData1.getCost()))
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(BigDecimal.ZERO)
                .totalAdSelfSale(BigDecimal.ZERO)
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getViewAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedConversions14d()));
        vo.setOtherAdOrderNum(0);
        return vo;
    }

    private AdCampaignHourVo buildAdCampaignWeekVoSdVcpm(Integer key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare, Integer hour) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .weekDay(key)
                .hour(hour)
                .adSale(getDefaultBigDecimal(streamData1.getViewAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getViewAttributedConversions14d()))
                .selfAdOrderNum(0)
                .adSaleNum(getDefaultInteger(streamData1.getViewAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getViewAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getViewAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getViewAttributedSalesNewToBrand14d()))
                .otherAdOrderNum(0)
                .vcpmCost(getDefaultBigDecimal(streamData1.getCost()))
                .vcpmImpressions(getDefaultInteger(streamData1.getViewImpressions()))
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(BigDecimal.ZERO)
                .totalAdSelfSale(BigDecimal.ZERO)
                .build();
        if (marketingStreamDataCompare != null) {
            vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
            vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
            vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
            vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
            vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
            vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        }
        return vo;
    }

    private AdCampaignHourVo buildAdCampaignHourVoSp(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales7d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales7dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions7d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions7dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7d()))
                .adSelfSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adOtherSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7d()) - getDefaultInteger(streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(0)
                .unitsOrderedNewToBrand(0)
                .salesNewToBrand(BigDecimal.ZERO)
                .vcpmImpressions(0L)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales7d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales7dSameSku()))
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered7d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales7d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions7d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }


    private AdCampaignHourVo buildAdCampaignWeekVoSp(Integer key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare, Integer hour) {
        AdCampaignHourVo vo = AdCampaignHourVo.builder()
                .hour(hour)
                .weekDay(key)
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales7d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales7dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions7d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions7dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7d()))
                .adSelfSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adOtherSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7d()) - getDefaultInteger(streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(0)
                .unitsOrderedNewToBrand(0)
                .salesNewToBrand(BigDecimal.ZERO)
                .otherAdOrderNum(MathUtil.subtractInteger(streamData1.getAttributedConversions7d(), streamData1.getAttributedConversions7dSameSku()))
                .vcpmImpressions(0L)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales7d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales7dSameSku()))
                .build();
        if (marketingStreamDataCompare != null) {
            vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
            vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
            vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered7d()));
            vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales7d()));
            vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
            vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions7d()));
        }
        return vo;
    }


    private void sumMetricData(List<? extends AdReportWeeklyDayVO> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(AdReportWeeklyDayVO::getAdCost).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(AdReportWeeklyDayVO::getAdSale).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdReportWeeklyDayVO::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdReportWeeklyDayVO::getAdSaleNum).sum()));
    }

    // 填充指标占比数据
    private void metricData(List<? extends AdReportWeeklyDayVO> voList, AdMetricDto adMetricDto) {
        for (AdReportWeeklyDayVO vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            metricData(adMetricDto, vo);
        }
    }

    private void metricData(AdMetricDto adMetricDto, AdReportWeeklyDayVO vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }


    private void filterSumMetricData(List<AdCampaignHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdCampaignHourVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                    map(AdCampaignHourVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                    map(AdCampaignHourVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                    map(AdCampaignHourVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                    map(AdCampaignHourVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }

    private void filterMetricData(List<AdCampaignHourVo> voList, AdMetricDto adMetricDto) {
        for (AdCampaignHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdCampaignHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private void buildAdWeekReportHourlyVO(List<AmazonMarketingStreamData> streamData, List<AmazonMarketingStreamData> streamDataCompare,
                                           List<AdReportWeeklyDayVO> adReportWeeklyDayVOS, boolean bool,
                                           ThFunction<String, String, AmazonMarketingStreamData, AdReportHourlyVO> thFunction) {
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamData.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        Map<Integer, List<AmazonMarketingStreamData>> compareMap = streamDataCompare.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamDataList = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            Map<String, AmazonMarketingStreamData> compareList = compareMap.getOrDefault(key, new ArrayList<>())
                    .stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            List<AdReportHourlyVO> adReportHourlyVOS = new ArrayList<>(24);
            for (Map.Entry<String, String> entry : HourConvert.HOUR_FEED_MAP.entrySet()) {
                String mapKey = entry.getKey();
                AmazonMarketingStreamData amazonMarketingStreamData = streamDataList.getOrDefault(mapKey, new AmazonMarketingStreamData());
                AdReportHourlyVO adReportHourlyVO = thFunction.apply(mapKey, null, amazonMarketingStreamData);
                AmazonMarketingStreamData compareData = compareList.getOrDefault(mapKey, new AmazonMarketingStreamData());
                AdReportHourlyVO compareVO = thFunction.apply(mapKey, null, compareData);

                // 对比
                adReportHourlyVO.setClicksCompare(getDefaultInteger(compareVO.getClicks()));
                adReportHourlyVO.setImpressionsCompare(getDefaultInteger(compareVO.getImpressions()));
                adReportHourlyVO.setAdCostCompare(getDefaultBigDecimal(compareVO.getAdCost()));
                adReportHourlyVO.setAdSaleNumCompare(getDefaultInteger(compareVO.getAdSaleNum()));
                adReportHourlyVO.setAdSaleCompare(getDefaultBigDecimal(compareVO.getAdSale()));
                adReportHourlyVO.setAdOrderNumCompare(getDefaultInteger(compareVO.getAdOrderNum()));

                calculateBuild(adReportHourlyVO, bool, true);
                adReportHourlyVOS.add(adReportHourlyVO);
            }
            adReportHourlyVOS.sort(Comparator.comparing(AdReportHourlyVO::getHour));
            if (CollectionUtils.isNotEmpty(adReportHourlyVOS)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                HourlyAndWeeklyDataHandler.filterSumMetricData(adReportHourlyVOS, adMetricDto);
                HourlyAndWeeklyDataHandler.filterMetricData(adReportHourlyVOS, adMetricDto);
            }
            AdReportWeeklyDayVO adReportWeeklyDayVO = AdReportWeeklyDayVO.builder().weekDay(key)
                    .build();
            adReportWeeklyDayVO.setDetails(adReportHourlyVOS);
            adReportWeeklyDayVO.staticsFromHourVos(adReportHourlyVOS);
            adReportWeeklyDayVO.calculateCompareRate();
            calculateBuild(adReportWeeklyDayVO, bool);
            adReportWeeklyDayVOS.add(adReportWeeklyDayVO);
        });
    }

    private void buildReportHourlyVO(List<AmazonMarketingStreamData> streamData, List<AmazonMarketingStreamData> streamDataCompare, List<AdReportHourlyVO> voList, boolean bool, ThFunction<String, String, AmazonMarketingStreamData, AdReportHourlyVO> thFunction, boolean isVcpm) {
        Map<String, AmazonMarketingStreamData> streamDataMap = streamData.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        Map<String, AmazonMarketingStreamData> streamDataCompareMap = streamDataCompare.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        HourConvert.HOUR_FEED_MAP.forEach((key, value) -> {
            AmazonMarketingStreamData streamData1 = streamDataMap.getOrDefault(key, new AmazonMarketingStreamData());
            AmazonMarketingStreamData marketingStreamDataCompare = streamDataCompareMap.getOrDefault(key, new AmazonMarketingStreamData());
            AdReportHourlyVO vo = thFunction.apply(key, null, streamData1);
            // 类比
            vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
            vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
            vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
            if (isVcpm) {
                vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedUnitsOrdered14d()));
                vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getViewAttributedSales14d()));
                vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedConversions14d()));
            } else {
                vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
                vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
                vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
            }
            // 通过计算出来
            calculateBuild(vo, bool, true);
            voList.add(vo);
        });
    }

    private AdReportHourlyVO buildSbAdReportHourlyVOBasic(String key, String value, AmazonMarketingStreamData streamData) {
        return AdReportHourlyVO.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData.getCost()))
                .clicks(getDefaultInteger(streamData.getClicks()))
                .impressions(getDefaultInteger(streamData.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData.getViewableImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData.getAttributedSalesNewToBrand14d()))
                .build();
    }

    private AdReportHourlyVO buildSdCpcAdReportHourlyVOBasic(String key, String value, AmazonMarketingStreamData streamData) {
        return AdReportHourlyVO.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData.getCost()))
                .clicks(getDefaultInteger(streamData.getClicks()))
                .impressions(getDefaultInteger(streamData.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(getDefaultInteger(streamData.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData.getAttributedSalesNewToBrand14d()))
                .build();
    }

    private AdReportHourlyVO buildSdVcpmAdReportHourlyVOBasic(String key, String value, AmazonMarketingStreamData streamData) {
        return AdReportHourlyVO.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData.getViewAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData.getViewAttributedConversions14d()))
                .selfAdOrderNum(0)
                .adSaleNum(getDefaultInteger(streamData.getViewAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData.getCost()))
                .clicks(getDefaultInteger(streamData.getClicks()))
                .impressions(getDefaultInteger(streamData.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData.getViewImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData.getViewAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData.getViewAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData.getViewAttributedSalesNewToBrand14d()))
                .build();
    }

    private void buildExportHourlyVO(List<AmazonMarketingStreamData> streamData, List<AmazonMarketingStreamData> streamDataCompare, List<AdCampaignHourVo> voList, boolean bool, ThFunction<String, AmazonMarketingStreamData, AmazonMarketingStreamData, AdCampaignHourVo> thFunction) {
        Map<String, AmazonMarketingStreamData> streamDataMap = streamData.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        Map<String, AmazonMarketingStreamData> streamDataCompareMap = streamDataCompare.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        HourConvert.HOUR_FEED_MAP.forEach((key, value) -> {
            AmazonMarketingStreamData streamData1 = streamDataMap.getOrDefault(key, new AmazonMarketingStreamData());
            AmazonMarketingStreamData marketingStreamDataCompare = streamDataCompareMap.getOrDefault(key, new AmazonMarketingStreamData());
            AdCampaignHourVo vo = thFunction.apply(key, streamData1, marketingStreamDataCompare);
            // 通过计算出来
            calculateBuild(vo, bool);
            voList.add(vo);
        });
    }

    /**
     * 构造条件
     */
    private FeedHourlySelectDTO build(LocalDate start, LocalDate end, String type, String sellerId, String marketplaceId, String campaignId, String week, String adGroups, List<String> adIds, ShopAuth shopAuth) {
        FeedHourlySelectDTO feedHourlySelectDTO = new FeedHourlySelectDTO();
        if (StringUtils.isNotBlank(type)) {
            feedHourlySelectDTO.setType(type.toUpperCase());
        }
        feedHourlySelectDTO.setSellerId(sellerId);
        feedHourlySelectDTO.setMarketplaceId(marketplaceId);
        if (StringUtils.isNotBlank(campaignId)) {
            feedHourlySelectDTO.setCampaignIds(StringUtil.splitStr(campaignId, ","));
        }
        if (StringUtils.isNotBlank(adGroups)) {
            feedHourlySelectDTO.setAdGroupIds(StringUtil.splitStr(adGroups, ","));
        }
        if (CollectionUtils.isNotEmpty(adIds)) {
            feedHourlySelectDTO.setAdIds(adIds);
        }
        feedHourlySelectDTO.setPuid(shopAuth.getPuid());
        feedHourlySelectDTO.setShopId(shopAuth.getId());
        feedHourlySelectDTO.setStart(start);
        feedHourlySelectDTO.setEnd(end);
        if (StringUtils.isNotBlank(week)) {
            feedHourlySelectDTO.setWeekdayList(StringUtil.splitInt(week));
        }
        feedHourlySelectDTO.setIsCompare(0);
        return feedHourlySelectDTO;
    }

    /**
     * 多店铺构造条件
     */
    public static FeedHourlySelectDTO buildMultiple(LocalDate start, LocalDate end, String campaignId, String week, List<ShopAuth> shopAuths){
        FeedHourlySelectDTO feedHourlySelectDTO = new FeedHourlySelectDTO();
        if (StringUtils.isNotBlank(campaignId)) {
            feedHourlySelectDTO.setCampaignIds(StringUtil.splitStr(campaignId, ","));
        }
        feedHourlySelectDTO.setPuid(shopAuths.get(0).getPuid());
        feedHourlySelectDTO.setStart(start);
        feedHourlySelectDTO.setEnd(end);
        if (StringUtils.isNotBlank(week)) {
            feedHourlySelectDTO.setWeekdayList(StringUtil.splitInt(week));
        }
        feedHourlySelectDTO.setIsCompare(0);
        feedHourlySelectDTO.setShopAuths(shopAuths);
        List<String> sellerIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId);
        List<String> marketplaceIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId);
        List<String> concatWsList = StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId());
        feedHourlySelectDTO.setSellerIdList(sellerIdList);
        feedHourlySelectDTO.setMarketplaceIdList(marketplaceIdList);
        feedHourlySelectDTO.setConcatWsList(concatWsList);
        return feedHourlySelectDTO;
    }

    /**
     * 多店铺投放构造条件
     */
    public static FeedHourlySelectDTO buildMultipleTargetSelectDto(List<ShopAuth> shopAuths, HourlyWeeklySuperpositionTargetReq req){
        FeedHourlySelectDTO feedHourlySelectDTO = new FeedHourlySelectDTO();
        feedHourlySelectDTO.setPuid(shopAuths.get(0).getPuid());
        feedHourlySelectDTO.setType(req.getType());
        feedHourlySelectDTO.setStart(LocalDate.parse(req.getStartDate()));
        feedHourlySelectDTO.setEnd(LocalDate.parse(req.getEndDate()));
        if (StringUtils.isNotBlank(req.getWeeks())) {
            feedHourlySelectDTO.setWeekdayList(StringUtil.splitInt(req.getWeeks()));
        }
        feedHourlySelectDTO.setIsCompare(req.getIsCompare());
        if (req.getIsCompare() == 1) {
            feedHourlySelectDTO.setStartCompare(LocalDate.parse(req.getStartDateCompare()));
            feedHourlySelectDTO.setEndCompare(LocalDate.parse(req.getEndDateCompare()));
        }
        feedHourlySelectDTO.setShopAuths(shopAuths);
        List<String> sellerIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId);
        List<String> marketplaceIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId);
        List<String> concatWsList = StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId());
        feedHourlySelectDTO.setSellerIdList(sellerIdList);
        feedHourlySelectDTO.setMarketplaceIdList(marketplaceIdList);
        feedHourlySelectDTO.setConcatWsList(concatWsList);
        return feedHourlySelectDTO;
    }

    private void compareBuild(FeedHourlySelectDTO feedHourlySelectDTO, LocalDate start, LocalDate end) {
        feedHourlySelectDTO.setIsCompare(1);
        feedHourlySelectDTO.setStartCompare(start);
        feedHourlySelectDTO.setEndCompare(end);
    }

    private BigDecimal getDefaultBigDecimal(BigDecimal bigDecimal) {
        return bigDecimal == null ? BigDecimal.ZERO : bigDecimal.setScale(2, RoundingMode.HALF_UP);
    }

    private BigDecimal getDefaultBigDecimal(Double bigDecimal) {
        return bigDecimal == null ? BigDecimal.ZERO : new BigDecimal(String.valueOf(bigDecimal)).setScale(2, RoundingMode.HALF_UP);
    }

    private Integer getDefaultInteger(Integer number) {
        return number == null ? 0 : number;
    }

    private Long getDefaultInteger(Long number) {
        return number == null ? 0L : number;
    }

    /**
     * 构造计算
     */
    private void calculateBuild(AdReportHourlyVO vo, boolean bool, boolean afterProperties) {
        vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()).setScale(4, RoundingMode.HALF_UP));
        if (bool) {
            vo.setVcpm(BigDecimal.ZERO);
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        } else {
            vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
            vo.setOtherAdOrderNum(0);
        }

        vo.setAdOtherSaleNum(0);
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(BigDecimal.valueOf(100)).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));

        // 对比数据
        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setCpaCompare(vo.getAdOrderNumCompare() == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().divide(BigDecimal.valueOf(vo.getAdOrderNumCompare()), 4, RoundingMode.HALF_UP));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(BigDecimal.valueOf(100)).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));

        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        if (bool) {
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        } else {
            vo.setAdvertisingProductUnitPrice(BigDecimal.ZERO);
            vo.setAdvertisingOtherProductUnitPrice(BigDecimal.ZERO);
        }
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        if (afterProperties) {
            vo.afterPropertiesSet();//为各对比率属性设值
        }
    }

    private void calculateBuild(AdCampaignHourVo vo, boolean bool) {
        vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()).setScale(4, RoundingMode.HALF_UP));
        if (bool) {
            vo.setVcpm(BigDecimal.ZERO);
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        } else {
            vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
            vo.setOtherAdOrderNum(0);
        }
        vo.setAdOtherSaleNum(0);
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(BigDecimal.valueOf(100)).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        if (bool) {
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        } else {
            vo.setAdvertisingProductUnitPrice(BigDecimal.ZERO);
            vo.setAdvertisingOtherProductUnitPrice(BigDecimal.ZERO);
        }
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));

        vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setCpaCompare(vo.getAdOrderNumCompare() == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().divide(BigDecimal.valueOf(vo.getAdOrderNumCompare()), 4, RoundingMode.HALF_UP));
        vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
        vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
        vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdCostCompare().multiply(BigDecimal.valueOf(100)).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
        vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));

        vo.afterPropertiesSet();//为各对比率属性设值
    }

    private void calculateBuild(AdReportWeeklyDayVO vo, boolean bool) {
        vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()).setScale(4, RoundingMode.HALF_UP));
        if (bool) {
            vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
            vo.setVcpm(BigDecimal.ZERO);
        } else {
            vo.setOtherAdOrderNum(0);
            vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        }
        vo.setAdOtherSaleNum(0);
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(BigDecimal.valueOf(100)).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum())));
        if (bool) {
            vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
            vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        } else {
            vo.setAdvertisingProductUnitPrice(BigDecimal.ZERO);
            vo.setAdvertisingOtherProductUnitPrice(BigDecimal.ZERO);
        }
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
    }


    @Override
    public List<AdGroupHourVo> listAggregateGroupHourList(ShopAuth shopInfo, List<String> aggregateIds, GroupAggregateHourVo param) {
        FeedHourlySelectDTO feedHourlySelectDTO = build(LocalDate.parse(param.getAdPageBasicData().getStartDate()),
                LocalDate.parse(param.getAdPageBasicData().getEndDate()),
                StringUtils.isNotBlank(param.getAdPageBasicData().getType()) ? param.getAdPageBasicData().getType() : Constants.SP,
                shopInfo.getSellingPartnerId(),
                shopInfo.getMarketplaceId(),
                param.getCampaignId(),
                param.getWeeks(),
                param.getAdGroupId(),
                null,
                shopInfo);
        if (Integer.valueOf(1).equals(param.getAdPageBasicData().getIsCompare().getValue())) {
            compareBuild(feedHourlySelectDTO, LocalDate.parse(param.getAdPageBasicData().getStartDateCompare()), LocalDate.parse(param.getAdPageBasicData().getEndDateCompare()));
        }
        // 并发查询
        List<List<AdGroupHourVo>> list = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                () -> aggregateIds, feedHourlySelectDTO, this::multiThreadQueryGroupAms);
        List<AdGroupHourVo> adGroupHourVoVos = ReportChartUtil.getAdGroupHourReportVos(list);
        if (CollectionUtils.isNotEmpty(adGroupHourVoVos)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterAdGroupSumMetricData(adGroupHourVoVos, adMetricDto);
            filterAdGroupMetricData(adGroupHourVoVos, adMetricDto);
        }
        return adGroupHourVoVos;
    }

    @Override
    public List<AdCampaignHourVo> multiThreadQueryWeekAms(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) {
        FeedHourlySelectDTO queryBuilder = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        queryBuilder.setCampaignIds(ids);
        // 分为四种情况
        // sp  sb  sd cpc sd vcpm
        List<AmazonAdCampaignAll> list = amazonAdCampaignAllDao.getByCampaignIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), null, ids, null);
        List<AdCampaignHourVo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            // sp类型
            List<String> listSp = list.stream().filter(key -> Constants.SP.equalsIgnoreCase(key.getType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSp)) {
                queryBuilder.setCampaignIds(listSp);
                buildAggregateWeek(voList, queryBuilder, this::buildAdCampaignWeekVoSp);
            }
            // sb类型 且 isMultiAdGroupsEnabled   || sd类型 且 cpc
            List<String> listSb = list.stream().filter(key -> (Constants.SB.equalsIgnoreCase(key.getType()) && key.getIsMultiAdGroupsEnabled())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSb)) {
                queryBuilder.setCampaignIds(listSb);
                buildAggregateWeek(voList, queryBuilder, this::buildAdCampaignWeekVoSb);
            }

            List<String> listSdCpc = list.stream().filter(key -> (Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_CPC.equalsIgnoreCase(key.getCostType()))).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSdCpc)) {
                queryBuilder.setCampaignIds(listSdCpc);
                buildAggregateWeek(voList, queryBuilder, this::buildAdCampaignWeekVoSdCpc);
            }

            // sd类型 且 vcpm
            List<String> listSdVcpm = list.stream().filter(key -> Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_VCPM.equalsIgnoreCase(key.getCostType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(listSdVcpm)) {
                queryBuilder.setCampaignIds(listSdVcpm);
                buildAggregateWeek(voList, queryBuilder, this::buildAdCampaignWeekVoSdVcpm);
            }
        }
        return voList;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> multiThreadQueryAmsForTarget(List<String> ids, FeedHourlySelectDTO dto) {
        dto.setKeywordIds(ids);
        List<AdKeywordAndTargetHourVo> voList;
        if (Constants.SD.equalsIgnoreCase(dto.getType())) {
            voList = AdKeywordAndTargetHourVoForSd(dto.getPuid(), dto.getShopAuths(), ids, dto);
        } else {
            voList = new ArrayList<>();
            List<AmazonMarketingStreamData> streamDataList = this.getWeekStreamData(dto);
            Map<Integer, List<AmazonMarketingStreamData>> weekDayCompareMap = Maps.newHashMap();
            if (dto.getIsCompare() == 1) {
                LocalDate startDate = dto.getStart();
                LocalDate endDate = dto.getEnd();
                dto.setStart(dto.getStartCompare());
                dto.setEnd(dto.getEndCompare());
                List<AmazonMarketingStreamData> compareList = this.getWeekStreamData(dto);
                weekDayCompareMap.putAll(StreamUtil.groupingBy(compareList, AmazonMarketingStreamData::getWeekday, Function.identity()));
                // 参数还原
                dto.setStart(startDate);
                dto.setEnd(endDate);
            }
            Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday, LinkedHashMap::new, Collectors.toList()));
            HourConvert.weeKs.forEach(key -> {
                Map<String, AmazonMarketingStreamData> timeCompareMap = StreamUtil.toMap(weekDayCompareMap.getOrDefault(key, Lists.newArrayList()), AmazonMarketingStreamData::getTime);
                Map<String, AmazonMarketingStreamData> weekdayStreamDataMap = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
                HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                    AdKeywordAndTargetHourVo vo = buildAdKeywordAndTargetWeekVo(key, dto.getType(),
                            weekdayStreamDataMap.getOrDefault(k, new AmazonMarketingStreamData()), value, timeCompareMap.getOrDefault(k, null));
                    voList.add(vo);
                });
            });
        }
        return voList;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> multiThreadQueryAmsForTarget(List<String> ids, CommonHourlyReportSelectDto dto) {
        ShopAuth shop = shopAuthDao.getBySellerIdAndMarketplaceId(dto.getSellerId(), dto.getMarketplaceId());
        if (Objects.isNull(shop)) {
            throw new BizServiceException("error param, CommonHourlyReportSelectDto: " + JSON.toJSONString(dto));
        }
        dto.setKeywordTargetingIds(ids);
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();
        if (Constants.SD.equalsIgnoreCase(dto.getType())) {
            AdKeywordAndTargetHourVoForSd(voList, shop, ids, dto);
        } else {
            List<AmazonMarketingStreamData> streamDataList = amazonMarketingStreamDataDao.statisticsByWeek(dto);
            Map<Integer, List<AmazonMarketingStreamData>> weekDayCompareMap = Maps.newHashMap();
            if (dto.getIsCompare() == 1) {
                String startDate = dto.getStartDate();
                String endDate = dto.getEndDate();
                dto.setStartDate(dto.getStartDateCompare());
                dto.setEndDate(dto.getEndDateCompare());
                List<AmazonMarketingStreamData> compareList = amazonMarketingStreamDataDao.statisticsByWeek(dto);
                weekDayCompareMap.putAll(StreamUtil.groupingBy(compareList, AmazonMarketingStreamData::getWeekday, Function.identity()));
                // 参数还原
                dto.setStartDate(startDate);
                dto.setEndDate(endDate);
            }
            Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday, LinkedHashMap::new, Collectors.toList()));
            HourConvert.weeKs.forEach(key -> {
                Map<String, AmazonMarketingStreamData> timeCompareMap = StreamUtil.toMap(weekDayCompareMap.getOrDefault(key, Lists.newArrayList()), AmazonMarketingStreamData::getTime);
                Map<String, AmazonMarketingStreamData> weekdayStreamDataMap = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
                HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                    AdKeywordAndTargetHourVo vo = buildAdKeywordAndTargetWeekVo(key, dto.getType(),
                            weekdayStreamDataMap.getOrDefault(k, new AmazonMarketingStreamData()), value, timeCompareMap.getOrDefault(k, null));
                    voList.add(vo);
                });
            });
        }

        return voList;
    }

    @Override
    public List<AdCampaignHourVo> listAggregateHourListMultiShop(List<ShopAuth> shopAuth, List<String> relationIds, CampaignAggregateHourMultiShopParamVO param) {
        FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO = buildDto(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                relationIds,
                param.getWeeks(),
                shopAuth,
                param.getPuid(),
                param);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            compareBuildMultiShop(feedHourlySelectMultiShopDTO, LocalDate.parse(param.getStartDateCompare()), LocalDate.parse(param.getEndDateCompare()));
        }
        //并发查询
        List<List<AdCampaignHourVo>> list = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(), () -> relationIds, feedHourlySelectMultiShopDTO, this::multiThreadQueryAmsMultiShop);
        List<AdCampaignHourVo> adCampaignHourVos = ReportChartUtil.getCampaignHourReportVos(list);
        if (CollectionUtils.isNotEmpty(adCampaignHourVos)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(adCampaignHourVos, adMetricDto);
            filterMetricData(adCampaignHourVos, adMetricDto);
            adCampaignHourVos.sort(Comparator.comparing(AdCampaignHourVo::getHour));
        }
        return adCampaignHourVos;
    }

    @Override
    public List<AdCampaignHourVo> listAggregateWeekListMultiShop(List<ShopAuth> shopAuth, List<String> relationIds, CampaignHourParamMultiShop param) {
        List<String> sellerIds = shopAuth.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        List<String> marketplaceIds = param.getMarketplaceIds();
        FeedHourlySelectDTOMultiShop feedHourlySelectDTOMultiShop = buildMultiShop(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                null,
                sellerIds,
                marketplaceIds,
                relationIds,
                param.getWeeks(),
                null,
                null,
                param,
                shopAuth
                );
        List<List<AdCampaignHourVo>> list = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                () -> relationIds, feedHourlySelectDTOMultiShop, this::multiThreadQueryWeekAmsMultiShop);
        return ReportChartUtil.getCampaignWeekReportVos(list);
    }

    private List<AdCampaignHourVo> multiThreadQueryWeekAmsMultiShop(List<String> ids, FeedHourlySelectDTOMultiShop feedHourlySelectDTOMultiShop) {
        FeedHourlySelectDTOMultiShop queryBuilder = new FeedHourlySelectDTOMultiShop();
        BeanUtils.copyProperties(feedHourlySelectDTOMultiShop, queryBuilder);
        queryBuilder.setCampaignIds(ids);
        // 分为四种情况
        // sp  sb  sd cpc sd vcpm
        List<AmazonAdCampaignAll> list = amazonAdCampaignAllDao.getByCampaignIdsAndShopIdListAndMarketplaceIdList(feedHourlySelectDTOMultiShop.getPuid(), feedHourlySelectDTOMultiShop.getShopIds(), feedHourlySelectDTOMultiShop.getMarketplaceIds(), ids, null);
        List<AdCampaignHourVo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            //重复id查多次报告数据
            if (queryBuilder.getMultipleIdComputation() != null && queryBuilder.getMultipleIdComputation()) {
                Map<String, Integer> idCountMap = new HashMap<>();
                ids.forEach(id -> {
                    idCountMap.put(id, idCountMap.getOrDefault(id, 0) + 1);
                });
                Map<String, AmazonAdCampaignAll> campaignIdMap = list.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
                if (!idCountMap.isEmpty()) {
                    int i = 0;
                    while (true) {
                        int j = i++;
                        List<AmazonAdCampaignAll> campaignList = idCountMap.entrySet().stream().filter(e -> campaignIdMap.containsKey(e.getKey()) && e.getValue() > j)
                                .map(e -> campaignIdMap.get(e.getKey())).collect(Collectors.toList());
                        if (campaignList.isEmpty()) {
                            break;
                        }
                        this.multiThreadQueryWeekAmsMultiShopByCampaignList(queryBuilder, campaignList, voList);
                    }
                }
            } else {
                this.multiThreadQueryWeekAmsMultiShopByCampaignList(queryBuilder, list, voList);
            }
        }
        return voList;
    }

    private void multiThreadQueryWeekAmsMultiShopByCampaignList(FeedHourlySelectDTOMultiShop queryBuilder, List<AmazonAdCampaignAll> list, List<AdCampaignHourVo> voList) {
        // sp类型
        List<String> listSp = list.stream().filter(key -> Constants.SP.equalsIgnoreCase(key.getType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSp)) {
            queryBuilder.setCampaignIds(listSp);
            buildAggregateWeekMultiShop(voList, queryBuilder, this::buildAdCampaignWeekVoSp);
        }
        // sb类型 且 isMultiAdGroupsEnabled   || sd类型 且 cpc
        List<String> listSb = list.stream().filter(key -> (Constants.SB.equalsIgnoreCase(key.getType()) && key.getIsMultiAdGroupsEnabled())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSb)) {
            queryBuilder.setCampaignIds(listSb);
            buildAggregateWeekMultiShop(voList, queryBuilder, this::buildAdCampaignWeekVoSb);
        }

        // sd类型 且 cpc
        List<String> listSdCpc = list.stream().filter(key -> (Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_CPC.equalsIgnoreCase(key.getCostType()))).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSdCpc)) {
            queryBuilder.setCampaignIds(listSdCpc);
            buildAggregateWeekMultiShop(voList, queryBuilder, this::buildAdCampaignWeekVoSdCpc);
        }

        // sd类型 且 vcpm
        List<String> listSdVcpm = list.stream().filter(key -> Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_VCPM.equalsIgnoreCase(key.getCostType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSdVcpm)) {
            queryBuilder.setCampaignIds(listSdVcpm);
            buildAggregateWeekMultiShop(voList, queryBuilder, this::buildAdCampaignWeekVoSdVcpm);
        }
    }

    private void buildAggregateWeekMultiShop(List<AdCampaignHourVo> voList, FeedHourlySelectDTOMultiShop queryBuilder, FourFunction<Integer, AmazonMarketingStreamData, AmazonMarketingStreamData, Integer, AdCampaignHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listWeekByHourlyMultiShop(queryBuilder);
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamData.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        Map<Integer, List<AmazonMarketingStreamData>> streamDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(queryBuilder.getIsCompare())) {
            streamDataCompareMap.putAll(amazonMarketingStreamDataDao.listWeekByHourlyMultiShop(copyFeedHourlySelectDTOMultiShop(queryBuilder))
                    .stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday)));
        }
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamData1 = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            Map<String, AmazonMarketingStreamData> streamData1Compare = streamDataCompareMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                AdCampaignHourVo vo = hourlyFunc.apply(key, streamData1.getOrDefault(k, new AmazonMarketingStreamData()), streamData1Compare.getOrDefault(k, new AmazonMarketingStreamData()), value);
                voList.add(vo);
            });
        });
    }

    private FeedHourlySelectDTOMultiShop buildMultiShop(LocalDate start, LocalDate end, String type, List<String> sellerIds, List<String> marketplaceIds, List<String> campaignIds, String week, String adGroups, List<String> adIds, CampaignHourParamMultiShop param, List<ShopAuth> shopAuth) {
        FeedHourlySelectDTOMultiShop feedHourlySelectDTOMultiShop = new FeedHourlySelectDTOMultiShop();
        if (StringUtils.isNotBlank(type)) {
            feedHourlySelectDTOMultiShop.setType(type.toUpperCase());
        }
        feedHourlySelectDTOMultiShop.setSellerIds(sellerIds);
        feedHourlySelectDTOMultiShop.setMarketplaceIds(marketplaceIds);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            feedHourlySelectDTOMultiShop.setCampaignIds(campaignIds);
        }
        if (StringUtils.isNotBlank(adGroups)) {
            feedHourlySelectDTOMultiShop.setAdGroupIds(StringUtil.splitStr(adGroups, ","));
        }
        if (CollectionUtils.isNotEmpty(adIds)) {
            feedHourlySelectDTOMultiShop.setAdIds(adIds);
        }
        feedHourlySelectDTOMultiShop.setPuid(param.getPuid());
        List<Integer> shopIds = shopAuth.stream().map(ShopAuth::getId).collect(Collectors.toList());
        feedHourlySelectDTOMultiShop.setShopIds(shopIds);
        feedHourlySelectDTOMultiShop.setStart(start);
        feedHourlySelectDTOMultiShop.setEnd(end);
        feedHourlySelectDTOMultiShop.setTo(param.getCurrency());
        if (StringUtils.isNotBlank(week)) {
            feedHourlySelectDTOMultiShop.setWeekdayList(StringUtil.splitInt(week));
        }
        feedHourlySelectDTOMultiShop.setIsCompare(param.getIsCompare());
        if (param.getIsCompare() != null && param.getIsCompare() == 1) {
            feedHourlySelectDTOMultiShop.setStartCompare(LocalDate.parse(param.getStartDateCompare()));
            feedHourlySelectDTOMultiShop.setEndCompare(LocalDate.parse(param.getEndDateCompare()));
        }
        feedHourlySelectDTOMultiShop.setMultipleIdComputation(param.getMultipleIdComputation());
        return feedHourlySelectDTOMultiShop;
    }

    private List<AdCampaignHourVo> multiThreadQueryAmsMultiShop(List<String> ids, FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO) {
        FeedHourlySelectMultiShopDTO queryBuilder = new FeedHourlySelectMultiShopDTO();
        BeanUtils.copyProperties(feedHourlySelectMultiShopDTO, queryBuilder);
        //分四种情况
        // sp  sb  sd cpc sd vcpm
        List<AmazonAdCampaignAll> list = amazonAdCampaignAllDao.getByCampaignIdsAndShopIdListAndMarketplaceIdList(feedHourlySelectMultiShopDTO.getPuid(), feedHourlySelectMultiShopDTO.getShopIds(), feedHourlySelectMultiShopDTO.getMarketplaceIds(), ids, null);
        List<AdCampaignHourVo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            //重复id查多次报告数据
            if (queryBuilder.getMultipleIdComputation() != null && queryBuilder.getMultipleIdComputation()) {
                Map<String, Integer> idCountMap = new HashMap<>();
                ids.forEach(id -> {
                    idCountMap.put(id, idCountMap.getOrDefault(id, 0) + 1);
                });
                Map<String, AmazonAdCampaignAll> campaignIdMap = list.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
                if (!idCountMap.isEmpty()) {
                    int i = 0;
                    while (true) {
                        int j = i++;
                        List<AmazonAdCampaignAll> campaignList = idCountMap.entrySet().stream().filter(e -> campaignIdMap.containsKey(e.getKey()) && e.getValue() > j)
                                .map(e -> campaignIdMap.get(e.getKey())).collect(Collectors.toList());
                        if (campaignList.isEmpty()) {
                            break;
                        }
                        this.multiTheadQueryAmsMultiShopByCampaignList(queryBuilder, campaignList, voList);
                    }
                }
            } else {
                this.multiTheadQueryAmsMultiShopByCampaignList(queryBuilder, list, voList);
            }
        }
        return voList;
    }

    private void multiTheadQueryAmsMultiShopByCampaignList(FeedHourlySelectMultiShopDTO queryBuilder, List<AmazonAdCampaignAll> list, List<AdCampaignHourVo> voList) {
        //sp类型
        List<String> listSp = list.stream().filter(key -> Constants.SP.equalsIgnoreCase(key.getType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSp)) {
            queryBuilder.setCampaignIds(listSp);
            buildAggregateHourMultiShop(voList, queryBuilder, this::buildAdCampaignHourVoSp);
        }
        // sb类型 且 isMultiAdGroupsEnabled   || sd类型 且 cpcs
        List<String> listSb = list.stream().filter(key -> (Constants.SB.equalsIgnoreCase(key.getType()) && key.getIsMultiAdGroupsEnabled())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSb)) {
            queryBuilder.setCampaignIds(listSb);
            buildAggregateHourMultiShop(voList, queryBuilder, this::buildAdCampaignHourVoSb);
        }
        // sd类型 且 cpc
        List<String> listSdCpc = list.stream().filter(key -> Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_CPC.equalsIgnoreCase(key.getCostType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSdCpc)) {
            queryBuilder.setCampaignIds(listSdCpc);
            buildAggregateHourMultiShop(voList, queryBuilder, this::buildAdCampaignHourVoSdCpc);
        }
        // sd类型 且 vcpm
        List<String> listSdVcpm = list.stream().filter(key -> Constants.SD.equalsIgnoreCase(key.getType()) && Constants.SD_REPORT_VCPM.equalsIgnoreCase(key.getCostType())).map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(listSdVcpm)) {
            queryBuilder.setCampaignIds(listSdVcpm);
            buildAggregateHourMultiShop(voList, queryBuilder, this::buildAdCampaignHourVoSdVcpm);
        }
    }

    private void buildAggregateHourMultiShop(List<AdCampaignHourVo> voList, FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO, ThFunction<String, AmazonMarketingStreamData, AmazonMarketingStreamData, AdCampaignHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listMultiShopByHourly(feedHourlySelectMultiShopDTO);
        List<AmazonMarketingStreamData> streamDataCompare = new ArrayList<>();
        if (Integer.valueOf(1).equals(feedHourlySelectMultiShopDTO.getIsCompare())) {
            streamDataCompare = amazonMarketingStreamDataDao.listMultiShopByHourly(copyFeedHourlySelectMultiShopDTO(feedHourlySelectMultiShopDTO));
        }
        Map<String, AmazonMarketingStreamData> streamDataMap = streamData.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        Map<String, AmazonMarketingStreamData> streamDataCompareMap = streamDataCompare.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        HourConvert.HOUR_FEED_MAP.forEach((key, value) -> {
            AmazonMarketingStreamData streamData1 = streamDataMap.getOrDefault(key, new AmazonMarketingStreamData());
            AmazonMarketingStreamData marketingStreamDataCompare = streamDataCompareMap.getOrDefault(key, new AmazonMarketingStreamData());
            AdCampaignHourVo vo = hourlyFunc.apply(key, streamData1, marketingStreamDataCompare);
            voList.add(vo);
        });
    }

    private FeedHourlySelectMultiShopDTO copyFeedHourlySelectMultiShopDTO(FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO) {
        FeedHourlySelectMultiShopDTO queryBuilder = new FeedHourlySelectMultiShopDTO();
        BeanUtils.copyProperties(feedHourlySelectMultiShopDTO, queryBuilder);
        queryBuilder.setStart(feedHourlySelectMultiShopDTO.getStartCompare());
        queryBuilder.setEnd(feedHourlySelectMultiShopDTO.getEndCompare());
        return queryBuilder;
    }

    private void compareBuildMultiShop(FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO, LocalDate start, LocalDate end) {
        feedHourlySelectMultiShopDTO.setIsCompare(1);
        feedHourlySelectMultiShopDTO.setStartCompare(start);
        feedHourlySelectMultiShopDTO.setEndCompare(end);
    }

    private FeedHourlySelectMultiShopDTO buildDto(LocalDate start, LocalDate end, List<String> relationIds, String week, List<ShopAuth> shopAuth, Integer puid, CampaignAggregateHourMultiShopParamVO param) {
        FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO = new FeedHourlySelectMultiShopDTO();
        List<String> sellerIds = shopAuth.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        List<String> marketplaceIds = param.getMarketplaceIds();
        List<Integer> shopIds = shopAuth.stream().map(ShopAuth::getId).collect(Collectors.toList());
        feedHourlySelectMultiShopDTO.setSellerId(sellerIds.stream().distinct().collect(Collectors.toList()));
        feedHourlySelectMultiShopDTO.setMarketplaceIds(marketplaceIds);
        if (CollectionUtils.isNotEmpty(relationIds)) {
            feedHourlySelectMultiShopDTO.setCampaignIds(relationIds);
        }
        feedHourlySelectMultiShopDTO.setPuid(puid);
        feedHourlySelectMultiShopDTO.setShopIds(shopIds);
        feedHourlySelectMultiShopDTO.setStart(start);
        feedHourlySelectMultiShopDTO.setEnd(end);
        if (StringUtils.isNotBlank(week)) {
            feedHourlySelectMultiShopDTO.setWeekdayList(StringUtil.splitInt(week));
        }
        feedHourlySelectMultiShopDTO.setIsCompare(param.getIsCompare());
        feedHourlySelectMultiShopDTO.setTo(param.getCurrency());
        feedHourlySelectMultiShopDTO.setMultipleIdComputation(param.getMultipleIdComputation());
        return feedHourlySelectMultiShopDTO;
    }

    /**
     * 构建sd AdKeywordAndTargetHourVo
     * @param voList voList
     * @param shopAuth shopAuth
     * @param keywordIds keywordIds
     * @param dto dto
     * @return
     */
    private List<AdKeywordAndTargetHourVo> AdKeywordAndTargetHourVoForSd(List<AdKeywordAndTargetHourVo> voList, ShopAuth shopAuth, List<String> keywordIds, CommonHourlyReportSelectDto dto){
        List<String> adSdGroupIdList = amazonSdAdTargetingDao.getAdGroupIdsByTargetIds(shopAuth.getPuid(), keywordIds);
        List<String> campaignIdList = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), adSdGroupIdList);
        List<FeedTargetCampaignDto> feedTargetCampaignDtoList = amazonAdCampaignAllDao.getFeedTargetListByCampaignIds(shopAuth.getPuid(), campaignIdList);
        List<FeedTargetCampaignDto> cpcFeedCampignDtoList = feedTargetCampaignDtoList.stream().filter(e -> Constants.SD_REPORT_CPC.equalsIgnoreCase(e.getCostType())).collect(Collectors.toList());
        List<FeedTargetCampaignDto> vcpmFeedCampignDtoList = feedTargetCampaignDtoList.stream().filter(e -> Constants.SD_REPORT_VCPM.equalsIgnoreCase(e.getCostType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cpcFeedCampignDtoList)) {
            dto.setCampaignIds(cpcFeedCampignDtoList.stream().map(FeedTargetCampaignDto::getCampaignId).collect(Collectors.toList()));
            List<AmazonMarketingStreamData> cpcAmazonMarketingStreamDataList = amazonMarketingStreamDataDao.statisticsByWeek(dto);
            //对比数据
            Map<Integer, List<AmazonMarketingStreamData>> weekDayCompareMap = Maps.newHashMap();
            if (dto.getIsCompare() == 1) {
                String startDate = dto.getStartDate();
                String endDate = dto.getEndDate();
                dto.setStartDate(dto.getStartDateCompare());
                dto.setEndDate(dto.getEndDateCompare());
                List<AmazonMarketingStreamData> compareList = amazonMarketingStreamDataDao.statisticsByWeek(dto);
                weekDayCompareMap.putAll(StreamUtil.groupingBy(compareList, AmazonMarketingStreamData::getWeekday, Function.identity()));
                // 参数还原
                dto.setStartDate(startDate);
                dto.setEndDate(endDate);
            }
            Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = cpcAmazonMarketingStreamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday, LinkedHashMap::new, Collectors.toList()));
            HourConvert.weeKs.forEach(key -> {
                Map<String, AmazonMarketingStreamData> weekdayStreamDataCompareMap = StreamUtil.toMap(weekDayCompareMap.getOrDefault(key, Lists.newArrayList()), AmazonMarketingStreamData::getTime);
                Map<String, AmazonMarketingStreamData> weekdayStreamDataMap = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
                HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                    AdKeywordAndTargetHourVo vo = po2SdCpcVo(weekdayStreamDataMap.getOrDefault(k, new AmazonMarketingStreamData()), weekdayStreamDataCompareMap.getOrDefault(k, new AmazonMarketingStreamData()), value, key);
                    voList.add(vo);
                });
            });
        }
        if (CollectionUtils.isNotEmpty(vcpmFeedCampignDtoList)) {
            dto.setCampaignIds(vcpmFeedCampignDtoList.stream().map(FeedTargetCampaignDto::getCampaignId).collect(Collectors.toList()));
            List<AmazonMarketingStreamData> vcpmAmazonMarketingStreamDataList = amazonMarketingStreamDataDao.statisticsByWeek(dto);
            //对比数据
            Map<Integer, List<AmazonMarketingStreamData>> weekDayCompareMap = Maps.newHashMap();
            if (dto.getIsCompare() == 1) {
                String startDate = dto.getStartDate();
                String endDate = dto.getEndDate();
                dto.setStartDate(dto.getStartDateCompare());
                dto.setEndDate(dto.getEndDateCompare());
                List<AmazonMarketingStreamData> compareList = amazonMarketingStreamDataDao.statisticsByWeek(dto);
                weekDayCompareMap.putAll(StreamUtil.groupingBy(compareList, AmazonMarketingStreamData::getWeekday, Function.identity()));
                // 参数还原
                dto.setStartDate(startDate);
                dto.setEndDate(endDate);
            }
            Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = vcpmAmazonMarketingStreamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday, LinkedHashMap::new, Collectors.toList()));
            HourConvert.weeKs.forEach(key -> {
                Map<String, AmazonMarketingStreamData> weekdayStreamDataCompareMap = StreamUtil.toMap(weekDayCompareMap.getOrDefault(key, Lists.newArrayList()), AmazonMarketingStreamData::getTime);
                Map<String, AmazonMarketingStreamData> weekdayStreamDataMap = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
                HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                    AdKeywordAndTargetHourVo vo = po2SdVcpmVo(weekdayStreamDataMap.getOrDefault(k, new AmazonMarketingStreamData()), weekdayStreamDataCompareMap.getOrDefault(k, new AmazonMarketingStreamData()), value, key);
                    voList.add(vo);
                });
            });
        }
        return voList;
    }

    private List<AdKeywordAndTargetHourVo> AdKeywordAndTargetHourVoForSd(Integer puid, List<ShopAuth> shopAuths, List<String> keywordIds, FeedHourlySelectDTO dto){
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();
        List<String> adSdGroupIdList = amazonSdAdTargetingDao.getAdGroupIdsByTargetIds(puid, keywordIds);
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<String> marketplaceIds = shopAuths.stream().map(ShopAuth::getMarketplaceId).distinct().collect(Collectors.toList());
        List<String> campaignIdList = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(puid, shopIds, marketplaceIds, adSdGroupIdList);
        List<FeedTargetCampaignDto> feedTargetCampaignDtoList = amazonAdCampaignAllDao.getFeedTargetListByCampaignIds(puid, campaignIdList);
        List<FeedTargetCampaignDto> cpcFeedCampignDtoList = feedTargetCampaignDtoList.stream().filter(e -> Constants.SD_REPORT_CPC.equalsIgnoreCase(e.getCostType())).collect(Collectors.toList());
        List<FeedTargetCampaignDto> vcpmFeedCampignDtoList = feedTargetCampaignDtoList.stream().filter(e -> Constants.SD_REPORT_VCPM.equalsIgnoreCase(e.getCostType())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(cpcFeedCampignDtoList)) {
            dto.setCampaignIds(cpcFeedCampignDtoList.stream().map(FeedTargetCampaignDto::getCampaignId).collect(Collectors.toList()));
            List<AmazonMarketingStreamData> cpcAmazonMarketingStreamDataList = this.getWeekStreamData(dto);
            //对比数据
            Map<Integer, List<AmazonMarketingStreamData>> weekDayCompareMap = Maps.newHashMap();
            if (dto.getIsCompare() == 1) {
                LocalDate startDate = dto.getStart();
                LocalDate endDate = dto.getEnd();
                dto.setStart(dto.getStartCompare());
                dto.setEnd(dto.getEndCompare());
                List<AmazonMarketingStreamData> compareList = this.getWeekStreamData(dto);
                weekDayCompareMap.putAll(StreamUtil.groupingBy(compareList, AmazonMarketingStreamData::getWeekday, Function.identity()));
                // 参数还原
                dto.setStart(startDate);
                dto.setEnd(endDate);
            }
            Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = cpcAmazonMarketingStreamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday, LinkedHashMap::new, Collectors.toList()));
            HourConvert.weeKs.forEach(key -> {
                Map<String, AmazonMarketingStreamData> weekdayStreamDataCompareMap = StreamUtil.toMap(weekDayCompareMap.getOrDefault(key, Lists.newArrayList()), AmazonMarketingStreamData::getTime);
                Map<String, AmazonMarketingStreamData> weekdayStreamDataMap = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
                HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                    AdKeywordAndTargetHourVo vo = po2SdCpcVo(weekdayStreamDataMap.getOrDefault(k, new AmazonMarketingStreamData()), weekdayStreamDataCompareMap.getOrDefault(k, new AmazonMarketingStreamData()), value, key);
                    voList.add(vo);
                });
            });
        }
        if (CollectionUtils.isNotEmpty(vcpmFeedCampignDtoList)) {
            dto.setCampaignIds(vcpmFeedCampignDtoList.stream().map(FeedTargetCampaignDto::getCampaignId).collect(Collectors.toList()));
            List<AmazonMarketingStreamData> vcpmAmazonMarketingStreamDataList = this.getWeekStreamData(dto);
            //对比数据
            Map<Integer, List<AmazonMarketingStreamData>> weekDayCompareMap = Maps.newHashMap();
            if (dto.getIsCompare() == 1) {
                LocalDate startDate = dto.getStart();
                LocalDate endDate = dto.getEnd();
                dto.setStart(dto.getStartCompare());
                dto.setEnd(dto.getEndCompare());
                List<AmazonMarketingStreamData> compareList = this.getWeekStreamData(dto);
                weekDayCompareMap.putAll(StreamUtil.groupingBy(compareList, AmazonMarketingStreamData::getWeekday, Function.identity()));
                // 参数还原
                dto.setStart(startDate);
                dto.setEnd(endDate);
            }
            Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = vcpmAmazonMarketingStreamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday, LinkedHashMap::new, Collectors.toList()));
            HourConvert.weeKs.forEach(key -> {
                Map<String, AmazonMarketingStreamData> weekdayStreamDataCompareMap = StreamUtil.toMap(weekDayCompareMap.getOrDefault(key, Lists.newArrayList()), AmazonMarketingStreamData::getTime);
                Map<String, AmazonMarketingStreamData> weekdayStreamDataMap = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
                HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                    AdKeywordAndTargetHourVo vo = po2SdVcpmVo(weekdayStreamDataMap.getOrDefault(k, new AmazonMarketingStreamData()), weekdayStreamDataCompareMap.getOrDefault(k, new AmazonMarketingStreamData()), value, key);
                    voList.add(vo);
                });
            });
        }
        return voList;
    }

    private AdKeywordAndTargetHourVo buildAdKeywordAndTargetWeekVo(Integer key, String type, AmazonMarketingStreamData item, Integer hour, AmazonMarketingStreamData compareItem) {
        //SP
        if (Constants.SP.equalsIgnoreCase(type)) {
            return po2SpVo(item, hour, key, compareItem);
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            return po2SbVo(item, hour, key, compareItem);
        }
        throw new BizServiceException("error param, AmazonMarketingStreamData: " + JSON.toJSONString(item));
    }

    private AdKeywordAndTargetHourVo po2SdVcpmVo(AmazonMarketingStreamData po, AmazonMarketingStreamData comparePo, Integer hour, Integer weekDay) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setHour(hour);
        vo.setWeekDay(weekDay);

        AdKeywordAndTargetHourVo adTargetHourVo = convertToVcpm(po);

        vo.setVcpmCost(adTargetHourVo.getAdCost());
        vo.setVcpmImpressions(adTargetHourVo.getViewableImpressions().longValue());
        vo.setTotalImpressions(adTargetHourVo.getImpressions());
        vo.setTotalClicks(adTargetHourVo.getClicks());
        vo.setTotalAdSale(BigDecimal.ZERO);
        vo.setTotalAdSelfSale(BigDecimal.ZERO);
        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setRoas(adTargetHourVo.getRoas());
        vo.setCtr(adTargetHourVo.getCtr());
        vo.setCvr(adTargetHourVo.getCvr());
        vo.setViewableImpressions(adTargetHourVo.getViewableImpressions());
        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setVrt(MathUtil.divideIntegerByOneHundred(vo.getViewableImpressions(), vo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(vo.getClicks(), vo.getViewableImpressions()));
        vo.setVcpm(MathUtil.divideByThousand(vo.getAdCost(), vo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum()), 2));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        if (Objects.nonNull(comparePo)) {
            vo.setClicksCompare(comparePo.getClicks());
            vo.setImpressionsCompare(comparePo.getImpressions());
            vo.setAdCostCompare(BigDecimal.valueOf(comparePo.getCost()));
            vo.setAdOrderNumCompare(Objects.nonNull(comparePo.getViewAttributedConversions14d()) ? comparePo.getViewAttributedConversions14d() : 0);
            vo.setAdSaleCompare(Objects.nonNull(comparePo.getViewAttributedSales14d()) ? comparePo.getViewAttributedSales14d().setScale(4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
            vo.setAdSaleNumCompare(Objects.nonNull(comparePo.getViewAttributedUnitsOrdered14d()) ? comparePo.getViewAttributedUnitsOrdered14d() : 0);
            vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
            vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
            vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
            vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
            vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
            vo.calculateCompareRate();
        }
        return vo;
    }

    private AdKeywordAndTargetHourVo convertToVcpm(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(Objects.nonNull(data.getViewAttributedConversions14d()) ? data.getViewAttributedConversions14d() : 0);
        vo.setAdSale(Objects.nonNull(data.getViewAttributedSales14d()) ? data.getViewAttributedSales14d().setScale(4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        vo.setAdSelfSale(Objects.nonNull(data.getAttributedSales14dSameSku()) ? data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        vo.setAdOtherSale(MathUtil.subtract(data.getViewAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(Objects.nonNull(data.getViewAttributedUnitsOrdered14d()) ? data.getViewAttributedUnitsOrdered14d() : 0);
        vo.setImpressions(data.getImpressions());
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(Objects.nonNull(data.getViewImpressions()) ? data.getViewImpressions().intValue() : 0);
        vo.setOrdersNewToBrand(Objects.nonNull(data.getViewAttributedOrdersNewToBrand14d()) ? data.getViewAttributedOrdersNewToBrand14d() : 0);
        vo.setSalesNewToBrand(Objects.nonNull(data.getViewAttributedSalesNewToBrand14d()) ? data.getViewAttributedSalesNewToBrand14d() : BigDecimal.ZERO);
        vo.setUnitsOrderedNewToBrand(Objects.nonNull(data.getViewAttributedUnitsOrderedNewToBrand14d()) ? data.getViewAttributedUnitsOrderedNewToBrand14d() : 0);
        return vo;
    }

    private AdKeywordAndTargetHourVo po2SdCpcVo(AmazonMarketingStreamData po, AmazonMarketingStreamData comparePo, Integer hour, Integer weekDay) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setHour(hour);
        vo.setWeekDay(weekDay);

        AdKeywordAndTargetHourVo adTargetHourVo = convertCpcTo(po);

        vo.setVcpmCost(BigDecimal.ZERO);
        vo.setVcpmImpressions(0L);
        vo.setTotalImpressions(0L);
        vo.setTotalClicks(0L);
        vo.setTotalAdSale(adTargetHourVo.getAdSale());
        vo.setTotalAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setRoas(adTargetHourVo.getRoas());
        vo.setCtr(adTargetHourVo.getCtr());
        vo.setCvr(adTargetHourVo.getCvr());
        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(vo.getAdSale(), BigDecimal.valueOf(vo.getAdOrderNum()), 2));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(vo.getAdSelfSale(), BigDecimal.valueOf(vo.getSelfAdOrderNum())));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(vo.getAdSale().subtract(vo.getAdSelfSale()), BigDecimal.valueOf(vo.getAdOrderNum() - vo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(vo.getSalesNewToBrand(), BigDecimal.valueOf(100)), vo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getAdSaleNum())));
        if (Objects.nonNull(comparePo)) {
            vo.setClicksCompare(comparePo.getClicks());
            vo.setImpressionsCompare(comparePo.getImpressions());
            vo.setAdCostCompare(BigDecimal.valueOf(comparePo.getCost()));
            vo.setAdSaleCompare(comparePo.getAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
            vo.setAdOrderNumCompare(comparePo.getAttributedConversions14d());
            vo.setAdSaleNumCompare(comparePo.getAttributedUnitsOrdered14d());
            vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
            vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
            vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
            vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
            vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
            vo.calculateCompareRate();
        }
        return vo;
    }

    private AdKeywordAndTargetHourVo convertCpcTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions14d());
        vo.setSelfAdOrderNum(data.getAttributedConversions14dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions14d(), data.getAttributedConversions14dSameSku()));
        vo.setAdSale(data.getAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered14d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered14dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered14d(), data.getAttributedUnitsOrdered14dSameSku()));
        vo.setImpressions(data.getImpressions());
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(0);
        vo.setOrdersNewToBrand(Objects.nonNull(data.getAttributedOrdersNewToBrand14d()) ? data.getAttributedOrdersNewToBrand14d() : 0);
        vo.setUnitsOrderedNewToBrand(Objects.nonNull(data.getAttributedUnitsOrderedNewToBrand14d()) ? data.getAttributedUnitsOrderedNewToBrand14d() : 0);
        vo.setSalesNewToBrand(Objects.nonNull(data.getAttributedSalesNewToBrand14d()) ? data.getAttributedSalesNewToBrand14d() : BigDecimal.ZERO);
        return vo;
    }

    private AdKeywordAndTargetHourVo po2SbVo(AmazonMarketingStreamData po, Integer hour, Integer weekDay, AmazonMarketingStreamData compareItem) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setHour(hour);
        vo.setWeekDay(weekDay);

        AdKeywordAndTargetHourVo adTargetHourVo = sbConvertTo(po);
        vo.setAdSale(adTargetHourVo.getAdSale());
        vo.setTotalAdSale(adTargetHourVo.getAdSale());
        vo.setAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setTotalAdSelfSale(adTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adTargetHourVo.getAdCost());
        vo.setClicks(adTargetHourVo.getClicks());
        vo.setTotalClicks(adTargetHourVo.getClicks());
        vo.setImpressions(adTargetHourVo.getImpressions());
        vo.setTotalImpressions(adTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adTargetHourVo.getAdCostPerClick());
        vo.setCpa(adTargetHourVo.getCpa());
        vo.setAcos(adTargetHourVo.getAcos());
        vo.setRoas(adTargetHourVo.getRoas());
        vo.setCtr(adTargetHourVo.getCtr());
        vo.setCvr(adTargetHourVo.getCvr());
        vo.setViewableImpressions(adTargetHourVo.getViewableImpressions());
        vo.setOrdersNewToBrand(adTargetHourVo.getOrdersNewToBrand());
        vo.setUnitsOrderedNewToBrand(adTargetHourVo.getUnitsOrderedNewToBrand());
        vo.setSalesNewToBrand(adTargetHourVo.getSalesNewToBrand());
        vo.setVrt(MathUtil.divideIntegerByOneHundred(adTargetHourVo.getViewableImpressions(), adTargetHourVo.getImpressions()));
        vo.setVCtr(MathUtil.divideIntegerByOneHundred(adTargetHourVo.getClicks(), adTargetHourVo.getViewableImpressions()));
        vo.setAdvertisingUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSale(), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum()), 2));
        vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSelfSale(), BigDecimal.valueOf(adTargetHourVo.getSelfAdOrderNum()), 2));
        vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(adTargetHourVo.getAdSale().subtract(adTargetHourVo.getAdSelfSale()), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum() - adTargetHourVo.getSelfAdOrderNum())));
        vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVo.getAdOrderNum())));
        vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(adTargetHourVo.getSalesNewToBrand(), BigDecimal.valueOf(100)), adTargetHourVo.getAdSale()));
        vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adTargetHourVo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adTargetHourVo.getAdSaleNum())));
        if (Objects.nonNull(compareItem)) {
            AdKeywordAndTargetHourVo compareVO = sbConvertTo(compareItem);
            vo.setAdCostCompare(compareVO.getAdCost());
            vo.setClicksCompare(compareVO.getClicks());
            vo.setImpressionsCompare(compareVO.getImpressions());
            vo.setAdSaleCompare(compareVO.getAdSale());
            vo.setAdOrderNumCompare(compareVO.getAdOrderNum());
            vo.setAdSaleNumCompare(compareVO.getAdSaleNum());
            vo.setAdCostPerClickCompare(compareVO.getAdCostPerClick());
            vo.setAcosCompare(compareVO.getAcos());
            vo.setRoasCompare(compareVO.getRoas());
            vo.setCtrCompare(compareVO.getCtr());
            vo.setCvrCompare(compareVO.getCvr());
            vo.calculateCompareRate();
        }
        return vo;
    }

    private AdKeywordAndTargetHourVo sbConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions14d());
        vo.setSelfAdOrderNum(data.getAttributedConversions14dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions14d(), data.getAttributedConversions14dSameSku()));
        vo.setAdSale(data.getAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered14d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered14dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered14d(), data.getAttributedUnitsOrdered14dSameSku()));
        vo.setImpressions(data.getImpressions());
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));

        vo.setViewableImpressions(Objects.nonNull(data.getViewableImpressions()) ? data.getViewableImpressions().intValue() : 0);
        vo.setOrdersNewToBrand(Objects.nonNull(data.getAttributedOrdersNewToBrand14d()) ? data.getAttributedOrdersNewToBrand14d() : 0);
        vo.setUnitsOrderedNewToBrand(Objects.nonNull(data.getAttributedUnitsOrderedNewToBrand14d()) ? data.getAttributedUnitsOrderedNewToBrand14d() : 0);
        vo.setSalesNewToBrand(Objects.nonNull(data.getAttributedSalesNewToBrand14d()) ? data.getAttributedSalesNewToBrand14d() : BigDecimal.ZERO);
        return vo;
    }

    private AdKeywordAndTargetHourVo po2SpVo(AmazonMarketingStreamData po, Integer hour, Integer weekDay, AmazonMarketingStreamData compareItem) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setHour(hour);
        vo.setWeekDay(weekDay);

        vo.setAdSale(po.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(po.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(po.getAttributedSales7d(), po.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOrderNum(po.getAttributedConversions7d());
        vo.setSelfAdOrderNum(po.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(po.getAttributedConversions7d(), po.getAttributedConversions7dSameSku()));
        vo.setAdSaleNum(po.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(po.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(po.getAttributedUnitsOrdered7d(), po.getAttributedUnitsOrdered7dSameSku()));
        vo.setAdCost(BigDecimal.valueOf(po.getCost()));
        vo.setClicks(po.getClicks());
        vo.setImpressions(po.getImpressions());
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        if (Objects.nonNull(compareItem)) {
            vo.setClicksCompare(compareItem.getClicks());
            vo.setAdCostCompare(BigDecimal.valueOf(compareItem.getCost()));
            vo.setImpressionsCompare(compareItem.getImpressions());
            vo.setAdSaleCompare(compareItem.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
            vo.setAdOrderNumCompare(compareItem.getAttributedConversions7d());
            vo.setAdSaleNumCompare(compareItem.getAttributedUnitsOrdered7d());
            vo.setAdCostPerClickCompare(MathUtil.divideByZero(vo.getAdCostCompare(), BigDecimal.valueOf(vo.getClicksCompare())));
            vo.setAcosCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdCostCompare().multiply(new BigDecimal("100")).divide(vo.getAdSaleCompare(), 4, RoundingMode.HALF_UP));
            vo.setCtrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicksCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressionsCompare())));
            vo.setCvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNumCompare()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicksCompare())));
            vo.setRoasCompare(vo.getAdSaleCompare().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCostCompare().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO :
                    vo.getAdSaleCompare().divide(vo.getAdCostCompare(), 4, RoundingMode.HALF_UP));
            vo.calculateCompareRate();
        }
        return vo;
    }

    @Override
    public List<AdGroupHourVo> multiThreadQueryAmsForGroup(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) {
        FeedHourlySelectDTO queryBuilder = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        queryBuilder.setAdGroupIds(ids);
        // sp sb 类型正常查询
        // sd 区分 vcpm 和 cpc 类型；
        List<AdGroupHourVo> voList = new ArrayList<>();
        if (Constants.SD.equalsIgnoreCase(feedHourlySelectDTO.getType())) {
            List<String> campaignIds = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), feedHourlySelectDTO.getMarketplaceId(), ids);
            if (campaignIds.size() == 0) {
                buildAggregateWeekForGroup(voList, queryBuilder, this::buildAdWeekVoSbForGroup);
            } else {
                List<String> vcpmCampaignIds = amazonAdCampaignAllDao.getVcpmCampaignIdsByCampaignIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), campaignIds);

                if (CollectionUtils.isNotEmpty(vcpmCampaignIds)) {
                    //查询vcpm
                    queryBuilder.setCampaignIds(vcpmCampaignIds);
                    buildAggregateWeekForGroup(voList, queryBuilder, this::buildAdWeekVoSdVcpmForGroup);
                    //查询cpc
                    if (campaignIds.size() != vcpmCampaignIds.size()) {
                        queryBuilder.setCampaignIds(null);
                        queryBuilder.setExcludeCampaignIds(vcpmCampaignIds);
                        buildAggregateWeekForGroup(voList, queryBuilder, this::buildAdWeekVoSdCpcForGroup);
                    }
                } else {
                    buildAggregateWeekForGroup(voList, queryBuilder, this::buildAdWeekVoSbForGroup);
                }
            }
        } else {
            buildAggregateWeekForGroup(voList, queryBuilder, Constants.SB.equalsIgnoreCase(feedHourlySelectDTO.getType()) ? this::buildAdWeekVoSbForGroup : this::buildAdWeekVoSpForGroup);
        }
        return voList;
    }

    private void buildAggregateWeekForGroup(List<AdGroupHourVo> voList, FeedHourlySelectDTO feedHourlySelectDTO, TwFunction<AmazonMarketingStreamData, AmazonMarketingStreamData, AdGroupHourVo> weeklyFunc) {

        CommonHourlyReportSelectDto queryDto = new CommonHourlyReportSelectDto();
        queryDto.setSellerId(feedHourlySelectDTO.getSellerId());
        queryDto.setMarketplaceId(feedHourlySelectDTO.getMarketplaceId());
        queryDto.setType(feedHourlySelectDTO.getType());
        queryDto.setStartDate(feedHourlySelectDTO.getStart().toString());
        queryDto.setEndDate(feedHourlySelectDTO.getEnd().toString());
        queryDto.setWeekdayList(feedHourlySelectDTO.getWeekdayList());
        queryDto.setGroupIds(feedHourlySelectDTO.getAdGroupIds());
        queryDto.setCampaignIds(feedHourlySelectDTO.getCampaignIds());
        queryDto.setExcludeCampaignIds(feedHourlySelectDTO.getExcludeCampaignIds());
        List<AmazonMarketingStreamData> streamDataList = amazonMarketingStreamDataDao.statisticsByWeek(queryDto);
        List<AmazonMarketingStreamData> streamDataCompare = new ArrayList<>();
        if (Integer.valueOf(1).equals(feedHourlySelectDTO.getIsCompare())) {
            streamDataCompare = amazonMarketingStreamDataDao.statisticsByWeek(copyCommonHourlyReportSelectDto(queryDto, feedHourlySelectDTO));
        }
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamDataList.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        Map<Integer, List<AmazonMarketingStreamData>> streamDataCompareMap = streamDataCompare.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamData = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            Map<String, AmazonMarketingStreamData> marketingStreamDataCompare =  streamDataCompareMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                AdGroupHourVo vo = weeklyFunc.apply(streamData.getOrDefault(k, new AmazonMarketingStreamData()), marketingStreamDataCompare.getOrDefault(k, new AmazonMarketingStreamData()));
                vo.setHour(value);
                vo.setWeekDay(key);
                voList.add(vo);
            });
        });
    }


    @Override
    public List<AdGroupHourVo> multiThreadQueryGroupAms(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) {
        FeedHourlySelectDTO queryBuilder = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        // sp sb 类型正常查询
        // sd 区分 vcpm 和 cpc 类型；


        List<AdGroupHourVo> voList = new ArrayList<>();
        queryBuilder.setAdGroupIds(ids);
        if (Constants.SD.equalsIgnoreCase(feedHourlySelectDTO.getType())) {
            List<String> campaignIds = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), feedHourlySelectDTO.getMarketplaceId(), ids);
            if (campaignIds.size() == 0) {
                buildAggregateGroupHour(voList, queryBuilder, this::buildAdGroupHourVoSb);
            } else {
                List<String> vcpmCampaignIds = amazonAdCampaignAllDao.getVcpmCampaignIdsByCampaignIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), campaignIds);

                if (CollectionUtils.isNotEmpty(vcpmCampaignIds)) {
                    //查询vcpm
                    queryBuilder.setCampaignIds(vcpmCampaignIds);
                    buildAggregateGroupHour(voList, queryBuilder, this::buildAdGroupHourVoSdVcpm);
                    //查询cpc
                    if (campaignIds.size() != vcpmCampaignIds.size()) {
                        queryBuilder.setCampaignIds(null);
                        queryBuilder.setExcludeCampaignIds(vcpmCampaignIds);
                        buildAggregateGroupHour(voList, queryBuilder, this::buildAdGroupHourVoSdCpc);
                    }
                } else {
                    buildAggregateGroupHour(voList, queryBuilder, this::buildAdGroupHourVoSb);
                }
            }


        } else {
            buildAggregateGroupHour(voList, queryBuilder, Constants.SB.equalsIgnoreCase(feedHourlySelectDTO.getType()) ? this::buildAdGroupHourVoSb : this::buildAdGroupHourVoSp);
        }
        return voList;
    }


    private void buildAggregateGroupHour(List<AdGroupHourVo> voList, FeedHourlySelectDTO feedHourlySelectDTO, ThFunction<String, AmazonMarketingStreamData, AmazonMarketingStreamData, AdGroupHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listByHourly(feedHourlySelectDTO);
        List<AmazonMarketingStreamData> streamDataCompare = new ArrayList<>();
        if (Integer.valueOf(1).equals(feedHourlySelectDTO.getIsCompare())) {
            streamDataCompare = amazonMarketingStreamDataDao.listByHourly(copyFeedHourlySelectDTO(feedHourlySelectDTO));
        }
        Map<String, AmazonMarketingStreamData> streamDataMap = streamData.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        Map<String, AmazonMarketingStreamData> streamDataCompareMap = streamDataCompare.stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, e1 -> e1, (e1, e2) -> e2));
        HourConvert.HOUR_FEED_MAP.forEach((key, value) -> {
            AmazonMarketingStreamData streamData1 = streamDataMap.getOrDefault(key, new AmazonMarketingStreamData());
            AmazonMarketingStreamData marketingStreamDataCompare = streamDataCompareMap.getOrDefault(key, new AmazonMarketingStreamData());
            AdGroupHourVo vo = hourlyFunc.apply(key, streamData1, marketingStreamDataCompare);
            voList.add(vo);
        });
    }


    private AdGroupHourVo buildAdGroupHourVoSdVcpm(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getViewAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getViewAttributedConversions14d()))
                .selfAdOrderNum(0)
                .adSaleNum(getDefaultInteger(streamData1.getViewAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getViewAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getViewAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getViewAttributedSalesNewToBrand14d()))
                .vcpmImpressions(getDefaultInteger(streamData1.getViewImpressions()).intValue())
                .vcpmCost(getDefaultBigDecimal(streamData1.getCost()))
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(BigDecimal.ZERO)
                .totalAdSelfSale(BigDecimal.ZERO)
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getViewAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedConversions14d()));
        vo.setOtherAdOrderNum(0);
        return vo;
    }
    private AdGroupHourVo buildAdWeekVoSdVcpmForGroup(AmazonMarketingStreamData streamData, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
            .adSale(getDefaultBigDecimal(streamData.getViewAttributedSales14d()))
            .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
            .adOrderNum(getDefaultInteger(streamData.getViewAttributedConversions14d()))
            .selfAdOrderNum(0)
            .adSaleNum(getDefaultInteger(streamData.getViewAttributedUnitsOrdered14d()))
            .adSelfSaleNum(0)
            .adOtherSaleNum(0)
            .adCost(getDefaultBigDecimal(streamData.getCost()))
            .clicks(getDefaultInteger(streamData.getClicks()))
            .impressions(getDefaultInteger(streamData.getImpressions()))
            .viewableImpressions(getDefaultInteger(streamData.getViewImpressions()).intValue())
            .ordersNewToBrand(getDefaultInteger(streamData.getViewAttributedOrdersNewToBrand14d()))
            .unitsOrderedNewToBrand(getDefaultInteger(streamData.getViewAttributedUnitsOrderedNewToBrand14d()))
            .salesNewToBrand(getDefaultBigDecimal(streamData.getViewAttributedSalesNewToBrand14d()))
            .vcpmImpressions(getDefaultInteger(streamData.getViewImpressions()).intValue())
            .vcpmCost(getDefaultBigDecimal(streamData.getCost()))
            .totalImpressions(getDefaultInteger(streamData.getImpressions()))
            .totalClicks(getDefaultInteger(streamData.getClicks()))
            .totalAdSale(BigDecimal.ZERO)
            .totalAdSelfSale(BigDecimal.ZERO)
            .build();
        vo.setOtherAdOrderNum(0);
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getViewAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getViewAttributedConversions14d()));
        return vo;
    }

    private AdGroupHourVo buildAdGroupHourVoSdCpc(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adOtherSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .vcpmImpressions(0)
                .vcpmCost(BigDecimal.ZERO)
                .totalImpressions(0L)
                .totalClicks(0L)
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }

    private AdGroupHourVo buildAdWeekVoSdCpcForGroup(AmazonMarketingStreamData streamData, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
            .adSale(getDefaultBigDecimal(streamData.getAttributedSales14d()))
            .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
            .adOrderNum(getDefaultInteger(streamData.getAttributedConversions14d()))
            .selfAdOrderNum(getDefaultInteger(streamData.getAttributedConversions14dSameSku()))
            .adSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered14d()))
            .adSelfSaleNum(0)
            .adOtherSaleNum(0)
            .adCost(getDefaultBigDecimal(streamData.getCost()))
            .clicks(getDefaultInteger(streamData.getClicks()))
            .impressions(getDefaultInteger(streamData.getImpressions()))
            .viewableImpressions(0)
            .ordersNewToBrand(getDefaultInteger(streamData.getAttributedOrdersNewToBrand14d()))
            .unitsOrderedNewToBrand(getDefaultInteger(streamData.getAttributedUnitsOrderedNewToBrand14d()))
            .salesNewToBrand(getDefaultBigDecimal(streamData.getAttributedSalesNewToBrand14d()))
            .vcpmImpressions(0)
            .vcpmCost(BigDecimal.ZERO)
            .totalImpressions(0L)
            .totalClicks(0L)
            .totalAdSale(getDefaultBigDecimal(streamData.getAttributedSales14d()))
            .totalAdSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
            .build();

        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }


    private AdGroupHourVo buildAdGroupHourVoSp(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales7d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales7dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions7d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions7dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7d()))
                .adSelfSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adOtherSaleNum(MathUtil.subtractInteger(streamData1.getAttributedUnitsOrdered7d(), streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .ordersNewToBrand(0)
                .unitsOrderedNewToBrand(0)
                .salesNewToBrand(BigDecimal.ZERO)
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered7d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales7d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions7d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }

    private AdGroupHourVo buildAdWeekVoSpForGroup(AmazonMarketingStreamData streamData, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
            .adSale(getDefaultBigDecimal(streamData.getAttributedSales7d()))
            .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales7dSameSku()))
            .adOrderNum(getDefaultInteger(streamData.getAttributedConversions7d()))
            .selfAdOrderNum(getDefaultInteger(streamData.getAttributedConversions7dSameSku()))
            .adSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered7d()))
            .adSelfSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered7dSameSku()))
            .adOtherSaleNum(MathUtil.subtractInteger(streamData.getAttributedUnitsOrdered7d(), streamData.getAttributedUnitsOrdered7dSameSku()))
            .adCost(getDefaultBigDecimal(streamData.getCost()))
            .clicks(getDefaultInteger(streamData.getClicks()))
            .impressions(getDefaultInteger(streamData.getImpressions()))
            .viewableImpressions(getDefaultInteger(streamData.getViewableImpressions()).intValue())
            .ordersNewToBrand(0)
            .unitsOrderedNewToBrand(0)
            .salesNewToBrand(BigDecimal.ZERO)
            .build();

        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered7d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales7d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions7d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }


    private AdGroupHourVo buildAdGroupHourVoSb(String key, AmazonMarketingStreamData streamData1, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
                .label(HourConvert.HOUR_FEED_MAP.get(key))
                .hour(HourConvert.HOUR_FEED_SORT_MAP.get(key))
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .vcpmImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .vcpmCost(getDefaultBigDecimal(streamData1.getCost()))
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .totalImpressions(getDefaultInteger(streamData1.getImpressions()))
                .totalClicks(getDefaultInteger(streamData1.getClicks()))
                .totalAdSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .totalAdSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .build();
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        return vo;
    }

    private AdGroupHourVo buildAdWeekVoSbForGroup(AmazonMarketingStreamData streamData, AmazonMarketingStreamData marketingStreamDataCompare) {
        AdGroupHourVo vo = AdGroupHourVo.builder()
            .adSale(getDefaultBigDecimal(streamData.getAttributedSales14d()))
            .adSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
            .adOrderNum(getDefaultInteger(streamData.getAttributedConversions14d()))
            .selfAdOrderNum(getDefaultInteger(streamData.getAttributedConversions14dSameSku()))
            .adSaleNum(getDefaultInteger(streamData.getAttributedUnitsOrdered14d()))
            .adSelfSaleNum(0)
            .adCost(getDefaultBigDecimal(streamData.getCost()))
            .clicks(getDefaultInteger(streamData.getClicks()))
            .impressions(getDefaultInteger(streamData.getImpressions()))
            .viewableImpressions(getDefaultInteger(streamData.getViewableImpressions()).intValue())
            .vcpmImpressions(getDefaultInteger(streamData.getViewableImpressions()).intValue())
            .vcpmCost(getDefaultBigDecimal(streamData.getCost()))
            .ordersNewToBrand(getDefaultInteger(streamData.getAttributedOrdersNewToBrand14d()))
            .unitsOrderedNewToBrand(getDefaultInteger(streamData.getAttributedUnitsOrderedNewToBrand14d()))
            .salesNewToBrand(getDefaultBigDecimal(streamData.getAttributedSalesNewToBrand14d()))
            .totalImpressions(getDefaultInteger(streamData.getImpressions()))
            .totalClicks(getDefaultInteger(streamData.getClicks()))
            .totalAdSale(getDefaultBigDecimal(streamData.getAttributedSales14d()))
            .totalAdSelfSale(getDefaultBigDecimal(streamData.getAttributedSales14dSameSku()))
            .build();
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(vo.getAdOrderNum(), vo.getSelfAdOrderNum()));
        // 类比
        vo.setClicksCompare(getDefaultInteger(marketingStreamDataCompare.getClicks()));
        vo.setImpressionsCompare(getDefaultInteger(marketingStreamDataCompare.getImpressions()));
        vo.setAdSaleNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedUnitsOrdered14d()));
        vo.setAdSaleCompare(getDefaultBigDecimal(marketingStreamDataCompare.getAttributedSales14d()));
        vo.setAdCostCompare(getDefaultBigDecimal(marketingStreamDataCompare.getCost()));
        vo.setAdOrderNumCompare(getDefaultInteger(marketingStreamDataCompare.getAttributedConversions14d()));
        return vo;

    }


    private void filterAdGroupSumMetricData(List<AdGroupHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdGroupHourVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                    map(AdGroupHourVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                    map(AdGroupHourVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                    map(AdGroupHourVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                    map(AdGroupHourVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }


    private void filterAdGroupMetricData(List<AdGroupHourVo> voList, AdMetricDto adMetricDto) {
        for (AdGroupHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeAdGroupMetricData(adMetricDto, vo);
        }
    }


    private void computeAdGroupMetricData(AdMetricDto adMetricDto, AdGroupHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }


    @Override
    public List<AdGroupHourVo> listAggregateAdGroupWeekList(ShopAuth shopInfo, List<String> aggregateIds, GroupHourParam param) {
        FeedHourlySelectDTO feedHourlySelectDTO = build(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                null,
                shopInfo.getSellingPartnerId(),
                shopInfo.getMarketplaceId(),
                null,
                param.getWeeks(),
                null,
                null,
                shopInfo);
        List<List<AdGroupHourVo>> list = multiThreadQueryAndMergeUtil.multiThreadQuery(ThreadPoolUtil.getCpcAggregateIdsSyncPool(),
                () -> aggregateIds, feedHourlySelectDTO, this::multiThreadQueryAdGroupWeekAms);
        return ReportChartUtil.getAdGroupWeekReportVos(list);
    }


    private List<AdGroupHourVo> multiThreadQueryAdGroupWeekAms(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) {
        FeedHourlySelectDTO queryBuilder = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(feedHourlySelectDTO, queryBuilder);
        // sp sb 类型正常查询
        // sd 区分 vcpm 和 cpc 类型；


        List<AdGroupHourVo> voList = new ArrayList<>();
        if (Constants.SD.equalsIgnoreCase(feedHourlySelectDTO.getType())) {
            List<String> campaignIds = amazonSdAdGroupDao.getAdCampaignIdsByGroupIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), feedHourlySelectDTO.getMarketplaceId(), ids);
            queryBuilder.setAdGroupIds(ids);
            if (campaignIds.size() == 0) {
                buildAggregateAdGroupWeek(voList, queryBuilder, this::buildAdGroupWeekVoSp);
            } else {
                List<String> vcpmCampaignIds = amazonAdCampaignAllDao.getVcpmCampaignIdsByCampaignIds(feedHourlySelectDTO.getPuid(), feedHourlySelectDTO.getShopId(), campaignIds);

                if (CollectionUtils.isNotEmpty(vcpmCampaignIds)) {
                    //查询vcpm
                    queryBuilder.setCampaignIds(vcpmCampaignIds);
                    buildAggregateAdGroupWeek(voList, queryBuilder, this::buildAdGroupWeekVoSdVcpm);
                    //查询cpc
                    if (campaignIds.size() != vcpmCampaignIds.size()) {
                        queryBuilder.setCampaignIds(null);
                        queryBuilder.setExcludeCampaignIds(vcpmCampaignIds);
                        buildAggregateAdGroupWeek(voList, queryBuilder, this::buildAdGroupWeekVoSdCpc);
                    }
                } else {
                    buildAggregateAdGroupWeek(voList, queryBuilder, this::buildAdGroupWeekVoSb);
                }
            }


        } else {
            buildAggregateAdGroupWeek(voList, queryBuilder, Constants.SB.equalsIgnoreCase(feedHourlySelectDTO.getType()) ? this::buildAdGroupWeekVoSb : this::buildAdGroupWeekVoSp);
        }
        return voList;
    }


    private void buildAggregateAdGroupWeek(List<AdGroupHourVo> voList, FeedHourlySelectDTO feedHourlySelectDTO, ThFunction<Integer, AmazonMarketingStreamData, Integer, AdGroupHourVo> hourlyFunc) {
        List<AmazonMarketingStreamData> streamData = amazonMarketingStreamDataDao.listWeekByHourly(feedHourlySelectDTO);
        Map<Integer, List<AmazonMarketingStreamData>> streamDataMap = streamData.stream().collect(Collectors.groupingBy(AmazonMarketingStreamData::getWeekday));
        HourConvert.weeKs.forEach(key -> {
            Map<String, AmazonMarketingStreamData> streamData1 = streamDataMap.getOrDefault(key, new ArrayList<>()).stream().collect(Collectors.toMap(AmazonMarketingStreamData::getTime, Function.identity()));
            HourConvert.HOUR_FEED_SORT_MAP.forEach((k, value) -> {
                AdGroupHourVo vo = hourlyFunc.apply(key, streamData1.getOrDefault(k, new AmazonMarketingStreamData()), value);
                voList.add(vo);
            });
        });
    }

    private AdGroupHourVo buildAdGroupWeekVoSb(Integer key, AmazonMarketingStreamData streamData1, Integer hour) {
        return AdGroupHourVo.builder()
                .weekDay(key)
                .hour(hour)
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .build();
    }

    private AdGroupHourVo buildAdGroupWeekVoSdCpc(Integer key, AmazonMarketingStreamData streamData1, Integer hour) {
        return AdGroupHourVo.builder()
                .weekDay(key)
                .hour(hour)
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions14d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions14dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(0)
                .ordersNewToBrand(getDefaultInteger(streamData1.getAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getAttributedSalesNewToBrand14d()))
                .vcpmCost(BigDecimal.ZERO)
                .vcpmImpressions(0)
                .build();
    }


    private AdGroupHourVo buildAdGroupWeekVoSp(Integer key, AmazonMarketingStreamData streamData1, Integer hour) {
        return AdGroupHourVo.builder()
                .hour(hour)
                .weekDay(key)
                .adSale(getDefaultBigDecimal(streamData1.getAttributedSales7d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales7dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getAttributedConversions7d()))
                .selfAdOrderNum(getDefaultInteger(streamData1.getAttributedConversions7dSameSku()))
                .adSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7d()))
                .adSelfSaleNum(getDefaultInteger(streamData1.getAttributedUnitsOrdered7dSameSku()))
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .ordersNewToBrand(0)
                .unitsOrderedNewToBrand(0)
                .salesNewToBrand(BigDecimal.ZERO)
                .build();
    }

    private AdGroupHourVo buildAdGroupWeekVoSdVcpm(Integer key, AmazonMarketingStreamData streamData1, Integer hour) {
        return AdGroupHourVo.builder()
                .weekDay(key)
                .hour(hour)
                .adSale(getDefaultBigDecimal(streamData1.getViewAttributedSales14d()))
                .adSelfSale(getDefaultBigDecimal(streamData1.getAttributedSales14dSameSku()))
                .adOrderNum(getDefaultInteger(streamData1.getViewAttributedConversions14d()))
                .selfAdOrderNum(0)
                .adSaleNum(getDefaultInteger(streamData1.getViewAttributedUnitsOrdered14d()))
                .adSelfSaleNum(0)
                .adCost(getDefaultBigDecimal(streamData1.getCost()))
                .clicks(getDefaultInteger(streamData1.getClicks()))
                .impressions(getDefaultInteger(streamData1.getImpressions()))
                .viewableImpressions(getDefaultInteger(streamData1.getViewableImpressions()).intValue())
                .ordersNewToBrand(getDefaultInteger(streamData1.getViewAttributedOrdersNewToBrand14d()))
                .unitsOrderedNewToBrand(getDefaultInteger(streamData1.getViewAttributedUnitsOrderedNewToBrand14d()))
                .salesNewToBrand(getDefaultBigDecimal(streamData1.getViewAttributedSalesNewToBrand14d()))
                .build();
    }

    /**
     * 查询完整数据的开始时间
     *
     * @param sellerIds
     * @param marketplaceId
     * @param startDate
     * @return
     */
    @Override
    public String getSellerIdsDataStartTime(List<String> sellerIds, String marketplaceId, String startDate) {
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE);
        //查询完整数据开始时间,不完整数据不呈现
        AmazonMarketingStreamSubscriber subscriber = amazonMarketingStreamSubscriberDao.
                getBySellerIdsAndMarketplaceId(sellerIds, marketplaceId);
        if (subscriber != null) {
            start = start.isBefore(subscriber.getDataStart()) ? subscriber.getDataStart() : start;
        }
        return start.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    /**
     * 查询完整数据的开始时间
     *
     * @param sellerId
     * @param marketplaceId
     * @param startDate
     * @return
     */
    @Override
    public String getDataStartTime(String sellerId, String marketplaceId, String startDate) {
        LocalDate start = LocalDate.parse(startDate, DateTimeFormatter.ISO_LOCAL_DATE);
        //查询完整数据开始时间,不完整数据不呈现
        AmazonMarketingStreamSubscriber subscriber = amazonMarketingStreamSubscriberDao.
                getBySellerIdAndMarketplaceId(sellerId, marketplaceId);
        if (subscriber != null) {
            start = start.isBefore(subscriber.getDataStart()) ? subscriber.getDataStart() : start;
        }
        return start.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }


    private CommonHourlyReportSelectDto copyCommonHourlyReportSelectDto(CommonHourlyReportSelectDto queryDto, FeedHourlySelectDTO feedHourlySelectDTO) {
        CommonHourlyReportSelectDto queryBuilder = new CommonHourlyReportSelectDto();
        BeanUtils.copyProperties(queryDto, queryBuilder);
        queryBuilder.setStartDate(feedHourlySelectDTO.getStartCompare().format(DateTimeFormatter.ISO_LOCAL_DATE));
        queryBuilder.setEndDate(feedHourlySelectDTO.getEndCompare().format(DateTimeFormatter.ISO_LOCAL_DATE));
        return queryBuilder;
    }
}
