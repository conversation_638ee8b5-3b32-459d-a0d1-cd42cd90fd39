package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.AdProductDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdMarkupTagDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdTagDao;
import com.meiyunji.sponsored.service.cpc.dto.AdProductCommonInfoDto;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcProductService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageVo;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import com.meiyunji.sponsored.service.enums.SBCampaignCostTypeEnum;
import com.meiyunji.sponsored.service.enums.StateEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.vo.AdProductExportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.export.util.ExportStringUtil.formatToNumber;
import static com.meiyunji.sponsored.service.export.util.ExportStringUtil.modifyFormat;

@Service(AdManagePageExportTaskConstant.PRODUCT)
@Slf4j
public class AdProductExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcProductService cpcProductService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;

    private final static List<String> SP_EXCLUDE_FIELDS = Arrays.asList("viewImpressions", "vcpm", "ordersNewToBrandFTD", "orderRateNewToBrandFTD", "salesNewToBrandFTD", "salesRateNewToBrandFTD", "unitsOrderedNewToBrandFTD", "unitsOrderedRateNewToBrandFTD",
        "newToBrandDetailPageViews", "addToCart", "addToCartRate", "ecpAddToCart", "videoFirstQuartileViews", "videoMidpointViews", "videoThirdQuartileViews",
        "videoCompleteViews", "videoUnmutes", "viewabilityRate", "viewClickThroughRate", "brandedSearches", "detailPageViews", "cumulativeReach", "impressionsFrequencyAverage");
    private final static List<String> SD_EXCLUDE_FIELDS = Arrays.asList("adOtherOrderNum", "adOtherSales", "adSelfSaleNum", "adOtherSaleNum");



    @Override
    public void export(AdManagePageExportTask task) {
        //1,参数校验
        AdProductPageParam param = JSONUtil.jsonToObject(task.getParam(), AdProductPageParam.class);
        if (param == null) {
            log.error(String.format("Placement export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());

        //2，构建excel的导出数据
        List<AdProductDataResponse.AdProductPageVo> list = buildExportData(param, shopAuth);
        if (CollectionUtils.isEmpty(list)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        //3，导出excel
        List<String> urlList = exportToExcel(param, shopAuth, list, task.getPuid());

        //4，修改导出任务执行状态并写缓存
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    /**
     * 导出excel
     *
     * @param param
     * @param shopAuth
     * @param dataList
     * @param puid
     * @return
     */
    private List<String> exportToExcel(AdProductPageParam param, ShopAuth shopAuth, List<AdProductDataResponse.AdProductPageVo> dataList, Integer puid) {
        List<String> excludeFileds = Lists.newArrayList();
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            excludeFileds = SP_EXCLUDE_FIELDS;
        }
        if (Constants.SD.equalsIgnoreCase(param.getType())) {
            excludeFileds = SD_EXCLUDE_FIELDS;
        }
        String fileName = shopAuth.getName() + "_广告产品" + "_" + param.getStartDate() + "_" + param.getEndDate();
        List<String> urlList = new ArrayList<>();
        int count = 0;
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AdProductExportVo.class);
        List<List<AdProductDataResponse.AdProductPageVo>> partition = Lists.partition(dataList, Constants.FILE_MAX_SIZE);
        for (List<AdProductDataResponse.AdProductPageVo> list1 : partition) {
            List<AdProductExportVo> adProductDataVoList = new LinkedList<>();
            for (AdProductDataResponse.AdProductPageVo keyVo : list1) {
                adProductDataVoList.add(buildExportVo(keyVo, AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value()));
            }
            if (!adProductDataVoList.isEmpty()) {
                urlList.add(excelService.easyExcelHandlerExport(puid, adProductDataVoList, fileName + "(" + (count++) + ")", AdProductExportVo.class, build, excludeFileds));
            }
        }
        return urlList;
    }

    /**
     * 构建导出参数
     *
     * @param param    param
     * @param shopAuth shopAuth
     * @return List<AdProductDataResponse.AdProductPageVo>
     */
    private List<AdProductDataResponse.AdProductPageVo> buildExportData(AdProductPageParam param, ShopAuth shopAuth) {
        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        List<AdProductPageVo> voList;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            voList = cpcProductService.getSpProductVoList(shopAuth, param.getPuid(), param, null, true);
        } else {
            voList = cpcProductService.getSdProductVoList(shopAuth, param.getPuid(), param, null, true);
        }
        //填充标签
        this.fillAdTagData(param.getPuid(), param.getShopId(), param, voList);
        Map<String, AdProductCommonInfoDto> fbaInventory = cpcProductService.getAdProductFbaInfo(voList, shopAuth.getPuid());
        return voList.stream().filter(Objects::nonNull).map((item) -> {
            if (fbaInventory != null && fbaInventory.containsKey(item.getShopId() + item.getSku())) {
                return buildGrpcVo(item, fbaInventory.get(item.getShopId() + item.getSku()));
            }
            return buildGrpcVo(item, null);
        }).collect(Collectors.toList());
    }

    private AdProductDataResponse.AdProductPageVo buildGrpcVo(AdProductPageVo item, AdProductCommonInfoDto adProductCommonInfoDto) {
        AdProductDataResponse.AdProductPageVo.Builder vo = AdProductDataResponse.AdProductPageVo.newBuilder();
        if (StringUtils.isNotBlank(item.getAsin())) {
            vo.setAsin(item.getAsin());
        }
        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
            vo.setCampaignTargetingType(item.getCampaignTargetingType());
        }
        if (StringUtils.isNotBlank(item.getSku())) {
            vo.setSku(item.getSku());
        }
        if (StringUtils.isNotBlank(item.getState())) {
            vo.setState(item.getState());
        }
        if (StringUtils.isNotBlank(item.getCampaignName())) {
            vo.setCampaignName(item.getCampaignName());
        }
        if (StringUtils.isNotBlank(item.getAdGroupName())) {
            vo.setAdGroupName(item.getAdGroupName());
        }
        if (StringUtils.isNotBlank(item.getType())) {
            vo.setType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getPrice())) {
            vo.setPrice(item.getPrice());
        }
        if (StringUtils.isNotBlank(item.getPortfolioName())) {
            vo.setPortfolioName(item.getPortfolioName());
        }
        if (StringUtils.isNotBlank(item.getServingStatusName())) {
            vo.setServingStatusName(item.getServingStatusName());
        }
        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
        vo.setViewImpressions(Int32Value.of(Optional.ofNullable(item.getViewImpressions()).orElse(0)));
        vo.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
        if(SBCampaignCostTypeEnum.VCPM.getCode().equals(item.getCostType())){
            vo.setVcpm(StringUtils.isNotBlank(item.getVcpm()) ? item.getVcpm() : "0");
        }else{
            vo.setVcpm("-");
        }
        vo.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
        vo.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
        vo.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
        vo.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        vo.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
        vo.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
        vo.setOrdersNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getOrdersNewToBrandFTD()).orElse(0)));
        vo.setOrderRateNewToBrandFTD(StringUtils.isNotBlank(item.getOrderRateNewToBrandFTD()) ? item.getOrderRateNewToBrandFTD() : "0");
        vo.setSalesNewToBrandFTD(StringUtils.isNotBlank(item.getSalesNewToBrandFTD()) ? item.getSalesNewToBrandFTD() : "0");
        vo.setSalesRateNewToBrandFTD(StringUtils.isNotBlank(item.getSalesRateNewToBrandFTD()) ? item.getSalesRateNewToBrandFTD() : "0");
        vo.setUnitsOrderedNewToBrandFTD(Int32Value.of(Optional.ofNullable(item.getUnitsOrderedNewToBrandFTD()).orElse(0)));
        vo.setUnitsOrderedRateNewToBrandFTD(StringUtils.isNotBlank(item.getUnitsOrderedRateNewToBrandFTD()) ? item.getUnitsOrderedRateNewToBrandFTD() : "0");
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

        vo.setNewToBrandDetailPageViews(Optional.ofNullable(item.getNewToBrandDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setAddToCart(Optional.ofNullable(item.getAddToCart()).map(String::valueOf).orElse("0"));
        vo.setAddToCartRate(Optional.ofNullable(item.getAddToCartRate()).map(String::valueOf).orElse("0"));
        vo.setECPAddToCart(Optional.ofNullable(item.getECPAddToCart()).map(String::valueOf).orElse("0"));
        vo.setVideoFirstQuartileViews(Optional.ofNullable(item.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoMidpointViews(Optional.ofNullable(item.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        vo.setVideoThirdQuartileViews(Optional.ofNullable(item.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        vo.setVideoCompleteViews(Optional.ofNullable(item.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        vo.setVideoUnmutes(Optional.ofNullable(item.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        vo.setViewabilityRate(Optional.ofNullable(item.getViewabilityRate()).map(String::valueOf).orElse("0"));
        vo.setViewClickThroughRate(Optional.ofNullable(item.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
        vo.setBrandedSearches(Optional.ofNullable(item.getBrandedSearches()).map(String::valueOf).orElse("0"));
        vo.setDetailPageViews(Optional.ofNullable(item.getDetailPageViews()).map(String::valueOf).orElse("0"));
        vo.setCumulativeReach(Optional.ofNullable(item.getCumulativeReach()).map(String::valueOf).orElse("0"));
        vo.setImpressionsFrequencyAverage(Optional.ofNullable(item.getImpressionsFrequencyAverage()).map(String::valueOf).orElse("0"));
        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        if (adProductCommonInfoDto != null) {
            if (adProductCommonInfoDto.getRating() != null) {
                vo.setRating(String.valueOf(adProductCommonInfoDto.getRating()));
            }
            if (adProductCommonInfoDto.getRatingCount() != null) {
                vo.setRatingCount(adProductCommonInfoDto.getRatingCount());
            }
            if (adProductCommonInfoDto.getFbaInventoryDto() != null && adProductCommonInfoDto.getFbaInventoryDto().getAvailable() != null) {
                vo.setAvailableSell(adProductCommonInfoDto.getFbaInventoryDto().getAvailable().toString());
            }
        }
        //处理标签，取出标签名称进行导出
        String adTagName = "";
        if (CollectionUtils.isNotEmpty(item.getAdTags())) {
            List<String> adTag = item.getAdTags().stream().map(AdTag::getName).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adTag) && adTag.size() > 1) {
                adTagName = String.join(",", adTag);
                vo.setAdTag(adTagName);
            } else {
                adTagName = String.join("", adTag);
                vo.setAdTag(adTagName);
            }
        }
        vo.setAdTag(adTagName);

        return vo.build();
    }

    private AdProductExportVo buildExportVo(AdProductDataResponse.AdProductPageVo productVo, String currency) {
        return AdProductExportVo.builder()
                .servingStatusName(productVo.getServingStatusName())
                .campaignName(productVo.getCampaignName())
                .campaignTargetingType(TargetingEnum.getTargetingValue(productVo.getCampaignTargetingType())).groupName(productVo.getAdGroupName())
                .adOrderNum(productVo.getAdOrderNum().getValue())
                .adSale(currency + formatToNumber(productVo.getAdSale()))
                .asin(productVo.getAsin())
                .msku(productVo.getSku())
                .adCost(currency + formatToNumber(productVo.getAdCost()))
                .adCostPerClick(currency + formatToNumber(productVo.getAdCostPerClick()))
                .asots(modifyFormat(productVo.getAsots()))
                .acos(modifyFormat(productVo.getAcos()))
                .acots(modifyFormat(productVo.getAcots()))
                .roas(productVo.getRoas())
                .ctr(modifyFormat(productVo.getCtr()))
                .cvr(modifyFormat(productVo.getCvr()))
                .clicks(productVo.getClicks().getValue())
                .impressions(productVo.getImpressions().getValue())
                .price(currency + formatToNumber(productVo.getPrice()))
                .state(StateEnum.getStateValue(productVo.getState()))
                .portfolioName(productVo.getPortfolioName())
                .type(productVo.getType())
                .viewImpressions(productVo.getViewImpressions().getValue())
                .cpa(currency + formatToNumber(productVo.getCpa()))
                .vcpm("-".equals(productVo.getVcpm()) ? "-" : currency + formatToNumber(productVo.getVcpm()))
                .adSaleNum(productVo.getAdSaleNum().getValue())
                .adOtherOrderNum(productVo.getAdOtherOrderNum().getValue())
                .adSales(currency + formatToNumber(productVo.getAdSales()))
                .adOtherSales(currency + formatToNumber(productVo.getAdOtherSales()))
                .orderNum(productVo.getOrderNum().getValue())
                .adSelfSaleNum(productVo.getAdSelfSaleNum().getValue())
                .adOtherSaleNum(productVo.getAdOtherSaleNum().getValue())
                .ordersNewToBrandFTD(productVo.getOrdersNewToBrandFTD().getValue())
                .orderRateNewToBrandFTD(modifyFormat(productVo.getOrderRateNewToBrandFTD()))
                .salesNewToBrandFTD(currency + formatToNumber(productVo.getSalesNewToBrandFTD()))
                .salesRateNewToBrandFTD(modifyFormat(productVo.getSalesRateNewToBrandFTD()))
                .unitsOrderedNewToBrandFTD(productVo.getUnitsOrderedNewToBrandFTD().getValue())
                .unitsOrderedRateNewToBrandFTD(modifyFormat(productVo.getUnitsOrderedRateNewToBrandFTD()))
                .adCostPercentage(StringUtils.isNotBlank(productVo.getAdCostPercentage()) ? productVo.getAdCostPercentage() : "0")
                .adSalePercentage(StringUtils.isNotBlank(productVo.getAdSalePercentage()) ? productVo.getAdSalePercentage() : "0")
                .adOrderNumPercentage(StringUtils.isNotBlank(productVo.getAdOrderNumPercentage()) ? productVo.getAdOrderNumPercentage() : "0")
                .orderNumPercentage(StringUtils.isNotBlank(productVo.getOrderNumPercentage()) ? productVo.getOrderNumPercentage() : "0")
                .newToBrandDetailPageViews(productVo.getNewToBrandDetailPageViews())
                .addToCart(productVo.getAddToCart())
                .addToCartRate(modifyFormat(productVo.getAddToCartRate()))
                .ecpAddToCart(currency + formatToNumber(productVo.getECPAddToCart()))
                .videoFirstQuartileViews(productVo.getVideoFirstQuartileViews())
                .videoMidpointViews(productVo.getVideoMidpointViews())
                .videoThirdQuartileViews(productVo.getVideoThirdQuartileViews())
                .videoCompleteViews(productVo.getVideoCompleteViews())
                .videoUnmutes(productVo.getVideoUnmutes())
                .viewabilityRate(modifyFormat(productVo.getViewabilityRate()))
                .viewClickThroughRate(modifyFormat(productVo.getViewClickThroughRate()))
                .brandedSearches(productVo.getBrandedSearches())
                .detailPageViews(productVo.getDetailPageViews())
                .cumulativeReach(productVo.getCumulativeReach())
                .impressionsFrequencyAverage(productVo.getImpressionsFrequencyAverage())
                .advertisingUnitPrice(currency + formatToNumber(productVo.getAdvertisingUnitPrice()))
                .adTag(productVo.getAdTag())
                .rating(productVo.getRating())
                .ratingCount(productVo.getRatingCount())
                .availableSell(productVo.getAvailableSell())
                .build();
    }

    private void fillAdTagData(Integer puid, Integer shopId, AdProductPageParam param, List<AdProductPageVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> groups = rows.stream().map(AdProductPageVo::getAdId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.PRODUCT.getType(), param.getType(), null,null, groups);
        if(CollectionUtils.isEmpty(relationVos)){
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if(CollectionUtils.isEmpty(byLongIdList)){
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (AdProductPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getAdId());
            if(adMarkupTagVo == null){
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if(tagIds == null){
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }
}
