package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.dataWarehouse.service.DWShopSalesService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopOrderDto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: wade
 * @date: 2021/11/9 17:39
 * @describe:
 */
@Service
public class CpcShopDataService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private DWShopSalesService dwShopSalesService;


    public ShopSaleDto getShopSaleData(Integer shopId,String startDate,String endDate) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth==null) {
            return null;
        }
        // 取店铺销售额
        List<ShopSaleDto> list = dwShopSalesService.listShopSaleByDateRange(shopAuth.getPuid(),
                Lists.newArrayList(shopId), startDate, endDate);
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : null;
    }

    public List<ShopSaleDto> getListShopSale(Integer puid, List<Integer> shopIdList, String startDate,String endDate) {
        // 取店铺销售额
        return dwShopSalesService.listShopSaleByDateRange(puid, shopIdList, startDate, endDate);
    }

    public List<ShopSaleDto> listDailyShopSaleByDateRange(Integer puid, List<Integer> shopIdList, String startDate,String endDate) {
        // 取店铺销售额
        return dwShopSalesService.listDailyShopSaleByDateRange(puid, shopIdList, startDate, endDate);
    }

    public BigDecimal getShopSalesByDate(Integer shopId, String startDate, String endDate) {
        ShopSaleDto shopSaleData = getShopSaleData(shopId, startDate, endDate);
        return shopSaleData == null ? BigDecimal.ZERO : shopSaleData.getSumRange();
    }

    public BigDecimal getShopSalesByDate(Integer puid, List<Integer> shopIds, String startDate, String endDate) {
        //获取店铺销售额
        List<ShopSaleDto> shopSaleDtoList = dwShopSalesService.listShopSaleByDateRange(puid, shopIds, startDate, endDate);
        BigDecimal shopSales = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(shopSaleDtoList)) {
            shopSales = shopSaleDtoList.stream().filter(item -> item != null && item.getSumRange() != null).map(ShopSaleDto::getSumRange).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return shopSales;
    }

    public List<ShopSaleDto> shopSaleGroupByShop(Integer puid, List<Integer> shopIds, List<String> marketplaceIdList,
                                                     String startDate, String endDate, String currency) {
        return dwShopSalesService.shopSaleGroupByShop(puid, shopIds, marketplaceIdList, startDate, endDate, currency);
    }

    public List<ShopSaleDto> shopSaleGroupByMarketplace(Integer puid, List<Integer> shopIds, List<String> marketplaceIdList,
                                                 String startDate, String endDate, String currency) {
        return dwShopSalesService.shopSaleGroupByMarketplace(puid, shopIds, marketplaceIdList, startDate, endDate, currency);
    }

    public List<ShopOrderDto> shopOrderGroupByCountDay(Integer puid, List<Integer> shopIds, List<String> marketplaceIdList,
                                                       String startDate, String endDate) {
        return dwShopSalesService.shopOrderGroupByCountDay(puid, shopIds, marketplaceIdList, startDate, endDate);
    }
}


