package com.meiyunji.sponsored.service.cpc.util;

import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.service.cpc.vo.CpcCommPageVo;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * @ClassName Constants
 * @Desc amazon广告相关的常量
 * <AUTHOR>
 * @Date 2019/1/29 19:44
 */
public class Constants {

    // feed报告同步天数
    public static final int FEED_SYNC_DAY = 30;
    // 最大条数
    public static final Integer FILE_MAX_SIZE = 50000;
    //广告类型 sp sb sd
    public static final String  SP= "sp";
    public static final String  SB= "sb";
    public static final String  SD= "sd";

    //区分同步和在amzUp创建的
    public static final Integer  CREATE_IN_AMZUP= 1;  //活动在dxm创建标记
    public static final Integer  UPDATE_IN_AMZUP= 2;  //同步的在dxm编辑标记

    //活动投放类型
    public static final String  AUTO= "auto";  //自动投放
    public static final String  AUTO_UP= "AUTO";  //自动投放
    public static final String  MANUAL= "manual";  //手动投放
    public static final String  KEYWORD_UP= "KEYWORD";  //自动投放
    public static final String  CATAGORY= "category";  //自动投放

    //关键词type
    public static final String  BIDDABLE = "biddable";  //竞价关键词
    public static final String  NEGATIVE = "negative";  //否定关键词
    public static final String  CAMPAIGN_NEGATIVE = "campaignNegative";  //否定关键词


    //广告组type
    public static final String  GROUP_TYPE_KEYWORD = "keyword";  //关键词
    public static final String  GROUP_TYPE_TARGETING = "targeting";  //定位
    public static final String  GROUP_TYPE_AUTO= "auto";  //自动
    public static final String GROUP_TYPE_PRODUCT = "product";
    /**
     * 日志traceId
     */
    public static final String TRACE_ID = "traceId";
    /**
     * puid
     */
    public static final String PUID = "puid";


    //同步时默认分页处理
    public static final int  DEFLAUTPAGESIZE = 100;  //默认size

    public static final int TOTALSIZELIMIT = 100000;  //广告列表限制 10万
    public static final int PLAMENTLIMIT = 35000;  //广告位列表限制

    //每次批量创建广告产品和关键词的最大个数
    public static final int  MAX_SIZE = 100;  //最大个数

    //活动相关的状态（活动，广告组，产品，关键词共用）
    public static final String  ARCHIVED= "archived";  //归档
    public static final String  PAUSED= "paused";  //禁用
    public static final String  ENABLED= "enabled";  //启用

    //竞价关键词匹配类型
    public static final String  BROAD= "broad";
    public static final String  PHRASE= "phrase";
    public static final String  EXACT= "exact";

    //否定关键词匹配类型
    public static final String  NEGATIVEEXACT= "negativeExact";
    public static final String  NEGATIVEPHRASE= "negativePhrase";

    //广告类型
    public static final String  SPONSORED_PRODUCTS= "sponsoredProducts";

    public static final String CPC_TASK_TRIGGER_TYPE_TIME = "time"; //时间触发类型

    public static final Integer CPC_TASK_TRIGGER_TIME_TYPE_SINGLE = 0; //单次执行
    public static final Integer CPC_TASK_TRIGGER_TIME_TYPE_CUSTOMIZE = 1; //自定义,周几：1,2,3
    public static final Integer CPC_TASK_TRIGGER_TIME_TYPE_EVERY = 2; //每天

    //策略任务的队列执行结果
    public static final Integer  CPC_TASK_EXEC_RESULT_SUCCESS= 1;  //成功

    public static final Integer  CPC_TASK_EXEC_TYPE_STATE= 0;  //修改状态
    public static final Integer  CPC_TASK_EXEC_TYPE_BID= 1;  //修改竞价

    // 应用搜索词时，根据适配类型获取可支持的广告活动
    public static String CPC_CAMPAIGN_ADAPT_TYPE_KEYWORD = "keyword";       // 添加关键词
    public static String CPC_CAMPAIGN_ADAPT_TYPE_NE_KEYWORD = "neKeyword";  // 添加否定关键词
    public static String CPC_CAMPAIGN_ADAPT_TYPE_TARGET = "targeting";         // 添加产品投放
    public static String CPC_CAMPAIGN_ADAPT_TYPE_NE_TARGET = "neTarget";    // 添加否定产品投放

    //amazon活动状态
    private static final  Map<String,String> campaignStateMap = Maps.newHashMap();
    static{
        campaignStateMap.put("CAMPAIGN_INCOMPLETE","未完成");
        campaignStateMap.put("PENDING_START_DATE","预设中");
        campaignStateMap.put("CAMPAIGN_STATUS_ENABLED","进行中");
        campaignStateMap.put("CAMPAIGN_PAUSED","已暂停");
        campaignStateMap.put("ENDED","已结束");
        campaignStateMap.put("CAMPAIGN_ARCHIVED","已归档");
        campaignStateMap.put("CAMPAIGN_OUT_OF_BUDGET","超预算");
        campaignStateMap.put("EXCEPTION_STATUS","异常");
    }
    public static Map<String,String> getCampaignState(){
        return campaignStateMap;
    }
    //高级策略周几的对应
    private static final ImmutableMap<Integer,String> dateMap = new ImmutableMap.Builder<Integer,String>()
            .put(1, "周一")
            .put(2, "周二")
            .put(3, "周三")
            .put(4, "周四")
            .put(5, "周五")
            .put(6, "周六")
            .put(7, "周日").build();

    public static Map<Integer,String> getDateMap(){
        return dateMap;
    }

    public static final List<String> placementSiteAmazonBusinessMarketplaceIds = new ArrayList<>();
    static {
        placementSiteAmazonBusinessMarketplaceIds.add("ATVPDKIKX0DER");
        placementSiteAmazonBusinessMarketplaceIds.add("A2EUQ1WTGCTBG2");
        placementSiteAmazonBusinessMarketplaceIds.add("A1AM78C64UM0Y8");
        placementSiteAmazonBusinessMarketplaceIds.add("A1PA6795UKMFR9");
        placementSiteAmazonBusinessMarketplaceIds.add("A1F83G8C2ARO7P");
        placementSiteAmazonBusinessMarketplaceIds.add("A13V1IB3VIYZZH");
        placementSiteAmazonBusinessMarketplaceIds.add("APJ6JRA9NG5V4");
        placementSiteAmazonBusinessMarketplaceIds.add("A1RKKUPIHCS9HS");
        placementSiteAmazonBusinessMarketplaceIds.add("A21TJRUUN4KGV");
        placementSiteAmazonBusinessMarketplaceIds.add("A1VC38T7YXB528");
    }


    //站点信息
    public static final String  US = "ATVPDKIKX0DER";
    public static final String  CA = "A2EUQ1WTGCTBG2";
    public static final String  MX = "A1AM78C64UM0Y8";
    public static final String  UK = "A1F83G8C2ARO7P";
    public static final String  FR = "A13V1IB3VIYZZH";
    public static final String  IT = "APJ6JRA9NG5V4";
    public static final String  ES = "A1RKKUPIHCS9HS";
    public static final String  DE = "A1PA6795UKMFR9";
    public static final String  JP = "A1VC38T7YXB528";


    public static final Integer  REPORT_EXEC_STATUS_WAIT= 0;  //待执行
    public static final Integer  REPORT_EXEC_STATUS_ING= 1;  //执行中

    //活动报告区分位置
    public static final String PLACEMENT_ALL  = "all";
    public static final String PLACEMENT_TOP  = "Top of Search on-Amazon";
    public static final String PLACEMENT_DETAIL_PAGE  = "Detail Page on-Amazon";
    public static final String PLACEMENT_OTHER  = "Other on-Amazon";
    public static final String PLACEMENT_AMAZON_BUSINESS = "Amazon Business";

    //定位类型
    public static final String  TARGETING_TYPE_CATEGORY= "category";
    public static final String  TARGETING_TYPE_ASIN= "asin";
    public static final String  TARGETING_TYPE_NEGATIVEASIN= "negativeAsin";
    public static final String  CAMPAIGN_TARGETING_TYPE_NEGATIVEASIN= "campaignNegativeAsin";
    public static final String  TARGETING_TYPE_AUTO= "auto";
    public static final String  TARGETING_TYPE_BRAND = "brand";
    public static final String  TARGETING_TYPE_MANUAL = "manual";


    //活动等
    public static final String  ITEM_TYPE_CAMPAIGN = "campaign";
    public static final String  ITEM_TYPE_ADGROUP = "adGroup";
    public static final String  ITEM_TYPE_GROUP = "group";  //就是adGroup,防止带ad被拦截
    public static final String  ITEM_TYPE_KEYWORD = "keyword";
    public static final String  ITEM_TYPE_PRODUCT = "product";
    public static final String  ITEM_TYPE_TARGET = "target";

    //参数区分pageType
    public static final String  PAGE_TYPE_CAMPAIGN = "campaign";
    public static final String  PAGE_TYPE_GROUP = "group";
    public static final String  PAGE_TYPE_KEYWORD = "keyword";
    public static final String  PAGE_TYPE_PRODUCT = "product";
    public static final String  PAGE_TYPE_SHOP = "shop";
    public static final String  PAGE_TYPE_TARGETING = "targeting";

    //广告组发布状态
    public static final String  GROUP_PUBLISH_DRAFT = "draft";
    public static final String  GROUP_PUBLISH_PUBLISHING = "publishing";
    public static final String  GROUP_PUBLISH_ERROR = "error";

    //广告报告的3个tab
    public static final String  PRODUCT_TYPE_ASIN = "asin";
    public static final String  PRODUCT_TYPE_SKU = "sku";
    public static final String  PRODUCT_TYPE_PARENTASIN = "parentAsin";
    public static final String  PRODUCT_TYPE_MSKU = "msku";

    //商品投放query报告类型（针对自动）
    public static final String  TARGETING_EXPRESSION_SUBSTITUTES = "substitutes"; //同类商品
    public static final String  TARGETING_EXPRESSION_CLOSE = "close-match"; //紧密匹配
    public static final String  TARGETING_EXPRESSION_LOOSE = "loose-match"; //宽泛匹配
    public static final String  TARGETING_EXPRESSION_COMPLEMENTS = "complements"; //关联商品

    //投放匹配方式
    public static final String TARGETING_EXPRESSION = "TARGETING_EXPRESSION"; //手动
    public static final String TARGETING_EXPRESSION_PREDEFINED = "TARGETING_EXPRESSION_PREDEFINED"; //自动

    //投放
    public static final String KEYWORD = "keyword";
    public static final String TARGET = "target";

    //报告相关字段转换
    public static String getOrderField(String field) {
        if(StringUtils.isEmpty(field)){
            return null;
        }
        switch (field){
            case "cpc":
                return "cpc";
            case "cost" :
                return "cost";
            case "sales" :
                return "sales";
            case "acos" :
                return "acos";
            case "impressions" :
                return "impressions";
            case "clicks" :
                return "clicks";
            case "orderNum" :
                return "order_num";
            case "saleNum" :
                return "sale_num";
            case "clickRate" :
                return "click_rate";
            case "salesConversionRate" :
                return "sales_conversion_rate";
            default:
                return null;
        }
    }

    //amazonCpc报错信息
    public static final String  UNAUTHORIZED = "UNAUTHORIZED"; //token失效

    // 检查页面排序字段是否是广告表现指标
    public static boolean isADperformanceOrderField(String orderField) {
        if (StringUtils.isNotBlank(orderField)) {
            Field[] fields = CpcCommPageVo.class.getDeclaredFields();
            for (Field field : fields) {
                if (field.getName().equalsIgnoreCase(orderField)) {
                    return true;
                }
            }
        }

        return false;
    }

    // 检查页面排序字段排除（报告排序）
    public static boolean isADOrderField(String orderField , Class<?> clazz) {
        if (StringUtils.isNotBlank(orderField) && clazz != null) {
            List<Field> fieldList = new ArrayList<>();
            //clazz类所有字段
            Field[] fields = clazz.getDeclaredFields();
            fieldList.addAll(Arrays.asList(fields));
            //clazz父类所有字段
            Field[] supFields = clazz.getSuperclass().getDeclaredFields();
            fieldList.addAll(Arrays.asList(supFields));

            for (Field field : fieldList) {
                if (field.getName().equalsIgnoreCase(orderField)) {
                    return true;
                }
            }
        }

        return false;
    }

    //SP广告活动批量操作类型根据类型只更新部分字段和判断部分字段是否合法
    /**
     * 每日预算
     */
    public static final String CPC_CAMPAIGN_BATCH_UPDATE_BUDGET = "1";
    /**
     * 广告位调整
     */
    public static final String CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE = "2";

    /**
     * 状态调整
     */
    public static final String CPC_BATCH_UPDATE_STATUS = "3";
    /**
     * sp 广告组合调整竞价
     */
    public static final String CPC_SP_GROUP_BATCH_UPDATE_BID = "4";
    /**
     * sp 关键词投放竞价调整
     */
    public static final String CPC_SP_KEYWORD_BATCH_UPDATE_BID = "5";
    /**
     * sp 商品投放竞价调整
     */
    public static final String CPC_SP_TARGET_BATCH_UPDATE_BID = "5";

    /**
     * SB 广告活动批量修改竞价
     */
    public static final String CPC_SB_KEYWORD_BATCH_UPDATE_BID = "5";



    /**
     * SD 广告组批量默认竞价
     */
    public static final String CPC_SD_GROUP_BATCH_BID = "4";

    /**
     * sp 商品投放竞价调整
     */
    public static final String CPC_SD_TARGET_BATCH_UPDATE_BID = "5";
    /**
     * sp 商品投放竞价调整
     */
    public static final String CPC_SB_TARGET_BATCH_UPDATE_BID = "5";


    public static final String SD_REPORT_CPC = "CPC";

    public static final String SD_REPORT_VCPM = "VCPM";

    public static final String SD_BID_OPTIMIZATION_CLICKS = "clicks";

    public static final String SD_BID_OPTIMIZATION_CONVERSIONS = "conversions";

    public static final String SD_BID_OPTIMIZATION_REACH = "reach";

    /**
     * cpc 可选竞价优化
     */
    public static final List<String> CPC_OPTIMIZATION = Lists.newArrayList(SD_BID_OPTIMIZATION_CLICKS,SD_BID_OPTIMIZATION_CONVERSIONS);

    /**
     * vcpm 可选竞价优化
     */
    public static final List<String> VCPM_OPTIMIZATION = Lists.newArrayList(SD_BID_OPTIMIZATION_REACH);

    public static final String  notIncludeBudgetOut= "2";  //不包含超预算
    public static final String  exclusiveBudgetOut = "3";  //仅包含超预算

    public static final String  BrandLogo= "brandLogo";
    public static final String  RectCustomImage= "rectCustomImage";
    public static final String  SquareCustomImage= "squareCustomImage";
    public static final String  Video= "video";

    public static final String ASIN_REGEX = "^[bB]{1}0[A-Za-z0-9]{8}$";

    /**
     * 批量改名称
     */
    public static final String CPC_BATCH_NAME = "8";

    /**
     * 自动化规则模板超限提示语
     */
    public static final String AUTO_TEMPLATE_RULE_TITLE = "企业内创建的规则数量已达上限，请先删除部分规则";

    /**
     * 自动化规则受控对象超限提示语
     */
    public static final String AUTO_STATUS_RULE_TITLE = "规则名称%s内受控对象已添加%s条，上限为%s，请移除部分受控对象";

    /**
     * 分时策略分片数量
     */
    public static final int STRATEGY_PARTITION_SIZE = 20;

    /**
     * 自动化规则分片数量
     */
    public static final int AUTO_RULE_PARTITION_SIZE = 1000;

    /**
     * 分时策略任务ID处理数量
     */
    public static final int STRATEGY_PROCESS_TASK_ID_SIZE = 500;

    /**
     * 产品透视分析获取Feed数据分片数量
     */
    public static final int ANALYSIS_VIEW_PARTITION_SIZE = 1000;

    /**
     * 分时策略分片数量
     */
    public static final int EXECUTE_RECORD_MESSAGE_PARTITION_SIZE = 20;

    public static final List<String> CAMPAIGN_PRODUCT_SELECT = Collections.unmodifiableList(Arrays.asList("asin","msku","parentAsin"));


    /**
     * SD投放报告下载类型
     */
    public static final String SD_TARGET_DOWNLOAD_TYPE = "sdTargetDownloadExcel";


    public static final ImmutableMap<String, List<String>> SERVER_STATUS_SELECT = new ImmutableMap.Builder<String, List<String>>()
            //正在投放
            .put("enabled", Collections.unmodifiableList(Arrays.asList("CAMPAIGN_STATUS_ENABLED", "RUNNING", "running"))).put("CAMPAIGN_STATUS_ENABLED", Collections.unmodifiableList(Arrays.asList("CAMPAIGN_STATUS_ENABLED", "RUNNING", "running")))
            //已暂停
            .put("CAMPAIGN_PAUSED", Collections.unmodifiableList(Arrays.asList("CAMPAIGN_PAUSED", "PAUSED", "paused")))
            //已归档
            .put("CAMPAIGN_ARCHIVED", Collections.unmodifiableList(Arrays.asList("CAMPAIGN_ARCHIVED", "TERMINATED", "terminated")))
            //超预算
            .put("CAMPAIGN_OUT_OF_BUDGET", Collections.unmodifiableList(Arrays.asList("CAMPAIGN_OUT_OF_BUDGET", "OUT_OF_BUDGET", "outOfBudget")))
            //已安排
            .put("PENDING_START_DATE", Collections.unmodifiableList(Arrays.asList("PENDING_START_DATE", "READY", "ready")))
            //已预定
            .put("SCHEDULED", Collections.unmodifiableList(Arrays.asList("SCHEDULED", "scheduled")))
            //已结束
            .put("ENDED", Collections.unmodifiableList(Arrays.asList("ENDED", "ended")))
            //待审核
            .put("PENDING_REVIEW", Collections.unmodifiableList(Arrays.asList("PENDING_REVIEW", "pendingReview")))
            //不完整
            .put("CAMPAIGN_INCOMPLETE", Collections.unmodifiableList(Arrays.asList("CAMPAIGN_INCOMPLETE")))
            //付款失败
            .put("ADVERTISER_PAYMENT_FAILED", Collections.unmodifiableList(Arrays.asList("ADVERTISER_PAYMENT_FAILED", "ADVERTISER_PAYMENT_FAILURE")))
            //着陆页失效
            .put("LANDING_PAGE_NOT_AVAILABLE", Collections.unmodifiableList(Arrays.asList("LANDING_PAGE_NOT_AVAILABLE", "landingPageNotAvailable")))
            //未获得批准
            .put("REJECTED", Collections.unmodifiableList(Arrays.asList("REJECTED", "rejected")))
            //不可售
            .put("ASIN_NOT_BUYABLE", Collections.unmodifiableList(Arrays.asList("ASIN_NOT_BUYABLE")))
            //不可用
            .put("STATUS_UNAVAILABLE", Collections.unmodifiableList(Arrays.asList("STATUS_UNAVAILABLE"))).build();

    /**
     * 分时调预算调整值超范围提示
     */
    public static final String PERCENTAGE_CHECK_PROMPT = "百分比值应在1%~100%内，请修改";

    /**
     * 关键词库最大标签数量
     */
    public static int KEYWORD_LIB_TAG_MAX_SIZE = 20;

    /**
     * 关键词库最大标签数量
     */
    public static int KEYWORD_LIB_PUBLIC_TAG_MAX_SIZE = 50;

    /**
     * 关键词库最大标签数量
     */
    public static int KEYWORD_LIB_CUSTOMIZE_TAG_MAX_SIZE = 50;


    /**
     * 搜索词词频分片数量
     */
    public static final int QUERY_WORD_ROOT_SIZE = 2000;

    /**
     * 词频分析导出分片数量
     */
    public static final int WORD_ROOT_EXPORT_SIZE = 1000;

    /**
     * 词频分析api接口每次翻译数量
     */
    public static final int WORD_ROOT_TRANSLATOR_SIZE = 1000;

    /**
     * 广告自定义搜索标签限制字符
     */
    public static final int CPC_FILTER_FIELD_SIZE = 2000;

    /**
     * 广告管理竞价预算日志时间
     */
    public static final int AD_MANAGE_OPERATION_LOG_TIME = 24;

    /**
     * 投放层级汇总趋势图ids查询数量限制
     */
    public static final int TARGET_AGGREGATE_IN_LIMIT = 10000;

    public static final int CAMPAIGN_AGGREGATE_IN_LIMIT = 10000;

    /**
     * 广告组内已存在相同的否定投放
     */
    public static final String NOT_TARGET_MSG = "广告组内已存在相同的否定投放";

    /**
     * 广告组内已存在相同的投放
     */
    public static final String TARGET_MSG = "广告组内已存在相同的投放";

    /**
     * 投放列表页汇总占比数据
     */
    public static final String TARGET_PAGE_SUM_METRIC = "TARGET_PAGE_SUM_METRIC:";

    /**
     * 关键词投放列表页汇总占比数据
     */
    public static final String KEYWORD_PAGE_SUM_METRIC = "KEYWORD_PAGE_SUM_METRIC:";    /**
     * 关键词投放列表页汇总占比数据
     */
    public static final String CAMPAIGN_PAGE_SUM_METRIC = "CAMPAIGN_PAGE_SUM_METRIC:";

    /**
     * 关键词投放列表页汇总占比数据
     */
    public static final int KEYWORD_PAGE_SUM_METRIC_TIME = 300000;

    public static final int GROUP_PAGE_SUM_METRIC_TIME = 300000;

    public static final int GROUP_PAGE_QUERY_REPORT_ID_LIMIT = 200000;


    /**
     * 关键词投放列表页报告表查询限制
     */
    public static final int KEYWORD_PAGE_QUERY_REPORT_ID_LIMIT = 200000;

    public static final int TARGET_PAGE_SUM_METRIC_TIME = 300000;

    /**
     * 投放列表页报告表查询限制
     */
    public static final int TARGET_PAGE_QUERY_REPORT_ID_LIMIT = 200000;

    /**
     * 投放列表页查询id分片
     */
    public static final int TARGET_PAGE_QUERY_ID_PARTITION = 10000;

    public static final int CAMPAIGN_PAGE_SUM_METRIC_TIME = 300000;

    public static final int CAMPAIGN_PAGE_QUERY_REPORT_ID_LIMIT = 200000;

    public static final String   SB_CAMPAIGN_MANUAL = "manual";  //手动投放

    public static final String   SB_CAMPAIGN_MANUAL_NEW = "sb_manual";  //手动投放

    public static final String   AUTO_TARGETING_STATE_ENABLE = "enable";  //自动广告投放开启状态
    public static final String   AUTO_TARGETING_STATE_PAUSE = "pause";  //自动广告投放停止状态



    /**
     * 分时调价报错信息
     */
    public static final String STATUS_BUDGET_MAX_MSG = "输入的值需符合亚马逊对%s站点规定的最大值:%s";

    /**
     * 分时调价报错信息
     */
    public static final String STATUS_BUDGET_Min_MSG = "输入的值需符合亚马逊对%s站点规定的最大值:%s";



    /**
     * 分时调价报错信息
     */
    public static final String SP_SD_STATUS_BUDGET_MIN_MSG = "输入的值需符合亚马逊对%s站点规定的最大值:%s";

    /**
     * 分时调价报错信息
     */
    public static final String SB_STATUS_BUDGET_MIN_MSG = "输入的值需符合亚马逊对%s站点规定的最大值:%s";

    /**
     * 分时调价报错信息
     */
    public static final String SCHEDULE_BUDGET_MIN_MSG = "添加对象中，存在将要分配的预算值不符合亚马逊预算最小限制:%s";

    /**
     * 分时调价报错信息
     */
    public static final String SCHEDULE_BUDGET_MAX_MSG = "添加对象中，存在将要分配的预算值不符合亚马逊预算最小限制:%s";

    /**
     * 分时策略受控对象数量最大上限校验
     */
    public static final String AD_STRATEGY_MAX_COUNT_MSG = "模板名称:%s内受控对象已添加%s条，上限为%s，请重新选择";

    /**
     * 分时策略受控对象数量最大上限校验
     */
    public static final String AD_STRATEGY_MAX_COUNT_MSGS = "模板内受控对象数量已超出上限%s，请选择其他模板";

    /**
     * 分时策略广告组受控对象数量最大上限校验
     */
    public static final String AD_GROUP_STRATEGY_MAX_COUNT_MSG = "受控对象超出上限%s，请减少受控对象";

    /**
     * 分时策略广告组模板数量最大上限校验
     */
    public static final String AD_GROUP_TEMPLATE_STRATEGY_MAX_COUNT_MSG = "企业内创建的“选择投放所属广告组”模板数量已达上限，请先删除部分模板";

    public static final String GROUP_PAGE_SUM_METRIC = "GROUP_PAGE_SUM_METRIC:";

    /**
     * 同步进度标识
     */
    public static final String SYNC_SP_AD_PARENT_ASIN_INIT = "SYNC_SP_AD_PARENT_ASIN_INIT";
    /**
     * 分时策略任务ID处理数量
     */
    public static final int AUTO_RULE_PROCESS_TASK_ID_SIZE = 1000;

    public static final String KEYWORDS_RELATED_TO_YOUR_BRAND = "KEYWORDS_RELATED_TO_YOUR_BRAND";
    public static final String KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES = "KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES";
    public static final String keywords_related_to_your_brand = "keywords-related-to-your-brand";
    public static final String keywords_related_to_your_landing_pages = "keywords-related-to-your-landing-pages";
    public static final String KEYWORDS_RELATED_TO_YOUR_BRAND_CN = "与您的品牌相关的关键词";
    public static final String KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN = "与您的落地页相关的关键词";
    // 导出最大条数
    public static final Integer EXPORT_MAX_SIZE = 60000;
    public static final Integer ES_EXPORT_MAX_SIZE = 10000;


    public static final String QUERY_NOT_TARGET = "notClassified";
    public static final String QUERY_TARGET = "classified";

    public static final String SEARCH_TERM_TYPE_KEYWORD = "keyword";
    public static final String SEARCH_TERM_TYPE_ASIN = "asin";

    public static final String SP_KEYWORD_GROUP_MATCH_TYPE = "theme";

    /**
     * 关键词词根计算服务页码标识
     */
    public static final String SP_KEYWORD_WORD_FREQUENCY_PAGE = "SP_KEYWORD_WORD_FREQUENCY_PAGE:";
    public static final String SB_KEYWORD_WORD_FREQUENCY_PAGE = "SB_KEYWORD_WORD_FREQUENCY_PAGE:";
    public static final String SP_KEYWORD_QUERY_TARGET_WORD_FREQUENCY_PAGE = "SP_KEYWORD_QUERY_TARGET_FREQUENCY_PAGE:";
    public static final String SP_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE = "SP_KEYWORD_QUERY_KEYWORD_FREQUENCY_PAGE:";
    public static final String SB_KEYWORD_QUERY_KEYWORD_WORD_FREQUENCY_PAGE = "SB_KEYWORD_QUERY_KEYWORD_FREQUENCY_PAGE:";

    /**
     * 词根计算增量每批条数，每天每个店铺产生条数最多也不超过这个值
     */
    public static final Integer WORD_ROOT_KEYWORD_INCREMENT_LIMIE = 20000;
    public static final Integer WORD_ROOT_QUERY_INCREMENT_LIMIE = 5000;
    /**
     * 词根插入时分片
     */
    public static final Integer WORD_ROOT_INSERT_PARTITION = 5000;
    /**
     * 小时级
     */
    public static final String PORTFOLIO_HOUR = "PORTFOLIO_HOUR";

    public static final String NONE = "NONE";
    /**
     * 标签系统单个广告活动上的标签最大数量
     */
    public static final Integer AD_TAG_SYSTEM_CAMPAIGN_TAG_MAX_SIZE = 5;

    public static final int ASIN_LIMIT_REQ_ONE_THOUSAND = 1000;
    public static final int ASIN_ADN_KEYWORD_COUNT_LIMIT_TOTAL_TWENTY_THOUSAND = 20000;
    public static final int ASIN_ADN_KEYWORD_COUNT_LIMIT_TOTAL_THIRTY_THOUSAND = 30000;
    public static final int ASIN_LENGTH_LIMIT_TEN = 10;

    /**
     * sb广告活动批量提交修改分片数超过值
     */
    public static final Integer SB_CAMPAIGN_UPDATE_MAX_PARTITION = 50;
    public static final Integer SB_CAMPAIGN_UPDATE_PARTITION = 10;

    /**
     * doris中in分桶超过47会索引失效
     */
    public static final Integer DORIS_IN_PARTITION_MAX = 40;

    /**
     * tiktok广告店铺授权地址
     */
    public static final String TIKTOK_AUTH_URL = "https://ads.tiktok.com/marketing_api/auth?app_id=7502370490531610641&state=%s&redirect_uri=https://www.sellfox.com/amzup-web-main/tiktok/adauthcb.html";
}
