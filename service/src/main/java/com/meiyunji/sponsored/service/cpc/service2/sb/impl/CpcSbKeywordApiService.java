package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.sb.entity.keyword.*;
import com.amazon.advertising.sb.entity.theme.CreateThemesResponse;
import com.amazon.advertising.sb.entity.theme.SBThemesErrorResult;
import com.amazon.advertising.sb.entity.theme.SBThemesResult;
import com.amazon.advertising.sb.entity.theme.ThemesClient;
import com.amazon.advertising.sb.entity.themeTargeting.SbThemeResult;
import com.amazon.advertising.sb.entity.themeTargeting.ThemeTargetingClient;
import com.amazon.advertising.sb.entity.themeTargeting.ThemeUpdateResult;
import com.amazon.advertising.sb.entity.themeTargeting.UpdateThemeTargetingResponse;
import com.amazon.advertising.sb.mode.keyword.Keyword;
import com.amazon.advertising.sb.mode.theme.Theme;
import com.amazon.advertising.sb.mode.themeTargeting.SbTheme;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.SbUpdateKeywordsVo;
import com.meiyunji.sponsored.service.enums.AdSbTargetingStateEnum;
import com.meiyunji.sponsored.service.enums.SBCreateErrorEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/7/30.
 * 对接口二次封装，直接和广告接口交互
 */

@Component
@Slf4j
public class CpcSbKeywordApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Resource
    private IShopAuthService shopAuthService;

    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 同步所有的关键词
     * @param shop：
     */
    public void syncKeywords(ShopAuth shop, String campaignId) {
        syncKeywords(shop, campaignId, null, null, null, false);
    }

    public void syncKeywordsIds(ShopAuth shop, String keywordIds) {
        syncKeywords(shop, null, null, keywordIds, null, false);
    }

    public void syncKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, List<AdSbTargetingStateEnum> stateList, boolean throwException) {
        syncKeywords(shop, campaignId, groupId, keywordId, stateList, throwException, false);
    }

    /**
     * 同步所有的关键词
     * @param shop：
     */
    public void syncKeywords(ShopAuth shop, String campaignId, String groupId, String keywordId, List<AdSbTargetingStateEnum> stateList, boolean throwException, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSbKeywords--配置信息为空");
            return;
        }
        KeywordClient client = KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = KeywordClient.getInstance(true);
        }

        int startIndex = 0;
        int count = 500;
        ListKeywordsResponse response;

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(AdSbTargetingStateEnum.values()).map(AdSbTargetingStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(AdSbTargetingStateEnum::getStateType).collect(Collectors.joining(","));
        }

        while (true) {
            int finalStatIndex = startIndex;
            KeywordClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalStatIndex, count, null, null, stateFilter, campaignId, groupId, keywordId, null, null));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SB keyword rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                KeywordClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalStatIndex, count, null, null, stateFilter, campaignId, groupId, keywordId, null, null));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && throwException) {
                throw new ServiceException("sb syncKeyword error");
            }

            if (response == null || CollectionUtils.isEmpty(response.getResultList())) {
                break;
            }

            int size = response.getResultList().size();

            // 入库
            AmazonSbAdKeyword amazonSbAdKeyword;
            List<AmazonSbAdKeyword> amazonSbAdKeywords = new ArrayList<>(size);
            for (Keyword keyword : response.getResultList()) {
                amazonSbAdKeyword = turnEntityToPO(keyword);
                if (StringUtils.isNotBlank(amazonSbAdKeyword.getKeywordId())) {
                    amazonSbAdKeyword.setPuid(shop.getPuid());
                    amazonSbAdKeyword.setShopId(shop.getId());
                    amazonSbAdKeyword.setMarketplaceId(shop.getMarketplaceId());
                    amazonSbAdKeyword.setProfileId(amazonAdProfile.getProfileId());
                    amazonSbAdKeywords.add(amazonSbAdKeyword);
                }
            }

            if (amazonSbAdKeywords.size() > 0) {
                Map<String, AmazonSbAdKeyword> keywordMap = amazonSbAdKeywordDao.listByKeywordId(shop.getPuid(), shop.getId(),
                        amazonSbAdKeywords.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity()));

                List<AmazonSbAdKeyword> insertList = new ArrayList<>();
                List<AmazonSbAdKeyword> updateList = new ArrayList<>();
                AmazonSbAdKeyword old;

                for (AmazonSbAdKeyword newKeyword : amazonSbAdKeywords) {
                    if (keywordMap.containsKey(newKeyword.getKeywordId())) {
                        old = keywordMap.get(newKeyword.getKeywordId());
                        if (StringUtils.isNotBlank(newKeyword.getKeywordText())) {
                            old.setKeywordText(newKeyword.getKeywordText());
                        }
                        if (StringUtils.isNotBlank(newKeyword.getNativeLanguageKeyword())) {
                            old.setNativeLanguageKeyword(newKeyword.getNativeLanguageKeyword());
                        }
                        if (StringUtils.isNotBlank(newKeyword.getMatchType())) {
                            old.setMatchType(newKeyword.getMatchType());
                        }
                        if (StringUtils.isNotBlank(newKeyword.getState())) {
                            old.setState(newKeyword.getState());
                        }
                        if (newKeyword.getBid() != null) {
                            old.setBid(newKeyword.getBid());
                        }
                        updateList.add(old);
                    } else {
                        newKeyword.setCreateInAmzup(0);
                        insertList.add(newKeyword);
                    }
                }

                try {
                    amazonSbAdKeywordDao.batchAdd(shop.getPuid(), insertList);
                    amazonSbAdKeywordDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSdKeyword:", e);
                }

            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
    }

    public Result createKeywords(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdKeyword> amazonAdKeywords) {

        List<Keyword> keywordList = makeKeywords(amazonAdKeywords);

        CreateKeywordResponse response = cpcApiHelper.call(shop, () -> KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<SbKeywordResult> resultList = response.getResultList();
            int index = 0;
            for (SbKeywordResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    amazonAdKeywords.get(index).setKeywordId(String.valueOf(result.getKeywordId()));
                } else {
                    amazonAdKeywords.get(index).setErrMsg("第" + (index+1) +"个:" + (AmazonErrorUtils.getError(StringUtils.isNotBlank(result.getDetails())
                            ? result.getDetails() : result.getDescription())));
                }
                index++;
            }
            return ResultUtil.success();
        }else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SB广告权限，请到Amazon后台开通SB广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails();
            } else {
                errMsg = response.getError().getDescription();
            }
            errMsg = AmazonErrorUtils.getError(errMsg);

        }
        return ResultUtil.error(errMsg);
    }

    public Result createKeywordsNew(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdKeyword> amazonAdKeywords) {

        List<Keyword> keywordList = makeKeywords(amazonAdKeywords);

        CreateKeywordResponse response = cpcApiHelper.call(shop, () -> KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            return ResultUtil.error(SBCreateErrorEnum.TRY_AGAIN_LATER.getMsg());
        }

        //处理返回结果中的错误信息
        String errMsg = "";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<SbKeywordResult> resultList = response.getResultList();
            int index = 0;
            for (SbKeywordResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    amazonAdKeywords.get(index).setKeywordId(String.valueOf(result.getKeywordId()));
                } else {
                    amazonAdKeywords.get(index).setErrMsg(StringUtils.isNotBlank(result.getDetails())
                            ? result.getDetails() : result.getDescription());
                }
                index++;
            }
            return ResultUtil.success();
        }else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SB广告权限，请到Amazon后台开通SB广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails();
            } else {
                errMsg = response.getError().getDescription();
            }
        }
        return ResultUtil.error(errMsg);
    }
    
    public Result update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<Keyword> keywordList) {

        UpdateKeywordResponse response = cpcApiHelper.call(shop, () -> KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        List<SbKeywordResult> resultList = response.getResultList();
        if (CollectionUtils.isNotEmpty(resultList)) {
            SbKeywordResult sbKeywordResult = resultList.get(0);
            if ("SUCCESS".equals(sbKeywordResult.getCode())) {
                return ResultUtil.success();
            } else {
                return ResultUtil.error(AmazonErrorUtils.getError(StringUtils.isNotBlank(sbKeywordResult.getDetails()) ?
                 sbKeywordResult.getDetails() : sbKeywordResult.getDescription()));
            }
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getDetails())) {
                msg = response.getError().getDetails();
            } else {
                msg = response.getError().getDescription();
            }
            msg = AmazonErrorUtils.getError(msg);
        }
        return ResultUtil.returnErr(msg);
    }
    public UpdateThemeTargetingResponse modifyTheme(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<SbTheme> sbThemes) {

        UpdateThemeTargetingResponse response = cpcApiHelper.call(shop, () -> ThemeTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
            shop.getMarketplaceId(), sbThemes));

        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            shopAuthService.refreshCpcAuth(shop);
            response = cpcApiHelper.call(shop, () -> ThemeTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), sbThemes));
        }
        if (response == null) {
            throw new SponsoredBizException("网络异常，请稍后重试");
        }
        return response;
    }
    
    public Result archive(ShopAuth shop, AmazonAdProfile amazonAdProfile, String keywordId) {

        ArchiveKeywordResponse response = cpcApiHelper.call(shop, () -> KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Long.valueOf(keywordId)));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getKeywordId() != null) {
            if ("SUCCESS".equalsIgnoreCase(response.getResult().getCode())) {
                return ResultUtil.success();
            }
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                msg = response.getResult().getDetails();
            } else {
                msg = response.getResult().getDescription();
            }
            msg = AmazonErrorUtils.getError(msg);
        }
        return ResultUtil.error(msg);
    }
    

    public List<Keyword> makeKeywords(List<AmazonSbAdKeyword> amazonAdKeywords) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonAdKeywords.size());
        Keyword keyword;
        for (AmazonSbAdKeyword sbAdKeyword : amazonAdKeywords) {
            keyword = new Keyword();
            if (StringUtils.isNotBlank(sbAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(sbAdKeyword.getAdGroupId()));
            }
            if (StringUtils.isNotBlank(sbAdKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(sbAdKeyword.getCampaignId()));
            }
            keyword.setKeywordText(sbAdKeyword.getKeywordText());
            keyword.setMatchType(sbAdKeyword.getMatchType());
            if (sbAdKeyword.getBid() != null) {
                keyword.setBid(sbAdKeyword.getBid().doubleValue());
            }
            list.add(keyword);
        }
        return list;
    }

    public Result createThemes(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdKeyword> amazonAdKeywords) {

        List<Theme> keywordList = makeThemes(amazonAdKeywords);

        CreateThemesResponse response = cpcApiHelper.call(shop, () -> ThemesClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), keywordList));

        if (response == null) {
            return ResultUtil.error(SBCreateErrorEnum.TRY_AGAIN_LATER.getMsg());
        }

        //处理返回结果中的错误信息
        String errMsg = "";
        if (CollectionUtils.isNotEmpty(response.getSuccess())) {
            List<SBThemesResult> resultList = response.getSuccess();
            for (SBThemesResult result : resultList) {
                amazonAdKeywords.get(result.getIndex()).setKeywordId(String.valueOf(result.getThemeId()));
            }
        }
        if (CollectionUtils.isNotEmpty(response.getError())) {
            List<SBThemesErrorResult> errorList = response.getError();
            for (SBThemesErrorResult error : errorList) {
                amazonAdKeywords.get(error.getIndex()).setErrMsg(error.getDetails());
            }
        }
        return ResultUtil.success();
    }

    private List<Theme> makeThemes(List<AmazonSbAdKeyword> amazonAdKeywords) {
        List<Theme> list = Lists.newArrayListWithCapacity(amazonAdKeywords.size());
        Theme theme;
        for (AmazonSbAdKeyword sbAdKeyword : amazonAdKeywords) {
            theme = new Theme();
            if (StringUtils.isNotBlank(sbAdKeyword.getAdGroupId())) {
                theme.setAdGroupId(sbAdKeyword.getAdGroupId());
            }
            if (StringUtils.isNotBlank(sbAdKeyword.getCampaignId())) {
                theme.setCampaignId(sbAdKeyword.getCampaignId());
            }
            theme.setThemeType(sbAdKeyword.getKeywordText());
            if (sbAdKeyword.getBid() != null) {
                theme.setBid(sbAdKeyword.getBid().doubleValue());
            }
            list.add(theme);
        }
        return list;
    }

    // 把接口返回的dto转换成po
    private AmazonSbAdKeyword turnEntityToPO(Keyword keyword) {
        AmazonSbAdKeyword amazonSbAdKeyword = new AmazonSbAdKeyword();
        if (keyword.getKeywordId() != null) {
            amazonSbAdKeyword.setKeywordId(keyword.getKeywordId().toString());
        }
        if (keyword.getCampaignId() != null) {
            amazonSbAdKeyword.setCampaignId(keyword.getCampaignId().toString());
        }
        if (keyword.getAdGroupId() != null) {
            amazonSbAdKeyword.setAdGroupId(keyword.getAdGroupId().toString());
        }
        if (StringUtils.isNotBlank(keyword.getKeywordText())) {
            amazonSbAdKeyword.setKeywordText(keyword.getKeywordText());
            amazonSbAdKeyword.setKeywordSize(Math.toIntExact(Arrays.stream(keyword.getKeywordText().trim().split(" ")).filter(StringUtils::isNotBlank).count()));
        }
        if (StringUtils.isNotBlank(keyword.getNativeLanguageKeyword())) {
            amazonSbAdKeyword.setNativeLanguageKeyword(keyword.getNativeLanguageKeyword());
        }
        if (StringUtils.isNotBlank(keyword.getMatchType())) {
            amazonSbAdKeyword.setMatchType(keyword.getMatchType());
        }
        if (StringUtils.isNotBlank(keyword.getState())) {
            amazonSbAdKeyword.setState(keyword.getState());
        }
        if (keyword.getBid() != null) {
            amazonSbAdKeyword.setBid(BigDecimal.valueOf(keyword.getBid()));
        }


        return amazonSbAdKeyword;
    }


    public Result<BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword>> update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdKeyword> amazonSbAdKeywords, String type) {
        if (CollectionUtils.isEmpty(amazonSbAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }
        List<Keyword> keywords = makeKeywords(amazonSbAdKeywords, type);
        UpdateKeywordResponse response = cpcApiHelper.call(shop, () -> KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(), keywords));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        Map<String, AmazonSbAdKeyword> amazonSbAdKeywordMap = amazonSbAdKeywords.stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, e -> e));
        BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> batchResponseVo = new BatchResponseVo<>();
        List<SbUpdateKeywordsVo> errorList = Lists.newArrayList();
        List<AmazonSbAdKeyword> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getResultList() != null && response.getResultList().size() > 0) {

            List<SbKeywordResult> resultList = response.getResultList();
            List<Long> successId = Lists.newArrayList();
            for (SbKeywordResult keywordResult : resultList) {

                if ("SUCCESS".equals(keywordResult.getCode())) {
                    AmazonSbAdKeyword amazonAdKeywordSuccess = amazonSbAdKeywordMap.remove(String.valueOf(keywordResult.getKeywordId()));
                    if (amazonAdKeywordSuccess != null) {
                        successList.add(amazonAdKeywordSuccess);
                    }
                    successId.add(amazonAdKeywordSuccess.getId());

                } else {
                    AmazonSbAdKeyword amazonAdKeywordFail = amazonSbAdKeywordMap.remove(String.valueOf(keywordResult.getKeywordId()));
                    if (amazonAdKeywordFail != null) {
                        SbUpdateKeywordsVo spKeywordsVoError = new SbUpdateKeywordsVo();
                        spKeywordsVoError.setId(amazonAdKeywordFail.getId());
                        spKeywordsVoError.setKeywordText(amazonAdKeywordFail.getKeywordText());
                        //更新失败数据处理
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(keywordResult.getDescription())) {
                            spKeywordsVoError.setFailReason(AmazonErrorUtils.getError(keywordResult.getDescription()));
                        } else {
                            spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                        }
                        spKeywordsVoError.setKeywordId(amazonAdKeywordFail.getKeywordId());
                        errorList.add(spKeywordsVoError);
                    }

                }
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSbAdKeywordMap)) {
                amazonSbAdKeywordMap.forEach((k, v) -> {
                    SbUpdateKeywordsVo spKeywordsVoError = new SbUpdateKeywordsVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setKeywordText(v.getKeywordText());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spKeywordsVoError);
                });
            }

        } else if (response.getError() != null && org.apache.commons.lang3.StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        }
        batchResponseVo.setCountNum(amazonSbAdKeywords.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    /**
     * 更新主题投放
     * @param shop shop
     * @param amazonAdProfile profile
     * @param keywords
     * @param type
     * @return
     */
    public Result<BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword>> updateTheme(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSbAdKeyword> keywords, String type) {
        if (CollectionUtils.isEmpty(keywords)) {
            return ResultUtil.returnErr("请求参数错误");
        }
        List<SbTheme> sbThemes = makeTheme(keywords, type);
        UpdateThemeTargetingResponse response = modifyTheme(shop, amazonAdProfile, sbThemes);
        if (response == null || response.getResult() == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        }

        //处理返回结果中的错误信息
        List<SbUpdateKeywordsVo> errorList = Lists.newArrayList();
        List<AmazonSbAdKeyword> successList = Lists.newArrayList();
        Map<String, AmazonSbAdKeyword> amazonSbAdKeywordMap = keywords.stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, e -> e));
        List<SbThemeResult> successResultList = response.getResult().getSuccess();
        List<SbThemeResult> errorResultList = response.getResult().getError();
        if (CollectionUtils.isNotEmpty(successResultList)) {
            for (SbThemeResult result : successResultList) {
                AmazonSbAdKeyword amazonAdKeywordSuccess = amazonSbAdKeywordMap.remove(String.valueOf(result.getThemeId()));
                if (amazonAdKeywordSuccess != null) {
                    successList.add(amazonAdKeywordSuccess);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(errorResultList)) {
            for (SbThemeResult result : errorResultList) {
                AmazonSbAdKeyword amazonAdKeywordFail = amazonSbAdKeywordMap.remove(String.valueOf(result.getThemeId()));
                if (amazonAdKeywordFail != null) {
                    SbUpdateKeywordsVo spKeywordsVoError = new SbUpdateKeywordsVo();
                    spKeywordsVoError.setId(amazonAdKeywordFail.getId());
                    spKeywordsVoError.setKeywordText(amazonAdKeywordFail.getKeywordText());
                    if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(amazonAdKeywordFail.getKeywordText())) {
                        spKeywordsVoError.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                    } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(amazonAdKeywordFail.getKeywordText())) {
                        spKeywordsVoError.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                    }
                    //更新失败数据处理
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(result.getDetails())) {
                        spKeywordsVoError.setFailReason(AmazonErrorUtils.getError(result.getDetails()));
                    } else {
                        spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    }
                    spKeywordsVoError.setKeywordId(amazonAdKeywordFail.getKeywordId());
                    errorList.add(spKeywordsVoError);
                }
            }
        }
        //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
        if (MapUtils.isNotEmpty(amazonSbAdKeywordMap)) {
            amazonSbAdKeywordMap.forEach((k, v) -> {
                SbUpdateKeywordsVo spKeywordsVoError = new SbUpdateKeywordsVo();
                spKeywordsVoError.setId(v.getId());
                spKeywordsVoError.setKeywordText(v.getKeywordText());
                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(v.getKeywordText())) {
                    spKeywordsVoError.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(v.getKeywordText())) {
                    spKeywordsVoError.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                }
                spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                errorList.add(spKeywordsVoError);
            });
        }

        BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> batchResponseVo = new BatchResponseVo<>();
        batchResponseVo.setCountNum(keywords.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);

    }

    /**
     * 同步所有的关键词
     * @param shop：
     */
    public List<AmazonSbAdKeyword> syncKeyword(ShopAuth shop, String keywordId) {
        List<AmazonSbAdKeyword> amazonSbAdKeywords = new ArrayList<>();
        if (shop == null) {
            return amazonSbAdKeywords;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSbKeywords--配置信息为空");
            return amazonSbAdKeywords;
        }

        KeywordClient client = KeywordClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 500;
        ListKeywordsResponse response;
        String stateFilter = "enabled,paused,archived,pending";

        while (true) {
            int finalStatIndex = startIndex;
            response = cpcApiHelper.call(shop, () -> client.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalStatIndex, count, null, null, stateFilter, null, null, keywordId, null, null));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SB keyword rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = cpcApiHelper.call(shop, () -> client.getList(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalStatIndex, count, null, null, stateFilter, null, null, keywordId, null, null));
                retry++;
            }
            if (response == null || CollectionUtils.isEmpty(response.getResultList())) {
                break;
            }

            int size = response.getResultList().size();

            // 入库
            AmazonSbAdKeyword amazonSbAdKeyword;
            for (Keyword keyword : response.getResultList()) {
                amazonSbAdKeyword = turnEntityToPO(keyword);
                if (StringUtils.isNotBlank(amazonSbAdKeyword.getKeywordId())) {
                    amazonSbAdKeyword.setPuid(shop.getPuid());
                    amazonSbAdKeyword.setShopId(shop.getId());
                    amazonSbAdKeyword.setMarketplaceId(shop.getMarketplaceId());
                    amazonSbAdKeyword.setProfileId(amazonAdProfile.getProfileId());
                    amazonSbAdKeywords.add(amazonSbAdKeyword);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
        return amazonSbAdKeywords;
    }

    private List<Keyword> makeKeywords(List<AmazonSbAdKeyword> amazonSbAdKeywordList, String type) {
        List<Keyword> list = Lists.newArrayListWithCapacity(amazonSbAdKeywordList.size());

        for (AmazonSbAdKeyword amazonSbAdKeyword : amazonSbAdKeywordList) {
            Keyword keyword = new Keyword();

            if (org.apache.commons.lang3.StringUtils.isNotBlank(amazonSbAdKeyword.getKeywordId())) {
                keyword.setKeywordId(Long.valueOf(amazonSbAdKeyword.getKeywordId()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(amazonSbAdKeyword.getCampaignId())) {
                keyword.setCampaignId(Long.valueOf(amazonSbAdKeyword.getCampaignId()));
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(amazonSbAdKeyword.getAdGroupId())) {
                keyword.setAdGroupId(Long.valueOf(amazonSbAdKeyword.getAdGroupId()));
            }

            if(Constants.CPC_SB_KEYWORD_BATCH_UPDATE_BID.equals(type)){
                keyword.setBid(amazonSbAdKeyword.getBid().doubleValue());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                keyword.setState(amazonSbAdKeyword.getState());
            }
            list.add(keyword);
        }
        return list;
    }
    private List<SbTheme> makeTheme(List<AmazonSbAdKeyword> amazonSbAdKeywordList, String type) {
        List<SbTheme> list = Lists.newArrayListWithCapacity(amazonSbAdKeywordList.size());

        for (AmazonSbAdKeyword amazonSbAdKeyword : amazonSbAdKeywordList) {
            SbTheme sbTheme = new SbTheme();
            sbTheme.setThemeId(amazonSbAdKeyword.getKeywordId());
            sbTheme.setAdGroupId(amazonSbAdKeyword.getAdGroupId());
            sbTheme.setCampaignId(amazonSbAdKeyword.getCampaignId());
            if(Constants.CPC_SB_KEYWORD_BATCH_UPDATE_BID.equals(type)){
                sbTheme.setBid(amazonSbAdKeyword.getBid().doubleValue());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                sbTheme.setState(amazonSbAdKeyword.getState());
            }
            list.add(sbTheme);
        }
        return list;
    }
}
