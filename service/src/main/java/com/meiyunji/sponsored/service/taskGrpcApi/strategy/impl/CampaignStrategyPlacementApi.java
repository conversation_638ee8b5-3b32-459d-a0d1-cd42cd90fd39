package com.meiyunji.sponsored.service.taskGrpcApi.strategy.impl;

import com.meiyunji.sellfox.aadas.api.entry.TargetStatusPb;
import com.meiyunji.sellfox.aadas.api.enumeration.AmazonAdvertiseTypePb;
import com.meiyunji.sellfox.aadas.api.enumeration.ScheduleModePb;
import com.meiyunji.sellfox.aadas.api.service.AadasApiGrpc;
import com.meiyunji.sellfox.aadas.api.service.RemoveCampaignPlacementTaskTimeScheduleRequestPb;
import com.meiyunji.sellfox.aadas.api.service.SetCampaignPlacementStrategyScheduleRequestPb;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.impl.ShopAuthServiceImpl;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.taskGrpcApi.AbstractAdvertiseStrategyApi;
import com.meiyunji.sponsored.service.util.GrpcExceptionUtil;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.util.ProtoBufUtil;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class CampaignStrategyPlacementApi extends AbstractAdvertiseStrategyApi {

    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;

    protected CampaignStrategyPlacementApi(ShopAuthServiceImpl shopAuthService, ManagedChannel taskManagedChannel, Producer<byte[]> compensationTaskProducer, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(shopAuthService, taskManagedChannel, compensationTaskProducer, dynamicRefreshConfiguration);
    }

    @Override
    public boolean checkValid(TaskTimeType taskType) {
        return taskType == TaskTimeType.campaignPlacement;
    }

    @Override
    public void setSchedule(Long taskId, Long templateId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        SetCampaignPlacementStrategyScheduleRequestPb.SetCampaignPlacementStrategyScheduleRequest request = builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId);
        try {
            log.info("新版广告策略-广告位调整请求参数: {}", request);
            AadasApiGrpc.AadasApiBlockingStub aadasApiBlockingStub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
            aadasApiBlockingStub.setCampaignPlacementStrategySchedule(request);
        } catch (StatusRuntimeException e) {
            throw GrpcExceptionUtil.unWrapException(e);
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }

    @Override
    public void removeSchedule(Integer puid, Integer shopId, Long taskId, boolean triggerNow) throws Exception {
        if (triggerNow) {
            AdvertiseStrategyTemplate template = advertiseStrategyTemplateDao.getTemplateByTaskId(puid, shopId, taskId);
            if (Objects.isNull(template) || AdStrategyEnableStatusEnum.DISABLED.getCode().equals(template.getStatus())) {
                triggerNow = false;
                log.info("分时策略拦截回调, puid: {}, shopId: {}, taskId: {}", puid, shopId, taskId);
            }
        }
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(shopId, puid);
        try {
            RemoveCampaignPlacementTaskTimeScheduleRequestPb.RemoveCampaignPlacementTaskTimeScheduleRequest.Builder builder =
                    RemoveCampaignPlacementTaskTimeScheduleRequestPb.RemoveCampaignPlacementTaskTimeScheduleRequest.newBuilder();
            builder.setSellerId(shopAuth.getSellingPartnerId());
            builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
            builder.setTaskId(taskId);
            builder.setTriggerNow(triggerNow);
            AadasApiGrpc.AadasApiBlockingStub aadasApiBlockingStub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
            aadasApiBlockingStub.removeCampaignPlacementTaskTimeSchedule(builder.build());
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }


    @Override
    public String builderScheduleRequestJson(Long taskId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        return ProtoBufUtil.toJsonStr(builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId));
    }

    @Override
    public TaskTimeType getTaskTimeType() {
        return TaskTimeType.campaignPlacement;
    }


    public SetCampaignPlacementStrategyScheduleRequestPb.SetCampaignPlacementStrategyScheduleRequest builderRequest(Long taskId, ShopAuth shopAuth, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId){
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        String campaignId = schedule.getCampaignId();
        String adType = schedule.getAdType();
        ScheduleModePb.ScheduleMode scheduleMode = schedule.getDay() == 0 ? ScheduleModePb.ScheduleMode.Daily :
                ScheduleModePb.ScheduleMode.Weekly;
        SetCampaignPlacementStrategyScheduleRequestPb.SetCampaignPlacementStrategyScheduleRequest.Builder builder =
                SetCampaignPlacementStrategyScheduleRequestPb.SetCampaignPlacementStrategyScheduleRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setTriggerNow(triggerNow);
        builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
        builder.setAdType(AmazonAdvertiseTypePb.AmazonAdvertiseType.valueOf(adType));
        builder.setTaskId(taskId);
        builder.setSchedulerMode(scheduleMode);
        builder.setCampaignId(campaignId);
        //拼装广告位参数
        ArrayList<TargetStatusPb.CampaignPlacementStatus.Adjustment> adjustments = new ArrayList<>(2);
        if (schedule.getOriginAdPlaceTopValue() != null) {
            adjustments.add(TargetStatusPb.CampaignPlacementStatus.Adjustment.newBuilder()
                    .setPredicate(TargetStatusPb.CampaignPlacementStatus.PredicateType.placementTop)
                    .setPercentage(schedule.getOriginAdPlaceTopValue().doubleValue()).build());
        }
        if (schedule.getOriginAdPlaceProductValue() != null) {
            adjustments.add(TargetStatusPb.CampaignPlacementStatus.Adjustment.newBuilder()
                    .setPredicate(TargetStatusPb.CampaignPlacementStatus.PredicateType.placementProductPage)
                    .setPercentage(schedule.getOriginAdPlaceProductValue().doubleValue()).build());
        }
        if (schedule.getOriginAdOtherValue() != null) {
            adjustments.add(TargetStatusPb.CampaignPlacementStatus.Adjustment.newBuilder()
                    .setPredicate(TargetStatusPb.CampaignPlacementStatus.PredicateType.placementRestOfSearch)
                    .setPercentage(schedule.getOriginAdOtherValue().doubleValue()).build());
        }
        TargetStatusPb.CampaignPlacementStatus.Builder addAllAdjustmentBuilder =
                TargetStatusPb.CampaignPlacementStatus.newBuilder()
                        .addAllAdjustments(adjustments);
        if (StringUtils.isNotBlank(schedule.getOriginStrategy())) {
            addAllAdjustmentBuilder.setStrategy(TargetStatusPb.CampaignPlacementStatus
                    .Strategy.valueOf(schedule.getOriginStrategy()));
        }
        builder.setTemplateId(templateId);
        builder.setOriginValue(addAllAdjustmentBuilder);
        builder.addAllItem(PbUtil.toCampaignPlacementItem(strategySchedule));
        SetCampaignPlacementStrategyScheduleRequestPb.SetCampaignPlacementStrategyScheduleRequest request
                = builder.build();
        return request;
    }
}
