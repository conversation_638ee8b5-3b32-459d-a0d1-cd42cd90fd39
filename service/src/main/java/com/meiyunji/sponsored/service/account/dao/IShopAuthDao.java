package com.meiyunji.sponsored.service.account.dao;

import com.meiyunji.amazon.sellerpartner.base.RegionEnum;
import com.meiyunji.sponsored.common.springjdbc.IBaseDao;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.post.response.ShopSitesResponse;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import com.meiyunji.sponsored.service.vo.ShopAuthSellerInfoVo;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * ShopAuth
 *
 * <AUTHOR>
 */
public interface IShopAuthDao extends IBaseDao<ShopAuth> {
    /**
     * 更新CPC的token
     *
     * @param puid
     * @param shopId
     * @param refreshToken
     * @param accessToken
     * @param adStatus
     * @return
     */
    int updateAdToken(int puid, Integer shopId, String refreshToken, String accessToken, String adStatus);

    /**
     * 更新
     *
     * @param puid
     * @param sellingPartnerId
     * @param region
     * @param accessToken
     * @return
     */
    int updateAdAccessToken(Integer puid, String sellingPartnerId, String region, String accessToken);

    int updateSingleField(int id, String fieldName, Object fieldValue, boolean updateTime);

    List<String> getAllMarketplaceIdByPuid(int puid);

    List<Integer> getShopByMid(int puid, String marketplaceId);

    List<ShopAuth> getListByPuid(int puid);

    List<ShopAuth> getAllId();

    List<Integer> getAllShopId();

    List<Integer> getAllShopId(int puid);

    List<Integer> getAllAdShopId(int puid);

    List<String> getAllSellerId(Integer puid);

    List<ShopAuth> listBySellerId(String sellerId);

    List<Integer> getIdByPuidAndShop(Integer puid, Integer shopId);

    Integer getCountByShopIds(Integer puid, List<Integer> shopIds);

    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    ShopAuth getBySellerIdAndMarketplaceId(Integer puid, String sellerId, String marketplaceId);

    /**
     * 批量获取店铺信息（有效的店铺）
     *
     * @param puid
     * @param shopIds
     * @return
     */
    List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds);

    /**
     * 批量获取店铺信息
     *
     * @param puid
     * @param shopIds
     * @return
     */
    List<ShopAuth> listAllByIds(Integer puid, List<Integer> shopIds);

    /**
     * 获取所有有效的店铺
     *
     * @return
     */
    List<ShopAuth> listAllValidShop();

    /**
     * 获取所有有效的广告店铺
     */
    List<ShopAuth> listAllValidAdShop(Integer puid);

    /**
     * 更新spToken
     *
     * @param puid
     * @param sellingPartnerId
     * @param region
     * @param accessToken
     * @return
     */
    int updateSpAccessToken(Integer puid, String sellingPartnerId, RegionEnum region, String accessToken);

    /**
     * 根据sellerid, region取店铺信息
     *
     * @param puid             puid
     * @param sellingPartnerId amazon seller id
     * @param region           区域
     * @return List
     */
    List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region);


    List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region, List<Integer> shopIds);

    /**
     * 更新授权
     *
     * @param puid             puid
     * @param sellingPartnerId sellingPartnerId
     * @param region           region
     * @param mwsAuthToken     mwsAuthToken
     * @param refreshToken     refreshToken
     * @param accessToken      accessToken
     * @return int
     */
    int updateAuth(int puid, String sellingPartnerId, String region, String mwsAuthToken, String refreshToken, String accessToken);

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    ShopAuth getByMarketplaceId(int puid, String sellerId, String marketplaceId);

    /**
     * 获取店铺信息
     */
    ShopAuth getBySellerIdAndMarketplaceId(String sellerId, String marketplaceId);

    /**
     * 清空授权信息
     *
     * @param puid
     * @param shopId
     * @return
     */
    int cleanAdToken(int puid, Integer shopId);

    /**
     * 分页取授权了广告的店铺ID
     *
     * @param puid   puid
     * @param shopId shopId
     * @param start  start
     * @param limit  limit
     * @return List
     */
    List<Integer> getAllValidAdShopIdByLimit(Integer puid, Integer shopId, int start, int limit);

    /**
     * 分页取有效的店铺
     *
     * @param puid   puid
     * @param shopId shopId
     * @param start  start
     * @param limit  limit
     * @return List
     */
    List<ShopAuth> getAllValidShopByLimit(Integer puid, Integer shopId, int start, int limit);


    /**
     * 根据时间分页取授权了广告的店铺(间隔3个小时)
     *
     * @param puid   puid
     * @param shopId shopId
     * @param localDateTime  localDateTime
     * @return List
     */
    int updateAdAuthDate(Integer puid, Integer shopId, LocalDateTime localDateTime);

    /**
     * 根据时间分页取授权了广告的店铺(间隔3个小时)
     *
     * @param puid   puid
     * @param shopId shopId
     * @return List
     */
    List<ShopAuth> getAllValidAdShopByLimitDate(Integer puid, Integer shopId);

    /**
     * 分页取授权了广告的店铺
     *
     * @param puid   puid
     * @param shopId shopId
     * @param start  start
     * @param limit  limit
     * @return List
     */
    List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit);

    /**
     * 按照puid获取有广告授权或者过期的店铺数据
     * @param puid
     * @return
     */
    List<ShopByPuidDto> getAllValidAdShopByPuid(Integer puid);

    /**
     * 分页获取有广告授权或者过期的puid
     * @param start
     * @param limit
     * @return
     */
    List<Integer> getAllValidAdShopPuidByLimit(int start, int limit);

    List<ShopAuth> getAllValidAdShopByLimitOrderByAdAuthTime(Integer puid, Integer shopId, int start, int limit);

    /**
     * 分页取授权店铺用户PUID
     *
     * @param start start
     * @param limit limit
     * @return List
     */
    List<Integer> getAllPuidByLimit(int start, int limit);

    /**
     * 取店铺partner
     *
     * @param puid puid
     * @return List
     */
    List<ShopAuth> getAllPartner(Integer puid);

    /**
     * 更新店铺状态
     *
     * @param puid             puid
     * @param sellingPartnerId seller id
     * @param region           大区
     * @return int
     */
    int updateShopStatusBySellerAndRegion(Integer puid, String sellingPartnerId, String region, Integer status);

    /**
     * 更新店铺状态
     *
     * @param puid   puid
     * @param shopId shop id
     * @param status status
     * @return int
     */
    int updateStatus(Integer puid, Integer shopId, Integer status);

    /**
     * 更新广告授权状态
     *
     * @param puid   puid
     * @param shopId shop id
     * @param adStatus status
     * @return int
     */
    int updateAdStatus(Integer puid, Integer shopId, String adStatus);

    /**
     * 根据用户id跟sellerid marketplace获取
     *
     * @param puid
     * @param sellId
     * @return
     */
    ShopAuth getByPuidAndSellerId(Integer puid, String sellId, String marketplaceId);

    List<ShopAuth> getByPuidAndSellerId(Integer puid, String sellerId);

    List<ShopAuth> getByPuidAndSellerIdAvailable(Integer puid, String sellerId);


    List<ShopAuth> listLimit(String marketplace, int maximumPoolSize);

    /**
     * 获得该区域下的所有的店铺的id
     *
     * @param puid
     * @param sellingPartnerId
     * @param region
     */
    List<ShopAuth> getBySellerIdAndRegion(Integer puid, String sellingPartnerId, String region);

    /**
     * 获取店铺的sellerId
     *
     * @param shopIdList
     * @return
     */
    List<String> getSellerIdByShopIds(List<Integer> shopIdList);

    /**
     * 复制店铺数据到备份表中
     *
     * @param shopId 店铺ID
     * @return int
     */
    int copyToDeleteShop(int shopId);

    /**
     * 根据大区取店铺数
     *
     * @param puid             puid
     * @param sellingPartnerId seller id
     * @param region           region
     * @return int
     */
    int countByPuidSellerIdRegion(int puid, String sellingPartnerId, String region);

    /**
     * 清空店铺广告授权
     *
     * @param puid puid
     * @param id   店铺ID
     * @return int
     */
    int clearAdAuth(Integer puid, Integer id);

    /**
     * 获取sellerApi正常的seller
     *
     * @return
     */
    List<String> getAuthNormalSellers();

    /**
     * 获取MWS正常,sellerApi不正常
     *
     * @return
     */
    List<String> getMWSSellers();

    /**
     * 通过店铺名称获取店铺信息
     *
     * @param puid
     * @param shopName
     * @return
     */
    ShopAuth getByName(Integer puid, String shopName);

    /**
     * 更新沙特站店铺状态为2，通过老接口同步
     *
     * @return int
     */
    int updateSaState();


    /**
     * @param puid puid
     * @return List
     */
    List<String> getSellerIdByPuid(Integer puid);

    /**
     * 获取所有店铺信息
     *
     * @param puid
     * @param sellerId
     * @return
     */
    List<ShopAuth> listByPuidAndSellerId(Integer puid, String sellerId);

    /**
     * 获取指定站点的店铺
     *
     * @param puid
     * @param mkList
     * @param shopList
     * @return
     */
    List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList);

    /**
     * 获取token
     * <p>
     * 根据大区分组
     * <p>
     * 获取大于timeSecond时长/秒 的数据
     *
     * @return
     */
    List<ShopAuth> getSellingToken(Integer puid, Integer shopId, long timeSecond);

    List<ShopAuth> getShopAuthGroupBySellerId(Integer puid, int syncType, int status);

    /**
     * 更新用户的扣费时间
     *
     * @param puid puid
     */
    int updateDeductTime(Integer puid);

    /**
     * 取用户下的所有店铺
     *
     * @param puids
     * @return
     */
    List<ShopAuth> listByPuids(List<Integer> puids);


    /**
     * 获取店铺信息
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    List<ShopAuth> getByMarketplaceIds(int puid, String sellerId, String marketplaceId);

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    List<ShopAuth> getByMarketplaceIds(int puid, String[] marketplaceIds);
    List<ShopAuth> getByPuidAndMarketplaceIds(int puid, String[] marketplaceIds);


    /**
     * 随机获取已授权对应marketplaceId的店铺
     * @param marketplaceId
     */
    List<ShopAuth> getShopAuthByMarketplaceId(String marketplaceId);

    List<ShopAuth> getShopAuthByAuthAndTokenIsNull();

    int updateAdAccessTokenAndRefreshToken(Integer id, String accessToken, String refreshToken, Date authTime);

    /**
     * 根据店铺获取所有站点
     * @param shopIdList
     * @return
     */
    List<String> marketplaceIdListByShopIds(List<Integer> shopIdList);

    List<Integer> IdListByShopIds(List<Integer> shopIdList);

    /**
     * 根据店铺id获取出已授权的店铺
     * @param puid
     * @param shopIdList
     * @return
     */
    List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList);

    List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);

    /**
     * 根据店铺id和puid获取出已授权的店铺
     * @param puid
     * @param shopIdList
     * @return
     */
    List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puid);

    /**
     * 根据店铺id和puid获取出已授权的店铺
     * @param puid
     * @param shopIdList
     * @return
     */
    List<ShopAuth> getAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId);

    /**
     * 获取店铺id和名称等基础信息
     *
     * @param puid
     * @param idList
     * @return
     */
    List<ShopAuthBo> getShopAuthBoByIds(int puid, List<Integer> idList);

    List<ShopAuthBo> getShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);

    /**
     * 获取授权或过期的店铺
     */
    List<ShopAuthBo> getAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);

    /**
     * 查询最大id和最小id
     *
     * @return
     */
    List<Integer> queryRandomSequentList();

    /**
     * 通过主键id查
     *
     * @param ids
     * @return
     */
    List<ShopAuth> unionQueryShopsByIdsWithConditions(List<Integer> ids);

    /**
     * 查询seller相关信息
     * @param integers
     * @return
     */
    List<ShopAuthSellerInfoVo> getSellerInfoByIdList(ArrayList<Integer> integers);

    /**
     * 获取一个有广告授权的店铺
     */
    ShopAuth getOneByAdStatus(Integer puid);

    List<Integer> listAllValidOrExpireAdShopByIds(Integer puid, List<Integer> shopIds);

    /**
     * 根据puid和站点获取店铺信息
     * @param puid
     * @return
     */
    List<ShopSitesResponse.ShopSite> getAuthShopListByPuid(int puid, String marketplaceId, List<Integer> shopIdList);

    List<ShopAuth> getScAndVcAdAuthShopByShopIdList(List<Integer> puids);

    List<ShopAuth> getScAndVcAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId);

    List<ShopAuthBo> getScAndVcShopAuthBoByIds(int puid, List<Integer> idList);

    List<ShopAuthBo> getScAndVcAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);

    List<Integer> queryScAndVcRandomSequentList();

    List<ShopAuthSellerInfoVo> getScAndVcSellerInfoByIdList(ArrayList<Integer> idList);

    List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus);

    List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);

    ShopAuth getScAndVcById(int id);

    List<ShopAuth> getScAndVcShopListByShopIdsAndAdStatus(List<Integer> shopIds, String adStatus);

    List<ShopAuth> getScAndVcByIds(List<Integer> ids);

    ShopAuth getScAndVcByIdAndPuid(int id, int puid);

    List<ShopAuthBo> getScAndVcShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);

    List<ShopAuth> listScAndVcAllByIds(int puid, List<Integer> shopIds);

    ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid);

    List<Integer> getScAndVcShopByMid(int puid, String marketplaceId);

    List<ShopAuth> getScAndVcListByPuid(int puid);

    List<ShopAuth> getScAndVcAllId();

    List<Integer> getScAndVcAllShopId();

    List<Integer> getScAndVcAllShopId(int puid);

    List<Integer> getScAndVcAllAdShopId(int puid);

    List<String> getScAndVcAllSellerId(Integer puid);

    List<String> getScAndVcSellerIdByPuid(Integer puid);

    List<ShopAuth> listScAndVcByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList);

    List<ShopAuth> listScAndVcValidByIds(Integer puid, List<Integer> shopIds);

    List<ShopAuth> listScAndVcAllValidAdShop(Integer puid);

    List<ShopAuth> getScAndVcAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit);

    List<ShopAuth> getScAndVcByPuidAndMarketplaceIds(int puid, String[] marketplaceIds);

    List<String> marketplaceIdScAndVcListByShopIds(List<Integer> shopIdList);

    List<Integer> IdListScAndVcByShopIds(List<Integer> shopIdList);

    List<ShopAuth> getScAndVcAuthShopByShopIdList(int puid, List<Integer> shopIdList);

    ShopAuth getScAndVcBySellerIdAndMarketplaceId(String sellerId, String marketplaceId);

    List<ShopAuth> listScAndVcAllByPuid(int puid);

    /**
     * 获取授权和过期的店铺
     * @param puid 用户id
     * @param shopIds 店铺id集合
     * @return 响应
     */
    List<ShopAuth> listValidShopByIds(Integer puid, List<Integer> shopIds);

}