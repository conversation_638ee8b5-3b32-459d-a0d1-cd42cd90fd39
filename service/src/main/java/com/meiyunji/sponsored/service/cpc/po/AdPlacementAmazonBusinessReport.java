package com.meiyunji.sponsored.service.cpc.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * Created by lm on 2021/5/13.
 *
 */
@DbTable(value = "t_amazon_ad_placement_amazon_business_report")
@Data
public class AdPlacementAmazonBusinessReport extends ReportBase {

    /**
     * id
     */
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    /**
     * 商户uid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺ID
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 站点
     */
    @DbColumn(value = "marketplace_id")
    private String marketplaceId;

    /**
     * 统计日期
     */
    @DbColumn(value = "count_date")
    private String countDate;

    /**
     * sp,sb,sd
     */
    @DbColumn(value = "type")
    private String type;

    /**
     * 冗余字段，sp,sb,sd,广告报告类型标识
     * sp 广告位置(all表示汇总), sb productCollection, video  广告类型,sd 活动类型   T00020、T00030  站内、站外
     */
    @DbColumn(value = "campaign_type")
    private String campaignType;

    /**
     *
     * 冗余字段
     * sp 广告位置(all表示汇总), sb productCollection
     */
    @DbColumn(value = "placement")
    private String placement;

    /**
     * 活动id
     */
    @DbColumn(value = "campaign_id")
    private String campaignId;

    /**
     * 活动名称
     */
    @DbColumn(value = "campaign_name")
    private String campaignName;

    /**
     * 曝光量
     */
    @DbColumn(value = "impressions")
    private Integer impressions;
    /**
     * 点击量
     */
    @DbColumn(value = "clicks")
    private Integer clicks;

    /**
     * 站点币种
     */
    @DbColumn(value = "currency")
    private String currency;

    /**
     * 花费
     */
    @DbColumn(value = "cost")
    private BigDecimal cost;

    /**
     * Total number of attributed conversion events occurring within 24 hours of ad click
     */
    @DbColumn(value = "conversions1d")
    private Integer conversions1d;

    /**
     * Total number of attributed conversion events occurring within 7 days of ad click
     */
    @DbColumn(value = "conversions7d")
    private Integer conversions7d;

    /**
     * Number of attributed conversion events occurring within 14 days of click on ad
     */
    @DbColumn(value = "conversions14d")
    private Integer conversions14d;

    /**
     * Total number of attributed conversion events occurring within 30 days of ad click.
     */
    @DbColumn(value = "conversions30d")
    private Integer conversions30d;

    /**
     * Total number of attributed conversion events occurring within 24 hours of ad click, where the SKU of the product advertised and the SKU of the conversion event are equivalent.
     */
    @DbColumn(value = "conversions1d_same_sku")
    private Integer conversions1dSameSKU;

    /**
     * Total number of attributed conversion events occurring within 7 days of ad click, where the SKU of the product advertised and the SKU of the conversion event are equivalent.
     */
    @DbColumn(value = "conversions7d_same_sku")
    private Integer conversions7dSameSKU;

    /**
     * Number of attributed conversion events occurring within 14 days of click on ad where the purchased SKU was the same as the one advertised. Not available for search term report.
     */
    @DbColumn(value = "conversions14d_same_sku")
    private Integer conversions14dSameSKU;

    /**
     * Total number of attributed conversion events occurring within 30 days of ad click, where the SKU of the product advertised and the SKU of the conversion event are equivalent.
     */
    @DbColumn(value = "conversions30d_same_sku")
    private Integer conversions30dSameSKU;

    /**
     * Total number of attributed units ordered within 24 hours of ad click
     */
    @DbColumn(value = "units_ordered1d")
    private Integer unitsOrdered1d;

    /**
     * Total number of attributed units ordered within 7 days of ad click
     */
    @DbColumn(value = "units_ordered7d")
    private Integer unitsOrdered7d;

    /**
     * Total number of attributed units ordered within 14 days of ad click
     */
    @DbColumn(value = "units_ordered14d")
    private Integer unitsOrdered14d;

    /**
     * Total number of attributed units ordered within 30 days of ad click
     */
    @DbColumn(value = "units_ordered30d")
    private Integer unitsOrdered30d;

    /**
     * Total number of attributed units ordered within 24 hours of ad click
     */
    @DbColumn(value = "units_ordered1d_same_sku")
    private Integer unitsOrdered1dSameSKU;

    /**
     * Total number of attributed units ordered within 7 days of ad click
     */
    @DbColumn(value = "units_ordered7d_same_sku")
    private Integer unitsOrdered7dSameSKU;

    /**
     * Total number of attributed units ordered within 14 days of ad click
     */
    @DbColumn(value = "units_ordered14d_same_sku")
    private Integer unitsOrdered14dSameSKU;

    /**
     * Total number of attributed units ordered within 30 days of ad click
     */
    @DbColumn(value = "units_ordered30d_same_sku")
    private Integer unitsOrdered30dSameSKU;

    /**
     * Total number of attributed sales occurring within 24 hours of ad click
     */
    @DbColumn(value = "sales1d")
    private BigDecimal sales1d;

    /**
     * Total number of attributed sales occurring within 7 days of ad click.
     */
    @DbColumn(value = "sales7d")
    private BigDecimal sales7d;

    /**
     * Number of attributed sales occurring within 14 days of click on an ad
     */
    @DbColumn(value = "sales14d")
    private BigDecimal sales14d;

    /**
     * Total number of attributed sales occurring within 30 days of ad click
     */
    @DbColumn(value = "sales30d")
    private BigDecimal sales30d;

    /**
     * Aggregate value of all attributed sales occurring within 24 hours of ad click, where the SKU of the product advertised and the SKU of the purchased item are equivalent.
     */
    @DbColumn(value = "sales1d_same_sku")
    private BigDecimal sales1dSameSKU;

    /**
     * Aggregate value of all attributed sales occurring within 7 days of ad click, where the SKU of the product advertised and the SKU of the purchased item are equivalent.
     */
    @DbColumn(value = "sales7d_same_sku")
    private BigDecimal sales7dSameSKU;

    /**
     * Aggregate value of attributed sales occurring within 14 days of click on ad where the purchased SKU was the same as the one advertised. Not available for search term report.
     */
    @DbColumn(value = "sales14d_same_sku")
    private BigDecimal sales14dSameSKU;

    /**
     * Aggregate value of all attributed sales occurring within 30 days of ad click, where the SKU of the product advertised and the SKU of the purchased item are equivalent.
     */
    @DbColumn(value = "sales30d_same_sku")
    private BigDecimal sales30dSameSKU;
}
