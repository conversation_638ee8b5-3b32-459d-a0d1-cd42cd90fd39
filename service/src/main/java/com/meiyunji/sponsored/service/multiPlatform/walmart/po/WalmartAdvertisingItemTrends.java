package com.meiyunji.sponsored.service.multiPlatform.walmart.po;

import java.io.Serializable;
import java.util.Date;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description: walmart广告活动产品热门
 */
public class WalmartAdvertisingItemTrends implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 报告日期
     */
    private Date reportDate;

    /**
     * 搜索关键字
     */
    private String searchKeyword;

    /**
     * 搜索关键字的数字排名
     */
    private Integer searchKeywordFrequencyRank;

    /**
     * 产品1
     */
    private String item1Id;

    /**
     * 产品1名称
     */
    private String item1Name;

    /**
     * 产品1品牌名称
     */
    private String item1BrandName;

    /**
     * 产品1点击占比
     */
    private Double item1ClickShare;

    /**
     * 产品1订单份额占比
     */
    private Double item1ConversionShare;

    /**
     * 产品21
     */
    private String item2Id;

    /**
     * 产品2名称
     */
    private String item2Name;

    /**
     * 产品2品牌名称
     */
    private String item2BrandName;

    /**
     * 产品2点击占比
     */
    private Double item2ClickShare;

    /**
     * 产品2订单份额占比
     */
    private Double item2ConversionShare;

    /**
     * 产品3
     */
    private String item3Id;

    /**
     * 产品3名称
     */
    private String item3Name;

    /**
     * 产品3品牌名称
     */
    private String item3BrandName;

    /**
     * 产品3点击占比
     */
    private Double item3ClickShare;

    /**
     * 产品3订单份额占比
     */
    private Double item3ConversionShare;

    /**
     * 产品id索引列
     */
    private String itemIdKey;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    public String getIdStr() {
        return id == null ? null : id.toString();
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getReportDate() {
        return reportDate;
    }

    public void setReportDate(Date reportDate) {
        this.reportDate = reportDate;
    }

    public String getSearchKeyword() {
        return searchKeyword;
    }

    public void setSearchKeyword(String searchKeyword) {
        this.searchKeyword = searchKeyword;
    }

    public Integer getSearchKeywordFrequencyRank() {
        return searchKeywordFrequencyRank;
    }

    public void setSearchKeywordFrequencyRank(Integer searchKeywordFrequencyRank) {
        this.searchKeywordFrequencyRank = searchKeywordFrequencyRank;
    }

    public String getItem1Id() {
        return item1Id;
    }

    public void setItem1Id(String item1Id) {
        this.item1Id = item1Id;
    }

    public String getItem1Name() {
        return item1Name;
    }

    public void setItem1Name(String item1Name) {
        this.item1Name = item1Name;
    }

    public String getItem1BrandName() {
        return item1BrandName;
    }

    public void setItem1BrandName(String item1BrandName) {
        this.item1BrandName = item1BrandName;
    }

    public Double getItem1ClickShare() {
        return item1ClickShare;
    }

    public void setItem1ClickShare(Double item1ClickShare) {
        this.item1ClickShare = item1ClickShare;
    }

    public Double getItem1ConversionShare() {
        return item1ConversionShare;
    }

    public void setItem1ConversionShare(Double item1ConversionShare) {
        this.item1ConversionShare = item1ConversionShare;
    }

    public String getItem2Id() {
        return item2Id;
    }

    public void setItem2Id(String item2Id) {
        this.item2Id = item2Id;
    }

    public String getItem2Name() {
        return item2Name;
    }

    public void setItem2Name(String item2Name) {
        this.item2Name = item2Name;
    }

    public String getItem2BrandName() {
        return item2BrandName;
    }

    public void setItem2BrandName(String item2BrandName) {
        this.item2BrandName = item2BrandName;
    }

    public Double getItem2ClickShare() {
        return item2ClickShare;
    }

    public void setItem2ClickShare(Double item2ClickShare) {
        this.item2ClickShare = item2ClickShare;
    }

    public Double getItem2ConversionShare() {
        return item2ConversionShare;
    }

    public void setItem2ConversionShare(Double item2ConversionShare) {
        this.item2ConversionShare = item2ConversionShare;
    }

    public String getItem3Id() {
        return item3Id;
    }

    public void setItem3Id(String item3Id) {
        this.item3Id = item3Id;
    }

    public String getItem3Name() {
        return item3Name;
    }

    public void setItem3Name(String item3Name) {
        this.item3Name = item3Name;
    }

    public String getItem3BrandName() {
        return item3BrandName;
    }

    public void setItem3BrandName(String item3BrandName) {
        this.item3BrandName = item3BrandName;
    }

    public Double getItem3ClickShare() {
        return item3ClickShare;
    }

    public void setItem3ClickShare(Double item3ClickShare) {
        this.item3ClickShare = item3ClickShare;
    }

    public Double getItem3ConversionShare() {
        return item3ConversionShare;
    }

    public void setItem3ConversionShare(Double item3ConversionShare) {
        this.item3ConversionShare = item3ConversionShare;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getItemIdKey() {
        return item1Id + "_" + item2Id + "_" + item3Id;
    }

    public void setItemIdKey(String itemIdKey) {
        this.itemIdKey = itemIdKey;
    }

}
