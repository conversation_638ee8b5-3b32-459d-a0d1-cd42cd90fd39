package com.meiyunji.sponsored.service.cpc.service.impl;

import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.service.cpc.constants.TargetingTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CommonAmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.ICommonAmazonAdTargetingService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.cpc.util.Constants.ASIN_REGEX;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-15 18:33
 */
@Service
public class CommonAmazonAdTargetingServiceImpl implements ICommonAmazonAdTargetingService {

    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;

    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Autowired
    private IAmazonSbAdNeTargetingDao amazonSbAdNeTargetingDao;
    @Autowired
    private IAmazonSdAdNeTargetingDao amazonSdAdNeTargetingDao;
    @Autowired
    private IAmazonAdCampaignNetargetingSpDao amazonAdCampaignNetargetingSpDao;
    @Override
    public List<CommonAmazonAdTargeting> listByTargetIds(String targetingType, int puid, List<Integer> shopIds, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return Collections.emptyList();
        }
        if (TargetingTypeEnum.SP_TARGETING.getCode().equals(targetingType)) {
            List<AmazonAdTargeting> adTargetings = amazonAdTargetDaoRoutingService.getListTargetByTargetIds(puid, shopIds, targetIds);
            return adTargetings.stream().map(this::buildBySpTargeting).collect(Collectors.toList());
        } else if (TargetingTypeEnum.SB_TARGETING.getCode().equals(targetingType)) {
            List<AmazonSbAdTargeting> adTargetings = amazonSbAdTargetingDao.listByShopIdsAndTargetIds(puid, shopIds, targetIds);
            return adTargetings.stream().map(this::buildBySbTargeting).collect(Collectors.toList());
        } else if (TargetingTypeEnum.SD_TARGETING.getCode().equals(targetingType)) {
            List<AmazonSdAdTargeting> adTargetings = amazonSdAdTargetingDao.listByShopIdsAndTargetIds(puid, shopIds, targetIds);
            return adTargetings.stream().map(this::buildBySdTargeting).collect(Collectors.toList());
        } else if (TargetingTypeEnum.SP_NE_TARGETING.getCode().equals(targetingType)) {
            List<AmazonAdNeTargeting> adTargetings = amazonAdNeTargetingDao.getListTargetByTargetIds(puid, shopIds, targetIds);
            return adTargetings.stream().map(this::buildBySpNeTargeting).collect(Collectors.toList());
        } else if (TargetingTypeEnum.SB_NE_TARGETING.getCode().equals(targetingType)) {
            List<AmazonSbAdNeTargeting> adTargetings = amazonSbAdNeTargetingDao.listByTargetId(puid, shopIds, targetIds);
            return adTargetings.stream().map(this::buildBySbNeTargeting).collect(Collectors.toList());
        } else if (TargetingTypeEnum.SD_NE_TARGETING.getCode().equals(targetingType)) {
            List<AmazonSdAdNeTargeting> adTargetings = amazonSdAdNeTargetingDao.listByTargetId(puid, shopIds, targetIds);
            return adTargetings.stream().map(this::buildBySdNeTargeting).collect(Collectors.toList());
        } else if (TargetingTypeEnum.SP_CAMPAIGN_NE_TARGETING.getCode().equals(targetingType)) {
            List<AmazonAdCampaignNetargetingSp> campaignNeTargetList = amazonAdCampaignNetargetingSpDao.listByTargetId(puid, shopIds, targetIds);
            return campaignNeTargetList.stream().map(this::buildBySpCampaignNeTargeting).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    private CommonAmazonAdTargeting buildBySpTargeting(AmazonAdTargeting adTargeting) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(adTargeting.getTargetId());
        commonAmazonAdTargeting.setExpression(adTargeting.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(adTargeting.getResolvedExpression());
        commonAmazonAdTargeting.setType(adTargeting.getType());
        commonAmazonAdTargeting.setTargetingValue(adTargeting.getTargetingValue());
        commonAmazonAdTargeting.setTitle(adTargeting.getTitle());
        commonAmazonAdTargeting.setImgUrl(adTargeting.getImgUrl());
        return commonAmazonAdTargeting;
    }

    private CommonAmazonAdTargeting buildBySbTargeting(AmazonSbAdTargeting adTargeting) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(adTargeting.getTargetId());
        commonAmazonAdTargeting.setExpression(adTargeting.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(adTargeting.getResolvedExpression());
        commonAmazonAdTargeting.setType(adTargeting.getType());
        commonAmazonAdTargeting.setTargetingValue(adTargeting.getTargetText());
        commonAmazonAdTargeting.setTitle(adTargeting.getTitle());
        commonAmazonAdTargeting.setImgUrl(adTargeting.getImgUrl());
        return commonAmazonAdTargeting;
    }

    private CommonAmazonAdTargeting buildBySdTargeting(AmazonSdAdTargeting adTargeting) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(adTargeting.getTargetId());
        commonAmazonAdTargeting.setExpression(adTargeting.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(adTargeting.getResolvedExpression());
        commonAmazonAdTargeting.setType(adTargeting.getType());
        commonAmazonAdTargeting.setTargetingValue(adTargeting.getTargetText());
        commonAmazonAdTargeting.setTitle(adTargeting.getTitle());
        commonAmazonAdTargeting.setImgUrl(adTargeting.getImgUrl());
        return commonAmazonAdTargeting;
    }

    private CommonAmazonAdTargeting buildBySpNeTargeting(AmazonAdNeTargeting adTargeting) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(adTargeting.getTargetId());
        commonAmazonAdTargeting.setExpression(adTargeting.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(adTargeting.getResolvedExpression());
        if (adTargeting.getExpression().contains(ExpressionEnum.asinSameAs.name()) || adTargeting.getExpression().contains(SpV3ExpressionEnum.asinSameAs.getValueV3())) {
            commonAmazonAdTargeting.setType(TargetTypeEnum.asin.name());
        } else {
            commonAmazonAdTargeting.setType(TargetTypeEnum.brand.name());
        }
        commonAmazonAdTargeting.setTargetingValue(adTargeting.getTargetingValue());
        commonAmazonAdTargeting.setTitle(adTargeting.getTitle());
        commonAmazonAdTargeting.setImgUrl(adTargeting.getImgUrl());
        return commonAmazonAdTargeting;
    }

    private CommonAmazonAdTargeting buildBySpCampaignNeTargeting(AmazonAdCampaignNetargetingSp campaignNeTarget) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(campaignNeTarget.getTargetId());
        commonAmazonAdTargeting.setExpression(campaignNeTarget.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(campaignNeTarget.getResolvedExpression());
        if (StringUtils.isNotEmpty(campaignNeTarget.getExpression())) {
            if ( campaignNeTarget.getExpression().contains(ExpressionEnum.asinSameAs.name()) || campaignNeTarget.getExpression().contains(SpV3ExpressionEnum.asinSameAs.getValueV3())) {
                commonAmazonAdTargeting.setType(TargetTypeEnum.asin.name());
            } else {
                commonAmazonAdTargeting.setType(TargetTypeEnum.brand.name());
            }
        } else if (StringUtils.isNotEmpty(campaignNeTarget.getType())) {
            commonAmazonAdTargeting.setType(campaignNeTarget.getType());
        }
        commonAmazonAdTargeting.setTargetingValue(campaignNeTarget.getTargetText());
        commonAmazonAdTargeting.setTitle(campaignNeTarget.getTitle());
        commonAmazonAdTargeting.setImgUrl(campaignNeTarget.getImgUrl());
        return commonAmazonAdTargeting;
    }

    private CommonAmazonAdTargeting buildBySbNeTargeting(AmazonSbAdNeTargeting adTargeting) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(adTargeting.getTargetId());
        commonAmazonAdTargeting.setExpression(adTargeting.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(adTargeting.getResolvedExpression());
        commonAmazonAdTargeting.setType(adTargeting.getType());
        commonAmazonAdTargeting.setTargetingValue(adTargeting.getTargetText());
        commonAmazonAdTargeting.setTitle(adTargeting.getTitle());
        commonAmazonAdTargeting.setImgUrl(adTargeting.getImgUrl());
        return commonAmazonAdTargeting;
    }

    private CommonAmazonAdTargeting buildBySdNeTargeting(AmazonSdAdNeTargeting adTargeting) {
        CommonAmazonAdTargeting commonAmazonAdTargeting = new CommonAmazonAdTargeting();
        commonAmazonAdTargeting.setTargetId(adTargeting.getTargetId());
        commonAmazonAdTargeting.setExpression(adTargeting.getExpression());
        commonAmazonAdTargeting.setResolvedExpression(adTargeting.getResolvedExpression());
        commonAmazonAdTargeting.setType(adTargeting.getType());
        commonAmazonAdTargeting.setTargetingValue(adTargeting.getTargetText());
        commonAmazonAdTargeting.setTitle(adTargeting.getTitle());
        commonAmazonAdTargeting.setImgUrl(adTargeting.getImgUrl());
        return commonAmazonAdTargeting;
    }

}
