package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertiserAttributes;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;


import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertiserAttributesService {

    /**
     * 新增
     */
    Long add(WalmartAdvertiserAttributes advertiserAttributes);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * 更新
     */
    int update(WalmartAdvertiserAttributes advertiserAttributes);

    /**
     * 根据主键 id 查询
     */
    WalmartAdvertiserAttributes getById(Integer puid, Long id);

    WalmartAdvertiserAttributes getById(Long id);

    /**
     * 根据主键 id 查询
     */
    WalmartAdvertiserAttributes getByShopId(Integer puid, Integer shopId);

    List<WalmartAdvertiserAttributes> getByPuid(Integer puid);

    /**
     * @author: pxq
     * @date: 2025/02/24
     * @description:  定时获取客户属性快照
     */
    void syncAdvertiserAttributes();

    Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    //执行快照
    void snapshotExecute(WalmartAdvertisingSnapshot snapshot, String detailsStr, String jobStatus);

    WalmartAdvertiserAttributes checkAttributesAndWrite(Integer puid, Integer shopId) throws ServiceException;

    List<Long> getAllId();
}
