package com.meiyunji.sponsored.service.multiPlatform.tiktok.service;

import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.TikTokAdAuthEnum;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokAdvertiserAccountDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokStoreInfoDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.dao.TikTokStoreTokenDao;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.enums.TikTokAdvertiserAccountAuthStatusEnum;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokAdvertiserAccount;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreInfo;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreToken;
import com.tiktok.advertising.model.auth.response.AdvertiserInfoResponse;
import com.tiktok.advertising.model.auth.response.AllStoreListResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.shardingsphere.transaction.annotation.ShardingSphereTransactionType;
import org.apache.shardingsphere.transaction.core.TransactionType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  20:36
 */
@Slf4j
@Service
public class TikTokAuthSaveService {
    @Autowired
    private IMultiPlatformShopAuthDao multiPlatformShopAuthDao;
    @Autowired
    private TikTokStoreTokenDao tikTokStoreTokenDao;

    @Autowired
    private TikTokStoreInfoDao tikTokStoreInfoDao;

    @Autowired
    private TikTokAdvertiserAccountDao tikTokAdvertiserAccountDao;

    @Autowired
    private TikTokSyncService tikTokSyncService;

    @Transactional(value = "adTransactionManager", rollbackFor = Exception.class)
    public void saveTikTokAuthData(Integer puid, Integer shopId, String token, List<AdvertiserInfoResponse.Advertiser> list, Map<String, List<AllStoreListResponse.Store>> storesMap) {
        insertOrUpdateStoreToken(puid, shopId, token);
        insertOrUpdateAdvertiserAccount(puid, shopId, list);
        insertOrUpdateStoreInfo(puid, shopId, storesMap);
    }

    @Transactional(value = "adTransactionManager", rollbackFor = Exception.class)
    public void revokeAdTikTokAuth(Integer puid, Integer shopId, String advertiserId) {
        tikTokStoreInfoDao.deleteByShopIdAndAdvertiserId(puid, shopId, advertiserId);
        tikTokAdvertiserAccountDao.updateAuthStatus(puid, shopId, advertiserId, TikTokAdvertiserAccountAuthStatusEnum.NO_AD_AUTH.getStatus());
    }

    public void updateMultiPlatformShopAdAuthStatus(Integer puid, Integer shopId) {
        //获取店铺下所有广告账号列表
        List<TikTokAdvertiserAccount> tikTokAdvertiserAccounts = tikTokAdvertiserAccountDao.listAllAccountByPuidAndShopId(puid, shopId);
        Integer adStatus = TikTokAdAuthEnum.AD_AUTH.getStatus();
        if (tikTokAdvertiserAccounts.stream().anyMatch(e -> e.getAuthStatus().equals(TikTokAdvertiserAccountAuthStatusEnum.NO_AD_AUTH.getStatus()))) {
            adStatus = TikTokAdAuthEnum.AD_EXIT_FAILURE.getStatus();
        }
        //修改多平台店铺授权状态
        multiPlatformShopAuthDao.updateShopAdAuth(puid, shopId, adStatus, LocalDateTime.now(), tikTokAdvertiserAccounts.stream().map(TikTokAdvertiserAccount::getName).collect(Collectors.joining("、")));
    }

    public void initGmvMaxCampaignByShop(Integer puid, Integer shopId) {
        //获取店铺下所有商店
        List<TikTokStoreInfo> tikTokStoreInfos = tikTokStoreInfoDao.listByPuidAndShopId(puid, shopId);
        for (TikTokStoreInfo tikTokStoreInfo : tikTokStoreInfos) {
            tikTokSyncService.initGmvMaxCampaignSync(puid, shopId, tikTokStoreInfo.getAdvertiserId(), tikTokStoreInfo.getStoreId());
            tikTokSyncService.initGmvMaxCampaignReport(puid, shopId, tikTokStoreInfo.getAdvertiserId(), tikTokStoreInfo.getStoreId());
        }
    }


    /**
     * 插入tiktok广告店铺访问令牌
     */
    private void insertOrUpdateStoreToken(Integer puid, Integer shopId, String token) {
        TikTokStoreToken tikTokStoreToken = new TikTokStoreToken();
        tikTokStoreToken.setPuid(puid);
        tikTokStoreToken.setShopId(shopId);
        tikTokStoreToken.setAccessToken(token);
        tikTokStoreTokenDao.insertOrUpdate(puid, tikTokStoreToken);
    }

    /**
     * 插入tiktok广告账号数据
     */
    private void insertOrUpdateAdvertiserAccount(Integer puid, Integer shopId, List<AdvertiserInfoResponse.Advertiser> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, Long> oldAdvertiserIdMap = tikTokAdvertiserAccountDao.listAllAccountByPuidAndShopId(puid, shopId)
                .stream().collect(Collectors.toMap(TikTokAdvertiserAccount::getAdvertiserId, TikTokAdvertiserAccount::getId));
        Set<String> advertiserIdSet = list.stream().map(AdvertiserInfoResponse.Advertiser::getAdvertiserId).collect(Collectors.toSet());
        List<Long> updateIdList = new ArrayList<>();
        oldAdvertiserIdMap.forEach((k, v) -> {
            if (!advertiserIdSet.contains(k)) {
                updateIdList.add(v);
            }
        });
        //插入
        List<TikTokAdvertiserAccount> batchInsertList = new ArrayList<>();
        for (AdvertiserInfoResponse.Advertiser advertiser : list) {
            TikTokAdvertiserAccount tikTokAdvertiserAccount = new TikTokAdvertiserAccount();
            tikTokAdvertiserAccount.setPuid(puid);
            tikTokAdvertiserAccount.setShopId(shopId);
            tikTokAdvertiserAccount.setOwnerBcId(advertiser.getOwnerBcId());
            tikTokAdvertiserAccount.setAdvertiserId(advertiser.getAdvertiserId());
            tikTokAdvertiserAccount.setName(advertiser.getName());
            tikTokAdvertiserAccount.setAuthStatus(TikTokAdvertiserAccountAuthStatusEnum.AD_AUTH.getStatus());
            tikTokAdvertiserAccount.setCurrency(advertiser.getCurrency());
            tikTokAdvertiserAccount.setTimezone(advertiser.getTimezone());
            tikTokAdvertiserAccount.setDisplayTimezone(advertiser.getDisplayTimezone());
            batchInsertList.add(tikTokAdvertiserAccount);
        }
        if (CollectionUtils.isNotEmpty(batchInsertList)) {
            tikTokAdvertiserAccountDao.batchInsertOrUpdate(puid, batchInsertList);
        }
        //更新
        if (CollectionUtils.isNotEmpty(updateIdList)) {
            tikTokAdvertiserAccountDao.batchUpdateAuthStatus(puid, updateIdList, TikTokAdvertiserAccountAuthStatusEnum.NO_AD_AUTH.getStatus());
        }
    }

    /**
     * 插入tiktok广告账号店铺数据
     */
    private void insertOrUpdateStoreInfo(Integer puid, Integer shopId, Map<String, List<AllStoreListResponse.Store>> storesMap) {
        if (MapUtils.isEmpty(storesMap)) {
            return;
        }
        Map<String, Long> storeInfoIdMap = tikTokStoreInfoDao.listByPuidAndShopId(puid, shopId)
                .stream().collect(Collectors.toMap(e -> e.getAdvertiserId() + "#" + e.getStoreId(), TikTokStoreInfo::getId));
        Set<String> storeIdSet = new HashSet<>();
        storesMap.forEach((k, v) -> {
            v.forEach(e -> storeIdSet.add(k + "#" + e.getStoreId()));
        });
        List<Long> deleteIdList = new ArrayList<>();
        storeInfoIdMap.forEach((k, v) -> {
            if (!storeIdSet.contains(k)) {
                deleteIdList.add(v);
            }
        });
        //新增
        List<TikTokStoreInfo> insertList = new ArrayList<>();
        for (Map.Entry<String, List<AllStoreListResponse.Store>> entry : storesMap.entrySet()) {
            List<AllStoreListResponse.Store> storeList = entry.getValue();
            for (AllStoreListResponse.Store store : storeList) {
                TikTokStoreInfo tikTokStoreInfo = new TikTokStoreInfo();
                tikTokStoreInfo.setPuid(puid);
                tikTokStoreInfo.setShopId(shopId);
                tikTokStoreInfo.setAdvertiserId(entry.getKey());
                tikTokStoreInfo.setStoreId(store.getStoreId());
                tikTokStoreInfo.setStoreAuthorizedBcId(store.getStoreAuthorizedBcId());
                tikTokStoreInfo.setStoreType(store.getStoreType());
                tikTokStoreInfo.setStoreName(store.getStoreName());
                tikTokStoreInfo.setStoreCode(store.getStoreCode());
                tikTokStoreInfo.setIsDel(0);
                insertList.add(tikTokStoreInfo);
            }
        }
        tikTokStoreInfoDao.batchInsertOrUpDate(puid, insertList);
        //删除
        tikTokStoreInfoDao.deleteByIds(puid, deleteIdList);
    }
}
