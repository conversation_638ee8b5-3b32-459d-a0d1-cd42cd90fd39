package com.meiyunji.sponsored.service.account.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.meiyunji.amazon.mws.base.AmznEndpoint;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.util.Date;

/**
 * VcShopAuth
 * <AUTHOR>
 */
@DbTable(value = "t_vc_shop_auth")
@Data
public class VcShopAuth extends BasePo {
    /**
     * id
     */
	@DbColumn(value = "id",key = true,autoIncrement = true)
	private Integer id;
    /**
     * 用户id
     */
	@DbColumn(value = "puid")
	private Integer puid;
    /**
     * 店铺名称
     */
	@DbColumn(value = "name")
	private String name;
    /**
     * 亚马逊卖家编号
     */
	@DbColumn(value = "selling_partner_id")
	private String sellingPartnerId;
    /**
     * 大区，na,eu,fe
     */
	@DbColumn(value = "region")
	private String region;
    /**
     * 站点的marketplace id
     */
	@DbColumn(value = "marketplace_id")
	private String marketplaceId;
    /**
     * seller partner api refresh token
     */
	@DbColumn(value = "refresh_token")
	private String refreshToken;
    /**
     * seller partner api access token
     */
	@DbColumn(value = "access_token")
	private String accessToken;
    /**
     * seller partner api 授权时间 
     */
	@DbColumn(value = "auth_time")
	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
	private Date authTime;
    /**
     * 店铺状态,0正常,1授权失效
     */
	@DbColumn(value = "status")
	private Integer status;
    /**
     * 创建人id
     */
	@DbColumn(value = "create_id")
	private Integer createId;
    /**
     * 修改人id
     */
	@DbColumn(value = "update_id")
	private Integer updateId;

	@DbColumn(value = "ad_refresh_token")
	private String adRefreshToken;
	/**
	 * Advertising api access token
	 */
	@DbColumn(value = "ad_access_token")
	private String adAccessToken;
	/**
	 * Advertising api 授权时间
	 */
	@DbColumn(value = "ad_auth_time")
	private Date adAuthTime;
	/**
	 * 广告授权状态：未授权，已授权，已过期
	 */
	@DbColumn(value = "ad_status")
	private String adStatus;

	private String marketplaceCN;

	public String getMarketplaceCN() {
		return AmznEndpoint.getByMarketplaceId(this.getMarketplaceId()).getMarketplaceCN();
	}

}