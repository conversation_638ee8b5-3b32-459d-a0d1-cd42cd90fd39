package com.meiyunji.sponsored.service.permission.aspect;

import com.meiyunji.sponsored.common.permission.annotation.AsinPermissionFilter;
import com.meiyunji.sponsored.common.permission.annotation.CampaignPermissionFilter;
import com.meiyunji.sponsored.common.permission.context.PermissionContext;
import com.meiyunji.sponsored.common.permission.context.PermissionContextHolder;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.common.permission.util.SpringElExpressionEvaluator;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用权限过滤切面
 * 支持多种权限过滤类型
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Aspect
@Component
public class PermissionFilterAspect {

    private static final Logger logger = LoggerFactory.getLogger(PermissionFilterAspect.class);

    @Autowired
    private SpringElExpressionEvaluator expressionEvaluator;

    /**
     * 拦截带有 @CampaignPermissionFilter 注解的方法
     */
    @Around("@annotation(campaignPermissionFilter)")
    public Object interceptCampaignPermission(ProceedingJoinPoint joinPoint,
                                            CampaignPermissionFilter campaignPermissionFilter) throws Throwable {
        return executeWithPermissionContext(joinPoint, () ->
            buildCampaignPermissionContext(joinPoint, campaignPermissionFilter));
    }

    /**
     * 拦截带有 @AsinPermissionFilter 注解的方法
     */
    @Around("@annotation(asinPermissionFilter)")
    public Object interceptAsinPermission(ProceedingJoinPoint joinPoint,
                                        AsinPermissionFilter asinPermissionFilter) throws Throwable {
        return executeWithPermissionContext(joinPoint, () ->
            buildAsinPermissionContext(joinPoint, asinPermissionFilter));
    }

    /**
     * 执行带权限上下文的方法
     */
    private Object executeWithPermissionContext(ProceedingJoinPoint joinPoint,
                                               ContextBuilder contextBuilder) throws Throwable {
        if (!isEnabled(joinPoint)) {
            return joinPoint.proceed();
        }

        try {
            // 构建权限上下文
            PermissionContext context = contextBuilder.build();

            if (context != null) {
                // 设置权限上下文
                PermissionContextHolder.setContext(context);
                logger.debug("设置权限上下文: type={}, puid={}, uid={}, shopIds={}",
                           context.getFilterType(), context.getPuid(), context.getUid(), context.getShopIds());
            }

            // 执行原方法
            return joinPoint.proceed();

        } finally {
            // 清理权限上下文
            PermissionContextHolder.clear();
        }
    }

    /**
     * 构建Campaign权限上下文
     */
    private PermissionContext buildCampaignPermissionContext(ProceedingJoinPoint joinPoint,
                                                           CampaignPermissionFilter annotation) {
        return buildPermissionContext(joinPoint, PermissionFilterType.CAMPAIGN,
                new CampaignPermissionConfig(annotation));
    }

    /**
     * 构建ASIN权限上下文
     */
    private PermissionContext buildAsinPermissionContext(ProceedingJoinPoint joinPoint,
                                                       AsinPermissionFilter annotation) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            // 提取请求参数
            Map<String, Object> requestParams = extractRequestParams(method, args);

            // 从MDC获取用户信息
            UserInfo userInfo = getUserInfoFromMDC();
            if (userInfo == null) {
                logger.warn("无法从MDC获取用户信息");
                return null;
            }

            // 使用SpEL表达式提取参数
            List<Integer> shopIds = extractShopIds(annotation.shopIdsExpression(), requestParams);

            // 构建权限上下文
            return PermissionContext.builder()
                    .puid(userInfo.puid)
                    .uid(userInfo.uid)
                    .isAdminUser(userInfo.isAdminUser)
                    .shopIds(shopIds)
                    .filterType(PermissionFilterType.ASIN)  // 设置为ASIN类型
                    .methodName(method.getName())
                    .className(method.getDeclaringClass().getSimpleName())
                    .requestParams(requestParams)
                    .build();

        } catch (Exception e) {
            logger.error("构建ASIN权限上下文失败", e);
            return null;
        }
    }

    /**
     * 检查注解是否启用
     */
    private boolean isEnabled(ProceedingJoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();

        CampaignPermissionFilter campaignFilter = method.getAnnotation(CampaignPermissionFilter.class);
        if (campaignFilter != null) {
            return campaignFilter.enabled();
        }

        AsinPermissionFilter asinFilter = method.getAnnotation(AsinPermissionFilter.class);
        if (asinFilter != null) {
            return asinFilter.enabled();
        }

        return true;
    }

    /**
     * 提取请求参数
     */
    private Map<String, Object> extractRequestParams(Method method, Object[] args) {
        Map<String, Object> params = new HashMap<>();
        Parameter[] parameters = method.getParameters();

        for (int i = 0; i < parameters.length && i < args.length; i++) {
            Parameter parameter = parameters[i];
            Object arg = args[i];

            if (arg != null) {
                params.put(parameter.getName(), arg);
                // 如果是复杂对象，尝试提取其中的字段
                extractFieldsFromObject(params, arg);
            }
        }

        return params;
    }

    /**
     * 从对象中提取字段
     */
    private void extractFieldsFromObject(Map<String, Object> params, Object obj) {
        if (obj == null) {
            return;
        }

        try {
            Class<?> clazz = obj.getClass();
            if (clazz.getPackage() != null && clazz.getPackage().getName().startsWith("com.meiyunji")) {
                java.lang.reflect.Field[] fields = clazz.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    if (value != null) {
                        params.put(field.getName(), value);
                    }
                }
            }
        } catch (Exception e) {
            logger.debug("提取对象字段失败: {}", e.getMessage());
        }
    }

    /**
     * 使用SpEL表达式提取店铺ID列表
     */
    @SuppressWarnings("unchecked")
    private List<Integer> extractShopIds(String expression, Map<String, Object> params) {
        try {
            if (StringUtils.isBlank(expression)) {
                return null;
            }

            Object result = expressionEvaluator.evaluate(expression, params);
            if (result instanceof List) {
                return (List<Integer>) result;
            }
        } catch (Exception e) {
            logger.debug("提取shopIds失败: expression={}, error={}", expression, e.getMessage());
        }
        return null;
    }

    /**
     * 使用SpEL表达式提取广告类型
     */
    @SuppressWarnings("unchecked")
    private List<String> extractAdType(String expression, Map<String, Object> params) {
        try {
            if (StringUtils.isBlank(expression)) {
                return null;
            }

            Object result = expressionEvaluator.evaluate(expression, params);
            if (result instanceof List) {
                return (List<String>) result;
            }
        } catch (Exception e) {
            logger.debug("提取adType失败: expression={}, error={}", expression, e.getMessage());
        }
        return null;
    }

    /**
     * 从MDC获取用户信息
     */
    private UserInfo getUserInfoFromMDC() {
        try {
            String puidStr = MDC.get("puid");
            String uidStr = MDC.get("uid");
            String adminUserStr = MDC.get("adminUser");

            if (StringUtils.isBlank(puidStr) || StringUtils.isBlank(uidStr)) {
                return null;
            }

            UserInfo userInfo = new UserInfo();
            userInfo.puid = Integer.parseInt(puidStr);
            userInfo.uid = Integer.parseInt(uidStr);
            userInfo.isAdminUser = BooleanUtils.toBoolean(adminUserStr);

            return userInfo;
        } catch (Exception e) {
            logger.error("从MDC获取用户信息失败", e);
            return null;
        }
    }

    /**
     * 上下文构建器接口
     */
    @FunctionalInterface
    private interface ContextBuilder {
        PermissionContext build();
    }

    /**
     * 用户信息内部类
     */
    private static class UserInfo {
        Integer puid;
        Integer uid;
        Boolean isAdminUser;
    }
}
