package com.meiyunji.sponsored.service.permission.aspect;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.right.holder.dto.UserDTO;
import com.meiyunji.sellfox.right.utils.RightContextUtil;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.permission.annotation.AdProductPermissionFilter;
import com.meiyunji.sponsored.common.permission.context.PermissionContext;
import com.meiyunji.sponsored.common.permission.context.PermissionContextHolder;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.common.permission.util.SpringElExpressionEvaluator;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.permission.filter.PermissionFilter;
import com.meiyunji.sponsored.service.permission.filter.PermissionFilterFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 通用权限过滤切面
 * 支持多种权限过滤类型
 *
 * <AUTHOR>
 * @date 2025-01-27
 */
@Aspect
@Component
@Slf4j
public class PermissionFilterAspect {


    @Autowired
    private SpringElExpressionEvaluator expressionEvaluator;

    @Autowired
    private PermissionFilterFactory filterFactory;

    /**
     * 拦截带有 @AdProductPermissionFilter 注解的方法（统一入口）
     */
    @Around("@annotation(adProductPermissionFilter)")
    public Object interceptAdProductPermission(ProceedingJoinPoint joinPoint,
                                             AdProductPermissionFilter adProductPermissionFilter) throws Throwable {
        return executeWithPermissionContext(joinPoint, adProductPermissionFilter,() ->
            buildAdProductPermissionContext(joinPoint, adProductPermissionFilter));
    }

    /**
     * 构建统一的广告产品权限上下文
     */
    private PermissionContext buildAdProductPermissionContext(ProceedingJoinPoint joinPoint,
                                                            AdProductPermissionFilter annotation) {
        return buildPermissionContext(joinPoint, annotation.type(),
                new AdProductPermissionConfig(annotation));
    }


    /**
     * 执行带权限上下文的方法
     */
    private Object executeWithPermissionContext(ProceedingJoinPoint joinPoint,AdProductPermissionFilter adProductPermissionFilter,
                                               ContextBuilder contextBuilder) throws Throwable {
        if (!adProductPermissionFilter.enabled()) {
            return joinPoint.proceed();
        }
        try {
            // 构建权限上下文
            PermissionContext context = contextBuilder.build();
            if (context.hasAdminPermission()) {
                return joinPoint.proceed();
            }
            // 设置权限上下文到当前线程
            PermissionContextHolder.setContext(context);
            log.debug("设置权限上下文: type={}, puid={}, uid={}, shopIds={}",
                       context.getFilterType(), context.getPuid(), context.getUid(), context.getShopIds());
            if (PermissionFilterStrategy.REJECT == context.getFilterStrategy() ) {
                // todo 拒绝策略：检查权限，无权限直接抛异常
                PermissionFilter filter = filterFactory.getFilter(adProductPermissionFilter.type());
                boolean needFilter = filter.isNeedFilter(context);
                if (needFilter) {
                    // 判断是否有活动id权限
                    boolean hasPermission = filter.hasPermission(context);
                    if (!hasPermission) {
                        throw new BizServiceException("访问被拒绝：用户无权限访问该资源");
                    }
                }
            }
            // 执行原方法
            return joinPoint.proceed();
        } finally {
            // 清理权限上下文
            PermissionContextHolder.clear();
        }
    }

    /**
     * 通用权限上下文构建方法（模板方法）
     */
    private PermissionContext buildPermissionContext(ProceedingJoinPoint joinPoint,
                                                   PermissionFilterType filterType,
                                                   PermissionConfig config) {
        try {
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            Object[] args = joinPoint.getArgs();

            // 提取请求参数
            Map<String, Object> requestParams = extractRequestParams(method, args);

            // 从MDC获取用户信息
            UserInfo userInfo = getUserInfoFromMDCOrRightContext();
            if (userInfo == null) {
                log.warn("无法获取用户信息");
                return null;
            }

            // 使用配置提取特定参数
            PermissionContext.PermissionContextBuilder builder = PermissionContext.builder()
                    .puid(userInfo.puid)
                    .uid(userInfo.uid)
                    .isAdminUser(userInfo.isAdminUser)
                    .filterType(filterType)
                    .methodName(method.getName())
                    .className(method.getDeclaringClass().getSimpleName())
                    .requestParams(requestParams);

            // 使用配置类提取特定参数
            config.configureContext(builder, requestParams, this);

            return builder.build();

        } catch (Exception e) {
            log.error("构建{}权限上下文失败", filterType, e);
            return null;
        }
    }


    /**
     * 提取请求参数
     */
    private Map<String, Object> extractRequestParams(Method method, Object[] args) {
        Map<String, Object> params = new HashMap<>();
        Parameter[] parameters = method.getParameters();

        for (int i = 0; i < parameters.length && i < args.length; i++) {
            Parameter parameter = parameters[i];
            Object arg = args[i];

            if (arg != null) {
                params.put(parameter.getName(), arg);
                // 如果是复杂对象，尝试提取其中的字段
                extractFieldsFromObject(params, arg);
            }
        }

        return params;
    }

    /**
     * 从对象中提取字段
     */
    private void extractFieldsFromObject(Map<String, Object> params, Object obj) {
        if (obj == null) {
            return;
        }

        try {
            Class<?> clazz = obj.getClass();
            if (clazz.getPackage() != null && clazz.getPackage().getName().startsWith("com.meiyunji")) {
                java.lang.reflect.Field[] fields = clazz.getDeclaredFields();
                for (java.lang.reflect.Field field : fields) {
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    if (value != null) {
                        params.put(field.getName(), value);
                    }
                }
            }
        } catch (Exception e) {
            log.debug("提取对象字段失败: {}", e.getMessage());
        }
    }

    /**
     * 使用SpEL表达式提取店铺ID列表
     */
    @SuppressWarnings("unchecked")
    private List<Integer> extractShopIds(String expression, Map<String, Object> params) {
        try {
            if (StringUtils.isBlank(expression)) {
                return null;
            }

            Object result = expressionEvaluator.evaluate(expression, params);
            if (result instanceof List) {
                return (List<Integer>) result;
            }
            if (result instanceof Integer) {
                return Lists.newArrayList((Integer) result);
            }
        } catch (Exception e) {
            log.debug("提取shopIds失败: expression={}, error={}", expression, e.getMessage());
        }
        return null;
    }

    /**
     * 使用SpEL表达式提取广告类型
     */
    @SuppressWarnings("unchecked")
    private List<String> extractAdType(String expression, Map<String, Object> params) {
        try {
            if (StringUtils.isBlank(expression)) {
                return null;
            }

            Object result = expressionEvaluator.evaluate(expression, params);
            if (result instanceof List) {
                return (List<String>) result;
            }
            if (result instanceof String) {
                return Lists.newArrayList((String) result);
            }
        } catch (Exception e) {
            log.debug("提取adType失败: expression={}, error={}", expression, e.getMessage());
        }
        return null;
    }

    /**
     * 从MDC or 权限上下文获取用户信息
     */
    private UserInfo getUserInfoFromMDCOrRightContext() {
        try {
            String puidStr = MDC.get(Constants.PUID);
            String uidStr = MDC.get(Constants.UID);
            boolean isAdminUser = BooleanUtils.toBoolean(MDC.get(Constants.ADMIN_USER));

            if (StringUtils.isBlank(puidStr) || StringUtils.isBlank(uidStr)) {
                // 尝试从权限
                UserDTO user = RightContextUtil.getUser(false);
                puidStr = String.valueOf(user.getPuid());
                uidStr = String.valueOf(user.getId());
            }
            if (StringUtils.isBlank(uidStr) || StringUtils.isBlank(puidStr)) {
                return null;
            }
            UserInfo userInfo = new UserInfo();
            userInfo.puid = Integer.parseInt(puidStr);
            userInfo.uid = Integer.parseInt(uidStr);
            userInfo.isAdminUser = isAdminUser;

            return userInfo;
        } catch (Exception e) {
            log.error("从MDC获取用户信息失败", e);
            return null;
        }
    }

    /**
     * 上下文构建器接口
     */
    @FunctionalInterface
    private interface ContextBuilder {
        PermissionContext build();
    }

    /**
     * 权限配置接口
     */
    private interface PermissionConfig {
        /**
         * 配置权限上下文
         * @param builder 上下文构建器
         * @param requestParams 请求参数
         * @param aspect 切面实例，用于调用参数提取方法
         */
        void configureContext(PermissionContext.PermissionContextBuilder builder,
                            Map<String, Object> requestParams,
                            PermissionFilterAspect aspect);

        /**
         * 是否启用
         */
        boolean isEnabled();
    }

    /**
     * 统一广告产品权限配置实现
     */
    private static class AdProductPermissionConfig implements PermissionConfig {
        private final AdProductPermissionFilter annotation;

        public AdProductPermissionConfig(AdProductPermissionFilter annotation) {
            this.annotation = annotation;
        }

        @Override
        public void configureContext(PermissionContext.PermissionContextBuilder builder,
                                   Map<String, Object> requestParams,
                                   PermissionFilterAspect aspect) {
            // 提取通用参数
            List<Integer> shopIds = aspect.extractShopIds(annotation.shopIdsExpression(), requestParams);

            // 设置通用字段
            builder.shopIds(shopIds)
                   .filterStrategy(annotation.strategy());

            // 根据权限类型设置特定字段
            PermissionFilterType filterType = annotation.type();
            switch (filterType) {
                case CAMPAIGN:
                    // 提取广告类型
                    List<String> adType = aspect.extractAdType(annotation.adTypeExpression(), requestParams);
                    builder.adType(adType);
                    break;

                case ASIN:

                default:
                    log.warn("未知的权限过滤类型: {}", filterType);
                    break;
            }
        }

        @Override
        public boolean isEnabled() {
            return annotation.enabled();
        }
    }

    /**
     * 用户信息内部类
     */
    private static class UserInfo {
        Integer puid;
        Integer uid;
        Boolean isAdminUser;
    }
}
