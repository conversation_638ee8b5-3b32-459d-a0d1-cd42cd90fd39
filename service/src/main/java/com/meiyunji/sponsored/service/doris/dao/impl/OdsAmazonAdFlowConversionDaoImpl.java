package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProfileBo;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdFlowConversionDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdFlowConversionBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * created time:2024-06-26 09:45:29
 */
@Repository
@Slf4j
public class OdsAmazonAdFlowConversionDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdCampaignAllReport> implements IOdsAmazonAdFlowConversionDao  {


    /**
     * @param puid
     * @param shopIdList
     * @param marketplaceIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @return DashboardAdFlowConversionBaseDataDto
     *
     */
    @Override
    public DashboardAdFlowConversionBaseDataDto queryFlowConversion(Integer puid, List<Integer> shopIdList, List<String> marketplaceIdList, String currency, String startDate, String endDate, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder();
        sql.append("select ");
        sql.append("sum(if (type = 'sp', sales7d,sales14d) * c.rate) sales, ");
        sql.append("sum(cost * c.rate) cost, ");
        sql.append("sum(clicks) clicks, ");
        sql.append("sum(impressions) impressions, ");
        sql.append("sum(if (type = 'sb' , viewable_impressions,view_impressions)) viewable,");
        sql.append("sum(if (type = 'sb' , cost , 0) * c.rate) sbCost,");
        sql.append("sum(if (type = 'sd' , cost , 0) * c.rate) sdCost,");
        sql.append("sum(if (type = 'sb' , viewable_impressions, 0)) sbViewable,");
        sql.append("sum(if (type = 'sd' , view_impressions, 0)) sdViewable,");
        sql.append("sum(if (type = 'sp' , conversions7d,conversions14d)) orderNum ");
        sql.append("from ods_t_amazon_ad_campaign_all_report report ");
        sql.append("join (select * from dim_currency_rate where puid = ? and `to` = ? ) c on report.puid = c.puid and DATE_FORMAT(report.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = report.marketplace_id and c.`from` = m.currency ");
        sql.append("where report.is_summary = 1 and report.puid = ? ");
        argsList.add(puid);
        argsList.add(currency);
        argsList.add(puid);

        if(CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sql.append(SqlStringUtil.dealDorisInList("report.marketplace_id", marketplaceIdList, argsList));

        }
        if(CollectionUtils.isNotEmpty(shopIdList)) {
            sql.append(SqlStringUtil.dealDorisInList("report.shop_id", shopIdList, argsList));
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sql.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sql.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sql.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sql.append(" and report.campaign_id in ( ");
            sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sql.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sql.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sql.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sql.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sql.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        log.info(sql.toString());
        return getJdbcTemplate().queryForObject(sql.toString(), new ObjectMapper<>(DashboardAdFlowConversionBaseDataDto.class), argsList.toArray());
    }

    @Override
    public List<AmazonAdCampaignAllReport> getMultiShopReportByCampaignIdListAll(Integer puid, List<Integer> shopIds, String startDate, String endDate, List<String> marketplaceIds, List<String> ids, String currency) {
        StringBuilder selectSql = new StringBuilder("SELECT cost_type,shop_id,count_date,r.marketplace_id,type,campaign_id,campaign_name,cost * c.rate cost,if (type = 'sp', sales7d * c.rate,sales14d * c.rate) total_sales,")
                .append(" if (type = 'sp', sales7d_same_sku * c.rate,sales14d_same_sku * c.rate) ad_sales,`impressions`,clicks,if (type = 'sp' , conversions7d,conversions14d) order_num, if (type = 'sp' ,`conversions7d_same_sku`,`conversions14d_same_sku`) ad_order_num,")
                .append(" if (type = 'sp' ,`units_ordered7d`,if (type = 'sb' ,`units_sold14d`,`units_ordered14d`)) sale_num,if (type = 'sp' ,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`) ad_sale_num, ")
                .append(" view_impressions, detail_page_view14d,")
                .append(" orders_new_to_brand14d, orders_new_to_brand_percentage14d,order_rate_new_to_brand14d,")
                .append(" sales_new_to_brand14d  * c.rate sales_new_to_brand14d, sales_new_to_brand_percentage14d  * c.rate sales_new_to_brand_percentage14d,units_ordered_new_to_brand14d, units_ordered_new_to_brand_percentage14d ,")
                .append(" vctr, video5second_view_rate, video5second_views, video_first_quartile_views,")
                .append(" video_midpoint_views, video_third_quartile_views, video_unmutes, viewable_impressions,")
                .append(" video_complete_views, vtr ");
        selectSql.append(" from ods_t_amazon_ad_campaign_all_report r ");
        selectSql.append("join (select * from dim_currency_rate where puid = ? and `to` = ? ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        selectSql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(currency);
        StringBuilder whereSql = new StringBuilder(" where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            whereSql.append(" and r.shop_id in ('").append(StringUtils.join(shopIds, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(marketplaceIds)) {
            whereSql.append(" and r.marketplace_id in ('").append(StringUtils.join(marketplaceIds, "','")).append("') ");
        }
        whereSql.append(" and r.count_day >= ? and r.count_day <= ? and r.is_summary = 1 ");
        argsList.add(DateUtil.getDateSqlFormat(startDate));
        argsList.add(DateUtil.getDateSqlFormat(endDate));
        // todo 是否使用子查询？
        if(CollectionUtils.isNotEmpty(ids)){
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("r.campaign_id", ids, argsList));
        }
        selectSql.append(whereSql);
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReport.class));
    }
}
