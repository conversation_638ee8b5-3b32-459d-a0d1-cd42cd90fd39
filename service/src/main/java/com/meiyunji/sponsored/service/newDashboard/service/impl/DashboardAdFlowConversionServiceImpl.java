package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdFlowConversionResponseVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdFlowConversionDao;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdFlowConversionBaseDataDto;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdFlowConversionService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardFlowConversionReqVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil.*;
import static com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil.calRate4Decimal;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DashboardAdFlowConversionServiceImpl implements IDashboardAdFlowConversionService {
    @Autowired
    private IOdsAmazonAdFlowConversionDao iOdsAmazonAdFlowConversionDao;

    @Override
    public DashboardAdFlowConversionResponseVo queryFlowConversion(DashboardFlowConversionReqVo reqVo) {

        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }


        DashboardAdFlowConversionBaseDataDto currentData = modifyNUllValue(iOdsAmazonAdFlowConversionDao.queryFlowConversion(reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceIdList(),
                reqVo.getCurrency(), reqVo.getStartDate(), reqVo.getEndDate(), siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds()));
        DashboardAdFlowConversionBaseDataDto momData = modifyNUllValue(iOdsAmazonAdFlowConversionDao.queryFlowConversion(reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceIdList(),
                reqVo.getCurrency(), reqVo.getMomStartDate(), reqVo.getMomEndDate(),
                null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds()));
        DashboardAdFlowConversionBaseDataDto yoyData = modifyNUllValue(iOdsAmazonAdFlowConversionDao.queryFlowConversion(reqVo.getPuid(),
                reqVo.getShopIdList(),
                reqVo.getMarketplaceIdList(),
                reqVo.getCurrency(), reqVo.getYoyStartDate(), reqVo.getYoyEndDate(), null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds()));
        DashboardAdFlowConversionResponseVo.Builder builder = DashboardAdFlowConversionResponseVo.newBuilder();
        //设置曝光量的五个业务值
        builder.setImpressions(String.valueOf(currentData.getImpressions()));
        builder.setImpressionsMom(String.valueOf(momData.getImpressions()));
        builder.setImpressionsYoy(String.valueOf(yoyData.getImpressions()));
        builder.setImpressionsMomRate(calRate4Long(Optional.ofNullable(currentData.getImpressions()).orElse((long) 0), momData.getImpressions()));
        builder.setImpressionsYoyRate(calRate4Long(currentData.getImpressions(), yoyData.getImpressions()));
        //设置可见广告次数的五个业务值
        builder.setViewableImpressions(String.valueOf(currentData.getViewable()));
        builder.setViewableImpressionsMom(String.valueOf(momData.getViewable()));
        builder.setViewableImpressionsYoy(String.valueOf(yoyData.getViewable()));
        builder.setViewableImpressionsMomRate(calRate4Int(currentData.getViewable(), momData.getViewable()));
        builder.setViewableImpressionsYoyRate(calRate4Int(currentData.getViewable(), yoyData.getViewable()));
        //广告点击率的五个业务值
        builder.setClickRate(calPercentStr4Long(currentData.getClicks().longValue(), currentData.getImpressions()));
        builder.setClickRateMom(calPercentStr4Long(momData.getClicks().longValue(), momData.getImpressions()));
        builder.setClickRateYoy(calPercentStr4Long(yoyData.getClicks().longValue(), yoyData.getImpressions()));
        builder.setClickRateMomRate(calRate4Decimal(calPercent4LongScale4(currentData.getClicks().longValue(), currentData.getImpressions()),
                calPercent4LongScale4(momData.getClicks().longValue(), momData.getImpressions())));
        builder.setClickRateYoyRate(calRate4Decimal(calPercent4LongScale4(currentData.getClicks().longValue(), currentData.getImpressions()),
                calPercent4LongScale4(yoyData.getClicks().longValue(), yoyData.getImpressions())));
        //广告点击量
        builder.setClicks(String.valueOf(currentData.getClicks()));
        builder.setClicksMom(String.valueOf(momData.getClicks()));
        builder.setClicksYoy(String.valueOf(yoyData.getClicks()));
        builder.setClicksMomRate(calRate4Int(currentData.getClicks(), momData.getClicks()));
        builder.setClicksYoyRate(calRate4Int(currentData.getClicks(), yoyData.getClicks()));

        //广告转化率的五个业务值 订单/点击
        builder.setConversionRate(calPercentStr4Int(currentData.getOrderNum(), currentData.getClicks()));
        builder.setConversionRateMom(calPercentStr4Int(momData.getOrderNum(), momData.getClicks()));
        builder.setConversionRateYoy(calPercentStr4Int(yoyData.getOrderNum(), yoyData.getClicks()));
        builder.setConversionRateMomRate(calRate4Decimal(calPercent4IntScale4(currentData.getOrderNum(), currentData.getClicks()),
                calPercent4IntScale4(momData.getOrderNum(), momData.getClicks())));
        builder.setConversionRateYoyRate(calRate4Decimal(calPercent4IntScale4(currentData.getOrderNum(), currentData.getClicks()),
                calPercent4IntScale4(yoyData.getOrderNum(), yoyData.getClicks())));
        //广告订单量
        builder.setOrderNum(String.valueOf(currentData.getOrderNum()));
        builder.setOrderNumMom(String.valueOf(momData.getOrderNum()));
        builder.setOrderNumYoy(String.valueOf(yoyData.getOrderNum()));
        builder.setOrderNumMomRate(calRate4Int(currentData.getOrderNum(), momData.getOrderNum()));
        builder.setOrderNumYoyRate(calRate4Int(currentData.getOrderNum(), yoyData.getOrderNum()));
        //CPC
        builder.setCpc(formatDecimal(calPercent4DecimalAndIntScale4(new BigDecimal(currentData.getCost()), currentData.getClicks())));
        builder.setCpcMom(formatDecimal(calPercent4DecimalAndIntScale4(new BigDecimal(momData.getCost()), momData.getClicks())));
        builder.setCpcYoy(formatDecimal(calPercent4DecimalAndIntScale4(new BigDecimal(yoyData.getCost()), yoyData.getClicks())));
        builder.setCpcMomRate(calRate4Decimal(calPercent2DecimalAndInt(new BigDecimal(currentData.getCost()), currentData.getClicks()),
                calPercent2DecimalAndInt(new BigDecimal(momData.getCost()), momData.getClicks())));
        builder.setCpcYoyRate(calRate4Decimal(calPercent2DecimalAndInt(new BigDecimal(currentData.getCost()), currentData.getClicks()),
                calPercent2DecimalAndInt(new BigDecimal(yoyData.getCost()), yoyData.getClicks())));

        //VCPM 千次可见广告的花费
        // 计算公式：1.sb可见展示次数为0时 sd广告花费/sd可见展示次数 2.sd可见展示次数为0时 sb广告花费/sb可见展示次数 3.都不为0 （sb广告花费+sd广告花费）/(sb可见展示次数+sd可见展示次数)
        BigDecimal vcpmCost = (currentData.getSbViewable() == 0 ? BigDecimal.ZERO : currentData.getSbCost()).add(currentData.getSdViewable() == 0 ? BigDecimal.ZERO : currentData.getSdCost());
        BigDecimal monVcpmCost = (momData.getSbViewable() == 0 ? BigDecimal.ZERO : momData.getSbCost()).add(momData.getSdViewable() == 0 ? BigDecimal.ZERO : momData.getSdCost());
        BigDecimal yoyVcpmCost = (yoyData.getSbViewable() == 0 ? BigDecimal.ZERO : yoyData.getSbCost()).add(yoyData.getSdViewable() == 0 ? BigDecimal.ZERO : yoyData.getSdCost());
        String vcpm = calVcpm2String(vcpmCost, currentData.getSbViewable() + currentData.getSdViewable());
        String momVcpm = calVcpm2String(monVcpmCost, momData.getSbViewable() + momData.getSdViewable());
        String yoyVcpm = calVcpm2String(yoyVcpmCost, yoyData.getSbViewable() + yoyData.getSdViewable());
        builder.setVcpm(vcpm);
        builder.setVcpmMom(momVcpm);
        builder.setVcpmYoy(yoyVcpm);
        builder.setVcpmMomRate(calRate4Decimal(new BigDecimal(vcpm), new BigDecimal(momVcpm)));
        builder.setVcpmYoyRate(calRate4Decimal(new BigDecimal(vcpm),new BigDecimal(yoyVcpm)));

        //广告笔单价
        builder.setUnitPrice(formatDecimal(calPercent4DecimalAndIntScale4(new BigDecimal(currentData.getSales()), currentData.getOrderNum())));
        builder.setUnitPriceMom(formatDecimal(calPercent4DecimalAndIntScale4(new BigDecimal(momData.getSales()), momData.getOrderNum())));
        builder.setUnitPriceYoy(formatDecimal(calPercent4DecimalAndIntScale4(new BigDecimal(yoyData.getSales()), yoyData.getOrderNum())));
        builder.setUnitPriceMomRate(calRate4Decimal(calPercent4DecimalAndIntScale4(new BigDecimal(currentData.getSales()), currentData.getOrderNum()),
                calPercent4DecimalAndIntScale4(new BigDecimal(momData.getSales()), momData.getOrderNum())));
        builder.setUnitPriceYoyRate(calRate4Decimal(calPercent4DecimalAndIntScale4(new BigDecimal(currentData.getSales()), currentData.getOrderNum()),
                calPercent4DecimalAndIntScale4(new BigDecimal(yoyData.getSales()), yoyData.getOrderNum())));

        //ACOS
        builder.setAcos(calPercentStr4Decimal(new BigDecimal(currentData.getCost()), new BigDecimal(currentData.getSales())));
        builder.setAcosMom(calPercentStr4Decimal(new BigDecimal(momData.getCost()), new BigDecimal(momData.getSales())));
        builder.setAcosYoy(calPercentStr4Decimal(new BigDecimal(yoyData.getCost()), new BigDecimal(yoyData.getSales())));
        builder.setAcosMomRate(calRate4Decimal(calPercent4DecimalScale4(new BigDecimal(currentData.getCost()), new BigDecimal(currentData.getSales())),
                calPercent4DecimalScale4(new BigDecimal(momData.getCost()), new BigDecimal(momData.getSales()))));
        builder.setAcosYoyRate(calRate4Decimal(calPercent4DecimalScale4(new BigDecimal(currentData.getCost()), new BigDecimal(currentData.getSales())),
                calPercent4DecimalScale4(new BigDecimal(yoyData.getCost()), new BigDecimal(yoyData.getSales()))));

        //ROAS
        builder.setRoas(calPercentStr2Decimal(new BigDecimal(currentData.getSales()), new BigDecimal(currentData.getCost())));
        builder.setRoasMom(calPercentStr2Decimal(new BigDecimal(momData.getSales()), new BigDecimal(momData.getCost())));
        builder.setRoasYoy(calPercentStr2Decimal(new BigDecimal(yoyData.getSales()), new BigDecimal(yoyData.getCost())));
        builder.setRoasMomRate(calRate4Decimal(calPercent2Decimal(new BigDecimal(currentData.getSales()), new BigDecimal(currentData.getCost())),
                calPercent2Decimal(new BigDecimal(momData.getSales()), new BigDecimal(momData.getCost()))));
        builder.setRoasYoyRate(calRate4Decimal(calPercent2Decimal(new BigDecimal(currentData.getSales()), new BigDecimal(currentData.getCost())),
                calPercent2Decimal(new BigDecimal(yoyData.getSales()), new BigDecimal(yoyData.getCost()))));

        //广告花费
        builder.setCost(formatDecimal(new BigDecimal((currentData.getCost()))));
        builder.setCostMom(formatDecimal(new BigDecimal((momData.getCost()))));
        builder.setCostYoy(formatDecimal(new BigDecimal((yoyData.getCost()))));
        builder.setCostMomRate(calRate4Decimal(new BigDecimal(currentData.getCost()), new BigDecimal(momData.getCost())));
        builder.setCostYoyRate(calRate4Decimal(new BigDecimal(currentData.getCost()), new BigDecimal(yoyData.getCost())));
        //广告销售额
        builder.setTotalSales(formatDecimal(new BigDecimal((currentData.getSales()))));
        builder.setTotalSalesMom(formatDecimal(new BigDecimal((momData.getSales()))));
        builder.setTotalSalesYoy(formatDecimal(new BigDecimal((yoyData.getSales()))));
        builder.setTotalSalesMomRate(calRate4Decimal(new BigDecimal(currentData.getSales()), new BigDecimal(momData.getSales())));
        builder.setTotalSalesYoyRate(calRate4Decimal(new BigDecimal(currentData.getSales()), new BigDecimal(yoyData.getSales())));
        return builder.build();
    }

    private DashboardAdFlowConversionBaseDataDto modifyNUllValue(DashboardAdFlowConversionBaseDataDto dto) {
        dto.setCost(Optional.ofNullable(dto.getCost()).orElse("0"));
        dto.setSales(Optional.ofNullable(dto.getSales()).orElse("0"));
        dto.setClicks(Optional.ofNullable(dto.getClicks()).orElse(0));
        dto.setOrderNum(Optional.ofNullable(dto.getOrderNum()).orElse(0));
        dto.setViewable(Optional.ofNullable(dto.getViewable()).orElse(0));
        dto.setImpressions(Optional.ofNullable(dto.getImpressions()).orElse((long) 0));
        dto.setSbCost(Optional.ofNullable(dto.getSbCost()).orElse(BigDecimal.ZERO));
        dto.setSdCost(Optional.ofNullable(dto.getSdCost()).orElse(BigDecimal.ZERO));
        dto.setSbViewable(Optional.ofNullable(dto.getSbViewable()).orElse(0));
        dto.setSdViewable(Optional.ofNullable(dto.getSdViewable()).orElse(0));
        return dto;
    }
}
