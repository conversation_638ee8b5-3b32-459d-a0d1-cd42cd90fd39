package com.meiyunji.sponsored.service.wordFrequency.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTopBo;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.dto.WordRootTopDto;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSp;

import java.util.List;


/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-20  22:44
 */
public interface IAmazonAdWordRootKeywordSpDao extends IBaseShardingSphereDao<AmazonAdWordRootKeywordSp> {

    /**
     * 词根翻译查询
     */
    List<WordRootTranslatorBo> listTranslatorBoByShopId(Integer puid, Integer shopId, String start, String end, int limit);

    /**
     * 批量更新词根翻译
     */
    void batchUpdateWordRootCn(Integer puid, List<WordRootTranslatorBo> updateList);

    /**
     * 根据关键词列表获取对应词根的关键词id
     */
    List<String> listKeywordIdByWordRootAndKeywordIdList(Integer puid, Integer shopId, String wordRoot, List<String> keywordIds);

    void batchInsertOrUpdateSpKeyword(Integer puid, List<AmazonAdWordRootKeywordSp> amazonAdWordRootKeywordSpList);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
