package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.walmart.oms.advertiser.base.dto.AddKeywordsToExistingKeywordBiddedCampaignDTO;
import com.walmart.oms.advertiser.model.AddKeywordsToExistingKeywordBiddedCampaignResponse;
import com.walmart.oms.advertiser.model.ListAllTheKeywordsCampaignResponse;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/25 11:26
 * @describe:
 */
public interface IWalmartAdvertisingKeywordClientService {
    AddKeywordsToExistingKeywordBiddedCampaignResponse addKeywordsToExistingKeywordBiddedCampaign(List<AddKeywordsToExistingKeywordBiddedCampaignDTO> dtos) throws ServiceException;

    ListAllTheKeywordsCampaignResponse getListAllTheKeywordsCampaign(String campaignId) throws ServiceException;
}
