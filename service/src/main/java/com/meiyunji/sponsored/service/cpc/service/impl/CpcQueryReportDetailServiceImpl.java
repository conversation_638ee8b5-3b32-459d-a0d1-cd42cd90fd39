package com.meiyunji.sponsored.service.cpc.service.impl;

import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.bo.SearchTermBO;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdQueryKeywordAndTargetVo;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class CpcQueryReportDetailServiceImpl implements ICpcQueryReportDetailService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;

    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;

    @Autowired
    private ICpcSbQueryKeywordReportDao cpcSbQueryKeywordReportDao;

    @Autowired
    private IOdsCpcQueryKeywordReportDao odsCpcQueryKeywordReportDao;



    private List<AdQueryKeywordAndTargetVo> getAdQueryDailyReports(int puid, String adType, CpcQueryWordDetailDto dto) {
        //日,周,月报告数据
        if ("SP".equalsIgnoreCase(adType)) {
            if (Boolean.TRUE.equals(dto.getIsTarget())) {
                List<CpcQueryTargetingReport> reports =
                        cpcQueryTargetingReportDao.getDetailList(puid, dto);
                return reports.stream().map(item -> {
                    AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();

                    vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                            .format(DateTimeFormatter.ISO_LOCAL_DATE));
                    vo.setDate(vo.getLabel());
                    vo.setAdSale(item.getTotalSales());
                    vo.setAdSelfSale(item.getAdSales());
                    vo.setAdOtherSale(item.getAdOtherSales());
                    vo.setAdOrderNum(item.getSaleNum());
                    vo.setSelfAdOrderNum(item.getAdSaleNum());
                    vo.setOtherAdOrderNum(item.getAdOtherOrderNum());
                    vo.setAdSaleNum(item.getOrderNum());
                    vo.setAdSelfSaleNum(item.getAdOrderNum());
                    vo.setAdOtherSaleNum(item.getAdOtherSaleNum());
                    vo.setAdCost(item.getCost());
                    vo.setClicks(Long.valueOf(item.getClicks()));
                    vo.setImpressions(Long.valueOf(item.getImpressions()));
                    vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                    vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                    vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                    vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                    vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                    vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                    return vo;
                }).collect(Collectors.toList());
            } else {
                List<CpcQueryKeywordReport> reports =
                        cpcQueryKeywordReportDao.getDetailList(puid, dto);
                return reports.stream().map(item -> {
                    AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();

                    vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                            .format(DateTimeFormatter.ISO_LOCAL_DATE));
                    vo.setDate(vo.getLabel());
                    vo.setAdSale(item.getTotalSales());
                    vo.setAdSelfSale(item.getAdSales());
                    vo.setAdOtherSale(item.getAdOtherSales());
                    vo.setAdOrderNum(item.getSaleNum());
                    vo.setSelfAdOrderNum(item.getAdSaleNum());
                    vo.setOtherAdOrderNum(item.getAdOtherOrderNum());
                    vo.setAdSaleNum(item.getOrderNum());
                    vo.setAdSelfSaleNum(item.getAdOrderNum());
                    vo.setAdOtherSaleNum(item.getAdOtherSaleNum());
                    vo.setAdCost(item.getCost());
                    vo.setClicks(Long.valueOf(item.getClicks()));
                    vo.setImpressions(Long.valueOf(item.getImpressions()));
                    vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                    vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                    vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                    vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                    vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                    vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                    return vo;
                }).collect(Collectors.toList());
            }
        } else if ("SB".equalsIgnoreCase(adType)) {
            List<CpcSbQueryKeywordReport> reports =
                    cpcSbQueryKeywordReportDao.getDetailList(puid, dto);
            List<AdQueryKeywordAndTargetVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getSales14d());
//                vo.setAdSelfSale(item.getSales14dSameSKU());
//                vo.setAdOtherSale(item.getSales14d().subtract(item.getSales14dSameSKU()));
                //                vo.setAdSelfSale(item.getSales14dSameSKU());
//                vo.setAdOtherSale(item.getSales14d().subtract(item.getSales14dSameSKU()));
                vo.setAdOrderNum(item.getConversions14d());
                //vo.setSelfAdOrderNum(item.getConversions14dSameSKU());
                //vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSKU());
                //vo.setAdSaleNum(item.getUnitsSold14d());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setViewableImpressions(item.getViewableImpressions());
                vo.setOrdersNewToBrand(item.getOrdersNewToBrand14d());
                vo.setUnitsOrderedNewToBrand(item.getUnitsOrderedNewToBrand14d());
                vo.setSalesNewToBrand(item.getSalesNewToBrand14d());
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
//                vo.setVrt(MathUtil.divideIntegerByOneHundred(item.getViewableImpressions(), item.getImpressions()));
//                vo.setVCtr(MathUtil.divideIntegerByOneHundred(item.getClicks(), item.getViewableImpressions()));
//                vo.setAdvertisingUnitPrice(MathUtil.divideByZero(item.getSales14d(), BigDecimal.valueOf(item.getConversions14d()), 2));
//                vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(item.getSales14dSameSKU(), item.getConversions14dSameSKU() != null ? BigDecimal.valueOf(item.getConversions14dSameSKU()) : BigDecimal.ZERO,2));
//                vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(item.getSales14d().subtract(item.getSales14dSameSKU()), BigDecimal.valueOf(item.getConversions14d() - (Optional.ofNullable(item.getConversions14dSameSKU()).orElse(0)))));
//                vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(item.getConversions14d())));
//                vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(item.getSalesNewToBrand14d(), BigDecimal.valueOf(100)), item.getSales14d()));
//                vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(item.getConversions14d())));
                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        }
        return null;
    }

    private List<AdQueryKeywordAndTargetVo> allSearchTermDailyReports(int puid, CpcQueryWordDetailDto dto) {
        List<SearchTermBO> detailList = odsCpcQueryKeywordReportDao.getDetailList(puid, dto);
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.emptyList();
        }
        return detailList.stream().map(item -> {
            AdQueryKeywordAndTargetVo vo = new AdQueryKeywordAndTargetVo();
            vo.setLabel(LocalDate.parse(item.getCountDay(), DateTimeFormatter.ofPattern("yyyy-MM-dd"))
                    .format(DateTimeFormatter.ISO_LOCAL_DATE));
            vo.setDate(vo.getLabel());
            vo.setAdSale(item.getTotalSales());
            vo.setAdOrderNum(item.getSaleNum());
            vo.setAdSaleNum(item.getOrderNum());
            vo.setAdCost(item.getCost());
            vo.setClicks(Long.valueOf(item.getClicks()));
            vo.setImpressions(Long.valueOf(item.getImpressions()));
            vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
            vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
            vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
            vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
            vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
            vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<AdQueryKeywordAndTargetVo> getWeeklyList(int puid, String adType, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares) {
        List<AdQueryKeywordAndTargetVo> reports = getAdQueryDailyReports(puid, adType, param);
        reports = ReportChartUtil.getWeekReportVosQuery(param.getStart(), param.getEnd(), reports);
        if (param.getIsCompare() == 1) {
            String startDate = param.getStart();
            String endDate = param.getEnd();
            param.setStart(param.getStartDateCompare());
            param.setEnd(param.getEndDateCompare());
            List<AdQueryKeywordAndTargetVo> compareList = getAdQueryDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getWeekReportVosQuery(param.getStart(), param.getEnd(), compareList);
            param.setStart(startDate);
            param.setEnd(endDate);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdQueryKeywordAndTargetVo> paddingWeekCompare(List<AdQueryKeywordAndTargetVo> reports, List<AdQueryKeywordAndTargetVo> compareList) {
        if (CollectionUtils.isEmpty(reports)) {
            return reports;
        }
        for (int i = 0; i < reports.size() && i < compareList.size(); i++) {
            reports.get(i).compareDataSet(compareList.get(i));
        }
        return reports;
    }

    @Override
    public List<AdQueryKeywordAndTargetVo> getDailyList(int puid, String adType, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares) {
        List<AdQueryKeywordAndTargetVo> reports = getAdQueryDailyReports(puid, adType, param);
        if (param.getIsCompare() == 1) {
            String startDate = param.getStart();
            String endDate = param.getEnd();
            param.setStart(param.getStartDateCompare());
            param.setEnd(param.getEndDateCompare());
            List<AdQueryKeywordAndTargetVo> compareList = getAdQueryDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStart(startDate);
            param.setEnd(endDate);
            return paddingDayCompare(param, reports, compareList, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } else {
            return reports;
        }
    }

    private List<AdQueryKeywordAndTargetVo> paddingDayCompare(CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> reports, List<AdQueryKeywordAndTargetVo> compareList, DateTimeFormatter formatter) {
        LocalDate start = LocalDate.parse(param.getStart(), formatter);
        LocalDate end = LocalDate.parse(param.getEnd(), formatter);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), formatter);
        List<AdQueryKeywordAndTargetVo> resList = new ArrayList<>();
        Map<String, AdQueryKeywordAndTargetVo> map = StreamUtil.toMap(reports, AdQueryKeywordAndTargetVo::getLabel);
        Map<String, AdQueryKeywordAndTargetVo> compareMap = StreamUtil.toMap(compareList, AdQueryKeywordAndTargetVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdQueryKeywordAndTargetVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdQueryKeywordAndTargetVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdQueryKeywordAndTargetVo vo;
            if (Objects.isNull(report)) {
                vo = new AdQueryKeywordAndTargetVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AdQueryKeywordAndTargetVo> getMonthlyList(int puid, String adType, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares) {
        List<AdQueryKeywordAndTargetVo> reports = getAdQueryDailyReports(puid, adType, param);
        reports = ReportChartUtil.getMonthReportVosQuery(reports);
        if (param.getIsCompare() == 1) {
            String startDate = param.getStart();
            String endDate = param.getEnd();
            param.setStart(param.getStartDateCompare());
            param.setEnd(param.getEndDateCompare());
            List<AdQueryKeywordAndTargetVo> compareList = getAdQueryDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getMonthReportVosQuery(compareList);
            param.setStart(startDate);
            param.setEnd(endDate);
            return paddingMonthCompare(param, reports, compareList, DateTimeFormatter.ofPattern("yyyyMMdd"));
        } else {
            return reports;
        }
    }

    private List<AdQueryKeywordAndTargetVo> paddingMonthCompare(CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> reports, List<AdQueryKeywordAndTargetVo> compareList, DateTimeFormatter formatter) {
        LocalDate start = LocalDate.parse(param.getStart(), formatter);
        LocalDate end = LocalDate.parse(param.getEnd(), formatter);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), formatter);
        List<AdQueryKeywordAndTargetVo> resList = new ArrayList<>();
        Map<String, AdQueryKeywordAndTargetVo> map = StreamUtil.toMap(reports, AdQueryKeywordAndTargetVo::getLabel);
        Map<String, AdQueryKeywordAndTargetVo> compareMap = StreamUtil.toMap(compareList, AdQueryKeywordAndTargetVo::getLabel);
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end));
             start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdQueryKeywordAndTargetVo report = map.get(start.format(monthFormatter));
            AdQueryKeywordAndTargetVo compareReport = compareMap.get(startCompare.format(monthFormatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdQueryKeywordAndTargetVo vo;
            if (Objects.isNull(report)) {
                vo = new AdQueryKeywordAndTargetVo();
                vo.setLabel(start.format(monthFormatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    @Override
    public List<AdQueryKeywordAndTargetVo> allSearchTermDailyList(int puid, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares) {
        List<AdQueryKeywordAndTargetVo> reports = allSearchTermDailyReports(puid, param);
        if (param.getIsCompare() == 1) {
            String startDate = param.getStart();
            String endDate = param.getEnd();
            param.setStart(param.getStartDateCompare());
            param.setEnd(param.getEndDateCompare());
            List<AdQueryKeywordAndTargetVo> compareList = allSearchTermDailyReports(puid, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStart(startDate);
            param.setEnd(endDate);
            return paddingDayCompare(param, reports, compareList, DateTimeFormatter.ISO_LOCAL_DATE);
        } else {
            return reports;
        }
    }

    @Override
    public List<AdQueryKeywordAndTargetVo> allSearchTermWeeklyList(int puid, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares) {
        List<AdQueryKeywordAndTargetVo> reports = allSearchTermDailyReports(puid, param);
        reports = ReportChartUtil.getWeekReportVosQuery(param.getStart(), param.getEnd(), reports);
        if (param.getIsCompare() == 1) {
            String startDate = param.getStart();
            String endDate = param.getEnd();
            param.setStart(param.getStartDateCompare());
            param.setEnd(param.getEndDateCompare());
            List<AdQueryKeywordAndTargetVo> compareList = allSearchTermDailyReports(puid, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getWeekReportVosQuery(param.getStart(), param.getEnd(), compareList);
            param.setStart(startDate);
            param.setEnd(endDate);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    @Override
    public List<AdQueryKeywordAndTargetVo> allSearchTermMonthlyList(int puid, CpcQueryWordDetailDto param, List<AdQueryKeywordAndTargetVo> compares) {
        List<AdQueryKeywordAndTargetVo> reports = allSearchTermDailyReports(puid, param);
        reports = ReportChartUtil.getMonthReportVosQuery(reports);
        if (param.getIsCompare() == 1) {
            String startDate = param.getStart();
            String endDate = param.getEnd();
            param.setStart(param.getStartDateCompare());
            param.setEnd(param.getEndDateCompare());
            List<AdQueryKeywordAndTargetVo> compareList = allSearchTermDailyReports(puid, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getMonthReportVosQuery(compareList);
            param.setStart(startDate);
            param.setEnd(endDate);
            return paddingMonthCompare(param, reports, compareList, DateTimeFormatter.ISO_LOCAL_DATE);
        } else {
            return reports;
        }
    }

    // 填充指标占比数据
    public static void filterMetricData(List<AdQueryKeywordAndTargetVo> voList, AdMetricDto adMetricDto) {
        for (AdQueryKeywordAndTargetVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    public static void computeMetricData(AdMetricDto adMetricDto, AdQueryKeywordAndTargetVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }


    public static void filterSumMetricData(List<AdQueryKeywordAndTargetVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdQueryKeywordAndTargetVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                    map(AdQueryKeywordAndTargetVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                    map(AdQueryKeywordAndTargetVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                    map(AdQueryKeywordAndTargetVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                    map(AdQueryKeywordAndTargetVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }

}
