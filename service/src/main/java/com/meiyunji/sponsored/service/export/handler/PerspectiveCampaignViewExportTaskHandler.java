package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.export.vo.perspective.CampaignViewExcelVO;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.service.ICampaignViewService;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CampaignViewPageVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.CampaignViewVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service(AdManagePageExportTaskConstant.PERSPECTIVE_CAMPAIGN_VIEW)
public class PerspectiveCampaignViewExportTaskHandler implements AdManagePageExportTaskHandler {

    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICampaignViewService campaignViewService;
    @Autowired
    private IAdProductRightService adProductRightService;

    @Override
    public void export(AdManagePageExportTask task) {
        CampaignViewParam param = JSONUtil.jsonToObject(task.getParam(), CampaignViewParam.class);
        if (Objects.isNull(param)) {
            log.error(String.format("产品广告透视 广告活动视图 export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }

        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        Integer puid = param.getPuid();
        Page<CampaignViewVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        //设置MDC
        adProductRightService.putUidAndAdminUserToMDC(param.getUid(), param.isAdminUser());
        CampaignViewPageVo campaignViewPageVo = campaignViewService.getAllCampaignViewPageVoList(puid, param, voPage);

        List<String> excludeFields = Lists.newArrayList();
        if (StringUtils.equalsIgnoreCase(param.getType(), Constants.SP)) {
            excludeFields.add("topImpressionShare");
        } else {
            excludeFields.add("placementSiteAmazonBusiness");
        }
        //站点币种
        String currency = AmznEndpoint.getByMarketplaceId(param.getMarketplaceId()).getCurrencyCode().value();

        List<CampaignViewExcelVO> dataList = Lists.newArrayList();
        if (StringUtils.equalsIgnoreCase(param.getType(), Constants.SP) && StringUtils.isBlank(param.getTargetingType())) {
            buildAggregateExcelVO(campaignViewPageVo, currency, dataList, excludeFields);
        } else {
            buildExcelVO(campaignViewPageVo, currency, dataList, excludeFields, param.getType());
        }


        if (CollectionUtils.isEmpty(dataList)) {
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        urlList.add(excelService.easyExcelHandlerExport(puid, dataList, param.getExportFileName(), CampaignViewExcelVO.class,
                build.currencyNew(CampaignViewExcelVO.class), excludeFields));
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private void buildAggregateExcelVO(CampaignViewPageVo campaignViewPageVo, String currency,
                                       List<CampaignViewExcelVO> dataList, List<String> excludeFields) {
        if (Objects.nonNull(campaignViewPageVo.getManualAggregateViewVo())) {
            dataList.add(CampaignViewExcelVO.getAggregateInstance(currency, "手动投放", campaignViewPageVo.getManualAggregateViewVo()));
        }
        if (Objects.nonNull(campaignViewPageVo.getAutoAggregateViewVo())) {
            dataList.add(CampaignViewExcelVO.getAggregateInstance(currency, "自动投放", campaignViewPageVo.getAutoAggregateViewVo()));
        }
        excludeFields.addAll(Lists.newArrayList("state", "campaignName", "portfolioName", "servingStatusName", "type",
                "strategy", "dailyBudget", "budgetRemaining", "suggestedBudget", "percentTimeInBudget", "costType",
                "placementTop", "placementRestOfSearch", "placementProductPage", "placementSiteAmazonBusiness",
                "startDate", "endDate"));
    }

    private void buildExcelVO(CampaignViewPageVo campaignViewPageVo, String currency, List<CampaignViewExcelVO> dataList,
                              List<String> excludeFields, String type) {
        List<CampaignViewVo> rows = campaignViewPageVo.getPage().getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            rows.forEach(row -> dataList.add(CampaignViewExcelVO.getInstance(currency, row)));
        }
        if (!StringUtils.equalsIgnoreCase(type, Constants.SP)) {
            excludeFields.addAll(Lists.newArrayList("targetingType", "placementSiteAmazonBusiness"));
        }
        excludeFields.addAll(Lists.newArrayList("targetingType"));
    }

}
