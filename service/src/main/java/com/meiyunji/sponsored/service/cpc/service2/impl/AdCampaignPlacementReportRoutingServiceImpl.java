package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.amazon.advertising.mode.PredicateEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.dao.IAdPlacementAmazonBusinessReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignPlacementReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.service2.IAdCampaignPlacementReportRoutingService;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2025-04-18 09:47
 */
@Service
public class AdCampaignPlacementReportRoutingServiceImpl implements IAdCampaignPlacementReportRoutingService {

    @Resource
    private IAdPlacementAmazonBusinessReportDao adPlacementAmazonBusinessReportDao;
    @Resource
    private IAmazonAdCampaignPlacementReportDao amazonAdCampaignPlacementReportDao;
    @Resource
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;

    private boolean isAmazonBusiness(String campaignSite) {
        return PredicateEnum.SITEAMAZONBUSINESS.value().equals(campaignSite);
    }

    @Override
    public Page getPageSpaceList(Integer puid, SearchVo search, Page page, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.getPageSpaceList(puid, search, page);
        } else {
            return amazonAdCampaignPlacementReportDao.getPageSpaceList(puid, search, page);
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdAndPlacement(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String campaignId, String placement, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.getReportByCampaignIdAndPlacement(puid, shopId, startDate, endDate, marketplaceId, campaignId, placement);
        } else {
            return amazonAdCampaignPlacementReportDao.getReportByCampaignIdAndPlacement(puid, shopId, startDate, endDate, marketplaceId, campaignId, placement);
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdListAndPlacement(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList, String placement, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.getReportByCampaignIdListAndPlacement(puid, shopId, startDate, endDate, marketplaceId, campaignIdList, placement);
        } else {
            return amazonAdCampaignPlacementReportDao.getReportByCampaignIdListAndPlacement(puid, shopId, startDate, endDate, marketplaceId, campaignIdList, placement);
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> listSumPlacementReports(Integer puid, PlacementPageParam param, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.listSumPlacementReports(puid, param);
        } else {
            return amazonAdCampaignPlacementReportDao.listSumPlacementReports(puid, param);
        }
    }

    @Override
    public List<AdHomePerformancedto> listAllPlacementReportsByDate(Integer puid, Integer shopId, String startDate, String endDate, String campaignId, String predicate, PlacementPageParam param, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.listAllPlacementReportsByDate(puid, shopId, startDate, endDate, campaignId, predicate, param);
        } else {
            return amazonAdCampaignPlacementReportDao.listAllPlacementReportsByDate(puid, shopId, startDate, endDate, campaignId, predicate, param);
        }
    }

    @Override
    public List<AdHomePerformancedto> listAllPlacementReportsByCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, String predicate, List<String> campaignIdList, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.listAllPlacementReportsByCampaignIdList(puid, shopId, startDate, endDate, predicate, campaignIdList);
        } else {
            return amazonAdCampaignPlacementReportDao.listAllPlacementReportsByCampaignIdList(puid, shopId, startDate, endDate, predicate, campaignIdList);
        }
    }

    @Override
    public List<String> getValidRecordByDate(Integer puid, Integer shopId, String marketId, String format, List<String> queryIdList, String campaignSite) {
        if (isAmazonBusiness(campaignSite)) {
            return adPlacementAmazonBusinessReportDao.getValidRecordByDate(puid, shopId, marketId, format, queryIdList);
        } else {
            return amazonAdCampaignAllReportDao.getValidRecordByDate(puid, shopId, marketId, format, queryIdList);
        }
    }
}
