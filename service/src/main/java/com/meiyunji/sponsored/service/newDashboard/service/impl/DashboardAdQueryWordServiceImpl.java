package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdQueryWordResponseVo;
import com.meiyunji.sponsored.rpc.newDashboard.QueryListDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbKeywordServiceImpl;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryKeywordReportDao;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.QueryMatchTypeEnum;
import com.meiyunji.sponsored.service.enums.SBThemesEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdQueryWordDataPageDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryWordMatchTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardThemeKeywordTextEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdQueryWordService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.util.OrderByUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardQueryWordReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-04-07 13:48
 */
@Service
@Slf4j
public class DashboardAdQueryWordServiceImpl implements IDashboardAdQueryWordService {
    //基础表头
    private List<String> baseHeaderList = Arrays.asList("queryWord", "matchType", "displayCost", "displayTotalSales", "displayAcos", "displayRoas",
            "impressions", "clicks", "orderNum", "displayClickRate", "displayConversionRate", "saleNum", "displayCpc", "displayCpa");
    private String fileNameTemplate = "用户搜索词%s%s";

    @Autowired
    private IOdsCpcQueryKeywordReportDao odsCpcQueryKeywordReportDao;
    @Autowired
    private IExcelService excelService;

    @Autowired
    private IOdsAmazonAdKeywordDao odsAmazonAdKeywordDao;
    @Autowired
    private IAmazonAdKeywordShardingDao amazonAdKeywordShardingDao;
    @Autowired
    private IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;

    @Override
    public List<DashboardAdQueryWordResponseVo> queryAdQueryWordCharts(DashboardQueryWordReqVo reqVo) {

        List<DashboardAdQueryWordDataDto> baseDataList = odsCpcQueryKeywordReportDao.queryAdQueryWordCharts(
                reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(), reqVo.getCurrency(),
                reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getOrderByField(), reqVo.getOrderBy(), reqVo.getLimit(),
                reqVo.getCampaignIds(), reqVo.getPortfolioIds(), reqVo.getSiteToday(), reqVo.getNoZero());
        if (StringUtils.isNotBlank(reqVo.getListOrderField()) || StringUtils.isNotBlank(reqVo.getListOrderType())) {
            OrderByUtil.sortedByOrderField(baseDataList, reqVo.getListOrderField(), reqVo.getListOrderType(), "queryWord");
        }
        return baseDataList.stream().map(this::buildResponse).collect(Collectors.toList());
    }

    @Override
    public List<String> exportAdQueryWordCharts(DashboardQueryWordReqVo reqVo) {
        List<DashboardAdQueryWordDataDto> baseDataList = odsCpcQueryKeywordReportDao.queryAdQueryWordCharts(
                reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(), reqVo.getCurrency(),
                reqVo.getStartDate(), reqVo.getEndDate(), reqVo.getOrderByField(), reqVo.getOrderBy(), reqVo.getLimit(), reqVo.getCampaignIds(),
                reqVo.getPortfolioIds(), reqVo.getSiteToday(), reqVo.getNoZero());

        if (StringUtils.isNotBlank(reqVo.getListOrderField()) || StringUtils.isNotBlank(reqVo.getListOrderType())) {
            OrderByUtil.sortedByOrderField(baseDataList, reqVo.getListOrderField(), reqVo.getListOrderType(), "queryWord");
        }
        List<DashboardAdQueryWordDataDto> exportDataList = new ArrayList<>();
        // 广告用户搜索词不需要打印汇总数据
        for (DashboardAdQueryWordDataDto baseData : baseDataList) {
            exportDataList.addAll(buildChildList(baseData, reqVo.getCurrency()));
        }
        log.info("dashboard export adqueryword charts, query finished and begin export, puid: {}", reqVo.getPuid());
        String url = writeExcelAndUpload(exportDataList, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export adqueryword charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    private DashboardAdQueryWordResponseVo buildResponse(DashboardAdQueryWordDataDto baseData) {
        List<DashboardAdQueryWordDataDto> childList = buildChildList(baseData, null);
        DashboardAdQueryWordResponseVo.Builder parent = buildGrpcResponseVo(baseData);
        List<DashboardAdQueryWordResponseVo> childDataList = childList.stream()
                .map(eachChild -> buildGrpcResponseVo(eachChild).build())
                .collect(Collectors.toList());
        parent.addAllChild(childDataList);
        return parent.build();
    }

    private List<DashboardAdQueryWordDataDto> buildChildList(DashboardAdQueryWordDataDto baseData, String currency) {
        String detailData = baseData.getDetailData();
        String[] childDataArr = detailData.split(";");
        Map<String, DashboardAdQueryWordDataDto> matchType2Data = new HashMap<>();
        for (String each : childDataArr) {
            DashboardAdQueryWordDataDto child = buildChildData(each, baseData.getQueryWord(), matchType2Data);
            if (StringUtils.isNotEmpty(currency)) {
                CalculateAdDataUtil.calAdCalDataForExport(child, currency);
            } else {
                CalculateAdDataUtil.calAdCalData(child);
            }
        }
        return Arrays.stream(DashboardQueryWordMatchTypeEnum.values())
                .map(each -> matchType2Data.get(each.getCode().toUpperCase()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private DashboardAdQueryWordDataDto buildChildData(String childData, String queryword, Map<String, DashboardAdQueryWordDataDto> matchType2Data) {
        String[] childDataDetailArr = childData.split(",");
        String matchType = childDataDetailArr[0];
        String displayMatchType = Optional.ofNullable(DashboardQueryWordMatchTypeEnum.upperCaseDataMap.get(childDataDetailArr[0] == null ? null : childDataDetailArr[0].toUpperCase()))
                .map(DashboardQueryWordMatchTypeEnum::getDesc)
                .orElse(StringUtils.EMPTY);
        DashboardAdQueryWordDataDto child = matchType2Data.getOrDefault(matchType == null ? null : matchType.toUpperCase(), buildDefaultData());
        child.setQueryWord(queryword);
        child.setMatchType(displayMatchType);
        child.setMatchTypeCode(matchType);
        child.setCost(child.getCost().add(new BigDecimal(childDataDetailArr[1])));
        child.setTotalSales(child.getTotalSales().add(new BigDecimal(childDataDetailArr[2])));
        child.setImpressions(child.getImpressions() + NumberUtils.toInt(childDataDetailArr[3]));
        child.setClicks(child.getClicks() + NumberUtils.toInt(childDataDetailArr[4]));
        child.setOrderNum(child.getOrderNum() + NumberUtils.toInt(childDataDetailArr[5]));
        child.setSaleNum(child.getSaleNum() + NumberUtils.toInt(childDataDetailArr[6]));
        child.setTargetNum(child.getTargetNum() + NumberUtils.toInt(childDataDetailArr[7]) );
        matchType2Data.put(matchType == null ? null : matchType.toUpperCase(), child);
        return child;
    }

    private DashboardAdQueryWordDataDto buildDefaultData() {
        DashboardAdQueryWordDataDto dataDto = new DashboardAdQueryWordDataDto();
        dataDto.setCost(BigDecimal.ZERO);
        dataDto.setTotalSales(BigDecimal.ZERO);
        dataDto.setImpressions(0L);
        dataDto.setClicks(0);
        dataDto.setOrderNum(0);
        dataDto.setSaleNum(0);
        return dataDto;
    }

    /**
     * 单条数据构建grpc响应对象
     *
     * @param dataDto
     * @return
     */
    private DashboardAdQueryWordResponseVo.Builder buildGrpcResponseVo(DashboardAdQueryWordDataDto dataDto) {
        DashboardAdQueryWordResponseVo.Builder builder = DashboardAdQueryWordResponseVo.newBuilder();
        //String类型直接拷贝
        BeanUtils.copyProperties(dataDto, builder);
        //手动设置的类型
        builder.setCost(CalculateUtil.formatDecimal(dataDto.getCost()));
        builder.setTotalSales(CalculateUtil.formatDecimal(dataDto.getTotalSales()));
        builder.setImpressions(String.valueOf(dataDto.getImpressions()));
        builder.setClicks(String.valueOf(dataDto.getClicks()));
        builder.setOrderNum(String.valueOf(dataDto.getOrderNum()));
        builder.setSaleNum(String.valueOf(dataDto.getSaleNum()));
        builder.setAcos(CalculateUtil.formatPercent(dataDto.getAcos()));
        builder.setRoas(CalculateUtil.formatDecimal(dataDto.getRoas()));
        builder.setClickRate(CalculateUtil.formatPercent(dataDto.getClickRate()));
        builder.setConversionRate(CalculateUtil.formatPercent(dataDto.getConversionRate()));
        builder.setCpc(CalculateUtil.formatDecimal(dataDto.getCpc()));
        builder.setCpa(CalculateUtil.formatDecimal(dataDto.getCpa()));
        return builder;
    }

    /**
     * 把数据写入excel并传到对象存储
     *
     * @param exportDataList
     * @param reqVo
     * @return
     */
    private String writeExcelAndUpload(List<DashboardAdQueryWordDataDto> exportDataList, DashboardQueryWordReqVo reqVo) {
        //所有数据计算完毕，构建导出数据
        String order = DashboardOrderByEnum.ASC.getCode().equals(reqVo.getOrderBy()) ? "后" : "前";
        String fileName = String.format(fileNameTemplate, order, reqVo.getLimit());

        List<String> headers = new ArrayList<>(baseHeaderList);

        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), exportDataList, fileName, DashboardAdQueryWordDataDto.class, headers, true);

    }



    @Override
    public QueryListDataResponse.KeywordListDataRpcVo.Page  queryAdQueryWordPageList(DashboardQueryWordReqVo reqVo) {


        Page<DashboardAdQueryWordDataPageDto> page = odsCpcQueryKeywordReportDao.queryAdQueryWordPage(
                reqVo.getPuid(), reqVo);

        List<DashboardAdQueryWordDataPageDto> rows = page.getRows();
        QueryListDataResponse.KeywordListDataRpcVo.Page.Builder builder = QueryListDataResponse.KeywordListDataRpcVo.Page.newBuilder();
        builder.setPageNo(page.getPageNo());
        builder.setPageSize(page.getPageSize());
        builder.setTotalPage(page.getTotalPage());
        builder.setTotalSize(page.getTotalSize());
        if (CollectionUtils.isNotEmpty(rows)) {
            builder.addAllRows(buildersPageVo(rows, reqVo));
        }
        return builder.build();
    }

    private List<QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo> buildersPageVo (List<DashboardAdQueryWordDataPageDto> rows, DashboardQueryWordReqVo reqVo) {
        Set<String> campaignIds = new HashSet<>();
        Set<String> spAdGroupIds = new HashSet<>();
        Set<String> sbAdGroupIds = new HashSet<>();
        Set<String> spKeywordIds = new HashSet<>();
        Set<String> sbKeywordIds = new HashSet<>();
        Set<String> spTargetIds = new HashSet<>();
        Set<Integer> shopIds = new HashSet<>();
        Map<String, DashboardAdQueryWordDataPageDto> keywordMap = rows.stream().peek(e -> {
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase((e.getAdType()))) {
                if ("keyword".equalsIgnoreCase(e.getType())){
                    spKeywordIds.add(e.getKeywordId());
                } else {
                    if (DashboardQueryWordMatchTypeEnum.THEME.getTargetType().equalsIgnoreCase(e.getMatchType())){
                        spKeywordIds.add(e.getKeywordId());
                    } else {
                        spTargetIds.add(e.getKeywordId());
                    }
                }
                spAdGroupIds.add(e.getAdGroupId());

            } else {
                sbKeywordIds.add(e.getKeywordId());
                sbAdGroupIds.add(e.getAdGroupId());
            }
            shopIds.add(e.getShopId());
            campaignIds.add(e.getCampaignId());
        }).collect(Collectors.toMap(e -> e.getKeywordId() + "|" + e.getShopId(), Function.identity()));



        //获取店铺名称
        long t1 = Instant.now().toEpochMilli();
        Map<Integer, ShopAuth> shopAuthMap = shopAuthDao.listAllByIds(reqVo.getPuid(), Lists.newArrayList(shopIds)).
                parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        log.info("获取店铺名称，共耗时：{}", Instant.now().toEpochMilli() - t1);
        //获取广告活动名称
        long t2 = Instant.now().toEpochMilli();
        Map<String, AmazonAdCampaignAll> campaignAllMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty((campaignIds))) {
            campaignAllMap = amazonAdCampaignAllDao.getNameByShopIdsAndCampaignIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(campaignIds), null, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));

        }
        log.info("获取广告活动名称，共耗时：{}", Instant.now().toEpochMilli() - t2);
        Map<String, AmazonAdGroup> adGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spAdGroupIds)) {
            //获取sp广告组名称
            long t3 = Instant.now().toEpochMilli();
            adGroupMap.putAll(amazonAdGroupDao.getNameByShopIdsAndGroupIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(spAdGroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity())));
            log.info("获取sp广告组名称，共耗时：{}", Instant.now().toEpochMilli() - t3);
        }


        Map<String, AmazonSbAdGroup> sbAdGroupMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(sbAdGroupIds)) {
            //获取sb广告组名称
            long t4 = Instant.now().toEpochMilli();
            sbAdGroupMap.putAll(amazonSbAdGroupDao.getNameListByShopIdsAndGroupIds(reqVo.getPuid(), Lists.newArrayList(shopIds), Lists.newArrayList(campaignIds), Lists.newArrayList(sbAdGroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity())));
            log.info("获取sb广告组名称，共耗时：{}", Instant.now().toEpochMilli() - t4);
        }

        Map<String, AmazonAdKeyword> spKeywordMap = new HashMap();

        Map<String, AmazonSbAdKeyword> sbKeywordMap = new HashMap();

        Map<String, AmazonAdTargeting> spTargetMap = new HashMap();

        if (CollectionUtils.isNotEmpty(spKeywordIds)) {
            spKeywordMap = amazonAdKeywordShardingDao.getListKeywordByKeywordIds(reqVo.getPuid(), reqVo.getShopIdList(), Lists.newArrayList(spKeywordIds)).stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, Function.identity(), (e1, e2) -> e2));
        }

        if (CollectionUtils.isNotEmpty(sbKeywordIds)) {
            sbKeywordMap = amazonSbAdKeywordDao.getListKeywordByKeywordIds(reqVo.getPuid(), reqVo.getShopIdList(), Lists.newArrayList(spKeywordIds)).stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity(), (e1, e2) -> e2));
        }

        if (CollectionUtils.isNotEmpty(spTargetIds)) {
            spTargetMap = amazonAdTargetingShardingDao.getListTargetByTargetIds(reqVo.getPuid(), reqVo.getShopIdList(), Lists.newArrayList(spTargetIds)).stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));
        }


        long t5 = Instant.now().toEpochMilli();
        Map<String, AmazonAdCampaignAll> finalCampaignAllMap = campaignAllMap;
        Map<String, AmazonAdKeyword> finalSpKeywordMap = spKeywordMap;
        Map<String, AmazonAdTargeting> finalSpTargetMap = spTargetMap;
        Map<String, AmazonSbAdKeyword> finalSbKeywordMap = sbKeywordMap;
        return rows.stream().filter(Objects::nonNull).map(e -> {

            QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder = QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.newBuilder();
            builder.setType(e.getAdType());
            setBuild(builder, e);
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(e.getAdType())) {
                AmazonAdGroup amazonAdGroup = adGroupMap.get(e.getAdGroupId());
                if (amazonAdGroup != null) {
                    Optional.ofNullable(amazonAdGroup.getName()).ifPresent(builder::setAdGroupName);
                }
                if ("keyword".equalsIgnoreCase(e.getType()) && finalSpKeywordMap.containsKey(e.getKeywordId())){
                    setSpKeyword(builder, finalSpKeywordMap.get(e.getKeywordId()));
                } else if (finalSpTargetMap.containsKey(e.getKeywordId())) {
                    if (DashboardQueryWordMatchTypeEnum.THEME.getTargetType().equalsIgnoreCase(e.getMatchType())){
                        setSpKeyword(builder, finalSpKeywordMap.get(e.getKeywordId()));
                    } else {
                        setSpTarget(builder, finalSpTargetMap.get(e.getKeywordId()));
                    }
                }

            } else {
                AmazonSbAdGroup amazonSbAdGroup = sbAdGroupMap.get(e.getAdGroupId());
                if (amazonSbAdGroup != null) {
                    Optional.ofNullable(amazonSbAdGroup.getName()).ifPresent(builder::setAdGroupName);
                }
                if (finalSbKeywordMap.containsKey(e.getKeywordId())) {
                    setSbKeyword(builder, finalSbKeywordMap.get(e.getKeywordId()));
                }
            }
            AmazonAdCampaignAll amazonAdCampaignAll = finalCampaignAllMap.get(builder.getCampaignId());
            if (amazonAdCampaignAll != null) {
                Optional.ofNullable(amazonAdCampaignAll.getName()).ifPresent(builder::setCampaignName);
            }
            ShopAuth shopAuth = shopAuthMap.get(e.getShopId());
            if (shopAuth != null) {
                Optional.ofNullable(shopAuth.getName()).ifPresent(builder::setShopName);
            }
            return builder.build();

        }).collect(Collectors.toList());

    }


    private void setBuild(QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder,
                          DashboardAdQueryWordDataPageDto dto){
        builder.setAdGroupId(dto.getAdGroupId());
        builder.setAdGroupId(dto.getAdGroupId());
        builder.setCampaignId(dto.getCampaignId());
        builder.setKeywordId(dto.getKeywordId());
        builder.setKeywordText(dto.getKeywordText());
        builder.setKeywordId(dto.getKeywordId());
        DashboardQueryWordMatchTypeEnum dashboardQueryWordMatchTypeEnum = DashboardQueryWordMatchTypeEnum.dataMap.get(dto.getMatchType());
        if (dashboardQueryWordMatchTypeEnum != null) {
            builder.setMatchType(dashboardQueryWordMatchTypeEnum.getDesc());
        }
        builder.setMatchTypeCode(dto.getMatchType());
        builder.setShopId(Int32Value.of(dto.getShopId()));
        if (QueryMatchTypeEnum.complements.getMatchType().equalsIgnoreCase(dto.getMatchType())
                ||QueryMatchTypeEnum.substitutes.getMatchType().equalsIgnoreCase(dto.getMatchType())
                ||QueryMatchTypeEnum.looseMatch.getMatchType().equalsIgnoreCase(dto.getMatchType())
                ||QueryMatchTypeEnum.closeMatch.getMatchType().equalsIgnoreCase(dto.getMatchType())) {
            builder.setTargetName("自动投放");
        } else if (QueryMatchTypeEnum.category.getMatchType().equalsIgnoreCase(dto.getMatchType()) && dto.getKeywordText().contains("\"")) {
            builder.setTargetName(dto.getKeywordText().substring(dto.getKeywordText().indexOf("\""),dto.getKeywordText().length()-1));
        } else if (QueryMatchTypeEnum.theme.getMatchType().equalsIgnoreCase(dto.getMatchType())) {

            if (DashboardQueryWordMatchTypeEnum.THEME.getCode().equalsIgnoreCase(dto.getMatchType())) {
                SpKeywordGroupValueEnum keywordGroupValueEnumByText = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(dto.getKeywordText());
                if (keywordGroupValueEnumByText != null) {
                    builder.setTargetName(keywordGroupValueEnumByText.getTextCn());
                }
            } else if (dto.getKeywordText().toLowerCase().contains(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getValue().toLowerCase())) {
                builder.setTargetName(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getMsg());
            } else if (dto.getKeywordText().toLowerCase().contains(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_BRAND.getValue().toLowerCase())) {
                builder.setTargetName(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getMsg());
            } else {
                builder.setTargetName(dto.getKeywordText());
            }
        } else if (dto.getKeywordText().equalsIgnoreCase("自动投放组")){
            builder.setTargetName("自动投放");
        } else {
            builder.setTargetName(dto.getKeywordText());
        }

        if (QueryMatchTypeEnum.broad.getMatchType().equalsIgnoreCase(dto.getMatchType())
                || QueryMatchTypeEnum.exact.getMatchType().equalsIgnoreCase(dto.getMatchType())
                || QueryMatchTypeEnum.phrase.getMatchType().equalsIgnoreCase(dto.getMatchType())) {
            builder.setTargetType("query");
        }

        if (QueryMatchTypeEnum.asin.getMatchType().equalsIgnoreCase(dto.getMatchType())
                || QueryMatchTypeEnum.asinExpanded.getMatchType().equalsIgnoreCase(dto.getMatchType())) {
            builder.setTargetType("asin");
        }
    }


    private void setSpKeyword(QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder, AmazonAdKeyword po) {
        builder.setState(po.getState());
        if (po.getMarketplaceId() != null) {
            builder.setMarketplaceId(po.getMarketplaceId());
        }
        if (po.getId() != null) {
            builder.setId(Int64Value.of(po.getId()));
        }
    }

    private void setSbKeyword(QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder, AmazonSbAdKeyword po) {
        builder.setState(po.getState());
        if (po.getMarketplaceId() != null) {
            builder.setMarketplaceId(po.getMarketplaceId());
        }
        if (po.getId() != null) {
            builder.setId(Int64Value.of(po.getId()));
        }

    }
    private void setSpTarget(QueryListDataResponse.KeywordListDataRpcVo.Page.KeywordsPageRpcVo.Builder builder, AmazonAdTargeting po) {
        builder.setState(po.getState());
        if (po.getMarketplaceId() != null) {
            builder.setMarketplaceId(po.getMarketplaceId());
        }
        if (po.getId() != null) {
            builder.setId(Int64Value.of(po.getId()));
        }
        if (po.getTargetingValue() != null && ("asin".equalsIgnoreCase(builder.getTargetType()) || QueryMatchTypeEnum.category.getMatchType().equalsIgnoreCase(builder.getMatchTypeCode()))) {
            builder.setTargetName(po.getTargetingValue());
        }
    }

}
