package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.strategy.vo.ControlledObjectParam;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.strategy.vo.AdPortfolioStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.StrategyPortfolioVo;
import com.meiyunji.sponsored.service.strategyTask.vo.ProcessTaskParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Repository
public class AmazonAdPortfolioDaoImpl extends BaseShardingDaoImpl<AmazonAdPortfolio> implements IAmazonAdPortfolioDao {


    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdPortfolio> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_portfolio`(`puid`,`shop_id`,`profile_id`,`marketplace_id`,`name`,")
                .append("`state`,`serving_status`,`portfolio_id`,`in_budget`,`budget`,`amount`,`policy`,`start_date`,`end_date`,`creation_date`,`last_updated_date`,`create_in_amzup`,`create_id`,")
                .append("`update_id`,`create_time`,`update_time`) values ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdPortfolio portfolio : list) {
            sql.append("(?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?,?, ?,?, now(3),now(3)),");
            argsList.add(portfolio.getPuid());
            argsList.add(portfolio.getShopId());
            argsList.add(portfolio.getProfileId());
            argsList.add(portfolio.getMarketplaceId());
            argsList.add(portfolio.getName());
            argsList.add(portfolio.getState());
            argsList.add(portfolio.getServingStatus());
            argsList.add(portfolio.getPortfolioId());
            argsList.add(portfolio.getInBudget());
            argsList.add(portfolio.getBudget());
            argsList.add(portfolio.getAmount());
            argsList.add(portfolio.getPolicy());
            argsList.add(portfolio.getBudgetStartDate());
            argsList.add(portfolio.getBudgetEndDate());
            argsList.add(portfolio.getCreationDate());
            argsList.add(portfolio.getLastUpdatedDate());
            argsList.add(portfolio.getCreateInAmzup());
            argsList.add(portfolio.getCreateId());
            argsList.add(portfolio.getUpdateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update name=values(name),state=values(state),serving_status=values(serving_status),amount=values(amount),budget=values(budget)," +
                "in_budget=values(in_budget),policy=values(policy),start_date=values(start_date),end_date=values(end_date),creation_date=values(creation_date),last_updated_date=values(last_updated_date),`update_time`=now(3)");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public void updateAdPortfolio(Integer puid, ScheduleTaskFinishedVo messageVo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("update t_amazon_ad_portfolio set amount=?, policy=?,end_date=? ,update_time=now(3) ");
        StringBuilder whereSql = new StringBuilder(" where puid= ? and shop_id = ? and portfolio_id=? ");
        if ("dateRange".equals(messageVo.getPolicyType())) {
            sql.append(",start_date=?");
            argsList.add(messageVo.getModifiedValue());
            argsList.add(messageVo.getPolicyType());
            if ("restore".equals(messageVo.getExecutorType())) {
                argsList.add(messageVo.getActualExecTimeSite().plusYears(2).format(DateTimeFormatter.ISO_LOCAL_DATE));
            } else {
                argsList.add(messageVo.getActualExecTimeSite().plusDays(30).format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
            argsList.add(messageVo.getActualExecTimeSite().format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else if ("monthlyRecurring".equals(messageVo.getPolicyType())) {
            sql.append(",start_date=?");
            argsList.add(messageVo.getModifiedValue());
            argsList.add("MonthlyRecurring");
            argsList.add(messageVo.getActualExecTimeSite().plusYears(2).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(null);
        } else {
            sql.append(",start_date=?");
            argsList.add(null);
            argsList.add("noBudget");
            argsList.add(null);
            argsList.add(null);
        }
        argsList.add(puid);
        argsList.add(messageVo.getShopId());
        argsList.add(messageVo.getItemId());
        sql.append(whereSql);
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioListNew(Integer puid, PortfolioPageParam param) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());
        if (param.getIsHidden() != null) {
            builder.equalTo("is_hidden", param.getIsHidden());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        // 仅展示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus()) && "enabled".equals(param.getServingStatus())) {
            builder.equalTo("serving_status",AmazonAdPortfolio.servingStatusEnum.PORTFOLIO_STATUS_ENABLED.getCode());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue());
            } else {
                builder.like("name", param.getSearchValue());
            }
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            builder.inStrList("portfolio_id", param.getPortfolioIds().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> portfolioIds = StringUtil.splitStr(param.getPortfolioId()).stream()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[]{}));
            }
            // 广告管理->选择'未分类'广告组合列表为空
            if ("-1".equals(param.getPortfolioId())) {
                return new ArrayList<>();
            }
        }

        builder.appendSql(" order by `rank` asc, last_updated_date desc ",false);
        builder.limit(Constants.TOTALSIZELIMIT);
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioListNew(Integer puid, PortfolioListParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid);
        List<Integer> shopidList = StringUtil.splitStr(param.getShopId(), ",").stream().map(item ->
                Integer.parseInt(item)).collect(Collectors.toList());
        builder.in("shop_id", shopidList.toArray());
        if (param.getIsHidden() != null) {
            builder.equalTo("is_hidden", param.getIsHidden());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        // 仅展示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus()) && "enabled".equals(param.getServingStatus())) {
            builder.equalTo("serving_status",AmazonAdPortfolio.servingStatusEnum.PORTFOLIO_STATUS_ENABLED.getCode());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue());
            } else {
                builder.like("name", param.getSearchValue());
            }
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            builder.inStrList("portfolio_id", param.getPortfolioIds().toArray(new String[]{}));
        }

        builder.appendSql(" order by `rank` asc, last_updated_date desc ",false);
        builder.limit(Constants.TOTALSIZELIMIT);
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioListNews(Integer puid, PortfolioPageParam param) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray());

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        // 仅展示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus()) && "enabled".equals(param.getServingStatus())) {
            builder.equalTo("serving_status",AmazonAdPortfolio.servingStatusEnum.PORTFOLIO_STATUS_ENABLED.getCode());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue());
            } else {
                builder.like("name", param.getSearchValue());
            }
        }

        builder.orderByDesc("last_updated_date", "id");
        builder.limit(Constants.TOTALSIZELIMIT);
        return listByCondition(puid, builder.build());
    }

    @Override
    public Page getPortfolioPageList(Integer puid, PortfolioPageParam param, Page page) {

        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_portfolio ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( SELECT id FROM `t_amazon_ad_portfolio` ");
        StringBuilder whereSql = new StringBuilder("where puid=? and shop_id=? ");
        List<Object> argsList = Lists.newArrayList(puid, param.getShopId());

        if (param.getIsHidden() != null) {
            whereSql.append(" and is_hidden =?");
            argsList.add(param.getIsHidden());
        }
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }
        if (StringUtils.isNotBlank(param.getServingStatus()) && "enabled".equals(param.getServingStatus())) {
            whereSql.append(" and serving_status = ?");
            argsList.add(AmazonAdPortfolio.servingStatusEnum.PORTFOLIO_STATUS_ENABLED.getCode());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                whereSql.append(" and name = ?");
                argsList.add(param.getSearchValue().trim());
            }else {
                whereSql.append(" and name like ?");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            }
        }
        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> portfolioIds = StringUtil.splitStr(param.getPortfolioId()).stream()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                whereSql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, argsList));
            }
            // 广告管理->选择'未分类'广告组合列表为空
            if ("-1".equals(param.getPortfolioId())) {
                return page;
            }
        }

        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        selectSql.append(" order by last_updated_date desc");

        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdPortfolio.class);
    }

    @Override
    public Page getPortfolioPageList(AdPortfolioStrategyParam param) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_portfolio ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( SELECT id FROM `t_amazon_ad_portfolio` ");
        StringBuilder whereSql = new StringBuilder("where puid=? and shop_id=? and is_amount_pricing = 0");
        List<Object> argsList = Lists.newArrayList(param.getPuid(), param.getShopId());

        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("serving_status", param.getServingStatusList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getPortfolioName())) {
            whereSql.append(" and name like ?");
            argsList.add("%" + param.getPortfolioName() + "%");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        selectSql.append(" order by last_updated_date desc");

        Object[] args = argsList.toArray();
        return this.getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdPortfolio.class);
    }

    @Override
    public void batchUpdateIsHidden(Integer puid, Integer uid, Integer shopId, boolean isHidden, List<String> portfolioIdList) {
        List<Object> argsList = new ArrayList<>();
        String sql = "update t_amazon_ad_portfolio set is_hidden=?, update_id=?, update_time=now(3) where puid= ? and shop_id = ? ";
        argsList.add(isHidden);
        argsList.add(uid);
        argsList.add(puid);
        argsList.add(shopId);
        sql += SqlStringUtil.dealInList("portfolio_id", portfolioIdList, argsList);
        getJdbcTemplate(puid).update(sql, argsList.toArray());
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioList(Integer puid, Integer shopId, List<String> portfolioIdList) {
        List<Object> args = Lists.newArrayList(puid, shopId);
        String sql = "select * from t_amazon_ad_portfolio where puid=? and shop_id=? ";
        if (CollectionUtils.isNotEmpty(portfolioIdList)) { //广告组合查询
            sql += SqlStringUtil.dealInList("portfolio_id", portfolioIdList, args);
        }
        return getJdbcTemplate(puid).query(sql, args.toArray(), getMapper());
    }

    @Override
    public List<AmazonAdPortfolio> listByShopId(Integer puid, List<Integer> shopIds, List<String> portfolioIdList) {
        List<Object> args = Lists.newArrayList(puid);
        String sql = "select * from t_amazon_ad_portfolio where puid=?";
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql += SqlStringUtil.dealInList("shop_id", shopIds, args);
        }

        if (CollectionUtils.isNotEmpty(portfolioIdList)) { //广告组合查询
            sql += SqlStringUtil.dealInList("portfolio_id", portfolioIdList, args);
        }
        return getJdbcTemplate(puid).query(sql, args.toArray(), getMapper());
    }

    @Override
    public AmazonAdPortfolio getByPortfolioId(Integer puid, Integer shopId, String portfolioId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("portfolio_id", portfolioId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdPortfolio> getByPortfolioIds(Integer puid, Integer shopId, List<String> portfolioId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .inStrList("portfolio_id", portfolioId.toArray(new String[]{}));
        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateNameById(Integer puid, Integer uid, Integer shopId, String name, Long id) {
        String sql = "update t_amazon_ad_portfolio set `name`=?, update_id=?, update_time=now(3) where puid= ? and shop_id = ? and id = ?";
        getJdbcTemplate(puid).update(sql, new Object[]{name, uid, puid, shopId, id});
    }

    @Override
    public void updatePortfolio(Integer puid, Integer shopId, Integer uid, Long id, PortfolioEditParam param) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_portfolio set `name`=?, update_id=?");
        StringBuilder whereSql = new StringBuilder(", update_time=now(3) where puid= ? and shop_id = ? and id = ? ");
        List<Object> argsList = Lists.newArrayList(param.getPortfolioName(), uid);

        if (StringUtils.isNotBlank(param.getPolicy())) {
            sql.append(", policy=?");
            argsList.add(StringUtils.capitalize(param.getPolicy()));
        }
        if (param.getAmount() != null) {
            sql.append(", amount=?");
            argsList.add(param.getAmount());
        }
        sql.append(", start_date=?");
        if (StringUtils.isNotBlank(param.getBudgetStartDate())) {
            argsList.add(DateUtil.stringToDate(param.getBudgetStartDate()));
        } else {
            argsList.add(null);
        }
        sql.append(", end_date=?");
        if (StringUtils.isNotBlank(param.getBudgetEndDate())) {
            argsList.add(DateUtil.stringToDate(param.getBudgetEndDate()));
        } else {
            argsList.add(null);
        }

        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(id);

        sql.append(whereSql);
        getJdbcTemplate(puid).update(String.valueOf(sql), argsList.toArray());

    }

    @Override
    public void updatePortfolioState(Integer puid, Integer shopId, Integer uid, Long id, String state) {
        String sql = "update t_amazon_ad_portfolio set `state`=?, update_id=?, update_time=now(3) where puid= ? and shop_id = ? and id = ?";
        getJdbcTemplate(puid).update(sql, new Object[]{state, uid, puid, shopId, id});
    }

    @Override
    public void updatePortfolioRank(Integer puid, Integer shopId, String portfolioId, Integer rank) {
        String sql = "update t_amazon_ad_portfolio set `rank`=? , update_time=now(3) where puid= ? and shop_id = ? and portfolio_id = ?";
        getJdbcTemplate(puid).update(sql, new Object[]{rank, puid, shopId, portfolioId});
    }

    @Override
    public void sortPortfolioRankByRankLastUpdateTime(Integer puid, Integer shopId) {
        String sql = "UPDATE t_amazon_ad_portfolio a, " +
                "    (SELECT IF(@i IS NULL , @i := 0 , 0 ) r, (@i := @i + 1) i, id FROM t_amazon_ad_portfolio WHERE puid = ? and shop_id = ?  order by is_hidden asc, rank asc,last_updated_date desc ) i, " +
                "    (SELECT @i := 0) ir " +
                "SET a.`rank` = i.i " +
                "WHERE a.id = i.id ";
        getJdbcTemplate(puid).update(sql, new Object[]{puid, shopId});
    }

    @Override
    public void sortPortfolioRankByRankUpdateTime(Integer puid, Integer shopId, Integer rank) {
        String sql = "UPDATE t_amazon_ad_portfolio a, " +
                "    (SELECT (@i := @i + 1) i, id FROM t_amazon_ad_portfolio WHERE puid = ? and shop_id = ?  order by is_hidden asc, rank asc,update_time desc ) i, " +
                "    (SELECT @i := 0) ir " +
                "SET a.`rank` = i.i " +
                "WHERE a.id = i.id ";
        getJdbcTemplate(puid).update(sql, new Object[]{puid, shopId});
    }

    @Override
    public void sortPortfolioRankByRankUpdateTimeAsc(Integer puid, Integer shopId, Integer rank) {
        String sql = "UPDATE t_amazon_ad_portfolio a, " +
                "    (SELECT (@i := @i + 1) i, id FROM t_amazon_ad_portfolio WHERE puid = ? and shop_id = ?  order by is_hidden asc, rank asc,update_time asc ) i, " +
                "    (SELECT @i := 0) ir " +
                "SET a.`rank` = i.i " +
                "WHERE a.id = i.id ";
        getJdbcTemplate(puid).update(sql, new Object[]{puid, shopId});
    }


    @Override
    public Integer sumRank(Integer puid, Integer shopId) {
        String sql = "select sum(`rank`) from t_amazon_ad_portfolio where puid= ? and shop_id = ?  ";
        return getJdbcTemplate(puid).queryForObject(sql,Integer.class, new Object[]{puid, shopId});
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioListAutoRule(Integer puid, Integer shopId, String marketplaceId, String portfolioName, Integer pageSize, Integer pageNo) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        builder.equalTo("marketplace_id",marketplaceId);
        builder.in("state",new Object[]{"enabled","paused"});
        if (StringUtils.isNotBlank(portfolioName)) {
            builder.like("name",portfolioName);
        }
        return listByCondition(puid,builder.build());
    }

    @Override
    public void updateStatePricing(Integer puid, Integer shopId, String portfolioId, Integer isPricing, Integer pricingState, int updateId) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_portfolio set is_amount_pricing=?,pricing_amount_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `portfolio_id`=? ");
        List<Object> args = Lists.newArrayList(isPricing, pricingState, updateId, puid, shopId, portfolioId);
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }

    @Override
    public List<String> queryPortfolioId(ControlledObjectParam param) {
        StringBuilder sql = new StringBuilder("select portfolio_id from " + getJdbcHelper().getTable() + " where puid=? and shop_id=?  ");
        List<Object> args = new ArrayList<>();
        args.add(param.getPuid());
        args.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            sql.append(" and name like ?");
            args.add("%" + param.getSearchValue() + "%");
        }
        return getJdbcTemplate(param.getPuid()).queryForList(sql.toString(), args.toArray(), String.class);
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioSumList(Integer puid, PortfolioPageParam param) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());
        if (param.getIsHidden() != null) {
            builder.equalTo("is_hidden", param.getIsHidden());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        // 仅展示正在投放
        if (StringUtils.isNotBlank(param.getServingStatus()) && "enabled".equals(param.getServingStatus())) {
            builder.equalTo("serving_status", AmazonAdPortfolio.servingStatusEnum.PORTFOLIO_STATUS_ENABLED.getCode());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                builder.equalTo("name", param.getSearchValue());
            } else {
                builder.like("name", param.getSearchValue());
            }
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIds())) {
            builder.inStrList("portfolio_id", param.getPortfolioIds().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> portfolioIds = StringUtil.splitStr(param.getPortfolioId()).stream()
                    .filter(item -> !item.equals("-1")).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                builder.inStrList("portfolio_id", portfolioIds.toArray(new String[]{}));
            }
            // 广告管理->选择'未分类'广告组合列表为空
            if ("-1".equals(param.getPortfolioId())) {
                return new ArrayList<>();
            }
        }

        builder.appendSql(" order by `rank` asc, last_updated_date desc ", false);
        builder.limit(Constants.TOTALSIZELIMIT);
        return listByCondition(puid, builder.build());
    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<StrategyPortfolioVo> listByCampaignIdList(Integer puid, List<Integer> shopIdList, List<String> campaignIds) {
        StringBuilder sql = new StringBuilder();
        List<Object> args = new ArrayList<>();
        sql.append("select distinct p.portfolio_id portfolioId, p.name portfolioName from t_amazon_ad_campaign_all c ");
        sql.append("left join t_amazon_ad_portfolio p on c.puid = p.puid and c.shop_id = p.shop_id and c.portfolio_id = p.portfolio_id ");
        sql.append("where c.puid= ? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("c.shop_id", shopIdList, args));
        sql.append(SqlStringUtil.dealInList("c.campaign_id", campaignIds, args));

        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(StrategyPortfolioVo.class), args.toArray());
    }

    @Override
    public List<String> getPortfolioIdList(ProcessTaskParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", param.getPuid());
        builder.like("name", param.getSearchValue());
        if (CollectionUtils.isNotEmpty(param.getItemIdList())) {
            builder.in("portfolio_id", param.getItemIdList().toArray());
        }
        return listDistinctFieldByCondition(param.getPuid(), "portfolio_id", builder.build(), String.class);
    }

    @Override
    public Page<AmazonAdPortfolio> getMultiShopPortfolioList(Integer puid, MultiShopPortfolioListParam param) {
        StringBuilder selectSql = new StringBuilder("select * from `t_amazon_ad_portfolio` ");
        StringBuilder countSql = new StringBuilder("select count(*) FROM `t_amazon_ad_portfolio` ");
        StringBuilder whereSql = new StringBuilder("where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        whereSql.append(SqlStringUtil.dealInList("shop_id", param.getShopIdList(), args));

        if (CollectionUtils.isNotEmpty(param.getMarketplaceId())) {
            whereSql.append(SqlStringUtil.dealInList("marketplace_id", param.getMarketplaceId(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            whereSql.append(SqlStringUtil.dealInList("portfolio_id", param.getPortfolioIdList(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            if ("exact".equals(param.getSearchType())) {
                whereSql.append(" and lower(name) = ? ");
                args.add(param.getSearchValue().trim().toLowerCase());
            }else {
                whereSql.append(" and lower(name) like ? ");
                args.add("%" + param.getSearchValue().trim().toLowerCase() + "%");
            }
        }

        List<String> searchValueList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            for (String value : param.getSearchValueList()) {
                searchValueList.add(value.toLowerCase());
            }
        }
        if (CollectionUtils.isNotEmpty(searchValueList)) {
            whereSql.append(SqlStringUtil.dealInList("lower(name)", searchValueList, args));
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        if("left".equals(param.getPosition())){
            selectSql.append(" order by shop_id asc ,`rank` asc, last_updated_date desc");
        }else{
            selectSql.append(" order by last_updated_date desc");
        }
        Object[] argsArr = args.toArray();
        return this.getPageResult(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), argsArr, selectSql.toString(), argsArr, AmazonAdPortfolio.class);
    }

    @Override
    public List<AmazonAdPortfolio> getByShopPortfolioPair(Integer puid, List<PortfolioListParam> paramList) {
        StringBuilder sql = new StringBuilder("select * from `t_amazon_ad_portfolio` where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);

        String multiIn = SqlStringUtil.dealMultiInList(Lists.newArrayList("shop_id", "portfolio_id"), paramList,
                args, Lists.newArrayList(PortfolioListParam::getShopId, PortfolioListParam::getPortfolioId));
        sql.append(multiIn);

        return getJdbcTemplate(puid).query(sql.toString(), args.toArray(), getMapper());
    }

    @Override
    public List<KeywordLibsPortfolioListVO> getAllPortfolioName(Integer puid, List<Integer> shopIds, List<String> portfolioIds) {
        StringBuilder sql = new StringBuilder("select portfolio_id, marketplace_id, shop_id, name, is_hidden  from `t_amazon_ad_portfolio` where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIds, args));
        }
        return getJdbcTemplate(puid).query(sql.toString(),args.toArray(), (res, i) -> KeywordLibsPortfolioListVO.builder()
                .marketplaceId(res.getString("marketplace_id"))
                .shopId(res.getInt("shop_id"))
                .portfolioId(res.getString("portfolio_id"))
                .portfolioName(res.getString("name"))
                .isHidden(res.getInt("is_hidden"))
                .build());
    }

    @Override
    public Page<KeywordLibsPortfolioListVO> getPortfolioName(Integer puid, KeywordLibsPageParam param) {
        StringBuilder sql = new StringBuilder("select portfolio_id, marketplace_id, shop_id, name  from `t_amazon_ad_portfolio` where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopIds(), args));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchPortfolioList())) {
            sql.append(SqlStringUtil.dealInList("portfolio_id", param.getSearchPortfolioList(), args));
        }
        if (StringUtils.isNotBlank(param.getSearchVal())) {
            sql.append(" and name like ? ");
            args.add("%" + param.getSearchVal() + "%");
        }
        String countSql = "select count(*) from ( " + sql + " ) c";

        return getPageByMapper(puid, param.getPageNo(), param.getPageSize(), countSql, args.toArray(), sql.toString(), args.toArray(), (res, i) -> KeywordLibsPortfolioListVO.builder()
                .marketplaceId(res.getString("marketplace_id"))
                .shopId(res.getInt("shop_id"))
                .portfolioName(res.getString("name"))
                .build());
    }

    @Override
    public List<KeywordLibsPortfolioListVO> getAllPortfolioName(Integer puid, List<Integer> shopIds) {
        StringBuilder sql = new StringBuilder("select portfolio_id, marketplace_id, shop_id, name  from `t_amazon_ad_portfolio` where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, args));
        }
        return getJdbcTemplate(puid).query(sql.toString(),args.toArray(), (res, i) -> KeywordLibsPortfolioListVO.builder()
                .marketplaceId(res.getString("marketplace_id"))
                .shopId(res.getInt("shop_id"))
                .portfolioId(res.getString("portfolio_id"))
                .portfolioName(res.getString("name"))
                .build());
    }
}
