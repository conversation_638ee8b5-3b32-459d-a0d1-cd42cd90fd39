package com.meiyunji.sponsored.service.multiPlatform.walmart.service;


import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartCampaignMultiplierInfoDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingMultiplier;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingMultipierDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.qo.AddOrUpdateMultiplierQo;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdMultiplierListCreateResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.WalmartAdBidMultiplierCreateVo;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;
import com.walmart.oms.advertiser.base.dto.PlacementBidMultipliersDTO;
import com.walmart.oms.advertiser.base.dto.PlatformBidMultipliersDTO;

import java.util.List;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingMultiplierService {

    /**
     * 删除
     */
    int delete(Integer puid, Long id);

    /**
     * 更新
     */
    int update(WalmartAdvertisingMultiplier multiplier);

    /**
     * 根据主键 id 查询
     */
    WalmartAdvertisingMultiplier getById(Integer puid, Long id);


    void addOrUpdateMultiplier(int puid, WalmartAdvertisingMultipierDTO dto) throws ServiceException;

    Result<WalmartAdMultiplierListCreateResp> addOrUpdateMultiplier(int puid, AddOrUpdateMultiplierQo dto);
    Result<WalmartAdMultiplierListCreateResp> addOrUpdateMultiplier(int puid, Integer shopId, String marketplaceCode, String adType,
                                         String campaignId,List<WalmartAdBidMultiplierCreateVo> voList, Integer operatorUid);

    List<WalmartAdvertisingMultiplier> getByCampaignId(Integer puid, Long shopId, Long campaignId, Integer isPlatform);

    int syncMultiplierByCampaigns(Integer puid, List<Integer> shopIdList,
                                  WalmartAdvertiserClient advertiserClient, List<WalmartAdvertisingCampaign> campaigns) throws ServiceException;

    int deleteByCampaignId(Integer puid, Long shopId, Long campaignId);
    void add(WalmartAdvertisingMultiplier multiplier);

    int deleteByCampaignId(Integer puid, Integer shopId, String campaignId);

    List<WalmartAdvertisingMultiplier> getByCampaignId(Integer puid, Integer shopId, String campaignId, Integer isPlatform);

    WalmartAdMultiplierListCreateResp createPlacementMultiplier(Integer puid, Integer shopId,
                                                                WalmartCampaignMultiplierInfoDTO campaignMultiInfo, List<PlacementBidMultipliersDTO> bidMultipliersDTOS) throws ServiceException;


    WalmartAdMultiplierListCreateResp createPlatformMultiplier(Integer puid, Integer shopId,
                                                               WalmartCampaignMultiplierInfoDTO campaignMultiInfo, List<PlatformBidMultipliersDTO> bidMultipliersDTOS) throws ServiceException;
}
