package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.po.OdsCpcTargetingReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;

import java.util.List;

/**
 * 商品投放报告(OdsCpcTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:22
 */
public interface IOdsCpcTargetingReportDao extends IDorisBaseDao<OdsCpcTargetingReport> {
    /**
     * 商品投放列表页
     */
    Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param);

    int getTargetAllCount(Integer puid, TargetingPageParam param);
    /**
     * 根据投放id查询报告数据
     */
    List<TargetPageVo> getReportListByTargetIds(Integer puid, Integer shopId, List<String> keywordIds, String startStr, String endStr);
    /**
     * 商品投放列表页占比数据
     */
    AdMetricDto getTargetPageSumMetricData(Integer puid, TargetingPageParam param);
    /**
     * 商品投放汇总查询所有keywordId
     */
    List<String> getTargetIdListByPage(Integer puid, String startStr, String endStr, TargetingPageParam param);
    /**
     * 商品投放汇总根据keywordId查询按天维度数据
     */
    List<AdHomePerformancedto> getReportAggregateByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupByDate);

    AdHomePerformancedto getReportAggregateCompareData(Integer puid, TargetingPageParam param, String startStr, String endStr);
    List<DashboardAdTargetingMatrixTopDto> getTargetingTopList(Integer puid, List<String> marketplaceIdList,
                                                               List<Integer> shopIdList, String currency,
                                                               String startDate, String endDate,
                                                               DashboardDataFieldEnum dataField, List<String> targetingIdList,
                                                               Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday,
                                                               List<String> portfolioIds, List<String> campaignIds, Boolean noZero);

    List<DashboardAdTargetingMatrixTopDto> getTargetingInfoList(Integer puid, List<String> marketplaceIdList,
                                                               List<Integer> shopIdList, String currency,
                                                               String startDate, String endDate,
                                                               DashboardDataFieldEnum dataField, List<String> targetingIdList,
                                                                DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday,
                                                                List<String> portfolioIds, List<String> campaignIds, Boolean noZero);

    /**
     * 根据投放Id查询对应的报告数据
     * @param puid
     * @param param
     * @param spTargetIds
     * @return
     */
    List<AsinLibsDetailVo> getSpAsinReportDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> spTargetIds);

    List<AdAsinOrderBo> getOrderFieldByAsins(Integer puid, List<String> shopIds, PageListAsinsRequest param, List<String> asinList);

    List<AsinLibsDetailVo> getAsinReportByAsins(Integer puid, PageListAsinsRequest param,
                                                String startDate, String endDate,
                                                List<String> asinList, String currency);

    List<OdsCpcTargetingReport> getSumReportByTargetIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startStr, String endStr,
                                                                   List<String> targetIds, boolean changeRate, String currency);

}

