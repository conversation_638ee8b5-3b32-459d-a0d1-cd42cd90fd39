package com.meiyunji.sponsored.service.autoRule.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@DbTable(value = "t_ad_auto_rule_template_report_complete")
@Data
public class AdvertiseAutoRuleTemplateReportComplete implements Serializable {

    public AdvertiseAutoRuleTemplateReportComplete() {
    }

    public AdvertiseAutoRuleTemplateReportComplete(Integer puid, Integer shopId, Integer reportComplete) {
        this.puid = puid;
        this.shopId = shopId;
        this.reportComplete = reportComplete;
    }

    @DbColumn(value = "id", key = true, autoIncrement = true)
    private Long id;

    @DbColumn(value = "puid")
    private Integer puid;

    @DbColumn(value = "shop_id")
    private Integer shopId;

    @DbColumn(value = "report_complete")
    private Integer reportComplete;

    @DbColumn(value = "create_time")
    private Date createTime;

    @DbColumn(value = "update_time")
    private Date updateTime;
}
