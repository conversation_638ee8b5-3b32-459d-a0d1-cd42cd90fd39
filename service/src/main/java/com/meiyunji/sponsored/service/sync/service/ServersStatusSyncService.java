package com.meiyunji.sponsored.service.sync.service;

import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.MD5Util;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.kafka.SyncServerStatusKafkaProducer;
import com.meiyunji.sponsored.service.stream.enums.AmazonStreamTaskTypeEnum;
import com.meiyunji.sponsored.service.sync.dto.SyncMessageDto;
import com.meiyunji.sponsored.service.sync.enums.SyncStatusProcessEnum;
import com.meiyunji.sponsored.service.sync.strgtegy.AbstractSyncServerStatusProcessStrategy;
import com.meiyunji.sponsored.service.syncTask.management.strgtegy.enume.AdProductEnum;
import com.meiyunji.sponsored.service.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2024/7/4 13:53
 */
@Slf4j
@Service
public class ServersStatusSyncService {
    @Resource
    private SyncServerStatusKafkaProducer syncServerStatusKafkaProducer;

    @Resource
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Resource
    private ISlaveScVcShopAuthDao shopAuthDao;
    @Resource
    private RedisService redisService;

    private final static int maxPageSize = 20;

    public void sendSyncMessage(Integer key, Object object) {
        syncServerStatusKafkaProducer.send(JSONUtil.objectToJson(object));
    }


    public void execute(SyncMessageDto syncMessageDto) {
        long start = System.currentTimeMillis();
        Map<AdProductEnum, List<SyncMessageDto.Item>> adProductEnumListMap = syncMessageDto.getItems().stream().collect(Collectors.groupingBy(SyncMessageDto.Item::getType));
        AmazonStreamTaskTypeEnum syncTypeEnum = syncMessageDto.getSyncTypeEnum();
        ShopAuth shopAuth = shopAuthDao.getValidAdShopById(syncMessageDto.getPuid(), syncMessageDto.getShopId());
        if (shopAuth == null) {
            log.info("sync manage server status is not fund shop or not auth puid:{}，shopId:{}", syncMessageDto.getPuid(), syncMessageDto.getShopId());
            return;
        }
        for (Map.Entry<AdProductEnum, List<SyncMessageDto.Item>> entry : adProductEnumListMap.entrySet()) {
            SyncStatusProcessEnum byType = SyncStatusProcessEnum.getByType(syncTypeEnum, entry.getKey());
            AbstractSyncServerStatusProcessStrategy bean = SpringContextUtil.getBean(byType.getProcessorClass());
            List<String> ids = entry.getValue().stream().map(SyncMessageDto.Item::getItemId).collect(Collectors.toList());
            bean.executeTask(shopAuth, entry.getKey(), syncTypeEnum, ids);
        }
        log.info("sync manage server status execute time :{}, puid:{}, shopId:{},type:{},", System.currentTimeMillis() - start, syncMessageDto.getPuid(), syncMessageDto.getShopId(), syncMessageDto.getSyncTypeEnum());
    }


    public void sendCampaign(Integer puid, Integer shopId, AllCampaignDataResponse.CampaignHomeVo homeVo) {
        if (!dynamicRefreshConfiguration.isSyncManage()) {
            log.info("sync ad manage switch off");
            return;
        }
        long start = System.currentTimeMillis();
        try {
            if (homeVo != null && homeVo.hasPage() && homeVo.getPage().hasPageNo() && homeVo.getPage().getPageNo().getValue() > maxPageSize) {
                log.info("max page size {} don't send", maxPageSize);
                return;
            }
            ThreadPoolUtil.getSendSyncManageWorkerPool().execute(() -> {
                try {
                    if (homeVo != null && homeVo.hasPage() && homeVo.getPage().getRowsCount() > 0) {
                        Boolean duplicate = isDuplicate(shopId, homeVo.getPage().getRowsList().stream().map(AllCampaignDataResponse.CampaignHomeVo.Page.CampaignPageVo::getCampaignId).collect(Collectors.toList()));
                        if (duplicate) {
                            return;
                        }
                        sendSyncMessage(puid, convertMessage(puid, shopId, homeVo.getPage().getRowsList().stream().map(e -> convertItem(e.getCampaignId(), e.getType())).collect(Collectors.toList()), AmazonStreamTaskTypeEnum.CAMPAIGN));
                    }
                } catch (Exception e) {
                    log.error("send sync message error, puid:{}, shopId:{}", puid, shopId, e);
                }

            });
        } catch (Exception e) {
            log.error("campaign manage send task error ", e);
        }
        log.info("campaign manage send task time consuming :{}", System.currentTimeMillis() - start);
    }


    public void sendGroup(Integer puid, Integer shopId, AllGroupDataResponse.GroupHomeVo homeVo) {
        if (!dynamicRefreshConfiguration.isSyncManage()) {
            log.info("sync ad manage switch off");
            return;
        }
        long start = System.currentTimeMillis();
        try {
            if (homeVo != null && homeVo.hasPage() && homeVo.getPage().hasPageNo() && homeVo.getPage().getPageNo().getValue() > maxPageSize) {
                log.info("max page size {} don't send", maxPageSize);
                return;
            }
            ThreadPoolUtil.getSendSyncManageWorkerPool().execute(() -> {
                try {
                    if (homeVo != null && homeVo.hasPage() && homeVo.getPage().getRowsCount() > 0) {
                        Boolean duplicate = isDuplicate(shopId, homeVo.getPage().getRowsList().stream().map(AllGroupDataResponse.GroupHomeVo.Page.GroupPageVo::getAdGroupId).collect(Collectors.toList()));
                        if (duplicate) {
                            return;
                        }
                        sendSyncMessage(puid, convertMessage(puid, shopId, homeVo.getPage().getRowsList().stream().map(e -> convertItem(e.getAdGroupId(), e.getType())).collect(Collectors.toList()), AmazonStreamTaskTypeEnum.GROUP));
                    }
                } catch (Exception e) {
                    log.error("send sync message error, puid:{}, shopId:{}", puid, shopId, e);
                }

            });
        } catch (Exception e) {
            log.error("group manage send task error ", e);
        }
        log.info("group manage send task time consuming :{}", System.currentTimeMillis() - start);

    }


    public void sendTarget(Integer puid, Integer shopId, AllTargetDataResponse.AdTargetingHomeRpcVo homeVo) {
        if (!dynamicRefreshConfiguration.isSyncManage()) {
            log.info("sync ad manage switch off");
            return;
        }
        long start = System.currentTimeMillis();
        try {
            if (homeVo != null && homeVo.hasPage() && homeVo.getPage().hasPageNo() && homeVo.getPage().getPageNo().getValue() > maxPageSize) {
                log.info("max page size {} don't send", maxPageSize);
                return;
            }
            ThreadPoolUtil.getSendSyncManageWorkerPool().execute(() -> {
                try {
                    if (homeVo != null && homeVo.hasPage() && homeVo.getPage().getRowsCount() > 0) {
                        Boolean duplicate = isDuplicate(shopId, homeVo.getPage().getRowsList().stream().map(AllTargetDataResponse.AdTargetingHomeRpcVo.Page.TargetingPageVo::getTargetId).collect(Collectors.toList()));
                        if (duplicate) {
                            return;
                        }
                        sendSyncMessage(puid, convertMessage(puid, shopId, homeVo.getPage().getRowsList().stream().map(e -> convertItem(e.getTargetId(), e.getAtype())).collect(Collectors.toList()), AmazonStreamTaskTypeEnum.TARGET));
                    }
                } catch (Exception e) {
                    log.error("send sync message error, puid:{}, shopId:{}", puid, shopId, e);
                }

            });
        } catch (Exception e) {
            log.error("target manage send task error ", e);
        }
        log.info("target manage send task time consuming :{}", System.currentTimeMillis() - start);

    }

    public void sendKeyword(Integer puid, Integer shopId, AllKeyWordDataResponse.AdkeywordHomeRpcVo homeVo) {
        if (!dynamicRefreshConfiguration.isSyncManage()) {
            log.info("sync ad manage switch off");
            return;
        }
        long start = System.currentTimeMillis();
        try {
            if (homeVo != null && homeVo.hasPage() && homeVo.getPage().hasPageNo() && homeVo.getPage().getPageNo().getValue() > maxPageSize) {
                log.info("max page size {} don't send", maxPageSize);
                return;
            }
            ThreadPoolUtil.getSendSyncManageWorkerPool().execute(() -> {
                try {
                    if (homeVo != null && homeVo.hasPage() && homeVo.getPage().getRowsCount() > 0) {
                        Boolean duplicate = isDuplicate(shopId, homeVo.getPage().getRowsList().stream().map(AllKeyWordDataResponse.AdkeywordHomeRpcVo.Page.KeywordsPageRpcVo::getKeywordId).collect(Collectors.toList()));
                        if (duplicate) {
                            return;
                        }
                        sendSyncMessage(puid, convertMessage(puid, shopId, homeVo.getPage().getRowsList().stream().map(e -> convertItem(e.getKeywordId(), e.getType())).collect(Collectors.toList()), AmazonStreamTaskTypeEnum.KEYWORD));
                    }
                } catch (Exception e) {
                    log.error("send sync message error, puid:{}, shopId:{}", puid, shopId, e);
                }

            });
        } catch (Exception e) {
            log.error("keyword manage send task error ", e);
        }
        log.info("keyword manage send task time consuming :{}", System.currentTimeMillis() - start);

    }

    /**
     * 同步当前页基础数据，20分钟内没同步过的数据进行同步；
     *
     * @param puid   puid
     * @param shopId shopId
     * @param homeVo homeVo
     */
    public void sendProduct(Integer puid, Integer shopId, AllProductDataResponse.AdProductHomeVo homeVo) {
        if (!dynamicRefreshConfiguration.isSyncManage()) {
            log.info("sync ad manage switch off");
            return;
        }
        long start = System.currentTimeMillis();
        try {
            if (homeVo != null && homeVo.hasPage() && homeVo.getPage().hasPageNo() && homeVo.getPage().getPageNo().getValue() > maxPageSize) {
                log.info("max page size {} don't send", maxPageSize);
                return;
            }
            ThreadPoolUtil.getSendSyncManageWorkerPool().execute(() -> {
                try {
                    if (homeVo != null && homeVo.hasPage() && homeVo.getPage().getRowsCount() > 0) {
                        Boolean duplicate = isDuplicate(shopId, homeVo.getPage().getRowsList().stream().map(AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo::getAdId).collect(Collectors.toList()));
                        if (duplicate) {
                            return;
                        }
                        sendSyncMessage(puid, convertMessage(puid, shopId, homeVo.getPage().getRowsList().stream().map(e -> convertItem(e.getAdId(), e.getType())).collect(Collectors.toList()), AmazonStreamTaskTypeEnum.AD));
                    }
                } catch (Exception e) {
                    log.error("send sync message error, puid:{}, shopId:{}", puid, shopId, e);
                }

            });
        } catch (Exception e) {
            log.error("product manage send task error ", e);
        }
        log.info("product manage send task time consuming :{}", System.currentTimeMillis() - start);

    }

    private SyncMessageDto convertMessage(Integer puid, Integer shopId, List<SyncMessageDto.Item> items, AmazonStreamTaskTypeEnum typeEnum) {
        SyncMessageDto dto = convertDto(puid, shopId, items);
        dto.setSyncTypeEnum(typeEnum);
        return dto;
    }

    private SyncMessageDto convertDto(Integer puid, Integer shopId, List<SyncMessageDto.Item> items) {
        SyncMessageDto dto = new SyncMessageDto();
        dto.setPuid(puid);
        dto.setShopId(shopId);
        dto.setItems(items);
        return dto;
    }

    private SyncMessageDto.Item convertItem(String id, String type) {
        SyncMessageDto.Item item = new SyncMessageDto.Item();
        AdProductEnum enumByAdType = AdProductEnum.getEnumByAdType(type);
        item.setType(enumByAdType);
        item.setItemId(id);
        return item;
    }


    private Boolean isDuplicate(Integer shopId, List<String> ids) {
        String idsStr = ids.stream().sorted(String::compareTo).collect(Collectors.joining("|"));
        String md5 = MD5Util.getMD5(shopId + "|" + idsStr);
        String redisKey = "sf-ad-sync-key:" + md5;
        Object o = redisService.get(redisKey);
        if (o != null) {
            log.info("send sync ad duplicate shopId :{}", shopId);
            return true;
        }
        redisService.set(redisKey, "s", 20, TimeUnit.MINUTES);
        return false;
    }


}
