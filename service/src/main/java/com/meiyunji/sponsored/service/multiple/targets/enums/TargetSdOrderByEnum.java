package com.meiyunji.sponsored.service.multiple.targets.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

import java.util.HashSet;
import java.util.Set;

/**
 * 活动列表页-排序高级刷新字段枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSdOrderByEnum {
    CREATE_TIME("createTime", " t.creation_date ", new HashSet<>()),
    BID("bid", " IFNULL(bid, g.default_bid) ", new HashSet<>()),
    SHOP_NAME("shopName", " CONVERT(s.`name` USING gbk) COLLATE gbk_chinese_ci ", new HashSet<>()),
    AD_COST("adCost", " ifnull(costDoris,0) ", CollectionUtil.newHashSet("costDoris")),
    AD_COST_PERCENTAGE("adCostPercentage", " ifnull(costDoris,0) ", CollectionUtil.newHashSet("costDoris")),
    TOTAL_SALES("adSale", " ifnull(totalSalesDoris,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    TOTAL_SALES_PERCENTAGE("adSalePercentage", " ifnull(totalSalesDoris,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES("adSales", " ifnull(adSalesDoris,0) ", CollectionUtil.newHashSet("adSalesDoris")),
    IMPRESSIONS("impressions", " ifnull(impressionsDoris,0) ", CollectionUtil.newHashSet("impressionsDoris")),
    CLICKS("clicks", " ifnull(clicksDoris,0) ", CollectionUtil.newHashSet("clicksDoris")),
    ORDER_NUM("adOrderNum", " ifnull(orderNumDoris,0) ", CollectionUtil.newHashSet("orderNumDoris")),
    ORDER_NUM_PERCENTAGE("adOrderNumPercentage", " ifnull(orderNumDoris,0) ", CollectionUtil.newHashSet("orderNumDoris")),
    AD_ORDER_NUM("adSaleNum", " ifnull(adOrderNumDoris,0) ", CollectionUtil.newHashSet("adOrderNumDoris")),
    SALE_NUM("orderNum", " ifnull(saleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris")),
    SALE_NUM_PERCENTAGE("orderNumPercentage", " ifnull(saleNumDoris,0) ", CollectionUtil.newHashSet("saleNumDoris")),
    VIEW_IMPRESSIONS("viewImpressions", " ifnull(viewImpressionsDoris,0) ", CollectionUtil.newHashSet("viewImpressionsDoris")),
    SALES_NEW_TO_BRAND("salesNewToBrandFTD", " ifnull(salesNewToBrand14dDoris,0) ", CollectionUtil.newHashSet("salesNewToBrand14dDoris")),
    ORDERS_NEW_TO_BRAND("ordersNewToBrandFTD", " ifnull(ordersNewToBrand14dDoris,0) ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris")),
    ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD("ordersNewToBrandPercentageFTD", " ifnull(ordersNewToBrand14dDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "clicksDoris")),
    NEW_TO_BRAND_DETAIL_PAGE_VIEWS("newToBrandDetailPageViews", " ifnull(newToBrandDetailPageViewsDoris,0) ", CollectionUtil.newHashSet("newToBrandDetailPageViewsDoris")),
    ADD_TO_CART("addToCart", " ifnull(addToCartDoris,0) ", CollectionUtil.newHashSet("addToCartDoris")),
    VIDEO_FIRST_QUARTILE_VIEWS("videoFirstQuartileViews", " ifnull(videoFirstQuartileViewsDoris,0) ", CollectionUtil.newHashSet("videoFirstQuartileViewsDoris")),
    VIDEO_MIDPOINT_VIEWS("videoMidpointViews", " ifnull(videoMidpointViewsDoris,0) ", CollectionUtil.newHashSet("videoMidpointViewsDoris")),
    VIDEO_THIRD_QUARTILE_VIEWS("videoThirdQuartileViews", " ifnull(videoThirdQuartileViewsDoris,0) ", CollectionUtil.newHashSet("videoThirdQuartileViewsDoris")),
    VIDEO_COMPLETE_VIEWS("videoCompleteViews", " ifnull(videoCompleteViewsDoris,0) ", CollectionUtil.newHashSet("videoCompleteViewsDoris")),
    VIDEO_UNMUTES("videoUnmutes", " ifnull(videoUnmutesDoris,0) ", CollectionUtil.newHashSet("videoUnmutesDoris")),
    BRANDED_SEARCHES("brandedSearches", " ifnull(brandedSearches14dDoris,0) ", CollectionUtil.newHashSet("brandedSearches14dDoris")),
    DETAIL_PAGE_VIEW("detailPageViews", " ifnull(detailPageView14dDoris,0)", CollectionUtil.newHashSet("detailPageView14dDoris")),
    CPA("cpa", " ifnull(costDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    AD_COST_PER_CLICK("adCostPerClick", " ifnull(costDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    VCPM("vcpm", " ifnull(costDoris/viewImpressionsDoris ,0) ", CollectionUtil.newHashSet("costDoris", "viewImpressionsDoris")),
    CTR("ctr", " ifnull(clicksDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    CVR("cvr", " ifnull(orderNumDoris/clicksDoris ,0) ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    ACOS("acos", " ifnull(costDoris/totalSalesDoris ,0) ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ROAS("roas", " ifnull(totalSalesDoris/costDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    ACOTS("acots", " ifnull(costDoris/shopSalesDoris ,0) ", CollectionUtil.newHashSet("costDoris")),
    ASOTS("asots", " ifnull(totalSalesDoris/shopSalesDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris")),
    ADVERTISING_UNIT_PRICE("advertisingUnitPrice", " ifnull(totalSalesDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    ORDER_RATE_NEW_TO_BRAND_FTD("orderRateNewToBrandFTD", " ifnull(ordersNewToBrand14dDoris/orderNumDoris ,0) ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "orderNumDoris")),
    SALES_RATE_NEW_TO_BRAND_FTD("salesRateNewToBrandFTD", " ifnull(salesNewToBrand14dDoris/totalSalesDoris ,0) ", CollectionUtil.newHashSet("salesNewToBrand14dDoris", "totalSalesDoris")),
    ADD_TO_CART_RATE("addToCartRate", " ifnull(addToCartDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("addToCartDoris", "impressionsDoris")),
    ECP_ADD_TO_CART("eCPAddToCart", " ifnull(costDoris/addToCartDoris ,0) ", CollectionUtil.newHashSet("costDoris", "addToCartDoris")),
    VIEW_ABILITY_RATE("viewabilityRate", " ifnull(viewImpressionsDoris/impressionsDoris ,0) ", CollectionUtil.newHashSet("viewImpressionsDoris", "impressionsDoris")),
    VIEW_CLICK_THROUGH_RATE("viewClickThroughRate", " ifnull(clicksDoris/viewImpressionsDoris ,0) ", CollectionUtil.newHashSet("clicksDoris", "viewImpressionsDoris")),
    ;

    // 排序字段
    private final String code;
    // 字段
    private final String orderBy;
    // 字段
    private final Set<String> columnList;

    TargetSdOrderByEnum(String code, String orderBy, Set<String> columnList) {
        this.code = code;
        this.orderBy = orderBy;
        this.columnList = columnList;
    }

    /**
     * 根据code获取枚举
     */
    public static TargetSdOrderByEnum getEnumByCode(String code) {
        for (TargetSdOrderByEnum orderByEnum : TargetSdOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum;
            }
        }
        return null;
    }

    /**
     * 根据code获取统计字段集合
     */
    public static Set<String> getSetByCode(String code) {
        for (TargetSdOrderByEnum orderByEnum : TargetSdOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum.getColumnList();
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据code获取排序
     */
    public static String getOrderByByCode(String code , String orderType) {
        // vcpm 特殊处理
        if("vcpm".equals(code)){
            if (StringUtils.isBlank(orderType) || "desc".equalsIgnoreCase(orderType)) {
                return " if(c.cost_type = 'vcpm',ifnull(costDoris/viewImpressionsDoris ,0),-2147483647) ";
            }else{
                return " if(c.cost_type = 'vcpm',ifnull(costDoris/viewImpressionsDoris ,0),2147483647) ";
            }
        }
        for (TargetSdOrderByEnum orderByEnum : TargetSdOrderByEnum.values()) {
            if (orderByEnum.getCode().equals(code)) {
                return orderByEnum.getOrderBy();
            }
        }
        return TargetSdOrderByEnum.CREATE_TIME.getOrderBy();
    }
}
