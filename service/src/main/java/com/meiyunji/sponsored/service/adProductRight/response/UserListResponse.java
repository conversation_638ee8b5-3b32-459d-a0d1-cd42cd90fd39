package com.meiyunji.sponsored.service.adProductRight.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.ALWAYS)
public class UserListResponse implements Serializable {
    private static final long serialVersionUID = 123456L;

    private List<UserInfo> data;

    @Data
    public static class UserInfo {
        private Integer uid;
        private String nickname;
    }
}
