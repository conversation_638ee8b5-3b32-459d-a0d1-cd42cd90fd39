package com.meiyunji.sponsored.service.multiPlatform.shop.dao.impl;

import com.google.common.collect.Lists;

import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.multiPlatform.shop.dao.IMultiPlatformShopAuthDao;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.MultiPlatformTypeEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.enums.WalmartAdAuthEnum;
import com.meiyunji.sponsored.service.multiPlatform.shop.po.MultiPlatformShopAuth;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.*;

@Repository
public class MultiPlatformShopAuthDaoImpl extends BaseDaoImpl<MultiPlatformShopAuth> implements IMultiPlatformShopAuthDao {



    @Override
    public List<MultiPlatformShopAuth> listByPuidAndPlatform(Integer puid, String platformType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("platform_type", platformType);
        return listByCondition(builder.build());
    }

    @Override
    public MultiPlatformShopAuth listByIdAndPlatform(Integer puid, Integer id, String platformType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("id", id);
        builder.equalTo("platform_type", platformType);
        return getByCondition(builder.build());
    }

    @Override
    public List<Integer> listAdAuthIdByPuidAndPlatform(Integer puid, List<Integer> idList, String platformType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("platform_type", platformType);
        builder.equalTo("ad_status", WalmartAdAuthEnum.AD_AUTH.getStatus());
        builder.in("id", idList.toArray());
        return listDistinctFieldByCondition("id", builder.build(), Integer.class);
    }

    @Override
    public MultiPlatformShopAuth getAdShopAuthByPlatform(Integer puid, Integer shopId, String platformType, List<Integer> adStatusList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("id", shopId);
        builder.equalTo("platform_type", platformType);
        builder.in("ad_status", adStatusList.toArray());
        return getByCondition(builder.build());
    }

    @Override
    public List<Integer> listAdAuthIdByPuidAndPlatform(Integer puid, List<Integer> shopIdList, String platformType, List<Integer> adStatusList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            builder.in("id", shopIdList.toArray());
        }
        builder.equalTo("platform_type", platformType);
        builder.in("ad_status", adStatusList.toArray());
        return listDistinctFieldByCondition("id", builder.build(), Integer.class);
    }

    @Override
    public List<MultiPlatformShopAuth> listByPuidAndPlatform(Integer puid, List<String> platformTypes) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.in("platform_type", platformTypes.toArray());
        return listByCondition(builder.build());
    }

    @Override
    public List<Integer> listPuidByPlatform(String platformType) {
        return listDistinctFieldByCondition("puid", new ConditionBuilder.Builder()
                .equalTo("platform_type", platformType)
                .build(), Integer.class);
    }

    @Override
    public List<MultiPlatformShopAuth> listByAuthExpiredTime(Integer puid, String platformType, LocalDateTime dateTime) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("platform_type", platformType);
        builder.lessThanOrEqualTo("auth_expired_time", dateTime);
        return listByCondition(builder.build());
    }

    @Override
    public MultiPlatformShopAuth getPuidAndName(Integer puid, String name) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("name", name);
        return getByCondition(builder.build());
    }

    @Override
    public int updateShopAuth(MultiPlatformShopAuth shopAuth) {
        StringBuilder sqlBuilder = new StringBuilder("update t_multi_platform_shop_auth set  update_time = NOW(3), ");

        StringBuilder setSql = new StringBuilder();

        List<Object> args = new ArrayList<>();
        if (StringUtils.isNotBlank(shopAuth.getName())) {
            setSql.append("name = ? ");
            args.add(shopAuth.getName());
        }
        if (shopAuth.getAuthTime() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("auth_time = ? ");
            args.add(shopAuth.getAuthTime());
        }
        if (shopAuth.getAuthExpiredTime() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("auth_expired_time = ? ");
            args.add(shopAuth.getAuthExpiredTime());
        }
        if (shopAuth.getStatus() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("status = ? ");
            args.add(shopAuth.getStatus());
        }
        if (shopAuth.getPlatformShopType() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("platform_shop_type = ? ");
            args.add(shopAuth.getPlatformShopType());
        }
        if (shopAuth.getPlatformShopId() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("platform_shop_id = ? ");
            args.add(shopAuth.getPlatformShopId());
        }
        if (shopAuth.getTemuEuOrderTokenStatus() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("temu_eu_order_token_status = ? ");
            args.add(shopAuth.getTemuEuOrderTokenStatus());
        }
        if (shopAuth.getTemuUsOrderTokenStatus() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("temu_us_order_token_status = ? ");
            args.add(shopAuth.getTemuUsOrderTokenStatus());
        }
        if (shopAuth.getTemuGlobalUsOrderTokenStatus() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("temu_global_us_order_token_status = ? ");
            args.add(shopAuth.getTemuGlobalUsOrderTokenStatus());
        }
        if (shopAuth.getUpdateId() != null) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("update_id = ? ");
            args.add(shopAuth.getUpdateId());
        }
        if (StringUtils.isNotBlank(shopAuth.getCurrency())) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("currency = ? ");
            args.add(shopAuth.getCurrency());
        }
        if (StringUtils.isNotBlank(shopAuth.getPlatformMerchantId())) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("platform_merchant_id = ? ");
            args.add(shopAuth.getPlatformMerchantId());
        }
        if (StringUtils.isNotBlank(shopAuth.getPlatformSellerId())) {
            if (!setSql.toString().isEmpty()) {
                setSql.append(",");
            }
            setSql.append("platform_seller_id = ? ");
            args.add(shopAuth.getPlatformSellerId());
        }
        sqlBuilder.append(setSql);
        sqlBuilder.append(" where puid = ? and id = ? ");
        args.add(shopAuth.getPuid());
        args.add(shopAuth.getId());
        return getJdbcTemplate().update(sqlBuilder.toString(), args.toArray());
    }


    @Override
    public int copyToDeleteShop(int shopId) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO t_multi_platform_shop_auth_delete (id, puid, name, platform_type, temu_type, platform_shop_type, shop_type, platform_shop_id, marketplace_code, ioss_number, temu_eu_order_token_status, temu_us_order_token_status, status, auth_time, auth_expired_time, create_id, update_id, create_time, update_time) ");
        sql.append("select id, puid, name, platform_type, temu_type, platform_shop_type, shop_type, platform_shop_id, marketplace_code, ioss_number, temu_eu_order_token_status, temu_us_order_token_status, status, auth_time, auth_expired_time, create_id, update_id, create_time, update_time from t_multi_platform_shop_auth where id= ?");
        return getJdbcTemplate().update(sql.toString(), shopId);
    }

    @Override
    public List<Integer> getAllShopId(Integer puid) {
        StringBuilder sql = new StringBuilder("select id from t_multi_platform_shop_auth where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public Integer countExpiredShops(int puid) {
        StringBuilder sql = new StringBuilder("select count(1) from t_multi_platform_shop_auth where puid = ? and status = 1 ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        return getJdbcTemplate().queryForObject(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public Integer countExpiringShops(int puid) {
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(7);
        StringBuilder sql = new StringBuilder("select count(1) from t_multi_platform_shop_auth where puid = ? and platform_type = 'TEMU' and auth_expired_time <  ? and status = 0 ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        args.add(localDateTime);
        return getJdbcTemplate().queryForObject(sql.toString(), Integer.class, args.toArray());
    }


    @Override
    public List<MultiPlatformShopAuth> listByPuidAndShopIds(Integer puid, List<Integer> shopIdList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.in("id", shopIdList.toArray());
        return listByCondition(builder.build());
    }

    @Override
    public List<MultiPlatformShopAuth> batchSelectValidaList(Integer minId, List<Long> puidList, String code) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (CollectionUtils.isNotEmpty(puidList)) {
            builder.in("puid", puidList.toArray());
        }
        builder.equalTo("platform_type", code);

        if (Objects.nonNull(minId)) {
            builder.greaterThan("id", minId);
        }
        // 0 默认 1 授权失效
        builder.equalTo("status", 0);
        if (MultiPlatformTypeEnum.TEMU.getCode().equals(code)) {
            builder.greaterThanOrEqualTo("auth_expired_time", new Date());
        }
        builder.orderBy("id");
        builder.limit(200);
        return listByCondition(builder.build());
    }

    @Override
    public List<MultiPlatformShopAuth> listNameByIds(Integer puid, List<Integer> authShopIdList) {
        StringBuilder sql = new StringBuilder("select id, name, platform_shop_type from t_multi_platform_shop_auth where puid = ? ");
        List<Object> args = new ArrayList<>();
        args.add(puid);
        if (CollectionUtils.isNotEmpty(authShopIdList)) {
            sql.append(SqlStringUtil.dealInList("id", authShopIdList, args));
        }
        return getJdbcTemplate().query(sql.toString(), getMapper(), args.toArray());
    }

    @Override
    public List<MultiPlatformShopAuth> listShopByPuidAndNames(Integer puid, List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return Collections.emptyList();
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.in("name", names.toArray());
        return listByCondition(builder.build());
    }

    @Override
    public MultiPlatformShopAuth getByPlatformShopId(Integer puid, String platformType, String platformShopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("platform_type", platformType);
        builder.equalTo("platform_shop_id", platformShopId);
        List<MultiPlatformShopAuth> shopAuths = listByCondition(builder.build());
        return CollectionUtils.isNotEmpty(shopAuths) ? shopAuths.get(0) : null;
    }

    @Override
    public List<MultiPlatformShopAuth> listByPlatformShopIds(Integer puid, String platformType, List<String> platformShopIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("platform_type", platformType);
        builder.in("platform_shop_id", platformShopIds.toArray());
        return listByCondition(builder.build());
    }

    @Override
    public List<MultiPlatformShopAuth> checkPlatformTypeExist(int puid, List<String> platformTypes) {
        if (CollectionUtils.isEmpty(platformTypes)) {
            return Lists.newArrayList();
        }
        StringBuilder sql = new StringBuilder("select platform_type, temu_type from t_multi_platform_shop_auth where puid = ?");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("platform_type", platformTypes, args));
        sql.append("group by platform_type, temu_type");
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new BeanPropertyRowMapper<>(MultiPlatformShopAuth.class));
    }

    @Override
    public String getIossNumber(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select ioss_number from t_multi_platform_shop_auth where puid = ?");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        sql.append(" and id = ?");
        argsList.add(shopId);
        List<String> list = getJdbcTemplate().queryForList(sql.toString(), String.class, argsList.toArray());
        return CollectionUtils.isEmpty(list) ? "" : list.get(0);
    }

    @Override
    public List<Integer> getNoIossNumberIdList(Integer puid, List<Integer> shopIdList) {
        if (CollectionUtils.isEmpty(shopIdList)) {
            return Collections.emptyList();
        }
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id from t_multi_platform_shop_auth where puid = ? and ioss_number = ''");
        args.add(puid);
        sql.append(SqlStringUtil.dealInList("id", shopIdList,args));
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public List<MultiPlatformShopAuth> getByPlatFormType(Integer puid, String platformType, List<String> temuTypeList) {
        // 如果平台类型为空，直接返回
        if(StringUtils.isBlank(platformType)){
            return Collections.emptyList();
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        // 如果puid不为空，则按照puid查询
        if(Objects.nonNull(puid)){
            builder.equalTo("puid", puid);
        }
        builder.equalTo("platform_type", platformType);
        // 根据temuType查询
        if (CollectionUtils.isNotEmpty(temuTypeList)) {
            builder.in("temu_type", temuTypeList.toArray());
        }
        return listByCondition(builder.build());
    }

    /**
     * 根据platformShopTypes过滤shopIdList
     * @param puid
     * @param platformType
     * @param shopIdList
     * @param platformShopTypes
     * @return
     */
    @Override
    public List<Integer> getIdListByShopTypes(Integer puid, String platformType, List<Integer> shopIdList, List<String> platformShopTypes) {
        if (CollectionUtils.isEmpty(shopIdList) || CollectionUtils.isEmpty(platformShopTypes)) {
            //没传shopIdList或platformShopTypes不过滤数据
            return shopIdList;
        }
        //根据platformShopTypes过滤shopIdList
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id from t_multi_platform_shop_auth where puid = ? and platform_type = ?");
        args.add(puid);
        args.add(platformType);
        sql.append(SqlStringUtil.dealInList("id", shopIdList, args));
        sql.append(SqlStringUtil.dealInList("platform_shop_type", platformShopTypes, args));
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public List<Integer> getAllPuid() {
        String sql = "select puid from t_multi_platform_shop_auth group by puid";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<Integer> getAllPuidByPlatformTypeAndState(String platformType, Integer adState) {

        String sql = "select puid from t_multi_platform_shop_auth where platform_type = ? and ad_status = ? group by puid";
        return getJdbcTemplate().queryForList(sql, Integer.class, platformType, adState);
    }


    @Override
    public List<MultiPlatformShopAuth> getAllListByPlatformTypeAndState(Integer puid, Integer shopId, String platformType, Integer adState, Integer start, Integer limit) {
        StringBuilder sql = new StringBuilder("select * from t_multi_platform_shop_auth where platform_type = ? and ad_status = ? ");
        List<Object> objects = new ArrayList<>();
        objects.add(platformType);
        objects.add(adState);
        if (puid != null) {
            sql.append(" and puid = ? ");
            objects.add(puid);
        }
        if (shopId != null) {
            sql.append(" and shop_id = ? ");
            objects.add(shopId);
        }
        sql.append(" limit ?,? ");
        objects.add(start);
        objects.add(limit);


        return getJdbcTemplate().query(sql.toString(), getRowMapper(), objects.toArray());
    }


    @Override
    public int batchInsert(int puid, List<MultiPlatformShopAuth> shopAuths) {
        if (CollectionUtils.isEmpty(shopAuths)) {
            return 0;
        }
        StringBuilder sql = new StringBuilder("INSERT INTO `t_multi_platform_shop_auth` (" +
                "id, puid, name, platform_type, shop_type, platform_shop_type, " +
                "platform_merchant_id, platform_seller_id, platform_shop_id, marketplace_code, ioss_number, status," +
                " auth_time, auth_expired_time, create_id, update_id) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (MultiPlatformShopAuth item : shopAuths) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?),");
            argsList.add(item.getId());
            argsList.add(puid);
            argsList.add(item.getName());
            argsList.add(item.getPlatformType());
            argsList.add(StringUtils.isNotBlank(item.getShopType()) ? item.getShopType() : StringUtils.EMPTY);
            argsList.add(StringUtils.isNotBlank(item.getPlatformShopType()) ? item.getPlatformShopType() : StringUtils.EMPTY);
            argsList.add(StringUtils.isNotBlank(item.getPlatformMerchantId()) ? item.getPlatformMerchantId() : StringUtils.EMPTY);
            argsList.add(StringUtils.isNotBlank(item.getPlatformSellerId()) ? item.getPlatformSellerId() : StringUtils.EMPTY);
            argsList.add(StringUtils.isNotBlank(item.getPlatformShopId()) ? item.getPlatformShopId() : StringUtils.EMPTY);
            argsList.add(StringUtils.isNotBlank(item.getMarketplaceCode()) ? item.getMarketplaceCode() : StringUtils.EMPTY);
            argsList.add(StringUtils.isNotBlank(item.getIossNumber()) ? item.getIossNumber() : StringUtils.EMPTY);
            argsList.add(item.getStatus());
            argsList.add(item.getAuthTime());
            argsList.add(item.getAuthExpiredTime());
            argsList.add(item.getCreateId());
            argsList.add(item.getUpdateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }



    @Override
    public int updateShopAdAuth(Integer puid, Integer shopId, Integer state, LocalDateTime adAuthTime) {
        StringBuilder sqlBuilder = new StringBuilder("update t_multi_platform_shop_auth set  update_time = NOW(3) ");
        List<Object> args = Lists.newArrayList();
        sqlBuilder.append(",  ad_status = ? ,  ad_auth_time = ? ");
        sqlBuilder.append(" where puid = ? and id = ? ");
        args.add(state);
        args.add(adAuthTime);
        args.add(puid);
        args.add(shopId);
        return getJdbcTemplate().update(sqlBuilder.toString(), args.toArray());
    }

    @Override
    public int updateShopAdAuth(Integer puid, Integer shopId, Integer state) {
        StringBuilder sqlBuilder = new StringBuilder("update t_multi_platform_shop_auth set  update_time = NOW(3) ");
        List<Object> args = Lists.newArrayList();
        sqlBuilder.append(",  ad_status = ? ");
        sqlBuilder.append(" where puid = ? and id = ? ");
        args.add(state);
        args.add(puid);
        args.add(shopId);
        return getJdbcTemplate().update(sqlBuilder.toString(), args.toArray());
    }


    @Override
    public int updateShopAdAuth(Integer puid, Integer shopId, Integer state, LocalDateTime adAuthTime, String adAccountName) {
        StringBuilder sqlBuilder = new StringBuilder("update t_multi_platform_shop_auth set  update_time = NOW(3) ");
        List<Object> args = Lists.newArrayList();
        sqlBuilder.append(",  ad_status = ? ,  ad_auth_time = ?, ad_account_name = ? ");
        sqlBuilder.append(" where puid = ? and id = ? ");
        args.add(state);
        args.add(adAuthTime);
        args.add(adAccountName);
        args.add(puid);
        args.add(shopId);
        return getJdbcTemplate().update(sqlBuilder.toString(), args.toArray());
    }


    @Override
    public List<MultiPlatformShopAuth> listAdAuthByPuidAndPlatform(Integer puid, List<Integer> idList, String platformType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("platform_type", platformType);
        builder.equalTo("ad_status", WalmartAdAuthEnum.AD_AUTH.getStatus());
        builder.in("id", idList.toArray());
        return listByCondition(builder.build());
    }

}
