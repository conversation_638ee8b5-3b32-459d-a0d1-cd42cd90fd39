package com.meiyunji.sponsored.service.account.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.SlaveBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopStatusEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.dto.ShopByPuidDto;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;


/**
 * ShopAuth
 *
 * <AUTHOR>
 */
@Repository
public class SlaveShopAuthDaoImpl extends SlaveBaseDaoImpl<ShopAuth> implements ISlaveShopAuthDao {
    private final static String SHOPAUTH_PUID_KEY = "shopauth_puid_%s_%s"; //判断puid是否缓存了配置信息
    private final static int TIME_OUT = 60 * 60 * 4;//过期时间 1小时
    private String cacheKeyPrefix = "shopauth-"; // cache 前缀
    private int cacheValidation = 5 * 60; // cache 有效期 5 分钟

    @Override
    public ShopAuth getByIdAndPuid(Object id, Integer puid) {
        return super.getByIdAndPuid(id, puid);
    }

    @Override
    public List<String> getAllMarketplaceIdByPuid(int puid) {
        String sql = "SELECT DISTINCT(marketplace_id) FROM t_shop_auth WHERE puid = ? ";
        return getJdbcTemplate().queryForList(sql, String.class, puid);
    }

    @Override
    public List<Integer> getShopByMid(int puid, String marketplaceId) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_shop_auth WHERE puid = ?");
        StringBuilder whereSql = new StringBuilder();
        List<Object> argList = Lists.newArrayList();
        argList.add(puid);
        if (StringUtils.isNotBlank(marketplaceId)) {
            whereSql.append(" and marketplace_id = ?");
            argList.add(marketplaceId);
        }
        whereSql.append(" and ad_status = 'auth'");
        sql.append(whereSql);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, argList.toArray());
    }

    @Override
    public List<ShopAuth> getListByPuid(int puid) {
        String sql = "select * from t_shop_auth where puid=?";
        return getJdbcTemplate().query(sql, new Object[]{puid}, getMapper());
    }

    @Override
    public List<ShopAuth> getAllId() {
        String sql = "select id,marketplace_id,puid from t_shop_auth order by id";
        return getJdbcTemplate().query(sql, getMapper());
    }

    @Override
    public List<Integer> getAllShopId() {
        String sql = "select id from t_shop_auth";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<Integer> getAllShopId(int puid) {
        String sql = "select id from t_shop_auth where puid=?";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public List<Integer> getAllAdShopId(int puid) {
        String sql = "select id from t_shop_auth where puid=? and ad_status='auth'";
        return getJdbcTemplate().queryForList(sql, new Object[]{puid}, Integer.class);
    }

    @Override
    public List<String> getAllSellerId(Integer puid) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        if (puid != null && puid > 0) {
            builder.equalTo("puid", puid);
        }
        return listDistinctFieldByCondition("selling_partner_id", builder.build(), String.class);
    }

    @Override
    public List<String> getSellerIdByPuid(Integer puid) {
        String sql = "select distinct selling_partner_id from t_shop_auth WHERE puid = ?";
        return getJdbcTemplate().queryForList(sql, String.class, puid);
    }

    @Override
    public List<ShopAuth> listByPuidAndSellerId(Integer puid, String sellerId) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        condition.equalTo("1", 1);
        if (puid != null) {
            condition.equalTo("puid", puid);
        }
        if (StringUtils.isNotBlank(sellerId)) {
            condition.equalTo("selling_partner_id", sellerId);
        }
        return super.listByCondition(condition.build());
    }

    @Override
    public List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid);
        if (CollectionUtils.isNotEmpty(mkList)) {
            condition.in("marketplace_id", mkList.toArray());
        }
        if (CollectionUtils.isNotEmpty(shopList)) {
            condition.in("id", shopList.toArray());
        }
        return listByCondition(condition.build());
    }

    /**
     * 获取token
     * <p>
     * 根据大区分组
     * <p>
     * 获取大于timeSecond时长/秒 的数据
     *
     * @return
     */
    @Override
    public List<ShopAuth> getSellingToken(Integer puid, Integer shopId, long timeSecond) {
        StringBuilder shopSql = new StringBuilder("SELECT * FROM t_shop_auth where status=0 ");
        List<Object> args = Lists.newArrayList();
        if (shopId != null) {
            shopSql.append(" and id=? ");
            args.add(shopId);
        }
        StringBuilder sql = new StringBuilder("select a.* from ( ").append(shopSql).append(" ) a left join t_sync_time b on a.puid = b.puid and a.selling_partner_id = b.selling_partner_id  and b.sync_type=30 where   (b.sync_status = 0 or b.sync_status is null) and (time_to_sec(timediff(now(), b.sync_last_time)) > (" + timeSecond + ") or b.sync_last_time is null) ");

        if (puid != null) {
            sql.append(" and a.puid=?");
            args.add(puid);
        }

        return getJdbcTemplate().query(sql.toString(), getMapper(), args.toArray());
    }

    @Override
    public List<ShopAuth> getShopAuthGroupBySellerId(Integer puid, int syncType, int status) {
        StringBuilder sql = new StringBuilder("select a.* from t_shop_auth a  left join t_sync_time b on a.puid = b.puid and a.id = b.shop_id and b.sync_type=? where 1=1 ");
        List<Object> args = Lists.newArrayList(syncType);
        if (puid != null) {
            sql.append(" and a.puid = ? ");
            args.add(puid);
        } else {
            if (status == 0) {
                sql.append(" and (b.sync_status = ? or b.sync_status is null)");
            } else {
                sql.append(" and b.sync_status = ? ");
            }
            args.add(status);

            sql.append(" GROUP BY a.`selling_partner_id` ");
        }

        return getJdbcTemplate().query(sql.toString(), getMapper(), args.toArray());
    }

    @Override
    public List<ShopAuth> listBySellerId(String sellerId) {
        String sql = "SELECT * FROM t_shop_auth WHERE selling_partner_id = ? ";
        return getJdbcTemplate().query(sql, getMapper(), sellerId);
    }


    @Override
    public List<Integer> getIdByPuidAndShop(Integer puid, Integer shopId) {
        StringBuilder sql = new StringBuilder("select id from t_shop_auth where 1=1");
        List<Object> args = Lists.newArrayList();
        if (puid != null) {
            sql.append(" and puid=?");
            args.add(puid);
        }
        if (shopId != null) {
            sql.append(" and id=?");
            args.add(shopId);
        }
        return getJdbcTemplate().queryForList(sql.toString(), args.toArray(), Integer.class);
    }

    @Override
    public Integer getCountByShopIds(Integer puid, List<Integer> shopIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid).greaterThan("status", 0)
                .in("id", shopIds.toArray());
        return super.getCountByCondition(builder.build());
    }

    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param puid
     * @param sellerId
     * @param marketplaceId
     * @return
     */
    @Override
    public ShopAuth getBySellerIdAndMarketplaceId(Integer puid, String sellerId, String marketplaceId) {

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellerId)
                .equalTo("marketplace_id", marketplaceId)
                .limit(1)
                .build();

        return super.getByCondition(condition);
    }

    @Override
    public List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds) {

        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIds.toArray())
                .equalTo("status", ShopStatusEnum.NORMAL.value())
                .build();

        return super.listByCondition(condition);
    }

    @Override
    public List<ShopAuth> listAllByIds(Integer puid, List<Integer> shopIds) {

        if (CollectionUtils.isEmpty(shopIds)) {
            return Lists.newArrayList();
        }

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIds.toArray())
                .build();

        return super.listByCondition(condition);
    }

    /**
     * 获取所有有效的店铺
     *
     * @return
     */
    @Override
    public List<ShopAuth> listAllValidShop() {

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("status", ShopStatusEnum.NORMAL.value())
                .build();

        return super.listByCondition(condition);
    }

    @Override
    public List<ShopAuth> listAllValidAdShop(Integer puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("ad_status", ShopAdStatusEnum.AUTH.getName())
                .build();
        return super.listByCondition(condition);
    }

    @Override
    public ShopAuth getValidAdShopById(Integer puid, Integer shopId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("id", shopId)
                .equalTo("ad_status", ShopAdStatusEnum.AUTH.getName())
                .limit(1)
                .build();
        return super.getByCondition(condition);
    }

    @Override
    public List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region).build());
    }

    @Override
    public List<ShopAuth> listByPuidSellerIdRegion(int puid, String sellingPartnerId, String region, List<Integer> shopIds) {
        return listByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region).in("id", shopIds.toArray()).build());
    }

    @Override
    public int countByPuidSellerIdRegion(int puid, String sellingPartnerId, String region) {
        return getCountByCondition(new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellingPartnerId)
                .equalTo("region", region).build());
    }

    @Override
    public ShopAuth getByMarketplaceId(int puid, String sellerId, String marketplaceId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellerId)
                .equalTo("marketplace_id", marketplaceId).build();
        return getByCondition(condition);
    }

    @Override
    public List<ShopAuth> listLimit(String marketplace, int maximumPoolSize) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("marketplace_id", marketplace)
                .equalTo("status", 0)
                .limit(maximumPoolSize).build();
        return listByCondition(condition);
    }

    @Override
    public List<ShopAuth> getBySellerIdAndRegion(Integer puid, String sellingPartnerId, String region) {
        StringBuilder sql = new StringBuilder("SELECT * FROM t_shop_auth WHERE puid = ? and selling_partner_id = ?  and region = ?");
        return getJdbcTemplate().query(sql.toString(), new Object[]{puid, sellingPartnerId, region}, getRowMapper());
    }

    @Override
    public List<String> getSellerIdByShopIds(List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .inIntList("id", shopIdList.toArray(new Integer[shopIdList.size()]))
                .build();
        return listDistinctFieldByCondition("selling_partner_id", condition, String.class);
    }

    @Override
    public ShopAuth getByName(Integer puid, String shopName) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("name", shopName)
                .build();
        return getByCondition(condition);
    }

    @Override
    public List<Integer> getAllValidAdShopIdByLimit(Integer puid, Integer shopId, int start, int limit) {
        StringBuilder sql = new StringBuilder("SELECT id FROM t_shop_auth WHERE ad_status=?");

        List<Object> argList = new ArrayList<>();
        argList.add(ShopAdStatusEnum.AUTH.name());
        if (puid != null) {
            sql.append(" AND `puid` = ?");
            argList.add(puid);
        }
        if (shopId != null) {
            sql.append(" AND `id` = ?");
            argList.add(shopId);
        }

        sql.append(" ORDER BY `id` LIMIT ?, ?");
        argList.add(start);
        argList.add(limit);

        return getJdbcTemplate().queryForList(sql.toString(), argList.toArray(), Integer.class);
    }

    @Override
    public List<ShopAuth> getAllValidShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("status", ShopStatusEnum.NORMAL.value());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimitDate(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();


        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }

        return listByCondition(builder.build());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("ad_status", ShopAdStatusEnum.AUTH.name());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimitOrderByIdDesc(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("ad_status", ShopAdStatusEnum.AUTH.name());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.orderByDesc(" id ");
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public List<ShopByPuidDto> getAllValidAdShopByPuid(Integer puid) {
        StringBuilder sql = new StringBuilder("select puid, id shopId, marketplace_id marketplaceId from t_shop_auth ");
        List<Object> args = new ArrayList<>(2);
        sql.append(" where ad_status != ? ");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        sql.append(" and puid = ? ");
        args.add(puid);
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(ShopByPuidDto.class), args.toArray());
    }

    @Override
    public List<Integer> getAllValidAdShopPuidByLimit(int start, int limit) {
        StringBuilder sql = new StringBuilder("select distinct puid from t_shop_auth ");
        List<Object> args = new ArrayList<>(4);
        sql.append(" where ad_status != ? order by puid limit ?, ?");
        args.add(ShopAdStatusEnum.UNAUTH.name());
        args.add(start);
        args.add(limit);
        return getJdbcTemplate().queryForList(sql.toString(), Integer.class, args.toArray());
    }

    @Override
    public List<ShopAuth> getAllValidAdShopByLimitOrderByAdAuthTime(Integer puid, Integer shopId, int start, int limit) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalToWithoutCheck("ad_status", ShopAdStatusEnum.AUTH.name());

        if (puid != null) {
            builder.equalTo("puid", puid);
        }
        if (shopId != null) {
            builder.equalTo("id", shopId);
        }
        builder.orderByDesc("ad_auth_time");
        builder.limitByOffset(start, limit);

        return listByCondition(builder.build());
    }

    @Override
    public List<Integer> getAllPuidByLimit(int start, int limit) {
        return getJdbcTemplate().queryForList("SELECT puid FROM t_shop_auth GROUP BY puid LIMIT ?, ?",
                Integer.class, start, limit);
    }

    @Override
    public List<ShopAuth> getAllPartner(Integer puid) {
        return getJdbcTemplate().query("SELECT selling_partner_id, region FROM `t_shop_auth` WHERE `puid` = ? GROUP BY selling_partner_id, region", getMapper(), puid);
    }

    @Override
    public ShopAuth getByPuidAndSellerId(Integer puid, String sellId, String marketplaceId) {
        String sql = "SELECT id,puid,region,`name`,marketplace_id,selling_partner_id FROM t_shop_auth where puid = ? and selling_partner_id = ? and marketplace_id = ?";
        List<ShopAuth> list = getJdbcTemplate().query(sql, new Object[]{puid, sellId, marketplaceId}, getRowMapper());
        if (list != null && list.size() > 0) {
            return list.get(0);
        }
        return null;
    }

    @Override
    public List<ShopAuth> getByPuidAndSellerId(Integer puid, String sellerId) {
        String sql = "SELECT * FROM t_shop_auth where puid = ? and selling_partner_id = ?";
        return getJdbcTemplate().query(sql, new Object[]{puid, sellerId}, getRowMapper());
    }

    @Override
    public List<ShopAuth> getByPuidAndSellerIdAvailable(Integer puid, String sellerId) {
        String sql = "SELECT * FROM t_shop_auth where puid = ? and selling_partner_id = ? and status=0";
        return getJdbcTemplate().query(sql, new Object[]{puid, sellerId}, getRowMapper());
    }

    @Override
    public List<String> getAuthNormalSellers() {
        String sql = "select distinct selling_partner_id from t_shop_auth where `status`=0";
        return getJdbcTemplate().queryForList(sql, String.class);
    }

    @Override
    public List<String> getMWSSellers() {
        String sql = "select distinct selling_partner_id from t_shop_auth where `status`=2";
        return getJdbcTemplate().queryForList(sql, String.class);
    }

    @Override
    public List<ShopAuth> listByPuids(List<Integer> puids) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .in("puid", puids.toArray())
                .build();
        return listByCondition(conditionBuilder);
    }


    @Override
    public List<ShopAuth> getByMarketplaceIds(int puid, String sellerId, String marketplaceId) {

        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("selling_partner_id", sellerId)
                .inStrList("marketplace_id", marketplaceId.split(",")).build();


        return listByCondition(condition);
    }

    /**
     * 获取店铺信息
     *
     * @param puid
     * @param marketplaceIds
     * @return
     */
    public List<ShopAuth> getByMarketplaceIds(int puid, String[] marketplaceIds) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("marketplace_id", marketplaceIds).build();
        return listByCondition(condition);
    }

    /**
     * 获取已授权对应marketplaceId的店铺
     */
    public List<ShopAuth> getShopAuthByMarketplaceId(String marketplaceId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("ad_status", ShopAdStatusEnum.AUTH.name())
                .limit(100).build();
        return listByCondition(condition);
    }

    @Override
    public List<ShopAuth> getShopAuthByAuthAndTokenIsNull() {
        StringBuilder sql = new StringBuilder("SELECT * FROM t_shop_auth WHERE ad_status = ? and ad_access_token is null ");
        return getJdbcTemplate().query(sql.toString(), new Object[]{ShopAdStatusEnum.AUTH.name()}, getRowMapper());
    }

    @Override
    public List<String> marketplaceIdListByShopIds(List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .in("id", shopIdList.toArray())
                .build();
        return listDistinctFieldByCondition("marketplace_id", condition, String.class);
    }

    @Override
    public List<Integer> IdListByShopIds(List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .in("id", shopIdList.toArray())
                .build();
        return listDistinctFieldByCondition("id", condition, Integer.class);
    }

    @Override
    public List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("id", shopIdList.toArray())
                .notEqualTo("ad_status", ShopAdStatusEnum.UNAUTH.name())
                .build();
        return listByCondition(condition);
    }


    /**
     * 根据 sellerPartnerId 和 marketplaceId 找到店铺
     * 理论上：同一个sellerPartnerId ，在同一个marketplaceId 下只会有一个店铺
     *
     * @param sellerIds
     * @param marketplaceIds
     * @return
     */
    @Override
    public List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds) {
        List<Object> args = new ArrayList<>();
        String sql = "select * from t_shop_auth where 1=1 " + SqlStringUtil.dealInList("selling_partner_id", sellerIds, args) + SqlStringUtil.dealInList("marketplace_id", marketplaceIds, args);
        return getJdbcTemplate().query(sql, getMapper(), args.toArray());
    }


    @Override
    public List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puids) {
        ConditionBuilder.Builder condition = new ConditionBuilder.Builder();
        if (CollectionUtils.isNotEmpty(puids)) {
            condition.in("puid", puids.toArray());
        }
        condition.equalTo("ad_status", ShopAdStatusEnum.AUTH.name());
        return listByCondition(condition.build());
    }

    @Override
    public ShopAuth getByShopAuth(String sellerId, String marketplaceId) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("selling_partner_id", sellerId)
                .equalTo("marketplace_id", marketplaceId)
                .limit(1)
                .build();
        return super.getByCondition(condition);
    }





}