package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.sd.constant.TacticEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.sb.campaign.ThemesInfoResponse;
import com.meiyunji.sponsored.rpc.sd.campaign.*;
import com.meiyunji.sponsored.rpc.sd.campaign.SdTargetingVo;
import com.meiyunji.sponsored.rpc.vo.ProductNewRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sd.*;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.enums.BudgetValueEnum;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/6
 */
@Service
@Slf4j
public class CpcSdCampaignServiceImpl implements ICpcSdCampaignService {

    @Autowired
    private IAmazonAdCampaignAllDao amazonSdAdCampaignDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdSdCampaignReportDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private CpcSdCampaignApiService cpcSdCampaignApiService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;

    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private ICpcSdGroupService cpcSdGroupService;

    @Autowired
    private ICpcSdAdProductService cpcSdAdProductService;

    @Autowired
    private ICpcSdTargetingService cpcSdTargetingService;

    @Autowired
    private ICpcSdNeTargetingService cpcSdNeTargetingService;

    @Autowired
    private ICpcSdCreativeService cpcSdCreativeService;

    @Autowired
    private IAmazonAdSdCreativeDao amazonAdSdCreativeDao;

    private ThreadPoolExecutor threadPool = ThreadPoolUtil.getSdNewCreateCampaignThreadPool();

    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Override
    public Result showCampaignPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null || StringUtils.isBlank(param.getCampaignId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdCampaignAll amazonAdCampaign = amazonSdAdCampaignDao.getByCampaignId(puid, param.getShopId(), null,param.getCampaignId(), CampaignTypeEnum.sd.getCampaignType());
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("请求参数错误");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdCampaign.getShopId());
        adPerformanceVo.setCampaignId(amazonAdCampaign.getCampaignId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdCampaignAllReport> reports = amazonAdSdCampaignReportDao.listReports(puid, param.getShopId(),
                param.getStartDate(), param.getEndDate(), param.getCampaignId(), CampaignTypeEnum.sd.getCampaignType());
        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), param.getStartDate(),  param.getEndDate());

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();
                        e.setSaleNum(e.getOrderNum());
                        e.setAdSaleNum(e.getAdOrderNum());
                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public List<CampaignPageVo> getList(Integer puid, CampaignPageParam param) {
        long t = Instant.now().toEpochMilli();
        List<AmazonAdCampaignAll>  poList = amazonSdAdCampaignDao.getList(puid, param);
        log.info("广告管理--广告活动接口调用-获取sd广告管理- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli()-t),JSONUtil.objectToJson(param));

        List<CampaignPageVo> voList = new ArrayList<>(poList.size());

        long t1 = Instant.now().toEpochMilli();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }

            Map<Integer, User> userMap = userDao.listByPuid(param.getPuid()).stream().collect(Collectors.toMap(User::getId, e -> e));

            // 按活动分组获取活动的汇总数据
            Map<String, AmazonAdCampaignAllReport> campaignReportMap = amazonAdSdCampaignReportDao.listSumReports(param.getPuid(), param.getShopId(),null, param.getStartDate(), param.getEndDate(),
                            poList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, e -> e));
            log.info("广告管理--广告活动接口调用-按campaignId获取sd广告活动报告- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli()-t1),JSONUtil.objectToJson(param));

            long t2 = Instant.now().toEpochMilli();
            // 取店铺销售额
            ShopSaleDto shopSaleDto =  new ShopSaleDto();
            if (param.getShopSales() != null) {  // 最外层查了一次了
                shopSaleDto.setSumRange(param.getShopSales());
            }

            CampaignPageVo vo;
            for (AmazonAdCampaignAll amazonAdCampaign : poList) {
                vo = new CampaignPageVo();
                voList.add(vo);
                convertPoToPageVo(amazonAdCampaign, vo);

                if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }

                // 创建人
                if (userMap.containsKey(amazonAdCampaign.getCreateId())) {
                    User user = userMap.get(amazonAdCampaign.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                // 填充报告数据
                if (campaignReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillReportDataIntoPageVo(vo, campaignReportMap.get(amazonAdCampaign.getCampaignId()).getReportBase(), shopSaleDto);
                }
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
                cpcCommService.filterCampaignAdvanceData(voList, param);
            }
            log.info("广告管理--广告活动接口调用-sd广告活动填充报告数据等- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli()-t2),JSONUtil.objectToJson(param));

        }

        return voList;
    }



    @Override
    public Result<String> createCampaign(SdCampaignVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //判断活动名称是否存在
        if (amazonSdAdCampaignDao.exist(puid, shopId, vo.getName().trim(),CampaignTypeEnum.sd.getCampaignType())) {
            return ResultUtil.returnErr("名称已存在");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonAdCampaignAll amazonAdCampaign = convertVoToCreatePo(vo, profile);

        Result result = cpcSdCampaignApiService.create(shop, profile, amazonAdCampaign);
        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }

        // 入库
        try {
            amazonSdAdCampaignDao.save(puid, amazonAdCampaign);
            saveDoris(Collections.singletonList(amazonAdCampaign), true);
            return ResultUtil.returnSucc(amazonAdCampaign.getCampaignId());
        } catch (Exception e) {
            log.error("createSdCampaign:", e);
        }

        return ResultUtil.returnErr("创建广告活动失败");
    }

    @Override
    public NewCreateResultResultVo createCampaign(SdCampaignVo vo, ShopAuth shop, AmazonAdProfile profile) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //判断活动名称是否存在
        if (amazonSdAdCampaignDao.exist(puid, shopId, vo.getName().trim(),CampaignTypeEnum.sd.getCampaignType())) {
            throw new ServiceException(SDCreateErrorEnum.CAMPAIGN_NAME_EXISTS.getMsg());
        }

        AmazonAdCampaignAll amazonAdCampaign = convertVoToCreatePo(vo, profile);
        Result result = cpcSdCampaignApiService.createNew(shop, profile, amazonAdCampaign);

        if (!result.success()) {
            throw new ServiceException(result.getMsg());
        }

        String campaignId = amazonAdCampaign.getCampaignId();

        // 入库
        try {
            amazonSdAdCampaignDao.save(puid, amazonAdCampaign);
            saveDoris(Collections.singletonList(amazonAdCampaign), true);
        } catch (Exception e) {
            log.error("createCampaign save fail:", e);
            throw new ServiceException(SBCreateErrorEnum.CAMPAIGN_SAVE_FAIL.getMsg());
        }

        ThreadPoolUtil.getCampaignCreateSyncPool().execute(() -> {
            try {
                // 创建成功 同步这个活动
                cpcAdSyncService.asyncSbByShop(shop, campaignId, null);
            } catch (Exception e) {
                log.info("添加成功后同步广告活动异常", e);
                throw new ServiceException(e.getMessage());
            }
        });

        /**
         * 广告活动增加日志-sd
         * 操作类型：新增广告活动
         * 逻辑：调用新增广告日志方法，传空对象作为旧值
         * start
         */
        //获取配置信息
        List<AdManageOperationLog> adSdManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adSdManageOperationLog = adManageOperationLogService.getSdAdManageOperationLog(null,amazonAdCampaign);
        adSdManageOperationLog.setIp(vo.getLoginIp());
        if (result.success()) {
            adSdManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adSdManageOperationLog.setCampaignId(campaignId);
        } else {
            adSdManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adSdManageOperationLog.setResultInfo(result.getMsg());
        }
        adSdManageOperationLogs.add(adSdManageOperationLog);
        //写日志-记录到es中
        adManageOperationLogService.printAdOperationLog(adSdManageOperationLogs);
        //end

        return NewCreateResultResultVo.builder()
                .campaignId(campaignId)
                .build();
    }

    @Override
    public SDCreateInfoNewResponse submitTogetherCreateCampaign(SDCreateCampaignNewRequest request) {
        SDCreateInfoNewResponse.Builder responseInfo = SDCreateInfoNewResponse.newBuilder();
        int shopId = request.getShopId();
        int puid = request.getPuid();
        responseInfo.setShopId(shopId);
        //拼装各service需要使用的参数vo
        //根据各层级id，判断各层级是新建，还是重新提交
        //入库
        //同步创建各层级，只要出现异常，立即返回，投放及否定投放层级放在最后进行创建
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            responseInfo.setCommonErrMsg(SBCreateErrorEnum.NOT_BEEN_AUTHORIZED.getMsg());
            return responseInfo.build();
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            responseInfo.setCommonErrMsg(SBCreateErrorEnum.NOT_HAVE_PROFILE.getMsg());
            return responseInfo.build();
        }

        //如果campaignId不存在，直接从活动层级开始创建
        if (StringUtils.isEmpty(request.getCampaignId())) {
            //组装广告活动参数vo
            SdCampaignVo campaignVo = buildCampaignVo(request);
            //组装广告组vo
            AmazonSdAdGroup groupVo = buildGroupVo(request);
            responseInfo = sdCreateCampaignStep(campaignVo, shop, profile, responseInfo);
            if (StringUtils.isNotEmpty(responseInfo.getCampaignResponse().getCampaignErrMsg())) {
                return responseInfo.build();
            }
            responseInfo = createGroupStep(groupVo, request.getLoginIp(), shop, profile, responseInfo,
                    responseInfo.getCampaignResponse().getCampaignId());
            if (StringUtils.isNotEmpty(responseInfo.getGroupResponse().getGroupErrMsg())) {
                return responseInfo.build();
            }
            responseInfo = createProductsStep(request, shop, profile, responseInfo, responseInfo.getCampaignResponse().getCampaignId(),
                    responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getProductsResponse().getProductErrMsg())) {
                return responseInfo.build();
            }

            //创建创意
            responseInfo = createCreativesStep(request, shop, profile, responseInfo,
                    responseInfo.getCampaignResponse().getCampaignId(), responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getCreativeResponse().getCreativeErrMsg())) {
                return responseInfo.build();
            }

            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);

            return responseInfo.build();
        }

        //广告活动已创建，从广告组开始创建
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isEmpty(request.getAdGroupId())) {
            //check campaignId
            AmazonAdCampaignAll campaignInfo = amazonSdAdCampaignDao.getSdAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (Objects.isNull(campaignInfo)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            }
            //set campaign info
            fillCampaignInfo(responseInfo, campaignInfo);

            //组装广告组vo
            AmazonSdAdGroup groupVo = buildGroupVo(request);
            responseInfo = createGroupStep(groupVo, request.getLoginIp(), shop, profile, responseInfo,
                    responseInfo.getCampaignResponse().getCampaignId());
            if (StringUtils.isNotEmpty(responseInfo.getGroupResponse().getGroupErrMsg())) {
                return responseInfo.build();
            }
            responseInfo = createProductsStep(request, shop, profile, responseInfo, responseInfo.getCampaignResponse().getCampaignId(),
                    responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getProductsResponse().getProductErrMsg())) {
                return responseInfo.build();
            }

            //创建创意
            responseInfo = createCreativesStep(request, shop, profile, responseInfo,
                    responseInfo.getCampaignResponse().getCampaignId(), responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getCreativeResponse().getCreativeErrMsg())) {
                return responseInfo.build();
            }

            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
            return responseInfo.build();
        }

        //广告活动和广告组都已存在，从广告产品开始创建
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isNotEmpty(request.getAdGroupId())
                && CollectionUtils.isNotEmpty(request.getProductInfoVo().getProductList())
                && request.getProductInfoVo().getProductList().parallelStream().anyMatch(p -> p.getId() == 0)
                && StringUtils.isEmpty(request.getCreativeVo().getCreativeId())) {
            //check campaignId
            AmazonAdCampaignAll campaignInfo = amazonSdAdCampaignDao.getSdAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (Objects.isNull(campaignInfo)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            }
            //set campaign info
            fillCampaignInfo(responseInfo, campaignInfo);

            //check adGroupId
            AmazonSdAdGroup groupInfo = amazonSdAdGroupDao.getByGroupId(puid, shop.getId(), request.getAdGroupId());
            if (Objects.isNull(groupInfo)) {
                GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
                groupResBuilder.setGroupErrMsg(SBCommonErrorVo.getErrorListByRaw(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg()));
                responseInfo.setGroupResponse(groupResBuilder.build());
                return responseInfo.build();
            }
            fillAdGroupInfo(responseInfo, groupInfo);

            responseInfo = createProductsStep(request, shop, profile, responseInfo, responseInfo.getCampaignResponse().getCampaignId(),
                    responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getProductsResponse().getProductErrMsg())) {
                return responseInfo.build();
            }

            //创建创意
            responseInfo = createCreativesStep(request, shop, profile, responseInfo,
                    responseInfo.getCampaignResponse().getCampaignId(), responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getCreativeResponse().getCreativeErrMsg())) {
                return responseInfo.build();
            }

            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
            return responseInfo.build();
        }

        //广告活动，广告组，广告产品都已经存在，创建创意，投放，否投
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isNotEmpty(request.getAdGroupId())
                && CollectionUtils.isNotEmpty(request.getProductInfoVo().getProductList())
                && request.getProductInfoVo().getProductList().parallelStream().noneMatch(p -> p.getId() == 0)
                && StringUtils.isEmpty(request.getCreativeVo().getCreativeId())) {
            //check campaignId
            AmazonAdCampaignAll campaignInfo = amazonSdAdCampaignDao.getSdAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (Objects.isNull(campaignInfo)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            }
            //set campaign info
            fillCampaignInfo(responseInfo, campaignInfo);

            //check adGroupId
            AmazonSdAdGroup groupInfo = amazonSdAdGroupDao.getByGroupId(puid, shop.getId(), request.getAdGroupId());
            if (Objects.isNull(groupInfo)) {
                GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
                groupResBuilder.setGroupErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.GROUP_NOT_EXIST.getMsg()));
                responseInfo.setGroupResponse(groupResBuilder.build());
                return responseInfo.build();
            }
            fillAdGroupInfo(responseInfo, groupInfo);

            List<String> adIds = request.getProductInfoVo().getProductList().stream().map(ProductNewRpcVo::getId).map(String::valueOf).collect(Collectors.toList());
            //check productId
            List<AmazonSdAdProduct> productInfoList = amazonSdAdProductDao.listByAdId(puid, shop.getId(), adIds);
            if (CollectionUtils.isEmpty(productInfoList)) {
                ProductResponse.Builder productResBuilder = ProductResponse.newBuilder();
                productResBuilder.setProductErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.PRODUCT_NOT_EXIST.getMsg()));
                responseInfo.setProductsResponse(productResBuilder.build());
                return responseInfo.build();
            }
            //创建创意
            responseInfo = createCreativesStep(request, shop, profile, responseInfo,
                    responseInfo.getCampaignResponse().getCampaignId(), responseInfo.getGroupResponse().getAdGroupId());
            if (StringUtils.isNotEmpty(responseInfo.getCreativeResponse().getCreativeErrMsg())) {
                return responseInfo.build();
            }

            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
        }

        //只创建，投放，否投
        if (StringUtils.isNotEmpty(request.getCampaignId()) && StringUtils.isNotEmpty(request.getAdGroupId())
                && CollectionUtils.isNotEmpty(request.getProductInfoVo().getProductList())
                && request.getProductInfoVo().getProductList().parallelStream().noneMatch(p -> p.getId() == 0)
                && StringUtils.isNotEmpty(request.getCreativeVo().getCreativeId())) {
            //check campaignId
            AmazonAdCampaignAll campaignInfo = amazonSdAdCampaignDao.getSdAsinByCampaignId(puid, shop.getId(), request.getCampaignId());
            if (Objects.isNull(campaignInfo)) {
                CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
                campaignResBuilder.setCampaignErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg()));
                responseInfo.setCampaignResponse(campaignResBuilder.build());
                return responseInfo.build();
            }
            //set campaign info
            fillCampaignInfo(responseInfo, campaignInfo);

            //check adGroupId
            AmazonSdAdGroup groupInfo = amazonSdAdGroupDao.getByGroupId(puid, shop.getId(), request.getAdGroupId());
            if (Objects.isNull(groupInfo)) {
                GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
                groupResBuilder.setGroupErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.GROUP_NOT_EXIST.getMsg()));
                responseInfo.setGroupResponse(groupResBuilder.build());
                return responseInfo.build();
            }
            fillAdGroupInfo(responseInfo, groupInfo);

            List<String> adIds = request.getProductInfoVo().getProductList().stream().map(ProductNewRpcVo::getId).map(String::valueOf).collect(Collectors.toList());
            //check productId
            List<AmazonSdAdProduct> productInfoList = amazonSdAdProductDao.listByAdId(puid, shop.getId(), adIds);
            if (CollectionUtils.isEmpty(productInfoList)) {
                ProductResponse.Builder productResBuilder = ProductResponse.newBuilder();
                productResBuilder.setProductErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.PRODUCT_NOT_EXIST.getMsg()));
                responseInfo.setProductsResponse(productResBuilder.build());
                return responseInfo.build();
            }
            //校验创意
            AmazonAdSdCreative creative = amazonAdSdCreativeDao.getByCreativeId(puid, shop.getId(), shop.getMarketplaceId(), request.getCreativeVo().getCreativeId());
            if (Objects.isNull(creative)) {
                CreativesResponse.Builder creativeResBuilder = CreativesResponse.newBuilder();
                creativeResBuilder.setCreativeErrMsg(SBCommonErrorVo.getErrorListByRaw(SDCreateErrorEnum.ADS_NOT_EXIST.getMsg()));
                responseInfo.setCreativeResponse(creativeResBuilder.build());
                return responseInfo.build();
            }
            //异步创建投放和否投
            createTargetAndNeTargetAsync(responseInfo, request, shop, profile);
        }

        return responseInfo.build();
    }

    private SDCreateInfoNewResponse.Builder sdCreateCampaignStep(SdCampaignVo campaignVo, ShopAuth shop,
                                                                 AmazonAdProfile profile, SDCreateInfoNewResponse.Builder responseInfo) {
        //这里得到的结果是广告活动的创建结果
        NewCreateResultResultVo campaignResult;
        CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
        try {
            campaignResult = this.createCampaign(campaignVo, shop, profile);
            campaignResBuilder.setCampaignId(campaignResult.getCampaignId());
            responseInfo.setCampaignResponse(campaignResBuilder.build());
        } catch (Exception e) {
            campaignResBuilder.setCampaignErrMsg(SDCommonErrorVo.getErrorListByRawWithField("广告活动", e.getMessage()));
            responseInfo.setCampaignResponse(campaignResBuilder.build());
            return responseInfo;
        }
        return responseInfo;
    }

    private SDCreateInfoNewResponse.Builder createGroupStep(AmazonSdAdGroup groupVo, String loginIp,
                                                            ShopAuth shop, AmazonAdProfile profile,
                                                            SDCreateInfoNewResponse.Builder responseInfo, String campaignId) {
        NewCreateResultResultVo groupResult;
        GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
        try {
            groupVo.setCampaignId(campaignId);
            groupResult = cpcSdGroupService.createAdGroup(groupVo, shop, profile, loginIp);
            groupResBuilder.setAdGroupId(groupResult.getAdGroupId());
            responseInfo.setGroupResponse(groupResBuilder.build());
        } catch (Exception e) {
            groupResBuilder.setGroupErrMsg(SDCommonErrorVo.getErrorListByRaw(e.getMessage()));
            responseInfo.setGroupResponse(groupResBuilder.build());
            return responseInfo;
        }
        return responseInfo;
    }

    private SDCreateInfoNewResponse.Builder createProductsStep(SDCreateCampaignNewRequest request, ShopAuth shop,
                                                               AmazonAdProfile profile, SDCreateInfoNewResponse.Builder responseInfo,
                                                               String campaignId, String adGroupId) {
        NewCreateResultResultVo<SBCommonErrorVo> productsResult;
        ProductResponse.Builder productBuilder = ProductResponse.newBuilder();
        try {
            productsResult = cpcSdAdProductService.createProductNew(shop, profile, campaignId, adGroupId,
                    request.getPortfolioId(), request.getProductInfoVo(), request.getLoginIp(), request.getUid());
            if (Objects.nonNull(productsResult) && CollectionUtils.isNotEmpty(productsResult.getProductList())) {
                List<ProductResponse.ProductInfo> productList = productsResult.getProductList().stream().map(p -> {
                    ProductResponse.ProductInfo.Builder builder = ProductResponse.ProductInfo.newBuilder();
                    Optional.ofNullable(p.getProductId()).ifPresent(builder::setProductId);
                    Optional.ofNullable(p.getSku()).ifPresent(builder::setSku);
                    Optional.ofNullable(p.getLandingPageURL()).ifPresent(builder::setLandingPageURL);
                    Optional.ofNullable(p.getAdName()).ifPresent(builder::setAdName);
                    return builder.build();
                }).collect(Collectors.toList());
                productBuilder.addAllProductList(productList);
            }
            if (Objects.nonNull(productsResult) && CollectionUtils.isNotEmpty(productsResult.getErrInfoList())) {
                productBuilder.setProductErrMsg(JSONObject.toJSONString(productsResult.getErrInfoList()));
            }

            responseInfo.setProductsResponse(productBuilder.build());
        } catch (Exception e) {
            productBuilder.setProductErrMsg(SDCommonErrorVo.getErrorListByRaw(e.getMessage()));
            responseInfo.setProductsResponse(productBuilder.build());
            return responseInfo;
        }
        return responseInfo;
    }

    private SDCreateInfoNewResponse.Builder createCreativesStep(SDCreateCampaignNewRequest request, ShopAuth shop,
                                                               AmazonAdProfile profile, SDCreateInfoNewResponse.Builder responseInfo,
                                                               String campaignId, String adGroupId) {
        NewCreateResultResultVo<SBCommonErrorVo> creativesResult;
        CreativesResponse.Builder creativeResult = CreativesResponse.newBuilder();
        CreativesInfoResponse.Builder cResult = CreativesInfoResponse.newBuilder();
        try {
            //转化参数
            SdCreativeVo creative = buildCreativeVo(request, profile, adGroupId);
            creativesResult = cpcSdCreativeService.sdCreativeCreateNew(creative, campaignId, adGroupId, shop, profile,
                    request.getUid() , request.getLoginIp());
            Optional.ofNullable(creativesResult.getCreativeIds()).filter(CollectionUtils::isNotEmpty)
                    .map(l -> l.get(0)).map(String::valueOf).ifPresent(cResult::setCreativeId);
            creativeResult.setCreativeInfo(cResult.build());
            responseInfo.setCreativeResponse(creativeResult.build());
        } catch (Exception e) {
            creativeResult.setCreativeErrMsg(JSONObject.toJSONString(Collections.singletonList(SDCommonErrorVo.getErrorVo("广告创意", e.getMessage()))));
            responseInfo.setCreativeResponse(creativeResult.build());
            return responseInfo;
        }
        return responseInfo;
    }

    //创建商品投放或者受众投放
    private TargetResponse.Builder createTargetStep(SDCreateCampaignNewRequest request, ShopAuth shop,
                                                    AmazonAdProfile profile, String campaignId,
                                                    String adGroupId, Integer uid, String loginIp) {
        TargetResponse.Builder targetBuilder = TargetResponse.newBuilder();

        if (CollectionUtils.isNotEmpty(request.getTargetInfoVoList())) {
            try {
                //组装关键词vo
                List<com.meiyunji.sponsored.service.cpc.vo.SdTargetingVo> targetingVoList = buildTargetingVo(request);
                if (StringUtils.isEmpty(campaignId) || StringUtils.isEmpty(adGroupId)) {
                    log.error("创建关键词投放campaignId或adGroupId不能为空");
                    return null;
                }
                NewCreateResultResultVo<SBCommonErrorVo> targetingResult = cpcSdTargetingService.
                        sdCreateTargetingNew(targetingVoList, campaignId, adGroupId, shop, profile, uid, loginIp);
                if (Objects.nonNull(targetingResult) && CollectionUtils.isNotEmpty(targetingResult.getTargetingList())) {
                    List<TargetInfoResponse> targetingList = targetingResult.getTargetingList().stream().map(k -> {
                        TargetInfoResponse.Builder t = TargetInfoResponse.newBuilder();
                        Optional.ofNullable(k.getTargetId()).ifPresent(t::setTargetId);
                        Optional.ofNullable(k.getTargetText()).ifPresent(t::setTargetText);
                        Optional.ofNullable(k.getMatchType()).ifPresent(t::setMatchType);
                        return t.build();
                    }).collect(Collectors.toList());
                    targetBuilder.addAllTargetList(targetingList);
                }
                if (Objects.nonNull(targetingResult) && CollectionUtils.isNotEmpty(targetingResult.getErrInfoList())) {
                    targetBuilder.setTargetErrMsg(JSONObject.toJSONString(targetingResult.getErrInfoList()));
                }
            } catch (Exception e) {
                targetBuilder.setTargetErrMsg(SDCommonErrorVo.getErrorListByRaw(e.getMessage()));
                return targetBuilder;
            }
        }
        return targetBuilder;
    }

    private NeTargetResponse.Builder createNeTargetStep(SDCreateCampaignNewRequest request, ShopAuth shop,
                                                        AmazonAdProfile profile, String campaignId,
                                                        String adGroupId, Integer uid,
                                                        String loginIp) {
        NeTargetResponse.Builder neTargetBuilder = NeTargetResponse.newBuilder();

        if (CollectionUtils.isNotEmpty(request.getNeTargetInfoVoList())) {
            //创建商品否定投放
            //创建关键词否定投放
            try {
                NewCreateResultResultVo<SBCommonErrorVo> neTargetingResult = this.submitTogetherNeTargeting(request, campaignId, adGroupId, shop, profile, uid, loginIp);
                if (Objects.nonNull(neTargetingResult) && CollectionUtils.isNotEmpty(neTargetingResult.getNeTargetingIdList())) {
                    List<NeTargetInfoResponse> neTargetResultList = neTargetingResult.getNeTargetingIdList().stream().map(t -> {
                        NeTargetInfoResponse.Builder neKeyword = NeTargetInfoResponse.newBuilder();
                        Optional.ofNullable(t.getNeTargetId()).ifPresent(neKeyword::setNeTargetId);
                        Optional.ofNullable(t.getNeTargetText()).ifPresent(neKeyword::setNeTargetText);
                        Optional.ofNullable(t.getNeMatchType()).ifPresent(neKeyword::setNeMatchType);
                        return neKeyword.build();
                    }).collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(neTargetResultList)) neTargetBuilder.addAllNeTargetList(neTargetResultList);
                }
                if (Objects.nonNull(neTargetingResult) && CollectionUtils.isNotEmpty(neTargetingResult.getErrInfoList())) {
                    neTargetBuilder.setNeTargetErrMsg(JSONObject.toJSONString(neTargetingResult.getErrInfoList()));
                }
            } catch (Exception e) {
                neTargetBuilder.setNeTargetErrMsg(SDCommonErrorVo.getErrorListByRaw(e.getMessage()));
                return neTargetBuilder;
            }
        }
        return neTargetBuilder;
    }

    @Override
    public Result updateCampaign(SdCampaignVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonAdCampaignAll oldAmazonSdAdCampaign = amazonSdAdCampaignDao.getByCampaignId(puid, shopId,null, vo.getCampaignId(),CampaignTypeEnum.sd.getCampaignType());
        if (oldAmazonSdAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonAdCampaignAll amazonSdAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonSdAdCampaign, amazonSdAdCampaign);

        //判断活动名称是否存在
        if (!oldAmazonSdAdCampaign.getName().equals(vo.getName()) && amazonSdAdCampaignDao.exist(puid, shopId, vo.getName().trim(),CampaignTypeEnum.sd.getCampaignType())) {
            return ResultUtil.error("名称已存在");
        }

        //若不存在则重新设置值
        if (StringUtils.isNotBlank(vo.getName())) {
            amazonSdAdCampaign.setName(vo.getName().trim());
        }

        if (StringUtils.isNotBlank(vo.getState())) {
            amazonSdAdCampaign.setState(vo.getState());
        }
        if (StringUtils.isNotBlank(vo.getBudget())) {
            amazonSdAdCampaign.setBudget(new BigDecimal(vo.getBudget()));
        }
        if (StringUtils.isNotBlank(vo.getStartDateStr())) {
            amazonSdAdCampaign.setStartDate(DateUtil.strToDate4(vo.getStartDateStr()));
        }
        if (vo.getEndDateStr() != null) {
            if (vo.getEndDateStr().isEmpty()) {  // 空串表示用户删除结束时间
                amazonSdAdCampaign.setEndDate(null);
                amazonSdAdCampaign.setEndTimeStr("");
            } else {
                amazonSdAdCampaign.setEndDate(DateUtil.strToDate4(vo.getEndDateStr()));
                amazonSdAdCampaign.setEndTimeStr(vo.getEndDateStr().replaceAll("-", ""));
            }
        }
        if (vo.getPortfolioId() != null) {
            amazonSdAdCampaign.setPortfolioId(vo.getPortfolioId());
        }
        //校验站点广告最大值最小值
        if (StringUtils.isNotBlank(amazonSdAdCampaign.getMarketplaceId()) && StringUtils.isNotBlank(vo.getBudget())) {
            Double maxValue = BudgetValueEnum.getMaxValue(amazonSdAdCampaign.getMarketplaceId());
            Double minValue = BudgetValueEnum.getMinValue(amazonSdAdCampaign.getMarketplaceId());
            if (maxValue != null) {
                if (new BigDecimal(vo.getBudget()).compareTo(BigDecimal.valueOf(maxValue)) > 0) {
                    return ResultUtil.error("每日预算填写错误，最大值："+maxValue);

                }
            }
            if (minValue != null) {
                if (new BigDecimal(vo.getBudget()).compareTo(BigDecimal.valueOf(minValue)) < 0) {
                    return ResultUtil.error("每日预算填写错误，最小值："+minValue);
                }
            }
        }
        amazonSdAdCampaign.setUpdateId(vo.getUid());

        Result result = cpcSdCampaignApiService.update(shop, profile, amazonSdAdCampaign);
        /**
         * 广告活动增加日志
         * 操作类型：编辑广告活动
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getSdAdManageOperationLog(oldAmazonSdAdCampaign, amazonSdAdCampaign);
        adManageOperationLog.setIp(vo.getLoginIp());
        if (!result.success()) {
            return result;
        }
        if (result.success()){
            //记操作日志
            //SD广告活动有两种计费方式：cpc/vcpm
            String costType_cpc = "cpc";
            if (costType_cpc.equals(amazonSdAdCampaign.getCostType())){
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }
        }
        if (result.error()){
            result.setMsg(StringUtils.isNotBlank(result.getMsg()) ? result.getMsg() : "更新失败，请稍后重试");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        // 入库
        try {
            amazonSdAdCampaignDao.updateById(puid, amazonSdAdCampaign);
            if (amazonSdAdCampaign.getEndDate() == null) {
                //结束时间为null时,为不影响现在功能,重新更新一次数据库,后期重构吧
                amazonSdAdCampaignDao.setEndDateNull(amazonSdAdCampaign);
                amazonSdAdCampaign.setEndDate(null);
                amazonSdAdCampaign.setEndTimeStr("");
            }
            saveDoris(Collections.singletonList(amazonSdAdCampaign), false);
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("createSdCampaign:", e);
        }

        return ResultUtil.returnErr("创建广告活动失败");
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String campaignId, String ip) {
        AmazonAdCampaignAll amazonAdCampaign = amazonSdAdCampaignDao.getByCampaignId(puid, shopId,null ,campaignId,CampaignTypeEnum.sd.getCampaignType());
        if (amazonAdCampaign == null) {
            return ResultUtil.error("没有活动信息");
        }

        Result result = cpcSdCampaignApiService.archive(amazonAdCampaign);

        AmazonAdCampaignAll oldCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(amazonAdCampaign, oldCampaign);
        amazonAdCampaign.setUpdateId(uid);
        amazonAdCampaign.setState(CpcStatusEnum.archived.name());
        logSdCampaignArchive(oldCampaign, amazonAdCampaign, ip, result);
        if (result.success()) {
            amazonSdAdCampaignDao.updateByIdAndPuid(puid, amazonAdCampaign);
            saveDoris(Collections.singletonList(amazonAdCampaign), false);
        }

        return result;
    }

    private void logSdCampaignArchive(AmazonAdCampaignAll oldCampaign, AmazonAdCampaignAll campaign, String ip, Result result) {
        try {
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getSdAdManageOperationLog(oldCampaign, campaign);
            adManageOperationLog.setIp(ip);
            if (result.success()) {
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
            adManageOperationLogService.printAdOperationLog(Lists.newArrayList(adManageOperationLog));
        } catch (Exception e) {
            log.error("logSdCampaignArchive:", e);
        }
    }

    @Override
    public Page getPageList(Integer puid, CampaignPageParam param, Page page) {
        long t = Instant.now().toEpochMilli();

        List<CampaignPageVo> voList = new ArrayList<>();
        page = amazonSdAdCampaignDao.getPageList(puid, param, page);

        long t1 = Instant.now().toEpochMilli();
        List<AmazonAdCampaignAll> poList = page.getRows();
        log.info("广告管理--广告活动接口调用-获取sd广告管理- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli()-t),JSONUtil.objectToJson(param));
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());


            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }

            Map<Integer, User> userMap = userDao.listByPuid(param.getPuid()).stream().collect(Collectors.toMap(User::getId, e -> e));

            // 按活动分组获取活动的汇总数据
            Map<String, AmazonAdCampaignAllReport> campaignReportMap = amazonAdSdCampaignReportDao.listSumReports(param.getPuid(), param.getShopId(),null, param.getStartDate(), param.getEndDate(),
                    poList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, e -> e));
            log.info("广告管理--广告活动接口调用-按campaignId获取sd广告活动报告- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli()-t1),JSONUtil.objectToJson(param));

            long t2 = Instant.now().toEpochMilli();
            // 取店铺销售额
            ShopSaleDto shopSaleDto =  new ShopSaleDto();
            if (param.getShopSales() != null) {  // 最外层查了一次了
                shopSaleDto.setSumRange(param.getShopSales());
            }

            page.setRows(voList);
            CampaignPageVo vo;
            for (AmazonAdCampaignAll amazonAdCampaign : poList) {
                vo = new CampaignPageVo();
                voList.add(vo);
                convertPoToPageVo(amazonAdCampaign, vo);

                if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }

                // 创建人
                if (userMap.containsKey(amazonAdCampaign.getCreateId())) {
                    User user = userMap.get(amazonAdCampaign.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                // 填充报告数据
                if (campaignReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillReportDataIntoPageVo(vo, campaignReportMap.get(amazonAdCampaign.getCampaignId()).getReportBase(), shopSaleDto);
                }
            }
            log.info("广告管理--广告活动接口调用-sd广告活动填充报告数据等- 花费时间 {} ,params: {}", (Instant.now().toEpochMilli()-t2),JSONUtil.objectToJson(param));
        }

        return page;
    }

    // po -> 列表页vo
    private void convertPoToPageVo(AmazonAdCampaignAll amazonAdCampaign, CampaignPageVo vo) {
        vo.setId(amazonAdCampaign.getId());
        vo.setShopId(amazonAdCampaign.getShopId());
        vo.setCampaignId(amazonAdCampaign.getCampaignId());
        vo.setName(amazonAdCampaign.getName());
        amazonAdCampaign.setServingStatus(amazonAdCampaign.getServingStatus());
        vo.setServingStatus(amazonAdCampaign.getServingStatus());
        vo.setState(amazonAdCampaign.getState());
        vo.setPortfolioId(amazonAdCampaign.getPortfolioId());
        vo.setServingStatusName(amazonAdCampaign.getServingStatusName());
        vo.setServingStatusDec(amazonAdCampaign.getServingStatusDec());
        //根据状态code查询状态描述
        if (AmazonAdCampaign.stateEnum.enabled.getCode().equals(amazonAdCampaign.getState())) {
            AmazonSdAdCampaign.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(amazonAdCampaign.getServingStatus(), AmazonSdAdCampaign.servingStatusEnum.class);
            vo.setServingStatusDec(null == servingStatusEnum ? StringUtils.EMPTY : servingStatusEnum.getDescription());
            if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                vo.setServingStatus("CAMPAIGN_OUT_OF_BUDGET");
            }
            //活动级别超预算才同步时间
            if (AmazonSdAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus()) || AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                //如果状态是超过预算
                String outOfTimeStr = "";
                if (amazonAdCampaign.getOutOfBudgetTime() != null) {
                    LocalDateTime localDateTime = LocalDateTimeUtil.timestampToDateTime(amazonAdCampaign.getOutOfBudgetTime());
                    ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(amazonAdCampaign.getMarketplaceId());
                    localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime,ZoneId.systemDefault(),zoneId);
                    Date date = LocalDateTimeUtil.convertLDTToDate(localDateTime);
                    outOfTimeStr = DateUtil.dateToStrWithFormat(date, "HH:mm");
                }
                vo.setServingStatusName(vo.getServingStatusName()+" "+outOfTimeStr);
                vo.setOutOfBudget(true);
            }
        }

        vo.setDailyBudget(String.valueOf(amazonAdCampaign.getBudget()));
        vo.setCreateTime(amazonAdCampaign.getCreateTime());
        vo.setUpdateTime(amazonAdCampaign.getUpdateTime());
        vo.setTargetingType(Constants.MANUAL);
        vo.setCampaignType(CampaignEnum.sponsoredDisplay.getCampaignType());
        vo.setType(Constants.SD);
        vo.setCampaignTargetingType(amazonAdCampaign.getTactic());
        vo.setCostType(amazonAdCampaign.getCostType());

        if (amazonAdCampaign.getStartDate() != null) {
            vo.setStartDate(DateUtil.format(amazonAdCampaign.getStartDate(), DateUtil.PATTERN));
        }
        if (amazonAdCampaign.getEndDate() != null) {
            vo.setEndDate(DateUtil.format(amazonAdCampaign.getEndDate(), DateUtil.PATTERN));
        }
    }

    // 创建活动时vo->po
    public AmazonAdCampaignAll convertVoToCreatePo(SdCampaignVo vo, AmazonAdProfile profile) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        amazonAdCampaign.setPuid(vo.getPuid());
        amazonAdCampaign.setShopId(profile.getShopId());
        amazonAdCampaign.setMarketplaceId(profile.getMarketplaceId());
        amazonAdCampaign.setProfileId(profile.getProfileId());
        amazonAdCampaign.setName(vo.getName().trim());
        amazonAdCampaign.setTactic(vo.getTactic());
        amazonAdCampaign.setState(Constants.ENABLED);

        if (StringUtils.isNotBlank(vo.getStartDateStr())) {
            amazonAdCampaign.setStartDate(DateUtil.strToDate4(vo.getStartDateStr()));
        }
        if (StringUtils.isNotBlank(vo.getEndDateStr())) {
            amazonAdCampaign.setEndDate(DateUtil.strToDate4(vo.getEndDateStr()));
        }
        if (vo.getPortfolioId() != null) {
            amazonAdCampaign.setPortfolioId(vo.getPortfolioId());
        }
        if(StringUtils.isNotBlank(vo.getCostType())){
            amazonAdCampaign.setCostType(vo.getCostType());
        } else {
            amazonAdCampaign.setCostType("cpc");
        }

        amazonAdCampaign.setBudget(new BigDecimal(vo.getBudget()));
        amazonAdCampaign.setBudgetType("daily");
        amazonAdCampaign.setCreateId(vo.getUid());
        amazonAdCampaign.setCreateInAmzup(Constants.CREATE_IN_AMZUP);
        amazonAdCampaign.setType(CampaignTypeEnum.sd.getCampaignType());
        amazonAdCampaign.setAdTargetType(amazonAdCampaign.getTactic());
        amazonAdCampaign.setCampaignType(CampaignEnum.sponsoredDisplay.getCampaignType());

        return amazonAdCampaign;
    }

    @Override
    public Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> updateBatchCampaign(List<BatchCampaignVo> vos ,String type) {
        int puid = vos.get(0).getPuid();
        int shopId = vos.get(0).getShopId();
        int uid = vos.get(0).getUid();
        String ip = vos.get(0).getLoginIp();

        List<String> campaignIds = vos.stream().map(BatchCampaignVo::getCampaignId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            return ResultUtil.returnErr("没有活动信息");
        }
        List<AmazonAdCampaignAll> amazonSdAdCampaigns = amazonSdAdCampaignDao.getByCampaignIds(puid, shopId, null,campaignIds,CampaignTypeEnum.sd.getCampaignType());
        if (CollectionUtils.isEmpty(amazonSdAdCampaigns)) {
            return ResultUtil.returnErr("没有活动信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }


        Map<String, AmazonAdCampaignAll> amazonSdAdCampaignMap = amazonSdAdCampaigns.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<BatchCampaignVo> errorList = new ArrayList<>();
        List<AmazonAdCampaignAll> updateList = new ArrayList<>();
        for (BatchCampaignVo vo: vos){
            checkParam(vo,type);
            if(vo.getDxmCampaignId() != null){
                vo.setId(vo.getDxmCampaignId());
            }
            AmazonAdCampaignAll oldAmazonSdAdCampaign = amazonSdAdCampaignMap.get(vo.getCampaignId());
            if(oldAmazonSdAdCampaign == null){
                vo.setFailReason("活动不存在");
                errorList.add(vo);
                continue;
            }

            if(StringUtils.isNotBlank(vo.getFailReason())){
                vo.setId(oldAmazonSdAdCampaign.getId());
                vo.setName(oldAmazonSdAdCampaign.getName());
                vo.setFailReason("请求参数错误");
                errorList.add(vo);
                continue;
            }
            AmazonAdCampaignAll amazonSdAdCampaign = new AmazonAdCampaignAll();
            BeanUtils.copyProperties(oldAmazonSdAdCampaign ,amazonSdAdCampaign);
            convertAmazonSdCampaign(amazonSdAdCampaign,vo,type);
            updateList.add(amazonSdAdCampaign);
        }

        if(CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data =  new  BatchResponseVo<> ();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            return ResultUtil.success(data);
        }
        Result<BatchResponseVo<BatchCampaignVo,AmazonAdCampaignAll>> result = cpcSdCampaignApiService.update(shop, profile, updateList,type);

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        for (AmazonAdCampaignAll newAdCampaign : updateList) {
            AmazonAdCampaignAll oldAdCampaign = amazonSdAdCampaignMap.get(newAdCampaign.getCampaignId());
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getSdAdManageOperationLog(oldAdCampaign, newAdCampaign);
            adManageOperationLog.setIp(ip);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = result.getData();
            List<AmazonAdCampaignAll> successList = data.getSuccessList();
            Map<String, AmazonAdCampaignAll> successDataMap = successList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
            Map<String, BatchCampaignVo> errorDataMap = Maps.newHashMap();
            if(CollectionUtils.isNotEmpty(errorList)){
                List<BatchCampaignVo> amazonAdProductError = data.getErrorList();
                amazonAdProductError.addAll(errorList);
                errorDataMap = amazonAdProductError.stream().collect(Collectors.toMap(BatchCampaignVo::getCampaignId, e -> e));
                data.setFailNum(data.getErrorList().size());
                data.setCountNum( (data.getErrorList() == null? 0 : data.getErrorList().size())+(data.getSuccessList() == null? 0 : data.getSuccessList().size())) ;
            }

            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getCampaignId()).getFailReason());
                }
            }

            if(org.apache.commons.collections.CollectionUtils.isNotEmpty(successList)){
                amazonSdAdCampaignDao.batchUpdateAmazonAdCampaign(puid, successList,type);
                //写入doris
                saveDoris(puid, shopId, successList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList()));
                //更新成功数据打日志
                data.getSuccessList().clear();
            }
        } else {
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
        }
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);

        return result;
    }

    @Override
    public List<CheckAdsResponseInfo> checkAdsInfoByCampaignId(Integer puid, Integer shopId, String campaignId,
                                                               String filterType) {
        //根据campaignId获取广告活动下存在的广告组
        //根据广告组id获取所有的ads信息
        //首先查询该广告活动下所有广告组，再通过全部广告组id列表，查询广告产品
        if (Objects.isNull(puid) || Objects.isNull(shopId) || StringUtils.isEmpty(campaignId)) {
            log.error("request param is null, puid:{}, shopId:{}, campaignId:{}", puid, shopId, campaignId);
            return Collections.emptyList();
        }
        List<AmazonSdAdProduct> productList = new ArrayList<>();
        List<AmazonSdAdGroup> existGroupList = amazonSdAdGroupDao.getByCampaignIds(puid, shopId, Collections.singletonList(campaignId));

        if (CollectionUtils.isNotEmpty(existGroupList)) {
            List<String> adGroupList = existGroupList.parallelStream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(adGroupList)) {
                productList = amazonSdAdProductDao.listValidIdByGroupIds(puid, shopId, adGroupList);
            }
        }

        if (CollectionUtils.isNotEmpty(productList)) {
            if (StringUtils.isNotEmpty(filterType)) {
                String[] filterList = filterType.split(",");
                if (filterList.length > 0) {
                    productList = productList.stream().filter(p -> Arrays.asList(filterList).parallelStream()
                            .anyMatch(f -> {
                                if (StringUtils.isEmpty(p.getLandingPageType()) && SDAdsLandingPageTypeEnum.DETAIL_PAGE.getType().equalsIgnoreCase(f)) {
                                    return true;
                                }
                                return f.equalsIgnoreCase(p.getLandingPageType());
                            })).collect(Collectors.toList());
                }
            }
            return productList.stream().map(ads -> {
                CheckAdsResponseInfo.Builder builder = CheckAdsResponseInfo.newBuilder();
                Optional.ofNullable(ads.getAdId()).filter(StringUtils::isNotEmpty).ifPresent(builder::setProductId);
                Optional.ofNullable(ads.getSku()).filter(StringUtils::isNotEmpty).ifPresent(builder::setSku);
                Optional.ofNullable(ads.getLandingPageURL()).filter(StringUtils::isNotEmpty).ifPresent(builder::setLandingPageURL);
                Optional.ofNullable(ads.getAdName()).filter(StringUtils::isNotEmpty).ifPresent(builder::setLandingPageURL);
                if (StringUtils.isNotEmpty(ads.getLandingPageType()) && ads.getLandingPageType().equals(SDAdsLandingPageTypeEnum.STORE.getType())) {
                    builder.setAdsType(SDAdsLandingPageTypeEnum.STORE.getType());
                } else {
                    builder.setAdsType(SDAdsLandingPageTypeEnum.DETAIL_PAGE.getType());
                }
                return builder.build();
            }).collect(Collectors.toList());
        }

        return null;
    }

    private BatchCampaignVo checkParam(BatchCampaignVo vo, String type){
        if(StringUtils.isBlank(vo.getCampaignId())){
            vo.setFailReason("请求参数错误");
            return vo;
        }
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            if(StringUtils.isBlank(vo.getState())){
                vo.setFailReason("请求参数错误");
                return vo;
            }
        } else if(Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)){
            if(vo.getDailyBudget() == null){
                vo.setFailReason("预算费用填写错误");
                return vo;
            }
        } else {
            vo.setFailReason("请求参数错误");
            return vo;
        }

        return vo;
    }

    private AmazonAdCampaignAll convertAmazonSdCampaign(AmazonAdCampaignAll campaign, BatchCampaignVo vo, String type) {
        campaign.setUpdateId(vo.getUid());
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            campaign.setState(vo.getState());
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            campaign.setBudget(BigDecimal.valueOf(vo.getDailyBudget()));
        }
        return campaign;
    }

    private List<Object> buildUpLogMessage(Map<String,AmazonAdCampaignAll> oldList,List<AmazonAdCampaignAll> newList,String type){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if(Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)){
            dataList.add("SD广告活动批量修改每日预算");
            
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            dataList.add("SD广告活动批量修改状态");
            
        }
        newList.forEach(e ->{
            AmazonAdCampaignAll old = oldList.get(e.getCampaignId());

            if(Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)){

                builder.append("campaignId:").append(e.getCampaignId());
                if(old != null){
                    builder.append(",旧值:").append(old.getBudget());
                }
                builder.append(",新值:").append(e.getBudget());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){

                builder.append("campaignId:").append(e.getCampaignId());
                if(old != null){
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }

    @Override
    public Map<String, String> getSdCampaignCostTypeByCampaignIds(Integer puid, Integer shopId, List<String> campaignIds) {
        if (CollectionUtils.isEmpty(campaignIds) || puid == null || shopId == null) {
            return new HashMap<>(1);
        }
        List<AmazonAdCampaignAll> byCampaignIds = amazonSdAdCampaignDao.getByCampaignIds(puid, shopId,null, campaignIds,CampaignTypeEnum.sd.getCampaignType());
        Map<String, String> campiagnCostType = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campiagnCostType = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, AmazonAdCampaignAll::getCostType, (e1, e2) -> e2));
        } else {
            campiagnCostType = new HashMap<>(1);
        }
        return campiagnCostType;
    }


    /**
     * 写入doris
     * @param amazonAdCampaignList
     */
    public void saveDoris(List<AmazonAdCampaignAll> amazonAdCampaignList, boolean create) {
        try {
            Date date = new Date();
            List<OdsAmazonAdCampaignAll> collect = amazonAdCampaignList.stream().map(x -> {
                OdsAmazonAdCampaignAll odsAmazonAdCampaignAll = new OdsAmazonAdCampaignAll();
                BeanUtils.copyProperties(x, odsAmazonAdCampaignAll);
                if (create) {
                    odsAmazonAdCampaignAll.setCreateTime(date);
                }
                odsAmazonAdCampaignAll.setUpdateTime(date);
                if (StringUtils.isNotBlank(odsAmazonAdCampaignAll.getState())) {
                    odsAmazonAdCampaignAll.setState(odsAmazonAdCampaignAll.getState().toLowerCase());
                }
                return odsAmazonAdCampaignAll;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sd campaign save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param campaignIdList
     */
    private void saveDoris(Integer puid, Integer shopId, List<String> campaignIdList) {
        try {
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return;
            }
            List<AmazonAdCampaignAll> campaignAllList = amazonSdAdCampaignDao.listByCampaignId(puid, shopId, campaignIdList, CampaignTypeEnum.sd.getCampaignType());
            saveDoris(campaignAllList, false);
        } catch (Exception e) {
            log.error("sd campaign save doris error", e);
        }
    }

    private SdCampaignVo buildCampaignVo(SDCreateCampaignNewRequest request) {
        SdCampaignVo vo = new SdCampaignVo();
        vo.setPuid(request.getPuid());
        vo.setShopId(request.getShopId());
        vo.setUid(request.getUid());
        vo.setName(request.getCampaignName().trim());
        vo.setState(StateEnum.enabled.getStateType());
//        vo.setBudgetType(SDBudgetTypeEnum.DAILY.getCode());
        vo.setBudget(Optional.of(request.getDailyBudget()).map(String::trim).filter(b -> !("0").equals(b)).map(String::valueOf).orElse(""));
        if (request.hasPortfolioId() && StringUtils.isNotBlank(request.getPortfolioId()) && !Constant.NON_PORTFOLIO_ID.equals(request.getPortfolioId())) {
            vo.setPortfolioId(request.getPortfolioId());
        }
        vo.setStartDateStr(request.getStartDateStr());
        vo.setEndDateStr(request.getEndDateStr());

        //获取广告组竞价优化属性通过此参数来设置对应的costType
        SDbidOptimizationEnum bidOptimization = SDbidOptimizationEnum.getSDbidOptimizationEnumByCode(request.getBidOptimization());
        if (Objects.nonNull(bidOptimization)) {
            vo.setCostType(bidOptimization.getCostType());
        }
        vo.setTactic(TacticEnum.T00030.name());
        return vo;
    }

    private AmazonSdAdGroup buildGroupVo(SDCreateCampaignNewRequest request) {
        AmazonSdAdGroup vo = new AmazonSdAdGroup();
        vo.setPuid(request.getPuid());
        vo.setShopId(request.getShopId());
        vo.setName(request.getGroupName());
        vo.setState(StateEnum.enabled.getStateType());
        vo.setCampaignId(request.getCampaignId());
        Optional.of(request.getDefaultBid()).filter(StringUtils::isNotEmpty).map(BigDecimal::new).ifPresent(vo::setDefaultBid);
        Optional.of(request.getBidOptimization()).map(SDbidOptimizationEnum::getSDbidOptimizationEnumByCode)
                .map(SDbidOptimizationEnum::getVal).ifPresent(vo::setBidOptimization);
        Optional.of(request.getCreativeType()).map(Integer::valueOf).map(SDCreativeTypeEnum::getSDAdFormatEnumByCode)
                .map(SDCreativeTypeEnum::getValue).ifPresent(vo::setCreativeType);
        vo.setTactic(TacticEnum.T00030.name());
        vo.setCreateId(request.getUid());
        return vo;
    }

    private List<com.meiyunji.sponsored.service.cpc.vo.SdTargetingVo> buildTargetingVo(SDCreateCampaignNewRequest request) {
        List<SdTargetingVo> targetingVoList = request.getTargetInfoVoList();

        //处理集合
        return targetingVoList.stream().filter(Objects::nonNull).filter(t -> StringUtils.isEmpty(t.getSdTargetId())).map(item -> {
            com.meiyunji.sponsored.service.cpc.vo.SdTargetingVo tvo = new com.meiyunji.sponsored.service.cpc.vo.SdTargetingVo();
            BeanUtils.copyProperties(item, tvo);
            if (item.hasPrimeShippingEligible()) {
                tvo.setPrimeShippingEligible(item.getPrimeShippingEligible().getValue());
            }
            if (item.hasMinReviewRating()) {
                tvo.setMinReviewRating(item.getMinReviewRating().getValue());
            }
            if (item.hasMaxReviewRating()) {
                tvo.setMaxReviewRating(item.getMaxReviewRating().getValue());
            }
            return tvo;
        }).collect(Collectors.toList());
    }

    private SdCreativeVo buildCreativeVo(SDCreateCampaignNewRequest request, AmazonAdProfile profile,
                                                                                     String adGroupId) {
        SdCreativeVo creative = new SdCreativeVo();
        SdCreativesVo vo = request.getCreativeVo();
        Optional.of(vo.getCreativeType()).filter(StringUtils::isNotEmpty)
                .map(Integer::valueOf)
                .map(SDCreativeTypeEnum::getSDAdFormatEnumByCode)
                .map(SDCreativeTypeEnum::getValue)
                .ifPresent(creative::setCreativeType);
        Optional.of(request.getUid()).ifPresent(creative::setUid);
        Optional.of(request.getPuid()).ifPresent(creative::setPuid);
        Optional.ofNullable(profile).map(AmazonAdProfile::getProfileId).ifPresent(creative::setProfileId);
        Optional.ofNullable(adGroupId).filter(StringUtils::isNotEmpty).ifPresent(creative::setAdGroupId);
        Optional.of(request.getLoginIp()).filter(StringUtils::isNotEmpty).ifPresent(creative::setLoginIp);
        com.amazon.advertising.sd.mode.creative.CreativeProperties properties = SdCreativeVo.creativeHandler(vo.getProperties());
        creative.setProperties(properties);
        return creative;
    }
    private List<SdNeTargetingVo> buildNeTargetingVo(List<SdNeTargetsVo> request) {

        List<SdNeTargetingVo> voList = new ArrayList<>(request.size());
        for (SdNeTargetsVo neVo : request)  {
            com.meiyunji.sponsored.service.cpc.vo.SdNeTargetingVo sdNeTargetsVo = new SdNeTargetingVo();
            if (StringUtils.isNotBlank(neVo.getType())) {
                sdNeTargetsVo.setType(neVo.getType());
            }
            if (StringUtils.isNotBlank(neVo.getAsin())) {
                sdNeTargetsVo.setAsin(neVo.getAsin());
            }
            if (StringUtils.isNotBlank(neVo.getTitle())) {
                sdNeTargetsVo.setTitle(neVo.getTitle());
            }
            if (StringUtils.isNotBlank(neVo.getImgUrl())) {
                sdNeTargetsVo.setImgUrl(neVo.getImgUrl());
            }
            voList.add(sdNeTargetsVo);
        }
        return voList;
    }
    private NewCreateResultResultVo<SBCommonErrorVo> submitTogetherNeTargeting(SDCreateCampaignNewRequest request, String campaignId,
                                                                               String adGroupId, ShopAuth shop,
                                                                               AmazonAdProfile profile, Integer uid,
                                                                               String loginIp) {
        List<SdNeTargetsVo> neTargetList = request.getNeTargetInfoVoList();
        if (CollectionUtils.isNotEmpty(neTargetList)) {
            Map<String, SdNeTargetsVo> neTargetMap= request.getNeTargetInfoVoList().stream()
                    .filter(n -> StringUtils.isEmpty(n.getNeTargetId())).
                    collect(Collectors.toMap(SdNeTargetsVo::getAsin, v1 -> v1, (old, current) -> current, LinkedHashMap::new));
            neTargetList = new ArrayList<>(neTargetMap.values());
            //组装商品否投
            List<SdNeTargetingVo> neTargetingVoList = buildNeTargetingVo(neTargetList);
            return cpcSdNeTargetingService.sdCreateNeTargeting(neTargetingVoList, campaignId, adGroupId, shop, profile, uid, loginIp);
        }
        return null;
    }

    private void createTargetAndNeTargetAsync(SDCreateInfoNewResponse.Builder responseInfo, SDCreateCampaignNewRequest request,
                                              ShopAuth shop, AmazonAdProfile profile) {
        String campaignId = responseInfo.getCampaignResponse().getCampaignId();
        String adGroupId = responseInfo.getGroupResponse().getAdGroupId();
        CompletableFuture<TargetResponse.Builder> targetFuture = CompletableFuture.supplyAsync(() -> createTargetStep(request, shop, profile, campaignId,
                adGroupId, request.getUid(), request.getLoginIp()), threadPool).handle((builder, throwable) -> {
            if (Objects.nonNull(throwable)) {
                TargetResponse.Builder targetBuilder = TargetResponse.newBuilder();
                targetBuilder.setTargetErrMsg(throwable.getMessage());
                return targetBuilder;
            }
            return builder;
        });
        CompletableFuture<NeTargetResponse.Builder> neTargetFuture = CompletableFuture.supplyAsync(() -> createNeTargetStep(request, shop, profile, campaignId,
                adGroupId, request.getUid(), request.getLoginIp()), threadPool).handle((builder, throwable) -> {
            if (Objects.nonNull(throwable)) {
                NeTargetResponse.Builder neTargetBuilder = NeTargetResponse.newBuilder();
                neTargetBuilder.setNeTargetErrMsg(throwable.getMessage());
                return neTargetBuilder;
            }
            return builder;
        });
        CompletableFuture<Void> targetAndNeTargetFuture = CompletableFuture.allOf(targetFuture, neTargetFuture);
        targetAndNeTargetFuture.join();
        try {
            TargetResponse.Builder targetBuild = targetFuture.get();
            NeTargetResponse.Builder neTargetBuild = neTargetFuture.get();
            responseInfo.setTargetResponse(targetBuild.build());
            responseInfo.setNeTargetResponse(neTargetBuild.build());
        } catch (InterruptedException | ExecutionException e) {
            log.error("sd create target and neTarget async create error:{}", e.getMessage());
            throw new RuntimeException(e);
        }
    }

    private void fillCampaignInfo(SDCreateInfoNewResponse.Builder responseInfo, AmazonAdCampaignAll campaignInfo) {
        CampaignResponse.Builder campaignResBuilder = CampaignResponse.newBuilder();
        campaignResBuilder.setCampaignId(campaignInfo.getCampaignId());
        responseInfo.setCampaignResponse(campaignResBuilder.build());
    }

    private void fillAdGroupInfo(SDCreateInfoNewResponse.Builder responseInfo, AmazonSdAdGroup groupInfo) {
        GroupResponse.Builder groupResBuilder = GroupResponse.newBuilder();
        groupResBuilder.setAdGroupId(groupInfo.getAdGroupId());
        responseInfo.setGroupResponse(groupResBuilder.build());
    }
}