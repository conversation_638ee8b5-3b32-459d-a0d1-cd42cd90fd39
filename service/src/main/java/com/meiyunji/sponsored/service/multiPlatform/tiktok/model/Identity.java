package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;

@Data
public class Identity {

    private String identityId;
    private String identityType;
    private String displayName;
    private String userName;
    private Boolean productGmvMaxAvailable;
    private String identityAuthorizedBcId;
    private String identityAuthorizedShopId;
    private String storeId;
    private String profileImage;

    public static Identity fromTkIdentity(com.tiktok.advertising.model.gmv_max.Identity identity) {
        Identity result = new Identity();
        result.setIdentityId(identity.getIdentityId());
        result.setIdentityType(IdentityTypeEnum.getDescByType(identity.getIdentityType()));
        result.setUserName(identity.getUserName());
        result.setProductGmvMaxAvailable(identity.getProductGmvMaxAvailable());
        result.setIdentityAuthorizedBcId(identity.getIdentityAuthorizedBcId());
        result.setIdentityAuthorizedShopId(identity.getIdentityAuthorizedShopId());
        result.setStoreId(identity.getStoreId());
        result.setProfileImage(identity.getProfileImage());
        result.setDisplayName(identity.getDisplayName());
        return result;
    }

    public static com.tiktok.advertising.model.gmv_max.Identity toTkIdentity(Identity identity) {
        com.tiktok.advertising.model.gmv_max.Identity result = new com.tiktok.advertising.model.gmv_max.Identity();
        result.setIdentityId(identity.getIdentityId());
        result.setIdentityType(IdentityTypeEnum.getTypeByDesc(identity.getIdentityType()));
        result.setIdentityAuthorizedBcId(identity.getIdentityAuthorizedBcId());
        result.setIdentityAuthorizedShopId(identity.getIdentityAuthorizedShopId());
        result.setStoreId(identity.getStoreId());
        return result;
    }

    @Getter
    @AllArgsConstructor
    public enum IdentityTypeEnum {
        AUTH_CODE("AUTH_CODE", "授权用户"),
        TT_USER("TT_USER", "TikTok 用户"),
        BC_AUTH_TT("BC_AUTH_TT", "添加到商务中心的 TikTok 用户"),
        TTS_TT("TTS_TT", "TikTok Shop 关联的 TikTok 用户");
        private String type;
        private String desc;

        public static String getDescByType(String type) {
            for (IdentityTypeEnum value : values()) {
                if (value.getType().equals(type)) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public static String getTypeByDesc(String desc) {
            for (IdentityTypeEnum value : values()) {
                if (value.getDesc().equals(desc)) {
                    return value.getType();
                }
            }
            return null;
        }
    }

}
