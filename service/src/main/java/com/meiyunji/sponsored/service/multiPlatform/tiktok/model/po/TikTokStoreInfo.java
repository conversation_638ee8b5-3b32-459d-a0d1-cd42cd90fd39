package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  10:50
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@DbTable(value = "t_tiktok_store_info")
public class TikTokStoreInfo extends BasePo {
    @DbColumn(value = "id", autoIncrement = true, key = true)
    private Long id;

    /**
     * 用户puid
     */
    @DbColumn(value = "puid")
    private Integer puid;

    /**
     * 店铺id
     */
    @DbColumn(value = "shop_id")
    private Integer shopId;

    /**
     * 广告账号ID
     */
    @DbColumn(value = "advertiser_id")
    private String advertiserId;

    /**
     * 商店ID
     */
    @DbColumn(value = "store_id")
    private String storeId;

    /**
     * 商务中心ID
     */
    @DbColumn(value = "store_authorized_bc_id")
    private String storeAuthorizedBcId;

    /**
     * 商店类型
     */
    @DbColumn(value = "store_type")
    private String storeType;

    /**
     * 商店名称
     */
    @DbColumn(value = "store_name")
    private String storeName;

    /**
     * 商店代码
     */
    @DbColumn(value = "store_code")
    private String storeCode;

    /**
     * 删除标识（0未删除，1删除）
     */
    @DbColumn(value = "is_del")
    private Integer isDel;
}
