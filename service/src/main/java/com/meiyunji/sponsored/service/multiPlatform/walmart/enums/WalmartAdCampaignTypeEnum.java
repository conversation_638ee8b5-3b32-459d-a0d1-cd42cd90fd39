package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2025/2/24 16:13
 * @describe:
 */
@Getter
public enum WalmartAdCampaignTypeEnum {
    SP_CAMPAIGN("sponsoredProducts", "sp", "商品推广"),
    SB_CAMPAIGN("sba", "sb", "品牌推广"),
    SV_CAMPAIGN("video",  "sbv","品牌推广-视频"),
    ;
    private String code;
    private String frontCode;
    private String msg;

    WalmartAdCampaignTypeEnum(String code, String frontCode, String msg) {
        this.code = code;
        this.frontCode = frontCode;
        this.msg = msg;
    }

    public static Map<String, WalmartAdCampaignTypeEnum> campaignTypeMap = Arrays.stream(WalmartAdCampaignTypeEnum.values())
            .collect(Collectors.toMap(WalmartAdCampaignTypeEnum::getCode, Function.identity()));
    public static Map<String, WalmartAdCampaignTypeEnum> campaignTypeFrontMap = Arrays.stream(WalmartAdCampaignTypeEnum.values())
            .collect(Collectors.toMap(WalmartAdCampaignTypeEnum::getFrontCode, Function.identity()));
}
