package com.meiyunji.sponsored.service.cpc.service;

import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.negativetargeting.entity.NegativeTargetSuccessResultV3;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.amazon.advertising.spV3.targeting.entity.TargetSuccessResultV3;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.cpc.po.*;

import java.util.List;

/**
 * @ClassName ICpcSyncService
 * @Description 和amazon交互
 * <AUTHOR>
 * @Date 2020/4/23 17:09
 **/
public interface ICpcSyncService {


    /**
     * 根据asin获取推荐关键词
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param asinList
     * @return
     */
    Result getSuggestKeywordByAsin(Integer shopId, String profileId, String marketPlaceId, List<String> asinList);

    /**
     * 根据类目id获取品牌信息
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param categoryId
     * @return
     */
    Result getBandsByCategory(Integer shopId, String profileId, String marketPlaceId, String categoryId);



    /**
     * 批量修改竞价关键词
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param list
     * @return
     */
    Result updateBiddableKeyword(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> list);

    /**
     * 批量修改定位信息
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param list
     * @return
     */
    Result updateTarget(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdTargeting> list);



    /**
     * 获取建议竞价
     * @param shopId
     * @param profileId
     * @param marketPlaceId
     * @param adGroupId
     * @param keywordlist
     * @return
     */
    Result getSuggestBidByKeyword(Integer shopId, String profileId, String marketPlaceId, String adGroupId, List<AmazonAdKeyword> keywordlist);





    /**
     * 同步配置文件
     * @param puid
     * @param shopId
     * @return
     */
    Result syncProfile(int puid, Integer shopId);



    Result<ApiResponseV3<KeywordSuccessResultV3>> createBiddableKeywordV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> biddableKeywordList);

    Result<ApiResponseV3<KeywordSuccessResultV3>> createNegativeKeywordV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdKeyword> negativeKeywordList);

    Result<ApiResponseV3<TargetSuccessResultV3>> createTargetV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdTargeting> list);

    Result<ApiResponseV3<NegativeTargetSuccessResultV3>> createNegativeTargetV3(Integer shopId, String profileId, String marketPlaceId, List<AmazonAdTargeting> list);
}
