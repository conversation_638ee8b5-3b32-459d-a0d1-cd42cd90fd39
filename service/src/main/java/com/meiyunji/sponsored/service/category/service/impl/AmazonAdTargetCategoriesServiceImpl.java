package com.meiyunji.sponsored.service.category.service.impl;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.category.service.IAmazonAdTargetCategoriesService;
import com.meiyunji.sponsored.service.category.dao.AmazonAdTargetCategoriesDao;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryParam;
import com.meiyunji.sponsored.service.category.vo.TargetCategoryVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AmazonAdTargetCategoriesServiceImpl implements IAmazonAdTargetCategoriesService {

    @Autowired
    private AmazonAdTargetCategoriesDao amazonAdTargetCategoriesDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Override
    public Result<Page<TargetCategoryVo>> getPageList(TargetCategoryParam param) {
        Result result = ResultUtil.success();
        Page<TargetCategoryVo> page = amazonAdTargetCategoriesDao.listcategoryByKeyword(param);
        result.setData(page);
        return result;
    }
}
