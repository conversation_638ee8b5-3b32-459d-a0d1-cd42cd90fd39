package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignNetargetingSpDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignNetargetingSp;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsVo;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeTargetingSpParam;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: wade
 * @date: 2021/8/20 11:39
 * @describe: 广告活动-否定商品DAO
 */
@Repository
public class AmazonAdCampaignNetargetingSpDaoImpl extends BaseShardingDaoImpl<AmazonAdCampaignNetargetingSp> implements IAmazonAdCampaignNetargetingSpDao {

    /**
     * 批量保存数据
     *
     * @param list
     */
    @Override
    public void batchSave(Integer puid, List<AmazonAdCampaignNetargetingSp> list) {

        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_campaign_netargeting_sp`(`puid`, `shop_id`, ");
        sql.append("`marketplace_id`,`target_id`, `campaign_id`, `type`, `target_text`, `expression_type`,`expression`, ");
        sql.append("`resolved_expression`, `state`,`serving_status`, `title`, `img_url`, `create_id`, `update_id`, `create_in_amzup`,`create_state`,`creation_date`, ");
        sql.append( "`create_time`, `update_time`) VALUES ");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdCampaignNetargetingSp netargetingSp : list) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(netargetingSp.getPuid());
            argsList.add(netargetingSp.getShopId());
            argsList.add(netargetingSp.getMarketplaceId());
            argsList.add(netargetingSp.getTargetId());
            argsList.add(netargetingSp.getCampaignId());
            argsList.add(netargetingSp.getType());
            argsList.add(netargetingSp.getTargetText());
            argsList.add(netargetingSp.getExpressionType());
            argsList.add(netargetingSp.getExpression());
            argsList.add(netargetingSp.getResolvedExpression());
            argsList.add(netargetingSp.getState());
            argsList.add(netargetingSp.getServingStatus());
            argsList.add(netargetingSp.getTitle());
            argsList.add(netargetingSp.getImgUrl());
            argsList.add(netargetingSp.getCreateId());
            argsList.add(netargetingSp.getUpdateId());
            argsList.add(netargetingSp.getCreateInAmzup());
            argsList.add(netargetingSp.getCreateState());
            argsList.add(netargetingSp.getCreationDate());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update ")
                .append("`state`=values(state),`serving_status`=values(serving_status),`create_state`=values(create_state),`expression`=values(expression),`expression_type`=values(expression_type),`resolved_expression`=values(resolved_expression),`update_id`=values(update_id),`creation_date`=values(creation_date)");

        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());

    }

    /**
     * 分页查询
     * @param puid
     * @param param
     * @return
     */
    @Override
    public Page<AmazonAdCampaignNetargetingSp> pageList(Integer puid, CampaignNeTargetingSpParam param) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        if (StringUtils.isNotBlank(param.getCampaignId())) {
            builder.inStrList("campaign_id", StringUtil.splitStr(param.getCampaignId()).toArray(new String[]{}));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id",param.getCampaignIdList().toArray(new String[]{}));
        }
        if (StringUtils.isNotBlank(param.getState())) {
            builder.equalTo("state",param.getState());
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("target_text", param.getSearchValue());
        }
        builder.equalTo("create_state", "SUCCESS");

        String orderSql = " order by id desc ";

        return page(puid, param.getPageNo(), param.getPageSize(), orderSql, builder.build());
    }

    @Override
    public List<AmazonAdCampaignNetargetingSp> ListByCampaignId(Integer puid, Integer shopId, String campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .equalTo("campaign_id", campaignId)
            .equalTo("state", "enabled");
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignNetargetingSp> listNoAsinInfo(Integer puid, Integer shopId, String startDate, String campaignId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .isNull("img_url")
            .greaterThanOrEqualTo("create_time", startDate)
            .equalTo("create_state", "SUCCESS");

        if (StringUtils.isNotBlank(campaignId)) {
            builder.equalTo("campaign_id", campaignId);
        }
        return listByCondition(puid,builder.build());
    }


    /**
     * 批量修改asin信息
     * @param puid
     * @param needUpdateList
     */
    @Override
    public void batchUpdateAsinInfo(Integer puid, List<AmazonAdCampaignNetargetingSp> needUpdateList) {
        if (CollectionUtils.isNotEmpty(needUpdateList)) {
            String sql = "update t_amazon_ad_campaign_netargeting_sp set img_url=?, title=?, update_time=now(3) where id=? and puid=?";
            List<Object[]> argsList = new ArrayList<>();
            List<Object> args;
            for (AmazonAdCampaignNetargetingSp t : needUpdateList) {
                args = new ArrayList<>(4);
                args.add(t.getImgUrl() == null ? ""
                        : (t.getImgUrl().length() > 1000 ? t.getImgUrl().substring(0, 1000) : t.getImgUrl()));
                args.add(t.getTitle() == null ? ""
                        : (t.getTitle().length() > 1000 ? t.getTitle().substring(0, 1000) : t.getTitle()));
                args.add(t.getId());
                args.add(t.getPuid());
                argsList.add(args.toArray());
            }
            getJdbcTemplate(puid).batchUpdate(sql, argsList);
        }
    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {
        String sql = "select target_id from t_amazon_ad_campaign_netargeting_sp where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .equalTo("state", "archived")
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {
        String sql = "select target_id from t_amazon_ad_campaign_netargeting_sp where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id",shopId)
                .greaterThan("update_time", syncAt)
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public void batchUpdateArchive(Integer puid, List<AmazonAdCampaignNetargetingSp> needUpdateList) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_campaign_netargeting_sp` set `state`=?,")
                .append("`update_time`= now(),update_id=? where puid=? and shop_id=? and campaign_id=? and target_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdCampaignNetargetingSp amazonAdCampaignNetargetingSp : needUpdateList) {
            batchArg = new Object[]{
                    amazonAdCampaignNetargetingSp.getState(),
                    amazonAdCampaignNetargetingSp.getUpdateId(),
                    amazonAdCampaignNetargetingSp.getPuid(),
                    amazonAdCampaignNetargetingSp.getShopId(),
                    amazonAdCampaignNetargetingSp.getCampaignId(),
                    amazonAdCampaignNetargetingSp.getTargetId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);
    }


    @Override
    public List<AmazonAdCampaignNetargetingSp> listByTargetId(Integer puid, Integer shopId, List<String> targetIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("target_id", targetIds.toArray(new String[0]));
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdCampaignNetargetingSp> listByTargetId(Integer puid, List<Integer> shopIds, List<String> targetIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .inStrList("target_id", targetIds.toArray(new String[0]));
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select target_text asin, count(*) targetNum from t_amazon_ad_campaign_netargeting_sp " +
                "where puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIdList)){
            sql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        }
        //通过来源站点筛选
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(param.getContryList())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getContryList(), argsList));
        }

        sql.append(" and `type` = 'asin' ");

        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(asinList)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String asin : asinList) {
                lowerCaseList.add(asin.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(target_text)",lowerCaseList,argsList));
        }
        sql.append(" group by target_text");
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(AsinLibsVo.class), argsList.toArray());
    }

    @Override
    public List<AmazonAdCampaignNetargetingSp> listByTargetText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, target_id, target_text, campaign_id, type," +
                " state from " + getJdbcHelper().getTable() + " where state != 'archived' and puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealMultiColumnIn(Lists.newArrayList("campaign_id", "target_text"), infoList,
                Lists.newArrayList(
                        NegativeArchiveRequest.NegativeInfo::getCampaignId,
                        NegativeArchiveRequest.NegativeInfo::getNegativeText
                ),
                args));
        return getJdbcTemplate(puid).query(sql.toString(), new ObjectMapper<>(AmazonAdCampaignNetargetingSp.class), args.toArray());
    }
}
