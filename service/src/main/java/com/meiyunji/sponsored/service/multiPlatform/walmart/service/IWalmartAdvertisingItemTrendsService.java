package com.meiyunji.sponsored.service.multiPlatform.walmart.service;



import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingItemTrends;

import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingItemTrendsService {

    /**
     * 新增
     */
    int add(WalmartAdvertisingItemTrends itemTrends);

    /**
     * 删除
     */
    int delete(Long id);

    /**
     * 更新
     */
    int update(WalmartAdvertisingItemTrends itemTrends);

    Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    void syncItemTrends();


}
