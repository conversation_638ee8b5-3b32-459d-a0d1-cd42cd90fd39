package com.meiyunji.sponsored.service.cpc.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: wade
 * @date: 2021/8/20 10:38
 * @describe: sp广告活动-否定商品投放vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel
public class CampaignNeTargetingSpVo {

    @ApiModelProperty("广告类型")
    private String type;

    @ApiModelProperty("id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer id;

    @ApiModelProperty("活动ID")
    private String campaignId;

    @ApiModelProperty("活动名称")
    private String campaignName;

    @ApiModelProperty("投放类型")
    private String campaignTargetingType;

    @ApiModelProperty("asin")
    private String asin;

    @ApiModelProperty("状态 启用 暂停 归档")
    private String state;

    @ApiModelProperty("asin标题")
    private String title;

    @ApiModelProperty("asin图片")
    private String imgUrl;

    @ApiModelProperty("创建时间")
    private String createTime;




}
