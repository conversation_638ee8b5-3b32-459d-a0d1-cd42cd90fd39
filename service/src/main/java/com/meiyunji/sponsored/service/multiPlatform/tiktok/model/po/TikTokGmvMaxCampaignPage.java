package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po;

import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class TikTokGmvMaxCampaignPage {

    private Integer puid;

    @DbColumn(value = "shop_id")
    private Integer shopId;
    private String shopName;

    @DbColumn(value = "advertiser_id")
    private String advertiserId;
    private String advertiserName;

    @DbColumn(value = "store_id")
    private String storeId;

    @DbColumn(value = "store_authorized_bc_id")
    private String storeAuthorizedBcId;

    @DbColumn(value = "campaign_id")
    private String campaignId;
    @DbColumn(value = "campaign_name")
    private String campaignName;

    @DbColumn(value = "operation_status")
    private String status;

    @DbColumn(value = "primary_status")
    private String primaryStatus;

    @DbColumn(value = "shopping_ads_type")
    private String shoppingAdsType; // 商品还是直播

    @DbColumn(value = "optimization_goal")
    private String optimizationGoal;
    @DbColumn(value = "roas_bid")
    private BigDecimal roasBid;
    private BigDecimal budget;

    private BigDecimal cost;
    private BigDecimal orders;
    private BigDecimal roi;
    private BigDecimal netCost;
    private BigDecimal costPerOrder;
    private BigDecimal grossRevenue;

    private Integer hasReport;

}
