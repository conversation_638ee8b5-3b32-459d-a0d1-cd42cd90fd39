package com.meiyunji.sponsored.service.permission.filter.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.meiyunji.sponsored.common.permission.context.PermissionContext;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.permission.filter.AbstractPermissionFilter;
import com.meiyunji.sponsored.service.permission.filter.PermissionFilter;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Campaign权限过滤器实现
 * 
 * <AUTHOR>
 * @date 2025-05-28
 */
@Component
@Slf4j
public class CampaignPermissionFilter extends AbstractPermissionFilter {

    @Autowired
    private IAdProductRightService adProductRightService;
    
    @Override
    public PermissionFilterType getSupportedType() {
        return PermissionFilterType.CAMPAIGN;
    }
    
    @Override
    public boolean supports(PermissionFilterType filterType) {
        return PermissionFilterType.CAMPAIGN == filterType;
    }

    @Override
    public boolean isNeedFilter(PermissionContext context) {
        if (context.hasAdminPermission()) {
            return false;
        }
        // todo 未配置asin权限 则默认不过滤  这里最好是加载到缓存  不然每次都要查库判断
        return adProductRightService.checkUidNeedProductRight(context.getPuid(), context.getUid(), context.getShopIds(), context.getIsAdminUser());
    }

    @Override
    protected String doDorisGenerateFilterSql(String fieldName, PermissionContext context, List<Object> argsList) {
        try {
            // 获取店铺ID列表
            List<Integer> shopIds = context.getShopIds();

            // 解析广告类型
            List<CampaignTypeEnum> types = parseAdTypes(context.getAdType());

            // 调用AdProductRightService生成权限过滤SQL
            String permissionSql = adProductRightService.getProductRightCampaignIdsSql(
                    fieldName,
                    context.getPuid(),
                    context.getUid(),
                    context.getIsAdminUser() != null ? context.getIsAdminUser() : false,
                    shopIds,
                    types,
                    argsList
            );

            if (StringUtils.isNotBlank(permissionSql)) {
                log.debug("生成Campaign权限过滤SQL: field={}, shopIds={}, types={}",
                        fieldName, shopIds, types);
                return "and " + permissionSql;
            } else {
                log.debug("AdProductRightService返回空SQL，用户可能有默认权限");
                return "";
            }

        } catch (Exception e) {
            log.error("生成Campaign权限过滤SQL失败: field={}", fieldName, e);
            return "";
        }
    }

    @Override
    protected String doMysqlGenerateFilterSql(String fieldName, PermissionContext context, List<Object> argsList) {
        List<String> campaignIds = adProductRightService.getProductRightCampaignIds(
                context.getPuid(),
                context.getUid(),
                context.getIsAdminUser() != null ? context.getIsAdminUser() : false,
                context.getShopIds(),
                parseAdTypes(context.getAdType())
        );
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            return SqlStringUtil.dealInList(fieldName,campaignIds, argsList);
        }
        return "";
    }

    /**
     * 解析广告类型
     */
    private List<CampaignTypeEnum> parseAdTypes(List<String> adTypeList) {
        try {
            if (CollectionUtils.isEmpty(adTypeList)) {
                // 默认返回所有类型
                return Arrays.asList(CampaignTypeEnum.sp, CampaignTypeEnum.sb, CampaignTypeEnum.sd);
            }
            
            return adTypeList.stream()
                    .map(type -> {
                        try {
                            return CampaignTypeEnum.valueOf(type.toLowerCase());
                        } catch (Exception e) {
                            log.warn("未知的广告类型: {}", type);
                            return null;
                        }
                    })
                    .filter(java.util.Objects::nonNull)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("解析广告类型失败", e);
            return Arrays.asList(CampaignTypeEnum.sp, CampaignTypeEnum.sb, CampaignTypeEnum.sd);
        }
    }

    @Override
    public boolean hasPermission(PermissionContext context) {
        String campaignId = context.getCampaignId();
        if (StringUtils.isBlank(campaignId)) {
            return true;
        }
        List<String> campaignIds = adProductRightService.getProductRightCampaignIds(context.getPuid(), context.getUid(), context.getIsAdminUser(), context.getShopIds(), parseAdTypes(context.getAdType()));
        return CollectionUtils.isNotEmpty(campaignIds) && campaignIds.contains(campaignId);
    }
}
