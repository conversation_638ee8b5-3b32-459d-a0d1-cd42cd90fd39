package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import com.meiyunji.sponsored.service.multiPlatform.walmart.util.Constants;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2025/3/18 10:12
 * @describe:
 */
@Getter
public enum WalmartAdErrorMsgEnum {
    CAMPAIGN_NAME_EXIST(Constants.AD_CAMPAIGN_NAME, " Unique campaign name required.", "活动名称重复，请修改活动名称后再提交"),
    CAMPAIGN_ADTYPE_ERROR(Constants.AD_CAMPAIGN_ADTYPE, "campaign adType not exist", "广告类型错误"),
    CAMPAIGN_START_TIME_ERROR(Constants.AD_CAMPAIGN_START_TIME, "startTime - Start Time should be today or in future.", "开始时间最早仅支持选择当前时间-1天"),
    CAMPAIGN_TOTAL_BUDGET_OVER_LIMIT(Constants.AD_CAMPAIGN_TOTAL_BUDGET, "totalBudget - Total Budget cannot be more than $1000000000.0.", "总预算不能超过1000000000USD，请修改总预算"),
    CAMPAIGN_TOTAL_BUDGET_LESS_THAN_DAILY(Constants.AD_CAMPAIGN_TOTAL_BUDGET, "totalBudget - Total budget cannot be less than the Daily Budget.", "总预算不能低于每日预算"),
    CAMPAIGN_DAILY_BUDGET_OVER_LIMIT(Constants.AD_CAMPAIGN_TOTAL_BUDGET, "dailyBudget - Daily Budget cannot be more than $1000000000.0", "每日预算不能超过1000000000USD，请修改每日预算"),
    CAMPAIGN_TOTAL_BUDGET_LESS_THAN_ALLOWED(Constants.AD_CAMPAIGN_TOTAL_BUDGET, "totalBudget - Total Budget cannot be less than $100.0. ", "总预算不能低于$100"),
    CAMPAIGN_DAILY_BUDGET_LESS_THAN_ALLOWED(Constants.AD_CAMPAIGN_DAILY_BUDGET, "Daily Budget cannot be less than $50.0. ", "每日预算不能低于$50"),
    GROUP_NAME_DUPLICATE(Constants.AD_GROUP_NAME, "name - Ad Group name should be unique within a Campaign", "广告组名称不能重复"),
    SUGGEST_KEYWORD_FETCH_ERROR(Constants.AD_SUGGEST_KEYWORD, "Given ad group does not have any items. Please add items to get the keyword", "推荐关键词获取失败，请提交广告产品后再获取"),
    DELETE_ERROR(Constants.AD_DEFAULT, "cannot be deleted as it has been scheduled before", "当前状态不支持删除，可在活动结束后删除广告活动"),
    AD_BID(Constants.AD_DEFAULT, "Bid cannot be greater than 100.0", "竞价不能大于100 USD"),
    DELETE_STATUE_ERROR(Constants.AD_DEFAULT, "cannot be deleted as the campaign has been live before", "当前状态不支持删除，广告活动未结束"),
    ITEM_NOT_FOUND_ID(Constants.AD_ITEM, "Item not found, Input id: null", "无产品ID的产品不支持参加广告，请同步产品后再加入广告"),
    ITEM_NOT_OWNER_YOU(Constants.AD_ITEM, "item or base item not owned by you", "该广告产品不属于当前用户"),
    ITEM_NOT_FOUND(Constants.AD_ITEM, "Item not found", "广告产品不存在"),
    CAMPAIGN_CREATE_ERROR_DEFAULT(Constants.AD_CAMPAIGN, "camapign create error", "广告活动创建异常"),
    KEYWORD_LENGTH_OVER_LIMIT(Constants.AD_KEYWORD, "Keyword Text length greater than 80 / ", "关键词长度不能超过80")
    ;
    private String fieldCn;
    private String errorMsgEn;
    private String errorMsgCn;

    WalmartAdErrorMsgEnum(String fieldCn, String errorMsgEn, String errorMsgCn) {
        this.fieldCn = fieldCn;
        this.errorMsgEn = errorMsgEn;
        this.errorMsgCn = errorMsgCn;
    }

    public static Map<String, WalmartAdErrorMsgEnum> WALMART_AD_ERROR_MAP = Arrays.stream(WalmartAdErrorMsgEnum.values())
            .collect(Collectors.toMap(WalmartAdErrorMsgEnum::getErrorMsgEn, v1 -> v1, (old, current) -> current));
}
