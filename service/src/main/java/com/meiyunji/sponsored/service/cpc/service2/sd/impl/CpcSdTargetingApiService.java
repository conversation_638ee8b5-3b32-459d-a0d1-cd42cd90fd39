package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.mode.targeting.TargetingClauseResult;
import com.amazon.advertising.sd.entity.targeting.*;
import com.amazon.advertising.sd.mode.ExpressionNested;
import com.amazon.advertising.sd.mode.TargetingClause;
import com.amazon.advertising.spV3.targeting.entity.TargetExtendEntityV3;
import com.amazon.advertising.spV3.targeting.entity.TargetResolveExpression;
import com.amazon.advertising.spV3.targeting.entity.TargetSuccessResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.audiences.entity.AmazonAdAudience;
import com.meiyunji.sponsored.service.audiences.service.AmazonAudiencesService;
import com.meiyunji.sponsored.service.batchCreate.enums.KeywordAndTargetingExcludeNeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdTargetingDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.UpdateBatchTargetVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.AudienceCategoryTypeEnum;
import com.meiyunji.sponsored.service.enums.SdTacticTypeEnum;
import com.meiyunji.sponsored.service.enums.TargetingExpressionPredicate;
import com.meiyunji.sponsored.service.syncAd.enums.SdStateEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/7/7.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSdTargetingApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Autowired
    private AmazonAudiencesService amazonAudiencesService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IDorisService dorisService;

    public Result create(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdTargeting> amazonSdAdTargetings) {
        StopWatchUtil.start();
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        if (CollectionUtils.isEmpty(amazonSdAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<TargetingClause> targetingClauses = amazonSdAdTargetings.stream().map(e -> {
            TargetingClause targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(e.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(e.getAdGroupId()));
            }
            targetingClause.setExpressionType(e.getExpressionType());
            targetingClause.setState(e.getState());
            if (e.getBid() != null) {
                targetingClause.setBid(e.getBid().doubleValue());
            }
            if (StringUtils.isNotBlank(e.getExpression())) {
                targetingClause.setExpressions(JSONUtil.jsonToArray(e.getExpression(), ExpressionNested.class));
            }
            return targetingClause;
        }).collect(Collectors.toList());

        CreateTargetingClausesResponse response = cpcApiHelper.call(shop, () -> ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
                .createNew(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingClauses));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        StopWatchUtil.start();
        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<TargetingClauseResult> resultList = response.getResultList();
            int index = 0;
            for (TargetingClauseResult productAdResult : resultList) {
                if ("SUCCESS".equals(productAdResult.getCode())) {
                    amazonSdAdTargetings.get(index).setTargetId(String.valueOf(productAdResult.getTargetId()));
                } else {
                    amazonSdAdTargetings.get(index).setErrMsg(AmazonErrorUtils.getError(StringUtils.isNotBlank(productAdResult.getDetails())
                            ? productAdResult.getDetails() : productAdResult.getDescription()));
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SD广告权限，请到Amazon后台开通SD广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
            }
        }
        return ResultUtil.error(errMsg);
    }

    public Result update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonSdAdTargeting> amazonSdAdTargetings) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        if (CollectionUtils.isEmpty(amazonSdAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        List<TargetingClause> targetingClauses = amazonSdAdTargetings.stream().map(e -> {
            TargetingClause targetingClause = new TargetingClause();
            if (StringUtils.isNotBlank(e.getTargetId())) {
                targetingClause.setTargetId(Long.valueOf(e.getTargetId()));
            }
            targetingClause.setState(e.getState());
            if (e.getBid() != null) {
                targetingClause.setBid(e.getBid().doubleValue());
            }
            return targetingClause;
        }).collect(Collectors.toList());

        UpdateTargetingClausesResponse response = cpcApiHelper.call(shop, () -> ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingClauses));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            List<TargetingClauseResult> resultList = response.getResultList();
            int index = 0;
            for (TargetingClauseResult result : resultList) {
                if ("SUCCESS".equals(result.getCode())) {
                    amazonSdAdTargetings.get(index).setApiSucc(true);
                } else {
                    amazonSdAdTargetings.get(index).setApiSucc(false);
                    amazonSdAdTargetings.get(index).setErrMsg(AmazonErrorUtils.getError(StringUtils.isNotBlank(result.getDetails())
                            ? result.getDetails() : result.getDescription()));
                }
                index++;
            }

            return ResultUtil.success();
        } else if (response.getError() != null) {
            errMsg = AmazonErrorUtils.getError(response.getError().getDetails());
        }

        return ResultUtil.error(errMsg);
    }

    /**
     * 同步所有的投放
     *
     * @param shop：
     */
    public void syncTargetings(ShopAuth shop, String campaignId) {
        syncTargetings(shop, campaignId, null, null, false);
    }

    public void syncTargetingsByGroupId(ShopAuth shop, String adGroupId) {
        syncTargetings(shop, null, adGroupId, null, false);
    }

    public void syncByTargetId(ShopAuth shop, String targetId) {
        syncTargetings(shop, null, null, targetId, false);
    }

    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, boolean nullResThrowException) {
        syncTargetings(shop, campaignId, groupId, targetId, null, nullResThrowException);
    }

    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<SdStateEnum> stateList, boolean nullResThrowException) {
        syncTargetings(shop, campaignId, groupId, targetId, stateList, nullResThrowException, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String groupIds){
        List<AmazonSdAdTargeting> amazons = amazonSdAdTargetingDao.listByTargetId(puid, shopId, StringUtil.stringToList(groupIds, ","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                    key.setServingStatus(key.getServingStatus());
                    return AmazonServingStatusDto.build(key.getTargetId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());
                }
        ).collect(Collectors.toList());
    }

    /**
     * 同步所有的投放
     *
     * @param shop：
     */
    public void syncTargetings(ShopAuth shop, String campaignId, String groupId, String targetId, List<SdStateEnum> stateList, boolean nullResThrowException, boolean isProxy) {
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdGroup--配置信息为空");
            return;
        }

        Map<String, AudienceCategoryTypeEnum> audienceMap = amazonAudiencesService.listAll().stream()
                .collect(Collectors.toMap(AmazonAdAudience::getAudienceName, audience -> AudienceCategoryTypeEnum.fromValueOrDefault(audience.getCategory()), (v1, v2) -> v2));

        ProductTargetingClient client = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = ProductTargetingClient.getInstance(true);
        }
        int startIndex = 0;
        int count = 500;
        ListTargetingClauseExResponse response;

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(SdStateEnum.values()).map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        }

        while (true) {
            int finalSartIndex = startIndex;
            ProductTargetingClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, stateFilter, campaignId, groupId, targetId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD targeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                ProductTargetingClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, stateFilter, campaignId, groupId, targetId));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && nullResThrowException) {
                throw new ServiceException("sd syncTargetings error");
            }

            if (response == null || CollectionUtils.isEmpty(response.getTargetingClauseList())) {
                break;
            }

            int size = response.getTargetingClauseList().size();
            AmazonSdAdTargeting amazonSdAdTargeting;
            List<AmazonSdAdTargeting> amazonSdAdTargetings= new ArrayList<>(size);
            for (TargetingClause targetingClause : response.getTargetingClauseList()) {
                amazonSdAdTargeting = turnEntityToPO(targetingClause, audienceMap);
                if (StringUtils.isNotBlank(amazonSdAdTargeting.getTargetId())) {
                    amazonSdAdTargeting.setPuid(shop.getPuid());
                    amazonSdAdTargeting.setShopId(shop.getId());
                    amazonSdAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonSdAdTargeting.setProfileId(amazonAdProfile.getProfileId());
                    amazonSdAdTargetings.add(amazonSdAdTargeting);
                }
            }

            if (amazonSdAdTargetings.size() > 0) {
                Map<String, AmazonSdAdTargeting> groupMap = amazonSdAdTargetingDao.listByTargetId(shop.getPuid(), shop.getId(),
                        amazonSdAdTargetings.stream().map(AmazonSdAdTargeting::getTargetId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity()));

                List<AmazonSdAdTargeting> insertList = new ArrayList<>();
                List<AmazonSdAdTargeting> updateList = new ArrayList<>();
                AmazonSdAdTargeting old;

                for (AmazonSdAdTargeting c : amazonSdAdTargetings) {
                    if (groupMap.containsKey(c.getTargetId())) {
                        old = groupMap.get(c.getTargetId());
                        if (StringUtils.isNotBlank(c.getState())) {
                            old.setState(c.getState());
                        }
                        if (StringUtils.isNotBlank(c.getType())) {
                            old.setType(c.getType());
                        }
                        if (StringUtils.isNotBlank(c.getTacticType())) {
                            old.setTacticType(c.getTacticType());
                        }
                        if (StringUtils.isNotBlank(c.getTargetText())) {
                            old.setTargetText(c.getTargetText());
                        }
                        if (c.getBid() != null) {
                            old.setBid(c.getBid());
                        }
                        if (StringUtils.isNotBlank(c.getExpressionType())) {
                            old.setExpressionType(c.getExpressionType());
                        }
                        if (StringUtils.isNotBlank(c.getExpression())) {
                            old.setExpression(c.getExpression());
                        }
                        if (StringUtils.isNotBlank(c.getResolvedExpression())) {
                            old.setResolvedExpression(c.getResolvedExpression());
                        }
                        if (StringUtils.isNotBlank(c.getServingStatus())) {
                            old.setServingStatus(c.getServingStatus());
                        }
                        if (c.getCreationDate() != null) {
                            old.setCreationDate(c.getCreationDate());
                        }
                        if (c.getLastUpdatedDate() != null) {
                            old.setLastUpdatedDate(c.getLastUpdatedDate());
                        }
                        if (StringUtils.isNotBlank(c.getTargetType())) {
                            old.setTargetType(c.getTargetType());
                        }
                        updateList.add(old);
                    } else {
                        c.setCreateInAmzup(0);
                        insertList.add(c);
                    }
                }

                try {
                    amazonSdAdTargetingDao.batchAdd(shop.getPuid(), insertList);
                    amazonSdAdTargetingDao.batchUpdate(shop.getPuid(), updateList);
                } catch (Exception e) {
                    log.error("syncSdTargeting:", e);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
    }

    /**
     * 归档
     *
     * @param amazonSdAdTargeting：
     * @return ：Result
     */
    public Result archive(AmazonSdAdTargeting amazonSdAdTargeting) {
        if (amazonSdAdTargeting == null) {
            return ResultUtil.error("没有投放定位信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonSdAdTargeting.getShopId(), amazonSdAdTargeting.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        ArchiveTargetingClauseResponse response = cpcApiHelper.call(shop, () -> ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop),
                amazonSdAdTargeting.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonSdAdTargeting.getTargetId())));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getTargetId() != null) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            msg = AmazonErrorUtils.getError(response.getResult().getDetails());
        }
        return ResultUtil.error(msg);
    }

    // 把接口返回的dto转换成po
    private AmazonSdAdTargeting turnEntityToPO(TargetingClause targetingClause, Map<String, AudienceCategoryTypeEnum> audienceMap) {
        AmazonSdAdTargeting amazonSdAdTargeting = new AmazonSdAdTargeting();
        if (targetingClause.getCampaignId() != null) {
            amazonSdAdTargeting.setCampaignId(targetingClause.getCampaignId().toString());
        }
        if (targetingClause.getAdGroupId() != null) {
            amazonSdAdTargeting.setAdGroupId(targetingClause.getAdGroupId().toString());
        }
        if (targetingClause.getTargetId() != null) {
            amazonSdAdTargeting.setTargetId(targetingClause.getTargetId().toString());
        }
        amazonSdAdTargeting.setState(targetingClause.getState());
        if (targetingClause.getBid() != null) {
            amazonSdAdTargeting.setBid(BigDecimal.valueOf(targetingClause.getBid()));
        }

        if (CollectionUtils.isNotEmpty(targetingClause.getExpressions())) {
            amazonSdAdTargeting.setExpression(JSONUtil.objectToJson(targetingClause.getExpressions()));
            amazonSdAdTargeting.setTargetType(targetingClause.getExpressions().get(0).getType());
        }

        // 确定投放的类型
        List<ExpressionNested> resolvedExpressions = targetingClause.getResolvedExpressions();
        if (CollectionUtils.isNotEmpty(resolvedExpressions)) {
            amazonSdAdTargeting.setResolvedExpression(JSONUtil.objectToJson(resolvedExpressions));
            if (KeywordAndTargetingExcludeNeEnum.sdAudienceTargetSet.contains(resolvedExpressions.get(0).getType())) {
                amazonSdAdTargeting.setTacticType(SdTacticTypeEnum.audienceTarget.getCode());
            } else {
                amazonSdAdTargeting.setTacticType(SdTacticTypeEnum.productTarget.getCode());
            }
            for (ExpressionNested expressionNested : targetingClause.getResolvedExpressions()) {
                // tactic是T00030时，是一个内嵌的expression
                if (expressionNested.getValue() instanceof List) {
                    List<Expression> expressions = JSONUtil.jsonToArray(JSONUtil.objectToJson(expressionNested.getValue()), Expression.class);
                    if (CollectionUtils.isNotEmpty(expressions)) {
                        for (Expression expression : expressions) {
                            if (ExpressionEnum.asinSameAs.value().equalsIgnoreCase(expression.getType())) {
                                amazonSdAdTargeting.setType(SdTargetTypeEnum.asin.getValue());
                                amazonSdAdTargeting.setTargetText(expression.getValue());
                                break;
                            }
                            if (ExpressionEnum.asinCategorySameAs.value().equalsIgnoreCase(expression.getType())) {
                                amazonSdAdTargeting.setType(SdTargetTypeEnum.category.getValue());
                                amazonSdAdTargeting.setTargetText(expression.getValue());
                                break;
                            }
                            if (ExpressionEnum.exactProduct.value().equalsIgnoreCase(expression.getType())) {
                                amazonSdAdTargeting.setType(SdTargetTypeEnum.exactProduct.getValue());
                                amazonSdAdTargeting.setTargetText(SdTargetTypeEnum.exactProduct.getDesc());
                                break;
                            }
                            if (ExpressionEnum.similarProduct.value().equalsIgnoreCase(expression.getType())) {
                                amazonSdAdTargeting.setType(SdTargetTypeEnum.similarProduct.getValue());
                                amazonSdAdTargeting.setTargetText(SdTargetTypeEnum.similarProduct.getDesc());
                                break;
                            }
                            if (ExpressionEnum.relatedProduct.value().equalsIgnoreCase(expression.getType())) {
                                amazonSdAdTargeting.setType(SdTargetTypeEnum.relatedProduct.getValue());
                                amazonSdAdTargeting.setTargetText(SdTargetTypeEnum.relatedProduct.getDesc());
                                break;
                            }
                            if (ExpressionEnum.audienceSameAs.value().equalsIgnoreCase(expression.getType())) {
                                String audienceName = expression.getValue();
                                AudienceCategoryTypeEnum categoryType = audienceMap.getOrDefault(audienceName, AudienceCategoryTypeEnum.DEFAULT);
                                audienceName = categoryType.getDesc() + ": " + audienceName;
                                amazonSdAdTargeting.setType(categoryType.getType());
                                amazonSdAdTargeting.setTargetText(audienceName);
                                break;
                            }

                            // 默认只取第一个，防止后面覆盖前面
                            if (StringUtils.isNotBlank(amazonSdAdTargeting.getType())) {
                                amazonSdAdTargeting.setType(expression.getType());
                                amazonSdAdTargeting.setTargetText(expression.getType());
                            }
                        }
                    }
                } else { // tactic是T00020时, 是一个普通的expression
                    if (ExpressionEnum.asinSameAs.value().equalsIgnoreCase(expressionNested.getType())) {
                        amazonSdAdTargeting.setType(SdTargetTypeEnum.asin.getValue());
                        amazonSdAdTargeting.setTargetText(expressionNested.getValue() != null ? expressionNested.getValue().toString() : null);
                        break;
                    }
                    if (ExpressionEnum.asinCategorySameAs.value().equalsIgnoreCase(expressionNested.getType())) {
                        amazonSdAdTargeting.setType(SdTargetTypeEnum.category.getValue());
                        amazonSdAdTargeting.setTargetText(expressionNested.getValue() != null ? expressionNested.getValue().toString() : null);
                        break;
                    }
                    if (ExpressionEnum.similarProduct.value().equalsIgnoreCase(expressionNested.getType())) {
                        amazonSdAdTargeting.setType(SdTargetTypeEnum.similarProduct.getValue());
                        amazonSdAdTargeting.setTargetText(SdTargetTypeEnum.similarProduct.getDesc());
                        break;
                    }
                    if (ExpressionEnum.contentCategory.value().equalsIgnoreCase(expressionNested.getType())) {
                        amazonSdAdTargeting.setType(SdTargetTypeEnum.contentCategory.getValue());
                        amazonSdAdTargeting.setTargetText(Optional.ofNullable(expressionNested.getValue()).map(String::valueOf).orElse(""));
                        break;
                    }
                    amazonSdAdTargeting.setType(expressionNested.getType());
                    amazonSdAdTargeting.setTargetText(expressionNested.getType());
                }
            }
        }

        amazonSdAdTargeting.setServingStatus(targetingClause.getServingStatus());

        if (StringUtils.isNotBlank(targetingClause.getCreationDate())) {
            amazonSdAdTargeting.setCreationDate(DateUtil.getDateByMillisecond(Long.valueOf(targetingClause.getCreationDate())));
        }
        if (StringUtils.isNotBlank(targetingClause.getLastUpdatedDate())) {
            amazonSdAdTargeting.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(targetingClause.getLastUpdatedDate())));
        }

        return amazonSdAdTargeting;
    }

    /**
     * 修改
     *
     * @param amazonAdTargetings:
     * @return :
     */
    public Result<BatchResponseVo<UpdateBatchTargetVo, AmazonSdAdTargeting>> update(ShopAuth shop,AmazonAdProfile amazonAdProfile,List<AmazonSdAdTargeting> amazonAdTargetings , String type) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误");
        }



        List<TargetingClause> targetingList = makeTargetings(amazonAdTargetings,type);
        UpdateTargetingClausesResponse response = cpcApiHelper.call(shop, () -> ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), targetingList));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        Map<String, AmazonSdAdTargeting> amazonAdTargetingMap = amazonAdTargetings.stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, e -> e));
        BatchResponseVo<UpdateBatchTargetVo,AmazonSdAdTargeting> batchResponseVo = new BatchResponseVo<>();
        List<UpdateBatchTargetVo> errorList = Lists.newArrayList();
        List<AmazonSdAdTargeting> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getResultList() != null && response.getResultList().size() > 0) {

            List<TargetingClauseResult> resultList = response.getResultList();
            List<Long> successId = Lists.newArrayList();
            for (TargetingClauseResult targetingClauseResult : resultList) {

                if ("SUCCESS".equals(targetingClauseResult.getCode())) {
                    AmazonSdAdTargeting amazonAdTargetingSuccess = amazonAdTargetingMap.remove(String.valueOf(targetingClauseResult.getTargetId()));
                    if (amazonAdTargetingSuccess != null) {
                        successList.add(amazonAdTargetingSuccess);
                    }
                    successId.add(amazonAdTargetingSuccess.getId());

                } else {
                    AmazonSdAdTargeting amazonAdTargetingFail = amazonAdTargetingMap.remove(String.valueOf(targetingClauseResult.getTargetId()));
                    if (amazonAdTargetingFail != null) {
                        UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                        spKeywordsVoError.setId(amazonAdTargetingFail.getId());
                        spKeywordsVoError.setTargetId(amazonAdTargetingFail.getTargetId());
                        //更新失败数据处理
                        if (StringUtils.isNotBlank(targetingClauseResult.getDescription())) {
                            spKeywordsVoError.setFailReason(AmazonErrorUtils.getError(targetingClauseResult.getDescription()));
                        } else {
                            spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(spKeywordsVoError);
                    }

                }
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spKeywordsVoError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdTargetingMap)) {
                amazonAdTargetingMap.forEach((k, v) -> {
                    UpdateBatchTargetVo spKeywordsVoError = new UpdateBatchTargetVo();
                    spKeywordsVoError.setId(v.getId());
                    spKeywordsVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(spKeywordsVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdTargetings.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    /**
     * 同步所有的产品广告
     *
     * @param shop：
     */
    public List<AmazonSdAdTargeting> syncTargeting(ShopAuth shop, String targetId) {
        List<AmazonSdAdTargeting> amazonSdAdTargetings= new ArrayList<>();
        if (shop == null) {
            return amazonSdAdTargetings;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdGroup--配置信息为空");
            return amazonSdAdTargetings;
        }

        Map<String, AudienceCategoryTypeEnum> audienceMap = amazonAudiencesService.listAll().stream()
                .collect(Collectors.toMap(AmazonAdAudience::getAudienceName, audience -> AudienceCategoryTypeEnum.fromValueOrDefault(audience.getCategory()), (v1, v2) -> v2));

        ProductTargetingClient client = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 500;
        ListTargetingClauseExResponse response;

        while (true) {
            int finalSartIndex = startIndex;
            response = cpcApiHelper.call(shop, () -> client.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, null, null, null, targetId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD targeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = cpcApiHelper.call(shop, () -> client.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, null, null, null, targetId));
                retry++;
            }
            if (response == null || CollectionUtils.isEmpty(response.getTargetingClauseList())) {
                break;
            }

            int size = response.getTargetingClauseList().size();
            AmazonSdAdTargeting amazonSdAdTargeting;
            for (TargetingClause targetingClause : response.getTargetingClauseList()) {
                amazonSdAdTargeting = turnEntityToPO(targetingClause, audienceMap);
                if (StringUtils.isNotBlank(amazonSdAdTargeting.getTargetId())) {
                    amazonSdAdTargeting.setPuid(shop.getPuid());
                    amazonSdAdTargeting.setShopId(shop.getId());
                    amazonSdAdTargeting.setMarketplaceId(shop.getMarketplaceId());
                    amazonSdAdTargeting.setProfileId(amazonAdProfile.getProfileId());
                    amazonSdAdTargetings.add(amazonSdAdTargeting);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
        return amazonSdAdTargetings;
    }

    private List<TargetingClause> makeTargetings(List<AmazonSdAdTargeting> amazonAdTargetingList, String type) {
        List<TargetingClause> list = Lists.newArrayListWithCapacity(amazonAdTargetingList.size());

        for (AmazonSdAdTargeting amazonAdTargeting : amazonAdTargetingList) {
            TargetingClause targetingClause = new TargetingClause();

            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(Long.valueOf(amazonAdTargeting.getTargetId()));
            }

            if (StringUtils.isNotBlank(amazonAdTargeting.getAdGroupId())) {
                targetingClause.setAdGroupId(Long.valueOf(amazonAdTargeting.getAdGroupId()));
            }

            if(Constants.CPC_SP_TARGET_BATCH_UPDATE_BID.equals(type)){
                targetingClause.setBid(amazonAdTargeting.getBid().doubleValue());
            }
            if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                targetingClause.setState(amazonAdTargeting.getState());
            }
            list.add(targetingClause);
        }
        return list;
    }
}
