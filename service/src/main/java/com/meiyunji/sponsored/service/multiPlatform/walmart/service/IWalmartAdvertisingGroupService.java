package com.meiyunji.sponsored.service.multiPlatform.walmart.service;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingGroup;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdCampaignCreateTogetherResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.*;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;

import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingGroupService {


    void add(WalmartAdvertisingGroup group);

    /**
     * 删除
     */
    Boolean deleteByAdGroupId(Integer puid, Integer shopId, String adGroupId);

    /**
     * 更新
     */
    int update(WalmartAdvertisingGroup group);

    /**
     * 根据主键 id 查询
     */
    WalmartAdGroupPageDTO getByAdGroupId(Integer puid, Integer shopId, String adGroupId);

    Page<WalmartAdGroupPageDTO> getPageList(WalmartAdGroupVo reqVo);

    WalmartAdGroupPageDTO getAggregate(WalmartAdGroupVo reqVo);

    int syncGroupByCampaigns(Integer puid, List<Integer> shopList,
                             WalmartAdvertiserClient advertiserClient, List<WalmartAdvertisingCampaign> campaigns) throws ServiceException;

    WalmartAdvertisingGroup getByGroupId(Integer puid, Integer shopId, String campaignId, String groupId);

    int deleteByCampaignId(Integer puid, Integer shopId, String campaignId);

    /**
     * 根据id获取广告组名称，方便用于列表页反查
     * @param puid
     * @param shopIds
     * @return
     */
    Map<String, String> getGroupNameMap(Integer puid, List<Integer> shopIds, List<String> groupIds);

    WalmartAdCampaignCreateTogetherResp createGroup(WalmartAdGroupCreateTogetherVo req) throws ServiceException;

    WalmartAdGroupPageDTO updateGroupByAdGroupId(Integer puid, Integer shopId, WalmartAdGroupPageDTO reqVo);
    WalmartAdGroupPageDTO updateGroupNameOrStatus(Integer puid, Integer shopId, WalmartAdGroupPageDTO reqVo);

    Page<WalmartAdGroupPageDTO> getAllListByShopId(WalmartGroupGetAllReq req);

}
