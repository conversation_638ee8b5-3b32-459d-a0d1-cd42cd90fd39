package com.meiyunji.sponsored.service.productPerspectiveAnalysis.handler;

import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.bo.AmazonSdAdCampaignCostTypeBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSdAdGroupDao;
import com.meiyunji.sponsored.service.cpc.dto.ProductPerspectiveAnalysisFilterDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdGroupSdDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdProductReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSdProductReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonSbAdsDao;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-08-21  16:09
 */
@Component
public class ViewManageHandler {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService CpCShopDataService;
    @Autowired
    private IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    @Autowired
    private IOdsAmazonSbAdsDao odsAmazonSbAdsDao;
    @Autowired
    private IOdsAmazonAdSdProductReportDao odsAmazonAdSdProductReportDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;

    public void fillShopSellerId(Integer puid, ViewBaseParam param) {
        //获取店铺
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuthList)) {
            AssertUtil.fail("获取店铺异常，请联系管理员");
        }
        param.setShopIdList(shopAuthList.stream().map(ShopAuth::getId).collect(Collectors.toList()));
        param.setSellerIdList(shopAuthList.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList()));
    }

    /**
     * 填充店铺销售额
     */
    public void fillShopSale(Integer puid, ViewBaseParam param) {
        //获取店铺销售额
        List<ShopSaleDto> shopSaleDtoList = CpCShopDataService.getListShopSale(puid, param.getShopIdList(), param.getStartDate(), param.getEndDate());
        BigDecimal shopSales = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(shopSaleDtoList)) {
            shopSales = shopSaleDtoList.stream().filter(item -> item != null && item.getSumRange() != null).map(ShopSaleDto::getSumRange).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        param.setShopSales(shopSales);
    }

    public List<AmazonAdProductPerspectiveBO> queryProductBoLists(Integer puid, ViewBaseParam param) {
        List<AmazonAdProductPerspectiveBO> productBoList = new ArrayList<>();
        List<String> types = StringUtil.splitStr(param.getType());
        if (types.contains(Constants.SP) || types.contains(Constants.SP.toUpperCase())) {
            productBoList.addAll(odsAmazonAdProductReportDao.productPerspectiveBoListByProduct(puid, param.getShopIdList(), param.getMarketplaceId(),
                    param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        } else if (types.contains(Constants.SB) || types.contains(Constants.SB.toUpperCase())) {
            productBoList.addAll(odsAmazonSbAdsDao.productPerspectiveBoListByProduct(puid, param.getShopIdList(), param.getMarketplaceId(),
                    param.getSearchType(), param.getSearchValue()));
        } else if (types.contains(Constants.SD) || types.contains(Constants.SD.toUpperCase())) {
            productBoList.addAll(odsAmazonAdSdProductReportDao.productPerspectiveBoListByProduct(puid, param.getShopIdList(), param.getMarketplaceId(),
                    param.getSearchType(), param.getSearchValue(), param.getStartDate(), param.getEndDate()));
        }
        return productBoList;
    }

    /**
     * 填充投放的类型，cpc或vcpm，通过投放id->广告组id->活动id->活动costType，后面投放表有广告活动id可以优化
     * @param puid
     * @param shopIdList
     * @param targetGroupIdMap
     * @param itemList
     */
    public void fillFeedSdType(Integer puid, List<Integer> shopIdList, Map<String, String> targetGroupIdMap, List<AmazonMarketingStreamData> itemList) {
        if (MapUtils.isEmpty(targetGroupIdMap) || CollectionUtils.isEmpty(itemList)) {
            return;
        }
        Map<String, String> groupCampaignIdMap = amazonSdAdGroupDao.getGroupByShopIdsAndGroupIds(puid, shopIdList, new ArrayList<>(targetGroupIdMap.values()))
                .stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getCampaignId));
        List<AmazonSdAdCampaignCostTypeBo> campaignBoList = amazonAdCampaignAllDao.listSdCostTypeBoByCampaignIds(puid, shopIdList, new ArrayList<>(groupCampaignIdMap.values()));
        if (CollectionUtils.isEmpty(campaignBoList)) {
            return;
        }
        Map<String, String> campaignIdCostTypeMap = campaignBoList.stream().collect(Collectors.toMap(AmazonSdAdCampaignCostTypeBo::getCampaignId, AmazonSdAdCampaignCostTypeBo::getCostType));
        itemList.forEach(e -> {
            if (Constants.SD.equalsIgnoreCase(e.getType()) && targetGroupIdMap.containsKey(e.getKeywordId())) {
                String adGroupId = targetGroupIdMap.get(e.getKeywordId());
                if (groupCampaignIdMap.containsKey(adGroupId)) {
                    String campaignId = groupCampaignIdMap.get(adGroupId);
                    if (campaignIdCostTypeMap.containsKey(campaignId)) {
                        e.setSdType(campaignIdCostTypeMap.get(campaignId));
                    }
                }
            }
        });
    }

    /**
     * sd本产品广告订单和其他产品广告订单量内存过滤
     */
    public void sdFilterItemList(ProductPerspectiveAnalysisFilterDto advance, List<AmazonMarketingStreamData> itemList) {
        //计算本产品广告订单量和其他产品广告订单量
        itemList.forEach(e -> {
            //sd的vcpm类型没有本产品广告订单量和其他广告产品订单量
            if (Constants.SD_REPORT_VCPM.equalsIgnoreCase(e.getSdType())) {
                e.setAttributedConversions7dSameSku(0);
                e.setAdOtherOrderNum(0);
            } else {
                e.setAdOtherOrderNum(MathUtil.subtractInteger(e.getAttributedConversions7d(), e.getAttributedConversions7dSameSku()));
            }
        });
        //内存过滤
        Iterator<AmazonMarketingStreamData> itemListIterator = itemList.iterator();
        while (itemListIterator.hasNext()) {
            AmazonMarketingStreamData item = itemListIterator.next();
            if (Constants.SD.equalsIgnoreCase(item.getType())) {
                if (advance.getAdSaleNumMin() != null && item.getAttributedConversions7dSameSku() < advance.getAdSaleNumMin()) {
                    itemListIterator.remove();
                    continue;
                }
                if (advance.getAdSaleNumMax() != null && item.getAttributedConversions7dSameSku() > advance.getAdSaleNumMax()) {
                    itemListIterator.remove();
                    continue;
                }
                if (advance.getAdOtherOrderNumMin() != null && item.getAdOtherOrderNum() < advance.getAdOtherOrderNumMin()) {
                    itemListIterator.remove();
                    continue;
                }
                if (advance.getAdOtherOrderNumMax() != null && item.getAdOtherOrderNum() > advance.getAdOtherOrderNumMax()) {
                    itemListIterator.remove();
                }
            }
        }
    }

}
