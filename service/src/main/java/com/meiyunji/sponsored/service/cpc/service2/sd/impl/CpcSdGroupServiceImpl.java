package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.sd.campaign.CreativesInfoResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroupSd;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.SDCreateErrorEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> on 2021/7/6
 */
@Service
@Slf4j
public class CpcSdGroupServiceImpl implements ICpcSdGroupService {

    @Autowired
    private IAmazonSdAdCampaignDao amazonSdAdCampaignDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdSdGroupReportDao amazonAdSdGroupReportDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private IAmazonSdAdProductDao amazonSdAdProductDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSdGroupApiService cpcSdGroupApiService;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private IAdManageOperationLogService operationAdGroupLogService;
    @Autowired
    private IAmazonAdSdCreativeDao amazonAdSdCreativeDao;

    @Override
    public Result<Page<GroupPageVo>> pageList(GroupPageParam param) {
        Page<GroupPageVo> voPage = new Page<>();
        AmazonAdCampaignAll amazonAdCampaign = amazonSdAdCampaignDao.getByCampaignId(param.getPuid(), param.getShopId(), param.getCampaignId());

        if (amazonAdCampaign != null) {
            // 是否需要按广告表现指标排序, 需要的要取满足过滤条件的所有活动，排序后在用程序分页；
            // 不需要的直接走数据库分页
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
            List<AmazonSdAdGroup> poList;
            if (isSorted) {
                poList = amazonSdAdGroupDao.listByCondition(param.getPuid(), param);

                voPage.setPageNo(param.getPageNo());
                voPage.setPageSize(param.getPageSize());
            } else {
                Page<AmazonSdAdGroup> page = amazonSdAdGroupDao.pageList(param.getPuid(), param);
                BeanUtils.copyProperties(page, voPage);
                poList = page.getRows();
            }

            if (CollectionUtils.isNotEmpty(poList)) {
                List<String> adGroupIds = poList.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());
                List<String> status = Lists.newArrayList(CpcStatusEnum.enabled.name());

                // 广告组下的产品开启的投放数
                Map<String, Integer> targetCountMap = amazonSdAdTargetingDao.statCountByAdGroup(param.getPuid(), param.getShopId(), status, adGroupIds);

                // 广告组下的开启的广告数
                Map<String, Integer> adCountMap = amazonSdAdProductDao.statCountByAdGroup(param.getPuid(), param.getShopId(), status, adGroupIds);

                Map<Integer, User> userMap = userDao.listByPuid(param.getPuid()).stream().collect(Collectors.toMap(User::getId, e -> e));

                // 按活动分组获取活动的汇总数据
                List<AmazonAdSdGroupReport> groupReports = amazonAdSdGroupReportDao.listSumReports(param.getPuid(), param.getShopId(), param.getStartDate(), param.getEndDate(),
                            poList.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList()));

                Map<String, ReportBase> groupReportMap = groupReports.stream()
                        .collect(Collectors.toMap(AmazonAdSdGroupReport::getAdGroupId, AmazonAdSdGroupReport::getReportBase));

                // 取店铺销售额
                String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
                String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

                ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

                List<GroupPageVo> voList = new ArrayList<>(poList.size());
                voPage.setRows(voList);
                GroupPageVo vo;
                for (AmazonSdAdGroup amazonAdGroup : poList) {
                    vo = new GroupPageVo();
                    voList.add(vo);
                    convertPoToPageVo(amazonAdGroup, vo);

                    // 广告组下的产品投放数
                    if (targetCountMap.containsKey(amazonAdGroup.getAdGroupId())) {
                        vo.setTargetingNum(targetCountMap.get(amazonAdGroup.getAdGroupId()));
                    } else {
                        vo.setTargetingNum(0);
                    }

                    // 广告组下的广告数
                    if (adCountMap.containsKey(amazonAdGroup.getAdGroupId())) {
                        vo.setAdProductNum(adCountMap.get(amazonAdGroup.getAdGroupId()));
                    } else {
                        vo.setAdProductNum(0);
                    }

                    // 创建人
                    if (userMap.containsKey(amazonAdGroup.getCreateId())) {
                        User user = userMap.get(amazonAdGroup.getCreateId());
                        if (StringUtils.isNotBlank(user.getNickname())) {
                            vo.setCreator(user.getNickname());
                        }
                    }

                    // 填充报告数据
                    cpcCommService.fillReportDataIntoPageVo(vo, groupReportMap.get(amazonAdGroup.getAdGroupId()), shopSaleDto);
                }

                // 需要程序排序的
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                    PageUtil.getPage(voPage, voList);
                }
            }
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result showGroupPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getGroupId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonSdAdGroup amazonAdGroup = amazonSdAdGroupDao.getByGroupId(puid, param.getShopId(), param.getGroupId());
        if (amazonAdGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdGroup.getShopId());
        adPerformanceVo.setCampaignId(amazonAdGroup.getCampaignId());
        adPerformanceVo.setGroupId(amazonAdGroup.getAdGroupId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdSdGroupReport> reports = amazonAdSdGroupReportDao.listReports(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param.getGroupId());
        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));

            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e.getReportBase(), shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public Result<String> createAdGroup(SPadGroupVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //判断活动名称是否存在
        if (amazonSdAdGroupDao.exist(puid, shopId, vo.getCampaignId(), vo.getName().trim())) {
            return ResultUtil.returnErr("名称已存在");
        }

        AmazonAdCampaignAll amazonSdAdCampaign = amazonSdAdCampaignDao.getByCampaignId(puid, shopId, vo.getCampaignId());
        if (amazonSdAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        // 展示型广告的默认竞价必须少于预算金额的一半
        if (vo.getDefaultBid() * 2 >= amazonSdAdCampaign.getBudget().doubleValue()) {
            return ResultUtil.returnErr("展示型广告的默认竞价必须少于预算金额的一半");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSdAdGroup amazonSdAdGroup = convertVoToCreatePo(vo, amazonSdAdCampaign);

        Result result = cpcSdGroupApiService.create(shop, profile, amazonSdAdGroup);
        if (!result.success()) {
            return ResultUtil.returnErr(result.getMsg());
        }
        //TODO 外层同步广告组产品需要用到的参数
        vo.setGroupId(amazonSdAdGroup.getAdGroupId());
        // 入库
        try {
            amazonSdAdGroupDao.save(puid, amazonSdAdGroup);
            //写入doris
            saveDoris(Collections.singletonList(amazonSdAdGroup), true, true);
            /**
             * 广告组增加日志
             * 操作类型：新增广告组
             * 逻辑：调用新增广告日志方法，传空对象作为旧值
             * start
             */
            List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
            AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdSdGroupLog(null, amazonSdAdGroup);
            adManageOperationLog.setIp(vo.getLoginIp());
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLogs.add(adManageOperationLog);
            adManageOperationLog.setAdGroupId(amazonSdAdGroup.getAdGroupId());
            operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
            return ResultUtil.returnSucc(amazonSdAdGroup.getAdGroupId());
        } catch (Exception e) {
            log.error("createSdCampaign:", e);
        }
        return ResultUtil.returnErr("创建广告组失败");
    }

    @Override
    public NewCreateResultResultVo createAdGroup(AmazonSdAdGroup vo, ShopAuth shop,
                                                 AmazonAdProfile profile, String loginIp) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        //判断活动名称是否存在
        if (amazonSdAdGroupDao.exist(puid, shopId, vo.getCampaignId(), vo.getName().trim())) {
            throw new ServiceException(SDCreateErrorEnum.AD_GROUP_NAME_EXISTS.getMsg());
        }

        AmazonAdCampaignAll amazonSdAdCampaign = amazonSdAdCampaignDao.getByCampaignId(puid, shopId, vo.getCampaignId());
        if (amazonSdAdCampaign == null) {
            throw new ServiceException(SDCreateErrorEnum.CAMPAIGN_NOT_EXIST.getMsg());
        }

        // 展示型广告的默认竞价必须少于预算金额的一半
        if (vo.getDefaultBid().intValue() * 2 >= amazonSdAdCampaign.getBudget().doubleValue()) {
            throw new ServiceException(SDCreateErrorEnum.ADGROUP_DEFAULT_BIN_OVER_HALF_BUDGET.getMsg());
        }
        vo.setMarketplaceId(shop.getMarketplaceId());
        vo.setProfileId(profile.getProfileId());
        Result result = cpcSdGroupApiService.create(shop, profile, vo);
        if (!result.success()) {
            throw new ServiceException(result.getMsg());
        }
        // 入库
        try {
            amazonSdAdGroupDao.save(puid, vo);
            //写入doris
            saveDoris(Collections.singletonList(vo), true, true);
            /**
             * 广告组增加日志
             * 操作类型：新增广告组
             * 逻辑：调用新增广告日志方法，传空对象作为旧值
             * start
             */
            List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
            AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdSdGroupLog(null, vo);
            Optional.ofNullable(loginIp).ifPresent(adManageOperationLog::setIp);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLogs.add(adManageOperationLog);
            adManageOperationLog.setAdGroupId(vo.getAdGroupId());
            operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);

            return NewCreateResultResultVo.builder()
                    .campaignId(vo.getCampaignId())
                    .adGroupId(vo.getAdGroupId())
                    .build();
        } catch (Exception e) {
            log.error("createSdCampaign:", e);
            throw new ServiceException("创建广告组失败");
        }
    }

    @Override
    public Result updateAdGroup(SPadGroupVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonSdAdGroup oldAmazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, vo.getGroupId());
        if (oldAmazonSdAdGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);

        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSdAdGroup amazonSdAdGroup = new AmazonSdAdGroup();
        BeanUtils.copyProperties(oldAmazonSdAdGroup, amazonSdAdGroup);

        //判断活动名称是否存在
        if (!amazonSdAdGroup.getName().equals(vo.getName()) && amazonSdAdGroupDao.exist(puid, shopId, amazonSdAdGroup.getCampaignId(), vo.getName().trim())) {
            return ResultUtil.error("名称已存在");
        }

        if (StringUtils.isNotBlank(vo.getName())) {
            amazonSdAdGroup.setName(vo.getName().trim());
        }
        if (StringUtils.isNotBlank(vo.getState())) {
            amazonSdAdGroup.setState(vo.getState());
        }
        if(StringUtils.isNotBlank(vo.getBidOptimization()) && (Constants.CPC_OPTIMIZATION.contains(vo.getBidOptimization())||Constants.VCPM_OPTIMIZATION.contains(vo.getBidOptimization()))){
            amazonSdAdGroup.setBidOptimization(vo.getBidOptimization());
        }
        AmazonAdCampaignAll amazonSdAdCampaign = amazonSdAdCampaignDao.getByCampaignId(puid, shopId, oldAmazonSdAdGroup.getCampaignId());
        if (vo.getDefaultBid() != null) {
            if (amazonSdAdCampaign == null) {
                return ResultUtil.returnErr("没有活动信息");
            }

            // 展示型广告的默认竞价必须少于预算金额的一半
            if (vo.getDefaultBid() * 2 >= amazonSdAdCampaign.getBudget().doubleValue()) {
                return ResultUtil.returnErr("展示型广告的默认竞价必须少于预算金额的一半");
            }

            amazonSdAdGroup.setDefaultBid(new BigDecimal(vo.getDefaultBid()));
        } else {
            amazonSdAdGroup.setDefaultBid(null);
        }
        amazonSdAdGroup.setUpdateId(vo.getUid());

        Result result = cpcSdGroupApiService.update(shop, profile, amazonSdAdGroup);
        /**
         * 广告组增加日志
         * 操作类型：编辑广告组
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象
         * start
         */
        AdManageOperationLog adManageOperationLog = operationAdGroupLogService.getAdSdGroupLog(oldAmazonSdAdGroup, amazonSdAdGroup);
        adManageOperationLog.setIp(vo.getLoginIp());
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        if (result.success()){
            //记操作日志
            //SD广告组计费方式有两种：cpc和vpm
            String costType_cpc = "cpc";
            if (costType_cpc.equals(amazonSdAdCampaign.getCostType())){
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }
            adManageOperationLogs.add(adManageOperationLog);
            operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        if (!result.success()) {
            adManageOperationLogs.add(adManageOperationLog);
            operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
            return result;
        }

        // 入库
        try {
            amazonSdAdGroupDao.updateByIdAndPuid(puid, amazonSdAdGroup);
            //写入doris
            saveDoris(puid, shopId, Collections.singletonList(vo.getGroupId()));
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("updateAdGroup:", e);
        }
        return ResultUtil.returnErr("系统异常");
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, String groupId, String ip) {
        AmazonSdAdGroup amazonSdAdGroup = amazonSdAdGroupDao.getByGroupId(puid, shopId, groupId);
        if (amazonSdAdGroup == null) {
            return ResultUtil.error("没有广告组信息");
        }

        Result result = cpcSdGroupApiService.archive(amazonSdAdGroup);

        AmazonSdAdGroup oldGroup = new AmazonSdAdGroup();
        BeanUtils.copyProperties(amazonSdAdGroup, oldGroup);
        amazonSdAdGroup.setUpdateId(uid);
        amazonSdAdGroup.setState(CpcStatusEnum.archived.name());
        logSdGroupArchive(oldGroup, amazonSdAdGroup, result, ip);

        if (result.success()) {
            amazonSdAdGroupDao.updateByIdAndPuid(puid, amazonSdAdGroup);
            //写入doris
            saveDoris(Collections.singletonList(amazonSdAdGroup), false, true);
        }

        return result;
    }

    private void logSdGroupArchive(AmazonSdAdGroup oldGroup, AmazonSdAdGroup amazonSdAdGroup, Result result, String ip) {
        try {
            AdManageOperationLog operationLog = operationAdGroupLogService.getAdSdGroupLog(oldGroup, amazonSdAdGroup);
            operationLog.setIp(ip);
            if (result.success()){
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            operationAdGroupLogService.printAdOperationLog(Lists.newArrayList(operationLog));
        } catch (Exception e) {
            log.error("logSdGroupArchiveLog:", e);
        }
    }

    // po -> 列表页vo
    private void convertPoToPageVo(AmazonSdAdGroup amazonAdGroup, GroupPageVo vo) {
        vo.setShopId(amazonAdGroup.getShopId());
        vo.setAdGroupId(amazonAdGroup.getAdGroupId());
        vo.setName(amazonAdGroup.getName());
        vo.setState(amazonAdGroup.getState());
        vo.setDefaultBid(String.valueOf(amazonAdGroup.getDefaultBid()));
        vo.setTargetingType(amazonAdGroup.getTactic());
    }

    // 创建活动时vo->po
    private AmazonSdAdGroup convertVoToCreatePo(SPadGroupVo vo, AmazonAdCampaignAll amazonSdAdCampaign) {
        AmazonSdAdGroup amazonAdGroup = new AmazonSdAdGroup();
        amazonAdGroup.setPuid(vo.getPuid());
        amazonAdGroup.setShopId(amazonSdAdCampaign.getShopId());
        amazonAdGroup.setMarketplaceId(amazonSdAdCampaign.getMarketplaceId());
        amazonAdGroup.setCampaignId(amazonSdAdCampaign.getCampaignId());
        amazonAdGroup.setProfileId(amazonSdAdCampaign.getProfileId());
        amazonAdGroup.setName(vo.getName().trim());
        amazonAdGroup.setDefaultBid(BigDecimal.valueOf(vo.getDefaultBid()));
        amazonAdGroup.setState(Constants.ENABLED);
        amazonAdGroup.setTactic(amazonSdAdCampaign.getTactic());
        amazonAdGroup.setCreateId(vo.getUid());
        amazonAdGroup.setCreateInAmzup(1);
        amazonAdGroup.setBidOptimization(vo.getBidOptimization());
        return amazonAdGroup;
    }

    @Override
    public Result updateBatchAdGroup(List<SPadGroupVo> vos, String type, String ip) {
        int puid = vos.get(0).getPuid();
        int shopId = vos.get(0).getShopId();
        int uid = vos.get(0).getUid();

        List<String> groupIds = vos.stream().map(SPadGroupVo::getGroupId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            return ResultUtil.returnErr("没有广告组信息");
        }
        List<AmazonSdAdGroup> amazonSdAdGroups = amazonSdAdGroupDao.getByGroupIds(puid, shopId, groupIds);
        if (CollectionUtils.isEmpty(amazonSdAdGroups)) {
            return ResultUtil.returnErr("没有活动信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SPadGroupVo> errorList = new ArrayList<>();
        List<AmazonSdAdGroup> updateList = new ArrayList<>();

        Map<String,AmazonAdCampaignAll> amazonSdAdCampaignMap= new HashMap<>();

        if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){
            List<String> campaignids = amazonSdAdGroups.stream().map(AmazonSdAdGroup::getCampaignId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(campaignids)) {
                return ResultUtil.returnErr("没有活动信息");
            }
            List<AmazonAdCampaignAll>  amazonSdAdCampaigns = amazonSdAdCampaignDao.getByCampaignIds(puid,shopId,campaignids);
            if(CollectionUtils.isEmpty(amazonSdAdCampaigns)){
                return ResultUtil.returnErr("没有活动信息");
            }
            amazonSdAdCampaignMap = amazonSdAdCampaigns.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId,e->e));
        }
        Map<String, AmazonSdAdGroup> sdAdGroupMap = amazonSdAdGroups.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
        for (SPadGroupVo vo: vos){
            checkParam(vo,type);

            AmazonSdAdGroup oldAmazonSdAdGroup = sdAdGroupMap.get(vo.getGroupId());
            if(oldAmazonSdAdGroup == null){
                vo.setId(vo.getDxmGroupId());
                vo.setFailReason("广告不存在");
                errorList.add(vo);
                continue;
            }

            if(StringUtils.isNotBlank(vo.getFailReason())){
                vo.setId(oldAmazonSdAdGroup.getId());
                vo.setName(oldAmazonSdAdGroup.getName());
                vo.setFailReason("请求参数错误");
                errorList.add(vo);
                continue;
            }
            if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){
                AmazonAdCampaignAll amazonSdAdCampaign = amazonSdAdCampaignMap.get(oldAmazonSdAdGroup.getCampaignId());
                if (amazonSdAdCampaign == null) {
                    vo.setId(oldAmazonSdAdGroup.getId());
                    vo.setName(oldAmazonSdAdGroup.getName());
                    vo.setFailReason("没有活动信息");
                    errorList.add(vo);
                    continue;
                }

                // 展示型广告的默认竞价必须少于预算金额的一半
                if (vo.getDefaultBid() * 2 >= amazonSdAdCampaign.getBudget().doubleValue()) {
                    vo.setId(oldAmazonSdAdGroup.getId());
                    vo.setName(oldAmazonSdAdGroup.getName());
                    vo.setFailReason("展示型广告的默认竞价必须少于预算金额的一半");
                    errorList.add(vo);
                    continue;

                }
            }
            AmazonSdAdGroup amazonSdAdGroup = new AmazonSdAdGroup();
            BeanUtils.copyProperties(oldAmazonSdAdGroup ,amazonSdAdGroup);
            convertAmazonSdGroup(amazonSdAdGroup,vo,type);
            updateList.add(amazonSdAdGroup);
        }


        if(CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<SPadGroupVo, AmazonAdGroup> data =  new  BatchResponseVo<> ();
            data.setErrorList(errorList);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            data.setSuccessNum(0);
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<SPadGroupVo, AmazonSdAdGroup>> result = cpcSdGroupApiService.update(shop, profile, updateList, type);

        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
        for (AmazonSdAdGroup sdAdGroup : updateList) {
            AmazonSdAdGroup oldAmazonSdAdGroup = sdAdGroupMap.get(sdAdGroup.getAdGroupId());
            AdManageOperationLog adSdGroupLog = operationAdGroupLogService.getAdSdGroupLog(oldAmazonSdAdGroup, sdAdGroup);
            adSdGroupLog.setIp(ip);
            adManageOperationLogs.add(adSdGroupLog);
        }

        if (result.success()) {
            BatchResponseVo<SPadGroupVo, AmazonSdAdGroup> data = result.getData();
            List<SPadGroupVo> amazonAdProductError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                amazonAdProductError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
                data.setCountNum((data.getErrorList() == null ? 0 : data.getErrorList().size()) + (data.getSuccessList() == null ? 0 : data.getSuccessList().size()));
            }
            List<AmazonSdAdGroup> successList = data.getSuccessList();

            Map<String, AmazonSdAdGroup> successMap = successList.stream().collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e->e));
            Map<String, SPadGroupVo> errorMap = amazonAdProductError.stream().collect(Collectors.toMap(SPadGroupVo::getGroupId, e->e));
            for (AdManageOperationLog operationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(errorMap.get(operationLog.getAdGroupId()))) {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    operationLog.setResultInfo(errorMap.get(operationLog.getAdGroupId()).getFailReason());
                }
                if (!StringUtil.isEmptyObject(successMap.get(operationLog.getAdGroupId()))) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
            }

            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(successList)) {
                amazonSdAdGroupDao.batchUpdateAmazonSdAdGroup(puid, successList, type);
                //写入doris
                saveDoris(shop.getPuid(), shop.getId(), successList.stream().map(x -> x.getAdGroupId()).collect(Collectors.toList()));

                data.getSuccessList().clear();
            }
        } else {
            adManageOperationLogs.forEach(item -> {
                item.setResult(OperationLogResultEnum.FAIL.getResultValue());
                item.setResultInfo(result.getMsg());
            });
        }
        operationAdGroupLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    private SPadGroupVo checkParam(SPadGroupVo vo,String type){
        if(StringUtils.isBlank(vo.getGroupId())){
            vo.setFailReason("请求参数错误");
            return vo;
        }
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            if(StringUtils.isBlank(vo.getState())){
                vo.setFailReason("请求参数错误");
                return vo;
            }
        } else if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){
            if(vo.getDefaultBid() == null || vo.getDefaultBid().isNaN()){
                vo.setFailReason("预算费用填写错误");
                return vo;
            }
        } else {
            vo.setFailReason("请求参数错误");
            return vo;
        }

        return vo;
    }

    private AmazonSdAdGroup convertAmazonSdGroup(AmazonSdAdGroup amazonSdAdGroup,SPadGroupVo vo,String type){
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            amazonSdAdGroup.setState(vo.getState());
        } else if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){
            amazonSdAdGroup.setDefaultBid(BigDecimal.valueOf(vo.getDefaultBid()));
        }
        amazonSdAdGroup.setUpdateId(vo.getUid());
        return amazonSdAdGroup;
    }

    private List<Object> buildUpLogMessage(Map<String, AmazonSdAdGroup> oldList,List<AmazonSdAdGroup> newList,String type){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){
            dataList.add("SD广告组批量修改默认竞价");

        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            dataList.add("SD广告组批量修改状态");

        }
        newList.forEach(e ->{
            AmazonSdAdGroup old = oldList.get(e.getAdGroupId());

            if(Constants.CPC_SD_GROUP_BATCH_BID.equals(type)){

                builder.append("adGroupId:").append(e.getAdGroupId());
                if(old != null){
                    builder.append(",旧值:").append(old.getDefaultBid());
                }
                builder.append(",新值:").append(e.getDefaultBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){

                builder.append("adGroupId:").append(e.getAdGroupId());
                if(old != null){
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }


    /**
     * 写入doris
     * @param amazonSdAdGroupList mysql实体
     * @param create 是否创建
     * @param create 是否更新
     */
    @Override
    public void saveDoris(List<AmazonSdAdGroup> amazonSdAdGroupList, boolean create, boolean update) {
        try {
            //写入doris
            List<OdsAmazonAdGroupSd> collect = amazonSdAdGroupList.stream().map(x -> {
                OdsAmazonAdGroupSd odsAmazonAdGroupSd = new OdsAmazonAdGroupSd();
                BeanUtils.copyProperties(x, odsAmazonAdGroupSd);
                if (Objects.isNull(odsAmazonAdGroupSd.getIsStateBidding())) {
                    odsAmazonAdGroupSd.setIsStateBidding(0);
                }
                if (Objects.isNull(odsAmazonAdGroupSd.getPricingStateBidding())) {
                    odsAmazonAdGroupSd.setPricingStateBidding(0);
                }
                Date date = new Date();
                if (create) {
                    odsAmazonAdGroupSd.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdGroupSd.setUpdateTime(date);
                }
                if (StringUtils.isNotBlank(odsAmazonAdGroupSd.getState())) {
                    odsAmazonAdGroupSd.setState(odsAmazonAdGroupSd.getState().toLowerCase());
                }
                return odsAmazonAdGroupSd;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sd adgroup save doris error", e);
        }
    }

    @Override
    public Result<List<AmazonAdSdCreative>> checkCreativesInfo(Integer puid, Integer shopId, String groupId, String filterType) {
        if (shopId == null || StringUtils.isBlank(groupId)) {
            log.error("shopId or groupId is null, puid:{}, shopId:{}, groupId:{}", puid, shopId, groupId);
            return ResultUtil.returnErr("请求参数错误");
        }
        //校验广告组是否存在
        List<AmazonSdAdGroup> groupList = amazonSdAdGroupDao.getByGroupIds(puid, shopId, Collections.singletonList(groupId));
        if (CollectionUtils.isEmpty(groupList)) {
            log.error("group not exist, puid:{}, shopId:{}, groupId:{}", puid, shopId, groupId);
            return ResultUtil.returnErr("广告组不存在");
        }
        //根据广告组获取其下的广告创意
        List<AmazonAdSdCreative> creativeList = amazonAdSdCreativeDao.listByGroupIds(puid, shopId, Collections.singletonList(groupId));
        if (CollectionUtils.isEmpty(creativeList)) {
            return ResultUtil.success();
        }
        if (StringUtils.isNotEmpty(filterType)) {
            String[] filterList = filterType.split(",");
            if (filterList.length > 0) {
                creativeList = creativeList.stream().filter(c -> Arrays.asList(filterList)
                        .parallelStream().anyMatch(t -> t.equalsIgnoreCase(c.getCreativeType()))).collect(Collectors.toList());
            }
        }
        return ResultUtil.success(creativeList);
    }

    @Override
    public Result<List<AmazonSdAdTargeting>> checkTargetDynamicInfo(Integer puid, Integer shopId, String groupId, String filterType) {
        if (shopId == null || StringUtils.isBlank(groupId)) {
            log.error("shopId or groupId is null, puid:{}, shopId:{}, groupId:{}", puid, shopId, groupId);
            return ResultUtil.returnErr("请求参数错误");
        }
        //校验广告组是否存在
        List<AmazonSdAdGroup> groupList = amazonSdAdGroupDao.getByGroupIds(puid, shopId, Collections.singletonList(groupId));
        if (CollectionUtils.isEmpty(groupList)) {
            log.error("group not exist, puid:{}, shopId:{}, groupId:{}", puid, shopId, groupId);
            return ResultUtil.returnErr("广告组不存在");
        }
        //根据广告组获取其下的广告创意
        List<AmazonSdAdTargeting> dynamicTargetList = amazonSdAdTargetingDao.getByGroupIdsAndTargetTypesNew(puid,
                Collections.singletonList(groupId), Arrays.asList(SdTargetTypeEnum.similarProduct.getValue(),
                        SdTargetTypeEnum.exactProduct.getValue(), SdTargetTypeEnum.relatedProduct.getValue()));

        if (CollectionUtils.isEmpty(dynamicTargetList)) {
            return ResultUtil.success();
        }
        return ResultUtil.success(dynamicTargetList);
    }

    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param adGroupIdList
     */
    private void saveDoris(Integer puid, Integer shopId, List<String> adGroupIdList) {
        try {
            if (CollectionUtils.isEmpty(adGroupIdList)) {
                return;
            }
            List<AmazonSdAdGroup> groupList = amazonSdAdGroupDao.getByGroupIds(puid, shopId, adGroupIdList);
            saveDoris(groupList, false, false);
        } catch (Exception e) {
            log.error("sd adgroup save doris error", e);
        }
    }
}