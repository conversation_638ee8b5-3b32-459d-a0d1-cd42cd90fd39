package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordExtend;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargetingExtend;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetExtendInfo;

import java.util.List;

/**
 * AmazonAdKeywordReport
 * <AUTHOR>
 */
public interface IAmazonAdTargetingExtendDao extends IBaseShardingSphereDao<AmazonAdTargetingExtend> {
    /**
     * 批量插入数据
     * @param puid
     * @param list
     */
    void insertList(Integer puid, List<AmazonAdTargetingExtend> list);

    /**
     * 单店铺查询扩展数据
     * @param puid
     * @param shopId
     * @param targetIdList
     * @return
     */
    List<AmazonAdTargetingExtend> selectByShopIdAndTargetIdList(Integer puid, Integer shopId, List<String> targetIdList);

    List<TargetExtendInfo> selectByShopIdAndKeywordIdList(Integer puid, List<Integer> shopIdList, List<String> targetIdList);
}