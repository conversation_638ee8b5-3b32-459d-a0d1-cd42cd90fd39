package com.meiyunji.sponsored.service.reportImport2.processor;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.util.GZipUtils;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdGroupReport;
import com.meiyunji.sponsored.service.reportImport2.modle.LxAmazonAdProductTargetReport;
import com.meiyunji.sponsored.service.reportImport2.vo.AmazonAdReportImportMessage;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * lx活动报告导入处理器
 *
 * <AUTHOR>
 * @date 2023/05/26
 */
@Service
@Slf4j
public class LxAmazonAdAudienceReportImportProcessor extends AbstractAmazonAdLxReportImportProcessor<LxAmazonAdGroupReport> {


    private final IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    private final IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    private final IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao;
    private final IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao;
    private final ICpcTargetingReportDao cpcTargetingReportDao;
    private final IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;


    protected LxAmazonAdAudienceReportImportProcessor(IAmazonAdCampaignAllDao amazonAdCampaignAllDao, IAmazonAdGroupDao amazonAdGroupDao,
                                                      IAmazonSdAdGroupDao amazonSdAdGroupDao, IAmazonSbAdGroupDao amazonSbAdGroupDao,
                                                      IScVcShopAuthDao shopAuthDao, CosBucketClient tempBucketClient,
                                                      IAmazonSdAdTargetingDao amazonSdAdTargetingDao,
                                                      IAmazonSbAdTargetingDao amazonSbAdTargetingDao,
                                                      IAmazonAdSbTargetingReportDao amazonAdSbTargetingReportDao,
                                                      IAmazonAdSdTargetingReportDao amazonAdSdTargetingReportDao,
                                                      ICpcTargetingReportDao cpcTargetingReportDao,
                                                      IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService) {
        super(amazonAdCampaignAllDao, amazonAdGroupDao, amazonSdAdGroupDao, amazonSbAdGroupDao, shopAuthDao, tempBucketClient);
        this.amazonSdAdTargetingDao = amazonSdAdTargetingDao;
        this.amazonSbAdTargetingDao = amazonSbAdTargetingDao;
        this.amazonAdSbTargetingReportDao = amazonAdSbTargetingReportDao;
        this.amazonAdSdTargetingReportDao = amazonAdSdTargetingReportDao;
        this.cpcTargetingReportDao = cpcTargetingReportDao;
        this.amazonAdTargetDaoRoutingService = amazonAdTargetDaoRoutingService;
    }


    @Override
    public void importReport(AmazonAdReportImportMessage importMessage) {

        //读取数据
        try (InputStreamReader inputStreamReader = new InputStreamReader(new ByteArrayInputStream(GZipUtils.release(tempBucketClient.getObjectToBytes(importMessage.getFileId().replaceFirst("/", "")))))) {
            ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(importMessage.getShopId(), importMessage.getPuid());
            JSONReader jsonReader = new JSONReader(inputStreamReader);
            jsonReader.startArray();
            List<LxAmazonAdProductTargetReport> reports = Lists.newArrayListWithExpectedSize(500);
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                LxAmazonAdProductTargetReport report = new LxAmazonAdProductTargetReport();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                    if (reports.size() >= 500) {
                        dealReport(importMessage, reports, shopAuth);
                        reports = Lists.newArrayListWithExpectedSize(500);
                    }
                }

            }
            jsonReader.endArray();
            jsonReader.close();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReport(importMessage, reports, shopAuth);
            }
        } catch (Exception e) {
            log.error("import campaign report error puid:{},shopId：{}，scheduleId :{} error:", importMessage.getPuid(), importMessage.getShopId(), importMessage.getScheduleId(), e);
        }

    }

    private void dealReport(AmazonAdReportImportMessage importMessage, List<LxAmazonAdProductTargetReport> reports, ShopAuth shopAuth) {
        Integer puid = importMessage.getPuid();
        Integer shopId = importMessage.getShopId();

        String adType = importMessage.getAdType().toLowerCase();
        List<String> campaignIds = reports.stream().map(LxAmazonAdProductTargetReport::getCampaignId).collect(Collectors.toList());
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, shopAuth.getMarketplaceId(), campaignIds, adType);
        List<String> adTargetIds = reports.stream().map(LxAmazonAdProductTargetReport::getTargetId).collect(Collectors.toList());

        Map<String, AmazonAdTargeting> spTarget = new HashMap<>();
        Map<String, AmazonSbAdTargeting> sbTarget = new HashMap<>();
        Map<String, AmazonSdAdTargeting> sdTarget = new HashMap<>();

        if (CampaignTypeEnum.sp.name().equalsIgnoreCase(importMessage.getAdType())) {
            spTarget = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, shopId, adTargetIds).stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));

        } else if (CampaignTypeEnum.sb.name().equalsIgnoreCase(importMessage.getAdType())) {
            sbTarget = amazonSbAdTargetingDao.listByTargetId(puid, shopId, adTargetIds).stream().collect(Collectors.toMap(AmazonSbAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));
        } else if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
            sdTarget = amazonSdAdTargetingDao.listByTargetId(puid, shopId, adTargetIds).stream().collect(Collectors.toMap(AmazonSdAdTargeting::getTargetId, Function.identity(), (e1, e2) -> e2));
        }
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = byCampaignIds.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        List<CpcTargetingReport> spReports = new ArrayList<>();
        List<AmazonAdSbTargetingReport> sbReports = new ArrayList<>();
        List<AmazonAdSdTargetingReport> sdReports = new ArrayList<>();
        Map<String, AmazonAdTargeting> finalSpTarget = spTarget;
        Map<String, AmazonSbAdTargeting> finalSbTarget = sbTarget;
        Map<String, AmazonSdAdTargeting> finalSdTarget = sdTarget;
        reports.forEach(e -> {
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(e.getCampaignId());
            if (amazonAdCampaignAll == null) {
                log.error("pxq-report-import puid : {} shop_id : {} campaignId : {} 不存在", puid, shopId, e.getCampaignId());
                return;
            }

            if (CampaignTypeEnum.sd.name().equalsIgnoreCase(importMessage.getAdType())) {
                AmazonSdAdTargeting adTargeting = finalSdTarget.get(e.getTargetId());
                if (adTargeting == null) {
                    log.error("pxq-report-import puid : {} shop_id : {} campaignId : {}, targetId:{} 不存在", puid, shopId, e.getCampaignId(), e.getTargetId());
                    return;
                }
                AmazonAdSdTargetingReport sdReport = buildSdProductTargetReport(importMessage.getCountDate(), e, adTargeting, amazonAdCampaignAll, shopAuth);
                sdReports.add(sdReport);
            }
        });


        if (CollectionUtils.isNotEmpty(sdReports)) {
            amazonAdSdTargetingReportDao.insertOrUpdateList(puid, sdReports);
        }
    }


    /**
     * 构建sd组报告
     *
     * @param report              报告
     * @param amazonSdAdTargeting 亚马逊sd广告组
     * @param campaignAll         活动所有
     * @return {@link AmazonAdSdTargetingReport}
     */
    private AmazonAdSdTargetingReport buildSdProductTargetReport(String countDate, LxAmazonAdProductTargetReport report, AmazonSdAdTargeting amazonSdAdTargeting, AmazonAdCampaignAll campaignAll, ShopAuth shopAuth) {
        AmazonAdSdTargetingReport sdTargetingReport = new AmazonAdSdTargetingReport();
        sdTargetingReport.setPuid(shopAuth.getPuid());
        sdTargetingReport.setShopId(shopAuth.getId());
        sdTargetingReport.setMarketplaceId(shopAuth.getMarketplaceId());
        sdTargetingReport.setCountDate(countDate);
        sdTargetingReport.setTacticType(campaignAll.getTactic());
        sdTargetingReport.setCurrency(Marketplace.fromId(shopAuth.getMarketplaceId()).getCurrencyCode().name());
        sdTargetingReport.setCampaignName(report.getCampaignName());
        sdTargetingReport.setCampaignId(report.getCampaignId());
        sdTargetingReport.setAdGroupName(report.getAdGroupName());
        sdTargetingReport.setAdGroupId(report.getAdGroupId());
        sdTargetingReport.setTargetId(report.getTargetId());
        sdTargetingReport.setSales14d(report.getSales() != null ? report.getSales() : BigDecimal.ZERO);
        sdTargetingReport.setSales14dSameSKU(report.getDirectSales() != null ? report.getDirectSales() : BigDecimal.ZERO);
        sdTargetingReport.setUnitsOrdered14d(report.getAdUnits() != null ? report.getAdUnits() : 0);
        sdTargetingReport.setConversions14d(report.getOrders() != null ? report.getOrders() : 0);
        sdTargetingReport.setConversions14dSameSKU(report.getDirectOrders() != null ? report.getDirectOrders() : 0);
        sdTargetingReport.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        sdTargetingReport.setCost(report.getSpends() != null ? report.getSpends() : BigDecimal.ZERO);
        sdTargetingReport.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        sdTargetingReport.setViewImpressions(report.getViewImpressions() != null ? report.getViewImpressions() : 0);
        sdTargetingReport.setCostType(campaignAll.getCostType());

//
//        sdTargetingReport.setOrdersNewToBrand14d(isDxmNumeric(report.getOrdersNewToBrand14d()) ? Integer.parseInt(report.getOrdersNewToBrand14d()) : 0);
//        sdTargetingReport.setSalesNewToBrand14d(isDxmNumeric(report.getSalesNewToBrand14d()) ? new BigDecimal(report.getSalesNewToBrand14d()) : BigDecimal.ZERO);
//        sdTargetingReport.setUnitsOrderedNewToBrand14d(isDxmNumeric(report.getUnitsOrderedNewToBrand14d()) ? Integer.parseInt(report.getUnitsOrderedNewToBrand14d()) : 0);
//
//
        return sdTargetingReport;
    }


}
