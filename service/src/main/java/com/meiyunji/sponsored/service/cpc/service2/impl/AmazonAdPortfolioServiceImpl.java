package com.meiyunji.sponsored.service.cpc.service2.impl;


import com.amazon.advertising.mode.portfolios.Budget;
import com.amazon.advertising.mode.portfolios.Portfolio;
import com.amazon.advertising.portfoliosV3.entity.CreatePortfolioV3RequestEntity;
import com.amazon.advertising.portfoliosV3.entity.UpdatePortfolioV3RequestEntity;
import com.amazon.advertising.sb.mode.campaigm.V4Campaign;
import com.amazon.advertising.spV3.campaign.entity.CampaignEntityV3;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.SyncBasicDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdCampaignAllReportService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.sb.impl.CpcSbCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sd.impl.CpcSdCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCampaignApiService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdPortfolio;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.MarketplaceTimeZoneEnum;
import com.meiyunji.sponsored.service.enums.PortfolioPolicyV3Enum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonAdPortfolioServiceImpl implements IAmazonAdPortfolioService {

    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private AmazonAdPortfolioApiService portfolioApiService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao profileDao;
    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;
    @Autowired
    private CpcSdCampaignApiService cpcSdCampaignApiService;
    @Autowired
    private CpcSbCampaignApiService cpcSbCampaignApiService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonAdCampaignAllReportService amazonAdCampaignAllReportService;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignAllReportDao;
    @Autowired
    private IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;


    @Override
    public List<AmazonAdPortfolio> getPortfolioNameAndIdList(Integer puid, PortfolioPageParam param) {
        return portfolioDao.getPortfolioListNew(puid, param);
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioNameAndIdList(Integer puid, PortfolioListParam param) {
        return portfolioDao.getPortfolioListNew(puid, param);
    }

    @Override
    public List<AmazonAdPortfolio> getPortfolioNameAndIdLists(Integer puid, PortfolioPageParam param) {
        return portfolioDao.getPortfolioListNews(puid, param);
    }

    /**
     * 获取广告组合页面数据(填充广告组合和广告活动报告数据)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<PortfolioPageVo> getPortfolioPageVoList(Integer puid, PortfolioPageParam param) {
        long t = System.currentTimeMillis();
        // 获取广告组合数据
        List<AmazonAdPortfolio> poList = portfolioDao.getPortfolioListNew(puid, param);
        log.info("广告管理 --广告组合接口调用-获取广告组合信息- 花费时间 {}, params: {}", (System.currentTimeMillis() - t), JSONUtil.objectToJson(param));

        // 定义最终的页面数据大小
        List<PortfolioPageVo> voList = new ArrayList<>(poList.size());

        long t1 = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(poList)) {

            // 取店铺销售额  最外层已取
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            if (param.getShopSales() != null) {
                shopSaleDto.setSumRange(param.getShopSales());
            }
            getAndFillPageDate(puid, param, poList, voList, shopSaleDto);
        }

        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(voList, adMetricDto);
        // 填充占比
        filterAdMetricData(voList, adMetricDto);

        log.info("广告管理 --广告组合接口调用-广告组合填充报告数据等- 花费时间 {}, params: {}", (System.currentTimeMillis() - t1), JSONUtil.objectToJson(param));
        return voList;
    }

    /**
     * 获取广告组合页面数据(填充广告组合和广告活动报告数据)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<PortfolioPageVo> getDorisPortfolioVoList(Integer puid, PortfolioPageParam param) {
        return getDorisPortfolioVoPage(puid, param, true).getRows();
    }

    @Override
    public Page<PortfolioPageVo> getDorisPortfolioVoPage(Integer puid, PortfolioPageParam param, boolean queryAll) {
        long t = System.currentTimeMillis();
        // 获取广告组合数据
        List<AmazonAdPortfolio> poList = portfolioDao.getPortfolioListNew(puid, param);
        log.info("广告管理 --广告组合接口调用-获取广告组合信息- 花费时间 {}, params: {}", (System.currentTimeMillis() - t), JSONUtil.objectToJson(param));

        // 定义最终的页面数据大小

        long t1 = System.currentTimeMillis();

        // 取店铺销售额  最外层已取
        ShopSaleDto shopSaleDto = new ShopSaleDto();
        if (param.getShopSales() != null) {
            shopSaleDto.setSumRange(param.getShopSales());
        }
        Page<PortfolioPageVo> page = getAndFillDorisPageDate(puid, param, shopSaleDto, poList, queryAll);
        List<PortfolioPageVo> voList = page.getRows();

        // 获取汇总信息
        AmazonAdCampaignDorisAllReport allReport = amazonAdCampaignAllDorisDao.getSumReport(puid, param, poList.stream().map(AmazonAdPortfolio::getPortfolioId).collect(Collectors.toList()));
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetricData(allReport, adMetricDto);
        // 填充占比
        filterAdMetricData(voList, adMetricDto);

        log.info("广告管理 --广告组合接口调用-广告组合填充报告数据等- 花费时间 {}, params: {}", (System.currentTimeMillis() - t1), JSONUtil.objectToJson(param));
        return page;
    }


    @Override
    public Page getPortfolioPageVoPageList(Integer puid, PortfolioPageParam param, Page page) {

        List<PortfolioPageVo> voList = new ArrayList<>();

        // 获取广告组合数据
        page = portfolioDao.getPortfolioPageList(puid, param, page);
        List<AmazonAdPortfolio> poList = page.getRows();

        // 新增类型指标占比数据(目前只能复查,看看以后能不能优化)
        List<String> portfolioIdsList;
        List<AdHomePerformancedto> reportList;
        List<AmazonAdPortfolio> amazonAdPortfoliosList = portfolioDao.getPortfolioListNew(puid, param);
        portfolioIdsList = amazonAdPortfoliosList.stream().map(AmazonAdPortfolio::getPortfolioId).collect(Collectors.toList());
        List<String> allCampaignId = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdList(puid, param.getShopId(), portfolioIdsList);
        if (CollectionUtils.isEmpty(amazonAdPortfoliosList) || CollectionUtils.isEmpty(allCampaignId)) {
            reportList = new ArrayList<>();
        } else {
            reportList = amazonAdCampaignAllReportDao.getReportByCampaignIdsAndDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), allCampaignId);
        }

        // 获取汇总信息
        AdMetricDto adMetricDto = new AdMetricDto();
        filterSumMetric(reportList, adMetricDto);


        long t1 = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(poList)) {

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            if (param.getShopSales() != null) {
                shopSaleDto.setSumRange(param.getShopSales());
            }

            page.setRows(voList);
            getAndFillPageDate(puid, param, poList, voList, shopSaleDto);
        }

        filterAdMetricData(voList, adMetricDto);

        log.info("广告管理 --广告组合接口调用-广告组合填充报告数据等- 花费时间 {}, params: {}", (System.currentTimeMillis() - t1), JSONUtil.objectToJson(param));

        return page;
    }

    @Override
    public void getAndFillPageDate(Integer puid, PortfolioPageParam param, List<AmazonAdPortfolio> poList, List<PortfolioPageVo> voList, ShopSaleDto shopSaleDto) {
        PortfolioPageVo vo;

        // 所有广告组合Id
        List<String> allPortfolioIds = poList.stream().filter(Objects::nonNull).map(AmazonAdPortfolio::getPortfolioId).distinct().collect(Collectors.toList());

        // 所有广告活动
        List<AmazonAdCampaignAll> allCampaigns = amazonAdCampaignAllDao.getCampaignsByPortfolioIdList(puid, param.getShopId(), allPortfolioIds);

        // 所有广告活动Id
        List<String> allCampaignIds = allCampaigns.stream().filter(Objects::nonNull).distinct().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());

        // 所有广告活动映射 <portfolioId, List<campaignIds>>
        Map<String, List<String>> allCampaignIdMap = new HashMap<>();
        for (AmazonAdCampaignAll campaign : allCampaigns) {
            if (allCampaignIdMap.containsKey(campaign.getPortfolioId())) {
                allCampaignIdMap.get(campaign.getPortfolioId()).add(campaign.getCampaignId());
            } else {
                List<String> allCampaignIdList = new ArrayList<>();
                allCampaignIdList.add(campaign.getCampaignId());
                allCampaignIdMap.put(campaign.getPortfolioId(), allCampaignIdList);
            }
        }

        // 所有活动报告数据
        List<AmazonAdCampaignAllReport> allReports = null;
        allReports = amazonAdCampaignAllReportService.getAllCampaignIdsSumReport(puid, param.getShopId(),
                poList.get(0).getMarketplaceId(), param.getStartDate(), param.getEndDate(), allCampaignIds);

        // 所有活动报告映射
        Map<String, AmazonAdCampaignAllReport> allReportMap = allReports.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, item -> item, (a, b) -> a));

        // 数据填充
        for (AmazonAdPortfolio amazonAdPortfolio : poList) {
            List<AmazonAdCampaignAllReport> listReport = new ArrayList<>();
            ReportBase sumReport = new ReportBase();

            // 该广告组合下的活动Id
            List<String> campaignIds = new ArrayList<>();
            campaignIds = allCampaignIdMap.get(amazonAdPortfolio.getPortfolioId());

            if (campaignIds != null) {
                for (String campaignId : campaignIds) {
                    AmazonAdCampaignAllReport report = allReportMap.get(campaignId);
                    if (report != null) {
                        listReport.add(report);
                    }
                }
                // 同一广告组合下广告活动报告数据汇总
                sumReport.setCost(listReport.stream().filter(item -> item != null && item.getCost() != null).map(AmazonAdCampaignAllReport::getCost).reduce(BigDecimal.ZERO, BigDecimal::add));
                sumReport.setTotalSales(listReport.stream().filter(item -> item != null && item.getTotalSales() != null).map(ReportBase::getTotalSales).reduce(BigDecimal.ZERO, BigDecimal::add));
                sumReport.setAdSales(listReport.stream().filter(item -> item != null && item.getAdSales() != null).map(ReportBase::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add));
                sumReport.setImpressions(listReport.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AmazonAdCampaignAllReport::getImpressions).sum());
                sumReport.setClicks(listReport.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AmazonAdCampaignAllReport::getClicks).sum());
                // 广告订单量
                sumReport.setSaleNum(listReport.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AmazonAdCampaignAllReport::getOrderNum).sum());
                sumReport.setAdOrderNum(listReport.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AmazonAdCampaignAllReport::getAdOrderNum).sum());
                // 广告销量
                sumReport.setOrderNum(listReport.stream().filter(item -> item != null && item.getSaleNum() != null).mapToInt(AmazonAdCampaignAllReport::getSaleNum).sum());
                sumReport.setAdSaleNum(listReport.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AmazonAdCampaignAllReport::getAdSaleNum).sum());
                sumReport.setSalesNewToBrand14d(listReport.stream().filter(item -> item != null && item.getSalesNewToBrand14d() != null).map(AmazonAdCampaignAllReport::getSalesNewToBrand14d).reduce(BigDecimal.ZERO, BigDecimal::add));
                sumReport.setOrdersNewToBrand14d(listReport.stream().filter(item -> item != null && item.getOrdersNewToBrand14d() != null).mapToInt(AmazonAdCampaignAllReport::getOrdersNewToBrand14d).sum());
                sumReport.setUnitsOrderedNewToBrand14d(listReport.stream().filter(item -> item != null && item.getUnitsOrderedNewToBrand14d() != null).mapToInt(AmazonAdCampaignAllReport::getUnitsOrderedNewToBrand14d).sum());
                sumReport.setViewImpressions(listReport.stream().filter(item -> item != null && item.getViewImpressions() != null).mapToInt(AmazonAdCampaignAllReport::getViewImpressions).sum());
            }

            vo = new PortfolioPageVo();
            voList.add(vo);
            vo.setCampaignNumber(campaignIds == null ? 0 : campaignIds.size());
            vo.setIsHidden(param.getIsHidden());
            // Po转页面Vo
            convertPoToPageVo(amazonAdPortfolio, vo);
            // 报告数据填充
            cpcCommService.fillReportDataIntoPageVo(vo, sumReport, shopSaleDto);

        }
    }

    private Page<PortfolioPageVo> getAndFillDorisPageDate(Integer puid, PortfolioPageParam param, ShopSaleDto shopSaleDto, List<AmazonAdPortfolio> poList, boolean queryAll) {
        PortfolioPageVo vo;
        Map<String, AmazonAdPortfolio> amazonAdPortfolioMap = poList.stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (oldVal, newVal) -> newVal));
        List<String> portfolioIds = new ArrayList<>(amazonAdPortfolioMap.keySet());

        // 所有活动报告数据
        Page<AmazonAdPortfolioDorisAllReport> allReports;
        if (CollectionUtils.isEmpty(portfolioIds)) {
            allReports = new Page<>();
            allReports.setRows(Collections.emptyList());
            allReports.setPageNo(param.getPageNo());
            allReports.setPageSize(param.getPageSize());
        } else {
            allReports = amazonAdCampaignAllDorisDao.getSumReportByAllCampaignIds(puid, param, portfolioIds, queryAll);
        }
        List<PortfolioPageVo> voList = new ArrayList<>();
        // 数据填充
        for (AmazonAdPortfolioDorisAllReport report : allReports.getRows()) {
            AmazonAdPortfolio portfolio = amazonAdPortfolioMap.get(report.getPortfolioId());
            if (portfolio == null) {
                continue;
            }
            ReportBase sumReport = new ReportBase();
            // 同一广告组合下广告活动报告数据汇总
            sumReport.setCost(Optional.ofNullable(report.getCost()).map(each -> each.setScale(4, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO));
            sumReport.setTotalSales(Optional.ofNullable(report.getTotalSales()).map(each -> each.setScale(4, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO));
            sumReport.setAdSales(Optional.ofNullable(report.getAdSales()).map(each -> each.setScale(4, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO));
            sumReport.setImpressions(Optional.ofNullable(report.getImpressions()).orElse(0));
            sumReport.setClicks(Optional.ofNullable(report.getClicks()).orElse(0));
            // 广告订单量
            sumReport.setSaleNum(Optional.ofNullable(report.getSaleNum()).orElse(0));
            sumReport.setAdOrderNum(Optional.ofNullable(report.getAdOrderNum()).orElse(0));
            // 广告销量
            sumReport.setOrderNum(Optional.ofNullable(report.getOrderNum()).orElse(0));
            sumReport.setAdSaleNum(Optional.ofNullable(report.getAdSaleNum()).orElse(0));
            sumReport.setSalesNewToBrand14d(Optional.ofNullable(report.getSalesNewToBrand()).map(each -> each.setScale(4, RoundingMode.HALF_UP)).orElse(BigDecimal.ZERO));
            sumReport.setOrdersNewToBrand14d(Optional.ofNullable(report.getOrdersNewToBrand()).orElse(0));
            sumReport.setUnitsOrderedNewToBrand14d(Optional.ofNullable(report.getUnitsOrderedNewToBrand()).orElse(0));
            sumReport.setViewImpressions(Optional.ofNullable(report.getViewImpressions()).orElse(0));

            vo = new PortfolioPageVo();
            voList.add(vo);
            vo.setCampaignNumber(report.getCampaignCount());
            vo.setIsHidden(param.getIsHidden());
            // Po转页面Vo
            convertPoToPageVo(portfolio, vo);
            // 报告数据填充
            cpcCommService.fillReportDataIntoPageVo(vo, sumReport, shopSaleDto);

        }
        Page<PortfolioPageVo> page = new Page<>();
        page.setPageNo(allReports.getPageNo());
        page.setTotalPage(allReports.getTotalPage());
        page.setRows(voList);
        page.setPageSize(allReports.getPageSize());
        page.setTotalSize(allReports.getTotalSize());
        return page;
    }

    private void convertPoToPageVo(AmazonAdPortfolio amazonAdPortfolio, PortfolioPageVo vo) {
        vo.setId(amazonAdPortfolio.getId());
        vo.setShopId(amazonAdPortfolio.getShopId());
        vo.setPortfolioId(amazonAdPortfolio.getPortfolioId());
        vo.setName(amazonAdPortfolio.getName());
        vo.setState(amazonAdPortfolio.getState());
        vo.setPolicy(amazonAdPortfolio.getPolicy());
        vo.setAmount(amazonAdPortfolio.getAmount());
        vo.setBudgetStartDate(amazonAdPortfolio.getBudgetStartDateStr());
        vo.setBudgetEndDate(amazonAdPortfolio.getBudgetEndDateStr());
        vo.setMarketplaceId(amazonAdPortfolio.getMarketplaceId());
        amazonAdPortfolio.setServingStatus(amazonAdPortfolio.getServingStatus());
        vo.setServingStatus(amazonAdPortfolio.getServingStatus());
        vo.setServingStatusDec(amazonAdPortfolio.getServingStatusDec());
        vo.setServingStatusName(amazonAdPortfolio.getServingStatusName());
        vo.setIsAmountPricing(amazonAdPortfolio.getIsAmountPricing());
        vo.setPricingAmountState(amazonAdPortfolio.getPricingAmountState());

    }

    private void filterSumMetricData(AmazonAdCampaignDorisAllReport allReport, AdMetricDto adMetricDto) {
        if (allReport == null) {
            return;
        }
        adMetricDto.setSumCost(Optional.ofNullable(allReport.getCost()).orElse(BigDecimal.ZERO));
        adMetricDto.setSumAdSale(Optional.ofNullable(allReport.getTotalSales()).orElse(BigDecimal.ZERO));
        adMetricDto.setSumAdOrderNum(Optional.ofNullable(allReport.getSaleNum()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
        adMetricDto.setSumOrderNum(Optional.ofNullable(allReport.getOrderNum()).map(BigDecimal::valueOf).orElse(BigDecimal.ZERO));
    }

    // 获取指标总数据
    private void filterSumMetricData(List<PortfolioPageVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(item -> new BigDecimal(item.getAdCost())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(item -> new BigDecimal(item.getAdSale())).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(PortfolioPageVo::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(PortfolioPageVo::getOrderNum).sum()));
    }

    private void filterSumMetric(List<AdHomePerformancedto> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        adMetricDto.setSumCost(voList.stream().filter(item -> item != null && item.getAdCost() != null).map(AdHomePerformancedto::getAdCost).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(voList.stream().filter(item -> item != null && item.getAdSale() != null).map(AdHomePerformancedto::getAdSale).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(voList.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum()));
    }


    // 填充指标占比数据
    private void filterAdMetricData(List<PortfolioPageVo> voList, AdMetricDto adMetricDto) {
        for (PortfolioPageVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage("0");
                vo.setAdSalePercentage("0");
                vo.setAdOrderNumPercentage("0");
                vo.setOrderNumPercentage("0");
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, PortfolioPageVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdCost(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(vo.getAdSale(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(vo.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }
    }


    @Override
    public List<AdCampaignVo> getListCampaignVoByType(Integer puid, Integer shopId, String type, String portfolioId) {
        List<AdCampaignVo> voList;
        if (Constants.SP.equalsIgnoreCase(type)) {
            List<AmazonAdCampaignAll> spAdCampaignList = amazonAdCampaignAllDao.getListByPortfolioId(puid, shopId, portfolioId, CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(spAdCampaignList)) {
                voList = new ArrayList<>(spAdCampaignList.size());
                AdCampaignVo vo;
                for (AmazonAdCampaignAll campaign : spAdCampaignList) {
                    vo = new AdCampaignVo();
                    vo.setCampaignId(campaign.getCampaignId());
                    vo.setName(campaign.getName());
                    vo.setState(campaign.getState());
                    vo.setCampaignTargetingType(campaign.getTargetingType());
                    if (campaign.getBudget() != null) {
                        vo.setDailyBudget(String.valueOf(campaign.getBudget()));
                    }
                    vo.setId(campaign.getId());
                    voList.add(vo);
                }
                return voList;
            }
        } else if (Constants.SB.equalsIgnoreCase(type)) {
            List<AmazonAdCampaignAll> sbAdCampaignList = amazonAdCampaignAllDao.getListByPortfolioId(puid, shopId, portfolioId, CampaignTypeEnum.sb.getCampaignType());
            if (CollectionUtils.isNotEmpty(sbAdCampaignList)) {
                voList = new ArrayList<>(sbAdCampaignList.size());
                AdCampaignVo vo;
                for (AmazonAdCampaignAll campaign : sbAdCampaignList) {
                    vo = new AdCampaignVo();
                    vo.setCampaignId(campaign.getCampaignId());
                    vo.setName(campaign.getName());
                    vo.setState(campaign.getState());
                    if (campaign.getBudget() != null) {
                        vo.setDailyBudget(String.valueOf(campaign.getBudget()));
                    }
                    vo.setId(Long.valueOf(campaign.getId()));
                    vo.setTargetType(campaign.getTargetType());
                    voList.add(vo);
                }
                return voList;
            }
        } else if (Constants.SD.equalsIgnoreCase(type)) {
            List<AmazonAdCampaignAll> sdAdCampaignList = amazonAdCampaignAllDao.getListByPortfolioId(puid, shopId, portfolioId, CampaignTypeEnum.sd.getCampaignType());
            if (CollectionUtils.isNotEmpty(sdAdCampaignList)) {
                voList = new ArrayList<>(sdAdCampaignList.size());
                AdCampaignVo vo;
                for (AmazonAdCampaignAll campaign : sdAdCampaignList) {
                    vo = new AdCampaignVo();
                    vo.setCampaignId(campaign.getCampaignId());
                    vo.setName(campaign.getName());
                    vo.setState(campaign.getState());
                    if (campaign.getBudget() != null) {
                        vo.setDailyBudget(String.valueOf(campaign.getBudget()));
                    }
                    vo.setId(Long.valueOf(campaign.getId()));
                    voList.add(vo);
                }
                return voList;
            }
        }
        return null;
    }

    @Override
    public Result<String> updatePortfolioHiddenState(Integer puid, Integer uid, Integer shopId, String portfolioIds, Boolean isHidden) {
        if (StringUtils.isBlank(portfolioIds) || isHidden == null) {
            return ResultUtil.returnErr("参数有误.");
        }

        List<String> portfolioIdList = StringUtil.splitStr(portfolioIds, ",");

        portfolioDao.batchUpdateIsHidden(puid, uid, shopId, isHidden, portfolioIdList);

        saveDoris(puid, shopId, portfolioIdList);

        return ResultUtil.success();
    }

    @Override
    public Result<String> createPortfolioData(Integer puid, Integer uid, Integer shopId, String name, String ip) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = profileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.error("店铺配置不存在");
        }

        CreatePortfolioV3RequestEntity portfolio = new CreatePortfolioV3RequestEntity();
        portfolio.setName(name);
        // 默认 enabled
        portfolio.setState("ENABLED");
        Result<String> result = portfolioApiService.create(shopAuth, amazonAdProfile, portfolio);
        if (result.success()) {
            AmazonAdPortfolio adPortfolio = new AmazonAdPortfolio();
            adPortfolio.setPuid(puid);
            adPortfolio.setShopId(shopId);
            adPortfolio.setProfileId(amazonAdProfile.getProfileId());
            adPortfolio.setMarketplaceId(amazonAdProfile.getMarketplaceId());
            adPortfolio.setPortfolioId(result.getData());
            adPortfolio.setName(portfolio.getName());
            if (portfolio.getState() != null) {
                adPortfolio.setState(portfolio.getState().toLowerCase());
            } else {
                adPortfolio.setState(portfolio.getState());
            }
            // 默认 noBudget(amazon中无此预算状态 此处标记为未设置预算状态 实际预算状态类型:日期范围和每月定期)
            adPortfolio.setPolicy("noBudget");
            adPortfolio.setCreateInAmzup(1);
            adPortfolio.setCreateId(uid);
            adPortfolio.setUpdateId(uid);
            try {
                portfolioDao.save(puid, adPortfolio);
            } catch (Exception e) {
                log.error("createPortfolioData error", e);
            }
            // 同步一次
            portfolioApiService.syncPortfolio(shopAuth, amazonAdProfile, result.getData());

            //写入doris
            saveDoris(puid, shopId, Collections.singletonList(adPortfolio.getPortfolioId()));

            List<AdManageOperationLog> portfolioOperationLogs = Lists.newArrayList();
            AdManageOperationLog portfolioOperationLog = adManageOperationLogService.getPortfolioOperationLog(null, adPortfolio);
            portfolioOperationLog.setIp(ip);
            if (result.success()) {
                portfolioOperationLog.setResult(result.getCode());
                portfolioOperationLog.setPortfolioId(result.getData());
            } else {
                portfolioOperationLog.setResult(result.getCode());
                portfolioOperationLog.setResultInfo(result.getMsg() != null ? result.getMsg() : "");
            }
            portfolioOperationLogs.add(portfolioOperationLog);
            adManageOperationLogService.printAdOperationLog(portfolioOperationLogs);
        }

        return result;
    }

    @Override
    public Result<String> updatePortFolioData(PortfolioEditParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = profileDao.getProfile(param.getPuid(), param.getShopId());
        if (amazonAdProfile == null) {
            return ResultUtil.error("店铺配置不存在");
        }

        if (StringUtils.isBlank(param.getPortfolioId())) {
            return ResultUtil.error("广告组合id参数有误");
        }

        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(param.getPuid(), param.getShopId(), param.getPortfolioId());
        if (adPortfolio == null) {
            return ResultUtil.error("广告组合不存在");
        }

        AmazonAdPortfolio newPortfolio = new AmazonAdPortfolio();
        BeanUtils.copyProperties(adPortfolio, newPortfolio);
        buildUpdateInfo(param, newPortfolio);

        UpdatePortfolioV3RequestEntity portfolio = new UpdatePortfolioV3RequestEntity();
        Budget budget = new Budget();
        if (!PortfolioEditParam.PortfolioPolicyEnum.noBudget.getCode().equals(param.getPolicy())) {
            budget.setAmount(param.getAmount());
            //不知道怎么写合适了直接写死了
            PortfolioPolicyV3Enum portfolioPolicyV3Enum = PortfolioPolicyV3Enum.fromOldValue(param.getPolicy());
            if ("monthlyRecurring".equals(param.getPolicy())) {
                portfolioPolicyV3Enum = PortfolioPolicyV3Enum.MONTHLY_RECURRING;
            }
            if (portfolioPolicyV3Enum != null) {
                budget.setPolicy(portfolioPolicyV3Enum.getValue());
            } else {
                budget.setPolicy(param.getPolicy());
            }
            if (StringUtils.isNotBlank(param.getBudgetStartDate()) && portfolioPolicyV3Enum != PortfolioPolicyV3Enum.MONTHLY_RECURRING) {
                LocalDate parse = LocalDate.parse(param.getBudgetStartDate(), DateTimeFormatter.BASIC_ISO_DATE);
                budget.setStartDate(parse.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
            if (StringUtils.isNotBlank(param.getBudgetEndDate())) {
                LocalDate parse = LocalDate.parse(param.getBudgetEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
                budget.setEndDate(parse.format(DateTimeFormatter.ISO_LOCAL_DATE));
            }
        } else {
            budget.setPolicy(PortfolioPolicyV3Enum.NO_CAP.getValue());
        }

        portfolio.setName(param.getPortfolioName());
        portfolio.setPortfolioId(adPortfolio.getPortfolioId());

        portfolio.setBudget(budget);
        param.setBudget(budget);

        Result<String> result = portfolioApiService.update(shopAuth, amazonAdProfile, portfolio);
        if (result.success()) {
            portfolioDao.updatePortfolio(adPortfolio.getPuid(), adPortfolio.getShopId(), param.getUid(), adPortfolio.getId(), param);
            // 同步一次
            portfolioApiService.syncPortfolio(shopAuth, amazonAdProfile, result.getData());

            saveDoris(adPortfolio.getPuid(), adPortfolio.getShopId(), Collections.singletonList(adPortfolio.getPortfolioId()));
        }

        List<AdManageOperationLog> portfolioOperationLogs = Lists.newArrayList();
        AdManageOperationLog portfolioOperationLog = adManageOperationLogService.getPortfolioOperationLog(adPortfolio, newPortfolio);
        portfolioOperationLog.setIp(param.getIp());
        if (result.success()) {
            portfolioOperationLog.setResult(result.getCode());
            portfolioOperationLog.setPortfolioId(result.getData());
        } else {
            portfolioOperationLog.setResult(result.getCode());
            portfolioOperationLog.setResultInfo(result.getMsg() != null ? result.getMsg() : "");
        }
        portfolioOperationLogs.add(portfolioOperationLog);
        adManageOperationLogService.printAdOperationLog(portfolioOperationLogs);

        return  result;
    }

    private void buildUpdateInfo(PortfolioEditParam param, AmazonAdPortfolio portfolio) {
        portfolio.setName(param.getPortfolioName());
        portfolio.setPolicy(param.getPolicy());
        portfolio.setUpdateId(param.getUid());

        portfolio.setAmount(null);
        portfolio.setBudgetStartDate(null);
        portfolio.setBudgetEndDate(null);

        if (!PortfolioEditParam.PortfolioPolicyEnum.noBudget.getCode().equals(param.getPolicy())) {
            portfolio.setAmount(param.getAmount());
            if (StringUtils.isNotBlank(param.getBudgetEndDate())) {
                portfolio.setBudgetEndDate(DateUtil.stringToDate(param.getBudgetEndDate()));
            }
            if (PortfolioEditParam.PortfolioPolicyEnum.dateRange.getCode().equals(param.getPolicy())) {
                portfolio.setBudgetStartDate(DateUtil.stringToDate(param.getBudgetStartDate()));
            }
        }
    }

    @Override
    public Result<String> updateState(Integer puid, Integer uid, Integer shopId, String portfolioId, CpcStatusEnum statusEnum) {
        AmazonAdPortfolio oldAmazonAdPortfolio = portfolioDao.getByPortfolioId(puid, shopId, portfolioId);
        UpdatePortfolioV3RequestEntity portfolio = new UpdatePortfolioV3RequestEntity();
        BeanUtils.copyProperties(oldAmazonAdPortfolio, portfolio);

        if (oldAmazonAdPortfolio == null) {
            return ResultUtil.error("对象不存在");
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            return ResultUtil.error("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = profileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.error("店铺配置不存在");
        }

        String oldState = oldAmazonAdPortfolio.getState();
        if (oldState.equals(statusEnum.name())) {
            return ResultUtil.success("success");
        }


        portfolio.setPortfolioId(oldAmazonAdPortfolio.getPortfolioId());
        portfolio.setState(statusEnum.name());
        portfolio.setName(oldAmazonAdPortfolio.getName());

        Result<String> result = portfolioApiService.update(shopAuth, amazonAdProfile, portfolio);
        if (result.success()) {
            portfolioDao.updatePortfolioState(puid, shopId, uid, oldAmazonAdPortfolio.getId(), statusEnum.name());
            // 同步一次
            portfolioApiService.syncPortfolio(shopAuth, amazonAdProfile, result.getData());
        }

        return result;

    }

    @Override
    public Result<List<PortfolioErrorMsgVo>> removePortFolioData(Integer puid, Integer uid, Integer shopId, String type, String campaignIds, String loginIp) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = profileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.error("店铺配置不存在");
        }

        List<String> campaignIdList = StringUtil.splitStr(campaignIds, ",");
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return ResultUtil.error("选择活动为空");
        }
        if (campaignIdList.size() > 100) {
            return ResultUtil.error("选择活动数据大于100");
        }
        List<PortfolioErrorMsgVo> errorMsgVos = new ArrayList<>();
        Result<List<PortfolioErrorMsgVo>> result = ResultUtil.success(errorMsgVos);

        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, amazonAdProfile.getMarketplaceId(), campaignIdList, null);
        if (CollectionUtils.isEmpty(byCampaignIds)) {
            return ResultUtil.error("广告活动不存在");
        }
        Map<String, List<AmazonAdCampaignAll>> collect = byCampaignIds.stream().collect(Collectors.groupingBy(AmazonAdCampaignAll::getType));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : collect.entrySet()) {
            List<String> cids = entry.getValue().stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(entry.getKey())) {
                /**
                 * TODO 广告活动增加日志
                 * 操作类型：批量移除广告活动所属的广告组合
                 * 逻辑：创建广告新对象广告组合修改
                 * start
                 */
                List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
                List<AmazonAdCampaignAll> amazonAdCampaigns = entry.getValue();
                for (AmazonAdCampaignAll adCampaign : amazonAdCampaigns) {
                    AmazonAdCampaignAll newAmazonAdCampaign = new AmazonAdCampaignAll();
                    BeanUtils.copyProperties(adCampaign, newAmazonAdCampaign);
                    newAmazonAdCampaign.setPortfolioId("");
                    newAmazonAdCampaign.setUpdateId(uid);
                    AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(adCampaign, newAmazonAdCampaign);
                    adManageOperationLog.setIp(loginIp);
                    adManageOperationLogs.add(adManageOperationLog);
                }

                result = batchUpdateSpPortFolio(uid, shopAuth, amazonAdProfile, cids, null);

                List<PortfolioErrorMsgVo> resultData = result.getData();
                if (resultData != null) {
                    errorMsgVos.addAll(resultData);
                }

                for (AdManageOperationLog log : adManageOperationLogs) {
                    if (CollectionUtils.isNotEmpty(resultData)) {
                        Map<String, String> failCampMap = resultData.stream().collect(Collectors.toMap(PortfolioErrorMsgVo::getCampaignId, PortfolioErrorMsgVo::getErrMsg));
                        if (failCampMap.containsKey(log.getCampaignId())) {
                            //修改失败的广告活动日志
                            log.setResult(OperationLogResultEnum.FAIL.getResultValue());
                            log.setResultInfo(failCampMap.get(log.getCampaignId()));
                        }
                    } else {
                        //修改成功的广告活动日志
                        log.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                    }
                }
                adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
                //end
            } else if (Constants.SB.equalsIgnoreCase(entry.getKey())) {
                result = batchUpdateSbPortFolio(uid, shopAuth, amazonAdProfile, cids, null);
                if (CollectionUtils.isNotEmpty(result.getData())) {
                    errorMsgVos.addAll(result.getData());
                }
                logBatchMovePortfolio(entry.getValue(), result.getData(), "", entry.getKey(), uid, loginIp);
            } else {
                result = batchUpdateSdPortFolio(uid, shopAuth, amazonAdProfile, cids, null);
                if (CollectionUtils.isNotEmpty(result.getData())) {
                    errorMsgVos.addAll(result.getData());
                }
                logBatchMovePortfolio(entry.getValue(), result.getData(), "", entry.getKey(), uid, loginIp);
            }

        }
        return result;
    }


    @Override
    public Result<List<PortfolioErrorMsgVo>> removeMultiplePortFolioData(Integer puid, Integer uid, String loginIp, List<SyncBasicDto> syncBasicDtoList) {
        if (CollectionUtils.isEmpty(syncBasicDtoList)) {
            return ResultUtil.error("参数错误!");
        }
        if (syncBasicDtoList.size() > 100) {
            return ResultUtil.error("选择活动数据大于100");
        }
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid, syncBasicDtoList.stream().map(SyncBasicDto::getShopId).collect(Collectors.toList()), syncBasicDtoList.stream().map(SyncBasicDto::getId).collect(Collectors.toList()));
        if (byCampaignIds.size() != syncBasicDtoList.size()) {
            return ResultUtil.error("存在广告活动非法");
        }
        Map<String, AmazonAdCampaignAll> campaignAllMap = StreamUtil.toMap(byCampaignIds, AmazonAdCampaignAll::getCampaignId);
        for (SyncBasicDto syncBasicDto : syncBasicDtoList) {
            syncBasicDto.setType(campaignAllMap.get(syncBasicDto.getId()).getType());
        }
        Map<Integer, List<SyncBasicDto>> listMap = syncBasicDtoList.stream().collect(Collectors.groupingBy(SyncBasicDto::getShopId));
        List<PortfolioErrorMsgVo> errorMsgVos = new ArrayList<>();
        listMap.forEach((shopId, value) -> {
            Map<String, List<SyncBasicDto>> typeList = value.stream().collect(Collectors.groupingBy(SyncBasicDto::getType));
            ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
            AmazonAdProfile amazonAdProfile = profileDao.getProfile(puid, shopId);
            typeList.forEach((type, syncList) ->{
                List<String> cids = syncList.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(type)) {
                    /**
                     * TODO 抄的代码 写的太丑了
                     * TODO 广告活动增加日志
                     * 操作类型：批量移除广告活动所属的广告组合
                     * 逻辑：创建广告新对象广告组合修改
                     * start
                     */
                    List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
                    for (SyncBasicDto syncBasicDto : syncList) {
                        AmazonAdCampaignAll adCampaign = campaignAllMap.get(syncBasicDto.getId());
                        AmazonAdCampaignAll newAmazonAdCampaign = new AmazonAdCampaignAll();
                        BeanUtils.copyProperties(adCampaign, newAmazonAdCampaign);
                        newAmazonAdCampaign.setPortfolioId("");
                        newAmazonAdCampaign.setUpdateId(uid);
                        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(adCampaign, newAmazonAdCampaign);
                        adManageOperationLog.setIp(loginIp);
                        adManageOperationLogs.add(adManageOperationLog);
                    }
                    Result<List<PortfolioErrorMsgVo>> result = batchUpdateSpPortFolio(uid, shopAuth, amazonAdProfile, cids, null);
                    List<PortfolioErrorMsgVo> resultData = result.getData();
                    if (resultData != null) {
                        errorMsgVos.addAll(resultData);
                    }
                    for (AdManageOperationLog log : adManageOperationLogs) {
                        if (CollectionUtils.isNotEmpty(resultData)) {
                            Map<String, String> failCampMap = resultData.stream().collect(Collectors.toMap(PortfolioErrorMsgVo::getCampaignId, PortfolioErrorMsgVo::getErrMsg));
                            if (failCampMap.containsKey(log.getCampaignId())) {
                                //修改失败的广告活动日志
                                log.setResult(OperationLogResultEnum.FAIL.getResultValue());
                                log.setResultInfo(failCampMap.get(log.getCampaignId()));
                            }
                        } else {
                            //修改成功的广告活动日志
                            log.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                        }
                    }
                    adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
                    //end
                } else if (Constants.SB.equalsIgnoreCase(type)) {
                    Result<List<PortfolioErrorMsgVo>> result = batchUpdateSbPortFolio(uid, shopAuth, amazonAdProfile, cids, null);
                    if (CollectionUtils.isNotEmpty(result.getData())) {
                        errorMsgVos.addAll(result.getData());
                    }
                    List<AmazonAdCampaignAll> list = syncList.stream().map( k -> campaignAllMap.get(k.getId())).collect(Collectors.toList());
                    logBatchMovePortfolio(list, result.getData(), "", type, uid, loginIp);
                } else {
                    Result<List<PortfolioErrorMsgVo>> result = batchUpdateSdPortFolio(uid, shopAuth, amazonAdProfile, cids, null);
                    if (CollectionUtils.isNotEmpty(result.getData())) {
                        errorMsgVos.addAll(result.getData());
                    }
                    List<AmazonAdCampaignAll> list = syncList.stream().map( k -> campaignAllMap.get(k.getId())).collect(Collectors.toList());
                    logBatchMovePortfolio(list, result.getData(), "", type, uid, loginIp);
                }
            });
        });
        return ResultUtil.success(errorMsgVos);
    }

    @Override
    public Result<List<PortfolioErrorMsgVo>> moveMultiplePortFolioData(Integer puid, Integer uid, String loginIp, List<SyncBasicDto> syncBasicDtoList) {
        if (CollectionUtils.isEmpty(syncBasicDtoList)) {
            return ResultUtil.error("参数错误!");
        }
        if (syncBasicDtoList.size() > 100) {
            return ResultUtil.error("选择活动数据大于100");
        }
        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid, syncBasicDtoList.stream().map(SyncBasicDto::getShopId).collect(Collectors.toList()), syncBasicDtoList.stream().map(SyncBasicDto::getId).collect(Collectors.toList()));
        if (byCampaignIds.size() != syncBasicDtoList.size()) {
            return ResultUtil.error("存在广告活动非法");
        }

        List<String> portfolioList = syncBasicDtoList.stream().map(SyncBasicDto::getPortfolioId).distinct().collect(Collectors.toList());
        List<AmazonAdPortfolio> amazonAdPortfolios = portfolioDao.listByShopId(puid, syncBasicDtoList.stream().map(SyncBasicDto::getShopId).collect(Collectors.toList()), syncBasicDtoList.stream().map(SyncBasicDto::getPortfolioId).collect(Collectors.toList()));
        if (portfolioList.size() != amazonAdPortfolios.size()) {
            return ResultUtil.error("存在广告组合非法");
        }

        List<PortfolioErrorMsgVo> errorMsgVos = new ArrayList<>();
        Map<String, AmazonAdCampaignAll> campaignAllMap = StreamUtil.toMap(byCampaignIds, AmazonAdCampaignAll::getCampaignId);
//        Map<String, AmazonAdPortfolio> portfolioAllMap = StreamUtil.toMap(amazonAdPortfolios, AmazonAdPortfolio::getPortfolioId);
        Map<Integer, List<SyncBasicDto>> listMap = syncBasicDtoList.stream().collect(Collectors.groupingBy(SyncBasicDto::getShopId));
        listMap.forEach((shopId, value) -> {
            Map<String, List<SyncBasicDto>> typeList = value.stream().collect(Collectors.groupingBy(SyncBasicDto::getType));
            ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
            AmazonAdProfile amazonAdProfile = profileDao.getProfile(puid, shopId);
            typeList.forEach((type, syncList) -> {
                List<String> cids = syncList.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(type)) {
                    /**
                     * TODO 广告活动增加日志
                     * 操作类型：批量修改广告活动所属的广告组合
                     * 逻辑：创建广告新对象广告组合修改，比较广告组合
                     * start
                     */
                    List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
                    for (SyncBasicDto syncBasicDto : syncList) {
                        AmazonAdCampaignAll adCampaign = campaignAllMap.get(syncBasicDto.getId());
                        AmazonAdCampaignAll newAmazonAdCampaign = new AmazonAdCampaignAll();
                        BeanUtils.copyProperties(adCampaign, newAmazonAdCampaign);
                        adCampaign.setPortfolioId(syncBasicDto.getPortfolioId());
                        adCampaign.setUpdateId(uid);
                        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(newAmazonAdCampaign, adCampaign);
                        adManageOperationLog.setIp(loginIp);
                        adManageOperationLogs.add(adManageOperationLog);
                    }
                    Result<List<PortfolioErrorMsgVo>> result = batchUpdateSpPortFolio(uid, shopAuth, amazonAdProfile, syncList);
                    List<PortfolioErrorMsgVo> resultData = result.getData();
                    for (AdManageOperationLog log : adManageOperationLogs) {
                        if (CollectionUtils.isNotEmpty(resultData)) {
                            //修改失败的广告活动日志
                            Map<String, String> failCampMap = resultData.stream().collect(Collectors.toMap(PortfolioErrorMsgVo::getCampaignId, PortfolioErrorMsgVo::getErrMsg));
                            if (failCampMap.containsKey(log.getCampaignId())) {
                                log.setResult(OperationLogResultEnum.FAIL.getResultValue());
                                log.setResultInfo(failCampMap.get(log.getCampaignId()));
                            }
                        } else {
                            //修改成功的广告活动日志
                            log.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                        }
                    }
                    if (CollectionUtils.isNotEmpty(resultData)) {
                        errorMsgVos.addAll(resultData);
                    }
                    adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
                    //end
                } else if (Constants.SB.equalsIgnoreCase(type)) {
                    Result<List<PortfolioErrorMsgVo>> result = batchUpdateSbPortFolio(uid, shopAuth, amazonAdProfile, syncList);
                    if (CollectionUtils.isNotEmpty(result.getData())) {
                        errorMsgVos.addAll(result.getData());
                    }

                    Map<String, List<SyncBasicDto>> listMap1 = syncList.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                    listMap1.forEach((k, v) -> {
                        List<AmazonAdCampaignAll> list = syncList.stream().map(a -> campaignAllMap.get(a.getId())).collect(Collectors.toList());
                        logBatchMovePortfolio(list, result.getData(), k, type, uid, loginIp);
                    });
                } else {
                    Result<List<PortfolioErrorMsgVo>> result = batchUpdateSdPortFolio(uid, shopAuth, amazonAdProfile, syncList);
                    if (CollectionUtils.isNotEmpty(result.getData())) {
                        errorMsgVos.addAll(result.getData());
                    }
                    Map<String, List<SyncBasicDto>> listMap1 = syncList.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                    listMap1.forEach((k, v) -> {
                        List<AmazonAdCampaignAll> list = syncList.stream().map(a -> campaignAllMap.get(a.getId())).collect(Collectors.toList());
                        logBatchMovePortfolio(list, result.getData(), k, type, uid, loginIp);
                    });
                }
            });
        });
        return ResultUtil.success(errorMsgVos);
    }

    @Override
    public Result<List<PortfolioErrorMsgVo>> movePortFolioData(Integer puid, Integer uid, Integer shopId, String type, String campaignIds, String portfolioId, String loginIp) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        AmazonAdProfile amazonAdProfile = profileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.error("店铺配置不存在");
        }

        List<String> campaignIdList = StringUtil.splitStr(campaignIds, ",");
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return ResultUtil.error("选择活动为空");
        }
        if (campaignIdList.size() > 100) {
            return ResultUtil.error("选择活动数据大于100");
        }
        List<PortfolioErrorMsgVo> errorMsgVos = new ArrayList<>();
        Result<List<PortfolioErrorMsgVo>> result = ResultUtil.success(errorMsgVos);

        List<AmazonAdCampaignAll> byCampaignIds = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, amazonAdProfile.getMarketplaceId(), campaignIdList, null);

        if (CollectionUtils.isEmpty(byCampaignIds)) {
            return ResultUtil.error("广告活动不存在");
        }

        Map<String, List<AmazonAdCampaignAll>> collect = byCampaignIds.stream().collect(Collectors.groupingBy(AmazonAdCampaignAll::getType));
        for (Map.Entry<String, List<AmazonAdCampaignAll>> entry : collect.entrySet()) {
            List<String> cids = entry.getValue().stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(entry.getKey())) {
                /**
                 * TODO 广告活动增加日志
                 * 操作类型：批量修改广告活动所属的广告组合
                 * 逻辑：创建广告新对象广告组合修改，比较广告组合
                 * start
                 */
                List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
                List<AmazonAdCampaignAll> amazonAdCampaigns = entry.getValue();
                for (AmazonAdCampaignAll adCampaign : amazonAdCampaigns) {
                    AmazonAdCampaignAll newAmazonAdCampaign = new AmazonAdCampaignAll();
                    BeanUtils.copyProperties(adCampaign, newAmazonAdCampaign);
                    adCampaign.setPortfolioId(portfolioId);
                    adCampaign.setUpdateId(uid);
                    AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(newAmazonAdCampaign, adCampaign);
                    adManageOperationLog.setIp(loginIp);
                    adManageOperationLogs.add(adManageOperationLog);
                }
                result = batchUpdateSpPortFolio(uid, shopAuth, amazonAdProfile, cids, portfolioId);

                List<PortfolioErrorMsgVo> resultData = result.getData();
                for (AdManageOperationLog log : adManageOperationLogs) {
                    if (CollectionUtils.isNotEmpty(resultData)) {
                        //修改失败的广告活动日志
                        Map<String, String> failCampMap = resultData.stream().collect(Collectors.toMap(PortfolioErrorMsgVo::getCampaignId, PortfolioErrorMsgVo::getErrMsg));
                        if (failCampMap.containsKey(log.getCampaignId())) {
                            log.setResult(OperationLogResultEnum.FAIL.getResultValue());
                            log.setResultInfo(failCampMap.get(log.getCampaignId()));
                        }
                    } else {
                        //修改成功的广告活动日志
                        log.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                    }
                }
                if (CollectionUtils.isNotEmpty(resultData)) {
                    errorMsgVos.addAll(resultData);
                }
                adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
                //end
            } else if (Constants.SB.equalsIgnoreCase(entry.getKey())) {
                result = batchUpdateSbPortFolio(uid, shopAuth, amazonAdProfile, cids, portfolioId);
                if (CollectionUtils.isNotEmpty(result.getData())) {
                    errorMsgVos.addAll(result.getData());
                }
                logBatchMovePortfolio(entry.getValue(), result.getData(), portfolioId, entry.getKey(), uid, loginIp);
            } else {
                result = batchUpdateSdPortFolio(uid, shopAuth, amazonAdProfile, cids, portfolioId);
                if (CollectionUtils.isNotEmpty(result.getData())) {
                    errorMsgVos.addAll(result.getData());
                }
                logBatchMovePortfolio(entry.getValue(), result.getData(), portfolioId, entry.getKey(), uid, loginIp);
            }

        }
        result.setData(errorMsgVos);
        return result;
    }

    private void logBatchMovePortfolio(List<AmazonAdCampaignAll> amazonAdCampaigns, List<PortfolioErrorMsgVo> errorMsgList, String portfolioId, String type, Integer uid, String ip) {
        try {
            if (CollectionUtils.isEmpty(amazonAdCampaigns)) {
                return;
            }
            Map<String, String> failMap = Maps.newHashMap();
            if (CollectionUtils.isNotEmpty(errorMsgList)) {
                // 修改失败的广告活动日志
                failMap = errorMsgList.stream().collect(Collectors.toMap(PortfolioErrorMsgVo::getCampaignId, PortfolioErrorMsgVo::getErrMsg));
            }
            List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayList();
            for (AmazonAdCampaignAll adCampaign : amazonAdCampaigns) {
                AmazonAdCampaignAll newAmazonAdCampaign = new AmazonAdCampaignAll();
                BeanUtils.copyProperties(adCampaign, newAmazonAdCampaign);
                newAmazonAdCampaign.setPortfolioId(portfolioId);
                newAmazonAdCampaign.setUpdateId(uid);
                AdManageOperationLog adManageOperationLog;
                if (Constants.SB.equalsIgnoreCase(type)) {
                    adManageOperationLog = adManageOperationLogService.getSbCampaignOperationLog(adCampaign, newAmazonAdCampaign);
                } else {
                    adManageOperationLog = adManageOperationLogService.getSdAdManageOperationLog(adCampaign, newAmazonAdCampaign);
                }
                adManageOperationLog.setIp(ip);
                if (failMap.containsKey(adCampaign.getCampaignId())) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(failMap.get(adCampaign.getCampaignId()));
                } else {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                adManageOperationLogs.add(adManageOperationLog);
            }
            adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        } catch (Exception e) {
            log.error("logBatchMovePortfolio error", e);
        }
    }

    /**
     * 批量修改sp广告组合
     *
     * @param shopAuth
     * @param amazonAdProfile
     * @param campaignIdList
     * @return
     */
    private Result<List<PortfolioErrorMsgVo>> batchUpdateSpPortFolio(Integer uid, ShopAuth shopAuth, AmazonAdProfile amazonAdProfile, List<String> campaignIdList, String portfolioId) {
        Result<List<PortfolioErrorMsgVo>> result;

        List<CampaignEntityV3> campaignList = new ArrayList<>();
        CampaignEntityV3 campaign;
        for (String campaignId : campaignIdList) {
            campaign = new CampaignEntityV3();
            campaign.setCampaignId(campaignId);
            if (StringUtils.isNotBlank(portfolioId)) {
                campaign.setPortfolioId(portfolioId);
            } else {
                campaign.setPortfolioId("");  //表示删除广告组合
            }
            campaignList.add(campaign);
        }

        result = cpcCampaignApiService.batchUpdateSpCampaignsPortfolio(shopAuth, amazonAdProfile, campaignList);
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<String> failList = resultData.stream().map(PortfolioErrorMsgVo::getCampaignId).collect(Collectors.toList());

                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(),
                        shopAuth.getMarketplaceId(), campaignIdList, CampaignTypeEnum.sp.getCampaignType());
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if (spCampaignMap != null) {
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        if (spCampaignMap.containsKey(msgVo.getCampaignId())) {
                            msgVo.setName(spCampaignMap.get(msgVo.getCampaignId()).getName());
                        }
                    }
                }

                //取差集
                campaignIdList.removeAll(failList);
                if (CollectionUtils.isNotEmpty(campaignIdList)) {  //修改成功的活动
                    amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), portfolioId, uid, campaignIdList);
                    saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), campaignIdList);
                }
            } else {
                //修改成功的活动
                amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), portfolioId, uid, campaignIdList);
                saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), campaignIdList);
                return ResultUtil.success();
            }
        }

        return result;
    }

    private Result<List<PortfolioErrorMsgVo>> batchUpdateSpPortFolio(Integer uid, ShopAuth shopAuth, AmazonAdProfile amazonAdProfile, List<SyncBasicDto> campaignIdList) {
        Result<List<PortfolioErrorMsgVo>> result;

        List<CampaignEntityV3> campaignList = new ArrayList<>();
        CampaignEntityV3 campaign;
        for (SyncBasicDto campaignId : campaignIdList) {
            campaign = new CampaignEntityV3();
            campaign.setCampaignId(campaignId.getId());
            if (StringUtils.isNotBlank(campaignId.getPortfolioId())) {
                campaign.setPortfolioId(campaignId.getPortfolioId());
            } else {
                campaign.setPortfolioId("");  //表示删除广告组合
            }
            campaignList.add(campaign);
        }
        List<String> campaigns = campaignIdList.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
        result = cpcCampaignApiService.batchUpdateSpCampaignsPortfolio(shopAuth, amazonAdProfile, campaignList);
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<String> failList = resultData.stream().map(PortfolioErrorMsgVo::getCampaignId).collect(Collectors.toList());
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(),
                        shopAuth.getMarketplaceId(), campaigns, CampaignTypeEnum.sp.getCampaignType());
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if (spCampaignMap != null) {
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        if (spCampaignMap.containsKey(msgVo.getCampaignId())) {
                            msgVo.setName(spCampaignMap.get(msgVo.getCampaignId()).getName());
                        }
                    }
                }
                //取差集
                campaigns.removeAll(failList);
                if (CollectionUtils.isNotEmpty(campaigns)) {  //修改成功的活动
                    List<SyncBasicDto> list = campaignIdList.stream().filter(k -> campaigns.contains(k.getId())).collect(Collectors.toList());
                    Map<String, List<SyncBasicDto>> listMap = list.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                    listMap.forEach((k, v) -> {
                        List<String> ids = v.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                        amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), k, uid, ids);
                        saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), ids);
                    });
                }
            } else {
                Map<String, List<SyncBasicDto>> listMap = campaignIdList.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                listMap.forEach((k, v) -> {
                    List<String> ids = v.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                    amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), k, uid, ids);
                    saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), ids);
                });
            }
        }
        return result;
    }


    /**
     * 批量修改sb广告组合
     *
     * @param shopAuth
     * @param amazonAdProfile
     * @param campaignIdList
     * @return
     */
    private Result<List<PortfolioErrorMsgVo>> batchUpdateSbPortFolio(Integer uid, ShopAuth shopAuth, AmazonAdProfile amazonAdProfile, List<String> campaignIdList, String portfolioId) {
        Result<List<PortfolioErrorMsgVo>> result;
        List<V4Campaign> campaignList = campaignIdList.stream().map(campaignId -> {
            V4Campaign v4Campaign = new V4Campaign();
            v4Campaign.setCampaignId(campaignId);
            // "" 表示删除广告组合
            v4Campaign.setPortfolioId(StringUtils.isBlank(portfolioId) ? "" : portfolioId);
            return v4Campaign;
        }).collect(Collectors.toList());

        result = cpcSbCampaignApiService.batchUpdateCampaignsV4(shopAuth, amazonAdProfile, campaignList);

        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            Map<String, AmazonAdCampaignAll> sbCampaignMap = null;
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<String> failList = resultData.stream().map(PortfolioErrorMsgVo::getCampaignId).collect(Collectors.toList());
                List<AmazonAdCampaignAll> sbAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), campaignIdList, CampaignTypeEnum.sb.getCampaignType());
                if (CollectionUtils.isNotEmpty(sbAdCampaigns)) {
                    sbCampaignMap = sbAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if (sbCampaignMap != null) {
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        if (sbCampaignMap.containsKey(msgVo.getCampaignId())) {
                            msgVo.setName(sbCampaignMap.get(msgVo.getCampaignId()).getName());
                        }
                    }
                }
                //取差集
                campaignIdList.removeAll(failList);
                if (CollectionUtils.isNotEmpty(campaignIdList)) {  //修改成功的活动
                    amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), portfolioId, uid, campaignIdList);
                    saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), campaignIdList);
                }
            } else {
                //修改成功的活动
                amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), portfolioId, uid, campaignIdList);
                saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), campaignIdList);
                return ResultUtil.success();
            }
        }

        return result;
    }


    private Result<List<PortfolioErrorMsgVo>> batchUpdateSbPortFolio(Integer uid, ShopAuth shopAuth, AmazonAdProfile amazonAdProfile, List<SyncBasicDto> syncBasicDtoList) {
        Result<List<PortfolioErrorMsgVo>> result;
        List<V4Campaign> campaignList = syncBasicDtoList.stream().map(campaignId -> {
            V4Campaign v4Campaign = new V4Campaign();
            v4Campaign.setCampaignId(campaignId.getId());
            // "" 表示删除广告组合
            v4Campaign.setPortfolioId(StringUtils.isBlank(campaignId.getPortfolioId()) ? "" : campaignId.getPortfolioId());
            return v4Campaign;
        }).collect(Collectors.toList());
        result = cpcSbCampaignApiService.batchUpdateCampaignsV4(shopAuth, amazonAdProfile, campaignList);
        List<String> campaigns = syncBasicDtoList.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            Map<String, AmazonAdCampaignAll> sbCampaignMap = null;
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<String> failList = resultData.stream().map(PortfolioErrorMsgVo::getCampaignId).collect(Collectors.toList());

                List<AmazonAdCampaignAll> sbAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), campaigns, CampaignTypeEnum.sb.getCampaignType());
                if (CollectionUtils.isNotEmpty(sbAdCampaigns)) {
                    sbCampaignMap = sbAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if (sbCampaignMap != null) {
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        if (sbCampaignMap.containsKey(msgVo.getCampaignId())) {
                            msgVo.setName(sbCampaignMap.get(msgVo.getCampaignId()).getName());
                        }
                    }
                }
                //取差集
                campaigns.removeAll(failList);
                if (CollectionUtils.isNotEmpty(campaigns)) {  //修改成功的活动
                    List<SyncBasicDto> list = syncBasicDtoList.stream().filter(k -> campaigns.contains(k.getId())).collect(Collectors.toList());
                    Map<String, List<SyncBasicDto>> listMap = list.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                    listMap.forEach((k, v) -> {
                        List<String> ids = v.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                        amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), k, uid, ids);
                        saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), ids);
                    });
                }
            } else {
                Map<String, List<SyncBasicDto>> listMap = syncBasicDtoList.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                listMap.forEach((k, v) -> {
                    List<String> ids = v.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                    amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), k, uid, ids);
                    saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), ids);
                });
            }
        }
        return result;
    }

    /**
     * 批量修改sd广告组合
     *
     * @param shopAuth
     * @param amazonAdProfile
     * @param campaignIdList
     * @return
     */
    private Result<List<PortfolioErrorMsgVo>> batchUpdateSdPortFolio(Integer uid, ShopAuth shopAuth, AmazonAdProfile amazonAdProfile, List<String> campaignIdList, String portfolioId) {
        Result<List<PortfolioErrorMsgVo>> result;
        List<com.amazon.advertising.sd.mode.Campaign> campaignList = new ArrayList<>();
        com.amazon.advertising.sd.mode.Campaign campaign;
        for (String campaignId : campaignIdList) {
            campaign = new com.amazon.advertising.sd.mode.Campaign();
            campaign.setCampaignId(Long.valueOf(campaignId));
            if (StringUtils.isNotBlank(portfolioId)) {
                campaign.setPortfolioId(Long.valueOf(portfolioId));
            } else {
                campaign.setPortfolioId(0L);  //表示删除广告组合
            }
            campaignList.add(campaign);
        }
        result = cpcSdCampaignApiService.batchUpdateCampaigns(shopAuth, amazonAdProfile, campaignList);
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            Map<String, AmazonAdCampaignAll> sdCampaignMap = null;
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<String> failList = resultData.stream().map(PortfolioErrorMsgVo::getCampaignId).collect(Collectors.toList());

                List<AmazonAdCampaignAll> sdAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), campaignIdList, CampaignTypeEnum.sd.getCampaignType());
                if (CollectionUtils.isNotEmpty(sdAdCampaigns)) {
                    sdCampaignMap = sdAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if (sdCampaignMap != null) {
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        if (sdCampaignMap.containsKey(msgVo.getCampaignId())) {
                            msgVo.setName(sdCampaignMap.get(msgVo.getCampaignId()).getName());
                        }
                    }
                }

                //取差集
                campaignIdList.removeAll(failList);
                if (CollectionUtils.isNotEmpty(campaignIdList)) {  //修改成功的活动
                    amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), portfolioId, uid, campaignIdList);
                    saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), campaignIdList);
                }
            } else {
                //修改成功的活动
                amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), portfolioId, uid, campaignIdList);
                saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), campaignIdList);
                return ResultUtil.success();
            }
        }

        return result;
    }

    private Result<List<PortfolioErrorMsgVo>> batchUpdateSdPortFolio(Integer uid, ShopAuth shopAuth, AmazonAdProfile amazonAdProfile, List<SyncBasicDto> syncBasicDtoList) {
        Result<List<PortfolioErrorMsgVo>> result;
        List<com.amazon.advertising.sd.mode.Campaign> campaignList = new ArrayList<>();
        com.amazon.advertising.sd.mode.Campaign campaign;
        for (SyncBasicDto campaignId : syncBasicDtoList) {
            campaign = new com.amazon.advertising.sd.mode.Campaign();
            campaign.setCampaignId(Long.valueOf(campaignId.getId()));
            if (StringUtils.isNotBlank(campaignId.getPortfolioId())) {
                campaign.setPortfolioId(Long.valueOf(campaignId.getPortfolioId()));
            } else {
                campaign.setPortfolioId(0L);  //表示删除广告组合
            }
            campaignList.add(campaign);
        }

        List<String> campaignIds = syncBasicDtoList.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
        result = cpcSdCampaignApiService.batchUpdateCampaigns(shopAuth, amazonAdProfile, campaignList);
        if (result.success()) {
            List<PortfolioErrorMsgVo> resultData = result.getData();
            Map<String, AmazonAdCampaignAll> sdCampaignMap = null;
            if (CollectionUtils.isNotEmpty(resultData)) {
                List<String> failList = resultData.stream().map(PortfolioErrorMsgVo::getCampaignId).collect(Collectors.toList());

                List<AmazonAdCampaignAll> sdAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), campaignIds, CampaignTypeEnum.sd.getCampaignType());
                if (CollectionUtils.isNotEmpty(sdAdCampaigns)) {
                    sdCampaignMap = sdAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
                if (sdCampaignMap != null) {
                    for (PortfolioErrorMsgVo msgVo : resultData) {
                        if (sdCampaignMap.containsKey(msgVo.getCampaignId())) {
                            msgVo.setName(sdCampaignMap.get(msgVo.getCampaignId()).getName());
                        }
                    }
                }

                //取差集
                campaignIds.removeAll(failList);
                if (CollectionUtils.isNotEmpty(campaignIds)) {  //修改成功的活动
                    List<SyncBasicDto> list = syncBasicDtoList.stream().filter(k -> campaignIds.contains(k.getId())).collect(Collectors.toList());
                    Map<String, List<SyncBasicDto>> listMap = list.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                    listMap.forEach((k, v) -> {
                        List<String> ids = v.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                        amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), k, uid, ids);
                        saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), ids);
                    });
                }
            } else {
                //修改成功的活动
                Map<String, List<SyncBasicDto>> listMap = syncBasicDtoList.stream().collect(Collectors.groupingBy(SyncBasicDto::getPortfolioId));
                listMap.forEach((k, v) -> {
                    List<String> ids = v.stream().map(SyncBasicDto::getId).collect(Collectors.toList());
                    amazonAdCampaignAllDao.batchUpdatePortfolio(shopAuth.getPuid(), shopAuth.getId(), k, uid, ids);
                    saveDoris4Campaign(shopAuth.getPuid(), shopAuth.getId(), ids);
                });
            }
        }
        return result;
    }


    @Override
    public Result updateRank(Integer puid, Integer uid, Integer shopId, String portfolioId, Integer rank, Integer oldRank) {
        AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, shopId, portfolioId);
        if (amazonAdPortfolio == null) {
            return ResultUtil.error("对象不存在");
        }

        Integer integer = portfolioDao.sumRank(puid, shopId);
        if (null == integer || integer == 0) {
            //初始化一次排序
            portfolioDao.sortPortfolioRankByRankLastUpdateTime(puid, shopId);
        }
        portfolioDao.updatePortfolioRank(puid, shopId, portfolioId, rank + 1);
        if (rank > oldRank) {
            portfolioDao.sortPortfolioRankByRankUpdateTimeAsc(puid, shopId, rank);
        } else {
            portfolioDao.sortPortfolioRankByRankUpdateTime(puid, shopId, rank);
        }

        return ResultUtil.success();

    }

    /**
     * 写入doris
     *
     * @param portfolioList
     */
    @Override
    public void saveDoris(List<AmazonAdPortfolio> portfolioList) {
        try {
            List<OdsAmazonAdPortfolio> collect = portfolioList.stream().map(x -> {
                OdsAmazonAdPortfolio odsAmazonAdPortfolio = new OdsAmazonAdPortfolio();
                BeanUtils.copyProperties(x, odsAmazonAdPortfolio);
                return odsAmazonAdPortfolio;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("portfolio save doris error", e);
        }
    }

    @Override
    public List<KeywordLibsPortfolioListVO> getAllPortfolioName(Integer puid, List<Integer> shopIds, List<String> portfolioIds) {
        return portfolioDao.getAllPortfolioName(puid, shopIds, portfolioIds);
    }

    @Override
    public List<KeywordLibsPortfolioListVO> getAllPortfolioName(Integer puid, List<Integer> shopIds) {
        return portfolioDao.getAllPortfolioName(puid, shopIds);
    }

    @Override
    public Page<KeywordLibsPortfolioListVO> getPortfolioName(Integer puid, KeywordLibsPageParam param) {
        return portfolioDao.getPortfolioName(puid, param);
    }

    /**
     * 查询mysql并写入doris
     *
     * @param puid
     * @param shopId
     * @param portfolioIdList
     */
    private void saveDoris(Integer puid, Integer shopId, List<String> portfolioIdList) {
        try {
            if (CollectionUtils.isEmpty(portfolioIdList)) {
                return;
            }
            List<AmazonAdPortfolio> portfolioList = portfolioDao.getPortfolioList(puid, shopId, portfolioIdList);
            saveDoris(portfolioList);
        } catch (Exception e) {
            log.error("portfolio save doris error", e);
        }
    }

    /**
     * 写入doris
     *
     * @param campaignIdList
     */
    private void saveDoris4Campaign(Integer puid, Integer shopId, List<String> campaignIdList) {
        try {
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return;
            }
            List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.listByCampaignIdNoType(puid, shopId, campaignIdList);
            List<OdsAmazonAdCampaignAll> collect = campaignAllList.stream().map(x -> {
                OdsAmazonAdCampaignAll odsAmazonAdCampaignAll = new OdsAmazonAdCampaignAll();
                BeanUtils.copyProperties(x, odsAmazonAdCampaignAll);
                if (StringUtils.isNotBlank(odsAmazonAdCampaignAll.getState())) {
                    odsAmazonAdCampaignAll.setState(odsAmazonAdCampaignAll.getState().toLowerCase());
                }
                return odsAmazonAdCampaignAll;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }

    /**
     * puid是否可以访问doris
     */
    private boolean queryDoris(Integer puid) {
        Set<String> whiteDorisPuidSet = dynamicRefreshConfiguration.getDorisPageCampaign();
        return CollectionUtils.isNotEmpty(whiteDorisPuidSet) && (whiteDorisPuidSet.contains("all") || whiteDorisPuidSet.contains(puid));
    }
}
