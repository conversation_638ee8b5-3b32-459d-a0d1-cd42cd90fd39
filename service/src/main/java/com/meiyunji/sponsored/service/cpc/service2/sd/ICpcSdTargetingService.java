package com.meiyunji.sponsored.service.cpc.service2.sd;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.rpc.sd.targeting.SdTargetingRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdTargeting;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.vo.*;

import java.util.List;

/**
 * Created by xp on 2021/3/29.
 * 广告活动业务
 */
public interface ICpcSdTargetingService {

    Result pageList(TargetingPageParam param);

    Result showTargetPerformance(int puid, AdPerformanceParam param);

    Result createTargeting(AddSdTargetingVo addTargetingVo);
    NewCreateResultResultVo<SBCommonErrorVo> createTargetingNew(AddSdTargetingVo addTargetingVo, AmazonSdAdGroup amazonSdAdGroup,
                              ShopAuth shop, AmazonAdProfile profile, Integer uid, String loginIp);

    NewCreateResultResultVo<SBCommonErrorVo> sdCreateTargetingNew(List<SdTargetingVo> targetingList, String campaignId,
                                                                  String adGroupId, ShopAuth shop,
                                                                  AmazonAdProfile profile, Integer uid, String loginId);

    Result update(UpdateSdTargetingVo updateSdTargetingVo);

    Result archive(Integer puid, Integer shopId, Integer uid, String targetId, String loginIp);

    Result suggestCategory(int puid, Integer shopId, String groupId);
    Result suggestCategoryNew(int puid, ShopAuth shop, List<String> asinList, String predicate);
    Result suggestProductNew(int puid, ShopAuth shop, List<String> asinList);
    Result suggestAudiences(int puid, ShopAuth shop, List<String> asinList);

    /**
     * 列表页按指定target获取指定投放的建议竞价，目前支持asin投放
     * @param puid:
     * @param shopId:
     * @param targetIds:
     */
    Result getSuggestedBid(int puid, Integer shopId, List<String> targetIds);
    Result<List<SdSuggestedTargetVo>> getSuggestedBidNew(List<String> asins, ShopAuth shop,
                                                         List<SdTargetingVo> targetVo, AmazonAdProfile profile,
                                                         String bidOptimization, String creativeType);

    Result batchGetSuggestedBid(int puid, Integer shopId, List<Integer> sourceShopIds, List<TargetSuggestedBidDetail> details, String targetingType);

    /**
     * 多店铺，列表页按指定target获取指定投放的建议竞价，目前支持asin投放
     */
    Result<List<SdSuggestedTargetVo>> getSuggestedBidMultiShop(int puid, List<TargetSuggestBidBatchQo> qoList);

    /**
     * 创建投放时获取建议竞价
     * @param addTargetingVo :
     */
    Result getSuggestedBidByText(AddSdTargetingVo addTargetingVo);

    Result updateBatch(List<UpdateBatchTargetVo> vos, String type, String ip);

    Result updateBatchMultiShop(List<UpdateBatchTargetVo> vos, String type, String loginIp);

    void saveDoris(List<AmazonSdAdTargeting> list, boolean create, boolean update);

    void saveDoris(int puid, int shopId, List<String> targetIdList);
}
