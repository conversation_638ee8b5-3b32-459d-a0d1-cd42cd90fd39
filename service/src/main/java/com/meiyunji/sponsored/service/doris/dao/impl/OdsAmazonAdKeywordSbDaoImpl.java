package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.KeywordLibsVo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.newDashboard.enums.ThemeKeywordTextEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdKeywordSbDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordSb;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Arrays;
import java.util.List;

/**
 * amazon SB 关键词表(OdsAmazonAdKeywordSb)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:18
 */
@Repository
public class OdsAmazonAdKeywordSbDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdKeywordSb> implements IOdsAmazonAdKeywordSbDao {

    @Override
    public List<OdsAmazonAdKeywordSb> getByKeywordIds(int puid, Integer shopId, List<String> keywordIds) {
        StringBuilder sb = new StringBuilder("select * from ").append(this.getJdbcHelper().getTable())
                .append(" where puid = ? and shop_id = ? ");
        List<Object> argsList = new ArrayList<>();
        argsList.add(puid);
        argsList.add(shopId);
        sb.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, argsList));
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(OdsAmazonAdKeywordSb.class));
    }

    @Override
    public List<OdsAmazonAdKeywordSb> getByKeywordIds(int puid, List<Integer> shopIds, List<String> keywordIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuffer selectSql = new StringBuffer("SELECT * ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" k ");
        selectSql.append(" where k.puid = ? ");
        argsList.add(puid);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(shopIds)) {
            selectSql.append(SqlStringUtil.dealInList("k.shop_id", shopIds, argsList));
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(keywordIds)) {
            selectSql.append(SqlStringUtil.dealInList("k.keyword_id", keywordIds, argsList));
        }
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new ObjectMapper<>(OdsAmazonAdKeywordSb.class));
    }

    @Override
    public List<OdsAmazonAdKeywordSb> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid",puid)
                .equalTo("shop_id",shopId)
                .inStrList("ad_group_id", groupIdList.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(),CpcStatusEnum.paused.name()})
                .build();
        String sql = "select ad_group_id, keyword_text, match_type from " + this.getJdbcHelper().getTable() + " where " + builder.getSql();
        return getJdbcTemplate().query(sql, builder.getValues(), new BeanPropertyRowMapper<>(OdsAmazonAdKeywordSb.class));
    }

    @Override
    public List<KeywordLibsVo> getSbKeywordCount(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, count(*) targetNum from ods_t_amazon_ad_keyword_sb " +
                "where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordTexts)){
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : keywordTexts) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(keyword_text)",lowerCaseList,argsList));
        }
        sql.append(" group by keyword_text");
        return getJdbcTemplate().query(sql.toString(), new ObjectMapper<>(KeywordLibsVo.class), argsList.toArray());
    }

    @Override
    public List<OdsAmazonAdKeywordSb> getKeywordText(Integer puid, int start, int limit) {
        String sql = "select puid, shop_id, keyword_id, keyword_text, keyword_size from ods_t_amazon_ad_keyword_sb where puid = ? order by id limit ?, ?";
        return getJdbcTemplate().queryForList(sql, OdsAmazonAdKeywordSb.class, puid, start, limit);
    }

    @Override
    public void updateKeywordSizeById(Integer puid, List<OdsAmazonAdKeywordSb> keywords) {
        if (CollectionUtils.isEmpty(keywords)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(keywords.size());
        List<Object> arg;
        for (OdsAmazonAdKeywordSb keyword : keywords) {
            arg = new ArrayList<>();
            arg.add(keyword.getKeywordSize());
            arg.add(keyword.getPuid());
            arg.add(keyword.getShopId());
            arg.add(keyword.getKeywordId());
            arg.add(keyword.getKeywordText());
        }

        String sql = "update " + getJdbcHelper().getTable() + " set keyword_size = ? where puid = ? and shop_id = ? and keyword_id = ? and keyword_text = ?";
        getJdbcTemplate().batchUpdate(sql, argList);
    }

    @Override
    public int listAllKeywordByCondition(Integer puid, RepeatTargetingDetailPageVo detailPageVo) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select count(*) from (select count(*) from ods_t_amazon_ad_keyword_sb k ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(" left join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id and k.marketplace_id = r.marketplace_id ");
            sql.append(" and r.puid = ? and r.marketplace_id = ? ");
            args.add(puid);
            args.add(detailPageVo.getMarketplaceId());
            sql.append(" and lower(r.keyword_text) = ? and lower(r.match_type) = ? ");
            if (ThemeKeywordTextEnum.getTextByCode(detailPageVo.getKeywordText()) != null) {
                args.add(ThemeKeywordTextEnum.getTextByCode(detailPageVo.getKeywordText()));
            } else {
                args.add(detailPageVo.getKeywordText().toLowerCase());
            }
            args.add(detailPageVo.getDetailMatchType().trim().toLowerCase());
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            args.add(detailPageVo.getStartDate());
            args.add(detailPageVo.getEndDate());
        }
        sql.append(this.whereSql(puid, detailPageVo, args));
        //竞价高级筛选
        if (detailPageVo.getUseAdvanced()) {
            if (detailPageVo.getBidMin() != null) {
                sql.append(" and k.bid >= ? ");
                args.add(detailPageVo.getBidMin());
            }
            if (detailPageVo.getBidMax() != null) {
                sql.append(" and k.bid <= ? ");
                args.add(detailPageVo.getBidMax());
            }
        }
        sql.append(" group by k.keyword_id, k.ad_group_id ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(this.getHavingSql(detailPageVo, args));
        }
        sql.append(" ) d ");
        return countPageResult(puid, sql.toString(), args.toArray());
    }

    @Override
    public List<RepeatTargetingDetailVo> getKeywordIds(Integer puid, RepeatTargetingDetailPageVo detailPageVo) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select k.keyword_id keywordId, k.ad_group_id adGroupId  from ods_t_amazon_ad_keyword_sb k ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(" left join ods_t_amazon_ad_sb_keyword_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.keyword_id = r.keyword_id and k.marketplace_id = r.marketplace_id ");
            sql.append(" and r.puid = ? and r.marketplace_id = ? ");
            args.add(puid);
            args.add(detailPageVo.getMarketplaceId());
            sql.append(" and lower(r.keyword_text) = ? and lower(r.match_type) = ? ");
            if (ThemeKeywordTextEnum.getTextByCode(detailPageVo.getKeywordText()) != null) {
                args.add(ThemeKeywordTextEnum.getTextByCode(detailPageVo.getKeywordText()));
            } else {
                args.add(detailPageVo.getKeywordText().toLowerCase());
            }
            args.add(detailPageVo.getDetailMatchType().trim().toLowerCase());
            sql.append(" and r.count_day >= ? and r.count_day <= ? ");
            args.add(detailPageVo.getStartDate());
            args.add(detailPageVo.getEndDate());
        }
        sql.append(this.whereSql(puid, detailPageVo, args));
        //竞价高级筛选
        if (detailPageVo.getUseAdvanced()) {
            if (detailPageVo.getBidMin() != null) {
                sql.append(" and k.bid >= ? ");
                args.add(detailPageVo.getBidMin());
            }
            if (detailPageVo.getBidMax() != null) {
                sql.append(" and k.bid <= ? ");
                args.add(detailPageVo.getBidMax());
            }
        }
        sql.append(" group by k.keyword_id, k.ad_group_id ");
        //高级筛选
        if (detailPageVo.getUseAdvanced() && this.isBoolean(detailPageVo)) {
            sql.append(this.getHavingSql(detailPageVo, args));
        }
        return getJdbcTemplate().query(sql.toString(), args.toArray(), new ObjectMapper<>(RepeatTargetingDetailVo.class));
    }

    private String whereSql(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<Object> args) {
        StringBuilder sql = new StringBuilder();
        sql.append("where k.puid = ? and k.marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        sql.append(" and k.keyword_text = ? and lower(k.match_type) = ? ");
        args.add(detailPageVo.getKeywordText());
        args.add(detailPageVo.getDetailMatchType().trim().toLowerCase());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("k.shop_id", detailPageVo.getShopIdList(), args));
        }
        //广告组合和广告活动筛选
        if (CollectionUtils.isNotEmpty(detailPageVo.getCampaignIdList())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", detailPageVo.getCampaignIdList(), args));
        }
        //广告组筛选
        if (CollectionUtils.isNotEmpty(detailPageVo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("k.ad_group_id", detailPageVo.getSbAdGroupId(), args));
        }
        //投放运行状态
        if (CollectionUtils.isNotEmpty(detailPageVo.getStatus())) {
            sql.append(SqlStringUtil.dealInList("k.state", detailPageVo.getStatus(), args));
        }
        return sql.toString();
    }

    private Boolean isBoolean(RepeatTargetingDetailPageVo detailPageVo) {
        if (detailPageVo.getBidMin() == null && detailPageVo.getBidMax() == null &&
                (
                        detailPageVo.getImpressionsMin() != null || detailPageVo.getImpressionsMax() != null ||
                                detailPageVo.getClicksMin() != null || detailPageVo.getClicksMax() != null ||
                                detailPageVo.getClickRateMin() != null || detailPageVo.getClickRateMax() != null ||
                                detailPageVo.getCostMin() != null || detailPageVo.getCostMax() != null ||
                                detailPageVo.getCpaMin() != null || detailPageVo.getCpaMax() != null ||
                                detailPageVo.getOrderNumMin() != null || detailPageVo.getOrderNumMax() != null ||
                                detailPageVo.getSalesMin() != null || detailPageVo.getSalesMax() != null ||
                                detailPageVo.getAcosMin() != null || detailPageVo.getAcosMax() != null ||
                                detailPageVo.getRoasMin() != null || detailPageVo.getRoasMax() != null ||
                                detailPageVo.getSalesConversionRateMin() != null || detailPageVo.getSalesConversionRateMax() != null ||
                                detailPageVo.getCpcMin() != null || detailPageVo.getCpcMax() != null
                )
        ) {
            return true;
        } else {
            return false;
        }
    }

    private String getHavingSql(RepeatTargetingDetailPageVo detailPageVo, List<Object> args) {
        if (!detailPageVo.getUseAdvanced()) {
            return "";
        }
        StringBuilder havingSql = new StringBuilder();
        havingSql.append(" having 1=1 ");
        //展示量
        if (detailPageVo.getImpressionsMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.impressions), 0) >= ?");
            args.add(detailPageVo.getImpressionsMin());
        }
        if (detailPageVo.getImpressionsMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.impressions), 0) <= ?");
            args.add(detailPageVo.getImpressionsMax());
        }
        //点击量
        if (detailPageVo.getClicksMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.clicks), 0) >= ?");
            args.add(detailPageVo.getClicksMin());
        }
        if (detailPageVo.getClicksMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.clicks), 0) <= ?");
            args.add(detailPageVo.getClicksMax());
        }
        //点击率（clicks/impressions）
        if (detailPageVo.getClickRateMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.clicks)/SUM(r.impressions),0),4) >= ?");
            args.add(detailPageVo.getClickRateMin());
        }
        if (detailPageVo.getClickRateMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.clicks)/SUM(r.impressions),0),4) <= ?");
            args.add(detailPageVo.getClickRateMax());
        }
        //花费
        if (detailPageVo.getCostMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.cost), 0) >= ?");
            args.add(detailPageVo.getCostMin());
        }
        if (detailPageVo.getCostMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.cost), 0) <= ?");
            args.add(detailPageVo.getCostMax());
        }
        //cpc  平均点击费用
        if (detailPageVo.getCpcMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.clicks),0),2) >= ?");
            args.add(detailPageVo.getCpcMin());
        }
        if (detailPageVo.getCpcMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.clicks),0),2) <= ?");
            args.add(detailPageVo.getCpcMax());
        }
        //广告订单量
        if (detailPageVo.getOrderNumMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.conversions14d), 0) >= ?");
            args.add(detailPageVo.getOrderNumMin());
        }
        if (detailPageVo.getOrderNumMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.conversions14d), 0) <= ?");
            args.add(detailPageVo.getOrderNumMax());
        }
        //广告销售额
        if (detailPageVo.getSalesMin() != null) {
            havingSql.append(" and IFNULL(SUM(r.sales14d), 0) >= ?");
            args.add(detailPageVo.getSalesMin());
        }
        if (detailPageVo.getSalesMax() != null) {
            havingSql.append(" and IFNULL(SUM(r.sales14d), 0) <= ?");
            args.add(detailPageVo.getSalesMax());
        }
        //订单转化率
        if (detailPageVo.getSalesConversionRateMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.conversions14d)/SUM(r.clicks),0),4) >= ?");
            args.add(detailPageVo.getSalesConversionRateMin());
        }
        if (detailPageVo.getSalesConversionRateMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.conversions14d)/SUM(r.clicks),0),4) <= ?");
            args.add(detailPageVo.getSalesConversionRateMax());
        }
        //acos
        if (detailPageVo.getAcosMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.sales14d),0),4) >= ?");
            args.add(detailPageVo.getAcosMin());
        }
        if (detailPageVo.getAcosMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.cost)/SUM(r.sales14d),0),4) <= ?");
            args.add(detailPageVo.getAcosMax());
        }
        // roas
        if (detailPageVo.getRoasMin() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.sales14d)/SUM(r.cost),0),2) >= ?");
            args.add(detailPageVo.getRoasMin());
        }
        // roas
        if (detailPageVo.getRoasMax() != null) {
            havingSql.append(" and ROUND(IFNULL(SUM(r.sales14d)/SUM(r.cost),0),2) <= ?");
            args.add(detailPageVo.getRoasMax());
        }
        //CPA
        if (detailPageVo.getCpaMin() != null) {
            havingSql.append(" and ROUND(ROUND(IFNULL(SUM(r.cost)/SUM(r.conversions14d),0), 4), 2) >= ?");
            args.add(detailPageVo.getCpaMin());
        }
        if (detailPageVo.getCpaMax() != null) {
            havingSql.append(" and ROUND(ROUND(IFNULL(SUM(r.cost)/SUM(r.conversions14d),0), 4), 2) <= ?");
            args.add(detailPageVo.getCpaMax());
        }
        return havingSql.toString();
    }

    @Override
    public List<RepeatTargetingCountVo> getKeywordTargetingByReportingIndex(Integer puid, RepeatTargetingCountPageVo vo, List<RepeatTargetingCountVo> rows) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select any(keyword_text) keywordText, any(match_type) matchType, 'sb' as type, count(*) targetNum ");
        sql.append(" from ods_t_amazon_ad_keyword_sb where puid = ? and marketplace_id = ? ");
        args.add(puid);
        args.add(vo.getMarketplaceId());
        //顶部店铺筛选
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), args));
        }
        //词组数量筛选
        if (CollectionUtils.isNotEmpty(vo.getKeywordSize())) {
            sql.append(SqlStringUtil.dealInList("(keyword_size", vo.getKeywordSize(), args));
            if (vo.getKeywordSize().contains(5)) {
                sql.append(" or keyword_size > 5");
            }
            sql.append(")");
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", vo.getSbAdGroupId(), args));
        }
        //匹配类型筛选
        if (CollectionUtils.isNotEmpty(vo.getMatchType())) {
            sql.append(SqlStringUtil.dealInList("match_type", vo.getMatchType(), args));
        }
        sql.append(SqlStringUtil.dealMultiInList(Arrays.asList("keyword_text", "match_type"), rows, args, Arrays.asList(RepeatTargetingCountVo::getKeywordText, i -> i.getMatchType().toLowerCase())));
        sql.append(" group by keyword_text, match_type ");
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(sql.toString(), arg, new ObjectMapper<>(RepeatTargetingCountVo.class));
    }

    @Override
    public Page<RepeatTargetingCountVo> getKeywordTargeting(Integer puid, RepeatTargetingCountPageVo vo) {
        List<Object> args = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, match_type matchType, 'sb' as type, count(*) targetNum ");
        sql.append(" from ods_t_amazon_ad_keyword_sb where puid = ? and marketplace_id = ? ");
        args.add(puid);
        args.add(vo.getMarketplaceId());
        sql.append(" and keyword_text != '(_targeting_auto_)' and keyword_text != '*' ");
        //顶部店铺筛选
        if (CollectionUtils.isNotEmpty(vo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", vo.getShopIdList(), args));
        }
        //词组数量筛选
        if (CollectionUtils.isNotEmpty(vo.getKeywordSize())) {
            sql.append(SqlStringUtil.dealInList("(keyword_size", vo.getKeywordSize(), args));
            if (vo.getKeywordSize().contains(5)) {
                sql.append(" or keyword_size > 5");
            }
            sql.append(")");
        }
        //ASIN筛选-转换为通过广告组Id筛选
        if (CollectionUtils.isNotEmpty(vo.getSbAdGroupId())) {
            sql.append(SqlStringUtil.dealBitMapDorisInList("ad_group_id", vo.getSbAdGroupId(), args));
        }
        //匹配类型筛选
        if (CollectionUtils.isNotEmpty(vo.getMatchType())) {
            sql.append(SqlStringUtil.dealInList("match_type", vo.getMatchType(), args));
        }
        //关键词筛选
        //模糊与精准匹配查询
        if (StringUtils.isNotEmpty(vo.getSearchValue()) && StringUtils.isNotEmpty(vo.getSearchType())) {
            if ("exact".equalsIgnoreCase(vo.getSearchType())) {
                sql.append(" and keyword_text = ? ");
                args.add(vo.getSearchValue().trim());
            } else {
                sql.append(" and lower(keyword_text) like ? ");
                args.add("%" + vo.getSearchValue().trim().toLowerCase() + "%");
            }
        }
        sql.append(" group by keyword_text, match_type order by targetNum ");
        if (StringUtils.isNotBlank(vo.getOrderType())) {
            sql.append(vo.getOrderType());
        } else {
            sql.append("desc");
        }
        sql.append(", keywordText ");
        if (StringUtils.isNotBlank(vo.getOrderType())) {
            sql.append(vo.getOrderType());
        } else {
            sql.append("desc");
        }
        countSql.append(sql + ") s");
        Object[] arg = args.toArray();
        return getPageResultByClass(vo.getPageNo(), vo.getPageSize(), countSql.toString(), arg, sql.toString(), arg, RepeatTargetingCountVo.class);
    }

    @Override
    public Page<RepeatTargetingDetailVo> getDetailKeywordData(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder countSql = new StringBuilder("select count(*) from (");
        StringBuilder sql = new StringBuilder("select any(shop_id) shopId, keyword_id keywordId, 'sb' type, any(match_type) matchType, any(state) state, 'null' status, any(bid) bid, any(suggested) suggestBid, any(range_start) rangeStart, any(range_end) rangeEnd, any(campaign_id) campaignId, any(ad_group_id) adGroupId from ods_t_amazon_ad_keyword_sb ");
        sql.append("where puid = ? and marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), args));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, args));
        sql.append(" group by keyword_id ");
        if (StringUtils.isNotBlank(detailPageVo.getOrderField()) && StringUtils.isNotBlank(detailPageVo.getOrderType()) && !"null".equals(detailPageVo.getOrderType())) {
            sql.append("order by bid ");
            sql.append(detailPageVo.getOrderType());
        } else {
            sql.append(" order by keyword_id desc");
        }
        countSql.append(sql + ") c");
        Object[] arg = args.toArray();
        return getPageResultByClass(detailPageVo.getPageNo(), detailPageVo.getPageSize(), countSql.toString(), arg, sql.toString(), arg, RepeatTargetingDetailVo.class);
    }

    @Override
    public List<RepeatTargetingDetailVo> getSbKeywordDataOrderByIndex(Integer puid, RepeatTargetingDetailPageVo detailPageVo, List<String> keywordIds) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select any(shop_id) shopId, keyword_id keywordId, 'sb' type, any(match_type) matchType, any(state) state, 'null' status, any(bid) bid,");
        sql.append(" any(suggested) suggestBid, any(range_start) rangeStart, any(range_end) rangeEnd, any(campaign_id) campaignId, any(ad_group_id) adGroupId from ods_t_amazon_ad_keyword_sb ");
        sql.append("where puid = ? and marketplace_id = ?");
        args.add(puid);
        args.add(detailPageVo.getMarketplaceId());
        if (CollectionUtils.isNotEmpty(detailPageVo.getShopIdList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", detailPageVo.getShopIdList(), args));
        }
        sql.append(SqlStringUtil.dealBitMapDorisInList("keyword_id", keywordIds, args));
        sql.append(" group by keyword_id ");
        Object[] arg = args.toArray();
        return getJdbcTemplate().query(sql.toString(), arg, new ObjectMapper<>(RepeatTargetingDetailVo.class));
    }
}

