package com.meiyunji.sponsored.service.dataWarehouse.statsDo;

import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.service.util.CurrencyConversion;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import lombok.SneakyThrows;

import java.math.BigDecimal;
import java.time.LocalDate;

@AllArgsConstructor
@NoArgsConstructor
public class ShopSaleOrigDto implements Cloneable {

    private Integer puid;
    private Integer shopId;
    private String marketplaceId;
    @CurrencyConversion
    private BigDecimal sumRange;
    private Integer saleNum;
    private String currency;
    private LocalDate nowDate;
    /**
     * 总销量
     */
    private Long  sumSaleNum;
    /**
     * 店铺销售额原币种
     */
    @CurrencyConversion
    private BigDecimal origSumRange;

    public Long getSumSaleNum() {
        return sumSaleNum;
    }

    public void setSumSaleNum(Long sumSaleNum) {
        this.sumSaleNum = sumSaleNum;
    }

    public Integer getPuid() {
        return puid;
    }

    public void setPuid(Integer puid) {
        this.puid = puid;
    }

    public Integer getShopId() {
        return shopId;
    }

    public void setShopId(Integer shopId) {
        this.shopId = shopId;
    }

    public String getMarketplaceId() {
        return marketplaceId;
    }

    public void setMarketplaceId(String marketplaceId) {
        this.marketplaceId = marketplaceId;
    }

    public BigDecimal getSumRange() {
        return sumRange;
    }

    public void setSumRange(BigDecimal sumRange) {
        this.sumRange = sumRange;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public LocalDate getNowDate() {
        return nowDate;
    }

    public void setNowDate(LocalDate nowDate) {
        this.nowDate = nowDate;
    }

    public ShopSaleOrigDto(BigDecimal sumRange) {
        this.sumRange = sumRange;
    }

    public Integer getSaleNum() {
        return saleNum;
    }

    public void setSaleNum(Integer saleNum) {
        this.saleNum = saleNum;
    }

    public ShopSaleOrigDto merge(ShopSaleOrigDto obj) {
        if (obj != null) {
            this.sumRange = MathUtil.add(this.sumRange, obj.getSumRange());
            this.saleNum = MathUtil.add(this.saleNum, obj.getSaleNum());
            this.origSumRange = MathUtil.add(this.origSumRange, obj.getOrigSumRange());
        }


        return this;
    }

    public BigDecimal getOrigSumRange() {
        return origSumRange;
    }

    public void setOrigSumRange(BigDecimal origSumRange) {
        this.origSumRange = origSumRange;
    }

    @SneakyThrows
    @Override
    public ShopSaleOrigDto clone() {
        return (ShopSaleOrigDto) super.clone();
    }
}
