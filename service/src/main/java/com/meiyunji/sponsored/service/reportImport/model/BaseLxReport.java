package com.meiyunji.sponsored.service.reportImport.model;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.converters.string.StringNumberConverter;
import com.alibaba.excel.converters.string.StringStringConverter;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.service.reportImport.listener.converter.CustomStringNumberConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Data
@Slf4j
public class BaseLxReport {
   private final static Pattern p1 = Pattern.compile("\\d{4}-\\d{2}-\\d{2}");
   private final static Pattern p2 = Pattern.compile("\\d{4}/\\d{1,2}/\\d{1,2}");

    /**
     * 行号,主要报错时返回具体的行号
     */
    @ExcelIgnore
    private Integer rowNumber;

    @ExcelIgnore
    private Integer puid;

    @ExcelIgnore
    private Integer shopId;

    @ExcelIgnore
    private String campaignId;

    @ExcelIgnore
    private String groupId;

    @ExcelIgnore
    private String marketplaceId;

    /**
     * 店铺名称
     */
    @ExcelProperty(value = "店铺名称", converter = CustomStringNumberConverter.class)
    private String shopName;

    /**
     * 国家编码
     */
    @ExcelProperty(value = "国家", converter = CustomStringNumberConverter.class)
    private String countryCode;

    /**
     * 广告类型
     */
    @ExcelProperty(value = "类型", converter = CustomStringNumberConverter.class)
    private String adType;

    /**
     * 广告活动名称
     */
    @ExcelProperty(value = "广告活动", converter = CustomStringNumberConverter.class)
    private String campaignName;

    /**
     * 报告日期
     */
    @ExcelProperty(value = "日期", converter = CustomStringNumberConverter.class)
    private String countDate;

    public String getSfCountDate() {
        Matcher m1 = p1.matcher(this.getCountDate());
        if (!m1.matches()) {
            Matcher m2 = p2.matcher(this.getCountDate());
            if (m2.matches()) {
                String[] split = this.getCountDate().split("/");
                return LocalDate.of(Integer.parseInt(split[0]), Integer.parseInt(split[1]),
                        Integer.parseInt(split[2])).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            }

            throw new BizServiceException("日期格式错误,正确格式: yyyy-MM-dd字符串类型");
        }
        return this.getCountDate().replaceAll("-", "").replaceAll("/", "");
    }


    public boolean hasNull() {
        Field[] declaredFields = this.getClass().getDeclaredFields();
        for (Field declaredField : declaredFields) {
            declaredField.setAccessible(true);
            ExcelProperty annotation = declaredField.getAnnotation(ExcelProperty.class);
            if (annotation == null) {
                continue;
            }
            try {
                Object o = declaredField.get(this);
                if (o == null) {
                    log.error("字段{} 为null", declaredField.getName());
                    return true;
                }
            } catch (IllegalAccessException e) {
                return false;
            }
        }
        return false;
    }

}
