package com.meiyunji.sponsored.service.doris.dao.admanage;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportChartBase;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportDorisBase;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReportDoris;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;

import java.util.List;

/**
 * 广告管理-投放层级-dao
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
public interface IAdManageTargetDorisDao {

    /**
     * 统计报告数量
     *
     * @param req 请求参数
     * @return 响应参数
     */
    Integer countReport(TargetReqDto req);

    /**
     * 统计投放数量
     *
     * @param req 请求参数
     * @return 响应参数
     */
    Integer countTarget(TargetReqDto req);

    /**
     * 多店铺分页获取投放列表
     *
     * @param req 请求参数
     * @return 响应参数
     */
    Page<TargetResp> getTargetPage(TargetReqDto req);

    /**
     * 获取汇总数据用于计算占比数据
     *
     * @param req 请求参数
     * @return 响应参数
     */
    AdMetricDto getSumAdMetricMultiple(TargetReqDto req);

    /**
     * 根据投放id集合获取报告指标
     *
     * @param req    请求参数
     * @param export 是否导出
     * @return 响应参数
     */
    List<TargetReportDoris> listTargetReport(TargetReqDto req, Boolean export);

    /**
     * 获取汇总数据
     *
     * @param req 请求参数
     * @return 响应参数
     */
    ReportDorisBase getSumReport(TargetReqDto req, boolean selCompareDate);

    /**
     * 按天分组获取汇总数据
     *
     * @param req 请求参数
     * @return 响应参数
     */
    List<ReportChartBase> getSumReportGroupByCountDay(TargetReqDto req);

    /**
     * 获取存在报告数据的投放id
     * @param puid
     * @param shopIdList
     * @param startDate
     * @param req
     * @return
     */
    List<String> getValidRecordByDate(Integer puid, List<Integer> shopIdList, String startDate, TargetReqDto req);

    /**
     * 查询词根频次接口，获取topN词根
     * @param req 请求参数
     * @return 响应参数
     */
    List<WordRootTopVo> getKeywordTopList(TargetReqDto req);

    /**
     * 根据词根获取关键词
     * @param req 请求参数
     * @return 响应参数
     */
    List<String> getKeywordIdListByWordRoot(TargetReqDto req);
}
