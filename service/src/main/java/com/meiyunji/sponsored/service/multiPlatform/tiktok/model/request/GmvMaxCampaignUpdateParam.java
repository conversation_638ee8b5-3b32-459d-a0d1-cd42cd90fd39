package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.GmvMaxVideoItem;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

@Data
public class GmvMaxCampaignUpdateParam {

    @NotNull(message = "店铺ID不能为空")
    private Integer shopId;
    @NotNull(message = "广告主ID不能为空")
    private String advertiserId;
    @NotNull(message = "活动ID不能为空")
    private String campaignId;

    private String campaignName;

    private BigDecimal roasBid;
    private BigDecimal budget;

    private String scheduleEndTime;

    private Boolean affiliatePostsEnabled;
    private List<GmvMaxVideoItem> itemList;
    private List<GmvMaxVideoItem> customAnchorVideoList;

}
