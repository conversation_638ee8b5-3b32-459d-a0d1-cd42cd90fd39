package com.meiyunji.sponsored.service.export.convert.impl.sp;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.constants.ConvertBeanIdConstant;
import com.meiyunji.sponsored.service.export.convert.ReportVoExportConvert;
import com.meiyunji.sponsored.service.vo.AdSpaceVo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-09-08  14:23
 */

@Component(ConvertBeanIdConstant.AMAZON_BUSINESS)
public class AmazonBusinessReportExportConvert implements ReportVoExportConvert {

    @Override
    public AdReportExportTypeEnum getReportExportType() {
        return AdReportExportTypeEnum.AMAZON_BUSINESS;
    }

    public void processExcelDataList(ReportVo reportVo, List list, Map<Integer, ShopAuth> shopAuthMap, SearchVo searchVo) {
        AdSpaceVo vo = new AdSpaceVo();
        String placements = "";
        placements = PlacementPageParam.placementPredicateEnum.getDescription(reportVo.getCampaignType());
        if (StringUtils.isNotBlank(placements)) {
            placements += "(企业购广告位)";
        }
        //广告位赋值
        vo.setPlacement(placements);
        vo.setStartDate(searchVo.getStartDate());
        vo.setEndDate(searchVo.getEndDate());
        //每日日期格式转换
        if ("daily".equals(searchVo.getTabType()) && StringUtils.isNotBlank(reportVo.getCountDate())) {
            vo.setDailyDate(reportVo.getCountDate());
        }
        vo.setCampaignStartDate(reportVo.getCampaignStartDate());
        vo.setCampaignEndDate(reportVo.getCampaignEndDate());
        vo.setCampaignState(reportVo.getState());
        vo.setCampaignName(reportVo.getCampaignName());
        vo.setCampaignType(reportVo.getSpCampaignType());
        vo.setCost(reportVo.getCost());
        vo.setImpressions(reportVo.getImpressions());
        vo.setClicks(reportVo.getClicks());
        vo.setCpc(reportVo.getCpc());
        vo.setClickRate(reportVo.getClickRate());
        vo.setSalesConversionRate(reportVo.getSalesConversionRate());
        vo.setAcos(reportVo.getAcos());
        vo.setRoas(reportVo.getRoas());
        vo.setAttributedConversions7d(reportVo.getAttributedConversions7d());
        vo.setAttributedConversions7dSameSKU(reportVo.getAttributedConversions7dSameSKU());
        vo.setAttributedConversions7dOtherSameSKU(reportVo.getAttributedConversions7dOtherSameSKU());
        vo.setAttributedSales7d(reportVo.getAttributedSales7d());
        vo.setAttributedSales7dSameSKU(reportVo.getAttributedSales7dSameSKU());
        vo.setAttributedSales7dOtherSameSKU(reportVo.getAttributedSales7dOtherSameSKU());
        vo.setAttributedUnitsOrdered7d(reportVo.getAttributedUnitsOrdered7d());
        vo.setAttributedUnitsOrdered7dSameSKU(reportVo.getAttributedUnitsOrdered7dSameSKU());
        vo.setAttributedUnitsOrdered7dOtherSameSKU(reportVo.getAttributedUnitsOrdered7dOtherSameSKU());

        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(reportVo.getShopId())) {
            ShopAuth shop = shopAuthMap.get(reportVo.getShopId());
            vo.setShopName(shop.getName());
            MarketTimezoneAndCurrencyEnum m = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(shop.getMarketplaceId());
            if (null != m) {
                vo.setCurrency(m.getCurrencyCode());
            }
        }
        list.add(vo);
    }
}
