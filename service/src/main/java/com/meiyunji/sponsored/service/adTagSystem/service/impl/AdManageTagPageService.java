package com.meiyunji.sponsored.service.adTagSystem.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.grpc.adTagSystem.AdHomeChartRpcVo;
import com.meiyunji.sponsored.grpc.adTagSystem.AdTagCampaignData;
import com.meiyunji.sponsored.grpc.adTagSystem.TagData;
import com.meiyunji.sponsored.grpc.adTagSystem.TagGroupData;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IUserService;
import com.meiyunji.sponsored.service.adTagSystem.bo.AdManageTagBo;
import com.meiyunji.sponsored.service.adTagSystem.dao.*;
import com.meiyunji.sponsored.service.adTagSystem.dto.CampaignDto;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdManageTagTypeEnum;
import com.meiyunji.sponsored.service.adTagSystem.enums.AdTagLogModuleEnum;
import com.meiyunji.sponsored.service.adTagSystem.param.CampaignTagDataParam;
import com.meiyunji.sponsored.service.adTagSystem.param.TagAdCampaignListParam;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTag;
import com.meiyunji.sponsored.service.adTagSystem.po.AdManageTagRelation;
import com.meiyunji.sponsored.service.adTagSystem.req.QueryShopInfoReq;
import com.meiyunji.sponsored.service.adTagSystem.resp.QueryShopInfoResp;
import com.meiyunji.sponsored.service.adTagSystem.service.IAdManageTagPageService;
import com.meiyunji.sponsored.service.adTagSystem.vo.CampaignTagAggregateDataVo;
import com.meiyunji.sponsored.service.adTagSystem.dto.CampaignTagDataReportDto;
import com.meiyunji.sponsored.service.adTagSystem.vo.CampaignTagDataVo;
import com.meiyunji.sponsored.service.adTagSystem.vo.CampaignTagExportDataVo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDorisDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dto.AdBaseReportDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.service.impl.AdBaseChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageTagDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdCampaignAllReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-10-29  09:52
 * 标签系统各层级页面
 */
@Service
@Slf4j
public class AdManageTagPageService implements IAdManageTagPageService {
    @Autowired
    private IAdManageTagGroupDao adManageTagGroupDao;
    @Autowired
    private IAdManageTagDao adManageTagDao;
    @Autowired
    private IAdManageTagRelationDao adManageTagRelationDao;
    @Autowired
    private IOdsAmazonAdCampaignAllReportDao odsAmazonAdCampaignAllReportDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IUserService userService;
    @Autowired
    private IAdManageTagGroupUserDao adManageTagGroupUserDao;
    @Autowired
    private CpcPageIdsHandler cpcPageIdsHandler;
    @Autowired
    private AdBaseChartDataProcess adBaseChartDataProcess;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    @Lazy
    private IExcelService excelService;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonAdCampaignAllDorisDao campaignAllDorisDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAdManageTagLogDao adManageTagLogDao;
    @Autowired
    private IOdsAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IOdsAdManageTagDao odsAdManageTagDao;

    @Override
    public CampaignTagAggregateDataVo getCampaignTagData(Integer puid, CampaignTagDataParam param) {
        StopWatch sw = new StopWatch("getCampaignTagData");
        //初始化汇总vo
        CampaignTagAggregateDataVo aggregateDataVo = this.initCampaignTagAggregateDataVo(param);
        //获取店铺名称，并过滤掉未授权的店铺
        Map<Integer, ShopAuthBo> shopBoMap = shopAuthDao.getAuthShopBoByMarketPlaceAndIds(puid, param.getShopIdList(), param.getMarketplaceIdList()).stream().collect(Collectors.toMap(ShopAuthBo::getId, Function.identity()));
        param.setShopIdList(new ArrayList<>(shopBoMap.keySet()));
        //前置条件查询
        boolean returnEmpty = this.getCampaignTagAggregateDataBeforeConditionSel(puid, param);
        if (returnEmpty) {
            this.saveAggregateIds(puid, new ArrayList<>(), param.getPageSign(), "");
            return aggregateDataVo;
        }
        sw.start("查询报告数据");
        //获取所有的标签数据
        List<CampaignTagDataReportDto> reportDtoList = odsAmazonAdCampaignAllReportDao.getCampaignTagData(puid, param);
        sw.stop();
        if (CollectionUtils.isEmpty(reportDtoList)) {
            this.saveAggregateIds(puid, new ArrayList<>(), param.getPageSign(), "");
            return aggregateDataVo;
        }
        //若只需要汇总条数，直接返回
        if (param.getOnlyCount()) {
            aggregateDataVo.getPage().setTotalSize((int) reportDtoList.stream().map(CampaignTagDataReportDto::getGroupId).distinct().count());
            return aggregateDataVo;
        }
        sw.start("汇总成标签组数据");
        //汇总成标签组数据
        List<CampaignTagDataReportDto> groupReportDtoList = new ArrayList<>();
        Map<Long, List<CampaignTagDataReportDto>> groupIdReportMap = reportDtoList.stream().collect(Collectors.groupingBy(CampaignTagDataReportDto::getGroupId));
        for (Map.Entry<Long, List<CampaignTagDataReportDto>> entry : groupIdReportMap.entrySet()) {
            CampaignTagDataReportDto groupReportDto = this.sumCampaignTagDataReportDtoList(entry.getValue());
            groupReportDto.setGroupId(entry.getKey());
            groupReportDto.setShopIds(String.join(",", entry.getValue().stream().filter(e -> StringUtils.isNotBlank(e.getShopIds()))
                    .map(e -> StringUtil.splitStr(e.getShopIds())).flatMap(Collection::stream).map(String::trim).collect(Collectors.toSet())));
            //标签组计算指标
            AdBaseReportDto.calculateAdBaseReportDto(groupReportDto, param.getShopSales());
            groupReportDtoList.add(groupReportDto);
        }
        sw.stop();
        sw.start("汇总成所有数据");
        CampaignTagDataReportDto aggregateReportDto = this.sumCampaignTagDataReportDtoList(groupReportDtoList);
        sw.stop();
        aggregateReportDto.setShopIds(String.join(",", groupReportDtoList.stream().filter(e -> StringUtils.isNotBlank(e.getShopIds()))
                .map(e -> StringUtil.splitStr(e.getShopIds())).flatMap(Collection::stream).collect(Collectors.toSet())));
        sw.start("内存排序");
        //内存排序
        if (StringUtils.isNotBlank(param.getOrderField()) && OrderTypeEnum.typeSet().contains(param.getOrderType()) && CampaignTagDataReportDto.orderFieldMap.containsKey(param.getOrderField())) {
            if (StringUtils.isEmpty(CampaignTagDataReportDto.orderFieldMap.get(param.getOrderField()))) {
                List<Long> groupIds = groupReportDtoList.stream().map(CampaignTagDataReportDto::getGroupId).collect(Collectors.toList());
                Map<Long, String> groupIdNameMap = adManageTagGroupDao.getTagBoByIdList(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), groupIds)
                        .stream().collect(Collectors.toMap(AdManageTagBo::getId, AdManageTagBo::getName));
                if (OrderTypeEnum.desc.getType().equals(param.getOrderType())) {
                    groupReportDtoList.sort(Comparator.comparing(e -> groupIdNameMap.get(e.getGroupId()), Collections.reverseOrder()));
                } else {
                    groupReportDtoList.sort(Comparator.comparing(e -> groupIdNameMap.get(e.getGroupId())));
                }
            } else {
                groupReportDtoList = PageUtil.sort(groupReportDtoList, CampaignTagDataReportDto.orderFieldMap.get(param.getOrderField()), param.getOrderType());
            }
        } else {
            //默认按标签组下标签数量排序
            Comparator<CampaignTagDataReportDto> comparator = (o1, o2) -> CollectionUtils.size(groupIdReportMap.get(o2.getGroupId())) - CollectionUtils.size(groupIdReportMap.get(o1.getGroupId()));
            groupReportDtoList.sort(comparator);
        }
        sw.stop();
        sw.start("存储标签组下的标签id");
        //存储标签组下的标签id
        List<Long> tagIdList = new ArrayList<>();
        groupReportDtoList.forEach(e -> {
            if (groupIdReportMap.containsKey(e.getGroupId())) {
                List<Long> tagIds = groupIdReportMap.get(e.getGroupId()).stream().map(CampaignTagDataReportDto::getTagId).collect(Collectors.toList());
                this.saveAggregateIds(puid, tagIds, param.getPageSign(), e.getGroupId().toString());
                tagIdList.addAll(tagIds);
            }
        });
        this.saveAggregateIds(puid, tagIdList, param.getPageSign(), "");
        sw.stop();
        sw.start("内存分页");
        //内存分页
        Page<CampaignTagDataReportDto> dtoPage = new Page<>(param.getPageNo(), param.getPageSize());
        PageUtil.getPage(dtoPage, groupReportDtoList);
        groupReportDtoList = dtoPage.getRows();
        sw.stop();
        sw.start("获取对比数据");
        //获取对比数据
        Map<Long, CampaignTagDataReportDto> compareTagReportDtoMap = new HashMap<>();
        Map<Long, CampaignTagDataReportDto> compareGroupTagReportDtoMap = new HashMap<>();
        CampaignTagDataReportDto compareAggregateReportDto = null;
        if (param.getIsCompare()) {
            compareAggregateReportDto = this.getCompareAggregateReportData(puid, param, tagIdList, compareGroupTagReportDtoMap, compareTagReportDtoMap);
        }
        sw.stop();
        sw.start("获取标签详情");
        //获取标签详情
        List<AdManageTagBo> adManageTagBoList = adManageTagDao.getTagBoByIdList(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), tagIdList);
        Map<Long, AdManageTagBo> adManageTagMap = adManageTagBoList.stream().collect(Collectors.toMap(AdManageTagBo::getId, Function.identity()));
        List<Long> tagGroupIdList = groupReportDtoList.stream().map(CampaignTagDataReportDto::getGroupId).collect(Collectors.toList());
        List<AdManageTagBo> adManageTagGroupList = adManageTagGroupDao.getTagBoByIdList(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), tagGroupIdList);
        Map<Long, AdManageTagBo> adManageTagGroupMap = adManageTagGroupList.stream().collect(Collectors.toMap(AdManageTagBo::getId, Function.identity()));
        sw.stop();
        sw.start("获取创建人名称");
        //获取创建人名称
        List<Integer> createIdList = adManageTagGroupList.stream().map(AdManageTagBo::getCreateId).collect(Collectors.toList());
        createIdList.addAll(adManageTagBoList.stream().map(AdManageTagBo::getCreateId).collect(Collectors.toList()));
        Map<String, String> userNameIdMap = userService.getUserNameIdMap(createIdList);
        sw.stop();
        sw.start("拼接列表页数据");
        //拼接列表页数据
        Page<CampaignTagDataVo> page = aggregateDataVo.getPage();
        for (CampaignTagDataReportDto dto : groupReportDtoList) {
            CampaignTagDataVo groupTagDataVo = this.buildCampaignTagDataVo(dto, compareGroupTagReportDtoMap.get(dto.getGroupId()), param.getIsCompare());
            groupTagDataVo.setId(dto.getGroupId());
            this.fillCampaignTagData(dto, shopBoMap, adManageTagGroupMap.get(groupTagDataVo.getId()), userNameIdMap, groupTagDataVo);
            //标签
            List<CampaignTagDataVo> tagDataVoList = new ArrayList<>();
            List<CampaignTagDataReportDto> tagReportDtoList = groupIdReportMap.get(dto.getGroupId());
            for (CampaignTagDataReportDto tagDto : tagReportDtoList) {
                AdBaseReportDto.calculateAdBaseReportDto(tagDto, param.getShopSales());
                CampaignTagDataVo campaignTagDataVo = this.buildCampaignTagDataVo(tagDto, compareTagReportDtoMap.get(tagDto.getTagId()), param.getIsCompare());
                campaignTagDataVo.setId(tagDto.getTagId());
                campaignTagDataVo.setTagGroupId(dto.getGroupId());
                this.fillCampaignTagData(tagDto, shopBoMap, adManageTagMap.get(campaignTagDataVo.getId()), userNameIdMap, campaignTagDataVo);
                tagDataVoList.add(campaignTagDataVo);
            }
            groupTagDataVo.setTagDataVoList(tagDataVoList);
            page.getRows().add(groupTagDataVo);
        }
        sw.stop();
        page.setPageNo(dtoPage.getPageNo());
        page.setPageSize(dtoPage.getPageSize());
        page.setTotalPage(dtoPage.getTotalPage());
        page.setTotalSize(dtoPage.getTotalSize());
        //计算汇总指标
        sw.start("计算汇总指标");
        AdBaseReportDto.calculateAdBaseReportDto(aggregateReportDto, param.getShopSales());
        CampaignTagDataVo campaignTagAggregateDataVo = this.buildCampaignTagDataVo(aggregateReportDto, compareAggregateReportDto, param.getIsCompare());
        if (StringUtils.isNotBlank(aggregateReportDto.getShopIds())) {
            campaignTagAggregateDataVo.setShopVoList(this.buildShopVoByShopIds(shopBoMap, aggregateReportDto.getShopIds()));
        }
        sw.stop();
        aggregateDataVo.setAggregateVo(campaignTagAggregateDataVo);
        aggregateDataVo.setPage(page);
        //获取图表数据
        sw.start("获取图表数据");
        this.fillAggregateChartData(puid, param, aggregateDataVo, tagIdList);
        sw.stop();
        log.info(sw.prettyPrint());
        return aggregateDataVo;
    }

    @Async
    @Override
    public void exportCampaignTagData(Integer puid, String uuid, CampaignTagDataParam param) {
        //记录日志
        try {
            adManageTagLogDao.addLog(puid, param.getUid(), AdManageTagTypeEnum.CAMPAIGN.getCode(), AdTagLogModuleEnum.EXPORT.getCode(),
                    String.format("导出广告活动标签列表页, %s", JSONUtil.objectToJson(param)));
        } catch (Exception e) {
            log.error(String.format("记录导出广告活动标签列表页异常, puid: %d, param: %s", puid, JSONUtil.objectToJson(param)), e);
        }
        //获取店铺名称，并过滤掉未授权的店铺
        Map<Integer, ShopAuthBo> shopBoMap = shopAuthDao.getAuthShopBoByMarketPlaceAndIds(puid, param.getShopIdList(), param.getMarketplaceIdList()).stream().collect(Collectors.toMap(ShopAuthBo::getId, Function.identity()));
        param.setShopIdList(new ArrayList<>(shopBoMap.keySet()));
        //前置条件查询
        boolean returnEmpty = this.getCampaignTagAggregateDataBeforeConditionSel(puid, param);
        if (returnEmpty) {
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        //获取所有的标签数据
        List<CampaignTagDataReportDto> reportDtoList = odsAmazonAdCampaignAllReportDao.getCampaignTagData(puid, param);
        if (CollectionUtils.isEmpty(reportDtoList)) {
            stringRedisService.set(uuid, new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        //汇总成标签组数据
        List<CampaignTagDataReportDto> groupReportDtoList = new ArrayList<>();
        Map<Long, List<CampaignTagDataReportDto>> groupIdReportMap = reportDtoList.stream().collect(Collectors.groupingBy(CampaignTagDataReportDto::getGroupId));
        for (Map.Entry<Long, List<CampaignTagDataReportDto>> entry : groupIdReportMap.entrySet()) {
            CampaignTagDataReportDto groupReportDto = sumCampaignTagDataReportDtoList(entry.getValue());
            groupReportDto.setGroupId(entry.getKey());
            //标签组计算指标
            AdBaseReportDto.calculateAdBaseReportDto(groupReportDto, param.getShopSales());
            groupReportDtoList.add(groupReportDto);
        }
        CampaignTagDataReportDto aggregateReportDto = this.sumCampaignTagDataReportDtoList(groupReportDtoList);
        AdBaseReportDto.calculateAdBaseReportDto(aggregateReportDto, param.getShopSales());
        //内存排序
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADOrderField(param.getOrderField(), CampaignTagDataReportDto.class);
            if (isSorted) {
                groupReportDtoList = PageUtil.sort(groupReportDtoList, param.getOrderField(), param.getOrderType());
            }
        }
        //获取标签详情
        List<Long> tagIdList = reportDtoList.stream().map(CampaignTagDataReportDto::getTagId).collect(Collectors.toList());
        List<AdManageTagBo> adManageTagBoList = adManageTagDao.getTagBoByIdList(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), tagIdList);
        Map<Long, AdManageTagBo> adManageTagMap = adManageTagBoList.stream().collect(Collectors.toMap(AdManageTagBo::getId, Function.identity()));
        List<Long> tagGroupIdList = groupReportDtoList.stream().map(CampaignTagDataReportDto::getGroupId).collect(Collectors.toList());
        List<AdManageTagBo> adManageTagGroupList = adManageTagGroupDao.getTagBoByIdList(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), tagGroupIdList);
        Map<Long, AdManageTagBo> adManageTagGroupMap = adManageTagGroupList.stream().collect(Collectors.toMap(AdManageTagBo::getId, Function.identity()));
        //获取创建人名称
        List<Integer> createId = adManageTagGroupList.stream().map(AdManageTagBo::getCreateId).collect(Collectors.toList());
        createId.addAll(adManageTagBoList.stream().map(AdManageTagBo::getCreateId).collect(Collectors.toList()));
        Map<String, String> userNameIdMap = userService.getUserNameIdMap(createId);
        //拼接列表页数据
        List<CampaignTagDataVo> exportTagDataVoList = new ArrayList<>();
        CampaignTagDataVo aggregateTagDataVo = this.buildCampaignTagDataVo(aggregateReportDto, null, false);
        aggregateTagDataVo.setTagName("汇总");
        exportTagDataVoList.add(aggregateTagDataVo);
        for (CampaignTagDataReportDto dto : groupReportDtoList) {
            CampaignTagDataVo groupTagDataVo = this.buildCampaignTagDataVo(dto, null, false);
            this.fillCampaignTagData(dto, null, adManageTagGroupMap.get(dto.getGroupId()), userNameIdMap, groupTagDataVo);
            exportTagDataVoList.add(groupTagDataVo);
            //标签数据
            List<CampaignTagDataReportDto> campaignTagDataReportDtoList = groupIdReportMap.get(dto.getGroupId());
            for (CampaignTagDataReportDto tagDto : campaignTagDataReportDtoList) {
                AdBaseReportDto.calculateAdBaseReportDto(tagDto, param.getShopSales());
                CampaignTagDataVo tagDataVo = this.buildCampaignTagDataVo(tagDto, null, false);
                //导出不需要店铺信息
                tagDto.setShopIds(null);
                this.fillCampaignTagData(tagDto, null, adManageTagMap.get(tagDto.getTagId()), userNameIdMap, tagDataVo);
                tagDataVo.setTagName(groupTagDataVo.getTagName() + "-" + tagDataVo.getTagName());
                exportTagDataVoList.add(tagDataVo);
            }
        }
        String fileName = "标签系统_广告活动标签_" + DateUtil.getDateTime();
        List<CampaignTagExportDataVo> exportList = exportTagDataVoList.stream().map(e -> this.convertVoToReportVo(e, param.getCurrency())).collect(Collectors.toList());
        String downloadUrl = excelService.easyExcelHandlerExport(puid, exportList, fileName, CampaignTagExportDataVo.class, new WriteHandlerBuild().rate().currencyNew(CampaignTagExportDataVo.class));
        stringRedisService.set(uuid, new ProcessMsg(1, exportList.size(), exportList.size(), "导出完成", Collections.singletonList(downloadUrl)));
    }

    private void fillCampaignTagData(CampaignTagDataReportDto dto, Map<Integer, ShopAuthBo> shopBoMap, AdManageTagBo adManageTagBo, Map<String, String> userNameIdMap, CampaignTagDataVo vo) {
        if (adManageTagBo != null) {
            vo.setTagName(adManageTagBo.getName());
            vo.setTagColor(adManageTagBo.getColor());
            if (userNameIdMap.containsKey(adManageTagBo.getCreateId().toString())) {
                vo.setCreateName(userNameIdMap.get(adManageTagBo.getCreateId().toString()));
            }
        }
        if (StringUtils.isNotBlank(dto.getShopIds())) {
            vo.setShopVoList(this.buildShopVoByShopIds(shopBoMap, dto.getShopIds()));
        }
    }

    public CampaignTagExportDataVo convertVoToReportVo(CampaignTagDataVo campaignTagDataVo, String currency) {
        CampaignTagExportDataVo exportDataVo = new CampaignTagExportDataVo();
        exportDataVo.setTagName(campaignTagDataVo.getTagName());
        exportDataVo.setCreateName(campaignTagDataVo.getCreateName());
        exportDataVo.setCampaignCount(new BigDecimal(campaignTagDataVo.getCampaignCount()));
        exportDataVo.setAdCost(currency + campaignTagDataVo.getAdCost());
        exportDataVo.setImpressions(new BigDecimal(campaignTagDataVo.getImpressions()));
        exportDataVo.setClicks(new BigDecimal(campaignTagDataVo.getClicks()));
        exportDataVo.setCpa(currency + campaignTagDataVo.getCpa());
        exportDataVo.setAdCostPerClick(currency + campaignTagDataVo.getAdCostPerClick());
        exportDataVo.setCtr(campaignTagDataVo.getCtr() + "%");
        exportDataVo.setCvr(campaignTagDataVo.getCvr() + "%");
        exportDataVo.setAcos(campaignTagDataVo.getAcos() + "%");
        exportDataVo.setRoas(new BigDecimal(campaignTagDataVo.getRoas()));
        exportDataVo.setAcots(campaignTagDataVo.getAcots() + "%");
        exportDataVo.setAsots(campaignTagDataVo.getAsots() + "%");
        exportDataVo.setAdvertisingUnitPrice(currency + campaignTagDataVo.getAdvertisingUnitPrice());
        exportDataVo.setAdOrderNum(new BigDecimal(campaignTagDataVo.getAdOrderNum()));
        exportDataVo.setAdSale(currency + campaignTagDataVo.getAdSale());
        exportDataVo.setAdSaleNum(new BigDecimal(campaignTagDataVo.getAdSaleNum()));
        exportDataVo.setSelfAdOrderNum(new BigDecimal(campaignTagDataVo.getSelfAdOrderNum()));
        exportDataVo.setOtherAdOrderNum(new BigDecimal(campaignTagDataVo.getOtherAdOrderNum()));
        exportDataVo.setAdSelfSale(currency + campaignTagDataVo.getAdSelfSale());
        exportDataVo.setAdOtherSales(currency + campaignTagDataVo.getAdOtherSales());
        exportDataVo.setAdSelfSaleNum(new BigDecimal(campaignTagDataVo.getAdSelfSaleNum()));
        exportDataVo.setAdOtherSaleNum(new BigDecimal(campaignTagDataVo.getAdOtherSaleNum()));
        exportDataVo.setOrdersNewToBrandFTD(new BigDecimal(campaignTagDataVo.getOrdersNewToBrandFTD()));
        exportDataVo.setOrderRateNewToBrandFTD(campaignTagDataVo.getOrderRateNewToBrandFTD() + "%");
        exportDataVo.setSalesNewToBrandFTD(currency + campaignTagDataVo.getSalesNewToBrandFTD());
        exportDataVo.setSalesRateNewToBrandFTD(campaignTagDataVo.getSalesRateNewToBrandFTD() + "%");
        exportDataVo.setUnitsOrderedNewToBrandFTD(new BigDecimal(campaignTagDataVo.getUnitsOrderedNewToBrandFTD()));
        exportDataVo.setUnitsOrderedRateNewToBrandFTD(campaignTagDataVo.getUnitsOrderedRateNewToBrandFTD() + "%");
        return exportDataVo;
    }

    private List<CampaignTagDataVo.shopVo> buildShopVoByShopIds(Map<Integer, ShopAuthBo> shopBoMap, String shopIds) {
        return StringUtil.splitStr(shopIds).stream()
                .map(e -> {
                    Integer shopId = Integer.valueOf(e.trim());
                    ShopAuthBo shopAuthBo = shopBoMap.get(shopId);
                    AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(shopAuthBo.getMarketplaceId());
                    return new CampaignTagDataVo.shopVo(shopId, shopAuthBo.getName(), shopAuthBo.getMarketplaceId(), Optional.ofNullable(amznEndpoint).map(AmznEndpoint::getMarketplaceCN).orElse(""));
                }).collect(Collectors.toList());
    }

    private boolean getCampaignTagAggregateDataBeforeConditionSel(Integer puid, CampaignTagDataParam param) {
        //查询有权限的标签组id
        List<Long> groupIdList;
        if (param.getIsAdmin() != null && param.getIsAdmin()) {
            groupIdList = adManageTagGroupUserDao.listGroupIdByPuid(puid, AdManageTagTypeEnum.CAMPAIGN.getCode());
        } else {
            groupIdList = adManageTagGroupUserDao.listGroupIdByUid(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), param.getUid());
        }
        if (CollectionUtils.isEmpty(groupIdList)) {
            return true;
        }
        param.setGroupIds(groupIdList);
        if (CollectionUtils.isNotEmpty(param.getCreateIdList())) {
            //查出需要标签都展示的标签组
            List<Long> groupIds = adManageTagGroupDao.listGroupIdByGroupIdsAndCreateId(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), groupIdList, param.getCreateIdList());
            //查出需要展示的标签
            List<AdManageTag> adManageTags = adManageTagDao.listByGroupIdsAndCreateId(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), groupIdList, param.getCreateIdList());
            if (CollectionUtils.isEmpty(groupIds) && CollectionUtils.isEmpty(adManageTags)) {
                return true;
            }
            if (CollectionUtils.isNotEmpty(adManageTags)) {
                groupIds.addAll(adManageTags.stream().map(AdManageTag::getGroupId).filter(e -> !groupIds.contains(e)).collect(Collectors.toList()));
                List<Long> tagIds = adManageTags.stream().map(AdManageTag::getId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(param.getTagIdList())) {
                    tagIds.retainAll(param.getTagIdList());
                    if (CollectionUtils.isEmpty(tagIds)) {
                        return true;
                    }
                }
                param.setTagIdList(tagIds);
            }
            param.setGroupIds(groupIds);
        }
        //填充店铺销售额
        param.setShopSales(cpcShopDataService.getShopSalesByDate(puid, param.getShopIdList(), param.getStartDate(), param.getEndDate()));
        return false;
    }

    /**
     * 获取汇总数据
     *
     * @param puid
     * @param param
     * @param tagIdList
     * @param compareGroupTagReportDtoMap
     * @return
     */
    private CampaignTagDataReportDto getCompareAggregateReportData(Integer puid, CampaignTagDataParam param, List<Long> tagIdList,
                                                                   Map<Long, CampaignTagDataReportDto> compareGroupTagReportDtoMap, Map<Long, CampaignTagDataReportDto> compareTagReportDtoMap) {
        if (CollectionUtils.isEmpty(tagIdList)) {
            return null;
        }
        BigDecimal compareShopSales = cpcShopDataService.getShopSalesByDate(puid, param.getShopIdList(), param.getCompareStartDate(), param.getCompareEndDate());
        //通过分页后的标签id直接查询对比数据
        List<CampaignTagDataReportDto> compareReportDtoList = odsAmazonAdCampaignAllReportDao.getCampaignTagDataByTagIdList(puid, this.buildCompareAggregateParam(param, compareShopSales), tagIdList);
        if (CollectionUtils.isEmpty(compareReportDtoList)) {
            return null;
        }
        //标签数据
        compareReportDtoList.forEach(e -> {
            //计算指标数据
            AdBaseReportDto.calculateAdBaseReportDto(e, compareShopSales);
            compareTagReportDtoMap.put(e.getTagId(), e);
        });
        //标签组数据
        Map<Long, List<CampaignTagDataReportDto>> compareGroupIdReportMap = compareReportDtoList.stream().collect(Collectors.groupingBy(CampaignTagDataReportDto::getGroupId));
        compareGroupIdReportMap.forEach((k, v) -> {
            CampaignTagDataReportDto groupReportDto = this.sumCampaignTagDataReportDtoList(v);
            groupReportDto.setGroupId(k);
            //计算指标数据
            AdBaseReportDto.calculateAdBaseReportDto(groupReportDto, compareShopSales);
            compareGroupTagReportDtoMap.put(k, groupReportDto);
        });
        CampaignTagDataReportDto compareAggregateReportDto = this.sumCampaignTagDataReportDtoList(compareReportDtoList);
        AdBaseReportDto.calculateAdBaseReportDto(compareAggregateReportDto, compareShopSales);
        return compareAggregateReportDto;
    }

    /**
     * 填充图表数据
     */
    private void fillAggregateChartData(Integer puid, CampaignTagDataParam param, CampaignTagAggregateDataVo aggregateDataVo, List<Long> tagIdList) {
        StopWatch sw = new StopWatch("fillAggregateChartData");
        sw.start("查询活动id");
        List<String> campaignIds = adManageTagRelationDao.relationIdListByTagIds(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), tagIdList);
        sw.stop();
        sw.start("查询活动报告数据");
        List<AdBaseReportDto> adBaseReportDtoList = new ArrayList<>();
        //重复id查多次报告数据
        Map<String, Integer> idCountMap = new HashMap<>();
        campaignIds.forEach(id -> {
            idCountMap.put(id, idCountMap.getOrDefault(id, 0) + 1);
        });
        if (!idCountMap.isEmpty()) {
            for (int i = 1; i <= Constants.AD_TAG_SYSTEM_CAMPAIGN_TAG_MAX_SIZE; i++) {
                //重复次数
                int distinctCount = i;
                List<String> tagIds = idCountMap.entrySet().stream().filter(e -> e.getValue().equals(distinctCount)).map(Map.Entry::getKey).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(tagIds)) {
                    continue;
                }
                List<AdBaseReportDto> campaignTagChartDataList = odsAmazonAdCampaignAllReportDao.getCampaignTagChartData(puid, param, tagIdList, distinctCount);
                for (int j = 1; j <= i; j++) {
                    adBaseReportDtoList.addAll(campaignTagChartDataList);
                }
            }
        }
        sw.stop();
        //获取chart数据
        sw.start("拼装日数据");
        List<AdHomeChartRpcVo> dayPerformanceVos = adBaseChartDataProcess.getDayPerformanceVos(param.getCurrency(), adBaseReportDtoList, param.getShopSales());
        sw.stop();
        sw.start("拼装月数据");
        List<AdHomeChartRpcVo> monthPerformanceVos = adBaseChartDataProcess.getMonthPerformanceVos(param.getCurrency(), adBaseReportDtoList, param.getShopSales());
        sw.stop();
        sw.start("拼装周数据");
        List<AdHomeChartRpcVo> weekPerformanceVos = adBaseChartDataProcess.getWeekPerformanceVos(param.getCurrency(), param.getStartDate(), param.getEndDate(), adBaseReportDtoList, param.getShopSales());
        sw.stop();
        aggregateDataVo.setDay(dayPerformanceVos);
        aggregateDataVo.setWeek(weekPerformanceVos);
        aggregateDataVo.setMonth(monthPerformanceVos);
        log.info(sw.prettyPrint());
    }

    /**
     * 汇总返回vo初始化
     */
    private CampaignTagAggregateDataVo initCampaignTagAggregateDataVo(CampaignTagDataParam param) {
        CampaignTagAggregateDataVo aggregateDataVo = new CampaignTagAggregateDataVo();
        CampaignTagDataVo aggregateVo = CampaignTagDataVo.initCampaignTagDataVo(param.getIsCompare());
        Page<CampaignTagDataVo> page = new Page<>(param.getPageNo(), param.getPageSize(), 0, 0, new ArrayList<>());
        aggregateDataVo.setAggregateVo(aggregateVo);
        aggregateDataVo.setPage(page);
        //初始化chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adBaseChartDataProcess.getDayPerformanceVos(param.getCurrency(), new ArrayList<>(), BigDecimal.ZERO);
        List<AdHomeChartRpcVo> monthPerformanceVos = adBaseChartDataProcess.getMonthPerformanceVos(param.getCurrency(), new ArrayList<>(), BigDecimal.ZERO);
        List<AdHomeChartRpcVo> weekPerformanceVos = adBaseChartDataProcess.getWeekPerformanceVos(param.getCurrency(), param.getStartDate(), param.getEndDate(), new ArrayList<>(), BigDecimal.ZERO);
        aggregateDataVo.setDay(dayPerformanceVos);
        aggregateDataVo.setWeek(weekPerformanceVos);
        aggregateDataVo.setMonth(monthPerformanceVos);
        return aggregateDataVo;
    }

    /**
     * 存储列表页的所有id到数据库中，用于展示汇总趋势图
     */
    private void saveAggregateIds(Integer puid, List<Long> tagIdList, String pageSign, String searchParamStr) {
        ThreadPoolUtil.getCampaignTagAggregateSyncPool().execute(() -> {
            cpcPageIdsHandler.addIdsTemporarySynchronize(puid, tagIdList.stream().map(Object::toString).collect(Collectors.toList()), pageSign, searchParamStr);
        });
    }

    private CampaignTagDataReportDto sumCampaignTagDataReportDtoList(List<CampaignTagDataReportDto> reportDtoList) {
        CampaignTagDataReportDto sumVo = new CampaignTagDataReportDto();
        Integer campaignCount = 0;
        BigDecimal adCost = BigDecimal.ZERO;
        Long impressions = 0L;
        Integer clicks = 0;
        Integer adOrderNum = 0;
        BigDecimal adSale = BigDecimal.ZERO;
        Integer adSaleNum = 0;
        Integer selfAdOrderNum = 0;
        BigDecimal adSelfSale = BigDecimal.ZERO;
        Integer adSelfSaleNum = 0;
        Integer ordersNewToBrandFTD = 0;
        BigDecimal salesNewToBrandFTD = BigDecimal.ZERO;
        Integer unitsOrderedNewToBrandFTD = 0;
        Integer adOtherSaleNum = 0;
        for (CampaignTagDataReportDto dto : reportDtoList) {
            campaignCount += dto.getCampaignCount();
            adCost = adCost.add(dto.getSumAdCost());
            impressions += dto.getSumImpressions();
            clicks += dto.getSumClicks();
            adOrderNum += dto.getSumAdOrderNum();
            adSale = adSale.add(dto.getSumAdSale());
            adSaleNum += dto.getSumAdSaleNum();
            selfAdOrderNum += dto.getSumSelfAdOrderNum();
            adSelfSale = adSelfSale.add(dto.getSumAdSelfSale());
            adSelfSaleNum += dto.getSumAdSelfSaleNum();
            ordersNewToBrandFTD += dto.getSumOrdersNewToBrandFTD();
            salesNewToBrandFTD = salesNewToBrandFTD.add(dto.getSumSalesNewToBrandFTD());
            unitsOrderedNewToBrandFTD += dto.getSumUnitsOrderedNewToBrandFTD();
            adOtherSaleNum += dto.getSumAdOtherSaleNum();
        }
        sumVo.setCampaignCount(campaignCount);
        sumVo.setSumAdCost(adCost.setScale(2, RoundingMode.HALF_UP)); //花费
        sumVo.setSumImpressions(impressions);  //曝光量
        sumVo.setSumClicks(clicks);  //点击量
        sumVo.setSumAdOrderNum(adOrderNum);  //广告订单量
        sumVo.setSumAdSale(adSale.setScale(2, RoundingMode.HALF_UP));  //广告销售额
        sumVo.setSumAdSaleNum(adSaleNum); //广告销量
        sumVo.setSumSelfAdOrderNum(selfAdOrderNum); //本广告产品订单量
        sumVo.setSumAdSelfSale(adSelfSale.setScale(2, RoundingMode.HALF_UP)); //本广告产品销售额
        sumVo.setSumAdSelfSaleNum(adSelfSaleNum); //本产品广告销量
        sumVo.setSumOrdersNewToBrandFTD(ordersNewToBrandFTD); //“品牌新买家”订单量
        sumVo.setSumSalesNewToBrandFTD(salesNewToBrandFTD.setScale(2, RoundingMode.HALF_UP)); //“品牌新买家”销售额
        sumVo.setSumUnitsOrderedNewToBrandFTD(unitsOrderedNewToBrandFTD); //“品牌新买家”销量
        sumVo.setSumAdOtherSaleNum(adOtherSaleNum); //其他产品广告销量
        return sumVo;
    }

    /**
     * 拼装对比请求参数
     */
    private CampaignTagDataParam buildCompareAggregateParam(CampaignTagDataParam param, BigDecimal shopSales) {
        CampaignTagDataParam compareParam = new CampaignTagDataParam();
        compareParam.setPuid(param.getPuid());
        compareParam.setShopIdList(param.getShopIdList());
        compareParam.setMarketplaceIdList(param.getMarketplaceIdList());
        compareParam.setCurrency(param.getCurrency());
        compareParam.setStartDate(param.getCompareStartDate());
        compareParam.setEndDate(param.getCompareEndDate());
        compareParam.setShopSales(shopSales);
        return compareParam;
    }

    /**
     * 计算报告数据和对比数据
     *
     * @param reportDto
     * @param compareReportDto
     * @return
     */
    private CampaignTagDataVo buildCampaignTagDataVo(CampaignTagDataReportDto reportDto, CampaignTagDataReportDto compareReportDto, Boolean isCompare) {
        CampaignTagDataVo vo = CampaignTagDataVo.initCampaignTagDataVo(isCompare);
        vo.setCampaignCount(reportDto.getCampaignCount().toString());
        vo.setAdCost(reportDto.getSumAdCost().setScale(2, RoundingMode.HALF_UP).toPlainString()); //花费
        vo.setImpressions(reportDto.getSumImpressions().toString());  //曝光量
        vo.setClicks(reportDto.getSumClicks().toString());  //点击量
        vo.setAdOrderNum(reportDto.getSumAdOrderNum().toString());  //广告订单量
        vo.setAdSale(reportDto.getSumAdSale().setScale(2, RoundingMode.HALF_UP).toPlainString());  //广告销售额
        vo.setAdSaleNum(reportDto.getSumAdSaleNum().toString()); //广告销量
        vo.setSelfAdOrderNum(reportDto.getSumSelfAdOrderNum().toString()); //本广告产品订单量
        vo.setAdSelfSale(reportDto.getSumAdSelfSale().setScale(2, RoundingMode.HALF_UP).toPlainString()); //本广告产品销售额
        vo.setAdSelfSaleNum(reportDto.getSumAdSelfSaleNum().toString()); //本产品广告销量
        vo.setOrdersNewToBrandFTD(reportDto.getSumOrdersNewToBrandFTD().toString()); //“品牌新买家”订单量
        vo.setSalesNewToBrandFTD(reportDto.getSumSalesNewToBrandFTD().setScale(2, RoundingMode.HALF_UP).toPlainString()); //“品牌新买家”销售额
        vo.setUnitsOrderedNewToBrandFTD(reportDto.getSumUnitsOrderedNewToBrandFTD().toString()); //“品牌新买家”销量
        //计算指标
        vo.setCpa(reportDto.getSumCpa().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setAdvertisingUnitPrice(reportDto.getSumAdvertisingUnitPrice().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setAdCostPerClick(reportDto.getSumAdCostPerClick().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setCvr(reportDto.getSumCvr().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setCtr(reportDto.getSumCtr().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setAcos(reportDto.getSumAcos().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setRoas(reportDto.getSumRoas().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setAcots(reportDto.getSumAcots().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setAsots(reportDto.getSumAsots().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setOtherAdOrderNum(reportDto.getSumOtherAdOrderNum().toString()); //其他产品广告订单量
        vo.setAdOtherSales(reportDto.getSumAdOtherSales().setScale(2, RoundingMode.HALF_UP).toPlainString()); //其他产品广告销售额
        vo.setAdOtherSaleNum(reportDto.getSumAdOtherSaleNum().toString()); //其他产品广告销量
        vo.setOrderRateNewToBrandFTD(reportDto.getSumOrderRateNewToBrandFTD().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setSalesRateNewToBrandFTD(reportDto.getSumSalesRateNewToBrandFTD().setScale(2, RoundingMode.HALF_UP).toPlainString());
        vo.setUnitsOrderedRateNewToBrandFTD(reportDto.getSumUnitsOrderedRateNewToBrandFTD().setScale(2, RoundingMode.HALF_UP).toPlainString());
        //对比数据
        if (compareReportDto != null) {
            //花费
            vo.setCompareAdCost(compareReportDto.getSumAdCost().setScale(2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal adCostDiff = reportDto.getSumAdCost().subtract(compareReportDto.getSumAdCost());
            vo.setCompareAdCostRate(compareReportDto.getSumAdCost().compareTo(BigDecimal.ZERO) == 0 ? "-" : adCostDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAdCost(), 2, RoundingMode.HALF_UP).toPlainString());
            //曝光量
            vo.setCompareImpressions(compareReportDto.getSumImpressions().toString());
            long impressionDiff = reportDto.getSumImpressions() - compareReportDto.getSumImpressions();
            vo.setCompareImpressionsRate(compareReportDto.getSumImpressions() == 0 ? "-" : new BigDecimal(impressionDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumImpressions()), 2, RoundingMode.HALF_UP).toPlainString());
            //点击量
            vo.setCompareClicks(compareReportDto.getSumClicks().toString());
            int clicksDiff = reportDto.getSumClicks() - compareReportDto.getSumClicks();
            vo.setCompareClicksRate(compareReportDto.getSumClicks() == 0 ? "-" : new BigDecimal(clicksDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumClicks()), 2, RoundingMode.HALF_UP).toPlainString());
            //广告订单量
            vo.setCompareAdOrderNum(compareReportDto.getSumAdOrderNum().toString());
            int adOrderNumDiff = reportDto.getSumAdOrderNum() - compareReportDto.getSumAdOrderNum();
            vo.setCompareAdOrderNumRate(compareReportDto.getSumAdOrderNum() == 0 ? "-" : new BigDecimal(adOrderNumDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumAdOrderNum()), 2, RoundingMode.HALF_UP).toPlainString());
            //广告销售额
            vo.setCompareAdSale(compareReportDto.getSumAdSale().setScale(2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal adSaleDiff = reportDto.getSumAdSale().subtract(compareReportDto.getSumAdSale());
            vo.setCompareAdSaleRate(compareReportDto.getSumAdSale().compareTo(BigDecimal.ZERO) == 0 ? "-" : adSaleDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAdSale(), 2, RoundingMode.HALF_UP).toPlainString());
            //广告销量
            vo.setCompareAdSaleNum(compareReportDto.getSumAdSaleNum().toString());
            int adSaleNumDiff = reportDto.getSumAdSaleNum() - compareReportDto.getSumAdSaleNum();
            vo.setCompareAdSaleNumRate(compareReportDto.getSumAdSaleNum() == 0 ? "-" : new BigDecimal(adSaleNumDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumAdSaleNum()), 2, RoundingMode.HALF_UP).toPlainString());
            //本广告产品订单量
            vo.setCompareSelfAdOrderNum(compareReportDto.getSumSelfAdOrderNum().toString());
            int selfAdOrderNumDiff = reportDto.getSumSelfAdOrderNum() - compareReportDto.getSumSelfAdOrderNum();
            vo.setCompareSelfAdOrderNumRate(compareReportDto.getSumSelfAdOrderNum() == 0 ? "-" : new BigDecimal(selfAdOrderNumDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumSelfAdOrderNum()), 2, RoundingMode.HALF_UP).toPlainString());
            //本广告产品销售额
            vo.setCompareAdSelfSale(compareReportDto.getSumAdSelfSale().setScale(2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal adSelfSaleDiff = reportDto.getSumAdSelfSale().subtract(compareReportDto.getSumAdSelfSale());
            vo.setCompareAdSelfSaleRate(compareReportDto.getSumAdSelfSale().compareTo(BigDecimal.ZERO) == 0 ? "-" : adSelfSaleDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAdSelfSale(), 2, RoundingMode.HALF_UP).toPlainString());
            //本产品广告销量
            vo.setCompareAdSelfSaleNum(compareReportDto.getSumAdSelfSaleNum().toString());
            int adSelfSaleNumDiff = reportDto.getSumAdSelfSaleNum() - compareReportDto.getSumAdSelfSaleNum();
            vo.setCompareAdSelfSaleNumRate(compareReportDto.getSumAdSelfSaleNum() == 0 ? "-" : new BigDecimal(adSelfSaleNumDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumAdSelfSaleNum()), 2, RoundingMode.HALF_UP).toPlainString());
            //“品牌新买家”订单量
            vo.setCompareOrdersNewToBrandFTD(compareReportDto.getSumOrdersNewToBrandFTD().toString());
            int ordersNewToBrandFTDDiff = reportDto.getSumOrdersNewToBrandFTD() - compareReportDto.getSumOrdersNewToBrandFTD();
            vo.setCompareOrdersNewToBrandFTDRate(compareReportDto.getSumOrdersNewToBrandFTD() == 0 ? "-" : new BigDecimal(ordersNewToBrandFTDDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumOrdersNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
            //“品牌新买家”销售额
            vo.setCompareSalesNewToBrandFTD(compareReportDto.getSumSalesNewToBrandFTD().setScale(2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal salesNewToBrandFTDDiff = reportDto.getSumSalesNewToBrandFTD().subtract(compareReportDto.getSumSalesNewToBrandFTD());
            vo.setCompareSalesNewToBrandFTDRate(compareReportDto.getSumSalesNewToBrandFTD().compareTo(BigDecimal.ZERO) == 0 ? "-" : salesNewToBrandFTDDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumSalesNewToBrandFTD(), 2, RoundingMode.HALF_UP).toPlainString());
            //“品牌新买家”销量
            vo.setCompareUnitsOrderedNewToBrandFTD(compareReportDto.getSumUnitsOrderedNewToBrandFTD().toString());
            int unitsOrderedNewToBrandFTDDiff = reportDto.getSumUnitsOrderedNewToBrandFTD() - compareReportDto.getSumUnitsOrderedNewToBrandFTD();
            vo.setCompareUnitsOrderedNewToBrandFTDRate(compareReportDto.getSumUnitsOrderedNewToBrandFTD() == 0 ? "-" : new BigDecimal(unitsOrderedNewToBrandFTDDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumUnitsOrderedNewToBrandFTD()), 2, RoundingMode.HALF_UP).toPlainString());
            if (compareReportDto.getSumAdOrderNum() != null && compareReportDto.getSumAdOrderNum() != 0) {
                vo.setCompareCpa(MathUtil.divide(String.valueOf(compareReportDto.getSumAdCost()), String.valueOf(compareReportDto.getSumAdOrderNum()), 2));
                vo.setCompareAdvertisingUnitPrice(MathUtil.divide(String.valueOf(compareReportDto.getSumAdSale()), String.valueOf(compareReportDto.getSumAdOrderNum()), 2));
            }
            BigDecimal cpaDiff = MathUtil.subtract(reportDto.getSumCpa(), compareReportDto.getSumCpa());
            vo.setCompareCpaRate(compareReportDto.getSumCpa().compareTo(BigDecimal.ZERO) == 0 ? "-" : cpaDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumCpa(), 2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal advertisingUnitPriceDiff = reportDto.getSumAdvertisingUnitPrice().subtract(compareReportDto.getSumAdvertisingUnitPrice());
            vo.setCompareAdvertisingUnitPriceRate(compareReportDto.getSumAdvertisingUnitPrice().compareTo(BigDecimal.ZERO) == 0 ? "-" : advertisingUnitPriceDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAdvertisingUnitPrice(), 2, RoundingMode.HALF_UP).toPlainString());
            if (compareReportDto.getSumClicks() != null && compareReportDto.getSumClicks() != 0) {
                vo.setCompareAdCostPerClick(MathUtil.divide(String.valueOf(compareReportDto.getSumAdCost()), String.valueOf(compareReportDto.getSumClicks()), 2));
                vo.setCompareCvr(MathUtil.multiply(MathUtil.divide(String.valueOf(compareReportDto.getSumAdOrderNum()), String.valueOf(compareReportDto.getSumClicks()), 4), "100"));
            }
            BigDecimal adCostPerClickDiff = MathUtil.subtract(reportDto.getSumAdCostPerClick(), compareReportDto.getSumAdCostPerClick());
            vo.setCompareAdCostPerClickRate(compareReportDto.getSumAdCostPerClick().compareTo(BigDecimal.ZERO) == 0 ? "-" : adCostPerClickDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAdCostPerClick(), 2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal cvrDiff = MathUtil.subtract(reportDto.getSumCvr(), compareReportDto.getSumCvr());
            vo.setCompareCvrRate(compareReportDto.getSumCvr().compareTo(BigDecimal.ZERO) == 0 ? "-" : cvrDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumCvr(), 2, RoundingMode.HALF_UP).toPlainString());
            if (compareReportDto.getSumImpressions() != null && compareReportDto.getSumImpressions() > 0) {
                vo.setCompareCtr(MathUtil.multiply(MathUtil.divide(String.valueOf(compareReportDto.getSumClicks()), String.valueOf(compareReportDto.getSumImpressions()), 4), "100"));
            }
            BigDecimal ctrDiff = MathUtil.subtract(reportDto.getSumCtr(), compareReportDto.getSumCtr());
            vo.setCompareCtrRate(compareReportDto.getSumCtr().compareTo(BigDecimal.ZERO) == 0 ? "-" : ctrDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumCtr(), 2, RoundingMode.HALF_UP).toPlainString());
            if (compareReportDto.getSumAdSale() != null && compareReportDto.getSumAdSale().compareTo(BigDecimal.ZERO) > 0) {
                vo.setCompareAcos(MathUtil.multiply(MathUtil.divide(String.valueOf(compareReportDto.getSumAdCost()), String.valueOf(compareReportDto.getSumAdSale()), 4), "100"));
            }
            BigDecimal acosDiff = MathUtil.subtract(reportDto.getSumAcos(), compareReportDto.getSumAcos());
            vo.setCompareAcosRate(compareReportDto.getSumAcos().compareTo(BigDecimal.ZERO) == 0 ? "-" : acosDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAcos(), 2, RoundingMode.HALF_UP).toPlainString());
            if (compareReportDto.getSumAdCost() != null && compareReportDto.getSumAdCost().compareTo(BigDecimal.ZERO) > 0) {
                vo.setCompareRoas(MathUtil.divide(String.valueOf(compareReportDto.getSumAdSale()), String.valueOf(compareReportDto.getSumAdCost()), 2));
            }
            BigDecimal roasDiff = MathUtil.subtract(reportDto.getSumRoas(), compareReportDto.getSumRoas());
            vo.setCompareRoasRate(compareReportDto.getSumRoas().compareTo(BigDecimal.ZERO) == 0 ? "-" : roasDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumRoas(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setCompareAcots(compareReportDto.getSumAcots().setScale(2, RoundingMode.HALF_UP).toPlainString());
            vo.setCompareAsots(compareReportDto.getSumAsots().setScale(2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal acotsDiff = MathUtil.subtract(reportDto.getSumAcots(), compareReportDto.getSumAcots());
            vo.setCompareAcotsRate(compareReportDto.getSumAcots().compareTo(BigDecimal.ZERO) == 0 ? "-" : acotsDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAcots(), 2, RoundingMode.HALF_UP).toPlainString());
            BigDecimal asotsDiff = MathUtil.subtract(reportDto.getSumAsots(), compareReportDto.getSumAsots());
            vo.setCompareAsotsRate(compareReportDto.getSumAsots().compareTo(BigDecimal.ZERO) == 0 ? "-" : asotsDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAsots(), 2, RoundingMode.HALF_UP).toPlainString());
            //其他产品广告订单量
            vo.setCompareOtherAdOrderNum(MathUtil.subtract(String.valueOf(compareReportDto.getSumAdOrderNum()), String.valueOf(compareReportDto.getSumSelfAdOrderNum())));
            int otherAdOrderNumDiff = reportDto.getSumOtherAdOrderNum() - compareReportDto.getSumOtherAdOrderNum();
            vo.setCompareOtherAdOrderNumRate(compareReportDto.getSumOtherAdOrderNum() == 0 ? "-" : new BigDecimal(otherAdOrderNumDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumOtherAdOrderNum()), 2, RoundingMode.HALF_UP).toPlainString());
            //其他产品广告销售额
            vo.setCompareAdOtherSales(MathUtil.subtract(String.valueOf(compareReportDto.getSumAdSale()), String.valueOf(compareReportDto.getSumAdSelfSale())));
            BigDecimal adOtherSalesDiff = MathUtil.subtract(reportDto.getSumAdOtherSales(), compareReportDto.getSumAdOtherSales());
            vo.setCompareAdOtherSalesRate(compareReportDto.getSumAdOtherSales().compareTo(BigDecimal.ZERO) == 0 ? "-" : adOtherSalesDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumAdOtherSales(), 2, RoundingMode.HALF_UP).toPlainString());
            //其他产品广告销量
            vo.setCompareAdOtherSaleNum(MathUtil.subtract(String.valueOf(compareReportDto.getSumAdSaleNum()), String.valueOf(compareReportDto.getSumAdSelfSaleNum())));
            int adOtherSaleNumDiff = reportDto.getSumAdOtherSaleNum() - compareReportDto.getSumAdOtherSaleNum();
            vo.setCompareAdOtherSaleNumRate(compareReportDto.getSumAdOtherSaleNum() == 0 ? "-" : new BigDecimal(adOtherSaleNumDiff).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(compareReportDto.getSumAdOtherSaleNum()), 2, RoundingMode.HALF_UP).toPlainString());
            //“品牌新买家”订单百分比
            if (compareReportDto.getSumAdOrderNum() != null && BigDecimal.valueOf(compareReportDto.getSumAdOrderNum()).compareTo(BigDecimal.ZERO) > 0) {
                vo.setCompareOrderRateNewToBrandFTD(MathUtil.multiply(MathUtil.divide(String.valueOf(compareReportDto.getSumOrdersNewToBrandFTD()), String.valueOf(compareReportDto.getSumAdOrderNum()), 4), "100"));
            }
            BigDecimal orderRateNewToBrandFTDDiff = MathUtil.subtract(reportDto.getSumOrderRateNewToBrandFTD(), compareReportDto.getSumOrderRateNewToBrandFTD());
            vo.setCompareOrderRateNewToBrandFTDRate(compareReportDto.getSumOrderRateNewToBrandFTD().compareTo(BigDecimal.ZERO) == 0 ? "-" : orderRateNewToBrandFTDDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumOrderRateNewToBrandFTD(), 2, RoundingMode.HALF_UP).toPlainString());
            //“品牌新买家”销售额百分比
            if (compareReportDto.getSumAdSale() != null && compareReportDto.getSumAdSale().compareTo(BigDecimal.ZERO) > 0) {
                vo.setCompareSalesRateNewToBrandFTD(MathUtil.multiply(MathUtil.divide(String.valueOf(compareReportDto.getSumSalesNewToBrandFTD()), String.valueOf(compareReportDto.getSumAdSale()), 4), "100"));
            }
            BigDecimal salesRateNewToBrandFTDDiff = MathUtil.subtract(reportDto.getSumSalesRateNewToBrandFTD(), compareReportDto.getSumSalesRateNewToBrandFTD());
            vo.setCompareSalesRateNewToBrandFTDRate(compareReportDto.getSumSalesRateNewToBrandFTD().compareTo(BigDecimal.ZERO) == 0 ? "-" : salesRateNewToBrandFTDDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumSalesRateNewToBrandFTD(), 2, RoundingMode.HALF_UP).toPlainString());
            //“品牌新买家”销量百分比
            if (compareReportDto.getSumAdSaleNum() != null && BigDecimal.valueOf(compareReportDto.getSumAdSaleNum()).compareTo(BigDecimal.ZERO) > 0) {
                vo.setCompareUnitsOrderedRateNewToBrandFTD(MathUtil.multiply(MathUtil.divide(String.valueOf(compareReportDto.getSumUnitsOrderedNewToBrandFTD()), String.valueOf(compareReportDto.getSumAdSaleNum()), 4), "100"));
            }
            BigDecimal unitsOrderedRateNewToBrandFTDDiff = MathUtil.subtract(reportDto.getSumUnitsOrderedRateNewToBrandFTD(), compareReportDto.getSumUnitsOrderedRateNewToBrandFTD());
            vo.setCompareUnitsOrderedRateNewToBrandFTDRate(compareReportDto.getSumUnitsOrderedRateNewToBrandFTD().compareTo(BigDecimal.ZERO) == 0 ? "-" : unitsOrderedRateNewToBrandFTDDiff.multiply(new BigDecimal(100))
                    .divide(compareReportDto.getSumUnitsOrderedRateNewToBrandFTD(), 2, RoundingMode.HALF_UP).toPlainString());
        }
        return vo;
    }

    @Override
    public Page<AdTagCampaignData> campaignList4AdTag(TagAdCampaignListParam param) {
        int puid = param.getPuid();
        //过滤未授权的店铺
        param.setShopIds(shopAuthDao.getAuthShopByShopIdList(puid, param.getShopIds()).stream().map(ShopAuth::getId).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(param.getShopIds())) {
            return new Page<>(param.getPageNum(), param.getPageSize());
        }
        Map<Integer, String> shopIdNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(param.getShopIds())) {
            shopIdNameMap.putAll(shopAuthDao.getShopAuthBoByIds(puid, param.getShopIds()).stream().collect(Collectors.toMap(ShopAuthBo::getId, ShopAuthBo::getName)));
        }
        List<String> campaignIds = Lists.newArrayList();
        if (StringUtils.isNotBlank(param.getProductType())
                && Constants.CAMPAIGN_PRODUCT_SELECT.contains(param.getProductType())
                && StringUtils.isNotBlank(param.getProductValue())) {
            List<String> listProductValues = StringUtil.splitStr(param.getProductValue().trim(), StringUtil.SPECIAL_COMMA);
            campaignIds = amazonAdProductDao.getCampaignIdByProduct(puid, param.getShopIds(), param.getProductType(),
                    listProductValues, param.getAdTypes(), campaignIds);
            if (CollectionUtils.isEmpty(campaignIds)) {
                return new Page(param.getPageNum(), param.getPageSize());
            }
        }
        param.setCampaignIds(campaignIds);

        // 有权限的广告组
        List<Long> groupIds = adManageTagGroupUserDao.listGroupIdByPuid(puid, AdManageTagTypeEnum.CAMPAIGN.getCode());
        List<Long> accessGroupIds;
        if (param.getIsAdmin() != null && param.getIsAdmin()) {
            accessGroupIds = groupIds;
        } else {
            accessGroupIds = adManageTagGroupUserDao.listGroupIdByUid(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), param.getUid());
        }
        if (Boolean.TRUE.equals(param.isOnlyNoTagCampaign())) {
            param.setTagGroupIds(groupIds);
        }

        Page<AmazonAdCampaignAll> page = campaignAllDorisDao.list4AdTag(param);
        List<AmazonAdCampaignAll> campaignList = page.getRows();
        if (CollectionUtils.isEmpty(campaignList)) {
            return new Page(param.getPageNum(), param.getPageSize());
        }

        List<String> portfolioIds = campaignList.stream().map(AmazonAdCampaignAll::getPortfolioId)
                .filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList());
        List<String> campaignIdList = campaignList.stream().map(AmazonAdCampaignAll::getCampaignId)
                .filter(StringUtils::isNotBlank).map(String::trim).distinct().collect(Collectors.toList());

        Map<String, AmazonAdPortfolio> portfolioMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            portfolioMap.putAll(portfolioDao.listByShopId(puid, param.getShopIds(), portfolioIds).stream()
                    .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e)));
        }
        //有权限的广告活动标签关系
        Map<String, List<AdManageTagRelation>> campaignTagRelationMap = Maps.newHashMap();
        //全部广告活动标签数量
        Map<String, Long> campaignTagCountMap = Maps.newHashMap();
        //有权限的广告活动标签
        Map<Long, AdManageTag> tagMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(groupIds)) {
            List<AdManageTagRelation> adManageTagRelationList = adManageTagRelationDao.listByRelationId(puid, AdManageTagTypeEnum.CAMPAIGN.getCode(), campaignIdList, groupIds);
            if (CollectionUtils.isNotEmpty(adManageTagRelationList)) {
                campaignTagCountMap.putAll(adManageTagRelationList.stream().collect(Collectors.groupingBy(AdManageTagRelation::getRelationId, Collectors.counting())));
                List<AdManageTagRelation> relations = adManageTagRelationList.stream().filter(e -> accessGroupIds.contains(e.getGroupId())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(relations)) {
                    campaignTagRelationMap.putAll(StreamUtil.groupingBy(relations, AdManageTagRelation::getRelationId, e -> e));
                    List<Long> tagIds = relations.stream().map(AdManageTagRelation::getTagId).distinct().collect(Collectors.toList());
                    List<AdManageTag> adManageTags = adManageTagDao.listByIds(puid, tagIds);
                    if (CollectionUtils.isNotEmpty(adManageTags)) {
                        tagMap.putAll(StreamUtil.toMap(adManageTags, AdManageTag::getId));
                    }
                }
            }
        }

        List<AdTagCampaignData> tagCampaignList = campaignList.stream().map(i -> {
            i.setServingStatus(i.getServingStatus());
            AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(i.getMarketplaceId());
            String portfolioId = StringUtils.isNotBlank(i.getPortfolioId()) ? i.getPortfolioId() : "";
            AdTagCampaignData.Builder builder = AdTagCampaignData.newBuilder();
            builder.setShopId(i.getShopId())
                    .setShopName(shopIdNameMap.getOrDefault(i.getShopId(), ""))
                    .setMarketplaceId(i.getMarketplaceId())
                    .setMarketplaceCn(Optional.ofNullable(amznEndpoint).map(AmznEndpoint::getMarketplaceCN).orElse(""))
                    .setCampaignId(i.getCampaignId())
                    .setCampaignName(i.getName())
                    .setPortfolioId(portfolioId)
                    .setPortfolioName(portfolioMap.containsKey(i.getPortfolioId()) ? portfolioMap.get(i.getPortfolioId()).getName() : "")
                    .setAdType(i.getType())
                    .setState(i.getState())
                    .setServingStatus(StringUtils.isNotBlank(i.getServingStatus()) ? i.getServingStatus() : "")
                    .setServingStatusName(StringUtils.isNotBlank(i.getServingStatusName()) ? i.getServingStatusName() : "");

            List<AdManageTagRelation> relations = campaignTagRelationMap.get(i.getCampaignId());
            if (CollectionUtils.isNotEmpty(relations)) {
                List<TagData> tagDataList = relations.stream().map(relation -> {
                    AdManageTag adManageTag = tagMap.get(relation.getTagId());
                    TagData.Builder tagBuilder = TagData.newBuilder();
                    tagBuilder.setId(relation.getTagId());
                    if (Objects.nonNull(adManageTag)) {
                        tagBuilder.setName(adManageTag.getName())
                                .setColor(adManageTag.getColor())
                                .setGroupId(adManageTag.getGroupId());
                    }
                    return tagBuilder.build();
                }).collect(Collectors.toList());
                builder.addAllTags(tagDataList);
            }
            //达到打标签上限的标识
            builder.setMaxTagLimit(campaignTagCountMap.containsKey(i.getCampaignId()) && campaignTagCountMap.get(i.getCampaignId()).equals(Constants.AD_TAG_SYSTEM_CAMPAIGN_TAG_MAX_SIZE.longValue()));
            return builder.build();
        }).collect(Collectors.toList());

        return new Page<>(page.getPageNo(), page.getPageSize(), page.getTotalPage(), page.getTotalSize(), tagCampaignList);
    }

    @Override
    public List<QueryShopInfoResp.ShopVo> getShopInfo(QueryShopInfoReq req) {
        List<CampaignDto> campaignDtos = odsAdManageTagDao.getShopIdsAndCampaignIds(req.getPuid(), req.getShopIdList(), req.getTagIdList());
//        String shopIds = String.join(",", campaignDto.stream().filter(e -> StringUtils.isNotBlank(e.getShopIds()))
//                .map(e -> StringUtil.splitStr(e.getShopIds())).flatMap(Collection::stream).map(String::trim).collect(Collectors.toSet()));
//        List<String> shopIdList = Arrays.asList(shopIds.split(","));
//        String campaignIds = String.join(",", campaignDto.stream().filter(e -> StringUtils.isNotBlank(e.getCampaignIds()))
//                .map(e -> StringUtil.splitStr(e.getCampaignIds())).flatMap(Collection::stream).map(String::trim).collect(Collectors.toSet()));
//        List<String> campaignIdList = Arrays.asList(campaignIds.split(","));
//        List<QueryShopInfoResp.ShopVo> shopVos = amazonAdCampaignAllDao.getCampaignCountGroupByShopId(req.getPuid(), shopIdList, campaignIdList);
//        List<Integer> shopId =shopIdList.stream().map(Integer::parseInt)
//                .collect(Collectors.toList());
        Map<Integer, QueryShopInfoResp.ShopVo> shopVoMap = new HashMap<>();
        for (CampaignDto campaignDto : campaignDtos) {
            if (Optional.ofNullable(campaignDto.getCampaignNum()).orElse(0) == 0) {
                continue;
            }
            QueryShopInfoResp.ShopVo shopVo = shopVoMap.get(campaignDto.getShopId());
            if (shopVo == null) {
                shopVo = new QueryShopInfoResp.ShopVo();
                shopVo.setCampaignNum(0);
            }
            shopVo.setShopId(String.valueOf(campaignDto.getShopId()));
            shopVo.setCampaignNum(shopVo.getCampaignNum() + campaignDto.getCampaignNum());
            shopVoMap.put(campaignDto.getShopId(), shopVo);
        }
        List<Integer> shopIds = campaignDtos.stream().map(CampaignDto::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuthBo> shopAuths = shopAuthDao.getShopAuthBoByIds(req.getPuid(), shopIds);
        Map<Integer, ShopAuthBo> map = StreamUtil.toMap(shopAuths, ShopAuthBo::getId);
        List<QueryShopInfoResp.ShopVo> shopVos = new ArrayList<>(shopVoMap.values());
        for (QueryShopInfoResp.ShopVo shopVo : shopVos) {
            Integer id = Integer.valueOf(shopVo.getShopId());
            if (map.containsKey(id)) {
                shopVo.setShopName(map.get(id).getName());
                shopVo.setMarketplaceId(map.get(id).getMarketplaceId());
                shopVo.setMarketplaceCn(AmznEndpoint.getByMarketplaceId(map.get(id).getMarketplaceId()).getMarketplaceCN());
            }
        }
        return shopVos;
    }
}
