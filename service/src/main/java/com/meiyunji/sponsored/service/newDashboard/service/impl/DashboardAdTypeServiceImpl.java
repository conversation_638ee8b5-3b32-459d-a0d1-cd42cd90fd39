package com.meiyunji.sponsored.service.newDashboard.service.impl;

import com.google.common.base.Stopwatch;
import com.meiyunji.sponsored.common.util.ParamCopyUtil;
import com.meiyunji.sponsored.rpc.newDashboard.DashboardAdTypeResponseVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbGroupReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSdGroupReportDao;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTypeDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardAdTypeRowEnum;
import com.meiyunji.sponsored.service.newDashboard.service.IDashboardAdTypeService;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateAdDataUtil;
import com.meiyunji.sponsored.service.newDashboard.util.CalculateUtil;
import com.meiyunji.sponsored.service.newDashboard.vo.DashboardAdTypeReqVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: sunlinfeng
 * @email: <EMAIL>
 * @date: 2024-03-25  16:47
 */

@Service
@Slf4j
public class DashboardAdTypeServiceImpl implements IDashboardAdTypeService {

    @Autowired
    private IOdsAmazonAdGroupReportDao odsAmazonAdGroupReportDao;

    @Autowired
    private IOdsAmazonAdSbGroupReportDao odsAmazonAdSbGroupReportDao;

    @Autowired
    private IOdsAmazonAdSdGroupReportDao odsAmazonAdSdGroupReportDao;

    @Autowired
    private IExcelService excelService;

    @Override
    public List<DashboardAdTypeResponseVo> queryAdTypeCharts(DashboardAdTypeReqVo reqVo) {
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }

        //查询当期的数据
        List<DashboardAdTypeDataDto> curBaseDataList = queryAdTypeChartsBaseDataByDate(reqVo.getPuid(), reqVo.getMarketplaceIdList(), 
                reqVo.getShopIdList(), reqVo.getCurrency(), reqVo.getStartDate(), reqVo.getEndDate(),
                siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());

        //查询环比期的数据
        List<DashboardAdTypeDataDto> momBaseDataList = null;
        if (reqVo.getMom() == 1) {
            momBaseDataList = queryAdTypeChartsBaseDataByDate(reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(),
                    reqVo.getCurrency(), reqVo.getMomStartDate(), reqVo.getMomEndDate(),
                    null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
        }
        //查询同比期的数据
        List<DashboardAdTypeDataDto> yoyBaseDataList = null;
        if (reqVo.getMom() == 1 && !reqVo.getYoyOverLimit()) {
            yoyBaseDataList = queryAdTypeChartsBaseDataByDate(reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(),
                    reqVo.getCurrency(), reqVo.getYoyStartDate(), reqVo.getYoyEndDate(),
                    null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
        }

        //整理当期、环比期、同比期的所有数据
        Map<String, DashboardAdTypeDataDto> dtoMap = processAdTypeChartsData(curBaseDataList, momBaseDataList, yoyBaseDataList, reqVo.getYoyOverLimit());

        //将最终数据转换为grpc的response对象
        return convertAdTypeChartsData2Response(dtoMap);
    }

    @Override
    public List<String> exportAdTypeCharts(DashboardAdTypeReqVo reqVo) {
        List<String> siteToday = null;
        if (Boolean.TRUE.equals(reqVo.getSiteToday())) {
            siteToday = CalculateAdDataUtil.getSiteToday(reqVo.getMarketplaceIdList());
        }

        //查询当期的数据
        List<DashboardAdTypeDataDto> curBaseDataList = queryAdTypeChartsBaseDataByDate(reqVo.getPuid(), reqVo.getMarketplaceIdList(),
                reqVo.getShopIdList(), reqVo.getCurrency(), reqVo.getStartDate(), reqVo.getEndDate(),
                siteToday, reqVo.getSiteToday(), reqVo.getPortfolioIds(), reqVo.getCampaignIds());

        //查询环比期的数据
        List<DashboardAdTypeDataDto> momBaseDataList = null;
        if (reqVo.getMom() == 1) {
            momBaseDataList = queryAdTypeChartsBaseDataByDate(reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(),
                    reqVo.getCurrency(), reqVo.getMomStartDate(), reqVo.getMomEndDate(),
                    null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
        }
        //查询同比期的数据
        List<DashboardAdTypeDataDto> yoyBaseDataList = null;
        if (reqVo.getYoy() == 1 && !reqVo.getYoyOverLimit()) {
            yoyBaseDataList = queryAdTypeChartsBaseDataByDate(reqVo.getPuid(), reqVo.getMarketplaceIdList(), reqVo.getShopIdList(),
                    reqVo.getCurrency(), reqVo.getYoyStartDate(), reqVo.getYoyEndDate(),
                    null, null, reqVo.getPortfolioIds(), reqVo.getCampaignIds());
        }

        //整理当期、环比期、同比期的所有数据
        Map<String, DashboardAdTypeDataDto> dtoMap = processAdTypeChartsData(curBaseDataList, momBaseDataList, yoyBaseDataList, reqVo.getYoyOverLimit());
        log.info("dashboard export adtype charts, query finished and begin export, puid: {}", reqVo.getPuid());
        //将数据写入excel并上传对象存储，返回url
        String url = writeExcelAndUpload(dtoMap, reqVo);
        if (StringUtils.isBlank(url)) {
            return null;
        }
        log.info("dashboard export adtype charts, puid: {}, url: {}", reqVo.getPuid(), url);
        return Collections.singletonList(url);
    }

    /**
     * 查询指定周期内的广告类型数据，并为没有数据的类型填充0
     * @param puid
     * @param marketplaceIdList
     * @param shopIdList
     * @param currency
     * @param startDate
     * @param endDate
     * @return
     */
    private List<DashboardAdTypeDataDto> queryAdTypeChartsBaseDataByDate(Integer puid,
                                                                         List<String> marketplaceIdList,
                                                                         List<Integer> shopIdList,
                                                                         String currency,
                                                                         String startDate,
                                                                         String endDate, List<String> siteToday, Boolean isSiteToday,
                                                                         List<String> portfolioIds, List<String> campaignIds) {
        //取得当期SP数据
        List<DashboardAdTypeDataDto> spAdTypeDataList = odsAmazonAdGroupReportDao.queryAdTypeCharts(puid, marketplaceIdList, shopIdList, currency, startDate, endDate, siteToday, isSiteToday, portfolioIds, campaignIds);
        //取得当期SB数据
        List<DashboardAdTypeDataDto> sbAdTypeDataList = odsAmazonAdSbGroupReportDao.queryAdTypeCharts(puid, marketplaceIdList, shopIdList, currency, startDate, endDate, siteToday, isSiteToday, portfolioIds, campaignIds);
        //取得当期SD数据
        List<DashboardAdTypeDataDto> sdAdTypeDataList = odsAmazonAdSdGroupReportDao.queryAdTypeCharts(puid, marketplaceIdList, shopIdList, currency, startDate, endDate, siteToday, isSiteToday, portfolioIds, campaignIds);

        //补齐空数据，先收集所有数据，sp查出来没有groupType，需要手动填充一下
        List<DashboardAdTypeDataDto> allAdTypeBaseDataList = new ArrayList<>(23);
        spAdTypeDataList.forEach(x -> {
            x.setGroupType(DashboardAdTypeRowEnum.SP.getGroupType());
            allAdTypeBaseDataList.add(x);
        });
        allAdTypeBaseDataList.addAll(sbAdTypeDataList);
        allAdTypeBaseDataList.addAll(sdAdTypeDataList);
        //收集类型
        Set<String> dbTargetTypeSet = allAdTypeBaseDataList.stream().map(x -> x.getGroupType() + x.getTargetType()).collect(Collectors.toSet());
        //不存在的类型填充0
        DashboardAdTypeRowEnum.allTargetSet.forEach(x -> {
            if (!dbTargetTypeSet.contains(x)) {
                allAdTypeBaseDataList.add(builderZeroData(DashboardAdTypeRowEnum.map.get(x)));
            }
        });

        return allAdTypeBaseDataList;
    }


    /**
     * 为指定类型生成数据为0的对象
     * @param adTypeRowEnum
     * @return
     */
    private DashboardAdTypeDataDto builderZeroData(DashboardAdTypeRowEnum adTypeRowEnum) {
        DashboardAdTypeDataDto adTypeDto = new DashboardAdTypeDataDto();
        adTypeDto.setGroupType(adTypeRowEnum.getGroupType());
        adTypeDto.setTargetType(adTypeRowEnum.getTargetType());
        CalculateAdDataUtil.buildZeroAdBaseData(adTypeDto);
        return adTypeDto;
    }

    /**
     * 根据当期、环比期、同比期的基础数据，对数据进行整理
     * 当期数据需要遍历3次，同环比期数据需要遍历1次
     * 第一次遍历，3期数据转map、并计算好所有其他汇总行的基础数据
     * 第二次遍历，根据3个map，对应计算除基础数据外的其他数据，各项同环比增长和占比指标
     * @param curBaseDataList 当期数据
     * @param momBaseDataList 环比期数据
     * @param yoyBaseDataList 同比期数据
     * @param yoyOverLimit 同比期是否超出2年，超出时所有同比指标显示--
     * @return
     */
    private Map<String, DashboardAdTypeDataDto> processAdTypeChartsData(List<DashboardAdTypeDataDto> curBaseDataList, List<DashboardAdTypeDataDto> momBaseDataList, List<DashboardAdTypeDataDto> yoyBaseDataList, Boolean yoyOverLimit) {
        //转map，在转map过程中顺便把各项数据计算出来，除去已有得数据项，还有汇总、SP、SB、SD、SP手动、SB商品集、SB视频、SB旗舰店、SD商品、SD受众共10项数据需要计算
        Map<String, DashboardAdTypeDataDto> curBaseDataMap = convertBaseDataMap(curBaseDataList);
        Map<String, DashboardAdTypeDataDto> momBaseDataMap = convertBaseDataMap(momBaseDataList);
        Map<String, DashboardAdTypeDataDto> yoyBaseDataMap = convertBaseDataMap(yoyBaseDataList);

        //以上3个map对应了所有行的基础数据
        //遍历当期数据，构建其他计算的指标
        //当期汇总数据
        DashboardAdTypeDataDto summaryData = curBaseDataMap.get(DashboardAdTypeRowEnum.ALL.getGroupType());
        curBaseDataMap.forEach((k, v) -> computeAdTypeData(v, summaryData, momBaseDataMap.get(k), yoyBaseDataMap.get(k), yoyOverLimit));

        return curBaseDataMap;
    }

    /**
     * 根据枚举类的根节点，向下遍历构造response数据
     * @param curBaseDataMap
     * @return
     */
    private List<DashboardAdTypeResponseVo> convertAdTypeChartsData2Response(Map<String, DashboardAdTypeDataDto> curBaseDataMap) {
        //所有数据计算完毕，构建grpc响应数据
        List<DashboardAdTypeResponseVo> list = new ArrayList<>(curBaseDataMap.size());
        //汇总和SP、SB、SD平铺开
        list.add(buildGrpcResponseVo(curBaseDataMap.get(DashboardAdTypeRowEnum.ALL.getGroupType())).build());
        //递归构建SP、SB、SD及其子节点
        list.add(buildGrpcResponseVoRecursion(DashboardAdTypeRowEnum.SP, curBaseDataMap));
        list.add(buildGrpcResponseVoRecursion(DashboardAdTypeRowEnum.SB, curBaseDataMap));
        list.add(buildGrpcResponseVoRecursion(DashboardAdTypeRowEnum.SD, curBaseDataMap));

        return list;
    }


    /**
     * 把最底层的基础数据进行汇总，计算得到汇总的10行数据
     * 除去已有得数据项，还有汇总、SP、SB、SD、SP手动、SB商品集、SB视频、SB旗舰店、SD商品、SD受众共10项数据需要计算
     * @param baseDataList
     * @return
     */
    private Map<String, DashboardAdTypeDataDto> convertBaseDataMap(List<DashboardAdTypeDataDto> baseDataList) {
        //空不处理
        if (CollectionUtils.isEmpty(baseDataList)) {
            return new HashMap<>();
        }

        //构造map
        Map<String, DashboardAdTypeDataDto> baseDataMap = new HashMap<>(32);
        //先构造10个汇总对象并放入map
        DashboardAdTypeRowEnum.allComputeSet.forEach(x -> baseDataMap.put(x.getGroupType() + x.getTargetType(), builderZeroData(x)));

        //遍历底层基础数据，放入map，并累加到父层级
        baseDataList.forEach(x -> {
            baseDataMap.put(x.getGroupType() + x.getTargetType(), x);
            DashboardAdTypeRowEnum parent = DashboardAdTypeRowEnum.map.get(DashboardAdTypeRowEnum.map.get(x.getGroupType() + x.getTargetType()).getParent());
            //一直往父层级累加
            while (parent != null) {
                DashboardAdTypeDataDto parentDto = baseDataMap.get(parent.getGroupType() + parent.getTargetType());
                //累加各项基础指标
                parentDto.setCost(parentDto.getCost().add(x.getCost()));
                parentDto.setTotalSales(parentDto.getTotalSales().add(x.getTotalSales()));
                parentDto.setImpressions(parentDto.getImpressions() + x.getImpressions());
                parentDto.setClicks(parentDto.getClicks() + x.getClicks());
                parentDto.setOrderNum(parentDto.getOrderNum() + x.getOrderNum());
                parentDto.setSaleNum(parentDto.getSaleNum() + x.getSaleNum());
                //获取父层级进行累加
                parent = DashboardAdTypeRowEnum.map.get(parent.getParent());
            }
        });

        return baseDataMap;
    }


    /**
     * 计算除基础数据外的其他指标
     * @param curData 当期数据
     * @param summaryData 当期的汇总数据
     * @param momData 环比数据
     * @param yoyData 同比数据
     * @param yoyOverLimit 同比是否超出2年，超出则同比指标给--
     */
    private void computeAdTypeData(DashboardAdTypeDataDto curData,
                                   DashboardAdTypeDataDto summaryData,
                                   DashboardAdTypeDataDto momData,
                                   DashboardAdTypeDataDto yoyData,
                                   Boolean yoyOverLimit) {
        //处理计算指标：acos、clickRate、conversionRate、cpc、cpa
        CalculateAdDataUtil.calAdCalData(curData);
        if (Objects.nonNull(momData)) {
            CalculateAdDataUtil.calAdCalData(momData);
        }
        if (Objects.nonNull(yoyData)) {
            CalculateAdDataUtil.calAdCalData(yoyData);
        }


        //处理当期占比指标：costPercent、totalSalesPercent、impressionsPercent、clicksPercent、orderNumPercent、saleNumPercent
        CalculateAdDataUtil.calAdPercentData(curData, summaryData);

        //处理环比增长率：costMomRate、totalSalesMomRate、impressionsMomRate、clicksMomRate、orderNumMomRate、saleNumMomRate、acosMomRate、clickRateMomRate、conversionRateMomRate、cpcMomRate、cpaMomRate
        if (Objects.nonNull(momData)) {
            CalculateAdDataUtil.calAdMomData(curData, momData);
        }

        //处理同比增长率：costYoyRate、totalSalesYoyRate、impressionsYoyRate、clicksYoyRate、orderNumYoyRate、saleNumYoyRate、acosYoyRate、clickRateYoyRate、conversionRateYoyRate、cpcYoyRate、cpaYoyRate
        if (Objects.nonNull(yoyData)) {
            CalculateAdDataUtil.calAdYoyData(curData, yoyData);
        } else if (yoyOverLimit) {
            CalculateAdDataUtil.calAdYoyData(curData, null);
        }

        //设置类型
        curData.setDataType(DashboardAdTypeRowEnum.map.get(curData.getGroupType() + curData.getTargetType()).getDesc());
    }


    /**
     * 根据父节点，递归构建父节点和子节点的grpc响应对象
     * @param rowEnum 初始父节点行
     * @param dataMap 当期数据
     * @return
     */
    private DashboardAdTypeResponseVo buildGrpcResponseVoRecursion(DashboardAdTypeRowEnum rowEnum, Map<String, DashboardAdTypeDataDto> dataMap) {
        //构建当前节点
        DashboardAdTypeResponseVo.Builder responseVoBuilder = buildGrpcResponseVo(dataMap.get(rowEnum.getGroupType() + rowEnum.getTargetType()));

        if (CollectionUtils.isNotEmpty(rowEnum.getChildList())) {
            List<DashboardAdTypeResponseVo> responseVoChildList = new ArrayList<>(rowEnum.getChildList().size());
            for (DashboardAdTypeRowEnum childRowEnum : rowEnum.getChildList()) {
                responseVoChildList.add(buildGrpcResponseVoRecursion(childRowEnum, dataMap));
            }
            responseVoBuilder.addAllChild(responseVoChildList);

        }

        return responseVoBuilder.build();
    }


    /**
     * 单条数据构建grpc响应对象
     * @param dataDto 当期数据，已计算好同环占比
     * @return
     */
    private DashboardAdTypeResponseVo.Builder buildGrpcResponseVo(DashboardAdTypeDataDto dataDto) {
        DashboardAdTypeResponseVo.Builder builder = DashboardAdTypeResponseVo.newBuilder();
        //String类型直接拷贝
        String[] nullOrEmpty = ParamCopyUtil.checkPropertiesNullOrEmptySuper(dataDto);
        BeanUtils.copyProperties(dataDto, builder, nullOrEmpty);
        //手动设置的类型
        builder.setDataType(dataDto.getDataType());
        builder.setCost(CalculateUtil.formatDecimal(dataDto.getCost()));
        builder.setTotalSales(CalculateUtil.formatDecimal(dataDto.getTotalSales()));
        builder.setImpressions(String.valueOf(dataDto.getImpressions()));
        builder.setClicks(String.valueOf(dataDto.getClicks()));
        builder.setOrderNum(String.valueOf(dataDto.getOrderNum()));
        builder.setSaleNum(String.valueOf(dataDto.getSaleNum()));
        builder.setAcos(CalculateUtil.formatPercent(dataDto.getAcos()));
        builder.setClickRate(CalculateUtil.formatPercent(dataDto.getClickRate()));
        builder.setConversionRate(CalculateUtil.formatPercent(dataDto.getConversionRate()));
        builder.setCpc(CalculateUtil.formatDecimal(dataDto.getCpc()));
        builder.setCpa(CalculateUtil.formatDecimal(dataDto.getCpa()));
        return builder;
    }

    //表头
    private List<String> baseHeaderList = Arrays.asList("dataType",
            "displayCost", "costPercent", "costMomRate", "costYoyRate",
            "displayTotalSales", "totalSalesPercent", "totalSalesMomRate", "totalSalesYoyRate",
            "displayAcos", "acosMomRate", "acosYoyRate",
            "impressions", "impressionsPercent", "impressionsMomRate", "impressionsYoyRate",
            "clicks", "clicksPercent", "clicksMomRate", "clicksYoyRate",
            "orderNum", "orderNumPercent", "orderNumMomRate", "orderNumYoyRate",
            "displayClickRate", "clickRateMomRate", "clickRateYoyRate",
            "displayConversionRate", "conversionRateMomRate", "conversionRateYoyRate",
            "saleNum", "saleNumPercent", "saleNumMomRate", "saleNumYoyRate",
            "displayCpc", "cpcMomRate", "cpcYoyRate",
            "displayCpa", "cpaMomRate", "cpaYoyRate");

    private static final String fileName = "广告类型";


    /**
     * 把数据写入excel并传到对象存储
     * @param dtoMap
     * @param reqVo
     * @return
     */
    private String writeExcelAndUpload(Map<String, DashboardAdTypeDataDto> dtoMap, DashboardAdTypeReqVo reqVo) {
        //所有数据计算完毕，构建导出数据
        List<DashboardAdTypeDataDto> list = new ArrayList<>(dtoMap.size());
        DashboardAdTypeRowEnum.rowList.forEach(x -> {
            DashboardAdTypeDataDto dataDto = dtoMap.get(x);
            CalculateAdDataUtil.calAdCalDataForExport(dataDto, reqVo.getCurrency());
            dataDto.setDataType(DashboardAdTypeRowEnum.map.get(dataDto.getGroupType() + dataDto.getTargetType()).getExportName());
            list.add(dataDto);
        });

        List<String> headers = new ArrayList<>(baseHeaderList);
        //计算导出列
        if (reqVo.getPercent() != 1) {
            headers = headers.stream().filter(x -> !x.contains("Percent")).collect(Collectors.toList());
        }
        if (reqVo.getMom() != 1) {
            headers = headers.stream().filter(x -> !x.contains("MomRate")).collect(Collectors.toList());
        }
        if (reqVo.getYoy() != 1) {
            headers = headers.stream().filter(x -> !x.contains("YoyRate")).collect(Collectors.toList());
        }


        //写excel并上传
        return excelService.easyExcelHandlerDownload(reqVo.getPuid(), list, fileName, DashboardAdTypeDataDto.class, headers, true);

    }
}
