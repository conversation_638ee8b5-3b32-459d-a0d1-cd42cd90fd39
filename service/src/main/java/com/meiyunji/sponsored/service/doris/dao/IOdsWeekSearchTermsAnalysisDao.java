package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.amc.dto.QueryAbaRankDto;
import com.meiyunji.sponsored.service.amc.dto.QueryWordAdSpaceReportDto;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.searchTermsAnalysis.dto.MarketplaceStartDateDto;
import com.meiyunji.sponsored.service.searchTermsAnalysis.qo.SearchTermsAnalysisTrendQo;
import com.meiyunji.sponsored.service.searchTermsAnalysis.vo.MuiltMarketplaceRankVo;

import java.util.Date;
import java.util.List;

public interface IOdsWeekSearchTermsAnalysisDao extends IDorisBaseDao<OdsWeekSearchTermsAnalysis> {
    OdsWeekSearchTermsAnalysis getLatestDate(String marketplaceId);

    List<OdsWeekSearchTermsAnalysis> getLatestDateList(List<String> marketplaceIdList);

    List<OdsWeekSearchTermsAnalysis> queryRanks(List<String> searchTerms, String marketplaceId, String startDate);

    List<OdsWeekSearchTermsAnalysis> queryRanks(List<String> searchTerms, List<String> marketplaceIdList, List<String> startDateList, List<String> marketplaceStartDateList);

    List<OdsWeekSearchTermsAnalysis> queryRanks(List<QueryAbaRankDto> dtoList);

    List<OdsWeekSearchTermsAnalysis> getTrend(SearchTermsAnalysisTrendQo param, String minDay);

    List<OdsWeekSearchTermsAnalysis> getLatestDateAndMarketplaceId(List<String> keywordTexts);

    List<MuiltMarketplaceRankVo> getRank(List<MarketplaceStartDateDto> muiltMarketplaceRankList, List<String> keywordTexts);

    List<OdsWeekSearchTermsAnalysis> taskGetLatestDateAndMarketplaceId(String createTime, List<String> keywords);
}
