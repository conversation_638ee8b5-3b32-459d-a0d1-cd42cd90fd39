package com.meiyunji.sponsored.service.multiPlatform.walmart.util;

import com.alibaba.nacos.common.NotThreadSafe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;

import com.meiyunji.sponsored.common.exception.ServiceException;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.*;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.config.SocketConfig;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicCookieStore;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.Charset;
import java.security.KeyManagementException;
import java.security.KeyStore;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;


class AnyTrustStrategy implements TrustStrategy{

	@Override
	public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException {
		return true;
	}

}

public class HttpUtilFinal {

	private static final Logger log= LoggerFactory.getLogger(HttpUtilFinal.class);

	private static final int REQUEST_TIMEOUT = 15 * 1000; // 设置请求超时

	public static final int TIMEOUT         = 15 * 1000; // 连接超时时间

	public static final int SO_TIMEOUT      = 60 * 1000; // 数据传输超时

	private static int bufferSize= 1024;

	private static volatile HttpUtilFinal instance;

	private ConnectionConfig connConfig;

	private SocketConfig socketConfig;

	private ConnectionSocketFactory plainSF;

	private KeyStore trustStore;

	private SSLContext sslContext;

	private LayeredConnectionSocketFactory sslSF;

	private Registry<ConnectionSocketFactory> registry;

	private PoolingHttpClientConnectionManager connManager;

	private volatile HttpClient client;

	private volatile BasicCookieStore cookieStore;

	public static String defaultEncoding= "utf-8";

	private static List<NameValuePair> paramsConverter(Map<String, String> params){
		List<NameValuePair> nvps = new LinkedList<>();
		Set<Entry<String, String>> paramsSet= params.entrySet();
		for (Entry<String, String> paramEntry : paramsSet) {
			if(paramEntry.getKey().equals("sku") || paramEntry.getKey().equals("parent_sku")){
				nvps.add(new BasicNameValuePair(paramEntry.getKey(), StringEscapeUtils.unescapeHtml4(paramEntry.getValue())));
			}else{
				nvps.add(new BasicNameValuePair(paramEntry.getKey(), paramEntry.getValue()));
			}
		}
		return nvps;
	}

	public static String readStream(InputStream in, String encoding) {
		if (in == null) {
			return null;
		}
		InputStreamReader inReader = null;
		try {
			inReader = new InputStreamReader(in, Strings.isNullOrEmpty(encoding) ? defaultEncoding : encoding);
			char[] buffer = new char[bufferSize];
			int readLen;
			StringBuilder sb = new StringBuilder();
			while (0 < (readLen = inReader.read(buffer))) {
				sb.append(buffer, 0, readLen);
			}
			return sb.toString();
		} catch (IOException e) {
			log.error("读取返回内容出错", e);
		} finally {
			if (inReader != null) {
				try {
					inReader.close();
				} catch (IOException ignore) {
				}
			}
			try {
				in.close();
			} catch (IOException ignore) {
			}
		}
		return null;
	}

	public void doPdf(HttpGet httpGet, String url,OutputStream os) {
		try {
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectTimeout(TIMEOUT)
					.setConnectionRequestTimeout(REQUEST_TIMEOUT).build();
			httpGet.setConfig(requestConfig);
			URIBuilder builder = new URIBuilder(url);
			httpGet.setURI(builder.build());
			HttpResponse response = client.execute(httpGet);
			Header[] requestId = response.getHeaders("Wish-Request-Id");
			log.info(requestId != null && requestId.length > 0 ? "doPdf请求 Wish-Request-Id : " + requestId[0] :"");
			HttpUtilFinal.readPdf(response != null ? response.getEntity().getContent() : null, os, null);
		} catch (Exception e) {
			log.error(url,e);
		} finally {
			if (os != null){
				try {
					os.close ();
				} catch (IOException e) {
					log.error("",e);
				}
			}
			if (httpGet != null){
				httpGet.releaseConnection();
			}
		}
	}

	public void doCodePdf(HttpGet httpGet, String url,OutputStream os) throws Exception{
		try{
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectTimeout(TIMEOUT)
					.setConnectionRequestTimeout(REQUEST_TIMEOUT).build();
			httpGet.setConfig(requestConfig);
			URIBuilder builder = new URIBuilder(url);
			httpGet.setURI(builder.build());
			HttpResponse response = client.execute(httpGet);
			Header[] requestId = response.getHeaders("Wish-Request-Id");
			log.info(requestId != null && requestId.length > 0 ? "doCodePdf请求 Wish-Request-Id : " + requestId[0] : "");
			int code = response.getStatusLine().getStatusCode();
			if(code == 200){
				HttpUtilFinal.readPdf(response != null ? response.getEntity().getContent() : null, os, null);
			}else{
				throw new ServiceException(""+code);
			}
		} finally {
			if (os != null){
				try {
					os.close ();
				} catch (IOException e) {
					log.error("",e);
				}
			}
			if (httpGet != null){
				httpGet.releaseConnection();
			}
		}
	}

	public void doPostPdf(HttpPost httpPost, String url,OutputStream os) {
		try {
			RequestConfig requestConfig = RequestConfig.custom()
					.setConnectTimeout(TIMEOUT)
					.setConnectionRequestTimeout(REQUEST_TIMEOUT).build();
			httpPost.setConfig(requestConfig);
			URIBuilder builder = new URIBuilder(url);
			httpPost.setURI(builder.build());
			HttpResponse response = client.execute(httpPost);
			int code = response.getStatusLine().getStatusCode();
			if(code == 200){
				HttpUtilFinal.readPdf(response != null ? response.getEntity().getContent() : null, os, null);
			}else{
				throw new ServiceException(""+code);
			}
		} catch (Exception e) {
			log.error(url,e);
		} finally {
			if (os != null){
				try {
					os.close ();
				} catch (IOException e) {
					log.error("",e);
				}
			}
			if (httpPost != null){
				httpPost.releaseConnection();
			}
		}
	}

	public static void readPdf(InputStream in,OutputStream os, String encoding) {
		if (in == null) {
			return ;
		}
		try {
			byte[] buf = new byte[255];
			int len;
			while ((len = in.read(buf)) != -1) {
				os.write(buf, 0, len);
			}
			os.flush();
			os.close();
		} catch (IOException e) {
			log.error("读取返回内容出错", e);
		}
	}

	private HttpUtilFinal(){
		//设置连接参数
		connConfig = ConnectionConfig.custom().setCharset(Charset.forName(defaultEncoding)).build();
		socketConfig = SocketConfig.custom().setSoTimeout(SO_TIMEOUT).build();
		RegistryBuilder<ConnectionSocketFactory> registryBuilder = RegistryBuilder.<ConnectionSocketFactory>create();
		plainSF = new PlainConnectionSocketFactory();
		registryBuilder.register("http", plainSF);
		//指定信任密钥存储对象和连接套接字工厂
		try {
			trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
			sslContext = SSLContexts.custom().useTLS().loadTrustMaterial(trustStore, new AnyTrustStrategy()).build();
			sslSF = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
			registryBuilder.register("https", sslSF);
		} catch (KeyStoreException | KeyManagementException | NoSuchAlgorithmException e) {
			throw new RuntimeException(e);
		}
		registry = registryBuilder.build();
		//设置连接管理器
		connManager = new PoolingHttpClientConnectionManager(registry);
		connManager.setDefaultConnectionConfig(connConfig);
		connManager.setDefaultSocketConfig(socketConfig);
		connManager.setMaxTotal(200);
		connManager.setDefaultMaxPerRoute(100);
		//指定cookie存储对象
		cookieStore = new BasicCookieStore();
		//构建客户端
		client= HttpClientBuilder.create().setDefaultCookieStore(cookieStore).setConnectionManager(connManager).build();
	}

	public static HttpUtilFinal getInstance(){
		synchronized (HttpUtilFinal.class) {
			if (HttpUtilFinal.instance == null){
				instance = new HttpUtilFinal();
			}
			return instance;
		}
	}

	public InputStream doGet(HttpGet gm, String url) throws URISyntaxException, IOException{
		try {
			HttpResponse response= this.doGet(gm, url, null, null,null);
			return response!=null ? response.getEntity().getContent() : null;

		} catch (Exception e) {
			log.error(url,e);
		}
		return null;
	}

	public String doGetForString(String url,String encoding) throws URISyntaxException, IOException{
		HttpGet gm = new HttpGet();
		InputStream in = null;
		try{
			in = this.doGet(gm, url);
			return HttpUtilFinal.readStream(in, encoding);

		} catch (Exception e){
			log.error(url,e);

		} finally {
			if (in != null){
				try {
					in.close ();
				} catch (IOException e) {
					log.error("",e);
				}
			}
			gm.releaseConnection();
		}
		return null;
	}

	public InputStream doGetForStream(HttpGet gm, String url, Map<String, String> queryParams, HttpHost proxy,
									  Map<String,String> headerMap) throws URISyntaxException, IOException{
		HttpResponse response= this.doGet(gm ,url, queryParams, proxy, headerMap);
		return response!=null ? response.getEntity().getContent() : null;
	}

	public String doGetForString(String url, Map<String, String> queryParams, HttpHost proxy,
								 Map<String,String> headerMap) throws URISyntaxException, IOException{
		HttpGet gm = new HttpGet();
		InputStream in = null;
		try{
			in = this.doGetForStream(gm, url, queryParams, proxy, headerMap);
			return HttpUtilFinal.readStream(in, null);

		} catch (Exception e){
			log.error(url,e);
		} finally {
			if (in != null){
				try {
					in.close ();
				} catch (IOException e) {
					log.error("",e);
				}
			}
			gm.releaseConnection();
		}
		return null;
	}

	/**
	 * 基本的Get请求
	 * @param url 请求url
	 * @param queryParams 请求头的查询参数
	 * @throws URISyntaxException
	 * @throws IOException
	 * @throws ClientProtocolException
	 */
	public HttpResponse doGet(HttpGet gm, String url, Map<String, String> queryParams, HttpHost proxy,
							  Map<String,String> headerMap) throws URISyntaxException, IOException{

		RequestConfig requestConfig;
		if(proxy != null){
			requestConfig = RequestConfig.custom()
					.setConnectTimeout(TIMEOUT)
					.setConnectionRequestTimeout(REQUEST_TIMEOUT).setProxy(proxy).build();
		}else{
			requestConfig = RequestConfig.custom()
					.setConnectTimeout(TIMEOUT)
					.setConnectionRequestTimeout(REQUEST_TIMEOUT).build();
		}
		gm.setConfig(requestConfig);

		URIBuilder builder = new URIBuilder(url);
		//填入查询参数
		if (queryParams!=null && !queryParams.isEmpty()){
			builder.setParameters(HttpUtilFinal.paramsConverter(queryParams));
		}
		if(headerMap != null && !headerMap.isEmpty()){
			for (Entry<String, String> entry : headerMap.entrySet()) {
				gm.setHeader(entry.getKey(), entry.getValue());
			}
		}
		gm.setURI(builder.build());
		HttpResponse response =  client.execute(gm);
		Header[] requestId = response.getHeaders("Wish-Request-Id");
		log.info(requestId != null && requestId.length > 0 ? "doGet请求 Wish-Request-Id : " + requestId[0] : "");
		Header[] requestLimit = response.getHeaders("Wish-Rate-Limit-Reset");
		log.info(requestLimit != null && requestLimit.length > 0 ? "doGet请求 Wish-Rate-Limit-Reset : " + requestLimit[0] : "");
		return response;
	}


}