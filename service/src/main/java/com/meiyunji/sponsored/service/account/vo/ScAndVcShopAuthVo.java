package com.meiyunji.sponsored.service.account.vo;

import com.meiyunji.amazon.mws.base.AmznEndpoint;

import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import com.meiyunji.sponsored.service.enums.ShopSpStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * @description: some desc
 * @author: pxq
 * @email: <EMAIL>
 * @date: 2025/4/10 14:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ScAndVcShopAuthVo {

    private Integer id;

    private Integer puid;

    private String name;

    private String sellingPartnerId;

    private String region;

    private String marketplaceId;

    private String mwsAuthToken;

    private String spRefreshToken;

    private String spAccessToken;

    private Date spAuthTime;

    private String spStatus;

    private String adRefreshToken;

    private String adAccessToken;

    private Date adAuthTime;

    private String adStatus;

    private Integer status;

    private Integer createId;

    private Integer updateId;

    public String getSpStatusName() {
        if (spStatus != null) {
            return ShopSpStatusEnum.getSpTypeByName(spStatus).getChineseName();
        }
        return null;
    }

    public String getAdStatusName() {
        if (adStatus != null) {
            return ShopAdStatusEnum.getAdTypeByName(adStatus).getChineseName();
        }
        return null;
    }

    public String getRegionName() {
        if (region != null) {
            switch (region) {
                case "na":
                    return "北美区";
                case "eu":
                    return "欧洲区";
                case "fe":
                    return "远东区";
                default:
                    return "";
            }
        }
        return null;
    }


    public String getWarehouseName() {
        if (StringUtils.isBlank(marketplaceId)) {
            return "";
        }
        AmznEndpoint endpoint = AmznEndpoint.getByMarketplaceId(marketplaceId);
        if (endpoint != null) {
            return this.name + "-" + endpoint.getMarketplaceCN() + "仓";
        }
        return this.name + "仓";
    }

    public String getMarketplace() {
        if (marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplace().toLowerCase();
        }
        return null;
    }

    public String getMarketplaceName() {
        if (marketplaceId != null) {
            return AmznEndpoint.getByMarketplaceId(marketplaceId).getMarketplaceCN();
        }
        return null;
    }

}
