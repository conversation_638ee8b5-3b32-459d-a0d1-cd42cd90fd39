package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonSbAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdTargeting;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.AdGroupTargetCountDto;
import com.meiyunji.sponsored.service.cpc.vo.SbTargetingPageParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by lm on 2021/7/29.
 */
@Repository
public class AmazonSbAdNeTargetingDaoImpl extends BaseShardingDaoImpl<AmazonSbAdNeTargeting> implements IAmazonSbAdNeTargetingDao {

    private final int imgLimit = 1000;
    private final int titleLimit = 1000;

    @Override
    public void insertOrUpdate(Integer puid, List<AmazonSbAdNeTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append("(`puid`, `shop_id`, `marketplace_id`, `profile_id`, `target_id`, `ad_group_id`,");
        sql.append(" `campaign_id`, `type`, `target_text`, `expression`, `resolved_expression`,");
        sql.append(" `state`, `create_time`, `update_time`) values ");

        List<Object> argsList = Lists.newArrayList();
        for (AmazonSbAdNeTargeting neTarget : list) {
            sql.append(" (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, now(), now()),");
            argsList.add(puid);
            argsList.add(neTarget.getShopId());
            argsList.add(neTarget.getMarketplaceId());
            argsList.add(neTarget.getProfileId());
            argsList.add(neTarget.getTargetId());
            argsList.add(neTarget.getAdGroupId());
            argsList.add(neTarget.getCampaignId());
            argsList.add(neTarget.getType());
            argsList.add(neTarget.getTargetText());
            argsList.add(neTarget.getExpression());
            argsList.add(neTarget.getResolvedExpression());
            argsList.add(neTarget.getState());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `type`=values(type),`target_text`=values(target_text),`expression`=values(expression),");
        sql.append("`resolved_expression`=values(resolved_expression),`state`=values(state),`update_time`=values(update_time)");
        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public void batchAdd(int puid, List<AmazonSbAdNeTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonSbAdNeTargeting targeting : list) {
            if (targeting.getPuid() == null || targeting.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(targeting.getPuid());
            arg.add(targeting.getShopId());
            arg.add(targeting.getMarketplaceId());
            arg.add(targeting.getProfileId());
            arg.add(targeting.getTargetId());
            arg.add(targeting.getAdGroupId());
            arg.add(targeting.getCampaignId());
            arg.add(targeting.getType());
            arg.add(targeting.getTargetText());
            arg.add(targeting.getExpression());
            arg.add(targeting.getResolvedExpression());
            arg.add(targeting.getState());
            arg.add(targeting.getTitle() == null ? "" : (targeting.getTitle().length() > titleLimit ? targeting.getTitle().substring(0, titleLimit) : targeting.getTitle()));
            arg.add(targeting.getImgUrl() == null ? "" : (targeting.getImgUrl().length() > titleLimit ? targeting.getImgUrl().substring(0, imgLimit) : targeting.getImgUrl()));
            arg.add(targeting.getCreateId());
            arg.add(targeting.getUpdateId());
            arg.add(targeting.getCreateInAmzup());
            argList.add(arg.toArray());
        }

        String sql = "insert into t_amazon_ad_netargeting_sb (`puid`,`shop_id`,`marketplace_id`,`profile_id`,`target_id`," +
                "`ad_group_id`,`campaign_id`,`type`,`target_text`,`expression`,`resolved_expression`,`state`,`title`," +
                "`img_url`,`create_id`,`update_id`,`create_in_amzup`,`create_time`,`update_time`) " +
                "values (?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?,?, ?, now(), now())";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public void batchUpdate(int puid, List<AmazonSbAdNeTargeting> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(list.size());
        List<Object> arg;

        for (AmazonSbAdNeTargeting targeting : list) {
            if (targeting.getPuid() == null || targeting.getPuid() != puid) {
                continue;
            }
            arg = new ArrayList<>();
            arg.add(targeting.getState());
            arg.add(targeting.getType());
            arg.add(targeting.getTargetText());
            arg.add(targeting.getExpression());
            arg.add(targeting.getResolvedExpression());
            arg.add(targeting.getUpdateId());
            arg.add(targeting.getPuid());
            arg.add(targeting.getShopId());
            arg.add(targeting.getTargetId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_netargeting_sb set `state`=?,`type`=?,`target_text`=?," +
                "`expression`=?,`resolved_expression`=?,`update_id`=? " +
                " where puid=? and shop_id=? and target_id=?";
        getJdbcTemplate(puid).batchUpdate(sql, argList);
    }

    @Override
    public List<AmazonSbAdNeTargeting> listByTargetId(int puid, int shopId, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .in("target_id", targetIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonSbAdNeTargeting> listByTargetId(int puid, List<Integer> shopIds, List<String> targetIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("target_id", targetIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public Page<AmazonSbAdNeTargeting> pageList(int puid, SbTargetingPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("campaign_id", param.getCampaignId())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.archived.name(), CpcStatusEnum.pending.name()});

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalToWithoutCheck("state", param.getStatus());
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("target_text", param.getSearchValue());
        }

        String orderBySql = " order by create_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public AmazonSbAdNeTargeting getByTargetId(Integer puid, Integer shopId, String targetId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId)
                .equalToWithoutCheck("target_id", targetId)
                .build();

        return getByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AdGroupTargetCountDto> countByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", groupIdList.toArray(new String[]{}))
                .groupBy("ad_group_id")
                .build();
        String sql = "select ad_group_id adGroupId, count(*) targetCount from " + getJdbcHelper().getTable() + " where " + builder.getSql();
        return getJdbcTemplate(puid).query(sql, new BeanPropertyRowMapper<>(AdGroupTargetCountDto.class), builder.getValues());

    }

    @Override
    public List<AmazonSbAdNeTargeting> getListTargetByQuery(Integer puid, Integer shopId, String adGroupId, String targetValue, String type) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("type", type)
                .equalTo("target_text", targetValue)
                .in("state", new Object[]{"enabled","paused"})
                .build());
    }
}
