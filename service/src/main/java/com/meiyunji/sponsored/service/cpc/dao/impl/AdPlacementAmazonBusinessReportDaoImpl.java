package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.amazon.advertising.mode.PredicateEnum;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAdPlacementAmazonBusinessReportDao;
import com.meiyunji.sponsored.service.cpc.po.AdPlacementAmazonBusinessReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAllReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.AmazonAdvertisePredicateEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2025-04-16 19:33
 */
@Repository
@Slf4j
public class AdPlacementAmazonBusinessReportDaoImpl extends BaseShardingSphereDaoImpl<AdPlacementAmazonBusinessReport>
        implements IAdPlacementAmazonBusinessReportDao {
    @Override
    public void insertOrUpdateList(Integer puid, List<AdPlacementAmazonBusinessReport> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`type`,`count_date`,`campaign_type`,`placement`,`campaign_id`,`campaign_name`,")
                .append("`impressions`,`clicks`,`currency`,`cost`,")
                .append("`conversions1d`,`conversions7d`,`conversions14d`,`conversions30d`,")
                .append("`conversions1d_same_sku`,`conversions7d_same_sku`,`conversions14d_same_sku`,`conversions30d_same_sku`,")
                .append("`units_ordered1d`,`units_ordered7d`,`units_ordered14d`,`units_ordered30d`,")
                .append("`units_ordered1d_same_sku`,`units_ordered7d_same_sku`,`units_ordered14d_same_sku`,`units_ordered30d_same_sku`,")
                .append("`sales1d`,`sales7d`,`sales14d`,`sales30d`,")
                .append("`sales1d_same_sku`,`sales7d_same_sku`,`sales14d_same_sku`,`sales30d_same_sku`,")
                .append("`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();

        for (AdPlacementAmazonBusinessReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,")
                    .append("now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getType());
            argsList.add(report.getCountDate());
            argsList.add(report.getCampaignType());
            argsList.add(report.getPlacement());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignName() == null ? "" : report.getCampaignName());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            Marketplace marketplace = Marketplace.fromId(report.getMarketplaceId());
            if (marketplace != null) {
                // 设置币种 考虑后续同步doris逻辑
                report.setCurrency(marketplace.getCurrencyCode().name());
            }
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getConversions1d());
            argsList.add(report.getConversions7d());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions30d());
            argsList.add(report.getConversions1dSameSKU());
            argsList.add(report.getConversions7dSameSKU());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getConversions30dSameSKU());
            argsList.add(report.getUnitsOrdered1d());
            argsList.add(report.getUnitsOrdered7d());
            argsList.add(report.getUnitsOrdered14d());
            argsList.add(report.getUnitsOrdered30d());
            argsList.add(report.getUnitsOrdered1dSameSKU());
            argsList.add(report.getUnitsOrdered7dSameSKU());
            argsList.add(report.getUnitsOrdered14dSameSKU());
            argsList.add(report.getUnitsOrdered30dSameSKU());
            argsList.add(report.getSales1d());
            argsList.add(report.getSales7d());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales30d());
            argsList.add(report.getSales1dSameSKU());
            argsList.add(report.getSales7dSameSKU());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getSales30dSameSKU());
        }

        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),");
        sql.append("`placement`=values(placement),`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`conversions1d`=values(conversions1d),`conversions7d`=values(conversions7d),`conversions14d`=values(conversions14d),`conversions30d`=values(conversions30d), ");
        sql.append("`conversions1d_same_sku`=values(conversions1d_same_sku),`conversions7d_same_sku`=values(conversions7d_same_sku),`conversions14d_same_sku`=values(conversions14d_same_sku),`conversions30d_same_sku`=values(conversions30d_same_sku),`units_ordered1d`=values(units_ordered1d),");
        sql.append("`units_ordered7d`=values(units_ordered7d),`units_ordered14d`=values(units_ordered14d),`units_ordered30d`=values(units_ordered30d),`sales1d`=values(sales1d),`sales7d`=values(sales7d),`sales14d`=values(sales14d),`sales30d`=values(sales30d),");
        sql.append("`units_ordered1d_same_sku`=values(units_ordered1d_same_sku),`units_ordered7d_same_sku`=values(units_ordered7d_same_sku),`units_ordered14d_same_sku`=values(units_ordered14d_same_sku),`units_ordered30d_same_sku`=values(units_ordered30d_same_sku),");
        sql.append("`sales1d_same_sku`=values(sales1d_same_sku),`sales7d_same_sku`=values(sales7d_same_sku),`sales14d_same_sku`=values(sales14d_same_sku),`sales30d_same_sku`=values(sales30d_same_sku)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page getPageSpaceList(Integer puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,marketplace_id,type,campaign_id,count_date,campaign_name,campaign_type,sum(`cost`) cost,sum(sales7d) total_sales,")
                .append("sum(sales7d_same_sku) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions7d) order_num, sum(conversions7d_same_sku) ad_order_num,")
                .append("sum(units_ordered7d) sale_num,sum(units_ordered7d_same_sku) ad_sale_num ")
                .append(" FROM ");
        String queryTable = getJdbcHelper().getTable();
        selectSql.append(queryTable);
        StringBuilder countSql = new StringBuilder("select count(*) from ( select campaign_id FROM ");
        countSql.append(queryTable);
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getShopId() != null) {
            whereSql.append(" and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append(" and shop_id in ('").append(StringUtils.join(search.getShopIds(), "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(search.getCampaignIds())) {
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(search.getCampaignIds(), "','")).append("') ");
        }
        whereSql.append(" and count_date>=? and count_date<=? ");
        whereSql.append(" and type = ?");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(), "yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(), "yyyyMMdd"));
        argsList.add(search.getType());
        if (StringUtils.isNotBlank(search.getSearchValue())) {
            search.setSearchValue(SqlStringUtil.dealLikeSql(search.getSearchValue()));
            whereSql.append(" and campaign_name like ?");
            argsList.add(search.getSearchValue() + "%");
        }
        whereSql.append(" group by shop_id,campaign_id , campaign_type");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(" ,count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if (StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())) {
            String orderField = ReportService.getOrderField(search.getOrderField(), true);
            if (StringUtils.isNotBlank(orderField)) {
                selectSql.append(" order by ").append(orderField);
                if ("desc".equals(search.getOrderValue())) {
                    selectSql.append(" desc");
                }
                selectSql.append(" ,campaign_id ");   //增加一个排序字段
                if ("desc".equals(search.getOrderValue())) {
                    selectSql.append(" desc");
                }
            }
        }
        Object[] args = argsList.toArray();

        return this.getPageResultByClass(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdCampaignAllReport.class);
    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdAndPlacement(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, String campaignId, String placement) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,sum(cost) cost,sum(sales7d) total_sales,")
                .append(" sum(sales7d_same_sku) ad_sales,sum(`impressions`) `impressions`,sum(clicks) clicks, sum(conversions7d) order_num, sum(conversions7d_same_sku) ad_order_num,")
                .append(" sum(units_ordered7d) sale_num,sum(units_ordered7d_same_sku) ad_sale_num FROM ");
        selectSql.append(getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and marketplace_id = ? and campaign_id = ? " +
                " and count_date >= ? and count_date <= ? and type = 'sp'");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(campaignId);
        argsList.add(startDate);
        argsList.add(endDate);
        // 如果是企业购类型返回的是三个广告位的汇总
        if (!PlacementPageParam.placementPredicateEnum.siteAmazonBusiness.getContent().equals(placement)) {
            whereSql.append(" and placement = ?");
            argsList.add(placement);
        }
        whereSql.append(" group by count_date ");
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReport.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> getReportByCampaignIdListAndPlacement(Integer puid, Integer shopId, String startDate, String endDate, String marketplaceId, List<String> campaignIdList, String placement) {
        StringBuilder selectSql = new StringBuilder("SELECT shop_id,count_date,marketplace_id,type,campaign_id,campaign_name,cost,sales7d total_sales,")
                .append(" sales7d_same_sku ad_sales,`impressions`,clicks,conversions7d order_num, conversions7d_same_sku ad_order_num,")
                .append(" units_ordered7d sale_num,units_ordered7d_same_sku ad_sale_num FROM ");
        selectSql.append(getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and marketplace_id = ?");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);

        if (StringUtils.isNotBlank(placement)) {
            whereSql.append(" and placement = ? ");
            AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(placement, AmazonAdvertisePredicateEnum.class);
            argsList.add(predicateEnum != null ? predicateEnum.getValue() : "");
        }
        whereSql.append(" and count_date >= ? and count_date <= ? and type = 'sp'");
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            whereSql.append(" and campaign_id in ('").append(StringUtils.join(campaignIdList, "','")).append("') ");
        }
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AmazonAdCampaignAllReport.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdCampaignAllReport> listSumPlacementReports(Integer puid, PlacementPageParam param) {
        // 一级列表类型写死为企业购
        String campaignType = param.isQuerySublist() ? "" : "'" + Constants.PLACEMENT_AMAZON_BUSINESS + "'";
        String sql = "SELECT count_date,campaign_id," + campaignType +" campaign_type,shop_id,type,sum(cost) cost,sum(sales7d) total_sales,"
                + " sum(sales7d_same_sku) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions7d) sale_num, sum(conversions7d_same_sku) ad_sale_num,"
                + " sum(units_ordered7d) order_num,sum(units_ordered7d_same_sku) ad_order_num "
                + " FROM " + getJdbcHelper().getTable() + " where ";

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("type", CampaignTypeEnum.sp.getCampaignType())
                .greaterThanOrEqualTo("count_date", param.getStartDate())
                .lessThanOrEqualTo("count_date", param.getEndDate());
        // 二级列表campaign_id必传
        if (param.isQuerySublist()) {
            builder.equalTo("campaign_id", param.getCampaignId());
        } else {
            if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告组合查询
                builder.inStrList("campaign_id", param.getCampaignIds().toArray(new String[]{}));
            } else {
                if (StringUtils.isNotBlank(param.getCampaignId())) {
                    builder.inStrList("campaign_id", StringUtils.split(param.getCampaignId(), ","));
                }
            }
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
                builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
            }
        }


        if (StringUtils.isNotBlank(param.getPredicate())) {
            AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(param.getPredicate(), AmazonAdvertisePredicateEnum.class);
            builder.equalTo("campaign_type", predicateEnum != null ? predicateEnum.getValue() : "");
        }
        if (param.isQuerySublist()) {
            builder.groupBy("campaign_id,campaign_type");
        } else {
            builder.groupBy("campaign_id");
        }
        ConditionBuilder conditionBuilder = builder.build();
        sql += conditionBuilder.getSql();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, new BeanPropertyRowMapper<>(AmazonAdCampaignAllReport.class), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> listAllPlacementReportsByDate(Integer puid, Integer shopId, String startDate, String endDate, String campaignId, String predicate, PlacementPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT campaign_id,count_date,type,sum(cost) cost,sum(sales7d) total_sales,"
                + " sum(sales7d_same_sku) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions7d) order_num, sum(conversions7d_same_sku) ad_sale_num,"
                + " sum(units_ordered7d) sale_num,sum(units_ordered7d_same_sku) ad_order_num "
                + " FROM ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid= ? ");
        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(" and type = 'sp' ");
        if (StringUtils.isNotBlank(predicate)) {
            whereSql.append(" and campaign_type = ? ");
            AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(predicate, AmazonAdvertisePredicateEnum.class);
            argsList.add(predicateEnum != null ? predicateEnum.getValue() : "");
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIds())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIds(), argsList));
        } else {
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", Arrays.stream(StringUtils.split(param.getCampaignId(), ",")).collect(Collectors.toList()), argsList));
            }
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        whereSql.append("  and count_date >= ? and count_date <= ? group by campaign_id ");
        argsList.add(startDate);
        argsList.add(endDate);

        whereSql.append(subPlacementWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .campaignId(re.getString("campaign_id"))
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            .type(re.getString("type"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AdHomePerformancedto> listAllPlacementReportsByCampaignIdList(Integer puid, Integer shopId, String startDate, String endDate, String predicate, List<String> campaignIdList) {
        if (CollectionUtils.isEmpty(campaignIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT count_date,type,sum(cost) cost,sum(sales7d) total_sales,"
                + " sum(sales7d_same_sku) ad_sales,sum(`impressions`) impressions,sum(`clicks`) clicks,sum(conversions7d) order_num, sum(conversions7d_same_sku) ad_sale_num,"
                + " sum(units_ordered7d) sale_num,sum(units_ordered7d_same_sku) ad_order_num "
                + " FROM ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid= ? and shop_id= ? and type = 'sp' ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignIdList, argsList));

        if (StringUtils.isNotBlank(predicate)) {
            sql.append(" and campaign_type = ? ");
            AmazonAdvertisePredicateEnum predicateEnum = UCommonUtil.getByCode(predicate, AmazonAdvertisePredicateEnum.class);
            argsList.add(predicateEnum != null ? predicateEnum.getValue() : "");
        }

        sql.append("  and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startDate);
        argsList.add(endDate);


        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
                @Override
                public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                    AdHomePerformancedto dto = AdHomePerformancedto.builder()
                            .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                            .adOrderNum(Optional.ofNullable(re.getInt("order_num")).orElse(0))
                            .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                            .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                            .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                            .countDate(re.getString("count_date"))
                            .type(re.getString("type"))
                            /**
                             * TODO 广告报告重构
                             * 本广告产品订单量
                             */
                            .adSaleNum(Optional.ofNullable(re.getInt("ad_sale_num")).orElse(0))
                            //本广告产品销售额
                            .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                            //广告销量
                            .salesNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                            //本广告产品销量
                            .orderNum(Optional.ofNullable(re.getInt("ad_order_num")).orElse(0))
                            .build();
                    return dto;
                }
            }, argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getValidRecordByDate(int puid, int shopId, String marketId, String startDate, List<String> campaignIds) {
        StringBuilder sqlSb = new StringBuilder(" select distinct campaign_id from ");
        sqlSb.append(getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and marketplace_id = ? and count_date >= ? and impressions > 0 ");
        List<Object> argsList = Lists.newArrayList(puid, shopId, marketId, startDate);
        whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sqlSb.append(whereSql).toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    private StringBuilder subPlacementWhereSql(PlacementPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (param.getUseAdvanced()) {
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if (param.getImpressionsMin() != null) {
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if (param.getImpressionsMax() != null) {
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if (param.getClicksMin() != null) {
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if (param.getClicksMax() != null) {
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (param.getClickRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if (param.getClickRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if (param.getCostMin() != null) {
                subWhereSql.append(" and cost >= ? ");
                argsList.add(param.getCostMin());
            }
            if (param.getCostMax() != null) {
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if (param.getCpcMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if (param.getCpcMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量，线上历史遗留问题，广告订单量是order_num
            if (param.getOrderNumMin() != null) {
                subWhereSql.append(" and order_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if (param.getOrderNumMax() != null) {
                subWhereSql.append(" and order_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if (param.getSalesMin() != null) {
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if (param.getSalesMax() != null) {
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if (param.getSalesConversionRateMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if (param.getSalesConversionRateMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if (param.getAcosMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if (param.getAcosMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }

            /****************************高级筛选新增查询指标********************************/
            if (param.getCpaMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/order_num, 0), 4) >= ?");
                argsList.add(param.getCpaMin());
            }
            if (param.getCpaMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(cost/order_num, 0), 4) <= ?");
                argsList.add(param.getCpaMax());
            }
            //本广告产品订单量adSaleNumMin
            if (param.getAdSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) >= ?");
                argsList.add(param.getAdSaleNumMin());
            }
            if (param.getAdSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_sale_num, 0) <= ?");
                argsList.add(param.getAdSaleNumMax());
            }
            //其他广告产品订单量
            if (param.getAdOtherOrderNumMin() != null) {
                subWhereSql.append(" and ifnull(order_num - ad_sale_num, 0) >= ?");
                argsList.add(param.getAdOtherOrderNumMin());
            }
            if (param.getAdOtherOrderNumMax() != null) {
                subWhereSql.append(" and ifnull(order_num - ad_sale_num, 0) <= ?");
                argsList.add(param.getAdOtherOrderNumMax());
            }
            //本广告产品销售额
            if (param.getAdSalesMin() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) >= ?");
                argsList.add(param.getAdSalesMin());
            }
            if (param.getAdSalesMax() != null) {
                subWhereSql.append(" and ifnull(ad_sales, 0) <= ?");
                argsList.add(param.getAdSalesMax());
            }
            //其他产品广告销售额
            if (param.getAdOtherSalesMin() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) >= ?");
                argsList.add(param.getAdOtherSalesMin());
            }
            if (param.getAdOtherSalesMax() != null) {
                subWhereSql.append(" and ifnull(total_sales - ad_sales, 0) <= ?");
                argsList.add(param.getAdOtherSalesMax());
            }
            //广告销量
            if (param.getAdSalesTotalMin() != null) {
                subWhereSql.append(" and ifnull(sale_num, 0) >= ?");
                argsList.add(param.getAdSalesTotalMin());
            }
            if (param.getAdSalesTotalMax() != null) {
                subWhereSql.append(" and ifnull(sale_num, 0) <= ?");
                argsList.add(param.getAdSalesTotalMax());
            }
            //本广告产品销量
            if (param.getAdSelfSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) >= ?");
                argsList.add(param.getAdSelfSaleNumMin());
            }
            if (param.getAdSelfSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(ad_order_num, 0) <= ?");
                argsList.add(param.getAdSelfSaleNumMax());
            }
            //其他产品广告销量
            if (param.getAdOtherSaleNumMin() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_order_num, 0) >= ?");
                argsList.add(param.getAdOtherSaleNumMin());
            }
            if (param.getAdOtherSaleNumMax() != null) {
                subWhereSql.append(" and ifnull(sale_num - ad_order_num, 0) <= ?");
                argsList.add(param.getAdOtherSaleNumMax());
            }
            // 广告笔单价 广告销售额÷广告订单量×100%
            if (param.getAdvertisingUnitPriceMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) >= ?");
                argsList.add(param.getAdvertisingUnitPriceMin());
            }
            if (param.getAdvertisingUnitPriceMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/sale_num, 0), 2) <= ?");
                argsList.add(param.getAdvertisingUnitPriceMax());
            }

        }
        return subWhereSql;
    }
}
