package com.meiyunji.sponsored.service.multiple.portfolio.sevice.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.StopWatchUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDorisDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolioDorisAllReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolioDorisSumReport;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcessNew;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformanceNewDto;
import com.meiyunji.sponsored.service.doris.dao.IDwsSaleProfitShopDayDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdPortfolioDao;
import com.meiyunji.sponsored.service.doris.po.DwsSaleProfitShopDay;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdPortfolio;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.monitor.SaveMonitor;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.multiple.portfolio.dto.PortfolioPageDto;
import com.meiyunji.sponsored.service.multiple.portfolio.sevice.IMultiplePortfolioService;
import com.meiyunji.sponsored.service.multiple.portfolio.vo.MultiplePortfolioPageVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdAnalysisAndCompareVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class MultiplePortfolioServiceImpl implements IMultiplePortfolioService {

    private final IOdsAmazonAdPortfolioDao odsAmazonAdPortfolioDao;
    private final IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;
    private final IScVcShopAuthDao shopAuthDao;
    private final IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;
    private final IAmazonAdPortfolioDao amazonAdPortfolioDao;
    private final AdChartDataProcessNew adChartDataProcess;

    @Override
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.PORTFOLIO, monitorAnalysis = false)
    public Page<MultiplePortfolioPageVo> getAllPortfolioData(PortfolioPageParam param) {
        // 开始记时
        StopWatchUtil.start();
        Page<MultiplePortfolioPageVo> voPage = new Page<>();
        voPage.setPageSize(param.getPageSize());
        voPage.setPageNo(param.getPageNo());
        voPage.setRows(new ArrayList<>());
        // 过滤广告组合
        getQueryIdsByPageFilter(param);
        // 存在且长度为1 切值为1  直接返回
        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            if (param.getPortfolioIdList().size() == 1 && param.getPortfolioIdList().get(0).equals("1")) {
                return voPage;
            }
        }
        Page<OdsAmazonAdPortfolio> page = odsAmazonAdPortfolioDao.listMultiplePortfolioPage(param.getPuid(), param);
        log.info("广告组合 列表查询时间 = {}", StopWatchUtil.get());
        // 结束记时
        dealPageResult(page, voPage, param);
        log.info("广告组合 拼装数据时间 = {}", StopWatchUtil.get());
        // 比对
        if (param.getIsCompare() != null && param.getIsCompare()) {
            dealCompareResult(voPage, param);
            log.info("广告组合 比对数据时间 = {}", StopWatchUtil.get());
        }
        StopWatchUtil.remove();
        return voPage;
    }

    @Override
    public List<MultiplePortfolioPageVo> exportAllPortfolioData(PortfolioPageParam param) {
        StopWatchUtil.start();
        Page<MultiplePortfolioPageVo> voPage = new Page<>();
        param.setPageSize(60000);
        param.setPageNo(1);
        voPage.setPageSize(param.getPageSize());
        voPage.setPageNo(param.getPageNo());
        // 过滤广告组合
        getQueryIdsByPageFilter(param);
        // 存在且长度为1 切值为1  直接返回
        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            if (param.getPortfolioIdList().size() == 1 && param.getPortfolioIdList().get(0).equals("1")) {
                return new ArrayList<>();
            }
        }
        Page<OdsAmazonAdPortfolio> page = odsAmazonAdPortfolioDao.listMultiplePortfolioPage(param.getPuid(), param);
        log.info("广告组合 列表查询时间 = {}", StopWatchUtil.get());
        // 结束记时
        dealPageResult(page, voPage, param);
        log.info("广告组合 拼装数据时间 = {}", StopWatchUtil.get());
        StopWatchUtil.remove();
        return voPage.getRows();
    }

    @Override
    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.PORTFOLIO, monitorAnalysis = false)
    public MultiplePortfolioPageVo getAllPortfolioAggregateData(PortfolioPageParam param) {
        StopWatchUtil.start();
        MultiplePortfolioPageVo multiplePortfolioPageVo = new MultiplePortfolioPageVo();
        List<ShopAuth> shopAuths = shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList());
        // 是否多币种
        boolean isCurrency = MultipleUtils.changeRate(shopAuths);
        // 展示币种
        String currency = MultipleUtils.getCurrency(shopAuths);
        multiplePortfolioPageVo.setCurrency(currency);
        // 过滤参数
        getQueryIdsByPageFilter(param);
        // 存在且长度为1 切值为1  直接返回
        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            if (param.getPortfolioIdList().size() == 1 && param.getPortfolioIdList().get(0).equals("1")) {
                return multiplePortfolioPageVo;
            }
        }
        List<AmazonAdPortfolioDorisSumReport> list = odsAmazonAdPortfolioDao.getAllPortfolioAggregateByDate(param.getPuid(), param, isCurrency);
        BigDecimal shopSaleAmount = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), isCurrency);
        // 汇总数据
        DwsSaleProfitShopDay dwsSaleProfitShopDay = new DwsSaleProfitShopDay();
        dwsSaleProfitShopDay.setSalePrice(shopSaleAmount == null ? BigDecimal.ZERO : shopSaleAmount);
        AdMetricDto adMetricDto = new AdMetricDto();
        buildReport(multiplePortfolioPageVo, buildTotal(list), adMetricDto, dwsSaleProfitShopDay);
        if (param.getIsCompare() != null && param.getIsCompare()) {
            comparePortfolioAggregate(multiplePortfolioPageVo, param, isCurrency);
        }
        boolean isVc = shopAuths.stream().anyMatch(e -> ShopTypeEnum.VC.getCode().equals(e.getType()));
        Integer num = odsAmazonAdPortfolioDao.getPortfolioCampaignIdNum(param.getPuid(), param);
        multiplePortfolioPageVo.setCampaignNumber(num == null ? 0 : num);
        List<AdHomePerformanceNewDto> adHomePerformanceList = list.stream().map(this::build).collect(Collectors.toList());
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, adHomePerformanceList, dwsSaleProfitShopDay.getSalePrice(), isVc);
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, param.getStartDate(), param.getEndDate(), adHomePerformanceList, dwsSaleProfitShopDay.getSalePrice(), isVc);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, adHomePerformanceList, dwsSaleProfitShopDay.getSalePrice(), isVc);
        multiplePortfolioPageVo.setDayPerformanceVos(dayPerformanceVos);
        multiplePortfolioPageVo.setWeekPerformanceVos(weekPerformanceVos);
        multiplePortfolioPageVo.setMonthPerformanceVos(monthPerformanceVos);
        return multiplePortfolioPageVo;
    }

    private AdHomePerformanceNewDto build(AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisAllReport) {
        AdHomePerformanceNewDto adHomePerformancedto = new AdHomePerformanceNewDto();
        adHomePerformancedto.setCountDate(amazonAdPortfolioDorisAllReport.getCountDate().replaceAll("-", ""));
        adHomePerformancedto.setImpressions(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getImpressions()));
        adHomePerformancedto.setClicks(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getClicks()));
        adHomePerformancedto.setAdCost(StringUtil.getSafeValue(amazonAdPortfolioDorisAllReport.getCost()));
        adHomePerformancedto.setAdSale(StringUtil.getSafeValue(amazonAdPortfolioDorisAllReport.getTotalSales()));
        adHomePerformancedto.setAdOrderNum(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getOrderNum()));
        adHomePerformancedto.setSalesNum(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getSaleNum()));
        return adHomePerformancedto;
    }

    private void comparePortfolioAggregate(MultiplePortfolioPageVo multiplePortfolioPageVo, PortfolioPageParam param, boolean isCurrency) {
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        param.setStartDate(param.getCompareStartDate());
        param.setEndDate(param.getCompareEndDate());
        List<AmazonAdPortfolioDorisSumReport> compareList = odsAmazonAdPortfolioDao.getAllPortfolioAggregateByDate(param.getPuid(), param, isCurrency);
        BigDecimal shopSaleCompareAmount = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), isCurrency);
        DwsSaleProfitShopDay dwsSaleProfitShopCompareDay = new DwsSaleProfitShopDay();
        dwsSaleProfitShopCompareDay.setSalePrice(shopSaleCompareAmount == null ? BigDecimal.ZERO : shopSaleCompareAmount);
        buildCompareReport(multiplePortfolioPageVo, buildTotal(compareList), dwsSaleProfitShopCompareDay);
        buildCompareRateReport(multiplePortfolioPageVo);
        param.setStartDate(startDate);
        param.setEndDate(endDate);
    }

    /**
     * 求总数
     */
    private AmazonAdPortfolioDorisSumReport buildTotal(List<AmazonAdPortfolioDorisSumReport> list) {
        AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisSumReport = new AmazonAdPortfolioDorisSumReport();
        if (CollectionUtils.isEmpty(list)) {
            return amazonAdPortfolioDorisSumReport;
        }

        amazonAdPortfolioDorisSumReport.setImpressions(list.stream().map(AmazonAdPortfolioDorisSumReport::getImpressions).filter(Objects::nonNull).mapToLong(Long::longValue).sum());
        amazonAdPortfolioDorisSumReport.setClicks(list.stream().map(AmazonAdPortfolioDorisSumReport::getClicks).filter(Objects::nonNull).mapToLong(Long::longValue).sum());
        amazonAdPortfolioDorisSumReport.setOrderNum(list.stream().map(AmazonAdPortfolioDorisSumReport::getOrderNum).filter(Objects::nonNull).mapToLong(Long::longValue).sum());
        amazonAdPortfolioDorisSumReport.setSaleNum(list.stream().map(AmazonAdPortfolioDorisSumReport::getSaleNum).filter(Objects::nonNull).mapToLong(Long::longValue).sum());
        amazonAdPortfolioDorisSumReport.setCost(list.stream().map(AmazonAdPortfolioDorisSumReport::getCost).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        amazonAdPortfolioDorisSumReport.setTotalSales(list.stream().map(AmazonAdPortfolioDorisSumReport::getTotalSales).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        return amazonAdPortfolioDorisSumReport;
    }

    private void dealCompareResult(Page<MultiplePortfolioPageVo> voPage, PortfolioPageParam param) {
        if (CollectionUtils.isEmpty(voPage.getRows())) {
            return;
        }
        param.setStartDate(DateUtil.getDateSqlFormat(param.getCompareStartDate()));
        param.setEndDate(DateUtil.getDateSqlFormat(param.getCompareEndDate()));
        // 店铺Id 列表
        // 广告组合Id 列表
        List<String> idList = voPage.getRows().stream().map(MultiplePortfolioPageVo::getPortfolioId).collect(Collectors.toList());
        Map<String, AmazonAdPortfolioDorisSumReport> amazonAdPortfolioDorisAllReports = StreamUtil.toMap(amazonAdCampaignAllDorisDao.listByPortfolio(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), idList), AmazonAdPortfolioDorisSumReport::getPortfolioId);
        // 店铺销售额
        Map<Integer, DwsSaleProfitShopDay> saleProfitShopDays = StreamUtil.toMap(dwsSaleProfitShopDayDao.listShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate()), DwsSaleProfitShopDay::getShopId);
        DwsSaleProfitShopDay dwsSaleProfitShopDay = new DwsSaleProfitShopDay();
        AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisAllReport = new AmazonAdPortfolioDorisSumReport();
        for (MultiplePortfolioPageVo multiplePortfolioPageVo : voPage.getRows()) {
            dealComparePageResult(multiplePortfolioPageVo, saleProfitShopDays.getOrDefault(multiplePortfolioPageVo.getShopId(), dwsSaleProfitShopDay), amazonAdPortfolioDorisAllReports.getOrDefault(multiplePortfolioPageVo.getPortfolioId(), amazonAdPortfolioDorisAllReport));
        }
    }

    /**
     * 处理比对值
     */
    private void dealComparePageResult(MultiplePortfolioPageVo multiplePortfolioPageVo, DwsSaleProfitShopDay dwsSaleProfitShopDay, AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisAllReport) {
        buildCompareReport(multiplePortfolioPageVo, amazonAdPortfolioDorisAllReport, dwsSaleProfitShopDay);
        buildCompareRateReport(multiplePortfolioPageVo);
    }

    private void dealPageResult(Page<OdsAmazonAdPortfolio> page, Page<MultiplePortfolioPageVo> voPage, PortfolioPageParam param) {
        if (page == null || CollectionUtils.isEmpty(page.getRows())) {
            return;
        }
        voPage.setTotalPage(page.getTotalPage());
        voPage.setTotalSize(page.getTotalSize());
        // 准备数据
        PortfolioPageDto dto = prepareData(page, param);
        List<MultiplePortfolioPageVo> budgetAnalysisPageList = new ArrayList<>();
        for (OdsAmazonAdPortfolio odsAmazonAdPortfolio : page.getRows()) {
            // 封装参数
            budgetAnalysisPageList.add(build(dto, odsAmazonAdPortfolio.getPortfolioId()));
        }
        voPage.setRows(budgetAnalysisPageList);
    }

    /**
     * 准备数据
     */
    private PortfolioPageDto prepareData(Page<OdsAmazonAdPortfolio> page, PortfolioPageParam param) {
        PortfolioPageDto dto = new PortfolioPageDto();
        // 查看所有店铺 方便后面查看是否多币种
        dto.setShopAuthsMap(StreamUtil.toMap(shopAuthDao.listAllByIds(param.getPuid(), param.getShopIdList()), ShopAuth::getId));
        // 查询百分比的总值(花费 店铺销售额 订单量 销量)
        dto.setAdMetricDto(odsAmazonAdPortfolioDao.getSumAdMetric(param.getPuid(), param));
        // 店铺Id 列表
        List<Integer> shopIdList = page.getRows().stream().map(OdsAmazonAdPortfolio::getShopId).distinct().collect(Collectors.toList());
        // 广告组合Id 列表
        List<String> idList = page.getRows().stream().map(OdsAmazonAdPortfolio::getPortfolioId).collect(Collectors.toList());
        // 设置店铺和Id列表 缩小范围
        param.setPortfolioIdList(idList);
        param.setShopIdList(shopIdList);
        // 广告组合活动数量
        dto.setCountCampaignMap(StreamUtil.toMap(amazonAdCampaignAllDorisDao.countCampaignByPortfolio(param.getPuid(), param.getShopIdList(), idList), AmazonAdPortfolioDorisAllReport::getPortfolioId, AmazonAdPortfolioDorisAllReport::getCampaignCount));
        // 报告数据
        dto.setReportMap(StreamUtil.toMap(amazonAdCampaignAllDorisDao.listByPortfolio(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), idList), AmazonAdPortfolioDorisSumReport::getPortfolioId));
        // 店铺销售额
        dto.setShopSaleMap(StreamUtil.toMap(dwsSaleProfitShopDayDao.listShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate()), DwsSaleProfitShopDay::getShopId));
        // 查询doris的基础数据
        dto.setPortfolioDorisMap(StreamUtil.toMap(odsAmazonAdPortfolioDao.listByPortfolioId(param.getPuid(), param.getShopIdList(), idList), OdsAmazonAdPortfolio::getPortfolioId));
        // 查询mysql的基础数据
        dto.setPortfolioMap(StreamUtil.toMap(amazonAdPortfolioDao.listByShopId(param.getPuid(), param.getShopIdList(), idList), AmazonAdPortfolio::getPortfolioId));
        return dto;
    }

    /**
     * 拼装参数
     */
    private MultiplePortfolioPageVo build(PortfolioPageDto dto, String portfolioId) {
        AmazonAdPortfolio amazonAdPortfolio = dto.getPortfolioMap().get(portfolioId);
        OdsAmazonAdPortfolio odsAmazonAdPortfolio = dto.getPortfolioDorisMap().get(portfolioId);
        ShopAuth shopAuth = dto.getShopAuthsMap().get(amazonAdPortfolio.getShopId());
        // 构建基础信息
        MultiplePortfolioPageVo multiplePortfolioPageVo = new MultiplePortfolioPageVo();
        multiplePortfolioPageVo.setId(amazonAdPortfolio.getId());
        multiplePortfolioPageVo.setShopId(amazonAdPortfolio.getShopId());
        multiplePortfolioPageVo.setPortfolioId(amazonAdPortfolio.getPortfolioId());
        multiplePortfolioPageVo.setIsHidden(odsAmazonAdPortfolio.getIsHidden());
        multiplePortfolioPageVo.setName(odsAmazonAdPortfolio.getName());
        multiplePortfolioPageVo.setMarketplaceId(amazonAdPortfolio.getMarketplaceId());
        multiplePortfolioPageVo.setSiteName(AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getMarketplaceCN());
        multiplePortfolioPageVo.setShopName(shopAuth.getName());
        multiplePortfolioPageVo.setPolicy(amazonAdPortfolio.getPolicy());
        if (amazonAdPortfolio.getAmount() != null) {
            multiplePortfolioPageVo.setAmount(new BigDecimal(String.valueOf(amazonAdPortfolio.getAmount())));
        }
        multiplePortfolioPageVo.setBudgetEndDate(amazonAdPortfolio.getBudgetEndDateStr());
        multiplePortfolioPageVo.setBudgetStartDate(amazonAdPortfolio.getBudgetStartDateStr());
        multiplePortfolioPageVo.setIsAmountPricing(amazonAdPortfolio.getIsAmountPricing());
        multiplePortfolioPageVo.setPricingAmountState(amazonAdPortfolio.getPricingAmountState());
        amazonAdPortfolio.setServingStatus(amazonAdPortfolio.getServingStatus());
        multiplePortfolioPageVo.setServingStatus(amazonAdPortfolio.getServingStatus());
        multiplePortfolioPageVo.setServingStatusDec(amazonAdPortfolio.getServingStatusDec());
        multiplePortfolioPageVo.setServingStatusName(amazonAdPortfolio.getServingStatusName());
        multiplePortfolioPageVo.setCampaignNumber(dto.getCountCampaignMap().get(portfolioId));
        // 构建指标信息
        AmazonAdPortfolioDorisSumReport report = dto.getReportMap().getOrDefault(portfolioId, new AmazonAdPortfolioDorisSumReport());
        DwsSaleProfitShopDay shopSale = dto.getShopSaleMap().getOrDefault(amazonAdPortfolio.getShopId(), new DwsSaleProfitShopDay());
        buildReport(multiplePortfolioPageVo, report, dto.getAdMetricDto(), shopSale);
        return multiplePortfolioPageVo;
    }

    /**
     * 设置报告字段
     */
    private void buildReport(MultiplePortfolioPageVo multiplePortfolioPageVo, AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisAllReport, AdMetricDto adMetricDto, DwsSaleProfitShopDay dwsSaleProfitShopDay) {
        multiplePortfolioPageVo.setImpressions(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getImpressions()));
        multiplePortfolioPageVo.setClicks(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getClicks()));
        multiplePortfolioPageVo.setAdOrderNum(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getOrderNum()));
        multiplePortfolioPageVo.setAdSaleNum(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getSaleNum()));
        multiplePortfolioPageVo.setCtr(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getClicks(), amazonAdPortfolioDorisAllReport.getImpressions(), true));
        multiplePortfolioPageVo.setCvr(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getOrderNum(), amazonAdPortfolioDorisAllReport.getClicks(), true));
        multiplePortfolioPageVo.setAdCost(StringUtil.getSafeValue(amazonAdPortfolioDorisAllReport.getCost()).setScale(2, RoundingMode.HALF_UP));
        multiplePortfolioPageVo.setAdSale(StringUtil.getSafeValue(amazonAdPortfolioDorisAllReport.getTotalSales()).setScale(2, RoundingMode.HALF_UP));
        multiplePortfolioPageVo.setAcos(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), amazonAdPortfolioDorisAllReport.getTotalSales(), true));
        multiplePortfolioPageVo.setRoas(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), amazonAdPortfolioDorisAllReport.getCost(), false));
        multiplePortfolioPageVo.setAdCostPerClick(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), amazonAdPortfolioDorisAllReport.getClicks(), false));
        multiplePortfolioPageVo.setCpa(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), amazonAdPortfolioDorisAllReport.getOrderNum(), false));
        multiplePortfolioPageVo.setAdvertisingUnitPrice(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), amazonAdPortfolioDorisAllReport.getOrderNum(), false));
        // 百分比
        multiplePortfolioPageVo.setAdCostPercentage(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), adMetricDto.getSumCost(), true));
        multiplePortfolioPageVo.setAdSalePercentage(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), adMetricDto.getSumAdSale(), true));
        multiplePortfolioPageVo.setAdOrderNumPercentage(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getOrderNum(), adMetricDto.getSumAdOrderNum() == null ? null : adMetricDto.getSumAdOrderNum().longValue(), true));
        multiplePortfolioPageVo.setAdSaleNumPercentage(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getSaleNum(), adMetricDto.getSumOrderNum() == null ? null : adMetricDto.getSumOrderNum().longValue(), true));
        // 店铺销售百分比
        multiplePortfolioPageVo.setAcots(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), dwsSaleProfitShopDay.getSalePrice(), true));
        multiplePortfolioPageVo.setAsots(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), dwsSaleProfitShopDay.getSalePrice(), true));
    }

    /**
     * 设置环比报告字段 不使用反射
     */
    private void buildCompareReport(MultiplePortfolioPageVo multiplePortfolioPageVo, AmazonAdPortfolioDorisSumReport amazonAdPortfolioDorisAllReport, DwsSaleProfitShopDay dwsSaleProfitShopDay) {
        multiplePortfolioPageVo.setCompareImpressions(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getImpressions()));
        multiplePortfolioPageVo.setCompareClicks(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getClicks()));
        multiplePortfolioPageVo.setCompareAdOrderNum(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getOrderNum()));
        multiplePortfolioPageVo.setCompareAdSaleNum(StringUtil.getSafeLongValue(amazonAdPortfolioDorisAllReport.getSaleNum()));
        multiplePortfolioPageVo.setCompareCtr(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getClicks(), amazonAdPortfolioDorisAllReport.getImpressions(), true));
        multiplePortfolioPageVo.setCompareCvr(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getOrderNum(), amazonAdPortfolioDorisAllReport.getClicks(), true));
        multiplePortfolioPageVo.setCompareAdCost(StringUtil.getSafeValue(amazonAdPortfolioDorisAllReport.getCost()).setScale(2, RoundingMode.HALF_UP));
        multiplePortfolioPageVo.setCompareAdSale(StringUtil.getSafeValue(amazonAdPortfolioDorisAllReport.getTotalSales()).setScale(2, RoundingMode.HALF_UP));
        multiplePortfolioPageVo.setCompareAcos(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), amazonAdPortfolioDorisAllReport.getTotalSales(), true));
        multiplePortfolioPageVo.setCompareRoas(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), amazonAdPortfolioDorisAllReport.getCost(), false));
        multiplePortfolioPageVo.setCompareAdCostPerClick(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), amazonAdPortfolioDorisAllReport.getClicks(), false));
        multiplePortfolioPageVo.setCompareCpa(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), amazonAdPortfolioDorisAllReport.getOrderNum(), false));
        multiplePortfolioPageVo.setCompareAdvertisingUnitPrice(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), amazonAdPortfolioDorisAllReport.getOrderNum(), false));
        // 店铺销售百分比
        multiplePortfolioPageVo.setCompareAcots(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getCost(), dwsSaleProfitShopDay.getSalePrice(), true));
        multiplePortfolioPageVo.setCompareAsots(StringUtil.getSafeDivide(amazonAdPortfolioDorisAllReport.getTotalSales(), dwsSaleProfitShopDay.getSalePrice(), true));
    }

    private void buildCompareRateReport(MultiplePortfolioPageVo multiplePortfolioPageVo) {
        multiplePortfolioPageVo.setCompareRateImpressions(AdAnalysisAndCompareVo.calculateCompareReteWithLine(BigDecimal.valueOf(multiplePortfolioPageVo.getImpressions()), BigDecimal.valueOf(multiplePortfolioPageVo.getCompareImpressions())));
        multiplePortfolioPageVo.setCompareRateClicks(AdAnalysisAndCompareVo.calculateCompareReteWithLine(BigDecimal.valueOf(multiplePortfolioPageVo.getClicks()), BigDecimal.valueOf(multiplePortfolioPageVo.getCompareClicks())));
        multiplePortfolioPageVo.setCompareRateAdOrderNum(AdAnalysisAndCompareVo.calculateCompareReteWithLine(BigDecimal.valueOf(multiplePortfolioPageVo.getAdOrderNum()), BigDecimal.valueOf(multiplePortfolioPageVo.getCompareAdOrderNum())));
        multiplePortfolioPageVo.setCompareRateAdSaleNum(AdAnalysisAndCompareVo.calculateCompareReteWithLine(BigDecimal.valueOf(multiplePortfolioPageVo.getAdSaleNum()), BigDecimal.valueOf(multiplePortfolioPageVo.getCompareAdSaleNum())));

        multiplePortfolioPageVo.setCompareRateCtr(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getCtr(), multiplePortfolioPageVo.getCompareCtr()));
        multiplePortfolioPageVo.setCompareRateCvr(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getCvr(), multiplePortfolioPageVo.getCompareCvr()));
        multiplePortfolioPageVo.setCompareRateAdCost(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAdCost(), multiplePortfolioPageVo.getCompareAdCost()));
        multiplePortfolioPageVo.setCompareRateAdSale(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAdSale(), multiplePortfolioPageVo.getCompareAdSale()));
        multiplePortfolioPageVo.setCompareRateAcos(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAcos(), multiplePortfolioPageVo.getCompareAcos()));
        multiplePortfolioPageVo.setCompareRateRoas(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getRoas(), multiplePortfolioPageVo.getCompareRoas()));
        multiplePortfolioPageVo.setCompareRateAdCostPerClick(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAdCostPerClick(), multiplePortfolioPageVo.getCompareAdCostPerClick()));
        multiplePortfolioPageVo.setCompareRateCpa(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getCpa(), multiplePortfolioPageVo.getCompareCpa()));
        multiplePortfolioPageVo.setCompareRateAdvertisingUnitPrice(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAdvertisingUnitPrice(), multiplePortfolioPageVo.getCompareAdvertisingUnitPrice()));
        // 店铺销售百分比Rate
        multiplePortfolioPageVo.setCompareRateAcots(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAcots(), multiplePortfolioPageVo.getCompareAcots()));
        multiplePortfolioPageVo.setCompareRateAsots(AdAnalysisAndCompareVo.calculateCompareReteWithLine(multiplePortfolioPageVo.getAsots(), multiplePortfolioPageVo.getCompareAsots()));
    }

    private void getQueryIdsByPageFilter(PortfolioPageParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listValidShopByIds(param.getPuid(), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            throw new SponsoredBizException("店铺未授权");
        }
        // filter 有效shopIds
        List<Integer> shopIds = StreamUtil.toListDistinct(shopAuths, ShopAuth::getId);
        param.setShopIdList(shopIds);
        // isCompare字段处理 #issue 勾选对比 未传比对时间
        param.setIsCompare(param.getIsCompare() && StringUtil.isNotEmpty(param.getCompareStartDate())  && StringUtil.isNotEmpty( param.getCompareEndDate()));
        List<String> portfolioIdList = CollectionUtils.isNotEmpty(param.getPortfolioIdList()) ? new ArrayList<>(param.getPortfolioIdList()) : new ArrayList<>();
        if(CollectionUtil.isNotEmpty(portfolioIdList) && !portfolioIdList.contains("-1")){
            // 兼容前端异常场景 传的广告组合在店铺下不存在时清空广告组合筛选
            Integer count = odsAmazonAdPortfolioDao.countPortfolioList(param.getPuid(), param.getShopIdList(), portfolioIdList);
            if(count == 0){
                portfolioIdList = new ArrayList<>();
            }
        }
        // 虽然查询条件会查 但是可以快速过滤出组合Id
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            List<String> idList = odsAmazonAdPortfolioDao.listPortfolioIdByName(param.getPuid(), param);
            if (CollectionUtils.isEmpty(idList)) {
                // 返回空
                param.setPortfolioIdList(Lists.newArrayList("1"));
                return;
            }
            if (CollectionUtils.isEmpty(portfolioIdList)) {
                param.setPortfolioIdList(idList);
            } else {
                portfolioIdList.retainAll(idList);
                if(CollectionUtils.isEmpty(portfolioIdList)){
                    // 返回空
                    param.setPortfolioIdList(Lists.newArrayList("1"));
                }else{
                    param.setPortfolioIdList(portfolioIdList);
                }
            }
            return;
        }
        param.setPortfolioIdList(portfolioIdList);
    }
}
