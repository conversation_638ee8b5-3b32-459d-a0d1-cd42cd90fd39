package com.meiyunji.sponsored.service.multiPlatform.tiktok.dao;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.service.autoRule.po.GrabRankingSnapshot;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokStoreToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  11:32
 */
@Slf4j
@Component
public class TikTokStoreTokenDao extends AdBaseDaoImpl<TikTokStoreToken> {

    public int insertOrUpdate(int puid, TikTokStoreToken tikTokStoreToken) {
        StringBuilder sql = new StringBuilder("insert into t_tiktok_store_token(puid, shop_id, access_token, create_time, update_time) values");
        List<Object> argsList = Lists.newArrayList();
        sql.append("( ?,?,?,now(),now())");
        argsList.add(puid);
        argsList.add(tikTokStoreToken.getShopId());
        argsList.add(tikTokStoreToken.getAccessToken());
        sql.append(" on duplicate key update access_token=values(access_token) ");
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    public String getAccessTokenByShopId(int puid, int shopId) {
        String sql = "select access_token from t_tiktok_store_token where puid=? and shop_id=? limit 1";
        return getJdbcTemplate().queryForObject(sql, new Object[]{puid, shopId}, String.class);
    }

}
