package com.meiyunji.sponsored.service.reportDiffMonitor.service;

import com.alibaba.fastjson.JSON;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.syncReport.AdTypeEnum;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.bo.ReportMonitorBo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.reportDiffMonitor.DiffMonitorConstants;
import com.meiyunji.sponsored.service.reportDiffMonitor.dto.ShopDTO;
import com.meiyunji.sponsored.service.reportDiffMonitor.enums.LevelTypeEnum;
import com.meiyunji.sponsored.service.reportDiffMonitor.enums.TimeLevel;
import com.meiyunji.sponsored.service.reportDiffMonitor.repository.ReportDateDiffMonitorDao;
import com.meiyunji.sponsored.service.reportDiffMonitor.repository.po.ReportDateDiffMonitor;
import com.meiyunji.sponsored.service.util.WxNotificationUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.common.util.MathUtil.DEF_DIV_DIFF;
import static com.meiyunji.sponsored.service.reportDiffMonitor.service.ReportLevelDiffMonitorService.distinctByKey;

/**
 * 报告数据日期差异监控service
 *
 * @Author: hejh
 * @Date: 2024/5/30 11:14
 */
@Slf4j
@Service
public class ReportDateDiffMonitorService {
    @Autowired
    private ReportDateDiffMonitorDao reportDateDiffMonitorDao;
    @Autowired
    private IShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdCampaignAllDao campaignAllDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao campaignAllReportDao;
    @Autowired
    private IAmazonAdGroupReportDao groupReportDao;
    @Autowired
    private IAmazonAdKeywordReportDao keywordReportDao;
    @Autowired
    private ICpcTargetingReportDao targetingReportDao;
    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private DynamicRefreshConfiguration configuration;

    /**
     * 随机生成实验店铺数据
     *
     * @return
     */
    public List<ShopDTO> getRandomShopDTO() {
        List<Integer> minMaxId = shopAuthDao.queryRandomSequentList();
        List<ShopDTO> allShopDTOList = new ArrayList<>();
        int Loop = 0;
        while (Loop < 10) {
            Loop++;
            List<Integer> ids = new ArrayList<>();
            for (int i = 0; i < configuration.getRandomListSum(); i++) {
                int randomNum = new Random().nextInt(minMaxId.get(1) - minMaxId.get(0) + 1) + minMaxId.get(0);
                ids.add(randomNum);
            }
            List<ShopAuth> shopList = new ArrayList<>(shopAuthDao.unionQueryShopsByIdsWithConditions(ids));

            List<ShopDTO> filteredShopList = shopList.stream()
                .filter(Objects::nonNull) // 过滤掉 null 值
                .filter(distinctByKey(ShopAuth::getId)) // 过滤掉指定属性重复的 ShopAuth 实例
                .filter(shopAuth -> campaignAllDao.getValidShopCountByCondition(shopAuth.getPuid(), shopAuth.getId()) > configuration.getMinCampaignCount())
                .map(shopAuth -> {
                    ShopDTO shopDTO = new ShopDTO();
                    shopDTO.setPuid(shopAuth.getPuid());
                    shopDTO.setShopId(shopAuth.getId());
                    return shopDTO;
                })
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(filteredShopList)) {
                continue;
            }
            allShopDTOList.addAll(filteredShopList);
            if (allShopDTOList.size() >= 10) {
                break;
            }
        }
        List<ShopDTO> collect = allShopDTOList.stream().filter(distinctByKey(ShopDTO::getShopId)).collect(Collectors.toList());
        return collect;
    }

    public void createFirstReportDateDiffMonitor() {
        //1、随机选取样本数据
        List<ShopDTO> filteredShopList = getRandomShopDTO();
        if (CollectionUtils.isEmpty(filteredShopList)) {
            log.info("Random shopDTOS data is empty");
            return;
        }
        //2、查询样本数据各指标，根据广告类型分类，分别生成指标监控
        List<ReportDateDiffMonitor> diffMonitorList = new ArrayList<>();
        List<ReportDateDiffMonitor> spReportLevelDiffMonitors = generateMonitorByAdType(AdTypeEnum.sp, filteredShopList);
        List<ReportDateDiffMonitor> sdReportLevelDiffMonitors = generateMonitorByAdType(AdTypeEnum.sd, filteredShopList);
        diffMonitorList.addAll(spReportLevelDiffMonitors);
        diffMonitorList.addAll(sdReportLevelDiffMonitors);
        //3、落库
        reportDateDiffMonitorDao.saveList(diffMonitorList);
    }

    public void dealEndReportDateDiffMonitor() {
        //1、查询到达下一次指标采集时间记录
        List<ReportDateDiffMonitor> reportDateDiffMonitors = reportDateDiffMonitorDao.queryFinalRecord(configuration.getToBeDealRecordCount());
        for (ReportDateDiffMonitor monitor : reportDateDiffMonitors) {
            List<ShopDTO> shopDTOS = JSON.parseArray(monitor.getExperimentJson(), ShopDTO.class);
            List<ReportMonitorBo> startMetricList = JSON.parseArray(monitor.getStartMetricJson(), ReportMonitorBo.class);
            //2、查询最新数据并计算差异值
            List<ReportMonitorBo> endMetricList = querySingleLevelTypeMonitor(shopDTOS, AdTypeEnum.valueOf(monitor.getAdType()), monitor.getCountStartDate(), monitor.getCountEndDate(), LevelTypeEnum.valueOf(monitor.getLevelType()));
            BigDecimal diff = countDiff(startMetricList, endMetricList);
            monitor.setEndMetricJson(JSON.toJSONString(endMetricList));
            monitor.setDone(1);
            monitor.setDiff(diff);
            monitor.setDiffPercent(diff.multiply(new BigDecimal(100)).toPlainString() + "%");
            //3、更新
            reportDateDiffMonitorDao.updateById(monitor);
            if (diff.compareTo(BigDecimal.ZERO) == 0) {
                continue;
            }
            String msg = "店铺报告数据日期差异监控\n" + String.format("id：%s, 广告类型：%s, 广告层级类型：%s, 报告时间: %s, countDate: %s-%s, 统计时间间隔: %s分钟, 差异值: %s",
                monitor.getId(), monitor.getAdType(), LevelTypeEnum.valueOf(monitor.getLevelType()).getDesc(), monitor.getReportTime(),
                monitor.getCountStartDate(), monitor.getCountEndDate(), monitor.getIntervalType(), monitor.getDiffPercent());
            WxNotificationUtil.sendBigContent(DiffMonitorConstants.WX_DIFF_WARN_URL, msg);
        }
    }

    /**
     * 计算差异值
     *
     * @param startMetricList
     * @param endMetricList
     * @return
     */
    private BigDecimal countDiff(List<ReportMonitorBo> startMetricList, List<ReportMonitorBo> endMetricList) {
        Map<String, ReportMonitorBo> shopIdMonitorBoMap = endMetricList.stream()
            .collect(Collectors.toMap(this::getUniqueKey, Function.identity(), (bo1, bo2) -> {
                BigDecimal totalCost1 = Objects.isNull(bo1.getTotalCost()) ? BigDecimal.ZERO : bo1.getTotalCost();
                BigDecimal totalCost2 = Objects.isNull(bo2.getTotalCost()) ? BigDecimal.ZERO : bo2.getTotalCost();
                BigDecimal totalCost = totalCost1.add(totalCost2);
                long totalClicks = bo1.getTotalClicks() + bo2.getTotalClicks();
                long totalImpressions = bo1.getTotalImpressions() + bo2.getTotalImpressions();
                ReportMonitorBo bo = new ReportMonitorBo();
                bo.setTotalCost(totalCost);
                bo.setTotalClicks(totalClicks);
                bo.setTotalImpressions(totalImpressions);
                return bo;
            }));
        List<BigDecimal> diffList = new ArrayList<>();
        for (ReportMonitorBo bo : startMetricList) {
            if (bo.getShopId() == null || !shopIdMonitorBoMap.containsKey(getUniqueKey(bo))) {
                continue;
            }
            ReportMonitorBo compareBo = shopIdMonitorBoMap.get(getUniqueKey(bo));
            BigDecimal diff1 = BigDecimal.ZERO;
            if (Objects.nonNull(bo.getTotalCost()) && Objects.nonNull(compareBo.getTotalCost()) && bo.getTotalCost().compareTo(BigDecimal.ZERO) != 0) {
                diff1 = MathUtil.divide(compareBo.getTotalCost().subtract(bo.getTotalCost()), bo.getTotalCost(), DEF_DIV_DIFF);
            }
            BigDecimal diff2 = BigDecimal.ZERO;
            if (bo.getTotalClicks() != 0) {
                diff2 = MathUtil.divide(new BigDecimal(compareBo.getTotalClicks()).subtract(new BigDecimal(bo.getTotalClicks())), new BigDecimal(bo.getTotalClicks()), DEF_DIV_DIFF);
            }
            BigDecimal diff3 = BigDecimal.ZERO;
            if (bo.getTotalImpressions() != 0) {
                diff3 = MathUtil.divide(new BigDecimal(compareBo.getTotalImpressions()).subtract(new BigDecimal(bo.getTotalImpressions())), new BigDecimal(bo.getTotalImpressions()), DEF_DIV_DIFF);
            }
            BigDecimal avg = MathUtil.divide(diff1.add(diff2).add(diff3), new BigDecimal(3), DEF_DIV_DIFF);
            diffList.add(avg);
        }
        return MathUtil.avg(diffList);
    }

    private String getUniqueKey(ReportMonitorBo bo) {
        if (StringUtils.isBlank(bo.getType())) {
            return String.valueOf(bo.getShopId());
        }
        return bo.getShopId() + bo.getType();
    }

    /**
     * 根据广告类型分类，分别生成指标监控
     *
     * @param shopDTOS shopDTOS
     */
    private List<ReportDateDiffMonitor> generateMonitorByAdType(AdTypeEnum adTypeEnum, List<ShopDTO> shopDTOS) {
        ArrayList<ReportDateDiffMonitor> result = new ArrayList<>();
        List<LevelTypeEnum> levelTypeEnums = AdTypeEnum.sp.equals(adTypeEnum) ? LevelTypeEnum.spLevelTypeEnums : LevelTypeEnum.sdLevelTypeEnums;
        //分层：广告类型 -》层级类型 -》 报告时间，共生成：广告类型 * 层级类型 * 报告时间 条记录
        for (LevelTypeEnum levelTypeEnum : levelTypeEnums) {
            Map<String, Integer> spTimeLevel = AdTypeEnum.sp.equals(adTypeEnum) ? levelTypeEnum.getSpTimeLevel() : levelTypeEnum.getSdTimeLevel();
            spTimeLevel.forEach((timeGapStr, intervalMinutes) -> {
                List<Integer> timeGap = TimeLevel.splitTimeGap(timeGapStr);
                //统计的报告数据范围
                String startCountDate = LocalDate.now().minusDays(1).minusDays(timeGap.get(1)).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                String endCountDate = LocalDate.now().minusDays(1).minusDays(timeGap.get(0)).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
                List<ReportMonitorBo> reportMonitorBos = querySingleLevelTypeMonitor(shopDTOS, adTypeEnum, startCountDate, endCountDate, levelTypeEnum);
                ReportDateDiffMonitor dateDiffMonitor = generateDiffRecord(adTypeEnum, levelTypeEnum, timeGapStr, intervalMinutes, shopDTOS, reportMonitorBos, startCountDate, endCountDate, LocalDateTime.now().plusMinutes(intervalMinutes));
                result.add(dateDiffMonitor);
            });
        }
        return result;
    }

    private List<ReportMonitorBo> querySingleLevelTypeMonitor(List<ShopDTO> shopDTOS, AdTypeEnum adTypeEnum, String startCountDate, String endCountDate, LevelTypeEnum levelTypeEnum) {
        List<ReportMonitorBo> reportMonitorBos = new ArrayList<>();
        switch (levelTypeEnum) {
            case CAMPAIGN: {
                List<ReportMonitorBo> reportMonitorBoList = campaignAllReportDao.getReportLevelMonitorBoList(shopDTOS, adTypeEnum, startCountDate, endCountDate);
                if (CollectionUtils.isNotEmpty(reportMonitorBoList)) {
                    reportMonitorBos.addAll(reportMonitorBoList);
                }
            } break;
            case GROUP: {
                List<ReportMonitorBo> reportMonitorBoList = groupReportDao.getReportLevelMonitorBoList(shopDTOS, adTypeEnum, startCountDate, endCountDate);
                if (CollectionUtils.isNotEmpty(reportMonitorBoList)) {
                    reportMonitorBos.addAll(reportMonitorBoList);
                }
            } break;
            case TARGETING: {
                List<ReportMonitorBo> keywordBoList = new ArrayList<>();
                if (!AdTypeEnum.sd.equals(adTypeEnum)) {
                    keywordBoList = keywordReportDao.getReportLevelMonitorBoList(shopDTOS, adTypeEnum, startCountDate, endCountDate);
                }
                List<ReportMonitorBo> targetingBoList = targetingReportDao.getReportLevelMonitorBoList(shopDTOS, adTypeEnum, startCountDate, endCountDate);
                if (CollectionUtils.isNotEmpty(keywordBoList)) {
                    reportMonitorBos.addAll(keywordBoList);
                }
                if (CollectionUtils.isNotEmpty(targetingBoList)) {
                    reportMonitorBos.addAll(targetingBoList);
                }
            } break;
            case PRODUCT: {
                List<ReportMonitorBo> reportMonitorBoList = amazonAdProductReportDao.getReportLevelMonitorBoList(shopDTOS, adTypeEnum, startCountDate, endCountDate);
                if (CollectionUtils.isNotEmpty(reportMonitorBoList)) {
                    reportMonitorBos.addAll(reportMonitorBoList);
                }
            } break;
        }
        return reportMonitorBos;
    }

    /**
     * 生成监控差异记录
     *
     * @param adType
     * @param levelTypeEnum
     * @param reportTime
     * @param intervalType
     * @param shopDTOS
     * @param monitorBoList
     * @param countStartDate
     * @param countEndDate
     * @param endMetricTime
     * @return
     */
    private ReportDateDiffMonitor generateDiffRecord(AdTypeEnum adType, LevelTypeEnum levelTypeEnum, String reportTime, Integer intervalType,
        List<ShopDTO> shopDTOS, List<ReportMonitorBo> monitorBoList, String countStartDate, String countEndDate, LocalDateTime endMetricTime) {

        ReportDateDiffMonitor dateDiffMonitor = new ReportDateDiffMonitor();
        dateDiffMonitor.setAdType(adType.name());
        dateDiffMonitor.setLevelType(levelTypeEnum.getValue());
        dateDiffMonitor.setReportTime(reportTime);
        dateDiffMonitor.setIntervalType(intervalType);
        dateDiffMonitor.setExperimentJson(JSON.toJSONString(shopDTOS));
        dateDiffMonitor.setStartMetricJson(JSON.toJSONString(monitorBoList));
        dateDiffMonitor.setCountStartDate(countStartDate);
        dateDiffMonitor.setCountEndDate(countEndDate);
        dateDiffMonitor.setEndMetricTime(endMetricTime);
        return dateDiffMonitor;
    }
}
