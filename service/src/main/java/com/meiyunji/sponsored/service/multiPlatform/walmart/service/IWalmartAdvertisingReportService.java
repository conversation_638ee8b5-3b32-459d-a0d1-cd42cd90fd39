package com.meiyunji.sponsored.service.multiPlatform.walmart.service;



import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingGroupReportPage;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;

import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description
 */
public interface IWalmartAdvertisingReportService {

    void syncReport(Integer puid, Integer shopId, String reportDateManual, String startDateManual, String endDateManual);

    void snapshotExecute(WalmartAdvertisingSnapshot snapshot, String detailsStr, String jobStatus);

    //店铺排行榜
    List<WalmartAdvertisingGroupReportPage> getPageListShopReportTop(int puid, Long shopId, Map<String, Object> queryParams);

    //广告排行榜
    Page getPageListCampaignReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    //广告组排行榜
    Page getPageListGroupReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    //广告产品排行榜
    Page getPageListItemReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    //广告关键词排行榜
    Page getPageListKeywordReportTop(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    List<Map<String,Object>> getIndicatorList(int puid, Map<String, Object> queryParams);

    List<Map<String,Object>> getIndicatorDateList(int puid, Map<String, Object> queryParams);

    Page getIndicatorDateDetailsPage(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    WalmartAdvertisingGroupReportPage getSumReportDate(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    Map<String,Object> getAdvertisingTransition(int puid, Map<String, Object> queryParams);

    Map<String,Object> getTargetingRatio(int puid, Map<String, Object> queryParams);

}
