package com.meiyunji.sponsored.service.multiPlatform.tiktok.dao;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.AdBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.enums.TikTokAdvertiserAccountAuthStatusEnum;
import com.meiyunji.sponsored.service.multiPlatform.tiktok.model.po.TikTokAdvertiserAccount;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2025-05-19  11:21
 */
@Slf4j
@Component
public class TikTokAdvertiserAccountDao extends AdBaseDaoImpl<TikTokAdvertiserAccount> {

    public TikTokAdvertiserAccount getByPuidAndAdvertiserId(Integer puid, String advertiserId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("advertiser_id", advertiserId);
        return getByCondition(builder.build());
    }

    public List<TikTokAdvertiserAccount> getByPuidAndAdvertiserIds(Integer puid, List<String> advertiserIds) {
        if (CollectionUtils.isEmpty(advertiserIds)) {
            return Collections.emptyList();
        }
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .in("advertiser_id", advertiserIds.toArray());
        return listByCondition(builder.build());
    }

    public List<TikTokAdvertiserAccount> listAuthAccountByPuidAndShopIds(Integer puid, List<Integer> shopIds) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("SELECT puid, advertiser_id, any_value(name) name, any_value(currency) currency, max(auth_status) auth_status " +
                " FROM `t_tiktok_advertiser_account` WHERE puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sql.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        sql.append(" group by puid, advertiser_id");
        sql.append(" order by max(create_time) desc ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), getRowMapper());
    }

    public List<TikTokAdvertiserAccount> listAllAccountByPuidAndShopIds(Integer puid, List<Integer> shopIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .in("shop_id", shopIds.toArray());
        return listByCondition(builder.build());
    }

    public List<TikTokAdvertiserAccount> listAllAccountByPuidAndShopId(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .equalTo("shop_id", shopId);
        return listByCondition(builder.build());
    }

    public List<TikTokAdvertiserAccount> listAuthAccountByAdvertiserId(Integer puid, List<Integer> shopIds, List<String> advertiserId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .in("shop_id", shopIds.toArray())
                .in("advertiser_id", advertiserId.toArray())
                .equalTo("auth_status", TikTokAdvertiserAccountAuthStatusEnum.AD_AUTH.getStatus());
        return listByCondition(builder.build());
    }

    public Integer batchInsertOrUpdate(Integer puid, List<TikTokAdvertiserAccount> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_tiktok_advertiser_account`(`puid`,`shop_id`,`owner_bc_id`,`advertiser_id`,")
                .append("`name`,`auth_status`,`currency`,`timezone`,`display_timezone`,`create_time`,`update_time`) values");
        List<Object> argsList = Lists.newArrayList();
        for (TikTokAdvertiserAccount tikTokAdvertiserAccount : list) {
            sql.append("(?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(tikTokAdvertiserAccount.getShopId());
            argsList.add(tikTokAdvertiserAccount.getOwnerBcId());
            argsList.add(tikTokAdvertiserAccount.getAdvertiserId());
            argsList.add(tikTokAdvertiserAccount.getName());
            argsList.add(tikTokAdvertiserAccount.getAuthStatus());
            argsList.add(tikTokAdvertiserAccount.getCurrency());
            argsList.add(tikTokAdvertiserAccount.getTimezone());
            argsList.add(tikTokAdvertiserAccount.getDisplayTimezone());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update name=values(name),auth_status=values(auth_status),currency=values(currency),timezone=values(timezone),display_timezone=values(display_timezone) ");
        return getJdbcTemplate().update(sql.toString(), argsList.toArray());
    }

    public void updateAuthStatus(Integer puid, Integer shopId, String advertiserId, Integer authStatus) {
        String sql = "UPDATE `t_tiktok_advertiser_account` SET `auth_status` = ?,`update_time` = now() WHERE `puid` = ? AND `shop_id` = ? AND `advertiser_id` = ? ";
        List<Object> argsList = Lists.newArrayList();
        argsList.add(authStatus);
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(advertiserId);
        getJdbcTemplate().update(sql, argsList.toArray());
    }


    public void batchUpdateAuthStatus(Integer puid, List<Long> ids, Integer authStatus) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<Object[]> argList = new ArrayList<>(ids.size());
        List<Object> arg;
        for (Long id : ids) {
            arg = new ArrayList<>();
            arg.add(authStatus);
            arg.add(id);
            arg.add(puid);
            argList.add(arg.toArray());
        }
        String sql = "update t_tiktok_advertiser_account set auth_status = ?  where id = ? and puid = ? ";
        getJdbcTemplate().batchUpdate(sql, argList);
    }
}
