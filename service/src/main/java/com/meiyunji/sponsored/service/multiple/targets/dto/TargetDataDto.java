package com.meiyunji.sponsored.service.multiple.targets.dto;

import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdOperationLogBO;
import com.meiyunji.sponsored.service.cpc.po.AdTag;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.vo.AdMarkupTagVo;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.doris.po.OdsProduct;
import com.meiyunji.sponsored.service.doris.po.OdsWeekSearchTermsAnalysis;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyStatus;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 广告关联-投放层级数据准备dto
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Data
public class TargetDataDto {

    /**
     * 店铺信息map
     */
    private Map<Integer, ShopAuth> shopAuthMap = new HashMap<>();

    /**
     * 汇总数据
     */
    private AdMetricDto adMetricDto;

    /**
     * 投放id-报告数据Map
     */
    private Map<String, TargetReportDoris> reportMap = new HashMap<>();

    /**
     * 投放id-对比报告数据Map
     */
    private Map<String, TargetReportDoris> compareReportMap = new HashMap<>();

    /**
     * 店铺id-店铺销售额Map
     */
    private Map<Integer, BigDecimal> shopSalesMap = new HashMap<>();

    /**
     * 店铺id-环比店铺销售额Map
     */
    private Map<Integer, BigDecimal> compareShopSalesMap = new HashMap<>();

    /**
     * 投放id-投放基础信息map
     */
    private Map<String, TargetInfo> targetMap = new HashMap<>();

    /**
     * 投放id-投放扩展信息map
     */
    private Map<String, TargetExtendInfo> targetExtendInfoMap = new HashMap<>();

    /**
     * 投放id-投放doris基础信息map
     */
    private Map<String, TargetInfo> targetDorisMap = new HashMap<>();

    /**
     * 活动id-活动基础信息map
     */
    private Map<String, AmazonAdCampaignAll> campaignMap = new HashMap<>();

    /**
     * 广告组合id-广告组合信息Map
     */
    private Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();

    /**
     * 广告组id-广告组信息Map
     */
    private Map<String, GroupInfo> groupInfoMap = new HashMap<>();

    /**
     * 投放id-自动化规则信息
     */
    private Map<String, List<AdvertiseAutoRuleStatus>> autoRuleMap = new HashMap<>();

    /**
     * 广告组id-组受控自动化规则信息
     */
    private Map<String, List<AdvertiseAutoRuleStatus>> autoRuleGroupMap = new HashMap<>();

    /**
     * 投放id-分时策略信息
     */
    private  Map<String, List<AdvertiseStrategyStatus>> strategyStatusMap = new HashMap<>();

    /**
     * 标签id-标签信息
     */
    private Map<Long, AdTag> adTagMap = new HashMap<>();

    /**
     * 投放id-投放标签关联信息
     */
    private Map<String, AdMarkupTagVo> adMarkupTagVoMap = new HashMap<>();

    /**
     * 翻译前词-翻译后词
     */
    private Map<String, String> wordTranslateMap = new HashMap<>();

    /**
     * 翻译前词-翻译后词
     */
    private Map<String, OdsWeekSearchTermsAnalysis> abaRankMap = new HashMap<>();

    /**
     * 投放id-竞价日志
     */
    private Map<String, AmazonAdOperationLogBO> amazonAdOperationLogMap = new HashMap<>();

    /**
     * asin-asin信息map
     */
    private Map<String, AsinImage> asinMap = new HashMap<>();

    /**
     * sku-在线产品信息
     */
    private Map<String, OdsProduct> asinImageMap = new HashMap<>();

    /**
     * groupId-广告产品信息
     */
    private Map<String, List<AmazonAdProduct>> groupProductMap = new HashMap<>();

}
