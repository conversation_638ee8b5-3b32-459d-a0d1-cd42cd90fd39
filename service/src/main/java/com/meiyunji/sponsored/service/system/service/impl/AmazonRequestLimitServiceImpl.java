package com.meiyunji.sponsored.service.system.service.impl;

import com.meiyunji.sponsored.common.support.RedisConstant;
import com.meiyunji.sponsored.common.support.RedisService;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.system.po.AmazonRequestLimit;
import com.meiyunji.sponsored.service.system.service.IAmazonRequestLimitService;
import com.meiyunji.sponsored.service.util.AmazonRequestLimitType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName AmazonRequestLimitServiceImpl
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/5/12 9:54
 **/
@Service
public class AmazonRequestLimitServiceImpl implements IAmazonRequestLimitService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private RedisService redisService;

    @Override
    public AmazonRequestLimit getRequestLimit(Integer shopId, AmazonRequestLimitType requestLimitType) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        AmazonRequestLimit limit = new AmazonRequestLimit();
        limit.setType(requestLimitType);
        limit.setSellerId(shopAuth.getSellingPartnerId());
        limit.setRegion(shopAuth.getRegion());
        Long nextRequestMillis = redisService.getLong(String.format(RedisConstant.MWS_REQUEST_LIMIT,limit.getSellerId(),limit.getRegion(),requestLimitType));
        Long curMills = System.currentTimeMillis();
        if(nextRequestMillis == null || nextRequestMillis < curMills-limit.getType().getRestoreInterval()*limit.getType().getQuota()){
            nextRequestMillis = curMills - limit.getType().getRestoreInterval() * limit.getType().getQuota();
            redisService.set(String.format(RedisConstant.MWS_REQUEST_LIMIT,limit.getSellerId(),limit.getRegion(),requestLimitType), nextRequestMillis);
        }
        limit.setNextRequestMillis(nextRequestMillis);
        return limit;
    }


    @Override
    public void increase(AmazonRequestLimit requestLimit) {
        requestLimit.setNextRequestMillis(redisService.incr(String.format(RedisConstant.MWS_REQUEST_LIMIT,requestLimit.getSellerId(),requestLimit.getRegion(),requestLimit.getType()), requestLimit.getType().getRestoreInterval()));
    }

    @Override
    public AmazonRequestLimit sleep(Integer shopId, AmazonRequestLimitType amazonRequestLimitType){
        AmazonRequestLimit requestLimit = getRequestLimit(shopId, amazonRequestLimitType);
        Long currMillis = System.currentTimeMillis();
        while (requestLimit.getNextRequestMillis() > currMillis) {
            try {
                Thread.sleep(requestLimit.getNextRequestMillis() - currMillis);
                requestLimit = getRequestLimit(shopId, amazonRequestLimitType);
                currMillis = System.currentTimeMillis();
            } catch (InterruptedException ignore) {
            }
        }
        increase(requestLimit);
        return requestLimit;
    }

}
