package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class TikTokGmvMaxCampaignInfo {

    private Integer puid;

    private Integer shopId;
    private String shopName;

    private String storeId;

    private String storeName;

    private String advertiserId;
    private String advertiserName;

    private String currency;

    private String campaignId;
    private String campaignName;

    private String shoppingAdsType; // 商品还是直播

    private String productSpecificType; // 全部商品 or 部分商品
    private List<String> spuIdList; // 商品spu

    private String optimizationGoal;
    private String deepBidType;
    private BigDecimal roasBid;
    private BigDecimal budget;

    private String scheduleType;
    private String scheduleStartTime;
    private String scheduleEndTime;

    private String productVideoSpecificType; // 创意模式 自动 or 手动
    private List<Identity> identityList;
    private Boolean affiliatePostsEnabled;
    //已授权作品
    private List<GmvMaxVideoItem> itemList;
    private String campaignCustomAnchorVideoId;
    //自定义作品
    private List<GmvMaxVideoItem> customAnchorVideoList;


}
