package com.meiyunji.sponsored.service.cpc.service2.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.impl.UserDaoImpl;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AsinsLibBo;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CampaignTypeDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibMarkupAsinQo;
import com.meiyunji.sponsored.service.cpc.qo.KeywordLibUnMarkAsinQo;
import com.meiyunji.sponsored.service.cpc.service.IAdKeywordLibTagService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.ICpcAsinsLibService;
import com.meiyunji.sponsored.service.cpc.service2.IKeywordsLibAndAsinLibLimitService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdGroup;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.KeywordDataFieldEnum;
import com.meiyunji.sponsored.service.util.AdPageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by heqiwen on 2024/12/02.
 */
@Service
@Slf4j
public class CpcAsinsLibServiceImpl implements ICpcAsinsLibService {

    @Autowired
    private IAmazonAdAsinsLibDao amazonAdAsinsLibDao;

    @Autowired
    private IAdKeywordLibTagDao adKeywordLibTagDao;

    @Autowired
    private IAdKeywordLibMarkupTagDao adKeywordLibMarkupTagDao;

    @Autowired
    private IAsinsLibMarkupAsinDao asinsLibMarkupAsinDao;

    @Autowired
    private IAdKeywordLibTagService adKeywordLibTagService;

    //计算百分比
    public static final BigDecimal RATE = BigDecimal.valueOf(100L);

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdPortfolioService portfolioService;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IOdsAmazonAdTargetingDao odsAmazonAdTargetingDao;
    @Autowired
    private IOdsCpcTargetingReportDao odsCpcTargetingReportDao;
    @Autowired
    private IOdsAmazonAdSbTargetingReportDao odsAmazonAdSbTargetingReportDao;
    @Autowired
    private IOdsAmazonAdSdTargetingReportDao odsAmazonAdSdTargetingReportDao;
    @Autowired
    private IOdsAmazonAdTargetingSbDao odsAmazonAdTargetingSbDao;
    @Autowired
    private IOdsAmazonAdTargetingSdDao odsAmazonAdTargetingSdDao;
    @Autowired
    private IShopAuthService shopAuthService;

    @Autowired
    private IAmazonAdCampaignNetargetingSpDao amazonAdCampaignNetargetingSpDao;
    @Autowired
    private IOdsAmazonAdNeTargetingDao odsAmazonAdNeTargetingDao;
    @Autowired
    private OdsAmazonAdNeTargetingSbDao odsAmazonAdNeTargetingSbDao;
    @Autowired
    private OdsAmazonAdNeTargetingSdDao odsAmazonAdNeTargetingSdDao;
    @Autowired
    private UserDaoImpl userDao;
    @Autowired
    private IOdsAmazonAdGroupDao odsAmazonAdGroupDao;
    @Autowired
    private IKeywordsLibAndAsinLibLimitService keywordsLibAndAsinLibLimitService;
    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonSbAdTargetingDao amazonSbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao amazonSdAdTargetingDao;
    @Autowired
    private ITrackingDao trackingDao;
    private static final int DEFAULT_PARTITION_SIZE = 9000;
    @Override
    public Result<AddAsinLibVo> createAsins(Integer puid, Integer uid, List<AsinLibsVo> voList, List<Integer> uidList) {
        if (Objects.isNull(puid) || puid == 0 || Objects.isNull(uid) || uid == 0 || CollectionUtils.isEmpty(voList)) {
            return ResultUtil.error("puid或者添加的Asin不能为空");
        }

        //校验请求中的ASIN
        Result<AddAsinLibVo> asinReqCheck = checkCreateAsinInReq(puid, voList);
        if (Objects.nonNull(asinReqCheck)) {
            return asinReqCheck;
        }

        //校验该uid和其数据权限以下的uid已添加过的ASIN，标签和ASIN标签的数量，已存在的标签或ASIN标签数量+本次请求数量不能超过限制
        //即过滤出的ASIN需要插入数据库中
        Result<AddAsinLibVo> asinInfoListVoResult = asinExistAndTagAndAsinTagHandler(puid, uid, voList, uidList);

        //如果过滤校验之后，需要插入db的数据为空，则直接返回
        if (Objects.isNull(asinInfoListVoResult) || Objects.isNull(asinInfoListVoResult.getData())
                || CollectionUtils.isEmpty(asinInfoListVoResult.getData().getAddVos())) {
            return asinInfoListVoResult;
        }

        //入库，存在唯一索引，避免插入报错，INSERT TO ON UPDATE
        List<AmazonAdAsinsLib> poList = this.insertAsin(puid, uid, asinInfoListVoResult.getData());

        //标签和ASIN标签入库
        if (CollectionUtils.isNotEmpty(poList) && CollectionUtils.isNotEmpty(voList.get(0).getAdTags()) || CollectionUtils.isNotEmpty(voList.get(0).getAsinTags())) {
            addAsinLibTagAndAsinTag(puid, uid, poList, voList.get(0).getAdTags(), voList.get(0).getAsinTags(), asinInfoListVoResult.getData());
        }

        return ResultUtil.success(asinInfoListVoResult.getData());
    }

    @Override
    public Result<AsinLibsPageListVo> pageList(PageListAsinsRequest request) {
        if (request.getPuid() == 0 || request.getUid() == 0) {
            return ResultUtil.error("puid或者添uid不能为空");
        }
        Integer puid = request.getPuid();
        String currency;
        String startDate = request.getStartDate();
        String endDate = request.getEndDate();
        List<Integer> uidList = request.getUidListList();
        if (StringUtils.isEmpty(request.getTo())) {
            // 没有币种则说明是批量创建入口进入,默认赋值USD美国币种和报告查询时间近7天
            currency = "USD";
            startDate = DateUtil.format(DateUtil.offsetDay(new Date(),-6),"yyyy-MM-dd");
            endDate = DateUtil.format(new Date(),"yyyy-MM-dd");
        } else {
            currency = request.getTo();
        }
        if (StringUtils.isNotEmpty(request.getRemark()) || StringUtils.isNotEmpty(request.getSource())) {
            uidList = Collections.singletonList(request.getUid());//当筛选来源和备注时，只筛选当前uid的数据
        }

        AsinLibsPageListVo result = new AsinLibsPageListVo();

        //根据入参标签和ASIN标签，过滤要查询的asin lib id
        List<Long> asinQueryIdList = filterTagAndAsinTag(puid, request);
        if (Objects.nonNull(asinQueryIdList) && asinQueryIdList.isEmpty()) {
            result.setPage(new Page<>(request.getPageNo(), request.getPageSize()));
            return ResultUtil.returnSucc(result);
        }

        //获取列表数据
        result = this.getPageRow(puid, request, uidList, asinQueryIdList, startDate, endDate, currency);
        if (Objects.isNull(result.getPage()) || CollectionUtils.isEmpty(result.getPage().getRows())) {
            result.setPage(new Page<>(request.getPageNo(), request.getPageSize()));
            return ResultUtil.returnSucc(result);
        }

        return ResultUtil.returnSucc(result);
    }

    @Override
    public Result<List<AsinLibsVo>> exportList(PageListAsinsRequest request) {
        PageListAsinsRequest.Builder reqBuilder = request.toBuilder();
        reqBuilder.setPageNo(1);
        reqBuilder.setPageSize(10000);
        Result<AsinLibsPageListVo> result = this.pageList(reqBuilder.build());
        if (Objects.nonNull(result) && Objects.nonNull(result.getData()) && Objects.nonNull(result.getData().getPage())
                && CollectionUtils.isNotEmpty(result.getData().getPage().getRows())) {
            return ResultUtil.returnSucc(result.getData().getPage().getRows());
        }
        log.info("export asin lib list is empty");
        return ResultUtil.returnSucc(new ArrayList<>());
    }

    @Override
    public List<String> getAsinListByPuid(Integer puid) {
        //统一查询ASIN库和关键词库puid已添加的数量
        List<AmazonAdAsinsLib> asinList = amazonAdAsinsLibDao.getByPuid(puid);
        return ListUtils.emptyIfNull(asinList.parallelStream().map(AmazonAdAsinsLib::getAsin).collect(Collectors.toList()));
    }

    @Override
    public Integer getAsinByPuidAndAsinCnt(Integer puid, List<String> asinList) {
        return amazonAdAsinsLibDao.countByPuidAndAsin(puid, asinList);
    }

    private boolean checkTagExist(Integer puid, Integer uid, List<AsinLibsVo> vos) {
        Set<Long> ids = new HashSet<>();
        for (AsinLibsVo vo : vos) {
            List<AdKeywordLibTag> adTags = vo.getAdTags();
            if (CollectionUtils.isNotEmpty(adTags)) {
                ids.addAll(adTags.parallelStream().map(AdKeywordLibTag::getId).filter(Objects::nonNull).collect(Collectors.toList()));
            }
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            int count = adKeywordLibTagDao.getByAllUidAndIdList(puid, uid, new ArrayList<>(ids));
            return count == ids.size();
        }
        return true;
    }


    @Override
    public Result<Page<AsinLibsDetailVo>> getAsinDetail(AsinLibsDetailParam param) {
        StopWatch sw = new StopWatch("getAsinDetail");
        Integer puid = param.getPuid();
        Page<AsinLibsDetailVo> page = new Page<>();
        page.setRows(new ArrayList<>());
        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        try {
            if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
                sw.start("获取广告组id");
                List<String> groupIds = new ArrayList<>();
                groupIds.addAll(amazonAdGroupDao.getByShopIdsAndCampaignIds(puid, param.getShopIdList(), param.getSearchCampaignList()));
                groupIds.addAll(amazonSbAdGroupDao.getByShopIdsAndCampaignIds(puid, param.getShopIdList(), param.getSearchCampaignList()));
                groupIds.addAll(amazonSdAdGroupDao.getIdListByCampaignIds(puid, param.getShopIdList(), param.getSearchCampaignList()));
                if (CollectionUtils.isEmpty(groupIds)) {
                    return ResultUtil.returnSucc(page);
                }
                if (CollectionUtils.isEmpty(param.getSearchAdGroupList())) {
                    param.setSearchAdGroupList(groupIds);
                } else {
                    groupIds.retainAll(param.getSearchAdGroupList());
                    if (CollectionUtils.isEmpty(groupIds)) {
                        return ResultUtil.returnSucc(page);
                    }
                }
                sw.stop();
            }
            sw.start("查询分页展示的投放id");
            page = odsAmazonAdTargetingDao.getPageTargetIds(puid, param);
            sw.stop();
            List<AsinLibsDetailVo> rows = page.getRows();
            if (CollectionUtils.isEmpty(rows)) {
                return ResultUtil.returnSucc(page);
            }
            // 根据类型区分出不同类型投放id
            List<String> spTargetIds = rows.stream().filter(item ->
                    CampaignTypeEnum.sp.getCampaignType().equals(item.getType())).map(AsinLibsDetailVo::getTargetId).
                    collect(Collectors.toList());
            List<String> sbTargetIds = rows.stream().filter(item ->
                    CampaignTypeEnum.sb.getCampaignType().equals(item.getType())).map(AsinLibsDetailVo::getTargetId).
                    collect(Collectors.toList());
            List<String> sdTargetIds = rows.stream().filter(item ->
                    CampaignTypeEnum.sd.getCampaignType().equals(item.getType())).map(AsinLibsDetailVo::getTargetId).
                    collect(Collectors.toList());
            // todo 可以联表查询
            sw.start("查询报告数据：sp");
            List<AsinLibsDetailVo> spReportData = CollectionUtils.isNotEmpty(spTargetIds) ? odsCpcTargetingReportDao.getSpAsinReportDataByTargetId(puid, param, spTargetIds) : new ArrayList<>();
            sw.stop();
            sw.start("查询报告数据：sb");
            List<AsinLibsDetailVo> sbReportData = CollectionUtils.isNotEmpty(sbTargetIds) ? odsAmazonAdSbTargetingReportDao.getSbAsinReportDataByTargetId(puid, param, sbTargetIds) : new ArrayList<>();
            sw.stop();
            sw.start("查询报告数据：sd");
            List<AsinLibsDetailVo> sdReportData = CollectionUtils.isNotEmpty(sdTargetIds) ? odsAmazonAdSdTargetingReportDao.getSdAsinReportDataByTargetId(puid, param, sdTargetIds) : new ArrayList<>();
            sw.stop();
            // 报告数据---根据投放id聚合
            Map<String, AsinLibsDetailVo> map = new HashMap<>();
            for (AsinLibsDetailVo k : spReportData) {
                map.put(k.getTargetId(), null);
            }
            // 合并报告数据
            Map<String, AsinLibsDetailVo> reportDataMap = new HashMap<>();
            Map<String, AsinLibsDetailVo> spAndSbMap = mergeKeywordReportData(map, spReportData);
            Map<String, AsinLibsDetailVo> sbAndSdMap = mergeKeywordReportData(spAndSbMap, sbReportData);
            reportDataMap = mergeKeywordReportData(sbAndSdMap, sdReportData);
            //查询基础信息
            sw.start("查询基础数据：sp");
            List<AsinLibsDetailVo> spDetailData = CollectionUtils.isNotEmpty(spTargetIds) ? amazonAdTargetingShardingDao.getSpAsinDetailDataByTargetId(puid, param, spTargetIds) : new ArrayList<>();
            sw.stop();
            sw.start("查询基础数据：sb");
            List<AsinLibsDetailVo> sbDetailData = CollectionUtils.isNotEmpty(sbTargetIds) ? amazonSbAdTargetingDao.getSbAsinDetailDataByTargetId(puid, param, sbTargetIds) : new ArrayList<>();
            sw.stop();
            sw.start("查询基础数据：sd");
            List<AsinLibsDetailVo> sdDetailData = CollectionUtils.isNotEmpty(sdTargetIds) ? amazonSdAdTargetingDao.getSdAsinDetailDataByTargetId(puid, param, sdTargetIds) : new ArrayList<>();
            sw.stop();
            //聚合shopId set去重
            Set<Integer> allUniqueShopIds = new HashSet<>(Collections.emptySet());
            List<String> campaignIdList = new ArrayList<>();
            List<String> spGroupIdList = new ArrayList<>();
            List<String> sbGroupIdList = new ArrayList<>();
            List<String> sdGroupIdList = new ArrayList<>();
            //聚合所有店铺Id、广告活动Id、广告组Id
            for (AsinLibsDetailVo spData : spDetailData) {
                if (spData.getShopId() != null) {
                    allUniqueShopIds.add(spData.getShopId());
                }
                if (spData.getCampaignId() != null) {
                    campaignIdList.add(spData.getCampaignId());
                }
                if (spData.getAdGroupId() != null) {
                    spGroupIdList.add(spData.getAdGroupId());
                }
            }
            for (AsinLibsDetailVo sbData : sbDetailData) {
                allUniqueShopIds.add(sbData.getShopId());
                if (sbData.getShopId() != null) {
                    allUniqueShopIds.add(sbData.getShopId());
                }
                if (sbData.getCampaignId() != null) {
                    campaignIdList.add(sbData.getCampaignId());
                }
                if (sbData.getAdGroupId() != null) {
                    sbGroupIdList.add(sbData.getAdGroupId());
                }
            }
            for (AsinLibsDetailVo sdData : sdDetailData) {
                allUniqueShopIds.add(sdData.getShopId());
                if (sdData.getShopId() != null) {
                    allUniqueShopIds.add(sdData.getShopId());
                }
                if (sdData.getCampaignId() != null) {
                    campaignIdList.add(sdData.getCampaignId());
                }
                if (sdData.getAdGroupId() != null) {
                    sdGroupIdList.add(sdData.getAdGroupId());
                }
            }
            // 基础数据---转成map
            // 一个for循环聚合数据
            Map<String, AsinLibsDetailVo> spDetailMap = spDetailData.stream().collect(Collectors.toMap(AsinLibsDetailVo::getTargetId, item -> item));
            Map<String, AsinLibsDetailVo> sbDetailMap = sbDetailData.stream().collect(Collectors.toMap(AsinLibsDetailVo::getTargetId, item -> item));
            Map<String, AsinLibsDetailVo> sdDetailMap = sdDetailData.stream().collect(Collectors.toMap(AsinLibsDetailVo::getTargetId, item -> item));
            //dto 聚合参数
            AsinFillDataDto fillDataDto = new AsinFillDataDto();
            fillDataDto.setShopIds(allUniqueShopIds);
            fillDataDto.setCampaignIdList(campaignIdList);
            fillDataDto.setSpGroupIdList(spGroupIdList);
            fillDataDto.setSbGroupIdList(sbGroupIdList);
            fillDataDto.setSdGroupIdList(sdGroupIdList);
            fillDataDto.setSpDetailMap(spDetailMap);
            fillDataDto.setSbDetailMap(sbDetailMap);
            fillDataDto.setSdDetailMap(sdDetailMap);
            fillDataDto.setReportDataMap(reportDataMap);
            // 查询店铺名称、广告组合名称、广告活动名称、广告组名称
            this.fillData(puid, fillDataDto);
            //所有数据聚合组装
            this.buildAllData(rows, fillDataDto, page);
        } catch (Exception e) {
            log.error("get keyword lib detail msg puid:{}, error:{}", puid, e);
        }
        return ResultUtil.returnSucc(page);
    }

    @Override
    public Result<Page<AsinLibsDetailVo>> getAsinNeTargetDetail(AsinLibsDetailParam param) {
        StopWatch sw = new StopWatch("获取否投详情");
        Page<AsinLibsDetailVo> page = new Page<>();
        sw.start("查询分页展示的否投id");
        page = odsAmazonAdNeTargetingDao.getAsinNeTargetList(param.getPuid(), param);
        sw.stop();
        List<AsinLibsDetailVo> rows = page.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return ResultUtil.returnSucc(page);
        }
        Set<String> campaignIds = new HashSet<>();
        Set<String> spAdgroupIds = new HashSet<>();
        Set<String> sbAdgroupIds = new HashSet<>();
        Set<String> sdAdgroupIds = new HashSet<>();
        rows.forEach(e -> {
            campaignIds.add(e.getCampaignId());
            if (Constants.SP.equals(e.getType())){
                spAdgroupIds.add(e.getAdGroupId());
            } else if (Constants.SB.equals(e.getType())) {
                sbAdgroupIds.add(e.getAdGroupId());
            } else {
                sdAdgroupIds.add(e.getAdGroupId());
            }
        });
        List<Integer> shopIds = rows.stream().map(AsinLibsDetailVo::getShopId).distinct().collect(Collectors.toList());
        // 这里需要进行特殊处理，sd的广告活动Id需要根据根据广告组id去反查广告活动id
        if (CollectionUtils.isNotEmpty(sdAdgroupIds)) {
            //返回实体类
            List<Map<String, Object>> ids = amazonSdAdGroupDao.getCampaignIdsByGroupIds(param.getPuid(), shopIds, sdAdgroupIds);
            //根据groupId注入相应的campaignId
            String groupKey = "groupId";
            String campaignKey = "campaignId";
            rows.stream().filter(e -> Constants.SD.equals(e.getType()))
                    .forEach(e -> {
                        String adGroupId = e.getAdGroupId();
                        for (Map<String, Object> idMap : ids) {
                            if (idMap.get(groupKey).equals(adGroupId)) {
                                e.setCampaignId(idMap.get(campaignKey).toString());
                                break;
                            }
                        }
                    });
            // 提取每个Map中"campaignId"对应的值，若不存在该键则返回null
            List<String> campaignIdList = ids.stream().map(map -> (String) map.get(campaignKey)).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            campaignIds.addAll(campaignIdList);
        }
        sw.start("查询分页展示的店铺名称、广告组合、广告活动、广告组");
        Map<Integer, String> shopNameMap = shopAuthService.getShopNameMap(param.getPuid(), shopIds);
        List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.getNameByShopIdsAndCampaignIds(param.getPuid(), shopIds, new ArrayList<>(campaignIds), null, null);
        Map<String, String> campaignNameMap = campaignAllList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, AmazonAdCampaignAll::getName));
        Map<String, String> campaignIdportfolioIdMap = campaignAllList.stream().filter(campaign -> campaign.getPortfolioId() != null).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, AmazonAdCampaignAll::getPortfolioId));
        List<String> portfolioIdList = campaignAllList.stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        List<KeywordLibsPortfolioListVO> portfolioListVo = portfolioService.getAllPortfolioName(param.getPuid(), shopIds, portfolioIdList);
        Map<String, KeywordLibsPortfolioListVO> portfolioMap = portfolioListVo.parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(KeywordLibsPortfolioListVO::getPortfolioId, Function.identity()));
        Map<String, String> adGroupNameMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(spAdgroupIds)) {
            adGroupNameMap.putAll(amazonAdGroupDao.getNameByShopIdsAndGroupIds(param.getPuid(), shopIds, new ArrayList<>(spAdgroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, AmazonAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sbAdgroupIds)) {
            adGroupNameMap.putAll(amazonSbAdGroupDao.getNameListByShopIdsAndGroupIds(param.getPuid(), shopIds, null, new ArrayList<>(sbAdgroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, AmazonSbAdGroup::getName)));
        }
        if (CollectionUtils.isNotEmpty(sdAdgroupIds)) {
            adGroupNameMap.putAll(amazonSdAdGroupDao.getNameByShopIdsAndGroupIds(param.getPuid(), shopIds, null, new ArrayList<>(sdAdgroupIds), null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getName)));
        }
        sw.stop();
        rows.forEach(e -> {
            e.setShopName(shopNameMap.getOrDefault(e.getShopId(), ""));
            e.setPortfolioId(campaignIdportfolioIdMap.get(e.getCampaignId()));
            e.setCampaignName(campaignNameMap.getOrDefault(e.getCampaignId(), ""));
            e.setAdGroupName(adGroupNameMap.getOrDefault(e.getAdGroupId(), ""));
            e.setCreateTime(StringUtils.isNotBlank(e.getCreateTime()) && e.getCreateTime().length() > 19 ? e.getCreateTime().substring(0, 19) : e.getCreateTime());
            KeywordLibsPortfolioListVO portfolioListVO = portfolioMap.get(campaignIdportfolioIdMap.get(e.getCampaignId()));
            if (portfolioListVO != null) {
                e.setPortfolioName(portfolioListVO.getPortfolioName());
                e.setIsHidden(portfolioListVO.getIsHidden());
            }
        });
        page.setRows(rows);
        return ResultUtil.returnSucc(page);
    }

    @Override
    public Result<KeywordLibsTotalVo> getAsinDetailAggregateData(AsinLibsDetailParam param) {
        Integer puid = param.getPuid();
        List<KeywordLibsTotalVo> vo;
        KeywordLibsTotalVo totalVo = new KeywordLibsTotalVo();
        try {
            if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
                List<String> groupIds = new ArrayList<>();
                groupIds.addAll(amazonAdGroupDao.getByShopIdsAndCampaignIds(puid, param.getShopIdList(), param.getSearchCampaignList()));
                groupIds.addAll(amazonSbAdGroupDao.getByShopIdsAndCampaignIds(puid, param.getShopIdList(), param.getSearchCampaignList()));
                groupIds.addAll(amazonSdAdGroupDao.getIdListByCampaignIds(puid, param.getShopIdList(), param.getSearchCampaignList()));
                if (CollectionUtils.isEmpty(groupIds)) {
                    return ResultUtil.returnSucc(totalVo);
                }
                if (CollectionUtils.isEmpty(param.getSearchAdGroupList())) {
                    param.setSearchAdGroupList(groupIds);
                } else {
                    groupIds.retainAll(param.getSearchAdGroupList());
                    if (CollectionUtils.isEmpty(groupIds)) {
                        return ResultUtil.returnSucc(totalVo);
                    }
                }
            }
            //查询报告数据
            totalVo = odsAmazonAdTargetingDao.getAsinDetailAggregateData(puid, param);
        } catch (Exception e) {
            log.info("get asin_lib query aggregateData error msg puid:{}, error:{}",puid, e);
        }
        return ResultUtil.returnSucc(totalVo);
    }

    @Override
    public Result deleteAsin(int puid, int uid, List<String> asins) {
        if (CollectionUtils.isEmpty(asins)) {
            return ResultUtil.error("未指定删除的关键词");
        }
        try {
            //删除标签数据
            List<Long> ids = amazonAdAsinsLibDao.getIdListByKeyword(puid, uid, asins);
            if (CollectionUtils.isNotEmpty(ids)) {
                adKeywordLibMarkupTagDao.deleteAllByRelationId(puid, uid, AdTagTypeEnum.ASINLIB.getType(), null, null, ids.stream().map(String::valueOf).collect(Collectors.toList()), null);
                asinsLibMarkupAsinDao.deleteByAsinLibId(puid, uid, ids);
                int delCount = amazonAdAsinsLibDao.deleteByAsin(puid, uid, ids);
                return delCount > 0 ? ResultUtil.success() :  ResultUtil.error("该数据已被删除");
            }
            return ResultUtil.error("删除失败，请联系管理员");
        } catch (Exception e) {
            log.error("deleteAsin error msg puid:{}, error:{}",puid, e);
            return ResultUtil.error("删除失败，请联系管理员");
        }
    }

    @Override
    public Result<List<ErrorMsgVo>> markupAsin(Integer puid, Integer uid, List<Integer> shopIdList, KeywordLibMarkupAsinQo qo) {
        List<Long> idList = qo.getIdList().stream().distinct().collect(Collectors.toList());
        List<AsinsLibBo> boList = amazonAdAsinsLibDao.getAsinsLibBoByIds(puid, idList, qo.getUidList());
        if (boList.size() != idList.size()) {
            return ResultUtil.returnErr("标记对象不存在，请刷新页面重试");
        }
        List<AmazonAdAsinLibMarkupAsin> idMap = asinsLibMarkupAsinDao.getListByAsinsLibId(puid, qo.getUidList(), idList);
        //找到各个Id下的词
        Map<Long, Set<String>> setMap = idMap.stream().collect(Collectors.groupingBy(AmazonAdAsinLibMarkupAsin::getAsinLibId, Collectors.mapping(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId(), Collectors.toSet())));
        Map<String, List<AsinsLibBo>> map = boList.stream().collect(Collectors.groupingBy(k -> k.getAsin().toLowerCase()));
        List<ErrorMsgVo> errorVoList = new ArrayList<>();
        List<AmazonAdAsinLibMarkupAsin> insertList = new ArrayList<>();
        //遍历要添加的asin
        Set<String> addAsinSet = qo.getMarkupAsinList().stream().map(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId()).collect(Collectors.toSet());
        map.forEach((key, value) -> {
            Set<String> asinSet = new HashSet<>();
            //相关关键词的标签聚合在一起
            for (AsinsLibBo libBo : value) {
                Set<String> set = setMap.get(libBo.getId());
                if (set != null) {
                    asinSet.addAll(set);
                }
            }
            //添加当前标签到里面去
            asinSet.addAll(addAsinSet);
            if (asinSet.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                ErrorMsgVo errorVo = new ErrorMsgVo();
                errorVo.setId(0L);
                errorVo.setName(key);
                errorVo.setError("部分关键词添加的标签数量超过限制");
                errorVoList.add(errorVo);
                return;
            }
            for (AsinsLibBo libBo : value) {
                for (KeywordLibMarkupAsinQo.MarkupAsin markupAsin : qo.getMarkupAsinList()) {
                    AmazonAdAsinLibMarkupAsin amazonAdAsinLibMarkupAsin = new AmazonAdAsinLibMarkupAsin();
                    amazonAdAsinLibMarkupAsin.setPuid(puid);
                    amazonAdAsinLibMarkupAsin.setUid(uid);
                    amazonAdAsinLibMarkupAsin.setAsinLibId(libBo.getId());
                    amazonAdAsinLibMarkupAsin.setMarketplaceId(markupAsin.getMarketplaceId());
                    amazonAdAsinLibMarkupAsin.setAsin(markupAsin.getAsin());
                    amazonAdAsinLibMarkupAsin.setCreateId(uid);
                    amazonAdAsinLibMarkupAsin.setUpdateId(uid);
                    insertList.add(amazonAdAsinLibMarkupAsin);
                }
            }
        });

        if (CollectionUtils.isNotEmpty(insertList)) {
            asinsLibMarkupAsinDao.batchInsert(insertList);
        } else {
            return ResultUtil.returnErr("添加的标签数量超过限制");
        }
        if (CollectionUtils.isNotEmpty(errorVoList)) {
            return ResultUtil.success(errorVoList);
        }
        return ResultUtil.success();
    }

    @Override
    public Page<KeywordLibsCampaignListVO> getAsinsDetailCampaignListPage(AsinLibsParam param) {
        Integer puid = param.getPuid();
        Page<KeywordLibsCampaignListVO> page = new Page<>();
        List<KeywordLibsCampaignListVO> dataRow = Lists.newArrayList();

        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        //根据puid和asin查询campaign list
        filterPortfolio(param);
        try {
            Page<OdsAmazonAdTargeting> campaignPage;
            if (Constants.BIDDABLE.equalsIgnoreCase(param.getTargetType())) {
                campaignPage = odsAmazonAdTargetingDao.listPageByCondition(puid, param.getShopIds(), param);
            } else {
                campaignPage = convert(odsAmazonAdNeTargetingDao.listPageByCondition(puid, param.getShopIds(), param));
            }
            page.setTotalPage(campaignPage.getTotalPage());
            page.setTotalSize(campaignPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(campaignPage.getRows())) {
                Map<String, AmazonAdCampaignAll> amazonAdCampaignAlls = amazonAdCampaignAllDao.listByShopIdAndCampaignIds(puid,
                                campaignPage.getRows().stream().map(OdsAmazonAdTargeting::getShopId).distinct().collect(Collectors.toList()),
                                campaignPage.getRows().stream().map(OdsAmazonAdTargeting::getCampaignId).collect(Collectors.toList()))
                        .stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));
                dataRow = campaignPage.getRows().stream().map(k -> {
                    KeywordLibsCampaignListVO keywordLibsCampaignListVO = new KeywordLibsCampaignListVO();
                    keywordLibsCampaignListVO.setShopId(k.getShopId());
                    keywordLibsCampaignListVO.setCampaignId(k.getCampaignId());
                    AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAlls.get(k.getCampaignId());
                    if (amazonAdCampaignAll != null) {
                        keywordLibsCampaignListVO.setCampaignName(amazonAdCampaignAll.getName());
                        keywordLibsCampaignListVO.setMarketplaceId(amazonAdCampaignAll.getMarketplaceId());
                        keywordLibsCampaignListVO.setPortfolioId(amazonAdCampaignAll.getPortfolioId());
                    }
                    return keywordLibsCampaignListVO;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("query keyword lib detail campaign list by puid end asin error，puid:{},keyword_text:{}, e", puid, param.getAsin(), e);
        }
        page.setRows(dataRow);
        return page;
    }

    @Override
    public Page<KeywordLibsGroupListVO> getAsinsDetailGroupListPage(AsinLibsParam param) {
        Integer puid = param.getPuid();
        Page<KeywordLibsGroupListVO> page = new Page<>();
        page.setPageNo(param.getPageNo());
        page.setPageSize(param.getPageSize());
        List<KeywordLibsGroupListVO> dataRow = Lists.newArrayList();
        try {
            List<OdsAmazonAdTargeting> list;
            if (Constants.BIDDABLE.equalsIgnoreCase(param.getTargetType())) {
                list = odsAmazonAdTargetingDao.listAllGroupByCondition(puid, param.getShopIds(), param);
            } else {
                list = odsAmazonAdNeTargetingDao.listAllNeGroupByCondition(puid, param.getShopIds(), param);
            }
            if (CollectionUtils.isEmpty(list)) {
                page.setRows(dataRow);
                return page;
            }
            param.setAdGroupList(list.stream().map(OdsAmazonAdTargeting::getAdGroupId).collect(Collectors.toList()));
            Page<OdsAmazonAdGroup> odsAmazonAdGroupPage = odsAmazonAdGroupDao.listPageByByCondition(puid, param.getShopIds(), param);
            page.setTotalPage(odsAmazonAdGroupPage.getTotalPage());
            page.setTotalSize(odsAmazonAdGroupPage.getTotalSize());
            if (CollectionUtils.isNotEmpty(odsAmazonAdGroupPage.getRows())) {
                dataRow = odsAmazonAdGroupPage.getRows().stream().map(k -> {
                    KeywordLibsGroupListVO keywordLibsGroupListVO = new KeywordLibsGroupListVO();
                    keywordLibsGroupListVO.setAdGroupId(k.getAdGroupId());
                    keywordLibsGroupListVO.setAdGroupName(k.getName());
                    keywordLibsGroupListVO.setShopId(k.getShopId());
                    keywordLibsGroupListVO.setMarketplaceId(k.getMarketplaceId());
                    keywordLibsGroupListVO.setType(k.getAdGroupType());
                    return keywordLibsGroupListVO;
                }).collect(Collectors.toList());
            }
        } catch (Exception e) {
            log.error("query keyword lib detail group list by puid end keyword_text error，puid:{},asin:{}, e", puid, param.getAsin(), e);
        }
        page.setRows(dataRow);
        return page;
    }

    @Override
    public Result updateRemark(AsinLibsVo vo) {
        AmazonAdAsinsLib amazonAdAsinLib = amazonAdAsinsLibDao.getByPuidAndAsin(vo.getPuid(), vo.getUid(), vo.getAsin());
        if (amazonAdAsinLib == null) {
            amazonAdAsinLib = new AmazonAdAsinsLib();
            amazonAdAsinLib.setAsin(vo.getAsin().trim());
            amazonAdAsinLib.setRemark(vo.getRemark());
            amazonAdAsinLib.setPuid(vo.getPuid());
            amazonAdAsinLib.setUid(vo.getUid());
            amazonAdAsinLib.setUpdateId(vo.getUid());
            amazonAdAsinLib.setCreateId(vo.getUid());
            amazonAdAsinLib.setSource("manual");
            amazonAdAsinsLibDao.insertOnDuplicateKeyUpdate(vo.getPuid(), Lists.newArrayList(amazonAdAsinLib));
            return ResultUtil.success();
        }
        int updateCount = amazonAdAsinsLibDao.updateRemark(vo.getPuid(), vo.getUid(), vo.getAsin(), vo.getRemark(), vo.getUid());
        if (updateCount > 0) {
            return ResultUtil.success();
        } else {
            return ResultUtil.error("更新失败");
        }
    }

    @Override
    public Page<AmazonAdAsinLibMarkupAsin> getAsinPageList(Integer puid, List<Integer> uidList, List<Integer> shopIdList, AsinLibAsinTagPageListVo req) {
        List<String> marketplaceIdList = shopAuthDao.marketplaceIdListByShopIds(shopIdList);
        if (CollectionUtils.isEmpty(marketplaceIdList)) {
            return new Page<>(req.getPageNo(), req.getPageSize());
        }
        return asinsLibMarkupAsinDao.getAsinTagPageList(puid, uidList, marketplaceIdList, req);
    }

    @Override
    public Result unMarkAsin(int puid, List<Integer> uidList, List<Integer> shopIdList, KeywordLibUnMarkAsinQo qo) {
        List<String> marketplaceIdList = shopAuthDao.marketplaceIdListByShopIds(shopIdList);
        if (!marketplaceIdList.contains(qo.getMarketplaceId())) {
            return ResultUtil.returnErr("没有该ASIN店铺权限");
        }
        asinsLibMarkupAsinDao.deleteByKeywordsLibIdAndAsin(puid, uidList, qo.getIdList(), qo.getMarketplaceId(), qo.getAsin());
        return ResultUtil.success();
    }

    private Page<OdsAmazonAdTargeting> convert(Page<OdsAmazonAdTargeting> amazonAdNeKeywordPage) {
        return new Page<>(amazonAdNeKeywordPage.getPageNo(), amazonAdNeKeywordPage.getPageSize(), amazonAdNeKeywordPage.getTotalPage(), amazonAdNeKeywordPage.getTotalSize(), buildList(amazonAdNeKeywordPage.getRows()));
    }

    private List<OdsAmazonAdTargeting> buildList(List<OdsAmazonAdTargeting> amazonAdNeKeywords) {
        if (CollectionUtils.isEmpty(amazonAdNeKeywords)) {
            return new ArrayList<>();
        }
        return amazonAdNeKeywords.stream().map(k -> {
            OdsAmazonAdTargeting odsAmazonAdKeyword = new OdsAmazonAdTargeting();
            odsAmazonAdKeyword.setShopId(k.getShopId());
            odsAmazonAdKeyword.setCampaignId(k.getCampaignId());
            return odsAmazonAdKeyword;
        }).collect(Collectors.toList());
    }

    /**
     * 合并报告数据
     * @param map 合并后的Map
     * @param targetList 要合入的源数据List
     * @return
     */
    private Map<String, AsinLibsDetailVo> mergeKeywordReportData(Map<String, AsinLibsDetailVo> map, List<AsinLibsDetailVo> targetList) {
        for (AsinLibsDetailVo asinList : targetList) {
            AsinLibsDetailVo vo = map.get(asinList.getTargetId());
            if (vo != null) {
                if (asinList.getImpressions() != null) {
                    //曝光量
                    vo.setImpressions(vo.getImpressions() + asinList.getImpressions());
                }
                if (asinList.getTotalSales() != null) {
                    //广告销售额
                    vo.setTotalSales(vo.getTotalSales().add(asinList.getTotalSales()));
                }
                if (asinList.getClicks() != null) {
                    //广告点击量
                    vo.setClicks(vo.getClicks() + asinList.getClicks());
                }
                if (asinList.getClickRate() != null) {
                    //广告点击率
                    vo.setClickRate(MathUtil.multiply(vo.getClickRate().add(asinList.getClickRate()), RATE));
                }
                if (asinList.getSaleNum() != null) {
                    //广告订单量
                    vo.setSaleNum(vo.getSaleNum() + asinList.getSaleNum());
                }
                if (asinList.getSalesConversionRate() != null) {
                    //广告转化率
                    vo.setSalesConversionRate(MathUtil.multiply(vo.getSalesConversionRate().add(asinList.getSalesConversionRate()), RATE));
                }
                if (asinList.getCost() != null) {
                    //广告花费
                    vo.setCost(vo.getCost().add(asinList.getCost()));
                }
                if (asinList.getCpc() != null) {
                    //cpc
                    vo.setCpc(vo.getCpc().add(asinList.getCpc()));
                }
                if (asinList.getCpa() != null) {
                    //cpa
                    vo.setCpa(vo.getCpa().add(asinList.getCpa()));
                }
                if (asinList.getRoas() != null) {
                    vo.setRoas(vo.getRoas().add(asinList.getRoas()));
                }
                if (asinList.getAcos() != null) {
                    vo.setAcos(MathUtil.multiply(vo.getAcos().add(asinList.getAcos()), RATE));
                }
            } else {
                if (asinList.getClickRate() != null) {
                    //广告点击率
                    asinList.setClickRate(MathUtil.multiply(asinList.getClickRate(), RATE));
                }
                if (asinList.getSalesConversionRate() != null) {
                    //广告转化率
                    asinList.setSalesConversionRate(MathUtil.multiply(asinList.getSalesConversionRate(), RATE));
                }
                if (asinList.getTotalSales() != null && asinList.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    asinList.setAcos(MathUtil.multiply(asinList.getAcos(), RATE));
                }
                map.put(asinList.getTargetId(), asinList);
            }
        }
        return map;
    }

    /**
     * 查询店铺名称、广告组合名称、广告活动名称、广告组名称
     * @param puid
     * @param fillDataDto
     */
    private void fillData(Integer puid, AsinFillDataDto fillDataDto) {
        //获取店铺名称
        List<Integer> shopIds =  new ArrayList<>(fillDataDto.getShopIds());
        List<String> campaignIdList = fillDataDto.getCampaignIdList();
        List<String> spGroupIdList = fillDataDto.getSpGroupIdList();
        List<String> sbGroupIdList = fillDataDto.getSbGroupIdList();
        List<String> sdGroupIdList = fillDataDto.getSdGroupIdList();
        fillDataDto.setShopAuthMap(shopAuthDao.listAllByIds(puid, shopIds).
                parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity())));
        //获取广告活动名称
        fillDataDto.setCampaignAllMap(amazonAdCampaignAllDao.getNameByShopIdsAndCampaignIds(puid, shopIds, campaignIdList, null, null).
                parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity())));
        //获取广告组合名称
        List<String> portfolioIdList = fillDataDto.getCampaignAllMap().values().stream().map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
        List<KeywordLibsPortfolioListVO> portfolioListVo = portfolioService.getAllPortfolioName(puid, shopIds, portfolioIdList);
        fillDataDto.setPortfolioMap(portfolioListVo.parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(KeywordLibsPortfolioListVO::getPortfolioId, Function.identity())));
        //获取广告组名称
        //sp广告组名称
        if (CollectionUtils.isNotEmpty(spGroupIdList)) {
            fillDataDto.setSpAdGroupMap(amazonAdGroupDao.getNameByShopIdsAndGroupIds(puid, shopIds, spGroupIdList, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity())));
        }
        //sb广告组名称
        if (CollectionUtils.isNotEmpty(sbGroupIdList)) {
            fillDataDto.setSbAdGroupMap(amazonSbAdGroupDao.getNameListByShopIdsAndGroupIds(puid, shopIds, campaignIdList, sbGroupIdList, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity())));
        }
        //sd广告组名称
        if (CollectionUtils.isNotEmpty(sdGroupIdList)) {
            fillDataDto.setSdAdGroupMap(amazonSdAdGroupDao.getNameByShopIdsAndGroupIds(puid, shopIds, campaignIdList, sdGroupIdList, null).
                    parallelStream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, Function.identity())));
        }
        //处理sd投放
    }

    /**
     * 组装、聚合数据
     *
     * @param rows
     * @param fillDataDto
     * @param page
     */
    private void buildAllData(List<AsinLibsDetailVo> rows, AsinFillDataDto fillDataDto, Page<AsinLibsDetailVo> page) {
        rows.stream().filter(Objects::nonNull).peek(e -> {
            //基础数据
            AsinLibsDetailVo basicData;
            if (Constants.SP.equals(e.getType())) {
                basicData = fillDataDto.getSpDetailMap().get(e.getTargetId());
            } else if (Constants.SB.equals(e.getType())) {
                basicData = fillDataDto.getSbDetailMap().get(e.getTargetId());
            } else {
                basicData = fillDataDto.getSdDetailMap().get(e.getTargetId());
            }
            if (basicData != null) {
                e.setId(basicData.getId());
                e.setPuid(basicData.getPuid());
                e.setCreateTime(StringUtils.isNotBlank(basicData.getCreateTime()) && basicData.getCreateTime().length() > 19 ? basicData.getCreateTime().substring(0, 19) : basicData.getCreateTime());
                e.setMatchType(basicData.getMatchType());
                e.setState(basicData.getState());
                e.setShopId(basicData.getShopId());
                e.setCampaignId(basicData.getCampaignId());
                e.setAdGroupId(basicData.getAdGroupId());
                e.setMarketplaceId(basicData.getMarketplaceId());
                e.setBid(basicData.getBid());
                e.setMatchType(basicData.getMatchType());
                e.setIsArchived(basicData.getState().equals(AmazonAdGroup.stateEnum.archived.getCode()) || StringUtils.isBlank(basicData.getState()));
                e.setSelectType(Optional.ofNullable(basicData.getSelectType()).orElse("-"));
            }
            //店铺名称
            ShopAuth shop = fillDataDto.getShopAuthMap().get(e.getShopId());
            if (shop != null && StringUtils.isNotEmpty(shop.getName())) {
                e.setShopName(shop.getName());
            }
            //广告活动名称
            AmazonAdCampaignAll campaign = fillDataDto.getCampaignAllMap().get(e.getCampaignId());
            if (campaign != null && e.getType().equalsIgnoreCase(campaign.getType()) && StringUtils.isNotEmpty(campaign.getName())) {
                e.setCampaignName(campaign.getName());
            }
            //广告组合名称
            if (campaign != null && campaign.getPortfolioId() != null) {
                KeywordLibsPortfolioListVO portfolioListVO = fillDataDto.getPortfolioMap().get(campaign.getPortfolioId());
                if (portfolioListVO != null && StringUtils.isNotEmpty(portfolioListVO.getPortfolioName())) {
                    e.setPortfolioName(portfolioListVO.getPortfolioName());
                    e.setPortfolioId(portfolioListVO.getPortfolioId());
                    e.setIsHidden(portfolioListVO.getIsHidden());
                }
            }
            //广告组名称和竞价
            if (e.getAdGroupId() != null) {
                if (Constants.SP.equals(e.getType())) {
                    AmazonAdGroup adGroup = fillDataDto.getSpAdGroupMap().get(e.getAdGroupId());
                    if (adGroup != null && StringUtils.isNotEmpty(adGroup.getName())) {
                        e.setAdGroupName(adGroup.getName());
                        if (e.getBid() == null) {
                            e.setBid(adGroup.getDefaultBid());
                        }
                    }
                } else if (Constants.SB.equals(e.getType())) {
                    AmazonSbAdGroup adSbGroup = fillDataDto.getSbAdGroupMap().get(e.getAdGroupId());
                    if (adSbGroup != null && StringUtils.isNotEmpty(adSbGroup.getName())) {
                        e.setAdGroupName(adSbGroup.getName());
                        if (e.getBid() == null && adSbGroup.getBid() != null) {
                            e.setBid(adSbGroup.getBid().doubleValue());
                        }
                    }
                } else {
                    AmazonSdAdGroup adSdGroup = fillDataDto.getSdAdGroupMap().get(e.getAdGroupId());
                    if (adSdGroup != null && StringUtils.isNotEmpty(adSdGroup.getName())) {
                        e.setAdGroupName(adSdGroup.getName());
                        if (e.getBid() == null) {
                            e.setBid(adSdGroup.getDefaultBid().doubleValue());
                        }
                    }
                }
            }
            //报告数据
            AsinLibsDetailVo report = fillDataDto.getReportDataMap().get(e.getTargetId());
            if (report != null) {
                e.setImpressions(report.getImpressions());
                e.setClicks(report.getClicks());
                e.setClickRate(report.getClickRate().setScale(2, RoundingMode.HALF_UP));
                e.setSaleNum(report.getSaleNum());
                e.setSalesConversionRate(report.getSalesConversionRate().setScale(2, RoundingMode.HALF_UP));
                e.setTotalSales(report.getTotalSales().setScale(2, RoundingMode.HALF_UP));
                e.setCost(report.getCost().setScale(2, RoundingMode.HALF_UP));
                e.setCpc(report.getCpc().setScale(2, RoundingMode.HALF_UP));
                e.setCpa(report.getCpa().setScale(2, RoundingMode.HALF_UP));
                e.setAcos(report.getAcos().setScale(2, RoundingMode.HALF_UP));
                e.setRoas(report.getRoas().setScale(2, RoundingMode.HALF_UP));
            }
        }).collect(Collectors.toList());
        page.setRows(rows);
    }

    private Result<AddAsinLibVo> asinExistAndTagAndAsinTagHandler(Integer puid, Integer uid, List<AsinLibsVo> vo, List<Integer> uidList) {
        List<AdKeywordLibTag> tagsList = vo.get(0).getAdTags();
        if (CollectionUtils.isNotEmpty(tagsList) && tagsList.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
            return ResultUtil.error("单个ASIN最多可添加20个标签，请调整后重新添加");
        }
        List<AsinsLibMarkupAsinVo> asinsLibAsinTags = vo.get(0).getAsinTags();
        if (CollectionUtils.isNotEmpty(asinsLibAsinTags) && asinsLibAsinTags.size() > Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
            return ResultUtil.error("单个ASIN最多可添加20个asin标签，请调整后重新添加");
        }
        //校验入参中标签参数的有效性
        if (!this.checkTagExist(puid, uid, vo)) {
            return ResultUtil.error("您选择的标签不存在，请重新选择标签或刷新页面数据");
        }

        Map<String, List<AmazonAdAsinsLib>> existAsinsMap = new HashMap<>();
        Map<String, AdKeywordLibMarkupTagVo> existAdTagMap = new HashMap<>();
        Map<Long, List<AmazonAdAsinLibMarkupAsin>> existAdAsinTagMap = new HashMap<>();
        //根据用户信息获取用户已添加的ASIN LIB，TAG, ASIN TAG
        this.getExistLibAndTagAndAsinTag(puid, uid, uidList, vo, existAsinsMap, existAdTagMap, existAdAsinTagMap);

        List<AsinLibsVo> addList = new ArrayList<>();
        Set<String> tagReachedLimit = new HashSet<>();
        Set<String> asinReachedLimit = new HashSet<>();

        boolean existRepeatedAdded = false;
        // 校验tag/asinTag
        for (AsinLibsVo e : vo) {
            List<AmazonAdAsinsLib> existLib = existAsinsMap.get(e.getAsin().toLowerCase());
            // 是否为数据库已存在的ASIN
            if (existLib != null && !existRepeatedAdded) {
                existRepeatedAdded = true;
            }
            if (existLib == null) {
                addList.add(e);
                continue;
            }
            // 代表自己没有添加过这个词
            if (existLib.parallelStream().noneMatch(a -> uid.equals(a.getUid()))) {
                addList.add(e);
                continue;
            }
            boolean isAdd = false;
            //请求中的tag
            Set<Long> addTagIds = ListUtils.emptyIfNull(e.getAdTags()).stream().map(AdKeywordLibTag::getId).collect(Collectors.toSet());
            Set<Long> tagIds = getTag(existLib, existAdTagMap);//该ASIN已经添加过的TAG ID
            tagIds.addAll(addTagIds);//添加本次要添加的TAG ID
            if (tagIds.size() <= Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                isAdd = true;
            } else {
                tagReachedLimit.add(e.getAsin());
            }
            //请求中的asinTag
            Set<String> asinTagIds = ListUtils.emptyIfNull(e.getAsinTags()).stream().map(AsinsLibMarkupAsinVo::getAsin).collect(Collectors.toSet());
            Set<String> asinTag = getAsinTag(existLib, existAdAsinTagMap);
            asinTag.addAll(asinTagIds);
            if (asinTag.size() <= Constants.KEYWORD_LIB_TAG_MAX_SIZE) {
                isAdd = true;
            } else {
                asinReachedLimit.add(e.getAsin());
            }
            if (isAdd) {
                addList.add(e);
            }
        }

        List<String> success = addList.stream()
                .map(AsinLibsVo::getAsin)
                .filter(e -> !tagReachedLimit.contains(e) && !asinReachedLimit.contains(e))
                .collect(Collectors.toList());

        return ResultUtil.success(AddAsinLibVo.builder()
                .addVos(addList)
                .success(success)
                .tagReachedLimit(new ArrayList<>(tagReachedLimit))
                .asinReachedLimit(new ArrayList<>(asinReachedLimit))
                .existRepeatedAdded(existRepeatedAdded)
                .build());
    }

    private Set<Long> getTag(List<AmazonAdAsinsLib> asinLib, Map<String, AdKeywordLibMarkupTagVo> adAsinMap) {
        Set<Long> tagIds = new HashSet<>();
        for (AmazonAdAsinsLib asinLibsVo : asinLib) {
            AdKeywordLibMarkupTagVo adKeywordLibMarkupTagVo = adAsinMap.get(asinLibsVo.getId().toString());
            if (adKeywordLibMarkupTagVo != null) {
                if (CollectionUtils.isNotEmpty(adKeywordLibMarkupTagVo.getTagIds())) {
                    tagIds.addAll(adKeywordLibMarkupTagVo.getTagIds());
                }
            }
        }
        return tagIds;
    }

    private Set<String> getAsinTag(List<AmazonAdAsinsLib> asinLib, Map<Long, List<AmazonAdAsinLibMarkupAsin>> map) {
        Set<String> asinTags = new HashSet<>();
        for (AmazonAdAsinsLib asinLibsVo : asinLib) {
            List<AmazonAdAsinLibMarkupAsin> asinLibMarkupTagVo = map.get(asinLibsVo.getId());
            if (CollectionUtils.isNotEmpty(asinLibMarkupTagVo)) {
                asinTags.addAll(asinLibMarkupTagVo.stream().map(AmazonAdAsinLibMarkupAsin::getAsin).filter(StringUtils::isNotBlank).collect(Collectors.toList()));
            }
        }
        return asinTags;
    }

    private void addAsinLibTagAndAsinTag(Integer puid, Integer uid,
                                         List<AmazonAdAsinsLib> asinCreated, List<AdKeywordLibTag> asinLibTags,
                                         List<AsinsLibMarkupAsinVo> asinLibAsinTags, AddAsinLibVo addAsinLibVo) {
        // 标记标签
        List<AdKeywordLibMarkupTag> asinLibMarkupTags = Lists.newArrayList();
        //标记ASIN标签
        List<AmazonAdAsinLibMarkupAsin> asinLibMarkupAsins = Lists.newArrayList();

        if (CollectionUtils.isNotEmpty(asinLibTags)) {
            //根据请求中的标签id，查询标签信息，得到要添加的个人标签和公共标签
            //由于目前是关键词库和ASIN库共用一套标签，所有的标签类型设置为了keywordLib，但是关系是区分keywordLib和asinLib进行保存的
            Map<Long, AdKeywordLibTag> adAsinLibTags = adKeywordLibTagDao.listByUidAndIdAndType(puid, uid, AdTagTypeEnum.KEYWORDLIB.getType(),
                    asinLibTags.stream().map(AdKeywordLibTag::getId).collect(Collectors.toList())).stream()
                    .collect(Collectors.toMap(AdKeywordLibTag::getId, Function.identity(), (o, n) -> n));

            //组装标签关系表实体类
            asinCreated.stream()
                    .filter(e -> {
                        boolean flag = Objects.nonNull(e.getId());
                        if (!flag) {
                            log.error("asin lib id is null, e:{}", JSONObject.toJSONString(e));
                        }
                        return flag;
                    })
                    .filter(e -> {
                        boolean flag = true;
                        if (Objects.nonNull(addAsinLibVo.getTagNeedModify())) {
                            flag = !addAsinLibVo.getTagNeedModify().contains(e.getAsin());
                        }
                        if (flag && Objects.nonNull(addAsinLibVo.getTagReachedLimit())) {
                            flag = !addAsinLibVo.getTagReachedLimit().contains(e.getAsin());
                        }
                        return flag;
                    })
                    .forEach(e -> asinLibTags.forEach(item -> {
                        AdKeywordLibMarkupTag asinLibMarkupTag = new AdKeywordLibMarkupTag();
                        asinLibMarkupTag.setPuid(puid);
                        asinLibMarkupTag.setUid(Optional.ofNullable(adAsinLibTags.get(item.getId())).map(AdKeywordLibTag::getUid).orElse(0));
                        asinLibMarkupTag.setType(AdTagTypeEnum.ASINLIB.getType());
                        asinLibMarkupTag.setCreateId(uid);
                        asinLibMarkupTag.setUpdateId(uid);
                        asinLibMarkupTag.setTagId(item.getId());
                        asinLibMarkupTag.setRelationId(String.valueOf(e.getId()));
                        asinLibMarkupTags.add(asinLibMarkupTag);
                    }));
        }

        //组装ASIN标签实体类
        if (CollectionUtils.isNotEmpty(asinLibAsinTags)) {
            asinCreated.stream()
                    .filter(e -> {
                        boolean flag = Objects.nonNull(e.getId());
                        if (!flag) {
                            log.error("asin lib id is null, e:{}", JSONObject.toJSONString(e));
                        }
                        return flag;
                    })
                    .filter(e -> {
                        boolean flag = true;
                        if (Objects.nonNull(addAsinLibVo.getAsinReachedLimit())) {
                            flag = !addAsinLibVo.getAsinReachedLimit().contains(e.getAsin());
                        }
                        if (flag && Objects.nonNull(addAsinLibVo.getAsinNeedModify())) {
                            flag = !addAsinLibVo.getAsinNeedModify().contains(e.getAsin());
                        }
                        return flag;
                    })
                    .forEach(e -> asinLibAsinTags.forEach(item -> {
                        AmazonAdAsinLibMarkupAsin adAsinLibMarkupAsin = new AmazonAdAsinLibMarkupAsin();
                        adAsinLibMarkupAsin.setPuid(puid);
                        adAsinLibMarkupAsin.setUid(uid);
                        adAsinLibMarkupAsin.setAsinLibId(e.getId());
                        adAsinLibMarkupAsin.setAsin(item.getAsin());
                        adAsinLibMarkupAsin.setMarketplaceId(item.getMarketplaceId());
                        adAsinLibMarkupAsin.setCreateId(uid);
                        adAsinLibMarkupAsin.setUpdateId(uid);
                        asinLibMarkupAsins.add(adAsinLibMarkupAsin);
                    }));
        }

        if (CollectionUtils.isNotEmpty(asinLibMarkupTags)) {
            adKeywordLibMarkupTagDao.insertList(asinLibMarkupTags);
        }
        if (CollectionUtils.isNotEmpty(asinLibMarkupAsins)) {
            asinsLibMarkupAsinDao.batchInsert(asinLibMarkupAsins);
        }
    }

    private void getAsinPageOrderByReport(PageListAsinsRequest request, List<Long> asinQueryIdList,
                                          List<Integer> uidList, Page<AsinLibsVo> asinPage,
                                          String startDate, String endDate) {
        //过滤列表查询条件
        List<String> filterdAsinList = amazonAdAsinsLibDao.getAsinByQueryFilter(uidList, request, asinQueryIdList, startDate, endDate);
        //查询报告获取所有关键词需要排序的指标值，并在内存中进行汇总排序和分页，计算出分页后需要展示的asin
        // 没有词 截断
        if (CollectionUtils.isEmpty(filterdAsinList)) {
            asinPage.setRows(new ArrayList<>());
            return;
        }
        Map<String, AdAsinOrderBo> map = new HashMap<>();
        Map<String, AdAsinOrderBo> voMap = new HashMap<>();
        //当asin超过1万时，查询会报异常，且无法转bitmap进行查询，采用分片查询
        for (List<String> partitionList : ListUtils.partition(filterdAsinList, DEFAULT_PARTITION_SIZE)) {
            List<AdAsinOrderBo> spOrderList = odsCpcTargetingReportDao.getOrderFieldByAsins(request.getPuid(), request.getShopListList(), request, partitionList);
            List<AdAsinOrderBo> sbOrderList = odsAmazonAdSbTargetingReportDao.getOrderFieldByAsins(request.getPuid(), request.getShopListList(), request, partitionList);
            List<AdAsinOrderBo> sdOrderList = odsAmazonAdSdTargetingReportDao.getOrderFieldByAsins(request.getPuid(), request.getShopListList(), request, partitionList);
            for (String asin : partitionList) {
                map.put(asin, null);
            }

            //合并两份报告数据的查询字段的数据
            voMap = this.mergeOrderData(map, spOrderList);
            voMap = this.mergeOrderData(voMap, handlerAsin(() -> sbOrderList));
            voMap = this.mergeOrderData(voMap, handlerAsin(() -> sdOrderList));
        }


        //根据查询出来的字段进行计算，放入到orderFiled中
        List<AdAsinOrderBo> orderWithValueList = new ArrayList<>();
        for (String filterAsin : filterdAsinList) {
            AdAsinOrderBo orderBo = new AdAsinOrderBo();
            orderBo.setAsin(filterAsin);
            if (voMap.get(filterAsin) != null) {
                AdAsinOrderBo value = voMap.get(filterAsin);
                if (KeywordDataFieldEnum.IMPRESSIONS.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(BigDecimal.valueOf(value.getImpressions()));
                } else if (KeywordDataFieldEnum.CLICKS.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(BigDecimal.valueOf(value.getClicks()));
                } else if (KeywordDataFieldEnum.CLICK_RATE.getCode().equals(request.getOrderField())) {
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(value.getClicks()), BigDecimal.valueOf(value.getImpressions()));
                    BigDecimal orderField = MathUtil.multiply(divide, RATE);
                    orderBo.setOrderField(orderField);
                } else if (KeywordDataFieldEnum.COST.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(value.getCost());
                } else if (KeywordDataFieldEnum.ORDER_NUM.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(BigDecimal.valueOf(value.getAdOrder()));
                } else if (KeywordDataFieldEnum.CONVERSION_RATE.getCode().equals(request.getOrderField())) {
                    BigDecimal orderField = MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(value.getAdOrder()), BigDecimal.valueOf(value.getClicks())), RATE);
                    orderBo.setOrderField(orderField);
                } else if (KeywordDataFieldEnum.TOTAL_SALES.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(value.getAdSales());
                } else if (KeywordDataFieldEnum.CPC.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(MathUtil.divideCompatibleZero(value.getCost(), BigDecimal.valueOf(value.getClicks())));
                } else if (KeywordDataFieldEnum.CPA.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(MathUtil.divideCompatibleZero(value.getCost(), BigDecimal.valueOf(value.getAdOrder())));
                } else if (KeywordDataFieldEnum.ACOS.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(MathUtil.multiply(MathUtil.divideCompatibleZero(value.getCost(), value.getAdSales()), RATE));
                } else if (KeywordDataFieldEnum.ROAS.getCode().equals(request.getOrderField())) {
                    orderBo.setOrderField(MathUtil.multiply(MathUtil.divideCompatibleZero(value.getAdSales(), value.getCost()), RATE));
                }
            }
            orderWithValueList.add(orderBo);
        }

        //对合并后的数据进行排序，根据orderFiled字段里的数据进行排序
        List<AdAsinOrderBo> orderList = AdPageUtil.getOrderPage(request.getPageNo(), request.getPageSize(), orderWithValueList, request.getOrderType(), asinPage);
        List<AsinLibsVo> asinList = orderList.stream().map(order -> {
            AsinLibsVo vo = new AsinLibsVo();
            vo.setAsin(order.getAsin());
            return vo;
        }).collect(Collectors.toList());
        asinPage.setRows(asinList);
    }

    private Map<String, AdAsinOrderBo> mergeOrderData(Map<String, AdAsinOrderBo> map, List<AdAsinOrderBo> toBeMergeList) {
        for (AdAsinOrderBo asinBo : toBeMergeList) {
            AdAsinOrderBo existingAsin = map.get(asinBo.getAsin());
            if (existingAsin != null) {
                if (asinBo.getImpressions() != null) {
                    //曝光量
                    existingAsin.setImpressions(existingAsin.getImpressions() + asinBo.getImpressions());
                }
                if (asinBo.getAdSales() != null) {
                    //广告销售额
                    existingAsin.setAdSales(existingAsin.getAdSales().add(asinBo.getAdSales()));
                }
                if (asinBo.getClicks() != null) {
                    //广告点击量
                    existingAsin.setClicks(existingAsin.getClicks() + asinBo.getClicks());
                }
                if (existingAsin.getAdOrder() != null) {
                    //广告订单量
                    existingAsin.setAdOrder(existingAsin.getAdOrder() + asinBo.getAdOrder());
                }
                if (asinBo.getCost() != null) {
                    //广告花费
                    existingAsin.setCost(existingAsin.getCost().add(asinBo.getCost()));
                }
                map.put(asinBo.getAsin(), existingAsin);
            } else {
                map.put(asinBo.getAsin(), asinBo);
            }
        }
        return map;
    }

    private Map<String, AsinLibsDetailVo> mergeAsinRreportData(Map<String, AsinLibsDetailVo> map, List<AsinLibsDetailVo> toBeMergeList) {
        for (AsinLibsDetailVo asinInfo : toBeMergeList) {
            AsinLibsDetailVo existAsin = map.get(asinInfo.getAsin().toLowerCase());
            if (existAsin != null) {
                if (asinInfo.getImpressions() != null) {
                    //曝光量
                    existAsin.setImpressions(existAsin.getImpressions() + asinInfo.getImpressions());
                }
                if (asinInfo.getTotalSales() != null) {
                    //广告销售额
                    existAsin.setTotalSales(existAsin.getTotalSales().add(asinInfo.getTotalSales()));
                }
                if (asinInfo.getClicks() != null) {
                    //广告点击量
                    existAsin.setClicks(existAsin.getClicks() + asinInfo.getClicks());
                }
                if (existAsin.getImpressions() != null && existAsin.getImpressions() != 0) {
                    //广告点击率
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(existAsin.getClicks()), BigDecimal.valueOf(existAsin.getImpressions()));
                    existAsin.setClickRate(MathUtil.multiply(divide, RATE));
                }
                if (existAsin.getSaleNum() != null) {
                    //广告订单量
                    existAsin.setSaleNum(existAsin.getSaleNum() + asinInfo.getSaleNum());
                }
                if (existAsin.getSaleNum() != null && existAsin.getClicks() != null && existAsin.getClicks() != 0) {
                    //广告转化率
                    existAsin.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(existAsin.getSaleNum()), BigDecimal.valueOf(existAsin.getClicks())), RATE));
                }
                if (asinInfo.getCost() != null) {
                    //广告花费
                    existAsin.setCost(existAsin.getCost().add(asinInfo.getCost()));
                }
                if (existAsin.getCost() != null && existAsin.getClicks() != null && existAsin.getClicks() != 0) {
                    //cpc
                    existAsin.setCpc(MathUtil.divideCompatibleZero(existAsin.getCost(), BigDecimal.valueOf(existAsin.getClicks())));
                }
                if (existAsin.getSaleNum() != null && existAsin.getSaleNum() != 0) {
                    //cpa
                    existAsin.setCpa(MathUtil.divideCompatibleZero(existAsin.getCost(), BigDecimal.valueOf(existAsin.getSaleNum())));
                }
                if (existAsin.getTotalSales() != null && existAsin.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    existAsin.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(existAsin.getCost(), existAsin.getTotalSales()), RATE));
                }
                if (existAsin.getCost() != null && existAsin.getCost().compareTo(BigDecimal.ZERO) != 0) {
                    //roas
                    existAsin.setRoas(MathUtil.divideCompatibleZero(existAsin.getTotalSales(), existAsin.getCost()));
                }
                map.put(asinInfo.getAsin().toLowerCase(), existAsin);
            } else {
                if (asinInfo.getImpressions() != null && asinInfo.getImpressions() != 0) {
                    //广告点击率
                    BigDecimal divide = MathUtil.divideCompatibleZero(BigDecimal.valueOf(asinInfo.getClicks()), BigDecimal.valueOf(asinInfo.getImpressions()));
                    asinInfo.setClickRate(MathUtil.multiply(divide, RATE));
                }
                if (asinInfo.getClicks() != null && asinInfo.getClicks() != 0) {
                    //广告转化率
                    asinInfo.setSalesConversionRate(MathUtil.multiply(MathUtil.divideCompatibleZero(BigDecimal.valueOf(asinInfo.getSaleNum()), BigDecimal.valueOf(asinInfo.getClicks())), RATE));
                }
                if (asinInfo.getClicks() != null && asinInfo.getClicks() != 0) {
                    //cpc
                    asinInfo.setCpc(MathUtil.divideCompatibleZero(asinInfo.getCost(), BigDecimal.valueOf(asinInfo.getClicks())));
                }
                if (asinInfo.getSaleNum() != null && asinInfo.getSaleNum() != 0) {
                    //cpa
                    asinInfo.setCpa(MathUtil.divideCompatibleZero(asinInfo.getCost(), BigDecimal.valueOf(asinInfo.getSaleNum())));
                }
                if (asinInfo.getTotalSales() != null && asinInfo.getTotalSales().compareTo(BigDecimal.ZERO) != 0) {
                    //acos
                    asinInfo.setAcos(MathUtil.multiply(MathUtil.divideCompatibleZero(asinInfo.getCost(), asinInfo.getTotalSales()), RATE));
                }
                if (asinInfo.getCost() != null && asinInfo.getCost().compareTo(BigDecimal.ZERO) != 0) {
                    //roas
                    asinInfo.setRoas(MathUtil.divideCompatibleZero(asinInfo.getTotalSales(), asinInfo.getCost()));
                }
                map.put(asinInfo.getAsin().toLowerCase(), asinInfo);
            }
        }
        return map;
    }

    private void buildAsinLibsPageVo(PageListAsinsRequest param, Map<String, Integer> asinCountMap,
                                     Map<String, Integer> neAsinCountMap,
                                     Map<String, AsinLibsDetailVo> reportDataMap,
                                     Map<String, AsinLibsDetailVo> reportCompDataMap,
                                     Map<String, List<AmazonAdAsinsLib>> asinLibBasicInfoMap,
                                     AsinLibsVo vo, Map<Integer, User> mapUser) {
        vo.setPuid(param.getPuid());
        vo.setUid(param.getUid());
        List<AmazonAdAsinsLib> asinLibVoList = asinLibBasicInfoMap.get(vo.getAsin().toLowerCase());
        // 按照Id 由小到大排序
        asinLibVoList.sort(Comparator.comparing(AmazonAdAsinsLib::getId));
        AmazonAdAsinsLib asinLibsVo1 = asinLibVoList.get(0);
        // 查找是否有自己添加的关键词
        Optional<AmazonAdAsinsLib> listUser = asinLibVoList.stream().filter(a -> param.getUid() == a.getUid()).findFirst();
        vo.setId(listUser.isPresent() ? listUser.get().getId() : 0L);
        vo.setRemark(listUser.isPresent() ? listUser.get().getRemark() : "-");
        vo.setCreateTime((new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(listUser.orElse(asinLibsVo1).getCreateTime())));
        vo.setSource(listUser.orElse(asinLibsVo1).getSource());
        vo.setState(listUser.orElse(asinLibsVo1).getState());
        vo.setItemVos(asinLibVoList.stream().map(k -> {
            AsinLibsVo.AsinLibsItemVo itemVo = new AsinLibsVo.AsinLibsItemVo();
            itemVo.setId(k.getId());
            itemVo.setUid(k.getUid());
            User user = mapUser.get(k.getUid());
            if (user != null) {
                itemVo.setName(user.getUserNickname());
            }
            itemVo.setAddDate(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(k.getCreateTime()));
            itemVo.setSource(k.getSource());
            itemVo.setRemark(k.getRemark());
            return itemVo;
        }).collect(Collectors.toList()));

        vo.setTargetNum(asinCountMap.get(vo.getAsin().toLowerCase()));
        vo.setNegateTargetNum(neAsinCountMap.get(vo.getAsin().toLowerCase()));
        //报告数据
        AsinLibsDetailVo reportVo = reportDataMap.get(vo.getAsin().toLowerCase());
        if (reportVo != null) {
            if (reportVo.getImpressions() != null) {
                vo.setImpressions(reportVo.getImpressions());
            }
            if (reportVo.getClicks() != null) {
                vo.setClicks(reportVo.getClicks());
            }
            if (reportVo.getSaleNum() != null) {
                vo.setSaleNum(reportVo.getSaleNum());
            }
            if (reportVo.getTotalSales() != null) {
                vo.setTotalSales(reportVo.getTotalSales());
            }
            if (reportVo.getCost() != null) {
                vo.setCost(reportVo.getCost());
            }
            if (reportVo.getCpc() != null) {
                vo.setCpc(reportVo.getCpc());
            }
            if (reportVo.getCpa() != null) {
                vo.setCpa(reportVo.getCpa());
            }
            if (reportVo.getClickRate() != null) {
                vo.setClickRate(reportVo.getClickRate());
            }
            if (reportVo.getSalesConversionRate() != null) {
                vo.setSalesConversionRate(reportVo.getSalesConversionRate());
            }
            if (reportVo.getAcos() != null) {
                vo.setAcos(reportVo.getAcos());
            }
            if (reportVo.getRoas() != null) {
                vo.setRoas(reportVo.getRoas());
            }
        }
        //对比报告数据
        AsinLibsDetailVo compareReportVo = reportCompDataMap.get(vo.getAsin().toLowerCase());
        if (compareReportVo != null) {
            if (compareReportVo.getImpressions() != null) {
                vo.setCompareImpressions(compareReportVo.getImpressions());
            }
            if (compareReportVo.getClicks() != null) {
                vo.setCompareClicks(compareReportVo.getClicks());
            }
            if (compareReportVo.getSaleNum() != null) {
                vo.setCompareSaleNum(compareReportVo.getSaleNum());
            }
            if (compareReportVo.getTotalSales() != null) {
                vo.setCompareTotalSales(compareReportVo.getTotalSales());
            }
            if (compareReportVo.getCost() != null) {
                vo.setCompareCost(compareReportVo.getCost());
            }
            if (compareReportVo.getCpc() != null) {
                vo.setCompareCpc(compareReportVo.getCpc());
            }
            if (compareReportVo.getCpa() != null) {
                vo.setCompareCpa(compareReportVo.getCpa());
            }
            if (compareReportVo.getClickRate() != null) {
                vo.setCompareClickRate(compareReportVo.getClickRate());
            }
            if (compareReportVo.getSalesConversionRate() != null) {
                vo.setCompareSalesConversionRate(compareReportVo.getSalesConversionRate());
            }
            if (compareReportVo.getAcos() != null) {
                vo.setCompareAcos(compareReportVo.getAcos());
            }
            if (compareReportVo.getRoas() != null) {
                vo.setCompareRoas(compareReportVo.getRoas());
            }
            //差异数据报告
            vo.setDiffImpressions(new BigDecimal(vo.getCompareImpressions()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(vo.getImpressions()).subtract(new BigDecimal(vo.getCompareImpressions())).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getCompareImpressions()), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffClicks(new BigDecimal(vo.getCompareClicks()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(vo.getClicks()).subtract(new BigDecimal(vo.getCompareClicks())).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getCompareClicks()), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffSaleNum(new BigDecimal(vo.getCompareSaleNum()).compareTo(BigDecimal.ZERO) == 0 ? "-" : new BigDecimal(vo.getSaleNum()).subtract(new BigDecimal(vo.getCompareSaleNum())).multiply(new BigDecimal(100))
                    .divide(new BigDecimal(vo.getCompareSaleNum()), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffTotalSales(vo.getCompareTotalSales().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getTotalSales().subtract(vo.getCompareTotalSales()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareTotalSales(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffCost(vo.getCompareCost().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getCost().subtract(vo.getCompareCost()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareCost(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffCpc(vo.getCompareCpc().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getCpc().subtract(vo.getCompareCpc()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareCpc(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffCpa(vo.getCompareCpa().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getCpa().subtract(vo.getCompareCpa()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareCpa(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffClickRate(vo.getCompareClickRate().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getClickRate().subtract(vo.getCompareClickRate()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareClickRate(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffSalesConversionRate(vo.getCompareSalesConversionRate().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getSalesConversionRate().subtract(vo.getCompareSalesConversionRate()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareSalesConversionRate(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffAcos(vo.getCompareAcos().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getAcos().subtract(vo.getCompareAcos()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareAcos(), 2, RoundingMode.HALF_UP).toPlainString());
            vo.setDiffRoas(vo.getCompareRoas().compareTo(BigDecimal.ZERO) == 0 ? "-" : vo.getRoas().subtract(vo.getCompareRoas()).multiply(new BigDecimal(100))
                    .divide(vo.getCompareRoas(), 2, RoundingMode.HALF_UP).toPlainString());
        }
    }

    private void fillAdTagData(Integer puid, Integer uid, List<AsinLibsVo> rows) {
        if (CollectionUtils.isEmpty(rows)) {
            return;
        }
        List<String> asinIdList = new ArrayList<>();
        for (AsinLibsVo asinLibsVo : rows) {
            asinIdList.addAll(asinLibsVo.getItemVos().stream().map(AsinLibsVo.AsinLibsItemVo::getId).map(String::valueOf).collect(Collectors.toSet()));
        }
        List<AdKeywordLibMarkupTag> relationVos = adKeywordLibMarkupTagDao.getMarkupTagByRelationIdAndCreateId(puid, uid, AdTagTypeEnum.ASINLIB.getType(), asinIdList);
        if (CollectionUtils.isEmpty(relationVos)) {
            return;
        }
        List<Long> collect = relationVos.stream().map(AdKeywordLibMarkupTag::getTagId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<AdKeywordLibTag> byLongIdList = adKeywordLibTagDao.getListByLongIdList(puid, collect);
        if (CollectionUtils.isEmpty(byLongIdList)) {
            return;
        }
        //按tagId进行分组
        Map<Long, AdKeywordLibTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdKeywordLibTag::getId, Function.identity(), (e1, e2) -> e2));
        // 根据标签-asin_lib_id分组
        Map<Long, Set<Long>> tagMap = relationVos.stream().collect(Collectors.groupingBy(AdKeywordLibMarkupTag::getTagId, Collectors.mapping(k -> Long.parseLong(k.getRelationId()), Collectors.toSet())));

        // asin_lib_id tag_id进行分组
        Map<Long, Set<Long>> relationMap = relationVos.stream().collect(Collectors.groupingBy(k -> Long.parseLong(k.getRelationId()), Collectors.mapping(AdKeywordLibMarkupTag::getTagId, Collectors.toSet())));
        for (AsinLibsVo vo : rows) {
            if (CollectionUtils.isEmpty(vo.getItemVos())) {
                continue;
            }
            Set<Long> tagIds = new HashSet<>();
            for (AsinLibsVo.AsinLibsItemVo asinLibsItemVo : vo.getItemVos()) {
                Set<Long> set = relationMap.get(asinLibsItemVo.getId());
                if (set == null) {
                    continue;
                }
                tagIds.addAll(set);
            }
            if (CollectionUtils.isEmpty(tagIds)) {
                continue;
            }
            Set<Long> asinLibIdSet = vo.getItemVos().stream().map(AsinLibsVo.AsinLibsItemVo::getId).collect(Collectors.toSet());
            // 仅展示5条标签
            List<AdKeywordLibTag> collect1 = tagIds.stream().map(adTagMap::get).filter(Objects::nonNull).limit(Constants.KEYWORD_LIB_TAG_MAX_SIZE).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(collect1)) {
                // 设置tag下面的主键Id
                collect1.forEach(k -> {
                    Set<Long> set = tagMap.get(k.getId());
                    if (set == null) {
                        return;
                    }
                    Set<Long> retainSet = new HashSet<>(set);
                    retainSet.retainAll(asinLibIdSet);
                    if (CollectionUtils.isNotEmpty(retainSet)) {
                        k.setIdList(new ArrayList<>(retainSet));
                    }
                });
            }
            vo.setAdTags(collect1);
        }
    }

    private void fillAsinTagList(Integer puid, List<Integer> uid, List<AsinLibsVo> asinLibsVoList) {
        if (CollectionUtils.isEmpty(asinLibsVoList)) {
            return;
        }
        Set<Long> asinLibIdSet = new HashSet<>();
        for (AsinLibsVo asinLibsVo : asinLibsVoList) {
            asinLibIdSet.addAll(asinLibsVo.getItemVos().stream().map(AsinLibsVo.AsinLibsItemVo::getId).collect(Collectors.toSet()));
        }
        List<Long> asinLibIdList = new ArrayList<>(asinLibIdSet);

        //根据asin_lib_id获取所有对应的ASIN TAG
        List<AmazonAdAsinLibMarkupAsin> markupAsinList = asinsLibMarkupAsinDao.listByUidAndAsinIds(puid, uid, asinLibIdList);
        if (CollectionUtils.isEmpty(markupAsinList)) {
            return;
        }
        Map<String, Set<Long>> map = markupAsinList.stream().collect(Collectors.groupingBy(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId(), Collectors.mapping(AmazonAdAsinLibMarkupAsin::getAsinLibId, Collectors.toSet())));
        Map<Long, List<AmazonAdAsinLibMarkupAsin>> markupAsinMap = markupAsinList.stream().collect(Collectors.groupingBy(AmazonAdAsinLibMarkupAsin::getAsinLibId));
        for (AsinLibsVo asinLibsVo : asinLibsVoList) {
            Set<String> asinWithMarkplaceIdSet = new HashSet<>();
            for (AsinLibsVo.AsinLibsItemVo asinLibsItemVo : asinLibsVo.getItemVos()) {
                List<AmazonAdAsinLibMarkupAsin> list = markupAsinMap.get(asinLibsItemVo.getId());
                if (CollectionUtils.isNotEmpty(list)) {
                    asinWithMarkplaceIdSet.addAll(list.stream().map(k -> k.getAsin() + StringUtil.SPECIAL_COMMA + k.getMarketplaceId()).collect(Collectors.toSet()));
                }
            }
            Set<Long> asinLibIds = asinLibsVo.getItemVos().stream().map(AsinLibsVo.AsinLibsItemVo::getId).collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(asinWithMarkplaceIdSet)) {
                asinLibsVo.setAsinTags(asinWithMarkplaceIdSet.stream().map(e -> {
                    String[] asinArray = e.split(StringUtil.SPECIAL_COMMA);
                    AsinsLibMarkupAsinVo vo = new AsinsLibMarkupAsinVo();
                    vo.setAsin(asinArray[0]);
                    vo.setMarketplaceId(asinArray[1]);
                    vo.setMarketplaceCN(AmznEndpoint.getByMarketplaceId(asinArray[1]).getMarketplaceCN());
                    Set<Long> set = map.get(e);
                    if (set != null) {
                        Set<Long> retainSet = new HashSet<>(set);
                        retainSet.retainAll(asinLibIds);
                        if (CollectionUtils.isNotEmpty(retainSet)) {
                            vo.setIds(new ArrayList<>(retainSet));
                        }
                    }
                    return vo;
                }).collect(Collectors.toList()));
            }
        }
    }

    /**
     * 广告组合 和 名称 过滤广告活动
     */
    private void filterPortfolio(AsinLibsParam param) {
        if (CollectionUtils.isNotEmpty(param.getPortfolioList()) || StringUtils.isNotBlank(param.getSearchVal())) {
            String portfolioId = CollectionUtils.isNotEmpty(param.getPortfolioList()) ? String.join(",", param.getPortfolioList()) : "";
            //广告组合id不为空
            List<String> campaignIds = new ArrayList<>();
            if (Constants.BIDDABLE.equalsIgnoreCase(param.getTargetType())) {
                campaignIds = amazonAdCampaignAllDao.getCampaignIdsAndShopIdsByPortfolioId(param.getPuid(), param.getShopIds(), portfolioId, param.getSearchVal());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    param.setCampaignList(campaignIds);
                } else {
                    // 此处可以直接终结 不查询
                    param.setCampaignList(Lists.newArrayList("-1"));
                }
            } else {
                List<CampaignTypeDto> campaigns = amazonAdCampaignAllDao.getCampaignIdsAndTypeByPortfolioId(param.getPuid(), param.getShopIds(), portfolioId, param.getSearchVal());
                campaignIds = campaigns.stream().map(CampaignTypeDto::getCampaignId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    param.setCampaignList(campaignIds);
                    //通过sd广告活动id查询出sd广告组id
                    List<String> sdCampaignIds = campaigns.stream()
                            .filter(campaignTypeDto -> "sd".equals(campaignTypeDto.getType()))
                            .map(CampaignTypeDto::getCampaignId)
                            .collect(Collectors.toList());
                    List<AmazonSdAdGroup> groupIds = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(sdCampaignIds)) {
                        groupIds = amazonSdAdGroupDao.getNameByShopIdsAndGroupIds(param.getPuid(), param.getShopIds(), sdCampaignIds, null, null);
                    }
                    if (CollectionUtils.isNotEmpty(groupIds)) {
                        param.setAdGroupList(groupIds.stream().map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList()));
                    }
                } else {
                    // 此处可以直接终结 不查询
                    param.setCampaignList(Lists.newArrayList("-1"));
                }
            }
        }
    }

    private Result<AddAsinLibVo> checkCreateAsinInReq(Integer puid, List<AsinLibsVo> voList) {
        //过滤请求参数中长度不符合要求的ASIN
        List<String> asinListReq = voList.stream().filter(a -> StringUtil.isNotEmpty(a.getAsin())
                && a.getAsin().length() <= Constants.ASIN_LENGTH_LIMIT_TEN).map(AsinLibsVo::getAsin).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(voList)) {
            return ResultUtil.error("没有符合要求的ASIN，请重新添加");
        }

        //先校验入参单次添加的ASIN数量，单次不能超过1000
        if (voList.size() > Constants.ASIN_LIMIT_REQ_ONE_THOUSAND) {
            return ResultUtil.error("单次添加的ASIN数量不能超过1000");
        }
        if (!keywordsLibAndAsinLibLimitService.checkAddCountLimit(puid, asinListReq)) {
            return ResultUtil.error("用户添加的关键词和ASIN总数超过限制");
        }
        return null;
    }

    private List<AmazonAdAsinsLib> insertAsin(Integer puid, Integer uid, AddAsinLibVo asinInfoListVo) {
        List<AmazonAdAsinsLib> poList = new ArrayList<>();
        List<String> asinList = new ArrayList<>();
        for (AsinLibsVo vo : asinInfoListVo.getAddVos()) {
            AmazonAdAsinsLib po = AmazonAdAsinsLib.builder()
                    .puid(puid)
                    .uid(vo.getUid())
                    .asin(vo.getAsin().toUpperCase())
                    .state(Constants.ENABLED)
                    .createId(uid)
                    .updateId(uid)
                    .build();
            Optional.ofNullable(vo.getRemark()).filter(StringUtils::isNotEmpty).ifPresent(po::setRemark);
            Optional.ofNullable(vo.getSource()).filter(StringUtils::isNotEmpty).ifPresent(po::setSource);
            asinList.add(vo.getAsin().toUpperCase());
            poList.add(po);
        }
        amazonAdAsinsLibDao.insertOnDuplicateKeyUpdate(puid, poList);
        if (CollectionUtils.isNotEmpty(asinList)) {
            //再反查一遍id，应该如果只使用插入时返回的id，可能会存在
            //连续提交时，只更新了时间，但是没有插入，所以没有返回id
            return amazonAdAsinsLibDao.listByUidListAndAsins(puid, Collections.singletonList(uid), asinList);
        }
        return null;
    }

    private void getExistLibAndTagAndAsinTag(Integer puid, Integer uid,
                                             List<Integer> uidList, List<AsinLibsVo> vo,
                                             Map<String, List<AmazonAdAsinsLib>> asinsMap, Map<String, AdKeywordLibMarkupTagVo> adTagMap,
                                             Map<Long, List<AmazonAdAsinLibMarkupAsin>> adAsinTagMap) {
        //查找权限范围内关键词记录
        List<AmazonAdAsinsLib> asinLibInfoList = amazonAdAsinsLibDao.listByUidListAndAsins(puid, uidList, vo.stream().map(AsinLibsVo::getAsin)
                .collect(Collectors.toList()));
        asinsMap.putAll(asinLibInfoList.stream().collect(Collectors.groupingBy(asinLib -> asinLib.getAsin().toLowerCase())));

        if (CollectionUtils.isNotEmpty(asinLibInfoList)) {
            List<Long> ids = asinLibInfoList.stream().map(AmazonAdAsinsLib::getId).collect(Collectors.toList());
            List<String> idStrings = asinLibInfoList.stream().map(AmazonAdAsinsLib :: getId).map(Object::toString).collect(Collectors.toList());

            //用户具有数据权限的uid下自定义标签和公共标签 已经关联过ASIN的TAG
            List<AdKeywordLibMarkupTagVo> existTagList= adKeywordLibMarkupTagDao.getRelationVos(puid, uid,
                    AdTagTypeEnum.ASINLIB.getType(), null, null, null, idStrings);

            if (CollectionUtils.isNotEmpty(existTagList)) {
                adTagMap.putAll(existTagList.parallelStream().peek(e -> e.setTagIdsList(e.getTagIdsStr()))
                        .collect(Collectors.toMap(AdKeywordLibMarkupTagVo::getRelationId, Function.identity(), (e1, e2) -> e2)));
            }

            //获取具有数据权限下的已经关联过ASIN的ASIN标签
            List<AmazonAdAsinLibMarkupAsin> existAsinTag = asinsLibMarkupAsinDao.listByUidAndAsinIds(puid, uidList, ids);
            if (CollectionUtils.isNotEmpty(existAsinTag)) {
                adAsinTagMap.putAll(existAsinTag.parallelStream().collect(Collectors.groupingBy(AmazonAdAsinLibMarkupAsin::getAsinLibId)));
            }
        }
    }

    private List<Long> filterTagAndAsinTag (Integer puid, PageListAsinsRequest request) {
        List<Long> asinQueryIdList = null;
        Integer searchUid = null;
        if (request.getSearchUid() > 0) {
            searchUid = request.getSearchUid();
        }
        //标签筛选需要改成任一和全部，增加一个查询类型，全部通过GROUP_CONCAT查询，再通过逻辑进行筛选
        if (CollectionUtils.isNotEmpty(request.getAdTagIdList())) {
            asinQueryIdList = adKeywordLibTagService.getRelationIdsByTagFilter(puid, searchUid,
                    AdTagTypeEnum.ASINLIB.getType(), request.getAdTagIdList(), request.getAdTagQueryType());
            if (CollectionUtils.isEmpty(asinQueryIdList)) {
                return asinQueryIdList;
            }
        }

        //asin标签筛选
        if (StringUtils.isNotBlank(request.getAsin()) && StringUtils.isNotBlank(request.getMarketplaceId())) {
            List<Long> ids = asinsLibMarkupAsinDao.listAsinIdByAsin(puid, Optional.ofNullable(searchUid)
                            .map(Arrays::asList).orElse(Collections.emptyList()),
                    request.getMarketplaceId(), request.getAsin());
            //为空清空id列表，后续直接返回空列表页。不为空则与标记标签的id取交集
            if (CollectionUtils.isEmpty(ids)) {
                return asinQueryIdList;
            }
            if (CollectionUtils.isNotEmpty(asinQueryIdList)) {
                asinQueryIdList.retainAll(ids);
            } else {
                asinQueryIdList = ids;
            }
            if (CollectionUtils.isEmpty(asinQueryIdList)) {
                return asinQueryIdList;
            }
        }
        return asinQueryIdList;
    }

    private Page<AsinLibsVo> queryAsinLib(PageListAsinsRequest request,List<Integer> uidList,
                                          List<Long> asinQueryIdList, String startDate,
                                          String endDate) {
        Page<AsinLibsVo> asinPage = new Page<>();
        if (StringUtils.isBlank(request.getOrderField()) || StringUtils.isBlank(request.getOrderType()) || "createTime".equals(request.getOrderField())) {
            //无排序
            asinPage = amazonAdAsinsLibDao.pageAsinLib(asinQueryIdList, uidList, request, startDate, endDate);
        } else {
            //先根据列表筛选查询asin_lib基础数据中筛选的数据，再查询三种类型的广告投放报告数据进行分页排序，最后得到分页排序后的asin list
            getAsinPageOrderByReport(request, asinQueryIdList, uidList, asinPage, startDate, endDate);
        }
        return asinPage;
    }

    private void getTargetAndNeCnt(PageListAsinsRequest request, List<String> queryAsins,
                                   Map<String, Integer> asinTargetAllCntMap, Map<String, Integer> asinNeTargetAllCntMap) {
        List<Integer> shopIdList = request.getShopListList().stream().map(Integer::valueOf).collect(Collectors.toList());
        List<AsinLibsVo> spAsinTargetCount = odsAmazonAdTargetingDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        List<AsinLibsVo> sbAsinTargetCount = odsAmazonAdTargetingSbDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        List<AsinLibsVo> sdAsinTargetCount = odsAmazonAdTargetingSdDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        Map<String, Integer> asinTargetCountMap = spAsinTargetCount.parallelStream().collect(Collectors.toMap(vo -> vo.getAsin().toLowerCase(), AsinLibsVo::getTargetNum, (oldValue, newValue) -> newValue));
        sbAsinTargetCount.forEach(vo -> asinTargetCountMap.merge(vo.getAsin().toLowerCase(), vo.getTargetNum(), Integer::sum));
        sdAsinTargetCount.forEach(vo -> asinTargetCountMap.merge(vo.getAsin().toLowerCase(), vo.getTargetNum(), Integer::sum));

        //统计否投数量，包括SP广告活动层级否定，SP投放否定，SB投放否定，SD投放否定
        List<AsinLibsVo> spCampaignAsinNeTargetCount = amazonAdCampaignNetargetingSpDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        List<AsinLibsVo> spAsinNeTargetCount = odsAmazonAdNeTargetingDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        List<AsinLibsVo> sbAsinNeTargetCount = odsAmazonAdNeTargetingSbDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        List<AsinLibsVo> sdAsinNeTargetCount = odsAmazonAdNeTargetingSdDao.getCountByAsin(request.getPuid(), request, shopIdList, queryAsins);
        Map<String, Integer> asinNeTargetCountMap = spCampaignAsinNeTargetCount.parallelStream().collect(Collectors.toMap(vo -> vo.getAsin().toLowerCase(), AsinLibsVo::getTargetNum, (oldValue, newValue) -> newValue));
        spAsinNeTargetCount.forEach(vo -> asinNeTargetCountMap.merge(vo.getAsin().toLowerCase(), vo.getTargetNum(), Integer::sum));
        sbAsinNeTargetCount.forEach(vo -> asinNeTargetCountMap.merge(vo.getAsin().toLowerCase(), vo.getTargetNum(), Integer::sum));
        sdAsinNeTargetCount.forEach(vo -> asinNeTargetCountMap.merge(vo.getAsin().toLowerCase(), vo.getTargetNum(), Integer::sum));
        asinTargetAllCntMap.putAll(asinTargetCountMap);;
        asinNeTargetAllCntMap.putAll(asinNeTargetCountMap);
    }

    private Map<String, AsinLibsDetailVo> getTargetReport(Integer puid, PageListAsinsRequest request,
                                                          List<String> queryAsins, String currency,
                                                          String startDate, String endDate) {
        if (StringUtils.isEmpty(currency) && StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)) {
            return null;
        }
        Map<String, AsinLibsDetailVo> reportDataMap;
        //报告数据(对比指标也是同样方式)，取前端传入的币种，内存中汇总计算
        List<AsinLibsDetailVo> spAsinReportData = odsCpcTargetingReportDao.getAsinReportByAsins(puid, request, startDate, endDate, queryAsins, currency);
        List<AsinLibsDetailVo> sbAsinReportData = odsAmazonAdSbTargetingReportDao.getAsinReportByAsins(puid, request, startDate, endDate, queryAsins, currency);
        List<AsinLibsDetailVo> sdAsinReportData = odsAmazonAdSdTargetingReportDao.getAsinReportByAsins(puid, request, startDate, endDate, queryAsins, currency);
        Map<String, AsinLibsDetailVo> map = new HashMap<>();
        for (AsinLibsDetailVo a : spAsinReportData) {
            map.put(a.getAsin().toLowerCase(), null);
        }
        Map<String, AsinLibsDetailVo> voMap = mergeAsinRreportData(map, spAsinReportData);
        mergeAsinRreportData(map, sbAsinReportData);
        reportDataMap = mergeAsinRreportData(voMap, sdAsinReportData);
        return reportDataMap;
    }

    private void pageListFillResult(List<AsinLibsVo> rows, Integer puid,
                            PageListAsinsRequest request, List<String> asins,
                            Map<String, Integer> trgetCntMap, Map<String, Integer> neTargetCntMap,
                            Map<String, AsinLibsDetailVo> reportDataMap, Map<String, AsinLibsDetailVo> reportCompDataMap) {
        //再到asin_lib表中获取基本数据
        List<AmazonAdAsinsLib> asinLibBasicInfoList = amazonAdAsinsLibDao.listByUidListAndAsin(puid, request.getUidListList(), asins);
        Map<Integer, User> mapUser = userDao.listByIds(puid, asinLibBasicInfoList.stream().map(AmazonAdAsinsLib::getUid)
                .collect(Collectors.toList())).stream().collect(Collectors.toMap(User::getId, Function.identity()));
        Map<String, List<AmazonAdAsinsLib>> asinLibBasicInfoMap = asinLibBasicInfoList
                .stream().collect(Collectors.groupingBy(a -> a.getAsin().toLowerCase()));
        for (AsinLibsVo vo : rows) {
            this.buildAsinLibsPageVo(request, trgetCntMap, neTargetCntMap, reportDataMap, reportCompDataMap, asinLibBasicInfoMap, vo, mapUser);
        }
        fillAdTagData(puid, request.getUid(), rows);
        //若没有筛选ASIN标签，则填充ASIN标签列表
        if (StringUtils.isBlank(request.getAsin()) && StringUtils.isBlank(request.getMarketplaceId())) {
            this.fillAsinTagList(puid, request.getUidListList(), rows);
        }
        //查询竞品监控数据进行填充
        fillAsinMonitor(puid, request.getUid(), rows);
    }

    private AsinLibsPageListVo getPageRow(Integer puid, PageListAsinsRequest request,
                                          List<Integer> uidList, List<Long> asinQueryIdList,
                                          String startDate, String endDate,
                                          String  currency) {
        AsinLibsPageListVo result = new AsinLibsPageListVo();
        StopWatch sw = new StopWatch();
        //开始查询列表数据
        sw.start("get page query list");
        Page<AsinLibsVo> asinPage = this.queryAsinLib(request, uidList, asinQueryIdList, startDate, endDate);
        sw.stop();
        List<AsinLibsVo> rows = asinPage.getRows();
        if (CollectionUtils.isEmpty(rows)) {
            return result;
        }
        //分页排序后的结果
        List<String> asins = rows.stream().map(AsinLibsVo::getAsin).collect(Collectors.toList());

        //投放数量，内存中汇总计算
        Map<String, Integer> trgetCntMap = new HashMap<>();
        Map<String, Integer> neTargetCntMap = new HashMap<>();
        this.getTargetAndNeCnt(request, asins, trgetCntMap, neTargetCntMap);

        Map<String, AsinLibsDetailVo> reportDataMap = new HashMap<>();
        Map<String, AsinLibsDetailVo> reportCompDataMap = new HashMap<>();
        sw.start("get page list report row");
        if (StringUtils.isNotBlank(currency)) {
            //获取报告数据(对比指标也是同样方式)，取前端传入的币种，内存中汇总计算
            reportDataMap = getTargetReport(puid, request, asins, currency, startDate, endDate);

            //报告数据---对比指标查询
            if (StringUtils.isNotBlank(request.getCompareStartDate())) {
                reportCompDataMap = getTargetReport(puid, request, asins, currency, request.getCompareStartDate(), request.getCompareEndDate());
            }
        }
        sw.stop();
        //填充返回结果
        sw.start("fill page list result");
        this.pageListFillResult(rows, puid, request, asins, trgetCntMap, neTargetCntMap, reportDataMap, reportCompDataMap);
        sw.stop();
        log.info("page list result:{}", sw.prettyPrint());
        result.setPage(asinPage);
        return result;
    }

    private List<AdAsinOrderBo> handlerAsin(Supplier<List<AdAsinOrderBo>> sup) {
        List<AdAsinOrderBo> asinBoList = new ArrayList<>();
        if (Objects.nonNull(sup) && CollectionUtils.isNotEmpty(sup.get())) {
            List<AdAsinOrderBo> list = sup.get();
            return  list.parallelStream().peek(sd -> {
                if (StringUtils.isNotEmpty(sd.getAsin())) {
                    sd.setAsin(sd.getAsin().replaceAll("asin=", "").replaceAll("\"", ""));
                }
            }).collect(Collectors.toList());
        }
        return asinBoList;
    }

    private void fillAsinMonitor(Integer puid, Integer uid, List<AsinLibsVo> asinLibsVoList) {
        if (CollectionUtils.isEmpty(asinLibsVoList)) {
            return;
        }
        List<String> asinList = asinLibsVoList.stream().map(AsinLibsVo::getAsin).collect(Collectors.toList());
        List<TrackingAsinNumVo> competitorAasinCntList =  trackingDao.listAsinAllDomainNum(puid, uid, asinList);
        if (CollectionUtils.isEmpty(competitorAasinCntList)) {
            return;
        }
        Map<String, TrackingAsinNumVo> competitorCntMap = competitorAasinCntList.parallelStream().collect(Collectors.toMap(TrackingAsinNumVo::getAsin, v1 -> v1, (old, current) -> current));
        for (AsinLibsVo asinLibsVo : asinLibsVoList) {
            if (Objects.nonNull(competitorCntMap.get(asinLibsVo.getAsin()))) {
                TrackingAsinNumVo competitorCnt = competitorCntMap.get(asinLibsVo.getAsin());
                Optional.ofNullable(competitorCnt.getCnt()).ifPresent(asinLibsVo::setCompetitiveMonitor);
            }
        }
    }
}
