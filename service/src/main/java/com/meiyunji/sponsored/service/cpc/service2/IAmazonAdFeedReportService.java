package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sponsored.grpc.common.AdHourReportRequest;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dto.CommonHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.FeedHourlySelectDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.reportHour.vo.*;

import java.util.List;

public interface IAmazonAdFeedReportService {


    /**
     * 根据条件返回小时级数据
     *
     * @param shopAuth     授权
     * @param param        条件
     * @param campaignType 活动类型
     * @param costType     花费类型
     * @return
     */
    List<AdReportHourlyVO> listByAdHourCampaignTypeAndCostType(ShopAuth shopAuth, AdHourReportRequest param, String campaignType, String costType);


    /**
     * 导出小时级数据
     */
    List<AdCampaignHourVo> listAdCampaignHourByAdHourCampaignTypeAndCostType(ShopAuth shopAuth, CampaignHourParam param, String campaignType, String costType);

    /**
     * 获取周级别的数据
     */
    List<AdReportWeeklyDayVO> listAdReportWeeklyDayVOByAdHourReportRequest(ShopAuth shopAuth, AdHourReportRequest param, String campaignType, String costType);

    /**
     * 获取小时级的聚合数据
     */
    List<AdCampaignHourVo> listAggregateHourList(List<ShopAuth> shopAuths, List<String> aggregateIds,
                                                 CampaignAggregateHourParamVO param);

    /**
     * 获取小时级的聚合数据
     */
    List<AdCampaignHourVo> listAggregateHourList(List<ShopAuth> shopAuths, CampaignAggregateHourParam param);

    /**
     * 增加周级别的聚合数据
     */
    List<AdCampaignHourVo> listAggregateWeekList(List<ShopAuth> shopAuths, List<String> aggregateIds,
                                                 CampaignHourParam param);

    List<AmazonMarketingStreamData> getHourStreamData(FeedHourlySelectDTO dto);

    List<AmazonMarketingStreamData> getWeekStreamData(FeedHourlySelectDTO dto);

    List<AmazonMarketingStreamData> getTargetOfPlacementStreamData(FeedHourlySelectDTO dto);

    List<AdGroupHourVo> listAggregateGroupHourList(ShopAuth shopInfo, List<String> aggregateIds, GroupAggregateHourVo param);

    List<AdGroupHourVo> listAggregateAdGroupWeekList(ShopAuth shopInfo, List<String> aggregateIds, GroupHourParam param);

    /**
     * 查询完整数据的开始时间
     *
     * @param sellerIds
     * @param marketplaceId
     * @param startDate
     * @return
     */
    String getSellerIdsDataStartTime(List<String> sellerIds, String marketplaceId, String startDate);

    String getDataStartTime(String sellerId, String marketplaceId, String startDate);

     List<AdCampaignHourVo> multiThreadQueryWeekAms(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) ;

     List<AdGroupHourVo> multiThreadQueryGroupAms(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO);

    List<AdGroupHourVo> multiThreadQueryAmsForGroup(List<String> ids, FeedHourlySelectDTO feedHourlySelectDTO) ;

    List<AdKeywordAndTargetHourVo> multiThreadQueryAmsForTarget(List<String> ids, FeedHourlySelectDTO dto);

    List<AdKeywordAndTargetHourVo> multiThreadQueryAmsForTarget(List<String> ids, CommonHourlyReportSelectDto dto) ;

    /**
     * 获取小时级的聚合数据-多站点、多店铺
     */
    List<AdCampaignHourVo> listAggregateHourListMultiShop(List<ShopAuth> shopAuth, List<String> relationIds, CampaignAggregateHourMultiShopParamVO param);

    /**
     * 获取小时级的聚合数据-多站点、多店铺（周叠加）
     */
    List<AdCampaignHourVo> listAggregateWeekListMultiShop(List<ShopAuth> shopAuth, List<String> relationIds, CampaignHourParamMultiShop param);
}
