package com.meiyunji.sponsored.service.cpc.service.impl;

import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductAggregationReportDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductAggregationReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProductMetadata;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service.*;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.monitor.SaveMonitor;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.system.service.ICurrencyExchangeRateService;
import com.meiyunji.sponsored.service.util.ConvertUtil;
import com.meiyunji.sponsored.service.util.SummaryReportUtil;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonAdProductAggregationReportServiceImpl implements IAmazonAdProductAggregationReportService {

    @Autowired
    private IAmazonAdProfileDao profileDao;
    @Autowired
    private IAmazonAdProductAggregationReportDao aggregationReportDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IProductApi productDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdSdTargetingReportService targetingReportService;
    @Autowired
    private ICpcTargetingReportService cpcTargetingReportService;
    @Autowired
    private IAmazonAdKeywordReportService amazonAdKeywordReportService;
    @Autowired
    private IAmazonAdCampaignReportService spCampaignReportService;
    @Autowired
    private IAmazonAdSdCampaignReportService sdCampaignReportService;
    @Autowired
    private ICpcQueryKeywordReportService queryKeywordReportService;
    @Autowired
    private ICpcQueryTargetingReportService queryTargetingReportService;
    @Autowired
    private ICurrencyExchangeRateService rateService;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;

    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.PRODUCT_AGGREGATION_TARGET)
    @Override
    public Result<Page<ProductReportPageVo>> pageList(Integer puid, AdProductSearchVo searchVo) {
        Page<ProductReportPageVo> voPage = new Page<>();
        voPage.setPageNo(searchVo.getPageNo());
        voPage.setPageSize(searchVo.getPageSize());

        long t = Instant.now().toEpochMilli();

        List<Integer> shopIdList;
        if (StringUtils.isNotBlank(searchVo.getMarketplaceIds()) && StringUtils.isNotBlank(searchVo.getShopIds())) {
            List<String> marketplaceIdList = StringUtil.splitStr(searchVo.getMarketplaceIds(), ",");

            List<Integer> shopIds = profileDao.listByMarketplaceIdList(puid, marketplaceIdList).stream().
                    map(AmazonAdProfile::getShopId).collect(Collectors.toList());  //选择站点对应的店铺

            List<Integer> list = StringUtil.splitStr(searchVo.getShopIds(), ",").stream().
                    map(Integer::parseInt).collect(Collectors.toList());;  //选择的店铺

            shopIds.retainAll(list);  // 取交集
            if (CollectionUtils.isEmpty(shopIds)) {
                return ResultUtil.success(voPage);
            }
            shopIdList = shopIds;
        } else  if (StringUtils.isNotBlank(searchVo.getMarketplaceIds())) {
            List<String> marketplaceIdList = StringUtil.splitStr(searchVo.getMarketplaceIds(), ",");
            shopIdList = profileDao.listByMarketplaceIdList(puid, marketplaceIdList).stream().
                    map(AmazonAdProfile::getShopId).collect(Collectors.toList());  //选择站点对应的店铺
            if (CollectionUtils.isEmpty(shopIdList)) {
                return ResultUtil.success(voPage);
            }
        } else if (StringUtils.isNotBlank(searchVo.getShopIds())) {
            shopIdList = StringUtil.splitStr(searchVo.getShopIds(), ",").stream()
                    .map(Integer::parseInt).collect(Collectors.toList());;  //选择的店铺
        } else {
            shopIdList = profileDao.getList(searchVo.getPuid()).stream().
                    map(AmazonAdProfile::getShopId).collect(Collectors.toList());  //选择站点对应的店铺
            if (CollectionUtils.isEmpty(shopIdList)) {
                return ResultUtil.success(voPage);
            }
        }
        searchVo.setShopIdList(shopIdList);

        log.info("  查询shopIDs 花费时间 {}", (Instant.now().toEpochMilli()-t));

        long t2 = Instant.now().toEpochMilli();

        Page<AmazonAdProductAggregationReport> page = new Page<>();
        if (StringUtils.isBlank(searchVo.getShowType()) || "asin".equals(searchVo.getShowType())) {
            page = getPageList(puid, searchVo);
        }
        if ("parentAsin".equals(searchVo.getShowType())) {
            page = getPageListGroupByParentAsin(puid, searchVo);
        }
        log.info("  查询page列表 花费时间 {}", (Instant.now().toEpochMilli()-t2));

        BeanUtils.copyProperties(page, voPage);

        List<AmazonAdProductAggregationReport> poList = page.getRows();

        List<ProductReportPageVo> voList = new ArrayList<>();
        voPage.setRows(voList);

        Map<String, String> rateMap = searchVo.getRateMap();  //汇率
        long t3 = Instant.now().toEpochMilli();
        if (CollectionUtils.isNotEmpty(poList)) {
            Map<Integer, ShopSaleDto> saleDtoMap = new HashMap<>();

            BigDecimal sumShopSales = BigDecimal.ZERO; // 总的店铺销售额

            long t4 = Instant.now().toEpochMilli();

            List<ShopSaleDto> listShopSale = cpcShopDataService.getListShopSale(puid, shopIdList, searchVo.getStartDate(), searchVo.getEndDate());
            if (CollectionUtils.isNotEmpty(listShopSale)) {
                for (ShopSaleDto saleDto : listShopSale) {
                    saleDtoMap.put(saleDto.getShopId(), saleDto);
                }
            }

            log.info(" 循环遍历查店铺销售额 花费时间 {}", (Instant.now().toEpochMilli()-t4));

            ProductReportPageVo vo;

            Map<String, ProductAdReportVo> productMap = new HashMap<>();
            ProductAdReportVo product;

            Map<Integer, ShopAuth> shopMap = new HashMap<>();
            ShopAuth shopAuth;

            // 是否要转换币种
            boolean isConvertCurrency = false;
            List<String> currencyList = new ArrayList<>();
            for (AmazonAdProductAggregationReport report : poList) {
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(report.getMarketplaceId()).getCurrencyCode();
                if (!currencyList.contains(currencyCode)) {
                    currencyList.add(currencyCode);
                }
            }
            if (currencyList.size() > 1) {
                isConvertCurrency = true;
            }

            searchVo.setIsConvertCurrency(isConvertCurrency);

            Map<String, AmazonAdProductMetadata> amazonAdProductMetadataMap = null;
            List<String> asins = poList.stream().filter(Objects::nonNull).map(AmazonAdProductAggregationReport::getAsin).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(asins)) {
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(puid,null, null, asins);
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    Map<String, AmazonAdProductMetadata> map = new HashMap<>();
                    map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.toMap(s->s.getShopId()+"-"+s.getAsin()+"-"+s.getSku(),Function.identity(), (s1, s2) -> s1)));
                    amazonAdProductMetadataMap = map;
                }
            }

            Map<String,AmazonAdProductMetadata> finalMetadataMap = amazonAdProductMetadataMap;
            long t5 = Instant.now().toEpochMilli();
            for (AmazonAdProductAggregationReport report : poList) {
                BigDecimal sumShopSale = BigDecimal.ZERO;  //单店铺销售额
                if (saleDtoMap.containsKey(report.getShopId())) {
                    ShopSaleDto shopSaleDto1 = saleDtoMap.get(report.getShopId());
                    if (shopSaleDto1 != null && shopSaleDto1.getSumRange() != null) {
                        sumShopSale = shopSaleDto1.getSumRange();
                    }
                }

                vo = new ProductReportPageVo();

                if (shopMap.containsKey(report.getShopId())) {
                     shopAuth = shopMap.get(report.getShopId());
                } else {
                    shopAuth = shopAuthDao.getScAndVcById(report.getShopId());
                    shopMap.put(report.getShopId(), shopAuth);
                }
                if (shopAuth != null) {
                    vo.setShopName(shopAuth.getName());
                }

                //获取货币单位
                String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(report.getMarketplaceId()).getCurrencyCode();
                vo.setCurrency(currencyCode);

                BigDecimal rate =  BigDecimal.valueOf(1);
                String rateStr = rateMap.get(currencyCode);
                if (rateStr != null) {
                    rate = new BigDecimal(rateStr);;
                }

                BigDecimal usdPrice;
                if (isConvertCurrency) {
                    if (!"USD".equalsIgnoreCase(currencyCode) || sumShopSale.compareTo(BigDecimal.ZERO) != 0) {
                        usdPrice = rateService.getUSDPrice(searchVo.getPuid(), currencyCode, sumShopSale, rate);
                    } else {
                        usdPrice = sumShopSale;
                    }
                } else {
                    usdPrice = sumShopSale;
                }

                sumShopSales = MathUtil.add(sumShopSale, usdPrice);

                SummaryReportUtil marketplace = SummaryReportUtil.getByMarketplaceId(report.getMarketplaceId());
                if (marketplace != null) {
                    vo.setMarketplaceCN(marketplace.getMarketplaceCN());
                }
                vo.setAsin(report.getAsin());
                vo.setShopId(report.getShopId());
                vo.setMarketplaceId(report.getMarketplaceId());

                vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
                vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
                vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");
                vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
                vo.setAdSale(report.getAdSales() != null ?  String.valueOf(report.getAdSales().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");
                // 点击率
                Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
                vo.setCtr(String.valueOf(ctr));
                //订单转化率
                Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
                vo.setCvr(String.valueOf(cvr));
                //acos
                if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                    vo.setAcos("0");
                } else {
                    Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
                    vo.setAcos(String.valueOf(acos));
                }
                //acots
                if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                    vo.setAcots("0");
                } else {
                    Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
                    vo.setAcots(String.valueOf(acots));
                }
                //asots
                if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                    vo.setAsots("0");
                } else {
                    Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
                    vo.setAsots(String.valueOf(asots));
                }
                //adCostPerClick
                if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
                    vo.setAdCostPerClick("0");
                } else {
                    Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
                    vo.setAdCostPerClick(String.valueOf(adCostPerClick));
                }
                //roas
                if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                    vo.setRoas("0");
                } else {
                    Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
                    vo.setRoas(String.valueOf(roas));
                }

                if (productMap.containsKey(report.getAsin())) {
                    product = productMap.get(report.getAsin());
                } else {
                    product = productDao.getProductByAsinAndSku(puid, report.getShopId(), report.getAsin(), report.getSku());
                    if (product != null) {
                        productMap.put(report.getAsin(), product);
                    }
                }

                String titleAndImageKey= String.valueOf(report.getShopId())+"-"+report.getAsin()+"-"+report.getSku();
                if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(titleAndImageKey)) {
                    AmazonAdProductMetadata metadata = finalMetadataMap.get(titleAndImageKey);
                    if (metadata != null) {
                        vo.setTitle(metadata.getTitle());
                        vo.setImageUrl(metadata.getImageUrl());
                    }
                }

                if (StringUtils.isBlank(vo.getTitle()) || StringUtils.isBlank(vo.getImageUrl())) {
                    if (product != null) {
                        vo.setTitle(product.getTitle());
                        vo.setImageUrl(product.getMainImage());
                    }
                }

                if ("parentAsin".equals(searchVo.getShowType())) {
                    // 查父asin维度聚合报告时，parentAsin转为asin别名查在线产品图片以及标题不带sku条件。
                    ProductAdReportVo parentProduct = productDao.getProductByParentAsin(puid, report.getShopId(), report.getAsin());
                    if (parentProduct != null) {
                        vo.setTitle(StringUtils.isNotBlank(parentProduct.getTitle()) ? parentProduct.getTitle() : vo.getTitle());
                        vo.setImageUrl(StringUtils.isNotBlank(parentProduct.getMainImage()) ? parentProduct.getMainImage() : vo.getImageUrl());
                    }
                }

                voList.add(vo);
            }

            searchVo.setSumShopSale(sumShopSales);  // 总的店铺销售额
            log.info(" pageList遍历 花费时间 {}", (Instant.now().toEpochMilli()-t5));
        }

        log.info(" 循环遍历 花费时间 {}", (Instant.now().toEpochMilli()-t3));

        return ResultUtil.success(voPage);
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.PRODUCT_AGGREGATION_TARGET)
    @Override
    public CpcCommPageVo getSumVo(Integer puid, AdProductSearchVo searchVo, String currency,boolean isShopSale) {
        CpcCommPageVo vo = new CpcCommPageVo();

        BigDecimal sumShopSale = BigDecimal.ZERO;  // 转换过的店铺销售额
        if(!isShopSale && searchVo.getSumShopSale() != null){
            sumShopSale = searchVo.getSumShopSale();
        }

        Set<Integer> ids = new HashSet<>();
        AmazonAdProductAggregationReport daoSumVo = new AmazonAdProductAggregationReport();
        String currencyCode = null ;
        boolean isConvert = true;
        //统一转成USD币种
        long t7 = Instant.now().toEpochMilli();
        List<AmazonAdProductAggregationReport> reportList = Lists.newArrayList();
        if (StringUtils.isBlank(searchVo.getShowType()) || "asin".equals(searchVo.getShowType())) {
            reportList = aggregationReportDao.getListBySearch(puid, searchVo);
        }
        if ("parentAsin".equals(searchVo.getShowType())) {
            reportList = aggregationReportDao.getListGroupByParentAsin(puid, searchVo);
        }
        log.info("汇总花费时间 {} ", (Instant.now().toEpochMilli()-t7));
        Map<String, String> rateMap = searchVo.getRateMap(); // 汇率

        long t8 = Instant.now().toEpochMilli();
        if (CollectionUtils.isNotEmpty(reportList)) {
            BigDecimal rate;
            List<String> marketplaceIds = reportList.stream().map(AmazonAdProductAggregationReport::getMarketplaceId).distinct().collect(Collectors.toList());

            if(marketplaceIds.size() == 1){
                isConvert = false;
                vo.setCurrency(currency);
            } else {
                vo.setCurrency("USD");
            }
            for (AmazonAdProductAggregationReport report : reportList) {
                if(report.getShopId() != null){
                    ids.add(report.getShopId());
                }
                if(isConvert){
                    currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(report.getMarketplaceId()).getCurrencyCode();
                    String rateStr = rateMap.get(currencyCode);
                    if (rateStr != null) {
                        rate = new BigDecimal(rateStr);;
                    } else {
                        rate =  BigDecimal.valueOf(1);
                    }
                    //价格换币种
                    ConvertUtil.convert(report, rate);
                }


                report.setCost(report.getCost() != null ? report.getCost() : BigDecimal.ZERO);
                report.setAdSales(report.getAdSales() != null ? report.getAdSales() : BigDecimal.ZERO);

                daoSumVo.setShopId(report.getShopId());
                daoSumVo.setImpressions(daoSumVo.getImpressions() != null ? daoSumVo.getImpressions() + report.getImpressions() : report.getImpressions());
                daoSumVo.setClicks(daoSumVo.getClicks() != null ? daoSumVo.getClicks() + report.getClicks() : report.getClicks());
                daoSumVo.setSaleNum(daoSumVo.getSaleNum() != null ? daoSumVo.getSaleNum() + report.getSaleNum() : report.getSaleNum());
                daoSumVo.setCost(daoSumVo.getCost() != null ?  MathUtil.add(daoSumVo.getCost(), report.getCost()) : report.getCost());
                daoSumVo.setAdSales(daoSumVo.getAdSales() != null ? MathUtil.add(daoSumVo.getAdSales(), report.getAdSales()) :  report.getAdSales());
            }
        } else {
            vo.setCurrency(currency);
            return vo;
        }
        log.info(" 汇总 循环花费时间 {} ", (Instant.now().toEpochMilli()-t8));

        if(isShopSale){
            List<ShopSaleDto> listShopSale = cpcShopDataService.getListShopSale(puid,Lists.newArrayList(ids) , searchVo.getStartDate(), searchVo.getEndDate());
            if (CollectionUtils.isNotEmpty(listShopSale)) {

                BigDecimal rate;
                for (ShopSaleDto saleDto : listShopSale) {
                    if(isConvert){
                        String rateStr = rateMap.get(saleDto.getCurrency());
                        if (rateStr != null) {
                            rate = new BigDecimal(rateStr);;
                        } else {
                            rate =  BigDecimal.valueOf(1);
                        }
                        //价格换币种
                        ConvertUtil.convert(saleDto, rate);
                    }

                    sumShopSale = MathUtil.add(sumShopSale,saleDto.getSumRange());
                }
            }
        }



        long t9 = Instant.now().toEpochMilli();
        // 组装报告数据
        setReportData(daoSumVo, vo, sumShopSale);
        log.info("汇总组装报告数据花费时间 {} ", (Instant.now().toEpochMilli()-t9));

        return vo;
    }

    @Override
    public Result<AdReportDetailsVo> productReportDetails(ProductReportDetailsVo detailsVo) {
        AdReportDetailsVo adReportDetailsVo = new AdReportDetailsVo();

        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(detailsVo.getMarketplaceId()).getCurrencyCode();
        adReportDetailsVo.setCurrency(currencyCode);

        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd");

        ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), detailsVo.getStartDate(), detailsVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }

        CpcCommPageVo sumVo = new CpcCommPageVo();  //本期 报告数据汇总
        AmazonAdProductAggregationReport daoSumVo = getDetailsSumVo(detailsVo.getPuid(), detailsVo.getShopId(),
                detailsVo.getType(), detailsVo.getAsin(), startStr, endStr);
        // 组装报告数据
        setReportData(daoSumVo, sumVo, sumShopSale);
        adReportDetailsVo.setSumVo(sumVo);

        CpcCommPageVo lastSumVo = new CpcCommPageVo();  //上期 报告数据汇总
        Map<String,String> map = getLastTime(detailsVo.getStart(),detailsVo.getEnd());
        AmazonAdProductAggregationReport lastDaoSunVo = getDetailsSumVo(detailsVo.getPuid(), detailsVo.getShopId(),
                detailsVo.getType(), detailsVo.getAsin(),map.get("startStr"),map.get("endStr"));
        // 组装报告数据
        setReportData(lastDaoSunVo, lastSumVo, sumShopSale);
        adReportDetailsVo.setLastSumVo(lastSumVo);

        adReportDetailsVo.calculationGrow(); // 计算 本期、上期 环比

        List<AmazonAdProductAggregationReport> aggregationReportList = getReportListByDay(detailsVo.getPuid(), detailsVo);

        //组装每天数据
        setAggregationReportData(aggregationReportList, adReportDetailsVo);

        //填充日期数据
        setDayData(adReportDetailsVo.getList(), detailsVo.getStart(), detailsVo.getEnd());

        return ResultUtil.returnSucc(adReportDetailsVo);
    }

    @Override
    public Result<AdReportDetailsVo> targetReportDetails(TargetReportDetailsVo detailsVo) {
        AdReportDetailsVo adReportDetailsVo = new AdReportDetailsVo();

        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(detailsVo.getMarketplaceId()).getCurrencyCode();
        adReportDetailsVo.setCurrency(currencyCode);

        ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), detailsVo.getStartDate(), detailsVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }

        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd");
        detailsVo.setStartDate(startStr);
        detailsVo.setEndDate(endStr);

        CpcCommPageVo sumVo = new CpcCommPageVo();  //本期 报告数据汇总
        // 组装投放弹层数据
        setTargetDetailsData(detailsVo, sumVo, sumShopSale);
        adReportDetailsVo.setSumVo(sumVo);

        CpcCommPageVo lastSumVo = new CpcCommPageVo();  //上期 报告数据汇总
        Map<String,String> map = getLastTime(detailsVo.getStart(),detailsVo.getEnd());
        detailsVo.setStartDate(map.get("startStr"));
        detailsVo.setEndDate(map.get("endStr"));
        // 组装投放弹层数据
        setTargetDetailsData(detailsVo, lastSumVo, sumShopSale);
        adReportDetailsVo.setLastSumVo(lastSumVo);

        adReportDetailsVo.calculationGrow(); // 计算 本期、上期 环比

        //组装每天数据
        setTargetDetailsDay(adReportDetailsVo, detailsVo);

        //填充日期数据
        setDayData(adReportDetailsVo.getList(), detailsVo.getStart(), detailsVo.getEnd());

        return ResultUtil.returnSucc(adReportDetailsVo);
    }

    @Override
    public Result<AdReportDetailsVo> queryReportDetails(QueryReportDetailsVo detailsVo) {
        AdReportDetailsVo adReportDetailsVo = new AdReportDetailsVo();

        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(detailsVo.getMarketplaceId()).getCurrencyCode();
        adReportDetailsVo.setCurrency(currencyCode);

        ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), detailsVo.getStartDate(), detailsVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }

        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd");
        detailsVo.setStartDate(startStr);
        detailsVo.setEndDate(endStr);

        CpcCommPageVo sumVo = new CpcCommPageVo();  //本期 报告数据汇总
        // 组装投放弹层数据
        setQueryDetailsData(detailsVo, sumVo, sumShopSale);
        adReportDetailsVo.setSumVo(sumVo);

        CpcCommPageVo lastSumVo = new CpcCommPageVo();  //上期 报告数据汇总
        Map<String,String> map = getLastTime(detailsVo.getStart(),detailsVo.getEnd());
        detailsVo.setStartDate(map.get("startStr"));
        detailsVo.setEndDate(map.get("endStr"));
        // 组装投放弹层数据
        setQueryDetailsData(detailsVo, lastSumVo, sumShopSale);
        adReportDetailsVo.setLastSumVo(lastSumVo);

        adReportDetailsVo.calculationGrow(); // 计算 本期、上期 环比

        //组装每天数据
        setQueryDetailsDay(adReportDetailsVo, detailsVo);

        //填充日期数据
        setDayData(adReportDetailsVo.getList(), detailsVo.getStart(), detailsVo.getEnd());

        return ResultUtil.returnSucc(adReportDetailsVo);
    }

    @Override
    public Result<AdReportDetailsVo> queryTargetReportDetails(QueryTargetReportDetailsVo detailsVo) {
        AdReportDetailsVo adReportDetailsVo = new AdReportDetailsVo();

        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(detailsVo.getMarketplaceId()).getCurrencyCode();
        adReportDetailsVo.setCurrency(currencyCode);

        ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), detailsVo.getStartDate(), detailsVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }

        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd");
        detailsVo.setStartDate(startStr);
        detailsVo.setEndDate(endStr);

        CpcCommPageVo sumVo = new CpcCommPageVo();  //本期 报告数据汇总
        // 组装投放弹层数据
        setQueryTargetDetailsData(detailsVo, sumVo, sumShopSale);
        adReportDetailsVo.setSumVo(sumVo);

        CpcCommPageVo lastSumVo = new CpcCommPageVo();  //上期 报告数据汇总
        Map<String,String> map = getLastTime(detailsVo.getStart(),detailsVo.getEnd());
        detailsVo.setStartDate(map.get("startStr"));
        detailsVo.setEndDate(map.get("endStr"));
        // 组装投放弹层数据
        setQueryTargetDetailsData(detailsVo, lastSumVo, sumShopSale);
        adReportDetailsVo.setLastSumVo(lastSumVo);

        adReportDetailsVo.calculationGrow(); // 计算 本期、上期 环比

        //组装每天数据
        setQueryTargetDetailsDay(adReportDetailsVo, detailsVo);

        //填充日期数据
        setDayData(adReportDetailsVo.getList(), detailsVo.getStart(), detailsVo.getEnd());

        return ResultUtil.returnSucc(adReportDetailsVo);
    }

    @Override
    public Result<AdReportDetailsVo> campaignReportDetails(CampaignReportDetails detailsVo) {
        AdReportDetailsVo adReportDetailsVo = new AdReportDetailsVo();

        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(detailsVo.getMarketplaceId()).getCurrencyCode();
        adReportDetailsVo.setCurrency(currencyCode);

        ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), detailsVo.getStartDate(), detailsVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }

        //获取时间段的报告数据汇总
        String startStr = DateUtil.dateToStrWithFormat(detailsVo.getStart(),"yyyyMMdd");
        String endStr = DateUtil.dateToStrWithFormat(detailsVo.getEnd(),"yyyyMMdd");
        detailsVo.setStartDate(startStr);
        detailsVo.setEndDate(endStr);

        CpcCommPageVo sumVo = new CpcCommPageVo();  //本期 报告数据汇总
        // 组装投放弹层数据
        setCampaignDetailsData(detailsVo, sumVo, sumShopSale);
        adReportDetailsVo.setSumVo(sumVo);

        CpcCommPageVo lastSumVo = new CpcCommPageVo();  //上期 报告数据汇总
        Map<String,String> map = getLastTime(detailsVo.getStart(),detailsVo.getEnd());
        detailsVo.setStartDate(map.get("startStr"));
        detailsVo.setEndDate(map.get("endStr"));
        // 组装投放弹层数据
        setCampaignDetailsData(detailsVo, lastSumVo, sumShopSale);
        adReportDetailsVo.setLastSumVo(lastSumVo);

        adReportDetailsVo.calculationGrow(); // 计算 本期、上期 环比

        //组装每天数据
        setCampaignDetailsDay(adReportDetailsVo, detailsVo);

        //填充日期数据
        setDayData(adReportDetailsVo.getList(), detailsVo.getStart(), detailsVo.getEnd());

        return ResultUtil.returnSucc(adReportDetailsVo);
    }

    @Override
    public Result<List<TargetReportVo>> getTargetList(Integer puid, TargetReportSearchVo searchVo) {
        long t = Instant.now().toEpochMilli();
        List<TargetReportVo> voList = new ArrayList<>();

        //找出asin下所有广告组
        List<AmazonAdProductAggregationReport> reportList = Lists.newArrayList();
        if (StringUtils.isBlank(searchVo.getShowType()) || "asin".equals(searchVo.getShowType())) {
            reportList = getListByAsin(puid, searchVo.getShopId(), searchVo.getStart(),
                    searchVo.getEnd(), searchVo.getType(), searchVo.getAsin());
        }
        if ("parentAsin".equals(searchVo.getShowType())) {
            reportList = getListByParentAsin(puid, searchVo.getShopId(), searchVo.getStart(),
                    searchVo.getEnd(), searchVo.getType(), searchVo.getAsin());
        }
        log.info("找出asin下所有广告组 花费时间 {}", (Instant.now().toEpochMilli()-t));
        if (CollectionUtils.isEmpty(reportList)) {
            return ResultUtil.returnSucc(voList);
        }

        long t2 = Instant.now().toEpochMilli();
        ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(searchVo.getShopId(), searchVo.getStartDate(), searchVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }
        searchVo.setSumShopSale(sumShopSale);
        log.info("获取店铺销售额 花费时间{}", (Instant.now().toEpochMilli()-t2));

        Map<String, List<String>> groupTypeMap = new HashMap<>();
        for (AmazonAdProductAggregationReport report : reportList) {
            if (groupTypeMap.containsKey(report.getType())) {
                List<String> groupList = groupTypeMap.get(report.getType());
                if (!groupList.contains(report.getAdGroupId())) {
                    groupTypeMap.get(report.getType()).add(report.getAdGroupId());
                }
            } else {
                groupTypeMap.put(report.getType(), Lists.newArrayList(report.getAdGroupId()));
            }
        }

        List<String> spGroupIds = groupTypeMap.get("sp");
        List<String> sdGroupIds = groupTypeMap.get("sd");

        long t3 = Instant.now().toEpochMilli();
        if (CollectionUtils.isNotEmpty(spGroupIds)) {
            List<TargetReportVo> spKeywordList = amazonAdKeywordReportService.getReportVoListByGroupIds(puid, spGroupIds, searchVo);
            voList.addAll(spKeywordList);
            List<TargetReportVo> spTargetList = cpcTargetingReportService.getReportVoListByGroupIds(puid, spGroupIds, searchVo);
            voList.addAll(spTargetList);
        }
        log.info("查找sp广告投放 花费时间 {}", (Instant.now().toEpochMilli()-t3));

        long t4 = Instant.now().toEpochMilli();
        if (CollectionUtils.isNotEmpty(sdGroupIds)) {
            List<TargetReportVo> sdTargetList = targetingReportService.getReportVoListByGroupIds(puid, sdGroupIds, searchVo);
            voList.addAll(sdTargetList);
        }
        log.info("查找sd广告投放 花费时间 {}", (Instant.now().toEpochMilli()-t4));

        long t5 = Instant.now().toEpochMilli();
        if (CollectionUtils.isNotEmpty(voList)) {
            //排序
            if (StringUtils.isNotBlank(searchVo.getOrderField()) && StringUtils.isNotBlank(searchVo.getOrderValue())) {
                if ("adSales".equalsIgnoreCase(searchVo.getOrderField())) {  //兼容处理
                    voList = PageUtil.sort(voList, "adSale", searchVo.getOrderValue());
                } else if ("saleNum".equalsIgnoreCase(searchVo.getOrderField())) {
                    voList = PageUtil.sort(voList, "adOrderNum", searchVo.getOrderValue());
                }else if ("cost".equalsIgnoreCase(searchVo.getOrderField())) {
                    voList = PageUtil.sort(voList, "adCost", searchVo.getOrderValue());
                } else  {
                    boolean isSorted = StringUtils.isNotBlank(searchVo.getOrderField()) && Constants.isADperformanceOrderField(searchVo.getOrderField());
                    if (isSorted) {
                        voList = PageUtil.sort(voList, searchVo.getOrderField(), searchVo.getOrderValue());
                    }
                }
            }
        }
        log.info("排序 花费时间 {}", (Instant.now().toEpochMilli()-t5));


        log.info("总共 花费时间 {}", (Instant.now().toEpochMilli()-t));
        return ResultUtil.returnSucc(voList);
    }


    @Override
    public Result<List<CampaignReportVo>> getCampaignList(Integer puid, CampaignReportSearchVo searchVo) {
        ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(searchVo.getShopId(), searchVo.getStartDate(), searchVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }
        searchVo.setSumShopSale(sumShopSale);

        List<CampaignReportVo> voList = new ArrayList<>();
        if ("sp".equalsIgnoreCase(searchVo.getType())) {
            CampaignReportVo campaignReportVo = spCampaignReportService.getReportVoByCampaignId(puid, searchVo.getCampaignId(), searchVo);
            if (campaignReportVo != null) {
                voList.add(campaignReportVo);
                return ResultUtil.returnSucc(voList);
            }
        }

        if ("sd".equalsIgnoreCase(searchVo.getType())) {
            CampaignReportVo sdCampaignReportVo = sdCampaignReportService.getReportVoByCampaignId(puid, searchVo.getCampaignId(), searchVo);
            if (sdCampaignReportVo != null) {
                voList.add(sdCampaignReportVo);
                return ResultUtil.returnSucc(voList);
            }
        }

        return ResultUtil.returnSucc(voList);
    }

    @Override
    public Result<List<QueryReportVo>> getQueryList(Integer puid, QueryReportSearchVo searchVo) {
        List<QueryReportVo> voList = new ArrayList<>();

        //找出asin下所有广告组
        List<AmazonAdProductAggregationReport> reportList = Lists.newArrayList();
        if (StringUtils.isBlank(searchVo.getShowType()) || "asin".equals(searchVo.getShowType())) {
            reportList = getListByAsin(puid, searchVo.getShopId(), searchVo.getStart(),
                    searchVo.getEnd(), searchVo.getType(), searchVo.getAsin());
        }
        if ("parentAsin".equals(searchVo.getShowType())) {
            reportList = getListByParentAsin(puid, searchVo.getShopId(), searchVo.getStart(),
                    searchVo.getEnd(), searchVo.getType(), searchVo.getAsin());
        }

        if (CollectionUtils.isEmpty(reportList)) {
            return ResultUtil.returnSucc(voList);
        }

        ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(searchVo.getShopId(), searchVo.getStartDate(), searchVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }
        searchVo.setSumShopSale(sumShopSale);

        Map<String, List<String>> groupTypeMap = new HashMap<>();
        for (AmazonAdProductAggregationReport report : reportList) {
            if (groupTypeMap.containsKey(report.getType())) {
                List<String> groupList = groupTypeMap.get(report.getType());
                if (!groupList.contains(report.getAdGroupId())) {
                    groupTypeMap.get(report.getType()).add(report.getAdGroupId());
                }
            } else {
                groupTypeMap.put(report.getType(), Lists.newArrayList(report.getAdGroupId()));
            }
        }

        List<String> spGroupIds = groupTypeMap.get("sp");
        if (CollectionUtils.isNotEmpty(spGroupIds)) {

            List<QueryReportVo> queryKeywordList = queryKeywordReportService.getReportVoListByGroupIds(puid, spGroupIds, searchVo);
            voList.addAll(queryKeywordList);

            List<QueryReportVo> queryTargetList = queryTargetingReportService.getReportVoListByGroupIds(puid, spGroupIds, searchVo);
            voList.addAll(queryTargetList);
        }

        if (CollectionUtils.isNotEmpty(voList)) {
            //排序
            if (StringUtils.isNotBlank(searchVo.getOrderField()) && StringUtils.isNotBlank(searchVo.getOrderValue())) {
                if ("adSales".equalsIgnoreCase(searchVo.getOrderField())) {  //兼容处理
                    voList = PageUtil.sort(voList, "adSale", searchVo.getOrderValue());
                } else if ("saleNum".equalsIgnoreCase(searchVo.getOrderField())) {
                    voList = PageUtil.sort(voList, "adOrderNum", searchVo.getOrderValue());
                }else if ("cost".equalsIgnoreCase(searchVo.getOrderField())) {
                    voList = PageUtil.sort(voList, "adCost", searchVo.getOrderValue());
                } else  {
                    boolean isSorted = StringUtils.isNotBlank(searchVo.getOrderField()) && Constants.isADperformanceOrderField(searchVo.getOrderField());
                    if (isSorted) {
                        voList = PageUtil.sort(voList, searchVo.getOrderField(), searchVo.getOrderValue());
                    }
                }
            }
        }

        return ResultUtil.returnSucc(voList);
    }

    @Override
    public Result<List<TargetQueryReportVo>> getTargetQueryList(Integer puid, TargetQuerySearchVo searchVo) {
        List<TargetQueryReportVo> voList = new ArrayList<>();


        if (Constants.SD.equalsIgnoreCase(searchVo.getType())) {  // sd没有搜索词
            return ResultUtil.returnSucc(voList);
        }

        ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(searchVo.getShopId(), searchVo.getStartDate(), searchVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }
        searchVo.setSumShopSale(sumShopSale);

        if ("keyword".equalsIgnoreCase(searchVo.getTargetType())) {  //关键词
            voList = queryKeywordReportService.getListByKeywordId(puid, searchVo.getTargetId(), searchVo);
        } else {  //投放
            voList = queryTargetingReportService.getListByTargetId(puid, searchVo.getTargetId(), searchVo);
        }

        if (CollectionUtils.isNotEmpty(voList)) {
            //排序
            if (StringUtils.isNotBlank(searchVo.getOrderField()) && StringUtils.isNotBlank(searchVo.getOrderValue())) {
                if ("adSales".equalsIgnoreCase(searchVo.getOrderField())) {  //兼容处理
                    voList = PageUtil.sort(voList, "adSale", searchVo.getOrderValue());
                } else if ("saleNum".equalsIgnoreCase(searchVo.getOrderField())) {
                    voList = PageUtil.sort(voList, "adOrderNum", searchVo.getOrderValue());
                }else if ("cost".equalsIgnoreCase(searchVo.getOrderField())) {
                    voList = PageUtil.sort(voList, "adCost", searchVo.getOrderValue());
                } else  {
                    boolean isSorted = StringUtils.isNotBlank(searchVo.getOrderField()) && Constants.isADperformanceOrderField(searchVo.getOrderField());
                    if (isSorted) {
                        voList = PageUtil.sort(voList, searchVo.getOrderField(), searchVo.getOrderValue());
                    }
                }
            }
        }

        return ResultUtil.returnSucc(voList);
    }

    @Override
    public Result<List<QueryTargetReportVo>> getQueryTargetList(Integer puid, QueryTargetReportSearchVo searchVo) {
        ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(searchVo.getShopId(), searchVo.getStartDate(), searchVo.getEndDate());
        BigDecimal sumShopSale = BigDecimal.ZERO;
        if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
            sumShopSale = shopSaleDto.getSumRange();
        }
        searchVo.setSumShopSale(sumShopSale);

        List<QueryTargetReportVo> voList = new ArrayList<>();
        if ("keyword".equalsIgnoreCase(searchVo.getTargetType())) {
            QueryTargetReportVo keywordVo = amazonAdKeywordReportService.getReportVoByKeywordId(puid, searchVo);
            if (keywordVo != null) {
                voList.add(keywordVo);
                return ResultUtil.returnSucc(voList);
            }
        }
        if ("target".equalsIgnoreCase(searchVo.getTargetType())) {
            QueryTargetReportVo targetVo = cpcTargetingReportService.getReportVoByTargetId(puid, searchVo);
            if (targetVo != null) {
                voList.add(targetVo);
                return ResultUtil.returnSucc(voList);
            }
        }

        return ResultUtil.returnSucc(voList);
    }

    @Override
    public CpcCommPageVo getTargetSumVo(List<TargetReportVo> voList, TargetReportSearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        CpcCommPageVo vo = initializeDailyReport();  //初始化

        for (TargetReportVo reportVo : voList) {
            vo.setImpressions(reportVo.getImpressions() == null ? vo.getImpressions() : vo.getImpressions() + reportVo.getImpressions());
            vo.setClicks(reportVo.getClicks() == null ? vo.getClicks() : vo.getClicks() + reportVo.getClicks());
            vo.setAdOrderNum(reportVo.getAdOrderNum() == null ? vo.getAdOrderNum() : vo.getAdOrderNum() + reportVo.getAdOrderNum());
            if (reportVo.getAdCost() != null) {
                Double add = DoubleUtil.add(Double.valueOf(vo.getAdCost()), Double.valueOf(reportVo.getAdCost()));
                vo.setAdCost(String.valueOf(add));
            }
            if (reportVo.getAdSale() != null) {
                Double add = DoubleUtil.add(Double.valueOf(vo.getAdSale()), Double.valueOf(reportVo.getAdSale()));
                vo.setAdSale(String.valueOf(add));
            }
        }

        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }

        return vo;
    }

    @Override
    public CpcCommPageVo getQuerySumVo(List<QueryReportVo> voList, QueryReportSearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        CpcCommPageVo vo = initializeDailyReport();  //初始化

        for (QueryReportVo reportVo : voList) {
            vo.setImpressions(reportVo.getImpressions() == null ? vo.getImpressions() : vo.getImpressions() + reportVo.getImpressions());
            vo.setClicks(reportVo.getClicks() == null ? vo.getClicks() : vo.getClicks() + reportVo.getClicks());
            vo.setAdOrderNum(reportVo.getAdOrderNum() == null ? vo.getAdOrderNum() : vo.getAdOrderNum() + reportVo.getAdOrderNum());
            if (reportVo.getAdCost() != null) {
                Double add = DoubleUtil.add(Double.valueOf(vo.getAdCost()), Double.valueOf(reportVo.getAdCost()));
                vo.setAdCost(String.valueOf(add));
            }
            if (reportVo.getAdSale() != null) {
                Double add = DoubleUtil.add(Double.valueOf(vo.getAdSale()), Double.valueOf(reportVo.getAdSale()));
                vo.setAdSale(String.valueOf(add));
            }
        }

        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }

        return vo;
    }

    @Override
    public CpcCommPageVo getQueryTargetSumVo(List<TargetQueryReportVo> voList, TargetQuerySearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        CpcCommPageVo vo = initializeDailyReport();  //初始化

        for (TargetQueryReportVo reportVo : voList) {
            vo.setImpressions(reportVo.getImpressions() == null ? vo.getImpressions() : vo.getImpressions() + reportVo.getImpressions());
            vo.setClicks(reportVo.getClicks() == null ? vo.getClicks() : vo.getClicks() + reportVo.getClicks());
            vo.setAdOrderNum(reportVo.getAdOrderNum() == null ? vo.getAdOrderNum() : vo.getAdOrderNum() + reportVo.getAdOrderNum());
            if (reportVo.getAdCost() != null) {
                Double add = DoubleUtil.add(Double.valueOf(vo.getAdCost()), Double.valueOf(reportVo.getAdCost()));
                vo.setAdCost(String.valueOf(add));
            }
            if (reportVo.getAdSale() != null) {
                Double add = DoubleUtil.add(Double.valueOf(vo.getAdSale()), Double.valueOf(reportVo.getAdSale()));
                vo.setAdSale(String.valueOf(add));
            }
        }

        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }

        return vo;
    }

    private CpcCommPageVo initializeDailyReport() {
        CpcCommPageVo sum = new CpcCommPageVo();
        sum.setImpressions(0);
        sum.setClicks(0);
        sum.setAdCost("0");
        sum.setAdSale("0");
        sum.setAdOrderNum(0);
        return sum;
    }

    /**
     * 组装投放弹层数据
     */
    private void setTargetDetailsData(TargetReportDetailsVo detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale) {
        if ("sp".equalsIgnoreCase(detailsVo.getType())) {  //广告类型
            if ("keyword".equalsIgnoreCase(detailsVo.getTargetType())) {  //关键词还是投放
                amazonAdKeywordReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
            } else {  //投放
                cpcTargetingReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
            }
        } else { // sd
            // 只有投放
            targetingReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
        }
    }

    /**
     * 组装搜索词弹层数据
     * @param detailsVo
     * @param sumVo
     * @param sumShopSale
     */
    private void setQueryDetailsData(QueryReportDetailsVo detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale) {
        if ("keyword".equalsIgnoreCase(detailsVo.getTargetType())) {  //关键词
            queryKeywordReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
        } else {
            queryTargetingReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
        }
    }

    /**
     * 组装搜索词弹层每天数据
     */
    private void setQueryDetailsDay(AdReportDetailsVo adReportDetailsVo, QueryReportDetailsVo detailsVo) {
        if ("keyword".equalsIgnoreCase(detailsVo.getTargetType())) {  //关键词
            queryKeywordReportService.getQueryDetailsDay(adReportDetailsVo, detailsVo);
        } else {  //投放
            queryTargetingReportService.getQueryDetailsDay(adReportDetailsVo, detailsVo);
        }
        if (CollectionUtils.isEmpty(adReportDetailsVo.getList())) {
            adReportDetailsVo.setList(new ArrayList<>());
        }

    }

    /**
     * 组装搜索词 投放 弹层数据
     * @param detailsVo
     * @param sumVo
     * @param sumShopSale
     */
    private void setQueryTargetDetailsData(QueryTargetReportDetailsVo detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale) {
        if ("keyword".equalsIgnoreCase(detailsVo.getTargetType())) {  //关键词
            amazonAdKeywordReportService.getQueryDetailsSumVo(detailsVo, sumVo, sumShopSale);
        } else {
            cpcTargetingReportService.getQueryDetailsSumVo(detailsVo, sumVo, sumShopSale);
        }
    }

    /**
     * 组装搜索词 投放 弹层每天数据
     */
    private void setQueryTargetDetailsDay(AdReportDetailsVo adReportDetailsVo, QueryTargetReportDetailsVo detailsVo) {
        if ("keyword".equalsIgnoreCase(detailsVo.getTargetType())) {  //关键词
            amazonAdKeywordReportService.getQueryTargetDetailsDay(adReportDetailsVo, detailsVo);
        } else {  //投放
            cpcTargetingReportService.getQueryTargetDetailsDay(adReportDetailsVo, detailsVo);
        }

        if (CollectionUtils.isEmpty(adReportDetailsVo.getList())) {
            adReportDetailsVo.setList(new ArrayList<>());
        }
    }

    /**
     * 组装活动弹层数据
     */
    private void setCampaignDetailsData(CampaignReportDetails detailsVo,  CpcCommPageVo sumVo,  BigDecimal sumShopSale) {
        if ("sp".equalsIgnoreCase(detailsVo.getType())) {  //广告类型
            spCampaignReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
        } else { //sd
            sdCampaignReportService.getDetailsSumVo(detailsVo, sumVo, sumShopSale);
        }
    }

    /**
     * 组装活动弹层每天数据
     */
    private void setCampaignDetailsDay(AdReportDetailsVo adReportDetailsVo, CampaignReportDetails detailsVo) {
        if ("sp".equalsIgnoreCase(detailsVo.getType())) {  //广告类型
            spCampaignReportService.getCampaignDetailsDay(adReportDetailsVo, detailsVo);
        } else { //sd
            sdCampaignReportService.getCampaignDetailsDay(adReportDetailsVo, detailsVo);
        }

        if (CollectionUtils.isEmpty(adReportDetailsVo.getList())) {
            adReportDetailsVo.setList(new ArrayList<>());
        }
    }

    /**
     * 组装投放弹层每天数据
     */
    private void setTargetDetailsDay(AdReportDetailsVo adReportDetailsVo, TargetReportDetailsVo detailsVo) {
        if ("sp".equalsIgnoreCase(detailsVo.getType())) {  //广告类型
            if ("keyword".equalsIgnoreCase(detailsVo.getTargetType())) {  //关键词还是投放
                amazonAdKeywordReportService.getTargetDetailsDay(adReportDetailsVo, detailsVo);
            } else {  //投放
                cpcTargetingReportService.getTargetDetailsDay(adReportDetailsVo, detailsVo);
            }
        } else { // sd
            // 只有投放
            targetingReportService.getTargetDetailsDay(adReportDetailsVo, detailsVo);
        }

        if (CollectionUtils.isEmpty(adReportDetailsVo.getList())) {
            adReportDetailsVo.setList(new ArrayList<>());
        }
    }

    /**
     * 组装报告数据
     * @param daoSumVo
     * @param vo
     * @param sumShopSale
     */
    private void setReportData(AmazonAdProductAggregationReport daoSumVo, CpcCommPageVo vo, BigDecimal sumShopSale) {
        if (daoSumVo == null || daoSumVo.getShopId() == null) {
            return;
        }

        vo.setImpressions(daoSumVo.getImpressions() != null ? daoSumVo.getImpressions() : 0);
        vo.setClicks(daoSumVo.getClicks() != null ? daoSumVo.getClicks() : 0);
        vo.setAdCost(daoSumVo.getCost() != null ? String.valueOf(daoSumVo.getCost().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");
        vo.setAdOrderNum(daoSumVo.getSaleNum() != null ? daoSumVo.getSaleNum() : 0);
        vo.setAdSale(daoSumVo.getAdSales() != null ?  String.valueOf(daoSumVo.getAdSales().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");
        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }
    }

    /**
     * 组装按天数据
     * @param aggregationReportList
     * @param adReportDetailsVo
     */
    private void setAggregationReportData(List<AmazonAdProductAggregationReport> aggregationReportList, AdReportDetailsVo adReportDetailsVo) {
        List<CpcCommPageVo> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(aggregationReportList)) {
            adReportDetailsVo.setList(list);
            return;
        }
        CpcCommPageVo vo;
        for (AmazonAdProductAggregationReport report : aggregationReportList) {
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(report.getShopId(), report.getCountDate(), report.getCountDate());
            BigDecimal sumShopSale = BigDecimal.ZERO;
            if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
                sumShopSale = shopSaleDto.getSumRange();
            }
            vo = new CpcCommPageVo();
            setReportData(report, vo, sumShopSale);
            vo.setCountDate(report.getCountDate());
            list.add(vo);
        }

        adReportDetailsVo.setList(list);
    }

    /**
     * 填充日期数据
     */
    private void setDayData(List<CpcCommPageVo> list, Date start, Date end) {

        List<String> countDates = list.stream().filter(Objects::nonNull).
                map(CpcCommPageVo::getCountDate).collect(Collectors.toList());
        List<String> dateList =getDateList(DateUtil.dateToStrWithFormat(start, "yyyy-MM-dd"),
                DateUtil.dateToStrWithFormat(end, "yyyy-MM-dd"));
        CpcCommPageVo commPageVo;
        for (String item : dateList) {
            if (!countDates.contains(item)) {
                commPageVo = new CpcCommPageVo();
                commPageVo.setImpressions(0);
                commPageVo.setClicks(0);
                commPageVo.setCtr("0");
                commPageVo.setCvr("0");
                commPageVo.setAcos("0");
                commPageVo.setRoas("0");
                commPageVo.setAcots("0");
                commPageVo.setAsots("0");
                commPageVo.setAdOrderNum(0);
                commPageVo.setAdCost("0");
                commPageVo.setAdCostPerClick("0");
                commPageVo.setAdSale("0");
                commPageVo.setCountDate(item);
                list.add(commPageVo);
            }
        }
        //重新按日期排序
        Collections.sort(list, new Comparator<CpcCommPageVo>() {
            @Override
            public int compare(CpcCommPageVo o1, CpcCommPageVo o2) {
                return o1.getCountDate().compareTo(o2.getCountDate());
            }
        });
    }

    /**
     * 获取汇总报告对比时间段
     * @param start
     * @param end
     * @return
     */
    private Map<String,String> getLastTime(Date start,Date end){
        Map<String,String> map = Maps.newHashMapWithExpectedSize(2);
        //获取时间差
        int days = DateUtil.getDayBetween(start,end);
        end = DateUtil.addDay(start,-1);
        map.put("endStr",DateUtil.dateToStrWithFormat(end,"yyyyMMdd"));
        map.put("startStr",DateUtil.dateToStrWithFormat(DateUtil.addDay(end,-days),"yyyyMMdd"));
        return map;
    }

    private List<String> getDateList(String start, String end) {
        //日期工具类准备
        List<String> Datelist = Lists.newArrayList();
        Date startDate = DateUtil.stringToDate(start);
        Date endDate = DateUtil.stringToDate(end);
        endDate = DateUtil.addDay(endDate, 1);
        // startDate >= endDate   都会返回false
        while (startDate.before(endDate))  {
            Datelist.add(DateUtil.dateToStrWithFormat(startDate,DateUtil.PATTERN_YYYYMMDD));
            startDate = DateUtil.addDay(startDate, 1);
        }
        return Datelist;
    }

    private Page<AmazonAdProductAggregationReport> getPageList(Integer puid,AdProductSearchVo searchVo){
        return  aggregationReportDao.getPageList(puid, searchVo);
    }

    private List<AmazonAdProductAggregationReport> getListBySearch(Integer puid,AdProductSearchVo searchVo){
        return  aggregationReportDao.getListBySearch(puid, searchVo);
    }

    private AmazonAdProductAggregationReport getDetailsSumVo(Integer puid, Integer shopId, String type, String asin, String startStr, String endStr){
        return aggregationReportDao.getDetailsSumVo(puid, shopId,  type, asin,  startStr, endStr);
    }
    private List<AmazonAdProductAggregationReport> getReportListByDay(Integer puid, ProductReportDetailsVo detailsVo){
        return  aggregationReportDao.getReportListByDay( puid,  detailsVo);
    }

    private List<AmazonAdProductAggregationReport> getListByAsin(Integer puid, Integer shopId, Date start, Date end, String type, String asin){
        return  aggregationReportDao.getListByAsin(puid,  shopId, start,  end, type, asin);
    }

    private List<AmazonAdProductAggregationReport> getListByParentAsin(Integer puid, Integer shopId, Date start, Date end, String type, String asin){
        return  aggregationReportDao.getListByParentAsin(puid,  shopId, start,  end, type, asin);
    }

    private Page<AmazonAdProductAggregationReport> getPageListGroupByParentAsin(Integer puid,AdProductSearchVo searchVo){
        return  aggregationReportDao.getPageListGroupByParentAsin(puid, searchVo);
    }
}
