package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.sb.entity.bidRecommendation.*;
import com.amazon.advertising.sb.entity.keywordRecommendation.SBKeywordSuggestion;
import com.amazon.advertising.sb.entity.themeTargeting.ThemeUpdateResult;
import com.amazon.advertising.sb.entity.themeTargeting.UpdateThemeTargetingResponse;
import com.amazon.advertising.sb.mode.keyword.Keyword;
import com.amazon.advertising.sb.mode.themeTargeting.SbTheme;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.qo.KeywordSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonSbAdKeyword;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbKeywordService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordSb;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.MatchTypeEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/8/3.
 */
@Service
@Slf4j
public class CpcSbKeywordServiceImpl implements ICpcSbKeywordService {
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcSbKeyRecommendationApiService cpcSbKeyRecommendationApiService;
    @Autowired
    private CpcSbBidApiService cpcSbBidApiService;
    @Autowired
    private IAmazonSbAdGroupDao groupDao;
    @Autowired
    private IAmazonSbAdKeywordDao keywordDao;
    @Autowired
    private CpcSbKeywordApiService cpcSbKeywordApiService;
    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private ICpcSbGroupService cpcSbGroupService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonAdCampaignAllDao campaignAllDao;
    @Autowired
    private IWordTranslateService wordTranslateService;

    @Override
    public Result update(Integer puid, Integer shopId, Integer uid, Long id, String state, Double bid, String ip) {
        AmazonSbAdKeyword sbAdKeyword = keywordDao.getByPuidAndId(puid, id);
        if (sbAdKeyword == null) {
            return ResultUtil.error("没有关键词信息");
        }

        String keywordId = sbAdKeyword.getKeywordId();
        if (StringUtils.isBlank(keywordId)) {
            return ResultUtil.error("平台keyword id 为空, 请同步该活动在操作");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSbAdKeyword oldKeyword = new AmazonSbAdKeyword();
        BeanUtils.copyProperties(sbAdKeyword, oldKeyword);

        if("theme".equalsIgnoreCase(sbAdKeyword.getMatchType())) {
            if (StringUtils.isNotBlank(state)) {
                sbAdKeyword.setState(state);
            }
            if (bid != null) {
                sbAdKeyword.setBid(BigDecimal.valueOf(bid));
            }

            SbTheme sbTheme = new SbTheme();
            sbTheme.setThemeId(keywordId);
            sbTheme.setAdGroupId(sbAdKeyword.getAdGroupId());
            sbTheme.setCampaignId(sbAdKeyword.getCampaignId());
            sbTheme.setState(state);
            sbTheme.setBid(bid);

            UpdateThemeTargetingResponse response = cpcSbKeywordApiService.modifyTheme(shop, profile, Collections.singletonList(sbTheme));

            ThemeUpdateResult result = response.getResult();
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(result.getSuccess())) {
                Result error = ResultUtil.error(AmazonErrorUtils.getError(StringUtils.isNotBlank(result.getError().get(0).getDetails()) ?
                    result.getError().get(0).getDetails() : result.getError().get(0).getDescription()));
                logSbKeywordsUpdate(oldKeyword, sbAdKeyword, ip, error);
                return error;
            }
            sbAdKeyword.setUpdateId(uid);
            keywordDao.updateByIdAndPuid(puid, sbAdKeyword);
            saveDoris(Collections.singletonList(sbAdKeyword), false, true);
            logSbKeywordsUpdate(oldKeyword, sbAdKeyword, ip, ResultUtil.success());
            return ResultUtil.success();
        }else {
            List<Keyword> keywordList = new ArrayList<>(1);
            Keyword keyword = new Keyword();
            keyword.setKeywordId(Long.valueOf(sbAdKeyword.getKeywordId()));
            keyword.setCampaignId(Long.valueOf(sbAdKeyword.getCampaignId()));
            keyword.setAdGroupId(Long.valueOf(sbAdKeyword.getAdGroupId()));
            if (StringUtils.isNotBlank(state)) {
                keyword.setState(state);
                sbAdKeyword.setState(state);
            }
            if (bid != null) {
                keyword.setBid(bid);
                sbAdKeyword.setBid(BigDecimal.valueOf(bid));
            }

            keywordList.add(keyword);

            Result result = cpcSbKeywordApiService.update(shop, profile, keywordList);
            if (result.success()) {
                sbAdKeyword.setUpdateId(uid);
                keywordDao.updateByIdAndPuid(puid, sbAdKeyword);
                saveDoris(Collections.singletonList(sbAdKeyword), false, true);
            }
            logSbKeywordsUpdate(oldKeyword, sbAdKeyword, ip, result);
            return result;
        }
    }

    @Override
    public Result archive(Integer puid, Integer shopId, Integer uid, Long id, String ip) {
        AmazonSbAdKeyword sbAdKeyword = keywordDao.getByPuidAndId(puid, id);
        if (sbAdKeyword == null) {
            return ResultUtil.error("没有关键词信息");
        }

        String keywordId = sbAdKeyword.getKeywordId();
        if (StringUtils.isBlank(keywordId)) {
            return ResultUtil.error("平台keyword id 为空, 请同步该活动在操作");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonSbAdKeyword oldKeyword = new AmazonSbAdKeyword();
        BeanUtils.copyProperties(sbAdKeyword, oldKeyword);

        Result result = cpcSbKeywordApiService.archive(shop, profile, keywordId);
        if (result.success()) {
            sbAdKeyword.setUpdateId(uid);
            sbAdKeyword.setState(CpcStatusEnum.archived.name());
            keywordDao.updateByIdAndPuid(puid, sbAdKeyword);
            saveDoris(Collections.singletonList(sbAdKeyword), false, true);
        }
        logSbKeywordsUpdate(oldKeyword, sbAdKeyword, ip, result);
        return result;
    }

    private void logSbKeywordsUpdate(AmazonSbAdKeyword oldKeyword, AmazonSbAdKeyword keyword, String ip, Result result) {
        try {
            AdManageOperationLog operationLog = adManageOperationLogService.getSbKeywordLog(oldKeyword, keyword);
            operationLog.setIp(ip);
            if (result.success()){
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            }else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(result.getMsg());
            }
            adManageOperationLogService.printAdOperationLog(Lists.newArrayList(operationLog));
        } catch (Exception e) {
            log.error("sb关键词日志异常", e);
        }
    }

    @Override
    public Result<List<SBKeywordSuggestion>> getKeywordRecommendationList(int puid, Integer shopId, List<String> asinList, String url) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<SBKeywordSuggestion> suggestionList = null;
        Result<List<SBKeywordSuggestion>> recommendationList = cpcSbKeyRecommendationApiService.getKeywordRecommendationList(shop, profile, asinList,
                url, 100, null, null);
        if (recommendationList.success()) {
            suggestionList = recommendationList.getData();
        }
        return ResultUtil.returnSucc(suggestionList);
    }

    @Override
    public Result<SuggestedKeywordPageVo> getKeywordRecommendationListNew(int puid, Integer shopId,
                                                                          List<String> asinList, String url,
                                                                          Integer adGoal, Integer adFormat,
                                                                          Integer showExact, Integer showBroad,
                                                                          Integer showPhrase, String searchVal,
                                                                          Integer pageNo, Integer pageSize,
                                                                          List<String> creativeAsins, String campaignId, String groupId) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<List<SBKeywordSuggestion>> recommendationList;
        if (StringUtils.isNotBlank(campaignId) && StringUtils.isNotBlank(groupId)) {
            recommendationList = getSBKeywordSuggestionResultByCampaignIdAndGroupId(shop, profile, campaignId, groupId);
        } else {
            if (StringUtils.isEmpty(url) && CollectionUtils.isEmpty(asinList)) {
                return ResultUtil.returnErr("需要获取关键词的Asin或者Url不能同时为空");
            }
            String goal = Optional.ofNullable(adGoal).map(SBCampaignGoalEnum::getSBCampaignGoalEnumByCode)
                .map(SBCampaignGoalEnum::getType).orElse("");
            String creativeType = Optional.ofNullable(adFormat)
                .map(SBKeywordRecommendationCreativeTypeEnum::getSBKeywordRecommendationCreativeTypeEnumByCode).map(SBKeywordRecommendationCreativeTypeEnum::getVal).orElse("");

            recommendationList = cpcSbKeyRecommendationApiService.getKeywordRecommendationListNew(shop, profile, asinList,
                url, null, goal, creativeType, creativeAsins);
        }
        List<SuggestedKeywordPageVo.SbKeywordSuggestion> suggestionList = null;
        //需要对亚马逊结果，按照前端查询参数进行过滤，假分页
        if (recommendationList.success() && CollectionUtils.isNotEmpty(recommendationList.getData())) {
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = recommendationList.getData().stream().map(SBKeywordSuggestion::getValue).distinct()
                    .map(e -> new WordTranslateQo(shop.getMarketplaceId(), e)).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);

            suggestionList = recommendationList.getData().stream().map(e -> {
                SuggestedKeywordPageVo.SbKeywordSuggestion sbKeywordSuggestion = new SuggestedKeywordPageVo.SbKeywordSuggestion();
                sbKeywordSuggestion.setMatchType(e.getMatchType());
                sbKeywordSuggestion.setTranslation(e.getTranslation());
                sbKeywordSuggestion.setRecommendationId(e.getRecommendationId());
                sbKeywordSuggestion.setType(e.getType());
                sbKeywordSuggestion.setValue(e.getValue());
                sbKeywordSuggestion.setValueCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(shop.getMarketplaceId(), e.getValue())));
                return sbKeywordSuggestion;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(suggestionList)) {
                if (1 == showExact) {
                    suggestionList = suggestionList.parallelStream().filter(k -> !MatchTypeEnum.EXACT.getMatchType().equals(k.getMatchType())).collect(Collectors.toList());
                }
                if (1 == showBroad) {
                    suggestionList = suggestionList.parallelStream().filter(k -> !MatchTypeEnum.BROAD.getMatchType().equals(k.getMatchType())).collect(Collectors.toList());
                }
                if (1 == showPhrase) {
                    suggestionList = suggestionList.parallelStream().filter(k -> !MatchTypeEnum.PHRASE.getMatchType().equals(k.getMatchType())).collect(Collectors.toList());
                }
                if (org.apache.commons.lang3.StringUtils.isNotEmpty(searchVal)) {
                    suggestionList = suggestionList.parallelStream()
                            .filter(k -> org.apache.commons.lang3.StringUtils.isNotEmpty(k.getValue()))
                            .filter(k -> k.getValue().contains(searchVal)).collect(Collectors.toList());
                }
            }
        }
        return ResultUtil.returnSucc(getRecommendKeywordListPage(suggestionList, pageSize, pageNo));
    }

    private Result<List<SBKeywordSuggestion>> getSBKeywordSuggestionResultByCampaignIdAndGroupId(ShopAuth shop, AmazonAdProfile profile,  String campaignId, String groupId){

        List<String> asinByGroup = amazonSbAdsDao.getAsinByGroup(shop.getPuid(), shop.getId(), campaignId, groupId);
        if (CollectionUtils.isEmpty(asinByGroup)) {
            return ResultUtil.success();
        }

        Set<String> asinList = new HashSet<>();
        for (String asin : asinByGroup){
            if(StringUtils.isNotBlank(asin)){
                List<String> list = StringUtil.splitStr(asin);
                if(CollectionUtils.isNotEmpty(list)){
                    asinList.addAll(list);
                }
            }
        }

        List<SBKeywordSuggestion> suggestionList = null;
        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.returnSucc(suggestionList);
        }


        Result<List<SBKeywordSuggestion>> recommendationList = cpcSbKeyRecommendationApiService.getKeywordRecommendationList(shop, profile, Lists.newArrayList(asinList),
            null, null, null, null);
        if (recommendationList.success()) {
            suggestionList = recommendationList.getData();
        }
        return ResultUtil.returnSucc(suggestionList);
    }

    @Override
    public Result<List<SBKeywordSuggestion>> addSuggestKeyword(Integer puid, Integer shopId, String campaignId, String groupId) {

        List<String> asinByGroup = amazonSbAdsDao.getAsinByGroup(puid, shopId, campaignId, groupId);
        if (CollectionUtils.isEmpty(asinByGroup)) {
            return ResultUtil.success();
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Set<String> asinList = new HashSet<>();
        for (String asin : asinByGroup){
            if(StringUtils.isNotBlank(asin)){
                List<String> list = StringUtil.splitStr(asin);
                if(CollectionUtils.isNotEmpty(list)){
                    asinList.addAll(list);
                }
            }
        }

        List<SBKeywordSuggestion> suggestionList = null;
        if (CollectionUtils.isEmpty(asinList)) {
            return ResultUtil.returnSucc(suggestionList);
        }


        Result<List<SBKeywordSuggestion>> recommendationList = cpcSbKeyRecommendationApiService.getKeywordRecommendationList(shop, profile, Lists.newArrayList(asinList),
                null, 100, null, null);
        if (recommendationList.success()) {
            suggestionList = recommendationList.getData();
        }
        return ResultUtil.returnSucc(suggestionList);
    }

    @Override
    public Result getKeywordBids(Integer puid, Integer shopId, String campaignId, String groupId, List<KeywordsVo> keywords) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        // 接口要求：不能超过100个
        List<List<KeywordsVo>> lists = Lists.partition(keywords, 100);
        for (List<KeywordsVo> subList : lists) {

            List<SBBidRecommendationKeyword> keywordList = subList.stream().map(e -> {
                SBBidRecommendationKeyword keyword = new SBBidRecommendationKeyword();
                keyword.setMatchType(e.getMatchType());
                keyword.setKeywordText(e.getKeywordText());
                return keyword;
            }).collect(Collectors.toList());

            Long camId = null;
            if (StringUtils.isNotBlank(campaignId)) {
                camId = Long.valueOf(campaignId);
            }
            AmazonAdCampaignAll campaignAll = campaignAllDao.getByCampaignId(puid, shopId, campaignId);
            String sbFormatByGroupId = amazonSbAdsDao.getSbFormatByGroupId(puid, shopId, groupId);
            Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId,
                    sbFormatByGroupId, keywordList, null, campaignAll.getAdGoal());
            if (resultResult.success()) {
                BidRecommendationResult result = resultResult.getData();
                if (result == null) {
                    break;
                }
                List<KeywordsBidsSuccessResults> successList = result.getKeywordsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) {
                    break;
                }
                for (KeywordsBidsSuccessResults successResult : successList) {
                    Integer index = successResult.getKeywordIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        KeywordsVo keywordsVo = subList.get(index);
                        if (recommendedBid.getRecommended() != null) {
                            keywordsVo.setSuggested(recommendedBid.getRecommended().toString());
                        }
                        if (recommendedBid.getRangeStart() != null) {
                            keywordsVo.setRangeStart(recommendedBid.getRangeStart().toString());
                        }
                        if (recommendedBid.getRangeEnd() != null) {
                            keywordsVo.setRangeEnd(recommendedBid.getRangeEnd().toString());
                        }
                    }
                }
            }
        }

        return ResultUtil.returnSucc(keywords);
    }

    @Override
    public Result getKeywordBidsNew(Integer puid, Integer shopId,
                                    String adFormat, List<KeywordsVo> keywords,
                                    String goal, String costType) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        // 接口要求：不能超过100个
        List<List<KeywordsVo>> lists = Lists.partition(keywords, 100);
        for (List<KeywordsVo> subList : lists) {

            List<SBBidRecommendationKeyword> keywordList = subList.stream().map(e -> {
                SBBidRecommendationKeyword keyword = new SBBidRecommendationKeyword();
                keyword.setMatchType(e.getMatchType());
                keyword.setKeywordText(e.getKeywordText());
                return keyword;
            }).collect(Collectors.toList());

            Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBidsNew(shop, profile, null,
                    adFormat, costType, keywordList, null, null, goal);
            if (resultResult.success()) {
                BidRecommendationResult result = resultResult.getData();
                if (result == null) {
                    break;
                }
                List<KeywordsBidsSuccessResults> successList = result.getKeywordsBidsRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) {
                    break;
                }
                for (KeywordsBidsSuccessResults successResult : successList) {
                    Integer index = successResult.getKeywordIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        KeywordsVo keywordsVo = subList.get(index);
                        keywordsVo.setIndex(index);
                        if (recommendedBid.getRecommended() != null) {
                            keywordsVo.setSuggested(recommendedBid.getRecommended().toString());
                        }
                        if (recommendedBid.getRangeStart() != null) {
                            keywordsVo.setRangeStart(recommendedBid.getRangeStart().toString());
                        }
                        if (recommendedBid.getRangeEnd() != null) {
                            keywordsVo.setRangeEnd(recommendedBid.getRangeEnd().toString());
                        }
                    }
                }
            }
        }
        return ResultUtil.returnSucc(keywords);
    }

    @Override
    public Result<List<ThemeBidVo>> getThemesBidsNew(Integer puid, Integer shopId,
                                                     String adFormat, List<ThemesVo> themesList,
                                                     String goal, String costType) {
        List<ThemeBidVo> themeBidResultList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(themesList)) {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
            if (shop == null) {
                return ResultUtil.returnErr("没有CPC授权");
            }

            AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
            if (profile == null) {
                return ResultUtil.returnErr("没有站点对应的配置信息");
            }

            List<String> themeTypeList = themesList.stream().map(ThemesVo::getThemeType).collect(Collectors.toList());
            Result<BidRecommendationResult> themeResult = cpcSbBidApiService.getKeywordAndTargetBidsNew(shop, profile, null,
                    adFormat, costType, null, null, themeTypeList, goal);
            if (themeResult.success()) {
                BidRecommendationResult result = themeResult.getData();
                if (result == null) {
                    return ResultUtil.returnSucc(Collections.emptyList());
                }
                List<ThemesBidsSuccessResults> successList = result.getThemesRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successList)) {
                    return ResultUtil.returnSucc(Collections.emptyList());
                }
                for (ThemesBidsSuccessResults successResult : successList) {
                    Integer index = successResult.getThemeIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid != null && index != null) {
                        ThemesVo themesVo = themesList.get(index);
                        ThemeBidVo themeBidVo = new ThemeBidVo();
                        themeBidVo.setThemesType(themesVo.getThemeType());
                        if (recommendedBid.getRecommended() != null) {
                            themeBidVo.setSuggested(recommendedBid.getRecommended().toString());
                        }
                        if (recommendedBid.getRangeStart() != null) {
                            themeBidVo.setRangeStart(recommendedBid.getRangeStart().toString());
                        }
                        if (recommendedBid.getRangeEnd() != null) {
                            themeBidVo.setRangeEnd(recommendedBid.getRangeEnd().toString());
                        }
                        themeBidResultList.add(themeBidVo);
                    }
                }
            }
        }
        return ResultUtil.returnSucc(themeBidResultList);
    }

    @Override
    public Result<List<SuggestedKeywordVo>> getSuggestedBid(int puid, Integer shopId, List<String> keywordIds) {

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        List<AmazonSbAdKeyword> keywordList = keywordDao.listByKeywordId(puid, shopId, keywordIds);
        if (CollectionUtils.isEmpty(keywordList)) {
            return ResultUtil.returnErr("对象不存在");
        }

        // 过滤出有效的, 没有有效的也不返回报错
        keywordList = keywordList.stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keywordList)) {
            return ResultUtil.success(new ArrayList<>());
        }

        Map<String, AmazonSbAdKeyword> keywordMap = keywordList.stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity()));

        // 根据campaign分组查询建议竞价
        Map<String, List<AmazonSbAdKeyword>> adKeywordMap = keywordList.stream().collect(Collectors.groupingBy(AmazonSbAdKeyword::getAdGroupId));
        List<String> adGroupIds = adKeywordMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getAdGroupByIds(puid, shopId, adGroupIds);
        if (CollectionUtils.isEmpty(adGroups)){
            return ResultUtil.returnErr("没有广告组信息");
        }

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = campaignAllDao.listByCampaignIds(puid, shopId, campaignIds).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SuggestedKeywordVo>>> completableFutures = new ArrayList<>();
        List<SuggestedKeywordVo> suggestedKeywordVoList = new ArrayList<>();
        adGroups.forEach(adGroup-> completableFutures.add(CompletableFuture.supplyAsync(()->{
            Long camId = Optional.ofNullable(adGroup.getCampaignId()).filter(StringUtils::isNotBlank).map(Long::valueOf).orElse(null);
            String adFormat = adGroup.getAdFormat();
            String goal = null;
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(adGroup.getCampaignId());
            if (amazonAdCampaignAll != null) {
                goal = amazonAdCampaignAll.getAdGoal();
                if ("VIDEO".equalsIgnoreCase(adFormat) && "BRAND_IMPRESSION_SHARE".equalsIgnoreCase(goal)) {
                    adFormat = null;
                }
            }
            List<SuggestedKeywordVo> suggestedKeywordVos = adKeywordMap.get(adGroup.getAdGroupId()).stream().map(e -> {
                SuggestedKeywordVo suggestedBidVo = new SuggestedKeywordVo();
                suggestedBidVo.setKeywordId(e.getKeywordId());
                suggestedBidVo.setMatchType(e.getMatchType());
                suggestedBidVo.setKeywordText(e.getKeywordText());
                suggestedBidVo.setSuggested(Optional.ofNullable(e.getSuggested()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeStart(Optional.ofNullable(e.getRangeStart()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeEnd(Optional.ofNullable(e.getRangeEnd()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                return suggestedBidVo;
            }).collect(Collectors.toList());

            SBCampaignGoalEnum goalEn = SBCampaignGoalEnum.getSBCampaignGoalEnumByType(goal);
            String costType = null;
            if (Objects.nonNull(goalEn)) {
                if (SBCampaignGoalEnum.PAGE_VISIT == goalEn) {
                    costType = SBCampaignCostTypeEnum.CPC.getCode();
                }
                if (SBCampaignGoalEnum.BRAND_IMPRESSION_SHARE == goalEn) {
                    costType = SBCampaignCostTypeEnum.VCPM.getCode();
                }
            }
            String finalGoal = goal;
            String finalAdFormat = adFormat;
            String finalCostType = costType;
            Lists.partition(suggestedKeywordVos, 100).forEach(subList -> {
                List<SuggestedKeywordVo> keywordsList = subList.stream().filter(Objects::nonNull).filter(k->!"theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());
                List<SuggestedKeywordVo> themeList = subList.stream().filter(Objects::nonNull).filter(k -> "theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());

                List<SBBidRecommendationKeyword> queryKeywordsList = keywordsList.stream().map(e -> {
                    SBBidRecommendationKeyword keyword = new SBBidRecommendationKeyword();
                    keyword.setMatchType(e.getMatchType());
                    keyword.setKeywordText(e.getKeywordText());
                    return keyword;
                }).collect(Collectors.toList());

                List<String> queryThemeList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(themeList)) {
                    queryThemeList = Arrays.asList(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_BRAND.getValue(), SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getValue());
                }
                Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBidsNew(shop, profile, camId, finalAdFormat, finalCostType, queryKeywordsList, null,queryThemeList, finalGoal);

                //Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId, finalAdFormat, keywordsList, null, finalGoal);
                BidRecommendationResult result;
                if (!resultResult.success() || (result = resultResult.getData()) == null) return;
                List<KeywordsBidsSuccessResults> successKeywordList = result.getKeywordsBidsRecommendationSuccessResults();
                List<ThemesBidsSuccessResults> successThemeList = result.getThemesRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successKeywordList) && CollectionUtils.isEmpty(successThemeList)) return;

                successKeywordList.forEach(successResult-> {
                    Integer index = successResult.getKeywordIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid == null || index == null) return;
                    SuggestedKeywordVo returnKeywordsVo = keywordsList.get(index);
                    returnKeywordsVo.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(returnKeywordsVo.getSuggested()));
                    returnKeywordsVo.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(returnKeywordsVo.getRangeStart()));
                    returnKeywordsVo.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(returnKeywordsVo.getRangeEnd()));

                    AmazonSbAdKeyword updateAmazonSbAdKeyword = keywordMap.get(returnKeywordsVo.getKeywordId());
                    if (updateAmazonSbAdKeyword == null) return;
                    updateAmazonSbAdKeyword.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getSuggested()));
                    updateAmazonSbAdKeyword.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeStart()));
                    updateAmazonSbAdKeyword.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeEnd()));
                });
                themeList.forEach(theme-> {
                    if (CollectionUtils.isNotEmpty(successThemeList)) {
                        String keywordText = theme.getKeywordText();
                        List<ThemesBidsSuccessResults> collect = successThemeList.stream().filter(k -> keywordText.equalsIgnoreCase(k.getThemeType())).collect(Collectors.toList());
                        RecommendedBid recommendedBid = collect.get(0).getRecommendedBid();
                        theme.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(theme.getSuggested()));
                        theme.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(theme.getRangeStart()));
                        theme.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(theme.getRangeEnd()));

                        AmazonSbAdKeyword updateAmazonSbAdKeyword = keywordMap.get(theme.getKeywordId());
                        if (updateAmazonSbAdKeyword == null) return;
                        updateAmazonSbAdKeyword.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getSuggested()));
                        updateAmazonSbAdKeyword.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeStart()));
                        updateAmazonSbAdKeyword.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeEnd()));
                    }
                });
            });
            return suggestedKeywordVos;
        }, executor)));

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedKeywordVoList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("sb keywords getSuggestedBid error, puid:{}, shopId:{}", puid, shop.getId(), e);
            }
        });

        // 批量更新建议竞价
        keywordDao.batchUpdateSuggestValue(puid, Lists.newArrayList(keywordMap.values()));
        return ResultUtil.returnSucc(suggestedKeywordVoList);
    }

    public Result<List<SuggestedKeywordVo>> batchGetSuggestedBid(int puid, Integer shopId, List<KeywordSuggestedBidDetail> details) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        // 根据campaign分组查询建议竞价
        Map<String, List<KeywordSuggestedBidDetail>> adKeywordMap = details.stream().collect(Collectors.groupingBy(KeywordSuggestedBidDetail::getAdGroupId));
        List<String> adGroupIds = adKeywordMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getAdGroupByIds(puid, shopId, adGroupIds);
        if (CollectionUtils.isEmpty(adGroups)){
            return ResultUtil.returnErr("没有广告组信息");
        }

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = campaignAllDao.listByCampaignIds(puid, shopId, campaignIds).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));


        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SuggestedKeywordVo>>> completableFutures = new ArrayList<>();
        List<SuggestedKeywordVo> suggestedKeywordVoList = new ArrayList<>();
        adGroups.forEach(adGroup-> completableFutures.add(CompletableFuture.supplyAsync(()->{
            Long camId = Optional.ofNullable(adGroup.getCampaignId()).filter(StringUtils::isNotBlank).map(Long::valueOf).orElse(null);
            String adFormat = adGroup.getAdFormat();
            String goal = null;
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(adGroup.getCampaignId());
            if (amazonAdCampaignAll != null) {
                goal = amazonAdCampaignAll.getAdGoal();
                if ("VIDEO".equalsIgnoreCase(adFormat) && "BRAND_IMPRESSION_SHARE".equalsIgnoreCase(goal)) {
                    adFormat = null;
                }
            }
            List<SuggestedKeywordVo> suggestedKeywordVos = adKeywordMap.get(adGroup.getAdGroupId()).stream().map(e -> {
                SuggestedKeywordVo suggestedBidVo = new SuggestedKeywordVo();
                suggestedBidVo.setMatchType(e.getMatchType());
                suggestedBidVo.setKeywordText(e.getKeyword());
                suggestedBidVo.setIndex(e.getIndex());
                return suggestedBidVo;
            }).collect(Collectors.toList());

            SBCampaignGoalEnum goalEn = SBCampaignGoalEnum.getSBCampaignGoalEnumByType(goal);
            String costType = null;
            if (Objects.nonNull(goalEn)) {
                if (SBCampaignGoalEnum.PAGE_VISIT == goalEn) {
                    costType = SBCampaignCostTypeEnum.CPC.getCode();
                }
                if (SBCampaignGoalEnum.BRAND_IMPRESSION_SHARE == goalEn) {
                    costType = SBCampaignCostTypeEnum.VCPM.getCode();
                }
            }
            String finalGoal = goal;
            String finalAdFormat = adFormat;
            String finalCostType = costType;
            Lists.partition(suggestedKeywordVos, 100).forEach(subList -> {
                List<SuggestedKeywordVo> keywordsList = subList.stream().filter(Objects::nonNull).filter(k->!"theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());
                List<SuggestedKeywordVo> themeList = subList.stream().filter(Objects::nonNull).filter(k -> "theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());

                List<SBBidRecommendationKeyword> queryKeywordsList = keywordsList.stream().map(e -> {
                    SBBidRecommendationKeyword keyword = new SBBidRecommendationKeyword();
                    keyword.setMatchType(e.getMatchType());
                    keyword.setKeywordText(e.getKeywordText());
                    return keyword;
                }).collect(Collectors.toList());

                List<String> queryThemeList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(themeList)) {
                    queryThemeList = Arrays.asList(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_BRAND.getValue(), SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getValue());
                }
                Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBidsNew(shop, profile, camId, finalAdFormat, finalCostType, queryKeywordsList, null,queryThemeList, finalGoal);

                //Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId, finalAdFormat, keywordsList, null, finalGoal);
                BidRecommendationResult result;
                if (!resultResult.success() || (result = resultResult.getData()) == null) return;
                List<KeywordsBidsSuccessResults> successKeywordList = result.getKeywordsBidsRecommendationSuccessResults();
                List<ThemesBidsSuccessResults> successThemeList = result.getThemesRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successKeywordList) && CollectionUtils.isEmpty(successThemeList)) return;

                successKeywordList.forEach(successResult-> {
                    Integer index = successResult.getKeywordIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid == null || index == null) return;
                    SuggestedKeywordVo returnKeywordsVo = keywordsList.get(index);
                    returnKeywordsVo.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(returnKeywordsVo.getSuggested()));
                    returnKeywordsVo.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(returnKeywordsVo.getRangeStart()));
                    returnKeywordsVo.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(returnKeywordsVo.getRangeEnd()));
                });
                themeList.forEach(theme-> {
                    if (CollectionUtils.isNotEmpty(successThemeList)) {
                        String keywordText;
                        if (SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_BRAND.getMsg().equalsIgnoreCase(theme.getKeywordText())) {
                            keywordText = SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_BRAND.getValue();
                        } else if (SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getMsg().equalsIgnoreCase(theme.getKeywordText())) {
                            keywordText = SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getValue();
                        } else {
                            keywordText = theme.getKeywordText();
                        }
                        List<ThemesBidsSuccessResults> collect = successThemeList.stream().filter(k -> keywordText.equalsIgnoreCase(k.getThemeType())).collect(Collectors.toList());
                        RecommendedBid recommendedBid = collect.get(0).getRecommendedBid();
                        theme.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(theme.getSuggested()));
                        theme.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(theme.getRangeStart()));
                        theme.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(theme.getRangeEnd()));
                    }
                });
            });
            return suggestedKeywordVos;
        }, executor)));

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedKeywordVoList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info("sb keywords getSuggestedBid error, puid:{}, shopId:{}", puid, shop.getId(), e);
            }
        });

        return ResultUtil.returnSucc(suggestedKeywordVoList);
    }

    @Override
    public Result<List<SuggestedKeywordVo>> getSuggestedBidMultiShop(int puid, List<KeywordSuggestBidBatchQo> qoList) {
        List<Integer> shopIdList = qoList.stream().map(KeywordSuggestBidBatchQo::getShopId).distinct().collect(Collectors.toList());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, shopIdList);
        if (shopIdList.size() != shopAuthList.size()) {
            return ResultUtil.error("店铺不存在，请刷新页面重试");
        }
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        List<Integer> shopIds = new ArrayList<>(shopAuthMap.keySet());
        List<AmazonAdProfile> profileList = amazonAdProfileDao.getByPuidAndShopIdList(puid, new ArrayList<>(shopIds));
        if (profileList.size() != shopAuthMap.size()) {
            return ResultUtil.returnErr("没有站点对应的配置信息，请刷新页面重试");
        }
        Map<Integer, AmazonAdProfile> profileMap = profileList.stream().collect(Collectors.toMap(AmazonAdProfile::getShopId, Function.identity()));

        List<AmazonSbAdKeyword> keywordList = keywordDao.getByKeywordSuggestBidBatchQo(puid, qoList);
        if (CollectionUtils.isEmpty(keywordList)) {
            return ResultUtil.returnErr("对象不存在");
        }

        // 过滤出有效的, 没有有效的也不返回报错
        keywordList = keywordList.stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(keywordList)) {
            return ResultUtil.success(new ArrayList<>());
        }

        Map<String, AmazonSbAdKeyword> keywordMap = keywordList.stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, Function.identity()));

        // 根据campaign分组查询建议竞价
        Map<String, List<AmazonSbAdKeyword>> adKeywordMap = keywordList.stream().collect(Collectors.groupingBy(AmazonSbAdKeyword::getAdGroupId));
        List<String> adGroupIds = adKeywordMap.keySet().stream().filter(Objects::nonNull).collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getListByShopIdsAndGroupIds(puid, shopIds, adGroupIds);
        if (CollectionUtils.isEmpty(adGroups) || adGroups.size() < adKeywordMap.size()) {
            return ResultUtil.returnErr("系统异常，请联系管理员");
        }

        List<String> campaignIds = adGroups.stream().map(AmazonSbAdGroup::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonAdCampaignAllMap = campaignAllDao.listByShopIdAndCampaignIds(puid, shopIds, campaignIds).stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (e1, e2) -> e2));

        ThreadPoolExecutor executor = ThreadPoolUtil.getBatchGetSuggestedBitPool();
        List<CompletableFuture<List<SuggestedKeywordVo>>> completableFutures = new ArrayList<>();
        List<SuggestedKeywordVo> suggestedKeywordVoList = new ArrayList<>();
        adGroups.forEach(adGroup-> completableFutures.add(CompletableFuture.supplyAsync(()->{
            Long camId = Optional.ofNullable(adGroup.getCampaignId()).filter(StringUtils::isNotBlank).map(Long::valueOf).orElse(null);
            String adFormat = adGroup.getAdFormat();
            String goal = null;
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllMap.get(adGroup.getCampaignId());
            if (amazonAdCampaignAll != null) {
                goal = amazonAdCampaignAll.getAdGoal();
                if ("VIDEO".equalsIgnoreCase(adFormat) && "BRAND_IMPRESSION_SHARE".equalsIgnoreCase(goal)) {
                    adFormat = null;
                }
            }
            List<SuggestedKeywordVo> suggestedKeywordVos = adKeywordMap.get(adGroup.getAdGroupId()).stream().map(e -> {
                SuggestedKeywordVo suggestedBidVo = new SuggestedKeywordVo();
                suggestedBidVo.setKeywordId(e.getKeywordId());
                suggestedBidVo.setMatchType(e.getMatchType());
                suggestedBidVo.setKeywordText(e.getKeywordText());
                suggestedBidVo.setSuggested(Optional.ofNullable(e.getSuggested()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeStart(Optional.ofNullable(e.getRangeStart()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                suggestedBidVo.setRangeEnd(Optional.ofNullable(e.getRangeEnd()).map(o -> o.setScale(2, RoundingMode.HALF_UP).toString()).orElse(null));
                return suggestedBidVo;
            }).collect(Collectors.toList());

            SBCampaignGoalEnum goalEn = SBCampaignGoalEnum.getSBCampaignGoalEnumByType(goal);
            String costType = null;
            if (Objects.nonNull(goalEn)) {
                if (SBCampaignGoalEnum.PAGE_VISIT == goalEn) {
                    costType = SBCampaignCostTypeEnum.CPC.getCode();
                }
                if (SBCampaignGoalEnum.BRAND_IMPRESSION_SHARE == goalEn) {
                    costType = SBCampaignCostTypeEnum.VCPM.getCode();
                }
            }
            String finalGoal = goal;
            String finalAdFormat = adFormat;
            String finalCostType = costType;
            Lists.partition(suggestedKeywordVos, 100).forEach(subList -> {
                List<SuggestedKeywordVo> keywordsList = subList.stream().filter(Objects::nonNull).filter(k->!"theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());
                List<SuggestedKeywordVo> themeList = subList.stream().filter(Objects::nonNull).filter(k -> "theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());

                List<SBBidRecommendationKeyword> queryKeywordsList = keywordsList.stream().map(e -> {
                    SBBidRecommendationKeyword keyword = new SBBidRecommendationKeyword();
                    keyword.setMatchType(e.getMatchType());
                    keyword.setKeywordText(e.getKeywordText());
                    return keyword;
                }).collect(Collectors.toList());

                List<String> queryThemeList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(themeList)) {
                    queryThemeList = Arrays.asList(SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_BRAND.getValue(), SBThemesEnum.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.getValue());
                }
                Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBidsNew(shopAuthMap.get(adGroup.getShopId()), profileMap.get(adGroup.getShopId()), camId, finalAdFormat, finalCostType, queryKeywordsList, null,queryThemeList, finalGoal);

                //Result<BidRecommendationResult> resultResult = cpcSbBidApiService.getKeywordAndTargetBids(shop, profile, camId, finalAdFormat, keywordsList, null, finalGoal);
                BidRecommendationResult result;
                if (!resultResult.success() || (result = resultResult.getData()) == null) return;
                List<KeywordsBidsSuccessResults> successKeywordList = result.getKeywordsBidsRecommendationSuccessResults();
                List<ThemesBidsSuccessResults> successThemeList = result.getThemesRecommendationSuccessResults();
                if (CollectionUtils.isEmpty(successKeywordList) && CollectionUtils.isEmpty(successThemeList)) return;

                successKeywordList.forEach(successResult-> {
                    Integer index = successResult.getKeywordIndex();
                    RecommendedBid recommendedBid = successResult.getRecommendedBid();
                    if (recommendedBid == null || index == null) return;
                    SuggestedKeywordVo returnKeywordsVo = keywordsList.get(index);
                    returnKeywordsVo.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(returnKeywordsVo.getSuggested()));
                    returnKeywordsVo.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(returnKeywordsVo.getRangeStart()));
                    returnKeywordsVo.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(returnKeywordsVo.getRangeEnd()));

                    AmazonSbAdKeyword updateAmazonSbAdKeyword = keywordMap.get(returnKeywordsVo.getKeywordId());
                    if (updateAmazonSbAdKeyword == null) return;
                    updateAmazonSbAdKeyword.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getSuggested()));
                    updateAmazonSbAdKeyword.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeStart()));
                    updateAmazonSbAdKeyword.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeEnd()));
                });
                themeList.forEach(theme-> {
                    if (CollectionUtils.isNotEmpty(successThemeList)) {
                        String keywordText = theme.getKeywordText();
                        List<ThemesBidsSuccessResults> collect = successThemeList.stream().filter(k -> keywordText.equalsIgnoreCase(k.getThemeType())).collect(Collectors.toList());
                        RecommendedBid recommendedBid = collect.get(0).getRecommendedBid();
                        theme.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).orElse(theme.getSuggested()));
                        theme.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).orElse(theme.getRangeStart()));
                        theme.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).orElse(theme.getRangeEnd()));

                        AmazonSbAdKeyword updateAmazonSbAdKeyword = keywordMap.get(theme.getKeywordId());
                        if (updateAmazonSbAdKeyword == null) return;
                        updateAmazonSbAdKeyword.setSuggested(Optional.ofNullable(recommendedBid.getRecommended()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getSuggested()));
                        updateAmazonSbAdKeyword.setRangeStart(Optional.ofNullable(recommendedBid.getRangeStart()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeStart()));
                        updateAmazonSbAdKeyword.setRangeEnd(Optional.ofNullable(recommendedBid.getRangeEnd()).map(String::valueOf).map(BigDecimal::new).orElse(updateAmazonSbAdKeyword.getRangeEnd()));
                    }
                });
            });
            return suggestedKeywordVos;
        }, executor)));

        try {
            CompletableFuture.allOf(completableFutures.toArray(new CompletableFuture[0])).get();
        } catch (InterruptedException | ExecutionException ignored) {
        }
        completableFutures.forEach(future -> {
            try {
                suggestedKeywordVoList.addAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.info(String.format("sb keywords getSuggestedBidMultiShop error, puid:%s", puid), e);
            }
        });

        // 批量更新建议竞价
        keywordDao.batchUpdateSuggestValue(puid, Lists.newArrayList(keywordMap.values()));
        return ResultUtil.returnSucc(suggestedKeywordVoList);
    }

    @Override
    public Result createKeywords(AddSbKeywordsVo addSbKeywordsVo) {
        Integer puid = addSbKeywordsVo.getPuid();
        Integer shopId = addSbKeywordsVo.getShopId();
        String campaignId = addSbKeywordsVo.getCampaignId();
        String groupId = addSbKeywordsVo.getGroupId();
        List<KeywordsVo> keywords = addSbKeywordsVo.getKeywords();
        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        for (KeywordsVo vo : keywords) {
            if (StringUtils.isBlank(vo.getKeywordText()) || StringUtils.isBlank(vo.getMatchType())
                    || StringUtils.isBlank(vo.getBid())) {
                return ResultUtil.error("对象不存在");
            }
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        // 排除已存在的关键词
        Iterator<KeywordsVo> it = keywords.iterator();
        KeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (keywordDao.exist(puid, shopId, adGroup.getAdGroupId(), next.getKeywordText().trim(), next.getMatchType())) {
                it.remove();
            }
        }

        if (keywords.size() == 0) {
            return ResultUtil.success();
        }

        List<AmazonSbAdKeyword> amazonAdKeywords = convertAddKeywordsVoToPo(addSbKeywordsVo.getUid(), adGroup, keywords);

        Result result = cpcSbKeywordApiService.createKeywords(shop, profile, amazonAdKeywords);
        logSbKeywordsCreate(amazonAdKeywords, addSbKeywordsVo.getIp(), result);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdKeyword> succList = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = keywordDao.listByKeywordId(puid, shopId, succList.stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                keywordDao.batchAdd(puid, succList);
            } catch (Exception e) {
                log.error("createSbKeyword:", e);
            }

            //创建成功, 需要在同步 获取关键词状态
            cpcSbKeywordApiService.syncKeywords(shop, campaignId);
            //写入doris
            saveDoris(puid, shopId, succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));

            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("keyword");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
            }
        }

        List<AmazonSbAdKeyword> failList = amazonAdKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (failList.size() == 0) {
            return ResultUtil.success();
        }

        StringBuilder errMsg = new StringBuilder("创建失败的关键词原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });

        return ResultUtil.returnErr(errMsg.toString());
    }

    private void logSbKeywordsCreate(List<AmazonSbAdKeyword> keywordList, String ip, Result result) {
        try {
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(keywordList)) {
                return;
            }
            List<AdManageOperationLog> operationLogs = Lists.newArrayList();
            for (AmazonSbAdKeyword keyword : keywordList) {
                AdManageOperationLog operationLog = adManageOperationLogService.getSbKeywordLog(null, keyword);
                operationLog.setIp(ip);
                if (org.apache.commons.lang3.StringUtils.isNotBlank(keyword.getKeywordId())) {
                    operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    operationLog.setResultInfo(org.apache.commons.lang3.StringUtils.isNotBlank(keyword.getErrMsg()) ? "targetValue:" + keyword.getKeywordText() + ",desc:" + keyword.getErrMsg() : result.getMsg());
                }
                operationLogs.add(operationLog);
            }
            adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        } catch (Exception e) {
            log.error("sb关键词日志异常", e);
        }
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> createKeywords(AddSbKeywordsVo addSbKeywordsVo, ShopAuth shop, AmazonAdProfile profile) {
        Integer puid = addSbKeywordsVo.getPuid();
        Integer shopId = addSbKeywordsVo.getShopId();
        String campaignId = addSbKeywordsVo.getCampaignId();
        String groupId = addSbKeywordsVo.getGroupId();
        List<KeywordsVo> keywords = addSbKeywordsVo.getKeywords();
        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            throw new ServiceException(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg());
        }

        for (KeywordsVo vo : keywords) {
            if (StringUtils.isBlank(vo.getKeywordText()) || StringUtils.isBlank(vo.getMatchType())
                    || StringUtils.isBlank(vo.getBid())) {
                throw new ServiceException(SBCreateErrorEnum.KEYWORD_INFO_ERROR.getMsg());
            }
        }

        // 排除已存在的关键词
        Iterator<KeywordsVo> it = keywords.iterator();
        KeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (keywordDao.exist(puid, shopId, adGroup.getAdGroupId(), next.getKeywordText().trim(), next.getMatchType())) {
                it.remove();
            }
        }

        if (keywords.size() == 0) {
            NewCreateResultResultVo<SBCommonErrorVo> r = new NewCreateResultResultVo<>();
            r.setCampaignId(campaignId);
            r.setAdGroupId(groupId);
            return r;
        }

        List<AmazonSbAdKeyword> amazonAdKeywords = convertAddKeywordsVoToPo(addSbKeywordsVo.getUid(), adGroup, keywords);

        Result result = cpcSbKeywordApiService.createKeywordsNew(shop, profile, amazonAdKeywords);
        if (!result.success()) {
            throw new ServiceException(Optional.ofNullable(result.getData()).map(Object::toString).orElse(SBCreateErrorEnum.KEYWORD_CREATE_FAIL.getMsg()));
        }

        List<AmazonSbAdKeyword> succList = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (!succList.isEmpty()) {
            // 有可能已经添加过了
            List<String> existInDB = keywordDao.listByKeywordId(puid, shopId, succList.stream()
                            .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                keywordDao.batchAdd(puid, succList);
            } catch (Exception e) {
                log.error("createSbKeyword:", e);
                throw new ServiceException(e.getMessage());
            }

          /*  ThreadPoolUtil.getTargetCreateSyncPool().execute(() -> {
                try {*/
                    //创建成功, 需要在同步 获取关键词状态
                    cpcSbKeywordApiService.syncKeywords(shop, campaignId);
                /*} catch (Exception e) {
                    log.info("添加成功后同步关键词投放异常", e);
                    throw new ServiceException(e.getMessage());
                }
            });*/

            //写入doris
            saveDoris(puid, shopId, succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));

            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("keyword");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
                throw new ServiceException(e.getMessage());
            }
        }

        NewCreateResultResultVo<SBCommonErrorVo> keywordResultList = new NewCreateResultResultVo<>();
        keywordResultList.setCampaignId(campaignId);
        keywordResultList.setAdGroupId(groupId);
        keywordResultList.setKeywordList(amazonAdKeywords.stream()
                .map(s -> {
                    NewCreateTargetResultVo vo = new NewCreateTargetResultVo();
                    Optional.ofNullable(s.getKeywordId()).ifPresent(vo::setTargetId);
                    Optional.ofNullable(s.getKeywordText()).ifPresent(vo::setTargetText);
                    Optional.ofNullable(s.getMatchType()).ifPresent(vo::setMatchType);
                    return vo;
                })
                .collect(Collectors.toList()));
        List<AmazonSbAdKeyword> failList = amazonAdKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (failList.isEmpty()) {
            return keywordResultList;
        }

        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(e.getKeywordText(), e.getErrMsg()));
            }
        });
        keywordResultList.setErrInfoList(errorList);
        return keywordResultList;
    }

    @Override
    public NewCreateResultResultVo<SBCommonErrorVo> createThemes(AddSbThemesVo addSbThemesVo, ShopAuth shop, AmazonAdProfile profile) {
        Integer puid = addSbThemesVo.getPuid();
        Integer shopId = addSbThemesVo.getShopId();
        String campaignId = addSbThemesVo.getCampaignId();
        String groupId = addSbThemesVo.getGroupId();
        List<ThemesVo> themes = addSbThemesVo.getThemes();
        AmazonSbAdGroup adGroup = groupDao.getByGroupId(puid, shopId, groupId);
        if (adGroup == null) {
            throw new ServiceException(SBCreateErrorEnum.GROUP_NOT_EXIST.getMsg());
        }
        for (ThemesVo vo : themes) {
            if (StringUtils.isBlank(vo.getThemeType()) || Objects.isNull(vo.getBid()) || vo.getBid() == 0.0D) {
                throw new ServiceException(SBCreateErrorEnum.THEMES_INFO_ERROR.getMsg());
            }
        }
        // 排除已存在的关键词
        Iterator<ThemesVo> it = themes.iterator();
        ThemesVo next;
        while (it.hasNext()) {
            next = it.next();
            if (keywordDao.exist(puid, shopId, adGroup.getAdGroupId(), next.getThemeType(), MatchTypeEnum.theme.getMatchType())) {
                it.remove();
            }
        }

        if (themes.isEmpty()) {
            NewCreateResultResultVo<SBCommonErrorVo> r = new NewCreateResultResultVo<>();
            r.setCampaignId(campaignId);
            r.setAdGroupId(groupId);
            return r;
        }

        List<AmazonSbAdKeyword> amazonAdKeywords = convertAddThemeVoToPo(adGroup, themes);

        Result result = cpcSbKeywordApiService.createThemes(shop, profile, amazonAdKeywords);
        if (!result.success()) {
            log.error("createSbThemes error:{}:", result.getMsg());
            throw new ServiceException(Optional.ofNullable(result.getMsg()).map(Object::toString).orElse("创建主题投放异常"));
        }

        List<AmazonSbAdKeyword> succList = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (!succList.isEmpty()) {
            // 有可能已经添加过了
            List<String> existInDB = keywordDao.listByKeywordId(puid, shopId, succList.stream()
                            .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                keywordDao.batchAdd(puid, succList);
            } catch (Exception e) {
                log.error("createSbThemes error:", e);
                throw new ServiceException(e.getMessage());
            }

         /*   ThreadPoolUtil.getTargetCreateSyncPool().execute(() -> {
                try {*/
                    //创建成功, 需要在同步 获取关键词状态
                    cpcSbKeywordApiService.syncKeywords(shop, campaignId);
              /*  } catch (Exception e) {
                    log.info("添加成功后同步主题投放异常", e);
                    throw new ServiceException(e.getMessage());
                }
            });*/

            //写入doris
            saveDoris(puid, shopId, succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));

            //同步广告组投放类型字段
            try {
                AmazonSbAdGroup sbAdGroup = groupDao.getByGroupId(puid, shopId, groupId);
                if (StringUtils.isBlank(sbAdGroup.getAdGroupType())) {
                    sbAdGroup.setAdGroupType("keyword");
                    groupDao.updateByIdAndPuid(puid, sbAdGroup);
                    cpcSbGroupService.saveDoris(null, Collections.singletonList(sbAdGroup));
                }
            } catch (Exception e) {
                log.error("updateGroupTargetType:", e);
                throw new ServiceException(e.getMessage());
            }
        }

        NewCreateResultResultVo<SBCommonErrorVo> themesResultList = new NewCreateResultResultVo<>();
        themesResultList.setCampaignId(campaignId);
        themesResultList.setAdGroupId(groupId);
        themesResultList.setThemesList(amazonAdKeywords.stream().map(s -> {
                    NewCreateThemesResultVo vo = new NewCreateThemesResultVo();
                    Optional.ofNullable(s.getKeywordId()).ifPresent(vo::setThemesId);
                    Optional.ofNullable(s.getKeywordText()).map(SBThemesEnum::getSBThemesEnumByVal).map(SBThemesEnum::getCode)
                            .map(String::valueOf).ifPresent(vo::setThemesText);
                    return vo;
                }).collect(Collectors.toList()));
        List<AmazonSbAdKeyword> failList = amazonAdKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (failList.isEmpty()) {
            return themesResultList;
        }

        List<SBCommonErrorVo> errorList = new ArrayList<>();
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errorList.add(SBCommonErrorVo.getErrorVo(Optional
                        .ofNullable(SBThemesEnum.getSBThemesEnumByVal(e.getKeywordText()))
                        .map(SBThemesEnum::getMsg).orElse(e.getKeywordText()), e.getErrMsg()));
            }
        });
        themesResultList.setErrInfoList(errorList);
        return themesResultList;
    }

    @Override
    public Result createCampaignIdsKeywords(AddSbCampaignIdsKeywordsVo addSbKeywordsVo) {
        Integer puid = addSbKeywordsVo.getPuid();
        Integer shopId = addSbKeywordsVo.getShopId();
        List<CampaignIdKeywordsVo> keywords = addSbKeywordsVo.getKeywords();
        Map<String,List<Integer>> resultMap = new HashMap<>();
        Set<Integer> success = new HashSet<>();
        Set<Integer> fail = new HashSet<>();
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Iterator<CampaignIdKeywordsVo> iterator = keywords.iterator();
        while (iterator.hasNext()) {
            CampaignIdKeywordsVo e = iterator.next();
            if (StringUtils.isBlank(e.getKeywordText()) || StringUtils.isBlank(e.getMatchType())
                    || StringUtils.isBlank(e.getBid()) || StringUtils.isBlank(e.getCampaignId()) || StringUtils.isBlank(e.getGroupId())) {
                e.setMsg("请求参数有误");
                fail.add(e.getIndex());
                iterator.remove();
            }
        }


        if (CollectionUtils.isEmpty(keywords)) {
            return ResultUtil.returnErr("参数请求错误");
        }
        
        List<String> campaignIds = keywords.stream().map(CampaignIdKeywordsVo::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = keywords.stream().map(CampaignIdKeywordsVo::getGroupId).distinct().collect(Collectors.toList());
        List<AmazonSbAdGroup> adGroups = groupDao.getAdGroupByIds(puid, shopId, groupIds);

        if (CollectionUtils.isEmpty(adGroups)) {
            return ResultUtil.returnErr("没有广告组信息");
        }

        Map<String, AmazonSbAdGroup> sbAdGroupMap = adGroups.stream().collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e, (e1, e2) -> e2));
        // 排除已存在的关键词
        Iterator<CampaignIdKeywordsVo> it = keywords.iterator();
        CampaignIdKeywordsVo next;
        while (it.hasNext()) {

            next = it.next();
            //移除不存在广告组信息的数据；
            AmazonSbAdGroup amazonSbAdGroup = sbAdGroupMap.get(next.getGroupId());

            if(amazonSbAdGroup == null){
                next.setMsg("没有广告组信息");
                fail.add(next.getIndex());
                it.remove();
                continue;
            }
            if (keywordDao.exist(puid, shopId, amazonSbAdGroup.getAdGroupId(), next.getKeywordText(), next.getMatchType())) {
                success.add(next.getIndex());
                it.remove();
            }
        }

        if (keywords.size() == 0) {
            resultMap.put("success",Lists.newArrayList(success));
            resultMap.put("fail",Lists.newArrayList(fail));
            return ResultUtil.success(resultMap);
        }

        List<AmazonSbAdKeyword> amazonAdKeywords = convertAddKeywordsVoToPo(addSbKeywordsVo.getUid(), sbAdGroupMap, keywords);

        Result result = cpcSbKeywordApiService.createKeywords(shop, profile, amazonAdKeywords);
        if (!result.success()) {
            return result;
        }

        List<AmazonSbAdKeyword> succList = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if (succList.size() > 0) {
            // 有可能已经添加过了
            List<String> existInDB = keywordDao.listByKeywordId(puid, shopId, succList.stream()
                            .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList()))
                    .stream()
                    .map(AmazonSbAdKeyword :: getKeywordId).collect(Collectors.toList());

            // 排除掉已有的
            if (CollectionUtils.isNotEmpty(existInDB)) {
                succList = succList.stream().filter(e -> !existInDB.contains(e.getKeywordId())).collect(Collectors.toList());
            }
            // 入库
            try {
                keywordDao.batchAdd(puid, succList);
            } catch (Exception e) {
                log.error("createSbKeyword:", e);
            }
            //创建成功, 需要在同步 获取关键词状态
            List<String> campaignIdList = succList.stream().map(AmazonSbAdKeyword::getCampaignId).distinct().collect(Collectors.toList());
            cpcSbKeywordApiService.syncKeywords(shop, StringUtil.joinString(campaignIdList,","));
            saveDoris(puid, shopId, succList.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));
        }

        List<AmazonSbAdKeyword> failList = amazonAdKeywords.stream().filter(e -> StringUtils.isBlank(e.getKeywordId()))
                .collect(Collectors.toList());
        if(CollectionUtils.isNotEmpty(succList)){
            Set<Integer> collect = succList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                success.addAll(collect);
            }
        }
        if(CollectionUtils.isNotEmpty(failList)){
            Set<Integer> collect = failList.stream().filter(e -> e != null && e.getIndex() != null).map(AmazonSbAdKeyword::getIndex).collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                fail.addAll(collect);
            }
        }
        resultMap.put("success",Lists.newArrayList(success));
        resultMap.put("fail",Lists.newArrayList(fail));
        if (failList.size() == 0) {
            return ResultUtil.success(resultMap);
        }

        StringBuilder errMsg = new StringBuilder("创建失败的关键词原因：");
        failList.forEach((e) -> {
            if (StringUtils.isNotBlank(e.getErrMsg())) {
                errMsg.append(e.getErrMsg());
            }
        });

        Result<Object> objectResult = ResultUtil.returnErr(errMsg.toString());
        objectResult.setData(resultMap);
        return objectResult;
    }


    // vo -> po
    private List<AmazonSbAdKeyword> convertAddKeywordsVoToPo(Integer uid, AmazonSbAdGroup adGroup, List<KeywordsVo> keywords) {
        List<AmazonSbAdKeyword> keywordList = new ArrayList<>(keywords.size());
        AmazonSbAdKeyword amazonAdKeyword;
        for (KeywordsVo vo : keywords) {
            amazonAdKeyword = new AmazonSbAdKeyword();
            amazonAdKeyword.setPuid(adGroup.getPuid());
            amazonAdKeyword.setShopId(adGroup.getShopId());
            amazonAdKeyword.setMarketplaceId(adGroup.getMarketplaceId());
            amazonAdKeyword.setProfileId(adGroup.getProfileId());
            amazonAdKeyword.setAdGroupId(adGroup.getAdGroupId());
            amazonAdKeyword.setCampaignId(adGroup.getCampaignId());
            amazonAdKeyword.setKeywordText(vo.getKeywordText().trim());
            amazonAdKeyword.setMatchType(vo.getMatchType());
            amazonAdKeyword.setBid(new BigDecimal(vo.getBid()));

            if (StringUtils.isNotBlank(vo.getSuggested())) {
                amazonAdKeyword.setSuggested(new BigDecimal(vo.getSuggested()));
            }
            if (StringUtils.isNotBlank(vo.getRangeStart())) {
                amazonAdKeyword.setRangeStart(new BigDecimal(vo.getRangeStart()));
            }
            if (StringUtils.isNotBlank(vo.getRangeEnd())) {
                amazonAdKeyword.setRangeEnd(new BigDecimal(vo.getRangeEnd()));
            }
            amazonAdKeyword.setCreateId(uid);
            amazonAdKeyword.setCreateInAmzup(1);
            keywordList.add(amazonAdKeyword);
        }

        return keywordList;
    }

    //主题投放于关键词投放共用一张表
    private List<AmazonSbAdKeyword> convertAddThemeVoToPo(AmazonSbAdGroup adGroup, List<ThemesVo> themes) {
        List<AmazonSbAdKeyword> themeList = new ArrayList<>(themes.size());
        AmazonSbAdKeyword amazonAdKeyword;
        for (ThemesVo vo : themes) {
            amazonAdKeyword = new AmazonSbAdKeyword();
            amazonAdKeyword.setPuid(adGroup.getPuid());
            amazonAdKeyword.setShopId(adGroup.getShopId());
            amazonAdKeyword.setMarketplaceId(adGroup.getMarketplaceId());
            amazonAdKeyword.setProfileId(adGroup.getProfileId());
            amazonAdKeyword.setAdGroupId(adGroup.getAdGroupId());
            amazonAdKeyword.setCampaignId(adGroup.getCampaignId());
            amazonAdKeyword.setKeywordText(vo.getThemeType());
            amazonAdKeyword.setBid(new BigDecimal(vo.getBid()));
            amazonAdKeyword.setMatchType(MatchTypeEnum.theme.getMatchType());
            amazonAdKeyword.setState(StateEnum.enabled.getStateType());
            themeList.add(amazonAdKeyword);
        }
        return themeList;
    }


    @Override
    public Result updateBatchMultiShop(Integer puid, Integer uid, List<SbUpdateKeywordsVo> vos, String type, String loginIp) {
        Map<Integer, List<SbUpdateKeywordsVo>> voMap = vos.stream().collect(Collectors.groupingBy(SbUpdateKeywordsVo::getShopId));
        BatchResponseVo<SbUpdateKeywordsVo, AmazonAdKeyword> batchData = new BatchResponseVo<>();
        Result<BatchResponseVo<SbUpdateKeywordsVo, AmazonAdKeyword>> result;
        BatchResponseVo<SbUpdateKeywordsVo, AmazonAdKeyword> data;
        for (Map.Entry<Integer, List<SbUpdateKeywordsVo>> entry : voMap.entrySet()) {
            result = this.updateBatch(puid, entry.getKey(), uid, entry.getValue(), type, loginIp);
            if (result.error()) {
                return ResultUtil.error(result.getMsg());
            }

            data = result.getData();
            if (data != null) {
                batchData.addCountNum(data.getCountNum());
                batchData.addSuccessNum(data.getSuccessNum());
                batchData.addFailNum(data.getFailNum());
                batchData.addSuccessList(data.getSuccessList());
                batchData.addErrorList(data.getErrorList());
            }
        }
        return ResultUtil.success(batchData);
    }

    @Override
    public Result updateBatch(Integer puid, Integer shopId, Integer uid, List<SbUpdateKeywordsVo> vos, String type, String ip) {
        if(org.apache.commons.collections.CollectionUtils.isEmpty(vos)){
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }


        List<Long> ids = vos.stream().map(SbUpdateKeywordsVo::getId).collect(Collectors.toList());
        List<AmazonSbAdKeyword> amazonSbAdKeywords = keywordDao.getListByIds(puid, shopId, ids);
        if (org.apache.commons.collections.CollectionUtils.isEmpty(amazonSbAdKeywords)) {
            return ResultUtil.error("没有活动信息");
        }
        List<AmazonSbAdKeyword> updateList = Lists.newArrayList();
        List<SbUpdateKeywordsVo> errorList = Lists.newArrayList();
        Map<Long, AmazonSbAdKeyword> amazonAdKeywordMap = amazonSbAdKeywords.stream().collect(Collectors.toMap(AmazonSbAdKeyword::getId, e -> e));
        for (SbUpdateKeywordsVo vo: vos) {
            checkBatchVo(vo, type);
            if(org.apache.commons.lang3.StringUtils.isNotBlank(vo.getFailReason())){
                errorList.add(vo);
                continue;
            }
            AmazonSbAdKeyword oldAmazonSbKeyword = amazonAdKeywordMap.get(vo.getId());
            if (oldAmazonSbKeyword == null) {
                vo.setFailReason("对象不存在");
                errorList.add(vo);
                continue;
            }
            AmazonSbAdKeyword amazonSbAdKeyword = new AmazonSbAdKeyword();
            BeanUtils.copyProperties(oldAmazonSbKeyword, amazonSbAdKeyword);
            convertVoToBatchUpdatePo(amazonSbAdKeyword, vo,type);
            updateList.add(amazonSbAdKeyword);
        }
        if(CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> data =  new  BatchResponseVo<> ();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<SbUpdateKeywordsVo,AmazonSbAdKeyword>> result = batchUpdate(shop,profile,updateList,type);

        List<AdManageOperationLog> operationLogs = Lists.newArrayList();
        for (AmazonSbAdKeyword amazonSbAdKeyword : updateList) {
            if ("theme".equalsIgnoreCase(amazonSbAdKeyword.getMatchType())) {
                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(amazonSbAdKeyword.getKeywordText())) {
                    amazonSbAdKeyword.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(amazonSbAdKeyword.getKeywordText())) {
                    amazonSbAdKeyword.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                }
            }
            AdManageOperationLog sbKeywordLog = adManageOperationLogService.getSbKeywordLog(amazonAdKeywordMap.get(amazonSbAdKeyword.getId()), amazonSbAdKeyword);
            sbKeywordLog.setIp(ip);
            operationLogs.add(sbKeywordLog);
        }

        if (result.success()) {
            BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> data = result.getData();
            List<SbUpdateKeywordsVo> keywordsError = data.getErrorList();
            if(CollectionUtils.isNotEmpty(errorList)){
                keywordsError.addAll(errorList);
                data.setFailNum(data.getErrorList().size());
            }
            List<AmazonSbAdKeyword> amazonAdKeywordsSuccess = data.getSuccessList();

            Map<String, AmazonSbAdKeyword> successMap = amazonAdKeywordsSuccess.stream().collect(Collectors.toMap(AmazonSbAdKeyword::getKeywordId, e -> e, (v1, v2) -> v2));
            Map<String, SbUpdateKeywordsVo> errorMap = keywordsError.stream().collect(Collectors.toMap(SbUpdateKeywordsVo::getKeywordId, e -> e, (v1, v2) -> v2));
            for (AdManageOperationLog keywordLog : operationLogs) {
                if (!StringUtil.isEmptyObject(successMap.get(keywordLog.getTargetId()))) {
                    keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    String errMsg = errorMap.containsKey(keywordLog.getTargetId()) ? errorMap.get(keywordLog.getTargetId()).getFailReason() : "操作失败";
                    keywordLog.setResultInfo(errMsg);
                }
            }

            if(CollectionUtils.isNotEmpty(amazonAdKeywordsSuccess)){
                keywordDao.updateList(puid,amazonAdKeywordsSuccess,type);
                saveDoris(puid, shopId, amazonAdKeywordsSuccess.stream().map(AmazonSbAdKeyword::getKeywordId).collect(Collectors.toList()));
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},更新成功数据：{}",type,uid,puid,shopId, JSONUtil.objectToJson(amazonAdKeywordsSuccess));
                data.getSuccessList().clear();
            }
        } else {
            for (AdManageOperationLog keywordLog : operationLogs) {
                keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                keywordLog.setResultInfo(result.getMsg());
            }
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        return result;
    }

    private Result<BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword>> batchUpdate(ShopAuth shop, AmazonAdProfile profile, List<AmazonSbAdKeyword> updateList, String type) {
        // 检查请求参数是否为空
        if (CollectionUtils.isEmpty(updateList)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        // 将 updateList 分为 keywordList 和 themeList
        List<AmazonSbAdKeyword> keywordList = updateList.stream().filter(k -> !"theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());
        List<AmazonSbAdKeyword> themeList = updateList.stream().filter(k -> "theme".equalsIgnoreCase(k.getMatchType())).collect(Collectors.toList());

        Result<BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword>> keywordResult = cpcSbKeywordApiService.update(shop, profile, keywordList, type);
        Result<BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword>> themeResult = cpcSbKeywordApiService.updateTheme(shop, profile, themeList, type);

        // 初始化合并后的结果
        BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> batchResponseVo = new BatchResponseVo<>();

        // 合并成功和失败的列表
        List<SbUpdateKeywordsVo> errorList = new ArrayList<>();
        List<AmazonSbAdKeyword> successList = new ArrayList<>();

        // 处理 keywordResult 的结果
        if (keywordResult != null && keywordResult.success() && keywordResult.getData() != null) {
            BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> keywordResponse = keywordResult.getData();
            errorList.addAll(keywordResponse.getErrorList());
            successList.addAll(keywordResponse.getSuccessList());
        } else if (keywordResult != null && keywordResult.error()) {
            // 如果 keywordResult 失败，将所有 keywordList 标记为失败
            keywordList.forEach(k -> {
                SbUpdateKeywordsVo errorVo = new SbUpdateKeywordsVo();
                errorVo.setId(k.getId());
                errorVo.setKeywordText(k.getKeywordText());
                errorVo.setFailReason(keywordResult.getMsg());
                errorVo.setKeywordId(k.getKeywordId());
                errorList.add(errorVo);
            });
        }

        // 处理 themeResult 的结果
        if (themeResult != null && themeResult.success() && themeResult.getData() != null) {
            BatchResponseVo<SbUpdateKeywordsVo, AmazonSbAdKeyword> themeResponse = themeResult.getData();
            errorList.addAll(themeResponse.getErrorList());
            successList.addAll(themeResponse.getSuccessList());
        } else if (themeResult != null && themeResult.error()) {
            // 如果 themeResult 失败，将所有 themeList 标记为失败
            themeList.forEach(k -> {
                SbUpdateKeywordsVo errorVo = new SbUpdateKeywordsVo();
                errorVo.setId(k.getId());
                errorVo.setKeywordText(k.getKeywordText());
                if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(k.getKeywordText())) {
                    errorVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
                } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(k.getKeywordText())) {
                    errorVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
                }
                errorVo.setFailReason(themeResult.getMsg());
                errorVo.setKeywordId(k.getKeywordId());
                errorList.add(errorVo);
            });
        }

        // 设置合并后的结果
        batchResponseVo.setCountNum(updateList.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);

        // 返回成功结果
        return ResultUtil.success(batchResponseVo);
    }

    /**
     * 根据更新类型进行数据校验
     *
     */
    private SbUpdateKeywordsVo checkBatchVo(SbUpdateKeywordsVo sbKeywordsVo,String type) {

        if(sbKeywordsVo.getId() == null || sbKeywordsVo.getId() < 1 ){
            sbKeywordsVo.setFailReason("参数错误");
            return sbKeywordsVo;
        }

        if (Constants.CPC_SB_KEYWORD_BATCH_UPDATE_BID.equals(type)) {
            if(sbKeywordsVo.getBid() == null || sbKeywordsVo.getBid().isNaN() || sbKeywordsVo.getBid() < 0.02){
                sbKeywordsVo.setFailReason("竞价错误");
            }
            return sbKeywordsVo;
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            if(org.apache.commons.lang3.StringUtils.isEmpty(sbKeywordsVo.getState())){
                sbKeywordsVo.setFailReason("参数错误");
            }
            return sbKeywordsVo;
        } else {
            sbKeywordsVo.setFailReason("参数错误");
            return sbKeywordsVo;
        }

    }

    // 更新广告组时vo->po
    private void convertVoToBatchUpdatePo(AmazonSbAdKeyword amazonAdKeyword, SbUpdateKeywordsVo vo,String type) {
        if (Constants.CPC_SP_KEYWORD_BATCH_UPDATE_BID.equals(type)){
            amazonAdKeyword.setBid(BigDecimal.valueOf(vo.getBid()));
        }
        if(Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            amazonAdKeyword.setState(vo.getState());
        }
        amazonAdKeyword.setUpdateId(vo.getUid());
    }

    private List<Object> buildUpLogMessage(Map<Long, AmazonSbAdKeyword> oldList,List<AmazonSbAdKeyword> newList,String type){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if(Constants.CPC_SB_KEYWORD_BATCH_UPDATE_BID.equals(type)){
            dataList.add("SB关键词投放批量修改默认竞价");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            dataList.add("SB关键词投放批量修改状态");
        }
        newList.forEach(e ->{
            AmazonSbAdKeyword old = oldList.get(e.getId());

            if(Constants.CPC_SB_KEYWORD_BATCH_UPDATE_BID.equals(type)){

                builder.append("keywordId:").append(e.getKeywordId());
                if(old != null){
                    builder.append(",旧值:").append(old.getBid());
                }
                builder.append(",新值:").append(e.getBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
               
                builder.append("keywordId:").append(e.getKeywordId());
                if(old != null){
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }

    // vo -> po
    private List<AmazonSbAdKeyword> convertAddKeywordsVoToPo(Integer uid, Map<String,AmazonSbAdGroup> adGroup, List<CampaignIdKeywordsVo> keywords) {
        List<AmazonSbAdKeyword> keywordList = new ArrayList<>(keywords.size());
        for (CampaignIdKeywordsVo vo : keywords) {
            AmazonSbAdGroup amazonSbAdGroup = adGroup.get(vo.getGroupId());
            keywordList.add(convertAddKeyword(amazonSbAdGroup,vo,uid));
        }

        return keywordList;
    }

    private AmazonSbAdKeyword convertAddKeyword(AmazonSbAdGroup adGroup,CampaignIdKeywordsVo vo,Integer uid){
        AmazonSbAdKeyword amazonAdKeyword = new AmazonSbAdKeyword();
        amazonAdKeyword.setPuid(adGroup.getPuid());
        amazonAdKeyword.setShopId(adGroup.getShopId());
        amazonAdKeyword.setMarketplaceId(adGroup.getMarketplaceId());
        amazonAdKeyword.setProfileId(adGroup.getProfileId());
        amazonAdKeyword.setAdGroupId(adGroup.getAdGroupId());
        amazonAdKeyword.setCampaignId(adGroup.getCampaignId());
        amazonAdKeyword.setKeywordText(vo.getKeywordText());
        amazonAdKeyword.setMatchType(vo.getMatchType());
        amazonAdKeyword.setBid(new BigDecimal(vo.getBid()));
        amazonAdKeyword.setIndex(vo.getIndex());

        if (StringUtils.isNotBlank(vo.getSuggested())) {
            amazonAdKeyword.setSuggested(new BigDecimal(vo.getSuggested()));
        }
        if (StringUtils.isNotBlank(vo.getRangeStart())) {
            amazonAdKeyword.setRangeStart(new BigDecimal(vo.getRangeStart()));
        }
        if (StringUtils.isNotBlank(vo.getRangeEnd())) {
            amazonAdKeyword.setRangeEnd(new BigDecimal(vo.getRangeEnd()));
        }
        amazonAdKeyword.setCreateId(uid);
        amazonAdKeyword.setCreateInAmzup(1);
        return amazonAdKeyword;
    }


    /**
     * 写入doris
     * @param amazonSbAdKeywords
     */
    private void saveDoris(List<AmazonSbAdKeyword> amazonSbAdKeywords, boolean create, boolean update) {
        try {
            List<OdsAmazonAdKeywordSb> collect = amazonSbAdKeywords.stream().map(x -> {
                OdsAmazonAdKeywordSb odsAmazonAdKeywordSb = new OdsAmazonAdKeywordSb();
                BeanUtils.copyProperties(x, odsAmazonAdKeywordSb);
                if (create) {
                    odsAmazonAdKeywordSb.setKeywordSize(Math.toIntExact(Arrays.stream(odsAmazonAdKeywordSb.getKeywordText().trim().split(" ")).filter(StringUtils::isNotBlank).count()));
                    odsAmazonAdKeywordSb.setCreateTime(new Date());
                }
                if (update) {
                    odsAmazonAdKeywordSb.setUpdateTime(new Date());
                }
                if (StringUtils.isNotBlank(odsAmazonAdKeywordSb.getState())) {
                    odsAmazonAdKeywordSb.setState(odsAmazonAdKeywordSb.getState().toLowerCase());
                }
                return odsAmazonAdKeywordSb;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sb keyword save doris error", e);
        }
    }


    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param keywordIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> keywordIdList) {
        try {
            if (CollectionUtils.isEmpty(keywordIdList)) {
                return;
            }
            List<AmazonSbAdKeyword> keywordList = keywordDao.listByKeywordId(puid, shopId, keywordIdList);
            saveDoris(keywordList, false, false);
        } catch (Exception e) {
            log.error("sb keyword save doris error", e);
        }
    }

    private SuggestedKeywordPageVo getRecommendKeywordListPage(List<SuggestedKeywordPageVo.SbKeywordSuggestion> dataList, Integer pageSize,
                                     Integer pageNo) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(dataList)) {
            SuggestedKeywordPageVo suggestedKeywordPageVo = new SuggestedKeywordPageVo();
            suggestedKeywordPageVo.setTotalPage(0);
            suggestedKeywordPageVo.setTotalSize(0);
            return suggestedKeywordPageVo;
        }
        //总页数
        int totalSize = dataList.size();
        int totalPage = (totalSize - 1) / pageSize + 1;
        //如果当前页大于总页数,则显示最后一页
        pageNo = Math.min(pageNo, totalPage);
        //如果当前页小于0,则显示第一页
        pageNo = Math.max(pageNo, 1);
        //记录开始值
        int start = pageSize * (pageNo - 1);
        int end = start + pageSize;
        dataList = dataList.subList(start, Math.min(totalSize, end));
        return SuggestedKeywordPageVo.builder()
                .keywordList(dataList)
                .totalSize(totalSize)
                .totalPage(totalPage)
                .build();
    }
}
