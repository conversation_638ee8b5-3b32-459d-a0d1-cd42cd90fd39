package com.meiyunji.sponsored.service.cpc.manager;

import com.amazon.advertising.localization.keyword.Keyword;
import com.amazon.advertising.localization.keyword.*;
import com.amazon.advertising.mode.BiddingForTarget;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.spV3.campaign.CampaignNegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.campaign.CampaignNegativeTargetSpV3Client;
import com.amazon.advertising.spV3.campaign.CreateSpCampaignNegativeKeywordV3Response;
import com.amazon.advertising.spV3.campaign.CreateSpCampaignNegativeTargetV3Response;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordEntityV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeTargetApiResponseV3;
import com.amazon.advertising.spV3.campaign.entity.CreateCampaignNegativeTargetEntityV3;
import com.amazon.advertising.spV3.enumeration.*;
import com.amazon.advertising.spV3.keyword.CreateSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.KeywordSpV3Client;
import com.amazon.advertising.spV3.keyword.entity.CreateKeywordEntityV3;
import com.amazon.advertising.spV3.keyword.entity.KeywordApiResponseV3;
import com.amazon.advertising.spV3.negativekeyword.CreateSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.NegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.negativekeyword.entity.CreateNegativeKeywordEntityV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.negativetargeting.CreateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.NegativeTargetSpV3Client;
import com.amazon.advertising.spV3.negativetargeting.entity.CreateNegativeTargetEntityV3;
import com.amazon.advertising.spV3.negativetargeting.entity.NegativeTargetApiResponseV3;
import com.amazon.advertising.spV3.product.CreateSpProductV3Response;
import com.amazon.advertising.spV3.product.ProductSpV3Client;
import com.amazon.advertising.spV3.product.entity.ProductApiResponseV3;
import com.amazon.advertising.spV3.product.entity.ProductEntityV3;
import com.amazon.advertising.spV3.targeting.CreateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.TargetSpV3Client;
import com.amazon.advertising.spV3.targeting.UpdateSpTargetV3Response;
import com.amazon.advertising.spV3.targeting.entity.CreateTargetEntityV3;
import com.amazon.advertising.spV3.targeting.entity.PutTargetEntityV3;
import com.amazon.advertising.spV3.targeting.entity.TargetApiResponseV3;
import com.amazon.advertising.spV3.targeting.entity.TargetExpression;
import com.amazon.advertising.targeting.ProductTargetingClient;
import com.amazon.advertising.targeting.TargetingBidRecommendationsResponse;
import com.amazon.advertising.targeting.mode.BidRecommendation;
import com.amazon.advertising.targeting.mode.TargetingExpression;
import com.google.common.collect.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.vo.SpNeTargetingVo;
import com.meiyunji.sponsored.service.doris.dao.IDimCurrencyRateDao;
import com.meiyunji.sponsored.service.doris.po.DimCurrencyRate;
import com.meiyunji.sponsored.service.enums.AdStateV3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * sp投放manager
 *
 * @Author: hejh
 * @Date: 2024/7/23 15:08
 */
@Component
@Slf4j
public class CpcSpTargetingManager {

    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IDimCurrencyRateDao dimCurrencyRateDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    private static final int MAX_SIZE = 1000;

    /**
     * 查询投放建议竞价(新建广告组)
     *
     * @param shop
     * @param profile
     * @param asins
     * @param targetingExpressions
     * @param bidding
     * @return
     */
    public List<BidRecommendation> getBidRecommendationsWithNewGroup(ShopAuth shop, AmazonAdProfile profile, List<String> asins, List<TargetingExpression> targetingExpressions, BiddingForTarget bidding) {
        TargetingBidRecommendationsResponse response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBidRecommendationsWithNewGroup(shopAuthService.getAdToken(shop),
            profile.getProfileId(), profile.getMarketplaceId(), asins, targetingExpressions, bidding, "BIDS_FOR_NEW_AD_GROUP");
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBidRecommendationsWithNewGroup(shopAuthService.getAdToken(shop),
                profile.getProfileId(), profile.getMarketplaceId(), asins, targetingExpressions, bidding, "BIDS_FOR_NEW_AD_GROUP");
        }
        if (response == null) {
            log.error("getBidRecommendationsForTargeting 网络延迟，请稍后重试");
            return Collections.emptyList();
        }
        if (response.getStatusCode() != 200) {
            log.warn("getBidRecommendationsForTargeting: {}", response.getStatusMessage());
            return Collections.emptyList();
        }
        List<BidRecommendation> data = response.getBidRecommendations();
        if (data == null) {
            log.error("getBidRecommendationsForTargeting 网络延迟，请稍后重试");
            return Collections.emptyList();
        }
        return data;
    }

    /**
     * 查询投放建议竞价(新建广告组)
     *
     * @param shop                 shop
     * @param profile
     * @param targetingExpressions
     * @param campaignId
     * @param adGroupId
     * @return
     */
    public List<BidRecommendation> getBidRecommendationsWithExistedGroup(ShopAuth shop, AmazonAdProfile profile, List<TargetingExpression> targetingExpressions, String campaignId, String adGroupId) {
        TargetingBidRecommendationsResponse response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBidRecommendationsWithExistedGroup(shopAuthService.getAdToken(shop),
            profile.getProfileId(), profile.getMarketplaceId(), campaignId, adGroupId, targetingExpressions, "BIDS_FOR_EXISTING_AD_GROUP");
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBidRecommendationsWithExistedGroup(shopAuthService.getAdToken(shop),
                profile.getProfileId(), profile.getMarketplaceId(), campaignId, adGroupId, targetingExpressions, "BIDS_FOR_EXISTING_AD_GROUP");
        }
        if (response == null) {
            log.error("getBidRecommendationsForTargeting 网络延迟，请稍后重试");
            return Collections.emptyList();
        }
        if (response.getStatusCode() != 200) {
            log.warn("getBidRecommendationsForTargeting: {}", response.getStatusMessage());
            return Collections.emptyList();
        }
        List<BidRecommendation> data = response.getBidRecommendations();
        if (data == null) {
            log.error("getBidRecommendationsForTargeting 网络延迟，请稍后重试");
            return Collections.emptyList();
        }
        return data;
    }


    /**
     * 查询投放建议竞价(已存在广告组)
     *
     * @param shop                 shop
     * @param profile
     * @param targetingExpressions
     * @param campaignId
     * @param adGroupId
     * @return
     */
    public List<BidRecommendation> getBidRecommendationsWithExistedGroupV5(ShopAuth shop, AmazonAdProfile profile, List<TargetingExpression> targetingExpressions, String campaignId, String adGroupId) {
        TargetingBidRecommendationsResponse response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBidRecommendationsWithExistedGroupV5(shopAuthService.getAdToken(shop),
                profile.getProfileId(), profile.getMarketplaceId(), campaignId, adGroupId, targetingExpressions, "BIDS_FOR_EXISTING_AD_GROUP", true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBidRecommendationsWithExistedGroupV5(shopAuthService.getAdToken(shop),
                    profile.getProfileId(), profile.getMarketplaceId(), campaignId, adGroupId, targetingExpressions, "BIDS_FOR_EXISTING_AD_GROUP", true);
        }
        if (response == null) {
            log.error("getBidRecommendationsForTargeting 网络延迟，请稍后重试");
            return Collections.emptyList();
        }
        if (response.getStatusCode() != 200) {
            log.warn("getBidRecommendationsForTargeting: {}", response.getStatusMessage());
            return Collections.emptyList();
        }
        List<BidRecommendation> data = response.getBidRecommendations();
        if (data == null) {
            log.error("getBidRecommendationsForTargeting 网络延迟，请稍后重试");
            return Collections.emptyList();
        }
        return data;
    }

    public Result<TargetApiResponseV3> updateStateAndBid(List<AmazonAdTargeting> amazonAdTargetings, ShopAuth shop) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.returnErr("请求参数错误: 投放为空");
        }

        AmazonAdTargeting one = amazonAdTargetings.get(0);

        List<PutTargetEntityV3> targetingList = makePutTargetingClausesBidAndState(amazonAdTargetings);
        UpdateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
            one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getError() != null) {
            return ResultUtil.returnErr(response.getError().getMessage());
        }
        TargetApiResponseV3 data = response.getData();
        if (data == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        return ResultUtil.returnSucc(data);
    }

    /**
     * 创建广告产品
     *
     * @param adProducts
     * @param shop
     * @return
     */
    public Result<ProductApiResponseV3> createProductAdsV3(List<AmazonAdProduct> adProducts, ShopAuth shop, AmazonAdProfile targetAmazonAdProfile) {
        if (CollectionUtils.isEmpty(adProducts)) {
            return ResultUtil.returnErr("请求参数错误: 广告产品为空");
        }
        List<ProductEntityV3> products = makeCreateProductsV3(adProducts);
        CreateSpProductV3Response response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createProductAd(shopAuthService.getAdToken(shop),
            targetAmazonAdProfile.getProfileId(), shop.getMarketplaceId(), products, true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createProductAd(shopAuthService.getAdToken(shop),
                targetAmazonAdProfile.getProfileId(), shop.getMarketplaceId(), products, true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.returnErr(response.getError().getMessage());
        }
        ProductApiResponseV3 data = response.getData();
        if (data == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        return ResultUtil.returnSucc(data);
    }

    /**
     * 创建关键词
     *
     * @param amazonAdKeywords：
     * @return ：Result
     */
    public Result<KeywordApiResponseV3> createKeywordsV3(List<AmazonAdKeyword> amazonAdKeywords, ShopAuth shop) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.returnErr("请求参数错误: 关键词为空");
        }
        AmazonAdKeyword one = amazonAdKeywords.get(0);

        List<CreateKeywordEntityV3> keywords = makeCreateKeywordsV3(amazonAdKeywords);
        CreateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shopAuthService.getAdToken(shop),
            one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getError() != null) {
            return ResultUtil.returnErr(response.getError().getMessage());
        }
        KeywordApiResponseV3 data = response.getData();
        if (data == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        return ResultUtil.returnSucc(data);
    }

    /**
     * 创建产品投放
     *
     * @param targetings 产品投放list
     * @param shop
     * @return
     */
    public Result<TargetApiResponseV3> createTargetV3(List<AmazonAdTargeting> targetings, ShopAuth shop) {
        if (CollectionUtils.isEmpty(targetings)) {
            return ResultUtil.returnErr("请求参数错误: 产品投放为空");
        }

        AmazonAdTargeting one = targetings.get(0);

        List<CreateTargetEntityV3> targetingList = makeCreateTargetingClausesV3(targetings);
        CreateSpTargetV3Response response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shopAuthService.getAdToken(shop),
            one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = TargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        }

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.returnErr(response.getError().getMessage());
        }

        TargetApiResponseV3 data = response.getData();

        if (data == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        return ResultUtil.returnSucc(data);
    }

    /**
     * 创建否定关键词
     *
     * @param amazonAdKeywords：
     * @return ：Result
     */
    public Result<NegativeKeywordApiResponseV3> createNegativeKeywordV3(List<AmazonAdKeyword> amazonAdKeywords, ShopAuth shop) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.error("请求参数错误: 否定关键词为空");
        }

        AmazonAdKeyword one = amazonAdKeywords.get(0);

        List<CreateNegativeKeywordEntityV3> keywords = makeCreateNegativeKeywordsV3(amazonAdKeywords);
        CreateSpNegativeKeywordV3Response response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shopAuthService.getAdToken(shop),
            one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null
            && response.getStatusCode() != null
            && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.returnErr(response.getError().getMessage());
        }

        NegativeKeywordApiResponseV3 data = response.getData();

        if (data == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        return ResultUtil.returnSucc(data);
    }

    /**
     * 创建活动层级否定关键词
     *
     * @param spNeKeywordsVoList：
     * @return ：Result
     */
    public Result<CampaignNegativeKeywordApiResponseV3> createCampaignNegativeKeywordV3(List<AmazonAdCampaignNeKeywords> spNeKeywordsVoList, ShopAuth shop, AmazonAdProfile targetAmazonAdProfile) {
        if (CollectionUtils.isEmpty(spNeKeywordsVoList)) {
            return ResultUtil.success();
        }
        List<CampaignNegativeKeywordEntityV3> keywords = makeCampaignNegativeKeyword(spNeKeywordsVoList);
        CreateSpCampaignNegativeKeywordV3Response response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
            targetAmazonAdProfile.getProfileId(), targetAmazonAdProfile.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null
            && response.getStatusCode() != null
            && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignNegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeKeyword(shopAuthService.getAdToken(shop),
                targetAmazonAdProfile.getProfileId(), targetAmazonAdProfile.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        CampaignNegativeKeywordApiResponseV3 data = response.getData();

        if (data == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        return ResultUtil.returnSucc(data);
    }

    private List<CampaignNegativeKeywordEntityV3> makeCampaignNegativeKeyword(List<AmazonAdCampaignNeKeywords> amazonAdKeywordList) {
        List<CampaignNegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CampaignNegativeKeywordEntityV3 keyword;
        for (AmazonAdCampaignNeKeywords amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CampaignNegativeKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            //amazonAdKeyword.getMatchType()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            keyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()) == null ? amazonAdKeyword.getMatchType() : SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            //amazonAdKeyword.getState()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()) == null ? amazonAdKeyword.getState() : SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            list.add(keyword);
        }
        return list;
    }

    /**
     * 创建活动层级否定投放
     *
     * @param spNeTargetingsVoList：
     * @return ：Result
     */
    public Result<CampaignNegativeTargetApiResponseV3> createCampaignNegativeTargetingV3(List<AmazonAdCampaignNetargetingSp> spNeTargetingsVoList, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        if (CollectionUtils.isEmpty(spNeTargetingsVoList)) {
            return ResultUtil.error("请求参数错误: 否定关键词为空");
        }

        List<CreateCampaignNegativeTargetEntityV3> createCampaignNegativeTargetEntityV3s = makeCampaignNegativeTargeting(spNeTargetingsVoList);
        CreateSpCampaignNegativeTargetV3Response response = CampaignNegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeTargets(shopAuthService.getAdToken(shop),
            amazonAdProfile.getProfileId(), amazonAdProfile.getMarketplaceId(), createCampaignNegativeTargetEntityV3s, Boolean.TRUE);
        if (response != null
            && response.getStatusCode() != null
            && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignNegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaignNegativeTargets(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), amazonAdProfile.getMarketplaceId(), createCampaignNegativeTargetEntityV3s, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.returnErr(response.getError().getMessage());
        }

        CampaignNegativeTargetApiResponseV3 data = response.getData();

        if (data == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        return ResultUtil.returnSucc(data);
    }

    private List<CreateCampaignNegativeTargetEntityV3> makeCampaignNegativeTargeting(List<AmazonAdCampaignNetargetingSp> amazonAdKeywordList) {

        List<CreateCampaignNegativeTargetEntityV3> list = new LinkedList<>();
        amazonAdKeywordList.forEach(e -> {
            com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression expression = new com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression();
            expression.setType(SpV3ExpressionEnum.asinSameAs.getValueV3());
            expression.setValue(e.getTargetText());

            CreateCampaignNegativeTargetEntityV3 campaignNegativeTarget = new CreateCampaignNegativeTargetEntityV3();
            campaignNegativeTarget.setCampaignId(e.getCampaignId());
            campaignNegativeTarget.setState(SpV3StateEnum.ENABLED.valueV3());
            campaignNegativeTarget.setExpression(Stream.of(expression).collect(Collectors.toList()));
            list.add(campaignNegativeTarget);
        });
        return list;
    }

    /**
     * 创建否定商品
     *
     * @param neTargetings：
     * @return ：Result
     */
    public Result<NegativeTargetApiResponseV3> createNeTargetV3(List<SpNeTargetingVo> neTargetings, ShopAuth shop) {
        if (CollectionUtils.isEmpty(neTargetings)) {
            return ResultUtil.success();
        }

        SpNeTargetingVo one = neTargetings.get(0);

        List<CreateNegativeTargetEntityV3> targetingList = makeCreateNegativeTargetingClausesV3(neTargetings);
        CreateSpNegativeTargetV3Response response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shopAuthService.getAdToken(shop),
            one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), targetingList, Boolean.TRUE);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        NegativeTargetApiResponseV3 data = response.getData();

        if (data == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        return ResultUtil.returnSucc(data);
    }

    private List<CreateNegativeTargetEntityV3> makeCreateNegativeTargetingClausesV3(List<SpNeTargetingVo> list) {
        List<CreateNegativeTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        CreateNegativeTargetEntityV3 targetingClause;
        for (SpNeTargetingVo amazonAdTargeting : list) {
            targetingClause = new CreateNegativeTargetEntityV3();
            targetingClause.setCampaignId(amazonAdTargeting.getCampaignId());
            targetingClause.setAdGroupId(amazonAdTargeting.getAdGroupId());
            //amazonAdTargeting.getState()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            targetingClause.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()) == null ? amazonAdTargeting.getState() : SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()).valueV3());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                List<com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions) {
                    com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression e = new com.amazon.advertising.spV3.negativetargeting.entity.TargetExpression();
                    //expression.getType()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()) == null ? expression.getType() : SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private List<CreateNegativeKeywordEntityV3> makeCreateNegativeKeywordsV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CreateNegativeKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CreateNegativeKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CreateNegativeKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            //amazonAdKeyword.getMatchType()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            keyword.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()) == null ? amazonAdKeyword.getMatchType() : SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            //amazonAdKeyword.getState()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()) == null ? amazonAdKeyword.getState() : SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            list.add(keyword);
        }
        return list;
    }

    private List<CreateTargetEntityV3> makeCreateTargetingClausesV3(List<AmazonAdTargeting> list) {
        List<CreateTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        CreateTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new CreateTargetEntityV3();
            targetingClause.setCampaignId(amazonAdTargeting.getCampaignId());
            targetingClause.setAdGroupId(amazonAdTargeting.getAdGroupId());
            //amazonAdTargeting.getExpressionType()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            targetingClause.setExpressionType(SpV3TargetingTypeEnum.getSpV3TargetingTypeEnumByValue(amazonAdTargeting.getExpressionType()) == null ? amazonAdTargeting.getExpressionType() : SpV3TargetingTypeEnum.getSpV3TargetingTypeEnumByValue(amazonAdTargeting.getExpressionType()).valueV3());
            //amazonAdTargeting.getState()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            targetingClause.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()) == null ? amazonAdTargeting.getState() : SpV3StateEnum.getSpV3StateEnumByValue(amazonAdTargeting.getState()).valueV3());
            targetingClause.setBid(amazonAdTargeting.getBid());
            if (StringUtils.isNotBlank(amazonAdTargeting.getExpression())) {
                List<Expression> expressions = JSONUtil.jsonToArray(amazonAdTargeting.getExpression(), Expression.class);
                List<TargetExpression> targetExpressions = new ArrayList<>();
                for (Expression expression : expressions) {
                    TargetExpression e = new TargetExpression();
                    //expression.getType()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
                    e.setType(SpV3ExpressionEnum.getExpressionByValue(expression.getType()) == null ? expression.getType() : SpV3ExpressionEnum.getExpressionByValue(expression.getType()).getValueV3());
                    e.setValue(expression.getValue());
                    targetExpressions.add(e);
                }
                targetingClause.setExpression(targetExpressions);
            }
            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    private List<CreateKeywordEntityV3> makeCreateKeywordsV3(List<AmazonAdKeyword> amazonAdKeywordList) {
        List<CreateKeywordEntityV3> list = Lists.newArrayListWithCapacity(amazonAdKeywordList.size());
        CreateKeywordEntityV3 keyword;
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywordList) {
            keyword = new CreateKeywordEntityV3();
            keyword.setCampaignId(amazonAdKeyword.getCampaignId());
            keyword.setAdGroupId(amazonAdKeyword.getAdGroupId());
            keyword.setKeywordText(amazonAdKeyword.getKeywordText());
            //amazonAdKeyword.getMatchType()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            keyword.setMatchType(SpV3MatchTypeEnum.getSpV3MatchTypeEnumByValue(amazonAdKeyword.getMatchType()) == null ? amazonAdKeyword.getMatchType() : SpV3MatchTypeEnum.getSpV3MatchTypeEnumByValue(amazonAdKeyword.getMatchType()).valueV3());
            //amazonAdKeyword.getState()有可能是v2(赛狐创建的)也有可能是v3(在其他平台创建，同步回来的)，所以这边进行兼容
            keyword.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()) == null ? amazonAdKeyword.getState() : SpV3StateEnum.getSpV3StateEnumByValue(amazonAdKeyword.getState()).valueV3());
            keyword.setBid(amazonAdKeyword.getBid());
            list.add(keyword);
        }
        return list;
    }

    private List<ProductEntityV3> makeCreateProductsV3(List<AmazonAdProduct> adProducts) {
        List<ProductEntityV3> list = Lists.newArrayListWithCapacity(adProducts.size());
        ProductEntityV3 productAd;
        //3，po->接口数据
        for (AmazonAdProduct amazonAdProduct : adProducts) {
            productAd = new ProductEntityV3();
            productAd.setCampaignId(amazonAdProduct.getCampaignId());
            productAd.setAdGroupId(amazonAdProduct.getAdGroupId());
            //seller只能用sku，vendor才可以用asin
            productAd.setSku(amazonAdProduct.getSku());
            productAd.setState(SpV3StateEnum.ENABLED.valueV3());
            list.add(productAd);
        }
        return list;
    }

    private List<PutTargetEntityV3> makePutTargetingClausesBidAndState(List<AmazonAdTargeting> list) {
        List<PutTargetEntityV3> targetingList = Lists.newArrayListWithCapacity(list.size());
        PutTargetEntityV3 targetingClause;
        for (AmazonAdTargeting amazonAdTargeting : list) {
            targetingClause = new PutTargetEntityV3();
            if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                targetingClause.setTargetId(amazonAdTargeting.getTargetId());
            }
            if (StringUtils.isNotBlank(amazonAdTargeting.getState())) {
                targetingClause.setState(AdStateV3.fromOldValue(amazonAdTargeting.getState()).getValue());
            }
            if (amazonAdTargeting.getBid() != null) {
                targetingClause.setBid(amazonAdTargeting.getBid());
            }

            targetingList.add(targetingClause);
        }
        return targetingList;
    }

    /**
     * 处理关键词翻译
     *
     * @param keywords            关键词列表
     * @param sourceMarketplaceId sourceMarketplaceId
     * @param targetMarketplaceId targetMarketplaceId
     * @param shop                shop
     * @param amazonAdProfile     amazonAdProfile
     */
    public void dealTranslateKeyword(List<AmazonAdKeyword> keywords, String sourceMarketplaceId, String targetMarketplaceId, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        if (CollectionUtils.isEmpty(keywords)) {
            return;
        }
        List<String> keywordList = keywords.stream().filter(k -> k != null && StringUtils.isNotBlank(k.getKeywordText()))
            .map(AmazonAdKeyword::getKeywordText).collect(Collectors.toList());

        Map<String, String> translatedMap = translateKeywordsByMarketplaceId(sourceMarketplaceId, targetMarketplaceId, keywordList, shop, amazonAdProfile);

        for (AmazonAdKeyword keyword : keywords) {
            if (keyword != null && translatedMap.containsKey(keyword.getKeywordText())) {
                keyword.setKeywordText(translatedMap.get(keyword.getKeywordText()));
            }
        }
    }

    /**
     * 处理关键词翻译
     *
     * @param keywords            关键词列表
     * @param sourceMarketplaceId sourceMarketplaceId
     * @param targetMarketplaceId targetMarketplaceId
     * @param shop                shop
     * @param amazonAdProfile     amazonAdProfile
     */
    public void dealTranslateNeKeyword(List<AmazonAdCampaignNeKeywords> keywords, String sourceMarketplaceId, String targetMarketplaceId, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        if (CollectionUtils.isEmpty(keywords)) {
            return;
        }
        List<String> keywordList = keywords.stream().filter(k -> k != null && StringUtils.isNotBlank(k.getKeywordText()))
            .map(AmazonAdCampaignNeKeywords::getKeywordText).collect(Collectors.toList());

        Map<String, String> translatedMap = translateKeywordsByMarketplaceId(sourceMarketplaceId, targetMarketplaceId, keywordList, shop, amazonAdProfile);

        for (AmazonAdCampaignNeKeywords keyword : keywords) {
            if (keyword != null && translatedMap.containsKey(keyword.getKeywordText())) {
                keyword.setKeywordText(translatedMap.get(keyword.getKeywordText()));
            }
        }
    }

    /**
     * 货币汇率转换
     *
     * @param sourceMarketplaceId sourceMarketplaceId
     * @param targetMarketplaceId targetMarketplaceId
     * @param bid                 bid
     * @param puid                puid
     * @return
     */
    public Double exchangeRate(String sourceMarketplaceId, String targetMarketplaceId, Double bid, Integer puid) {
        try {
            AmznEndpoint sourceEndpoint = AmznEndpoint.getByMarketplaceId(sourceMarketplaceId);
            AmznEndpoint targetEndpoint = AmznEndpoint.getByMarketplaceId(targetMarketplaceId);
            DimCurrencyRate lastRate = dimCurrencyRateDao.getLastRate(puid, sourceEndpoint.getCurrencyCode().value(), targetEndpoint.getCurrencyCode().value());
            BigDecimal bigDecimalBid = new BigDecimal(bid);
            BigDecimal finalBid = bigDecimalBid.multiply(lastRate.getRate());
            return finalBid.doubleValue();
        } catch (Exception e) {
            return bid;
        }
    }

    /**
     * 调用亚马逊接口进行关键词的翻译
     *
     * @param sourceMarketplaceId
     * @param targetMarketplaceId
     * @param sourceKeywordList
     * @param shop
     * @param amazonAdProfile
     * @return
     */
    public Map<String, String> translateKeywordsByMarketplaceId(String sourceMarketplaceId, String targetMarketplaceId, List<String> sourceKeywordList, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        Map<String, String> resultMap = new HashMap<>(sourceKeywordList.size());
        if (CollectionUtils.isEmpty(sourceKeywordList)) {
            return resultMap;
        }
        List<List<String>> partition = Lists.partition(sourceKeywordList, MAX_SIZE);
        for (List<String> sourceKeywordPartition:partition) {
            SourceDetail sourceDetail = new SourceDetail();
            sourceDetail.setMarketplaceId(sourceMarketplaceId);
            TargetDetail targetDetail = new TargetDetail();
            targetDetail.setMarketplaceIds(Collections.singletonList(targetMarketplaceId));
            KeywordsLocalizationResponse response = KeywordsLocalizationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getKeywordsLocalize(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                amazonAdProfile.getMarketplaceId(), sourceKeywordPartition, sourceDetail, targetDetail);

            if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response = KeywordsLocalizationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getKeywordsLocalize(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                    amazonAdProfile.getMarketplaceId(), sourceKeywordPartition, sourceDetail, targetDetail);
            }

            if (response != null && response.getResult() != null && CollectionUtils.isNotEmpty(response.getResult().getLocalizedKeywordResponses())) {
                List<LocalizationKeywordResult> localizedKeywordResponses = response.getResult().getLocalizedKeywordResponses();
                for (LocalizationKeywordResult localizationKeywordResult : localizedKeywordResponses) {
                    if (localizationKeywordResult != null && localizationKeywordResult.getSourceKeyword() != null && StringUtils.isNotBlank(localizationKeywordResult.getSourceKeyword().getKeyword())) {
                        if (localizationKeywordResult.getLocalizedKeywords() != null && localizationKeywordResult.getLocalizedKeywords().containsKey(targetMarketplaceId)) {
                            Keyword keyword = localizationKeywordResult.getLocalizedKeywords().get(targetMarketplaceId);
                            if (keyword != null && StringUtils.isNotBlank(keyword.getKeyword())) {
                                resultMap.put(localizationKeywordResult.getSourceKeyword().getKeyword(), keyword.getKeyword());
                            }
                        }
                    }
                }
            }
        }

        return resultMap;
    }

}
