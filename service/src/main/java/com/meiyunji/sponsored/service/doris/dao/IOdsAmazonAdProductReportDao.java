package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dto.GroupCampaignDto;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdProductReport;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdProductDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdProductTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdSalesmanDto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdSalesmanTopDataDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByRateEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardQueryFieldEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.wordFrequency.qo.GetWordRootAggregateDataQo;

import java.time.LocalDate;
import java.util.List;

/**
 * amazon广告产品报告表(OdsAmazonAdProductReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:19
 */
public interface IOdsAmazonAdProductReportDao extends IDorisBaseDao<OdsAmazonAdProductReport> {

    /**
     * 根据asin、parentAsin、msku获取广告组和广告活动id
     */
    List<GroupCampaignDto> getGroupIdsAndCampaignIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                                         String searchType, String searchValue, String startDate, String endDate);
    /**
     * 根据asin、parentAsin、msku获取广告组id
     */
    List<String> getGroupIdsByCampaignIdsAndAsin(Integer puid, List<Integer> shopIds, List<String> campaignIds, List<String> groupIds,
                                                 String searchType, String searchValue, String startDate, String endDate);
    /**
     * 根据父asin获取asin列表
     */
    List<String> getAsinByParentAsinAndGroupIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> groupIds,
                                                              String parentAsin, String startDate, String endDate);

    List<AsinListDto> getAsinByParentAsinList(Integer puid, List<Integer> shopIds, List<String> groupIds,
                                                              List<String> parentAsinList, String startDate, String endDate);

    /**
     * 根据msku获取asin列表
     */
    List<String> getAsinByMskuAndGroupIdsAndCampaignIds(Integer puid, List<Integer> shopIds, List<String> groupIds,
                                                        String msku, String startDate, String endDate);

    List<AsinListDto> getAsinByMskuList(Integer puid, List<Integer> shopIds, List<String> groupIds,
                                        List<String> mskuList, String startDate, String endDate);

    /**
     * 产品透视分析根据产品获取adId
     */
    List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByProduct(Integer puid, List<Integer> shopIdList, String marketPlaceId,
                                                                      String searchType, String searchValue, String startDate, String endDate);

    /**
     * 产品透视分析根据产品获取adId
     */
    List<String> adIdListByProduct(Integer puid, List<Integer> shopIdList, String marketPlaceId,
                                   String searchType, String searchValue, String startDate, String endDate);

    /**
     * 根据asin获取广告组id
     */
    List<String> getGroupIdByAsin(Integer puid, GetWordRootAggregateDataQo qo, String start, String end);


    String adProductByAsinOrSkuQuerySql(Integer puid, List<Integer> shopIdList,
                                        List<String> marketplaceIdList, List<String> adIds,
                                        String currency, String startDate, String endDate,
                                        List<Object> argsList, boolean isAsin,
                                        List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds,
                                        Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);


    String adProductQuerySql(Integer puid, List<Integer> shopIdList,
                                  List<String> marketplaceIdList, List<String> adIds,
                                  String currency, String startDate, String endDate,
                                  List<Object> argsList, DashboardQueryFieldEnum dashboardQueryFieldEnum, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds, List<String> campaignIds,
                             Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, LocalDate categoryQueryDate, List<String> noFind);

    String adProductGroupByParentAsinQuerySql(Integer puid, List<Integer> shopIdList,
                                              List<String> marketplaceIdList, List<String> adIds,
                                              String currency, String startDate, String endDate,
                                              List<Object> argsList, List<String> siteToday, Boolean isSiteToday,
                                              List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum);

    String adProductGroupByCategoryQuerySql(Integer puid, List<Integer> shopIdList,
                                            List<String> marketplaceIdList, List<String> adIds,
                                            String currency, String startDate, String endDate,
                                            List<Object> argsList, List<String> siteToday, Boolean isSiteToday,
                                            List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, LocalDate categoryQueryDate,
                                            List<String> noFind);

    List<DashboardAdProductTopDataDto> queryAdProductYoyOrMomTop(String subSqlA, String subSqlB,
                                                                 List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                 DashboardOrderByRateEnum orderField, String orderBy,
                                                                 Integer limit, Boolean noZero, DashboardQueryFieldEnum dashboardQueryFieldEnum);

    List<DashboardAdProductDto> queryAdProductCharts(Integer puid, List<Integer> shopIdList,
                                                     List<String> marketplaceIdList, List<String> ads,
                                                     String currency, String startDate, String endDate, DashboardQueryFieldEnum dashboardQueryFieldEnum,
                                                     List<String> siteToday, Boolean isSiteToday,
                                                     List<String> portfolioIds, List<String> campaignIds, Boolean noZero,
                                                     DashboardDataFieldEnum dashboardDataFieldEnum, LocalDate categoryQueryDate, List<String> noFind);


    Page<AsinListDto> getAsinSkuPageByPuidAndShopId(AsinListReqVo reqVo);

    /**
     * 只查出parent_id
     */
    Page<AsinListDto> getParentAsinIdPage(AsinListReqVo reqVo);

    String adProductGroupByDevIdQuerySql(Integer puid, List<Integer> shopIdList,
                                         List<String> marketplaceIdList, List<Integer> devIds,
                                         String currency, String startDate, String endDate,
                                         List<Object> argsList, List<String> siteToday, Boolean isSiteToday,
                                         List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, boolean isSummary);

    List<DashboardAdSalesmanTopDataDto> queryAdSalesmanYoyOrMomTop(String subSqlA, String subSqlB,
                                                                   List<Object> queryParam, DashboardDataFieldEnum dataField,
                                                                   DashboardOrderByRateEnum orderField, String orderBy,
                                                                   Integer limit, Boolean noZero);

    List<DashboardAdSalesmanDto> queryAdSalesmanCharts(Integer puid, List<Integer> shopIdList,
                                                       List<String> marketplaceIdList, List<Integer> devIds,
                                                       String currency, String startDate, String endDate,
                                                       List<String> siteToday, Boolean isSiteToday,
                                                       List<String> portfolioIds, List<String> campaignIds, Boolean noZero, DashboardDataFieldEnum dashboardDataFieldEnum, boolean isSummary);
}

