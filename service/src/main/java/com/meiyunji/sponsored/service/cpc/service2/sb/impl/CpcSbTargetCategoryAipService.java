package com.meiyunji.sponsored.service.cpc.service2.sb.impl;

import com.amazon.advertising.sb.entity.targeting.Filters;
import com.amazon.advertising.sb.entity.targetingRecommendation.*;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;

import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by lm on 2021/8/3.
 */

@Component
@Slf4j
public class CpcSbTargetCategoryAipService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public Result<TargetCategoryResult> getTargetsCategoryList(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<String> asinList) {

        ListTargetCategoryRecommendationsResponse response = cpcApiHelper.call(shop, () -> TargetingRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getCategoryList(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), asinList));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResult());
        }

        String errMsg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                errMsg = response.getResult().getDetails();
            } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                errMsg = response.getResult().getDescription();
            }
        }
        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }

    public Result<TargetBrandResult> getTargetsBrandList(ShopAuth shop, AmazonAdProfile amazonAdProfile, String categoryId, String keyword) {

        ListTargetBrandRecommendationsResponse response = cpcApiHelper.call(shop, () -> TargetingRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getTargetBrandList(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), Long.valueOf(categoryId), keyword));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }


        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResult());
        }

        String errMsg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                errMsg = response.getResult().getDetails();
            } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                errMsg = response.getResult().getDescription();
            }
        }
        return ResultUtil.returnErr(errMsg);
    }

    public Result<List<RecommendedProducts>> getSuggestAsin(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<String> asinList) {
        // 因为只取100条，所以不做分页查询处理
        Integer maxResults  = 100;
        String nextToken = "";
        List<Filters> filtersList =  new ArrayList<>();
        Filters filters = new Filters();
        filters.setFilterType("ASINS");
        filters.setValues(asinList);
        filtersList.add(filters);
        ListTargetProductRecommendationsResponse response = cpcApiHelper.call(shop, () -> TargetingRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getProductList(shopAuthService.getAdToken(shop),
                amazonAdProfile.getProfileId(), shop.getMarketplaceId(), nextToken, maxResults, filtersList));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getStatusCode() != null && response.getStatusCode() == 200) {
            return ResultUtil.returnSucc(response.getResult().getRecommendedProducts());
        }

        String errMsg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            if (StringUtils.isNotBlank(response.getResult().getDetails())) {
                errMsg = response.getResult().getDetails();
            } else if (StringUtils.isNotBlank(response.getResult().getDescription())) {
                errMsg = response.getResult().getDescription();
            }
        }
        return ResultUtil.returnErr(errMsg);
    }


}
