package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.mode.Adjustment;
import com.amazon.advertising.mode.Bidding;
import com.amazon.advertising.mode.campaigns.Campaign;
import com.amazon.advertising.spV3.campaign.CampaignSpV3Client;
import com.amazon.advertising.spV3.campaign.ListSpCampaignV3Response;
import com.amazon.advertising.spV3.campaign.UpdateSpCampaignV3Response;
import com.amazon.advertising.spV3.campaign.entity.*;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.amazon.advertising.spV3.response.ErrorItemV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaign;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioErrorMsgVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.CampaignPlacementV3;
import com.meiyunji.sponsored.service.enums.CampaignStrategyV3;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/15.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcCampaignApiService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private AadasApiFactory aadasApiFactory;
    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IDorisService dorisService;

    /**
     * 是否同步日志
     * @param shop
     * @param campaignId
     * @param syncHistory
     */
    public void syncCampaigns(ShopAuth shop, String campaignId, List<CampaignV3State> stateList, boolean syncHistory, boolean isThrow, boolean isProxy) {
        long start = System.currentTimeMillis();
        if (shop == null) {
            return;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncCampaigns:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncCampaigns--配置信息为空");
            return;
        }

        List<String> campaignIds = null;
        if(StringUtils.isNotBlank(campaignId)){
            campaignIds = StringUtil.splitStr(campaignId,",");
        }

        //获取活动的基本信息
        CampaignSpV3Client client = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = CampaignSpV3Client.getInstance(true);
        }

        ListSpCampaignV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listCampaigns(shopAuthService.getAdToken(shop),
                    amazonAdProfile.getProfileId(),
                    shop.getMarketplaceId(),
                    campaignIds,
                    null,
                    stateList,
                    null,
                    null,
                    null,
                    nextToken,
                    null);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP campaigns rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listCampaigns(shopAuthService.getAdToken(shop),
                        amazonAdProfile.getProfileId(),
                        shop.getMarketplaceId(),
                        campaignIds,
                        null,
                        stateList,
                        null,
                        null,
                        null,
                        nextToken,
                        null);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }

            if (AmazonResponseUtil.isError(response) && isThrow) {
                throw new ServiceException("请求错误");
            }

            //未同步到数据
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getCampaigns())) {
                break;
            }

            List<CampaignExtendEntityV3> campaignList = response.getData().getCampaigns();
            int size = campaignList.size();
            AmazonAdCampaignAll amazonAdCampaignAll;
            List<AmazonAdCampaignAll> amazonAdCampaignAlls = new ArrayList<>(size);
            for (CampaignExtendEntityV3 campaign : campaignList) {
                amazonAdCampaignAll = turnCampaignToAllPO(campaign);
                if (StringUtils.isNotBlank(amazonAdCampaignAll.getCampaignId())) {
                    amazonAdCampaignAll.setPuid(shop.getPuid());
                    amazonAdCampaignAll.setShopId(shop.getId());
                    amazonAdCampaignAll.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdCampaignAll.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdCampaignAll.setSyncOutOfBudgetTimeState(0);
                    amazonAdCampaignAlls.add(amazonAdCampaignAll);
                }
            }

            amazonAdCampaignAllDao.insertOnDuplicateKeyUpdateSp(shop.getPuid(), amazonAdCampaignAlls, syncHistory);

            if (StringUtils.isBlank(response.getData().getNextToken())) {
                break;
            } else {
                nextToken = response.getData().getNextToken();
            }
        }
        log.info("同步所有SP广告活动信息时间 puid: {} ,shopid: {} 花费时间 {}", shop.getPuid(), shop.getId(), System.currentTimeMillis() - start);
    }

    public void syncCampaigns(ShopAuth shop, String campaignId, boolean syncHistory) {
        syncCampaigns(shop, campaignId, syncHistory, false, false);
    }

    public void syncCampaigns(ShopAuth shop, String campaignId, boolean syncHistory, boolean isThrow) {
        syncCampaigns(shop, campaignId, null, syncHistory, isThrow, false);
    }

    public void syncCampaigns(ShopAuth shop, String campaignId, boolean syncHistory, boolean isThrow, boolean isProxy) {
        syncCampaigns(shop, campaignId, null, syncHistory, isThrow, isProxy);
    }

    public void syncCampaigns(ShopAuth shop, String campaignId, List<CampaignV3State> stateList, boolean syncHistory, boolean isThrow) {
        syncCampaigns(shop, campaignId, stateList, syncHistory, isThrow, false);
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String groupIds){
        List<AmazonAdCampaignAll> amazons = amazonAdCampaignAllDao.listByCampaignIds(puid, shopId, StringUtil.stringToList(groupIds,","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                key.setServingStatus(key.getServingStatus());
                return AmazonServingStatusDto.build(key.getCampaignId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());}
        ).collect(Collectors.toList());
    }

    public Result<List<PortfolioErrorMsgVo>> batchUpdateSpCampaignsPortfolio(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<CampaignEntityV3> campaignList)  {
        List<PortfolioErrorMsgVo> errList = new ArrayList<>();

        UpdateSpCampaignV3Response response = cpcApiHelper.call(shop, () -> CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                amazonAdProfile.getMarketplaceId(), campaignList, true));
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getStatusCode() == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";

        //非207报错,直接返回
        if (response.getError() != null) {
            return ResultUtil.returnErr(StringUtils.isNotBlank(response.getError().getMessage())
                    ? response.getError().getMessage() : errMsg);
        }

        PortfolioErrorMsgVo msgVo;
        if (response.getData() != null) {
            ApiResponseV3<campaignSuccessResultV3> campaigns = response.getData().getCampaigns();
            List<campaignSuccessResultV3> successList = campaigns.getSuccess();
            List<ErrorItemResultV3> errorList = campaigns.getError();

            //处理成功
            //PortfolioService中统一处理了，这里无需处理
            /*for (campaignSuccessResultV3 successResultV3 : successList) {
                String campaignId = successResultV3.getCampaignId();
                CampaignExtendEntityV3 campaign = successResultV3.getCampaign();
                String portfolioId = campaign.getPortfolioId();
                //更新到数据
                amazonAdCampaignAllDao.batchUpdatePortfolio(shop.getPuid(), shop.getId(),
                        portfolioId, shop.getId(), Collections.singletonList(campaignId));
            }*/
            //处理失败
            for (ErrorItemResultV3 errorItemResultV3 : errorList) {
                Integer index = errorItemResultV3.getIndex();
                CampaignEntityV3 campaignEntityV3 = campaignList.get(index);
                ErrorItemV3 errorItemV3 = errorItemResultV3.getErrors().get(0);
                String errorType = errorItemResultV3.getErrors().get(0).getErrorType();

                msgVo = new PortfolioErrorMsgVo();
                msgVo.setCampaignId(campaignEntityV3.getCampaignId());
                msgVo.setErrMsg(String.valueOf(errorItemV3.getErrorValue().get(errorType).get("message")));
                errList.add(msgVo);
            }
        } else {
            return ResultUtil.returnErr(errMsg);
        }

        return ResultUtil.returnSucc(errList);
    }


    /**
     * 归档
     * @param amazonAdCampaign：
     * @return ：Result
     */
    Result archive(AmazonAdCampaignAll amazonAdCampaign) {
        if (amazonAdCampaign == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(),amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaigns(shopAuthService.getAdToken(shop),
                amazonAdCampaign.getProfileId(), shop.getMarketplaceId(),
                Collections.singletonList(amazonAdCampaign.getCampaignId()), false);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response =  CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaigns(shopAuthService.getAdToken(shop),
                    amazonAdCampaign.getProfileId(), shop.getMarketplaceId(),
                    Collections.singletonList(amazonAdCampaign.getCampaignId()), false);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && CollectionUtils.isNotEmpty(response.getData().getCampaigns().getSuccess())) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
        }
        return ResultUtil.error(msg);
    }

    public List<CampaignExtendEntityV3> detectAdTypeSyncSpCampaign(ShopAuth shop) {
        if (shop == null) {
            return null;
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncCampaigns:店铺{}没有授权广告", shop.getId());
            return null;
        }
        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncCampaigns--配置信息为空");
            return null;
        }
        //获取活动的基本信息
        CampaignSpV3Client client = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        ListSpCampaignV3Response response;
        String accessToken = aadasApiFactory.getAccessToken(shop.getSellingPartnerId(), shop.getMarketplaceId());
        response =  client.listCampaigns(accessToken, amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                null, null,null,null,null,false,null,1);

        if (response == null || response.getData() == null) {
            return null;
        }
        return response.getData().getCampaigns();
    }

    // 把接口返回的dto转换成po
    private AmazonAdCampaign turnCampainToPO(Campaign campaign) {
        AmazonAdCampaign amazonAdCampaign = new AmazonAdCampaign();
        if (campaign.getCampaignId() != null) {
            amazonAdCampaign.setCampaignId(campaign.getCampaignId().toString());
        }
        amazonAdCampaign.setName(campaign.getName());
        amazonAdCampaign.setCampaignType(campaign.getCampaignType());
        amazonAdCampaign.setTargetingType(campaign.getTargetingType());
        amazonAdCampaign.setState(campaign.getState());
        amazonAdCampaign.setDailyBudget(campaign.getDailyBudget());
        amazonAdCampaign.setServingStatus(campaign.getServingStatus());
        if (campaign.getPortfolioId() != null) {
            amazonAdCampaign.setPortfolioId(String.valueOf(campaign.getPortfolioId()));
        }


        if (StringUtils.isNotBlank(campaign.getStartDate())) {
            amazonAdCampaign.setStartDate(DateUtil.strToDate(campaign.getStartDate(),"yyyyMMdd"));
        }
        // 活动结束时间是可以为空的
        if (StringUtils.isNotBlank(campaign.getEndDate())) {
            amazonAdCampaign.setEndDate(DateUtil.strToDate(campaign.getEndDate(),"yyyyMMdd"));
        }

        Bidding bidding = campaign.getBidding();
        if (bidding != null) {
            amazonAdCampaign.setStrategy(bidding.getStrategy());
            List<Adjustment> adjustments = bidding.getAdjustments();
            if (adjustments != null) {
                amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(adjustments));
            }
        }

        if(StringUtils.isNotBlank(campaign.getCreationDate())){
            amazonAdCampaign.setCreationDate(DateUtil.getDateByMillisecond(Long.valueOf(campaign.getCreationDate())));
        }
        if(StringUtils.isNotBlank(campaign.getLastUpdatedDate())){
            amazonAdCampaign.setLastUpdatedDate(DateUtil.getDateByMillisecond(Long.valueOf(campaign.getLastUpdatedDate())));
        }

        return amazonAdCampaign;
    }

    public List<CampaignExtendEntityV3> checkAdTypeSyncSpCampaign(ShopAuth shop) throws IOException, ClassNotFoundException {
        if (shop == null) {
            return null;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncCampaigns--配置信息为空");
            return null;
        }

        //获取活动的基本信息
        CampaignSpV3Client client = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        ListSpCampaignV3Response response;
        String accessToken = aadasApiFactory.getAccessToken(shop.getSellingPartnerId(), shop.getMarketplaceId());

        response =  client.listCampaigns(accessToken, amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                null, null,null,null,null,false,null,1);

        if (response == null) {
            return new ArrayList<>();
        }

        if (response.getStatusCode() == 401) {
            return null;
        }

        if (response.getData() == null) {
            return new ArrayList<>();
        }

        return response.getData().getCampaigns();
    }



    private AmazonAdCampaignAll turnCampaignToAllPO(CampaignExtendEntityV3 campaign) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        amazonAdCampaign.setType(Constants.SP);
        if (campaign.getCampaignId() != null) {
            amazonAdCampaign.setCampaignId(campaign.getCampaignId());
        }
        //兼容v2版本数据,targetingType存储时转成小写
        amazonAdCampaign.setAdTargetType(campaign.getTargetingType().toLowerCase());
        amazonAdCampaign.setTargetingType(campaign.getTargetingType().toLowerCase());
        amazonAdCampaign.setName(campaign.getName());
        //v3版本无些字段,字段固定为"sponsoredProducts"
        amazonAdCampaign.setCampaignType("sponsoredProducts");
        //兼容v2版本数据,存储小写
        amazonAdCampaign.setState(campaign.getState().toLowerCase());
        amazonAdCampaign.setBudget(campaign.getBudget() == null ? BigDecimal.ZERO:BigDecimal.valueOf(campaign.getBudget().getBudget()));
        amazonAdCampaign.setServingStatus(campaign.getExtendedData().getServingStatus());
        if (campaign.getPortfolioId() != null) {
            amazonAdCampaign.setPortfolioId(campaign.getPortfolioId());
        }
        //v3版本格式yyyy-MM-dd格式
        if (StringUtils.isNotBlank(campaign.getStartDate())) {
            amazonAdCampaign.setStartDate(DateUtil.strToDate(campaign.getStartDate(),"yyyy-MM-dd"));
        }
        // 活动结束时间是可以为空的 //v3版本格式yyyy-MM-dd格式
        if (StringUtils.isNotBlank(campaign.getEndDate())) {
            amazonAdCampaign.setEndDate(DateUtil.strToDate(campaign.getEndDate(),"yyyy-MM-dd"));
        }

        CamapginDynamicBidding dynamicBidding = campaign.getDynamicBidding();
        if (dynamicBidding != null) {
            //v2版本都是小写无下划线, v3版本LEGACY_FOR_SALES,AUTO_FOR_SALES,MANUAL,RULE_BASED 统一按v2版本格式存储
            CampaignStrategyV3 campaignStrategyV3 = CampaignStrategyV3.fromValue(dynamicBidding.getStrategy());
            if (campaignStrategyV3 != null) {
                amazonAdCampaign.setStrategy(campaignStrategyV3.getOldValue());
                List<PlacementBidding> placementBidding = dynamicBidding.getPlacementBidding();
                List<Map<String, Object>> maps = new ArrayList<>(placementBidding.size());
                if (CollectionUtils.isNotEmpty(placementBidding)) {
                    for (PlacementBidding bidding : placementBidding) {
                        Map<String, Object> map = new HashMap<>(1);
                        CampaignPlacementV3 campaignPlacementV3 = CampaignPlacementV3.fromValue(bidding.getPlacement());
                        if (campaignPlacementV3 != null) {
                            String oldValue = campaignPlacementV3.getOldValue();
                            map.put("predicate", oldValue);
                            map.put("percentage", bidding.getPercentage());
                            maps.add(map);
                        }
                    }
                }
                amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(maps));
            }
        }

        if(campaign.getExtendedData().getCreationDateTime() != null){
            amazonAdCampaign.setCreationDate(LocalDateTime.ofEpochSecond(
                    campaign.getExtendedData().getCreationDateTime().toEpochSecond(ZoneOffset.UTC),
                    0, ZoneOffset.ofHours(8)));
        }
        if(campaign.getExtendedData().getLastUpdateDateTime() != null){
            amazonAdCampaign.setLastUpdatedDate(LocalDateTime.ofEpochSecond(
                    campaign.getExtendedData().getLastUpdateDateTime().toEpochSecond(ZoneOffset.UTC),
                    0, ZoneOffset.ofHours(8)));
        }

        return amazonAdCampaign;
    }

    /**
     * 是否同步日志
     * @param shop
     * @param campaignId
     */
    public List<AmazonAdCampaignAll> syncCampaigns(ShopAuth shop, String campaignId) {
        long start = System.currentTimeMillis();
        List<AmazonAdCampaignAll> amazonAdCampaignAlls = new ArrayList<>();
        if (shop == null) {
            return new ArrayList<>();
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncCampaigns:店铺{}没有授权广告", shop.getId());
            return new ArrayList<>();
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncCampaigns--配置信息为空");
            return new ArrayList<>();
        }

        List<String> campaignIds = null;
        if(StringUtils.isNotBlank(campaignId)){
            campaignIds = StringUtil.splitStr(campaignId,",");
        }

        //获取活动的基本信息
        CampaignSpV3Client client = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        ListSpCampaignV3Response response;
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        String nextToken = null;
        while (true) {
            response = client.listCampaigns(
                    shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIds, nextToken);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP campaigns rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listCampaigns(
                        shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIds, nextToken);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }
            //未同步到数据
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getCampaigns())) {
                break;
            }

            List<CampaignExtendEntityV3> campaignList = response.getData().getCampaigns();
            int size = campaignList.size();
            AmazonAdCampaignAll amazonAdCampaignAll;
            for (CampaignExtendEntityV3 campaign : campaignList) {
                amazonAdCampaignAll = turnCampaignToAllPO(campaign);
                if (StringUtils.isNotBlank(amazonAdCampaignAll.getCampaignId())) {
                    amazonAdCampaignAll.setPuid(shop.getPuid());
                    amazonAdCampaignAll.setShopId(shop.getId());
                    amazonAdCampaignAll.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdCampaignAll.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdCampaignAll.setSyncOutOfBudgetTimeState(0);
                    amazonAdCampaignAlls.add(amazonAdCampaignAll);
                }
            }
            if (StringUtils.isBlank(response.getData().getNextToken())) {
                break;
            } else {
                nextToken = response.getData().getNextToken();
            }
        }
        log.info("同步所有SP广告活动信息时间 puid: {} ,shopid: {} 花费时间 {}", shop.getPuid(), shop.getId(), System.currentTimeMillis() - start);
        return amazonAdCampaignAlls;
    }
}
