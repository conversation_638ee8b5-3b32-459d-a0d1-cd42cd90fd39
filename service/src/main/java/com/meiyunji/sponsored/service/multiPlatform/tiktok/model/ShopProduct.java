package com.meiyunji.sponsored.service.multiPlatform.tiktok.model;

import lombok.Data;

@Data
public class ShopProduct {

    private String spuId;
    private String title;
    private String imageUrl;
    private String minPrice;
    private String maxPrice;
    private String currency;
    private Long sales;
    //前端是否可添加到右边
    private Boolean adForGmvMax;
    //是否被处于启用状态的商品 GMV Max 推广系列占用
    private Boolean gmvMaxAdsStatus;
    //是否被处于启用状态的视频购物广告或商品购物广告占用
    private Boolean runningCustomShopAds;
    //是否可用
    private Boolean status;

}
