package com.meiyunji.sponsored.service.adActivity.service;

import com.meiyunji.sponsored.service.adActivity.dto.AdCampaignProductQueryReq;
import com.meiyunji.sponsored.service.adActivity.vo.AdCampaignProductQueryVo;

import java.util.List;

public interface AdCampaignService {

    /**
     * 获取广告活动下的产品列表
     *
     * @param req
     * @return
     */
    List<AdCampaignProductQueryVo> getGroupProductList(AdCampaignProductQueryReq req);

}
