package com.meiyunji.sponsored.service.cpc.service.impl;

import com.google.api.client.util.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingServiceImpl;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.qo.TargetSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.service.IAdTagService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.AdMarkupTargetTypeEnum;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *<AUTHOR>
 */
@Slf4j
@Service
public class AdTagServiceImpl extends BaseShardingServiceImpl<AdTag> implements IAdTagService {
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao spGroupDao;
    @Autowired
    private IAmazonSdAdGroupDao sdGroupDao;
    @Autowired
    private IAmazonAdProductDao spAdProductDao;
    @Autowired
    private IAmazonSdAdProductDao sdAdProductDao;
    @Autowired
    private IAmazonSbAdKeywordDao sbKeywordDao;
    @Autowired
    private IAmazonSbAdTargetingDao sbAdTargetingDao;
    @Autowired
    private IAmazonSdAdTargetingDao sdAdTargetingDao;
    @Autowired
    private IAmazonSbAdGroupDao sbAdGroupDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    @Override
    public Result<List<AdTag>> queryAdTagList(int puid, String type, String adTagName) {
        Result<List<AdTag>> result = new Result<>();
        try {
            List<AdTag> adTagList = adTagDao.getList(puid, type, adTagName);
            result.setData(adTagList);
            result.setMsg("查询成功");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("查询广告标签报错");
            log.error("puid:{} type:{} adTagName:{} 查询广告标签报错:", puid, type, adTagName, e);
        }
        return result;
    }

    @Override
    public Result deleteByAdTagId(int puid, int uid, String loginIp, Long adTagId) {

        AdTag byPuidAndId = adTagDao.getByPuidAndId(puid, adTagId);
        if(byPuidAndId == null){
            return ResultUtil.error("标签不存在");
        }
        adMarkupTagDao.deleteByTagId(puid,adTagId);
        adTagDao.deleteByPuidAndId(puid,adTagId);
        return ResultUtil.success();
    }



    @Override
    public Result createAdTag(int puid, int uid, String loginIp, AdTagVo adTagVo) {
        boolean exist = adTagDao.exist(puid, adTagVo.getType(), adTagVo.getName().trim(),null);
        if(exist){
            return ResultUtil.error("标签名称重复");
        }
        Integer listCountByType = adTagDao.getListCountByType(puid, adTagVo.getType());
        if(listCountByType >= 100){
            return ResultUtil.error("广告活动标签数量已达100个，无法添加");
        }

        AdTag adTag = new AdTag();
        adTag.setPuid(adTagVo.getPuid());
        adTag.setCreateId(adTagVo.getUid());
        adTag.setColor(adTagVo.getColor());
        adTag.setName(adTagVo.getName());
        adTag.setType(adTagVo.getType());
        adTagDao.insert(adTag);
        return ResultUtil.success();
    }


    @Override
    public Result updateAdTag(int puid, int uid, String loginIp, AdTagVo adTagVo) {
        AdTag adTag = new AdTag();
        adTag.setId(adTagVo.getId());
        adTag.setPuid(adTagVo.getPuid());
        adTag.setUpdateId(adTagVo.getUid());
        adTag.setColor(adTagVo.getColor());
        adTag.setName(adTagVo.getName());
        AdTag byPuidAndId = adTagDao.getByPuidAndId(puid, adTag.getId());
        if(byPuidAndId == null){
            return ResultUtil.error("标签不存在");
        }
        if(!byPuidAndId.getName().equals(adTagVo.getName())){
            boolean exist = adTagDao.exist(puid, adTagVo.getType(), adTagVo.getName(),null);
            if(exist){
                return ResultUtil.error("标签名称重复");
            }
        }
        adTagDao.updateById(adTag.getPuid(),adTag);
        return ResultUtil.success();
    }

    @Override
    public Result<List<AdTag>> listAdTag(int puid, String type, List<String> ids) {
        List<Long> idList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(ids)) {
            idList = adMarkupTagDao.getAdTagId(puid, type, ids);
        }
        //ids不为空时获取移除标签列表，需要返回空标签，否则为获取添加标签列表，需要返回全部标签
        if (CollectionUtils.isEmpty(idList) && CollectionUtils.isNotEmpty(ids)) {
            return ResultUtil.success(new ArrayList<>());
        }
        List<AdTag> list = adTagDao.getList(puid, type, idList);
        return ResultUtil.success(list);
    }

    @Override
    public Result<List<AdMarkupTagVo>> makeAdTag(int puid, int uid, String loginIp, MakeAdTagVo makeAdTagVo) {

        AdTag byPuidAndId = adTagDao.getByPuidAndId(puid, makeAdTagVo.getAdTagId());
        if(byPuidAndId == null){
            return ResultUtil.error("标签不存在");
        }
        List<String> idsList = makeAdTagVo.getIdsList();
        if(idsList == null){
            return ResultUtil.error("请求参数错误");
        }
        if(idsList.size() > 200){
            return ResultUtil.error("最多一次标记200条");
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(makeAdTagVo.getShopId());
        if (shopAuth == null) {
            return ResultUtil.error("店铺不存在");
        }
        List<String> relationIds = getRelationIds(idsList, makeAdTagVo);
        if (CollectionUtils.isEmpty(relationIds)) {
            return ResultUtil.error("标记对象不存在");
        }
        //单店铺添加标签
        return this.addAdTag(puid, uid, shopAuth, relationIds, makeAdTagVo);
    }

    /**
     * 多店铺添加标签
     * @param puid
     * @param uid
     * @param makeAdTagBatchVo
     * @return
     */
    @Override
    public Result<List<AdMarkupTagVo>> makeAdTagMultiShop(int puid, int uid, MakeAdTagBatchVo makeAdTagBatchVo) {
        AdTag adTag = adTagDao.getByPuidAndId(puid, makeAdTagBatchVo.getAdTagId());
        if (adTag == null) {
            return ResultUtil.error("标签不存在");
        }
        List<MakeAdTagBatchVo.MakeAdTagVo> vos = makeAdTagBatchVo.getVos();
        if (CollectionUtils.isEmpty(vos)) {
            return ResultUtil.error("请求参数错误");
        }
        if (vos.size() > 200) {
            return ResultUtil.error("最多一次标记200条");
        }
        Map<Integer, List<MakeAdTagBatchVo.MakeAdTagVo>> voMap = vos.stream().collect(Collectors.groupingBy(MakeAdTagBatchVo.MakeAdTagVo::getShopId));
        List<Integer> shopIdList = new ArrayList<>(voMap.keySet());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, shopIdList);
        if (shopIdList.size() != shopAuthList.size()) {
            return ResultUtil.error("店铺不存在，请刷新页面重试");
        }
        Map<Integer, ShopAuth> shopAuthMap = shopAuthList.stream().collect(Collectors.toMap(ShopAuth::getId, Function.identity()));

        MakeAdTagVo makeAdTagVo = new MakeAdTagVo();
        makeAdTagVo.setAdTagId(makeAdTagBatchVo.getAdTagId());
        makeAdTagVo.setPuid(makeAdTagBatchVo.getPuid());
        makeAdTagVo.setUid(makeAdTagBatchVo.getUid());
        makeAdTagVo.setLoginIp(makeAdTagBatchVo.getLoginIp());
        makeAdTagVo.setType(makeAdTagBatchVo.getType());
        makeAdTagVo.setAdType(makeAdTagBatchVo.getAdType());
        makeAdTagVo.setTargetType(makeAdTagBatchVo.getTargetType());

        //校验标记对象
        Map<Integer, List<String>> shopRelationIdsMap = new HashMap<>();
        for (Map.Entry<Integer, List<MakeAdTagBatchVo.MakeAdTagVo>> entry : voMap.entrySet()) {
            makeAdTagVo.setShopId(entry.getKey());
            List<String> idList = entry.getValue().stream().map(MakeAdTagBatchVo.MakeAdTagVo::getId).collect(Collectors.toList());
            List<String> relationIds = getRelationIds(idList, makeAdTagVo);
            if (CollectionUtils.isEmpty(relationIds)) {
                return ResultUtil.error("标记对象不存在");
            }
            shopRelationIdsMap.put(entry.getKey(), relationIds);
        }

        //批量修改标签
        List<AdMarkupTagVo> adMarkupTagVoList = new ArrayList<>();
        Result<List<AdMarkupTagVo>> result;
        for (Map.Entry<Integer, List<MakeAdTagBatchVo.MakeAdTagVo>> entry : voMap.entrySet()) {
            makeAdTagVo.setShopId(entry.getKey());
            makeAdTagVo.setIds(entry.getValue().stream().map(MakeAdTagBatchVo.MakeAdTagVo::getId).collect(Collectors.joining(",")));
            result = this.addAdTag(puid, uid, shopAuthMap.get(entry.getKey()), shopRelationIdsMap.get(entry.getKey()), makeAdTagVo);
            adMarkupTagVoList.addAll(result.getData());
        }

        return ResultUtil.success(adMarkupTagVoList);
    }


    @Override
    public Result clearMakeAdTag(int puid, int uid, String loginIp, MakeAdTagVo makeAdTagVo) {

        List<String> idsList = makeAdTagVo.getIdsList();
        if(idsList == null){
            return ResultUtil.error("请求参数错误");
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(makeAdTagVo.getShopId());
        if(shopAuth == null){
            return ResultUtil.error("店铺不存在");
        }
        adMarkupTagDao.deleteByRelationId(puid,shopAuth.getId(),makeAdTagVo.getType(), makeAdTagVo.getAdType(), makeAdTagVo.getTargetType(),makeAdTagVo.getIdsList(), makeAdTagVo.getAdTagId());
        return ResultUtil.success();
    }

    @Override
    public Result<List<AdMarkupTagVo>> clearMakeAdTagMultiShop(int puid, int uid, MakeAdTagBatchVo makeAdTagBatchVo) {

        List<MakeAdTagBatchVo.MakeAdTagVo> vos = makeAdTagBatchVo.getVos();
        if (CollectionUtils.isEmpty(vos)) {
            return ResultUtil.error("请求参数错误");
        }
        if (vos.size() > 200) {
            return ResultUtil.error("最多一次标记200条");
        }
        Map<Integer, List<String>> voMap = vos.stream().collect(Collectors.groupingBy(MakeAdTagBatchVo.MakeAdTagVo::getShopId,
                Collectors.mapping(MakeAdTagBatchVo.MakeAdTagVo::getId, Collectors.toList())));

        List<Integer> shopIdList = new ArrayList<>(voMap.keySet());
        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, shopIdList);
        if (shopIdList.size() != shopAuthList.size()) {
            return ResultUtil.error("店铺不存在，请刷新页面重试");
        }

        for (Map.Entry<Integer, List<String>> entry : voMap.entrySet()) {
            adMarkupTagDao.deleteByRelationId(puid, entry.getKey(), makeAdTagBatchVo.getType(), makeAdTagBatchVo.getAdType(), makeAdTagBatchVo.getTargetType(), entry.getValue(), makeAdTagBatchVo.getAdTagId());
        }
        return ResultUtil.success();
    }

    /**
     * 单店铺添加标签
     */
    private Result<List<AdMarkupTagVo>> addAdTag(int puid, int uid, ShopAuth shopAuth, List<String> relationIds,MakeAdTagVo makeAdTagVo) {
        List<String> idsList = makeAdTagVo.getIdsList();
        List<AdMarkupTagVo> errors = new ArrayList<>();
        List<AdMarkupTag> insList = new ArrayList<>();
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, makeAdTagVo.getShopId(),
                makeAdTagVo.getType(),
                makeAdTagVo.getAdType(),
                makeAdTagVo.getTargetType(), null,
                idsList);
        Map<String, AdMarkupTagVo> markupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e));
        Set<String> idsSet = new HashSet<>(relationIds);
        for (String relationId : idsList) {
            AdMarkupTagVo adMarkupTagVo = markupTagVoMap.get(relationId);
            if (adMarkupTagVo != null && adMarkupTagVo.getTagIds().size() >= 5) {
                adMarkupTagVo.setError("标签数量不能大于5个");
                errors.add(adMarkupTagVo);
                return ResultUtil.success(errors);
            }
            if (!idsSet.contains(relationId)) {
                AdMarkupTagVo error = new AdMarkupTagVo();
                error.setRelationId(relationId);
                error.setError("标记对象不存在");
                errors.add(error);
                return ResultUtil.success(errors);
            }
            AdMarkupTag in = new AdMarkupTag();
            in.setPuid(puid);
            in.setShopId(makeAdTagVo.getShopId());
            in.setType(makeAdTagVo.getType());
            in.setTargetType(makeAdTagVo.getTargetType());
            in.setAdType(makeAdTagVo.getAdType());
            in.setMarketplaceId(shopAuth.getMarketplaceId());
            in.setCreateId(uid);
            in.setUpdateId(uid);
            in.setTagId(makeAdTagVo.getAdTagId());
            in.setRelationId(relationId);

            insList.add(in);
        }
        if (CollectionUtils.isEmpty(insList)) {
            return ResultUtil.success(errors);
        }
        adMarkupTagDao.insertList(insList);
        return ResultUtil.success(errors);
    }

    /**
     * 查询关联id是否存在
     * @param idsList
     * @param makeAdTagVo
     * @return
     */
    private List<String> getRelationIds(List<String> idsList,MakeAdTagVo makeAdTagVo){

        List<String> relationIds = new ArrayList<>();
        if(AdTagTypeEnum.TARGET.getType().equals(makeAdTagVo.getType())){
            if(AdMarkupTargetTypeEnum.TARGET.getType().equals(makeAdTagVo.getTargetType())){
                if(CampaignTypeEnum.sp.getCampaignType().equals(makeAdTagVo.getAdType())){
                    relationIds = amazonAdTargetDaoRoutingService.getTargetIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
                } else if(CampaignTypeEnum.sd.getCampaignType().equals(makeAdTagVo.getAdType())) {
                    relationIds = sdAdTargetingDao.getTargetIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
                } else if(CampaignTypeEnum.sb.getCampaignType().equals(makeAdTagVo.getAdType())){
                    relationIds = sbAdTargetingDao.getTargetIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
                }
            } else if(AdMarkupTargetTypeEnum.KEYWORD.getType().equals(makeAdTagVo.getTargetType())){
                if(CampaignTypeEnum.sp.getCampaignType().equals(makeAdTagVo.getAdType())){
                    relationIds = amazonAdKeywordDaoRoutingService.getKeywordIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
                } else if(CampaignTypeEnum.sb.getCampaignType().equals(makeAdTagVo.getAdType())){
                    relationIds = sbKeywordDao.getKeywordIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
                }
            }
        } else if(AdTagTypeEnum.PRODUCT.getType().equals(makeAdTagVo.getType())){
            if(CampaignTypeEnum.sp.getCampaignType().equals(makeAdTagVo.getAdType())){
                relationIds = spAdProductDao.getAdIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
            } else if(CampaignTypeEnum.sd.getCampaignType().equals(makeAdTagVo.getAdType())) {
                relationIds = sdAdProductDao.getAdIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
            }
        } else if (AdTagTypeEnum.GROUP.getType().equals(makeAdTagVo.getType())){
            if(CampaignTypeEnum.sp.getCampaignType().equals(makeAdTagVo.getAdType())){
                relationIds = spGroupDao.getAdGroupIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
            } else if(CampaignTypeEnum.sd.getCampaignType().equals(makeAdTagVo.getAdType())) {
                relationIds = sdGroupDao.getAdGroupIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
            } else if(CampaignTypeEnum.sb.getCampaignType().equals(makeAdTagVo.getAdType())) {
                relationIds = sbAdGroupDao.getAdGroupIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), null, idsList);
            }
        } else if (AdTagTypeEnum.CAMPAIGN.getType().equals(makeAdTagVo.getType())) {
            relationIds = amazonAdCampaignAllDao.getCampaignIds(makeAdTagVo.getPuid(), makeAdTagVo.getShopId(), makeAdTagVo.getAdType(), idsList);
        }
        return relationIds;
    }


    /**
     * 初始化标签数据
     * @param puid
     * @param shopId
     */
    @Override
    public void initializeAdTag(Integer puid, Integer shopId) {
        long startTime = System.currentTimeMillis();
        int start = 0;
        int limit = 100;
        List<ShopAuth> shopAuths;
        while (true) {
            shopAuths = shopAuthDao.getAllValidAdShopByLimit(puid, shopId, start, limit);
            int size = shopAuths.size();
            for (ShopAuth shopAuth : shopAuths) {
                try {

                    //                  * 广告组
                    //                     * color: yellow
                    //                     * name: 特别关注
                    //                     * type: group
                    AdTag adTag = null;
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("yellow");
                        adTag.setName("特别关注");
                        adTag.setType("group");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {

                    }
                    // * 广告组
                    //                     *  color: blue
                    //                     * name: 重点
                    //                     * type: group
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("blue");
                        adTag.setName("重点");
                        adTag.setType("group");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //* 广告产品
                    //                     * color: green
                    //                     * name: 爆款产品
                    //                     * type: product
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("green");
                        adTag.setName("爆款产品");
                        adTag.setType("product");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }

                    //          * 广告产品
                    //                     * color: purple
                    //                     * name: 引流产品
                    //                     * type: product

                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("purple");
                        adTag.setName("引流产品");
                        adTag.setType("product");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //  * 广告产品
                    //                     * color: lightBlue
                    //                     * name: 战略产品
                    //                     * type: product
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("lightBlue");
                        adTag.setName("战略产品");
                        adTag.setType("product");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    // * 广告产品
                    //                     * color: red
                    //                     * name: 普通产品
                    //                     * type: product
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("red");
                        adTag.setName("普通产品");
                        adTag.setType("product");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //                     * 投放
                    //                     * color: cyan
                    //                     * name: 稳定阶段
                    //                     * type: target
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("cyan");
                        adTag.setName("稳定阶段");
                        adTag.setType("target");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    //                     * 投放
                    //                     * color: grey
                    //                     * name: 测试阶段
                    //                     * type: target
                    try {
                        adTag = new AdTag();
                        adTag.setPuid(shopAuth.getPuid());
                        adTag.setCreateId(shopAuth.getPuid());
                        adTag.setColor("grey");
                        adTag.setName("测试阶段");
                        adTag.setType("target");
                        adTagDao.insert(adTag);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } catch (Exception e) {
                    log.error("初始化标签数据错误:", e);
                }
            }

            if (size < limit) {
                break;
            }

            start += size;
        }
        log.info("初始化标签数据 time:{}",System.currentTimeMillis() - startTime);

    }

}
