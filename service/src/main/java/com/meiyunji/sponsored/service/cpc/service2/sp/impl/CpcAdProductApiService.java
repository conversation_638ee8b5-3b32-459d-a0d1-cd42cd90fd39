package com.meiyunji.sponsored.service.cpc.service2.sp.impl;


import com.amazon.advertising.spV3.enumeration.SpV3State;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.product.ListSpProductV3Response;
import com.amazon.advertising.spV3.product.ProductSpV3Client;
import com.amazon.advertising.spV3.product.UpdateSpProductV3Response;
import com.amazon.advertising.spV3.product.entity.ProductEntityV3;
import com.amazon.advertising.spV3.product.entity.ProductExtendEntityV3;
import com.amazon.advertising.spV3.product.entity.ProductSuccessResultV3;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.dto.AmazonServingStatusDto;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/15.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcAdProductApiService {

    @Value("${blacklist.shopIds}")
    private String blackShopIds;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IDorisService dorisService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public void syncAds(ShopAuth shop, String campaignId, String groupId, String adId){
        this.syncAds(shop, campaignId, groupId, adId, false);
    }

    public void syncAds(ShopAuth shop, String campaignId, String groupId, String adId, boolean excepThrow) {
        syncAds(shop, campaignId, groupId, adId, null, excepThrow);
    }

    public void syncAds(ShopAuth shop, String campaignId, String groupId, String adId, List<SpV3State> stateList, boolean excepThrow) {
        syncAds(shop, campaignId, groupId, adId, null, excepThrow, false);
    }

    /**
     * 同步所有的关键词
     *
     * @param shop：
     */
    public void syncAds(ShopAuth shop, String campaignId, String groupId, String adId, List<SpV3State> stateList, boolean excepThrow, boolean isProxy) {
        if (shop == null) {
            return;
        }
        if (StringUtils.isNotBlank(blackShopIds)) {
            List<String> blackList = Arrays.asList(blackShopIds.split(","));
            if (blackList.contains(String.valueOf(shop.getId()))) {
                return;
            }
        }
        if (StringUtils.isBlank(shop.getAdRefreshToken())) {
            log.error("syncAdGroups:店铺{}没有授权广告", shop.getId());
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncAdGroups--配置信息为空");
            return;
        }
        List<String> campaignIdList = null;
        if(StringUtils.isNotEmpty(campaignId)){
            campaignIdList =  StringUtil.splitStr(campaignId,",");
        }
        List<String> groupIdList =  null;
        if(StringUtils.isNotBlank(groupId)){
            groupIdList = StringUtil.splitStr(groupId,",");
        }
        List<String> adIdList = null;
        if(StringUtils.isNotEmpty(adId)){
            adIdList =  StringUtil.splitStr(adId,",");
        }
        String nextToken = null;
        int count = 5000;
        ListSpProductV3Response response;
        ProductSpV3Client client = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = ProductSpV3Client.getInstance(true);
        }
        boolean refreshedToken = false; // 是否刷新了token，保证只刷新一次
        Set<String> campaignIdSet = new HashSet<>();
        //没有指定id同步才会同步到sd广告产品，查出sd 广告活动id；
        if(CollectionUtils.isEmpty(campaignIdList) && CollectionUtils.isEmpty(groupIdList) && CollectionUtils.isEmpty(adIdList)){
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIds(shop.getPuid(), shop.getId(), CampaignTypeEnum.sd.getCampaignType());
            if(CollectionUtils.isNotEmpty(campaignIds)){
                campaignIdSet.addAll(campaignIds);
            }
        }

        while (true) {
            response = client.listProduct(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    campaignIdList,groupIdList,adIdList,stateList,true, nextToken, count);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SP adProduct rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listProduct(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        campaignIdList,groupIdList,adIdList,stateList,true, nextToken, count);
                retry++;
            }
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401
                    && !refreshedToken) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken = true;
                continue;
            }
            if (AmazonResponseUtil.isError(response) && excepThrow) {
                throw new ServiceException("sp ad api call error, please try again");
            }
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getProductAds())) {
                break;
            }

            int size = response.getData().getProductAds().size();
            AmazonAdProduct amazonAdProduct;
            List<AmazonAdProduct> amazonAdProducts = new ArrayList<>(size);
            for (ProductExtendEntityV3 productAd : response.getData().getProductAds()) {
                //如果包含sd广告活动数据直接丢弃不处理
                if(campaignIdSet.contains(productAd.getCampaignId())){
                    continue;
                }
                amazonAdProduct = turnToPO(productAd);
                if (StringUtils.isNotBlank(amazonAdProduct.getAdId())) {
                    amazonAdProduct.setPuid(shop.getPuid());
                    amazonAdProduct.setShopId(shop.getId());
                    amazonAdProduct.setMarketplaceId(shop.getMarketplaceId());
                    amazonAdProduct.setProfileId(amazonAdProfile.getProfileId());
                    amazonAdProducts.add(amazonAdProduct);
                }
            }
            amazonAdProductDao.insertOnDuplicateKeyUpdate(shop.getPuid(), amazonAdProducts);

            if (StringUtils.isNotBlank(response.getData().getNextToken())) {
                nextToken = response.getData().getNextToken();
            }  else {
                break;
            }
        }
    }

    public List<AmazonServingStatusDto> listByIds(Integer puid, Integer shopId, String ids){
        List<AmazonAdProduct> amazons = amazonAdProductDao.getByAdIds(puid, shopId, StringUtil.stringToList(ids, ","));
        amazons.forEach(i -> {
            if (Objects.nonNull(i)) {
                if (StringUtils.isNotBlank(i.getState())) {
                    i.setState(i.getState().toLowerCase());
                }
            }
        });
        dorisService.saveDorisByRoutineLoad4MysqlDto(amazons);
        return CollectionUtils.isEmpty(amazons) ? new ArrayList<>() : amazons.stream().map(key -> {
                    key.setServingStatus(key.getServingStatus());
                    return AmazonServingStatusDto.build(key.getAdId(), key.getServingStatus(), key.getServingStatusName(), key.getServingStatusDec());
                }
        ).collect(Collectors.toList());
    }

    /**
     * 只用于开启暂停 归档勿用
     * @param amazonAdProducts
     * @return
     */
    public Result<List<AmazonAdProduct>> update(ArrayList<AmazonAdProduct> amazonAdProducts) {
        if (CollectionUtils.isEmpty(amazonAdProducts)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdProduct one = amazonAdProducts.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }
        List<ProductEntityV3> productAds = makeAds(amazonAdProducts);
        UpdateSpProductV3Response response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putProdutAds(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), productAds,false);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putProdutAds(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), productAds,false);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null && response.getData().getProductAds() != null) {
            StringBuilder error = new StringBuilder();
            ApiResponseV3<ProductSuccessResultV3> productadsSuccess = response.getData().getProductAds();
            List<ProductSuccessResultV3> successGroups = productadsSuccess.getSuccess();
            List<ErrorItemResultV3> errorGroups = productadsSuccess.getError();

            int index = 0;
            List<AmazonAdProduct> succList = new ArrayList<>();
            for (ProductSuccessResultV3 groupSuccessResultV3 : successGroups){
                succList.add(amazonAdProducts.get(groupSuccessResultV3.getIndex()));
            }
            for (ErrorItemResultV3 errorItemResultV3 : errorGroups){
                error.append("targetValue:").append(amazonAdProducts.get(errorItemResultV3.getIndex()).getAsin()).append(",desc:").append(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage())).append(";");
            }
           
            if (succList.size() > 0) {
                Result<List<AmazonAdProduct>> result = ResultUtil.returnSucc(succList);
                result.setMsg(error.toString());
                return result;
            }

            return ResultUtil.returnErr(error.toString());
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }
        return ResultUtil.returnErr(msg);
    }

    /**
     *
     * 仅限于归档操作
     * @param amazonAdProduct：
     * @return ：Result
     */
    Result archive(AmazonAdProduct amazonAdProduct) {
        if (amazonAdProduct == null) {
            return ResultUtil.error("请求参数错误");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdProduct.getShopId(), amazonAdProduct.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        UpdateSpProductV3Response response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delProduct(shopAuthService.getAdToken(shop),
                amazonAdProduct.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdProduct.getAdId()),true);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delProduct(shopAuthService.getAdToken(shop),
                    amazonAdProduct.getProfileId(), shop.getMarketplaceId(), Lists.newArrayList(amazonAdProduct.getAdId()),true);
        }
        if (response == null) { return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getData() != null && response.getData().getProductAds() != null && CollectionUtils.isNotEmpty(response.getData().getProductAds().getSuccess())) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getData() != null && response.getData().getProductAds() != null && CollectionUtils.isNotEmpty(response.getData().getProductAds().getError()) && StringUtils.isNotBlank(response.getData().getProductAds().getError().get(0).getErrors().get(0).getErrorMessage())) {
            msg = response.getData().getProductAds().getError().get(0).getErrors().get(0).getErrorMessage();
        }
        if (response.getError() != null) {
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }

        }
        return ResultUtil.error(msg);
    }

    private List<ProductEntityV3> makeAds(List<AmazonAdProduct> amazonAdProducts) {
        List<ProductEntityV3> adProducts = Lists.newArrayList();
        ProductEntityV3 productAd;
        for (AmazonAdProduct amazonAdProduct : amazonAdProducts) {
            productAd = new ProductEntityV3();
            productAd.setState(amazonAdProduct.getState().toUpperCase());
            productAd.setAdId(amazonAdProduct.getAdId());
            adProducts.add(productAd);
        }

        return adProducts;
    }

    private AmazonAdProduct turnToPO(ProductExtendEntityV3 productAd) {
        AmazonAdProduct amazonAdProduct = new AmazonAdProduct();
        if (productAd.getCampaignId() != null) {
            amazonAdProduct.setCampaignId(productAd.getCampaignId().toString());
        }
        if (productAd.getAdGroupId() != null) {
            amazonAdProduct.setAdGroupId(productAd.getAdGroupId().toString());
        }
        if (productAd.getAdId() != null) {
            amazonAdProduct.setAdId(productAd.getAdId().toString());
        }
        amazonAdProduct.setSku(productAd.getSku());
        amazonAdProduct.setAsin(productAd.getAsin());
        amazonAdProduct.setState(productAd.getState().toLowerCase());
        amazonAdProduct.setServingStatus(productAd.getExtendedData().getServingStatus());
        return amazonAdProduct;
    }

    public Result<BatchResponseVo<AmazonAdProduct,AmazonAdProduct>> updateReturnErrorList(List<AmazonAdProduct> amazonAdProducts) {
        if (CollectionUtils.isEmpty(amazonAdProducts)) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdProduct one = amazonAdProducts.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(one.getShopId(), one.getPuid());
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }
        List<String> collect = amazonAdProducts.stream().map(AmazonAdProduct::getState).distinct().collect(Collectors.toList());
        if(collect.size() >1 ){
            return ResultUtil.returnErr("调整状态错误！");
        }
        String state = collect.get(0);

        boolean isArchived = SpV3StateEnum.ARCHIVED.value().equalsIgnoreCase(state);

        List<ProductEntityV3> productAds = makeAds(amazonAdProducts);
        UpdateSpProductV3Response response = null;
        List<String> adIds = productAds.stream().map(ProductEntityV3::getAdId).collect(Collectors.toList());
        if (isArchived) {
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delProduct(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), adIds,false);
        } else {
            response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putProdutAds(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), productAds,false);
        }
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            if (isArchived) {
                response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delProduct(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), adIds,false);
            } else {
                response = ProductSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putProdutAds(shopAuthService.getAdToken(shop),
                        one.getProfileId(), one.getMarketplaceId(), productAds,false);
            }
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        Map<String, AmazonAdProduct> amazonAdProductMap = amazonAdProducts.stream().collect(Collectors.toMap(AmazonAdProduct::getAdId, e -> e));
        BatchResponseVo<AmazonAdProduct, AmazonAdProduct> batchResponseVo = new BatchResponseVo<AmazonAdProduct, AmazonAdProduct>();
        List<AmazonAdProduct> errorList = Lists.newArrayList();
        List<AmazonAdProduct> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getData() != null && response.getData().getProductAds() != null) {
            List<ProductSuccessResultV3> successResultV3s = response.getData().getProductAds().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getProductAds().getError();
            List<Long> successId = Lists.newArrayList();
            for (ProductSuccessResultV3 successResultV3 : successResultV3s){
                AmazonAdProduct amazonAdProductSuccess = amazonAdProductMap.remove(successResultV3.getAdId());
                if (amazonAdProductSuccess != null) {
                    successList.add(amazonAdProductSuccess);
                    successId.add(amazonAdProductSuccess.getId());
                }

            }

            for(ErrorItemResultV3 errorItemResultV3: errorItemResultV3s){
                ProductEntityV3 productEntityV3 = productAds.get(errorItemResultV3.getIndex());
                AmazonAdProduct amazonAdProductFail = amazonAdProductMap.remove(String.valueOf(productEntityV3.getAdId()));
                if (amazonAdProductFail != null) {
                    AmazonAdProduct amazonAdProductError = new AmazonAdProduct();
                    BeanUtils.copyProperties(amazonAdProductFail,amazonAdProductError);
                    //更新失败数据处理
                    if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors()) &&  StringUtils.isNotBlank(errorItemResultV3.getErrors().get(0).getErrorMessage())) {
                        amazonAdProductError.setFailReason(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    } else {
                        amazonAdProductError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(amazonAdProductError);
                }
            }


            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdProductMap)) {
                amazonAdProductMap.forEach((k, v) -> {
                    AmazonAdProduct amazonAdProductError = new AmazonAdProduct();
                    BeanUtils.copyProperties(v,amazonAdProductError);
                    amazonAdProductError.setFailReason("更新失败，请稍后重试");
                    errorList.add(amazonAdProductError);
                });
            }

        } else if (response.getError() != null ) {
            String msg = "网络异常，请稍后再试";
            if(StringUtils.isNotBlank(response.getError().getMessage())){
                msg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if(CollectionUtils.isNotEmpty(response.getError().getErrors())){
                msg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            return ResultUtil.error(msg);
        }
        batchResponseVo.setCountNum(amazonAdProducts.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }
}
