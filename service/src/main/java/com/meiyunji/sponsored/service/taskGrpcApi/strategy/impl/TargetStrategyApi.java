package com.meiyunji.sponsored.service.taskGrpcApi.strategy.impl;


import com.meiyunji.sellfox.aadas.api.enumeration.AmazonAdvertiseTypePb;
import com.meiyunji.sellfox.aadas.api.enumeration.ScheduleModePb;
import com.meiyunji.sellfox.aadas.api.service.AadasApiGrpc;
import com.meiyunji.sellfox.aadas.api.service.RemoveTargetBidTaskTimeScheduleRequestPb;
import com.meiyunji.sellfox.aadas.api.service.SetTargetBidStrategyScheduleRequestPb;
import com.meiyunji.sellfox.aadas.types.enumeration.Marketplace;
import com.meiyunji.sellfox.aadas.types.enumeration.TaskTimeType;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.impl.ShopAuthServiceImpl;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategySchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTemplate;
import com.meiyunji.sponsored.service.taskGrpcApi.AbstractAdvertiseStrategyApi;
import com.meiyunji.sponsored.service.util.GrpcExceptionUtil;
import com.meiyunji.sponsored.service.util.PbUtil;
import com.meiyunji.sponsored.service.util.ProtoBufUtil;
import io.grpc.ManagedChannel;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pulsar.client.api.Producer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class TargetStrategyApi extends AbstractAdvertiseStrategyApi {

    @Autowired
    private AdvertiseStrategyTemplateDao advertiseStrategyTemplateDao;

    protected TargetStrategyApi(ShopAuthServiceImpl shopAuthService, ManagedChannel taskManagedChannel, Producer<byte[]> compensationTaskProducer, DynamicRefreshConfiguration dynamicRefreshConfiguration) {
        super(shopAuthService, taskManagedChannel, compensationTaskProducer, dynamicRefreshConfiguration);
    }

    @Override
    public boolean checkValid(TaskTimeType taskType) {
        return taskType == TaskTimeType.targetBid;
    }

    @Override
    public void setSchedule(Long taskId, Long templateId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        SetTargetBidStrategyScheduleRequestPb.SetTargetBidStrategyScheduleRequest request = builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId);
        try {
            log.info("新版广告策略-商品投放竞价请求参数: {}", request);
            AadasApiGrpc.AadasApiBlockingStub aadasApiBlockingStub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
            aadasApiBlockingStub.setTargetBidStrategySchedule(request);
        } catch (StatusRuntimeException e) {
            throw GrpcExceptionUtil.unWrapException(e);
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }

    @Override
    public void removeSchedule(Integer puid, Integer shopId, Long taskId, boolean triggerNow) throws Exception {
        if (triggerNow) {
            AdvertiseStrategyTemplate template = advertiseStrategyTemplateDao.getTemplateByTaskId(puid, shopId, taskId);
            if (Objects.isNull(template) || AdStrategyEnableStatusEnum.DISABLED.getCode().equals(template.getStatus())) {
                triggerNow = false;
                log.info("分时策略拦截回调, puid: {}, shopId: {}, taskId: {}", puid, shopId, taskId);
            }
        }
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(shopId, puid);
        try {
            RemoveTargetBidTaskTimeScheduleRequestPb.RemoveTargetBidTaskTimeScheduleRequest.Builder builder =
                    RemoveTargetBidTaskTimeScheduleRequestPb.RemoveTargetBidTaskTimeScheduleRequest.newBuilder();
            builder.setSellerId(shopAuth.getSellingPartnerId());
            builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
            builder.setTaskId(taskId);
            builder.setTriggerNow(triggerNow);
            AadasApiGrpc.AadasApiBlockingStub aadasApiBlockingStub = AadasApiGrpc.newBlockingStub(taskManagedChannel);
            aadasApiBlockingStub.removeTargetBidTaskTimeSchedule(builder.build());
        } finally {
            sendCompensationMessage(taskId, getTaskTimeType(), shopAuth, 5);
        }
    }

    @Override
    public String builderScheduleRequestJson(Long taskId, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId) throws Exception {
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        ShopAuth shopAuth = shopAuthService.getByIdAndPuid(schedule.getShopId(), schedule.getPuid());
        return ProtoBufUtil.toJsonStr(builderRequest(taskId, shopAuth, strategySchedule, triggerNow, templateId));
    }

    @Override
    public TaskTimeType getTaskTimeType() {
        return TaskTimeType.targetBid;
    }


    public SetTargetBidStrategyScheduleRequestPb.SetTargetBidStrategyScheduleRequest builderRequest(Long taskId, ShopAuth shopAuth, List<AdvertiseStrategySchedule> strategySchedule, boolean triggerNow, Long templateId){
        AdvertiseStrategySchedule schedule = strategySchedule.get(0);
        String adType = schedule.getAdType();
        ScheduleModePb.ScheduleMode scheduleMode = schedule.getDay() == 0 ? ScheduleModePb.ScheduleMode.Daily :
                ScheduleModePb.ScheduleMode.Weekly;
        SetTargetBidStrategyScheduleRequestPb.SetTargetBidStrategyScheduleRequest.Builder builder =
                SetTargetBidStrategyScheduleRequestPb.SetTargetBidStrategyScheduleRequest.newBuilder();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplace(PbUtil.toPb(Marketplace.fromId(shopAuth.getMarketplaceId())));
        builder.setAdType(AmazonAdvertiseTypePb.AmazonAdvertiseType.valueOf(adType));
        builder.setTaskId(taskId);
        builder.setTriggerNow(triggerNow);
        builder.setSchedulerMode(scheduleMode);
        if (StringUtils.isNotBlank(schedule.getCampaignId())) {
            builder.setCampaignId(schedule.getCampaignId());
        }
        if (StringUtils.isNotBlank(schedule.getAdGroupId())) {
            builder.setAdGroupId(schedule.getAdGroupId());
        }
        builder.setTemplateId(templateId);
        builder.setTargetId(schedule.getItemId());
        builder.setOriginValue(String.valueOf(schedule.getOriginBiddingValue()));
        builder.addAllItem(PbUtil.toTargetItem(strategySchedule));
        SetTargetBidStrategyScheduleRequestPb.SetTargetBidStrategyScheduleRequest request = builder.build();
        return request;
    }
}
