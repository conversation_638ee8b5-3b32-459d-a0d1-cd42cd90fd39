package com.meiyunji.sponsored.service.batchCreate.task;

import com.amazon.advertising.spV3.enumeration.SpV3NegativeMatchTypeEnum;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.negativekeyword.CreateSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.ListSpNegativeKeywordV3Response;
import com.amazon.advertising.spV3.negativekeyword.NegativeKeywordSpV3Client;
import com.amazon.advertising.spV3.negativekeyword.entity.CreateNegativeKeywordEntityV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordExtendEntityV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.BaseErrorV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchNekeywordDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.BatchCreateReturnDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.AdAmazonErrorTypeEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchConstants;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdLevelStatusEnum;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNekeyword;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeKeyword;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 否定关键词批量处理任务
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2023-11-24  14:20
 */
@Component
@Slf4j
public class NekeywordCallAmazonApiTask extends AbstractCallAmazonApiTask {
    @Autowired
    private IAmazonAdBatchNekeywordDao amazonAdBatchNekeywordDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private TaskStatusHelper taskStatusHelper;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    public void call(CallAmazonApiTaskResultDto resultDto) {
        log.info("sp batch create, create nekeyword, batch traceId: {}, taskId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            return ;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        String traceId = resultDto.getTraceDto().getTraceId();
        //1、获取redisson分布式锁，锁标识为："sp_batch:nekeyword:"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_NEKEYWORD.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info("nekeyword task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }
        if (!b) {
            log.info("nekeyword task tryLock err,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, traceId);
            return ;
        }

        List<AmazonAdBatchNekeyword> nekeywords = new ArrayList<>();
        try {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //2、根据传入的id列表获取不为成功或者终止状态的否定关键词集合
            List<Long> ids = resultDto.getSuccessIdList();
            if (resultDto.isCurrentLevel()) {
                nekeywords = amazonAdBatchNekeywordDao.listByIdList(puid, shopId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            } else {
                nekeywords = amazonAdBatchNekeywordDao.listByGroupIdList(puid, shopId, taskId, ids,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            }
            if (CollectionUtils.isEmpty(nekeywords)) {
                return ;
            }
            //3、分片请求亚马逊返回列表分成成功失败可重试三种类型
            Map<Integer, String> succMap = new HashMap<>();
            Map<Integer, String> errMap = new HashMap<>();
            List<Integer> retryList = new ArrayList<>();
            List<List<AmazonAdBatchNekeyword>> nekeywordsPartition = Lists.partition(nekeywords, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (int i = 0; i < nekeywordsPartition.size(); i++) {
                BatchCreateReturnDto batchCreateReturnDto = this.amazonApiCreateByPartition(traceId, taskId, shop, nekeywordsPartition.get(i), i * SpBatchConstants.REQUEST_PARTITION_SIZE);
                succMap.putAll(batchCreateReturnDto.getSuccMap());
                errMap.putAll(batchCreateReturnDto.getErrMap());
                retryList.addAll(batchCreateReturnDto.getRetryList());
            }

            //4、成功处理
            if (succMap.size() > 0) {
                List<AmazonAdKeyword> succNekeywords = new ArrayList<>();
                Map<Long, String> succBatchKeywordIdMap = new HashMap<>();
                for (Map.Entry<Integer, String> succMapEntry : succMap.entrySet()) {
                    AmazonAdBatchNekeyword amazonAdBatchNekeyword = nekeywords.get(succMapEntry.getKey());
                    succBatchKeywordIdMap.put(amazonAdBatchNekeyword.getId(), succMapEntry.getValue());
                    //构建日志的po
                    AmazonAdKeyword amazonAdKeyword = this.batchNeKeyword2NeKeyword(amazonAdBatchNekeyword);
                    amazonAdKeyword.setKeywordId(succMapEntry.getValue());
                    succNekeywords.add(amazonAdKeyword);
                }
                //插入成功的否定关键词
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(puid, succNekeywords, Constants.NEGATIVE);
                //修改当前层级任务的状态为成功
                amazonAdBatchNekeywordDao.updateSuccTaskStatusByIdList(puid, succBatchKeywordIdMap);
                //记录成功日志
                this.collectSuccLog(succNekeywords, resultDto.getLoginIp());
                log.info("sp batch create, create nekeyword success, batch traceId: {}, taskId: {}, succBatchProductIdMap: {}", traceId, resultDto.getTaskId(), succBatchKeywordIdMap);
            }

            //5、失败处理
            List<AmazonAdKeyword> errNekeywords = new ArrayList<>();
            if (errMap.size() > 0) {
                Map<Long, String> idMsgMap = new HashMap<>();
                for (Map.Entry<Integer, String> errMapEntry : errMap.entrySet()) {
                    AmazonAdBatchNekeyword amazonAdBatchNekeyword = nekeywords.get(errMapEntry.getKey());
                    idMsgMap.put(amazonAdBatchNekeyword.getId(), errMapEntry.getValue());
                    //构建日志po
                    AmazonAdKeyword amazonAdKeyword = this.batchNeKeyword2NeKeyword(amazonAdBatchNekeyword);
                    amazonAdKeyword.setError(errMapEntry.getValue());
                    errNekeywords.add(amazonAdKeyword);
                }
                //修改当前层级任务状态为失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_NEKEYWORD,  null, true);
                log.info("sp batch create, create nekeyword error, batch traceId: {}, taskId: {}, idMsgMap: {}", traceId, resultDto.getTaskId(), idMsgMap);
            }

            //6、重试处理
            if (retryList.size() > 0) {
                List<Long> retryIdList = new ArrayList<>();
                Map<Long, String> errIdMap = new HashMap<>();
                for (Integer index : retryList) {
                    AmazonAdBatchNekeyword amazonAdBatchNekeyword = nekeywords.get(index);
                    AmazonAdKeyword amazonAdKeyword = this.batchNeKeyword2NeKeyword(amazonAdBatchNekeyword);
                    //若重试超过三次则直接置为失败
                    if (amazonAdBatchNekeyword.getExecuteCount() + 1 >= SpBatchConstants.EXECUTE_COUNT_LIMIT) {
                        errIdMap.put(amazonAdBatchNekeyword.getId(), SpBatchConstants.RETRY_ERROR_MSG);
                        amazonAdKeyword.setError(SpBatchConstants.RETRY_ERROR_MSG);
                    } else {
                        retryIdList.add(amazonAdBatchNekeyword.getId());
                        amazonAdKeyword.setError(SpBatchConstants.RETRY_MSG);
                    }
                    errNekeywords.add(amazonAdKeyword);
                }
                if (CollectionUtils.isNotEmpty(retryIdList)) {
                    amazonAdBatchNekeywordDao.updateRetryTaskStatusByIdList(puid, retryIdList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
                    log.info("sp batch create, create nekeyword retry, batch traceId: {}, taskId: {}, retryIdList: {}", traceId, resultDto.getTaskId(), StringUtils.join(retryIdList, ","));
                }
                if (!errIdMap.isEmpty()) {
                    amazonAdBatchNekeywordDao.updateErrTaskStatusByIdList(puid, errIdMap, true);
                    log.info("sp batch create, create nekeyword error, batch traceId: {}, taskId: {}, errIdMap: {}", traceId, resultDto.getTaskId(), errIdMap);
                }
            }
            //7、记录失败日志
            this.collectFailLog(errNekeywords, resultDto.getLoginIp());
        } catch (Exception e) {
            log.error(String.format("sp batch create nekeyword, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //有异常将本层级全部改为重试状态
            if (CollectionUtils.isEmpty(nekeywords)) {
                List<Long> idList = nekeywords.stream().map(AmazonAdBatchNekeyword::getId).collect(Collectors.toList());
                amazonAdBatchNekeywordDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //8、释放redission锁
            lock.unlock();
        }
    }

    /**
     * 批量调亚马逊创建接口
     * @param shop
     * @param nekeywords
     * @param offset 偏移量，返回index加上这个值
     * @return
     */
    private BatchCreateReturnDto amazonApiCreateByPartition(String traceId, Long taskId, ShopAuth shop, List<AmazonAdBatchNekeyword> nekeywords, int offset) {
        BatchCreateReturnDto dto = new BatchCreateReturnDto();
        Map<Integer, String> succMap = new HashMap<>();
        Map<Integer, String> errMap = new HashMap<>();
        List<Integer> retryList = new ArrayList<>();
        dto.setSuccMap(succMap);
        dto.setErrMap(errMap);
        dto.setRetryList(retryList);
        List<Integer> duplicateValueErrorList = new ArrayList<>();
        //组装亚马逊请求
        List<CreateNegativeKeywordEntityV3> negativeKeywordEntityV3List = Lists.newArrayList();
        for (AmazonAdBatchNekeyword amazonAdBatchKeyword : nekeywords) {
            negativeKeywordEntityV3List.add(this.batchNeKeyword2CreateNeKeywordsV3(amazonAdBatchKeyword));
        }
        //批量将这些数据提交给亚马逊。
        String profileId = nekeywords.get(0).getProfileId();
        CreateSpNegativeKeywordV3Response response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shop.getAdAccessToken(),
                profileId, shop.getMarketplaceId(), negativeKeywordEntityV3List, Boolean.TRUE);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            log.info("sp batch create nekeyword, response is null or http code not 200, batch traceId: {}, taskId: {}", traceId, taskId);
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeKeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeKeywords(shop.getAdAccessToken(),
                    profileId, shop.getMarketplaceId(), negativeKeywordEntityV3List, Boolean.TRUE);
        }

        //异常返回，全部加入到重试列表
        if (response == null || response.getData() == null || response.getStatusCode() == -1 || response.getStatusCode() == 429 || response.getStatusCode() == 500) {
            log.info("sp batch create, create nekeyword response none data, fail all, batch traceId: {}, taskId: {}", traceId, taskId);
            for (int i = 0; i < nekeywords.size(); i++) {
                retryList.add(i + offset);
            }
            return dto;
        }

        //4、活动提交给亚马逊之后，需要处理其响应，将成功的和失败的分成2组。
        String errMsg = "创建否定关键词失败";
        if (response.getData() != null && response.getData().getNegativeKeywords() != null) {
            List<NegativeKeywordSuccessResultV3> success = response.getData().getNegativeKeywords().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getNegativeKeywords().getError();
            //成功的否定关键词，批量和单个否定关键词都设置否定关键词id
            for (NegativeKeywordSuccessResultV3 negativeKeywordSuccessResultV3 : success) {
                succMap.put(offset + negativeKeywordSuccessResultV3.getIndex(), negativeKeywordSuccessResultV3.getNegativeKeywordId());
            }
            //失败的否定关键词，批量和单个否定关键词都设置错误信息
            for (ErrorItemResultV3 nekeywordResult : errorItemResultV3s) {
                //如果错误信息为创建重复，直接设置为创建成功，后续查询否定关键词id
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(nekeywordResult.getErrors().get(0).getErrorType())) {
                    duplicateValueErrorList.add(nekeywordResult.getIndex());
                    continue;
                }
                errMap.put(offset + nekeywordResult.getIndex(), nekeywordResult.getErrors().get(0).getErrorMessage());
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            for (int i = 1; i <= nekeywords.size(); i++) {
                errMap.put(offset + i, errMsg);
            }
        }

        //对创建重复的调用列表接口获取否定关键词id，若有则加入到成功列表中
        if (CollectionUtils.isNotEmpty(duplicateValueErrorList)) {
            Map<Integer, CreateNegativeKeywordEntityV3> duplicateValueErrorNekeywordMap = duplicateValueErrorList.stream().collect(Collectors.toMap(e -> e, negativeKeywordEntityV3List::get));
            Map<Integer, String> retrySuccMap = this.duplicateKeywordGetId(shop, profileId, duplicateValueErrorNekeywordMap, offset);
            succMap.putAll(retrySuccMap);
            retryList.addAll(duplicateValueErrorList.stream().filter(e -> !succMap.containsKey(e + offset)).collect(Collectors.toList()));
        }
        return dto;
    }

    /**
     * 创建重复的否定关键词获取否定关键词id，并且全部加入到创建成功列表
     */
    private Map<Integer, String> duplicateKeywordGetId(ShopAuth shop, String profileId, Map<Integer, CreateNegativeKeywordEntityV3> duplicateValueErrorNekeywordMap, int offset) {
        //获取重复成功的index和否定关键词id
        Map<Integer, String> succMap = new HashMap<>();
        //否定关键词标识(广告组+否定关键词)与index的map
        Map<String, Integer> nekeywordTextIndexMap = new HashMap<>();
        //获取创建重复的活动和广告组
        List<String> campaignIds = new ArrayList<>();
        List<String> adGroupIds = new ArrayList<>();
        duplicateValueErrorNekeywordMap.forEach((k, v) -> {
            campaignIds.add(v.getCampaignId());
            adGroupIds.add(v.getAdGroupId());
            nekeywordTextIndexMap.put(v.getAdGroupId() + "#" + v.getKeywordText(), k);
        });
        NegativeKeywordSpV3Client client = NegativeKeywordSpV3Client.getInstance();
        // 刷新token次数，保证不会无限刷新
        int refreshedToken = 0;
        String nextToken = null;
        ListSpNegativeKeywordV3Response response;
        Map<String, String> nekeywordTextIdMap;
        while (true) {
            response = client.listNegativeKeyword(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
                log.info("batch create keyword rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.listNegativeKeyword(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), campaignIds, adGroupIds, null, nextToken);
                retry++;
            }
            //刷新token
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401 && refreshedToken < 2) {
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken++;
                continue;
            }
            if (response == null || response.getData() == null || CollectionUtils.isEmpty(response.getData().getNegativeKeywords())) {
                break;
            }
            //将成功返回的nekeywordId存入成功列表中
            nekeywordTextIdMap = response.getData().getNegativeKeywords().stream().collect(Collectors.toMap(e -> e.getAdGroupId() + "#" + e.getKeywordText(), NegativeKeywordExtendEntityV3::getKeywordId));
            Iterator<Map.Entry<String, Integer>> iterator = nekeywordTextIndexMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> entry = iterator.next();
                if (nekeywordTextIdMap.containsKey(entry.getKey())) {
                    succMap.put(offset + entry.getValue(), nekeywordTextIdMap.get(entry.getKey()));
                    iterator.remove();
                }
            }
            //nextToken刷新
            if (StringUtils.isNotBlank(response.getData().getNextToken())){
                nextToken = response.getData().getNextToken();
            } else {
                break;
            }
        }
        return succMap;
    }

    /**
     * 记录日志
     */
    private void collectFailLog(List<AmazonAdKeyword> amazonAdKeywords, String loginIp) {
        List<AdManageOperationLog> nekeywordLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ;
        }
        Map<String, List<AmazonAdKeyword>> keywordGroupMap = amazonAdKeywords.stream().collect(Collectors.groupingBy(AmazonAdKeyword::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdKeyword>> entry : keywordGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdKeyword> nekeywordList = entry.getValue();
            for (AmazonAdKeyword nekeyword : nekeywordList) {
                msgSb.append("targetValue:").append(nekeyword.getKeywordText()).append(",desc:").append(nekeyword.getError()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建否定关键词失败";
        for (AmazonAdKeyword amazonAdKeyword: amazonAdKeywords) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getkeywordsLog(null, amazonAdKeyword);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            if (StringUtils.isNotBlank(amazonAdKeyword.getError())) {
                keywordLog.setResultInfo(amazonAdKeyword.getError());
            } else {
                keywordLog.setResultInfo(groupMsgMap.getOrDefault(amazonAdKeyword.getAdGroupId(), err));
            }
            nekeywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(nekeywordLogs);
    }

    /**
     * 记录日志
     */
    private void collectSuccLog(List<AmazonAdKeyword> amazonAdKeywords, String loginIp) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return;
        }
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        for (AmazonAdKeyword amazonAdKeyword: amazonAdKeywords) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getkeywordsLog(null, amazonAdKeyword);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }

    /**
     * 批量创建否定关键词实体类转亚马逊创建否定关键词实体类
     * @param amazonAdBatchNekeyword
     */
    private CreateNegativeKeywordEntityV3 batchNeKeyword2CreateNeKeywordsV3(AmazonAdBatchNekeyword amazonAdBatchNekeyword) {
        CreateNegativeKeywordEntityV3 createNegativeKeywordEntityV3 = new CreateNegativeKeywordEntityV3();
        createNegativeKeywordEntityV3.setCampaignId(amazonAdBatchNekeyword.getAmazonCampaignId());
        createNegativeKeywordEntityV3.setAdGroupId(amazonAdBatchNekeyword.getAmazonAdGroupId());
        createNegativeKeywordEntityV3.setKeywordText(amazonAdBatchNekeyword.getKeywordText());
        createNegativeKeywordEntityV3.setMatchType(SpV3NegativeMatchTypeEnum.getSpV3NegativeMatchTypeEnumByValue(amazonAdBatchNekeyword.getMatchType()).valueV3());
        createNegativeKeywordEntityV3.setState(SpV3StateEnum.ENABLED.valueV3());
        return createNegativeKeywordEntityV3;
    }

    /**
     * 批量创建否定关键词实体类转否定关键词实体类
     * @param amazonAdBatchNekeyword
     */
    private AmazonAdKeyword batchNeKeyword2NeKeyword(AmazonAdBatchNekeyword amazonAdBatchNekeyword) {
        AmazonAdKeyword amazonAdKeyword = new AmazonAdKeyword();
        amazonAdKeyword.setPuid(amazonAdBatchNekeyword.getPuid());
        amazonAdKeyword.setShopId(amazonAdBatchNekeyword.getShopId());
        amazonAdKeyword.setMarketplaceId(amazonAdBatchNekeyword.getMarketplaceId());
        amazonAdKeyword.setProfileId(amazonAdBatchNekeyword.getProfileId());
        amazonAdKeyword.setAdGroupId(amazonAdBatchNekeyword.getAmazonAdGroupId());
        amazonAdKeyword.setCampaignId(amazonAdBatchNekeyword.getAmazonCampaignId());
        amazonAdKeyword.setKeywordText(amazonAdBatchNekeyword.getKeywordText());
        amazonAdKeyword.setMatchType(amazonAdBatchNekeyword.getMatchType());
        amazonAdKeyword.setType(Constants.NEGATIVE);
        amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
        amazonAdKeyword.setCreateId(amazonAdBatchNekeyword.getCreateId());
        return amazonAdKeyword;
    }
}
