package com.meiyunji.sponsored.service.account.service;

import com.meiyunji.sponsored.common.springjdbc.IBaseService;
import com.meiyunji.sponsored.service.account.bo.ShopAuthBo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.vo.ShopAuthSellerInfoVo;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface IShopAuthService extends IBaseService<ShopAuth> {

    /**
     * 刷新cpcToken
     *
     * @param shop
     * @return
     */
    String refreshCpcAuth(ShopAuth shop);

    int getCpcAuth(int puid, Integer shopId, String marketplaceId);

    String getAdToken(ShopAuth shopAuth);

    Map<Integer, String> getShopNameMap(Integer puid, List<Integer> shopIds);

    List<Integer> checkAuthByShopIds(Integer puid, List<Integer> shopIds);

    ShopAuth getScAndVcById(int shopId);

    Map<Integer, String> getShopType(List<Integer> shopIds);


    ShopAuth getVcAndScByIdAndPuid(Object id, Integer puid);

    List<Integer> getShopByMid(int puid, String marketplaceId);

    List<ShopAuth> getListByPuid(int puid);

    List<ShopAuth> getAllId();

    List<Integer> getAllShopId();

    List<Integer> getAllShopId(int puid);

    List<Integer> getAllAdShopId(int puid);

    List<String> getAllSellerId(Integer puid);




    /**
     * 批量获取店铺信息（有效的店铺）
     *
     * @param puid
     * @param shopIds
     * @return
     */
    List<ShopAuth> listValidByIds(Integer puid, List<Integer> shopIds);




    /**
     * 获取所有有效的广告店铺
     */
    List<ShopAuth> listAllValidAdShop(Integer puid);



    /**
     * @param puid puid
     * @return List
     */
    List<String> getSellerIdByPuid(Integer puid);



    /**
     * 获取指定站点的店铺
     *
     * @param puid
     * @param mkList
     * @param shopList
     * @return
     */
    List<ShopAuth> listByPuidAndMarketplace(int puid, List<String> mkList, List<Integer> shopList);


    List<ShopAuth> getAllValidAdShopByLimit(Integer puid, Integer shopId, int start, int limit);

    List<ShopAuth> getByPuidAndMarketplaceIds(int puid, String[] marketplaceIds);


    /**
     * 根据店铺获取所有站点
     * @param shopIdList
     * @return
     */
    List<String> marketplaceIdListByShopIds(List<Integer> shopIdList);

    List<Integer> IdListByShopIds(List<Integer> shopIdList);

    /**
     * 根据店铺id获取出已授权的店铺
     * @param puid
     * @param shopIdList
     * @return
     */
    List<ShopAuth> getAuthShopByShopIdList(int puid, List<Integer> shopIdList);

    List<ShopAuth> getBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);

    /**
     * 根据店铺id和puid获取出已授权的店铺
     * @param puid
     * @param shopIdList
     * @return
     */
    List<ShopAuth> getAdAuthShopByShopIdList(List<Integer> puid);

    /**
     * 根据店铺id和puid获取出已授权的店铺
     * @param puid
     * @param shopIdList
     *
     * @return
     */
    List<ShopAuth> getAdAuthShopByShopId(List<Integer> puid, List<Integer> shopId);

    /**
     * 获取店铺id和名称等基础信息
     *
     * @param puid
     * @param idList
     * @return
     */
    List<ShopAuthBo> getShopAuthBoByIds(int puid, List<Integer> idList);

    List<ShopAuthBo> getShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);

    /**
     * 获取授权或过期的店铺
     */
    List<ShopAuthBo> getAuthShopBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);

    /**
     * 查询最大id和最小id
     *
     * @return
     */
    List<Integer> queryRandomSequentList();


    /**
     * 查询seller相关信息
     * @param integers
     * @return
     */
    List<ShopAuthSellerInfoVo> getSellerInfoByIdList(ArrayList<Integer> integers);


    List<ShopAuth> getScAndVcShopList(int puid, List<Integer> shopIds, String adStatus);

    List<ShopAuth> getScAndVcBySellerIdsAndMarketplaceIds(List<String> sellerIds, List<String> marketplaceIds);


    List<ShopAuth> getScAndVcShopListByShopIdsAndAdStatus(List<Integer> shopIds, String adStatus);

    List<ShopAuth> getScAndVcByIds(List<Integer> ids);

    ShopAuth getScAndVcByIdAndPuid(int id, int puid);

    List<ShopAuthBo> getScAndVcShopAuthBoByMarketPlaceAndIds(int puid, List<Integer> idList, List<String> marketplaceIdList);



    List<ShopAuth> listAllByIds(int puid, List<Integer> shopIds);

    List<ShopAuth> getScAndVcAuthShopByShopIdList(int puid, List<Integer> shopIdList);


    ShopAuth getBySellerIdAndMarketplaceId(String sellerId, String marketplaceId);

    List<ShopAuth> listScAndVcAllByPuid(int puid);

}
