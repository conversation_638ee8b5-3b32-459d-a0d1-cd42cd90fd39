package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadras.types.schedule.AdvertiseRuleTaskExecuteRecordV2Message;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.SelectBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.common.util.UCommonUtil;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleExecuteRecord;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordCardAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AdKeywordTargetAutoRuleParam;
import com.meiyunji.sponsored.service.autoRule.vo.AllUpdateAutoRuleParam;
import com.meiyunji.sponsored.service.cpc.bo.AdOrderBo;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeKeyword;
import com.meiyunji.sponsored.service.cpc.po.SearchQueryTagParam;
import com.meiyunji.sponsored.service.cpc.qo.KeywordSuggestBidBatchQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.pricing.ScheduleTaskFinishedVo;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.DiagnoseCountParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.KeywordViewAggregateDto;
import com.meiyunji.sponsored.service.strategy.vo.AdTargetStrategyParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.SingleColumnRowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AmazonAdNekeyword
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdNeKeywordDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdNeKeyword> implements IAmazonAdNeKeywordDao {
    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdNeKeyword> amazonAdKeywords) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_ne_keyword` (`puid`,`shop_id`,`marketplace_id`,")
                .append("`keyword_id`,`ad_group_id`,`dxm_group_id`,`campaign_id`,`profile_id`,`keyword_text`,`match_type`,`bid`,`type`,")
                .append("`state`,`serving_status`,`range_start`,`range_end`,`suggested`,`create_id`,`update_id`,`creation_date`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdNeKeyword amazonAdKeyword : amazonAdKeywords) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(amazonAdKeyword.getPuid());
            argsList.add(amazonAdKeyword.getShopId());
            argsList.add(amazonAdKeyword.getMarketplaceId());
            argsList.add(amazonAdKeyword.getKeywordId());
            argsList.add(amazonAdKeyword.getAdGroupId());
            argsList.add(null);
            argsList.add(amazonAdKeyword.getCampaignId());
            argsList.add(amazonAdKeyword.getProfileId());
            argsList.add(amazonAdKeyword.getKeywordText());
            argsList.add(amazonAdKeyword.getMatchType());
            argsList.add(amazonAdKeyword.getBid());
            argsList.add(amazonAdKeyword.getType());
            argsList.add(amazonAdKeyword.getState());
            argsList.add(amazonAdKeyword.getServingStatus());
            argsList.add(amazonAdKeyword.getRangeStart());
            argsList.add(amazonAdKeyword.getRangeEnd());
            argsList.add(amazonAdKeyword.getSuggested());
            argsList.add(amazonAdKeyword.getCreateId());
            argsList.add(amazonAdKeyword.getUpdateId());
            argsList.add(amazonAdKeyword.getCreationDate());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update match_type=values(match_type),keyword_text=values(keyword_text),bid=values(bid),`state`=values(state),`serving_status`=values(serving_status),`creation_date`=values(creation_date)");
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Integer countByDxmAdGroupId(int puid, Integer shopId, String adGroupId) {
        String sql = "SELECT COUNT(*) FROM `t_amazon_ad_ne_keyword` WHERE puid=? AND shop_id=? AND ad_group_id=? AND `type`='negative'";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql, new Object[]{puid, shopId, adGroupId}, Integer.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getKeywordIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineKeywordId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("keyword_id", onlineKeywordId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid, "keyword_id", builder, String.class);
    }

    @Override
    public void updateList(Integer puid, List<AmazonAdNeKeyword> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_keyword` set bid=?,`state`=?,")
                .append(" update_id=? where puid=? and shop_id=? and campaign_id=? and keyword_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdNeKeyword amazonAdKeyword : list) {
            batchArg = new Object[]{
                    amazonAdKeyword.getBid(),
                    amazonAdKeyword.getState(),
                    amazonAdKeyword.getUpdateId(),
                    amazonAdKeyword.getPuid(),
                    amazonAdKeyword.getShopId(),
                    amazonAdKeyword.getCampaignId(),
                    amazonAdKeyword.getKeywordId()
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public String getProfileId(int puid, Integer shopId, String marketPlaceId, String keywordId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("keyword_id", keywordId)
                .equalTo("marketplace_id", marketPlaceId)
                .build();
        return getByCondition(puid, "profile_id", String.class, builder);
    }

    @Override
    public List<AmazonAdNeKeyword> listKeyWordAndMatchTypeByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIdList.toArray(new String[]{}))
                .equalTo("type", Constants.NEGATIVE)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return listByConditionWithFields(puid, Lists.newArrayList("keyword_text", "match_type", "ad_group_id"), builder);
    }


    @Override
    public void updateState(int puid, Integer shopId, String keywordId, String state, int updateId) {
        String sql = "update t_amazon_ad_ne_keyword set state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `keyword_id`=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, new Object[]{state, updateId, puid, shopId, keywordId});
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdNeKeyword> getListByGroup(int puid, Integer shopId, String adGroupId) {
        String sql = "select id,keyword_id,keyword_text,match_type,bid,`type`,state,range_start,range_end,suggested" +
                " from t_amazon_ad_ne_keyword where puid=? and shop_id =? and ad_group_id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, new Object[]{puid, shopId, adGroupId}, getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public void updateBid(Integer puid, Integer updateId, List<AmazonAdNeKeyword> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_keyword` set bid=?,")
                .append("`update_time`= now(),update_id=? where id=? and puid=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdNeKeyword amazonAdKeyword : list) {
            batchArg = new Object[]{
                    amazonAdKeyword.getBid(),
                    amazonAdKeyword.getUpdateId(),
                    amazonAdKeyword.getId(),
                    puid
            };
            batchArgs.add(batchArg);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }

    }


    @Override
    public List<AmazonAdNeKeyword> listByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", groupIdList.toArray(new String[]{}))
                .equalTo("type", "negative")
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdNeKeyword> listByCampaignOrGroupOrName(Integer puid, Integer shopId, List<String> campaignIdList, List<String> groupIdList, String kewordText) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        builder.in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});
        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            builder.inStrList("campaign_id", campaignIdList.toArray(new String[]{}));
        }
        if (CollectionUtils.isNotEmpty(groupIdList)) {
            builder.inStrList("ad_group_id", groupIdList.toArray(new String[]{}));
        }
        if (StringUtils.isNotBlank(kewordText)) {
            builder.like("keyword_text", kewordText);
        }
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdNeKeyword> listByCampaignOrGroupOrName(AllUpdateAutoRuleParam allUpdateAutoRuleParam) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", allUpdateAutoRuleParam.getPuid());
        builder.equalTo("shop_id", allUpdateAutoRuleParam.getShopId());
        if (StringUtils.isNotBlank(allUpdateAutoRuleParam.getState())) {
            builder.equalTo("state", allUpdateAutoRuleParam.getState());
        }
        if (StringUtils.isNotBlank(allUpdateAutoRuleParam.getMatchType())) {
            builder.equalTo("match_type", allUpdateAutoRuleParam.getMatchType());
        }
        if (CollectionUtils.isNotEmpty(allUpdateAutoRuleParam.getCampaignIds())) {
            builder.inStrList("campaign_id", allUpdateAutoRuleParam.getCampaignIds().toArray(new String[]{}));
        }
        if (CollectionUtils.isNotEmpty(allUpdateAutoRuleParam.getAdGroupIds())) {
            builder.inStrList("ad_group_id", allUpdateAutoRuleParam.getAdGroupIds().toArray(new String[]{}));
        }
        if (StringUtils.isNotBlank(allUpdateAutoRuleParam.getSearchValue())) {
            builder.like("keyword_text", allUpdateAutoRuleParam.getSearchValue());
        }
        return listByCondition(allUpdateAutoRuleParam.getPuid(), builder.build());
    }


    @Override
    public AmazonAdNeKeyword getByKeywordId(int puid, Integer shopId, String marketplaceId, String keywordId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("keyword_id", keywordId)
                .equalTo("marketplace_id", marketplaceId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public String getKeywordText(Integer puid, Integer shopId, String keywordId) {
        String sql = "SELECT `keyword_text` FROM `t_amazon_ad_ne_keyword` WHERE `puid`=? AND `shop_id`=? AND `keyword_id`= ? ";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql, String.class, puid, shopId, keywordId);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public AmazonAdNeKeyword getByKeywordId(int puid, Integer shopId, String keywordId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("keyword_id", keywordId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder("select keyword_id,ad_group_id,campaign_id,keyword_text,match_type,create_time from t_amazon_ad_ne_keyword");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_ne_keyword ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and state in ('enabled','paused') and type='negative' and keyword_id is not null ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        if (StringUtils.isNotEmpty(dto.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            argsList.add(dto.getCampaignId());
        }
        if (StringUtils.isNotEmpty(dto.getGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            argsList.add(dto.getGroupId());
        }
        if (Constants.ITEM_TYPE_KEYWORD.equals(dto.getSearchField()) && StringUtils.isNotEmpty(dto.getSearchValue())) {
            String searchValue = SqlStringUtil.dealLikeSql(dto.getSearchValue());
            whereSql.append(" and keyword_text like ? ");
            argsList.add(searchValue + "%");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by id desc");
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdNeKeyword.class);
    }

    @Override
    public List<AmazonAdNeKeyword> listKeywords(int puid, List<String> keywordIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("keyword_id", keywordIdList.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public AmazonAdNeKeyword getByKeywordText(Integer puid, Integer shopId, String marketplaceId, String campaignId, String adGroupId, String query, String type) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("keyword_text", query)
                .equalTo("type", type)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public int count(Integer puid, Integer shopId) {
        String sql = "select count(*) from t_amazon_ad_ne_keyword where puid=? and shop_id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForObject(sql, new Object[]{puid, shopId}, Integer.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public boolean exist(Integer puid, Integer shopId, String adGroupId, String keywordText, String matchType) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .equalTo("keyword_text", keywordText)
                .equalTo("type", Constants.NEGATIVE)
                .equalTo("match_type", matchType);
        Integer count = getCountByCondition(puid, builder.build());
        return count > 0;
    }

    @Override
    public Page<AmazonAdNeKeyword> neKeywordsPageList(Integer puid, NeKeywordsPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .equalTo("type", Constants.NEGATIVE);

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("keyword_text", param.getSearchValue());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        String orderBySql = " order by update_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public Page<AmazonAdNeKeyword> keywordsPageList(Integer puid, KeywordsPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .equalTo("type", Constants.NEGATIVE);

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("keyword_text", param.getSearchValue());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        String orderBySql = " order by update_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonAdNeKeyword> listByCondition(Integer puid, KeywordsPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId())
                .equalTo("type", Constants.NEGATIVE);

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            builder.like("keyword_text", param.getSearchValue());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public Map<String, Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", Constants.NEGATIVE)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .groupBy("ad_group_id").build();

        String sql = "select ad_group_id adGroupId, count(*) c from t_amazon_ad_ne_keyword where " + conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, conditionBuilder.getValues())
                    .stream().collect(Collectors.toMap(e -> e.get("adGroupId").toString(), e -> Integer.parseInt(e.get("c").toString())));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public int countByGroup(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();

        return getCountByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdNeKeyword> getByKeywordIds(int puid, Integer shopId, List<String> keywordIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("keyword_id", keywordIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public List<AmazonAdNeKeyword> getByKeywordSuggestBidBatchQo(int puid, List<KeywordSuggestBidBatchQo> keywordList) {
        StringBuilder sb = new StringBuilder("select * from " + getJdbcHelper().getTable() + " where puid = ? and (shop_id,keyword_id) in (");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        for (KeywordSuggestBidBatchQo qo : keywordList) {
            sb.append("(?,?),");
            argsList.add(qo.getShopId());
            argsList.add(qo.getKeywordId());
        }
        sb.deleteCharAt(sb.length() - 1);
        sb.append(")");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), getMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdNeKeyword> autoRuleKeyword(int puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds, String state, String matchType, String keywordText, List<String> keywordIds, List<String> seringStatus) {
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopId);
        conditionBuilder.in("keyword_id", keywordIds.toArray());
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.in("campaign_id", campaignIds.toArray());
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            conditionBuilder.in("ad_group_id", adGroupIds.toArray());
        }
        if (StringUtils.isNotBlank(state)) {
            conditionBuilder.equalTo("state", state);
        }
        if (StringUtils.isNotBlank(matchType)) {
            conditionBuilder.equalTo("match_type", matchType);
        }
        if (StringUtils.isNotBlank(keywordText)) {
            conditionBuilder.like("keyword_text", keywordText);
        }
        if (CollectionUtils.isNotEmpty(seringStatus)) {
            conditionBuilder.in("serving_status", seringStatus.toArray());
        }
        return listByCondition(puid, conditionBuilder.build());
    }

    @Override
    public List<AmazonAdNeKeyword> getByKeywordIds(int puid, List<String> keywordIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("keyword_id", keywordIds.toArray())
                .build();
        return listByCondition(puid, conditionBuilder);
    }

    @Override
    public void batchUpdateSuggestValue(Integer puid, List<AmazonAdNeKeyword> amazonAdKeywords) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return;
        }

        List<Object[]> argList = new ArrayList<>(amazonAdKeywords.size());
        List<Object> arg;
        for (AmazonAdNeKeyword po : amazonAdKeywords) {
            arg = new ArrayList<>();
            arg.add(po.getSuggested());
            arg.add(po.getRangeStart());
            arg.add(po.getRangeEnd());
            arg.add(po.getId());
            argList.add(arg.toArray());
        }

        String sql = "update t_amazon_ad_ne_keyword set suggested=?, range_start=?, range_end=?, update_time=now(3) where id=?";

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql, argList);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page<NeKeywordsPageDto> getAllTypeNeKeyword(Integer puid, NeKeywordsPageParam param) {

        SelectBuilder selectBuilder = getNeKewordSelectSql(puid, param);

        StringBuilder sql = new StringBuilder("select id,shop_id,type,create_time,state,match_type,keyword_text,campaign_id,ad_group_id ");
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            sql.append(" ,creation_date ");
        }
        sql.append(" from ( ").append(selectBuilder.toSql()).append(" ) c ");

        //默认排序
        String orderSql;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            orderSql = " order by creation_date desc ";
        } else {
            orderSql = " order by create_time desc ";
        }

        sql.append(orderSql);

        StringBuilder countSql = new StringBuilder("select count(*) from ( ").append(selectBuilder.toSql()).append(" ) c");

        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            return getPageByMapperForTotalSize(puid, param.getPageNo(), param.getPageSize(), param.getTotalSize(), sql.toString(), selectBuilder.getQueryValues(), new RowMapper<NeKeywordsPageDto>() {
                @Override
                public NeKeywordsPageDto mapRow(ResultSet res, int i) throws SQLException {
                    return NeKeywordsPageDto.builder()
                            .groupId(res.getString("ad_group_id"))
                            .campaignId(res.getString("campaign_id"))
                            .keywordText(res.getString("keyword_text"))
                            .matchType(res.getString("match_type"))
                            .state(res.getString("state"))
                            .createTime(res.getString("create_time"))
                            .type(res.getString("type"))
                            .shopId(res.getInt("shop_id"))
                            .id(res.getLong("id"))
                            .creationDate(Constants.SP.equalsIgnoreCase(param.getType()) ? res.getObject("creation_date", LocalDateTime.class) : null)
                            .build();
                }
            });
        } else {
            return getPageByMapper(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), selectBuilder.getQueryValues(), sql.toString(), selectBuilder.getQueryValues(), new RowMapper<NeKeywordsPageDto>() {
                @Override
                public NeKeywordsPageDto mapRow(ResultSet res, int i) throws SQLException {
                    return NeKeywordsPageDto.builder()
                            .groupId(res.getString("ad_group_id"))
                            .campaignId(res.getString("campaign_id"))
                            .keywordText(res.getString("keyword_text"))
                            .matchType(res.getString("match_type"))
                            .state(res.getString("state"))
                            .createTime(res.getString("create_time"))
                            .type(res.getString("type"))
                            .shopId(res.getInt("shop_id"))
                            .id(res.getLong("id"))
                            .creationDate(Constants.SP.equalsIgnoreCase(param.getType()) ? res.getObject("creation_date", LocalDateTime.class) : null)
                            .build();
                }
            });
        }
    }


    private SelectBuilder getNeKewordSelectSql(Integer puid, NeKeywordsPageParam param) {

        SelectBuilder selectBuilder = new SelectBuilder();

        StringBuilder spSql = new StringBuilder();
        //未指定类型 或 指定sp
        if (Constants.SP.equalsIgnoreCase(param.getType())) {

            spSql.append("  'sp' as type, id, `puid`, `shop_id`, `keyword_id`, `ad_group_id`, `campaign_id`, ");
            spSql.append("`keyword_text`, `match_type`, `state`, `creation_date`, `create_time`, `update_time` from t_amazon_ad_ne_keyword  where  ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId())
                    .equalTo("type", Constants.NEGATIVE); //sp否定关键词和非关键词在同一张表

            //状态查询
            if (StringUtils.isNotBlank(param.getState())) {
                builder.equalTo("state", param.getState());
            }
            //关键搜索
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if (searchFieldEnum != null) {
                    if ("blur".equals(param.getSearchType())) { //模糊搜索
                        builder.like(searchFieldEnum.getColumn(), "%" + param.getSearchValue().trim() + "%");
                    } else {//默认精确
                        if (param.getListSearchValue().size() > 1) {
                            builder.inStrList(searchFieldEnum.getColumn(), param.getListSearchValue().toArray(new String[]{}));
                        } else {
                            builder.equalTo(searchFieldEnum.getColumn(), param.getSearchValue().trim());
                        }
                    }
                } else {
                    logger.error("否定关键词搜索条件异常：puid:{},shopId:{},searchField:{}", puid, param.getShopId(), param.getSearchField());
                }
            }
            //广告活动ID搜索
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.inStrList("campaign_id", StringUtil.splitStr(param.getCampaignId()).toArray(new String[]{}));
            }
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
                builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
            }
            //广告组ID搜索
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.inStrList("ad_group_id", StringUtil.splitStr(param.getGroupId()).toArray(new String[]{}));
            }
            //匹配类型搜索
            if (StringUtils.isNotBlank(param.getMatchType())) {
                builder.equalTo("match_type", param.getMatchType());
            }

            spSql.append(builder.build().getSql());

            selectBuilder.appendSql(spSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }


        //未指定类型 或 指定sb
        StringBuilder sbSql = new StringBuilder();
        if (Constants.SB.equalsIgnoreCase(param.getType())) {

            sbSql.append("  'sb' as type, id, `puid`, `shop_id`,`keyword_id`, `ad_group_id`, `campaign_id`,");
            sbSql.append(" `keyword_text`, `match_type`, `state`, ");
            sbSql.append(" `create_time`, `update_time` from t_amazon_ad_nekeyword_sb where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //状态查询
            if (StringUtils.isNotBlank(param.getState())) {
                builder.equalTo("state", param.getState());
            }
            //关键搜索
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
                KeywordsPageParam.SearchFieldEnum searchFieldEnum = UCommonUtil.getByCode(param.getSearchField(), KeywordsPageParam.SearchFieldEnum.class);
                if ("blur".equals(param.getSearchType())) { //模糊搜索
                    builder.like(searchFieldEnum.getColumn(), "%" + param.getSearchValue().trim() + "%");
                } else {//默认精确
                    if (param.getListSearchValue().size() > 1) {
                        builder.inStrList(searchFieldEnum.getColumn(), param.getListSearchValue().toArray(new String[]{}));
                    } else {
                        builder.equalTo(searchFieldEnum.getColumn(), param.getSearchValue().trim());
                    }
                }
            }
            //广告活动ID搜索
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.inStrList("campaign_id", StringUtil.splitStr(param.getCampaignId()).toArray(new String[]{}));
            }
            if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
                builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
            }
            //广告组ID搜索
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.inStrList("ad_group_id", StringUtil.splitStr(param.getGroupId()).toArray(new String[]{}));
            }
            //匹配类型搜索
            if (StringUtils.isNotBlank(param.getMatchType())) {
                builder.equalTo("match_type", param.getMatchType());
            }

            sbSql.append(builder.build().getSql());

            selectBuilder.appendSql(sbSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }
        return selectBuilder;
    }

    @Override
    public void updatePricing(Integer puid, Integer shopId, String keywordId, Integer isPricing, Integer pricingState, int updateId) {
        String sql = "update t_amazon_ad_ne_keyword set is_pricing=?,pricing_state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `keyword_id`=?";

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, new Object[]{isPricing, pricingState, updateId, puid, shopId, keywordId});
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page getPageList(Integer puid, KeywordsPageParam param, Page page) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_ne_keyword ");
        StringBuilder countSql = new StringBuilder("select count(*)  FROM `t_amazon_ad_ne_keyword` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and type='negative' "); //sp否定关键词和非关键词在同一张表
        argsList.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));

        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealInList("keyword_id", param.getKeywordIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(param.getSearchType())) { //模糊搜索
                whereSql.append(" and keyword_text Like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            } else {//默认精确
                if (param.getListSearchValue().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("keyword_text", param.getListSearchValue(), argsList));
                } else {
                    whereSql.append(" and keyword_text = ? ");
                    argsList.add(param.getSearchValue().trim());
                }
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdNeKeyword.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            whereSql.append(" and match_type = ? ");
            argsList.add(param.getMatchType());
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdNeKeyword.class);
    }

    @Override
    public List<AmazonAdNeKeyword> getList(Integer puid, KeywordsPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("type", Constants.NEGATIVE); //sp否定关键词和非关键词在同一张表

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", list.toArray(new String[]{}));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
        }
        //广告组ID搜索
        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> list = StringUtil.splitStr(param.getGroupId());
            builder.inStrList("ad_group_id", list.toArray(new String[]{}));
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            builder.inStrList("keyword_id", param.getKeywordIds().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(param.getSearchType())) { //模糊搜索
                builder.like("keyword_text", "%" + param.getSearchValue().trim() + "%");
            } else {//默认精确
                if (param.getListSearchValue().size() > 1) {
                    builder.inStrList("keyword_text", param.getListSearchValue().toArray(new String[]{}));
                } else {
                    builder.equalTo("keyword_text", param.getSearchValue().trim());
                }
            }
        }


        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonAdNeKeyword.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }

        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            builder.equalTo("match_type", param.getMatchType());
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                builder.greaterThanOrEqualTo("bid", param.getBidMin());
            }
            if (param.getBidMax() != null) {
                builder.lessThanOrEqualTo("bid", param.getBidMax());
            }
        }

        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String keywordId, LocalDate localDate) {
        String sql = "update t_amazon_ad_ne_keyword set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `keyword_id`=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, new Object[]{localDate, puid, shopId, keywordId});
        } finally {
            hintManager.close();
        }
    }

    /**
     * @param puid
     * @param shopId
     * @param ids
     * @return
     */
    @Override
    public List<AmazonAdNeKeyword> getListByIds(Integer puid, Integer shopId, List<Long> ids) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("id", ids.toArray());
        return listByCondition(puid, builder.build());
    }


    @Override
    public void updateList(Integer puid, List<AmazonAdNeKeyword> list, String type) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_ne_keyword` set update_id=?,update_time=now() ");
        if (Constants.CPC_SP_KEYWORD_BATCH_UPDATE_BID.equals(type)) {
            sql.append(",`bid` = ? ");
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            sql.append(",`state`=? ");
        }
        sql.append(" where puid=? and shop_id=? and campaign_id=? and keyword_id=? and id = ?");
        List<Object[]> batchArgs = Lists.newArrayList();
        List<Object> batchArg = new ArrayList<>();
        for (AmazonAdNeKeyword amazonAdKeyword : list) {
            batchArg.add(amazonAdKeyword.getUpdateId());
            if (Constants.CPC_SP_KEYWORD_BATCH_UPDATE_BID.equals(type)) {
                batchArg.add(amazonAdKeyword.getBid());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
                batchArg.add(amazonAdKeyword.getState());

            }
            batchArg.add(amazonAdKeyword.getPuid());
            batchArg.add(amazonAdKeyword.getShopId());
            batchArg.add(amazonAdKeyword.getCampaignId());
            batchArg.add(amazonAdKeyword.getKeywordId());
            batchArg.add(amazonAdKeyword.getId());
            batchArgs.add(batchArg.toArray());
            batchArg.clear();
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).batchUpdate(sql.toString(), batchArgs);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getKeywordByCampaignId(Integer puid, Integer shopId, String campaignId) {

        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listDistinctFieldByCondition(puid, "keyword_text", builder.build(), String.class);
    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {
        String sql = "select keyword_id from t_amazon_ad_ne_keyword where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("state", "archived")
                .build();
        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, String.class, conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {
        String sql = "select keyword_id from t_amazon_ad_ne_keyword where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThan("update_time", syncAt)
                .build();
        sql += conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql, String.class, conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeKeyword> getListByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
            .equalTo("puid", puid)
            .equalTo("shop_id", shopId)
            .in("ad_group_id", adGroupIds.toArray())
            .in("state", new Object[] {"enabled", "paused"})
            .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<String> getKeywordByShopId(Integer puid, CpcQueryWordDto dto) {
        List<Object> tarArgList = Lists.newArrayList();
        StringBuilder tarIdSql = new StringBuilder("select w.keyword_id from t_amazon_ad_ne_keyword w where w.puid = ? and w.shop_id = ? ");
        tarArgList.add(puid);
        tarArgList.add(dto.getShopId());
        if (StringUtils.isNotBlank(dto.getState())) {
            tarIdSql.append(" and w.state = ? ");
            tarArgList.add(dto.getState());
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(tarIdSql.toString(), tarArgList.toArray(), String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> getKeywordIdsByKeyword(Integer puid, KeywordsPageParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.keyword_id from t_amazon_ad_ne_keyword c ");
        StringBuilder whereSql = new StringBuilder(" where c.puid = ? and c.type='negative' ");
        argsList.add(puid);

        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }

        //标签管理
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.keyword_id", param.getKeywordIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(param.getSearchType())) { //模糊搜索
                whereSql.append(" and keyword_text Like ? ");
                argsList.add("%" + param.getSearchValue().trim() + "%");
            } else {//默认精确
                if (param.getListSearchValue().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("keyword_text", param.getListSearchValue(), argsList));
                } else {
                    whereSql.append(" and keyword_text = ? ");
                    argsList.add(param.getSearchValue().trim());
                }
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdNeKeyword.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            whereSql.append(" and c.match_type = ? ");
            argsList.add(param.getMatchType());
        }

        // 开启了高级搜索 竞价值
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                whereSql.append(" and c.bid >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and c.bid <= ? ");
                argsList.add(param.getBidMax());
            }
        }

        sql.append(whereSql);
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sql.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }


    }

    @Override
    public Integer statSumCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("type", Constants.NEGATIVE)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .build();

        String sql = "select count(*) c from t_amazon_ad_ne_keyword where " + conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            List<Integer> list = getJdbcTemplate(puid, hintManager).queryForList(sql, Integer.class, conditionBuilder.getValues());
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0;

        } finally {
            hintManager.close();
        }
    }

    @Override
    public Integer statSumCountByAdGroupPage(Integer puid, Integer shopId, List<String> status, GroupPageParam param) {
        StringBuilder builder = new StringBuilder("select count(*) c from t_amazon_ad_ne_keyword where puid = ? and shop_id = ? ");
        List<Object> args = Lists.newArrayList(puid, shopId);
        builder.append(" and type = ? ");
        args.add(Constants.NEGATIVE);
        builder.append(SqlStringUtil.dealInList("state", status, args));
        builder.append(" and ad_group_id in ( ").append(getGroupPageSql(puid, param, args)).append(" )");
        HintManager hintManager = HintManager.getInstance();
        try {
            List<Integer> list = getJdbcTemplate(puid, hintManager).queryForList(builder.toString(), Integer.class, args.toArray());
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0;

        } finally {
            hintManager.close();
        }
    }

    @Override
    public Page<AmazonAdNeKeyword> querySpKeyword(AdTargetStrategyParam param) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_ne_keyword t left join t_amazon_ad_campaign_all c on (t.puid = c.puid and t.shop_id = c.shop_id and t.marketplace_id = c.marketplace_id and t.campaign_id = c.campaign_id and c.campaign_id is not null and c.type = 'sp')" +
                " left join t_amazon_ad_group g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        StringBuilder countSql = new StringBuilder("SELECT count(t.id) FROM t_amazon_ad_ne_keyword t left join t_amazon_ad_campaign_all c on (t.puid = c.puid and t.shop_id = c.shop_id and t.marketplace_id = c.marketplace_id and t.campaign_id = c.campaign_id and c.campaign_id is not null and c.type = 'sp')"
                + " left join t_amazon_ad_group g on (t.puid = g.puid and t.shop_id = g.shop_id and t.marketplace_id = g.marketplace_id and t.ad_group_id = g.ad_group_id)");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where t.puid=? and t.type='negative' and t.is_pricing = 0 and c.state not in ('archived','pendingReview','rejected') and g.state not in ('archived','pendingReview','rejected')"); //sp否定关键词和非关键词在同一张表
        argsList.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getState())) {//状态
            if (param.getState().equals("all")) {
                whereSql.append(" and t.state in ('enabled','paused')");
            } else {
                whereSql.append(" and t.state = ?");
                argsList.add(param.getState());
            }
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getServingStatusList())) {  //服务状态查询
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));

        }
        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {  //广告组id
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getMatchType())) {
            whereSql.append("and t.match_type = ?");
            argsList.add(param.getMatchType());
        }

        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and t.keyword_text like ? ");
            argsList.add("%" + param.getSearchValue() + "%");
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);

        return getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(),
                countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), AmazonAdNeKeyword.class);
    }

    @Override
    public Page<AmazonAdNeKeyword> pageKeywordCardSpKeyword(AdKeywordCardAutoRuleParam param, List<String> itemIds) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_ne_keyword t LEFT JOIN t_amazon_ad_campaign_all c" +
                " ON t.puid=c.puid and t.shop_id = c.shop_id and t.campaign_id = c.campaign_id LEFT JOIN t_amazon_ad_group g ON " +
                " t.puid=g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id");
        StringBuilder countSql = new StringBuilder("select count(t.id) from t_amat_amazon_ad_ne_keywordzon_ad_keyword t LEFT JOIN t_amazon_ad_campaign_all c" +
                " ON t.puid=c.puid and t.shop_id = c.shop_id and t.campaign_id = c.campaign_id LEFT JOIN t_amazon_ad_group g ON " +
                " t.puid=g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where t.puid=? "); //sp否定关键词和非关键词在同一张表
        argsList.add(param.getPuid());
        if (param.getShopId() != null) {
            whereSql.append(" and t.shop_id = ?");
            argsList.add(param.getShopId());
        }
        if (CollectionUtils.isNotEmpty(itemIds)) {
            //过滤模板关系
            whereSql.append(SqlStringUtil.dealNotInList("t.keyword_id", itemIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告活动id查询
            whereSql.append(SqlStringUtil.dealInList("t.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(param.getServingStatusList())) {
            //过滤模板关系
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", param.getServingStatusList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) {  //广告组id
            whereSql.append(SqlStringUtil.dealInList("t.ad_group_id", param.getGroupIdList(), argsList));
        }
        whereSql.append(" and t.type='negative'");
        whereSql.append(" and t.match_type='exact'");
        whereSql.append(" and c.strategy='manual'");
        whereSql.append(" and t.is_pricing=0");
        if (StringUtils.isNotBlank(param.getState())) {//状态
            if (param.getState().equals("all")) {
                whereSql.append(" and t.state in ('enabled','paused')");
            } else {
                whereSql.append(" and t.state = ?");
                argsList.add(param.getState());
            }
        }

        if (StringUtils.isNotBlank(param.getMatchType())) {
            whereSql.append(" and t.match_type = ?");
            argsList.add(param.getMatchType());
        }

        if (StringUtils.isNotBlank(param.getKeywordText())) {
            whereSql.append(" and t.keyword_text like ? ");
            argsList.add("%" + param.getKeywordText() + "%");
        }
        whereSql.append(" and c.state in ('enabled','paused') and g.state in ('enabled','paused') and c.targeting_type='manual'");

        selectSql.append(whereSql);
        countSql.append(whereSql);

        return getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(),
                countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(), AmazonAdNeKeyword.class);
    }

    @Override
    public List<String> queryAutoRuleSpKeyword(Integer puid, Integer shopId, List<String> campaignIds, List<String> groupIds, String state, String matchType) {
        StringBuilder selectSql = new StringBuilder("select keyword_id from t_amazon_ad_ne_keyword ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id = ? and type='negative' "); //sp否定关键词和非关键词在同一张表
        argsList.add(puid);
        argsList.add(shopId);
        if (StringUtils.isNotBlank(state)) {//状态
            if (state.equals("all")) {
                whereSql.append(" and state in ('enabled','paused')");
            } else {
                whereSql.append(" and state = ?");
                argsList.add(state);
            }
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(groupIds)) {  //广告组id
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }

        if (StringUtils.isNotBlank(matchType)) {
            whereSql.append(" and match_type = ?");
            argsList.add(matchType);
        }

        selectSql.append(whereSql);

        Object[] args = argsList.toArray();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(selectSql.toString(), args, String.class);
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<String> querySpKeywordIdList(Integer puid, Integer shopId,
                                             List<String> campaignIds, List<String> groupIds, String state, String targetType, String matchType, List<String> keywordIdList) {
        StringBuilder selectSql = new StringBuilder("select keyword_id from t_amazon_ad_ne_keyword ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id = ? and type='negative' "); //sp否定关键词和非关键词在同一张表
        argsList.add(puid);
        argsList.add(shopId);
        whereSql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        if (StringUtils.isNotBlank(state)) {//状态
            if (state.equals("all")) {
                whereSql.append(" and state in ('enabled','paused')");
            } else {
                whereSql.append(" and state = ?");
                argsList.add(state);
            }
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }

        if (CollectionUtils.isNotEmpty(groupIds)) {  //广告组id
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }

        if (StringUtils.isNotBlank(matchType)) {
            whereSql.append(" and match_type = ?");
            argsList.add(matchType);
        }

        selectSql.append(whereSql);

        Object[] args = argsList.toArray();

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(selectSql.toString(), args, String.class);
        } finally {
            hintManager.close();
        }

    }

    private String getGroupPageSql(Integer puid, GroupPageParam param, List<Object> args) {
        StringBuilder selectSql = new StringBuilder("select ad_group_id from t_amazon_ad_group ");


        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        args.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            args.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(" and campaign_id = ? ");
            args.add(param.getCampaignId());
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {  // 广告标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), args));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            whereSql.append(" and name like ? ");
            args.add("%" + param.getSearchValue().trim() + "%");
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
            whereSql.append(" and serving_status = 'AD_GROUP_STATUS_ENABLED' ");
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ? ");
                args.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ? ");
                args.add(param.getBidMax());
            }
        }
        selectSql.append(whereSql);
        return selectSql.toString();
    }

    /**
     * TODO 关联关键词库查询
     *
     * @param puid
     * @return
     */
    @Override
    public List<KeywordLibsDetailVo> aggregateKeyword(Integer puid, List<String> keywordTextList) {

        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select `puid`, `shop_id`, `marketplace_id`, `keyword_id`, `keyword_text`, `campaign_id`, `ad_group_id`, `match_type`, `state`," +
                "`bid`, `suggested`, `type` target_type from t_amazon_ad_ne_keyword where puid = ? and type='negative' ");
        args.add(puid);

        if (CollectionUtils.isNotEmpty(keywordTextList)) {
            sql.append(SqlStringUtil.dealInList("keyword_text", keywordTextList, args));
        }

        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new RowMapper<KeywordLibsDetailVo>() {
                @Override
                public KeywordLibsDetailVo mapRow(ResultSet res, int i) throws SQLException {
                    return KeywordLibsDetailVo.builder()
                            .puid(res.getInt("puid"))
                            .shopId(res.getInt("shop_id"))
                            .marketplaceId(res.getString("marketplace_id"))
                            .keywordId(res.getString("keyword_id"))
                            .keywordText(res.getString("keyword_text"))
                            .campaignId(res.getString("campaign_id"))
                            .adGroupId(res.getString("ad_group_id"))
                            .matchType(res.getString("match_type"))
                            .state(res.getString("state"))
                            .bid(Optional.ofNullable(res.getDouble("bid")).orElse(0.00))
                            .suggested(Optional.ofNullable(res.getDouble("suggested")).orElse(0.00))
                            .type("sp")
                            .targetType(res.getString("target_type"))
                            .build();
                }
            }, args.toArray());
        } finally {
            hintManager.close();
        }


    }

    /**
     * TODO 关联关键词库查询
     * 获取详情否定投放总数
     */
    public int aggregateNeKeywordCount(Integer puid, List<Integer> shopIds, String keywordText) {
        StringBuilder builder = new StringBuilder("SELECT COUNT(*) FROM `t_amazon_ad_ne_keyword` WHERE puid=? and `type`='negative' ");
        List<Object> argsList = Lists.newArrayList(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            builder.append(SqlStringUtil.dealInList("shop_id", shopIds, argsList));
        }
        if (StringUtils.isNotEmpty(keywordText)) {
            builder.append(" and keyword_text = ? ");
            argsList.add(keywordText);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            List<Integer> list = getJdbcTemplate(puid, hintManager).queryForList(builder.toString(), Integer.class, argsList.toArray());
            return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0;
        } finally {
            hintManager.close();
        }

    }

    @Override
    public void updateKeywordRank(Integer puid, Integer shopId, String keywordId, String advRank, Integer uid) {
        String sql = "update `t_amazon_ad_ne_keyword` set `adv_rank` = ?, update_time = now(3), update_id = ? " +
                "where puid = ? and shop_id = ? and keyword_id = ?";
        Object[] arg = new Object[]{advRank, uid, puid, shopId, keywordId};
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, arg);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public void strategyUpdate(int puid, int shopId, ScheduleTaskFinishedVo message) {
        String sql = "update `t_amazon_ad_ne_keyword` set bid=? where puid = ? and shop_id = ? and keyword_id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, message.getModifiedValue(), message.getPuid(), message.getShopId(), message.getItemId());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public void autoUpdate(int puid, int shopId, AdvertiseRuleTaskExecuteRecordV2Message message) {
        StringBuilder updateSql = new StringBuilder("update t_amazon_ad_ne_keyword ");
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id = ? and keyword_id = ?");
        List<Object> args = Lists.newArrayList();
        if ("restore".equals(message.getOperation().name())) {
            updateSql.append(" set bid=?");
            args.add(message.getRecoveryAdjustment().getExecuteValue());
        } else {
            if ("stateClose".equals(message.getPerformOperation().get(0).getRuleAction().name())) {
                updateSql.append(" set state=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            } else {
                updateSql.append(" set bid=?");
                args.add(message.getPerformOperation().get(0).getExecuteValue());
            }
        }
        updateSql.append(whereSql);
        args.add(message.getPuid());
        args.add(message.getShopId());
        args.add(message.getItemId());
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(message.getPuid(), hintManager).update(updateSql.toString(), args.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeKeyword> getKeywordsByGroupIdsAndKeywordTexts(Integer puid, List<String> groupIds, List<String> keywordTexts) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("ad_group_id", groupIds.toArray(new String[0]))
                .inStrList("keyword_text", keywordTexts.toArray(new String[0]))
                .build());
    }

    @Override
    public List<AmazonAdNeKeyword> getListKeywordByKeywordIds(Integer puid, List<Integer> shopIds, List<String> keywords) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIds.toArray(shopIds.toArray(new Integer[]{})))
                .inStrList("keyword_id", keywords.toArray(new String[]{}))
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdNeKeyword> getKeywordViewList(Integer puid, KeywordViewParam param) {
        StringBuilder selectSql = new StringBuilder("select * from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = getKeywordViewCondition(puid, param);
        builder.orderByDesc("data_update_time", "id");
        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), build.getValues(), BeanPropertyRowMapper.newInstance(AmazonAdNeKeyword.class));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page<AmazonAdNeKeyword> getKeywordViewPage(Integer puid, KeywordViewParam param) {
        StringBuilder selectSql = new StringBuilder("select * from " + getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("select count(*) from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = getKeywordViewCondition(puid, param);
        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql()).append(" order by data_update_time desc, id desc ");
        countSql.append(" where ").append(build.getSql());

        Object[] args = build.getValues();
        return getPageResult(puid, param.getPageNo(), param.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdNeKeyword.class);
    }

    @Override
    public List<KeywordViewAggregateDto> getKeywordViewAggregateList(Integer puid, KeywordViewParam param) {
        StringBuilder selectSql = new StringBuilder("select keyword_id as keywordId, keyword_text as keywordText from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = getKeywordViewCondition(puid, param);
        builder.orderByDesc("data_update_time", "id");
        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), build.getValues(), BeanPropertyRowMapper.newInstance(KeywordViewAggregateDto.class));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getDiagnoseCountKeywordId(DiagnoseCountParam param) {
        StringBuilder selectSql = new StringBuilder("select keyword_id from " + getJdbcHelper().getTable());
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", param.getPuid())
                .in("shop_id", param.getShopIdList().toArray())
                .equalTo("type", Constants.NEGATIVE); //sp否定关键词和非关键词在同一张表

        //广告组ID搜索
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            builder.in("ad_group_id", param.getAdGroupIdList().toArray());
        }

        ConditionBuilder build = builder.build();
        selectSql.append(" where ").append(build.getSql());
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(param.getPuid(), hintManager).query(selectSql.toString(), new SingleColumnRowMapper<String>(), build.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeKeyword> getListKeywordByQuery(Integer puid, Integer shopId, String adGroupId, String keywordText, String matchType, String type) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .equalTo("match_type", matchType)
                .equalTo("type", type)
                .equalTo("keyword_text", keywordText)
                .in("state", new Object[]{"enabled", "paused"})
                .build());
    }

    private ConditionBuilder.Builder getKeywordViewCondition(Integer puid, KeywordViewParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", param.getShopIdList().toArray())
                .equalTo("type", Constants.NEGATIVE); //sp否定关键词和非关键词在同一张表

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", list.toArray(new String[]{}));
        }
        //广告组ID搜索
        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            builder.in("ad_group_id", param.getAdGroupIdList().toArray());
        }
        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            List<String> matchTypeList = StringUtil.splitStr(param.getMatchType(), ",");
            builder.in("match_type", matchTypeList.toArray());
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonAdNeKeyword.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }
        }
        //关键词筛选
        if (StringUtils.isNotBlank(param.getKeywordText())) {
            builder.equalTo("keyword_text", param.getKeywordText());
        }

        return builder;
    }

    @Override
    public void keywordCardUpdate(int puid, AdvertiseAutoRuleExecuteRecord message) {
        String sql = "update `t_amazon_ad_ne_keyword` set bid=? where puid = ? and shop_id = ? and keyword_id=?";
        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql, message.getExecuteValue(), message.getPuid(), message.getShopId(), message.getItemId());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        if (shopId != null) {
            sql.append("and shop_id = ?");
            argsList.add(shopId);
        }
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public Page<KeywordPageVo> getKeywordPage(Integer puid, KeywordsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder(" select count(*) from ( select keyword_id ");
        StringBuilder sqlSb = new StringBuilder("select ")
                .append("k.id, k.puid, k.shop_id shopId, k.marketplace_id marketplaceId, k.keyword_id keywordId, k.ad_group_id adGroupId, k.campaign_id campaignId, k.profile_id profileId,")
                .append("k.keyword_text keywordText, k.match_type matchType, k.bid, k.state, k.serving_status servingStatus, k.range_start rangeStart, k.range_end rangeEnd, k.suggested,")
                .append("k.is_pricing isPricing, k.pricing_state pricingState, k.adv_rank advRank, k.data_update_time dataUpdateTime");
        StringBuilder sb = new StringBuilder(" from t_amazon_ad_ne_keyword k ");
        if ((StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))
                || (param.getUseAdvanced() && (param.getBidMin() != null || param.getBidMax() != null))) {
            sb.append(" join t_amazon_ad_group g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ");
        }
        sb.append(this.getKeywordPageWhereSql(puid, param, null, argsList));

        sqlSb.append(sb);
        sqlCountSb.append(sb).append(" ) c ");

        if (StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField())) {
            sqlSb.append(" order by IFNULL(k.bid, g.default_bid) ").append(OrderTypeEnum.desc.getType().equals(param.getOrderType()) ? "desc" : "").append(" ,k.id desc ");
        } else {
            sqlSb.append(" order by k.id desc ");
        }

        Object[] args = argsList.toArray();
        return getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), sqlCountSb.toString(), args, sqlSb.toString(), args, KeywordPageVo.class);
    }

    @Override
    public List<KeywordPageVo> getKeywordPageVoListByKeywordIdList(Integer puid, KeywordsPageParam param, List<String> keywordIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ")
                .append("k.id, k.puid, k.shop_id shopId, k.marketplace_id marketplaceId, k.keyword_id keywordId, k.ad_group_id adGroupId, k.campaign_id campaignId, k.profile_id profileId,")
                .append("k.keyword_text keywordText, k.match_type matchType, k.bid, k.state, k.serving_status servingStatus, k.range_start rangeStart, k.range_end rangeEnd, k.suggested,")
                .append("k.is_pricing isPricing, k.pricing_state pricingState, k.adv_rank advRank, k.data_update_time dataUpdateTime")
                .append(" from t_amazon_ad_ne_keyword k ");
        sb.append(" where k.puid = ? and k.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            sb.append(SqlStringUtil.dealInList("k.keyword_id", keywordIdList, argsList));
        }
        sb.append(" order by field(keyword_id, ").append(StringUtil.joinString(keywordIdList)).append(")");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(KeywordPageVo.class));
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<String> getKeywordIdListByParam(Integer puid, KeywordsPageParam param, List<String> keywordIdList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select keyword_id from t_amazon_ad_ne_keyword k ");
        if (param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null)) {
            sb.append(" join t_amazon_ad_group g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ");
        }
        sb.append(this.getKeywordPageWhereSql(puid, param, keywordIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).queryForList(sb.toString(), argsList.toArray(), String.class);
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AdOrderBo> getKeywordIdAndOrderFieldList(Integer puid, KeywordsPageParam param, List<String> keywordIdList, String orderField) {
        String orderByField = "";
        if (SqlStringReportUtil.BID.equals(orderField)) {
            orderByField = "IFNULL(k.bid, g.default_bid)";
        } else if (SqlStringReportUtil.ID.equals(orderField)) {
            orderByField = "id";
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select keyword_id id")
                .append(StringUtils.isNotBlank(orderByField) ? "," + orderByField + " orderField" : "")
                .append(" from t_amazon_ad_ne_keyword k ");
        if (SqlStringReportUtil.BID.equals(orderField) || (param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sb.append(" join t_amazon_ad_group g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ");
        }
        sb.append(this.getKeywordPageWhereSql(puid, param, keywordIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdOrderBo.class));
        } finally {
            hintManager.close();
        }

    }

    /**
     * 关键词投放列表页where条件sql拼接
     */
    private String getKeywordPageWhereSql(Integer puid, KeywordsPageParam param, List<String> keywordIdList, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where k.puid = ? and k.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(keywordIdList)) {
            sb.append(SqlStringUtil.dealInList("k.keyword_id", keywordIdList, argsList));
        }
        sb.append(" and k.type = ? ");
        argsList.add(Constants.NEGATIVE);//sp否定关键词和非关键词在同一张表

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            sb.append(SqlStringUtil.dealInList("k.campaign_id", StringUtil.splitStr(param.getCampaignId()), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealInList("k.campaign_id", param.getCampaignIdList(), argsList));
        }
        //广告组ID搜索
        if (StringUtils.isNotBlank(param.getGroupId())) {
            sb.append(SqlStringUtil.dealInList("k.ad_group_id", StringUtil.splitStr(param.getGroupId()), argsList));
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getKeywordIds())) {
            sb.append(SqlStringUtil.dealInList("k.keyword_id", param.getKeywordIds(), argsList));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            //该批量搜索条件默认为：name,不需要条件判断searchField值。
            if ("blur".equals(param.getSearchType())) { //模糊搜索
                sb.append(" and k.keyword_text like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue()) + "%");
            } else {//默认精确
                if (param.getListSearchValue().size() > 1) {
                    sb.append(SqlStringUtil.dealInList("k.keyword_text", param.getListSearchValue(), argsList));
                } else {
                    sb.append("and k.keyword_text = ? ");
                    argsList.add(param.getSearchValue().trim());
                }
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            sb.append(SqlStringUtil.dealInList("k.state", StringUtil.splitStr(param.getStatus()), argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                sb.append("  and k.serving_status = ? ");
                argsList.add(AmazonAdNeKeyword.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                sb.append(SqlStringUtil.dealInList("k.serving_status", StringUtil.splitStr(param.getServingStatus()), argsList));
            }

        }

        //匹配类型搜索
        if (StringUtils.isNotBlank(param.getMatchType())) {
            sb.append(" and k.match_type = ? ");
            argsList.add(param.getMatchType());
        }

        //竞价高级筛选
        if (param.getUseAdvanced()) {
            if (param.getBidMin() != null) {
                sb.append(" and IFNULL(k.bid, g.default_bid) >= ? ");
                argsList.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                sb.append(" and IFNULL(k.bid, g.default_bid) <= ? ");
                argsList.add(param.getBidMax());
            }
        }
        return sb.toString();
    }

    @Override
    public List<AdGroupTargetCountDto> countByGroupIdSet(Integer puid, Integer shopId, Set<String> groupIdSet) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", groupIdSet.toArray(new String[]{}))
                .equalTo("type", "negative")
                .groupBy("ad_group_id")
                .build();
        String sql = "select ad_group_id adGroupId, count(*) targetCount from " + getJdbcHelper().getTable() + " where " + conditionBuilder.getSql();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql, new BeanPropertyRowMapper<>(AdGroupTargetCountDto.class), conditionBuilder.getValues());
        } finally {
            hintManager.close();
        }

    }

    @Override
    public List<AmazonAdNeKeyword> listKeywordByGroupIdList(Integer puid, Integer shopId, List<String> groupIdList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .notEqualTo("type", "negative")
                .inStrList("ad_group_id", groupIdList.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdNeKeyword> listKeywordByGroupId(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .notEqualTo("type", "negative")
                .equalTo("ad_group_id", adGroupId)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public List<AmazonAdNeKeyword> listByGroupIdAndItemIdList(Integer puid, Integer shopId, String adGroupId, List<String> itemIdList) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid);
        builder.equalTo("shop_id", shopId);
        builder.notEqualTo("type", "negative");
        builder.equalTo("ad_group_id", adGroupId);
        if (CollectionUtils.isNotEmpty(itemIdList)) {
            builder.notIn("keyword_id", itemIdList.toArray());
        }
        builder.in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});
        return listByCondition(puid, builder.build());
    }

    @Override
    public List<SearchQueryTagParam> getSearchQueryTag(Integer puid, Integer shopId, List<String> matchTypeList, List<SearchQueryTagParam> queryTagParams) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select ")
                .append(" k.ad_group_id as adGroupId, k.campaign_id as campaignId, k.keyword_text as query")
                .append(" from t_amazon_ad_ne_keyword k ");
        sb.append(" where k.puid = ? and k.shop_id = ? and k.state != 'archived' ");
        argsList.add(puid);
        argsList.add(shopId);
        if (org.apache.commons.collections.CollectionUtils.isNotEmpty(matchTypeList)) {
            sb.append(SqlStringUtil.dealInList("k.match_type", matchTypeList, argsList));
        }
        if (CollectionUtils.isNotEmpty(queryTagParams)) {
            sb.append(" and (ad_group_id, keyword_text) in ");
            StringBuilder stringBuilder = new StringBuilder(" ( ");
            for (SearchQueryTagParam param : queryTagParams) {
                stringBuilder.append("( ?,? ),");
                argsList.add(param.getAdGroupId());
                argsList.add(param.getQuery());
            }
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
            stringBuilder.append(" )");
            sb.append(stringBuilder);
        }
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(SearchQueryTagParam.class));
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdNeKeyword> getListByTargetTaskCondition(Integer puid, Integer shopId, Set<String> adGroupIds, Set<String> keywordTexts, Set<String> matchTypes) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()})
                .in("keyword_text", keywordTexts.toArray())
                .equalTo("type", Constants.NEGATIVE)
                .in("match_type", matchTypes.toArray());
        return listByCondition(puid, builder.build());
    }

    @Override
    public Page<KeywordLibsDetailVo> getKeywordNeTargetList(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param, String keywordText) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select create_time, state, shop_id, marketplace_id, campaign_id, match_type,keyword_id ");
        List<Object> argList = new ArrayList<>();
        List<Object> argList2 = new ArrayList<>();

        StringBuilder whereSql = new StringBuilder(" where puid = ? ");
        StringBuilder whereSql2 = new StringBuilder(" where puid = ? ");

        argList.add(puid);
        argList2.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            whereSql.append(SqlStringUtil.dealInList("shop_id", shopIds, argList));
            whereSql2.append(SqlStringUtil.dealInList("shop_id", shopIds, argList2));
        }

        whereSql.append(" and keyword_text = ? ");
        whereSql2.append(" and keyword_text = ? ");

        argList.add(keywordText);
        argList2.add(keywordText);

        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getSearchCampaignList(), argList));
            whereSql2.append(SqlStringUtil.dealInList("campaign_id", param.getSearchCampaignList(), argList2));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getSearchAdGroupList(), argList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchMatchTypeList())) {
            whereSql.append(SqlStringUtil.dealInList("match_type", param.getSearchMatchTypeList(), argList));
            whereSql2.append(SqlStringUtil.dealInList("match_type", param.getSearchMatchTypeList(), argList2));
        }
        StringBuilder sb = new StringBuilder()
                .append(" select create_time createTime, state, shop_id shopId,keyword_id keywordId, campaign_id campaignId, marketplace_id marketplaceId, ad_group_id adGroupId, match_type matchType, type from (")
                .append(selectSql).append(" , '' as ad_group_id,'sp' as type from t_amazon_ad_campaign_nekeywords ").append(whereSql2);
        argsList.addAll(argList2);
        sb.append(" UNION ALL ").append(selectSql).append(" , ad_group_id, 'sp' as type from ").append(this.getJdbcHelper().getTable()).append(whereSql);
        argsList.addAll(argList);
        sb.append(" UNION ALL ").append(selectSql).append(" , ad_group_id, 'sb' as type from t_amazon_ad_nekeyword_sb ").append(whereSql);
        argsList.addAll(argList);
        sb.append(") c ");
        StringBuilder countSb = new StringBuilder("select count(*) from (").append(sb).append(") d");
        sb.append(" order by keyword_id desc ");
        Object[] array = argsList.toArray();
        return this.getPageResultByClass(puid, param.getPageNo(), param.getPageSize(), countSb.toString(), array, sb.toString(), array, KeywordLibsDetailVo.class);
    }

    @Override
    public Page<AmazonAdNeKeyword> listPageByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildSql(puid,  shopIds, param, "t_amazon_ad_ne_keyword", args);
        String sbSql = buildSql(puid,  shopIds, param, "t_amazon_ad_nekeyword_sb", args);
        String sbcSql = buildSql(puid,  shopIds, param, "t_amazon_ad_campaign_nekeywords", args);
        String totalSql = spSql + " union " + sbSql + " union " + sbcSql;
        String countSql = " select count(*)  from ( " + totalSql + " ) r";
        String selectSql = " select shop_id, campaign_id from ( " + totalSql + " ) r order by campaign_id desc";
        Object[] arg = args.toArray();
        return getPageResult(puid, param.getPageNo(), param.getPageSize(), countSql, arg, selectSql, arg, AmazonAdNeKeyword.class);
    }

    @Override
    public List<AmazonAdNeKeyword> listAllCampaignByCondition(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param) {
        List<Object> args = new ArrayList<>();
        String spSql = buildSql(puid,  shopIds, param, "t_amazon_ad_ne_keyword", args);
        String sbSql = buildSql(puid,  shopIds, param, "t_amazon_ad_nekeyword_sb", args);
        String sbcSql = buildSql(puid,  shopIds, param, "t_amazon_ad_campaign_nekeywords", args);
        String totalSql = spSql + " union " + sbSql + " union " + sbcSql;
        String selectSql = " select shop_id, campaign_id from ( " + totalSql + " ) r ";
        Object[] arg = args.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql, arg, new ObjectMapper<>(AmazonAdNeKeyword.class));
        } finally {
            hintManager.close();
        }
    }

    private String buildSql(Integer puid, List<Integer> shopIds, KeywordLibsPageParam param, String tableName, List<Object> args) {
        StringBuilder sbSql = new StringBuilder("select shop_id, campaign_id from " + tableName +
                " where puid = ? ");
        args.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)) {
            sbSql.append(SqlStringUtil.dealInList("shop_id",shopIds, args));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchCampaignList())) {
            sbSql.append(SqlStringUtil.dealInList("campaign_id",param.getSearchCampaignList(), args));
        }
        if (StringUtils.isNotEmpty(param.getKeywordText())) {
            sbSql.append(" and keyword_text = ? ");
            args.add(param.getKeywordText().toLowerCase());
        }
        sbSql.append(" group by campaign_id,shop_id ");
        return sbSql.toString();
    }

    @Override
    public List<KeywordLibsVo> getSpNeKeywordCount(Integer puid, List<String> shopIds, KeywordLibsPageParam param, List<String> keywordTexts) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select keyword_text keywordText, count(*) negateTargetNum from t_amazon_ad_ne_keyword " +
                "where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sql.append(SqlStringUtil.dealInList("shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if(CollectionUtils.isNotEmpty(param.getCountry())){
            sql.append(SqlStringUtil.dealInList("marketplace_id", param.getCountry(), argsList));
        }
        if (CollectionUtils.isNotEmpty(keywordTexts)){
            sql.append(SqlStringUtil.dealInList("keyword_text",keywordTexts,argsList));
        }
        sql.append(" group by keyword_text");
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), new ObjectMapper<>(KeywordLibsVo.class), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<AmazonAdNeKeyword> listByKeywordText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList) {
        List<Object> args = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select id, keyword_id, keyword_text, campaign_id, ad_group_id, match_type," +
                " state from t_amazon_ad_ne_keyword where state != 'archived' and puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        sql.append(SqlStringUtil.dealMultiColumnIn(Lists.newArrayList("ad_group_id", "keyword_text COLLATE utf8mb4_bin"), infoList,
                Lists.newArrayList(
                        NegativeArchiveRequest.NegativeInfo::getAdGroupId,
                        NegativeArchiveRequest.NegativeInfo::getNegativeText
                ),
                args));
        Object[] arg = args.toArray();
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(sql.toString(), arg, new ObjectMapper<>(AmazonAdNeKeyword.class));
        } finally {
            hintManager.close();
        }
    }
}