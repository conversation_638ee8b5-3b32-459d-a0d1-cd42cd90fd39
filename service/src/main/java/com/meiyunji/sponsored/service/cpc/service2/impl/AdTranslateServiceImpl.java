package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.amazon.advertising.localization.keyword.KeywordsLocalizationClient;
import com.amazon.advertising.localization.keyword.KeywordsLocalizationResponse;
import com.amazon.advertising.localization.keyword.SourceDetail;
import com.amazon.advertising.localization.keyword.TargetDetail;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.IAdTranslateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-06 19:47
 */
@Service
@Slf4j
public class AdTranslateServiceImpl implements IAdTranslateService {

    @Autowired
    private ISlaveScVcShopAuthDao slaveShopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Override
    public Map<String, String> translate(Integer puid, Integer shopId, String marketplaceId, List<String> keywordList) {
        ShopAuth shopAuth = slaveShopAuthDao.getValidAdShopById(puid, shopId);
        if (shopAuth == null) {
            log.info("translate fail. no shop auth. puid:{} shopId:{}", puid, shopId);
            return Collections.emptyMap();
        }
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopAuth.getId());
        if (profile == null) {
            log.info("translate fail. no profile. puid:{} shopId:{}", puid, shopId);
            return Collections.emptyMap();
        }
        String accessToken = shopAuthService.getAdToken(shopAuth);
        SourceDetail sourceDetail = new SourceDetail();
        sourceDetail.setMarketplaceId(shopAuth.getMarketplaceId());
        TargetDetail targetDetail = new TargetDetail();
        targetDetail.setLocales(Collections.singletonList("zh_CN"));
        KeywordsLocalizationResponse response = KeywordsLocalizationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getKeywordsLocalize(accessToken, profile.getProfileId(),
                shopAuth.getMarketplaceId(), keywordList, sourceDetail, targetDetail);
        if (response.getStatusCode() != null && response.getStatusCode() == 200 && response.getResult() != null && response.getResult().getLocalizedKeywordResponses() != null) {
            Map<String, String> map = new HashMap<>();
            response.getResult().getLocalizedKeywordResponses().forEach(e -> {
                if (!map.containsKey(e.getSourceKeyword().getKeyword())) {
                    if (e.getSourceKeyword() != null && e.getSourceKeyword().getKeyword() != null &&
                            e.getLocalizedKeywordResults() != null && e.getLocalizedKeywordResults().get("zh_CN") != null && e.getLocalizedKeywordResults().get("zh_CN").getKeyword() != null) {
                        map.put(e.getSourceKeyword().getKeyword(), e.getLocalizedKeywordResults().get("zh_CN").getKeyword().getKeyword());
                    } else {
                        log.info("single word translate fail. execute word root translators task return error e:{}", JSONUtil.objectToJson(e));
                    }
                }
            });
            return map;
        } else {
            log.info("translate fail.Request amazon sponsored product keyword localization api with an error. execute word root translators task {}@{} status: {} message: {}",
                    puid, shopAuth.getId(), response.getStatusCode(), response.getStatusMessage());
            return Collections.emptyMap();
        }
    }
}
