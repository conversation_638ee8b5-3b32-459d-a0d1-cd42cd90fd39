package com.meiyunji.sponsored.service.multiPlatform.walmart.util;


import cn.hutool.core.util.NumberUtil;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.exception.SponsoredBizException;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdReportBaseDTO;
import com.meiyunji.sponsored.service.multiPlatform.walmart.enums.WalmartAdCampaignStatusEnum;
import com.meiyunji.sponsored.service.multiPlatform.walmart.enums.WalmartAdErrorMsgEnum;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.IBaseWalmartAdReportDetail;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description
 */
public class WalmartAdvetisingUtil {
    private static Logger logger = LoggerFactory.getLogger(WalmartAdvetisingUtil.class);

    private static WalmartAdvetisingUtil instance;

    private WalmartAdvetisingUtil(){};

    public static WalmartAdvetisingUtil getInstance(){
        synchronized (WalmartAdvetisingUtil.class){
            if(null == WalmartAdvetisingUtil.instance){
                instance = new WalmartAdvetisingUtil();
            }
            return instance;
        }
    }

    public String getSnapshotJsonStr(String detailsStr) {
        StringBuilder jsonResponse = new StringBuilder();
        String json = "";
        try {
            URL url = new URL(detailsStr);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            // 设置接受gzip压缩的内容
            connection.setRequestProperty("Accept-Encoding", "gzip");
            // 发起请求
            connection.connect();
            // 检查响应代码
            if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                // 获取输入流，如果内容是gzip压缩的，则自动解压
                InputStream inputStream = connection.getInputStream();
                if ("gzip".equals(connection.getContentEncoding())) {
                    inputStream = new GZIPInputStream(inputStream);
                }
                // 读取输入流
                BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8));
                String line;
                while ((line = reader.readLine()) != null) {
                    jsonResponse.append(line);
                }
                // 关闭连接和流
                reader.close();
                inputStream.close();
                connection.disconnect();
                // 输出JSON字符串
                json = jsonResponse.toString();
                logger.info("Walmart:::snapshot:::json:::" + json);
            } else {
                logger.error("Walmart:::snapshot:::Error connecting to the server");
            }
        } catch (Exception e) {
            logger.error("Walmart:::snapshot:::", e);
        }
        return json;
    }

    public static void convertReportVal(WalmartAdReportBaseDTO reportDto) {
        reportDto.setAdCost(Optional.ofNullable(reportDto.getAdCost())
                .map(Object::toString).map(BigDecimal::new)
                .map(d -> d.setScale(2, RoundingMode.HALF_UP)).map(BigDecimal::doubleValue).orElse(0.0D));
        reportDto.setClicks(Optional.ofNullable(reportDto.getClicks()).orElse(0));
        reportDto.setImpressions(Optional.ofNullable(reportDto.getImpressions()).orElse(0));
        reportDto.setAdSale(Optional.ofNullable(reportDto.getAdSale())
                .map(Object::toString).map(BigDecimal::new)
                .map(d -> d.setScale(2, RoundingMode.HALF_UP)).map(BigDecimal::doubleValue).orElse(0.0D));
        reportDto.setAdOrderNum(Optional.ofNullable(reportDto.getAdOrderNum()).orElse(0));
        reportDto.setAdSaleNum(Optional.ofNullable(reportDto.getAdSaleNum()).orElse(0));
        calculationRate(reportDto);
    }

    public static void calculationRate(IBaseWalmartAdReportDetail report) {
        if (report == null) {
            return;
        }
        report.setAdCostPerClick(0.0);
        report.setAcos(0.0);
        report.setCtr(0.0);
        report.setCvr(0.0);
        //平均点击费用
        if (Objects.nonNull(report.getAdCost()) && Objects.nonNull(report.getClicks()) &&
                NumberUtil.isValid(report.getAdCost()) && NumberUtil.isValid(report.getClicks())) {
            report.setAdCostPerClick(report.getAdCost() == 0.0D? 0: DoubleUtil.divide(report.getAdCost(), report.getClicks().doubleValue(),2));
        }

        // 广告花费 / 销售额
        if (Objects.nonNull(report.getAdCost()) && Objects.nonNull(report.getAdSale()) &&
                NumberUtil.isValid(report.getAdCost()) && NumberUtil.isValid(report.getAdSale())) {
            report.setAcos(report.getAdSale() == 0.0D? 0: DoubleUtil.divide(report.getAdCost(), report.getAdSale(), 4));
            report.setAcos(DoubleUtil.round(report.getAcos() * 100, 2));
        }
        // 点击率
        if (Objects.nonNull(report.getClicks()) && Objects.nonNull(report.getImpressions()) &&
                NumberUtil.isValid(report.getClicks()) && NumberUtil.isValid(report.getImpressions())) {
            report.setCtr(report.getClicks() == 0? 0: DoubleUtil.divide(report.getClicks().doubleValue(), report.getImpressions().doubleValue(), 4));
            report.setCtr(DoubleUtil.round(report.getCtr() * 100, 2));
        }
        //订单转换率
        if (Objects.nonNull(report.getAdOrderNum()) && Objects.nonNull(report.getClicks()) &&
                NumberUtil.isValid(report.getAdOrderNum()) && NumberUtil.isValid(report.getClicks())) {
            report.setCvr(report.getAdOrderNum() == 0? 0: DoubleUtil.divide(report.getAdOrderNum().doubleValue(), report.getClicks().doubleValue(), 4));
            report.setCvr(DoubleUtil.round(report.getCvr() * 100, 2));
        }
    }

    public static void calculationRate(WalmartAdReportBaseDTO report) {
        if (report == null) {
            return;
        }
        report.setAdCostPerClick(0.0);
        report.setAcos(0.0);
        report.setCtr(0.0);
        report.setCvr(0.0);
        //平均点击费用
        if (Objects.nonNull(report.getAdCost()) && Objects.nonNull(report.getClicks()) &&
                NumberUtil.isValid(report.getAdCost()) && NumberUtil.isValid(report.getClicks()) && report.getClicks() != 0) {
            report.setAdCostPerClick(report.getAdCost() == 0.0D? 0: DoubleUtil.divide(report.getAdCost(), report.getClicks().doubleValue(),2));
        }

        // 广告花费 / 销售额
        if (Objects.nonNull(report.getAdCost()) && Objects.nonNull(report.getAdSale()) &&
                NumberUtil.isValid(report.getAdCost()) && NumberUtil.isValid(report.getAdSale()) && report.getAdSale() != 0.0D) {
            report.setAcos(report.getAdSale() == 0.0D? 0: DoubleUtil.divide(report.getAdCost(), report.getAdSale(), 4));
            report.setAcos(DoubleUtil.round(report.getAcos() * 100, 2));
        }
        // 点击率
        if (Objects.nonNull(report.getClicks()) && Objects.nonNull(report.getImpressions()) &&
                NumberUtil.isValid(report.getClicks()) && NumberUtil.isValid(report.getImpressions()) && report.getImpressions() != 0) {
            report.setCtr(report.getClicks() == 0? 0: DoubleUtil.divide(report.getClicks().doubleValue(), report.getImpressions().doubleValue(), 4));
            report.setCtr(DoubleUtil.round(report.getCtr() * 100, 2));
        }
        //订单转换率
        if (Objects.nonNull(report.getAdOrderNum()) && Objects.nonNull(report.getClicks()) &&
                NumberUtil.isValid(report.getAdOrderNum()) && NumberUtil.isValid(report.getClicks()) && report.getClicks() != 0) {
            report.setCvr(report.getAdOrderNum() == 0? 0: DoubleUtil.divide(report.getAdOrderNum().doubleValue(), report.getClicks().doubleValue(), 4));
            report.setCvr(DoubleUtil.round(report.getCvr() * 100, 2));
        }
    }

    //处理环比计算
    public static Double chainGrowthEquation(Double a, Double b) {
        //环比计算公式 (当前周期-上一周期)/上一周期*100%
        Double result = 0.0;
        if (a == null) {
            a = 0.0;
        }
        if (b == null || b == 0) {
            if (a == 0) {
                return 0d;
            }
            return 100d;
        }
        result = DoubleUtil.round(DoubleUtil.mul(DoubleUtil.divide(DoubleUtil.sub(a, b), b), 100d), 2);
        return result;
    }

    //处理占比计算
    public static Double calculationRatio(Double a, Double b) {
        Double result = 0.0;
        if (a == null || a == 0) {
            return 0d;
        }
        if (b == null || b == 0) {
            return 0d;
        }
        result = DoubleUtil.round(DoubleUtil.mul(DoubleUtil.divide(a, b), 100d), 2);
        return result;
    }

    public static List<WalmartAdErrorMsgEnum> getErrorEnList(String error) {
        if (StringUtils.isBlank(error)) {
            return Collections.emptyList();
        }
        String[] split = error.split("\\|");
        return Arrays.stream(split).map(e ->
                WalmartAdErrorMsgEnum.WALMART_AD_ERROR_MAP.entrySet().stream()
                        .filter(entry -> StringUtils.contains(e, entry.getKey()))
                        .findFirst().map(Map.Entry::getValue).orElse(null)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static String getErrorStr(String error) {
        if (StringUtils.isBlank(error)) {
            return "";
        }
        StringBuilder errorStr = new StringBuilder();
        String[] split = error.split("\\|");
        for (String s : split) {
            if (StringUtils.isBlank(s)) {
                continue;
            }
            if (StringUtils.contains(s, "startTime - Start Time should be today or in future.")) {
                errorStr.append("开始时间最早仅支持选择当前时间-1天");
            } else if (StringUtils.contains(s, " Unique campaign name required.")) {
                errorStr.append("活动名称重复，请修改活动名称后再提交");
            } else if (StringUtils.contains(s, "totalBudget - Total Budget cannot be more than $1000000000.0.")) {
                errorStr.append("总预算不能超过1000000000USD，请修改总预算");
            } else if (StringUtils.contains(s, "totalBudget - Total budget cannot be less than the Daily Budget.")) {
                errorStr.append("总预算不能低于每日预算");
            } else if (StringUtils.contains(s, "dailyBudget - Daily Budget cannot be more than $1000000000.0")) {
                errorStr.append("每日预算不能超过1000000000USD，请修改每日预算");
            } else if (StringUtils.contains(s, "Given ad group does not have any items. Please add items to get the keyword")) {
                errorStr.append("推荐关键词获取失败，请提交广告产品后再获取");
            } else if (StringUtils.contains(s, "cannot be deleted as it has been scheduled before")) {
                errorStr.append("当前状态不支持删除，可在活动结束后删除广告活动");
            } else if (StringUtils.contains(s, "Bid cannot be greater than 100.0")) {
                errorStr.append("竞价不能大于100 USD");
            } else if (StringUtils.contains(s, "cannot be deleted as the campaign has been live before")) {
                errorStr.append("当前状态不支持删除，广告活动未结束");
            } else if (StringUtils.contains(s, "Item not found, Input id: null")) {
                errorStr.append("无产品ID的产品不支持参加广告，请同步产品后再加入广告");
            } else if (StringUtils.contains(s, "Keyword Text length greater than 80 / ")) {
                errorStr.append("关键词长度不能超过80");
            } else if (StringUtils.contains(s, "AdGroup not found.")) {
                errorStr.append("广告组不存在，请点击数据同步按钮进行同步");
            } else if (StringUtils.contains(s, "Campaign not found.")) {
                errorStr.append("广告活动不存在，请点击数据同步按钮进行同步");
            } else if (StringUtils.contains(s, "item or base item not owned by you") || StringUtils.contains(s, "Item not found")) {
                int starIndex = s.indexOf("id");
                if (starIndex != -1) {
                    errorStr.append("产品").append(s.substring(starIndex)).append(",");
                }
                errorStr.append("请前往在线产品同步更新产品状态");
            } else {
                errorStr.append(s);
            }
            errorStr.append(";");
        }
        return errorStr.toString();
    }

    public static String getErrorMsgByEnum(WalmartAdErrorMsgEnum errorEn) {
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        sb.append("{\"field\":").append("\"").append(errorEn.getFieldCn()).append("\",");
        sb.append("\"msg\":").append("\"").append(errorEn.getErrorMsgCn()).append("\"}");
        sb.append("]");
        return sb.toString();
    }
    public static String getErrorMsgByEnum(List<WalmartAdErrorMsgEnum> errorEnList) {
        if (CollectionUtils.isEmpty(errorEnList)) {
            return "";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        errorEnList.forEach(en -> {
            sb.append("{\"field\":").append("\"").append(en.getFieldCn()).append("\",");
            sb.append("\"msg\":").append("\"").append(en.getErrorMsgCn()).append("\"}");
            sb.append(",");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append("]");
        return sb.toString();
    }

    public static String getErrorMsgByMsg(String msg) {
        List<WalmartAdErrorMsgEnum> errorEnList = getErrorEnList(msg);
        if (CollectionUtils.isEmpty(errorEnList)) {
            errorEnList.add(WalmartAdErrorMsgEnum.CAMPAIGN_CREATE_ERROR_DEFAULT);
        }
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        errorEnList.forEach(en -> {
            sb.append("{\"field\":").append("\"").append(en.getFieldCn()).append("\",");
            sb.append("\"msg\":").append("\"").append(en.getErrorMsgCn()).append("\"}");
            sb.append(",");
        });
        sb.deleteCharAt(sb.length() - 1);
        sb.append("]");
        return sb.toString();
    }
}
