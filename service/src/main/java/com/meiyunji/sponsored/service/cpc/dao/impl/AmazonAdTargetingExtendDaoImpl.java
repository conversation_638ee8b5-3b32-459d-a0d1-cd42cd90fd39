package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingSphereDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdTargetingExtendDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargetingExtend;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetExtendInfo;
import org.apache.shardingsphere.infra.hint.HintManager;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * AmazonAdKeywordReport
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdTargetingExtendDaoImpl extends BaseShardingSphereDaoImpl<AmazonAdTargetingExtend> implements IAmazonAdTargetingExtendDao {


    @Override
    public void insertList(Integer puid, List<AmazonAdTargetingExtend> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" (`puid`,`shop_id`,`marketplace_id`,`campaign_id`,`ad_group_id`,")
                .append("`target_id`,`estimated_impression_upper`,`estimated_impression_lower`,`create_time`,`update_time` ) VALUES");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdTargetingExtend targetingExtend : list) {
            sql.append(" (?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(targetingExtend.getShopId());
            argsList.add(targetingExtend.getMarketplaceId());
            argsList.add(targetingExtend.getCampaignId());
            argsList.add(targetingExtend.getAdGroupId());
            argsList.add(targetingExtend.getTargetId());
            argsList.add(targetingExtend.getEstimatedImpressionUpper());
            argsList.add(targetingExtend.getEstimatedImpressionLower());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `estimated_impression_upper`=values(estimated_impression_upper),`estimated_impression_lower`=values(estimated_impression_lower)");

        HintManager hintManager = HintManager.getInstance();
        try {
            getJdbcTemplate(puid, hintManager).update(sql.toString(), argsList.toArray());
        } finally {
            hintManager.close();
        }
    }


    @Override
    public List<AmazonAdTargetingExtend> selectByShopIdAndTargetIdList(Integer puid, Integer shopId, List<String> targetIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT * from " + getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);
        selectSql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), getRowMapper());
        } finally {
            hintManager.close();
        }
    }

    @Override
    public List<TargetExtendInfo> selectByShopIdAndKeywordIdList(Integer puid, List<Integer> shopIdList, List<String> targetIdList) {
        StringBuilder selectSql = new StringBuilder("SELECT target_id targetId, estimated_impression_upper estimatedImpressionUpper, estimated_impression_lower estimatedImpressionLower from " + getJdbcHelper().getTable());
        List<Object> argsList = Lists.newArrayList();
        selectSql.append(" where puid = ? ");
        argsList.add(puid);
        selectSql.append(SqlStringUtil.dealInList("shop_id", shopIdList, argsList));
        selectSql.append(SqlStringUtil.dealInList("target_id", targetIdList, argsList));
        HintManager hintManager = HintManager.getInstance();
        try {
            return getJdbcTemplate(puid, hintManager).query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetExtendInfo.class));
        } finally {
            hintManager.close();
        }
    }
}