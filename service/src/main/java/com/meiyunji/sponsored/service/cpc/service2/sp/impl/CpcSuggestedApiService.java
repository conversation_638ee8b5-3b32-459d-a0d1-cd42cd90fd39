package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.amazon.advertising.keywords.suggested.GetAdGroupSuggestedKeywordsResponse;
import com.amazon.advertising.keywords.suggested.SuggestedKeywordsClient;
import com.amazon.advertising.mode.keywords.MatchTypeEnum;
import com.amazon.advertising.mode.keywords.SuggestedBid;
import com.amazon.advertising.mode.keywords.SuggestedKeyword;
import com.amazon.advertising.mode.targeting.Expression;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.amazon.advertising.mode.targeting.TargetingBidRecommendation;
import com.amazon.advertising.targeting.ListTargetingBidRecommendationsResponse;
import com.amazon.advertising.targeting.ProductTargetingClient;
import com.amazon.advertising.targeting.TargetingBidRecommendationsResponse;
import com.amazon.advertising.targeting.mode.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.SuggestedKeywordVo;
import com.meiyunji.sponsored.service.cpc.vo.SuggestedTargetVo;

import com.meiyunji.sponsored.service.enums.SpCategoryBidV3ConvertV5Enum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/4/15.
 * 获取建议关键词，建议竞价
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSuggestedApiService {

    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    /**
     * 取建议关键词
     */
    public Result<List<SuggestedKeyword>> getAdGroupSuggestedKeywords(ShopAuth shop, AmazonAdGroup amazonAdGroup) {
        if (shop == null) {
            log.error("取关键词的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取关键词的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        GetAdGroupSuggestedKeywordsResponse response = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getAdGroupSuggestedKeywordsResponse(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(), shop.getMarketplaceId(),
                Long.valueOf(amazonAdGroup.getAdGroupId()), 100, null);
        if (response != null
                && response.getStatusCode() != null
                && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getAdGroupSuggestedKeywordsResponse(shopAuthService.getAdToken(shop), amazonAdGroup.getProfileId(), shop.getMarketplaceId(),
                    Long.valueOf(amazonAdGroup.getAdGroupId()), 100, null);
        }

        if (response == null || response.getResult() == null) {
            log.error("getSuggestBidByKeywords,amazon接口返回空");
            return ResultUtil.error(3, "网络延迟，请稍后重试");
        }
        if (StringUtils.isNotBlank(response.getResult().getCode())) {
            return ResultUtil.error(3, response.getResult().getDetails());
        }
        if (response.getStatusCode() != 200) {
            log.error("getSuggestBidByKeywords,调用amazon接口报错");
            return ResultUtil.error(3, "网络延迟，请稍后重试");
        }

        return ResultUtil.success(response.getResult().getSuggestedKeywords());
    }

    /**
     * 取关键词的建议竞价
     * 接口要求：每次指定的关键词数量不能超过10个
     * @param keywordlist：
     */
    public Result getSuggestBidForKeywords(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<SuggestedKeywordVo> keywordlist) {
        if (CollectionUtils.isEmpty(keywordlist)) {
            log.error("取关键词的建议竞价--keywordlist is null");
            return ResultUtil.error();
        }
        if (shop == null) {
            log.error("取关键词的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取关键词的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        List<List<Expression>> expressios = keywordlist.stream().map(e -> {
            Expression expression = new Expression();
            expression.setValue(e.getKeywordText());
            expression.setType(mapMatchType(e.getMatchType()));
            return Lists.newArrayList(expression);
        }).collect(Collectors.toList());

        Result<Map<Map<String, String>, SuggestedBid>> result = getSuggestBid(shop, amazonAdGroup, expressios);
        if (result.success()) {
            Map<Map<String, String>, SuggestedBid> suggestedBidMap = result.getData();
            if (suggestedBidMap != null && suggestedBidMap.size() > 0) {
                SuggestedBid suggestedBid;
                Map<String, String> expressionMap = new HashMap<>(1);
                for (SuggestedKeywordVo keyword : keywordlist) {
                    expressionMap.clear();
                    expressionMap.put(mapMatchType(keyword.getMatchType()), keyword.getKeywordText());
                    suggestedBid = suggestedBidMap.get(expressionMap);
                    if (suggestedBid != null) {
                        if (suggestedBid.getSuggested() != null) {
                            keyword.setSuggested(suggestedBid.getSuggested().toString());
                        }
                        if (suggestedBid.getRangeStart() != null) {
                            keyword.setRangeStart(suggestedBid.getRangeStart().toString());
                        }
                        if (suggestedBid.getRangeEnd() != null) {
                            keyword.setRangeEnd(suggestedBid.getRangeEnd().toString());
                        }
                    }
                }
            }
        }

        return result;
    }

    /**
     * 取关键词的建议竞价V5版本
     * 可以传100个投放关键词
     * @param keywordlist：
     */
    public Result getSuggestBidForKeywordsV5(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<SuggestedKeywordVo> keywordlist) {
        if (CollectionUtils.isEmpty(keywordlist)) {
            log.error("取关键词的建议竞价--keywordlist is null");
            return ResultUtil.error();
        }
        if (shop == null) {
            log.error("取关键词的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取关键词的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        List<TargetingExpression> expressions = new ArrayList<>();
        for (SuggestedKeywordVo e : keywordlist) {
            BidRecommendationExpressionEnumV5 enumV5 = BidRecommendationExpressionEnumV5.typeMap.get(e.getMatchType());
            if (Objects.nonNull(enumV5)) {
                TargetingExpression expression = new TargetingExpression();
                expression.setType(enumV5.getBidTargetingExpressionType());
                expression.setValue(e.getKeywordText());
                e.setExpressionMark(expression.getType() + "-" + expression.getValue());
                expressions.add(expression);
            }
        }

        if (CollectionUtils.isEmpty(expressions)) {
            log.error("取关键词的建议竞价--转换获取建议竞价表达式为空, keywordSuggest: {}", keywordlist);
            return ResultUtil.error();
        }

        Result<Map<String, Pair<SuggestedBid, SuggestedBidImpactMetrics>>> result = getSuggestBidV5(shop, amazonAdGroup, expressions);
        if (!result.success() || MapUtils.isEmpty(result.getData())) {
            return result;
        }

        Map<String, Pair<SuggestedBid, SuggestedBidImpactMetrics>> suggestedBidMap = result.getData();
        for (SuggestedKeywordVo suggest : keywordlist) {
            if (StringUtils.isBlank(suggest.getExpressionMark())) {
                continue;
            }
            Pair<SuggestedBid, SuggestedBidImpactMetrics> pair = suggestedBidMap.get(suggest.getExpressionMark());
            if (Objects.isNull(pair)) {
                continue;
            }
            SuggestedBid suggestedBid = pair.getLeft();
            if (suggestedBid != null) {
                if (suggestedBid.getSuggested() != null) {
                    suggest.setSuggested(suggestedBid.getSuggested().toString());
                }
                if (suggestedBid.getRangeStart() != null) {
                    suggest.setRangeStart(suggestedBid.getRangeStart().toString());
                }
                if (suggestedBid.getRangeEnd() != null) {
                    suggest.setRangeEnd(suggestedBid.getRangeEnd().toString());
                }
            }
            SuggestedBidImpactMetrics metrics = pair.getRight();
            if (metrics != null) {
                if (metrics.getEstimatedImpressionLower() != null) {
                    suggest.setEstimatedImpressionLower(metrics.getEstimatedImpressionLower());
                }
                if (metrics.getEstimatedImpressionUpper() != null) {
                    suggest.setEstimatedImpressionUpper(metrics.getEstimatedImpressionUpper());
                }
            }
        }

        return result;
    }

    /**
     * 取投放的建议竞价
     *
     * @param targetVos：
     */
    public Result getSuggestBidForTarget(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<SuggestedTargetVo> targetVos) {
        if (CollectionUtils.isEmpty(targetVos)) {
            log.error("取关键词的建议竞价--keywordlist is null");
            return ResultUtil.error();
        }
        if (shop == null) {
            log.error("取关键词的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取关键词的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        List<List<Expression>> expressios = targetVos.stream().map(e -> Lists.newArrayList(e.getExpression())).collect(Collectors.toList());

        Result<Map<Map<String, String>, SuggestedBid>> result = getSuggestBid(shop, amazonAdGroup, expressios);
        if (result.success()) {
            Map<Map<String, String>, SuggestedBid> suggestedBidMap = result.getData();
            if (suggestedBidMap != null && suggestedBidMap.size() > 0) {
                SuggestedBid suggestedBid;
                Map<String, String> expressionMap = new HashMap<>(1);
                for (SuggestedTargetVo target : targetVos) {
                    expressionMap.clear();
                    target.getExpression().forEach((e) -> expressionMap.put(e.getType(), e.getValue()));
                    suggestedBid = suggestedBidMap.get(expressionMap);
                    if (suggestedBid != null) {
                        if (suggestedBid.getSuggested() != null) {
                            target.setSuggested(suggestedBid.getSuggested().toString());
                        }
                        if (suggestedBid.getRangeStart() != null) {
                            target.setRangeStart(suggestedBid.getRangeStart().toString());
                        }
                        if (suggestedBid.getRangeEnd() != null) {
                            target.setRangeEnd(suggestedBid.getRangeEnd().toString());
                        }
                    }
                }
            }
        }

        return result;
    }

    /**
     * 取投放的建议竞价
     *
     * @param targetVos：
     */
    public Result getSuggestBidForTargetV5(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<SuggestedTargetVo> targetVos) {
        if (CollectionUtils.isEmpty(targetVos)) {
            log.error("取投放的建议竞价--targetVos is null");
            return ResultUtil.error();
        }
        if (shop == null) {
            log.error("取投放的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取投放的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        List<TargetingExpression> expressions = new ArrayList<>();
        Set<String> distinctAsinSet = new HashSet<>();
        for (SuggestedTargetVo e : targetVos) {
            List<Expression> targetExpressionList = e.getExpression();
            //自动，未细化品类，asin（针对asin需要去重下，因为该接口不区分精准和扩展）
            if (targetExpressionList.size() == 1) {
                Expression targetExpression = targetExpressionList.get(0);
                BidRecommendationExpressionEnumV5 enumV5 = BidRecommendationExpressionEnumV5.typeMap.get(targetExpression.getType());
                if (Objects.nonNull(enumV5)) {
                    if (BidRecommendationExpressionEnumV5.PAT_ASIN.equals(enumV5)) {
                        String asinExpression = BidRecommendationExpressionEnumV5.PAT_ASIN + "-" + targetExpression.getValue();
                        e.setExpressionMark(asinExpression);
                        distinctAsinSet.add(asinExpression);
                    } else {
                        TargetingExpression bidExpression = new TargetingExpression();
                        bidExpression.setType(enumV5.getBidTargetingExpressionType());
                        bidExpression.setValue(targetExpression.getValue());
                        e.setExpressionMark(bidExpression.getType() + "-" + bidExpression.getValue());
                        expressions.add(bidExpression);
                    }
                }
            } else {
                //细化类目，需要解析表达式
                TargetingExpression bidExpression = new TargetingExpression();
                bidExpression.setType(BidRecommendationExpressionEnumV5.PAT_CATEGORY_REFINEMENT.getBidTargetingExpressionType());
                String expressionV5Value = getExpressionV5Value(targetExpressionList);
                if (StringUtils.isNotBlank(expressionV5Value)) {
                    bidExpression.setValue(expressionV5Value);
                    e.setExpressionMark(bidExpression.getType() + "-" + bidExpression.getValue());
                    expressions.add(bidExpression);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(distinctAsinSet)) {
            distinctAsinSet.forEach(x -> {
                String[] split = x.split("-");
                TargetingExpression bidExpression = new TargetingExpression();
                bidExpression.setType(split[0]);
                bidExpression.setValue(split[1]);

                expressions.add(bidExpression);
            });
        }

        Result<Map<String, Pair<SuggestedBid, SuggestedBidImpactMetrics>>> result = getSuggestBidV5(shop, amazonAdGroup, expressions);
        if (!result.success() || MapUtils.isEmpty(result.getData())) {
            return result;
        }

        Map<String, Pair<SuggestedBid, SuggestedBidImpactMetrics>> suggestedBidMap = result.getData();
        for (SuggestedTargetVo target : targetVos) {
            if (StringUtils.isBlank(target.getExpressionMark())) {
                continue;
            }
            Pair<SuggestedBid, SuggestedBidImpactMetrics> pair = suggestedBidMap.get(target.getExpressionMark());
            if (Objects.isNull(pair)) {
                continue;
            }
            SuggestedBid suggestedBid = pair.getLeft();
            if (suggestedBid != null) {
                if (suggestedBid.getSuggested() != null) {
                    target.setSuggested(suggestedBid.getSuggested().toString());
                }
                if (suggestedBid.getRangeStart() != null) {
                    target.setRangeStart(suggestedBid.getRangeStart().toString());
                }
                if (suggestedBid.getRangeEnd() != null) {
                    target.setRangeEnd(suggestedBid.getRangeEnd().toString());
                }
            }
            SuggestedBidImpactMetrics metrics = pair.getRight();
            if (metrics != null) {
                if (metrics.getEstimatedImpressionLower() != null) {
                    target.setEstimatedImpressionLower(metrics.getEstimatedImpressionLower());
                }
                if (metrics.getEstimatedImpressionUpper() != null) {
                    target.setEstimatedImpressionUpper(metrics.getEstimatedImpressionUpper());
                }
            }
        }

        return result;
    }

    /**
     * 细化类目转换成获取建议竞价的表达式
     * 把以下转为：category="${categoryId}" brand="${brand}" rating=${minReviewRating}-${maxReviewRating} price=${minPrice}-${maxPrice} prime-shipping-eligible="${primeShippingEligible}"
     *
     * asinCategorySameAs，value为categoryId，直接使用
     * asinBrandSameAs，value为brandId，直接使用
     * asinIsPrimeShippingEligible，value为true或false，直接使用
     * asinPriceLessThan，value转换成0-max
     * asinPriceBetween，value转换成min-max，直接使用
     * asinPriceGreaterThan，value转换成min-
     * asinReviewRatingLessThan，value转换成0-max
     * asinReviewRatingBetween，value转换成min-max，直接使用
     * asinReviewRatingGreaterThan，value转换成min-5
     * @param targetExpressionList
     * @return
     */

    private String getExpressionV5Value(List<Expression> targetExpressionList) {
        List<String> expressionList = new ArrayList<>(targetExpressionList.size());
        for (Expression expression : targetExpressionList) {
            SpCategoryBidV3ConvertV5Enum v5Enum = SpCategoryBidV3ConvertV5Enum.typeMap.get(expression.getType());
            if (Objects.isNull(v5Enum)) {
                return null;
            }

            String expressionValue = String.format(v5Enum.getV5Expression(), expression.getValue());
            expressionList.add(expressionValue);
        }

        if (CollectionUtils.isNotEmpty(expressionList)) {
            expressionList.sort(null);
            return StringUtils.join(expressionList, " ");
        }
        return null;
    }

    public Result<Map<Map<String, String>, SuggestedBid>> getSuggestBid(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<List<Expression>> expressios) {
        if (shop == null) {
            log.error("取关键词的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取关键词的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        SuggestedKeywordsClient suggestedKeywordsClient = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int retries = 3;
        while (retries > 0) {
            ListTargetingBidRecommendationsResponse response = suggestedKeywordsClient.listTargetsBidRecommendations(shopAuthService.getAdToken(shop),
                            amazonAdGroup.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonAdGroup.getAdGroupId()), expressios);
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                response = suggestedKeywordsClient.listTargetsBidRecommendations(shopAuthService.getAdToken(shop),
                                amazonAdGroup.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonAdGroup.getAdGroupId()), expressios);
            }

            if (response == null || response.getResult() == null) {
                log.error("getSuggestBidByKeywords,amazon接口返回空");
                return ResultUtil.error(3, "网络延迟，请稍后重试");
            }
            // Too Many Requests, retry
            if (response.getStatusCode() == 429) {
                log.info("getSuggestBidByKeywords, 429, Too Many Requests, retries remaining:{}", retries--);
                continue;
            }
            if (StringUtils.isNotBlank(response.getResult().getCode())) {
                return ResultUtil.error(3, response.getResult().getDetails());
            }
            if (response.getStatusCode() != 200) {
                log.error("getSuggestBidByKeywords,调用amazon接口报错");
                return ResultUtil.error(3, "网络延迟，请稍后重试");
            }

            if (CollectionUtils.isNotEmpty(response.getResult().getRecommendations())) {
                Map<Map<String, String>, SuggestedBid> suggestedBidMap = new HashMap<>();
                Map<String, String> map;
                for (TargetingBidRecommendation e : response.getResult().getRecommendations()) {
                    map = new HashMap<>(e.getExpression().size());
                    for (Expression expression : e.getExpression()) {
                        map.put(expression.getType(), expression.getValue());
                    }
                    suggestedBidMap.put(map, e.getSuggestedBid());
                }
                return ResultUtil.success(suggestedBidMap);
            }

            log.error("getSuggestBidByKeywords, amazon接口返回空, response:{}", JSONUtil.objectToJson(response));
            return ResultUtil.success(new HashMap<>());
        }

        log.info("getSuggestBidByKeywords, 429, Too Many Requests, retry fail");
        return ResultUtil.error(3, "网络延迟，请稍后重试");
    }

    public Result<Map<String, Pair<SuggestedBid, SuggestedBidImpactMetrics>>> getSuggestBidV5(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<TargetingExpression> expressios) {
        if (shop == null) {
            log.error("取关键词的建议竞价--shop is null");
            return ResultUtil.error("店铺不存在");
        }
        if (amazonAdGroup == null) {
            log.error("取关键词的建议竞价--profile is null");
            return ResultUtil.error("广告组不存在");
        }

        ProductTargetingClient productTargetingClient = ProductTargetingClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int retries = 3;
        while (retries > 0) {
            TargetingBidRecommendationsResponse response = productTargetingClient.getBidRecommendationsWithExistedGroupV5(shopAuthService.getAdToken(shop),
                    amazonAdGroup.getProfileId(), shop.getMarketplaceId(), amazonAdGroup.getCampaignId(), amazonAdGroup.getAdGroupId(), expressios, "BIDS_FOR_EXISTING_AD_GROUP", true);
            if (response != null
                    && response.getStatusCode() != null
                    && response.getStatusCode() == 401) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                response = productTargetingClient.getBidRecommendationsWithExistedGroupV5(shopAuthService.getAdToken(shop),
                        amazonAdGroup.getProfileId(), shop.getMarketplaceId(), amazonAdGroup.getCampaignId(), amazonAdGroup.getAdGroupId(), expressios, "BIDS_FOR_EXISTING_AD_GROUP", true);
            }

            if (response == null || CollectionUtils.isEmpty(response.getBidRecommendations())) {
                log.error("/sp/targets/bid/recommendations v5接口, amazon接口返回空");
                return ResultUtil.error(3, "网络延迟，请稍后重试");
            }
            // Too Many Requests, retry
            if (response.getStatusCode() == 429) {
                log.info("/sp/targets/bid/recommendations v5接口, 429, Too Many Requests, retries remaining:{}", retries--);
                continue;
            }

            if (response.getStatusCode() != 200) {
                log.error("/sp/targets/bid/recommendations v5接口, 调用amazon接口报错");
                return ResultUtil.error(3, "网络延迟，请稍后重试");
            }

            Map<String, Pair<SuggestedBid, SuggestedBidImpactMetrics>> suggestedBidMap = new HashMap<>();
            for (BidRecommendation e : response.getBidRecommendations()) {
                for (BidRecommendationsForTargetingExpression expression : e.getBidRecommendationsForTargetingExpressions()) {
                    String key = expression.getTargetingExpression().getType() + "-" + expression.getTargetingExpression().getValue();
                    SuggestedBid suggestedBid = new SuggestedBid();
                    if (CollectionUtils.isNotEmpty(expression.getBidValues()) && expression.getBidValues().size() == 3){
                        suggestedBid.setRangeStart(expression.getBidValues().get(0).getSuggestedBid());
                        suggestedBid.setSuggested(expression.getBidValues().get(1).getSuggestedBid());
                        suggestedBid.setRangeEnd(expression.getBidValues().get(2).getSuggestedBid());
                    }
                    suggestedBidMap.put(key, Pair.of(suggestedBid, expression.getSuggestedBidImpactMetrics()));
                }
            }
            return ResultUtil.success(suggestedBidMap);
        }

        log.info("/sp/targets/bid/recommendations v5接口, 429, Too Many Requests, retry fail");
        return ResultUtil.error(3, "网络延迟，请稍后重试");
    }


    public Result getQuerySuggestBid(ShopAuth shop, AmazonAdGroup amazonAdGroup, List<List<Expression>> expressios) {
        ListTargetingBidRecommendationsResponse response = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).listTargetsBidRecommendations(shopAuthService.getAdToken(shop),
                amazonAdGroup.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonAdGroup.getAdGroupId()), expressios);
        if (response.getResult() != null && Constants.UNAUTHORIZED.equals(response.getResult().getCode())) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = SuggestedKeywordsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).listTargetsBidRecommendations(shopAuthService.getAdToken(shop),
                    amazonAdGroup.getProfileId(), amazonAdGroup.getMarketplaceId(), Long.valueOf(amazonAdGroup.getAdGroupId()), expressios);
        }
        if (response == null || response.getResult() == null) {
            log.error("getSuggestBidByKeywords,amazon接口返回空");
            return ResultUtil.error( "网络延迟，请稍后重试");
        }
        if (StringUtils.isNotBlank(response.getResult().getCode())) {
            return ResultUtil.error(response.getResult().getDetails());
        }
        if (response.getStatusCode() != 200) {
            log.error("getQuerySuggestBid,调用amazon接口报错");
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (CollectionUtils.isNotEmpty(response.getResult().getRecommendations())) {
            return ResultUtil.success(response.getResult().getRecommendations());
        }
        return ResultUtil.error(response.getResult().getDetails());
    }

    // 关键词匹配类型和获取建议竞价要求传的expression的type之间的映射
    private String mapMatchType(String matchType) {
        String type = "";
        if (MatchTypeEnum.BROAD.value().equalsIgnoreCase(matchType)) {
            type = ExpressionEnum.queryBroadMatches.value();
        } else if (MatchTypeEnum.PHRASE.value().equalsIgnoreCase(matchType)) {
            type = ExpressionEnum.queryPhraseMatches.value();
        } else if (MatchTypeEnum.EXACT.value().equalsIgnoreCase(matchType)) {
            type = ExpressionEnum.queryExactMatches.value();
        }
        return type;
    }
}
