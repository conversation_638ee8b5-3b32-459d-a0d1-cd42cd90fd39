package com.meiyunji.sponsored.service.doris.dao.admanage.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.doris.dao.admanage.IAdManageTargetDorisDao;
import com.meiyunji.sponsored.service.doris.po.OdsCpcTargetingReport;
import com.meiyunji.sponsored.service.enums.AdOrderByFieldEnum;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportChartBase;
import com.meiyunji.sponsored.service.multiple.common.dto.ReportDorisBase;
import com.meiyunji.sponsored.service.multiple.common.vo.AdvanceFilterVo;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReportDoris;
import com.meiyunji.sponsored.service.multiple.targets.dto.TargetReqDto;
import com.meiyunji.sponsored.service.multiple.targets.enums.*;
import com.meiyunji.sponsored.service.multiple.targets.resp.TargetResp;
import com.meiyunji.sponsored.service.multiple.targets.service.AbstractTargetProcessor;
import com.meiyunji.sponsored.service.util.Constant;
import com.meiyunji.sponsored.service.wordFrequency.enums.WordRoot;
import com.meiyunji.sponsored.service.wordFrequency.vo.WordRootTopVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;

/**
 * 广告管理-投放层级-dao实现类
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Repository
@Slf4j
public class AdManageTargetDorisDaoImpl extends DorisBaseDaoImpl<OdsCpcTargetingReport> implements IAdManageTargetDorisDao {

    @Resource
    @Lazy
    private Map<String, AbstractTargetProcessor> abstractTargetProcessorMap;

    @Override
    public Integer countReport(TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(req.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(req.getEndDate());
        StringBuilder selectSql = new StringBuilder("  ");
        selectSql.append(" select count(*) from ").append(targetTableEnum.getReportTableName()).append(" r ");
        selectSql.append(" where r.puid= ?  and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList(" r.shop_id ", req.getShopIdList(), argsList));
        selectSql.append(" and r. ").append(targetTableEnum.getPrimaryId()).append(" in ( select t.").
                append(targetTableEnum.getPrimaryId()).append(" from ").append(targetTableEnum.getTableName()).append(" t ");
        Set<String> reportColumnList = new HashSet<>();
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        // 默认join连报告表
        reportColumnList.add("impressionsDoris");
        String joinSql = joinSql(req, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        selectSql.append(" )");
        return getJdbcTemplate().queryForObject(selectSql.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public Integer countTarget(TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        boolean bool = StringUtils.isNotBlank(req.getOrderType()) && !"null".equalsIgnoreCase(req.getOrderType());
        StringBuilder selectSql = new StringBuilder(" select t. ");
        selectSql.append(targetTableEnum.getPrimaryId()).append(" targetId, t.shop_id shopId from ").append(targetTableEnum.getTableName()).append(" t ");
        // 获取需要统计的报告表字段
        Set<String> reportColumnList = getReportColumnList(req, bool);
        // 关联sql
        String joinSql = joinSql(req, bool, argsList, reportColumnList, false);
        selectSql.append(joinSql);
        String countSql = " select count(*) from ( " + selectSql + ") r";
        String sql = SqlStringUtil.exactSql(countSql.toString(), argsList);
        logger.info("广告投放高级筛选统计sql: " + sql);
        return getJdbcTemplate().queryForObject(countSql.toString(), Integer.class, argsList.toArray());
    }

    @Override
    public Page<TargetResp> getTargetPage(TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        boolean bool = StringUtils.isNotBlank(req.getOrderType()) && !"null".equalsIgnoreCase(req.getOrderType());
        StringBuilder selectSql = new StringBuilder(" select t. ");
        selectSql.append(targetTableEnum.getPrimaryId()).append(" targetId, t.shop_id shopId from ").append(targetTableEnum.getTableName()).append(" t ");
        // 获取需要统计的报告表字段
        Set<String> reportColumnList = getReportColumnList(req, bool);
        // 关联sql
        String joinSql = joinSql(req, bool, argsList, reportColumnList, false);
        selectSql.append(joinSql);
        String countSql = " select count(*) from ( " + selectSql + ") r";
        // 查看是否排序
        selectSql.append(" order by ").append(getOrderByByCode(req, bool)).append(" ");
        if (StringUtils.isBlank(req.getOrderType()) || "null".equalsIgnoreCase(req.getOrderType()) || "desc".equalsIgnoreCase(req.getOrderType())) {
            selectSql.append(" desc");
        }
        selectSql.append(" ,t.").append(targetTableEnum.getPrimaryId()).append(" desc");
        Object[] args = argsList.toArray();
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告投放列表页sql: " + sql);
        return getPageResultByRowMapper(req.getPageNo(), req.getPageSize(), countSql, args, selectSql.toString(), args, (re, i) -> TargetResp.builder()
                .targetId(Optional.ofNullable(re.getString("targetId")).orElse(""))
                .shopId(re.getInt("shopId")).build());
    }

    @Override
    public AdMetricDto getSumAdMetricMultiple(TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        Set<String> reportColumnList = new HashSet<>();
        reportColumnList.add("costDoris");
        reportColumnList.add("totalSalesDoris");
        reportColumnList.add("orderNumDoris");
        reportColumnList.add("saleNumDoris");
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        StringBuilder selectSql = new StringBuilder(" select sum(costDoris) as sumCost,sum(totalSalesDoris) as sumAdSale,");
        selectSql.append(" sum(orderNumDoris) as sumAdOrderNum,sum(saleNumDoris) as sumOrderNum ");
        selectSql.append(" from (select costDoris,totalSalesDoris,orderNumDoris,saleNumDoris from ");
        selectSql.append(targetTableEnum.getTableName()).append(" t ");
        // 关联sql
        String joinSql = joinSql(req, false, argsList, reportColumnList, true);
        selectSql.append(joinSql);
        selectSql.append(") r");
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告投放计算占比指标汇总sql: " + sql);
        return getJdbcTemplate().queryForObject(selectSql.toString(), new ObjectMapper<>(AdMetricDto.class), argsList.toArray());
    }

    @Override
    public List<TargetReportDoris> listTargetReport(TargetReqDto req, Boolean export) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        String startDate = DateUtil.getDateSqlFormat(req.getStartDate());
        String endDate = DateUtil.getDateSqlFormat(req.getEndDate());
        StringBuilder selectSql = new StringBuilder(" select r.").append(targetTableEnum.getPrimaryId()).append(" targetId ");
        // 统计的报告字段
        reportColumn(req, selectSql);
        selectSql.append(" from ").append(targetTableEnum.getReportTableName()).append(" r ");
        selectSql.append(" where puid = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList("r.shop_id", req.getShopIdList(), argsList));
        if (export) {
            // 导出使用子查询
            selectSql.append(" and r.").append(targetTableEnum.getPrimaryId()).append(" in ( select t.").append(targetTableEnum.getPrimaryId()).
                    append(" target_id from ").append(targetTableEnum.getTableName()).append(" t ");
            Set<String> reportColumnList = new HashSet<>();
            if (req.getUseAdvanced()) {
                reportColumnList.addAll(getSelectKey(req));
            }
            String joinSql = joinSql(req, false, argsList, reportColumnList, true);
            selectSql.append(joinSql);
            selectSql.append(" )");
        } else {
            selectSql.append(SqlStringUtil.dealInList("r." + targetTableEnum.getPrimaryId(), req.getTargetIds(), argsList));
        }
        selectSql.append(" group by r. ").append(targetTableEnum.getPrimaryId());
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告投放列表页报告指标sql: " + sql);
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetReportDoris.class));
    }

    @Override
    public ReportDorisBase getSumReport(TargetReqDto req , boolean selCompareDate) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select 1");
        // 报告sql
        sumReportSql(req, selectSql, argsList, selCompareDate);
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告投放列表页汇总sql: " + sql);
        return getJdbcTemplate().queryForObject(selectSql.toString(), new ObjectMapper<>(ReportDorisBase.class), argsList.toArray());
    }

    @Override
    public List<ReportChartBase> getSumReportGroupByCountDay(TargetReqDto req) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select count_day countDay ");
        // 报告sql
        sumReportSql(req, selectSql, argsList, false);
        selectSql.append(" group by r.count_day ");
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告投放列表页按天汇总sql: " + sql);
        return getJdbcTemplate().query(selectSql.toString(), new BeanPropertyRowMapper<>(ReportChartBase.class), argsList.toArray());
    }

    @Override
    public List<String> getValidRecordByDate(Integer puid, List<Integer> shopIdList, String startDate, TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select distinct ");
        selectSql.append(targetTableEnum.getPrimaryId()).append(" targetId from ").append(targetTableEnum.getReportTableName());
        selectSql.append(" where puid = ? and count_day >= ? and impressions > 0 ");
        argsList.add(puid);
        argsList.add(startDate);
        selectSql.append(SqlStringUtil.dealInList(" shop_id ", shopIdList, argsList));
        selectSql.append(" and ").append(targetTableEnum.getPrimaryId()).append(" in ( select t.").
                append(targetTableEnum.getPrimaryId()).append(" from ").append(targetTableEnum.getTableName()).append(" t ");
        Set<String> reportColumnList = new HashSet<>();
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        // 默认join连报告表
        reportColumnList.add("impressionsDoris");
        String joinSql = joinSql(req, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        selectSql.append(" )");
        return getJdbcTemplate().query(selectSql.toString(), (rs, rowNum) -> rs.getString("targetId"),argsList.toArray());
    }

    @Override
    public List<WordRootTopVo> getKeywordTopList(TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select word_root, count(distinct keyword_id) count from ");
        selectSql.append(targetTableEnum.getWordRootTableName());
        selectSql.append(" where puid = ? ");
        argsList.add(req.getPuid());
        selectSql.append(SqlStringUtil.dealInList(" shop_id ", req.getShopIdList(), argsList));
        if (StringUtils.isNotBlank(req.getWordFrequencyType())) {
            WordRoot.WordFrequencyType wordFrequencyType = WordRoot.WordFrequencyType.getWordFrequencyType(Integer.valueOf(req.getWordFrequencyType()));
            if (wordFrequencyType != null) {
                selectSql.append(" and word_frequency_type = ? ");
                argsList.add(req.getWordFrequencyType());
            }
        }
        selectSql.append(" and keyword_id in ( select t.keyword_id from ");
        selectSql.append(targetTableEnum.getTableName()).append(" t ");
        Set<String> reportColumnList = new HashSet<>();
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        // 默认join连报告表 保持和词频分析独立页一致
        reportColumnList.add("impressionsDoris");
        String joinSql = joinSql(req, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        selectSql.append(" )");
        selectSql.append(" group by word_root order by count desc ");
        selectSql.append(" limit ").append(req.getTop().toString());
        String sql = SqlStringUtil.exactSql(selectSql.toString(), argsList);
        logger.info("广告投放关键词频sql: " + sql);
        return getJdbcTemplate().query(selectSql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(WordRootTopVo.class));
    }

    @Override
    public List<String> getKeywordIdListByWordRoot(TargetReqDto req) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder("select distinct keyword_id from ");
        selectSql.append(targetTableEnum.getWordRootTableName());
        selectSql.append(" where puid = ? and word_root = ? ");
        argsList.add(req.getPuid());
        argsList.add(req.getWordRoot());
        selectSql.append(SqlStringUtil.dealInList(" shop_id ", req.getShopIdList(), argsList));
        selectSql.append(" and keyword_id in ( select t.keyword_id from ");
        selectSql.append(targetTableEnum.getTableName()).append(" t ");
        Set<String> reportColumnList = new HashSet<>();
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        // 默认join连报告表 保持和词频分析独立页一致
        reportColumnList.add("impressionsDoris");
        String joinSql = joinSql(req, false, argsList, reportColumnList,true);
        selectSql.append(joinSql);
        selectSql.append(" )");
        return getJdbcTemplate().queryForList(selectSql.toString(), argsList.toArray(), String.class);
    }

    /**
     * 报告字段统计sql
     */
    private void sumReportSql(TargetReqDto req, StringBuilder selectSql,List<Object> argsList, boolean selCompareDate) {
        // 获取投放层级表枚举
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        String startDate;
        String endDate;
        //查对比数据时用对比的时间
        if (selCompareDate) {
            startDate = DateUtil.getDateSqlFormat(req.getCompareStartDate());
            endDate = DateUtil.getDateSqlFormat(req.getCompareEndDate());
        } else {
            startDate = DateUtil.getDateSqlFormat(req.getStartDate());
            endDate = DateUtil.getDateSqlFormat(req.getEndDate());
        }
        // 统计的报告字段
        appendReportColumn(req, selectSql);
        selectSql.append(" from ").append(targetTableEnum.getReportTableName()).append(" r ");
        // 关联汇率表 多店铺
        if(req.getChangeRate()){
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            // 关联汇率表
            selectSql.append(" join (select m.marketplace_id, c.month, c.rate,c.puid ");
            selectSql.append(" from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency ");
            selectSql.append(" and c.puid = ? and `to` = 'USD' and month >= ? and month <= ? ) d ");
            selectSql.append(" on d.puid = r.puid and d.month = r.count_month and  d.marketplace_id = r.marketplace_id ");
            argsList.add(req.getPuid());
            argsList.add(start);
            argsList.add(end);
        }
        selectSql.append(" where r.puid= ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(req.getPuid());
        argsList.add(startDate);
        argsList.add(endDate);
        selectSql.append(SqlStringUtil.dealInList(" r.shop_id ", req.getShopIdList(), argsList));
        selectSql.append(" and r. ").append(targetTableEnum.getPrimaryId()).append(" in ( select t.").
                append(targetTableEnum.getPrimaryId()).append(" from ").append(targetTableEnum.getTableName()).append(" t ");
        Set<String> reportColumnList = new HashSet<>();
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        // 默认join连报告表
        reportColumnList.add("impressionsDoris");
        //查对比数据时并且使用高级筛选时，需要使用left join，才能把列表页展示的id都查出来(不使用高级筛选时子表只查基础表，过滤出的id就是列表页的id)，然后查询这些id的报告数据汇总起来
        //如果采用join的话查出的对比数据只有当前时间有报告数据的id，会较少对不上
        String joinSql = joinSql(req, false, argsList, reportColumnList, !selCompareDate);
        selectSql.append(joinSql);
        selectSql.append(" )");
    }

    private static void reportColumn(TargetReqDto req, StringBuilder selectSql) {
        Set<String> allCode;
        if (Constants.SP.equals(req.getAdType())) {
            allCode = TargetSpColumnEnum.getAllColumn();
        } else if (Constants.SB.equals(req.getAdType())) {
            allCode = TargetSbColumnEnum.getAllColumn();
        } else {
            allCode = TargetSdColumnEnum.getAllColumn();
        }
        for (String code : allCode) {
            selectSql.append(code);
        }
    }

    private static void appendReportColumn(TargetReqDto req, StringBuilder selectSql) {
        Set<String> columnList;
        if (Constants.SP.equals(req.getAdType())) {
            columnList = TargetSpColumnEnum.getAllCode();
        } else if (Constants.SB.equals(req.getAdType())) {
            columnList = TargetSbColumnEnum.getAllCode();
        } else {
            columnList = TargetSdColumnEnum.getAllCode();
        }
        for(String column : columnList){
            String code;
            if (Constants.SP.equals(req.getAdType())) {
                code = TargetSpColumnEnum.getColumnByCode(column, req.getChangeRate());
            } else if (Constants.SB.equals(req.getAdType())) {
                code = TargetSbColumnEnum.getColumnByCode(column, req.getChangeRate());
            } else {
                code = TargetSdColumnEnum.getColumnByCode(column, req.getChangeRate());
            }
            selectSql.append(code);
        }
    }

    private static String getOrderByByCode(TargetReqDto req, boolean bool) {
        if (Constants.SP.equals(req.getAdType())) {
            return TargetSpOrderByEnum.getOrderByByCode(bool ? req.getOrderField() : null, req.getTargetType());
        } else if (Constants.SB.equals(req.getAdType())) {
            return TargetSbOrderByEnum.getOrderByByCode(bool ? req.getOrderField() : null, req.getTargetType());
        } else {
            return TargetSdOrderByEnum.getOrderByByCode(bool ? req.getOrderField() : null, req.getOrderType());
        }
    }

    /**
     * 连表sql
     */
    private String joinSql(TargetReqDto req, boolean order, List<Object> argsList, Set<String> reportColumnList, boolean innerJoin) {
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        String startDate = req.getStartDate();
        String endDate = req.getEndDate();
        StringBuilder joinSql = new StringBuilder();
        // 关联报告表
        joinReport(req, order, argsList, reportColumnList, innerJoin, joinSql, targetTableEnum, startDate, endDate);
        // 关联店铺表
        joinShopInfo(req, order, argsList, joinSql);
        // 关联Aba搜索词排名表
        joinAbaRank(req, order, argsList, joinSql);
        // 竞价筛选关联广告组表
        joinGroup(req, order, argsList, joinSql, targetTableEnum);
        // sd vcpm查询关联活动表
        joinCampaign(req, order, argsList, joinSql);
        // where 条件
        joinSql.append(getTargetPageWhereSql(req, argsList));
        // 高级筛选
        joinSql.append(advancedFilter(req, argsList, order, reportColumnList));
        return joinSql.toString();
    }

    /**
     * sd vcpm查询关联活动表
     */
    private void joinCampaign(TargetReqDto req, boolean order, List<Object> argsList, StringBuilder joinSql) {
        boolean joinCampaign = isJoinCampaign(req, order);
        if (joinCampaign) {
            joinSql.append(" join ods_t_amazon_ad_campaign_all c  on t.puid = c.puid and t.shop_id = c.shop_id and t.campaign_id = c.campaign_id ");
            joinSql.append(" and c.puid = ? and lower(c.type) = 'sd' ");
            argsList.add(req.getPuid());
            joinSql.append(SqlStringUtil.dealInList(" c.shop_id ", req.getShopIdList(), argsList));
        }
    }

    /**
     * 竞价筛选关联广告组表
     */
    private void joinGroup(TargetReqDto req, boolean order, List<Object> argsList, StringBuilder joinSql, TargetTableEnum targetTableEnum) {
        boolean joinGroup = isJoinGroup(req, order);
        if (joinGroup) {
            joinSql.append(" join ").append(targetTableEnum.getGroupTableName()).append(" g  on t.puid = g.puid and t.shop_id = g.shop_id and t.ad_group_id = g.ad_group_id ");
            joinSql.append(" and g.puid = ? ");
            argsList.add(req.getPuid());
            joinSql.append(SqlStringUtil.dealInList(" g.shop_id ", req.getShopIdList(), argsList));
        }
    }

    /**
     * 高级筛选、排揎关联报告表
     */
    private void joinReport(TargetReqDto req, boolean order, List<Object> argsList, Set<String> reportColumnList, boolean innerJoin, StringBuilder joinSql, TargetTableEnum targetTableEnum, String startDate, String endDate) {
        // 是否关联销售额表
        boolean joinShopSale = isJoinShopSale(req, order);
        // 获取报告统计字段
        List<String> sumColumnList = getSumColumnList(req, reportColumnList);
        // 关联报告表
        if (CollectionUtils.isNotEmpty(sumColumnList)) {
            if (!innerJoin) {
                joinSql.append(" left join ( select a.* ");
            } else {
                // 汇总使用join效率高
                joinSql.append(" join ( select a.* ");
            }
            if (joinShopSale) {
                joinSql.append(" ,s.shopSalesDoris ");
            }
            joinSql.append(" from ( ");
            joinSql.append(" select any_value(r.puid) as puid,any_value(r.shop_id) as shop_id,r. ").append(targetTableEnum.getPrimaryId());
            // 获取需要排序或高级筛选的报告字段
            for (String column : sumColumnList) {
                joinSql.append(column);
            }
            joinSql.append(" from ").append(targetTableEnum.getReportTableName()).append(" r");
            joinSql.append(" where r.puid = ? and r.count_day >= ?  and r.count_day <= ? ");
            argsList.add(req.getPuid());
            argsList.add(startDate);
            argsList.add(endDate);
            joinSql.append(SqlStringUtil.dealInList(" r.shop_id ", req.getShopIdList(), argsList));
            joinSql.append("  group by r.").append(targetTableEnum.getPrimaryId());
            joinSql.append("  ) a ");
            // 关联店铺销售额表
            if (joinShopSale) {
                joinSql.append(" left join ( select shop_id, ifnull(sum(sale_price), 0) shopSalesDoris from dws_sale_profit_shop_day");
                joinSql.append("  where puid = ? and now_date >= ? and now_date <= ? ");
                argsList.add(req.getPuid());
                argsList.add(startDate);
                argsList.add(endDate);
                joinSql.append(SqlStringUtil.dealInList(" shop_id ", req.getShopIdList(), argsList));
                joinSql.append("  group by shop_id ) s on s.shop_id = a.shop_id   ");
            }
            joinSql.append(" ) r on r.puid = t.puid and r.shop_id = t.shop_id and r.").append(targetTableEnum.getPrimaryId()).append(" = t.").append(targetTableEnum.getPrimaryId());
        }
    }

    private static List<String> getSumColumnList(TargetReqDto req, Set<String> reportColumnList) {
        List<String> sumColumnList = new ArrayList<>();
        for (String column : reportColumnList) {
            String columnByCode;
            if (Constants.SP.equals(req.getAdType())) {
                columnByCode = TargetSpColumnEnum.getColumnByCode(column, false);
            } else if (Constants.SB.equals(req.getAdType())) {
                columnByCode = TargetSbColumnEnum.getColumnByCode(column, false);
            } else {
                columnByCode = TargetSdColumnEnum.getColumnByCode(column, false);
            }
            sumColumnList.add(columnByCode);
        }
        return sumColumnList;
    }

    /**
     * 店铺名称排序关联店铺表
     */
    private static void joinShopInfo(TargetReqDto req, boolean order, List<Object> argsList, StringBuilder joinSql) {
        // 是否关联店铺表
        boolean joinShopInfo = order && "shopName".equals(req.getOrderField());
        // 店铺排序关联店铺表
        if (joinShopInfo) {
            joinSql.append(" join ( select puid,shop_id,name from dim_t_shop_auth where puid = ? ");
            argsList.add(req.getPuid());
            joinSql.append(SqlStringUtil.dealInList(" shop_id ", req.getShopIdList(), argsList));
            joinSql.append(") s on s.puid = t.puid and s.shop_id = t.shop_id ");
        }
    }

    /**
     * Aba搜索词排名高级筛选、排序关联aba排名表
     */
    private void joinAbaRank(TargetReqDto req, boolean order, List<Object> argsList, StringBuilder joinSql) {
        // 是否关联ABA搜索词排名表
        boolean joinAbaRank = isJoinAbaRank(req, order);
        // aba搜索詞排名
        if (joinAbaRank) {
            joinSql.append(" left join ( select marketplace_id,search_term,search_frequency_rank,week_ratio from ods_t_week_search_terms_analysis where 1=1 ");
            joinSql.append(SqlStringUtil.dealInList(" start_date ", req.getAbaRankDateList(), argsList));
            joinSql.append(SqlStringUtil.dealInList(" marketplace_id  ", req.getMarketplaceIdList(), argsList));
            joinSql.append(SqlStringUtil.dealDorisInList("concat_ws('|', marketplace_id, start_date)", req.getMarketplaceAbaRankDateList(), argsList));
            joinSql.append(") a on lower(t.keyword_text) = a.search_term and a.marketplace_id = t.marketplace_id  ");
        }
    }

    /**
     * 投放列表页where条件sql拼接
     */
    private String getTargetPageWhereSql(TargetReqDto req, List<Object> argsList) {
        TargetTableEnum targetTableEnum = req.getTargetTypeEnum().getTargetTableEnum();
        String targetId = "t." + targetTableEnum.getPrimaryId();
        StringBuilder whereSql = new StringBuilder();
        whereSql.append(" where t.puid = ? ");
        argsList.add(req.getPuid());
        whereSql.append(SqlStringUtil.dealInList(" t.shop_id ", req.getShopIdList(), argsList));
        // 广告组合筛选
        if (CollectionUtils.isNotEmpty(req.getPortfolioIdList())) {
            wherePortfolio(req, argsList, whereSql);
        }
        // 广告活动筛选
        if (CollectionUtils.isNotEmpty(req.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("t.campaign_id", req.getCampaignIdList(), argsList));
        }
        // 广告组筛选
        if (CollectionUtils.isNotEmpty(req.getGroupIdList())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList("t.ad_group_id", req.getGroupIdList(), argsList));
        }
        // 广告标签筛选
        if (CollectionUtils.isNotEmpty(req.getTargetIds())) {
            whereSql.append(SqlStringUtil.dealBitMapDorisInList(targetId, req.getTargetIds(), argsList));
        }
        // 广告策略筛选
        if (CollectionUtils.isNotEmpty(req.getAdStrategyTypeList())) {
            String sql = AdTargetStrategyTypeEnum.getSql(req.getAdStrategyTypeList(), req.getAutoRuleIds(), req.getAutoRuleGroupIds(), argsList, targetId, "t.ad_group_id");
            if (StringUtils.isNotEmpty(sql)) {
                // 关键词投放需要过滤主题投放
                if ("keyword".equals(req.getTargetType())) {
                    if (!req.getAdStrategyTypeList().contains(AdTargetStrategyTypeEnum.NONE.getCode())) {
                        whereSql.append(sql);
                        whereSql.append(" and t.match_type != 'theme' ");
                    } else {
                        whereSql.append(" and ( ");
                        // 去掉第一个and
                        sql = sql.replaceFirst("and", "");
                        whereSql.append(sql);
                        whereSql.append(" or t.match_type = 'theme' )");
                    }
                } else {
                    whereSql.append(sql);
                }
            }
        }
        // 状态筛选
        if (CollectionUtils.isNotEmpty(req.getStatusList())) {
            whereSql.append(SqlStringUtil.dealInList("t.state", req.getStatusList(), argsList));
        }
        // 服务状态筛选 sb类型不存在服务状态
        if (CollectionUtils.isNotEmpty(req.getServingStatusList()) && !Constants.SB.equals(req.getAdType())) {
            whereSql.append(SqlStringUtil.dealInList("t.serving_status", req.getServingStatusList(), argsList));
        }
        // 各投放类型特殊刷选
        AbstractTargetProcessor targetProcessor = abstractTargetProcessorMap.get(req.getTargetTypeEnum().getBeanName());
        targetProcessor.abstractBuildWhereSql(req, whereSql, argsList);
        return whereSql.toString();
    }

    /**
     * 广告组合筛选子查询
     */
    private static void wherePortfolio(TargetReqDto req, List<Object> argsList, StringBuilder sql) {
        sql.append(" and t.campaign_id in ( ");
        sql.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? ");
        argsList.add(req.getPuid());
        sql.append(SqlStringUtil.dealDorisInList("shop_id", req.getShopIdList(), argsList));
        if (StringUtils.isNotBlank(req.getAdType())) {
            sql.append(" and type = ? ");
            argsList.add(req.getAdType());
        }
        if (req.getPortfolioIdList().contains(Constant.NON_PORTFOLIO_ID)) {
            sql.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ");
            if (req.getPortfolioIdList().size() > 1) {
                sql.append(SqlStringUtil.dealInListOr("portfolio_id", req.getPortfolioIdList(), argsList));
            }
            sql.append(" ) ");
        } else {
            sql.append(SqlStringUtil.dealDorisInList("portfolio_id", req.getPortfolioIdList(), argsList));
        }
        sql.append(" ) ");
    }

    /**
     * 是否关联报告表
     */
    private Set<String> getReportColumnList(TargetReqDto req, boolean bool) {
        Set<String> reportColumnList = new HashSet<>();
        // 获取排序字段
        if (bool) {
            Set<String> columnList;
            if (Constants.SP.equals(req.getAdType())) {
                columnList = TargetSpOrderByEnum.getSetByCode(req.getOrderField());
            } else if (Constants.SB.equals(req.getAdType())) {
                columnList = TargetSbOrderByEnum.getSetByCode(req.getOrderField());
            } else {
                columnList = TargetSdOrderByEnum.getSetByCode(req.getOrderField());
            }
            if (CollectionUtils.isNotEmpty(columnList)) {
                reportColumnList.addAll(columnList);
            }
        }
        // 获取高级筛选字段
        if (req.getUseAdvanced()) {
            reportColumnList.addAll(getSelectKey(req));
        }
        return reportColumnList;
    }

    /**
     * 是否关联销售额表
     */
    private boolean isJoinShopSale(TargetReqDto req, boolean order) {
        boolean joinShopSale = false;
        if (order && ("acots".equals(req.getOrderField()) || "asots".equals(req.getOrderField()))) {
            joinShopSale = true;
        }
        if (req.getUseAdvanced() && (req.getAdvanceFilter().getAcotsMin() != null || req.getAdvanceFilter().getAcotsMax() != null || req.getAdvanceFilter().getAsotsMax() != null || req.getAdvanceFilter().getAsotsMin() != null)) {
            joinShopSale = true;
        }
        return joinShopSale;
    }

    /**
     * 是否关联销售额表
     */
    private boolean isJoinAbaRank(TargetReqDto req, boolean order) {
        if (!"keyword".equals(req.getTargetType())) {
            return false;
        }
        boolean joinShopSale = false;
        if (order && ("searchFrequencyRank".equals(req.getOrderField()) || "weekRatio".equals(req.getOrderField()))) {
            joinShopSale = true;
        }
        if (req.getUseAdvanced() && (req.getAdvanceFilter().getSearchFrequencyRankMin() != null || req.getAdvanceFilter().getSearchFrequencyRankMax() != null || req.getAdvanceFilter().getWeekRatioMin() != null || req.getAdvanceFilter().getWeekRatioMax() != null)) {
            joinShopSale = true;
        }
        return joinShopSale;
    }

    /**
     * 是否关联广告活动
     */
    private boolean isJoinCampaign(TargetReqDto req, boolean checkOrderField) {
        boolean filter = req.getUseAdvanced() && (Objects.nonNull(req.getAdvanceFilter().getVcpmMin()) || Objects.nonNull(req.getAdvanceFilter().getVcpmMax()));
        if (!checkOrderField) {
            return filter;
        }
        if (StringUtils.isNotBlank(req.getOrderField()) && StringUtils.equalsAnyIgnoreCase(req.getOrderType(), "asc", "desc") && AdOrderByFieldEnum.VCPM.getCode().equals(req.getOrderField())) {
            return true;
        }
        return filter;
    }

    /**
     * 是否关联广告组
     */
    private boolean isJoinGroup(TargetReqDto req, boolean checkOrderField) {
        boolean filter = req.getUseAdvanced() && (Objects.nonNull(req.getAdvanceFilter().getBiddingMin()) || Objects.nonNull(req.getAdvanceFilter().getBiddingMax()));
        if (!checkOrderField) {
            return filter;
        }
        if (StringUtils.isNotBlank(req.getOrderField()) && StringUtils.equalsAnyIgnoreCase(req.getOrderType(), "asc", "desc") && SqlStringReportUtil.BID.equals(req.getOrderField())) {
            return true;
        }
        return filter;
    }

    /**
     * 获取高级筛选报告统计字段
     */
    private Set<String> getSelectKey(TargetReqDto req) {
        Set<String> keySet = new HashSet<>();
        if (CollectionUtils.isNotEmpty(req.getAdvanceFilterVoList())) {
            for (AdvanceFilterVo advance : req.getAdvanceFilterVoList()) {
                Set<String> columnList;
                if (Constants.SP.equals(req.getAdType())) {
                    columnList = TargetSpAdvanceEnum.getSetByCode(advance.getCode());
                } else if (Constants.SB.equals(req.getAdType())) {
                    columnList = TargetSbAdvanceEnum.getSetByCode(advance.getCode());
                } else {
                    columnList = TargetSdAdvanceEnum.getSetByCode(advance.getCode());
                }
                if (CollectionUtils.isNotEmpty(columnList)) {
                    keySet.addAll(columnList);
                }
            }
        }
        return keySet;
    }

    /**
     * 高级排序
     */
    private StringBuilder advancedFilter(TargetReqDto req, List<Object> argsList, boolean order, Set<String> reportColumnList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if (req.getUseAdvanced() && CollectionUtils.isNotEmpty(req.getAdvanceFilterVoList())) {
            subWhereSql.append(" having 1=1 ");
            for (AdvanceFilterVo advance : req.getAdvanceFilterVoList()) {
                String havingBy;
                if (Constants.SP.equals(req.getAdType())) {
                    havingBy = TargetSpAdvanceEnum.getHavingByByCode(advance.getCode(), req.getTargetType());
                } else if (Constants.SB.equals(req.getAdType())) {
                    havingBy = TargetSbAdvanceEnum.getHavingByByCode(advance.getCode(), req.getTargetType());
                } else {
                    havingBy = TargetSdAdvanceEnum.getHavingByByCode(advance.getCode(), req.getTargetType());
                }
                if (StringUtils.isNotBlank(havingBy)) {
                    subWhereSql.append(havingBy);
                    argsList.add(advance.getValue());
                }
            }
        }
        return subWhereSql;
    }

}
