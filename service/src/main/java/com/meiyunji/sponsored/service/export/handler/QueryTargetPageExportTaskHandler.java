package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.base.AdReportBeanConvertProcess;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskMatchTypeEnum;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service.ICpcQueryTargetingReportService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdStrategyVo;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.ReportExcelVo;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.enums.AdvertisingQueryTargetExportFieldEnum;
import com.meiyunji.sponsored.service.enums.SbMatchValueEnum;
import com.meiyunji.sponsored.service.enums.SpKeywordGroupValueEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.product.po.AsinInfo;
import com.meiyunji.sponsored.service.product.service.IAsinInfoService;
import com.meiyunji.sponsored.service.searchTermsAnalysis.dto.MarketplaceStartDateDto;
import com.meiyunji.sponsored.service.vo.KeywordCommodityTargetVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.util.CollectionSplitUtil.splitList;


@Slf4j
@Service(AdManagePageExportTaskConstant.QUERY_ASIN)
public class QueryTargetPageExportTaskHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private ICpcQueryTargetingReportService cpcQueryTargetingReportService;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private IAsinInfoService asinInfoService;

    private static String currency;
    /**
     * 导出
     * @param task
     */
    @Override
    public void export(AdManagePageExportTask task) {
        CpcQueryWordDto param = JSONUtil.jsonToObject(task.getParam(), CpcQueryWordDto.class);
        List<String> feilds = Arrays.asList("orderNum", "orderNumPercentage", "salesConversionRate", "query", "targetingExpression", "adGroupName", "campaignName", "targetingType"
                , "impressions", "clicks", "clickRate", "cost", "adCostPercentage", "cpc", "cpa", "sales", "adSalePercentage", "acos", "acots", "roas", "asots", "adOrderNumPercentage");
        if (param == null) {
            log.error(String.format("ad-query-搜索词(投放)export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
        currency = AmznEndpoint.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode().value();
        String fileName = auth.getName() + "_搜索词" + "_" + param.getStart() + "_" + param.getEnd();

        List<String> urlList = new ArrayList<>();
        Page pageTemp = new Page();
        pageTemp.setPageNo(1);
        pageTemp.setPageSize(Constants.FILE_MAX_SIZE);
        Page page;
        page = cpcQueryTargetingReportService.dorisPageExportList(param.getPuid(), param, pageTemp);
        List<ReportVo> rows = page.getRows();
        List<ReportRpcVo> rpcVos;
        if (CollectionUtils.isNotEmpty(rows)) {
            // 填充广告标签
            cpcQueryTargetingReportService.fillAdStrategy(param, rows);

            /*
            查询需要导出的asin的产品名称、价格、星级和评分
            拿该页的asin去在线产品表里进行查询是否存在：
              ---存在则拿对应的产品名称、价格、星级、评分
              ---不存在则去查询调用爬虫程序去获取该asin的产品名称、价格、星级、评分的表
                  ---存 在：取对应的产品名称、价格、星级、评分
            */
            this.getRatingByAsinList(param.getPuid(), param.getShopId(), param.getMarketplaceId(), rows);
            rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                ReportRpcVo.Builder builder1 = convertVoToRpcMessage(item);
                builder1.setOrderNum(item.getSaleNum() == null ? Int32Value.of(0) : Int32Value.of(item.getSaleNum()));
                builder1.setSaleNum(item.getOrderNum() == null ? Int32Value.of(0) : Int32Value.of(item.getOrderNum()));
                if(item.getSaleNum() != null && item.getClicks() != null ){
                    Double aDouble = calculationRateDouble(Double.valueOf(item.getOrderNum()), Double.valueOf(item.getClicks()));
                    builder1.setSalesConversionRate(DoubleValue.of(aDouble));
                }
                return builder1.build();
            }).collect(Collectors.toList());
        } else {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }

        List<List<ReportRpcVo>> partition = Lists.partition(rpcVos, Constants.FILE_MAX_SIZE);

        if (StringUtils.isNotBlank(param.getExportSortField()) && Objects.nonNull(param.getFreezeNum())) {
            urlList = newExport(partition, param, fileName, auth);
        } else {
            urlList = oldExport(partition, param, fileName);
        }

        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private void getRatingByAsinList(Integer puid, Integer shopId, String marketplaceId, List<ReportVo> rows) {
        HashSet<String> asins = new HashSet<>();
        rows.forEach(item -> {
            asins.add(item.getQuery());
        });
        //先去在线产品表里进行过滤，存在则取对应的商品名称、价格、星级、评分
        //这里asins的数量可能会超过1w，所以这里对它进行切割处理
        List<String> list = new ArrayList<>(asins);
        List<List<String>> batches = splitList(list, 9000);
        List<ReportVo> ratingList = odsProductDao.getRatingByAsinList(puid, shopId, marketplaceId, batches);
        //取出不存在的去爬虫信息表里获取
        List<String> existAsin = ratingList.stream().map(ReportVo::getAsin).collect(Collectors.toList());
        Map<String, ReportVo> voMap = StreamUtil.toMap(ratingList, ReportVo::getAsin);
        //过滤出不存在在线产品表的数据
        HashSet<String> asinList = new HashSet<>();
        asins.forEach(i -> {
            if (!existAsin.contains(i)) {
                asinList.add(i);
            }
        });
        //获取爬虫信息表里的对应数据
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        List<AsinInfo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(asinList)) {
            voList = asinInfoService.getRatingByAsinList(asinList, shopAuth.getMarketplaceId());
        }

        Map<String, AsinInfo> map = StreamUtil.toMap(voList, AsinInfo::getAsin);
        //设置商品名称、价格、星级、评分数
        rows.forEach(i -> {
            ReportVo vo = voMap.get(i.getQuery());
            AsinInfo vo1 = map.get(i.getQuery());
            if (vo != null) {
                i.setTitle(vo.getTitle());
                i.setPrice(vo.getPrice());
                i.setRating(vo.getRating());
                i.setRatingCount(vo.getRatingCount());
            }
            if (vo1 != null) {
                i.setTitle(vo1.getTitle());
                i.setPrice(vo1.getPrice());
                i.setRating(vo1.getStarRating());
                i.setRatingCount(vo1.getRatingCount());
                i.setMainImage(vo1.getImage());
            }
        });
    }

    private List<String> newExport(List<List<ReportRpcVo>> partition, CpcQueryWordDto param, String fileName, ShopAuth shop) {
        //存储文件路径urlList
        List<String> downloadUrl = new ArrayList<>();
        //组装需要排除的字段
        List<String> excludeFileds = Lists.newArrayList("ordersNewToBrand14d", "orderRateNewToBrand14d", "salesNewToBrand14d", "salesRateNewToBrand14d", "ordersNewToBrandPercentage14d", "impressionRank", "impressionShare", "searchFrequencyRank", "weekRatio");

        //自定义排序：根据param的exportSortField，中的字段作为表格中的表头字段顺序导出
        List<String> sortFields = Arrays.asList(param.getExportSortField().split(","));
        //排除字段
        sortFields = sortFields.stream().filter(item -> !excludeFileds.contains(item)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sortFields)) {
            return downloadUrl;
        }
        // 默认导出广告策略标签
        sortFields.add("adStrategyTag");
        //导出
        for (List<ReportRpcVo> partitionList : partition) {
            List<KeywordCommodityTargetVo> listExports = Lists.newLinkedList();
            for (ReportRpcVo reportVo : partitionList) {
                ReportVo vo = AdReportBeanConvertProcess.convertRpcVoToReportVo(reportVo);
                ReportExcelVo excelVo = getExcelVo(vo);
                KeywordCommodityTargetVo exportVo = new KeywordCommodityTargetVo();
                BeanUtils.copyProperties(excelVo, exportVo);
                exportVo.setAdOrderNum(reportVo.hasAdSelfSaleNum() ? reportVo.getAdSelfSaleNum().getValue() : 0);
                // 广告策略标签筛选
                if(CollectionUtils.isNotEmpty(reportVo.getAdStrategysList())){
                    List<String> adStrategyTag = reportVo.getAdStrategysList().stream().map(it-> AdQueryStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
                    exportVo.setAdStrategyTag(String.join(",", adStrategyTag));
                }else{
                    exportVo.setAdStrategyTag("");
                }
                listExports.add(exportVo);
            }
            if (CollectionUtils.isEmpty(listExports)) {
                continue;
            }

            String url = customFieldSortExport(listExports, sortFields, param.getFreezeNum(), shop, fileName);
            downloadUrl.add(url);
        }
        return downloadUrl;

    }

    private String customFieldSortExport(List<KeywordCommodityTargetVo> reportExcelVos, List<String> sortFields, Integer freezeNum, ShopAuth shop, String fileName) {
        //Excel样式builder
        WriteHandlerBuild build = new WriteHandlerBuild()
            .rate();
        //冻结前n列前1行
        if (freezeNum != null) {
            build.freezeRowAndCol(freezeNum, 1);
        }
        build.noModleHandler(getCurrencyIndex(sortFields));

        //构建行
        List<List<Object>> rows = new ArrayList<>(reportExcelVos.size());
        for (KeywordCommodityTargetVo cpVo : reportExcelVos) {
            rows.add(buildExportSingleRow(cpVo, sortFields));
        }
        //构建表头
        List<String> headNames = new ArrayList<>(sortFields.size());
        for (String sortField : sortFields) {
            AdvertisingQueryTargetExportFieldEnum fieldEnum = AdvertisingQueryTargetExportFieldEnum.fromPoParamKey(sortField);
            if (fieldEnum == null) {
                log.error("sortFields 包含非法字符，导出阻止，返回空, sortFields:{}", sortFields);
                throw new RuntimeException("sortFields 包含非法字符，导出阻止，返回空, sortFields:" + sortFields);
            }
            headNames.add(fieldEnum.getTableColName());
        }
        //导出
        return excelService.exportByCustomColSort(shop.getPuid(), headNames, rows, fileName + "(" + (0) + ")", build);
    }

    private List<Object> buildExportSingleRow(KeywordCommodityTargetVo cpVo, List<String> sortFields) {
        List<Object> cols = new ArrayList<>(AdvertisingQueryTargetExportFieldEnum.values().length);
        for (String fieldName : sortFields) {
            AdvertisingQueryTargetExportFieldEnum fieldEnum = AdvertisingQueryTargetExportFieldEnum.fromPoParamKey(fieldName);
            if (fieldEnum == null) {
                return Collections.emptyList();
            }
            Object value = getObjectByField(cpVo, fieldEnum);
            cols.add(value);
        }
        return cols;
    }

    private Object getObjectByField(KeywordCommodityTargetVo keyVo, AdvertisingQueryTargetExportFieldEnum fieldEnum) {
        Object value = null;
        switch (fieldEnum) {
            case QUERY:
                value = keyVo.getQuery();
                break;
            case TITLE:
                value = keyVo.getTitle();
                break;
            case CVR:
                value = keyVo.getSalesConversionRate();
                break;
            case ADVERTISING_UNIT_PRICE:
                value = keyVo.getAdvertisingUnitPrice();
                break;
            case AD_ORDER_NUM:
                value = keyVo.getOrderNum();
                break;
            case AD_ORDER_NUM_PERCENTAGE:
                value = keyVo.getAdOrderNumPercentage();
                break;
            case PUTIN:
                value = keyVo.getTargetingExpression();
                break;
            case CAMPAIGN_TYPE:
                value = keyVo.getTargetingType();
                break;
            case GROUP_NAME:
                value = keyVo.getAdGroupName();
                break;
            case CAMPAIGN_NAME:
                value = keyVo.getCampaignName();
                break;
            case PORTFOLIO_NAME:
                value = keyVo.getPortfolioName();
                break;
            case AD_COST:
                value = keyVo.getCost();
                break;
            case AD_COST_PERCENTAGE:
                value = keyVo.getAdCostPercentage();
                break;
            case IMPRESSIONS:
                value = keyVo.getImpressions();
                break;
            case CLICKS:
                value = keyVo.getClicks();
                break;
            case CPA:
                value = keyVo.getCpa();
                break;
            case CPC:
                value = keyVo.getCpc();
                break;
            case CTR:
                value = keyVo.getClickRate();
                break;
            case ACOS:
                value = keyVo.getAcos();
                break;
            case ROAS:
                value = keyVo.getRoas();
                break;
            case ACOTS:
                value = keyVo.getAcots();
                break;
            case ASOTS:
                value = keyVo.getAsots();
                break;
            case SELF_AD_ORDER_NUM:
                value = keyVo.getAdSaleNum();
                break;
            case OTHER_AD_ORDER_NUM:
                value = keyVo.getAdOtherOrderNum();
                break;
            case AD_SALE:
                value = keyVo.getSales();
                break;
            case AD_SALE_PERCENTAGE:
                value = keyVo.getAdSalePercentage();
                break;
            case AD_SELF_SALE:
                value = keyVo.getAdSales();
                break;
            case AD_OTHER_SALES:
                value = keyVo.getAdOtherSales();
                break;
            case AD_SALE_NUM:
                value = keyVo.getSaleNum();
                break;
            case AD_SALE_NUM_PERCENTAGE:
                value = keyVo.getOrderNumPercentage();
                break;
            case AD_SELF_SALE_NUM:
                value = keyVo.getAdOrderNum();
                break;
            case AD_OTHER_SALE_NUM:
                value = keyVo.getAdOtherSaleNum();
                break;
            case IMPRESSION_RANK:
                value = keyVo.getImpressionRank();
                break;
            case QUERY_TAG:
                value = keyVo.getQueryTag();
                break;
            case IMPRESSION_SHARE:
                value = keyVo.getImpressionShare();
                break;
            case ORDERS_NEW_TO_BRAND_FTD:
                value = keyVo.getOrdersNewToBrand14d();
                break;
            case ORDER_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getOrderRateNewToBrand14d();
                break;
            case ORDERS_NEW_TO_BRAND_PERCENTAGE_FTD:
                value = keyVo.getOrdersNewToBrandPercentage14d();
                break;
            case SALES_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesNewToBrand14d();
                break;
            case SALES_RATE_NEW_TO_BRAND_FTD:
                value = keyVo.getSalesRateNewToBrand14d();
                break;
            case PRICE:
                value = keyVo.getPrice();
                break;
            case RATING:
                value = keyVo.getRating();
                break;
            case RATING_COUNT:
                value = keyVo.getRatingCount();
                break;
            case MATCH_TYPE:
                value = keyVo.getMatchType();
                break;
            case AD_STRATEGY_TAG:
                value = keyVo.getAdStrategyTag();
                break;
            default:
        }
        return value;
    }

    private List<Integer> getCurrencyIndex(List<String> sortFields) {
        List<Integer> currencyIndex = new ArrayList<>();
        for (int i = 0; i < sortFields.size(); i++) {
            AdvertisingQueryTargetExportFieldEnum keywordExportFieldEnum = AdvertisingQueryTargetExportFieldEnum.fromPoParamKey(sortFields.get(i));
            //出现这种情况就是有问题，暂时不考虑
            if (keywordExportFieldEnum == null) {
                return Collections.emptyList();
            }
            if (keywordExportFieldEnum.getCurrencyStyle()) {
                currencyIndex.add(i);
            }
        }

        return currencyIndex;
    }

    private List<String> oldExport(List<List<ReportRpcVo>> partition, CpcQueryWordDto param, String fileName) {
        WriteHandlerBuild build = setWriteHandlerBuild();
        int count = 0;

        List<String> urlList = new ArrayList<>();
        for (List<ReportRpcVo> reportVos : partition) {
            List<KeywordCommodityTargetVo> listExports = Lists.newLinkedList();
            for (ReportRpcVo reportVo : reportVos) {
                ReportVo vo = AdReportBeanConvertProcess.convertRpcVoToReportVo(reportVo);
                ReportExcelVo excelVo = getExcelVo(vo);
                KeywordCommodityTargetVo exportVo = new KeywordCommodityTargetVo();
                BeanUtils.copyProperties(excelVo, exportVo);
                exportVo.setAdOrderNum(reportVo.hasAdSelfSaleNum() ? reportVo.getAdSelfSaleNum().getValue() : 0);
                // 广告策略标签筛选
                if(CollectionUtils.isNotEmpty(reportVo.getAdStrategysList())){
                    List<String> adStrategyTag = reportVo.getAdStrategysList().stream().map(it-> AdQueryStrategyTypeEnum.getEnumByCode(it.getAdStrategyType()).getDesc()).collect(Collectors.toList());
                    exportVo.setAdStrategyTag(String.join(",", adStrategyTag));
                }else{
                    exportVo.setAdStrategyTag("");
                }
                listExports.add(exportVo);
            }
            if (CollectionUtils.isNotEmpty(listExports)) {
                List<String> excludeFileds = Lists.newArrayList("ordersNewToBrand14d", "orderRateNewToBrand14d", "salesNewToBrand14d", "salesRateNewToBrand14d", "ordersNewToBrandPercentage14d", "impressionRank", "impressionShare", "searchFrequencyRank", "weekRatio");
                urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), listExports, fileName + "(" + count++ + ")", KeywordCommodityTargetVo.class, build.currencyNew(KeywordCommodityTargetVo.class), excludeFileds));
            }
        }
        return urlList;
    }

    private ReportExcelVo getExcelVo(ReportVo obj) {
        ReportExcelVo excelVo = new ReportExcelVo();
        excelVo.setCountDate(obj.getCountDate());
        excelVo.setQuery(obj.getQuery());
        excelVo.setQueryCn(obj.getQueryCn());
        if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND.equalsIgnoreCase(obj.getKeywordText()) || Constants.keywords_related_to_your_brand.equalsIgnoreCase(obj.getKeywordText())) {
            excelVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN);
        } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES.equalsIgnoreCase(obj.getKeywordText()) || Constants.keywords_related_to_your_landing_pages.equalsIgnoreCase(obj.getKeywordText())) {
            excelVo.setKeywordText(Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN);
        } else {
            excelVo.setKeywordText(obj.getKeywordText());
        }

        SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByText(obj.getKeywordText());
        if (keywordGroupValueEnumByTextCn  != null) {
            excelVo.setKeywordText(keywordGroupValueEnumByTextCn.getTextCn());
        }

        if(StringUtils.isNotEmpty(obj.getMatchType())){
            if(Constants.TARGETING_EXPRESSION_SUBSTITUTES.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("同类商品");
            } else if (Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("紧密匹配");
            } else if (Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("宽泛匹配");
            } else if (Constants.TARGETING_EXPRESSION_COMPLEMENTS.equalsIgnoreCase(obj.getMatchType())){
                excelVo.setMatchType("关联商品");
            } else if (AdTargetTaskMatchTypeEnum.ASIN.getCode().equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("精确");
            } else if (AdTargetTaskMatchTypeEnum.ASIN_EXPANDED_FROM.getCode().equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("扩展");
            } else if (AdTargetTaskMatchTypeEnum.CATEGORY.getCode().equalsIgnoreCase(obj.getMatchType())) {
                excelVo.setMatchType("类目");
            }
        }
        if(StringUtils.isNotEmpty(obj.getTargetingExpression())){
            if(Constants.TARGETING_EXPRESSION_SUBSTITUTES.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("同类商品");
            }else if(Constants.TARGETING_EXPRESSION_CLOSE.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("紧密匹配");
            }else if(Constants.TARGETING_EXPRESSION_LOOSE.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("宽泛匹配");
            }else if(Constants.TARGETING_EXPRESSION_COMPLEMENTS.equalsIgnoreCase(obj.getTargetingExpression())){
                excelVo.setTargetingExpression("关联商品");
            }else{
                excelVo.setTargetingExpression(obj.getTargetingExpression());
            }
        }
        //导出标签填充
        List<String> queryTagList = new ArrayList<>();
        if (obj.getIsBroad() != null && obj.getIsBroad()) {
            queryTagList.add("投-广泛");
        }
        if (obj.getIsExact() != null && obj.getIsExact()) {
            queryTagList.add("投-精确");
        }
        if (obj.getIsPhrase() != null && obj.getIsPhrase()) {
            queryTagList.add("投-词组");
        }
        if (obj.getIsNegativePhrase() != null && obj.getIsNegativePhrase()) {
            queryTagList.add("否-词组");
        }
        if (obj.getIsNegativeExact() != null && obj.getIsNegativeExact()) {
            queryTagList.add("否-精确");
        }
        if (obj.getIsTargetAsin() != null && obj.getIsTargetAsin()) {
            queryTagList.add("投ASIN");
        }
        if (obj.getIsNeTargetAsin() != null && obj.getIsNeTargetAsin()) {
            queryTagList.add("否ASIN");
        }
        if (queryTagList.isEmpty()) {
            excelVo.setQueryTag("无标签");
        } else {
            StringBuilder queryTagBuilder = new StringBuilder();
            for (int i = 0; i < queryTagList.size(); i++) {
                queryTagBuilder.append(queryTagList.get(i));
                if (i < queryTagList.size() - 1) {
                    queryTagBuilder.append(",");
                }
            }
            excelVo.setQueryTag(queryTagBuilder.toString());
        }
        excelVo.setAdGroupName(obj.getAdGroupName());
        excelVo.setCampaignName(obj.getCampaignName());
        if(StringUtils.isNotEmpty(obj.getTargetingType())){
            excelVo.setTargetingType(Constants.AUTO.equals(obj.getTargetingType()) ? "自动":"手动");
        }
        excelVo.setPortfolioName(obj.getPortfolioName());
        excelVo.setImpressions(obj.getImpressions());
        excelVo.setClicks(obj.getClicks());
        excelVo.setRoas(obj.getRoas() == null ? "-" : obj.getRoas().toString());
        excelVo.setClickRate(obj.getClickRate() == null ? "-" : obj.getClickRate()+"%");
        excelVo.setCost(obj.getCost()!=null?currency+obj.getCost():"-");
        excelVo.setCpc(obj.getCpc()!=null?currency+obj.getCpc():"-");
        excelVo.setOrderNum(obj.getSaleNum());
        excelVo.setSalesConversionRate(obj.getSalesConversionRate() == null ? "-" : obj.getSalesConversionRate()+"%");
        excelVo.setCpa(obj.getCpa()!=null?currency+obj.getCpa():"-");
        excelVo.setSales(obj.getSales()!=null?currency+obj.getSales():"-");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos()+"%");
        excelVo.setAcos(obj.getAcos() == null ? "-" : obj.getAcos()+"%");
        excelVo.setAcots(obj.getAcots() == null ? "-" : obj.getAcots()+"%");
        excelVo.setAsots(obj.getAsots() == null ? "-" : obj.getAsots()+"%");
        excelVo.setRoas(obj.getRoas() == null ? "-" : obj.getRoas().toString());
        /**
         * TODO 广告报告重构
         * 本广告产品销售额
         */
        excelVo.setAdSales(obj.getAdSales()!=null?currency+obj.getAdSales():"-");
        //本广告产品订单量
        excelVo.setAdSaleNum(obj.getAdSaleNum());
        //广告销量
        excelVo.setSaleNum(obj.getOrderNum());
        //本广告产品销量
        excelVo.setAdSelfSaleNum(obj.getAdSelfSaleNum());
        //其他产品广告订单量
        excelVo.setAdOtherOrderNum(obj.getAdOtherOrderNum());
        //其他产品广告销售额
        excelVo.setAdOtherSales(obj.getAdOtherSales()!=null?currency+obj.getAdOtherSales():"-");
        //其他产品广告销量
        excelVo.setAdOtherSaleNum(obj.getAdOtherSaleNum());
        //“品牌新买家”订单量
        excelVo.setOrdersNewToBrand14d(obj.getOrdersNewToBrand14d());
        //“品牌新买家”销售额
        excelVo.setSalesNewToBrand14d(obj.getSalesNewToBrand14d()!=null?currency+obj.getSalesNewToBrand14d():"-");
        //搜索词展示量排名
        excelVo.setImpressionRank(obj.getImpressionRank());
        //搜索词展示份额
        excelVo.setImpressionShare(obj.getImpressionShare() == null ? "-" : obj.getImpressionShare()+"%");
        //“品牌新买家”订单百分比
        excelVo.setOrderRateNewToBrand14d(obj.getOrderRateNewToBrand14d() == null ? "-" : obj.getOrderRateNewToBrand14d()+"%");
        //“品牌新买家”销售额百分比
        excelVo.setSalesRateNewToBrand14d(obj.getSalesRateNewToBrand14d() == null ? "-" : obj.getSalesRateNewToBrand14d()+"%");
        //“品牌新买家”订单转化率
        excelVo.setOrdersNewToBrandPercentage14d(obj.getOrdersNewToBrandPercentage14d() == null ? "-" : obj.getOrdersNewToBrandPercentage14d()+"%");
        // 广告花费占比
        excelVo.setAdCostPercentage(StringUtils.isNotBlank(obj.getAdCostPercentage()) ? obj.getAdCostPercentage()+"%" : "0.00%");
        // 广告销售额占比
        excelVo.setAdSalePercentage(StringUtils.isNotBlank(obj.getAdSalePercentage()) ? obj.getAdSalePercentage()+"%" : "0.00%");
        // 广告订单量占比
        excelVo.setAdOrderNumPercentage(StringUtils.isNotBlank(obj.getAdOrderNumPercentage()) ? obj.getAdOrderNumPercentage()+"%" : "0.00%");
        // 广告销量占比
        excelVo.setOrderNumPercentage(StringUtils.isNotBlank(obj.getOrderNumPercentage()) ? obj.getOrderNumPercentage()+"%" : "0.00%");

        excelVo.setVideo5SecondViews(Optional.ofNullable(obj.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideo5SecondViewRate(Optional.ofNullable(obj.getVideo5SecondViewRate()).map(String::valueOf).map(s->s.concat("%")).orElse("0.00%"));
        excelVo.setVideoFirstQuartileViews(Optional.ofNullable(obj.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoMidpointViews(Optional.ofNullable(obj.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoThirdQuartileViews(Optional.ofNullable(obj.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoCompleteViews(Optional.ofNullable(obj.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
        excelVo.setVideoUnmutes(Optional.ofNullable(obj.getVideoUnmutes()).map(String::valueOf).orElse("0"));
        excelVo.setViewImpressions(Optional.ofNullable(obj.getViewImpressions()).map(String::valueOf).orElse("0"));
        excelVo.setViewabilityRate(Optional.ofNullable(obj.getViewabilityRate()).map(String::valueOf).map(s->s.concat("%")).orElse("0.00%"));
        excelVo.setViewClickThroughRate(Optional.ofNullable(obj.getViewClickThroughRate()).map(String::valueOf).map(s->s.concat("%")).orElse("0.00%"));
        excelVo.setAdvertisingUnitPrice(Optional.ofNullable(obj.getAdvertisingUnitPrice()).map(String::valueOf).map(e -> currency + e).orElse("0"));
        if (obj.getTitle() != null) {
            excelVo.setTitle(obj.getTitle());
        }
        if (obj.getPrice() != null) {
            excelVo.setPrice(currency + BigDecimal.valueOf(obj.getPrice()).setScale(2, RoundingMode.HALF_UP).toPlainString());
        }
        if (obj.getRating() != null) {
            excelVo.setRating(Optional.of(obj.getRating()).map(rating -> {
                try {
                    BigDecimal bd = new BigDecimal(rating);
                    return bd.setScale(1, RoundingMode.HALF_UP).toPlainString();
                } catch (Exception e) {
                    log.info("asin: {}, retaing: {} is not a number",obj.getAsin(),  rating);
                    return "";
                }
            }).orElse(""));
        }
        if (obj.getRatingCount() != null) {
            excelVo.setRatingCount(obj.getRatingCount().toString());
        }
        return excelVo;
    }
    private static Double calculationRateDouble(Double value1, Double value2) {
        return value2 == 0 ? 0 : DoubleUtil.divide(value1*100,value2,2);
    }
    private static WriteHandlerBuild setWriteHandlerBuild() {
        WriteHandlerBuild build = new WriteHandlerBuild();
        build = build.rate();
        //需要进行格式化的表头
        List<String> formatHeads = new ArrayList<>();
        formatHeads.add("impressions");
        formatHeads.add("clicks");
        build.integerFormat(formatHeads);
        return build;
    }
    private ReportRpcVo.Builder convertVoToRpcMessage(ReportVo reportVo) {
        ReportRpcVo.Builder reportBuilder = ReportRpcVo.newBuilder();

        if (reportVo!=null) {
            if (reportVo.getShopId() != null) {
                reportBuilder.setShopId(Int32Value.of(reportVo.getShopId()));
            }
            if (reportVo.getCountDate() != null) {
                reportBuilder.setCountDate(reportVo.getCountDate());
            }
            reportBuilder.setCpc(DoubleValue.of(Optional.ofNullable(reportVo.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setCost(DoubleValue.of(Optional.ofNullable(reportVo.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setSales(DoubleValue.of(Optional.ofNullable(reportVo.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAcos(DoubleValue.of(Optional.ofNullable(reportVo.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setRoas(DoubleValue.of(Optional.ofNullable(reportVo.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAcots(DoubleValue.of(Optional.ofNullable(reportVo.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setAsots(DoubleValue.of(Optional.ofNullable(reportVo.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setImpressions(Int32Value.of(Optional.ofNullable(reportVo.getImpressions()).orElse(0)));
            reportBuilder.setClicks(Int32Value.of(Optional.ofNullable(reportVo.getClicks()).orElse(0)));
            reportBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getOrderNum()).orElse(0)));
            reportBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getSaleNum()).orElse(0)));
            reportBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(reportVo.getClickRate()).orElse(0.0)));
            reportBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getSalesConversionRate()).orElse(0.0)));
            reportBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getAdConversionRate()).orElse(0.0)));
            reportBuilder.setCpa(DoubleValue.of(Optional.ofNullable(reportVo.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(reportVo.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
            reportBuilder.setNaturalClicks(Optional.ofNullable(reportVo.getNaturalClicks()).orElse("0"));
            reportBuilder.setNaturalOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getNaturalOrderNum()).orElse(0)));
            reportBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(reportVo.getAdClickRatio()).orElse(0.0)));
            reportBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(reportVo.getAdConversionRate()).orElse(0.0)));
            // 广告花费占比
            reportBuilder.setAdCostPercentage(StringUtils.isNotBlank(reportVo.getAdCostPercentage()) ? reportVo.getAdCostPercentage() : "0.00");
            // 广告销售额占比
            reportBuilder.setAdSalePercentage(StringUtils.isNotBlank(reportVo.getAdSalePercentage()) ? reportVo.getAdSalePercentage() : "0.00");
            // 广告订单量占比
            reportBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(reportVo.getAdOrderNumPercentage()) ? reportVo.getAdOrderNumPercentage() : "0.00");
            // 广告销量占比
            reportBuilder.setOrderNumPercentage(StringUtils.isNotBlank(reportVo.getOrderNumPercentage()) ? reportVo.getOrderNumPercentage() : "0.00");

            if (reportVo.getCampaignId() != null) {
                reportBuilder.setCampaignId(reportVo.getCampaignId());
            }
            if (reportVo.getCampaignName() != null) {
                reportBuilder.setCampaignName(reportVo.getCampaignName());
            }
            if (reportVo.getAdGroupId() != null) {
                reportBuilder.setAdGroupId(reportVo.getAdGroupId());
            }
            if (reportVo.getAdGroupName() != null) {
                reportBuilder.setAdGroupName(reportVo.getAdGroupName());
            }
            if (reportVo.getKeywordText() != null) {
                reportBuilder.setKeywordText(reportVo.getKeywordText());
            }
            if (reportVo.getMatchType() != null) {
                reportBuilder.setMatchType(reportVo.getMatchType());
            }
            if (reportVo.getKeywordId() != null) {
                reportBuilder.setKeywordId(reportVo.getKeywordId());
            }
            if (reportVo.getSku() != null) {
                reportBuilder.setSku(reportVo.getSku());
            }
            if (reportVo.getAsin() != null) {
                reportBuilder.setAsin(reportVo.getAsin());
            }
            if (reportVo.getParentAsin() != null) {
                reportBuilder.setParentAsin(reportVo.getParentAsin());
            }
            if (reportVo.getTitle() != null) {
                reportBuilder.setTitle(reportVo.getTitle());
            }
            if (reportVo.getPrice() != null) {
                reportBuilder.setPrice(BigDecimal.valueOf(reportVo.getPrice()).setScale(2, RoundingMode.HALF_UP).toPlainString());
            }
            if (reportVo.getRating() != null) {
                reportBuilder.setRating(reportVo.getRating());
            }
            if (reportVo.getRatingCount() != null) {
                reportBuilder.setRatingCount(reportVo.getRatingCount().toString());
            }
            if (reportVo.getMainImage() != null) {
                reportBuilder.setMainImage(reportVo.getMainImage());
            }
            if (reportVo.getQuery() != null) {
                reportBuilder.setQuery(reportVo.getQuery());
            }
            if (reportVo.getQueryCn() != null) {
                reportBuilder.setQueryCn(reportVo.getQueryCn());
            }
            if (reportVo.getNegaType() != null) {
                reportBuilder.setNegaType(reportVo.getNegaType());
            }
            if (reportVo.getTargetingType() != null) {
                reportBuilder.setTargetingType(reportVo.getTargetingType());
            }
            if (reportVo.getTargetingExpression() != null) {
                reportBuilder.setTargetingExpression(reportVo.getTargetingExpression());
            }
            if (reportVo.getTargetId() != null) {
                reportBuilder.setTargetId(reportVo.getTargetId());
            }
            if (reportVo.getAdId() != null) {
                reportBuilder.setAdId(reportVo.getAdId());
            }
            if (reportVo.getTargetingText() != null) {
                reportBuilder.setTargetingText(reportVo.getTargetingText());
            }
            if (reportVo.getSpCampaignType() != null) {
                reportBuilder.setSpCampaignType(reportVo.getSpCampaignType());
            }
            if (reportVo.getSpGroupType() != null) {
                reportBuilder.setSpGroupType(reportVo.getSpGroupType());
            }
            if (reportVo.getSpTargetType() != null) {
                reportBuilder.setSpTargetType(reportVo.getSpTargetType());
            }
            if (reportVo.getPortfolioName() != null) {
                reportBuilder.setPortfolioName(reportVo.getPortfolioName());
            }
            //标签填充
            if (reportVo.getIsBroad() != null) {
                reportBuilder.setIsBroad(BoolValue.of(reportVo.getIsBroad()));
            }
            if (reportVo.getIsPhrase() != null) {
                reportBuilder.setIsPhrase(BoolValue.of(reportVo.getIsPhrase()));
            }
            if (reportVo.getIsExact() != null) {
                reportBuilder.setIsExact(BoolValue.of(reportVo.getIsExact()));
            }
            if (reportVo.getIsNegativeExact() != null) {
                reportBuilder.setIsNegativeExact(BoolValue.of(reportVo.getIsNegativeExact()));
            }
            if (reportVo.getIsNegativePhrase() != null) {
                reportBuilder.setIsNegativePhrase(BoolValue.of(reportVo.getIsNegativePhrase()));
            }
            if (reportVo.getIsTargetAsin() != null) {
                reportBuilder.setIsTargetAsin(BoolValue.of(reportVo.getIsTargetAsin()));
            }
            if (reportVo.getIsNeTargetAsin() != null) {
                reportBuilder.setIsNeTargetAsin(BoolValue.of(reportVo.getIsNeTargetAsin()));
            }
            /**
             * TODO 广告报告重构
             * 本广告产品销售额
             */
            reportBuilder.setAdSales(DoubleValue.of(Optional.ofNullable(reportVo.getAdSales()).orElse(BigDecimal.ZERO).doubleValue()));
            //本广告产品订单量
            reportBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdSaleNum()).orElse(0)));
            //本广告产品销量
            reportBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdSelfSaleNum()).orElse(0)));
            //其他产品广告订单量
            reportBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(reportVo.getAdOtherOrderNum()).orElse(0)));
            //其他产品广告销售额
            reportBuilder.setAdOtherSales(DoubleValue.of(Optional.ofNullable(reportVo.getAdOtherSales()).orElse(BigDecimal.ZERO).doubleValue()));
            //其他产品广告销量
            reportBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(reportVo.getAdOtherSaleNum()).orElse(0)));
            //“品牌新买家”订单量
            if (reportVo.getOrdersNewToBrand14d() != null) {
                reportBuilder.setOrdersNewToBrandFTD(Int32Value.of(reportVo.getOrdersNewToBrand14d()));
            }
            //“品牌新买家”销售额
            if (reportVo.getSalesNewToBrand14d() != null) {
                reportBuilder.setSalesNewToBrandFTD(DoubleValue.of(reportVo.getSalesNewToBrand14d().doubleValue()));
            }
            //搜索词展示量排名
            if (reportVo.getImpressionRank() != null) {
                reportBuilder.setImpressionRank(Int32Value.of(reportVo.getImpressionRank()));
            }
            //搜索词展示份额
            if (reportVo.getImpressionShare() != null) {
                reportBuilder.setImpressionShare(DoubleValue.of(reportVo.getImpressionShare().doubleValue()));
            }
            //“品牌新买家”订单百分比
            if (reportVo.getOrderRateNewToBrand14d() != null) {
                reportBuilder.setOrderRateNewToBrandFTD(DoubleValue.of(reportVo.getOrderRateNewToBrand14d()));
            }
            //“品牌新买家”销售额百分比
            if (reportVo.getSalesRateNewToBrand14d() != null) {
                reportBuilder.setSalesRateNewToBrandFTD(DoubleValue.of(reportVo.getSalesRateNewToBrand14d()));
            }
            //“品牌新买家”订单转化率
            if (reportVo.getOrdersNewToBrandPercentage14d() != null) {
                reportBuilder.setOrdersNewToBrandPercentageFTD(DoubleValue.of(reportVo.getOrdersNewToBrandPercentage14d()));
            }

            reportBuilder.setVideo5SecondViews(Optional.ofNullable(reportVo.getVideo5SecondViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideo5SecondViewRate(Optional.ofNullable(reportVo.getVideo5SecondViewRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoFirstQuartileViews(Optional.ofNullable(reportVo.getVideoFirstQuartileViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoMidpointViews(Optional.ofNullable(reportVo.getVideoMidpointViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoThirdQuartileViews(Optional.ofNullable(reportVo.getVideoThirdQuartileViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoCompleteViews(Optional.ofNullable(reportVo.getVideoCompleteViews()).map(String::valueOf).orElse("0"));
            reportBuilder.setVideoUnmutes(Optional.ofNullable(reportVo.getVideoUnmutes()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewImpressions(Optional.ofNullable(reportVo.getViewImpressions()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewabilityRate(Optional.ofNullable(reportVo.getViewabilityRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setViewClickThroughRate(Optional.ofNullable(reportVo.getViewClickThroughRate()).map(String::valueOf).orElse("0"));
            reportBuilder.setAdvertisingUnitPrice(Optional.ofNullable(reportVo.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
            // 广告策略标签信息
            if(CollectionUtils.isNotEmpty(reportVo.getStrategyList())){
                List<com.meiyunji.sponsored.rpc.vo.AdStrategy> strategyList = new ArrayList<>();
                for (AdStrategyVo strategyVo : reportVo.getStrategyList()) {
                    com.meiyunji.sponsored.rpc.vo.AdStrategy.Builder strategyBuilder = com.meiyunji.sponsored.rpc.vo.AdStrategy.newBuilder();
                    strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
                    strategyBuilder.setStatus(strategyVo.getStatus());
                    strategyList.add(strategyBuilder.build());
                }
                reportBuilder.addAllAdStrategys(strategyList);
            }
        }

        return reportBuilder;
    }
}
