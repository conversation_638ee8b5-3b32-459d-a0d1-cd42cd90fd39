package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/4/21 19:36
 * @describe:
 */
@Getter
public enum WalmartSnapshotJobStatusEnum {
    PENDING("pending", "等待中"),
    PROCESSING("processing", "处理中"),
    DONE("done", "已完成"),
    FAILED("failed", "失败"),
    EXPIRED("expired", "已过期"),
    ;
    private String code;
    private String msg;

    WalmartSnapshotJobStatusEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static WalmartSnapshotJobStatusEnum getWalmartSnapshotJobStatusEnumByCode (String code) {
        for (WalmartSnapshotJobStatusEnum en : WalmartSnapshotJobStatusEnum.values()) {
            if (en.getCode().equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
