package com.meiyunji.sponsored.service.cpc.po;

import com.meiyunji.sponsored.common.base.BasePo;
import com.meiyunji.sponsored.common.springjdbc.DbColumn;
import com.meiyunji.sponsored.common.springjdbc.DbTable;
import lombok.Data;

/**
 * AmazonAdTargeting
 * <AUTHOR>
 */

@Data
@DbTable(value = "t_amazon_ad_targeting_extend")
public class AmazonAdTargetingExtend extends BasePo {
	/**
	 * id
	 */
	@DbColumn(value = "id",autoIncrement = true,key = true)
	private Long id;
	/**
	 * 商户uid
	 */
	@DbColumn(value = "puid")
	private Integer puid;
	/**
	 * 店铺ID
	 */
	@DbColumn(value = "shop_id")
	private Integer shopId;
	/**
	 * 站点
	 */
	@DbColumn(value = "marketplace_id")
	private String marketplaceId;
	/**
	 * 定位id
	 */
	@DbColumn(value = "target_id")
	private String targetId;
	/**
	 * 广告组id
	 */
	@DbColumn(value = "ad_group_id")
	private String adGroupId;

	/**
	 * 活动id
	 */
	@DbColumn(value = "campaign_id")
	private String campaignId;

	/**
	 * 预估曝光量下限
	 */
	@DbColumn(value = "estimated_impression_lower")
	private Long estimatedImpressionLower;
	/**
	 * 预估曝光量上限
	 */
	@DbColumn(value = "estimated_impression_upper")
	private Long estimatedImpressionUpper;

}