package com.meiyunji.sponsored.service.cpc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * Created by xp on 2021/4/16.
 * 广告位列表
 */
@Data
@ApiModel
public class PlacementPageVo extends CpcCommPageVo {
    @ApiModelProperty("id")
    private Long id;
    @ApiModelProperty("类型")
    private String type;
    @ApiModelProperty("位置")
    private String predicate;
    @ApiModelProperty("变动比率")
    private String percentage;
    @ApiModelProperty("企业购广告位竞价调整")
    private String amazonBusinessPercentage;
    @ApiModelProperty("策略")
    private String strategy;
    @ApiModelProperty("活动ID")
    private String campaignId;
    @ApiModelProperty("活动名称")
    private String campaignName;
    @ApiModelProperty("活动投放类型")
    private String campaignTargetingType;
    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty("广告组合名称")
    private String portfolioName;
    @ApiModelProperty("活动状态")
    private String campaignState;
}
