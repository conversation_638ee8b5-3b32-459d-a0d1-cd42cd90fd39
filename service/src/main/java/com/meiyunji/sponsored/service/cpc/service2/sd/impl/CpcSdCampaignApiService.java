package com.meiyunji.sponsored.service.cpc.service2.sd.impl;

import com.amazon.advertising.mode.campaigns.CampaignResult;
import com.amazon.advertising.sd.constant.TacticEnum;
import com.amazon.advertising.sd.entity.campaign.*;
import com.amazon.advertising.sd.mode.Campaign;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.common.util.AmazonResponseUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdCampaignAllDao;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignAll;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service2.ICpcHistoryApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcApiHelper;
import com.meiyunji.sponsored.service.cpc.util.CpcUpdateAdAuthHelper;
import com.meiyunji.sponsored.service.cpc.vo.BatchCampaignVo;
import com.meiyunji.sponsored.service.cpc.vo.BatchResponseVo;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioErrorMsgVo;
import com.meiyunji.sponsored.service.stream.enums.ManagementStreamResponseStatusEnum;
import com.meiyunji.sponsored.service.syncAd.enums.SdStateEnum;
import com.meiyunji.sponsored.service.taskGrpcApi.AadasApiFactory;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/7/7.
 * 对接口二次封装，直接和广告接口交互
 */
@Component
@Slf4j
public class CpcSdCampaignApiService {

    @Autowired
    private CpcApiHelper cpcApiHelper;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private AadasApiFactory aadasApiFactory;
    @Autowired
    private ICpcHistoryApiService cpcHistoryApiService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Resource
    private IShopAuthService shopAuthService;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    public static final Map<String, String> errorMap = new HashMap<String, String>() {{
        put(" End date cannot precede or equal start date", "开始时间不得晚于结束时间");
    }};

    public Result create(ShopAuth shop, AmazonAdProfile amazonAdProfile, AmazonAdCampaignAll amazonSdAdCampaign) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Campaign campaign = makeEntity4Creation(amazonSdAdCampaign);

        CreateCampaignsResponse response = cpcApiHelper.call(shop, () -> CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Lists.newArrayList(campaign)));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getStatusCode() == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告活动失败";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            CampaignResult campaignResult = response.getResultList().get(0);
            if ("SUCCESS".equals(campaignResult.getCode())) {
                amazonSdAdCampaign.setCampaignId(campaignResult.getCampaignId().toString());
                return ResultUtil.success();
            }
            if (StringUtils.isNotBlank(campaignResult.getDetails())) {
                errMsg = campaignResult.getDetails();
            } else if (StringUtils.isNotBlank(campaignResult.getDescription())) {
                errMsg = campaignResult.getDescription();
            }
        } else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SD广告权限，请到Amazon后台开通SD广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails();
            }
        }

        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }

    public Result createNew(ShopAuth shop, AmazonAdProfile amazonAdProfile, AmazonAdCampaignAll amazonSdAdCampaign) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Campaign campaign = makeEntity4Creation(amazonSdAdCampaign);

        CreateCampaignsResponse response = cpcApiHelper.call(shop, () -> CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).create(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Lists.newArrayList(campaign)));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getStatusCode() == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告活动失败";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            CampaignResult campaignResult = response.getResultList().get(0);
            if ("SUCCESS".equals(campaignResult.getCode())) {
                amazonSdAdCampaign.setCampaignId(campaignResult.getCampaignId().toString());
                return ResultUtil.success();
            }
            if (StringUtils.isNotBlank(campaignResult.getDetails())) {
                errMsg = campaignResult.getDetails().trim();
            } else if (StringUtils.isNotBlank(campaignResult.getDescription())) {
                errMsg = campaignResult.getDescription().trim();
            }
        } else if (response.getError() != null) {
            if ("403".equals(response.getError().getCode())) {
                errMsg = "店铺没有SD广告权限，请到Amazon后台开通SD广告管理";
            } else if (StringUtils.isNotBlank(response.getError().getDetails())) {
                errMsg = response.getError().getDetails().trim();
            }
        }

        return ResultUtil.returnErr(errMsg);
    }

    public Result update(ShopAuth shop, AmazonAdProfile amazonAdProfile, AmazonAdCampaignAll amazonSdAdCampaign) {
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Campaign campaign = makeEntity4Update(amazonSdAdCampaign);

        UpdateCampaignsResponse response = cpcApiHelper.call(shop, () -> CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), Lists.newArrayList(campaign)));

        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";
        if (CollectionUtils.isNotEmpty(response.getResultList())) {
            CampaignResult campaignResult = response.getResultList().get(0);
            if ("SUCCESS".equals(campaignResult.getCode())) {
                return ResultUtil.success();
            }

            if (StringUtils.isNotBlank(campaignResult.getDetails())) {
                errMsg = campaignResult.getDetails();
            } else {
                if (errorMap.containsKey(campaignResult.getDescription())) {
                    errMsg = errorMap.get(campaignResult.getDescription());
                } else {
                    errMsg = campaignResult.getDescription();
                }
            }
        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDetails())) {
            errMsg = response.getError().getDetails();
        }

        return ResultUtil.returnErr(AmazonErrorUtils.getError(errMsg));
    }

    /**
     * 同步所有的活动
     *
     * @param shop：
     * @param campaignId：逗号分隔
     * @param syncHistory：是否同步日志
     */
    public void syncCampaigns(ShopAuth shop, String campaignId, List<SdStateEnum> stateList, boolean syncHistory, boolean isThrow, boolean isProxy) {
        long start = System.currentTimeMillis();
        if (shop == null) {
            return;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdCampaigns--配置信息为空");
            return;
        }

        String stateFilter;
        if (CollectionUtils.isEmpty(stateList)) {
            stateFilter = Arrays.stream(SdStateEnum.values()).map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        } else {
            stateFilter = stateList.stream().map(SdStateEnum::getStateType).collect(Collectors.joining(","));
        }

        //获取活动的基本信息
        CampaignsClient client = CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        if (isProxy) {
            client = CampaignsClient.getInstance(true);
        }

        int startIndex = 0;
        int count = 500;
        ListCampaignExResponse response;
        while (true) {
            int finalSartIndex = startIndex;
            CampaignsClient finalClient = client;
            response = cpcApiHelper.call(shop, () -> finalClient.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, stateFilter, null, campaignId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD campaigns rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                CampaignsClient finalClient1 = client;
                response = cpcApiHelper.call(shop, () -> finalClient1.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, stateFilter, null, campaignId));
                retry++;
            }

            if (AmazonResponseUtil.isError(response) && isThrow) {
                throw new ServiceException("请求异常");
            }

            if (response == null || CollectionUtils.isEmpty(response.getCampaignList())) {
                break;
            }

            int size = response.getCampaignList().size();

            List<AmazonAdCampaignAll> amazonAdCampaigns = new ArrayList<>(size);
            //新表入库
            AmazonAdCampaignAll amazonAdCampaignall;
            List<AmazonAdCampaignAll> amazonAdCampaignAlls = new ArrayList<>(size);
            for (Campaign campaign : response.getCampaignList()) {

                // 目前亚马逊只支持这两种类型的活动，以前的remarking, T00001接口虽然返回但是我们不再维护
                if (TacticEnum.T00030.name().equals(campaign.getTactic())
                        || TacticEnum.T00020.name().equals(campaign.getTactic())) {
                    amazonAdCampaignall = turnCampainToAllPO(campaign);
                    if (StringUtils.isNotBlank(amazonAdCampaignall.getCampaignId())) {
                        amazonAdCampaignall.setPuid(shop.getPuid());
                        amazonAdCampaignall.setShopId(shop.getId());
                        amazonAdCampaignall.setMarketplaceId(shop.getMarketplaceId());
                        amazonAdCampaignall.setProfileId(amazonAdProfile.getProfileId());
                        amazonAdCampaignall.setSyncOutOfBudgetTimeState(0);

                        amazonAdCampaignAlls.add(amazonAdCampaignall);
                    }
                }
            }

            if (amazonAdCampaignAlls.size() > 0) {
                Map<String, AmazonAdCampaignAll> campaignMap = amazonAdCampaignAllDao.listByCampaignId(shop.getPuid(), shop.getId(),
                                amazonAdCampaigns.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList()),Constants.SD)
                        .stream().filter(e -> StringUtils.isNotBlank(e.getCampaignId()))
                        .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));

                List<AmazonAdCampaignAll> insertList = new ArrayList<>();
                List<AmazonAdCampaignAll> updateList = new ArrayList<>();
                AmazonAdCampaignAll old;

                for (AmazonAdCampaignAll c : amazonAdCampaignAlls) {
                    if (campaignMap.containsKey(c.getCampaignId())) {
                        old = campaignMap.get(c.getCampaignId());
                        if (StringUtils.isNotBlank(c.getName())) {
                            old.setName(c.getName());
                        }
                        if (StringUtils.isNotBlank(c.getBudgetType())) {
                            old.setBudgetType(c.getBudgetType());
                        }
                        if (c.getBudget() != null) {
                            old.setBudget(c.getBudget());
                        }
                        if (c.getStartDate() != null) {
                            old.setStartDate(c.getStartDate());
                        }
                        old.setEndDate(c.getEndDate());
                        old.setPortfolioId(c.getPortfolioId());
                        if (StringUtils.isNotBlank(c.getCostType())) {
                            old.setCostType(c.getCostType());
                        }
                        if (StringUtils.isNotBlank(c.getState())) {
                            old.setState(c.getState());
                        }
                        if (StringUtils.isNotBlank(c.getTactic())) {
                            old.setTactic(c.getTactic());
                            old.setAdTargetType(c.getAdTargetType());
                        }

                        if (StringUtils.isNotBlank(c.getServingStatus())) {
                            old.setServingStatus(c.getServingStatus());
                        }
                        if (c.getOutOfBudgetTime()!=null) {
                            old.setOutOfBudgetTime(c.getOutOfBudgetTime());
                        }
                        if (c.getSyncOutOfBudgetTimeState()!=null) {
                            old.setSyncOutOfBudgetTimeState(c.getSyncOutOfBudgetTimeState());
                        }
                        if (c.getCreationDate() != null) {
                            old.setCreationDate(c.getCreationDate());
                        }
                        if (c.getLastUpdatedDate() != null) {
                            old.setLastUpdatedDate(c.getLastUpdatedDate());
                        }
                        updateList.add(old);
                    } else {
                        c.setCreateInAmzup(0);
                        insertList.add(c);
                    }
                }

                try {
                    amazonAdCampaignAllDao.batchAddSd(shop.getPuid(), insertList,syncHistory);
                    amazonAdCampaignAllDao.batchUpdateSd(shop.getPuid(), updateList,syncHistory);
                } catch (Exception e) {
                    log.error("syncSdCampaign:", e);
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
        log.info("同步所有SD广告活动信息时间 puid: {} ,shopid: {} 花费时间 {}",shop.getPuid(),shop.getId(),System.currentTimeMillis()-start);

    }

    public void syncCampaigns(ShopAuth shop, String campaignId, boolean syncHistory, boolean throwException) throws InterruptedException {
        syncCampaigns(shop, campaignId, null, syncHistory, throwException);
    }

    public void syncCampaigns(ShopAuth shop, String campaignId, boolean syncHistory) throws InterruptedException {
        syncCampaigns(shop, campaignId, syncHistory, false);
    }

    public void syncCampaigns(ShopAuth shop, String campaignId, List<SdStateEnum> stateList, boolean syncHistory, boolean isThrow) {
        syncCampaigns(shop, campaignId, stateList, syncHistory, isThrow, false);
    }

    /**
     * 归档
     *
     * @param amazonSdAdCampaign：
     * @return ：Result
     */
    public Result archive(AmazonAdCampaignAll amazonSdAdCampaign) {
        if (amazonSdAdCampaign == null) {
            return ResultUtil.error("没有活动信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonSdAdCampaign.getShopId(), amazonSdAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        ArchiveCampaignResponse response = cpcApiHelper.call(shop, () -> CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).archive(shopAuthService.getAdToken(shop),
                amazonSdAdCampaign.getProfileId(), shop.getMarketplaceId(), Long.valueOf(amazonSdAdCampaign.getCampaignId())));

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getResult() != null && response.getResult().getCampaignId() != null) {
            return ResultUtil.success();
        }

        //处理返回结果中的错误信息
        String msg = "网络延迟，请稍后重试";
        if (response.getResult() != null) {
            msg = AmazonErrorUtils.getError(response.getResult().getDetails());
        }
        return ResultUtil.error(msg);
    }

    public Result<List<PortfolioErrorMsgVo>> batchUpdateCampaigns(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<Campaign> campaignList)  {
        List<PortfolioErrorMsgVo> errList = new ArrayList<>();

        UpdateCampaignsResponse response = cpcApiHelper.call(shop, () -> CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                amazonAdProfile.getMarketplaceId(), campaignList));
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }
        if (response.getStatusCode() == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "网络延迟，请稍后重试";

        PortfolioErrorMsgVo msgVo;
        if (response.getResultList() != null && response.getResultList().size() > 0) {
            int i = 0;
            List<CampaignResult> resultList = response.getResultList();
            for (CampaignResult result : resultList) {  //可能部分成功
                if ("SUCCESS".equals(result.getCode())) {
                    //更新成功同步一次活动
                    if (result.getCampaignId() != null) {
                        try {
                            syncCampaigns(shop, String.valueOf(result.getCampaignId()), false);
                        } catch (Exception e) {
                            log.error("同步广告活动异常shopId:" + shop.getId(), e);
                        }
                    }
                } else if (StringUtils.isNotBlank(result.getDescription()) || StringUtils.isNotBlank(result.getDetails())) {
                    if (result.getCampaignId() != null) { //避免无效参数导致id没有返回
                        msgVo = new PortfolioErrorMsgVo();
                        msgVo.setCampaignId(String.valueOf(result.getCampaignId()));;
                        msgVo.setErrMsg(result.getDescription() != null ? result.getDescription() : result.getDetails());
                        errList.add(msgVo);
                    } else {
                        Campaign campaign = campaignList.get(i);
                        msgVo = new PortfolioErrorMsgVo();
                        msgVo.setCampaignId(String.valueOf(campaign.getCampaignId()));
                        msgVo.setErrMsg(result.getDescription() != null ? result.getDescription() : result.getDetails());
                    }
                }
            }
        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getDescription())) {
            errMsg = response.getError().getDescription();
            return ResultUtil.returnErr(errMsg);
        } else {
            return ResultUtil.returnErr(errMsg);
        }

        return ResultUtil.returnSucc(errList);
    }


    public List<Campaign> detectAdTypeSyncSdCampaign(ShopAuth shop) {
        if (shop == null) {
            return null;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            return null;
        }

        //获取活动的基本信息
        CampaignsClient client = CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 1;
        ListCampaignExResponse response;

        String accessToken = aadasApiFactory.getAccessToken(shop.getSellingPartnerId(), shop.getMarketplaceId());

        response =  client.getListEx(accessToken, amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                startIndex, count, null, null, null);

        if (response == null) {
            return null;
        }

        return response.getCampaignList();
    }

    // 组装接口数据
    private Campaign makeEntity4Creation(AmazonAdCampaignAll amazonAdCampaign) {
        Campaign campaign = new Campaign();
        campaign.setName(amazonAdCampaign.getName());
        campaign.setBudgetType(amazonAdCampaign.getBudgetType());
        campaign.setBudget(amazonAdCampaign.getBudget().doubleValue());

        campaign.setStartDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getStartDate(), "yyyyMMdd"));
        if (amazonAdCampaign.getEndDate() != null) {
            campaign.setEndDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getEndDate(), "yyyyMMdd"));
        }
        if (amazonAdCampaign.getPortfolioId() != null) {
            if (!"".equalsIgnoreCase(amazonAdCampaign.getPortfolioId())) {
                campaign.setPortfolioId(Long.valueOf(amazonAdCampaign.getPortfolioId()));
            } else {
                campaign.setPortfolioId(0L);
            }
        }

        campaign.setCostType(amazonAdCampaign.getCostType());
        campaign.setState(amazonAdCampaign.getState());
        campaign.setTactic(amazonAdCampaign.getTactic());

        return campaign;
    }

    private Campaign makeEntity4Update(AmazonAdCampaignAll amazonSdAdCampaign) {
        Campaign campaign = new Campaign();
        campaign.setCampaignId(Long.valueOf(amazonSdAdCampaign.getCampaignId()));
        campaign.setName(amazonSdAdCampaign.getName());
        if (amazonSdAdCampaign.getBudget() != null) {
            campaign.setBudget(amazonSdAdCampaign.getBudget().doubleValue());
        }

        if (amazonSdAdCampaign.getStartDate() != null) {
            campaign.setStartDate(DateUtil.dateToStrWithFormat(amazonSdAdCampaign.getStartDate(), "yyyyMMdd"));
        }
        campaign.setEndDate(amazonSdAdCampaign.getEndTimeStr());
        if (amazonSdAdCampaign.getPortfolioId() != null) {
            if ("".equalsIgnoreCase(amazonSdAdCampaign.getPortfolioId())) {
                campaign.setPortfolioId(0L);
            } else {
                campaign.setPortfolioId(Long.valueOf(amazonSdAdCampaign.getPortfolioId()));
            }
        }

        campaign.setState(amazonSdAdCampaign.getState());
        return campaign;
    }


    // 把接口返回的dto转换成po
    private AmazonAdCampaignAll turnCampainToAllPO(Campaign campaign) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        if (campaign.getCampaignId() != null) {
            amazonAdCampaign.setCampaignId(campaign.getCampaignId().toString());
        }
        amazonAdCampaign.setName(campaign.getName());
        amazonAdCampaign.setBudgetType(campaign.getBudgetType());
        if (campaign.getBudget() != null && campaign.getBudget() > 0) {
            amazonAdCampaign.setBudget(BigDecimal.valueOf(campaign.getBudget()));
        }
        if (StringUtils.isNotBlank(campaign.getStartDate())) {
            amazonAdCampaign.setStartDate(DateUtil.strToDate(campaign.getStartDate(), "yyyyMMdd"));
        }
        // 活动结束时间是可以为空的
        if (StringUtils.isNotBlank(campaign.getEndDate())) {
            amazonAdCampaign.setEndDate(DateUtil.strToDate(campaign.getEndDate(), "yyyyMMdd"));
        }

        if (campaign.getPortfolioId() != null) {
            amazonAdCampaign.setPortfolioId(String.valueOf(campaign.getPortfolioId()));
        }

        amazonAdCampaign.setCostType(campaign.getCostType());
        amazonAdCampaign.setState(campaign.getState());
        amazonAdCampaign.setTactic(campaign.getTactic());
        amazonAdCampaign.setServingStatus(campaign.getServingStatus());

        if (StringUtils.isNotBlank(campaign.getCreationDate())) {
            amazonAdCampaign.setCreationDate(new Date(Long.parseLong(campaign.getCreationDate())).toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime());
        }
        if (StringUtils.isNotBlank(campaign.getLastUpdatedDate())) {
            amazonAdCampaign.setLastUpdatedDate(new Date(Long.parseLong(campaign.getLastUpdatedDate())).toInstant().atOffset(ZoneOffset.of("+8")).toLocalDateTime());
        }

        return amazonAdCampaign;
    }

    public Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> update(ShopAuth shop, AmazonAdProfile amazonAdProfile, List<AmazonAdCampaignAll> AmazonSdAdCampaigns, String type) {

        BatchResponseVo<BatchCampaignVo,AmazonAdCampaignAll> batchResponseVo = new BatchResponseVo<>();
        List<Campaign> campaigns = AmazonSdAdCampaigns.stream().map(e -> {
            return convertCampaign(e, type);
        }).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> amazonSdAdCampaignMap = AmazonSdAdCampaigns.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
       UpdateCampaignsResponse response = cpcApiHelper.call(shop, () -> CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).update(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(),
                shop.getMarketplaceId(), campaigns));
        List<AmazonAdCampaignAll> successList = Lists.newArrayList();
        List<BatchCampaignVo> errorList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getResultList() != null && response.getResultList().size() > 0) {

            List<CampaignResult> resultList = response.getResultList();
            List<Long> successId = Lists.newArrayList();
            for (CampaignResult campaignResult : resultList) {

                if ("SUCCESS".equals(campaignResult.getCode())) {
                    AmazonAdCampaignAll amazonSdAdCampaignSuccess = amazonSdAdCampaignMap.remove(String.valueOf(campaignResult.getCampaignId()));
                    if (amazonSdAdCampaignSuccess != null) {
                        successList.add(amazonSdAdCampaignSuccess);
                    }
                    successId.add(amazonSdAdCampaignSuccess.getId());
                } else {
                    AmazonAdCampaignAll amazonSdAdCampaignFail = amazonSdAdCampaignMap.remove(String.valueOf(campaignResult.getCampaignId()));
                    if (amazonSdAdCampaignFail != null) {
                        BatchCampaignVo sdUpdateCampaignVoError = new BatchCampaignVo();
                        sdUpdateCampaignVoError.setCampaignId(amazonSdAdCampaignFail.getCampaignId());
                        sdUpdateCampaignVoError.setId(amazonSdAdCampaignFail.getId());
                        sdUpdateCampaignVoError.setName(amazonSdAdCampaignFail.getName());
                        //更新失败数据处理
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(campaignResult.getDescription())) {
                            sdUpdateCampaignVoError.setFailReason(AmazonErrorUtils.getError(campaignResult.getDescription()));
                        } else {
                            sdUpdateCampaignVoError.setFailReason("更新失败，请稍后重试");
                        }
                        errorList.add(sdUpdateCampaignVoError);
                    }

                }
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSdAdCampaignMap)) {
                amazonSdAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo sdUpdateCampaignVoError = new BatchCampaignVo();
                    sdUpdateCampaignVoError.setCampaignId(v.getCampaignId());
                    sdUpdateCampaignVoError.setId(v.getId());
                    sdUpdateCampaignVoError.setName(v.getName());
                    sdUpdateCampaignVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sdUpdateCampaignVoError);
                });
            }

        } else if (response.getError() != null && org.apache.commons.lang3.StringUtils.isNotBlank(response.getError().getDescription())) {
            return ResultUtil.error(AmazonErrorUtils.getError(response.getError().getDescription()));
        } else {
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonSdAdCampaignMap)) {
                amazonSdAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo sdUpdateCampaignVoError = new BatchCampaignVo();
                    sdUpdateCampaignVoError.setId(v.getId());
                    sdUpdateCampaignVoError.setCampaignId(v.getCampaignId());
                    sdUpdateCampaignVoError.setName(v.getName());
                    sdUpdateCampaignVoError.setFailReason("更新失败，请稍后重试");
                    errorList.add(sdUpdateCampaignVoError);
                });
            }
        }
        batchResponseVo.setCountNum(AmazonSdAdCampaigns.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);

    }

    public List<Campaign> checkAdTypeSyncSdCampaign(ShopAuth shop) {
        if (shop == null) {
            return null;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            return null;
        }

        //获取活动的基本信息
        CampaignsClient client = CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 1;
        ListCampaignExResponse response;

        String accessToken = aadasApiFactory.getAccessToken(shop.getSellingPartnerId(), shop.getMarketplaceId());

        response =  client.getListEx(accessToken, amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                startIndex, count, null, null, null);

        if (response == null) {
            return new ArrayList<>();
        }

        if (response.getStatusCode() == 401) {
            return null;
        }

        if (response.getCampaignList() == null) {
            return new ArrayList<>();
        }

        return response.getCampaignList();
    }

    /**
     * 同步所有的活动
     *
     * @param shop：
     * @param campaignId：逗号分隔
     */
    public List<AmazonAdCampaignAll> syncCampaigns(ShopAuth shop, String campaignId) throws InterruptedException {
        List<AmazonAdCampaignAll> amazonAdCampaignAlls = new ArrayList<>();
        long start = System.currentTimeMillis();
        if (shop == null) {
            return amazonAdCampaignAlls;
        }

        //先获取到配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(shop.getPuid(), shop.getId());
        if (amazonAdProfile == null) {
            log.error("syncSdCampaigns--配置信息为空");
            return amazonAdCampaignAlls;
        }

        //获取活动的基本信息
        CampaignsClient client = CampaignsClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable());
        int startIndex = 0;
        int count = 500;
        ListCampaignExResponse response;
        while (true) {
            int finalSartIndex = startIndex;
            response = cpcApiHelper.call(shop, () -> client.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                    finalSartIndex, count, null, null, campaignId));
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode().intValue() == AmazonAdUtils.rateLimitingCode) {
                log.info("SD campaigns rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if(retry > AmazonAdUtils.retry) {
                    break;
                }
                Thread.sleep(AmazonAdUtils.exponentialBackOffSec(retry) * 1000);
                response = cpcApiHelper.call(shop, () -> client.getListEx(shopAuthService.getAdToken(shop), amazonAdProfile.getProfileId(), shop.getMarketplaceId(),
                        finalSartIndex, count, null, null, campaignId));
                retry++;
            }
            if (response == null || CollectionUtils.isEmpty(response.getCampaignList())) {
                break;
            }

            int size = response.getCampaignList().size();

            //新表入库
            AmazonAdCampaignAll amazonAdCampaignall;
            for (Campaign campaign : response.getCampaignList()) {

                // 目前亚马逊只支持这两种类型的活动，以前的remarking, T00001接口虽然返回但是我们不再维护
                if (TacticEnum.T00030.name().equals(campaign.getTactic())
                        || TacticEnum.T00020.name().equals(campaign.getTactic())) {
                    amazonAdCampaignall = turnCampainToAllPO(campaign);
                    if (StringUtils.isNotBlank(amazonAdCampaignall.getCampaignId())) {
                        amazonAdCampaignall.setPuid(shop.getPuid());
                        amazonAdCampaignall.setShopId(shop.getId());
                        amazonAdCampaignall.setMarketplaceId(shop.getMarketplaceId());
                        amazonAdCampaignall.setProfileId(amazonAdProfile.getProfileId());
                        amazonAdCampaignall.setSyncOutOfBudgetTimeState(0);

                        amazonAdCampaignAlls.add(amazonAdCampaignall);
                    }
                }
            }

            if (size < count) {
                break;
            }

            startIndex += size;
        }
        log.info("同步所有SD广告活动信息时间 puid: {} ,shopid: {} 花费时间 {}",shop.getPuid(),shop.getId(),System.currentTimeMillis()-start);
        return amazonAdCampaignAlls;
    }

    private Campaign convertCampaign(AmazonAdCampaignAll vo, String type){

        Campaign cam  = new Campaign();
        cam.setCampaignId(Long.valueOf(vo.getCampaignId()));
        if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            cam.setState(vo.getState());
        } else if(Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)){
            cam.setBudget(vo.getBudget().doubleValue());
        }
        return cam;
    }
}
