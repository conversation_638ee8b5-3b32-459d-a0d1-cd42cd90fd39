package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IBaseShardingDao;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdCampaignNetargetingSp;
import com.meiyunji.sponsored.service.cpc.vo.AsinLibsVo;
import com.meiyunji.sponsored.service.cpc.vo.CampaignNeTargetingSpParam;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: wade
 * @date: 2021/8/20 11:38
 * @describe: 广告活动-否定商品DAO
 */
public interface IAmazonAdCampaignNetargetingSpDao extends IBaseShardingDao<AmazonAdCampaignNetargetingSp> {

    /**
     * 批量保存数据
     * @param puid
     * @param list
     */
    void batchSave(Integer puid, List<AmazonAdCampaignNetargetingSp> list);

    /**
     * 分页查询
     *
     * @param puid
     * @param campaignNeTargetingSpParam
     * @return
     */
    Page<AmazonAdCampaignNetargetingSp> pageList(Integer puid, CampaignNeTargetingSpParam campaignNeTargetingSpParam);

    List<AmazonAdCampaignNetargetingSp> ListByCampaignId(Integer puid, Integer shopId, String campaignId);

    /**
     * 同步asin信息
     *
     * @param puid
     * @param shopId
     * @param startDate
     * @param campaignId
     * @return
     */
    List<AmazonAdCampaignNetargetingSp> listNoAsinInfo(Integer puid, Integer shopId, String startDate, String campaignId);


    /**
     * 批量修改asin信息
     * @param puid
     * @param needUpdateList
     */
    void batchUpdateAsinInfo(Integer puid, List<AmazonAdCampaignNetargetingSp> needUpdateList);

    List<String> getArchivedItems(Integer puid, Integer shopId);

    List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt);

    /**
     * 批量修改批量归档信息
     * @param puid
     * @param needUpdateList
     */
    void batchUpdateArchive(Integer puid, List<AmazonAdCampaignNetargetingSp> needUpdateList);

    List<AmazonAdCampaignNetargetingSp> listByTargetId(Integer puid, Integer shopId, List<String> targetIds);

    List<AmazonAdCampaignNetargetingSp> listByTargetId(Integer puid, List<Integer> shopIds, List<String> targetIds);

    List<AsinLibsVo> getCountByAsin(Integer puid, PageListAsinsRequest param, List<Integer> shopIdList, List<String> asinList);

    List<AmazonAdCampaignNetargetingSp> listByTargetText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> neTargetInfos);
}
