package com.meiyunji.sponsored.service.multiple.targets.enums;

import com.meiyunji.sponsored.common.util.StringUtil;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投放列表页-报告统计枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSdColumnEnum {
    COST("costDoris", " ,IFNULL(sum(r.cost),0) `costDoris`", ",IFNULL(sum(r.cost*d.rate),0) `costDoris` "),
    TOTAL_SALES("totalSalesDoris", " ,IFNULL(sum(sales14d),0) totalSalesDoris", ",IFNULL(sum(sales14d*d.rate),0) totalSalesDoris"),
    AD_SALES("adSalesDoris", " ,IFNULL(sum(sales14d_same_sku),0) adSalesDoris", " ,IFNULL(sum(sales14d_same_sku*d.rate),0) adSalesDoris"),
    IMPRESSIONS("impressionsDoris", " ,IFNULL(sum(`impressions`),0) impressionsDoris",""),
    CLICKS("clicksDoris", " ,IFNULL(sum(`clicks`),0) clicksDoris",""),
    SALE_NUM("orderNumDoris", " ,IFNULL(sum(conversions14d),0) orderNumDoris",""),
    AD_SALE_NUM("adOrderNumDoris", " ,IFNULL(sum(conversions14d_same_sku),0) adOrderNumDoris",""),
    ORDER_NUM("saleNumDoris", " ,IFNULL(sum(units_ordered14d),0) saleNumDoris",""),
    VIEW_IMPRESSIONS("viewImpressionsDoris", " ,IFNULL(sum(view_impressions),0) viewImpressionsDoris",""),
    SALES_NEW_TO_BRAND("salesNewToBrand14dDoris", " ,IFNULL(sum(`sales_new_to_brand14d`),0) salesNewToBrand14dDoris", " ,IFNULL(sum(`sales_new_to_brand14d`*d.rate),0) salesNewToBrand14dDoris"),
    ORDERS_NEW_TO_BRAND("ordersNewToBrand14dDoris", " ,IFNULL(sum(orders_new_to_brand14d),0) ordersNewToBrand14dDoris",""),
    NEW_TO_BRAND_DETAIL_PAGE_VIEWS("newToBrandDetailPageViewsDoris", " ,IFNULL(sum(`new_to_brand_detail_page_views`),0) AS newToBrandDetailPageViewsDoris",""),
    ADD_TO_CART("addToCartDoris", " ,IFNULL(sum(`add_to_cart`),0) AS addToCartDoris",""),
    VIDEO_FIRST_QUARTILE_VIEWS("videoFirstQuartileViewsDoris", " ,IFNULL(sum(`video_first_quartile_views`),0) AS `videoFirstQuartileViewsDoris`",""),
    VIDEO_MIDPOINT_VIEWS("videoMidpointViewsDoris", " ,IFNULL(sum(`video_Midpoint_Views`),0) AS `videoMidpointViewsDoris`",""),
    VIDEO_THIRD_QUARTILE_VIEWS("videoThirdQuartileViewsDoris", " ,IFNULL(sum(`video_third_quartile_views`),0) AS `videoThirdQuartileViewsDoris`",""),
    VIDEO_COMPLETE_VIEWS("videoCompleteViewsDoris", " ,IFNULL(sum(`video_complete_views`),0) AS `videoCompleteViewsDoris`",""),
    VIDEO_UNMUTES("videoUnmutesDoris", " ,IFNULL(sum(`video_unmutes`),0) AS `videoUnmutesDoris`",""),
    BRANDED_SEARCHES("brandedSearches14dDoris", " ,IFNULL(sum(`branded_searches14d`),0) AS `brandedSearches14dDoris`",""),
    DETAIL_PAGE_VIEW("detailPageView14dDoris", " ,IFNULL(sum(`detail_page_view14d`),0) AS `detailPageView14dDoris`",""),
    ;

    // 排序字段
    private final String code;
    // 字段
    private final String column;
    // 汇率字段
    private final String rateColumn;

    TargetSdColumnEnum(String code, String column, String rateColumn) {
        this.code = code;
        this.column = column;
        this.rateColumn = rateColumn;
    }

    /**
     * 根据code获取统计字段
     */
    public static String getColumnByCode(String code,Boolean changeRate) {
        for (TargetSdColumnEnum columnEnum : TargetSdColumnEnum.values()) {
            if (columnEnum.getCode().equals(code)) {
                if(changeRate && StringUtil.isNotEmpty(columnEnum.getRateColumn())){
                    return columnEnum.getRateColumn();
                }else{
                    return columnEnum.getColumn();
                }
            }
        }
        return null;
    }

    public static Set<String> getAllCode() {
        Set<String> list = new HashSet<>();
        for (TargetSdColumnEnum columnEnum : TargetSdColumnEnum.values()) {
            list.add(columnEnum.getCode());
        }
        return list;
    }

    public static Set<String> getAllColumn() {
        Set<String> list = new HashSet<>();
        for (TargetSdColumnEnum columnEnum : TargetSdColumnEnum.values()) {
            list.add(columnEnum.getColumn());
        }
        return list;
    }
}
