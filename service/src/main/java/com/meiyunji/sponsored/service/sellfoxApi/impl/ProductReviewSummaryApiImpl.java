package com.meiyunji.sponsored.service.sellfoxApi.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.cpc.vo.AdProductReviewSummaryVo;
import com.meiyunji.sponsored.service.enums.SellfoxApiEnum;
import com.meiyunji.sponsored.service.sellfoxApi.IProductReviewSummaryApi;
import com.meiyunji.sponsored.service.util.OkHttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.OkHttpClient;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.TreeMap;

/**
 * <AUTHOR> 2021-04-02
 * 售后评价父asin级别汇总Dao实现类
 */
@Service
@Slf4j
public class ProductReviewSummaryApiImpl implements IProductReviewSummaryApi {

    @Value("${services.amzup.prefix}")
    private String amzupPrefix;


    @Override
    public AdProductReviewSummaryVo getByParentAsin(int puid, Integer shopId, String marketplaceId, String parentAsin) {

        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketplaceId",marketplaceId);
        map.put("parentAsin",parentAsin);
        log.info("======================开始请求商品评价接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.productReviewSummary,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<AdProductReviewSummaryVo> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<AdProductReviewSummaryVo>>() {
            });
            return result.getData();
        } catch (IOException e) {
            log.info("======================请求商品评价接口出现异常======================", e);

        }
        log.info("======================结束请求商品评价接口异常======================");
        return null;
    }

    @Override
    public List<AdProductReviewSummaryVo> getByParentAsinList(int puid, Integer shopId, String marketplaceId, String parentAsinList) {
        OkHttpClient okHttpClient = OkHttpClientUtil.getClient();
        //封装请求参数
        TreeMap<String, Object> map = new TreeMap<>();
        map.put("puid",puid);
        map.put("shopId",shopId);
        map.put("marketplaceId",marketplaceId);
        map.put("parentAsinList",parentAsinList);
        log.info("======================开始请求商品评价接口 :params: {}======================",map);

        Call call = okHttpClient.newCall(OkHttpClientUtil.getRequest(amzupPrefix,SellfoxApiEnum.productReviewSummaryList,map));

        try {
            Response response = call.execute();
            String body = response.body().string();
            log.info("response: " + body);
            Result<List<AdProductReviewSummaryVo>> result = JSONUtil.jsonToObjectIgnoreUnKnown(body, new TypeReference<Result<List<AdProductReviewSummaryVo>>>() {
            });
            return result.getData();
        } catch (IOException e) {
            log.info("======================请求商品评价接口出现异常======================", e);

        }
        log.info("======================结束请求商品评价接口异常======================");
        return null;
    }
}
