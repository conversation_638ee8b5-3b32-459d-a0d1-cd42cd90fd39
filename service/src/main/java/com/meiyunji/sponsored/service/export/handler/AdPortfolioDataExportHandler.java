package com.meiyunji.sponsored.service.export.handler;

import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.rpc.export.AdPortfolioDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service2.ICpcPortfolioService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.KeywordsPageParam;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageVo;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.vo.AdvertisingCampaignVo;
import com.meiyunji.sponsored.service.vo.AdvertisingPortfolioVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.export.util.ExportStringUtil.*;

@Service(AdManagePageExportTaskConstant.PORTFOLIO)
@Slf4j
public class AdPortfolioDataExportHandler implements AdManagePageExportTaskHandler {
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private ICpcPortfolioService cpcPortfolioService;
    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    /**
     * 广告组合导出
     *
     * @param task
     */
    @Override
    public void export(AdManagePageExportTask task) {
        PortfolioPageParam param = JSONUtil.jsonToObject(task.getParam(), PortfolioPageParam.class);
        if (param == null) {
            log.error(String.format("keyword export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth shop = shopAuthDao.getScAndVcById(param.getShopId());
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        List<PortfolioPageVo> allPortfolioVoList = cpcPortfolioService.getAllPortfolioVoList(param.getPuid(), param.getShopId(), param);
        //先把pageVo转成grpcVo
        List<AdPortfolioDataResponse.PortfolioPageVo> list = allPortfolioVoList.stream().filter(Objects::nonNull).map(AdPortfolioDataExportHandler::buildGrpcVo).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        String currency = AmznEndpoint.getByMarketplaceId(shop.getMarketplaceId()).getCurrencyCode().value();
        String fileName = shop.getName() + "_广告组合" + "_" + param.getStartDate() + "_" + param.getEndDate();
        int count = 0;
        List<String> urlList = new ArrayList<>();
        WriteHandlerBuild build = new WriteHandlerBuild().rate().currencyNew(AdvertisingPortfolioVo.class);
        List<List<AdPortfolioDataResponse.PortfolioPageVo>> partition = Lists.partition(list, Constants.FILE_MAX_SIZE);
        for (List<AdPortfolioDataResponse.PortfolioPageVo> partitionList : partition) {
            List<AdvertisingPortfolioVo> advertPfList = partitionList.stream().filter(Objects::nonNull).map(k -> buildExportVo(k, shop.getName(), currency)).collect(Collectors.toList());
            if (!advertPfList.isEmpty()) {
                urlList.add(excelService.easyExcelHandlerExport(task.getPuid(),
                        advertPfList, fileName + "(" + (count++) + ")", AdvertisingPortfolioVo.class, build));
            }
        }
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private AdvertisingPortfolioVo buildExportVo(AdPortfolioDataResponse.PortfolioPageVo pfVo, String name, String currency) {
        AdvertisingPortfolioVo advertPfVo = new AdvertisingPortfolioVo();

        advertPfVo.setShopName(name);
        // 组合名字
        advertPfVo.setName(pfVo.getPortfolioName());
        // 状态
        advertPfVo.setState(pfVo.getState());
        // 二级状态名字
        advertPfVo.setServingStatusName(pfVo.getServingStatusName());
        // 预算上限类型
        advertPfVo.setPolicy(pfVo.getPolicy());
        // 预算金额
        advertPfVo.setAmmount(currency + pfVo.getAmount());
        // 预算开始日期
        advertPfVo.setBudgetStartDate(pfVo.getBudgetStartDate());
        // 预算结束日期
        advertPfVo.setBudgetEndDate(getDateState(pfVo.getBudgetEndDate()));
        // 广告活动数量
        advertPfVo.setCampaignNumber(pfVo.getCampaignNumber().getValue());
        // 曝光量
        advertPfVo.setImpressions(pfVo.getImpressions().getValue());
        // 点击量
        advertPfVo.setClicks(pfVo.getClicks().getValue());
        // 点击率
        advertPfVo.setCtr(modifyFormat(pfVo.getCtr()));
        // 广告花费 (数据为空时候展示为0.00)
        advertPfVo.setAdCost(currency + formatToNumber(pfVo.getAdCost()));
        // 广告订单量
        advertPfVo.setAdOrderNum(pfVo.getAdOrderNum().getValue());
        // 平均点击费用 (数据为空时候展示为0.00)
        advertPfVo.setAdCostPerClick(currency + getAdCostPerClick(pfVo.getAdCostPerClick()));
        // 广告销售额 (数据为空时候展示为0.00)
        advertPfVo.setAdSale(currency + formatToNumber(pfVo.getAdSale()));
        // ACos
        advertPfVo.setAcos(modifyFormat(pfVo.getAcos()));
        // Roas
        advertPfVo.setRoas(pfVo.getRoas());
        // 订单转化率
        advertPfVo.setCvr(modifyFormat(pfVo.getCvr()));
        // ACoTS
        advertPfVo.setAcots(modifyFormat(pfVo.getAcots()));
        // ASoTS
        advertPfVo.setAsots(modifyFormat(pfVo.getAsots()));

        advertPfVo.setCpa(currency + formatToNumber(pfVo.getCpa()));
        advertPfVo.setOrderNum(pfVo.getOrderNum().getValue());
        // 花费占比
        advertPfVo.setAdCostPercentage(modifyFormat(pfVo.getAdCostPercentage()));
        // 销售额占比
        advertPfVo.setAdSalePercentage(modifyFormat(pfVo.getAdSalePercentage()));
        // 订单量占比
        advertPfVo.setAdOrderNumPercentage(modifyFormat(pfVo.getAdOrderNumPercentage()));
        // 销量占比
        advertPfVo.setOrderNumPercentage(modifyFormat(pfVo.getOrderNumPercentage()));
        // 广告笔单价
        advertPfVo.setAdvertisingUnitPrice(currency + formatToNumber(pfVo.getAdvertisingUnitPrice()));
        return advertPfVo;
    }

    private static String getDateState(String endDate) {
        if (endDate != null) {
            return endDate;
        } else {
            return "无结束日期";
        }
    }

    private static AdPortfolioDataResponse.PortfolioPageVo buildGrpcVo(PortfolioPageVo item) {
        AdPortfolioDataResponse.PortfolioPageVo.Builder vo = AdPortfolioDataResponse.PortfolioPageVo.newBuilder();
        if (StringUtils.isNotBlank(item.getName())) {
            vo.setPortfolioName(item.getName());
        }
        if (StringUtils.isNotBlank(item.getState())) {
            vo.setState(item.getState());
        }
        if (StringUtils.isNotBlank(item.getServingStatusName())) {
            vo.setServingStatusName(item.getServingStatusName());
        }
        if (StringUtils.isNotBlank(item.getPolicy())) {
            vo.setPolicy(item.getPolicy());
        }
        if (item.getAmount() != null) {
            vo.setAmount(item.getAmount());
        }
        if (StringUtils.isNotBlank(item.getBudgetStartDate())) {
            vo.setBudgetStartDate(item.getBudgetStartDate());
        }
        if (StringUtils.isNotBlank(item.getBudgetEndDate())) {
            vo.setBudgetEndDate(item.getBudgetEndDate());
        }
        vo.setCampaignNumber(Int32Value.of(Optional.ofNullable(item.getCampaignNumber()).orElse(0)));
        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
        vo.setCpa(StringUtils.isNotBlank(item.getCpa()) ? item.getCpa() : "0");
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");

        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

        return vo.build();
    }
}
