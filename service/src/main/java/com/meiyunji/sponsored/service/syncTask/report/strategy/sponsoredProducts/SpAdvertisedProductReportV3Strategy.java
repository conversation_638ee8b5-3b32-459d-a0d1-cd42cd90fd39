package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.aggregationReport.dao.IAmazonAggregationReportScheduleDao;
import com.meiyunji.sponsored.service.aggregationReport.po.AmazonAggregationReportSchedule;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductAdvertisedProduct;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpAdvertisedProductReportV3Strategy extends AbstractReportProcessStrategy {

    private final IAmazonAdProductReportDao amazonAdProductReportDao;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final IAmazonAdProductAggregationReportDao aggregationReportDao;
    private final IAmazonAggregationReportScheduleDao amazonAggregationReportScheduleDao;
    private final IAmazonAdProfileDao amazonAdProfileDao;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;

    public SpAdvertisedProductReportV3Strategy(
            CosBucketClient dataBucketClient,
            IAmazonAdProductReportDao amazonAdProductReportDao,
            IAmazonAdProductDao amazonAdProductDao,
            IAmazonAdProductAggregationReportDao aggregationReportDao,
            IAmazonAggregationReportScheduleDao amazonAggregationReportScheduleDao,
            IAmazonAdProfileDao amazonAdProfileDao, PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdProductReportDao = amazonAdProductReportDao;
        this.amazonAdProductDao = amazonAdProductDao;
        this.aggregationReportDao = aggregationReportDao;
        this.amazonAggregationReportScheduleDao = amazonAggregationReportScheduleDao;
        this.amazonAdProfileDao = amazonAdProfileDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }


    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && notification.getV3Type() == AmazonReportV3Type.sp_ad_product;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath()))));JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductAdvertisedProduct> reports = Lists.newArrayListWithExpectedSize(batchSize);
            boolean isUpdateSchedule = false;
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductAdvertisedProduct report = new SponsoredProductAdvertisedProduct();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= batchSize) {
                    isUpdateSchedule = true;
                    dealReport(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                isUpdateSchedule = true;
                dealReport(notification, reports, shopType);
            }
            //聚合最近两天广告组和广告活动报告 移除聚合逻辑
            //StatisticCampaignAndGroupReports(notification);
            if (isUpdateSchedule) {
                try {
                    AmazonAggregationReportSchedule schedule =
                            amazonAggregationReportScheduleDao.getByShopId(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
                    if (schedule != null) {
                        schedule.setNextSyncAt(LocalDateTime.now());
                        amazonAggregationReportScheduleDao.updateNextSyncAtById(schedule);
                    } else {
                        AmazonAdProfile profile = amazonAdProfileDao.getProfile(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier());
                        if (profile == null) {
                            return;
                        }
                        AmazonAggregationReportSchedule aggregationReportSchedule = new AmazonAggregationReportSchedule();
                        aggregationReportSchedule.setPuid(notification.getSellerIdentifier());
                        aggregationReportSchedule.setShopId(notification.getMarketplaceIdentifier());
                        aggregationReportSchedule.setMarketplaceId(profile.getMarketplaceId());
                        aggregationReportSchedule.setProfileId(profile.getProfileId());
                        amazonAggregationReportScheduleDao.save(aggregationReportSchedule);
                    }
                } catch (Exception e) {
                    log.info("聚合产品报告数据同步任务：aggregationReportSchedule 任务添加失败:{}@{}", notification.getSellerIdentifier()
                            , notification.getMarketplaceIdentifier(), e);
                }
            }

        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReport(ReportReadyNotification notification, List<SponsoredProductAdvertisedProduct> reports, String shopType) {
        List<AmazonAdProductReport> poList = getPoByReportAdProduct(notification, reports, shopType);
        List<List<AmazonAdProductReport>> partition = Lists.partition(poList, batchSize);
        for (List<AmazonAdProductReport> amazonAdProductReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdProductReports, 0, amazonAdProductReportDao::insertList);
            if (DateUtil.checkDateRange(notification.getV3EndDate(), 2L)) {
                amazonAdProductReportDao.insertDorisList(notification.getSellerIdentifier(), amazonAdProductReports);
            }
        }
        List<AmazonAdProductReport> collect = poList.stream().filter(e -> StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(collect)) {
            aggregationSpProductReport(notification.getSellerIdentifier(), collect);
        }
    }

    private void aggregationSpProductReport(int puid, List<AmazonAdProductReport> reportList) {
        if (CollectionUtils.isNotEmpty(reportList)) {
            //批量插入更新数据(分batchSize条一批插入)
            List<List<AmazonAdProductReport>> lists = Lists.partition(reportList, batchSize);
            for (List<AmazonAdProductReport> subList : lists) {

                List<AmazonAdProductAggregationReport> list = new ArrayList<>(subList.size());
                AmazonAdProductAggregationReport aggregationReport;

                for (AmazonAdProductReport report : subList) {
                    aggregationReport = new AmazonAdProductAggregationReport();
                    aggregationReport.setPuid(report.getPuid());
                    aggregationReport.setShopId(report.getShopId());
                    aggregationReport.setMarketplaceId(report.getMarketplaceId());
                    aggregationReport.setType(Constants.SP);
                    aggregationReport.setCountDate(report.getCountDate());
                    aggregationReport.setCampaignId(report.getCampaignId());
                    aggregationReport.setAdGroupId(report.getAdGroupId());
                    aggregationReport.setAdId(report.getAdId());
                    aggregationReport.setSku(report.getSku());
                    aggregationReport.setAsin(report.getAsin());
                    aggregationReport.setAdGroupName(report.getAdGroupName());
                    aggregationReport.setCampaignName(report.getCampaignName());
                    aggregationReport.setImpressions(report.getImpressions());
                    aggregationReport.setClicks(report.getClicks());
                    aggregationReport.setCost(report.getCost());
                    aggregationReport.setSaleNum(report.getSaleNum());
                    aggregationReport.setAdSales(report.getTotalSales());
                    list.add(aggregationReport);
                }
                partitionSqlUtil.save(puid, list, 0, aggregationReportDao::insertOrUpdateList);
            }
        }

    }


    private List<AmazonAdProductReport> getPoByReportAdProduct(ReportReadyNotification notification, List<SponsoredProductAdvertisedProduct> reports, String shopType) {
        List<AmazonAdProductReport> list = Lists.newArrayList();
        AmazonAdProductReport amazonAdProductReport;
        List<String> adIds = reports.stream().filter($ -> StringUtils.isBlank($.getAdvertisedAsin()) || ($.getAdvertisedSku() != null && $.getAdvertisedSku().contains("\\")))
                .map(o-> String.valueOf(o.getAdId())).distinct().collect(Collectors.toList());

        Map<String, AmazonAdProduct> adMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(adIds)) {
            List<AmazonAdProduct> adProducts = amazonAdProductDao.getByAdIds(notification.getSellerIdentifier(), notification.getMarketplaceIdentifier(), adIds);
            adMap = adProducts.stream().collect(Collectors.
                    toMap(AmazonAdProduct::getAdId, Function.identity(), (a, b) -> a));
        }

        for (SponsoredProductAdvertisedProduct report : reports) {
            amazonAdProductReport = new AmazonAdProductReport();
            amazonAdProductReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyyMMdd")));
            amazonAdProductReport.setPuid(notification.getSellerIdentifier());
            amazonAdProductReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdProductReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdProductReport.setAdId(String.valueOf(report.getAdId()));
            amazonAdProductReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdProductReport.setAdGroupId(String.valueOf(report.getAdGroupId()));
            //修改product的报告没有返回asin和sku的bug
            if (StringUtils.isBlank(report.getAdvertisedAsin()) || (report.getAdvertisedSku() != null && report.getAdvertisedSku().contains("\\"))) {
                AmazonAdProduct amazonAdProduct = adMap.get(String.valueOf(report.getAdId()));
                if (amazonAdProduct != null) {
                    amazonAdProductReport.setAsin(amazonAdProduct.getAsin());
                    amazonAdProductReport.setSku(amazonAdProduct.getSku());
                } else if (StringUtils.isNotBlank(report.getAdvertisedAsin())) {
                    amazonAdProductReport.setAsin(report.getAdvertisedAsin());
                    amazonAdProductReport.setSku(report.getAdvertisedSku());
                }
            } else {
                amazonAdProductReport.setAsin(report.getAdvertisedAsin());
                amazonAdProductReport.setSku(report.getAdvertisedSku());
            }

            amazonAdProductReport.setAdGroupName(report.getAdGroupName());
            amazonAdProductReport.setCampaignName(report.getCampaignName());

            amazonAdProductReport.setImpressions(report.getImpressions());
            amazonAdProductReport.setClicks(report.getClicks());
            amazonAdProductReport.setCost(report.getCost());
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                amazonAdProductReport.setTotalSales(report.getSales14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdProductReport.setAdSales(report.getAttributedSalesSameSku14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdProductReport.setOrderNum(report.getUnitsSoldClicks14d());
                amazonAdProductReport.setAdOrderNum(report.getUnitsSoldSameSku14d());
                amazonAdProductReport.setSaleNum(report.getPurchases14d());
                amazonAdProductReport.setAdSaleNum(report.getPurchasesSameSku14d());
            } else {
                amazonAdProductReport.setTotalSales(report.getSales7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdProductReport.setAdSales(report.getAttributedSalesSameSku7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdProductReport.setOrderNum(report.getUnitsSoldClicks7d());
                amazonAdProductReport.setAdOrderNum(report.getUnitsSoldSameSku7d());
                amazonAdProductReport.setSaleNum(report.getPurchases7d());
                amazonAdProductReport.setAdSaleNum(report.getPurchasesSameSku7d());
            }
            list.add(amazonAdProductReport);
        }
        return list;
    }
}
