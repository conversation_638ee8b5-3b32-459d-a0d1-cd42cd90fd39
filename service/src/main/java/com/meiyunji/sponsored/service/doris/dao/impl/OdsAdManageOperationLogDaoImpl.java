package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.doris.dao.IOdsAdManageOperationLogDao;
import com.meiyunji.sponsored.service.log.enums.OperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogFromEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.log.vo.AdManageLogDayVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Repository
public class OdsAdManageOperationLogDaoImpl extends DorisBaseDaoImpl<AdManageOperationLog> implements IOdsAdManageOperationLogDao {

    @Autowired
    private IUserDao userDao;

    @Override
    public Page<AdManageOperationLog> pageList(OperationLogQo operationLogQo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder select = new StringBuilder(" select * from ");
        StringBuilder countSql = new StringBuilder(" select count(*) from ");
        StringBuilder selectSql = buildBasic(operationLogQo, argsList);
        Object[] arg = argsList.toArray();
        select.append(selectSql);
        select.append(" order by  site_operation_time desc ");
        return getPageResult(operationLogQo.getPageNo(), operationLogQo.getPageSize(), countSql.append(selectSql).toString(), arg, select.toString(), arg, AdManageOperationLog.class);
    }

    @Override
    public List<AdManageLogDayVo> dayCountList(OperationLogQo operationLogQo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder(" select site_operation_day op_date, count(*) as num from ");
        StringBuilder sql = buildBasic(operationLogQo, argsList);
        selectSql.append(sql);
        selectSql.append(" group by site_operation_day ");
        return getJdbcTemplate().query(selectSql.toString(), (re, i) -> {
            AdManageLogDayVo dto =  new AdManageLogDayVo();
            dto.setCount(Optional.of(re.getLong("num")).orElse(0L));
            dto.setDate(Optional.ofNullable(re.getString("op_date")).orElse(""));
            return dto;
        }, argsList.toArray());
    }

    @Override
    public List<AdManageLogDayVo> getOpListByCountDay(OperationLogQo operationLogQo, List<String> countDays) {
        List<Object> argsList = new ArrayList<>();
        String selectSql = " select site_operation_day op_date, substring(GROUP_CONCAT(id order by site_operation_time desc),1 ,500) ids from " + buildBasic(operationLogQo, argsList) +
                SqlStringUtil.dealInList("count_day", countDays, argsList) +
                " group by site_operation_day ";
        return getJdbcTemplate().query(selectSql, (re, i) -> {
            AdManageLogDayVo dto =  new AdManageLogDayVo();
            dto.setIds(Optional.of(re.getString("ids")).orElse(""));
            dto.setDate(Optional.ofNullable(re.getString("op_date")).orElse(""));
            return dto;
        }, argsList.toArray());
    }

    @Override
    public List<AdManageOperationLog> listById(Integer puid, Integer shopId, List<String> ids, String from) {
        List<Object> args = new ArrayList<>();
        StringBuilder select = new StringBuilder("SELECT * from ods_ad_manage_operation_log_" + from
                + " where puid = ? and shop_id = ? ");
        args.add(puid);
        args.add(shopId);
        select.append(SqlStringUtil.dealInList("id", ids, args));
        return getJdbcTemplate().query(select.toString(), getRowMapper(), args.toArray());
    }

    @Override
    public List<AdManageOperationLog> listBudgetByDate(Integer puid, Integer shopId, String form, String countDay) {
        List<Object> args = new ArrayList<>();
        String select = "SELECT site_operation_time,campaign_id from ods_ad_manage_operation_log_" + form
                + " where puid = ? and shop_id = ? and count_day = ? and operation_object = '广告活动' and result = 0 and message like '%每日预算%'  ";
        args.add(puid);
        args.add(shopId);
        args.add(countDay);
        return getJdbcTemplate().query(select, getRowMapper(), args.toArray());
    }

    @Override
    public List<AdManageOperationLog> listBudgetBetweenDate(Integer puid, Integer shopId, String form, List<String> campaignId, String startDay, String endDay) {
        List<Object> args = new ArrayList<>();
        String select = "SELECT site_operation_time,campaign_id,message,operation_content from ods_ad_manage_operation_log_" + form
                + " where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? and operation_object = '广告活动' and result = 0 and message like '%|每日预算|%'  ";
        args.add(puid);
        args.add(shopId);
        args.add(startDay);
        args.add(endDay);
        // 不考虑判空 有问题直接抛错
        select = select + SqlStringUtil.dealInList("campaign_id", campaignId, args);
        return getJdbcTemplate().query(select, getRowMapper(), args.toArray());
    }

    @Override
    public List<AdManageOperationLog> listBudgetBetweenDateAmazon(Integer puid, Integer shopId, List<String> campaignId, String startDay, String endDay) {
        List<Object> args = new ArrayList<>();
        String select = "SELECT site_operation_time,campaign_id,message from ods_ad_manage_operation_log_amazon"
                + " where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? and operation_object = '广告活动' and result = 0 and message like '%广告活动预算费用%'  ";
        args.add(puid);
        args.add(shopId);
        args.add(startDay);
        args.add(endDay);
        // 不考虑判空 有问题直接抛错
        select = select + SqlStringUtil.dealInList("campaign_id", campaignId, args);
        return getJdbcTemplate().query(select, getRowMapper(), args.toArray());
    }

    private StringBuilder buildBasic(OperationLogQo operationLogQo, List<Object> argsList) {
        StringBuilder selectSql = new StringBuilder();
        selectSql.append(" ods_ad_manage_operation_log_").append(operationLogQo.getFrom()).append(" ");
        selectSql.append(" where puid = ? ");
        argsList.add(operationLogQo.getPuid());
        if (operationLogQo.getShopId() != null) {
            selectSql.append(" and  shop_id = ? ");
            argsList.add(operationLogQo.getShopId());
        }
        if (StringUtils.isNotBlank(operationLogQo.getChangeType())) {
            selectSql.append(" and  change_type = ? ");
            argsList.add(operationLogQo.getChangeType());
        }
        if (StringUtils.isNotBlank(operationLogQo.getTargetId())) {
            selectSql.append(" and  target_id = ? ");
            argsList.add(operationLogQo.getTargetId());
        }
        if (StringUtils.isNotBlank(operationLogQo.getModule())) {
            selectSql.append(" and  module = ? ");
            argsList.add(operationLogQo.getModule());
        }
        if (StringUtils.isNotBlank(operationLogQo.getAdType())) {
            selectSql.append(" and `type` = ? ");
            argsList.add(operationLogQo.getAdType());
        }

        if (StringUtils.isNotBlank(operationLogQo.getProductSearchValue())
                && StringUtils.isNotBlank(operationLogQo.getProductSearchType())
                && StringUtils.isNotBlank(operationLogQo.getProductSearchQueryType())) {
            if (SearchTypeEnum.BLUR.getValue().equalsIgnoreCase(operationLogQo.getProductSearchQueryType())) {
                if ("msku".equalsIgnoreCase(operationLogQo.getProductSearchType())) {
                    selectSql.append(" and sku like ? ");
                } else {
                    selectSql.append(" and asin like ? ");
                }
                argsList.add("%" + operationLogQo.getProductSearchValue() + "%");
            } else {
                if ("msku".equalsIgnoreCase(operationLogQo.getProductSearchType())) {
                    selectSql.append(" and sku  = ? ");
                } else {
                    selectSql.append(" and asin = ? ");
                }
                argsList.add(operationLogQo.getProductSearchValue());
            }
        }

        List<String> portfolioIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(operationLogQo.getPortfolioIdList())) {
            portfolioIdList.addAll(operationLogQo.getPortfolioIdList());
        }
        if (StringUtils.isNotBlank(operationLogQo.getPortfolioId())) {
            portfolioIdList.add(operationLogQo.getPortfolioId());
        }
        List<String> campaignIdList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(operationLogQo.getPortfolioCampaignIds())) {
            campaignIdList.addAll(operationLogQo.getPortfolioCampaignIds());
        }
        if (StringUtils.isNotBlank(operationLogQo.getCampaignId()) || StringUtils.isNotBlank(operationLogQo.getCampaignIds())) {
            String campaignIdStr = String.join(",", Lists.newArrayList(operationLogQo.getCampaignIds(), operationLogQo.getCampaignId()));
            if (CollectionUtils.isNotEmpty(campaignIdList)) {
                campaignIdList.retainAll(StringUtil.splitStr(campaignIdStr));
            } else {
                campaignIdList = StringUtil.splitStr(campaignIdStr);
            }
        }
        // 如果没有活动Id 但有选广告组合
        if (CollectionUtils.isEmpty(campaignIdList) && CollectionUtils.isNotEmpty(operationLogQo.getPortfolioIdList())) {
            campaignIdList.add("-1");
        }

        if (CollectionUtils.isNotEmpty(campaignIdList)) {
            selectSql.append(SqlStringUtil.dealBitMapDorisInList("campaign_id", campaignIdList, argsList));
        }

        if (CollectionUtils.isNotEmpty(portfolioIdList) && Constants.NONE.equals(operationLogQo.getAdType())) {
            selectSql.append(SqlStringUtil.dealInList("portfolio_id", portfolioIdList, argsList));
        }

        if (StringUtils.isNotBlank(operationLogQo.getGroupId()) || StringUtils.isNotBlank(operationLogQo.getGroupIds())) {
            String groupIdStr = String.join(",", Lists.newArrayList(operationLogQo.getGroupIds(), operationLogQo.getGroupId()));
            selectSql.append(SqlStringUtil.dealInList("adGroup_id", StringUtil.splitStr(groupIdStr), argsList));
        }
        if (StringUtils.isNotBlank(operationLogQo.getOperationObject()) || CollectionUtils.isNotEmpty(operationLogQo.getOperationObjectList())) {
            List<String> operationObjectList = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(operationLogQo.getOperationObjectList())) {
                operationObjectList.addAll(operationLogQo.getOperationObjectList());
            }
            if (StringUtils.isNotBlank(operationLogQo.getOperationObject())) {
                operationObjectList.add(operationLogQo.getOperationObject());
            }
            selectSql.append(SqlStringUtil.dealInList("operation_object", operationObjectList.stream().distinct().collect(Collectors.toList()), argsList));
//            selectSql.append(" and  operation_object = ? ");
//            argsList.add(operationLogQo.getOperationObject());
        }
        if (StringUtils.isNotBlank(operationLogQo.getOperationType())) {
            selectSql.append(" and  `action` = ? ");
            argsList.add(operationLogQo.getOperationType());
        }
        if (StringUtils.isNotBlank(operationLogQo.getFrom())) {
            selectSql.append(" and  `from` = ? ");
            argsList.add(operationLogQo.getFrom());
        }
        // 投放类型(自动化调整用)
        if (StringUtils.isNotBlank(operationLogQo.getTargetType())) {
            selectSql.append(" and  `target_type` = ? ");
            argsList.add(operationLogQo.getTargetType());
        }
        // 调整范围(自动化调整用)
        if (StringUtils.isNotBlank(operationLogQo.getAdjustmentRange())) {
            selectSql.append(" and  `adjustment_range` = ? ");
            argsList.add(operationLogQo.getAdjustmentRange());
        }
        // 模板筛选(自动化调整用)
        if (operationLogQo.getTemplateId() != null) {
            selectSql.append(" and  `template_id` = ? ");
            argsList.add(operationLogQo.getTemplateId());
        }
        if (StringUtils.isNotBlank(operationLogQo.getTargetName())) {
            if (operationLogQo.getListSearchValue().size() > 1) {
                selectSql.append(SqlStringUtil.dealInList("target_name", operationLogQo.getListSearchValue(), argsList));
            } else {
                selectSql.append(" and  `target_name` like ? ");
                argsList.add("%" + operationLogQo.getTargetName() + "%");
            }
        }
        if (Constants.notIncludeBudgetOut.equals(operationLogQo.getIsShow())) {
            selectSql.append(" and  `change_Type` != ? ");
            argsList.add(OperationLogChangeTypeEnum.IN_BUDGET.getChangeTypeType());
        } else if (Constants.exclusiveBudgetOut.equalsIgnoreCase(operationLogQo.getIsShow())) {
            selectSql.append(" and  `change_Type` = ? ");
            argsList.add(OperationLogChangeTypeEnum.IN_BUDGET.getChangeTypeType());
        }
        if (operationLogQo.getIsSuccess() != null && operationLogQo.getIsSuccess() < 2) {
            selectSql.append(" and  `result` = ? ");
            argsList.add(operationLogQo.getIsSuccess());
        }
        if (StringUtils.isNotBlank(operationLogQo.getUserName())) {
            List<String> strUserIds = Arrays.asList(operationLogQo.getUserName().split(","));
            List<Integer> userIds = strUserIds.stream().map(Integer::parseInt).collect(Collectors.toList());
            List<User> users = userDao.listByIds(operationLogQo.getPuid(), userIds);
            List<String> userNames = users.stream().map(User::getUserNickname).collect(Collectors.toList());
//            selectSql.append(SqlStringUtil.dealInList("user_name", userNames, argsList));
            selectSql.append(" and (")
                    .append(SqlStringUtil.dealInListNotAnd("user_name", userNames, argsList))
                    .append(" or ")
                    .append(SqlStringUtil.dealInListNotAnd("uid", userIds, argsList))
                    .append(") ");
        }
        if (StringUtils.isNotBlank(operationLogQo.getOperationContent())) {
            if (OperationLogFromEnum.OTHER.getOperationType().equals(operationLogQo.getFrom())){
                selectSql.append(" and  lower(`operation_content`) like ? ");
            } else {
                selectSql.append(" and  lower(`message`) like ? ");
            }
            argsList.add("%" + operationLogQo.getOperationContent().toLowerCase() + "%");
        }
        if (StringUtils.isNotBlank(operationLogQo.getSiteStart())) {
            selectSql.append(" and  `count_day` >= ? ");
            argsList.add(operationLogQo.getSiteStart());
        }
        if (StringUtils.isNotBlank(operationLogQo.getSiteEnd())) {
            selectSql.append(" and  `count_day` <= ? ");
            argsList.add(operationLogQo.getSiteEnd());
        }
        return selectSql;
    }
}
