package com.meiyunji.sponsored.service.adProductRight.service;

import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.service.adProductRight.request.*;
import com.meiyunji.sponsored.service.adProductRight.response.*;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.post.response.GetUserInfoResponse;
import org.apache.commons.lang3.tuple.Pair;

import java.util.List;

public interface IAdProductRightService {
    /**
     * 获取下拉框站点和店铺
     * @return
     */
    List<ShopSitesResponse> getShopSiteList(int puid, Boolean isAdd, List<Integer> shopIdList);

    /**
     * 获取下拉框账号
     *
     * @param puid
     * @param request
     * @return
     */
    List<GetUserInfoResponse.UserInfo> getUserList(int puid, UserListRequest request);

    /**
     * 添加页面的账号下拉接口
     * @param puid
     * @return
     */
    List<GetUserInfoResponse.UserInfo> getUser(int puid);

    /**
     * 获取广告产品权限列表页数据
     * @param puid
     * @param request
     * @return
     */
    GetRightListResponse pageList(int puid, GetRightListRequest request);

    /**
     * 广告产品权限列表页 - 商品详情查询
     * @param puid
     * @param request
     * @return
     */
    AsinDetailResponse getAsinDetail(int puid, AsinDetailRequest request);

    /**
     * 删除账号产品权限
     * @param puid
     * @param uid
     * @param request
     * @return
     */
    Result<String> deleteRight(int puid, int uid, DeleteRightRequest request);

    /**
     * 添加广告产品权限
     * @param puid
     * @param request
     * @return
     */
    Result<String> addRight(int puid, AddRightRequest request);

    /**
     * 编辑广告产品权限 - 右侧产品详情
     * @param puid
     * @param request
     * @return
     */
    AsinListResponse getAsinInfo(int puid, AsinInfoRequest request);

    /**
     * 编辑广告产品权限 - 提交接口
     * @param puid
     * @param request
     * @return
     */
    Result<String> updateRight(int puid,  AddRightRequest request);

    /**
     * 添加/编辑广告产品权限 - 左侧产品详情
     * @param puid
     * @param request
     * @return
     */
    AsinListResponse getAsinList(int puid, AsinListRequest request);

    boolean checkUidNeedProductRight(Integer puid, Integer uid, List<Integer> shopIds, boolean isAdminUser);

    boolean checkUidNeedProductRight(Integer puid, List<Integer> shopIds);

    List<Integer> getUidNeedProductRightShopIds(Integer puid, List<Integer> shopIds);

    List<Integer> getUidNeedProductRightShopIds(Integer puid, Integer uid, List<Integer> shopIds, boolean isAdminUser);

    String getProductRightSql(String shopIdFieldName, String campaignIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    String getProductRightCampaignIdsSqlAnd(String campaignIdFieldName, Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    String getProductRightCampaignIdsSql(String campaignIdFieldName, Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    String getProductRightCampaignIdsSql(String campaignIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    List<String> getProductRightCampaignIds(Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types);

    String getProductRightCampaignIdsSqlFromGrpc(Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    String getProductRightCampaignIdsSqlFromGrpc(Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args, String campaignIdFieldName);

    Pair<Boolean, List<String>> getProductRightCampaignIdsFromGrpc(Integer puid, Integer shopId, CampaignTypeEnum type);

    Pair<Boolean, List<String>> getProductRightCampaignIdsFromGrpc(Integer puid, List<Integer> shopIds, List<CampaignTypeEnum> types);

    String getProductRightSqlFromGrpc(int puid, String shopIdFieldName, String campaignIdFieldName, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    void putUidAndAdminUserToMDC(Integer uid, Boolean adminUser);


    Pair<Boolean, List<String>> getProductRightPortfolioIdsFromGrpc(Integer puid, List<Integer> shopIds, List<String> portfolioIds);

    List<String> getProductRightPortfolioIds(Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<String> portfolioIds);

    String getProductRightPortfolioIdsSql(String campaignIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<Object> args);

    String getPortfolioRightSqlFromGrpc(int puid, String shopIdFieldName, String portfolioIdFieldName, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);

    String getPortfolioProductRightSql(String shopIdFieldName, String portfolioIdFieldName, Integer puid, Integer uid, boolean isAdminUser, List<Integer> shopIds, List<CampaignTypeEnum> types, List<Object> args);
}
