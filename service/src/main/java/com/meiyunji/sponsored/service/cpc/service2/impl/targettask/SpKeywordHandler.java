package com.meiyunji.sponsored.service.cpc.service2.impl.targettask;

import com.amazon.advertising.spV3.keyword.CreateSpKeywordV3Response;
import com.amazon.advertising.spV3.keyword.KeywordSpV3Client;
import com.amazon.advertising.spV3.keyword.entity.CreateKeywordEntityV3;
import com.amazon.advertising.spV3.keyword.entity.KeywordApiResponseV3;
import com.amazon.advertising.spV3.keyword.entity.KeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskConstant;
import com.meiyunji.sponsored.service.cpc.constants.AdTargetTaskStatusEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupDao;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTask;
import com.meiyunji.sponsored.service.cpc.po.AdTargetTaskDetail;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeyword;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcKeywordsApiService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: nongwenhua
 * @email: <EMAIL>
 * @create: 2024-05-08 19:21
 */
@Service(AdTargetTaskConstant.SP_KEYWORD_HANDLER)
@Slf4j
public class SpKeywordHandler implements TargetTaskHandler {

    @Autowired
    private TargetTaskComponent targetTaskComponent;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private CpcKeywordsApiService cpcKeywordsApiService;
    @Autowired
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private ICpcKeywordsService cpcKeywordsService;

    @Override
    public void handle(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        //处理业务返回结果
        addKeywords(adTargetTask, adTargetTaskDetails);
    }

    public void addKeywords(AdTargetTask adTargetTask, List<AdTargetTaskDetail> adTargetTaskDetails) {
        List<AdTargetTaskDetail> needUpdateDetailList = new ArrayList<>();
        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        // 排除已存在的关键词
        Set<String> adGroupIdSet = new HashSet<>();
        Set<String> keywordTextSet = new HashSet<>();
        Set<String> matchTypeSet = new HashSet<>();
        Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap = new HashMap<>();
        for (AdTargetTaskDetail targetTaskDetail : adTargetTaskDetails) {
            adGroupIdSet.add(targetTaskDetail.getAdGroupId());
            keywordTextSet.add(targetTaskDetail.getTargetObject().trim());
            matchTypeSet.add(targetTaskDetail.getMatchType());
            adTargetTaskDetailMap.put(targetTaskDetail.getId(), targetTaskDetail);
        }

        List<AmazonAdKeyword> repeatedAmazonAdKeywords = amazonAdKeywordDaoRoutingService.getListByTargetTaskCondition(adTargetTask.getPuid(), adTargetTask.getShopId(), adGroupIdSet, keywordTextSet, matchTypeSet);
        Map<String, AmazonAdKeyword> amazonAdKeywordMap = repeatedAmazonAdKeywords.stream()
                .collect(Collectors.toMap(each -> String.join("-", each.getAdGroupId(), each.getKeywordText(), each.getMatchType()), Function.identity(), (newVal, oldVal) -> newVal));

        int originalTaskDetailSize = adTargetTaskDetails.size();
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail next;
        while (it.hasNext()) {
            next = it.next();
            String key = String.join("-", next.getAdGroupId(), next.getTargetObject().trim(), next.getMatchType());
            if (amazonAdKeywordMap.containsKey(key)) {
                if (next.getStatus() <= AdTargetTaskStatusEnum.FAILURE.getCode()) {
                    next.setFailureReason("历史已存在相同的投放，请检查");
                    next.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    needUpdateDetailList.add(next);
                }
                it.remove();
            }
        }

        if (CollectionUtils.isEmpty(adTargetTaskDetails)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getAdGroupByIds(adTargetTask.getPuid(), adTargetTask.getShopId(), new ArrayList<>(adGroupIdSet));
        Map<String, AmazonAdGroup> amazonAdGroupMap = amazonAdGroups.stream().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (newVal, oldVal) -> newVal));

        List<AmazonAdKeyword> amazonAdKeywords = convertAddKeywordsVoToPo(adTargetTask.getUid(), amazonAdGroupMap, adTargetTaskDetails, needUpdateDetailList);
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(adTargetTask.getShopId(), adTargetTask.getPuid());
        if (shop == null) {
            adTargetTaskDetails.forEach(each -> {
                each.setFailureReason("店铺不存在");
                each.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                needUpdateDetailList.add(each);
            });
            targetTaskComponent.updateTaskStatus(adTargetTask, needUpdateDetailList);
            return;
        }

        List<List<AmazonAdKeyword>> amazonAdKeywordPartition = Lists.partition(amazonAdKeywords, AdTargetTaskConstant.MAX_SP_TARGET_SIZE);
        int failureNums = 0;
        for (List<AmazonAdKeyword> amazonAdKeywordList : amazonAdKeywordPartition) {
            Result result = createKeywordsV3(amazonAdKeywordList, adTargetTaskDetailMap, shop);
            List<AdManageOperationLog> keywordLogs = Lists.newArrayListWithExpectedSize(2);
            for (AmazonAdKeyword keyword : amazonAdKeywordList) {
                AdManageOperationLog keywordLog = adManageOperationLogService.getkeywordsLog(null, keyword);
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(keyword.getTargetTaskDetailId());
                needUpdateDetailList.add(adTargetTaskDetail);
                keywordLog.setIp(adTargetTask.getLoginIp());
                if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                    keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                } else {
                    keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    keywordLog.setResultInfo(adTargetTaskDetail.getFailureReason());
                }
                keywordLogs.add(keywordLog);
                if (AdTargetTaskStatusEnum.FAILURE.getCode() == adTargetTaskDetail.getStatus()) {
                    failureNums++;
                }
            }
            //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
            adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
            if (result.success()) {
                amazonAdKeywordList = amazonAdKeywordList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(adTargetTask.getPuid(), amazonAdKeywordList, Constants.BIDDABLE);
                cpcKeywordsService.saveDoris(amazonAdKeywordList, true, true);
                // 维护广告组类型
                Set<String> needUpdateAdGroupIds = amazonAdKeywordList.stream()
                        .map(AmazonAdKeyword::getAdGroupId).collect(Collectors.toSet());
                String marketplaceId = amazonAdKeywordList.get(0).getMarketplaceId();
                amazonAdGroupDao.updateAdGroupType(adTargetTask.getPuid(), adTargetTask.getShopId(), marketplaceId, needUpdateAdGroupIds, Constants.GROUP_TYPE_KEYWORD);
            }
            targetTaskComponent.updateTaskDetailStatus(adTargetTask, needUpdateDetailList);
            needUpdateDetailList.clear();
        }

        failureNums += originalTaskDetailSize - amazonAdKeywords.size();
        int adTargetTaskStatus = targetTaskComponent.getAdTargetTaskStatus(originalTaskDetailSize, failureNums);
        targetTaskComponent.updateTaskStatus(adTargetTask, adTargetTaskStatus);
    }

    private Result createKeywordsV3(List<AmazonAdKeyword> amazonAdKeywords,
                                    Map<Long, AdTargetTaskDetail> adTargetTaskDetailMap,
                                    ShopAuth shop) {
        AmazonAdKeyword one = amazonAdKeywords.get(0);
        List<CreateKeywordEntityV3> keywords = cpcKeywordsApiService.makeCreateKeywordsV3(amazonAdKeywords);
        CreateSpKeywordV3Response response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shopAuthService.getAdToken(shop),
                one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = KeywordSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createKeywords(shopAuthService.getAdToken(shop),
                    one.getProfileId(), one.getMarketplaceId(), keywords, Boolean.TRUE);
        }
        if (response == null) {
            for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywords) {
                AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                adTargetTaskDetail.setFailureReason("网络延迟，请稍后重试");
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
            }
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (response.getData() != null) {
            boolean hasSucc = false;
            StringBuilder error = new StringBuilder();
            KeywordApiResponseV3 data = response.getData();
            if (CollectionUtils.isNotEmpty(data.getKeywords().getSuccess())) {
                for (KeywordSuccessResultV3 keywordSuccessResultV3 : data.getKeywords().getSuccess()) {
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(keywordSuccessResultV3.getIndex());
                    amazonAdKeyword.setKeywordId(keywordSuccessResultV3.getKeywordId());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.SUCCESS.getCode());
                }

                hasSucc = true;
            }

            if (CollectionUtils.isNotEmpty(data.getKeywords().getError())) {
                for (ErrorItemResultV3 errorItemResultV3 : data.getKeywords().getError()) {
                    AmazonAdKeyword amazonAdKeyword = amazonAdKeywords.get(errorItemResultV3.getIndex());
//                    String errorMsg = AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
                    String errorMsg = AmazonErrorUtils.getKeywordTargetError(errorItemResultV3.getErrors().get(0).getErrorMessage(), adTargetTaskDetail.getTargetObject());
                    adTargetTaskDetail.setFailureReason(targetTaskComponent.getError(errorMsg, errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    adTargetTaskDetail.setFailureReasonDetail(errorItemResultV3.getErrors().get(0).getErrorMessage());
                    adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                    error.append("targetValue:").append(amazonAdKeywords.get(errorItemResultV3.getIndex()).getKeywordText()).append(",desc:").append(errorMsg).append(";");
                }
            }
            if (hasSucc) {
                return ResultUtil.success(error.toString());
            }

            return ResultUtil.error(error.toString());
        }


        String msg = "网络延迟，请稍后重试";
        String formatMsg = msg;
        if (response.getError() != null) {
            msg = AmazonErrorUtils.getError(response.getError().getMessage());
            formatMsg = targetTaskComponent.getError(msg, response.getError().getMessage());
        }
        for (AmazonAdKeyword amazonAdKeyword : amazonAdKeywords) {
            AdTargetTaskDetail adTargetTaskDetail = adTargetTaskDetailMap.get(amazonAdKeyword.getTargetTaskDetailId());
            adTargetTaskDetail.setFailureReason(formatMsg);
            adTargetTaskDetail.setFailureReasonDetail(msg);
            adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
        }
        return ResultUtil.error(formatMsg);
    }

    private List<AmazonAdKeyword> convertAddKeywordsVoToPo(Integer uid, Map<String, AmazonAdGroup> amazonAdGroupMap, List<AdTargetTaskDetail> adTargetTaskDetails, List<AdTargetTaskDetail> needUpdateDetailList) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(adTargetTaskDetails.size());
        AmazonAdKeyword amazonAdKeyword;
        Iterator<AdTargetTaskDetail> it = adTargetTaskDetails.iterator();
        AdTargetTaskDetail adTargetTaskDetail;
        while (it.hasNext()) {
            adTargetTaskDetail = it.next();
            AmazonAdGroup amazonAdGroup = amazonAdGroupMap.get(adTargetTaskDetail.getAdGroupId());
            if (amazonAdGroup == null) {
                adTargetTaskDetail.setStatus(AdTargetTaskStatusEnum.FAILURE.getCode());
                adTargetTaskDetail.setFailureReason("广告组不存在");
                needUpdateDetailList.add(adTargetTaskDetail);
                it.remove();
            } else {
                amazonAdKeyword = new AmazonAdKeyword();
                amazonAdKeyword.setPuid(amazonAdGroup.getPuid());
                amazonAdKeyword.setShopId(amazonAdGroup.getShopId());
                amazonAdKeyword.setMarketplaceId(amazonAdGroup.getMarketplaceId());
                amazonAdKeyword.setAdGroupId(amazonAdGroup.getAdGroupId());
                amazonAdKeyword.setDxmGroupId(amazonAdGroup.getId());
                amazonAdKeyword.setCampaignId(amazonAdGroup.getCampaignId());
                amazonAdKeyword.setProfileId(amazonAdGroup.getProfileId());
                amazonAdKeyword.setKeywordText(adTargetTaskDetail.getTargetObject().trim());
                amazonAdKeyword.setMatchType(adTargetTaskDetail.getMatchType());
                amazonAdKeyword.setType(Constants.BIDDABLE);
                amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
                amazonAdKeyword.setBid(adTargetTaskDetail.getBid().doubleValue());

                if (adTargetTaskDetail.getSuggested() != null) {
                    amazonAdKeyword.setSuggested(adTargetTaskDetail.getSuggested().doubleValue());
                }
                if (adTargetTaskDetail.getRangeStart() != null) {
                    amazonAdKeyword.setRangeStart(adTargetTaskDetail.getRangeStart().doubleValue());
                }
                if (adTargetTaskDetail.getRangeEnd() != null) {
                    amazonAdKeyword.setRangeEnd(adTargetTaskDetail.getRangeEnd().doubleValue());
                }

                amazonAdKeyword.setCreateId(uid);
                amazonAdKeyword.setTargetTaskDetailId(adTargetTaskDetail.getId());
                amazonAdKeywords.add(amazonAdKeyword);
            }
        }
        return amazonAdKeywords;
    }
}
