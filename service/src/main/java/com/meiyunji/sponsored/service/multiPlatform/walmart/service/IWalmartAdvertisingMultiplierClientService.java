package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.exception.ServiceException;
import com.walmart.oms.advertiser.base.dto.PlacementBidMultipliersDTO;
import com.walmart.oms.advertiser.base.dto.PlatformBidMultipliersDTO;
import com.walmart.oms.advertiser.model.CreatePlacementBidMultipliersResponse;
import com.walmart.oms.advertiser.model.CreatePlatformBidMultipliersResponse;
import com.walmart.oms.advertiser.model.ListAllThePlacementBidMultipliersResponse;
import com.walmart.oms.advertiser.model.ListAllThePlatformBidMultipliersResponse;

import java.util.List;

/**
 * @author: ys
 * @date: 2025/3/21 11:23
 * @describe:
 */
public interface IWalmartAdvertisingMultiplierClientService {
    CreatePlacementBidMultipliersResponse createMultiplierPlacement(List<PlacementBidMultipliersDTO> multiplierDto) throws ServiceException;

    ListAllThePlacementBidMultipliersResponse getAllMultiplierPlacement(String campaignId) throws ServiceException;

    CreatePlatformBidMultipliersResponse createPlatformMultiplier(List<PlatformBidMultipliersDTO> bidMultipliersDTOS) throws ServiceException;

    ListAllThePlatformBidMultipliersResponse getAllPlatformMultiplier(String campaignId) throws ServiceException;
}
