package com.meiyunji.sponsored.service.multiPlatform.walmart.service;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingCampaign;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeyword;
import com.meiyunji.sponsored.service.multiPlatform.walmart.qo.*;
import com.meiyunji.sponsored.service.multiPlatform.walmart.resp.WalmartAdKeywordListCreateResp;
import com.meiyunji.sponsored.service.multiPlatform.walmart.vo.*;
import com.walmart.oms.advertiser.WalmartAdvertiserClient;

import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingKeywordService {

    /**
     * 新增
     */
    void add(WalmartAdvertisingKeyword keyword);

    /**
     * 更新
     */
    int update(WalmartAdvertisingKeyword keyword);

    /**
     * 关键词列表页
     */
    Page<KeywordPageVo> keywordPageList(Integer puid, KeywordPageQo qo);

    /**
     * 关键词列表汇总
     */
    KeywordPageAggregateVo keywordPageAggregate(Integer puid, KeywordPageAggregateQo qo);

    /**
     * 创建关键词
     */
    Result<WalmartAdKeywordListCreateResp> addKeyword(Integer puid, AddKeywordsQo qo);

    /**
     * 更新关键词
     */
    Result<String> updateKeyword(Integer puid, UpdateKeywordQo qo);

    /**
     * 根据广告组id获取建议关键词
     */
    Result<List<GetSuggestedKeywordVo>> getSuggestedKeywords(int puid, GetSuggestedKeywordQo qo);
    Result<List<GetSuggestedKeywordVo>> getKeywordsMatchSuggested(int puid, GetKeywordMatchSuggestedReq qo);

    int syncKeywordByCampaigns(Integer puid, List<Integer> shopIdList,
                               WalmartAdvertiserClient advertiserClient, List<WalmartAdvertisingCampaign> campaigns) throws ServiceException;

    List<WalmartAdvertisingKeyword> getByCampaignId(Integer puid, Integer shopId, String campaignId);

    int delete(Integer puid, Integer shopId, String keywordId);
}
