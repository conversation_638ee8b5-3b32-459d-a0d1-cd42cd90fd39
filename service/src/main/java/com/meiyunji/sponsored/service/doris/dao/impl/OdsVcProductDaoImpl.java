package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.vo.SPPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsVcProductDao;
import com.meiyunji.sponsored.service.product.po.OdsVcProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Repository
@Slf4j
public  class OdsVcProductDaoImpl extends DorisBaseDaoImpl<OdsVcProduct> implements IOdsVcProductDao {

    @Override
    public Page<OdsVcProduct> getProductPageListWithProductRight(Integer puid, Long uid , SPPageParam param , boolean isNeedCheckRight) {
        // 1. 初始化参数列表和SQL构建器（完全参考 getProductRightPageList 模式）
        List<Object> argsList = new ArrayList<>();
        StringBuilder select = new StringBuilder();
        StringBuilder countSql = new StringBuilder("SELECT COUNT(*) FROM (");

        // 2. 构建SELECT子句（字段映射到实体属性）
        select.append("SELECT ");
        select.append("vp.id, ");
        select.append("vp.puid, ");
        select.append("vp.shop_id, ");
        select.append("vp.marketplace_id, ");
        select.append("vp.asin, ");
        select.append("vp.parent_asin, ");
        select.append("vp.child_asins, ");
        select.append("vp.main_image, ");
        select.append("vp.msku, ");
        select.append("vp.commodity_id, ");
        select.append("vp.match_commodity_time, ");
        select.append("vp.title, ");
        select.append("vp.brand, ");
        select.append("vp.manufacturer, ");
        select.append("vp.manufacturer_code, ");
        select.append("vp.variation_child_str, ");
        select.append("vp.part_number, ");
        select.append("vp.product_bundle, ");
        select.append("vp.is_variation, ");
        select.append("vp.standard_product_id, ");
        select.append("vp.standard_product_type, ");
        select.append("vp.standard_price, ");
        select.append("vp.standard_price_currency, ");
        select.append("vp.net_cost_amount, ");
        select.append("vp.net_cost_amount_currency, ");
        select.append("vp.status, ");
        select.append("vp.display_group_rank, ");
        select.append("vp.display_group_ranks, ");
        select.append("vp.classification_rank, ");
        select.append("vp.classification_ranks, ");
        select.append("vp.last_sync_time, ");
        select.append("vp.create_time, ");
        select.append("vp.update_time, ");
        select.append("vp.create_id, ");
        select.append("vp.update_id ");

        // 3. 构建FROM子句
        select.append("FROM ods_t_vc_product vp");
        if (isNeedCheckRight) {
            select.append(" join ods_t_ad_product_permission p on p.puid = vp.puid and p.shop_id = vp.shop_id and p.asin = vp.asin and p.uid = ? ");
            argsList.add(uid);
        }

        // 4. 构建WHERE条件（参考 getProductRightPageList 的参数顺序）
        select.append("WHERE vp.puid = ? AND vp.shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());

        // 5. 添加基础过滤条件（参考原逻辑）
        select.append("AND vp.is_variation IN (0, 2) ");
        select.append("AND vp.status = 1 ");

        // 6. 添加搜索条件（完全参考 getProductRightPageList 的处理方式）
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("title".equals(param.getSearchField())) {
                select.append("AND vp.title LIKE ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue()) + "%");

            } else if ("asin".equals(param.getSearchField())) {
                // 使用 SqlStringUtil.dealInList 处理ASIN多值搜索（与 getProductRightPageList 一致）
                select.append(SqlStringUtil.dealInList("vp.asin",
                        Arrays.asList(Arrays.stream(param.getSearchValue().split(StringUtil.SPLIT_COMMA)).toArray()),
                        argsList));

            } else if ("msku".equals(param.getSearchField())) {
                select.append("AND vp.msku LIKE ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue()) + "%");
            }
        }

        // 7. 添加GROUP BY和ORDER BY（参考原逻辑）
        select.append("GROUP BY vp.puid, vp.shop_id, vp.asin ");
        select.append("ORDER BY ");
        if ("asc".equalsIgnoreCase(param.getOrderType())) {
            select.append("vp.id ASC ");
        } else {
            select.append("vp.id DESC ");
        }

        // 8. 构建COUNT SQL（完全参考 getProductRightPageList 模式）
        countSql.append(select).append(") c");

        // 9. 准备参数数组（参考 getProductRightPageList）
        Object[] args = argsList.toArray();

        return getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), args , select.toString() , args , OdsVcProduct.class);
    }
}
