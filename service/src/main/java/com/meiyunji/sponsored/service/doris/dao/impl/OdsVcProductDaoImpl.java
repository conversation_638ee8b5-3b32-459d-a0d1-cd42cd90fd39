package com.meiyunji.sponsored.service.doris.dao.impl;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.vo.SPPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsVcProductDao;
import com.meiyunji.sponsored.service.product.po.OdsVcProduct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Repository
@Slf4j
public  class OdsVcProductDaoImpl extends DorisBaseDaoImpl<OdsVcProduct> implements IOdsVcProductDao {

    @Override
    public Page<OdsVcProduct> getProductPageListWithProductRight(Integer puid, Long uid , SPPageParam param , boolean isNeedCheckRight) {
        // 1. 初始化参数列表和SQL构建器（完全参考 getProductRightPageList 模式）
        List<Object> argsList = new ArrayList<>();
        StringBuilder select = new StringBuilder();
        StringBuilder countSql = new StringBuilder("SELECT COUNT(*) FROM (");

        // 2. 构建SELECT子句（字段映射到实体属性）
        select.append("SELECT ");
        select.append("id, ");
        select.append("puid, ");
        select.append("shop_id, ");
        select.append("marketplace_id, ");
        select.append("asin, ");
        select.append("parent_asin, ");
        select.append("child_asins, ");
        select.append("main_image, ");
        select.append("msku, ");
        select.append("commodity_id, ");
        select.append("match_commodity_time, ");
        select.append("title, ");
        select.append("brand, ");
        select.append("manufacturer, ");
        select.append("manufacturer_code, ");
        select.append("variation_child_str, ");
        select.append("part_number, ");
        select.append("product_bundle, ");
        select.append("is_variation, ");
        select.append("standard_product_id, ");
        select.append("standard_product_type, ");
        select.append("standard_price, ");
        select.append("standard_price_currency, ");
        select.append("net_cost_amount, ");
        select.append("net_cost_amount_currency, ");
        select.append("status, ");
        select.append("display_group_rank, ");
        select.append("display_group_ranks, ");
        select.append("classification_rank, ");
        select.append("classification_ranks, ");
        select.append("last_sync_time, ");
        select.append("create_time, ");
        select.append("update_time, ");
        select.append("create_id, ");
        select.append("update_id ");

        // 3. 构建FROM子句
        select.append("FROM ods_t_vc_product ");
        if (isNeedCheckRight) {
            select.append(" join ods_t_ad_product_permission p on p.puid = puid and p.shop_id = shop_id and p.asin = asin and p.uid = ? ");
            argsList.add(uid);
        }

        // 4. 构建WHERE条件（参考 getProductRightPageList 的参数顺序）
        select.append("WHERE puid = ? AND shop_id = ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());

        // 5. 添加基础过滤条件（参考原逻辑）
        select.append("AND is_variation IN (0, 2) ");
        select.append("AND status = 1 ");

        // 6. 添加搜索条件（完全参考 getProductRightPageList 的处理方式）
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("title".equals(param.getSearchField())) {
                select.append("AND title LIKE ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue()) + "%");

            } else if ("asin".equals(param.getSearchField())) {
                // 使用 SqlStringUtil.dealInList 处理ASIN多值搜索（与 getProductRightPageList 一致）
                select.append(SqlStringUtil.dealInList("asin",
                        Arrays.asList(Arrays.stream(param.getSearchValue().split(StringUtil.SPLIT_COMMA)).toArray()),
                        argsList));

            } else if ("msku".equals(param.getSearchField())) {
                select.append("AND msku LIKE ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(param.getSearchValue()) + "%");
            }
        }

        // 7. 添加GROUP BY和ORDER BY（参考原逻辑）
        select.append("GROUP BY puid, shop_id, asin ");
        select.append("ORDER BY ");
        if ("asc".equalsIgnoreCase(param.getOrderType())) {
            select.append("MAX(id) ASC ");
        } else {
            select.append("MAX(id) DESC ");
        }

        // 8. 构建COUNT SQL（完全参考 getProductRightPageList 模式）
        countSql.append(select).append(") c");

        // 9. 准备参数数组（参考 getProductRightPageList）
        Object[] args = argsList.toArray();

        return getPageResult(param.getPageNo(), param.getPageSize(), countSql.toString(), args , select.toString() , args , OdsVcProduct.class);
    }
}
