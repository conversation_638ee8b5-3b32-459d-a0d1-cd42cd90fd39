package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by xp on 2021/4/14.
 * 广告组下的否定关键词列表页入参
 */
@Data
@ApiModel
public class NeKeywordsPageParam {
    @ApiModelProperty("广告类型 sp sb")
    private String type;
    private Integer pageNo;
    private Integer pageSize;
    @ApiModelProperty(value = "店铺ID",required = true)
    private Integer shopId;
    private Integer puid;
    private Integer uid;
    private String uuid;
    @ApiModelProperty("活动ID")
    private String campaignId;
    @ApiModelProperty("广告组ID")
    private String groupId;
    @ApiModelProperty("匹配类型 negativeExact 精确匹配，negativePhrase 词组匹配")
    private String matchType;
    @ApiModelProperty("操作状态")
    private String state;
    @ApiModelProperty("服务状态")
    private String servingStatus;
    private String searchField;
    private String searchValue;
    private String status;
    private Integer dxmCampaignId;
    private String startDate;
    private String endDate;

    @ApiModelProperty("广告组合ID")
    private String portfolioId;
    @ApiModelProperty(value = "广告组合下的活动id")
    private List<String> campaignIdList;
    private String searchType;
    private int totalSize;

    /**
     * 否定投放报告筛选dto
     */
    private NeTargetReportFilterDto neTargetReportFilterDto;
    private Pair<Boolean, List<String>> checkProductRightPair;

    private boolean adminUser;

    //关键词投放-》增加批量搜索
    public List<String> getListSearchValue(){
        if(StringUtils.isNotBlank(this.searchValue)){
            return com.meiyunji.sponsored.common.util.StringUtil.splitStr(this.searchValue.trim(),"%±%");
        }
        return new ArrayList<>();
    }

}
