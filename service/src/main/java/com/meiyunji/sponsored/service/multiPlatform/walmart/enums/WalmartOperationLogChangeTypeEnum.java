package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

@Getter
public enum WalmartOperationLogChangeTypeEnum {
    startDate("startDate","开始时间", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_DATE}),
    endDate("endDate","结束时间", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_DATE}),
    BID("bid","竞价", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_BID}),
    STATUS("status","状态", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_STATE_OR_DELETE}),
    NAME("name","状态", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_NAME}),
    BID_STRATEGY("biddingStrategy","竞价策略", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_BID_STRATEGY}),
    TOTAL_BUDGET("totalBudget","总预算", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_BUDGET}),
    DAILY_BUDGET("dailyBudget","每日预算", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_BUDGET}),
    STATE("state","状态", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_STATE_OR_DELETE}),
    MULTIPLIER("multiplier","竞价倍数", new WalmartOperationLogActionEnum[]{WalmartOperationLogActionEnum.EDIT_PLACEMENT_MULTIPLIER, WalmartOperationLogActionEnum.EDIT_PLATFORM_MULTIPLIER}),
    ;

    private String changeField;

    private String changeValue;

    private WalmartOperationLogActionEnum[] actionType;

    WalmartOperationLogChangeTypeEnum(String changeField, String changeValue, WalmartOperationLogActionEnum[] actionType) {
        this.changeField = changeField;
        this.changeValue = changeValue;
        this.actionType = actionType;
    }

    public static WalmartOperationLogActionEnum[] getOperationLogActionType(String changeField){
        WalmartOperationLogChangeTypeEnum[] values = values();
        for (WalmartOperationLogChangeTypeEnum value : values) {
            if(changeField.equalsIgnoreCase(value.getChangeField())){
                return value.getActionType();
            }
        }
        return null;
    }

    public static WalmartOperationLogChangeTypeEnum getOperationLogChange(String changeType){
        WalmartOperationLogChangeTypeEnum[] values = values();
        for (WalmartOperationLogChangeTypeEnum value : values) {
            if(changeType.equalsIgnoreCase(value.getChangeField())){
                return value;
            }
        }
        return null;
    }

}
