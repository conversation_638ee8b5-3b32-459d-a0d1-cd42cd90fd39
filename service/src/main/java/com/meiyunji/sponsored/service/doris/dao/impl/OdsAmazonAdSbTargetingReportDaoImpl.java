package com.meiyunji.sponsored.service.doris.dao.impl;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.bo.AdKeywordOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbTargetingReport;
import com.meiyunji.sponsored.service.cpc.qo.ReportAdvancedFilterBaseQo;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.KeywordDataFieldEnum;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSbTargetingReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbTargetingReport;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * sb商品投放报告(OdsAmazonAdSbTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Repository
@Slf4j
public class OdsAmazonAdSbTargetingReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdSbTargetingReport> implements IOdsAmazonAdSbTargetingReportDao {

    private static final Map<String, String> sbTargetingFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> sbTargetingContainRateFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> sbTargetingOrderFieldMap = Maps.newHashMap();

    static {
        sbTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.COST.getCode(), "ifnull(sum(report.cost * c.rate), 0) as cost ");
        sbTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.TOTAL_SALES.getCode(), "ifnull(sum(sales14d * c.rate), 0) as totalSales ");
        sbTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.ACOS.getCode(), " ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(sales14d * c.rate), 0), 4), 0) as acos ");
        sbTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.ROAS.getCode(), " ifnull(ROUND(ifnull(sum(sales14d * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) as roas ");

        sbTargetingFieldSumMap.put(DashboardDataFieldEnum.IMPRESSIONS.getCode(), "ifnull(sum(report.impressions), 0) as impressions ");
        sbTargetingFieldSumMap.put(DashboardDataFieldEnum.CLICKS.getCode(), "ifnull(sum(report.clicks), 0) as clicks ");
        sbTargetingFieldSumMap.put(DashboardDataFieldEnum.ORDER_NUM.getCode(), " ifnull(sum(conversions14d), 0) as orderNum ");
        sbTargetingFieldSumMap.put(DashboardDataFieldEnum.SALE_NUM.getCode(), "ifnull(sum(units_sold14d), 0) as saleNum ");
        sbTargetingFieldSumMap.put(DashboardDataFieldEnum.CLICK_RATE.getCode(), " ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) as clickRate ");//点击率
        sbTargetingFieldSumMap.put(DashboardDataFieldEnum.CONVERSION_RATE.getCode(), " ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) as conversionRate ");//转化率

        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.IMPRESSIONS.getCode(), " impressions as impressions ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.CLICKS.getCode(), " clicks as clicks ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.COST.getCode(), " cost as cost ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.ORDER_NUM.getCode(), " conversions14d as adOrder ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.TOTAL_SALES.getCode()," sales14d as adSales ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.CPC.getCode()," cost as cost,clicks as clicks ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.CPA.getCode()," cost as cost,conversions14d as adOrder ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.ACOS.getCode()," cost as cost,sales14d as adSales ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.ROAS.getCode()," cost as cost,sales14d as adSales ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.CONVERSION_RATE.getCode(), " ,clicks as clicks,conversions14d as adOrder ");
        sbTargetingOrderFieldMap.put(KeywordDataFieldEnum.CLICK_RATE.getCode(), "clicks as clicks,impressions as impressions ");
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getTargetingTopList(Integer puid, List<String> marketplaceIdList,
                                                                      List<Integer> shopIdList, String currency,
                                                                      String startDate, String endDate,
                                                                      DashboardDataFieldEnum dataField, List<String> targetIdList,
                                                                      Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                      List<String> campaignIds, Boolean noZero) {
        boolean isWhere = false;
        if (Objects.isNull(sbTargetingFieldSumMap.get(dataField.getCode())) && Objects.isNull(sbTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT 'sb' as `type`, report.target_id targetingId, ");
        if (Objects.nonNull(sbTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            for (String rateField : sbTargetingContainRateFieldSumMap.keySet()) {
                sb.append(sbTargetingContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            sb.append(sbTargetingFieldSumMap.get(dataField.getCode()));
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append(" report ");
        if (Objects.nonNull(sbTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
            sb.append(" and report.puid = ? ");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            isWhere = true;
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append("and report.target_id in ('").append(StringUtils.join(targetIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sb.append(" and ");
            } else {
                sb.append(" where ");
            }
            sb.append(" report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by report.target_id ");
        sb.append(" ORDER BY ").append(dataField.getCode());
        if (StringUtils.isNotEmpty(orderBy.getCode())) {
            sb.append(" ").append(orderBy.getCode());
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getTargetingTopInfoList(Integer puid, List<String> marketplaceIdList,
                                                                          List<Integer> shopIdList, String currency,
                                                                          String startDate, String endDate,
                                                                          DashboardDataFieldEnum dataField, List<String> targetIdList,
                                                                          DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                          List<String> campaignIds, Boolean noZero) {
        if (Objects.isNull(sbTargetingFieldSumMap.get(dataField.getCode())) && Objects.isNull(sbTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT 'sb' as `type`, report.target_id targetingId, ");
        if (Objects.nonNull(sbTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sbTargetingFieldSumMap.forEach((k, v) -> {
                    sb.append(v);
                    sb.append(",");
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            for (String rateField : sbTargetingContainRateFieldSumMap.keySet()) {
                sb.append(sbTargetingContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sbTargetingFieldSumMap.forEach((k, v) -> {
                if (!k.equals((dataField.getCode()))) {
                    sb.append(v);
                    sb.append(",");
                }
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append(" report ");
        if (Objects.isNull(sbTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month and report.puid = ?");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(" and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(" and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append(" and report.target_id in ('").append(StringUtils.join(targetIdList, "','")).append("') ");
        }

        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sb' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by report.target_id ");
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public int getTargetAllCount(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select k.target_id ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_targeting_sb k");
        // 没有高级筛选或排序时，只查基础数据表即为列表页所有的关键词，有高级筛选或排序时需连报告表过滤
        if (param.getUseAdvanced() || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()))) {
            sb.append(" left join ods_t_amazon_ad_sb_targeting_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.target_id = r.target_id ").append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null)) || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ").append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sb.append(this.getTargetPageWhereSql(puid, param, argsList));
        sb.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        Object[] args = argsList.toArray();
        return countPageResult(puid, sqlCountSb.toString(), args);
    }

    @Override
    public Page<TargetPageVo> getTargetPage(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();

        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select k.target_id ");
        StringBuilder sqlSb = new StringBuilder("select ANY(k.target_id) targetId ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_targeting_sb k");
        // 没有高级筛选或排序时，只查基础数据表即为列表页所有的关键词，有高级筛选或排序时需连报告表过滤
        if (param.getUseAdvanced() || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()))) {
            sb.append(" left join ods_t_amazon_ad_sb_targeting_report r on k.puid = r.puid and k.shop_id = r.shop_id and k.target_id = r.target_id ").append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null)) || (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType()) && SqlStringReportUtil.BID.equals(param.getOrderField()))) {
            sb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ").append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sb.append(this.getTargetPageWhereSql(puid, param, argsList));
        sb.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        sqlSb.append(sb);
        //排序
        sqlSb.append(SqlStringReportUtil.getDorisTargetPageOrderBySql(param.getOrderField(), param.getOrderType(), "ANY(k.create_time) desc", param.getType()) + ", targetId desc");
        Object[] args = argsList.toArray();
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), sqlCountSb.toString(), args, sqlSb.toString(), args, TargetPageVo.class);
    }

    @Override
    public List<TargetPageVo> getReportListByTargetIds(Integer puid, Integer shopId, List<String> targetIdList, String startStr, String endStr) {
        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder("select  ANY(target_id) targetId,'sb' type,").append("IFNULL(SUM(impressions), 0) impressions,").append("IFNULL(SUM(clicks), 0) clicks,").append("IFNULL(SUM(cost), 0)  cost,").append("IFNULL(SUM(units_sold14d), 0) order_num,").append("IFNULL(SUM(conversions14d), 0) sale_num,").append("IFNULL(SUM(conversions14d_same_sku), 0) ad_sale_num,").append("IFNULL(SUM(sales14d), 0)      total_sales,").append("IFNULL(SUM(sales14d_same_sku), 0)         `ad_sales`,").append("IFNULL(MAX(top_of_search_is), 0) max_top_is,").append("IFNULL(MIN(top_of_search_is), 0) min_top_is,").append("IFNULL(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,").append("IFNULL(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,").append("IFNULL(sum(video5second_views), 0) video5SecondViews,").append("IFNULL(sum(video_first_quartile_views), 0) video_first_quartile_views,").append("IFNULL(sum(video_Midpoint_Views), 0) video_Midpoint_Views,").append("IFNULL(sum(video_third_quartile_views), 0) video_third_quartile_views,").append("IFNULL(sum(video_complete_views), 0) video_complete_views,").append("IFNULL(sum(video_unmutes), 0) video_unmutes,").append("IFNULL(sum(viewable_impressions), 0) viewable_impressions,").append("IFNULL(sum(branded_searches14d), 0) brandedSearches").append(" from ").append(this.getJdbcHelper().getTable());
        sb.append(" where puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startStr);
        argsList.add(endStr);
        sb.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIdList, argsList));
        sb.append(" group by target_id ");
        return getJdbcTemplate().query(sb.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(TargetPageVo.class));
    }

    @Override
    public AdMetricDto getTargetPageSumMetricData(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder topSb = new StringBuilder("select SUM(cost) sumCost,SUM(sale_num) sumAdOrderNum,SUM(total_sales) sumAdSale, SUM(order_num) sumOrderNum from ( ");
        StringBuilder sqlSb = new StringBuilder(" select ANY(k.puid) puid, ANY(k.shop_id) shopId, ANY(k.ad_group_id) adGroupId, ANY(k.bid) bid, SUM(cost) cost,SUM(conversions14d) sale_num,SUM(sales14d) total_sales, SUM(units_sold14d) order_num ");
        sqlSb.append(" from ods_t_amazon_ad_targeting_sb k join ods_t_amazon_ad_sb_targeting_report r ").append(" on k.puid = r.puid and k.shop_id = r.shop_id and k.target_id = r.target_id ").append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());

        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sqlSb.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ").append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sqlSb.append(this.getTargetPageWhereSql(puid, param, argsList));
        sqlSb.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sqlSb.append(this.getTargetPageHavingSql(param, argsList));
        }
        sqlSb = new StringBuilder(topSb).append(sqlSb).append(" ) s");
        Object[] args = argsList.toArray();
        List<AdMetricDto> list = getJdbcTemplate().query(sqlSb.toString(), args, new BeanPropertyRowMapper<>(AdMetricDto.class));
        return list.size() == 1 ? list.get(0) : null;
    }

    @Override
    public List<String> getTargetIdListByPage(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String sql = getTargetIdListByPageSql(puid, param, argsList, false);
        return this.getJdbcTemplate().queryForList(sql, String.class, argsList.toArray());
    }

    private String getTargetIdListByPageSql(Integer puid, TargetingPageParam param, List<Object> argsList, boolean isLeftJoin) {
        StringBuilder sql = new StringBuilder();
        sql.append(" select k.target_id from ods_t_amazon_ad_targeting_sb k ");
        if (isLeftJoin) {
            sql.append(" left ");
        }
        sql.append(" join ");
        sql.append(this.getJdbcHelper().getTable())
                .append(" r on r.puid = k.puid and r.shop_id = k.shop_id and r.target_id = k.target_id ")
                .append(" and r.puid = ? and r.shop_id = ? and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        //若有竞价排序或者竞价的高级筛选，需要连广告组表，获取广告组默认竞价
        if ((param.getUseAdvanced() && (param.getBidMax() != null || param.getBidMin() != null))) {
            sql.append(" join ods_t_amazon_ad_group_sb g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sql.append(this.getTargetPageWhereSql(puid, param, argsList));
        sql.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sql.append(this.getTargetPageHavingSql(param, argsList));
        }
        return sql.toString();
    }

    @Override
    public List<AdHomePerformancedto> getReportAggregateByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupByDate) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' type,");
        if (isGroupByDate) {
            sql.append(" ANY(count_date) count_date, ");
        }
        sql.append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(cost), 0)  adCost,")
                .append("IFNULL(SUM(conversions14d), 0) ad_order_num,")
                .append("IFNULL(SUM(units_sold14d), 0) salesNum,")
                .append("IFNULL(SUM(conversions14d_same_sku), 0) ad_sale_num,")
                .append("IFNULL(SUM(sales14d), 0)      adSale,")
                .append("IFNULL(SUM(sales14d_same_sku), 0)         `ad_sales`,")
                .append("IFNULL(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append("IFNULL(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append("IFNULL(sum(video5second_views), 0) video5SecondViews,")
                .append("IFNULL(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append("IFNULL(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append("IFNULL(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append("IFNULL(sum(video_complete_views), 0) video_complete_views,")
                .append("IFNULL(sum(video_unmutes), 0) video_unmutes,")
                .append("IFNULL(sum(viewable_impressions), 0) viewable_impressions,")
                .append("IFNULL(sum(branded_searches14d), 0) brandedSearches")
                .append(" from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIdList, argsList));
        sql.append("  and count_day >= ? and count_day <= ? ");
        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }
        argsList.add(startStr);
        argsList.add(endStr);
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class));
    }

    @Override
    public AdHomePerformancedto getReportAggregateCompareData(Integer puid, TargetingPageParam param, String startStr, String endStr) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' type,");
        sql.append("IFNULL(SUM(impressions), 0) impressions,")
                .append("IFNULL(SUM(clicks), 0) clicks,")
                .append("IFNULL(SUM(cost), 0)  adCost,")
                .append("IFNULL(SUM(conversions14d), 0) ad_order_num,")
                .append("IFNULL(SUM(units_sold14d), 0) salesNum,")
                .append("IFNULL(SUM(conversions14d_same_sku), 0) ad_sale_num,")
                .append("IFNULL(SUM(sales14d), 0)      adSale,")
                .append("IFNULL(SUM(sales14d_same_sku), 0)         `ad_sales`,")
                .append("IFNULL(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append("IFNULL(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append("IFNULL(sum(video5second_views), 0) video5SecondViews,")
                .append("IFNULL(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append("IFNULL(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append("IFNULL(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append("IFNULL(sum(video_complete_views), 0) video_complete_views,")
                .append("IFNULL(sum(video_unmutes), 0) video_unmutes,")
                .append("IFNULL(sum(viewable_impressions), 0) viewable_impressions,")
                .append("IFNULL(sum(branded_searches14d), 0) brandedSearches")
                .append(" from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sql.append("  and count_day >= ? and count_day <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);
        sql.append(" and target_id in ( ").append(this.getTargetIdListByPageSql(puid, param, argsList, true)).append(" ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }
    @Override
    public List<AsinLibsDetailVo> getSbAsinReportDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> sbTargetIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select target_id targetId, any(targeting_text) targetingText, sum(cost * c.rate) cost, sum(impressions) impressions, " +
                "sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales, ifnull(ROUND(ifnull(sum(cost), 0)/ ifnull(sum(sales14d), 0), 4), 0) acos, " +
                "ifnull(ROUND(ifnull(sum(sales14d), 0)/ ifnull(sum(cost), 0), 4), 0) roas, ifnull(sum(cost * c.rate)/sum(clicks),0) cpc, ifnull(sum(cost * c.rate)/sum(conversions14d),0) cpa, " +
                "ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) salesConversionRate, ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" r ");
        selectSql.append(" join (select * from dim_currency_rate where puid = ? and `to` = ?) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        argsList.add(puid);
        argsList.add(param.getTo());
        selectSql.append(" join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(sbTargetIds)) {
            selectSql.append(SqlStringUtil.dealInList("r.target_id", sbTargetIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealInList("r.ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" group by target_id ");
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AsinLibsDetailVo.class), argsList.toArray());
    }

    @Override
    public List<AdAsinOrderBo> getOrderFieldByAsins(Integer puid, List<String> shopIds, PageListAsinsRequest param, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select targeting_text asin ");
        if (StringUtils.isNotBlank(param.getOrderField())) {
            sqlSb.append(",");
            sqlSb.append(sbTargetingOrderFieldMap.get(param.getOrderField()));
        }
        sqlSb.append("from ods_t_amazon_ad_sb_targeting_report report where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sqlSb.append(SqlStringUtil.dealInList("report.shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if (StringUtils.isNotEmpty(param.getMarketplaceId())) {
            sqlSb.append(SqlStringUtil.dealInList("report.marketplace_id", Arrays.asList(param.getMarketplaceId().split(",")), argsList));
        }

        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            sqlSb.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }

        sqlSb.append(" and target_id in ");

        sqlSb.append(" (SELECT target_id FROM ods_t_amazon_ad_targeting_sb b WHERE b.puid = ? ");
        argsList.add(puid);

        sqlSb.append("and  b.type = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)) {
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : asinList) {
                lowerCaseList.add(str.toLowerCase());
            }
            sqlSb.append(SqlStringUtil.dealInList("lower(b.target_text)", lowerCaseList, argsList));
        }
        sqlSb.append(" ) ");

        return getJdbcTemplate().query(sqlSb.toString(), new ObjectMapper<>(AdAsinOrderBo.class), argsList.toArray());

    }

    @Override
    public List<OdsAmazonAdSbTargetingReport> getReportByTargetIdsByCountDate(int puid, List<Integer> shopIds, List<String> marketplaceIds, String startDate, String endDate,
                                                                              List<String> targetIds, boolean changeRate, String currency) {
        List<Object> args = new ArrayList<>();
        //子查询sql
        StringBuilder sql = new StringBuilder("SELECT count_day,")
                .append("ifnull(sum(`clicks`), 0) clicks, ifnull(sum(`impressions`), 0) impressions, ")
                .append("ifnull(sum(`conversions14d`), 0) conversions14d, ifnull(sum(`conversions14d_same_sku`), 0) conversions14d_same_sku,")
                .append("ifnull(sum(`units_sold14d`), 0) units_sold14d, ifnull(sum(`viewable_impressions`), 0) viewable_impressions, ");
//                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d, sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d, ")
        if (changeRate) {
            sql.append(" ifnull(sum(`cost` * d.rate), 0) cost, ")
                    .append(" ifnull(sum(`sales14d` * d.rate), 0) sales14d, ")
                    .append(" ifnull(sum(`sales14d_same_sku` * d.rate), 0) sales14d_same_sku ");
//                    .append(" sum(`sales_new_to_brand14d` * d.rate) sales_new_to_brand14d ")
        } else {
            sql.append(" ifnull(sum(`cost`), 0) cost, ")
                    .append(" ifnull(sum(`sales14d`), 0) sales14d, ")
                    .append(" ifnull(sum(`sales14d_same_sku`), 0) sales14d_same_sku ");
//                    .append(" sum(`sales_new_to_brand14d`) sales_new_to_brand14d ")
        }
        sql.append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        if (changeRate) {
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            //关联币种表、汇率表
            sql.append(" join ");
            sql.append(" ( select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? and c.month >= ? and c.month <= ? ");
            args.add(puid);
            args.add(currency);
            args.add(start);
            args.add(end);
            sql.append(SqlStringUtil.dealInList("m.marketplace_id", marketplaceIds, args));
            sql.append(" ) d ");
            sql.append(" on d.marketplace_id = r.marketplace_id and d.month = r.count_month ");
        }
        sql.append(" where`puid`=? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIds, args));
        sql.append(" and `count_day`>=? and count_day<=? ");
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIds, args));
        sql.append(" group by count_day ");
        return getJdbcTemplate().query(sql.toString(), new BeanPropertyRowMapper<>(OdsAmazonAdSbTargetingReport.class), args.toArray());
    }

    @Override
    public List<AsinLibsDetailVo> getAsinReportByAsins(Integer puid, PageListAsinsRequest param,
                                                      String startDate, String endDate,
                                                      List<String> asinList, String currency) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select targeting_text asin, sum(cost * c.rate) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales from ods_t_amazon_ad_sb_targeting_report r ");
        sql.append("join (select * from dim_currency_rate where puid = ?");
        argsList.add(puid);
        sql.append(" and `to` = ? ");
        argsList.add(currency);
        sql.append(" ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopListList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopListList(), argsList));
        }
        //通过来源站点筛选
        if (CollectionUtils.isNotEmpty(param.getContryList())) {
            sql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getContryList(), argsList));
        }

        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        sql.append(" and target_id in ");

        sql.append(" (SELECT target_id FROM ods_t_amazon_ad_targeting_sb b WHERE b.puid = ? ");
        argsList.add(puid);

        sql.append("and  b.type = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)) {
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : asinList) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(b.target_text)", lowerCaseList, argsList));
        }
        sql.append(" ) ");
//        if (CollectionUtils.isNotEmpty(asinList)) {
//            // 创建一个新的列表来存储转换后的字符串
//            List<String> lowerCaseList = new ArrayList<>();
//            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
//            for (String str : asinList) {
//                lowerCaseList.add("asin=\"" + str.toLowerCase() + "\"");
//            }
//            sql.append(SqlStringUtil.dealInList("lower(targeting_text)", lowerCaseList, argsList));
//        }
        sql.append(" group by targeting_text");
        return getJdbcTemplate().query(sql.toString(), (res, i) -> {
            String strAsin = res.getString("asin").replaceAll("asin=", "").replaceAll("\"", "");
            String asin = Optional.of(strAsin).filter(StringUtils::isNotEmpty).orElse("");
            return AsinLibsDetailVo.builder()
                    .asin(asin)
                    .cost(res.getBigDecimal("cost"))
                    .impressions(res.getLong("impressions"))
                    .clicks(res.getLong("clicks"))
                    .saleNum(res.getInt("saleNum"))
                    .totalSales(res.getBigDecimal("totalSales"))
                    .build();
        }, argsList.toArray());
    }

    /**
     * 投放列表页where条件sql拼接
     */
    private String getTargetPageWhereSql(Integer puid, TargetingPageParam param, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where k.puid = ? and k.shop_id = ?");
        argsList.add(puid);
        argsList.add(param.getShopId());
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", list, argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.ad_group_id", list, argsList));

        }
        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.target_id", param.getTargetIds(), argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(param.getAdStrategyTypeList())){
            String sql = AdTargetStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(),param.getAutoRuleGroupIds(), argsList, "k.target_id","k.ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                sb.append(sql);
            }
        }
        if (StringUtils.isNotBlank(param.getFilterTargetType())) {
            if ("asin".equalsIgnoreCase(param.getFilterTargetType())) {
                sb.append(" and k.type='asin' ");
            } else if ("category".equalsIgnoreCase(param.getFilterTargetType())) {
                sb.append(" and k.type='category'  ");
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and lower(k.target_text) = ? and k.type = 'asin' ");
                argsList.add(param.getSearchValue().toLowerCase());
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and lower(k.target_text) like ? and k.type = 'category' ");
                argsList.add("%" + param.getSearchValue().toLowerCase() + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            sb.append(SqlStringUtil.dealInList("k.state", statusList, argsList));
        }
        return sb.toString();
    }

    /**
     * 投放投放列表页having条件sql拼接
     */
    private String getTargetPageHavingSql(TargetingPageParam param, List<Object> argsList) {
        ReportAdvancedFilterBaseQo qo = new ReportAdvancedFilterBaseQo();
        BeanUtils.copyProperties(param, qo);
        return SqlStringReportUtil.getDorisSbTargetPageHavingSql(qo, argsList);
    }
}

