package com.meiyunji.sponsored.service.batchCreate.task;

import com.amazon.advertising.spV3.enumeration.SpV3ExpressionEnum;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.negativetargeting.CreateSpNegativeTargetV3Response;
import com.amazon.advertising.spV3.negativetargeting.NegativeTargetSpV3Client;
import com.amazon.advertising.spV3.negativetargeting.entity.*;
import com.amazon.advertising.spV3.response.BaseErrorV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.batchCreate.dao.IAmazonAdBatchNetargetingDao;
import com.meiyunji.sponsored.service.batchCreate.dto.task.BatchCreateReturnDto;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.*;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNekeyword;
import com.meiyunji.sponsored.service.batchCreate.po.AmazonAdBatchNetargeting;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdNeTargetingDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdNeTargeting;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdTargeting;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 关键词批量处理任务
 *
 * @author: panxiaoqing
 * @email: <EMAIL>
 * @date: 2023-11-28  14:20
 */
@Component
@Slf4j
public class NegativeTargetingCallAmazonApiTask extends AbstractCallAmazonApiTask {
    @Autowired
    private IAmazonAdBatchNetargetingDao amazonAdBatchNetargetingDao;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private TaskStatusHelper taskStatusHelper;
    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;

    private final String logPrefix = "batch create neTargeting api call ";

    public void call(CallAmazonApiTaskResultDto resultDto) {
        log.info(logPrefix + ",puid:{},shopId:{},taskId:{},traceId:{},start", resultDto.getPuid(), resultDto.getShopId(), resultDto.getTaskId(), resultDto.getTraceDto().getTraceId());
        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList())) {
            return;
        }
        Integer puid = resultDto.getPuid();
        Integer shopId = resultDto.getShopId();
        Long taskId = resultDto.getTaskId();
        //1、获取redisson分布式锁，锁标识为："sp_batch:keyword:"+ task_id;
        RLock lock = redissonClient.getLock(SpBatchCreateAdTaskLevelEnum.LEVEL_NETATRGETING.getLockKey() + taskId);
        boolean b = false;
        try {
            b = lock.tryLock(SpBatchConstants.TRY_LOCK_SECONDS, SpBatchConstants.LOCK_MINUTES, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            log.info(logPrefix + "tryLock err,tract,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, resultDto.getTraceDto().getTraceId());
            return;
        }
        if (!b) {
            log.info(logPrefix + " in progress,puid：{},shopId:{},taskId:{},traceId:{}", puid, shopId, taskId, resultDto.getTraceDto().getTraceId());
            return;
        }

        List<AmazonAdBatchNetargeting> netargetings = new ArrayList<>();
        try {
            ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(resultDto.getShopId(), resultDto.getPuid());
            //2、根据传入的id列表获取不为成功或者终止状态的关键词集合
            List<Long> groupIdList = resultDto.getSuccessIdList();
            if (resultDto.isCurrentLevel()) {
                netargetings = amazonAdBatchNetargetingDao.listByIdList(puid, shopId, groupIdList,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            } else {
                netargetings = amazonAdBatchNetargetingDao.listByGroupIdList(puid, shopId, taskId, groupIdList,
                        Lists.newArrayList(SpBatchCreateAdLevelStatusEnum.DOING.getCode(), SpBatchCreateAdLevelStatusEnum.FAILURE_AND_RETRY.getCode()));
            }
            if (CollectionUtils.isEmpty(netargetings)) {
                return;
            }
            //3、分片请求亚马逊返回列表分成成功失败可重试三种类型
            Map<Integer, String> succMap = new HashMap<>();
            Map<Integer, String> errMap = new HashMap<>();
            List<Integer> retryList = new ArrayList<>();
            List<List<AmazonAdBatchNetargeting>> netargetingPartition = Lists.partition(netargetings, SpBatchConstants.REQUEST_PARTITION_SIZE);
            for (int i = 0; i < netargetingPartition.size(); i++) {
                BatchCreateReturnDto batchCreateReturnDto = this.callByPartition(shop, netargetingPartition.get(i), i * SpBatchConstants.REQUEST_PARTITION_SIZE);
                succMap.putAll(batchCreateReturnDto.getSuccMap());
                errMap.putAll(batchCreateReturnDto.getErrMap());
                retryList.addAll(batchCreateReturnDto.getRetryList());
            }

            //4、成功处理
            if (succMap.size() > 0) {
                List<AmazonAdTargeting> succNetargeting = new ArrayList<>();
                Map<Long, String> succBatchKeywordIdMap = new HashMap<>();
                for (Map.Entry<Integer, String> succMapEntry : succMap.entrySet()) {
                    AmazonAdBatchNetargeting amazonAdBatchNetargeting = netargetings.get(succMapEntry.getKey());
                    succBatchKeywordIdMap.put(amazonAdBatchNetargeting.getId(), succMapEntry.getValue());
                    //构建日志的po
                    AmazonAdTargeting amazonAdTargeting = this.batchNegativeTargeting2NegativeTargeting(amazonAdBatchNetargeting);
                    amazonAdTargeting.setTargetId(succMapEntry.getValue());
                    succNetargeting.add(amazonAdTargeting);
                }
                //插入成功的关键词
                amazonAdTargetDaoRoutingService.insertOnDuplicateKeyUpdate(puid, succNetargeting, Constants.TARGETING_TYPE_NEGATIVEASIN);
                //修改当前层级任务的状态为成功
                amazonAdBatchNetargetingDao.updateSuccTaskStatusByIdList(puid, succBatchKeywordIdMap);
                //记录成功日志
                this.collectSuccLog(succNetargeting, resultDto.getLoginIp());
            }

            //5、失败处理
            List<AmazonAdTargeting> errNetargeting = new ArrayList<>();
            if (errMap.size() > 0) {
                Map<Long, String> idMsgMap = new HashMap<>();
                for (Map.Entry<Integer, String> errMapEntry : errMap.entrySet()) {
                    AmazonAdBatchNetargeting amazonAdBatchNetargeting = netargetings.get(errMapEntry.getKey());
                    idMsgMap.put(amazonAdBatchNetargeting.getId(), errMapEntry.getValue());
                    //构建日志po
                    AmazonAdTargeting amazonAdTargeting = this.batchNegativeTargeting2NegativeTargeting(amazonAdBatchNetargeting);
                    amazonAdTargeting.setError(errMapEntry.getValue());
                    errNetargeting.add(amazonAdTargeting);
                }
                //修改当前层级任务和层级下任务的状态为失败
                taskStatusHelper.batchUpdateErrStatus(puid, taskId, idMsgMap, SpBatchCreateAdTaskLevelEnum.LEVEL_NETATRGETING, null, true);
            }

            //6、重试处理
            if (retryList.size() > 0) {
                List<Long> retryIdList = new ArrayList<>();
                Map<Long, String> errIdMap = new HashMap<>();
                for (Integer index : retryList) {
                    AmazonAdBatchNetargeting amazonAdBatchNetargeting = netargetings.get(index);
                    AmazonAdTargeting amazonAdTargeting = this.batchNegativeTargeting2NegativeTargeting(amazonAdBatchNetargeting);
                    //若重试超过三次则直接置为失败
                    if (amazonAdBatchNetargeting.getExecuteCount() + 1 >= SpBatchConstants.EXECUTE_COUNT_LIMIT) {
                        errIdMap.put(amazonAdBatchNetargeting.getId(), SpBatchConstants.RETRY_ERROR_MSG);
                        amazonAdTargeting.setError(SpBatchConstants.RETRY_ERROR_MSG);
                    } else {
                        retryIdList.add(amazonAdBatchNetargeting.getId());
                        amazonAdTargeting.setError(SpBatchConstants.RETRY_MSG);
                    }
                    errNetargeting.add(amazonAdTargeting);
                }
                if (CollectionUtils.isNotEmpty(retryIdList)) {
                    amazonAdBatchNetargetingDao.updateRetryTaskStatusByIdList(puid, retryIdList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
                    log.info("sp batch create, create netarget retry, batch traceId: {}, taskId: {}, retryIdList: {}", resultDto.getTaskId(), resultDto.getTaskId(), StringUtils.join(retryIdList, ","));
                }
                if (!errIdMap.isEmpty()) {
                    amazonAdBatchNetargetingDao.updateErrTaskStatusByIdList(puid, errIdMap, true);
                    log.info("sp batch create, create netarget error, batch traceId: {}, taskId: {}, errIdMap: {}", resultDto.getTaskId(), resultDto.getTaskId(), errIdMap);
                }
            }
            //7、记录失败日志
            this.collectFailLog(errNetargeting, resultDto.getLoginIp());
        } catch (Exception e) {
            log.error(String.format("sp batch create netarget, exception, batch traceId: %s, taskId: %s", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId()), e);
            //有异常将本层级全部改为重试状态
            if (CollectionUtils.isEmpty(netargetings)) {
                List<Long> idList = netargetings.stream().map(AmazonAdBatchNetargeting::getId).collect(Collectors.toList());
                amazonAdBatchNetargetingDao.updateRetryTaskStatusByIdList(puid, idList, DateUtil.addSecond(new Date(), SpBatchConstants.RETRY_AFTER_SECONDS));
            }
        } finally {
            //8、释放redission锁
            lock.unlock();
        }
    }

    /**
     * 根据多个广告组维度调亚马逊接口，否定投放不超过分片值
     */
    private BatchCreateReturnDto callByPartition(ShopAuth shop, List<AmazonAdBatchNetargeting> netargetings, int offset) {
        BatchCreateReturnDto dto = new BatchCreateReturnDto();
        Map<Integer, String> succMap = new HashMap<>();
        Map<Integer, String> errMap = new HashMap<>();
        List<Integer> retryList = new ArrayList<>();
        dto.setSuccMap(succMap);
        dto.setErrMap(errMap);
        dto.setRetryList(retryList);
        List<Integer> duplicateValueErrorList = new ArrayList<>();
        //组装亚马逊请求
        List<CreateNegativeTargetEntityV3> negativeTargetEntityV3List = Lists.newArrayList();
        for (AmazonAdBatchNetargeting amazonAdBatchNetargeting : netargetings) {
            negativeTargetEntityV3List.add(this.batchNegativeTargeting2CreateNegativeTargetingV3(amazonAdBatchNetargeting));
        }
        //批量将这些数据提交给亚马逊。
        String profileId = netargetings.get(0).getProfileId();
        CreateSpNegativeTargetV3Response response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shop.getAdAccessToken(), profileId,
                shop.getMarketplaceId(), negativeTargetEntityV3List, Boolean.TRUE);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = NegativeTargetSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createNegativeTargets(shop.getAdAccessToken(), profileId,
                    shop.getMarketplaceId(), negativeTargetEntityV3List, Boolean.TRUE);
        }

        //异常返回，全部加入到重试列表
        if (response == null || response.getStatusCode() == 429 || response.getStatusCode() == 500 || response.getStatusCode() ==-1 || response.getData() == null) {
            for (int i = 0; i < netargetings.size(); i++) {
                retryList.add(i);
            }
            return dto;
        }

        //4、活动提交给亚马逊之后，需要处理其响应，将成功的和失败的分成2组。
        String errMsg = "创建否定投放失败";
        if (response.getData() != null && response.getData().getNegativeTargetingClauses() != null) {
            List<NegativeTargetSuccessResultV3> success = response.getData().getNegativeTargetingClauses().getSuccess();
            List<ErrorItemResultV3> errorItemResultV3s = response.getData().getNegativeTargetingClauses().getError();
            //成功的关键词，批量和单个关键词都设置关键词id
            for (NegativeTargetSuccessResultV3 negativeTargetSuccessResultV3 : success) {
                succMap.put(offset + negativeTargetSuccessResultV3.getIndex(), negativeTargetSuccessResultV3.getTargetId());
            }
            //失败的关键词，批量和单个关键词都设置错误信息
            for (ErrorItemResultV3 keywordResult : errorItemResultV3s) {
                //如果错误信息为创建重复，直接设置为创建成功，后续查询关键词id
                if (AdAmazonErrorTypeEnum.DUPLICATE_VALUE_ERROR.getType().equals(keywordResult.getErrors().get(0).getErrorType())) {
                    duplicateValueErrorList.add(keywordResult.getIndex());
                    continue;
                }
                errMap.put(offset + keywordResult.getIndex(), keywordResult.getErrors().get(0).getErrorMessage());
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
            for (int i = 0; i < netargetings.size(); i++) {
                errMap.put(offset + i, errMsg);
            }
        }

        //对创建重复的调用列表接口获取关键词id，若有则加入到成功列表中
        if (CollectionUtils.isNotEmpty(duplicateValueErrorList)) {
            Map<Integer, CreateNegativeTargetEntityV3> duplicateValueErrorKeywordMap = duplicateValueErrorList.stream().collect(Collectors.toMap(e -> e, negativeTargetEntityV3List::get));
            Map<Integer, String> retrySuccMap = this.duplicateNeTargetingGetId(shop, profileId, duplicateValueErrorKeywordMap, offset);
            succMap.putAll(retrySuccMap);
            retryList.addAll(duplicateValueErrorList.stream().filter(e -> !succMap.containsKey(e + offset)).collect(Collectors.toList()));
        }
        return dto;
    }


    /**
     * 批量创建否定词实体类转亚马逊创建投放实体类
     *
     * @param amazonAdBatchNetargeting
     */
    private CreateNegativeTargetEntityV3 batchNegativeTargeting2CreateNegativeTargetingV3(AmazonAdBatchNetargeting amazonAdBatchNetargeting) {
        CreateNegativeTargetEntityV3 createKeywordEntityV3 = new CreateNegativeTargetEntityV3();
        createKeywordEntityV3.setCampaignId(amazonAdBatchNetargeting.getAmazonCampaignId());
        createKeywordEntityV3.setAdGroupId(amazonAdBatchNetargeting.getAmazonAdGroupId());
        createKeywordEntityV3.setState(SpV3StateEnum.ENABLED.valueV3());
        List<TargetExpression> targetExpressions = new ArrayList<>();
        if (StringUtils.isNotBlank(amazonAdBatchNetargeting.getExpression())) {
            targetExpressions = JSONUtil.jsonToArray(amazonAdBatchNetargeting.getExpression(), TargetExpression.class);
            for (TargetExpression targetExpression : targetExpressions) {
                targetExpression.setType(SpV3ExpressionEnum.getExpressionByValue(targetExpression.getType()).getValueV3());
            }
        }
        createKeywordEntityV3.setExpression(targetExpressions);
        return createKeywordEntityV3;
    }

    /**
     * 批量创建关键词实体类转关键词实体类
     *
     * @param amazonAdBatchNetargeting
     */
    private AmazonAdTargeting batchNegativeTargeting2NegativeTargeting(AmazonAdBatchNetargeting amazonAdBatchNetargeting) {

        AmazonAdTargeting amazonAdTargeting = new AmazonAdTargeting();
        amazonAdTargeting.setPuid(amazonAdBatchNetargeting.getPuid());
        amazonAdTargeting.setShopId(amazonAdBatchNetargeting.getShopId());
        amazonAdTargeting.setMarketplaceId(amazonAdBatchNetargeting.getMarketplaceId());
        amazonAdTargeting.setAdGroupId(amazonAdBatchNetargeting.getAmazonAdGroupId());
        amazonAdTargeting.setDxmGroupId(amazonAdBatchNetargeting.getId());
        amazonAdTargeting.setCampaignId(amazonAdBatchNetargeting.getAmazonCampaignId());
        amazonAdTargeting.setProfileId(amazonAdBatchNetargeting.getProfileId());
        amazonAdTargeting.setState(CpcStatusEnum.enabled.name());
        amazonAdTargeting.setType(Constants.TARGETING_TYPE_NEGATIVEASIN);
        amazonAdTargeting.setCreateId(amazonAdBatchNetargeting.getCreateId());
        amazonAdTargeting.setTargetingValue(amazonAdBatchNetargeting.getTargetingValue());
        if (AdNeTargetingTypeEnum.ASIN.name().equals(amazonAdBatchNetargeting.getType())) {
            amazonAdTargeting.setImgUrl(amazonAdBatchNetargeting.getImgUrl());
            amazonAdTargeting.setTitle(amazonAdTargeting.getTitle());
        }
        amazonAdTargeting.setExpression(amazonAdBatchNetargeting.getExpression());
        return amazonAdTargeting;
    }


    private String generateKey(AmazonAdTargeting amazonAdTargeting) {
        return amazonAdTargeting.getAdGroupId() + "#" + amazonAdTargeting.getTargetingValue();
    }

    private String generateKey(AmazonAdBatchNetargeting amazonAdBatchNetargeting) {
        return amazonAdBatchNetargeting.getAmazonAdGroupId() + "#" + amazonAdBatchNetargeting.getTargetingValue();
    }

    private String generateKey(CreateNegativeTargetEntityV3 createNegativeTargetEntityV3) {
        return createNegativeTargetEntityV3.getAdGroupId() + "#" + createNegativeTargetEntityV3.getExpression().get(0).getValue();
    }

    private String generateKey(ListNegativeTargetSuccessResultV3 listNegativeTargetSuccessResultV3) {
        return listNegativeTargetSuccessResultV3.getAdGroupId() + "#" + listNegativeTargetSuccessResultV3.getExpression().get(0).getValue();
    }


    /**
     * 创建重复的关键词获取关键词id，并且全部加入到创建成功列表
     */
    private Map<Integer, String> duplicateNeTargetingGetId(ShopAuth shop, String profileId, Map<Integer, CreateNegativeTargetEntityV3> duplicateValueErrorNetargetingMap, int offset) {
        //获取重复成功的index和关键词id
        Map<Integer, String> succMap = new HashMap<>();
        //关键词标识(广告组+关键词)与index的map
        Map<String, Integer> keywordTextIndexMap = new HashMap<>();
        //获取创建重复的活动和广告组
        List<String> campaignIds = new ArrayList<>();
        List<String> adGroupIds = new ArrayList<>();
        duplicateValueErrorNetargetingMap.forEach((k, v) -> {
            campaignIds.add(v.getCampaignId());
            adGroupIds.add(v.getAdGroupId());
            keywordTextIndexMap.put(generateKey(v), k);
        });
        NegativeTargetSpV3Client client = NegativeTargetSpV3Client.getInstance();
        // 刷新token次数，保证不会无限刷新
        int refreshedToken = 0;
        String nextToken = null;
        ListSpNegativeTargetV3Response response;
        Map<String, String> keywordTextIdMap;
        while (MapUtils.isNotEmpty(keywordTextIndexMap)) {
            response = client.getNegativeTargetList(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), null, campaignIds, null, null, adGroupIds, null, nextToken, true);
            int retry = 1;
            // 出现429，使用指数回避策略重试
            while (response != null && response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
                log.info("batch create neTargeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
                if (retry > AmazonAdUtils.retry) {
                    break;
                }
                response = client.getNegativeTargetList(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), null, campaignIds, null, null, adGroupIds, null, nextToken, true);
                retry++;
            }
            //刷新token
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401 && refreshedToken < 2) {
                shopAuthService.refreshCpcAuth(shop);
                refreshedToken++;
                continue;
            }
            if (response == null || response.getResult() == null || CollectionUtils.isEmpty(response.getResult().getNegativeTargetingClauses())) {
                break;
            }
            //将成功返回的keywordId存入成功列表中
            keywordTextIdMap = response.getResult().getNegativeTargetingClauses().stream().collect(Collectors.toMap(e -> generateKey(e), ListNegativeTargetSuccessResultV3::getTargetId));
            Iterator<Map.Entry<String, Integer>> iterator = keywordTextIndexMap.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, Integer> entry = iterator.next();
                if (keywordTextIdMap.containsKey(entry.getKey())) {
                    succMap.put(offset + entry.getValue(), keywordTextIdMap.get(entry.getKey()));
                    iterator.remove();
                }
            }
            //nextToken刷新
            if (StringUtils.isNotBlank(response.getResult().getNextToken())) {
                nextToken = response.getResult().getNextToken();
            } else {
                break;
            }
        }
        return succMap;
    }

    private ListSpNegativeTargetV3Response getAmazonNegativeTargeting(ShopAuth shop, String profileId, List<String> campaignIds, List<String> adGroupIds, String nextToken) {
        ListSpNegativeTargetV3Response listResponse = NegativeTargetSpV3Client.getInstance().getNegativeTargetList(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), null, campaignIds, null, null, adGroupIds, null, nextToken, true);
        int retry = 1;
        // 出现429，使用指数回避策略重试
        while (listResponse != null && listResponse.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
            log.info("SP batch create neTargeting rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
            if (retry > AmazonAdUtils.retry) {
                break;
            }
            listResponse = NegativeTargetSpV3Client.getInstance().getNegativeTargetList(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), null, campaignIds, null, null, adGroupIds, null, nextToken, true);
            retry++;
        }
        // token过期再重试一次
        if (listResponse != null && listResponse.getStatusCode() != null && listResponse.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            listResponse = NegativeTargetSpV3Client.getInstance().getNegativeTargetList(shop.getAdAccessToken(), profileId, shop.getMarketplaceId(), null, campaignIds, null, null, adGroupIds, null, nextToken, true);
        }
        return listResponse;
    }


    /**
     * 记录日志
     */
    private void collectFailLog(List<AmazonAdTargeting> amazonAdTargetings, String loginIp) {
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return;
        }
        Map<String, List<AmazonAdTargeting>> netargetingGroupMap = amazonAdTargetings.stream().collect(Collectors.groupingBy(AmazonAdTargeting::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdTargeting>> entry : netargetingGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdTargeting> neTargetingList = entry.getValue();
            for (AmazonAdTargeting amazonAdTargeting : neTargetingList) {
                msgSb.append("targetValue:").append(amazonAdTargeting.getTargetingValue()).append(",desc:").append(amazonAdTargeting.getError()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建否定投放失败";
        for (AmazonAdTargeting targeting : amazonAdTargetings) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getTargetsLog(null, targeting);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            keywordLog.setResultInfo(groupMsgMap.getOrDefault(targeting.getAdGroupId(), err));
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }

    /**
     * 记录日志
     */
    private void collectSuccLog(List<AmazonAdTargeting> amazonAdTargetings, String loginIp) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return;
        }
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        for (AmazonAdTargeting amazonAdTargeting : amazonAdTargetings) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getTargetsLog(null, amazonAdTargeting);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }

    /**
     * 记录日志
     */
    private void collectSuccNeLog(List<AmazonAdNeTargeting> amazonAdTargetings, String loginIp) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return;
        }
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        for (AmazonAdNeTargeting amazonAdTargeting : amazonAdTargetings) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getNeTargetsLog(null, amazonAdTargeting);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }


    /**
     * 记录日志
     */
    private void collectNeFailLog(List<AmazonAdNeTargeting> amazonAdTargetings, String loginIp) {
        List<AdManageOperationLog> keywordLogs = new ArrayList<>();
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return;
        }
        Map<String, List<AmazonAdNeTargeting>> netargetingGroupMap = amazonAdTargetings.stream().collect(Collectors.groupingBy(AmazonAdNeTargeting::getAdGroupId));
        Map<String, String> groupMsgMap = new HashMap<>();
        StringBuilder msgSb;
        for (Map.Entry<String, List<AmazonAdNeTargeting>> entry : netargetingGroupMap.entrySet()) {
            msgSb = new StringBuilder();
            List<AmazonAdNeTargeting> neTargetingList = entry.getValue();
            for (AmazonAdNeTargeting amazonAdTargeting : neTargetingList) {
                msgSb.append("targetValue:").append(amazonAdTargeting.getTargetingValue()).append(",desc:").append(amazonAdTargeting.getError()).append(";");
            }
            groupMsgMap.put(entry.getKey(), msgSb.toString());
        }

        String err = "创建否定投放失败";
        for (AmazonAdNeTargeting targeting : amazonAdTargetings) {
            AdManageOperationLog keywordLog = adManageOperationLogService.getNeTargetsLog(null, targeting);
            keywordLog.setIp(loginIp);
            keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            keywordLog.setResultInfo(groupMsgMap.getOrDefault(targeting.getAdGroupId(), err));
            keywordLogs.add(keywordLog);
        }
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
    }

}
