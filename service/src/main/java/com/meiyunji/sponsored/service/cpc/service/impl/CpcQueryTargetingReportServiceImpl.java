package com.meiyunji.sponsored.service.cpc.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.mode.MarketTimezoneAndCurrencyEnum;
import com.amazon.advertising.mode.targeting.ExpressionEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.aadras.types.enumeration.SpV3ExpressionEnum;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.AmazonAd;
import com.meiyunji.sponsored.common.exception.BizServiceException;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryTargetDataResponse;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.autoRule.dao.IAdvertiseAutoRuleStatusDao;
import com.meiyunji.sponsored.service.autoRule.po.AdvertiseAutoRuleStatus;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.constants.BrandMessageConstants;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdQueryStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.ICpcQueryTargetingReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.ResolvedExpressionParseHelper;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeTargetingDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdTargetingDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsCpcQueryTargetingReportDao;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeTargeting;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdTargeting;
import com.meiyunji.sponsored.service.enums.AutoRuleChildrenItemTypeEnum;
import com.meiyunji.sponsored.service.enums.AutoRuleItemTypeEnum;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.po.AsinInfo;
import com.meiyunji.sponsored.service.product.service.IAsinInfoService;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CpcQueryTargetingReportServiceImpl extends ReportService<CpcQueryTargetingReport> implements ICpcQueryTargetingReportService {
    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private ICpcQueryTargetingReportDao cpcQueryTargetingReportDao;
    @Autowired
    private ICpcQueryKeywordReportDao cpcQueryKeywordReportDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;

    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IOdsCpcQueryTargetingReportDao odsCpcQueryTargetingReportDao;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IAmazonAdTargetingShardingDao amazonAdTargetingShardingDao;
    @Autowired
    private IAmazonAdNeTargetingDao amazonAdNeTargetingDao;
    @Autowired
    private IOdsAmazonAdTargetingDao odsAmazonAdTargetingDao;
    @Autowired
    private IOdsAmazonAdNeTargetingDao odsAmazonAdNeTargetingDao;
    @Autowired
    private IAdvertiseAutoRuleStatusDao advertiseAutoRuleStatusDao;
    @Autowired
    private IOdsProductDao odsProductDao;
    @Autowired
    private IAsinInfoService asinInfoService;
    @Autowired
    private IWordTranslateService wordTranslateService;

    @Override
    public Page pageList(Integer puid, CpcQueryWordDto dto, Page page) {

        page = cpcQueryTargetingReportDao.pageList(puid, dto, page);
        List<CpcQueryTargetingReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
            BigDecimal sumRange = shopSaleDto==null ? BigDecimal.ZERO : shopSaleDto.getSumRange();

            poList.forEach(e -> {
                ReportVo vo = getVo(e,sumRange);
                vo.setQuery(e.getQuery());
                vo.setTargetingExpression(e.getTargetingExpression());
                vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());
                vo.setTargetId(e.getTargetId());
                String mainImage = e.getMainImage();
                if (StringUtils.isBlank(mainImage) && ("substitutes".equals(vo.getTargetingExpression()) || "complements".equals(vo.getTargetingExpression()) || vo.getTargetingExpression().contains("asin"))) {
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                } else if (StringUtils.isBlank(mainImage) && StringUtils.isNotBlank(vo.getQuery()) && Pattern.compile("^[bB]+[A-Za-z0-9]{9}+$").matcher(vo.getQuery()).matches()) { //搜索词符合 b 开头，后面跟9位字母和数字的组合
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                }
                if(mainImage != null && mainImage.endsWith("S60_.jpg")){
                    mainImage = mainImage.replace("S60_.jpg","S600_.jpg");
                }
                vo.setMainImage(mainImage);

                //检查该搜索词是否已经加入到否定
                if (Constants.TARGETING_EXPRESSION_CLOSE.equals(vo.getTargetingExpression())
                        || Constants.TARGETING_EXPRESSION_LOOSE.equals(vo.getTargetingExpression())) {
                    AmazonAdKeyword keyword = amazonAdKeywordDaoRoutingService.getByKeywordText(puid, dto.getShopId(), dto.getMarketplaceId(), e.getCampaignId(), e.getAdGroupId(), e.getQuery(), Constants.NEGATIVE);
                    if (keyword != null && !Constants.ARCHIVED.equalsIgnoreCase(keyword.getState())) {
                        vo.setNegaType(keyword.getMatchType());
                    }
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public ReportVo sumReport(Integer puid, CpcQueryWordDto dto, boolean type, BigDecimal sumRange) {
        CpcQueryTargetingReport report = cpcQueryTargetingReportDao.sumReport(puid, dto, type);
        if (sumRange == null) {
            ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
            sumRange = shopSaleDto == null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
        }
        return getVo(report, sumRange);
    }

    @Override
    public Page pageManageList(Integer puid, CpcQueryWordDto dto, Page page) {
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);

        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getStatus()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getStatus(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcQueryTargetingReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if (dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                    matchTypeList.add("asin");
                    matchTypeList.add("negativeAsin");
                } else {
                    if ("isTargetAsin".equalsIgnoreCase(matchType)) {
                        matchTypeList.add("asin");
                    }
                    if ("isNeTargetAsin".equalsIgnoreCase(matchType)) {
                        matchTypeList.add("negativeAsin");
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = Lists.newArrayList();
                    if (matchTypeList.contains("asin")) {
                        searchQueryTagParamList = amazonAdTargetingShardingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    }
                    List<SearchQueryTagParam> searchQueryTagParams = amazonAdNeTargetingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        return page;
                    }
                } else {
                    return page;
                }
            } else {
                return page;
            }
        }

        AdMetricDto adMetricDto;

        page = cpcQueryTargetingReportDao.pageKeywordAndTargetManageList(puid, dto, page);
        adMetricDto = cpcQueryTargetingReportDao.getSumAdMetricDto(puid, dto);
        List<CpcQueryTargetingReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());


            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getCampaignId).distinct().collect(Collectors.toList());
            List<AmazonAdCampaignAll> campaignList = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                campaignList = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
            }

            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(campaignList)) {
                campaignMap = campaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item->item,(a,b)->a));
            }

            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;


            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
            //查询广告组类型
            List<String> adGroupIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getAdGroupId).distinct().collect(Collectors.toList());
            //sp广告组
            if (CollectionUtils.isNotEmpty(adGroupIds)) {
                List<AmazonAdGroup> adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, adGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    spGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e,(e1,e2)->e1));
                }
            }

            List<AmazonAdTargeting> targetingList = null;
            Map<String, List<AmazonAdTargeting>> groupTargetMapList =  new HashMap<>();

            List<String> targetIds = new ArrayList<>();
            Map<String, AmazonAdTargeting> targetMap = new HashMap<>();

            getTargetingData(puid, dto.getShopId(), adGroupIds, targetingList, groupTargetMapList);

            Map<String, AmazonAdGroup> finalSpGroupMap = spGroupMap;
            poList.forEach(e -> {
                ReportVo vo = getVo(e,shopSalesByData);
                vo.setQuery(e.getQuery() != null ? e.getQuery().toUpperCase() : "");
                vo.setTargetingExpression(e.getTargetingExpression());
                vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                if (vo.getTargetingType().equals(Constants.MANUAL)) {
                    targetIds.add(e.getTargetId());
                }
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setTargetId(e.getTargetId());
                filterMetricData(e, vo, adMetricDto);

                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(e.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }

                //广告组类型
                if (MapUtils.isNotEmpty(finalSpGroupMap) && finalSpGroupMap.containsKey(e.getAdGroupId())) {
                    AmazonAdGroup amazonAdGroup = finalSpGroupMap.get(e.getAdGroupId());
                    vo.setAdGroupType(amazonAdGroup.getAdGroupType());
                    vo.setAdGroupName(amazonAdGroup.getName());
                }

                String mainImage = e.getMainImage();

                if (StringUtils.isBlank(mainImage) && ("substitutes".equals(Optional.ofNullable(vo.getTargetingExpression()).orElse(StringUtils.EMPTY)) || "complements".equals(Optional.ofNullable(vo.getTargetingExpression()).orElse(StringUtils.EMPTY)) || Optional.ofNullable(vo.getTargetingExpression()).orElse(StringUtils.EMPTY).contains("asin"))) {
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                } else if (StringUtils.isBlank(mainImage) && StringUtils.isNotBlank(vo.getQuery()) && Pattern.compile("^[bB]+[A-Za-z0-9]{9}+$").matcher(vo.getQuery()).matches()) { //搜索词符合 b 开头，后面跟9位字母和数字的组合
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                }
                if(mainImage != null && mainImage.endsWith("S60_.jpg")){
                    mainImage = mainImage.replace("S60_.jpg","S600_.jpg");
                }
                vo.setMainImage(mainImage);

                filterTagMessage(groupTargetMapList, e.getAdGroupId(), e.getQuery(), null, null, vo);
                list.add(vo);
            });
            if (CollectionUtils.isNotEmpty(targetIds)) {
                targetMap = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, dto.getShopId(), targetIds)
                        .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
            }

            Map<String, AmazonAdTargeting> finalTargetMap = targetMap;
            for (ReportVo vo : list) {
                if (finalTargetMap.containsKey(vo.getTargetId())) {
                    AmazonAdTargeting amazonAdTargeting = finalTargetMap.get(vo.getTargetId());
                    vo.setMatchType(amazonAdTargeting.getSelectType());
                }
            }
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page dorisPageExportList(Integer puid, CpcQueryWordDto dto, Page page) {
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);
        boolean isNull = setParam(puid, dto);
        if(isNull){
            return page;
        }
        page = odsCpcQueryTargetingReportDao.pageManageList(puid, dto, page);
        AdMetricDto adMetricDto = odsCpcQueryTargetingReportDao.getSumAdMetricDto(puid, dto);
        List<CpcQueryTargetingReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getCampaignId).distinct().collect(Collectors.toList());
            List<AmazonAdCampaignAll> campaignList = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                campaignList = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
            }

            Map<String, AmazonAdCampaignAll> campaignMap = null;
            if (CollectionUtils.isNotEmpty(campaignList)) {
                campaignMap = campaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item->item,(a,b)->a));
            }

            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;


            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
            //查询广告组类型
            List<String> adGroupIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getAdGroupId).distinct().collect(Collectors.toList());
            //sp广告组
            if (CollectionUtils.isNotEmpty(adGroupIds)) {
                List<AmazonAdGroup> adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, adGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    spGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e,(e1,e2)->e1));
                }
            }

            List<AmazonAdTargeting> targetingList = null;
            Map<String, List<AmazonAdTargeting>> groupTargetMapList =  new HashMap<>();

            getTargetingDorisData(puid, dto.getShopId(), adGroupIds, targetingList, groupTargetMapList);

            Map<String, AmazonAdGroup> finalSpGroupMap = spGroupMap;
            poList.forEach(e -> {
                ReportVo vo = getVo(e,shopSalesByData);
                vo.setQueryId(e.getQueryId());
                vo.setQuery(e.getQuery() != null ? e.getQuery().toUpperCase() : "");
                vo.setTargetingExpression(e.getTargetingExpression());
                vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                vo.setCampaignId(e.getCampaignId());
                vo.setCampaignName(e.getCampaignName());
                vo.setAdGroupId(e.getAdGroupId());
                vo.setAdGroupName(e.getAdGroupName());
                vo.setTargetId(e.getTargetId());
                filterMetricData(e, vo, adMetricDto);
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(e.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                }
                //广告组类型
                if (MapUtils.isNotEmpty(finalSpGroupMap) && finalSpGroupMap.containsKey(e.getAdGroupId())) {
                    AmazonAdGroup amazonAdGroup = finalSpGroupMap.get(e.getAdGroupId());
                    vo.setAdGroupType(amazonAdGroup.getAdGroupType());
                }
                filterTagMessage(groupTargetMapList, e.getAdGroupId(), e.getQuery(), null, null, vo);
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public Page detailList(Integer puid, CpcQueryWordDetailDto dto, Page page) {

        page = cpcQueryTargetingReportDao.detailList(puid, dto, page);

        List<CpcQueryTargetingReport> poList = page.getRows();
        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = getVoWithDateType(e, dto.getDateType(),null);
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }

    @Override
    public ReportVo sumDetailReport(Integer puid, CpcQueryWordDetailDto dto) {
        CpcQueryTargetingReport report = cpcQueryTargetingReportDao.sumDetailReport(puid, dto);

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal sumRange = shopSaleDto==null ? BigDecimal.ZERO : shopSaleDto.getSumRange();
        return getVo(report,sumRange);
    }

    @Override
    public List<ReportVo> detailListChart(int puid, CpcQueryWordDetailDto dto) {
        List<CpcQueryTargetingReport> poList = cpcQueryTargetingReportDao.detailListChart(puid, dto);

        if (poList != null && poList.size() > 0) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            poList.forEach(e -> {
                ReportVo vo = getVoWithDateType(e, dto.getDateType(),null);
                list.add(vo);
            });
            return list;
        }
        return null;
    }


    @Override
    public AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        //默认开启高级选项
        dto.setUseAdvanced(true);
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);

        boolean isNull = false;  // 查询的数据为空

        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcQueryTargetingReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if ("isTargetAsin".equalsIgnoreCase(matchType)) {
                    matchTypeList.add("asin");
                }
                if ("isNeTargetAsin".equalsIgnoreCase(matchType)) {
                    matchTypeList.add("negativeAsin");
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = Lists.newArrayList();
                    if (matchTypeList.contains("asin")) {
                        searchQueryTagParamList = amazonAdTargetingShardingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    }
                    List<SearchQueryTagParam> searchQueryTagParams = amazonAdNeTargetingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        isNull = true;
                    }
                } else {
                    isNull = true;
                }
            } else {
                isNull = true;
            }
        }


        AdMetricDto adMetricDto = new AdMetricDto();
        //分页数据
        if (!isNull) {

            page = cpcQueryTargetingReportDao.pageKeywordAndTargetManageList(puid, dto, page);
            adMetricDto = cpcQueryTargetingReportDao.getSumAdMetricDto(puid, dto);
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {  //总数大于十万
                int totalPage = Constants.TOTALSIZELIMIT / page.getPageSize();
                page.setTotalPage(totalPage);
                page.setTotalSize(Constants.TOTALSIZELIMIT);
            }
        }

        this.getPageReportVo(puid, dto, page, adMetricDto, false);

        //处理分页
        AllQueryTargetDataResponse.AdQueryTargetingHomeVo.Page.Builder pageBuilder = AllQueryTargetDataResponse.AdQueryTargetingHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));
        List<ReportVo> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            //环比数据
            Map<String, ReportVo> compareQueryMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(),
                        dto.getCompareStartDate(), dto.getCompareEndDate());
                BigDecimal shopSalesByDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSalesByDataCompare = BigDecimal.ZERO;
                } else {
                    shopSalesByDataCompare = shopSaleDtoCompare.getSumRange();
                }
                dto.setShopSales(shopSalesByData);
                dto.setShopSales(shopSalesByDataCompare);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());

                //通过asin精确查询本来
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).collect(Collectors.joining(StringUtil.SPECIAL_COMMA)));


                Page<ReportVo> pageCompare = cpcQueryTargetingReportDao.pageKeywordAndTargetManageList(puid, dto, page);

                this.getPageReportVo(puid, dto, pageCompare, adMetricDto, false);

                compareQueryMap = pageCompare.getRows().stream()
                        .collect(Collectors.toMap(k-> Optional.ofNullable(k.getTargetId()).orElse("").concat("#")
                                .concat(Optional.ofNullable(k.getQuery()).orElse("")), Function.identity(), (a, b) -> a));
            }


            Map<String, ReportVo> finalCompareQueryMap = compareQueryMap;
            List<ReportRpcVo> rpcVos = getReportRpcVos(rows, finalCompareQueryMap);
            pageBuilder.addAllRows(rpcVos);
        }

        ReportRpcVo reportRpcVo = ReportRpcVo.newBuilder().build();

        return AllQueryTargetDataResponse.AdQueryTargetingHomeVo.newBuilder()
                .setSum(reportRpcVo)
                .setPage(pageBuilder.build()).build();
    }

    @Override
    public AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto) {
        //默认开启高级选项
        dto.setUseAdvanced(true);
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        boolean isNull = false;  // 查询的数据为空

        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            List<SearchQueryTagParam> queryTagParams = cpcQueryTargetingReportDao.listAdGroupIdByQueryWordDto(dto);
            List<String> matchTypeList = Lists.newArrayList();
            for (String matchType : dto.getQueryWordTagTypeList()) {
                if ("isTargetAsin".equalsIgnoreCase(matchType)) {
                    matchTypeList.add("asin");
                }
                if ("isNeTargetAsin".equalsIgnoreCase(matchType)) {
                    matchTypeList.add("negativeAsin");
                }
            }
            if (CollectionUtils.isNotEmpty(queryTagParams)) {
                if (CollectionUtils.isNotEmpty(matchTypeList)) {
                    List<SearchQueryTagParam> searchQueryTagParamArrayList = Lists.newArrayList();
                    List<SearchQueryTagParam> searchQueryTagParamList = Lists.newArrayList();
                    if (matchTypeList.contains("asin")) {
                        searchQueryTagParamList = amazonAdTargetingShardingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    }
                    List<SearchQueryTagParam> searchQueryTagParams = amazonAdNeTargetingDao.getSearchQueryTag(puid, dto.getShopId(), matchTypeList, queryTagParams);
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamList)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParamList);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParams)) {
                        searchQueryTagParamArrayList.addAll(searchQueryTagParams);
                    }
                    if (CollectionUtils.isNotEmpty(searchQueryTagParamArrayList)) {
                        dto.setSearchQueryTagParamList(searchQueryTagParamArrayList);
                    } else {
                        isNull = true;
                    }
                } else {
                    isNull = true;
                }
            } else {
                isNull = true;
            }
        }

        //按条件查询所有数据
        List<AdHomePerformancedto> reportList;
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = new ArrayList<>();
        if (isNull) {  // 查询数据置为空
            reportList = new ArrayList<>();
        } else {

            reportList = cpcQueryTargetingReportDao.getKeywordAndTargetListAllTargetingReportByDate(puid, dto);
            if (dto.getIsCompare()) {
                CpcQueryWordDto dtoCompare = new CpcQueryWordDto();
                BeanUtils.copyProperties(dto, dtoCompare);
                dtoCompare.setStart(dto.getCompareStartDate());
                dtoCompare.setEnd(dto.getCompareEndDate());
                reportListCompare = cpcQueryTargetingReportDao.getKeywordAndTargetListAllTargetingReportByDate(puid, dtoCompare);
            }

        }

        AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo.Builder builder = AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo.newBuilder();

        int searchDataType = Optional.ofNullable(dto.getSearchDataType()).orElse(SearchDataTypeEnum.ALL.getCode());
        //是否查询汇总数据
        boolean searchAggregateData = SearchDataTypeEnum.containsAggregate(searchDataType);
        //是否查询图表数据
        boolean searchChartData = SearchDataTypeEnum.containsChart(searchDataType);

        List<String> targetIdList = reportList.stream().map(AdHomePerformancedto::getTargetId).filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<String> keywordIdList = reportList.stream().map(AdHomePerformancedto::getKeywordId).filter(StringUtils::isNotBlank).collect(Collectors.toList());

        //每日汇总数据
        if (searchChartData){
            reportDayList.addAll(cpcQueryTargetingReportDao.getReportByTargetIdList(puid, dto.getShopId(), dto.getStart(), dto.getEnd(),targetIdList, dto));
            reportDayList.addAll(cpcQueryKeywordReportDao.getReportByKeywordIdList(puid, dto.getShopId(), dto.getStart(), dto.getEnd(), keywordIdList, dto));
        }

        //环比值
        BigDecimal  shopSalesByDataCompare = BigDecimal.ZERO;
        if (dto.getIsCompare()) {
            ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate()
                    , dto.getCompareEndDate());
            if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                shopSalesByDataCompare = BigDecimal.ZERO;
            } else {
                shopSalesByDataCompare = shopSaleDtoCompare.getSumRange();
            }
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryTargetAggregateDataVo(reportList, reportListCompare, shopSalesByData, shopSalesByDataCompare, isVc);

        if (searchChartData){
            //处理chart数据
            List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, dto.getStart(), dto.getEnd(), reportDayList,shopSalesByData);
            List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList,shopSalesByData);
            List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList,shopSalesByData);
            builder.addAllDay(dayPerformanceVos)
                    .addAllMonth(monthPerformanceVos)
                    .addAllWeek(weekPerformanceVos)
                    .setAggregateDataVo(aggregateDataVo);
        }

        return builder
                .setAggregateDataVo(aggregateDataVo)
                .build();
    }


    @Override
    public List<QueryReportVo> getReportVoListByGroupIds(Integer puid, List<String> spGroupIds, QueryReportSearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        List<QueryReportVo> voList = new ArrayList<>();

        List<CpcQueryTargetingReport> reportList = cpcQueryTargetingReportDao.getReportVoListByGroupIds(puid, spGroupIds, searchVo);

        if (CollectionUtils.isEmpty(reportList)) {
            return voList;
        }

        String marketplaceId = reportList.get(0).getMarketplaceId();
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId).getCurrencyCode();

        List<String> campaignIds = reportList.stream().distinct().map(CpcQueryTargetingReport::getCampaignId).collect(Collectors.toList());
        List<String> groupIds = reportList.stream().distinct().map(CpcQueryTargetingReport::getAdGroupId).collect(Collectors.toList());

        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignDao.getByCampaignIds(puid, searchVo.getShopId(), marketplaceId, campaignIds);
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<AmazonAdGroup> groupList = amazonAdGroupDao.getAdGroupByIds(puid, searchVo.getShopId(), marketplaceId, groupIds);
        Map<String, AmazonAdGroup> groupMap = groupList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));

        List<AmazonAdTargeting> targetingList = null;
        Map<String, List<AmazonAdTargeting>> groupTargetMapList =  new HashMap<>();

        getTargetingData(puid, searchVo.getShopId(), groupIds, targetingList, groupTargetMapList);
        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = reportList.stream().map(CpcQueryTargetingReport::getQuery).filter(e -> !e.matches(Constants.ASIN_REGEX)).distinct()
                .map(e -> new WordTranslateQo(searchVo.getMarketplaceId(), e)).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);

        QueryReportVo vo;
        for (CpcQueryTargetingReport report : reportList) {
            vo = new QueryReportVo();
            vo.setShopId(report.getShopId());
            vo.setMarketplaceId(report.getMarketplaceId());
            if (StringUtils.isNotBlank(report.getQuery()) && report.getQuery().matches(Constants.ASIN_REGEX)) {
                vo.setQuery(report.getQuery().toUpperCase());
            } else {
                vo.setQuery(report.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(searchVo.getMarketplaceId(), report.getQuery())));
            }
            vo.setCampaignId(report.getCampaignId());
            vo.setCampaignName(report.getCampaignName());
            vo.setAdGroupId(report.getAdGroupId());
            vo.setAdGroupName(report.getAdGroupName());
            vo.setType("sp");
            vo.setTargetType("target");
            vo.setTargetId(report.getTargetId());
            vo.setCurrency(currencyCode);

            if (campaignMap.containsKey(report.getCampaignId())) {
                vo.setCampaignName(campaignMap.get(report.getCampaignId()).getName());
                vo.setCampaignTargetingType(campaignMap.get(report.getCampaignId()).getTargetingType());
            }
            if (groupMap.containsKey(report.getAdGroupId())) {
                vo.setAdGroupName(groupMap.get(report.getAdGroupId()).getName());
            }

            // 投放值
            if ("close-match".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("close_match");
            } else if ("loose-match".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("loose_match");
            } else if ("substitutes".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("substitutes");
            } else if ("complements".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("complements");
            } else {
                vo.setMatchType(report.getTargetingText());
            }

            if (StringUtils.isNotBlank(report.getTargetingText())) {
                if (report.getTargetingText().equals("substitutes") || report.getTargetingText().equals("close-match")
                        || report.getTargetingText().equals("loose-match") || report.getTargetingText().equals("complements")) {
                    vo.setTargetTitle(report.getTitle());
                    vo.setImgUrl(report.getMainImage());
                } else if (report.getTargetingText().contains("category=")) {
                    vo.setMatchType(report.getMatchType());
                } else {
                    if (vo.getMatchType().contains("asin-expanded")) {
                        vo.setMatchType("asinExpandedFrom");
                    } else{
                        vo.setMatchType("asinSameAs");
                    }
                    vo.setTargetTitle(report.getTitle());
                    vo.setImgUrl(report.getMainImage());
                }
            }

            filterTagMessage(groupTargetMapList, report.getAdGroupId(), report.getQuery(), null, vo, null);

            vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
            vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
            vo.setAdSale(report.getTotalSales() != null ?  String.valueOf(report.getTotalSales().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");

            // 点击率
            Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
            vo.setCtr(String.valueOf(ctr));
            //订单转化率
            Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
            vo.setCvr(String.valueOf(cvr));
            //acos
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setAcos("-");
            } else {
                Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
                vo.setAcos(String.valueOf(acos));
            }
            //acots
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAcots("0");
            } else {
                Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
                vo.setAcots(String.valueOf(acots));
            }
            //asots
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAsots("0");
            } else {
                Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
                vo.setAsots(String.valueOf(asots));
            }
            //adCostPerClick
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
                vo.setAdCostPerClick("0");
            } else {
                Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
                vo.setAdCostPerClick(String.valueOf(adCostPerClick));
            }
            //roas
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setRoas("0");
            } else {
                Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
                vo.setRoas(String.valueOf(roas));
            }

            voList.add(vo);
        }

        return voList;
    }

    @Override
    public List<TargetQueryReportVo> getListByTargetId(Integer puid, String targetId, TargetQuerySearchVo searchVo) {
        BigDecimal sumShopSale = searchVo.getSumShopSale();

        List<TargetQueryReportVo> voList = new ArrayList<>();

        List<CpcQueryTargetingReport> reportList = cpcQueryTargetingReportDao.getListByTargetId(puid, targetId, searchVo);

        if (CollectionUtils.isEmpty(reportList)) {
            return voList;
        }

        String marketplaceId = reportList.get(0).getMarketplaceId();
        //获取货币单位
        String currencyCode = MarketTimezoneAndCurrencyEnum.getByMarketplaceId(marketplaceId).getCurrencyCode();

        List<String> campaignIds = reportList.stream().distinct().map(CpcQueryTargetingReport::getCampaignId).collect(Collectors.toList());
        List<String> groupIds = reportList.stream().distinct().map(CpcQueryTargetingReport::getAdGroupId).collect(Collectors.toList());

        List<AmazonAdCampaignAll> campaignList = amazonAdCampaignDao.getByCampaignIds(puid, searchVo.getShopId(), marketplaceId, campaignIds);
        Map<String, AmazonAdCampaignAll> campaignMap = campaignList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        List<AmazonAdGroup> groupList = amazonAdGroupDao.getAdGroupByIds(puid, searchVo.getShopId(), marketplaceId, groupIds);
        Map<String, AmazonAdGroup> groupMap = groupList.stream().filter(Objects::nonNull).
                collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));

        List<AmazonAdTargeting> targetingList = null;
        Map<String, List<AmazonAdTargeting>> groupTargetMapList =  new HashMap<>();

        getTargetingData(puid, searchVo.getShopId(), groupIds, targetingList, groupTargetMapList);

        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = reportList.stream().map(CpcQueryTargetingReport::getQuery).filter(e -> !e.matches(Constants.ASIN_REGEX)).distinct()
                .map(e -> new WordTranslateQo(searchVo.getMarketplaceId(), e)).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, false);
        TargetQueryReportVo vo;
        List<String> asins = new ArrayList<>();
        List<String> targetIds = new ArrayList<>();
        Map<String, AmazonAdTargeting> targetMap = new HashMap<>();
        Set<Integer> shopIds = new HashSet<>();
        for (CpcQueryTargetingReport report : reportList) {
            vo = new TargetQueryReportVo();
            vo.setShopId(report.getShopId());
            shopIds.add(report.getShopId());
            vo.setMarketplaceId(report.getMarketplaceId());
            if (StringUtils.isNotBlank(report.getQuery()) && report.getQuery().matches(Constants.ASIN_REGEX)) {
                vo.setQuery(report.getQuery().toUpperCase());
            } else {
                vo.setQuery(report.getQuery());
                vo.setQueryCn(wordTranslateMap.get(wordTranslateService.getWordTranslateKey(searchVo.getMarketplaceId(), report.getQuery())));
            }
            vo.setCampaignId(report.getCampaignId());
            vo.setCampaignName(report.getCampaignName());
            vo.setAdGroupId(report.getAdGroupId());
            vo.setAdGroupName(report.getAdGroupName());
            vo.setType("sp");
            vo.setTargetType("target");
            vo.setTargetId(report.getTargetId());
            vo.setCurrency(currencyCode);

            if (campaignMap.containsKey(report.getCampaignId())) {
                vo.setCampaignName(campaignMap.get(report.getCampaignId()).getName());
                vo.setCampaignTargetingType(campaignMap.get(report.getCampaignId()).getTargetingType());
            }
            if (groupMap.containsKey(report.getAdGroupId())) {
                vo.setAdGroupName(groupMap.get(report.getAdGroupId()).getName());
            }

            if ("close-match".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("close_match");
            } else if ("loose-match".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("loose_match");
            } else if ("substitutes".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("substitutes");
            } else if ("complements".equalsIgnoreCase(report.getTargetingText())) {
                vo.setMatchType("complements");
            } else {
                vo.setMatchType(report.getTargetingText());
            }

            if (StringUtils.isNotBlank(report.getTargetingText())) {
                if (report.getTargetingText().equals("substitutes") || report.getTargetingText().equals("close-match")
                        || report.getTargetingText().equals("loose-match") || report.getTargetingText().equals("complements")) {
                    vo.setTargetTitle(report.getTitle());
                    vo.setImgUrl(report.getMainImage());
                } else if (report.getTargetingText().contains("category=")) {
                    vo.setMatchType("category");
                    vo.setTargetTitle(report.getTitle());
                    vo.setImgUrl(report.getMainImage());
                } else {
                    if (report.getTargetingText().startsWith("asin-expanded=")) {
                        vo.setMatchType("asinExpandedFrom");
                    } else{
                        vo.setMatchType("asinSameAs");
                    }
                    vo.setTargetTitle(report.getTitle());
                    vo.setImgUrl(report.getMainImage());
                }
            }

            filterTagMessage(groupTargetMapList, report.getAdGroupId(), report.getQuery(), vo, null, null);

            vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
            vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
            vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
            vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
            vo.setAdSale(report.getTotalSales() != null ?  String.valueOf(report.getTotalSales().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");

            // 点击率
            Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
            vo.setCtr(String.valueOf(ctr));
            //订单转化率
            Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
            vo.setCvr(String.valueOf(cvr));
            //acos
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setAcos("0");
            } else {
                Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
                vo.setAcos(String.valueOf(acos));
            }
            //acots
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAcots("0");
            } else {
                Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
                vo.setAcots(String.valueOf(acots));
            }
            //asots
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
                vo.setAsots("0");
            } else {
                Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
                vo.setAsots(String.valueOf(asots));
            }
            //adCostPerClick
            if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
                vo.setAdCostPerClick("0");
            } else {
                Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
                vo.setAdCostPerClick(String.valueOf(adCostPerClick));
            }
            //roas
            if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
                vo.setRoas("0");
            } else {
                Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
                vo.setRoas(String.valueOf(roas));
            }

            voList.add(vo);
        }

        return voList;
    }

    @Override
    public void getDetailsSumVo(QueryReportDetailsVo detailsVo, CpcCommPageVo vo, BigDecimal sumShopSale) {
        CpcQueryTargetingReport report = cpcQueryTargetingReportDao.getDetailsSumVo(detailsVo.getPuid(), detailsVo);

        if (report == null || report.getShopId() == null) {
            return;
        }
        // 组装报告数据
        setReportData(report, vo, sumShopSale);
    }

    @Override
    public void getQueryDetailsDay(AdReportDetailsVo adReportDetailsVo, QueryReportDetailsVo detailsVo) {
        List<CpcQueryTargetingReport> reportList = cpcQueryTargetingReportDao.getListQueryDetailsDay(detailsVo.getPuid(), detailsVo);

        if (CollectionUtils.isEmpty(reportList)) {
            return;
        }
        List<CpcCommPageVo> list = new ArrayList<>(reportList.size());
        CpcCommPageVo vo;
        for (CpcQueryTargetingReport report : reportList) {
            ShopSaleDto shopSaleDto = cpcShopDataService.getShopSaleData(detailsVo.getShopId(), report.getCountDate(), report.getCountDate());
            BigDecimal sumShopSale = BigDecimal.ZERO;
            if (shopSaleDto != null && shopSaleDto.getSumRange() != null) {  //店铺销售额
                sumShopSale = shopSaleDto.getSumRange();
            }
            vo = new CpcCommPageVo();
            // 组装报告数据
            setReportData(report, vo, sumShopSale);
            vo.setCountDate(report.getCountDate());
            list.add(vo);
        }
        adReportDetailsVo.setList(list);
    }

    @Override
    public AllQueryTargetDataResponse.AdQueryTargetingHomeVo getDorisAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);
        boolean isNull = setParam(puid, dto);  // 查询的数据为空
        AdMetricDto adMetricDto = new AdMetricDto();
        //分页数据
        if (!isNull) {
            page = odsCpcQueryTargetingReportDao.pageManageList(puid, dto, page);
            adMetricDto = odsCpcQueryTargetingReportDao.getSumAdMetricDto(puid, dto);
        }
        this.getPageReportVo(puid, dto, page, adMetricDto, true);
        // 填充广告策略
        fillAdStrategy(dto, page.getRows());
        //处理分页
        AllQueryTargetDataResponse.AdQueryTargetingHomeVo.Page.Builder pageBuilder = AllQueryTargetDataResponse.AdQueryTargetingHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(page.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(page.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(page.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(page.getTotalSize()));
        List<ReportVo> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            /*
             查询该页asin的产品名称、价格、星级和评分
             拿该页的asin去在线产品表里进行查询是否存在：
                ---存在则拿对应的产品名称、价格、星级、评分
                ---不存在则去查询调用爬虫程序去获取该asin的产品名称、价格、星级、评分的表
                    ---存  在：取对应的产品名称、价格、星级、评分
                    ---不存在：展示-
             */
            this.getRatingByAsinList(puid, dto.getShopId(), dto.getMarketplaceId(), rows);
            //环比数据
            Map<String, ReportVo> compareQueryMap = null;
            if (dto.getIsCompare()) {
                //对比时无须高级搜索条件
                dto.setUseAdvanced(false);
                ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(),
                        dto.getCompareStartDate(), dto.getCompareEndDate());
                BigDecimal shopSalesByDataCompare;
                if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                    shopSalesByDataCompare = BigDecimal.ZERO;
                } else {
                    shopSalesByDataCompare = shopSaleDtoCompare.getSumRange();
                }
                dto.setShopSales(shopSalesByData);
                dto.setShopSales(shopSalesByDataCompare);
                dto.setStart(dto.getCompareStartDate());
                dto.setEnd(dto.getCompareEndDate());
                //通过asin精确查询本来
                dto.setSearchType("exact");
                dto.setSearchField("query");
                dto.setSearchValue(rows.stream().map(ReportVo::getQuery).collect(Collectors.joining(StringUtil.SPECIAL_COMMA)));
                Page<ReportVo> pageCompare = odsCpcQueryTargetingReportDao.pageManageList(puid, dto, page);
                this.getPageReportVo(puid, dto, pageCompare, adMetricDto, true);
                compareQueryMap = pageCompare.getRows().stream()
                        .collect(Collectors.toMap(k-> Optional.ofNullable(k.getTargetId()).orElse("").concat("#")
                                .concat(Optional.ofNullable(k.getQuery()).orElse("")), Function.identity(), (a, b) -> a));
            }
            Map<String, ReportVo> finalCompareQueryMap = compareQueryMap;
            List<ReportRpcVo> rpcVos = getReportRpcVos(rows, finalCompareQueryMap);
            pageBuilder.addAllRows(rpcVos);
        }
        ReportRpcVo reportRpcVo = ReportRpcVo.newBuilder().build();
        return AllQueryTargetDataResponse.AdQueryTargetingHomeVo.newBuilder()
                .setSum(reportRpcVo)
                .setPage(pageBuilder.build()).build();
    }

    /**
     * 查询在线产品表和ASIN图片信息表的数据
     *
     * @param puid
     * @param shopId
     * @param marketplaceId
     * @param rows
     */
    private void getRatingByAsinList(Integer puid, Integer shopId, String marketplaceId, List<ReportVo> rows) {
        HashSet<String> asins = new HashSet<>();
        rows.forEach(i -> {
            asins.add(i.getQuery().toUpperCase());
        });
        //先去在线产品表里进行过滤，存在则取对应的商品名称、价格、星级、评分数
        List<ReportVo> ratingList = odsProductDao.getRatingByAsinList(puid, shopId, marketplaceId, asins);
        //取出不存在的去爬虫信息表里获取
        List<String> existAsin = ratingList.stream().map(ReportVo::getAsin).collect(Collectors.toList());
        Map<String, ReportVo> voMap = StreamUtil.toMap(ratingList, ReportVo::getAsin);
        //过滤出不存在在线产品表的数据
        HashSet<String> list = new HashSet<>();
        asins.forEach(i -> {
            if (!existAsin.contains(i)) {
                list.add(i);
            }
        });
        //获取爬虫信息表里的对应数据
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        List<AsinInfo> voList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            voList = asinInfoService.getRatingByAsinList(list, shopAuth.getMarketplaceId());
        }
        Map<String, AsinInfo> map = StreamUtil.toMap(voList, AsinInfo::getAsin);
        //设置商品名称、价格、星级、评分数
        rows.forEach(i -> {
            ReportVo vo = voMap.get(i.getQuery());
            AsinInfo vo1 = map.get(i.getQuery());
            if (vo != null) {
                i.setTitle(vo.getTitle());
                i.setPrice(vo.getPrice());
                i.setRating(vo.getRating());
                i.setRatingCount(vo.getRatingCount());
            }
            if (vo1 != null) {
                i.setTitle(vo1.getTitle());
                i.setPrice(vo1.getPrice());
                i.setRating(vo1.getStarRating());
                i.setRatingCount(vo1.getRatingCount());
                i.setMainImage(vo1.getImage());
            }
        });
    }

    private static List<ReportRpcVo> getReportRpcVos(List<ReportVo> rows, Map<String, ReportVo> finalCompareQueryMap) {
        List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
            ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
            if (StringUtils.isNotBlank(item.getCountDate())) {
                voBuilder.setCountDate(item.getCountDate());
            }

            if (item.getShopId() != null) {
                voBuilder.setShopId(Int32Value.of(item.getShopId()));
            }
            if(item.getMarketplaceId() != null){
                voBuilder.setMarketplaceId(item.getMarketplaceId());
            }
            voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setRoas(DoubleValue.of(Optional.ofNullable(item.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setAcots(DoubleValue.of(Optional.ofNullable(item.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setAsots(DoubleValue.of(Optional.ofNullable(item.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
            voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
            voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
            voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
            voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
            voBuilder.setCpa(DoubleValue.of(Optional.ofNullable(item.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setNaturalSales(DoubleValue.of(Optional.ofNullable(item.getNaturalSales()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setAdClickRatio(DoubleValue.of(Optional.ofNullable(item.getAdClickRatio()).orElse(0.0)));
            voBuilder.setAdConversionRate(DoubleValue.of(Optional.ofNullable(item.getAdConversionRate()).orElse(0.0)));
            voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
            voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
            voBuilder.setNaturalClicks(StringUtils.isNotBlank(item.getNaturalClicks()) ? item.getNaturalClicks() : "0");
            voBuilder.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
            voBuilder.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
            voBuilder.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
            voBuilder.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
            if (StringUtils.isNotBlank(item.getCampaignId())) {
                voBuilder.setCampaignId(item.getCampaignId());
            }
            if (StringUtils.isNotBlank(item.getAdGroupId())) {
                voBuilder.setAdGroupId(item.getAdGroupId());
            }
            if (StringUtils.isNotBlank(item.getAdGroupType())) {
                voBuilder.setAdGroupType(item.getAdGroupType());
            }
            if (StringUtils.isNotBlank(item.getAdGroupName())) {
                voBuilder.setAdGroupName(item.getAdGroupName());
            }
            if (StringUtils.isNotBlank(item.getCampaignStatus())) {
                voBuilder.setCampaignState(item.getCampaignStatus());
            }
            if (StringUtils.isNotBlank(item.getAdGroupState())) {
                voBuilder.setAdGroupState(item.getAdGroupState());
            }
            if(StringUtils.isNotBlank(item.getTargetState())) {
                voBuilder.setTargetState(item.getTargetState());
            }
            if (StringUtils.isNotBlank(item.getCampaignName())) {
                voBuilder.setCampaignName(item.getCampaignName());
            }
            if (StringUtils.isNotBlank(item.getKeywordText())) {
                voBuilder.setKeywordText(item.getKeywordText());
            }
            if (StringUtils.isNotBlank(item.getMatchType())) {
                voBuilder.setMatchType(item.getMatchType());
            }
            if (StringUtils.isNotBlank(item.getSku())) {
                voBuilder.setSku(item.getSku());
            }
            if (StringUtils.isNotBlank(item.getAsin())) {
                voBuilder.setAsin(item.getAsin());
            }

            if (StringUtils.isNotBlank(item.getQuery())) {
                voBuilder.setQuery(item.getQuery());
            }

            //新增投放类型列
            if (StringUtils.isNotBlank(item.getTargetTitle())) {
                voBuilder.setTargetTitle(item.getTargetTitle());
            }
            if (StringUtils.isNotBlank(item.getImgUrl())) {
                voBuilder.setImgUrl(item.getImgUrl());
            }
            if (StringUtils.isNotBlank(item.getCategory())) {
                voBuilder.setCategory(item.getCategory());
            }
            if (StringUtils.isNotBlank(item.getBrandName())) {
                voBuilder.setBrandName(item.getBrandName());
            }
            if (StringUtils.isNotBlank(item.getCommodityPriceRange())) {
                voBuilder.setCommodityPriceRange(item.getCommodityPriceRange());
            }
            if (StringUtils.isNotBlank(item.getCategoryRating())) {
                voBuilder.setCategoryRating(item.getCategoryRating());
            }
            if (StringUtils.isNotBlank(item.getDistribution())) {
                voBuilder.setDistribution(item.getDistribution());
            }
            if (StringUtils.isNotBlank(item.getLookBack())) {
                voBuilder.setLookBack(item.getLookBack());
            }

            //新增商品名称、价格、星级、评分
            voBuilder.setTitle(Optional.ofNullable(item.getTitle()).orElse(""));
            voBuilder.setPrice(Optional.ofNullable(item.getPrice())
                    .map(price -> {
                        BigDecimal bd = new BigDecimal(price);
                        return bd.setScale(2, RoundingMode.HALF_UP).toPlainString();
                    })
                    .orElse(""));

            voBuilder.setRating(Optional.ofNullable(item.getRating()).map(rating -> {
                try {
                    BigDecimal bd = new BigDecimal(rating);
                    return bd.setScale(1, RoundingMode.HALF_UP).toPlainString();
                } catch (Exception e) {
                    log.info("asin: {}, retaing: {} is not a number",item.getQuery(), rating);
                    return "";
                }
            }).orElse(""));
            voBuilder.setRatingCount(Optional.ofNullable(item.getRatingCount()).map(Object::toString).orElse(""));

            if (StringUtils.isNotBlank(item.getParentAsin())) {
                voBuilder.setParentAsin(item.getParentAsin());
            }
            if (StringUtils.isNotBlank(item.getMainImage())) {
                if(item.getMainImage().endsWith("S60_.jpg")){
                    item.setMainImage(item.getMainImage().replace("S60_.jpg","S600_.jpg"));
                }
                voBuilder.setMainImage(item.getMainImage());
            }
            if (StringUtils.isNotBlank(item.getNegaType())) {
                voBuilder.setNegaType(item.getNegaType());
            }
            if (StringUtils.isNotBlank(item.getTargetingType())) {
                voBuilder.setTargetingType(item.getTargetingType());
            }
            if (StringUtils.isNotBlank(item.getTargetId())) {
                voBuilder.setTargetId(item.getTargetId());
            }

            if (StringUtils.isNotBlank(item.getAdId())) {
                voBuilder.setAdId(item.getAdId());
            }
            if (StringUtils.isNotBlank(item.getTargetingText())) {
                voBuilder.setTargetingText(item.getTargetingText());
            }
            if (StringUtils.isNotBlank(item.getSpCampaignType())) {
                voBuilder.setSpCampaignType(item.getSpCampaignType());
            }
            if (StringUtils.isNotBlank(item.getSpGroupType())) {
                voBuilder.setSpGroupType(item.getSpGroupType());
            }

            if (StringUtils.isNotBlank(item.getSpTargetType())) {
                voBuilder.setSpTargetType(item.getSpTargetType());
            }

            if (StringUtils.isNotBlank(item.getTargetingExpression())) {
                voBuilder.setTargetingExpression(item.getTargetingExpression());
            }
            if (item.getIsTargetAsin() != null) {
                voBuilder.setIsTargetAsin(BoolValue.of(item.getIsTargetAsin()));
            }
            if (item.getIsNeTargetAsin() != null) {
                voBuilder.setIsNeTargetAsin(BoolValue.of(item.getIsNeTargetAsin()));
            }
            if (item.getDefaultBid() != null) {
                voBuilder.setDefaultBid(item.getDefaultBid());
            }
            if (item.getPortfolioId() != null) {
                voBuilder.setPortfolioId(item.getPortfolioId());
            }
            if (item.getPortfolioName() != null) {
                voBuilder.setPortfolioName(item.getPortfolioName());
            }
            if (item.getIsHidden() != null) {
                voBuilder.setIsHidden(item.getIsHidden());
            }
            if (StringUtils.isNotBlank(item.getQueryId())) {
                voBuilder.setQueryId(item.getQueryId());
            }
            voBuilder.setQueryType(AmazonAd.AdQueryTypeEnum.SP_TARGETING.getSearchField());
            // 广告策略标签
            if(CollectionUtils.isNotEmpty(item.getStrategyList())){
                voBuilder.addAllAdStrategys(buildStrategyList(item));
            }
                /**
                 * TODO 广告报告重构
                 * 本广告产品销售额
                 */
                voBuilder.setAdSales(DoubleValue.of(Optional.ofNullable(item.getAdSales().doubleValue()).orElse(0.0)));
                //本广告产品订单量
                voBuilder.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
                //本广告产品销量
                voBuilder.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
                //其他产品广告订单量
                voBuilder.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
                //其他产品广告销售额
                voBuilder.setAdOtherSales(DoubleValue.of(Optional.ofNullable(item.getAdOtherSales().doubleValue()).orElse(0.0)));
                //其他产品广告销量
                voBuilder.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
                //广告类型
                if (StringUtils.isNotBlank(item.getType())) {
                    voBuilder.setType(item.getType());
                }
                //true 都不为T00030
            fillDefaultBrandMessage(voBuilder, item, true);voBuilder.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
                //环比指标数据
                if (MapUtils.isNotEmpty(finalCompareQueryMap)) {
                    String mapKey = Optional.ofNullable(item.getTargetId()).orElse("").concat("#")
                            .concat(Optional.ofNullable(item.getQuery()).orElse(""));
                    if (finalCompareQueryMap.containsKey(mapKey)) {
                        ReportVo compareItem = finalCompareQueryMap.get(mapKey);
                        //环比值
                        voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareCpc(DoubleValue.of(Optional.ofNullable(compareItem.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareRoas(DoubleValue.of(Optional.ofNullable(compareItem.getRoas()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAcots(DoubleValue.of(Optional.ofNullable(compareItem.getAcots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAsots(DoubleValue.of(Optional.ofNullable(compareItem.getAsots()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareImpressions(Int32Value.of(Optional.ofNullable(compareItem.getImpressions()).orElse(0)));
                        voBuilder.setCompareClicks(Int32Value.of(Optional.ofNullable(compareItem.getClicks()).orElse(0)));
                        voBuilder.setCompareOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getOrderNum()).orElse(0)));
                        voBuilder.setCompareSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getSaleNum()).orElse(0)));
                        voBuilder.setCompareClickRate(DoubleValue.of(Optional.ofNullable(compareItem.getClickRate()).orElse(0.0)));
                        voBuilder.setCompareCpa(DoubleValue.of(Optional.ofNullable(compareItem.getCpa()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAdClickRatio(DoubleValue.of(Optional.ofNullable(compareItem.getAdClickRatio()).orElse(0.0)));
                        voBuilder.setCompareAdConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getAdConversionRate()).orElse(0.0)));
                        voBuilder.setCompareSales(DoubleValue.of(Optional.ofNullable(compareItem.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareSalesConversionRate(DoubleValue.of(Optional.ofNullable(compareItem.getSalesConversionRate()).orElse(0.0)));
                        voBuilder.setCompareCost(DoubleValue.of(Optional.ofNullable(compareItem.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAcos(DoubleValue.of(Optional.ofNullable(compareItem.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));
                        voBuilder.setCompareAdCostPercentage(StringUtils.isNotBlank(compareItem.getAdCostPercentage()) ? compareItem.getAdCostPercentage() : "0");
                        voBuilder.setCompareAdSalePercentage(StringUtils.isNotBlank(compareItem.getAdSalePercentage()) ? compareItem.getAdSalePercentage() : "0");
                        voBuilder.setCompareAdOrderNumPercentage(StringUtils.isNotBlank(compareItem.getAdOrderNumPercentage()) ? compareItem.getAdOrderNumPercentage() : "0");
                        voBuilder.setCompareOrderNumPercentage(StringUtils.isNotBlank(compareItem.getOrderNumPercentage()) ? compareItem.getOrderNumPercentage() : "0");
                        voBuilder.setCompareAdSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdSales().doubleValue()).orElse(0.0)));
                        //本广告产品订单量
                        voBuilder.setCompareAdSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSaleNum()).orElse(0)));
                        //本广告产品销量
                        voBuilder.setCompareAdSelfSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdSelfSaleNum()).orElse(0)));
                        //其他产品广告订单量
                        voBuilder.setCompareAdOtherOrderNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherOrderNum()).orElse(0)));
                        //其他产品广告销售额
                        voBuilder.setCompareAdOtherSales(DoubleValue.of(Optional.ofNullable(compareItem.getAdOtherSales().doubleValue()).orElse(0.0)));
                        //其他产品广告销量
                        voBuilder.setCompareAdOtherSaleNum(Int32Value.of(Optional.ofNullable(compareItem.getAdOtherSaleNum()).orElse(0)));
                        voBuilder.setCompareAdvertisingUnitPrice(Optional.ofNullable(compareItem.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));

                    //对比值
                    voBuilder.setCompareCpaRate(voBuilder.getCompareCpa().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getCpa().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpa().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareCpcRate(voBuilder.getCompareCpc().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getCpc().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCpc().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareRoasRate(voBuilder.getCompareRoas().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getRoas().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareRoas().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAcotsRate(voBuilder.getCompareAcots().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAcots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAsotsRate(voBuilder.getCompareAsots().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAsots().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAsots().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareImpressionsRate(voBuilder.getCompareImpressions().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getImpressions().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareImpressions().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareClicksRate(voBuilder.getCompareClicks().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getClicks().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClicks().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareOrderNumRate(voBuilder.getCompareOrderNum().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareSaleNumRate(voBuilder.getCompareSaleNum().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareClickRateRate(voBuilder.getCompareClickRate().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getClickRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareClickRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAdClickRatioRate(voBuilder.getCompareAdClickRatio().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdClickRatio().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdClickRatio().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAdConversionRateRate(voBuilder.getCompareAdConversionRate().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareSalesRate(voBuilder.getCompareSales().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSales().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareSalesConversionRateRate(voBuilder.getCompareSalesConversionRate().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getSalesConversionRate().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareSalesConversionRate().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareCostRate(voBuilder.getCompareCost().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getCost().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareCost().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareCost().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAcosRate(voBuilder.getCompareAcos().getValue() == 0.0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAcos().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAcos().getValue()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAdCostPercentageRate(new BigDecimal(voBuilder.getCompareAdCostPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                            (new BigDecimal(voBuilder.getAdCostPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdCostPercentage())))
                                    .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdCostPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAdSalePercentageRate(new BigDecimal(voBuilder.getAdSalePercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                            (new BigDecimal(voBuilder.getAdSalePercentage()).subtract(new BigDecimal(voBuilder.getCompareAdSalePercentage())))
                                    .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getAdSalePercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAdOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                            (new BigDecimal(voBuilder.getAdOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage())))
                                    .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareAdOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareOrderNumPercentageRate(new BigDecimal(voBuilder.getCompareOrderNumPercentage()).compareTo(BigDecimal.ZERO) == 0 ? "-" :
                            (new BigDecimal(voBuilder.getOrderNumPercentage()).subtract(new BigDecimal(voBuilder.getCompareOrderNumPercentage())))
                                    .multiply(new BigDecimal(100)).divide(new BigDecimal(voBuilder.getCompareOrderNumPercentage()), 2, RoundingMode.HALF_UP).toPlainString());

                    voBuilder.setCompareAdSalesRate(voBuilder.getCompareAdSales().getValue() == 0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    //本广告产品订单量
                    voBuilder.setCompareAdSaleNumRate(voBuilder.getCompareAdSaleNum().getValue() == 0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSaleNum().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    //本广告产品销量
                    voBuilder.setCompareAdSelfSaleNumRate(voBuilder.getCompareAdSelfSaleNum().getValue() == 0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdSelfSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdSelfSaleNum().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdSelfSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    //其他产品广告订单量
                    voBuilder.setCompareAdOtherOrderNumRate(voBuilder.getCompareAdOtherOrderNum().getValue() == 0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdOtherOrderNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherOrderNum().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherOrderNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    //其他产品广告销售额
                    voBuilder.setCompareAdOtherSalesRate(voBuilder.getCompareAdOtherSales().getValue() == 0 ? "-" :
                            (BigDecimal.valueOf(voBuilder.getAdOtherSales().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSales().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    //其他产品广告销量
                    voBuilder.setCompareAdOtherSaleNumRate(voBuilder.getCompareAdOtherSaleNum().getValue() == 0 ? "0" :
                            (BigDecimal.valueOf(voBuilder.getAdOtherSaleNum().getValue()).subtract(BigDecimal.valueOf(voBuilder.getCompareAdOtherSaleNum().getValue())))
                                    .multiply(new BigDecimal(100)).divide(BigDecimal.valueOf(voBuilder.getCompareAdOtherSaleNum().getValue()), 2, RoundingMode.HALF_UP).toPlainString());
                    voBuilder.setCompareAdvertisingUnitPriceRate(MathUtil.isNullOrZero(voBuilder.getCompareAdvertisingUnitPrice()) ? "-" :
                            String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(voBuilder.getAdvertisingUnitPrice(), voBuilder.getCompareAdvertisingUnitPrice(), 4), 100)));
                }
            }
            return voBuilder.build();

        }).collect(Collectors.toList());
        return rpcVos;
    }

    private static List<com.meiyunji.sponsored.rpc.vo.AdStrategy> buildStrategyList(ReportVo item) {
        List<com.meiyunji.sponsored.rpc.vo.AdStrategy> strategyList = new ArrayList<>();
        for (AdStrategyVo strategyVo : item.getStrategyList()) {
            com.meiyunji.sponsored.rpc.vo.AdStrategy.Builder strategyBuilder = com.meiyunji.sponsored.rpc.vo.AdStrategy.newBuilder();
            strategyBuilder.setAdStrategyType(strategyVo.getAdStrategyType());
            strategyBuilder.setStatus(strategyVo.getStatus());
            strategyList.add(strategyBuilder.build());
        }
        return strategyList;
    }

    /**
     * 填充广告策略
     */
    public void fillAdStrategy(CpcQueryWordDto dto, List<ReportVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> queryIds = rows.stream().map(ReportVo::getQueryId).distinct().collect(Collectors.toList());
        List<String> groupIds = rows.stream().map(ReportVo::getAdGroupId).distinct().collect(Collectors.toList());
        // 根据投放id集合获取自动化规则受控对象
        List<AdvertiseAutoRuleStatus> autoRuleStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(), queryIds, AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString());
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleMap = StreamUtil.groupingBy(autoRuleStatuses, AdvertiseAutoRuleStatus::getItemId);
        List<AdvertiseAutoRuleStatus> autoRuleGroupStatuses = advertiseAutoRuleStatusDao.listByItemIdMutiple(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(), groupIds, AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString());
        Map<String, List<AdvertiseAutoRuleStatus>> autoRuleGroupMap = StreamUtil.groupingBy(autoRuleGroupStatuses, AdvertiseAutoRuleStatus::getItemId);
        for (ReportVo vo : rows) {
            // 自动化规则标签
            List<AdStrategyVo> adstrategyList = new ArrayList<>();
            // key:标签 value:状态集合
            Map<String,List<String>> strategyMap = new HashMap<>();
            if(autoRuleMap.containsKey(vo.getQueryId())){
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleMap.get(vo.getQueryId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdQueryStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtil.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
            }
            if(autoRuleGroupMap.containsKey(vo.getAdGroupId())){
                // key 标签策略 status 状态集合  一个标签存在多种状态 如果存在已开启则为开启状态 否则为暂停状态
                Map<Integer, List<AdvertiseAutoRuleStatus>> autoRuleOperationMap = StreamUtil.groupingBy(autoRuleGroupMap.get(vo.getAdGroupId()), AdvertiseAutoRuleStatus::getOperationType);
                for (Integer operationType : autoRuleOperationMap.keySet()) {
                    List<String> statusList = StreamUtil.toListDistinct(autoRuleOperationMap.get(operationType), AdvertiseAutoRuleStatus::getStatus);
                    String strategy = AdQueryStrategyTypeEnum.getStrategyMap().get(operationType);
                    if(StringUtil.isNotEmpty(strategy)){
                        List<String> statusAllList = strategyMap.getOrDefault(strategy, new ArrayList<>());
                        statusAllList.addAll(statusList);
                        strategyMap.put(strategy,statusAllList);
                    }
                }
            }
            // 自动化规则标签
            for (String strategy : strategyMap.keySet()) {
                int status = 0;
                List<String> statusList = strategyMap.get(strategy);
                if(statusList.contains("ENABLED")){
                    status = 1;
                }
                AdStrategyVo strategyVo = new AdStrategyVo();
                strategyVo.setAdStrategyType(strategy);
                strategyVo.setStatus(status);
                adstrategyList.add(strategyVo);
            }
            vo.setStrategyList(adstrategyList);
        }
    }

    private boolean setParam(Integer puid, CpcQueryWordDto dto) {
        //默认开启高级选项
        dto.setUseAdvanced(true);
        if (StringUtils.isNotBlank(dto.getPortfolioId()) || StringUtils.isNotBlank(dto.getState()) || StringUtils.isNotBlank(dto.getServingStatus())) {  //广告组合id不为空
            List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioIdOrStatusOrServingStatus(puid, dto.getShopId(), dto.getPortfolioId(), dto.getState(), dto.getServingStatus(), CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                dto.setCampaignIdList(campaignIds);
            } else {
                return true;
            }
        }
        //若没有选ASIN标签，则直接返回空列表
        if (CollectionUtils.isNotEmpty(dto.getQueryWordTagTypeList())) {
            if (!dto.getQueryWordTagTypeList().contains(CpcQueryWordDto.QueryWordTagTypeEnum.TARGET_ASIN.getValue())
                    && !dto.getQueryWordTagTypeList().contains(CpcQueryWordDto.QueryWordTagTypeEnum.NE_TARGET_ASIN.getValue())
                    && !dto.getQueryWordTagTypeList().contains(Constants.QUERY_NOT_TARGET)) {
                return true;
            }
        }
        // 自动化规则筛选投放id集合、组id集合
        List<Integer> operationTypeList = AdQueryStrategyTypeEnum.operationTypeList(dto.getAdStrategyTypeList());
        if (CollectionUtils.isNotEmpty(operationTypeList)) {
            String queryType = AmazonAd.AdQueryTypeEnum.SP_TARGETING.getSearchField();
            List<String> targetIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(dto.getPuid(), cn.hutool.core.collection.CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, dto.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY.toString(), null, queryType);
            dto.setAutoRuleIds(targetIdList);
            List<String> groupIdList = advertiseAutoRuleStatusDao.listItemIdByAdManage(dto.getPuid(), CollectionUtil.newArrayList(dto.getShopId()), AutoRuleItemTypeEnum.GROUP_SEARCH_QUERY.getName(),
                    operationTypeList, dto.getType(), AutoRuleChildrenItemTypeEnum.CHILDREN_SEARCH_QUERY_GROUP.toString(), null, null);
            dto.setAutoRuleGroupIds(groupIdList);
            if (CollectionUtils.isEmpty(targetIdList) && CollectionUtils.isEmpty(groupIdList) &&
                    !dto.getAdStrategyTypeList().contains(AdQueryStrategyTypeEnum.NONE.getCode())) {
                // 只存在自动化规则筛选没数据时返回
                return true;
            }
        }
        return false;
    }

    /**
     * 填充 品牌细节 默认信息
     */
    private static void fillDefaultBrandMessage(ReportRpcVo.Builder voBuilder, ReportVo item, boolean fillDefault) {
        if (TargetTypeEnum.category.name().equalsIgnoreCase(item.getMatchType())) {
            String brandName = org.apache.commons.lang3.StringUtils.isNotBlank(item.getBrandName()) ? item.getBrandName() : fillDefault ? BrandMessageConstants.DEFAULT_BRAND_NAME : null;
            String commodityPriceRange = org.apache.commons.lang3.StringUtils.isNotBlank(item.getCommodityPriceRange()) ? item.getCommodityPriceRange() : fillDefault ? BrandMessageConstants.DEFAULT_COMMODITY_PRICE_RANGE : null;
            String rating = org.apache.commons.lang3.StringUtils.isNotBlank(item.getCategoryRating()) ? item.getCategoryRating() : fillDefault ? BrandMessageConstants.DEFAULT_RATING : null;
            String distribution = org.apache.commons.lang3.StringUtils.isNotBlank(item.getDistribution()) ? item.getDistribution() : fillDefault ? BrandMessageConstants.DEFAULT_DISTRIBUTION : null;

            if (org.apache.commons.lang3.StringUtils.isNotBlank(brandName)) {
                voBuilder.setBrandName(brandName);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(commodityPriceRange)) {
                voBuilder.setCommodityPriceRange(commodityPriceRange);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(rating)) {
                voBuilder.setCategoryRating(rating);
            }
            if (org.apache.commons.lang3.StringUtils.isNotBlank(distribution)) {
                voBuilder.setDistribution(distribution);
            }
        }
        if (org.apache.commons.lang3.StringUtils.isNotBlank(item.getLookBack())) {
            voBuilder.setLookBack(item.getLookBack());
        }
    }

    @Override
    public AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getDorisAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto) {
        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSalesByData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {  //店铺销售额
            shopSalesByData = BigDecimal.ZERO;
        } else {
            shopSalesByData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSalesByData);
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        boolean isVc = ShopTypeEnum.VC.getCode().equals(shopAuth.getType());
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();
        boolean isNull = setParam(puid, dto);  // 查询的数据为空
        //按条件查询所有数据
        List<AdHomePerformancedto> reportDayList;
        List<AdHomePerformancedto> reportListCompare = new ArrayList<>();
        if (isNull) {
            reportDayList = new ArrayList<>();
        } else {
            reportDayList = odsCpcQueryTargetingReportDao.getQueryAsinReportAggregate(puid, dto, true, false);
            if (dto.getIsCompare()) {
                reportListCompare = odsCpcQueryTargetingReportDao.getQueryAsinReportAggregate(puid, dto, false, true);
            }
        }
        //环比值
        BigDecimal  shopSalesByDataCompare = BigDecimal.ZERO;
        if (dto.getIsCompare()) {
            ShopSaleDto shopSaleDtoCompare = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getCompareStartDate()
                    , dto.getCompareEndDate());
            if (shopSaleDtoCompare == null || shopSaleDtoCompare.getSumRange() == null) {  //店铺销售额
                shopSalesByDataCompare = BigDecimal.ZERO;
            } else {
                shopSalesByDataCompare = shopSaleDtoCompare.getSumRange();
            }
        }
        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryTargetAggregateDataVo(reportDayList, reportListCompare, shopSalesByData, shopSalesByDataCompare, isVc);
        //处理chart数据
        List<AdHomeChartRpcVo> weekPerformanceVos = adChartDataProcess.getWeekPerformanceVos(currency, dto.getStart(), dto.getEnd(), reportDayList,shopSalesByData);
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList,shopSalesByData);
        List<AdHomeChartRpcVo> monthPerformanceVos = adChartDataProcess.getMonthPerformanceVos(currency, reportDayList,shopSalesByData);

        return AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo.newBuilder()
                .addAllDay(dayPerformanceVos)
                .addAllMonth(monthPerformanceVos)
                .addAllWeek(weekPerformanceVos)
                .setAggregateDataVo(aggregateDataVo)
                .build();
    }

    private void setReportData(CpcQueryTargetingReport report, CpcCommPageVo vo, BigDecimal sumShopSale) {
        vo.setImpressions(report.getImpressions() != null ? report.getImpressions() : 0);
        vo.setClicks(report.getClicks() != null ? report.getClicks() : 0);
        vo.setAdCost(report.getCost() != null ? String.valueOf(report.getCost().setScale(2, BigDecimal.ROUND_HALF_UP)) : "0");
        vo.setAdOrderNum(report.getSaleNum() != null ? report.getSaleNum() : 0);
        vo.setAdSale(report.getTotalSales() != null ?  String.valueOf(report.getTotalSales().setScale(2,BigDecimal.ROUND_HALF_UP)) : "0");

        // 点击率
        Double ctr = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) *100,Double.valueOf(vo.getImpressions()),2);
        vo.setCtr(String.valueOf(ctr));
        //订单转化率
        Double cvr = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getAdOrderNum()) *100,Double.valueOf(vo.getClicks()),2);
        vo.setCvr(String.valueOf(cvr));
        //acos
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setAcos("0");
        } else {
            Double acos = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), Double.valueOf(vo.getAdSale()) , 2);
            vo.setAcos(String.valueOf(acos));
        }
        //acots
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAcots("0");
        } else {
            Double acots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdCost()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAcots(String.valueOf(acots));
        }
        //asots
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || sumShopSale.compareTo(BigDecimal.ZERO) == 0) {
            vo.setAsots("0");
        } else {
            Double asots = DoubleUtil.divide(DoubleUtil.mul(Double.valueOf(vo.getAdSale()), 100d), sumShopSale.doubleValue() , 2);
            vo.setAsots(String.valueOf(asots));
        }
        //adCostPerClick
        if (Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0 || vo.getClicks() == 0) {
            vo.setAdCostPerClick("0");
        } else {
            Double adCostPerClick = DoubleUtil.divide(Double.valueOf(vo.getAdCost()), Double.valueOf(vo.getClicks()), 2);
            vo.setAdCostPerClick(String.valueOf(adCostPerClick));
        }
        //roas
        if (Double.valueOf(vo.getAdSale()).compareTo(0d) <= 0 || Double.valueOf(vo.getAdCost()).compareTo(0d) <= 0) {
            vo.setRoas("0");
        } else {
            Double roas = DoubleUtil.divide(Double.valueOf(vo.getAdSale()), Double.valueOf(vo.getAdCost()), 2);
            vo.setRoas(String.valueOf(roas));
        }
    }

    private AdHomeAggregateDataRpcVo getQueryTargetAggregateDataVo(List<AdHomePerformancedto> rows, List<AdHomePerformancedto> rowsCompare, BigDecimal shopSales, BigDecimal shopSalesCompare, boolean isVc) {

        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 2, BigDecimal.ROUND_HALF_UP);
        BigDecimal cpa = sumAdcost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(sumAdOrderNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(sumAdcost, BigDecimal.valueOf(sumAdOrderNum));
        //本广告产品订单量
        int sumAdSaleNum = rows.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //本广告产品销售额
        BigDecimal sumAdSales = rows.stream().filter(item -> item != null && item.getAdSales() != null).map(e -> e.getAdSales()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNum = sumAdOrderNum - sumAdSaleNum;
        //其他产品广告销售额
        BigDecimal sumAdOtherSales = sumAdSale.subtract(sumAdSales);
        //本广告产品销量
        int sumOrderNum = rows.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNum = sumSalesNum - sumOrderNum;
        String sumAdCostPercentage = sumAdcost.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdSalePercentage = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? "0" : "100.0000";
        String sumAdOrderNumPercentage = sumAdOrderNum == 0 ? "0" : "100.0000";
        String sumOrderNumPercentage = sumSalesNum == 0 ? "0" : "100.0000";
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPrice = sumAdOrderNum == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSale, sumAdOrderNum, 2);

        //环比数据
        //点击量
        int sumClicksCompare = rowsCompare.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressionsCompare = rowsCompare.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSaleCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSale() != null).map(e ->e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcostCompare = rowsCompare.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcosCompare = sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(sumAdSaleCompare, 2, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClickCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvrCompare = sumClicksCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNumCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicksCompare), 2, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtrCompare = sumImpressionsCompare == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicksCompare).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressionsCompare), 2, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roasCompare = sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSaleCompare.divide(sumAdcostCompare, 2, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdcostCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asotsCompare = shopSalesCompare.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSaleCompare.multiply(new BigDecimal("100")).divide(shopSalesCompare, 2, BigDecimal.ROUND_HALF_UP);
        // 广告笔单价,广告销售额/广告订单量*100%
        BigDecimal sumAdvertisingUnitPriceCompare  = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : MathUtil.divideOfObject(sumAdSaleCompare , sumAdOrderNumCompare , 2);
        //每笔订单花费
        BigDecimal sumCpaCompare = sumAdOrderNumCompare == 0 ? BigDecimal.ZERO : sumAdcostCompare.divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP);
        //广告销量
        int sumSalesNumCompare = rowsCompare.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();
        //本广告产品订单量
        int sumAdSaleNumCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSaleNum() != null).mapToInt(AdHomePerformancedto::getAdSaleNum).sum();
        //其他产品广告订单量
        int sumAdOtherOrderNumCompare = sumAdOrderNumCompare - sumAdSaleNumCompare;
        //本广告产品销售额
        BigDecimal sumAdSalesCompare = rowsCompare.stream().filter(item -> item != null && item.getAdSales() != null).map(AdHomePerformancedto::getAdSales).reduce(BigDecimal.ZERO, BigDecimal::add);
        //其他产品广告销售额
        BigDecimal sumAdOtherSalesCompare = sumAdSaleCompare.subtract(sumAdSalesCompare);
        //本广告产品销量
        int sumOrderNumCompare = rowsCompare.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AdHomePerformancedto::getOrderNum).sum();
        //其他产品广告销量
        int sumAdOtherSaleNumCompare = sumSalesNumCompare - sumOrderNumCompare;

        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toString())
                .setAcots(acots.stripTrailingZeros().toString())
                .setAsots(asots.stripTrailingZeros().toString())
                .setRoas(roas.stripTrailingZeros().toString())
                .setAdCost(sumAdcost.stripTrailingZeros().toString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toString())
                .setCtr(sumCtr.stripTrailingZeros().toString())
                .setAdSale(sumAdSale.stripTrailingZeros().toString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setCpa(cpa.stripTrailingZeros().toString())
                .setAdSaleNum(Int32Value.of(sumAdSaleNum))
                .setAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNum))
                .setAdSales(sumAdSales.stripTrailingZeros().toString())
                .setAdOtherSales(sumAdOtherSales.stripTrailingZeros().toString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .setAdSelfSaleNum(Int32Value.of(sumOrderNum))
                .setAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNum))
                .setAdCostPercentage(sumAdCostPercentage)
                .setAdSalePercentage(sumAdSalePercentage)
                .setAdOrderNumPercentage(sumAdOrderNumPercentage)
                .setOrderNumPercentage(sumOrderNumPercentage)
                .setAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPrice))

                //环比数据
                .setCompareAcos(sumAcosCompare.toPlainString())
                .setCompareRoas(roasCompare.toPlainString())
                .setCompareAdCost(sumAdcostCompare.toPlainString())
                .setCompareAdCostPerClick(sumAdCostPerClickCompare.toPlainString())
                .setCompareAdOrderNum(Int32Value.of(sumAdOrderNumCompare))
                .setCompareCvr(sumCvrCompare.toPlainString())
                .setCompareCtr(sumCtrCompare.toPlainString())
                .setCompareAdSale(sumAdSaleCompare.toPlainString())
                .setCompareClicks(Int32Value.of(sumClicksCompare))
                .setCompareImpressions(Int32Value.of(sumImpressionsCompare))
                .setCompareAcots(acotsCompare.toPlainString())
                .setCompareAsots(asotsCompare.toPlainString())
                .setCompareAdvertisingUnitPrice(String.valueOf(sumAdvertisingUnitPriceCompare))
                .setCompareCpa(sumCpaCompare.toPlainString())
                .setCompareOrderNum(Int32Value.of(sumSalesNumCompare))
                .setCompareAdSaleNum(Int32Value.of(sumAdSaleNumCompare))
                .setCompareAdOtherOrderNum(Int32Value.of(sumAdOtherOrderNumCompare))
                .setCompareAdSales(sumAdSalesCompare.toPlainString())
                .setCompareAdOtherSales(sumAdOtherSalesCompare.toPlainString())
                .setCompareAdSelfSaleNum(Int32Value.of(sumOrderNumCompare))
                .setCompareAdOtherSaleNum(Int32Value.of(sumAdOtherSaleNumCompare))

                //环比值
                .setCompareAcosRate(sumAcosCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAcos.subtract(sumAcosCompare))
                        .multiply(new BigDecimal(100)).divide(sumAcosCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareRoasRate(roasCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (roas.subtract(roasCompare))
                        .multiply(new BigDecimal(100)).divide(roasCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostRate(sumAdcostCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdcost.subtract(sumAdcostCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdcostCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdCostPerClickRate(sumAdCostPerClickCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdCostPerClick.subtract(sumAdCostPerClickCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdCostPerClickCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdOrderNumRate(sumAdOrderNumCompare == 0 ? "-" : new BigDecimal(sumAdOrderNum - sumAdOrderNumCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumAdOrderNumCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCvrRate(sumCvrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCVr.subtract(sumCvrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCvrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareCtrRate(sumCtrCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumCtr.subtract(sumCtrCompare))
                        .multiply(new BigDecimal(100)).divide(sumCtrCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAdSaleRate(sumAdSaleCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSale.subtract(sumAdSaleCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSaleCompare, 2, RoundingMode.HALF_UP).toPlainString())


                .setCompareClicksRate(sumClicksCompare == 0 ? "-" : new BigDecimal(sumClicks - sumClicksCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumClicksCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareImpressionsRate(sumImpressionsCompare == 0 ? "-" : new BigDecimal(sumImpressions - sumImpressionsCompare)
                        .multiply(new BigDecimal(100)).divide(new BigDecimal(sumImpressionsCompare), 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAcotsRate(acotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (acots.subtract(acotsCompare))
                        .multiply(new BigDecimal(100)).divide(acotsCompare, 2, RoundingMode.HALF_UP).toPlainString())

                .setCompareAsotsRate(asotsCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (asots.subtract(asotsCompare))
                        .multiply(new BigDecimal(100)).divide(asotsCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdvertisingUnitPriceRate(sumAdvertisingUnitPriceCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdvertisingUnitPrice, sumAdvertisingUnitPriceCompare, 4), 100)))

                .setCompareCpaRate(sumCpaCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (cpa.subtract(sumCpaCompare))
                        .multiply(new BigDecimal(100)).divide(sumCpaCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareOrderNumRate(sumSalesNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumSalesNum, sumSalesNumCompare, 4), 100)))
                .setCompareAdSaleNumRate(sumAdSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdSaleNum, sumAdSaleNumCompare, 4), 100)))
                .setCompareAdOtherOrderNumRate(sumAdOtherOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherOrderNum, sumAdOtherOrderNumCompare, 4), 100)))
                .setCompareAdSalesRate(sumAdSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdSales.subtract(sumAdSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdOtherSalesRate(sumAdOtherSalesCompare.compareTo(BigDecimal.ZERO) == 0 ? "-" : (sumAdOtherSales.subtract(sumAdOtherSalesCompare))
                        .multiply(new BigDecimal(100)).divide(sumAdOtherSalesCompare, 2, RoundingMode.HALF_UP).toPlainString())
                .setCompareAdSelfSaleNumRate(sumOrderNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumOrderNum, sumOrderNumCompare, 4), 100)))
                .setCompareAdOtherSaleNumRate(sumAdOtherSaleNumCompare == 0 ? "-" :
                        String.valueOf(MathUtil.multiplyOfObject(MathUtil.growthRateOfObject(sumAdOtherSaleNum, sumAdOtherSaleNumCompare, 4), 100)));
        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setCompareAcots("-");
            builder.setAsots("-");
            builder.setCompareAsotsRate("-");
            builder.setCompareAsots("-");
        }
        return builder.buildPartial();
    }




    private AdHomeAggregateDataRpcVo getAdHomeAggregateDataVo(ReportVo reportVo,BigDecimal shopSales, boolean isVc) {
        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (reportVo == null) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setRoas("0")
                    .setAsots("0")
                    .setAsots("0")
                    .setAdCost("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .build();
        }
        Integer clicks = Optional.ofNullable(reportVo.getClicks()).orElse(0);
        Integer impressions = Optional.ofNullable(reportVo.getImpressions()).orElse(0);
        BigDecimal acos = Optional.ofNullable(reportVo.getAcos()).orElse(BigDecimal.ZERO);
        BigDecimal adCost = Optional.ofNullable(reportVo.getCost()).orElse(BigDecimal.ZERO);
        Integer adOrderNum = Optional.ofNullable(reportVo.getOrderNum()).orElse(0);
        BigDecimal adSale = Optional.ofNullable(reportVo.getSales()).orElse(BigDecimal.ZERO);

        //平均点击费
        BigDecimal adCostPerClick = clicks == 0 ? BigDecimal.ZERO : adCost.divide(new BigDecimal(clicks), 4, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal cvr = clicks == 0 ? BigDecimal.ZERO : new BigDecimal(adOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(clicks), 4, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal ctr = impressions == 0 ? BigDecimal.ZERO : new BigDecimal(clicks).multiply(new BigDecimal("100")).divide(new BigDecimal(impressions), 4, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = adCost.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : adSale.divide(adCost, 4, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : adCost.multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP);
        //asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : adSale.multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP);
        AdHomeAggregateDataRpcVo.Builder builder = AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(acos.toString())
                .setRoas(roas.stripTrailingZeros().toString())
                .setAsots(asots.stripTrailingZeros().toString())
                .setAcots(acots.stripTrailingZeros().toString())
                .setAdCost(adCost.toString())
                .setClicks(Int32Value.of(clicks))
                .setAdOrderNum(Int32Value.of(adOrderNum))
                .setAdSale(adSale.toString())
                .setImpressions(Int32Value.of(impressions))
                .setCtr(ctr.toString())
                .setCvr(cvr.toString())
                .setAdCostPerClick(adCostPerClick.toString());
        if (isVc) {
            builder.setAcots("-");
            builder.setCompareAcotsRate("-");
            builder.setCompareAcots("-");
            builder.setAsots("-");
            builder.setCompareAsotsRate("-");
            builder.setCompareAsots("-");
        }
        return builder.build();

        //.build();

    }


    private void getPageReportVo(Integer puid, CpcQueryWordDto dto, Page page, AdMetricDto adMetricDto, boolean isDoris) {
        List<CpcQueryTargetingReport> poList = page.getRows();
        if (CollectionUtils.isEmpty(poList)) {
            return;
        }
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getAdGroupId).distinct().collect(Collectors.toList());
        List<String> asins =  poList.stream().filter(Objects::nonNull).map(CpcQueryTargetingReport::getQuery).distinct().collect(Collectors.toList());
        List<String> allTargetIds = poList.stream().map(CpcQueryTargetingReport::getTargetId).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());

        //批量查询广告活动和广告组
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
        }


        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item->item,(a,b)->a));
        }

        Map<String, AmazonAdGroup> groupMap = null;

        List<AmazonAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item->item,(a,b)->a));
        }
        Map<String,AmazonAdTargeting> allTargetMap = null;
        if (CollectionUtils.isNotEmpty(allTargetIds)) {
            allTargetMap = amazonAdTargetingShardingDao.getByAdTargetIds(puid, dto.getShopId(), allTargetIds)
                    .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
        }
        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;
            Map<String, AmazonAdTargeting> finalTargetMap = allTargetMap;


            List<AmazonAdTargeting> targetingList = null;
            if (CollectionUtils.isNotEmpty(groupIds)) {
                if (isDoris) {
                    targetingList = this.dorisTargetListByGroupIdList(puid, dto.getShopId(), groupIds).stream().map(e -> {
                        AmazonAdTargeting targeting = new AmazonAdTargeting();
                        targeting.setAdGroupId(e.getAdGroupId());
                        targeting.setTargetingValue(e.getTargetingValue());
                        targeting.setType(e.getType());
                        return targeting;
                    }).collect(Collectors.toList());
                } else {
                    targetingList = amazonAdTargetDaoRoutingService.listByGroupIdList(puid, dto.getShopId(), groupIds);
                }
            }


            Map<String, List<AmazonAdTargeting>> groupTargetMapList =  new HashMap<>();
            if (CollectionUtils.isNotEmpty(targetingList)) {
                for (AmazonAdTargeting target : targetingList) {
                    if (StringUtils.isBlank(target.getAdGroupId())) {
                        continue;
                    }
                    if (groupTargetMapList.containsKey(target.getAdGroupId())) {
                        groupTargetMapList.get(target.getAdGroupId()).add(target);
                    } else {
                        groupTargetMapList.put(target.getAdGroupId(),  Lists.newArrayList(target));
                    }
                }
            }


            BigDecimal sumRange =  dto.getShopSales();  //店铺销售额

            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息

            Map<String,List<AmazonAdProductMetadata>> metadataMap = null;
            if (CollectionUtils.isNotEmpty(asins)) {
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(puid,dto.getShopId(),null,asins);
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    Map<String,List<AmazonAdProductMetadata>> map = new HashMap<>();
                    map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getAsin)));
                    metadataMap = map;
                }
            }
            Map<String,List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;
            List<String> targetIds = new ArrayList<>();
            Map<String, AmazonAdTargeting> targetMap = new HashMap<>();
            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = getVo(e, sumRange);
                filterMetricData(e, vo, adMetricDto);
                vo.setQueryId(e.getQueryId());
                vo.setMarketplaceId(e.getMarketplaceId());
                vo.setQuery(e.getQuery() != null ? e.getQuery().toUpperCase() : "");
                vo.setTargetId(e.getTargetId());
                vo.setTargetingExpression(e.getTargetingExpression());
                if ("close-match".equalsIgnoreCase(e.getTargetingExpression())) {
                    vo.setMatchType("close_match");
                } else if ("loose-match".equalsIgnoreCase(e.getTargetingExpression())) {
                    vo.setMatchType("loose_match");
                } else if ("substitutes".equalsIgnoreCase(e.getTargetingExpression())) {
                    vo.setMatchType("substitutes");
                } else if ("complements".equalsIgnoreCase(e.getTargetingExpression())) {
                    vo.setMatchType("complements");
                } else if (e.getTargetingExpression () != null && e.getTargetingExpression().contains(SpKeywordGroupValueEnum.getKeywordGroupKey())) {
                    vo.setMatchType("theme");
                } else {
                    vo.setMatchType(StringUtil.toStringSafe(e.getTargetingExpression()));
                }
                if (SpKeywordGroupValueEnum.getTheme().equalsIgnoreCase(vo.getMatchType())) {
                    vo.setKeywordText(e.getTargetingExpression());
                } else {
                    if (Constants.TARGETING_EXPRESSION_PREDEFINED.equals(e.getTargetingType())) {
                        vo.setKeywordText("自动投放组");
                    } else {
                        //将手动投放的targetId放入到需要查询投放表的list中
                        //该类型为精确/拓展/类目
                        targetIds.add(vo.getTargetId());
                        String expression = e.getTargetingExpression();
                        if (expression.startsWith("asin=")) {
                            vo.setKeywordText(expression.substring("asin=".length()));
                            // 去除首尾双引号
                            if (vo.getKeywordText().startsWith("\"") && vo.getKeywordText().endsWith("\"")) {
                                vo.setKeywordText(vo.getKeywordText().substring(1, vo.getKeywordText().length() - 1).toUpperCase());
                            }
                        } else if (expression.startsWith("asin-expanded=")) {
                            vo.setKeywordText(expression.substring("asin-expanded=".length()));
                            if (vo.getKeywordText().startsWith("\"") && vo.getKeywordText().endsWith("\"")) {
                                vo.setKeywordText(vo.getKeywordText().substring(1, vo.getKeywordText().length() - 1).toUpperCase());
                            }
                        } else {
                            vo.setKeywordText(expression);
                        }
                    }
                }
                vo.setSalesConversionRate(e.getSalesConversionRateSaleNum());
                vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                vo.setCampaignId(e.getCampaignId());
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(e.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    vo.setCampaignStatus(campaign.getState());
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                            if (amazonAdPortfolio != null) {
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                vo.setPortfolioName("广告组合待同步");
                            }
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    vo.setAdGroupName(finalGroupMap.get(e.getAdGroupId()).getName());
                    vo.setAdGroupType(finalGroupMap.get(e.getAdGroupId()).getAdGroupType());
                    vo.setDefaultBid(finalGroupMap.get(e.getAdGroupId()).getDefaultBid());
                    vo.setAdGroupState(finalGroupMap.get(e.getAdGroupId()).getState());
                } else {
                    vo.setAdGroupName(e.getAdGroupName());
                }
                if (MapUtils.isNotEmpty(finalTargetMap) && finalTargetMap.containsKey(e.getTargetId())){
                    AmazonAdTargeting amazonAdTargeting = finalTargetMap.get(e.getTargetId());
                    vo.setTargetState(amazonAdTargeting.getState());
                }
                String mainImage = e.getMainImage();
                if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(vo.getQuery())) {
                    AmazonAdProductMetadata metadata = finalMetadataMap.get(vo.getQuery()).get(0);
                    if (metadata != null) {
                        mainImage = metadata.getImageUrl();
                    }
                }
                if (StringUtils.isBlank(mainImage) && ("substitutes".equals(vo.getTargetingExpression()) || "complements".equals(vo.getTargetingExpression()) || (vo.getTargetingExpression() !=null && vo.getTargetingExpression().contains("asin")))) {
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                } else if (StringUtils.isBlank(mainImage) && StringUtils.isNotBlank(vo.getQuery()) && Pattern.compile("^[bB]+[A-Za-z0-9]{9}+$").matcher(vo.getQuery()).matches()) { //搜索词符合 b 开头，后面跟9位字母和数字的组合
                    mainImage = syncAsinImageService.getMainImage(vo.getQuery(), dto.getMarketplaceId());
                }
                if(mainImage != null && mainImage.endsWith("S60_.jpg")){
                    mainImage = mainImage.replace("S60_.jpg","S600_.jpg");
                }
                vo.setMainImage(mainImage);

                if (groupTargetMapList.size() > 0 && groupTargetMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<AmazonAdTargeting> amazonAdTargetings = groupTargetMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                        for (AmazonAdTargeting targeting : amazonAdTargetings) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(targeting.getTargetingValue())) {
                                if (e.getQuery().trim().equalsIgnoreCase(targeting.getTargetingValue().trim())) {
                                    if ("asin".equalsIgnoreCase(targeting.getType())) {
                                        vo.setIsTargetAsin(true);
                                    }
                                    if ("negativeAsin".equalsIgnoreCase(targeting.getType())) {
                                        vo.setIsNeTargetAsin(true);
                                    }
                                }
                            }
                        }
                    }
                }
                //广告类型
                vo.setType(Constants.SP);
                list.add(vo);
            });
            if (CollectionUtils.isNotEmpty(targetIds)) {
                targetMap = amazonAdTargetDaoRoutingService.getByAdTargetIds(puid, dto.getShopId(), targetIds)
                        .stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, Function.identity()));
            }
            List<String> asinList = list.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getKeywordText()) && Pattern.compile(Constants.ASIN_REGEX).matcher(e.getKeywordText()).matches() && org.apache.commons.lang3.StringUtils.isBlank(e.getImgUrl())).map(e -> e.getKeywordText().toUpperCase()).distinct().collect(Collectors.toList());
            Map<String, AsinImage> asinMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(asinList)) {
                //填充标签数据
                long t4 = Instant.now().toEpochMilli();
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(), shopAuth.getMarketplaceId(), asins);
                log.info("获取图片花费时间 {}", Instant.now().toEpochMilli() - t4);
                asinMap = listByAsins.stream().filter(e -> org.apache.commons.lang3.StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
            }
//            Map<String, AmazonAdTargeting> finalTargetMap = targetMap;
            for (ReportVo vo : list) {
                if (targetMap.containsKey(vo.getTargetId())) {
                    AmazonAdTargeting target = targetMap.get(vo.getTargetId());
                    if (target != null && TargetTypeEnum.category.name().equals(target.getType())) {
                        vo.setMatchType(target.getType());
                        vo.setCategory(target.getTargetingValue());
                        // 改版前路径是存在这个字段里的，因为要支持列表页模糊搜索改到了targeting_value
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(target.getCategoryPath())) {
                            vo.setCategory(target.getCategoryPath());
                        }
                        //如果为数字ID,表明类目或品牌已经被amazon删除
                        if (org.apache.commons.lang3.StringUtils.isNumeric(vo.getCategory())) {
                            vo.setCategory("此类目亚马逊已删除");
                        }
                    }
                    // asin图片和商品标题
                    if (target != null && TargetTypeEnum.asin.name().equals(target.getType())) {
                        vo.setMatchType(target.getSelectType());
                        vo.setTargetTitle(target.getTitle());
                        vo.setImgUrl(target.getImgUrl());
                        vo.setKeywordText(target.getTargetingValue());
                    }
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(vo.getKeywordText()) && (org.apache.commons.lang3.StringUtils.isBlank(vo.getTitle()) || org.apache.commons.lang3.StringUtils.isBlank(vo.getImgUrl()))) {
                        AsinImage asinImage = asinMap.get(vo.getKeywordText().toUpperCase());
                        if (asinImage != null) {
                            Optional.ofNullable(asinImage.getTitle()).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).ifPresent(t -> {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(vo.getTargetTitle())) {
                                    vo.setTargetTitle(t);
                                }
                            });
                            Optional.ofNullable(asinImage.getImage()).filter(org.apache.commons.lang3.StringUtils::isNotEmpty).ifPresent(m -> {
                                if (org.apache.commons.lang3.StringUtils.isEmpty(vo.getImgUrl())) {
                                    vo.setImgUrl(m);
                                }
                            });
                        }
                    }
                    if (target != null && org.apache.commons.lang3.StringUtils.isNotBlank(target.getResolvedExpression()) && TargetTypeEnum.category.name().equalsIgnoreCase(target.getType())) {
                        JSONArray jsonArray = JSONArray.parseArray(target.getResolvedExpression());
                        if (jsonArray != null && !jsonArray.isEmpty()) {
                            this.fillBrandMessage(vo, jsonArray);
                        }
                    }
                }
            }
            page.setRows(list);
        }
    }

    /**
     * 填充品牌细节信息
     *
     * @param vo
     * @param jsonArray
     */
    private void fillBrandMessage(QueryReportVo vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (org.apache.commons.lang3.StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setCategoryRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookBack(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    /**
     * 填充品牌细节信息
     *
     * @param vo
     * @param jsonArray
     */
    private void fillBrandMessage(TargetQueryReportVo vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (org.apache.commons.lang3.StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setCategoryRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookBack(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    /**
     * 填充品牌细节信息
     *
     * @param vo
     * @param jsonArray
     */
    private void fillBrandMessage(ReportVo vo, JSONArray jsonArray) {
        if (jsonArray == null || jsonArray.isEmpty()) return;
        try {
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                String value = jsonObject.getString("type");
                // T00030 TargetingPredicateNested
                if (SBTargetingAudienceTypeEnum.fromValue(value) != null) {
                    JSONArray valueArray = ResolvedExpressionParseHelper.getJsonValueAsArray(jsonObject);
                    fillBrandMessage(vo, valueArray);
                }
                if (ExpressionEnum.asinCategorySameAs.value().equals(value) || SpV3ExpressionEnum.asinCategorySameAs.getValueV3().equals(value)) {
                    vo.setCategory(jsonObject.getString("value"));
                    //如果为数字ID,表明类目或品牌已经被amazon删除
                    if (org.apache.commons.lang3.StringUtils.isNumeric(vo.getCategory())) {
                        vo.setCategory("此类目亚马逊已删除");
                    }
                }
                if (ExpressionEnum.asinBrandSameAs.value().equals(value) || SpV3ExpressionEnum.asinBrandSameAs.getValueV3().equals(value)) {
                    vo.setBrandName(jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinPriceBetween.value().equals(value) || SpV3ExpressionEnum.asinPriceBetween.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value").replace("-", ","));
                }
                if (ExpressionEnum.asinPriceLessThan.value().equals(value) || SpV3ExpressionEnum.asinPriceLessThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及以下");
                }
                if (ExpressionEnum.asinPriceGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinPriceGreaterThan.getValueV3().equals(value)) {
                    vo.setCommodityPriceRange(jsonObject.getString("value") + " 及更高");
                }
                if (ExpressionEnum.asinReviewRatingBetween.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingBetween.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value").replace('-', '~'));
                }
                if (ExpressionEnum.asinReviewRatingLessThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingLessThan.getValueV3().equals(value)) {
                    vo.setCategoryRating("1~" + jsonObject.getString("value"));
                }
                if (ExpressionEnum.asinReviewRatingGreaterThan.value().equals(value) || SpV3ExpressionEnum.asinReviewRatingGreaterThan.getValueV3().equals(value)) {
                    vo.setCategoryRating(jsonObject.getString("value") + "~5");
                }
                if (ExpressionEnum.asinIsPrimeShippingEligible.value().equals(value) || SpV3ExpressionEnum.asinIsPrimeShippingEligible.getValueV3().equals(value)) {
                    if ("true".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("具有Prime资格");
                    } else if ("false".equalsIgnoreCase(jsonObject.getString("value"))) {
                        vo.setDistribution("不具有Prime资格");
                    }
                }
                if (ExpressionEnum.lookback.value().equals(value)) {
                    vo.setLookBack(jsonObject.getString("value"));
                }
            }
        } catch (Exception e) {
            log.error("解析json失败", e);
        }
    }

    private List<AmazonAdTargeting> dorisTargetListByGroupIdList(Integer puid, Integer shopId, List<String> groupIds) {
        List<AmazonAdTargeting> result = new ArrayList<>();
        List<OdsAmazonAdTargeting> targetings = odsAmazonAdTargetingDao.listByGroupIdList(puid, shopId, groupIds);
        if (CollectionUtils.isNotEmpty(targetings)) {
            result.addAll(targetings.stream().map(neTarget -> {
                AmazonAdTargeting ne = new AmazonAdTargeting();
                ne.setAdGroupId(neTarget.getAdGroupId());
                ne.setType(neTarget.getType());
                ne.setTargetingValue(neTarget.getTargetingValue());
                return ne;
            }).collect(Collectors.toList()));
        }
        List<OdsAmazonAdNeTargeting> amazonAdNeTargetings = odsAmazonAdNeTargetingDao.listByGroupIdList(puid, shopId, groupIds);
        if (CollectionUtils.isNotEmpty(amazonAdNeTargetings)) {
            result.addAll(amazonAdNeTargetings.stream().map(neTarget -> {
                AmazonAdTargeting ne = new AmazonAdTargeting();
                ne.setAdGroupId(neTarget.getAdGroupId());
                ne.setType(neTarget.getType());
                ne.setTargetingValue(neTarget.getTargetingValue());
                return ne;
            }).collect(Collectors.toList()));
        }
        return result;
    }

    // 填充指标占比数据
    private void filterMetricData(CpcQueryTargetingReport report, ReportVo vo, AdMetricDto adMetricDto) {
        if (adMetricDto == null) {
            vo.setAdCostPercentage("0");
            vo.setAdSalePercentage("0");
            vo.setAdOrderNumPercentage("0");
            vo.setOrderNumPercentage("0");
            return;
        }
        computeMetricData(adMetricDto, report, vo);
    }


    private void computeMetricData(AdMetricDto adMetricDto, CpcQueryTargetingReport report, ReportVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (report.getCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(MathUtil.multiply(MathUtil.divide(report.getCost().toString(), adMetricDto.getSumCost().toString()), "100"));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (report.getTotalSales() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(MathUtil.multiply(MathUtil.divide(report.getTotalSales().toString(), adMetricDto.getSumAdSale().toString()), "100"));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (report.getSaleNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(MathUtil.multiply(MathUtil.divide(report.getSaleNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100"));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (report.getOrderNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(MathUtil.multiply(MathUtil.divide(report.getOrderNum().toString(), adMetricDto.getSumOrderNum().toString()), "100"));
        }

    }


    // 获取投放信息
    private void getTargetingData(Integer puid, Integer shopId, List<String> groupIds, List<AmazonAdTargeting> targetingList, Map<String, List<AmazonAdTargeting>> groupTargetMapList) {
        if (CollectionUtils.isNotEmpty(groupIds)) {
            targetingList = amazonAdTargetDaoRoutingService.listByGroupIdList(puid, shopId, groupIds);
        }

        if (CollectionUtils.isNotEmpty(targetingList)) {
            for (AmazonAdTargeting target : targetingList) {
                if (StringUtils.isBlank(target.getAdGroupId())) {
                    continue;
                }
                if (groupTargetMapList.containsKey(target.getAdGroupId())) {
                    groupTargetMapList.get(target.getAdGroupId()).add(target);
                } else {
                    groupTargetMapList.put(target.getAdGroupId(),  Lists.newArrayList(target));
                }
            }
        }
    }

    // 获取投放信息
    private void getTargetingDorisData(Integer puid, Integer shopId, List<String> groupIds, List<AmazonAdTargeting> targetingList, Map<String, List<AmazonAdTargeting>> groupTargetMapList) {
        if (CollectionUtils.isNotEmpty(groupIds)) {
            targetingList = dorisTargetListByGroupIdList(puid, shopId, groupIds);
        }

        if (CollectionUtils.isNotEmpty(targetingList)) {
            for (AmazonAdTargeting target : targetingList) {
                if (StringUtils.isBlank(target.getAdGroupId())) {
                    continue;
                }
                if (groupTargetMapList.containsKey(target.getAdGroupId())) {
                    groupTargetMapList.get(target.getAdGroupId()).add(target);
                } else {
                    groupTargetMapList.put(target.getAdGroupId(),  Lists.newArrayList(target));
                }
            }
        }
    }


    // 填充标签信息
    private void filterTagMessage(Map<String, List<AmazonAdTargeting>> groupTargetMapList, String groupId, String query, TargetQueryReportVo targetQueryReportVo, QueryReportVo queryReportVo, ReportVo reportVo) {
        if (groupTargetMapList.size() > 0 && groupTargetMapList.containsKey(groupId) && StringUtils.isNotBlank(query)) {
            List<AmazonAdTargeting> amazonAdTargetings = groupTargetMapList.get(groupId);
            if (CollectionUtils.isNotEmpty(amazonAdTargetings)) {
                for (AmazonAdTargeting targeting : amazonAdTargetings) {
                    if (StringUtils.isNotBlank(query) && StringUtils.isNotBlank(targeting.getTargetingValue())) {
                        if (query.trim().equalsIgnoreCase(targeting.getTargetingValue().trim())) {
                            if ("asin".equalsIgnoreCase(targeting.getType())) {
                                if (targetQueryReportVo != null) {
                                    targetQueryReportVo.setIsTargetAsin(true);
                                }
                                if (queryReportVo != null) {
                                    queryReportVo.setIsTargetAsin(true);
                                }
                                if (reportVo != null) {
                                    reportVo.setIsTargetAsin(true);
                                }
                            }
                            if ("negativeAsin".equalsIgnoreCase(targeting.getType())) {
                                if (targetQueryReportVo != null) {
                                    targetQueryReportVo.setIsNeTargetAsin(true);
                                }
                                if (queryReportVo != null) {
                                    queryReportVo.setIsNeTargetAsin(true);
                                }
                                if (reportVo != null) {
                                    reportVo.setIsNeTargetAsin(true);
                                }
                            }
                        }
                    }
                }
            }
        }
    }

}
