package com.meiyunji.sponsored.service.cpc.service2.impl;


import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllProductAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllProductDataResponse;
import com.meiyunji.sponsored.rpc.vo.AdTagVo;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdProductMetadataService;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdTargetDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.impl.AdChartDataProcess;
import com.meiyunji.sponsored.service.cpc.service2.IWxCpcGroupService;
import com.meiyunji.sponsored.service.cpc.service2.sp.impl.CpcCommService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.enums.AdTagTypeEnum;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.sellfoxApi.IProductApi;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.vo.ProductAdReportVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * 微信-广告组相关业务接口实现
 * <AUTHOR> on 2023/02/06
 */
@Service
@Slf4j
public class WxCpcGroupServiceImpl implements IWxCpcGroupService {
    @Autowired
    private IAmazonAdProductDao amazonAdProductDao;
    @Autowired
    private IAmazonSdAdProductDao sdAdProductDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IProductApi productDao;
    @Autowired
    private IAmazonAdProductReportDao amazonAdProductReportDao;
    @Autowired
    private IAmazonAdSdProductReportDao amazonAdSdProductReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private AdChartDataProcess adChartDataProcess;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonSbAdCampaignDao amazonSbAdCampaignDao;
    @Autowired
    private IAmazonSdAdCampaignDao amazonSdAdCampaignDao;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAmazonAdProductMetadataService amazonAdProductMetadataService;
    @Autowired
    private IAdMarkupTagDao adMarkupTagDao;
    @Autowired
    private IAdTagDao adTagDao;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Resource
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Autowired
    private IAmazonAdTargetDaoRoutingService amazonAdTargetDaoRoutingService;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    @Autowired
    private IOdsAmazonAdNeKeywordDao odsAmazonAdNeKeywordDao;


    /**
     * 广告产品(sb sd)
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllProductDataResponse.AdProductHomeVo getAllProductData(int puid, AdProductPageParam param) {
        Page<AdProductPageVo> voPage = new Page<>();
        voPage.setPageSize(param.getPageSize());
        voPage.setPageNo(param.getPageNo());
        //查询所有数据(sb sd)

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        if (StringUtils.isBlank(param.getType())) {
            param.setType("sp");
        }

        //获取不同类型数据 sp、sd
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            getSpProductVoList(shopAuth, puid, param, voPage, false);
        } else {
            getSdProductVoList(shopAuth, puid, param, voPage, false);
        }

        //填充标签数据
        long t1 = Instant.now().toEpochMilli();
        fillAdTagData(puid, param.getShopId(), param,voPage.getRows());
        log.info("wx端-标签数据填充花费时间 {}", Instant.now().toEpochMilli() - t1);


        //分页后,填充活动,广告组,asin信息
        fillinAdInfoForVo(shopAuth, voPage.getRows(), false);

        //处理分页
        AllProductDataResponse.AdProductHomeVo.Page.Builder pageBuilder = AllProductDataResponse.AdProductHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(voPage.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(voPage.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(voPage.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(voPage.getTotalSize()));
        List<AdProductPageVo> rows = voPage.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            List< AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo> rpcVos = rows.stream().filter(Objects::nonNull).map(item -> {
                AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.Builder voBuilder = AllProductDataResponse.AdProductHomeVo.Page.AdProductPageVo.newBuilder();
                voBuilder.setType(item.getType());
                voBuilder.setId(Int64Value.of(item.getId()));
                voBuilder.setShopId(Int32Value.of(item.getShopId()));

                if(CollectionUtils.isNotEmpty(item.getAdTags())){
                    item.getAdTags().forEach(e->{
                        com.meiyunji.sponsored.rpc.vo.AdTagVo.Builder builder = com.meiyunji.sponsored.rpc.vo.AdTagVo.newBuilder();
                        AdTagVo tagVo = builder.setId(Int64Value.of(e.getId())).setColor(e.getColor()).setName(e.getName()).build();
                        voBuilder.addAdTags(tagVo);
                    });
                }


                if (item.getDxmAdGroupId()!=null) {
                    voBuilder.setDxmAdGroupId(Int64Value.of(item.getDxmAdGroupId()));
                }
                if (StringUtils.isNotBlank(item.getCampaignId())) {
                    voBuilder.setCampaignId(item.getCampaignId());
                }
                if (StringUtils.isNotBlank(item.getCampaignName())) {
                    voBuilder.setCampaignName(item.getCampaignName());
                }
                if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
                    voBuilder.setCampaignTargetingType(item.getCampaignTargetingType());
                }
                if (StringUtils.isNotBlank(item.getAdGroupId())) {
                    voBuilder.setAdGroupId(item.getAdGroupId());
                }
                if (StringUtils.isNotBlank(item.getAdGroupType())) {
                    voBuilder.setAdGroupType(item.getAdGroupType());
                }
                if (StringUtils.isNotBlank(item.getAdGroupName())) {
                    voBuilder.setAdGroupName(item.getAdGroupName());
                }
                if (StringUtils.isNotBlank(item.getAdId())) {
                    voBuilder.setAdId(item.getAdId());
                }
                if (StringUtils.isNotBlank(item.getState())) {
                    voBuilder.setState(item.getState());
                }
                if (StringUtils.isNotBlank(item.getAsin())) {
                    voBuilder.setAsin(item.getAsin());
                }
                if (StringUtils.isNotBlank(item.getSku())) {
                    voBuilder.setSku(item.getSku());
                }
                if (StringUtils.isNotBlank(item.getTitle())) {
                    voBuilder.setTitle(item.getTitle());
                }
                if (StringUtils.isNotBlank(item.getImgUrl())) {
                    if( item.getImgUrl().endsWith("S60_.jpg") ){
                        item.setImgUrl(item.getImgUrl().replace("S60_.jpg","S600_.jpg"));
                    }
                    voBuilder.setImgUrl(item.getImgUrl());
                }
                if (StringUtils.isNotBlank(item.getPrice())) {
                    voBuilder.setPrice(item.getPrice());
                }
                if (StringUtils.isNotBlank(item.getDomain())) {
                    voBuilder.setDomain(item.getDomain());
                }
                if (StringUtils.isNotBlank(item.getPortfolioId())) {
                    voBuilder.setPortfolioId(item.getPortfolioId());
                }
                if (StringUtils.isNotBlank(item.getPortfolioName())) {
                    voBuilder.setPortfolioName(item.getPortfolioName());
                }
                if (item.getIsHidden() != null) {
                    voBuilder.setIsHidden(item.getIsHidden());
                }

                if(item.getServingStatus() != null){
                    voBuilder.setServingStatus(item.getServingStatus());
                }
                if(item.getServingStatusDec() != null){
                    voBuilder.setServingStatusDec(item.getServingStatusDec());
                }
                if(item.getServingStatusName() != null){
                    voBuilder.setServingStatusName(item.getServingStatusName());
                }

                voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
                voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
                voBuilder.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
                voBuilder.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
                voBuilder.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
                voBuilder.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
                voBuilder.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
                voBuilder.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
                voBuilder.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
                return voBuilder.build();

            }).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllProductDataResponse.AdProductHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();

    }


    @Override
    public AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(int puid, AdProductPageParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(shopAuth.getId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);

        //获取不同类型数据 sp、sd
        List<AdHomePerformancedto> reportList;
        List<AdHomePerformancedto> reportDayList;
        List<AdHomePerformancedto> compareList;
        List<String> adIdList;

        boolean isNull = false;  // 查询的数据为空
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            //标签筛选
            if(param.getAdTagId() != null){
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(),  param.getAdTagId(), param.getType(),null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setAdIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if(!isNull){
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }
            }


            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
                compareList = new ArrayList<>();
            } else {

                AdProductPageParam compareParam = new AdProductPageParam();
                dealCompareParam(param, compareParam);

                reportList = amazonAdProductReportDao.getSpReportByDate(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);
                compareList = amazonAdProductReportDao.getSpReportByDate(puid, compareParam.getShopId(), compareParam.getStartDate(), compareParam.getEndDate(), compareParam);

                adIdList = reportList.stream().map(AdHomePerformancedto::getAdId).collect(Collectors.toList());

                reportDayList = amazonAdProductReportDao.getSpReportByAdIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), adIdList);

            }

        } else {

            //标签筛选
            if(param.getAdTagId() != null){
                List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(),  param.getAdTagId(), param.getType(),null);
                if (CollectionUtils.isNotEmpty(relationIds)) {
                    param.setAdIds(relationIds);
                } else {
                    isNull = true;
                }
            }

            if(!isNull){
                if (StringUtils.isNotBlank(param.getPortfolioId())) {  //广告组合id不为空
                    List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                    if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
                        param.setCampaignIdList(campaignIds);
                    } else {
                        isNull = true;
                    }
                }
            }


            if (isNull) {
                reportList = new ArrayList<>();
                reportDayList  = new ArrayList<>();
                compareList = new ArrayList<>();
            } else {

                AdProductPageParam compareParam = new AdProductPageParam();
                dealCompareParam(param, compareParam);

                reportList = amazonAdSdProductReportDao.getSdReportByDate(puid, param.getShopId(), shopAuth.getMarketplaceId(), param.getStartDate(), param.getEndDate(), param);
                compareList = amazonAdSdProductReportDao.getSdReportByDate(puid, compareParam.getShopId(), shopAuth.getMarketplaceId(), compareParam.getStartDate(), compareParam.getEndDate(), compareParam);

                adIdList = reportList.stream().map(AdHomePerformancedto::getAdId).collect(Collectors.toList());
                reportDayList = amazonAdSdProductReportDao.getSdReportByAdIdList(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), adIdList);
            }
        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo productAggregateDataVo = getAdProductAggregateDataVo(reportList, shopSalesByDate);
        AdHomeAggregateDataRpcVo compareAggregateDataVo = getAdProductAggregateDataVo(compareList, shopSalesByDate);

        //查询货币类型
        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();

        //处理环比数据
        AdHomeAggregateDataRpcVo adHomeAggregateDataRpcVo = getProductAggregateDataChainVo(productAggregateDataVo, compareAggregateDataVo);

        //获取chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList,shopSalesByDate);

        return AllProductAggregateDataResponse.AdProductHomeVo.newBuilder()
                .setAggregateDataVo(adHomeAggregateDataRpcVo)
                .addAllDay(dayPerformanceVos)
                .build();
    }


    private void fillAdTagData(Integer puid, Integer shopId,AdProductPageParam param,List<AdProductPageVo> rows){
        if(CollectionUtils.isEmpty(rows)){
            return ;
        }
        List<String> groups = rows.stream().map(AdProductPageVo::getAdId).distinct().collect(Collectors.toList());
        List<AdMarkupTagVo> relationVos = adMarkupTagDao.getRelationVos(puid, shopId, AdTagTypeEnum.PRODUCT.getType(), param.getType(), null,null, groups);
        if(CollectionUtils.isEmpty(relationVos)){
            return;
        }
        List<Long> collect = relationVos.stream().map(AdMarkupTagVo::getTagIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if(CollectionUtils.isEmpty(collect)){
            return;
        }
        List<AdTag> byLongIdList = adTagDao.getListByLongIdList(puid, collect);
        if(CollectionUtils.isEmpty(byLongIdList)){
            return;
        }
        Map<Long, AdTag> adTagMap = byLongIdList.stream().collect(Collectors.toMap(AdTag::getId, e -> e, (e1, e2) -> e2));
        Map<String, AdMarkupTagVo> adMarkupTagVoMap = relationVos.stream().collect(Collectors.toMap(AdMarkupTagVo::getRelationId, e -> e, (e1, e2) -> e2));
        for (AdProductPageVo vo : rows) {
            AdMarkupTagVo adMarkupTagVo = adMarkupTagVoMap.get(vo.getAdId());
            if(adMarkupTagVo == null){
                continue;
            }
            List<Long> tagIds = adMarkupTagVo.getTagIds();
            if(tagIds == null){
                continue;
            }
            List<AdTag> collect1 = tagIds.stream().map(e -> adTagMap.get(e)).collect(Collectors.toList());
            vo.setAdTags(collect1);
        }
    }



    /**
     * 填充广告信息
     * @param shopAuth 门店对象
     */
    private void fillinAdInfoForVo(ShopAuth shopAuth,  List<AdProductPageVo> rows, boolean isExport) {
        long startTime = System.currentTimeMillis();
        if (CollectionUtils.isNotEmpty(rows)) {

            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            Map<String, AmazonAdCampaignAll> sdCampaignMap = null;
            Map<String, AmazonAdGroup> spGroupMap = null;
            Map<String, AmazonSdAdGroup> sdGroupMap = null;
            Map<String, List<ProductAdReportVo>> asinImageMap = null;
            Map<String, List<AmazonAdProductMetadata>> metadataMap = null;
            //分组获取广告活动和广告组IDS
            Map<String, Set<String>> allTypeCamMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductPageVo::getType, Collectors.mapping(AdProductPageVo::getCampaignId, Collectors.toSet())));
            Map<String, Set<String>> allTypeGroupMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdProductPageVo::getType, Collectors.mapping(AdProductPageVo::getAdGroupId, Collectors.toSet())));


            //sp广告活动
            if (MapUtils.isNotEmpty(allTypeCamMap) && allTypeCamMap.containsKey(Constants.SP) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SP))) {
                List<String> spCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SP).toArray(new String[]{}));
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), spCampaignIds);
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step1 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            //sd广告活动
            if (MapUtils.isNotEmpty(allTypeCamMap) && allTypeCamMap.containsKey(Constants.SD) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SD))) {
                List<String> sdCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SD).toArray(new String[]{}));
                List<AmazonAdCampaignAll> sdCampaignList = amazonSdAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), sdCampaignIds);
                if (CollectionUtils.isNotEmpty(sdCampaignList)) {
                    sdCampaignMap = sdCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step2 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            //sp广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && allTypeGroupMap.containsKey(Constants.SP) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SP))) {
                List<String> spGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SP).toArray(new String[]{}));
                List<AmazonAdGroup> spGroupList = amazonAdGroupDao.getAdGroupByIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), spGroupIds);
                if (CollectionUtils.isNotEmpty(spGroupList)) {
                    spGroupMap = spGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step3 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);

            //sd广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && allTypeGroupMap.containsKey(Constants.SD) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SD))) {
                List<String> sdGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SD).toArray(new String[]{}));
                List<AmazonSdAdGroup> sdGroupList = amazonSdAdGroupDao.getByGroupIds(shopAuth.getPuid(), shopAuth.getId(), sdGroupIds);
                if (CollectionUtils.isNotEmpty(sdGroupList)) {
                    sdGroupMap = sdGroupList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
                }
            }
            log.info("============================== 查询所有广告产品未分页数据 step4 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);


            // 导出不需要取图片需要取价格
            // 到产品表取asin图片
            List<String> skus = rows.stream().filter(Objects::nonNull).map(AdProductPageVo::getSku).collect(Collectors.toList());
            Map<String, List<ProductAdReportVo>> hashMap = new HashMap<>();
            Map<String, List<AmazonAdProductMetadata>> map = new HashMap<>();
            if (CollectionUtils.isNotEmpty(skus)) {
                List<AmazonAdProductMetadata> amazonAdProductMetadataList = amazonAdProductMetadataService.getAsinBySkus(shopAuth.getPuid(),shopAuth.getId(),skus,null);
                if (CollectionUtils.isNotEmpty(amazonAdProductMetadataList)) {
                    map.putAll(amazonAdProductMetadataList.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdProductMetadata::getSku)));
                    metadataMap = map;
                }
                // 兼容老代码逻辑
                List<List<String>> partition = Lists.partition(skus, 100);
                for (List<String> batchSkus : partition) {
                    List<ProductAdReportVo> asinBySkus = productDao.getAsinBySkus(shopAuth.getPuid(), shopAuth.getId(), "", batchSkus);
                    if (CollectionUtils.isNotEmpty(asinBySkus)) {
                        hashMap.putAll(asinBySkus.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(ProductAdReportVo::getSku)));
                    }
                }
                asinImageMap = hashMap;
            }
            log.info("============================== 查询所有广告产品未分页数据 step5 ,并转vo花费时间 {} ==============================", System.currentTimeMillis() - startTime);

            Map<String, AmazonSdAdGroup> finalSdGroupMap = sdGroupMap;
            Map<String, AmazonAdCampaignAll> finalSdCampaignMap = sdCampaignMap;
            Map<String, AmazonAdGroup> finalSpGroupMap = spGroupMap;
            Map<String, AmazonAdCampaignAll> finalSpCampaignMap = spCampaignMap;
            Map<String, List<ProductAdReportVo>> finalAsinImageMap = asinImageMap;
            Map<String, List<AmazonAdProductMetadata>> finalMetadataMap = metadataMap;

            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息

            rows.stream().filter(Objects::nonNull).forEach(vo-> {
                // asin图片
                vo.setAsin(vo.getAsin());
                if (MapUtils.isNotEmpty(finalMetadataMap) && finalMetadataMap.containsKey(vo.getSku())) {
                    AmazonAdProductMetadata metadata = finalMetadataMap.get(vo.getSku()).get(0);
                    vo.setTitle(metadata.getTitle());
                    vo.setImgUrl(metadata.getImageUrl());
                    if (metadata.getPriceToPayAmount() != null) {
                        vo.setPrice((metadata.getPriceToPayAmount().toString()));
                    }
                }
                if (StringUtils.isBlank(vo.getTitle()) || StringUtils.isBlank(vo.getImgUrl()) || StringUtils.isBlank(vo.getPrice())) {
                    if (MapUtils.isNotEmpty(finalAsinImageMap) && finalAsinImageMap.containsKey(vo.getSku())) {
                        ProductAdReportVo product = finalAsinImageMap.get(vo.getSku()).get(0);
                        vo.setTitle(product.getTitle());
                        vo.setImgUrl(product.getMainImage());
                        if (product.getStandardPrice() != null) {
                            vo.setPrice(product.getStandardPrice().toString());
                        }
                    }
                }

                if (Constants.SP.equalsIgnoreCase(vo.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(finalSpCampaignMap) && finalSpCampaignMap.containsKey(vo.getCampaignId())) {
                        AmazonAdCampaignAll campaign = finalSpCampaignMap.get(vo.getCampaignId());
                        vo.setCampaignName(campaign.getName());
                        vo.setCampaignTargetingType(campaign.getTargetingType());

                        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                            vo.setPortfolioId(campaign.getPortfolioId());
                            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    vo.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                    vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    vo.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            vo.setPortfolioName("-");
                        }

                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(finalSpGroupMap) && finalSpGroupMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdGroupName(finalSpGroupMap.get(vo.getAdGroupId()).getName());
                        vo.setAdGroupType(finalSpGroupMap.get(vo.getAdGroupId()).getAdGroupType());
                    }

                }

                if (Constants.SD.equalsIgnoreCase(vo.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(finalSdCampaignMap) && finalSdCampaignMap.containsKey(vo.getCampaignId())) {
                        AmazonAdCampaignAll amazonSdAdCampaign = finalSdCampaignMap.get(vo.getCampaignId());

                        vo.setCampaignName(amazonSdAdCampaign.getName());
                        vo.setCampaignTargetingType(amazonSdAdCampaign.getTactic());

                        if (StringUtils.isNotBlank(amazonSdAdCampaign.getPortfolioId())) {
                            vo.setPortfolioId(amazonSdAdCampaign.getPortfolioId());
                            if (portfolioMap.containsKey(amazonSdAdCampaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSdAdCampaign.getPortfolioId());
                                vo.setPortfolioName(amazonAdPortfolio.getName());
                                vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(amazonSdAdCampaign.getPuid(), amazonSdAdCampaign.getShopId(), amazonSdAdCampaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    vo.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(amazonSdAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                    vo.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    vo.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            vo.setPortfolioName("-");
                        }
                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(finalSdGroupMap) && finalSdGroupMap.containsKey(vo.getAdGroupId())) {
                        vo.setAdGroupName(finalSdGroupMap.get(vo.getAdGroupId()).getName());
                    }
                }
            });
        }
    }

    /**
     * 处理统计信息
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getAdProductAggregateDataVo(List<AdHomePerformancedto> rows,BigDecimal shopSales) {

        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (CollectionUtils.isEmpty(rows)) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setAdCost("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .build();
        }
        //点击量
        int sumClicks = rows.stream().filter(item -> item != null && item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item != null && item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item != null && item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item != null && item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item != null && item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 4, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCvr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 4, BigDecimal.ROUND_HALF_UP);
        return AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toString())
                .setAdCost(sumAdcost.stripTrailingZeros().toString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCvr.stripTrailingZeros().toString())
                .setCtr(sumCtr.stripTrailingZeros().toString())
                .setAdSale(sumAdSale.stripTrailingZeros().toString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .build();
    }


    @Override
    public List<AdProductPageVo> getSpProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport) {

        List<AdProductPageVo> pageVoList;

        if (isExport) {
            pageVoList = getSpProductPageVoList(puid, param);
            if (CollectionUtils.isEmpty(pageVoList)) {
                return pageVoList;
            }
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                }
            }
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > 30000) {
                pageVoList =  pageVoList.subList(0, 30000);
            }
            fillinAdInfoForVo(shopAuth, pageVoList, isExport);
            return pageVoList;
        }

        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {
            List<AdProductPageVo> voList = getSpProductPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page =  getSpPageList(puid, param, page);
            //总数大于十万
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    @Override
    public List<AdProductPageVo> getSdProductVoList(ShopAuth shopAuth, Integer puid, AdProductPageParam param, Page<AdProductPageVo> voPage, boolean isExport) {
        List<AdProductPageVo> pageVoList;

        if (isExport) {
            pageVoList = getSdProductPageVoList(puid, param);
            if (CollectionUtils.isEmpty(pageVoList)) {
                return pageVoList;
            }
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    pageVoList = PageUtil.sort(pageVoList, param.getOrderField(), param.getOrderType());
                }
            }
            //限制30000条，需要优化
            if (CollectionUtils.isNotEmpty(pageVoList) && pageVoList.size() > 30000) {
                pageVoList =  pageVoList.subList(0, 30000);
            }
            fillinAdInfoForVo(shopAuth, pageVoList, isExport);
            return pageVoList;
        }
        //页面有排序或有高
        if ((StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) || param.getUseAdvanced()) {
            List<AdProductPageVo> voList = getSdProductPageVoList(puid, param);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                boolean isSorted = StringUtils.isNotBlank(param.getOrderField()) && Constants.isADperformanceOrderField(param.getOrderField());
                if (isSorted) {
                    voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
                }
            }
            //分页
            PageUtil.getPage(voPage, voList);
        } else {
            Page page = new Page(voPage.getPageNo(), voPage.getPageSize());
            page =  getSdPageList(puid, param, page);
            //总数大于十万
            if (page.getTotalSize() > Constants.TOTALSIZELIMIT) {
                int totalPage = Constants.TOTALSIZELIMIT / voPage.getPageSize();
                voPage.setTotalPage(totalPage);
                voPage.setTotalSize(Constants.TOTALSIZELIMIT);
            } else {
                voPage.setTotalPage(page.getTotalPage());
                voPage.setTotalSize(page.getTotalSize());
            }
            voPage.setRows(page.getRows());
        }

        return null;
    }

    private List<AdProductPageVo> getSpProductPageVoList(Integer puid, AdProductPageParam param) {
        List<AdProductPageVo> voList = Lists.newArrayList();

        //标签筛选
        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(),  param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return voList;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return voList;
            }
        }

        List<AmazonAdProduct> poList = amazonAdProductDao.getList(puid, param);
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> adIds = poList.stream().map(AmazonAdProduct::getAdId).collect(Collectors.toList());
            List<AdHomePerformancedto> list = new ArrayList<>();
            if (adIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(adIds, 20000);
                List<AdHomePerformancedto> dtoList;
                for (List<String> subList : lists) {
                    dtoList = amazonAdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);
                    list.addAll(dtoList);
                }

            } else {
                list = amazonAdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, adIds);
            }

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            for (AmazonAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SP);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSpDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        //兼容旧代码
                        report.setTotalSales(adHomePerformancedto.getAdSale());
                        //本广告产品销售额
                        //兼容旧代码
                        report.setAdSales(adHomePerformancedto.getAdSales());
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }
            //开启了高级搜索,需要过滤
            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
                cpcCommService.filterProductAdVanceData(voList, param);
            }
        }

        return voList;
    }

    private Page getSpPageList(Integer puid, AdProductPageParam param, Page page) {
        List<AdProductPageVo> voList = Lists.newArrayList();


        //标签筛选
        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(),  param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }


        page = amazonAdProductDao.getPageList(puid, param, page);

        List<AmazonAdProduct> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> adIds = poList.stream().map(AmazonAdProduct::getAdId).collect(Collectors.toList());

            List<AdHomePerformancedto> list = amazonAdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(),
                        param.getEndDate(), param, adIds);

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            page.setRows(voList);
            for (AmazonAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SP);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSpDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        //兼容旧代码
                        report.setTotalSales(adHomePerformancedto.getAdSale());
                        //本广告产品销售额
                        //兼容旧代码
                        report.setAdSales(adHomePerformancedto.getAdSales());
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //本广告产品销量
                        report.setAdOrderNum(adHomePerformancedto.getOrderNum());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }
        }

        return page;

    }

    private List<AdProductPageVo> getSdProductPageVoList(Integer puid, AdProductPageParam param) {
        List<AdProductPageVo> voList = Lists.newArrayList();

        //标签筛选
        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(),  param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return voList;
            }
        }
        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return voList;
            }
        }

        List<AmazonSdAdProduct> poList = sdAdProductDao.getList(puid, param);
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> adIds = poList.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());
            List<AdHomePerformancedto> list = new ArrayList<>();
            if (adIds.size() > 20000) {
                List<List<String>> lists = Lists.partition(adIds, 20000);
                List<AdHomePerformancedto> dtoList;
                for (List<String> subList : lists) {

                    dtoList = amazonAdSdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, subList);

                    list.addAll(dtoList);
                }

            } else {

                list = amazonAdSdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param, adIds);

            }

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            for (AmazonSdAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SD);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSdDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        //兼容旧代码
                        report.setTotalSales(adHomePerformancedto.getAdSale());
                        //本广告产品销售额
                        //兼容旧代码
                        report.setAdSales(adHomePerformancedto.getAdSales());
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }
            //开启了高级搜索,需要过滤
            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {
                cpcCommService.filterProductAdVanceData(voList, param);
            }
        }

        return voList;
    }

    private Page getSdPageList(Integer puid, AdProductPageParam param, Page page) {
        List<AdProductPageVo> voList = Lists.newArrayList();

        //标签筛选
        if(param.getAdTagId() != null){
            List<String> relationIds = adMarkupTagDao.getRelationIds(param.getPuid(), param.getShopId(), AdTagTypeEnum.PRODUCT.getType(),  param.getAdTagId(), param.getType(),null);
            if (CollectionUtils.isNotEmpty(relationIds)) {
                param.setAdIds(relationIds);
            } else {
                return page;
            }
        }

        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }

        page = sdAdProductDao.getPageList(puid, param, page);

        List<AmazonSdAdProduct> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {
            List<String> adIds = poList.stream().map(AmazonSdAdProduct::getAdId).collect(Collectors.toList());
            List<AdHomePerformancedto> list;

            list = amazonAdSdProductReportDao.listSumReportByAdIds(puid, param.getShopId(), param.getStartDate(),
                    param.getEndDate(), param, adIds);

            Map<String, AdHomePerformancedto> productReportMap = list.stream().filter(Objects::nonNull).collect(Collectors.toMap(AdHomePerformancedto::getAdId, e -> e));

            // 取店铺销售额
            ShopSaleDto shopSaleDto = new ShopSaleDto();
            BigDecimal shopSales = param.getShopSales();
            shopSaleDto.setSumRange(shopSales);

            String domain = AmznEndpoint.getByMarketplaceId(poList.get(0).getMarketplaceId()).getDomain();
            AdProductPageVo vo;
            page.setRows(voList);
            for (AmazonSdAdProduct amazonAdProduct : poList) {
                vo = new AdProductPageVo();
                amazonAdProduct.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatus(amazonAdProduct.getServingStatus());
                vo.setServingStatusDec(amazonAdProduct.getServingStatusDec());
                vo.setServingStatusName(amazonAdProduct.getServingStatusName());
                vo.setDomain(domain);
                vo.setType(Constants.SD);
                vo.setAsin(amazonAdProduct.getAsin());
                convertSdDtoToPageVo(amazonAdProduct, vo);

                if (productReportMap.size() > 0) {
                    AdHomePerformancedto adHomePerformancedto = productReportMap.get(amazonAdProduct.getAdId());
                    if (adHomePerformancedto != null) {
                        // 填充报告数据
                        AmazonAdProductReport report = new AmazonAdProductReport();
                        report.setCountDate(adHomePerformancedto.getCountDate());
                        report.setImpressions(adHomePerformancedto.getImpressions());
                        report.setClicks(adHomePerformancedto.getClicks());
                        //兼容旧代码
                        report.setTotalSales(adHomePerformancedto.getAdSale());
                        //本广告产品销售额
                        //兼容旧代码
                        report.setAdSales(adHomePerformancedto.getAdSales());
                        report.setSaleNum(adHomePerformancedto.getAdOrderNum());
                        report.setCost(adHomePerformancedto.getAdCost());
                        //可见展示次数(VCPM专用)
                        report.setViewImpressions(adHomePerformancedto.getViewImpressions());
                        //本广告产品订单量
                        report.setAdSaleNum(adHomePerformancedto.getAdSaleNum());
                        //CPC,VCPM-广告销量
                        report.setOrderNum(adHomePerformancedto.getSalesNum());
                        //CPC,VCPM-“品牌新买家”订单量
                        report.setOrdersNewToBrand14d(adHomePerformancedto.getOrdersNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销售额
                        report.setSalesNewToBrand14d(adHomePerformancedto.getSalesNewToBrand14d());
                        //CPC,VCPM-“品牌新买家”销量
                        report.setUnitsOrderedNewToBrand14d(adHomePerformancedto.getUnitsOrderedNewToBrand14d());
                        cpcCommService.fillReportDataIntoPageVo(vo, report, shopSaleDto);
                    }
                }
                voList.add(vo);
            }
        }

        return page;
    }

    // po->列表页vo
    private void convertSpDtoToPageVo(AmazonAdProduct amazonAdProduct, AdProductPageVo vo) {
        vo.setId(amazonAdProduct.getId());
        vo.setShopId(amazonAdProduct.getShopId());
        vo.setCampaignId(amazonAdProduct.getCampaignId());
        vo.setAdGroupId(amazonAdProduct.getAdGroupId());
        vo.setAdId(amazonAdProduct.getAdId());
        vo.setState(amazonAdProduct.getState());
        vo.setAsin(amazonAdProduct.getAsin());
        vo.setSku(amazonAdProduct.getSku());
        vo.setUpdateTime(amazonAdProduct.getUpdateTime());
    }

    // po->列表页vo
    private void convertSdDtoToPageVo(AmazonSdAdProduct amazonAdProduct, AdProductPageVo vo) {
        vo.setId(amazonAdProduct.getId());
        vo.setShopId(amazonAdProduct.getShopId());
        vo.setCampaignId(amazonAdProduct.getCampaignId());
        vo.setAdGroupId(amazonAdProduct.getAdGroupId());
        vo.setAdId(amazonAdProduct.getAdId());
        vo.setState(amazonAdProduct.getState());
        vo.setAsin(amazonAdProduct.getAsin());
        vo.setSku(amazonAdProduct.getSku());
        vo.setUpdateTime(amazonAdProduct.getUpdateTime());
    }


    @Override
    public Page<NeKeywordsPageRpcVo> getAllNeKeywordPageList(NeKeywordsPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth==null) {
            AssertUtil.fail("店铺未授权");
        }
        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            List<String> campaignIds = null;
            if (Constants.SP.equalsIgnoreCase(param.getType())) {
                campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(shopAuth.getPuid(), param.getShopId(), param.getPortfolioId());
            } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
                campaignIds = amazonSbAdCampaignDao.getCampaignIdsByPortfolioId(shopAuth.getPuid(), param.getShopId(), param.getPortfolioId());
            }

            if (CollectionUtils.isNotEmpty(campaignIds)) {
                param.setCampaignIdList(campaignIds);
            } else {
                return new Page<>(param.getPageNo(), param.getPageSize());
            }
        }
        int totalSize = 0;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            totalSize = odsAmazonAdNeKeywordDao.getTotalSizeFromDoris(param.getPuid(), param);
            param.setTotalSize(totalSize);
        }

        Page<NeKeywordsPageDto> page = amazonAdKeywordDaoRoutingService.getAllTypeNeKeyword(param.getPuid(), param);
        Page<NeKeywordsPageRpcVo> voPage = new Page<>();
        voPage.setPageNo(page.getPageNo());
        voPage.setPageSize(page.getPageSize());
        voPage.setTotalSize(page.getTotalSize());
        voPage.setTotalPage(page.getTotalPage());

        List<NeKeywordsPageDto> rows = page.getRows();
        getAllKeyWordVoList(param, shopAuth, voPage, rows);
        return voPage;
    }


    private void getAllKeyWordVoList(NeKeywordsPageParam param, ShopAuth shopAuth, Page<NeKeywordsPageRpcVo> voPage, List<NeKeywordsPageDto> rows) {
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询sp广告活动ID
            List<String> spCampaignIds = rows.stream().filter(item -> Constants.SP.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getCampaignId).collect(Collectors.toList());

            //查询sp广告组
            List<String> spGroupIds = rows.stream().filter(item -> Constants.SP.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getGroupId).collect(Collectors.toList());

            //查询sb广告活动ID
            List<String> sbCampaignIds = rows.stream().filter(item -> Constants.SB.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getCampaignId).collect(Collectors.toList());

            //查询sb广告活动ID
            List<String> sbGroupIds = rows.stream().filter(item -> Constants.SB.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getGroupId).collect(Collectors.toList());

            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            //sp广告活动
            if (CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(param.getPuid(), param.getShopId(), shopAuth.getMarketplaceId(), spCampaignIds);
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }


            //sb广告活动
            Map<String, AmazonAdCampaignAll> sbCampaignMap = null;
            if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                List<AmazonAdCampaignAll> sbCampaignList = amazonSbAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), sbCampaignIds);
                if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                    sbCampaignMap = sbCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }


            Map<String, AmazonAdGroup> spGroupMap = null;
            //sp广告组
            if (CollectionUtils.isNotEmpty(spGroupIds)) {
                List<AmazonAdGroup> adGroupByIds = amazonAdGroupDao.getAdGroupByIds(shopAuth.getPuid(), param.getShopId(), shopAuth.getMarketplaceId(), spGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    spGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
            }
            Map<String, AmazonSbAdGroup> sbGroupMap = null;
            //sb广告组
            if (CollectionUtils.isNotEmpty(sbGroupIds)) {
                List<AmazonSbAdGroup> adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(shopAuth.getPuid(), param.getShopId(), sbGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    sbGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e));
                }
            }

            List<NeKeywordsPageRpcVo> list = new ArrayList<>(rows.size());
            voPage.setRows(list);
            NeKeywordsPageVo vo;
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
            for (NeKeywordsPageDto amazonAdKeyword : rows) {
                NeKeywordsPageRpcVo.Builder builder = NeKeywordsPageRpcVo.newBuilder();
                if (amazonAdKeyword.getId() != null) {
                    builder.setId(Int64Value.of(amazonAdKeyword.getId()));
                }
                if (amazonAdKeyword.getShopId() != null) {
                    builder.setShopId(Int32Value.of(amazonAdKeyword.getShopId()));
                }
                if (amazonAdKeyword.getDxmAdGroupId() != null) {
                    builder.setDxmAdGroupId(Int64Value.of(amazonAdKeyword.getDxmAdGroupId()));
                }
                if (amazonAdKeyword.getCreateTime() != null) {
                    builder.setCreateTime(DateUtil.dateToStrWithFormat(DateUtil.stringToDate(amazonAdKeyword.getCreateTime()),DateUtil.PATTERN_DATE_TIME));
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                    builder.setState(amazonAdKeyword.getState());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getMatchType())) {
                    builder.setMatchType(amazonAdKeyword.getMatchType());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordText())) {
                    builder.setKeywordText(amazonAdKeyword.getKeywordText());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                    builder.setCampaignId(amazonAdKeyword.getCampaignId());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getGroupId())) {
                    builder.setGroupId(amazonAdKeyword.getGroupId());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getType())) {
                    builder.setType(amazonAdKeyword.getType());
                }

                if (Constants.SP.equalsIgnoreCase(amazonAdKeyword.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                        AmazonAdCampaignAll campaign = spCampaignMap.get(amazonAdKeyword.getCampaignId());
                        if (StringUtils.isNotBlank(campaign.getName())) {
                            builder.setCampaignName(campaign.getName());
                        }
                        if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                            builder.setCampaignTargetingType(campaign.getTargetingType());
                        }

                        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                            builder.setPortfolioId(campaign.getPortfolioId());
                            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                                builder.setPortfolioName(amazonAdPortfolio.getName());
                                builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    builder.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                    builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    builder.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            builder.setPortfolioName("-");
                        }
                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(spGroupMap) && spGroupMap.containsKey(amazonAdKeyword.getGroupId())) {
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getGroupId()).getName())) {
                            builder.setAdGroupName(spGroupMap.get(amazonAdKeyword.getGroupId()).getName());
                        }
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(spGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType());
                        }
                    }

                }

                if (Constants.SB.equalsIgnoreCase(amazonAdKeyword.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(sbCampaignMap) && sbCampaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                        AmazonAdCampaignAll amazonSbAdCampaign = sbCampaignMap.get(amazonAdKeyword.getCampaignId());
                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getName())) {
                            builder.setCampaignName(amazonSbAdCampaign.getName());
                        }

                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getPortfolioId())) {
                            builder.setPortfolioId(amazonSbAdCampaign.getPortfolioId());
                            if (portfolioMap.containsKey(amazonSbAdCampaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSbAdCampaign.getPortfolioId());
                                builder.setPortfolioName(amazonAdPortfolio.getName());
                                builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(amazonSbAdCampaign.getPuid(), amazonSbAdCampaign.getShopId(), amazonSbAdCampaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    builder.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(amazonSbAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                    builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    builder.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            builder.setPortfolioName("-");
                        }

                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(sbGroupMap) && sbGroupMap.containsKey(amazonAdKeyword.getGroupId())) {
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getName())) {
                            builder.setAdGroupName(sbGroupMap.get(amazonAdKeyword.getGroupId()).getName());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdFormat())) {
                            builder.setCampaignTargetingType(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdFormat());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType());
                        }
                    }
                }
                list.add(builder.build());
            }
        }
    }


    @Override
    public Page<NeTargetingPageRpcVo> getAllNeTargetingPageList(int puid, NeTargetingPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        if (StringUtils.isNotBlank(param.getPortfolioId())) {
            if (Constants.SP.equalsIgnoreCase(param.getType())) {
                List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    param.setCampaignIdList(campaignIds);
                } else {
                    return new Page<>(param.getPageNo(), param.getPageSize());
                }
            } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
                List<String> campaignIds = amazonSbAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    param.setCampaignIdList(campaignIds);
                } else {
                    return new Page<>(param.getPageNo(), param.getPageSize());
                }
            } else {
                List<String> campaignIds = amazonSdAdCampaignDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId());
                if (CollectionUtils.isNotEmpty(campaignIds)) {
                    List<AmazonSdAdGroup> amazonAdGroupList = amazonSdAdGroupDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), campaignIds);
                    if (CollectionUtils.isEmpty(amazonAdGroupList)) {
                        return new Page<>(param.getPageNo(), param.getPageSize());
                    }
                    List<String> groupIds = amazonAdGroupList.stream().filter(Objects::nonNull).map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.toList());
                    param.setGroupIdList(groupIds);
                } else {
                    return new Page<>(param.getPageNo(), param.getPageSize());
                }
            }
        }



        //投放sd投放表中没有campaign_id 查询活动下投放list时,只能通过campaignId查询所有groupId,再通过groupId查询投放列表
        if (Constants.SD.equalsIgnoreCase(param.getType()) && StringUtils.isBlank(param.getGroupId()) && StringUtils.isNotBlank(param.getCampaignId())) {
            //查询活动下所有广告组
            List<AmazonSdAdGroup> amazonAdGroupList = amazonSdAdGroupDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), Stream.of(param.getCampaignId()).collect(Collectors.toList()));
            if (CollectionUtils.isEmpty(amazonAdGroupList)) {
                return new Page<>(param.getPageNo(), param.getPageSize());
            }
            String groupIds = amazonAdGroupList.stream().filter(Objects::nonNull).map(AmazonSdAdGroup::getAdGroupId).collect(Collectors.joining(","));
            param.setGroupId(groupIds);
        }
        Page<AmazonAdNeTargetingDto> page = amazonAdTargetDaoRoutingService.listAllNeTargetingByType(puid, param);

        Page<NeTargetingPageRpcVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);
        List<AmazonAdNeTargetingDto> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            String domain = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getDomain();
            //分组获取广告活动和广告组IDS
            Map<String, Set<String>> allTypeCamMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdNeTargetingDto::getAtype, Collectors.mapping(AmazonAdNeTargetingDto::getCampaignId, Collectors.toSet())));
            Map<String, Set<String>> allTypeGroupMap = rows.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AmazonAdNeTargetingDto::getAtype, Collectors.mapping(AmazonAdNeTargetingDto::getGroupId, Collectors.toSet())));


            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            //sp广告活动
            if (MapUtils.isNotEmpty(allTypeCamMap) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SP))) {
                List<String> spCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SP).toArray(new String[]{}));
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(puid, param.getShopId(), shopAuth.getMarketplaceId(), spCampaignIds);
                spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
            }

            //sb广告活动
            Map<String, AmazonAdCampaignAll> sbCampaignMap = null;
            if (MapUtils.isNotEmpty(allTypeCamMap) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SB))) {
                List<String> sbCampaignIds = Arrays.asList(allTypeCamMap.get(Constants.SB).toArray(new String[]{}));
                List<AmazonAdCampaignAll> sbCampaignList = amazonSbAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), sbCampaignIds);
                sbCampaignMap = sbCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));

            }


            Map<String, AmazonAdGroup> spGroupMap = null;
            //sp广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SP))) {
                List<String> spGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SP).toArray(new String[]{}));
                List<AmazonAdGroup> adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, param.getShopId(), shopAuth.getMarketplaceId(), spGroupIds);
                spGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
            }

            Map<String, AmazonSbAdGroup> sbGroupMap = null;
            //sp广告组
            if (MapUtils.isNotEmpty(allTypeGroupMap) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SB))) {
                List<String> spGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SB).toArray(new String[]{}));
                List<AmazonSbAdGroup> adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(puid, param.getShopId(), spGroupIds);
                sbGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e));
            }


            //sd广告组
            Map<String, AmazonSdAdGroup> sdGroupMap = null;
            Map<String, String> sdCampaignAndGroupMap = null;
            List<String> sdCampaignIds = Lists.newArrayList();
            if (MapUtils.isNotEmpty(allTypeGroupMap) && CollectionUtils.isNotEmpty(allTypeGroupMap.get(Constants.SD))) {
                List<String> sdGroupIds = Arrays.asList(allTypeGroupMap.get(Constants.SD).toArray(new String[]{}));
                List<AmazonSdAdGroup> byGroupIds = amazonSdAdGroupDao.getByGroupIds(shopAuth.getPuid(), shopAuth.getId(), sdGroupIds);
                if (CollectionUtils.isNotEmpty(byGroupIds)) {
                    sdCampaignIds = byGroupIds.stream().filter(Objects::nonNull).map(AmazonSdAdGroup::getCampaignId).distinct().collect(Collectors.toList());
                    sdCampaignAndGroupMap = byGroupIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, AmazonSdAdGroup::getCampaignId));

                    sdGroupMap = byGroupIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSdAdGroup::getAdGroupId, e -> e));
                }
            }


            //sd广告活动
            Map<String, AmazonAdCampaignAll> sdCampaignMap = null;
            if (MapUtils.isNotEmpty(allTypeCamMap) && CollectionUtils.isNotEmpty(allTypeCamMap.get(Constants.SD))) {
                List<AmazonAdCampaignAll> sdCampaignList = amazonSdAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), sdCampaignIds);
                sdCampaignMap = sdCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
            }


            List<NeTargetingPageRpcVo> list = new ArrayList<>(rows.size());
            voPage.setRows(list);
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();

            List<String> asins = rows.stream().filter(e -> StringUtils.isNotBlank(e.getTargetText()) && StringUtils.isBlank(e.getImgUrl())).map(e -> e.getTargetText().toUpperCase()).distinct().collect(Collectors.toList());
            Map<String, AsinImage> asinMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(asins)){
                long t4 = Instant.now().toEpochMilli();
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(),shopAuth.getMarketplaceId(),asins);
                log.info("获取图片花费时间 {}", Instant.now().toEpochMilli() - t4);
                asinMap = listByAsins.stream().filter(e->StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e->e.getAsin().toUpperCase(),e1->e1,(e2,e3)->e3));
            }

            for (AmazonAdNeTargetingDto amazonAdTargeting : rows) {
                if(StringUtils.isNotBlank(amazonAdTargeting.getTargetText()) && StringUtils.isBlank(amazonAdTargeting.getImgUrl())){
                    AsinImage asinImage = asinMap.get(amazonAdTargeting.getTargetText().toUpperCase());
                    if(asinImage != null){
                        amazonAdTargeting.setImgUrl(asinImage.getImage());
                        amazonAdTargeting.setTitle(asinImage.getTitle());
                    }
                }
                NeTargetingPageRpcVo.Builder builder = NeTargetingPageRpcVo.newBuilder();
                if (amazonAdTargeting.getId() != null) {
                    builder.setId(Int64Value.of(amazonAdTargeting.getId()));
                }
                if (amazonAdTargeting.getShopId() != null) {
                    builder.setShopId(Int32Value.of(amazonAdTargeting.getShopId()));
                }
                if (amazonAdTargeting.getDxmAdGroupId() != null) {
                    builder.setDxmAdGroupId(Int64Value.of(amazonAdTargeting.getDxmAdGroupId()));
                }
                if (amazonAdTargeting.getCreateTime() != null) {
                    builder.setCreateTime(DateUtil.dateToStrWithFormat(DateUtil.stringToDate(amazonAdTargeting.getCreateTime()), DateUtil.PATTERN_DATE_TIME));
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getState())) {
                    builder.setState(amazonAdTargeting.getState());
                }
                if (StringUtils.isNotBlank(domain)) {
                    builder.setDomain(domain);
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getTargetId())) {
                    builder.setTargetId(amazonAdTargeting.getTargetId());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getAsin())) {
                    builder.setAsin(amazonAdTargeting.getAsin());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getGroupId())) {
                    builder.setGroupId(amazonAdTargeting.getGroupId());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getTitle())) {
                    builder.setTitle(amazonAdTargeting.getTitle());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getImgUrl())) {
                    builder.setImgUrl(amazonAdTargeting.getImgUrl());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getCampaignId())) {
                    builder.setCampaignId(amazonAdTargeting.getCampaignId());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getAtype())) {
                    //原来type被否定类型占用
                    builder.setType(amazonAdTargeting.getAtype());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getTargetText())) {
                    //原来type被否定类型占用
                    builder.setAsin(amazonAdTargeting.getTargetText());
                }
                if (StringUtils.isNotBlank(amazonAdTargeting.getImgUrl())) {
                    if(amazonAdTargeting.getImgUrl().endsWith("S60_.jpg")){
                        amazonAdTargeting.setImgUrl(amazonAdTargeting.getImgUrl().replace("S60_.jpg","S600_.jpg"));
                    }
                    //原来type被否定类型占用
                    builder.setImgUrl(amazonAdTargeting.getImgUrl());
                }
                if (Constants.SP.equalsIgnoreCase(amazonAdTargeting.getAtype())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(amazonAdTargeting.getCampaignId())) {
                        AmazonAdCampaignAll campaign = spCampaignMap.get(amazonAdTargeting.getCampaignId());
                        if (StringUtils.isNotBlank(campaign.getName())) {
                            builder.setCampaignName(campaign.getName());
                        }
                        if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                            builder.setCampaignTargetingType(campaign.getTargetingType());
                        }

                        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                            builder.setPortfolioId(campaign.getPortfolioId());
                            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                                builder.setPortfolioName(amazonAdPortfolio.getName());
                                builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    builder.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                    builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    builder.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            builder.setPortfolioName("-");
                        }
                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(spGroupMap) && spGroupMap.containsKey(amazonAdTargeting.getGroupId())) {
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdTargeting.getGroupId()).getName())) {
                            builder.setAdGroupName(spGroupMap.get(amazonAdTargeting.getGroupId()).getName());
                        }
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdTargeting.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(spGroupMap.get(amazonAdTargeting.getGroupId()).getAdGroupType());
                        }
                    }
                }

                if (Constants.SB.equalsIgnoreCase(amazonAdTargeting.getAtype())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(sbCampaignMap) && sbCampaignMap.containsKey(amazonAdTargeting.getCampaignId())) {
                        AmazonAdCampaignAll amazonSbAdCampaign = sbCampaignMap.get(amazonAdTargeting.getCampaignId());
                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getName())) {
                            builder.setCampaignName(amazonSbAdCampaign.getName());
                        }


                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getPortfolioId())) {
                            builder.setPortfolioId(amazonSbAdCampaign.getPortfolioId());
                            if (portfolioMap.containsKey(amazonSbAdCampaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSbAdCampaign.getPortfolioId());
                                builder.setPortfolioName(amazonAdPortfolio.getName());
                                builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, amazonSbAdCampaign.getShopId(), amazonSbAdCampaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    builder.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(amazonSbAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                    builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    builder.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            builder.setPortfolioName("-");
                        }

                    }

                    //广告组名称填充
                    if (MapUtils.isNotEmpty(sbGroupMap) && sbGroupMap.containsKey(amazonAdTargeting.getGroupId())) {
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdTargeting.getGroupId()).getName())) {
                            builder.setAdGroupName(sbGroupMap.get(amazonAdTargeting.getGroupId()).getName());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdTargeting.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(sbGroupMap.get(amazonAdTargeting.getGroupId()).getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdTargeting.getGroupId()).getAdFormat())) {
                            builder.setCampaignTargetingType(sbGroupMap.get(amazonAdTargeting.getGroupId()).getAdFormat());
                        }

                    }
                }

                if (Constants.SD.equalsIgnoreCase(amazonAdTargeting.getAtype())) {

                    //广告组名称填充
                    if (MapUtils.isNotEmpty(sdGroupMap) && sdGroupMap.containsKey(amazonAdTargeting.getGroupId())) {
                        if (StringUtils.isNotBlank(sdGroupMap.get(amazonAdTargeting.getGroupId()).getName())) {
                            builder.setAdGroupName(sdGroupMap.get(amazonAdTargeting.getGroupId()).getName());
                        }

                    }
                    if (MapUtils.isNotEmpty(sdCampaignAndGroupMap) && sdCampaignAndGroupMap.containsKey(amazonAdTargeting.getGroupId())) {
                        String campaignId = sdCampaignAndGroupMap.get(amazonAdTargeting.getGroupId());
                        if (StringUtils.isNotBlank(campaignId)) {
                            if (MapUtils.isNotEmpty(sdCampaignMap) && sdCampaignMap.containsKey(campaignId)) {
                                AmazonAdCampaignAll amazonSdAdCampaign = sdCampaignMap.get(campaignId);
                                if (StringUtils.isNotBlank(amazonSdAdCampaign.getName())) {
                                    builder.setCampaignName(amazonSdAdCampaign.getName());
                                }
                                if (StringUtils.isNotBlank(amazonSdAdCampaign.getTactic())) {
                                    builder.setCampaignTargetingType(amazonSdAdCampaign.getTactic());
                                }
                                builder.setCampaignId(campaignId);

                                if (StringUtils.isNotBlank(amazonSdAdCampaign.getPortfolioId())) {
                                    builder.setPortfolioId(amazonSdAdCampaign.getPortfolioId());
                                    if (portfolioMap.containsKey(amazonSdAdCampaign.getPortfolioId())) {
                                        AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSdAdCampaign.getPortfolioId());
                                        builder.setPortfolioName(amazonAdPortfolio.getName());
                                        builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                    } else {
                                        AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(puid, amazonSdAdCampaign.getShopId(), amazonSdAdCampaign.getPortfolioId());
                                        if (amazonAdPortfolio != null) {
                                            builder.setPortfolioName(amazonAdPortfolio.getName());
                                            portfolioMap.put(amazonSdAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                            builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                        } else {
                                            builder.setPortfolioName("广告组合待同步");
                                        }
                                    }
                                } else {
                                    builder.setPortfolioName("-");
                                }
                            }
                        }
                    }
                }
                list.add(builder.build());
            }
        }
        return voPage;
    }


    private AdHomeAggregateDataRpcVo getProductAggregateDataChainVo(AdHomeAggregateDataRpcVo aggregateDataVo,AdHomeAggregateDataRpcVo compareAggregateDataVo){
        AdHomeAggregateDataRpcVo.Builder builder = aggregateDataVo.toBuilder();
        if (compareAggregateDataVo == null) {
            return builder
                    .setAcosCompare("0")
                    .setClicksCompare(0)
                    .setCtrCompare("0")
                    .setCvrCompare("0")
                    .setImpressionsCompare(0)
                    .setAdCostCompare("0")
                    .setAdSaleCompare("0")
                    .setAdOrderNumCompare(0)
                    .setAdCostPerClickCompare("0")
                    .setAcosChain("0")
                    .setClicksChain("0")
                    .setCtrChain("0")
                    .setCvrChain("0")
                    .setImpressionsChain("0")
                    .setAdCostChain("0")
                    .setAdSaleChain("0")
                    .setAdOrderNumChain("0")
                    .setAdCostPerClickChain("0")
                    .build();
        }

        return builder
                .setAcosCompare(compareAggregateDataVo.getAcos())
                .setClicksCompare(compareAggregateDataVo.getClicks().getValue())
                .setCtrCompare(compareAggregateDataVo.getCtr())
                .setCvrCompare(compareAggregateDataVo.getCvr())
                .setImpressionsCompare(compareAggregateDataVo.getImpressions().getValue())
                .setAdCostCompare(compareAggregateDataVo.getAdCost())
                .setAdSaleCompare(compareAggregateDataVo.getAdSale())
                .setAdOrderNumCompare(compareAggregateDataVo.getAdOrderNum().getValue())
                .setAdCostPerClickCompare(compareAggregateDataVo.getAdCostPerClick())
                .setAcosChain(calculationChain(new BigDecimal(aggregateDataVo.getAcos()),new BigDecimal(compareAggregateDataVo.getAcos())))
                .setClicksChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getClicks().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getClicks().getValue())))
                .setCtrChain(calculationChain(new BigDecimal(aggregateDataVo.getCtr()),new BigDecimal(compareAggregateDataVo.getCtr())))
                .setCvrChain(calculationChain(new BigDecimal(aggregateDataVo.getCvr()),new BigDecimal(compareAggregateDataVo.getCvr())))
                .setImpressionsChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getImpressions().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getImpressions().getValue())))
                .setAdCostChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCost()),new BigDecimal(compareAggregateDataVo.getAdCost())))
                .setAdSaleChain(calculationChain(new BigDecimal(aggregateDataVo.getAdSale()),new BigDecimal(compareAggregateDataVo.getAdSale())))
                .setAdOrderNumChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getAdOrderNum().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getAdOrderNum().getValue())))
                .setAdCostPerClickChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCostPerClick()),new BigDecimal(compareAggregateDataVo.getAdCostPerClick())))
                .build();
    }

    private String calculationChain(BigDecimal data, BigDecimal dataCompare) {
        if (dataCompare.compareTo(BigDecimal.ZERO) == 0 && data.compareTo(BigDecimal.ZERO) > 0) {
            return "100.0000";
        }
        return MathUtil.divideByZero(MathUtil.subtract(data, dataCompare).multiply(BigDecimal.valueOf(100)), dataCompare, 2).toString();
    }


    private void dealCompareParam(AdProductPageParam param, AdProductPageParam compareParam) {
        LocalDate startDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        String compareStartDate = startDate.minus(between).minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        String compareEndDate = startDate.minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));

        BeanUtils.copyProperties(param, compareParam);

        compareParam.setStartDate(compareStartDate);
        compareParam.setEndDate(compareEndDate);
    }

}