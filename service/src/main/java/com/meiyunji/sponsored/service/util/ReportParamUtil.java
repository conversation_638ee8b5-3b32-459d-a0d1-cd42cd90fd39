package com.meiyunji.sponsored.service.util;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2023/11/14 10:28
 * @describe:
 */
@Slf4j
public class ReportParamUtil {

    public static String getTopOfSearchImpressionShare(BigDecimal maxTopImpressionShare, BigDecimal minTopImpressionShare) {
        //设置搜索词首页首位曝光量字段
        String topOfSearchIS;
        BigDecimal maxTopIs = Optional.ofNullable(maxTopImpressionShare)
                .map(i -> i.setScale(2, RoundingMode.HALF_UP)).orElse(null);
        BigDecimal minTopIs = Optional.ofNullable(minTopImpressionShare)
                .map(i -> i.setScale(2, RoundingMode.HALF_UP)).orElse(null);
        if (Objects.isNull(maxTopIs)) {
            return null;
        }
        topOfSearchIS = Optional.ofNullable(minTopIs).orElse(maxTopIs) + "~" + maxTopIs;
        return topOfSearchIS;
    }

    public static String getExportTopIS(String topImpressionShare) {
        if (StringUtils.isEmpty(topImpressionShare) || "-".equals(topImpressionShare)) {
            return "-";
        }
        BigDecimal maxVal = BigDecimal.ZERO;
        BigDecimal minVal = BigDecimal.ZERO;
        try {
            String[] valArray = topImpressionShare.split("~");
            String max = valArray[valArray.length - 1];
            maxVal = new BigDecimal(max);

            String min = valArray[0];
            minVal = new BigDecimal(min);

            if (maxVal.compareTo(minVal) == 0) {
                return String.valueOf(minVal) + "%";
            } else {
                return String.valueOf(min) + "%" + "~" + String.valueOf(maxVal) + "%";
            }
        } catch (Exception e) {
            log.error("format export max value error", e);
        }
        return "-";
    }

    public static BigDecimal[] unTopOfSearchImpressionShare(String s) {
        if (StringUtils.isBlank(s)) {
            return new BigDecimal[2];
        }
        List<BigDecimal> list = Arrays.stream(s.split("~")).filter(StringUtils::isNotBlank).map(BigDecimal::new).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(list)) {
            return new BigDecimal[2];
        }
        BigDecimal first = list.get(0);
        BigDecimal second = first;
        if (list.size() >= 2) {
            second = list.get(1);
        }
        return new BigDecimal[]{first, second};
    }

    /**
     * 判断是否有交集
     */
    public static boolean intersectionOrNot(BigDecimal sourceMin, BigDecimal sourceMax, BigDecimal targetMin, BigDecimal targetMax) {
        // source或者target不能为空
        if ((sourceMin == null && sourceMax == null) || (targetMin == null && targetMax == null)) {
            return false;
        }
        // sourceMax或者targetMin为空表示不需要比较, sourceMax不能小于targetMin
        if (sourceMax != null && targetMin != null && sourceMax.compareTo(targetMin) < 0) {
            return false;
        }
        // targetMax或者sourceMin为空表示不需要比较, targetMax不能小于sourceMin
        if (targetMax != null && sourceMin != null && targetMax.compareTo(sourceMin) < 0) {
            return false;
        }
        return true;
    }

}