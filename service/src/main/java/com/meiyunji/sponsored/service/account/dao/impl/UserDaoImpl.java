package com.meiyunji.sponsored.service.account.dao.impl;

import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.enums.UserStatusEnum;
import com.meiyunji.sponsored.service.post.response.GetUserInfoResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.*;

@Slf4j
@Repository
public class UserDaoImpl extends BaseDaoImpl<User> implements IUserDao {
    @Override
    public List<User> listAllMainUser() {
        String sql = "SELECT * FROM t_user WHERE puid = id  AND status = 1;";
        return getJdbcTemplate().query(sql, getMapper());
    }

    @Override
    public List<Integer> getAllPuid() {
        String sql = "select distinct puid from t_user";
        return getJdbcTemplate().queryForList(sql, Integer.class);
    }

    @Override
    public List<User> listByIds(Integer puid, List<Integer> ids) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder();
        builder.equalTo("puid", puid)
                .in("id", ids.toArray());

        return super.listByCondition(builder.build());
    }

    /**
     * 查询所有免费用户
     * 根据条件planType = 0作为判断条件
     * @return
     */
    @Override
    public List<User> listAllFreeUser() {
        String sql = "SELECT * FROM t_user WHERE puid = id  AND status = 1 AND plan_type = 0";
        return getJdbcTemplate().query(sql, getMapper());
    }

    @Override
    public List<User> batchGetUser(Date startTime, Date endTime, int baseId, int limit) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id, puid FROM t_user WHERE id > ? ");
        argList.add(baseId);
        if (startTime != null && endTime != null) {
            sql.append(" and create_time >= ? and create_time <= ? ");
            argList.add(startTime);
            argList.add(endTime);
        }
        sql.append("order by id asc limit ? ");
        argList.add(limit);

        log.info("batchGetUser : {}", SqlStringUtil.exactSql(sql.toString(), argList));
        return getJdbcTemplate().query(sql.toString(), getMapper(), argList.toArray());
    }

    @Override
    public List<User> batchGetMainUser(Date startTime, Date endTime, int baseId, int limit) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id, puid FROM t_user WHERE id > ?  and id = puid ");
        argList.add(baseId);
        if (startTime != null && endTime != null) {
            sql.append(" and create_time >= ? and create_time <= ? ");
            argList.add(startTime);
            argList.add(endTime);
        }
        sql.append("order by id asc limit ? ");
        argList.add(limit);
        return getJdbcTemplate().query(sql.toString(), getMapper(), argList.toArray());
    }
    @Override
    public List<User> batchGetUserNOTRepatePuid(Date startTime, Date endTime, int baseId, int limit) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id, puid FROM t_user WHERE id > ? ");
        argList.add(baseId);
        if (startTime != null && endTime != null) {
            sql.append(" and create_time >= ? and create_time <= ? ");
            argList.add(startTime);
            argList.add(endTime);
        }
        sql.append(" and id = puid order by id asc limit ? ");
        argList.add(limit);

        return getJdbcTemplate().query(sql.toString(), getMapper(), argList.toArray());
    }

    @Override
    public List<User> listNoDeleteUser(int puid) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .notEqualTo("status", UserStatusEnum.DELETE.getCode())
                .build();
        return listByCondition(condition);
    }

    @Override
    public List<User> listNoDeleteUserByCreateTime(Date createTime) {
        ConditionBuilder condition = new ConditionBuilder.Builder()
                .notEqualTo("status", UserStatusEnum.DELETE.getCode())
                .greaterThan("create_time", createTime)
                .orderBy("create_time")
                .build();
        return listByCondition(condition);
    }

    @Override
    public List<User> getUserIdByName(Integer puid, String searchType, List<String> userName) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id, puid FROM t_user WHERE puid = ? ");
        argList.add(puid);
        if (userName != null && SearchTypeEnum.BLUR.getValue().equals(searchType)) {
            //  模糊查询
            sql.append(" and nickname like ? ");
            argList.add("%" + userName.get(0) + "%");
        } else {
            // 精确查询(默认)
            sql.append(SqlStringUtil.dealInList("nickname", userName, argList));
        }
        return getJdbcTemplate().query(sql.toString(), getMapper(), argList.toArray());
    }

    @Override
    public List<GetUserInfoResponse.UserInfo> getUserInfoByIds(Integer puid, Set<Integer> uids) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id uid, nickname name FROM t_user WHERE puid = ? ");
        argList.add(puid);
        sql.append(SqlStringUtil.dealInList("id", new ArrayList<>(uids), argList));
        return getJdbcTemplate().query(sql.toString(), argList.toArray(), new BeanPropertyRowMapper<>(GetUserInfoResponse.UserInfo.class));
    }

    @Override
    public List<User> listAllUserInfo(Integer puid, String searchValue, String searchType, List<Integer> postUid) {
        List<Object> argList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT id, nickname FROM t_user WHERE puid = ? ");
        argList.add(puid);
        sql.append(SqlStringUtil.dealInList("id", postUid, argList));
        if (searchValue != null && SearchTypeEnum.EXACT.getValue().equals(searchType)) {
            //  模糊查询
            sql.append(" and nickname = ? ");
            argList.add(searchValue);
        } else {
            // 模糊查询(默认)
            sql.append(" and nickname like ? ");
            argList.add("%" + searchValue + "%");
        }
        return getJdbcTemplate().query(sql.toString(), getMapper(), argList.toArray());
    }

}