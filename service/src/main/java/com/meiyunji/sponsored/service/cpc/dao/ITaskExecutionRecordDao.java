package com.meiyunji.sponsored.service.cpc.dao;

import com.meiyunji.sponsored.common.springjdbc.IAdBaseDao;
import com.meiyunji.sponsored.service.cpc.po.TaskExecutionRecord;

import java.text.ParseException;
import java.util.Date;
import java.util.List;

/**TaskExecutionRecordDao
 * <AUTHOR>
 * intro:1.根据任务类型来获取数据2.插入，输入puid，shopid和tasktype，如果数据已经存在，则更新
 */

public interface ITaskExecutionRecordDao extends IAdBaseDao<TaskExecutionRecord> {

    List<TaskExecutionRecord> getTaskExecutionRecordByTaskType(List<Integer>shopIds,String taskType);

    void insertOrUpdateTaskExecutionRecordByTaskType(TaskExecutionRecord record) throws ParseException;

    List<Integer> getTaskExecutionRecordByTaskType(String taskType);

    List<TaskExecutionRecord> getByTaskType(String taskType);

    TaskExecutionRecord getOneByTaskType(String taskType);

    int updateSyncTimeByTaskType(String taskType, Date updateTime);
}
