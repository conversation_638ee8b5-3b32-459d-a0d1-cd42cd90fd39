package com.meiyunji.sponsored.service.cpc.dao.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.SearchTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.springjdbc.LogicType;
import com.meiyunji.sponsored.common.util.SelectBuilder;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.autoRule.vo.AdProductAutoRuleParam;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroup;
import com.meiyunji.sponsored.service.export.dto.DownloadCenterBaseDataBO;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdGroupStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProductDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.AdProductByGroupVo;
import com.meiyunji.sponsored.service.cpc.vo.AdProductPageParam;
import com.meiyunji.sponsored.service.cpc.vo.CpcTaskSearchDto;
import com.meiyunji.sponsored.service.cpc.vo.GroupPageParam;
import com.meiyunji.sponsored.service.doris.dao.IOdsProductDao;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListDto;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.AsinListReqVo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.vo.InitAsinVo;
import com.meiyunji.sponsored.service.strategy.enums.ProductSearchFieldEnum;
import com.meiyunji.sponsored.service.strategy.vo.AdProductStrategyParam;
import com.meiyunji.sponsored.service.strategy.vo.AdProductStrategyVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * AmazonAdProduct
 *
 * <AUTHOR>
 */
@Repository
public class AmazonAdProductDaoImpl extends BaseShardingDaoImpl<AmazonAdProduct> implements IAmazonAdProductDao {

    @Autowired
    private IOdsProductDao odsProductDao;

    @Override
    public void insertOnDuplicateKeyUpdate(Integer puid, List<AmazonAdProduct> amazonAdProducts) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_product` (`unique_key`,`puid`,`shop_id`,`marketplace_id`,`ad_id`,")
                .append(" `ad_group_id`,`dxm_group_id`,`campaign_id`,`profile_id`,`sku`,`asin`,`state`,`serving_status`,`create_id`,`update_id`,`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdProduct amazonAdProduct : amazonAdProducts) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(amazonAdProduct.getUniqueKey());
            argsList.add(amazonAdProduct.getPuid());
            argsList.add(amazonAdProduct.getShopId());
            argsList.add(amazonAdProduct.getMarketplaceId());
            argsList.add(amazonAdProduct.getAdId());
            argsList.add(amazonAdProduct.getAdGroupId());
            argsList.add(amazonAdProduct.getDxmGroupId());
            argsList.add(amazonAdProduct.getCampaignId());
            argsList.add(amazonAdProduct.getProfileId());
            argsList.add(amazonAdProduct.getSku());
            argsList.add(amazonAdProduct.getAsin());
            argsList.add(amazonAdProduct.getState());
            argsList.add(amazonAdProduct.getServingStatus());
            argsList.add(amazonAdProduct.getCreateId());
            argsList.add(amazonAdProduct.getUpdateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `state`=values(state),`serving_status`=values(serving_status),`sku`=values(sku),`asin`=values(asin)");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());

    }

    @Override
    public Integer countByAdGroupId(int puid, Integer shopId, String adGroupId) {
        String sql = "SELECT COUNT(*) FROM `t_amazon_ad_product` WHERE puid=? AND shop_id=? AND ad_group_id=?";
        return getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, shopId, adGroupId}, Integer.class);
    }

    @Override
    public List<String> getAdIds(Integer puid, Integer shopId, String marketPlaceId, List<String> onlineAdId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketPlaceId)
                .inStrList("ad_id", onlineAdId.toArray(new String[]{}))
                .build();
        return listDistinctFieldByCondition(puid, "ad_id", builder, String.class);
    }

    @Override
    public void updateList(Integer puid, List<AmazonAdProduct> list) {
        StringBuilder sql = new StringBuilder("update `t_amazon_ad_product` set `state`=?,")
                .append("`update_time` = now(),update_id=? where puid=? and shop_id=? and campaign_id=? and ad_id=?");
        List<Object[]> batchArgs = Lists.newArrayList();
        Object[] batchArg;
        for (AmazonAdProduct amazonAdProduct : list) {
            batchArg = new Object[]{
                    amazonAdProduct.getState(),
                    amazonAdProduct.getUpdateId(),
                    amazonAdProduct.getPuid(),
                    amazonAdProduct.getShopId(),
                    amazonAdProduct.getCampaignId(),
                    amazonAdProduct.getAdId()
            };
            batchArgs.add(batchArg);
        }
        getJdbcTemplate(puid).batchUpdate(sql.toString(), batchArgs);

    }

    @Override
    public String getProfileId(int puid, Integer shopId, String marketPlaceId, String adId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_Id", adId)
                .equalTo("marketplace_id", marketPlaceId)
                .build();
        return getByCondition(puid, "profile_id", String.class, builder);
    }

    @Override
    public void updateState(int puid, Integer shopId, String adId, String state, int updateId) {
        String sql = "update t_amazon_ad_product set state=?,update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_Id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{state, updateId, puid, shopId, adId});

    }

    @Override
    public Page getPageList(Integer puid, AdProductPageParam param, Page page) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder("select * from t_amazon_ad_product ");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_product ");
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", groupIds, argsList));
        }
        //广告标签查询
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            whereSql.append(SqlStringUtil.dealInList("ad_id", param.getAdIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                argsList.add(AmazonAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, argsList));
            }

        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("asin", param.getListSearchValueNew(), argsList));
                } else {
                    whereSql.append(" and asin = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("sku", param.getListSearchValueNew(), argsList));
                } else {
                    whereSql.append(" and sku like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim() + "%");
                }

            }

        }


        sql.append(whereSql);
        countSql.append(whereSql);
        sql.append(" order by data_update_time desc, id desc ");

        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args, AmazonAdProduct.class);
    }

    @Override
    public List<String> getAsinByGroup(int puid, Integer shopId, String campaignId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return listDistinctFieldByCondition(puid, "asin", builder, String.class);
    }

    @Override
    public List<AmazonAdProduct> getListByGroup(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .build();
        return listByCondition(puid, builder);
    }


    @Override
    public List<String> checkRepeatedSkus(Integer puid, Integer shopId, String campaignId, String adGroupId, List<String> skus) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .in("sku", skus.toArray())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listDistinctFieldByCondition(puid, "sku", builder.build(), String.class);
    }



    @Override
    public List<String> checkRepeatedAsins(Integer puid, Integer shopId, String campaignId, String adGroupId, List<String> asins) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("ad_group_id", adGroupId)
                .in("asin", asins.toArray())
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listDistinctFieldByCondition(puid, "asin", builder.build(), String.class);
    }

    @Override
    public AmazonAdProduct getByAdId(int puid, Integer shopId, String marketplaceId, String adId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_id", adId)
                .equalTo("marketplace_id", marketplaceId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public AmazonAdProduct getByAdId(int puid, Integer shopId, String adId) {
        if (shopId == null || StringUtils.isBlank(adId)) {
            return null;
        }
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_id", adId)
                .build();
        return getByCondition(puid, builder);
    }

    @Override
    public Page pageListForTask(int puid, CpcTaskSearchDto dto, Page page) {
        StringBuilder selectSql = new StringBuilder("select ad_id,ad_group_id,campaign_id,sku,asin,create_time from t_amazon_ad_product");
        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_product ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and state in ('enabled','paused') and ad_id is not null");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        if (StringUtils.isNotEmpty(dto.getCampaignId())) {
            whereSql.append(" and campaign_id = ? ");
            argsList.add(dto.getCampaignId());
        }
        if (StringUtils.isNotEmpty(dto.getGroupId())) {
            whereSql.append(" and ad_group_id = ? ");
            argsList.add(dto.getGroupId());
        }
        if (Constants.ITEM_TYPE_PRODUCT.equals(dto.getSearchField()) && StringUtils.isNotEmpty(dto.getSearchValue())) {
            String searchValue = SqlStringUtil.dealLikeSql(dto.getSearchValue());
            whereSql.append(" and (sku like ? or asin like ?) ");
            argsList.add(searchValue + "%");
            argsList.add(searchValue + "%");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by id desc");
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdProduct.class);
    }

    @Override
    public int count(Integer puid, Integer shopId) {
        String sql = "select count(*) from t_amazon_ad_product where puid=? and shop_id=?";
        return getJdbcTemplate(puid).queryForObject(sql, new Object[]{puid, shopId}, Integer.class);
    }

    @Override
    public Page<AmazonAdProduct> pageList(Integer puid, AdProductPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            builder.and()
                    .leftBracket()
                    .like(LogicType.EPT, "asin", param.getSearchValue())
                    .or()
                    .like(LogicType.EPT, "sku", param.getSearchValue())
                    .rightBracket();
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        String orderBySql = " order by update_time desc";

        return page(puid, param.getPageNo(), param.getPageSize(), orderBySql, builder.build());
    }

    @Override
    public List<AmazonAdProduct> listByCondition(Integer puid, AdProductPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId())
                .equalTo("ad_group_id", param.getGroupId());

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {
            builder.and()
                    .leftBracket()
                    .like(LogicType.EPT, "asin", param.getSearchValue())
                    .or()
                    .like(LogicType.EPT, "sku", param.getSearchValue())
                    .rightBracket();
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            builder.equalTo("state", param.getStatus());
        }

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdProduct> listValidByGroupId(Integer puid, Integer shopId, String adGroupId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdProduct> listValidByGroupIds(Integer puid, Integer shopId, List<String> adGroupIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<AmazonAdProduct> listValidByGroupIdsAndShopIds(Integer puid, List<Integer> shopIdList, List<String> adGroupIds) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .inStrList("ad_group_id", adGroupIds.toArray(new String[]{}))
                .in("state", new Object[]{CpcStatusEnum.enabled.name(), CpcStatusEnum.paused.name()});

        return listByCondition(puid, builder.build());
    }

    @Override
    public List<String> listSkus(Integer puid, Integer shopId, String adGroupId, List<String> stateList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("ad_group_id", adGroupId)
                .in("`state`", stateList.toArray())
                .build();
        return listDistinctFieldByCondition(puid, "sku", builder, String.class);
    }

    @Override
    public Map<String, Integer> statCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .groupBy("ad_group_id").build();

        String sql = "select ad_group_id adGroupId, count(*) c from t_amazon_ad_product where " + conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, conditionBuilder.getValues())
                .stream().collect(Collectors.toMap(e -> e.get("adGroupId").toString(), e -> Integer.parseInt(e.get("c").toString())));

    }

    @Override
    public List<String> getListAsinByCampaignIdAndGroupId(Integer puid, Integer shopId, String campaignId, String groupId) {
        String sql = " SELECT DISTINCT(asin) FROM `t_amazon_ad_product` WHERE puid = ? AND shop_id = ? AND campaign_id = ?";
        List<String> list;

        if (StringUtils.isNotBlank(groupId)) {
            sql += " AND ad_group_id = ? AND state = 'enabled'";
            list = getJdbcTemplate(puid).queryForList(sql, String.class, puid, shopId, campaignId, groupId);
        } else {
            sql += " AND state = 'enabled'";
            list = getJdbcTemplate(puid).queryForList(sql, String.class, puid, shopId, campaignId);
        }
        return list;
    }

    @Override
    public List<AmazonAdProduct> getList(Integer puid, AdProductPageParam param) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", param.getShopId());

        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            List<String> campaignIds = StringUtil.splitStr(param.getCampaignId());
            builder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            builder.inStrList("campaign_id", param.getCampaignIdList().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {
            List<String> groupIds = StringUtil.splitStr(param.getGroupId());
            builder.inStrList("ad_group_id", groupIds.toArray(new String[]{}));
        }

        //标签查询
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            builder.inStrList("ad_id", param.getAdIds().toArray(new String[]{}));
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {

            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    builder.in("asin", param.getListSearchValueNew().toArray());
                } else {
                    builder.equalTo("asin", param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    builder.in("sku", param.getListSearchValueNew().toArray());
                } else {
                    builder.like("sku", param.getListSearchValueNew().get(0).trim());
                }
            } else if ("parentAsin".equalsIgnoreCase(param.getSearchField())) {
                List<String> asin = odsProductDao.listByParentAsin(param.getPuid(), param.getShopId(), param.getListSearchValueNew());
                if (asin.size() == 0) {
                    asin.add("-1");
                }
                builder.in("asin", asin.toArray());
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            builder.inStrList("state", statusList.toArray(new String[]{}));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                builder.equalTo("serving_status", AmazonAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                builder.inStrList("serving_status", list.toArray(new String[]{}));
            }

        }

        builder.orderByDesc("data_update_time", "id");
        builder.limit(Constants.TOTALSIZELIMIT);  // 限制10万

        return listByCondition(puid, builder.build());
    }

    @Override
    public void updateDataUpdateTime(Integer puid, Integer shopId, String adId, LocalDate localDate) {
        String sql = "update t_amazon_ad_product set data_update_time=?,update_time=now() where puid = ? and shop_id = ? and `ad_id`=?";
        getJdbcTemplate(puid).update(sql, new Object[]{localDate, puid, shopId, adId});
    }

    private SelectBuilder getSelectSql(Integer puid, AdProductPageParam param) {
        SelectBuilder selectBuilder = new SelectBuilder();

        StringBuilder spSql = new StringBuilder();
        //未指定类型 或 指定sp
        if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {

            spSql.append("  'sp' as type ,`id`,  `puid`, `shop_id`, `marketplace_id`,  `campaign_id`,`ad_group_id`, `ad_id`, `state`, `sku`,");
            spSql.append("  `asin`,  `create_id`, `update_id`, `create_time`, `update_time` from t_amazon_ad_product  where ");

            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());
            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            }
            //关键搜索
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue()) && "name".equalsIgnoreCase(param.getSearchField())) {
                builder.and()
                        .leftBracket()
                        .like(LogicType.EPT, "asin", param.getSearchValue())
                        .or()
                        .like(LogicType.EPT, "sku", param.getSearchValue())
                        .rightBracket();
            }
            //广告活动ID搜索
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.equalTo("campaign_id", param.getCampaignId());
            }
            //广告组ID搜索
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.equalTo("ad_group_id", param.getGroupId());
            }

            spSql.append(builder.build().getSql());

            selectBuilder.appendSql(spSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }


        //未指定类型 或 指定sd
        StringBuilder sdSql = new StringBuilder();
        if (StringUtils.isBlank(param.getType()) || Constants.SD.equalsIgnoreCase(param.getType())) {

            sdSql.append("  'sd' as type ,`id`,  `puid`, `shop_id`, `marketplace_id`,  `campaign_id`,`ad_group_id`, `ad_id`, `state`, `sku`,");
            sdSql.append("  `asin`,  `create_id`, `update_id`, `create_time`, `update_time` from t_amazon_ad_product_sd  where ");


            ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                    .equalTo("puid", puid)
                    .equalTo("shop_id", param.getShopId());

            //状态查询
            if (StringUtils.isNotBlank(param.getStatus())) {
                builder.equalTo("state", param.getStatus());
            }
            //关键搜索
            if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue()) && "name".equalsIgnoreCase(param.getSearchField())) {
                builder.and()
                        .leftBracket()
                        .like(LogicType.EPT, "asin", param.getSearchValue())
                        .or()
                        .like(LogicType.EPT, "sku", param.getSearchValue())
                        .rightBracket();
            }
            //广告活动ID搜索
            if (StringUtils.isNotBlank(param.getCampaignId())) {
                builder.equalTo("campaign_id", param.getCampaignId());
            }
            //广告组ID搜索
            if (StringUtils.isNotBlank(param.getGroupId())) {
                builder.equalTo("ad_group_id", param.getGroupId());
            }
            sdSql.append(builder.build().getSql());

            if (StringUtils.isNotBlank(spSql.toString())) {
                selectBuilder.appendSql(" union all select ");
            }
            selectBuilder.appendSql(sdSql.toString());
            selectBuilder.appendValue(builder.build().getValues());
        }
        return selectBuilder;
    }

    @Override
    public List<String> getAsinByCampaignId(int puid, Integer shopId, String campaignId) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("campaign_id", campaignId)
                .equalTo("state", "enabled")
                .build();
        return listDistinctFieldByCondition(puid, "asin", builder, String.class);
    }

    @Override
    public List<String> getArchivedItems(Integer puid, Integer shopId) {
        String sql = "select ad_id from t_amazon_ad_product where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("state", "archived")
                .build();
        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());
    }

    @Override
    public List<String> getUpdateAfterReportSyncTimeItems(Integer puid, Integer shopId, LocalDateTime syncAt) {
        String sql = "select ad_id from t_amazon_ad_product where ";
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThan("update_time", syncAt)
                .build();
        sql += conditionBuilder.getSql();
        return getJdbcTemplate(puid).queryForList(sql, String.class, conditionBuilder.getValues());

    }

    @Override
    public List<String> getAdIdsByProduct(int puid, AdProductPageParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder sql = new StringBuilder(" select c.ad_id from t_amazon_ad_product c ");
        StringBuilder whereSql = new StringBuilder(" where c.puid = ? ");
        argsList.add(puid);

        if (param.getShopId() != null) {
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }
        //广告活动ID查询
        if (StringUtils.isNotBlank(param.getCampaignId())) {
            whereSql.append(" and c.campaign_id = ? ");
            argsList.add(param.getCampaignId());
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }
        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            whereSql.append(" and c.ad_group_id = ? ");
            argsList.add(param.getGroupId());
        }
        //标签筛选
        if (CollectionUtils.isNotEmpty(param.getAdIds())) {
            whereSql.append(SqlStringUtil.dealInList("c.ad_id", param.getAdIds(), argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("c.asin", param.getListSearchValueNew(), argsList));
                } else {
                    whereSql.append(" and c.asin = ? ");
                    argsList.add(param.getListSearchValueNew().get(0).trim());
                }

            } else if ("msku".equalsIgnoreCase(param.getSearchField())) {
                if (param.getListSearchValueNew().size() > 1) {
                    whereSql.append(SqlStringUtil.dealInList("c.sku", param.getListSearchValueNew(), argsList));
                } else {
                    whereSql.append(" and c.sku like ? ");
                    argsList.add("%" + param.getListSearchValueNew().get(0).trim() + "%");
                }

            }
        }
        //状态查询
        if (StringUtils.isNotBlank(param.getStatus())) {
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }


        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and c.serving_status = ? ");
                argsList.add(AmazonAdProduct.servingStatusEnum.AD_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("c.serving_status", list, argsList));
            }

        }

        sql.append(whereSql);
        List<String> adIds = getJdbcTemplate(puid).queryForList(sql.toString(), argsList.toArray(), String.class);
        return adIds;
    }

    @Override
    public Integer statSumCountByAdGroup(Integer puid, Integer shopId, List<String> status, List<String> adGroupIds) {
        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("ad_group_id", adGroupIds.toArray())
                .in("state", status.toArray())
                .build();

        String sql = "select  count(*) c from t_amazon_ad_product where " + conditionBuilder.getSql();

        List<Integer> list = getJdbcTemplate(puid).queryForList(sql, Integer.class, conditionBuilder.getValues());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0;
    }

    @Override
    public Integer statSumCountGroupProduct(Integer puid, Integer shopId, List<String> status, GroupPageParam param) {
        StringBuilder builder = new StringBuilder("select count(*) c from t_amazon_ad_product where puid = ? and shop_id = ? ");
        List<Object> args = Lists.newArrayList(puid, shopId);
        builder.append(SqlStringUtil.dealInList("state", status, args));
        builder.append(" and ad_group_id in ( ").append(getGroupPageSql(puid, param, args)).append(" )");
        List<Integer> list = getJdbcTemplate(puid).queryForList(builder.toString(), Integer.class, args.toArray());
        return CollectionUtils.isNotEmpty(list) ? list.get(0) : 0;

    }

    @Override
    public List<AmazonAdProduct> getByAdIds(int puid, Integer shopId, List<String> adIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .inStrList("ad_id", adIds.toArray(new String[0])).build());
    }

    @Override
    public List<AmazonAdProduct> getByAdIds(int puid, List<Integer> shopIds, List<String> adIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inIntList("shop_id", shopIds.toArray(new Integer[0]))
                .inStrList("ad_id", adIds.toArray(new String[0])).build());
    }

    @Override
    public List<AmazonAdProduct> getByAdIds(int puid, Integer shopId, String marketplaceId, List<String> adIds) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .inStrList("ad_id", adIds.toArray(new String[0])).build());
    }

    private String getGroupPageSql(Integer puid, GroupPageParam param, List<Object> args) {
        StringBuilder selectSql = new StringBuilder("select ad_group_id from t_amazon_ad_group ");


        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        args.add(puid);

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and shop_id = ? ");
            args.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getCampaignId()), args));
        }
        if (StringUtils.isNotBlank(param.getMultiCampaignId())) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", StringUtil.splitStr(param.getMultiCampaignId()), args));
        }

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", param.getCampaignIdList(), args));
        }

        if (CollectionUtils.isNotEmpty(param.getGroupIds())) {  // 广告标签
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", param.getGroupIds(), args));
        }

        // 广告策略筛选
        if(CollectionUtil.isNotEmpty(param.getAdStrategyTypeList())) {
            String subSql = AdGroupStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(), args, "ad_group_id", false);
            if(StringUtils.isNotEmpty(subSql)){
                whereSql.append(subSql);
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            if (StringUtils.isNotBlank(param.getSearchType()) && SearchTypeEnum.EXACT.getValue().equals(param.getSearchType())) {
                whereSql.append(" and name = ? ");
                args.add(param.getSearchValue().trim());
            } else {
                whereSql.append(" and name like ? ");
                args.add("%" + param.getSearchValue().trim() + "%");
            }
        }

        if (CollectionUtils.isNotEmpty(param.getSearchValueList())) {
            whereSql.append(SqlStringUtil.dealInList("lower(name)", param.getSearchValueList(), args));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("state", statusList, args));
        }
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅展示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                whereSql.append(" and serving_status = ? ");
                args.add(AmazonAdGroup.servingStatusEnum.AD_GROUP_STATUS_ENABLED.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                whereSql.append(SqlStringUtil.dealInList("serving_status", list, args));
            }
        }
        if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索
            if (param.getBidMin() != null) {   //默认竞价
                whereSql.append(" and default_bid >= ? ");
                args.add(param.getBidMin());
            }
            if (param.getBidMax() != null) {
                whereSql.append(" and default_bid <= ? ");
                args.add(param.getBidMax());
            }
        }
        selectSql.append(whereSql);
        return selectSql.toString();
    }

    /**
     * 查询sp，sd 广告产品表
     *
     * @param puid
     * @param shopId
     * @param type
     * @param value
     * @param adType sp，sd 不传查两张表
     * @return
     */
    @Override
    public List<String> getCampaignIdByAsinOrMsku(Integer puid, Integer shopId, String type, List<String> value, String adType, List<String> campaignIds) {
        String spSql = "select campaign_id from t_amazon_ad_product where ";
        String sbSql = "select campaign_id from t_amazon_sb_ads where ";
        String sdSql = "select campaign_id from t_amazon_ad_product_sd where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId);
        ConditionBuilder.Builder conditionBuilderForSb = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .equalToWithoutCheck("shop_id", shopId);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
            conditionBuilderForSb.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }
        if ("parentAsin".equalsIgnoreCase(type)) {
            value = odsProductDao.listByParentAsin(puid, shopId, value);
            type = "asin";
        }
        if (CollectionUtils.isEmpty(value)) {
            return new ArrayList<>();
        }
        adType = StringUtils.isNotBlank(adType) ? adType : "sp,sb,sd";
        List<String> asinList = new ArrayList<>();
        // 处理sb切msku情况
        if (adType.contains(Constants.SB) && !"asin".equalsIgnoreCase(type)) {
            // 查询为空 重置为
            List<String> skuList = odsProductDao.listBySku(puid, shopId, value);
            if (CollectionUtils.isNotEmpty(skuList)) {
                asinList = skuList;
            } else {
                asinList.add("-1");
            }
        }
        if ("asin".equalsIgnoreCase(type)) {
            if (value.size() > 1) {
                conditionBuilder.in("asin", value.toArray());
            } else {
                conditionBuilder.equalTo("asin", value.get(0).trim());
            }
            conditionBuilderForSb.regexp("asins", value.toArray());
        } else {
            if (value.size() > 1) {
                conditionBuilder.in("sku", value.toArray());
            } else {
                conditionBuilder.like("sku", value.get(0).trim());
            }
            if (CollectionUtils.isNotEmpty(asinList)) {
                conditionBuilderForSb.regexp("asins", asinList.toArray());
            }
        }
        ConditionBuilder build = conditionBuilder.build();
        ConditionBuilder buildForSb = conditionBuilderForSb.build();
        StringBuilder sql = new StringBuilder();
        String where = build.getSql();
        String whereSb = buildForSb.getSql();
        Object[] values = build.getValues();
        Object[] sbValues = buildForSb.getValues();
        List<Object> params = new ArrayList<>();
        List<String> adTypes = StringUtil.splitStr(adType);
        int size = adTypes.size();
        for (int i = 0; i < size; i++) {
            if (adTypes.get(i).equalsIgnoreCase(Constants.SP)) {
                sql.append(spSql).append(where);
                params.addAll(Arrays.asList(values));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
            if (adTypes.get(i).equalsIgnoreCase(Constants.SD)) {
                sql.append(sdSql).append(where);
                params.addAll(Arrays.asList(values));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
            if (adTypes.get(i).equalsIgnoreCase(Constants.SB)) {
                sql.append(sbSql).append(whereSb);
                params.addAll(Arrays.asList(sbValues));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, params.toArray());
    }

    @Override
    public List<String> getCampaignIdByAsinOrMskuShopList(Integer puid, List<Integer> shopId, List<String> marketPlaceId, String type, List<String> value, String adType, List<String> campaignIds) {
        String spSql = "select campaign_id from t_amazon_ad_product where ";
        String sbSql = "select campaign_id from t_amazon_sb_ads where ";
        String sdSql = "select campaign_id from t_amazon_ad_product_sd where ";
        Integer[] shopIdList = shopId.toArray(new Integer[0]);
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .inIntList("shop_id", shopIdList);
        ConditionBuilder.Builder conditionBuilderForSb = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .inIntList("shop_id", shopIdList);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
            conditionBuilderForSb.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }
        if ("parentAsin".equalsIgnoreCase(type)) {
            value = odsProductDao.listByParentAsin(puid, marketPlaceId, shopId, value);
            type = "asin";
        }
        if (CollectionUtils.isEmpty(value)) {
            return new ArrayList<>();
        }
        adType = StringUtils.isNotBlank(adType) ? adType : "sp,sb,sd";
        List<String> asinList = new ArrayList<>();
        // 处理sb切msku情况
        if (adType.contains(Constants.SB) && !"asin".equalsIgnoreCase(type)) {
            // 查询为空 重置为
            List<String> skuList = odsProductDao.listBySkuAllShop(puid, shopId, marketPlaceId, value);
            if (CollectionUtils.isNotEmpty(skuList)) {
                asinList = skuList;
            } else {
                asinList.add("-1");
            }
        }
        if ("asin".equalsIgnoreCase(type)) {
            if (value.size() > 1) {
                conditionBuilder.in("asin", value.toArray());
            } else {
                conditionBuilder.equalTo("asin", value.get(0).trim());
            }
            conditionBuilderForSb.regexp("asins", value.toArray());
        } else {
            if (value.size() > 1) {
                conditionBuilder.in("sku", value.toArray());
            } else {
                conditionBuilder.like("sku", value.get(0).trim());
            }
            if (CollectionUtils.isNotEmpty(asinList)) {
                conditionBuilderForSb.regexp("asins", asinList.toArray());
            }
        }
        ConditionBuilder build = conditionBuilder.build();
        ConditionBuilder buildForSb = conditionBuilderForSb.build();
        StringBuilder sql = new StringBuilder();
        String where = build.getSql();
        String whereSb = buildForSb.getSql();
        Object[] values = build.getValues();
        Object[] sbValues = buildForSb.getValues();
        List<Object> params = new ArrayList<>();
        List<String> adTypes = StringUtil.splitStr(adType);
        int size = adTypes.size();
        for (int i = 0; i < size; i++) {
            if (adTypes.get(i).equalsIgnoreCase(Constants.SP)) {
                sql.append(spSql).append(where);
                params.addAll(Arrays.asList(values));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
            if (adTypes.get(i).equalsIgnoreCase(Constants.SD)) {
                sql.append(sdSql).append(where);
                params.addAll(Arrays.asList(values));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
            if (adTypes.get(i).equalsIgnoreCase(Constants.SB)) {
                sql.append(sbSql).append(whereSb);
                params.addAll(Arrays.asList(sbValues));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, params.toArray());
    }

    @Override
    public List<String> getCampaignIdByProduct(Integer puid, List<Integer> shopIds, String type, List<String> value, List<String> adTypes, List<String> campaignIds) {
        String spSql = "select campaign_id from t_amazon_ad_product where ";
        String sbSql = "select campaign_id from t_amazon_sb_ads where ";
        String sdSql = "select campaign_id from t_amazon_ad_product_sd where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .in("shop_id", shopIds.toArray());
        ConditionBuilder.Builder conditionBuilderForSb = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid)
                .in("shop_id", shopIds.toArray());
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            conditionBuilder.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
            conditionBuilderForSb.inStrList("campaign_id", campaignIds.toArray(new String[]{}));
        }
        if (CollectionUtils.isEmpty(value)) {
            return new ArrayList<>();
        }
        if ("parentAsin".equalsIgnoreCase(type)) {
            value = odsProductDao.listByParentAsin(puid, shopIds, value);
            type = "asin";
        }
        if (CollectionUtils.isEmpty(value)) {
            return new ArrayList<>();
        }
        adTypes = CollectionUtils.isNotEmpty(adTypes) ? adTypes : Lists.newArrayList("sp", "sb", "sd");
        List<String> asinList = new ArrayList<>();
        // 处理sb切msku情况
        if (adTypes.contains(Constants.SB) && !"asin".equalsIgnoreCase(type)) {
            // 查询为空 重置为
            List<String> skuAsinList = odsProductDao.listAsinBySku(puid, shopIds, value);
            if (CollectionUtils.isNotEmpty(skuAsinList)) {
                asinList = skuAsinList;
            } else {
                asinList.add("-1");
            }
        }
        if ("asin".equalsIgnoreCase(type)) {
            if (value.size() > 1) {
                conditionBuilder.in("asin", value.toArray());
            } else {
                conditionBuilder.equalTo("asin", value.get(0).trim());
            }
            conditionBuilderForSb.regexp("asins", value.toArray());
        } else {
            if (value.size() > 1) {
                conditionBuilder.in("sku", value.toArray());
            } else {
                conditionBuilder.like("sku", value.get(0).trim());
            }
            if (CollectionUtils.isNotEmpty(asinList)) {
                conditionBuilderForSb.regexp("asins", asinList.toArray());
            }
        }
        ConditionBuilder build = conditionBuilder.build();
        ConditionBuilder buildForSb = conditionBuilderForSb.build();
        StringBuilder sql = new StringBuilder();
        String where = build.getSql();
        String whereSb = buildForSb.getSql();
        Object[] values = build.getValues();
        Object[] sbValues = buildForSb.getValues();
        List<Object> params = new ArrayList<>();
        int size = adTypes.size();
        for (int i = 0; i < size; i++) {
            if (adTypes.get(i).equalsIgnoreCase(Constants.SP)) {
                sql.append(spSql).append(where);
                params.addAll(Arrays.asList(values));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
            if (adTypes.get(i).equalsIgnoreCase(Constants.SD)) {
                sql.append(sdSql).append(where);
                params.addAll(Arrays.asList(values));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
            if (adTypes.get(i).equalsIgnoreCase(Constants.SB)) {
                sql.append(sbSql).append(whereSb);
                params.addAll(Arrays.asList(sbValues));
                if (i < size - 1) {
                    sql.append(" Union All ");
                }
            }
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, params.toArray());
    }

    @Override
    public List<String> getSkuList(Integer puid, Integer shopId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .isNull("row_ad_id");
        return listDistinctFieldByCondition(puid, "sku", builder.build(), String.class);
    }

    @Override
    public AmazonAdProduct getProductList(Integer puid, Integer shopId, String asin, String adId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("asin", asin)
                .equalTo("row_ad_id", adId)
                .limit(1);

        return getByCondition(puid, builder.build());
    }

    @Override
    public AmazonAdProduct getProductList(Integer puid, String asin, String adId) {
        ConditionBuilder.Builder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("asin", asin)
                .equalTo("row_ad_id", adId)
                .limit(1);

        return getByCondition(puid, builder.build());
    }

    @Override
    public void updateRowAdId(int puid, Integer shopId, String adId, String campaignId, String adGroupId, String sku) {
        String sql = "update t_amazon_ad_product set row_ad_id=?,update_time=now() where puid = ? and shop_id = ? and `campaign_id`=? and ad_group_id=? and sku = ?";
        getJdbcTemplate(puid).update(sql, new Object[]{adId, puid, shopId, campaignId, adGroupId, sku});
    }

    @Override
    public List<AmazonAdProduct> getBySkus(int puid, List<String> skus) {
        return listByCondition(puid, new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .inStrList("sku", skus.toArray(new String[0]))
                .build());
    }

    @Override
    public Page<AmazonAdProduct> getAdProductList(AdProductAutoRuleParam param, List<String> adGroupIds) {
        StringBuilder selectSql = new StringBuilder("select * from t_amazon_ad_product");
        StringBuilder countSql = new StringBuilder("select count(t.id) from ( ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and state in ('enabled','paused')");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
        }
        if (StringUtils.isNotBlank(param.getSearchValue())) {
            whereSql.append(" and ( sku like ? or asin like ?) ");
            argsList.add("%" + param.getSearchValue() + "%");
            argsList.add("%" + param.getSearchValue() + "%");
        }
        whereSql.append(" group by puid,shop_id,sku");
        selectSql.append(whereSql);
        countSql.append(selectSql).append(" ) t");
        return getPageResult(param.getPuid(), param.getPageNo(), param.getPageSize(), countSql.toString(), argsList.toArray(),
                selectSql.toString(), argsList.toArray(), AmazonAdProduct.class);
    }

    @Override
    public List<String> getGroupIdByAsin(Integer puid, Integer shopId, List<String> skuList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .in("sku", skuList.toArray())
                .build();
        return listDistinctFieldByCondition(puid, "ad_group_id", builder, String.class);
    }

    @Override
    public List<AmazonAdProductPerspectiveBO> productPerspectiveBoListByAsin(Integer puid, List<Integer> shopIdList, String marketPlaceId, String asin) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("asin", asin)
                .build();
        String sql = "select `campaign_id`,`ad_group_id`, `ad_id` from " + getJdbcHelper().getTable() + " where " + builder.getSql();
        return getJdbcTemplate(puid).query(sql, builder.getValues(), new BeanPropertyRowMapper<>(AmazonAdProductPerspectiveBO.class));
    }

    @Override
    public List<String> adIdListByAsin(Integer puid, List<Integer> shopIdList, String marketPlaceId, String asin) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("shop_id", shopIdList.toArray())
                .equalTo("marketplace_id", marketPlaceId)
                .equalTo("asin", asin)
                .build();
        return listDistinctFieldByCondition(puid, "ad_id", builder, String.class);
    }


    @Override
    public List<InitAsinVo> getAsinByPuidAndShopId(Integer puid, List<Integer> shopIdList, String marketplaceId) {
        StringBuilder builder = new StringBuilder();
        List<Object> args = new ArrayList<>(2);
        builder.append("select `shop_id`, `marketplace_id`, `asin` from ");
        builder.append(getJdbcHelper().getTable());
        builder.append(" where puid = ? ");
        args.add(puid);
        builder.append(" and shop_id in ( ").append(StringUtils.join(shopIdList, ",")).append(" ) ");
        if (StringUtils.isNotBlank(marketplaceId)) {
            builder.append(" and marketplace_id = ? ");
            args.add(marketplaceId);
        }
        builder.append(" and asin is not null");
        builder.append(" limit 1");
        return getJdbcTemplate(puid).query(builder.toString(), new BeanPropertyRowMapper<>(InitAsinVo.class), args.toArray());
    }

    @Override
    public Page<AsinListDto> getAsinByPuidAndShopId(AsinListReqVo reqVo) {
        StringBuilder selectSql = new StringBuilder("select distinct asin from ").append(this.getJdbcHelper().getTable());
        StringBuilder countSql = new StringBuilder("select count(distinct asin) from ").append(this.getJdbcHelper().getTable());
        StringBuilder whereSql = new StringBuilder(" where puid = ? and shop_id in (").append(StringUtils.join(reqVo.getShopIdList(), ",")).append(") ");
        whereSql.append(" and marketplace_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(reqVo.getPuid());
        argsList.add(reqVo.getMarketplaceId());
        if (StringUtils.isNotEmpty(reqVo.getSearchValue())) {
            whereSql.append(" and asin = ? ");
            argsList.add(reqVo.getSearchValue());
        } else {
            whereSql.append(" and asin is not null and asin != ''");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql);
        selectSql.append(" order by asin ");
        Object[] args = argsList.toArray();
        return this.getPageResultByClass(reqVo.getPuid(), reqVo.getPageNo(), reqVo.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AsinListDto.class);
    }


    @Override
    public List<AdProductByGroupVo> getAdProductByAdGroupIdList(Integer puid, Integer shopId, List<String> campaignIds, List<String> adGroupIds) {
        StringBuilder selectSql = new StringBuilder("select puid,shop_id, campaign_id,ad_group_id,GROUP_CONCAT(DISTINCT asin) asins from t_amazon_ad_product");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and state in ('enabled','paused')");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            whereSql.append(SqlStringUtil.dealInList("campaign_id", campaignIds, argsList));
            whereSql.append(" group by campaign_id");
        }
        if (CollectionUtils.isNotEmpty(adGroupIds)) {
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", adGroupIds, argsList));
            whereSql.append(" group by ad_group_id");

        }
        selectSql.append(whereSql);


        return getJdbcTemplate(puid).query(selectSql.toString(), new RowMapper<AdProductByGroupVo>() {
            @Override
            public AdProductByGroupVo mapRow(ResultSet re, int i) throws SQLException {
                AdProductByGroupVo dto = AdProductByGroupVo.builder()
                        .puid(re.getInt("puid"))
                        .campaignId(re.getString("campaign_id"))
                        .adGroupId(re.getString("ad_group_id"))
                        .shopId(re.getInt("shop_id"))
                        .asins(re.getString("asins"))
                        .build();
                return dto;
            }
        }, argsList.toArray());

    }

    @Override
    public int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("delete from ").append(this.getJdbcHelper().getTable()).append(" where puid = ? ");
        argsList.add(puid);
        sql.append("and shop_id = ?");
        argsList.add(shopId);
        if (limit != null) {
            sql.append(" limit ? ");
            argsList.add(limit);
        }
        return getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<AmazonAdProduct> getGpsProduct(Integer puid, List<String> campaignIdList, List<String> adGroupIdList, List<String> skuList) {
        ConditionBuilder builder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .in("campaign_id", campaignIdList.toArray())
                .in("ad_group_id", adGroupIdList.toArray())
                .in("sku", skuList.toArray())
                .build();
        return listByCondition(puid, builder);
    }

    @Override
    public void insertOnDuplicateKeyUpdateGps(Integer puid, List<AmazonAdProduct> amazonAdProducts) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_product` (`unique_key`,`puid`,`shop_id`,`marketplace_id`,`ad_id`,")
                .append(" `ad_group_id`,`dxm_group_id`,`campaign_id`,`profile_id`,`sku`,`asin`,`state`,`serving_status`, row_ad_id, `create_id`,`update_id`,`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdProduct amazonAdProduct : amazonAdProducts) {
            sql.append("(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(amazonAdProduct.getUniqueKey());
            argsList.add(amazonAdProduct.getPuid());
            argsList.add(amazonAdProduct.getShopId());
            argsList.add(amazonAdProduct.getMarketplaceId());
            argsList.add(amazonAdProduct.getAdId());
            argsList.add(amazonAdProduct.getAdGroupId());
            argsList.add(amazonAdProduct.getDxmGroupId());
            argsList.add(amazonAdProduct.getCampaignId());
            argsList.add(amazonAdProduct.getProfileId());
            argsList.add(amazonAdProduct.getSku());
            argsList.add(amazonAdProduct.getAsin());
            argsList.add(amazonAdProduct.getState());
            argsList.add(amazonAdProduct.getServingStatus());
            argsList.add(amazonAdProduct.getRowAdId());
            argsList.add(amazonAdProduct.getCreateId());
            argsList.add(amazonAdProduct.getUpdateId());
        }
        sql.deleteCharAt(sql.length() - 1);
        sql.append(" on duplicate key update `row_ad_id`=values(row_ad_id) ");
        getJdbcTemplate(puid).update(sql.toString(), argsList.toArray());
    }

    @Override
    public List<String> getSpAdGroupIdListByAsinAndMsku(Integer puid, List<Integer> shopIdList, List<String> productValue, String productType) {
        String sql = "select ad_group_id from t_amazon_ad_product where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            conditionBuilder.in("shop_id", shopIdList.toArray());
        }
        if (Constants.PRODUCT_TYPE_PARENTASIN.equals(productType)) {
            productValue = odsProductDao.listByParentAsinMultiShop(puid, shopIdList, productValue);
            productType = Constants.PRODUCT_TYPE_ASIN;
        }
        if (CollectionUtils.isEmpty(productValue)) {
            return new ArrayList<>();
        }
        if (Constants.PRODUCT_TYPE_ASIN.equalsIgnoreCase(productType)) {
            if (productValue.size() > 1) {
                conditionBuilder.in("asin", productValue.toArray());
            } else {
                conditionBuilder.equalTo("asin", productValue.get(0).trim());
            }
        } else {
            if (productValue.size() > 1) {
                conditionBuilder.in("sku", productValue.toArray());
            } else {
                conditionBuilder.like("sku", productValue.get(0).trim());
            }
        }
        ConditionBuilder build = conditionBuilder.build();
        StringBuilder selectSql = new StringBuilder();
        String where = build.getSql();
        Object[] values = build.getValues();
        selectSql.append(sql).append(where);
        List<Object> params = new ArrayList<>(Arrays.asList(values));
        return getJdbcTemplate(puid).queryForList(selectSql.toString(), String.class, params.toArray());
    }

    @Override
    public List<String> getSbAdGroupIdListByAsinAndMsku(Integer puid, List<Integer> shopIdList, List<String> productValue, String productType) {
        String sbSql = "select ad_group_id from t_amazon_sb_ads where ";
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder()
                .equalToWithoutCheck("puid", puid);
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            conditionBuilder.in("shop_id", shopIdList.toArray());
        }
        if (Constants.PRODUCT_TYPE_PARENTASIN.equals(productType)) {
            productValue = odsProductDao.listByParentAsinMultiShop(puid, shopIdList, productValue);
            productType = Constants.PRODUCT_TYPE_ASIN;
        }
        if (CollectionUtils.isEmpty(productValue)) {
            return new ArrayList<>();
        }
        // 处理sb切msku情况
        List<String> asinList = new ArrayList<>();
        if (Constants.PRODUCT_TYPE_MSKU.equalsIgnoreCase(productType)) {
            // 查询为空 重置为
            List<String> skuList = odsProductDao.listBySkuMultiShop(puid, shopIdList, productValue);
            if (CollectionUtils.isNotEmpty(skuList)) {
                asinList = skuList;
            } else {
                asinList.add("-1");
            }
        }
        if (Constants.PRODUCT_TYPE_ASIN.equalsIgnoreCase(productType)) {
            conditionBuilder.regexp("asins", productValue.toArray());
        } else {
            if (CollectionUtils.isNotEmpty(asinList)) {
                conditionBuilder.regexp("asins", asinList.toArray());
            }
        }
        ConditionBuilder buildForSb = conditionBuilder.build();
        StringBuilder sql = new StringBuilder();
        String whereSb = buildForSb.getSql();
        Object[] sbValues = buildForSb.getValues();
        sql.append(sbSql).append(whereSb);
        List<Object> params = new ArrayList<>(Arrays.asList(sbValues));
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, params.toArray());
    }

    @Override
    public Page<AdProductStrategyVo> queryAdProductStrategy(AdProductStrategyParam param) {
        List<Object> argsList = Lists.newArrayList();
        StringBuilder selectSql = new StringBuilder("select p.puid puid, p.shop_id shopId, p.marketplace_id marketplaceId, p.ad_id adId, p.asin asin, p.sku sku,c.type adType, ");
        selectSql.append("c.portfolio_id portfolioId, p.campaign_id campaignId, c.name campaignName,c.state campaignState, ");
        selectSql.append("p.ad_group_id adGroupId, g.name adGroupName, g.state adGroupState, p.state productState ");
        selectSql.append("from t_amazon_ad_product p ");

        StringBuilder joinSql = new StringBuilder("inner join t_amazon_ad_group g on p.puid = g.puid and p.shop_id=g.shop_id and p.campaign_id = g.campaign_id and p.ad_group_id = g.ad_group_id ");
        joinSql.append("inner join t_amazon_ad_campaign_all c on p.puid = c.puid and p.shop_id=c.shop_id and p.campaign_id = c.campaign_id ");

        StringBuilder countSql = new StringBuilder("select count(*) from t_amazon_ad_product p ");

        selectSql.append(joinSql);
        countSql.append(joinSql);

        StringBuilder whereSql = new StringBuilder("where p.puid = ? and p.shop_id = ? and p.is_state_pricing = 0 ");
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());

        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) {
            whereSql.append(SqlStringUtil.dealInList("p.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (CollectionUtils.isNotEmpty(param.getAdGroupIdList())) {
            whereSql.append(SqlStringUtil.dealInList("p.ad_group_id", param.getAdGroupIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getPortfolioIdList())) {
            if (param.getPortfolioIdList().size() == 1 && param.getPortfolioIdList().contains("-1")) {
                whereSql.append(" and c.portfolio_id is null ");
            } else if (param.getPortfolioIdList().contains("-1")) {
                whereSql.append(" and ( ");
                whereSql.append(SqlStringUtil.dealInListNotAnd("c.portfolio_id", param.getPortfolioIdList(), argsList));
                whereSql.append(" or c.portfolio_id is null ) ");
            } else {
                whereSql.append(SqlStringUtil.dealInList("c.portfolio_id", param.getPortfolioIdList(), argsList));
            }
        }

        whereSql.append(" and p.asin is not null and p.sku is not null");

        if (CollectionUtils.isNotEmpty(param.getProductValueList())) {
            if (ProductSearchFieldEnum.asin.getCode().equals(param.getProductType())) {
                whereSql.append(SqlStringUtil.dealInList("p.asin", param.getProductValueList(), argsList));
            } else if (ProductSearchFieldEnum.msku.getCode().equals(param.getProductType())) {
                whereSql.append(SqlStringUtil.dealInList("p.sku", param.getProductValueList(), argsList));
            }
        }

        if (CollectionUtils.isNotEmpty(param.getStateList())) {
            whereSql.append(SqlStringUtil.dealInList("p.state", param.getStateList(), argsList));
        }

        selectSql.append(whereSql);
        countSql.append(whereSql);

        return getPageResultByClass(param.getPuid(), param.getPageNo(), param.getPageSize(),
                countSql.toString(), argsList.toArray(), selectSql.toString(), argsList.toArray(),
                AdProductStrategyVo.class);
    }

    @Override
    public void updateStatePricing(Integer puid, Integer shopId, String adId, int isPricing, int pricingState, Integer updateId) {
        StringBuilder sql = new StringBuilder("update t_amazon_ad_product set is_state_pricing=?,pricing_start_stop_state=?, update_id=?,update_time=now() where puid = ? and shop_id = ? and `ad_id`=? ");
        List<Object> args = Lists.newArrayList(isPricing, pricingState, updateId, puid, shopId, adId);
        getJdbcTemplate(puid).update(sql.toString(), args.toArray());
    }



    @Override
    public void updateState(int puid, Integer shopId, String adId, String state) {
        String sql = "update t_amazon_ad_product set state=?,update_time=now() where puid = ? and shop_id = ? and `ad_Id`=?";
        getJdbcTemplate(puid).update(sql,new Object[]{state,puid,shopId,adId});

    }


    @Override
    public List<String> getListAsinByCampaignIdAndGroupIdAndEnabled(Integer puid, Integer shopId, String campaignId, String groupId) {
        StringBuilder sql = new StringBuilder(" SELECT DISTINCT(asin) FROM `t_amazon_ad_product` WHERE puid = ? AND shop_id = ? AND campaign_id = ? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(campaignId);
        sql.append(" AND state in ('enabled','paused')  ");
        if (StringUtils.isNotBlank(groupId)) {
            sql.append(" and ad_group_id = ? ");
            argsList.add(groupId);
        }
        return getJdbcTemplate(puid).queryForList(sql.toString(), String.class, argsList.toArray());
    }

    @Override
    public List<DownloadCenterBaseDataBO> queryBaseData4DownloadByProductIdList(Integer puid, Integer shopId, List<String> adIdList) {
        StringBuilder sql = new StringBuilder("select ad_id id, state from ");
        sql.append(getJdbcHelper().getTable());
        sql.append(" where puid = ? ");
        sql.append(" and shop_id = ? ");
        sql.append(" and ad_id in ('").append(StringUtils.join(adIdList, "','")).append("')");
        return getJdbcTemplate(puid).query(sql.toString(), new BeanPropertyRowMapper<>(DownloadCenterBaseDataBO.class), puid, shopId);
    }
}