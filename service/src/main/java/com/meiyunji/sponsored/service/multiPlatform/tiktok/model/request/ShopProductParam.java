package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class ShopProductParam {

    @NotBlank(message = "advertiserId不能为空")
    private String advertiserId;
    @NotNull(message = "shopId不能为空")
    private Integer shopId;
    @NotBlank(message = "storeAuthorizedBcId不能为空")
    private String storeAuthorizedBcId;
    private String productName;
    private List<String> spuIdList;
    private String orderField;
    private String orderValue;
    private Integer pageNo;
    private Integer pageSize;

}
