package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.budget.client.BudgetRecommendationClient;
import com.amazon.advertising.budget.entity.SpCampaignBudgetRecommendationResponse;
import com.amazon.advertising.mode.StateEnum;
import com.amazon.advertising.mode.*;
import com.amazon.advertising.mode.campaigns.Campaign;
import com.amazon.advertising.profiles.ProfileClient;
import com.amazon.advertising.profiles.UpdateDailyBudgetResponse;
import com.amazon.advertising.spV3.campaign.CampaignSpV3Client;
import com.amazon.advertising.spV3.campaign.CreateSpCampaignV3Response;
import com.amazon.advertising.spV3.campaign.UpdateSpCampaignV3Response;
import com.amazon.advertising.spV3.campaign.entity.*;
import com.amazon.advertising.spV3.enumeration.SpV3StateEnum;
import com.amazon.advertising.spV3.enumeration.SpV3TargetingTypeEnum;
import com.amazon.advertising.spV3.response.ApiResponseV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.amazon.advertising.spV3.response.ErrorItemV3;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.api.client.util.Maps;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.config.DynamicRefreshNacosConfiguration;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.sp.campaign.BatchDataResponse;
import com.meiyunji.sponsored.rpc.sp.campaign.BatchUpdatePlacementAdjustmentRequest;
import com.meiyunji.sponsored.rpc.sp.campaign.NeKeywordResponse;
import com.meiyunji.sponsored.rpc.vo.CampaignNeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.CampaignNeTargetingSpRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.account.service.IShopAuthService;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.cpc.constants.AmazonAdOperationLogChangeTypeEnum;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdCampaignAllReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sd.ICpcSdCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcAdSyncService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.impl.CampaignNeKeywordReportRepository;
import com.meiyunji.sponsored.service.doris.dao.impl.CampaignNeTargetReportRepository;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignNeKeywords;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignNetargetingSp;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.product.po.AsinImage;
import com.meiyunji.sponsored.service.product.service.ISyncAsinImageService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetScheduleDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetTemplateDao;
import com.meiyunji.sponsored.service.strategy.enums.BudgetValueEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.strategy.vo.OriginToBudgetValueVo;
import com.meiyunji.sponsored.service.util.AmazonAdUtils;
import com.meiyunji.sponsored.service.util.AmazonErrorUtils;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import com.meiyunji.sponsored.service.util.ZoneUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/3/29.
 * 实现类
 */
@Service
@Slf4j
public class CpcSpCampaignServiceImpl implements ICpcSpCampaignService {

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IShopAuthService shopAuthService;
    @Autowired
    private IAmazonAdCampaignNoteDao amazonAdCampaignNoteDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IAmazonAdCampaignAllReportDao amazonAdCampaignReportDao;
    @Autowired
    private CpcCommService cpcCommService;
    @Autowired
    private CpcNeKeywordsApiService cpcNeKeywordsApiService;
    @Autowired
    private CpcCampaignApiService cpcCampaignApiService;
    @Autowired
    private ICpcAdSyncService cpcAdSyncService;

    @Autowired
    private CpcCampaignNeTargetingApiService campaignNeTargetingApiService;

    @Autowired
    private IAmazonAdCampaignNetargetingSpDao campaignNetargetingSpDao;

    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private IAmazonAdCampaignAllReportService amazonAdCampaignReportService;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private ISyncAsinImageService syncAsinImageService;
    @Autowired
    private AdvertiseStrategyTopBudgetTemplateDao advertiseStrategyTopBudgetTemplateDao;
    @Autowired
    private AdvertiseStrategyTopBudgetScheduleDao advertiseStrategyTopBudgetScheduleDao;

    @Resource
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private StringRedisService stringRedisService;

    @Resource
    private DynamicRefreshNacosConfiguration dynamicRefreshNacosConfiguration;

    @Autowired
    private IDorisService dorisService;

    @Autowired
    private IAmazonSbAdsDao amazonSbAdsDao;
    @Autowired
    private ICpcSdCampaignService sdCampaignService;
    @Autowired
    private ICpcSbCampaignService sbCampaignService;
    @Autowired
    private CampaignNeKeywordReportRepository campaignNeKeywordReportRepository;
    @Autowired
    private CampaignNeTargetReportRepository campaignNeTargetReportRepository;
    @Autowired
    private IWordTranslateService wordTranslateService;

    public static final Map<String, String> errorMap = new HashMap<String, String>() {{
        put("Start date cannot be after end date.", "开始时间不得晚于结束时间");
    }};

    @Override
    public Result<AdPerformanceVo> showCampaignPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null || StringUtils.isBlank(param.getCampaignId())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignAllDao.getCampaignByCampaignId(puid, param.getShopId(), param.getCampaignId(),CampaignTypeEnum.sp.getCampaignType());
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdCampaign.getShopId());
        adPerformanceVo.setCampaignId(amazonAdCampaign.getCampaignId());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }


        List<AmazonAdCampaignAllReport> reports = amazonAdCampaignReportDao.listReports(puid, param.getShopId(),
                    param.getStartDate(), param.getEndDate(), param.getCampaignId(),CampaignTypeEnum.sp.getCampaignType());

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            List<ShopSaleDto> sumFileData = cpcShopDataService.listDailyShopSaleByDateRange(puid, Lists.newArrayList(amazonAdCampaign.getShopId()),
                    param.getStartDate(), param.getEndDate());
            Map<String, CpcCommPageVo> resultMap = new HashMap<>();
            Map<String, ShopSaleDto> sumFileDataMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sumFileData)) {
                sumFileDataMap = sumFileData.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(
                        e -> e.getNowDate().format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), e -> e,(a,b)-> a));
            }

            Map<String, AmazonAdCampaignAllReport> reportMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> e, (p1, p2) -> p1));
            for (String key : reportMap.keySet()) {
                // 填充报告数据
                CpcCommPageVo campaignPageVo = new CpcCommPageVo();
                if(StringUtils.isNotEmpty(key)) {
                    cpcCommService.fillReportDataIntoPageVo(campaignPageVo, reportMap.get(key), sumFileDataMap.get(key));
                    resultMap.put(key, campaignPageVo);
                }
            }
            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }


    @Override
    public List<CampaignPageVo> getList(Integer puid, CampaignPageParam param) {
        long t = System.currentTimeMillis();
        List<String> campaignIdFilterBySbCreativeType = amazonSbAdsDao.getCampaignIdBySbCreativeType(puid, param.getShopId(), param.getFilterTargetType(), param.getUseAdvanced());
        param.setCreativeIds(campaignIdFilterBySbCreativeType);
        List<AmazonAdCampaignAll> poList = amazonAdCampaignAllDao.getList(puid, param);
        log.info("广告管理--广告活动接口调用-获取sp广告管理- 花费时间 {} ,params: {}", (System.currentTimeMillis()-t),JSONUtil.objectToJson(param));
        List<CampaignPageVo> voList = new ArrayList<>(poList.size());
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());
            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }

            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().collect(Collectors.toMap(User::getId, e -> e));//判断user表中id是否存在

            long t1 = System.currentTimeMillis();
            // 按活动分组获取活动的汇总数据
            List<String> campaignList = poList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());

            List<AmazonAdCampaignAllReport> list = new ArrayList<>();
            if (campaignList.size() > 20000) {
                List<List<String>> lists = Lists.partition(campaignList, 20000);
                List<AmazonAdCampaignAllReport> dtoList = null;
                for (List<String> subList : lists) {
                    dtoList = amazonAdCampaignReportService.listSumReports(puid, param.getShopId(),
                            poList.get(0).getMarketplaceId(), param.getStartDate(), param.getEndDate(),
                            subList);
                    list.addAll(dtoList);
                }
            } else {
                list = amazonAdCampaignReportService.listSumReports(puid, param.getShopId(),
                        poList.get(0).getMarketplaceId(), param.getStartDate(), param.getEndDate(),
                        campaignList);
            }

            Map<String, AmazonAdCampaignAllReport> campaignReportMap = list.stream()
                    .collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, e -> e));
            log.info("广告管理--广告活动接口调用-按campaignId获取sp广告活动报告- 花费时间 {} ,params: {}", (System.currentTimeMillis()-t1),JSONUtil.objectToJson(param));

            long t2 = System.currentTimeMillis();
            // 取店铺销售额
            ShopSaleDto shopSaleDto =  new ShopSaleDto();
            if (param.getShopSales() != null) {  // 最外层查了一次了
                shopSaleDto.setSumRange(param.getShopSales());
            }

            CampaignPageVo vo;
            Map<String,Long> outOfBudgetTime = new HashMap<>();
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(list, adMetricDto);

            for (AmazonAdCampaignAll amazonAdCampaign : poList) {
                vo = new CampaignPageVo();
                voList.add(vo);
                convertPoToPageVo(amazonAdCampaign, vo,outOfBudgetTime);
                filterAdMetricData(adMetricDto, vo);

                if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                        vo.setIsHidden(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getIsHidden());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }

                // 创建人
                if (userMap.containsKey(amazonAdCampaign.getCreateId())) {
                    User user = userMap.get(amazonAdCampaign.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                // 填充报告数据
                if (campaignReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillReportDataIntoPageVo(vo, campaignReportMap.get(amazonAdCampaign.getCampaignId()).getReportBase(), shopSaleDto);
                }

                //兼容本产品广告销量（sd无广告销量）
                if (CampaignTypeEnum.sb.getCampaignType().equals(vo.getType()) || CampaignTypeEnum.sd.getCampaignType().equals(vo.getType())) {
                    vo.setAdSelfSaleNum(0);
                    vo.setAdOtherSaleNum(0);
                }
            }

            if (param.getUseAdvanced() != null && param.getUseAdvanced()) {  //开启了高级搜索,需要过滤
                cpcCommService.filterCampaignAdvanceData(voList, param);
            }
            log.info("广告管理--广告活动接口调用-广告活动填充报告数据等- 花费时间 {} ,params: {}", (System.currentTimeMillis()-t2),JSONUtil.objectToJson(param));
        }

        return voList;
    }


    @Override
    public Result<List<PlacementPageVo>> placementPageList(int puid, PlacementPageParam param) {
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,param.getCampaignId());
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }
        Map<String, List<AmazonAdCampaignAllReport>> placementReportMap = null;

        // 此活动下的广告位数据报告

        placementReportMap = amazonAdCampaignReportDao.listPlacementReports(puid, amazonAdCampaign.getShopId(), amazonAdCampaign.getMarketplaceId(),
                        param.getStartDate(), param.getEndDate(), amazonAdCampaign.getCampaignId()).stream().filter(e -> StringUtils.isNotBlank(e.getCampaignType()))
                .collect(Collectors.groupingBy(AmazonAdCampaignAllReport::getCampaignType));


        // 取店铺销售额
        ShopSaleDto shopSaleDto = null;
        if (placementReportMap.size() > 0) {
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

        }

        Map<String, Adjustment> adjustmentMap = null;
        if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
            List<Adjustment> adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
            if (CollectionUtils.isNotEmpty(adjustments)) {
                adjustmentMap = adjustments.stream().collect(Collectors.toMap(Adjustment::getPredicate, e -> e));
            }
        }

        List<String> placements = Lists.newArrayList(Constants.PLACEMENT_TOP,  Constants.PLACEMENT_OTHER ,Constants.PLACEMENT_DETAIL_PAGE);
        PlacementPageVo placementPageVo;
        List<PlacementPageVo> placementPageVos = new ArrayList<>(placements.size());
        for (String placement : placements) {
            placementPageVo = new PlacementPageVo();
            placementPageVo.setStrategy(amazonAdCampaign.getStrategy());

            if (Constants.PLACEMENT_TOP.equals(placement)) {
                placementPageVo.setPredicate(PredicateEnum.PLACEMENTTOP.value());
            } else if (Constants.PLACEMENT_DETAIL_PAGE.equals(placement)) {
                placementPageVo.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
            }  else if (Constants.PLACEMENT_OTHER.equals(placement)) {
                placementPageVo.setPredicate("Other");
            }

            if (adjustmentMap != null && adjustmentMap.containsKey(placementPageVo.getPredicate())) {
                placementPageVo.setPercentage(String.valueOf(adjustmentMap.get(placementPageVo.getPredicate()).getPercentage()));
            }

            if (placementReportMap.containsKey(placement)) {
                // 填充报告数据
                cpcCommService.fillReportDataIntoPageVo(placementPageVo, placementReportMap.get(placement).get(0), shopSaleDto);
            }

            placementPageVos.add(placementPageVo);
        }

        // 排序
        if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
            placementPageVos = PageUtil.sort(placementPageVos, param.getOrderField(), param.getOrderType());
        }

        return ResultUtil.returnSucc(placementPageVos);
    }

    @Override
    public Result<AdPerformanceVo> showPlacementPerformance(int puid, AdPerformanceParam param) {
        if (param.getShopId() == null
                || StringUtils.isBlank(param.getCampaignId())
                || StringUtils.isBlank(param.getPlacement())) {
            return ResultUtil.returnErr("请求参数错误");
        }

        // 匹配报告里的广告位
        String placement = "";
        if (PredicateEnum.PLACEMENTTOP.value().equals(param.getPlacement())) {
            placement = Constants.PLACEMENT_TOP;
        } else if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(param.getPlacement())) {
            placement = Constants.PLACEMENT_DETAIL_PAGE;
        }  else if ("Other".equals(param.getPlacement())) {
            placement = Constants.PLACEMENT_OTHER;
        }

        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignAllDao.getCampaignByCampaignId(puid, param.getShopId(), param.getCampaignId(), CampaignTypeEnum.sp.getCampaignType());
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        // 拼装返回的数据VO
        AdPerformanceVo adPerformanceVo = new AdPerformanceVo();
        adPerformanceVo.setShopId(amazonAdCampaign.getShopId());
        adPerformanceVo.setCampaignId(amazonAdCampaign.getCampaignId());
        adPerformanceVo.setPlacement(param.getPlacement());

        // 初始化每天数据，应前端要求保证日期是连续的
        Map<String, CpcCommPageVo> map = new LinkedHashMap<>();
        adPerformanceVo.setMap(map);
        LocalDate startLocalDate = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        LocalDate endLocalDate = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        while (startLocalDate.equals(endLocalDate) || startLocalDate.isBefore(endLocalDate)) {
            map.put(startLocalDate.format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)), new CpcCommPageVo());
            startLocalDate = startLocalDate.plusDays(1);
        }

        List<AmazonAdCampaignAllReport> reports = null;

        reports = amazonAdCampaignReportDao.listPlacementReports(puid, param.getShopId(), amazonAdCampaign.getMarketplaceId(),
                param.getStartDate(), param.getEndDate(), param.getCampaignId(), placement);

        if (CollectionUtils.isNotEmpty(reports)) {
            // 取店铺销售额
            String start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            String end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN));
            ShopSaleDto  shopSaleDto = cpcShopDataService.getShopSaleData(param.getShopId(), start, end);

            Map<String, CpcCommPageVo> resultMap = reports.stream().collect(Collectors.toMap(
                    e -> LocalDate.parse(e.getCountDate(), DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD)).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN)),
                    e -> {
                        CpcCommPageVo campaignPageVo = new CpcCommPageVo();

                        // 填充报告数据
                        cpcCommService.fillReportDataIntoPageVo(campaignPageVo, e, shopSaleDto);
                        return campaignPageVo;

                    }, (p1, p2) -> p1));

            adPerformanceVo.getMap().putAll(resultMap);
        }

        return ResultUtil.returnSucc(adPerformanceVo);
    }

    @Override
    public Page<CampaignNeKeywordsPageRpcVo> neKeywordsPageList(Integer puid, CampaignNeKeywordsPageParam param) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth==null) {
            AssertUtil.fail("店铺未授权");
        }

        List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, param.getShopId(), param.getPortfolioId(),CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus());
        if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
            param.setCampaignIdList(campaignIds);
        } else {
            return new Page<>(param.getPageNo(), param.getPageSize());
        }
        Page<AmazonAdCampaignNeKeywords> page = amazonAdCampaignNeKeywordsDao.pageList(puid, param);
        Page<CampaignNeKeywordsPageRpcVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);

        if (CollectionUtils.isNotEmpty(page.getRows())) {
            //查询活动ids
            List<String> spCampaignIds = page.getRows().stream().filter(Objects::nonNull).map(AmazonAdCampaignNeKeywords::getCampaignId).collect(Collectors.toList());

            //sp广告活动
            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            if (CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(puid, param.getShopId(), shopAuth.getMarketplaceId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(puid, param.getShopId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }


            List<CampaignNeKeywordsPageRpcVo> list = new ArrayList<>(page.getRows().size());
            voPage.setRows(list);

            for (AmazonAdCampaignNeKeywords neKeywords : page.getRows()) {
                CampaignNeKeywordsPageRpcVo.Builder builder = CampaignNeKeywordsPageRpcVo.newBuilder();

                builder.setId(Int64Value.of(neKeywords.getId()));
                builder.setShopId(Int32Value.of(neKeywords.getShopId()));
                builder.setCampaignId(neKeywords.getCampaignId());
                if (shopAuth != null) {
                    builder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (StringUtils.isNotBlank(neKeywords.getState())) {
                    builder.setState(neKeywords.getState());
                }
                if (StringUtils.isNotBlank(neKeywords.getMatchType())) {
                    builder.setMatchType(neKeywords.getMatchType());
                }
                if (StringUtils.isNotBlank(neKeywords.getKeywordText())) {
                    builder.setKeywordText(neKeywords.getKeywordText());
                }
                if (neKeywords.getCreationDate() != null || neKeywords.getCreateTime() != null) {
                    if (neKeywords.getCreationDate() != null) {
                        builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(neKeywords.getCreationDate(), shopAuth.getMarketplaceId())));
                    } else {
                        builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTimeUtil.convertDateToLDT(neKeywords.getCreateTime()), shopAuth.getMarketplaceId())));
                    }
                }
                builder.setType(Constants.SP);
                //广告信息填充
                if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(neKeywords.getCampaignId())) {
                    AmazonAdCampaignAll campaign = spCampaignMap.get(neKeywords.getCampaignId());
                    if (StringUtils.isNotBlank(campaign.getName())) {
                        builder.setCampaignName(campaign.getName());
                    }
                    if (StringUtils.isNotBlank(campaign.getState())) {
                        builder.setCampaignState(campaign.getState());
                    }
                    if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                        builder.setCampaignTargetingType(campaign.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                        builder.setPortfolioId(campaign.getPortfolioId());
                        if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                            builder.setPortfolioName(amazonAdPortfolio.getName());
                            builder.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            builder.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        builder.setPortfolioName("-");
                    }
                    //活动状态归档,否定关键词也设为归档
//                    if (Constants.ARCHIVED.equalsIgnoreCase(spCampaignMap.get(neKeywords.getCampaignId()).getState())) {
//                        builder.setState(Constants.ARCHIVED);
//                    }
                }
                list.add(builder.build());
            }
        }

        return voPage;
    }

    @Override
    public Page<CampaignNeKeywordsPageRpcVo> neKeywordsPageListV2(CampaignNeKeywordsPageParam param, boolean isExport) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth==null) {
            AssertUtil.fail("未授权");
        }

        //1,查询符合条件的page<keywordId>
        Page<String> keywordIdPage = getCampaignNeKeywordIdPage(param);

        //2,根据page<keywordId>构建返回数据
        return buildRespVoPage(keywordIdPage, shopAuth, param, isExport);
    }

    /**
     * 构建返回数据
     * @param keywordIdPage keywordId分页
     * @param shopAuth 店铺信息
     * @param param param
     * @return
     */
   private Page<CampaignNeKeywordsPageRpcVo> buildRespVoPage(Page<String> keywordIdPage, ShopAuth shopAuth, CampaignNeKeywordsPageParam param, boolean isExport){
       Page<CampaignNeKeywordsPageRpcVo> voPage = new Page<>();
       BeanUtils.copyProperties(keywordIdPage, voPage);

       //查询
       List<CampaignNeKeywordsPageRpcVo> campaignNeKeywordsPageRpcVoList = queryDetailCampaignNeKeywordsPageRows(keywordIdPage.getRows(), shopAuth, param.getNeTargetReportFilterDto(), isExport);

       //排序
       //sortList(param.getNeTargetReportFilterDto().getOrderField(), param.getNeTargetReportFilterDto().getOrderType(), campaignNeKeywordsPageRpcVoList);//todo 删除

       voPage.setRows(campaignNeKeywordsPageRpcVoList);
       return voPage;
    }

    /**
     * 查询符合条件的page<keywordId>
     * @param param
     * @return
     */
    private Page<String> getCampaignNeKeywordIdPage(CampaignNeKeywordsPageParam param) {
        NeTargetReportFilterDto filterDto = param.getNeTargetReportFilterDto();

        // ①不需要筛选报告数据
        if (!Boolean.TRUE.equals(filterDto.getOnlyShowImpressions()) && !Boolean.TRUE.equals(filterDto.getDoAdvancedFilter()) &&
            StringUtil.isEmpty(filterDto.getOrderField()) && StringUtil.isEmpty(filterDto.getOrderType())) {
            return campaignNeKeywordReportRepository.pageCampaignNeKeywordWithoutReportFilter(param);
        }

        boolean dataFromQueryReport = Objects.equals(filterDto.getDataFrom(), 2);

        // ②从前后30天报告表中查数据
        if (StringUtils.isAnyBlank(filterDto.getReportStartDate(), filterDto.getReportEndDate())) {
            String reportTableName = dataFromQueryReport ?
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getQueryReportTableNameFor30Days() :
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getTargetReportTableNameFor30Days();
            return campaignNeKeywordReportRepository.page30DaysCampaignNeKeywordWithReportFilter(param, reportTableName);
        } else {
            // ③自定义时间数据查询
            String reportTableName = dataFromQueryReport ?
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getQueryReportTableName() :
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getReportTableName();
            return campaignNeKeywordReportRepository.pageCampaignNeKeywordWithReportFilter(param, reportTableName);
        }
    }

    /**
     * campaignNeKeywordsPageRpcVoList排序
     * @param orderField 排序字段
     * @param orderType desc,acs
     * @param campaignNeKeywordsPageRpcVoList 需要排序的list
     */
   /* private void sortList(String orderField, String orderType, List<CampaignNeKeywordsPageRpcVo> campaignNeKeywordsPageRpcVoList){
        if ( StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(orderType)) {
            Comparator<CampaignNeKeywordsPageRpcVo> comparator ;
            switch (orderField) {
                case "acos":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getAcos);
                    break;
                case "adOrderNum":
                    comparator =Comparator.comparing(CampaignNeKeywordsPageRpcVo::getAdOrderNum);
                    break;
                case "clicks":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getClicks);
                    break;
                case "impressions":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getImpressions);
                    break;
                case "adCost":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getAdCost);
                    break;
                case "adSale":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getAdSale);
                    break;
                case "ctr":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getCtr);
                    break;
                case "cvr":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getCvr);
                    break;
                case "roas":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getRoas);
                    break;
                case "adCostPerClick":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getAdCostPerClick);
                    break;
                case "cpa":
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getCpa);
                    break;
                default:
                    comparator = Comparator.comparing(CampaignNeKeywordsPageRpcVo::getCreateTime);
            }
            // 根据 desc 确定是否需要反转排序
            if (OrderTypeEnum.desc.getType().equals(orderType)) {
                comparator = comparator.reversed();
            }
            campaignNeKeywordsPageRpcVoList.sort(comparator);
        }
    }*/


    /**
     * 查询详细数据返回
     * @param keywordIdList
     * @param shopAuth
     * @return
     */
    private List<CampaignNeKeywordsPageRpcVo> queryDetailCampaignNeKeywordsPageRows(List<String> keywordIdList, ShopAuth shopAuth, NeTargetReportFilterDto neTargetReportFilterDto, boolean isExport) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return Collections.emptyList();
        }

        Integer puid = shopAuth.getPuid();

        // 1. 获取广告关键词
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopAuth.getId());
        conditionBuilder.in("keyword_id", keywordIdList.toArray());
        List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords = amazonAdCampaignNeKeywordsDao.listByCondition(puid, conditionBuilder.build());
        if (CollectionUtils.isEmpty(amazonAdCampaignNeKeywords)) {
            return Collections.emptyList();
        }
        Map<String, AmazonAdCampaignNeKeywords> campaignNeKeywordsMap = amazonAdCampaignNeKeywords.stream()
            .filter(e -> Objects.nonNull(e) && StringUtil.isNotEmpty(e.getKeywordId()))
            .collect(Collectors.toMap(AmazonAdCampaignNeKeywords::getKeywordId, Function.identity(), (existing, replacement) -> existing));

        // 2. 获取广告活动信息
        Map<String, AmazonAdCampaignAll> spCampaignMap = getCampaignMap(puid, shopAuth, amazonAdCampaignNeKeywords);

        // 3. 获取广告组合信息
        Map<String, AmazonAdPortfolio> portfolioMap = getPortfolioMap(puid, shopAuth, amazonAdCampaignNeKeywords);

        // 4. 获取创建者信息
        Map<Integer, User> userMap = getUserMap(puid, amazonAdCampaignNeKeywords);

        // 5. 获取报告数据
        Map<String, NeTargetReportDataDto> reportMap = getReportMap(puid, shopAuth, keywordIdList, neTargetReportFilterDto);

        //获取翻译词
        List<WordTranslateQo> wordTranslateQos = amazonAdCampaignNeKeywords.stream().map(e -> new WordTranslateQo(shopAuth.getMarketplaceId(), e.getKeywordText())).collect(Collectors.toList());
        Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, isExport);

        // 6. 构建返回数据
        return buildCampaignNeKeywordsPageRows(keywordIdList, campaignNeKeywordsMap, spCampaignMap, portfolioMap, userMap, reportMap, wordTranslateMap, shopAuth);
    }

    private Map<String, AmazonAdCampaignAll> getCampaignMap(Integer puid, ShopAuth shopAuth, List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords) {
        List<String> campaignIds = amazonAdCampaignNeKeywords.stream()
            .filter(Objects::nonNull)
            .map(AmazonAdCampaignNeKeywords::getCampaignId)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Collections.emptyMap();
        }

        List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(puid, shopAuth.getId(), shopAuth.getMarketplaceId(), campaignIds, CampaignTypeEnum.sp.getCampaignType());
        if (CollectionUtils.isEmpty(spAdCampaigns)) {
            return Collections.emptyMap();
        }

        return spAdCampaigns.stream()
            .filter(campaign -> Objects.nonNull(campaign) && StringUtil.isNotEmpty(campaign.getCampaignId()))
            .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (existing, replacement) -> existing));

    }

    private Map<String, AmazonAdPortfolio> getPortfolioMap(Integer puid, ShopAuth shopAuth, List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords) {
        List<String> campaignIds = amazonAdCampaignNeKeywords.stream()
            .filter(Objects::nonNull)
            .map(AmazonAdCampaignNeKeywords::getCampaignId)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            return Collections.emptyMap();
        }

        List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(puid, shopAuth.getId(), campaignIds, CampaignTypeEnum.sp.getCampaignType());
        if (CollectionUtils.isEmpty(portfolioIds)) {
            return Collections.emptyMap();
        }

        List<AmazonAdPortfolio> portfolios = portfolioDao.getPortfolioList(puid, shopAuth.getId(), portfolioIds);
        if (CollectionUtils.isEmpty(portfolios)) {
            return Collections.emptyMap();
        }

        return portfolios.stream()
            .filter(portfolio -> Objects.nonNull(portfolio) && StringUtil.isNotEmpty(portfolio.getPortfolioId()))
            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (existing, replacement) -> existing));
    }

    private Map<Integer, User> getUserMap(Integer puid, List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords) {
        List<Integer> createIds = amazonAdCampaignNeKeywords.stream()
            .filter(Objects::nonNull)
            .map(AmazonAdCampaignNeKeywords::getCreateId)
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(createIds)) {
            return Collections.emptyMap();
        }

        List<User> users = userDao.listByIds(puid, createIds);
        if (CollectionUtils.isEmpty(users)) {
            return Collections.emptyMap();
        }

        return users.stream()
            .filter(user -> Objects.nonNull(user) && Objects.nonNull(user.getId()))
            .collect(Collectors.toMap(User::getId, Function.identity(), (existing, replacement) -> existing));
    }

    private Map<String, NeTargetReportDataDto> getReportMap(Integer puid, ShopAuth shopAuth, List<String> keywordIdList, NeTargetReportFilterDto filterDto) {
        List<NeTargetReportDataDto> reportByKeyWordIds;

        // ①前后30天报告数据
        if (StringUtils.isAnyBlank(filterDto.getReportStartDate(), filterDto.getReportEndDate())) {
            String reportTableName = filterDto.getDataFrom() == 2 ?
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getQueryReportTableNameFor30Days() :
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getTargetReportTableNameFor30Days();
            reportByKeyWordIds = campaignNeKeywordReportRepository.get30ReportByKeyWordIds(puid, shopAuth.getId(), keywordIdList, reportTableName, filterDto.getReportDateType());
        } else {
            // ②自定义时间数据查询
            String reportTableName = filterDto.getDataFrom() == 2 ?
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getQueryReportTableName() :
                NeTargetReportTableEnum.SP_CAMPAIGN_NEKEYWORD.getReportTableName();
            reportByKeyWordIds = campaignNeKeywordReportRepository.getReportByKeyWordIds(
                puid, shopAuth.getId(), keywordIdList, reportTableName, filterDto.getReportStartDate(), filterDto.getReportEndDate());
        }
        if (CollectionUtils.isEmpty(reportByKeyWordIds)) {
            return Collections.emptyMap();
        }

        return reportByKeyWordIds.stream()
            .filter(reportDataDto -> Objects.nonNull(reportDataDto) && StringUtil.isNotEmpty(reportDataDto.getTargetId()))
            .collect(Collectors.toMap(NeTargetReportDataDto::getTargetId, Function.identity(), (existing, replacement) -> existing));
    }

    private List<CampaignNeKeywordsPageRpcVo> buildCampaignNeKeywordsPageRows(
        List<String> keywordIdList,
        Map<String, AmazonAdCampaignNeKeywords> campaignNeKeywordsMap,
        Map<String, AmazonAdCampaignAll> spCampaignMap,
        Map<String, AmazonAdPortfolio> portfolioMap,
        Map<Integer, User> userMap,
        Map<String, NeTargetReportDataDto> reportMap,
        Map<String, String> wordTranslateMap,
        ShopAuth shopAuth) {


        return keywordIdList.stream()
            .filter(campaignNeKeywordsMap::containsKey)
            .map(neKeywordId -> {
                CampaignNeKeywordsPageRpcVo.Builder builder = CampaignNeKeywordsPageRpcVo.newBuilder();
                AmazonAdCampaignNeKeywords campaignNeKeyword = campaignNeKeywordsMap.get(neKeywordId);
                builder.setTargetId(campaignNeKeyword.getKeywordId());
                builder.setId(Int64Value.of(campaignNeKeyword.getId()));
                builder.setShopId(Int32Value.of(campaignNeKeyword.getShopId()));
                builder.setCampaignId(campaignNeKeyword.getCampaignId());
                builder.setMarketplaceId(shopAuth.getMarketplaceId());
                setOptionalField(builder, campaignNeKeyword);
                builder.setKeywordTextCn(wordTranslateMap.getOrDefault(wordTranslateService.getWordTranslateKey(campaignNeKeyword.getMarketplaceId(), campaignNeKeyword.getKeywordText()), ""));
                setCreateTime(builder, campaignNeKeyword, shopAuth);
                builder.setType(Constants.SP);

                // 填充广告活动和广告组合信息
                fillCampaignAndPortfolioInfo(spCampaignMap, portfolioMap, builder, shopAuth.getPuid(), campaignNeKeyword.getCampaignId());

                // 填充创建者信息
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(campaignNeKeyword.getCreateId())) {
                    builder.setCreateName(userMap.get(campaignNeKeyword.getCreateId()).getNickname());
                }

                // 填充报告数据
                fillCampaignKeywordReportDataIntoPageVo(builder, reportMap.get(campaignNeKeyword.getKeywordId()));

                return builder.build();
            }).collect(Collectors.toList());
    }

    private void setOptionalField(CampaignNeKeywordsPageRpcVo.Builder builder, AmazonAdCampaignNeKeywords neKeyword) {
        if (StringUtils.isNotBlank(neKeyword.getState())) {
            builder.setState(neKeyword.getState());
        }
        if (StringUtils.isNotBlank(neKeyword.getMatchType())) {
            builder.setMatchType(neKeyword.getMatchType());
        }
        if (StringUtils.isNotBlank(neKeyword.getKeywordText())) {
            builder.setKeywordText(neKeyword.getKeywordText());
        }
    }

    private void setCreateTime(CampaignNeKeywordsPageRpcVo.Builder builder, AmazonAdCampaignNeKeywords neKeyword, ShopAuth shopAuth) {
        if (neKeyword.getCreationDate() != null || neKeyword.getCreateTime() != null) {
            builder.setCreateTime(DateUtil.dateToStrWithTime(
                LocalDateTimeUtil.convertChinaToSiteTime(
                    neKeyword.getCreationDate() != null ?
                        neKeyword.getCreationDate() : LocalDateTimeUtil.convertDateToLDT(neKeyword.getCreateTime()),
                    shopAuth.getMarketplaceId())
            ));
        }
    }


    private void fillCampaignKeywordReportDataIntoPageVo(CampaignNeKeywordsPageRpcVo.Builder builder, NeTargetReportDataDto dto) {
        if (dto == null) {
            return;
        }
        if (dto.getTotalSales()!= null && dto.getCost() != null) {
            builder.setAcos((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getCost().multiply(new BigDecimal("100")).divide(dto.getTotalSales(), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getAdOrderNum() != null) {
            builder.setAdOrderNum(dto.getAdOrderNum());
        }
        if (dto.getClicks() != null) {
            builder.setClicks(dto.getClicks());
        }
        if (dto.getImpressions() != null) {
            builder.setImpressions(dto.getImpressions());
        }
        if (dto.getCost() != null) {
            builder.setAdCost(dto.getCost().doubleValue());
        }
        if (dto.getTotalSales()!= null) {
            builder.setAdSale(dto.getTotalSales().doubleValue());
        }
        if (dto.getClicks() != null && dto.getImpressions() != null) {
            builder.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getImpressions())).doubleValue());
        }
        if (dto.getAdOrderNum() != null && dto.getClicks() != null) {
            builder.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getClicks())).doubleValue());
        }
        if (dto.getTotalSales() != null && dto.getCost() != null) {
            builder.setRoas((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getTotalSales().divide(dto.getCost(), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getCost()!= null && dto.getClicks() != null) {
            builder.setAdCostPerClick((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getClicks() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getClicks()), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getCost()!= null && dto.getAdOrderNum() != null) {
            builder.setCpa((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getAdOrderNum() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getAdOrderNum()), 4, RoundingMode.HALF_UP)).doubleValue());
        }
    }

    /**
     * 填充广告活动和广告组合信息
     * @param spCampaignMap campaignId->AmazonAdCampaignAll
     * @param portfolioMap portfolioId->AmazonAdPortfolio
     * @param builder respVo
     * @param puid puid
     * @param campaignId campaignId
     */
    private void fillCampaignAndPortfolioInfo(Map<String, AmazonAdCampaignAll> spCampaignMap, Map<String, AmazonAdPortfolio> portfolioMap, CampaignNeKeywordsPageRpcVo.Builder builder, Integer puid, String campaignId) {
        AmazonAdCampaignAll campaign = spCampaignMap.get(campaignId);
        if (StringUtils.isNotBlank(campaign.getName())) {
            builder.setCampaignName(campaign.getName());
        }
        if (StringUtils.isNotBlank(campaign.getState())) {
            builder.setCampaignState(campaign.getState());
        }
        if (StringUtils.isNotBlank(campaign.getTargetingType())) {
            builder.setCampaignTargetingType(campaign.getTargetingType());
        }
        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
            AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
            builder.setPortfolioId(campaign.getPortfolioId());
            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                builder.setPortfolioName(amazonAdPortfolio.getName());
                builder.setIsHidden(adPortfolio.getIsHidden());
            } else {
                builder.setPortfolioName("广告组合待同步");
            }
        } else {
            builder.setPortfolioName("-");
        }
    }

    @Override
    public Page<CampaignNeTargetingSpRpcVo> neTargetingPageListV2(CampaignNeTargetingSpParam param) {
        Integer shopId = param.getShopId();
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            AssertUtil.fail("未授权");
        }

        //1,查询符合条件的page<keywordId>
        Page<String> targetIdPage = getCampaignNeTargetIdPage(param);

        //2,根据page<targetId>构建返回数据
        return buildRespVoPage(targetIdPage, shopAuth, param);
    }

    private Page<CampaignNeTargetingSpRpcVo> buildRespVoPage(Page<String> targetIdPage, ShopAuth shopAuth, CampaignNeTargetingSpParam param){
        Page<CampaignNeTargetingSpRpcVo> voPage = new Page<>();
        BeanUtils.copyProperties(targetIdPage, voPage);

        //查询
        List<CampaignNeTargetingSpRpcVo> campaignNeTargetingSpVos = queryDetailCampaignNeTargetingsPageRows(targetIdPage.getRows(), shopAuth, param.getNeTargetReportFilterDto());

        //排序
        //sortList(param, campaignNeTargetingSpVos);
        voPage.setRows(campaignNeTargetingSpVos);
        return voPage;
    }

    private Page<String> getCampaignNeTargetIdPage(CampaignNeTargetingSpParam param) {
        NeTargetReportFilterDto neTargetReportFilterDto = param.getNeTargetReportFilterDto();

        //①不需要筛选报告数据
        if (!Boolean.TRUE.equals(neTargetReportFilterDto.getOnlyShowImpressions()) && !Boolean.TRUE.equals(neTargetReportFilterDto.getDoAdvancedFilter()) && (StringUtil.isEmpty(neTargetReportFilterDto.getOrderField()) || StringUtil.isEmpty(neTargetReportFilterDto.getOrderType()))) {
            return campaignNeTargetReportRepository.pageCampaignNeTargetWithoutReportFilter(param);
        }

        boolean dataFromQueryReport = Objects.nonNull(neTargetReportFilterDto.getDataFrom()) && neTargetReportFilterDto.getDataFrom() == 2;

        //②从前后30天报告表中查数据
        if (StringUtils.isAnyBlank(neTargetReportFilterDto.getReportStartDate(), neTargetReportFilterDto.getReportEndDate())) {
            //从投放表还是搜索词表
            String reportTableName = dataFromQueryReport ? NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getQueryReportTableNameFor30Days() : NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getTargetReportTableNameFor30Days();
            return campaignNeTargetReportRepository.page30DaysCampaignNeTargetWithReportFilter(param, reportTableName);
        } else {//③从自定义时间报告表中查数据
            String reportTableName = dataFromQueryReport ? NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getQueryReportTableName() : NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getReportTableName();
            return campaignNeTargetReportRepository.pageCampaignNeTargetWithReportFilter(param, reportTableName);
        }

    }


    /*private void sortList(CampaignNeTargetingSpParam param, List<CampaignNeTargetingSpRpcVo> list){
        //list排序
        String orderField = param.getNeTargetReportFilterDto().getOrderField();
        String orderType = param.getNeTargetReportFilterDto().getOrderType();
        if ( StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(orderType)) {
            Comparator<CampaignNeTargetingSpRpcVo> comparator ;
            switch (orderField) {
                case "acos":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getAcos);
                    break;
                case "adOrderNum":
                    comparator =Comparator.comparing(CampaignNeTargetingSpRpcVo::getAdOrderNum);
                    break;
                case "clicks":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getClicks);
                    break;
                case "impressions":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getImpressions);
                    break;
                case "adCost":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getAdCost);
                    break;
                case "adSale":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getAdSale);
                    break;
                case "ctr":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getCtr);
                    break;
                case "cvr":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getCvr);
                    break;
                case "roas":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getRoas);
                    break;
                case "adCostPerClick":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getAdCostPerClick);
                    break;
                case "cpa":
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getCpa);
                    break;
                default:
                    comparator = Comparator.comparing(CampaignNeTargetingSpRpcVo::getCreateTime);
            }
            // 根据 desc 确定是否需要反转排序
            if (OrderTypeEnum.desc.getType().equals(orderType)) {
                comparator = comparator.reversed();
            }
            list.sort(comparator);
        }
    }*/

    private List<CampaignNeTargetingSpRpcVo> queryDetailCampaignNeTargetingsPageRows(List<String> targetIdList, ShopAuth shopAuth, NeTargetReportFilterDto neTargetReportFilterDto) {

        if (CollectionUtils.isEmpty(targetIdList)) {
            return Collections.emptyList();
        }
        Integer puid = shopAuth.getPuid();

        // 1. 获取否定商品
        ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
        conditionBuilder.equalTo("puid", puid);
        conditionBuilder.equalTo("shop_id", shopAuth.getId());
        conditionBuilder.in("target_id", targetIdList.toArray());
        List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSps = campaignNetargetingSpDao.listByCondition(puid, conditionBuilder.build());
        if (CollectionUtils.isEmpty(amazonAdCampaignNetargetingSps)) {
            return Collections.emptyList();
        }
        Map<String, AmazonAdCampaignNetargetingSp> campaignNeTargetsMap = amazonAdCampaignNetargetingSps.stream()
            .filter(e -> Objects.nonNull(e) && StringUtil.isNotEmpty(e.getTargetId()))
            .collect(Collectors.toMap(AmazonAdCampaignNetargetingSp::getTargetId, Function.identity(), (existing, replacement) -> existing));

        //2,查询活动id
        List<String> campaignId = amazonAdCampaignNetargetingSps.stream()
            .filter(Objects::nonNull)
            .map(AmazonAdCampaignNetargetingSp::getCampaignId)
            .filter(StringUtils::isNotEmpty)
            .distinct()
            .collect(Collectors.toList());

        //3,查询广告活动
        Map<String, AmazonAdCampaignAll> spCampaignMap = getCampaignAllMap(shopAuth, campaignId);

        //4,查询广告组合
        Map<String, AmazonAdPortfolio> portfolioMap = getPortfolioMap(shopAuth, campaignId);

        //5,查询创建者名称
        Map<Integer, User> userMap = getUserMapForTarget(puid, amazonAdCampaignNetargetingSps);
        //查询图片
        Map<String, AsinImage> asinMap = getAsinImageMap(shopAuth, amazonAdCampaignNetargetingSps);

        //查询报告数据
        Map<String, NeTargetReportDataDto> reportMap = getReportMap(targetIdList, shopAuth, neTargetReportFilterDto, puid);

        return buildCampaignNeTargetsPageRows(targetIdList, campaignNeTargetsMap, spCampaignMap, portfolioMap, userMap, asinMap, reportMap, shopAuth);

    }

    private Map<String, NeTargetReportDataDto> getReportMap(List<String> targetIdList, ShopAuth shopAuth, NeTargetReportFilterDto neTargetReportFilterDto, Integer puid) {
        List<NeTargetReportDataDto> reportDataDtoList;
        //①从前后30天报告表中查数据
        if (StringUtils.isAnyBlank(neTargetReportFilterDto.getReportStartDate(), neTargetReportFilterDto.getReportEndDate())) {
            String reportTableName = neTargetReportFilterDto.getDataFrom() == 2 ? NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getQueryReportTableNameFor30Days() : NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getTargetReportTableNameFor30Days();
            reportDataDtoList = campaignNeTargetReportRepository.get30ReportByTargetIds(puid, shopAuth.getId(), targetIdList, reportTableName, neTargetReportFilterDto.getReportDateType());
        } else {//②从自定义时间报告表中查数据
            String reportTableName = neTargetReportFilterDto.getDataFrom() == 2 ? NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getQueryReportTableName() : NeTargetReportTableEnum.SP_CAMPAIGN_NE_TARGET.getReportTableName();
            reportDataDtoList = campaignNeTargetReportRepository.getReportByTargetIds(puid, shopAuth.getId(), targetIdList, reportTableName, neTargetReportFilterDto.getReportStartDate(), neTargetReportFilterDto.getReportEndDate());
        }
        Map<String, NeTargetReportDataDto> reportMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(reportDataDtoList)) {
             reportMap = reportDataDtoList.stream()
                .filter(reportDataDto -> Objects.nonNull(reportDataDto) && StringUtil.isNotEmpty(reportDataDto.getTargetId()))
                .collect(Collectors.toMap(NeTargetReportDataDto::getTargetId, Function.identity(), (existing, replacement) -> existing));
        }
        return reportMap;
    }

    private Map<String, AsinImage> getAsinImageMap(ShopAuth shopAuth, List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSps) {
        Map<String, AsinImage> asinMap = new HashMap<>();
        List<String> asins = amazonAdCampaignNetargetingSps.stream()
            .filter(e -> Objects.nonNull(e) && StringUtils.isNotBlank(e.getTargetText()) && StringUtils.isBlank(e.getImgUrl()))
            .map(e -> e.getTargetText().toUpperCase())
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(asins)) {
            long t4 = Instant.now().toEpochMilli();
            List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(), shopAuth.getMarketplaceId(), asins);
            log.info("获取图片花费时间 {}", Instant.now().toEpochMilli() - t4);
            if (CollectionUtils.isNotEmpty(listByAsins)) {
                asinMap = listByAsins.stream()
                    .filter(e -> Objects.nonNull(e) && StringUtils.isNotBlank(e.getAsin()))
                    .collect(Collectors.toMap(e -> e.getAsin().toUpperCase(), e1 -> e1, (e2, e3) -> e3));
            }
        }
        return asinMap;
    }

    private Map<Integer, User> getUserMapForTarget(Integer puid, List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSps) {
        Map<Integer, User> userMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(amazonAdCampaignNetargetingSps)) {
            List<Integer> createIds = amazonAdCampaignNetargetingSps.stream()
                .filter(Objects::nonNull)
                .map(AmazonAdCampaignNetargetingSp::getCreateId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(createIds)) {
                List<User> users = userDao.listByIds(puid, createIds);
                if (CollectionUtils.isNotEmpty(users)) {
                    userMap = users.stream()
                        .filter(user -> Objects.nonNull(user) && Objects.nonNull(user.getId()))
                        .collect(Collectors.toMap(User::getId, Function.identity(), (existing, replacement) -> existing));
                }
            }
        }
        return userMap;
    }

    private Map<String, AmazonAdPortfolio> getPortfolioMap(ShopAuth shopAuth, List<String> campaignId) {
        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaignId)) {
            List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), campaignId, CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                List<AmazonAdPortfolio> portfolioList = portfolioDao.getPortfolioList(shopAuth.getPuid(), shopAuth.getId(), portfolioIds);
                if (CollectionUtils.isNotEmpty(portfolioList)) {
                    portfolioMap = portfolioList.stream()
                        .filter(portfolio -> Objects.nonNull(portfolio) && StringUtil.isNotEmpty(portfolio.getPortfolioId()))
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, Function.identity(), (existing, replacement) -> existing));
                }
            }
        }
        return portfolioMap;
    }

    private Map<String, AmazonAdCampaignAll> getCampaignAllMap(ShopAuth shopAuth, List<String> campaignId) {
        Map<String, AmazonAdCampaignAll> spCampaignMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(campaignId)) {
            List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), campaignId, CampaignTypeEnum.sp.getCampaignType());
            if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                spCampaignMap = spAdCampaigns.stream()
                    .filter(campaign -> Objects.nonNull(campaign) && StringUtil.isNotEmpty(campaign.getCampaignId()))
                    .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (existing, replacement) -> existing));
            }
        }
        return spCampaignMap;
    }

    private List<CampaignNeTargetingSpRpcVo> buildCampaignNeTargetsPageRows (
        List<String> targetIdList,
        Map<String, AmazonAdCampaignNetargetingSp> campaignNetargetingsMap,
        Map<String, AmazonAdCampaignAll> spCampaignMap,
        Map<String, AmazonAdPortfolio> portfolioMap,
        Map<Integer, User> userMap,
        Map<String, AsinImage> asinMap,
        Map<String, NeTargetReportDataDto> reportMap,
        ShopAuth shopAuth) {
        return targetIdList.stream()
            .filter(campaignNetargetingsMap::containsKey)
            .map(e -> {
                AmazonAdCampaignNetargetingSp netargetingSp = campaignNetargetingsMap.get(e);
                if (StringUtils.isNotBlank(netargetingSp.getTargetText()) && StringUtils.isBlank(netargetingSp.getImgUrl())) {
                    AsinImage asinImage = asinMap.get(netargetingSp.getTargetText().toUpperCase());
                    if (asinImage != null) {
                        netargetingSp.setImgUrl(asinImage.getImage());
                        netargetingSp.setTitle(asinImage.getTitle());
                    }
                }
                CampaignNeTargetingSpRpcVo.Builder builder = CampaignNeTargetingSpRpcVo.newBuilder();
                builder.setTargetId(netargetingSp.getTargetId());
                //填充广告活动和广告组合信息
                if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(netargetingSp.getCampaignId())) {
                    fillCampaignAndPortfolioInfo(spCampaignMap, portfolioMap, builder, shopAuth.getPuid(), netargetingSp.getCampaignId());
                }
                //填充创建者
                if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(netargetingSp.getCreateId())) {
                    builder.setCreateName(userMap.get(netargetingSp.getCreateId()).getNickname());
                }
                //填充报告数据
                if (MapUtils.isNotEmpty(reportMap) && reportMap.containsKey(netargetingSp.getTargetId())) {
                    fillCampaignNeTargetReportDataIntoPageVo(builder, reportMap.get(netargetingSp.getTargetId()));
                }

                builder.setMarketplaceId(shopAuth.getMarketplaceId());

                if (StringUtils.isNotBlank(netargetingSp.getTargetText())) {
                    builder.setAsin(netargetingSp.getTargetText());
                }
                if (StringUtils.isNotBlank(netargetingSp.getCampaignId())) {
                    builder.setCampaignId(netargetingSp.getCampaignId());
                }
                if (StringUtils.isNotBlank(netargetingSp.getImgUrl())) {
                    builder.setImgUrl(netargetingSp.getImgUrl());
                }

                if (StringUtils.isNotBlank(netargetingSp.getState())) {
                    builder.setState(netargetingSp.getState());
                }
                if (StringUtils.isNotBlank(netargetingSp.getTitle())) {
                    builder.setTitle(netargetingSp.getTitle());
                }
                if (netargetingSp.getId() != null) {
                    builder.setId(Int64Value.of(netargetingSp.getId()));
                }
                if (netargetingSp.getCreationDate() != null || netargetingSp.getCreateTime() != null) {
                    if (netargetingSp.getCreationDate() != null) {
                        builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(netargetingSp.getCreationDate(), shopAuth.getMarketplaceId())));
                    } else {
                        builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTimeUtil.convertDateToLDT(netargetingSp.getCreateTime()), shopAuth.getMarketplaceId())));
                    }
                }
                builder.setType(Constants.SP);
                return builder.build();
            }).collect(Collectors.toList());
    }

    private void fillCampaignNeTargetReportDataIntoPageVo(CampaignNeTargetingSpRpcVo.Builder builder, NeTargetReportDataDto dto) {
        if (dto == null) {
            return;
        }
        if (dto.getTotalSales()!= null && dto.getCost() != null) {
            builder.setAcos((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getCost().multiply(new BigDecimal("100")).divide(dto.getTotalSales(), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getAdOrderNum() != null) {
            builder.setAdOrderNum(dto.getAdOrderNum());
        }
        if (dto.getClicks() != null) {
            builder.setClicks(dto.getClicks());
        }
        if (dto.getImpressions() != null) {
            builder.setImpressions(dto.getImpressions());
        }
        if (dto.getCost() != null) {
            builder.setAdCost(dto.getCost().doubleValue());
        }
        if (dto.getTotalSales()!= null) {
            builder.setAdSale(dto.getTotalSales().doubleValue());
        }
        if (dto.getClicks() != null && dto.getImpressions() != null) {
            builder.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getImpressions())).doubleValue());
        }
        if (dto.getAdOrderNum() != null && dto.getClicks() != null) {
            builder.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getClicks())).doubleValue());
        }
        if (dto.getTotalSales() != null && dto.getCost() != null) {
            builder.setRoas((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getTotalSales().divide(dto.getCost(), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getCost()!= null && dto.getClicks() != null) {
            builder.setAdCostPerClick((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getClicks() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getClicks()), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getCost()!= null && dto.getAdOrderNum() != null) {
            builder.setCpa((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getAdOrderNum() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getAdOrderNum()), 4, RoundingMode.HALF_UP)).doubleValue());
        }
    }

    /**
     * 填充广告活动和广告组合信息
     * @param spCampaignMap campaignId->AmazonAdCampaignAll
     * @param portfolioMap portfolioId->AmazonAdPortfolio
     * @param builder respVo
     * @param puid puid
     * @param campaignId campaignId
     */
    private void fillCampaignAndPortfolioInfo(Map<String, AmazonAdCampaignAll> spCampaignMap, Map<String, AmazonAdPortfolio> portfolioMap, CampaignNeTargetingSpRpcVo.Builder builder, Integer puid, String campaignId) {
        AmazonAdCampaignAll campaign = spCampaignMap.get(campaignId);
        if (StringUtils.isNotBlank(campaign.getName())) {
            builder.setCampaignName(campaign.getName());
        }
        if (StringUtils.isNotBlank(campaign.getState())) {
            builder.setCampaignState(campaign.getState());
        }
        if (StringUtils.isNotBlank(campaign.getTargetingType())) {
            builder.setCampaignTargetingType(campaign.getTargetingType());
        }
        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
            AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
            builder.setPortfolioId(campaign.getPortfolioId());
            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                builder.setPortfolioName(amazonAdPortfolio.getName());
                builder.setIsHidden(adPortfolio.getIsHidden());
            } else {
                builder.setPortfolioName("广告组合待同步");
            }
        } else {
            builder.setPortfolioName("-");
        }
    }



    @Override
    public Result<List<NeKeywordResponse.Data>> createNeKeywords(AddCampaignNeKeywordsVo addNeKeywordsVo, String loginIp) {
        List<NeKeywordsVo> neKeywords = addNeKeywordsVo.getNeKeywords();
        if (CollectionUtils.isEmpty(neKeywords)) {
            return ResultUtil.error("对象不存在");
        }

        // 检查参数数据
        String msg = checkAddNeKeywordsVo(neKeywords);
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error("对象不存在");
        }
        // 排除已存在的关键词
        Iterator<NeKeywordsVo> it = neKeywords.iterator();
        NeKeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdCampaignNeKeywordsDao.exist(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getCampaignId(), next.getKeywordText(), next.getMatchType())) {
                it.remove();
            }
        }

        // 都已经存在了也认为成功了
        if (neKeywords.size() == 0) {
            return ResultUtil.success();
        }

        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignAllDao.getCampaignByCampaignId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getCampaignId(),CampaignTypeEnum.sp.getCampaignType());
        if (amazonAdCampaign == null) {
            return ResultUtil.returnErr("没有活动信息");
        }

        List<SpNeKeywordsVo> spNeKeywordsVos = Lists.newArrayList();
        neKeywords.forEach(item ->{
            SpNeKeywordsVo spNeKeywordsVo = new SpNeKeywordsVo();
            spNeKeywordsVo.setPuid(amazonAdCampaign.getPuid());
            spNeKeywordsVo.setShopId(amazonAdCampaign.getShopId());
            spNeKeywordsVo.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
            spNeKeywordsVo.setProfileId(amazonAdCampaign.getProfileId());
            spNeKeywordsVo.setCampaignId(amazonAdCampaign.getCampaignId());
            spNeKeywordsVo.setIndex(item.getIndex());
            spNeKeywordsVo.setKeywordText(item.getKeywordText());
            spNeKeywordsVo.setMatchType(item.getMatchType());
            spNeKeywordsVo.setState(CpcStatusEnum.enabled.name());
            spNeKeywordsVo.setUid(addNeKeywordsVo.getUid());
            spNeKeywordsVos.add(spNeKeywordsVo);
        });
        Result result = cpcNeKeywordsApiService.createCampaignNegativeKeyword(spNeKeywordsVos);
        List<AmazonAdCampaignNeKeywords> amazonAdKeywords = convertBatchAddNeKeywordsVoToPo(spNeKeywordsVos);

        /**
         * TODO 活动否定关键词投放增加日志
         * 操作类型：否定投放新增（活动否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AmazonAdKeyword> amazonAdKeywordList = convertAmazonAdKeyword(amazonAdKeywords);
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
        List<ErrorItemResultV3> resultList = (List<ErrorItemResultV3>)result.getData();
        List<NeKeywordResponse.Data> errorList = new ArrayList<>();
        Map<Integer,ErrorItemResultV3> resultV3Map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(resultList)){
            resultV3Map = resultList.stream().collect(Collectors.toMap(ErrorItemResultV3::getIndex,e->e,(e1,e2)->e2));
        }
        int resultIndex = 0;
        for (AmazonAdKeyword keyword : amazonAdKeywordList) {
            AdManageOperationLog operationLog = adManageOperationLogService.getkeywordsLog(null, keyword);
            operationLog.setIp(loginIp);
            if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                String errors = "";
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());

                if (CollectionUtils.isNotEmpty(resultList)) {

                    ErrorItemResultV3 resultV3 = resultV3Map.get(Integer.valueOf(resultIndex));
                    String m = "添加失败";
                    if(resultV3 != null ){
                        m = resultV3.getErrors().get(0).getErrorMessage();
                    }
                    errors = "targetValue:" + keyword.getKeywordText() + ",desc:" +m;
                } else {
                    errors = result.getMsg();
                }
                NeKeywordResponse.Data.Builder builder = NeKeywordResponse.Data.newBuilder();
                builder.setKeywordText(keyword.getKeywordText());
                builder.setMatchType(keyword.getMatchType());
                errorList.add(builder.build());
                operationLog.setResultInfo(errors);
            }
            operationLogs.add(operationLog);
            resultIndex++;
        }

        adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        if (result.success()) {
            amazonAdKeywords = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
            amazonAdCampaignNeKeywordsDao.insertOnDuplicateKeyUpdate(addNeKeywordsVo.getPuid(), amazonAdKeywords);
            AmazonAdCampaignNeKeywords amazonAdCampaignNeKeywords = amazonAdKeywords.get(0);
            List<String> keywordIds = amazonAdKeywords.stream().map(AmazonAdCampaignNeKeywords::getKeywordId).collect(Collectors.toList());
            saveCampaignNekeywordDoris(amazonAdCampaignNeKeywords.getPuid(), amazonAdCampaignNeKeywords.getShopId(), keywordIds);

        }

        if (CollectionUtils.isNotEmpty(errorList) && errorList.size() > 0) {
            result.setData(errorList.stream().distinct().collect(Collectors.toList()));
        } else {
            result.setData(null);
        }

        return result;

    }

    @Override
    public Result archiveNeKeywords(Integer puid, Integer uid, String loginIp, Long id) {
        AmazonAdCampaignNeKeywords neKeywords = amazonAdCampaignNeKeywordsDao.getByPuidAndId(puid,id);
        AmazonAdKeyword adKeyword = new AmazonAdKeyword();
        adKeyword.setPuid(neKeywords.getPuid());
        adKeyword.setShopId(neKeywords.getShopId());
        adKeyword.setMarketplaceId(neKeywords.getMarketplaceId());
        adKeyword.setCampaignId(neKeywords.getCampaignId());
        adKeyword.setKeywordText(neKeywords.getKeywordText());
        adKeyword.setState(neKeywords.getState());
        adKeyword.setMatchType(neKeywords.getMatchType());
        adKeyword.setKeywordId(neKeywords.getKeywordId());
        adKeyword.setCreateId(neKeywords.getCreateId());
        adKeyword.setUpdateId(uid);
        AmazonAdKeyword oldAdKeyword = new AmazonAdKeyword();
        BeanUtils.copyProperties(adKeyword, oldAdKeyword);
        adKeyword.setState(CpcStatusEnum.archived.name());

        if (neKeywords == null) {
            return ResultUtil.error("对象不存在");
        }

        Result result = cpcNeKeywordsApiService.archiveCampaignNegativeKeyword(neKeywords);

        /**
         * TODO 活动否定关键词投放增加日志
         * 操作类型：归档活动否定关键词投放
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getkeywordsLog(oldAdKeyword, adKeyword);
        adManageOperationLog.setIp(loginIp);

        if (result.success()) {
            neKeywords.setUpdateId(uid);
            neKeywords.setState(CpcStatusEnum.archived.name());
            amazonAdCampaignNeKeywordsDao.updateById(puid,neKeywords);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            saveCampaignNekeywordDoris(Lists.newArrayList(neKeywords),true, true);
        }
        if (result.error()) {
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);

        return result;
    }


    @Override
    public Result<BatchResponseVo<BatchNeKeywordVo, AmazonAdCampaignNeKeywords>> batchArchiveNeKeywords(Integer puid, Integer shopId, Integer uid, String loginIp, List<Long> idList) {
        List<AmazonAdCampaignNeKeywords> campaignNeKeywordList = amazonAdCampaignNeKeywordsDao.getListByLongIdList(puid, idList);
        if (CollectionUtils.isEmpty(campaignNeKeywordList)) {
            return ResultUtil.returnErr("没有关键词信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<Long, AmazonAdCampaignNeKeywords> amazonSpAdCampaignNeKeywordsMap =
                campaignNeKeywordList.stream().collect(Collectors.toMap(AmazonAdCampaignNeKeywords::getId, e -> e));
        List<BatchNeKeywordVo> errorList = new ArrayList<>();
        List<AmazonAdCampaignNeKeywords> archiveList = new ArrayList<>();

        for (Long id : idList) {
            BatchNeKeywordVo vo = new BatchNeKeywordVo();
            AmazonAdCampaignNeKeywords campaignNeKeyword = amazonSpAdCampaignNeKeywordsMap.get(id);
            vo.setIsFail(false);
            vo.setUid(uid);
            vo.setId(id);
            if (campaignNeKeyword == null) {
                vo.setIsFail(true);
                vo.setFailReason("关键词不存在");
                errorList.add(vo);
                continue;
            }
            String keywordId = campaignNeKeyword.getKeywordId();
            if (StringUtils.isBlank(keywordId)) {
                vo.setIsFail(true);
                vo.setFailReason("平台keyword id 为空, 请同步该活动再操作");
                errorList.add(vo);
                continue;
            }
            AmazonAdCampaignNeKeywords campaignSpNeKeyword = new AmazonAdCampaignNeKeywords();
            BeanUtils.copyProperties(campaignNeKeyword, campaignSpNeKeyword);
            convertVoToBatchUpdateNeKeywordsPo(campaignSpNeKeyword, vo);
            campaignNeKeyword.setId(id);
            archiveList.add(campaignSpNeKeyword);
        }

        if (CollectionUtils.isEmpty(archiveList)) {
            BatchResponseVo<BatchNeKeywordVo, AmazonAdCampaignNeKeywords> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(idList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<BatchNeKeywordVo, AmazonAdCampaignNeKeywords>> result =
                cpcNeKeywordsApiService.batchArchiveCampaignNegativeKeyword(shop, profile, archiveList);
        /**
         * TODO 广告活动否定关键词投放增加日志
         * 操作类型：归档否定关键词投放
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> keywordLogs = Lists.newArrayListWithExpectedSize(2);
        List<AmazonAdKeyword> amazonAdKeywordListNew = turnToAdManageOperationKeywordLogPo(archiveList);
        List<AmazonAdKeyword> amazonAdKeywordListOld = turnToAdManageOperationKeywordLogPo(campaignNeKeywordList);
        Map<String, AmazonAdKeyword> amazonSpAdCampaignNeKeywordsOldMap = amazonAdKeywordListOld.stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e));

        for (AmazonAdKeyword newCampaignNeKeyword : amazonAdKeywordListNew) {
            AmazonAdKeyword oldCampaignAdNeKeyword = amazonSpAdCampaignNeKeywordsOldMap.get(newCampaignNeKeyword.getKeywordId());
            AdManageOperationLog adCampaignNeKeywordLog = adManageOperationLogService.getkeywordsLog(oldCampaignAdNeKeyword, newCampaignNeKeyword);
            adCampaignNeKeywordLog.setIp(loginIp);
            keywordLogs.add(adCampaignNeKeywordLog);
        }
        if (result.success()) {
            BatchResponseVo<BatchNeKeywordVo, AmazonAdCampaignNeKeywords> data = result.getData();
            List<BatchNeKeywordVo> keywordsError = data.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                keywordsError.addAll(errorList);
            }
            List<AmazonAdCampaignNeKeywords> keywordsSuccess = data.getSuccessList();
            if (CollectionUtils.isNotEmpty(keywordsSuccess)) {
                amazonAdCampaignNeKeywordsDao.batchUpdateArchive(puid, keywordsSuccess);
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},更新成功数据：{}", Constants.CPC_BATCH_UPDATE_STATUS, uid, puid, shopId, JSONUtil.objectToJson(keywordsSuccess));
                List<String> keywordIds = keywordsSuccess.stream().map(AmazonAdCampaignNeKeywords::getKeywordId).collect(Collectors.toList());
                saveCampaignNekeywordDoris(puid, shopId, keywordIds);
            }

            Map<String, AmazonAdCampaignNeKeywords> successKeywordMap = keywordsSuccess.stream().collect(Collectors.toMap(AmazonAdCampaignNeKeywords::getKeywordId, e -> e));
            Map<String, BatchNeKeywordVo> errorKeywordMap = keywordsError.stream().collect(Collectors.toMap(BatchNeKeywordVo::getKeywordId, e -> e));

            for (AdManageOperationLog keywordLog : keywordLogs) {
                if (!StringUtil.isEmptyObject(successKeywordMap.get(keywordLog.getTargetId()))) {
                    keywordLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorKeywordMap.get(keywordLog.getTargetId()))) {
                    keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    keywordLog.setResultInfo(errorKeywordMap.get(keywordLog.getTargetId()).getFailReason());
                }
            }

            data.setCountNum((keywordsError == null ? 0 : keywordsError.size())+ (keywordsSuccess == null ? 0 : keywordsSuccess.size()));
            data.setFailNum(keywordsError == null ? 0 : keywordsError.size());
            //前端不需要展示成功消息，减少消耗移除成功数据
            data.getSuccessList().clear();
        }
        if (result.error()) {
            result.setMsg("更新失败，请稍后重试");
            for (AdManageOperationLog keywordLog: keywordLogs) {
                keywordLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                keywordLog.setResultInfo(result.getMsg());
            }
        }

        //批量操作日志需根据成功/失败，然后再根据广告活动以及广告组分组
        //目的：根据分组筛选出一条记录
        adManageOperationLogService.batchLogsMergeByAdGroup(keywordLogs);
        return result;
    }

    /**
     * 广告活动-创建否定商品投放
     * @return
     */
    @Override
    public Result<List<CampaignNeTargetingSpAddReturnVo>> createNetargeting(CampaignNeTargetingSpAddParam addParam) {
        List<NeTargetingVo> targetingVos = addParam.getAsinList();
        //查询店铺
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(addParam.getShopId());
        if (shopAuth==null) {
            AssertUtil.fail("店铺未授权");
        }

        //查询活动是否存在
        AmazonAdCampaignAll existAmazonAdCampaign = amazonAdCampaignAllDao.getByCampaignId(shopAuth.getPuid(),shopAuth.getId(),shopAuth.getMarketplaceId(),addParam.getCampaignId(),CampaignTypeEnum.sp.getCampaignType());
        if (existAmazonAdCampaign== null) {
            AssertUtil.fail("没有活动信息");
        }
        if (Constants.MANUAL.equalsIgnoreCase(existAmazonAdCampaign.getTargetingType())) {
            return ResultUtil.error("SP手动类型广告活动不支持添加否定投放");
        }
        targetingVos.forEach(e -> {
            e.setCampaignId(addParam.getCampaignId());
        });
        //调用接口,创建
        List<CampaignNeTargetingSpAddReturnVo> returnVos = campaignNeTargetingApiService.create(shopAuth, targetingVos);
        if (Objects.isNull(returnVos)) {
            return ResultUtil.error("请求接口失败，请联系管理员");
        }
        /**
         * TODO 活动否定商品投放增加日志
         * 操作类型：否定投放新增（活动否定商品投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
        returnVos.forEach(vo -> {
            AmazonAdTargeting targeting = new AmazonAdTargeting();
            targeting.setPuid(addParam.getPuid());
            targeting.setShopId(shopAuth.getId());
            targeting.setMarketplaceId(shopAuth.getMarketplaceId());
            targeting.setCampaignId(vo.getCampaignId());
            targeting.setType("negativeAsin");
            targeting.setCreateId(addParam.getUid());
            targeting.setTargetId(vo.getTargetId());
            targeting.setTitle(vo.getTitle());
            targeting.setTargetingValue(vo.getAsin());
            AdManageOperationLog operationLog = adManageOperationLogService.getTargetsLog(null, targeting);
            operationLog.setIp(addParam.getLoginIp());
            if ("SUCCESS".equals(vo.getState())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(vo.getFailReason());
            }
            operationLogs.add(operationLog);
        });
        adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);

        List<AmazonAdCampaignNetargetingSp> list = new ArrayList<>();

        //保存成功数据
        if (CollectionUtils.isNotEmpty(returnVos)) {
            returnVos.forEach(re->{

                AmazonAdCampaignNetargetingSp campaignNetargetingSp = AmazonAdCampaignNetargetingSp.builder()
                        .createId(addParam.getPuid())
                        .state(StateEnum.ENABLED.value())
                        .createState(re.getState())
                        .campaignId(re.getCampaignId())
                        .createInAmzup(1)
                        .targetText(re.getAsin())
                        .targetId(re.getTargetId())
                        .title(re.getTitle())
                        .imgUrl(re.getImgUrl())
                        .puid(shopAuth.getPuid())
                        .shopId(shopAuth.getId())
                        .marketplaceId(shopAuth.getMarketplaceId())
                        .type("asin").build();
                list.add(campaignNetargetingSp);
            });
        } else {
            return ResultUtil.error("请求接口失败");
        }

        //批量保存数据
        if (CollectionUtils.isNotEmpty(list)) {
            campaignNetargetingSpDao.batchSave(shopAuth.getPuid(), list);
            List<String> collect = list.stream().map(AmazonAdCampaignNetargetingSp::getTargetId).collect(Collectors.toList());
            saveCampaignNetargetDoris(shopAuth.getPuid(), shopAuth.getId(), collect);
        }

        return ResultUtil.successWithData(returnVos);
    }

    /**
     * 广告活动-否定商品投放-归档
     * @return
     * @param id
     */
    @Override
    public Map<Boolean, String> archiveNetargeting(Integer shopId,Integer uid,Long id, String loginIp) {

        //查询店铺
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth == null) {
            AssertUtil.fail("店铺不存在");
        }
        //查询是否存在
        AmazonAdCampaignNetargetingSp existNetargeting = campaignNetargetingSpDao.getByPuidAndId(shopAuth.getPuid(), id);
        AmazonAdTargeting adTargeting = new AmazonAdTargeting();
        adTargeting.setPuid(existNetargeting.getPuid());
        adTargeting.setShopId(existNetargeting.getShopId());
        adTargeting.setMarketplaceId(existNetargeting.getMarketplaceId());
        adTargeting.setCampaignId(existNetargeting.getCampaignId());
        adTargeting.setTargetingValue(existNetargeting.getTargetText());
        adTargeting.setState(existNetargeting.getState());
        adTargeting.setTargetId(existNetargeting.getTargetId());
        adTargeting.setCreateId(existNetargeting.getCreateId());
        adTargeting.setUpdateId(uid);
        AmazonAdTargeting oldTargeting = new AmazonAdTargeting();
        BeanUtils.copyProperties(adTargeting, oldTargeting);
        adTargeting.setState(CpcStatusEnum.archived.name());

        if (existNetargeting == null || existNetargeting.getTargetId()==null) {
            AssertUtil.fail("record not exist");
        }
        //请求接口
        Map<Boolean, String> archive = campaignNeTargetingApiService.archive(shopAuth, existNetargeting.getTargetId());
        String falseStr = archive.get(Boolean.FALSE);
        String trueStr = archive.get(Boolean.TRUE);
        /**
         * TODO 活动否定商品投放增加日志
         * 操作类型：归档活动否定商品投放
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getTargetsLog(oldTargeting, adTargeting);
        adManageOperationLog.setIp(loginIp);

        //调用接口成功 更新记录状态
        if (trueStr != null) {
            campaignNetargetingSpDao.updateById(shopAuth.getPuid(), AmazonAdCampaignNetargetingSp.builder()
                    .id(existNetargeting.getId())
                    .state(Constants.ARCHIVED)
                    .updateId(uid).build());
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            adManageOperationLogs.add(adManageOperationLog);
            adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
            return archive;
        }
        adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return archive;
    }


    /**
     * 广告活动-否定商品投放-批量归档
     * @param puid
     * @param shopId
     * @param uid
     * @param loginIp
     * @param idList
     * @return
     */
    @Override
    public Result<BatchResponseVo<BatchNeTargetVo, AmazonAdCampaignNetargetingSp>> batchArchiveNetargeting(Integer puid, Integer shopId, Integer uid, String loginIp, List<Long> idList) {
        List<AmazonAdCampaignNetargetingSp> spNeTargetingList = campaignNetargetingSpDao.getListByLongIdList(puid, idList);
        if (CollectionUtils.isEmpty(spNeTargetingList)) {
            return ResultUtil.returnErr("没有否定商品投放信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<Long, AmazonAdCampaignNetargetingSp> amazonSpAdCampaignNeTargetingMap =
                spNeTargetingList.stream().collect(Collectors.toMap(AmazonAdCampaignNetargetingSp::getId, e -> e));
        List<BatchNeTargetVo> errorList = new ArrayList<>();
        List<AmazonAdCampaignNetargetingSp> archiveList = new ArrayList<>();

        for (Long id : idList) {
            BatchNeTargetVo vo = new BatchNeTargetVo();
            AmazonAdCampaignNetargetingSp campaignSpNeTargeting = amazonSpAdCampaignNeTargetingMap.get(id);
            vo.setIsFail(false);
            vo.setUid(uid);
            vo.setId(id);
            if (campaignSpNeTargeting == null) {
                vo.setIsFail(true);
                vo.setFailReason("否定商品投放不存在");
                errorList.add(vo);
                continue;
            }
            String targetId = campaignSpNeTargeting.getTargetId();
            if (StringUtils.isBlank(targetId)) {
                vo.setIsFail(true);
                vo.setFailReason("平台target id 为空, 请同步该活动再操作");
                errorList.add(vo);
                continue;
            }
            AmazonAdCampaignNetargetingSp amazonCampaignNeTargetingSp = new AmazonAdCampaignNetargetingSp();
            BeanUtils.copyProperties(campaignSpNeTargeting, amazonCampaignNeTargetingSp);
            convertVoToBatchUpdateNeTargetingsPo(amazonCampaignNeTargetingSp, vo);
            amazonCampaignNeTargetingSp.setId(id);
            archiveList.add(amazonCampaignNeTargetingSp);
        }

        if (CollectionUtils.isEmpty(archiveList)) {
            BatchResponseVo<BatchNeTargetVo, AmazonAdCampaignNetargetingSp> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(idList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<BatchNeTargetVo, AmazonAdCampaignNetargetingSp>> result = campaignNeTargetingApiService.update(shop, profile, archiveList);
        /**
         * TODO 广告活动否定商品投放增加日志
         * 操作类型：批量编辑商品投放竞价
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> targetsLogs = Lists.newArrayListWithExpectedSize(2);
        List<AmazonAdTargeting> adTargetingListNew = turnToAdManageOperationTargetLogPo(archiveList);
        List<AmazonAdTargeting> adTargetingListOld = turnToAdManageOperationTargetLogPo(spNeTargetingList);
        Map<String, AmazonAdTargeting> amazonSpAdCampaignTargetingOldMap = adTargetingListOld.stream().collect(Collectors.toMap(AmazonAdTargeting::getTargetId, e -> e));
        for (AmazonAdTargeting newAmazonAdTargeting : adTargetingListNew) {
            AdManageOperationLog targetsLog = adManageOperationLogService.getTargetsLog(amazonSpAdCampaignTargetingOldMap.get(newAmazonAdTargeting.getTargetId()), newAmazonAdTargeting);
            //日志新增ip
            targetsLog.setIp(loginIp);
            targetsLogs.add(targetsLog);
        }
        if (result.success()) {
            // 记录操作日志
            BatchResponseVo<BatchNeTargetVo, AmazonAdCampaignNetargetingSp> data = result.getData();
            List<BatchNeTargetVo> targetsError = data.getErrorList();
            if(CollectionUtils.isNotEmpty(errorList)){
                targetsError.addAll(errorList);
            }
            List<AmazonAdCampaignNetargetingSp> amazonAdTargetsSuccess = data.getSuccessList();

            if(CollectionUtils.isNotEmpty(amazonAdTargetsSuccess)){
                campaignNetargetingSpDao.batchUpdateArchive(puid, amazonAdTargetsSuccess);
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},更新成功数据：{}", Constants.CPC_BATCH_UPDATE_STATUS, uid, puid, shopId, JSONUtil.objectToJson(amazonAdTargetsSuccess));
                List<String> list = amazonAdTargetsSuccess.stream().map(AmazonAdCampaignNetargetingSp::getTargetId).collect(Collectors.toList());
                saveCampaignNetargetDoris(puid, shopId, list);
            }
            //收集日志
            Map<String, AmazonAdCampaignNetargetingSp> successTargetsMap = amazonAdTargetsSuccess.stream().collect(Collectors.toMap(AmazonAdCampaignNetargetingSp::getTargetId,e -> e));
            Map<String, BatchNeTargetVo> errorTargetsMap = targetsError.stream().collect(Collectors.toMap(BatchNeTargetVo::getTargetId,e -> e));
            for (AdManageOperationLog targetsLog: targetsLogs) {
                if (!StringUtil.isEmptyObject(successTargetsMap.get(targetsLog.getTargetId()))) {
                    targetsLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorTargetsMap.get(targetsLog.getTargetId()))) {
                    targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    targetsLog.setResultInfo(errorTargetsMap.get(targetsLog.getTargetId()).getFailReason());
                }
            }
            data.setCountNum((targetsError == null ? 0 : targetsError.size())+ (amazonAdTargetsSuccess == null ? 0 : amazonAdTargetsSuccess.size()));
            data.setFailNum(targetsError == null ? 0 : targetsError.size());
            //前端不需要展示成功消息，减少消耗移除成功数据
            data.getSuccessList().clear();

        }
        if (result.error()) {
            result.setMsg("更新失败，请稍后重试");
            for (AdManageOperationLog targetsLog: targetsLogs) {
                targetsLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                targetsLog.setResultInfo(result.getMsg());
            }
        }
        adManageOperationLogService.printAdOperationLog(targetsLogs);
        return result;
    }

    /**
     * 广告活动-否定商品投放-分页列表
     * @return
     */
    @Override
    public Page<CampaignNeTargetingSpRpcVo> neTargetingPageList(CampaignNeTargetingSpParam campaignNeTargetingSpParam) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(campaignNeTargetingSpParam.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺不存在");
        }

        List<String> campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(shopAuth.getPuid(), campaignNeTargetingSpParam.getShopId(), campaignNeTargetingSpParam.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), campaignNeTargetingSpParam.getStatus(), campaignNeTargetingSpParam.getServingStatus());
        if (CollectionUtils.isNotEmpty(campaignIds)) {  // 用户选了广告组合又筛选了活动  从广告组合里面的活动列表在做一个过滤
            campaignNeTargetingSpParam.setCampaignIdList(campaignIds);
        } else {
            return new Page<>(campaignNeTargetingSpParam.getPageNo(), campaignNeTargetingSpParam.getPageSize());
        }

        Page<AmazonAdCampaignNetargetingSp> page = campaignNetargetingSpDao.pageList(shopAuth.getPuid(), campaignNeTargetingSpParam);

        List<CampaignNeTargetingSpRpcVo> campaignNeTargetingSpVos = new ArrayList<>();
        List<AmazonAdCampaignNetargetingSp> rows = page.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {

            List<String> spCampaignIds = rows.stream().filter(Objects::nonNull).map(AmazonAdCampaignNetargetingSp::getCampaignId).collect(Collectors.toList());

            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            //sp广告活动
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignAllDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), shopAuth.getMarketplaceId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<String> portfolioIds = amazonAdCampaignAllDao.getPortfolioListByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), spCampaignIds,CampaignTypeEnum.sp.getCampaignType());
                if (CollectionUtils.isNotEmpty(portfolioIds)) {
                    portfolioMap = portfolioDao.getPortfolioList(shopAuth.getPuid(), shopAuth.getId(), portfolioIds).stream()
                            .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
                }
            }
            List<String> asins = rows.stream().filter(e -> StringUtils.isNotBlank(e.getTargetText()) && StringUtils.isBlank(e.getImgUrl())).map(e -> e.getTargetText().toUpperCase()).distinct().collect(Collectors.toList());
            Map<String, AsinImage> asinMap = new HashMap<>();
            if(CollectionUtils.isNotEmpty(asins)){
                long t4 = Instant.now().toEpochMilli();
                List<AsinImage> listByAsins = syncAsinImageService.getListByAsinsNoSave(shopAuth.getPuid(),shopAuth.getMarketplaceId(),asins);
                log.info("获取图片花费时间 {}", Instant.now().toEpochMilli() - t4);
                asinMap = listByAsins.stream().filter(e->StringUtils.isNotBlank(e.getAsin())).collect(Collectors.toMap(e->e.getAsin().toUpperCase(),e1->e1,(e2,e3)->e3));
            }
            Map<String, AmazonAdCampaignAll> finalSpCampaignMap = spCampaignMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;
            Map<String, AsinImage> finalAsinMap = asinMap;
            rows.forEach(e-> {
                if(StringUtils.isNotBlank(e.getTargetText()) && StringUtils.isBlank(e.getImgUrl())){
                    AsinImage asinImage = finalAsinMap.get(e.getTargetText().toUpperCase());
                    if(asinImage != null){
                        e.setImgUrl(asinImage.getImage());
                        e.setTitle(asinImage.getTitle());
                    }
                }
                CampaignNeTargetingSpRpcVo.Builder builder = CampaignNeTargetingSpRpcVo.newBuilder();
                if (MapUtils.isNotEmpty(finalSpCampaignMap) && finalSpCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalSpCampaignMap.get(e.getCampaignId());
                    if (StringUtils.isNotBlank(campaign.getName())) {
                        builder.setCampaignName(campaign.getName());
                    }
                    if (StringUtils.isNotBlank(campaign.getState())) {
                        builder.setCampaignState(campaign.getState());
                    }
                    if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                        builder.setCampaignTargetingType(campaign.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                        builder.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            builder.setPortfolioName(amazonAdPortfolio.getName());
                            builder.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            builder.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        builder.setPortfolioName("-");
                    }
                }

                if (shopAuth != null) {
                    builder.setMarketplaceId(shopAuth.getMarketplaceId());
                }

                if (StringUtils.isNotBlank(e.getTargetText())) {
                    builder.setAsin(e.getTargetText());
                }
                if (StringUtils.isNotBlank(e.getCampaignId())) {
                    builder.setCampaignId(e.getCampaignId());
                }
                if (StringUtils.isNotBlank(e.getImgUrl())) {
                    builder.setImgUrl(e.getImgUrl());
                }

                if (StringUtils.isNotBlank(e.getState())) {
                    builder.setState(e.getState());
                }
                if (StringUtils.isNotBlank(e.getTitle())) {
                    builder.setTitle(e.getTitle());
                }
                if (e.getId()!=null) {
                    builder.setId(Int64Value.of(e.getId()));
                }
                if (e.getCreationDate() != null || e.getCreateTime() != null) {
                    if (e.getCreationDate() != null) {
                        builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(e.getCreationDate(), shopAuth.getMarketplaceId())));
                    } else {
                        builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTimeUtil.convertDateToLDT(e.getCreateTime()), shopAuth.getMarketplaceId())));
                    }
                }
                builder.setType(Constants.SP);

                campaignNeTargetingSpVos.add(builder.build());
            });
        }
        Page<CampaignNeTargetingSpRpcVo> spVoPage = new Page<>();
        spVoPage.setPageNo(page.getPageNo());
        spVoPage.setPageSize(page.getPageSize());
        spVoPage.setTotalPage(page.getTotalPage());
        spVoPage.setTotalSize(page.getTotalSize());
        spVoPage.setRows(campaignNeTargetingSpVos);
        return spVoPage;
    }

    @Override
    public Result<List<CampaignNameVo>> listForQueryWord(int puid, Integer shopId, String adaptType) {
        List<CampaignNameVo> nameVos = new ArrayList<>();

        List<String> adGroupTypes = null;
        if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_KEYWORD.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_KEYWORD);
        } else if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_NE_KEYWORD.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_KEYWORD, Constants.GROUP_TYPE_AUTO);
        } else if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_TARGET.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_TARGETING);
        } else if (Constants.CPC_CAMPAIGN_ADAPT_TYPE_NE_TARGET.equalsIgnoreCase(adaptType)) {
            adGroupTypes = Lists.newArrayList(Constants.GROUP_TYPE_TARGETING, Constants.GROUP_TYPE_AUTO);
        }
        if (CollectionUtils.isNotEmpty(adGroupTypes)) {
            List<AmazonAdGroup> amazonAdGroups = amazonAdGroupDao.getListByType(puid, shopId, adGroupTypes);
            List<String> campaignIds = amazonAdGroups.stream().map(AmazonAdGroup::getCampaignId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                nameVos = amazonAdCampaignAllDao.getByCampaignIds(puid, shopId, amazonAdGroups.get(0).getMarketplaceId(), campaignIds,CampaignTypeEnum.sp.getCampaignType())
                        .stream().filter(e -> CpcStatusEnum.validList().contains(e.getState())).map(CampaignNameVo::new).collect(Collectors.toList());
            }
        }

        return ResultUtil.returnSucc(nameVos);
    }

    @Override
    public Result topBudget(int puid, Integer shopId, Double budget) {
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        UpdateDailyBudgetResponse response = ProfileClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).updateDailyBudget(shopAuthService.getAdToken(shop), shop.getMarketplaceId(),
                Long.valueOf(profile.getProfileId()), budget);
        if (response != null && response.getError() != null && Constants.UNAUTHORIZED.equals(response.getError().getCode())) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = ProfileClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).updateDailyBudget(shopAuthService.getAdToken(shop), shop.getMarketplaceId(),
                    Long.valueOf(profile.getProfileId()), budget);
        }

        int retry = 1;
        // 出现429，使用指数回避策略重试
        while (response != null && response.getStatusCode() == AmazonAdUtils.rateLimitingCode) {
            log.info(" profile update rate limiting code: {}", AmazonAdUtils.rateLimitingCode);
            if(retry > AmazonAdUtils.retry) {
                break;
            }
            response = ProfileClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).updateDailyBudget(shopAuthService.getAdToken(shop), shop.getMarketplaceId(),
                    Long.valueOf(profile.getProfileId()), budget);
            retry++;
        }

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        if (CollectionUtils.isNotEmpty(response.getProfiles())) {
            Profile profile1 = response.getProfiles().get(0);
            if (!"SUCCESS".equals(profile1.getCode())) {
                return ResultUtil.error(profile1.getDescription());
            }

            profile.setDailyBudget(budget);
            amazonAdProfileDao.updateByIdAndPuid(profile);
            try {
                updateTopBudget(shop,budget);
            } catch (Exception e) {
                log.error("分时顶级预算数据回写失败:"+e);
                return ResultUtil.returnErr("分时顶级预算数据回写失败:"+e);
            }

            return ResultUtil.success();
        }

        String msg = "网络延迟，请稍后重试";
        if (response.getError() != null) {
            msg = response.getError().getDetails();
        }
        return ResultUtil.error(msg);
    }

    private void updateTopBudget(ShopAuth shopAuth,Double budget) {
        double topBudget = 9.99999999E8;
        AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate =
                advertiseStrategyTopBudgetTemplateDao.selectById(shopAuth.getPuid(),shopAuth.getId());
        if (advertiseStrategyTopBudgetTemplate != null && advertiseStrategyTopBudgetTemplate.getStatus().equals("DISABLED")) {
            OriginToBudgetValueVo reductionValueVo = new OriginToBudgetValueVo();
            if (budget != null && budget < topBudget) {
                reductionValueVo.setUseTopBudget(true);
                reductionValueVo.setToBudgetValue(BigDecimal.valueOf(budget));
            } else {
                reductionValueVo.setUseTopBudget(false);
            }
            String reductionValueJson = JSONUtil.objectToJson(reductionValueVo);

            advertiseStrategyTopBudgetTemplateDao.updateReductionValue(advertiseStrategyTopBudgetTemplate.getPuid(),
                    advertiseStrategyTopBudgetTemplate.getId(),reductionValueJson);
            advertiseStrategyTopBudgetScheduleDao.updateReductionValue(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getTaskId(),
                    reductionValueJson);
        }
    }

    @Override
    public Result<String> createCampaign(CampaignVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();
        vo.setName(StringUtil.trimAndClearInvisibleChar(vo.getName()));

        //判断活动名称是否存在
        if (amazonAdCampaignAllDao.exist(puid, shopId, vo.getName(), CampaignTypeEnum.sp.getCampaignType())) {
            return ResultUtil.returnErr("名称已存在");
        }

        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        AmazonAdCampaignAll amazonAdCampaign = convertVoToCreatePo(vo, amazonAdProfile);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("店铺不存在");
        }

        CampaignEntityV3 campaign = makeCampaignByCampaignPoV3(amazonAdCampaign);

        CreateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
            amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).createCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        //处理返回结果中的错误信息
        String errMsg = "创建广告活动失败";
        if (response.getData() != null) {
            ApiResponseV3<campaignSuccessResultV3> campaigns = response.getData().getCampaigns();
            if (CollectionUtils.isNotEmpty(campaigns.getSuccess())) {
                amazonAdCampaign.setCampaignId(campaigns.getSuccess().get(0).getCampaignId());
                amazonAdCampaign.setType(CampaignTypeEnum.sp.getCampaignType());
                try {
                    amazonAdCampaignAllDao.save(puid, amazonAdCampaign);

                    saveDoris(Collections.singletonList(amazonAdCampaign), true, true);

                    //创建后同步一次 广告二级活动状态
                    cpcAdSyncService.syncSpCampaignState(shop, campaigns.getSuccess().get(0).getCampaignId());

                    return ResultUtil.returnSucc(amazonAdCampaign.getCampaignId());
                } catch (Exception e) {
                    log.error("createCampaign:", e);
                }
            } else if (CollectionUtils.isNotEmpty(campaigns.getError())) {
                if (CollectionUtils.isNotEmpty(campaigns.getError().get(0).getErrors()) && StringUtils.isNotBlank(campaigns.getError().get(0).getErrors().get(0).getErrorMessage())) {
                    errMsg = AmazonErrorUtils.getError(campaigns.getError().get(0).getErrors().get(0).getErrorMessage());
                }

            }
        } else if (response.getError() != null) {

            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }

        return ResultUtil.returnErr(errMsg);
    }

    @Override
    public Result<String> createCampaignWithAuthed(CampaignVo vo, ShopAuth shop, AmazonAdProfile amazonAdProfile) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();
        vo.setName(StringUtil.trimAndClearInvisibleChar(vo.getName()));

        if (amazonAdCampaignAllDao.exist(puid, shopId, vo.getName(), CampaignTypeEnum.sp.getCampaignType())) {
            return ResultUtil.returnErr("广告活动名称已存在");
        }
        //企业购某些站点不支持
        if (!com.meiyunji.sponsored.service.cpc.util.Constants.placementSiteAmazonBusinessMarketplaceIds.contains(shop.getMarketplaceId())) {
            vo.setPlacementSiteAmazonBusiness(null);
        }

        AmazonAdCampaignAll amazonAdCampaign = convertVoToCreatePo(vo, amazonAdProfile);
        if(StringUtils.isNotEmpty(vo.getState())){
            // 复制广告 可以控制状态开启、关闭
            amazonAdCampaign.setState(vo.getState());
        }
        CampaignEntityV3 campaign = makeCampaignByCampaignPoV3(amazonAdCampaign);

        CreateSpCampaignV3Response response = createCampaign(shop, amazonAdCampaign, campaign);
        if (response == null) {
            return ResultUtil.returnErr("网络延迟，请稍后重试");
        }

        return processCampaignResponse(response, amazonAdCampaign, vo, shop);
    }

    private CreateSpCampaignV3Response createCampaign(ShopAuth shop, AmazonAdCampaignAll amazonAdCampaign, CampaignEntityV3 campaign) {
        CreateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
            .createCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(campaign), true);

        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable())
                .createCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Lists.newArrayList(campaign), true);
        }

        return response;
    }

    private Result<String> processCampaignResponse(CreateSpCampaignV3Response response, AmazonAdCampaignAll amazonAdCampaign, CampaignVo vo, ShopAuth shop) {
        String errMsg = "创建广告活动失败";

        if (response.getData() != null) {
            ApiResponseV3<campaignSuccessResultV3> campaigns = response.getData().getCampaigns();
            if (CollectionUtils.isNotEmpty(campaigns.getSuccess())) {
                amazonAdCampaign.setCampaignId(campaigns.getSuccess().get(0).getCampaignId());
                amazonAdCampaign.setType(CampaignTypeEnum.sp.getCampaignType());
                saveCampaignData(amazonAdCampaign, vo, shop);
                return ResultUtil.returnSucc(amazonAdCampaign.getCampaignId());
            } else if (CollectionUtils.isNotEmpty(campaigns.getError())) {
                if (CollectionUtils.isNotEmpty(campaigns.getError().get(0).getErrors()) && StringUtils.isNotBlank(campaigns.getError().get(0).getErrors().get(0).getErrorMessage())) {
                    errMsg = AmazonErrorUtils.getError(campaigns.getError().get(0).getErrors().get(0).getErrorMessage());
                }
            }
        } else if (response.getError() != null) {
            if (StringUtils.isNotBlank(response.getError().getMessage())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getMessage());
            } else if (CollectionUtils.isNotEmpty(response.getError().getErrors())) {
                errMsg = AmazonErrorUtils.getError(response.getError().getErrors().get(0).getErrorMessage());
            }
        }

        return ResultUtil.returnErr(errMsg);
    }

    private void saveCampaignData(AmazonAdCampaignAll amazonAdCampaign, CampaignVo vo, ShopAuth shop) {
        int puid = vo.getPuid();
        try {
            amazonAdCampaignAllDao.save(puid, amazonAdCampaign);
        } catch (Exception e) {
            log.error("createCampaign, save TdSql error:", e);
        }
        try {
            saveDoris(Collections.singletonList(amazonAdCampaign), true, true);
        } catch (Exception e) {
            log.error("createCampaign, save doris error:", e);
        }
        //异步同步一次广告活动状态，等整个广告创建完成之后再同步以下活动状态
        CompletableFuture.runAsync(() -> cpcAdSyncService.syncSpCampaignState(shop, amazonAdCampaign.getCampaignId()), ThreadPoolUtil.getCreateAdForSyncExecutor()).exceptionally(e -> {
            log.error("createCampaign, sync sp campaign state error:", e);
            return null;
        });
    }

    @Override
    public Result updateCampaign(CampaignVo vo) {
        int puid = vo.getPuid();
        int shopId = vo.getShopId();

        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid, vo.getDxmCampaignId());
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }

        //判断活动名称是否存在
        if (!oldAmazonAdCampaign.getName().equals(vo.getName()) && amazonAdCampaignAllDao.exist(puid, shopId, vo.getName().trim(),CampaignTypeEnum.sp.getCampaignType())) {
            return ResultUtil.error("名称已存在");
        }

        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);
        convertVoToUpdatePo(amazonAdCampaign, vo);


        //广告开始时间不修改,不传值
        boolean isUpdateStartDate = true;
        if (oldAmazonAdCampaign.getStartDateStr().equals(amazonAdCampaign.getStartDateStr())) {
            isUpdateStartDate = false;
        }

        Result result = commUpdate(amazonAdCampaign, isUpdateStartDate);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(oldAmazonAdCampaign, amazonAdCampaign);

        adManageOperationLog.setIp(vo.getLoginIp());
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            //修改预算标识
            this.fillBudgetAndBidUpdateKey(amazonAdCampaign, oldAmazonAdCampaign);
        }
        if (result.error()) {
            result.setMsg(StringUtils.isNotBlank(result.getMsg()) ? result.getMsg() : "更新失败，请稍后重试");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        // end
        return result;
    }

    @Override
    public Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> batchUpdateCampaign(List<BatchCampaignVo> vos, Integer puid, Integer uid, Integer shopId, String type) {

        String ip = vos.get(0).getLoginIp();
        List<BatchCampaignVo> errorList = Lists.newArrayList();

        List<Long> ids = vos.stream().filter(e-> e.getDxmCampaignId()!= null && e.getDxmCampaignId()>0).map(BatchCampaignVo::getDxmCampaignId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(ids)){
            return ResultUtil.error("参数错误");
        }
        List<AmazonAdCampaignAll> listByIdList = amazonAdCampaignAllDao.getListByLongIdList(puid, ids);
        List<AmazonAdCampaignAll> updateList = Lists.newArrayList();
        Set<String> dailyBudgetUpdateList = new HashSet<>();
        Set<String> placementProductPageUpdateList = new HashSet<>();
        Set<String> placementTopUpdateList = new HashSet<>();
        Set<String> placementRestOfSearchUpdateList = new HashSet<>();
        Set<String> placementSiteAmazonBusinessUpdateList = new HashSet<>();
        Map<Long, AmazonAdCampaignAll> amazonAdCampaignMap = listByIdList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getId, e -> e));
        for (BatchCampaignVo vo: vos) {
            checkBatchVo(vo, type);
            if(vo.getDxmCampaignId() != null){
                vo.setId(vo.getDxmCampaignId());
            }
            AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignMap.get(vo.getDxmCampaignId());
            if (oldAmazonAdCampaign == null) {
                vo.setFailReason("对象不存在");
                errorList.add(vo);
                continue;
            }
            //记录竞价修改的id，用于记录修改标识
            this.fillBudgetAndBidUpdateKey(dailyBudgetUpdateList, placementProductPageUpdateList, placementTopUpdateList, placementRestOfSearchUpdateList, placementSiteAmazonBusinessUpdateList, vo, oldAmazonAdCampaign);
            if(StringUtils.isNotBlank(vo.getFailReason())){
                vo.setName(oldAmazonAdCampaign.getName());
                vo.setCampaignId(oldAmazonAdCampaign.getCampaignId());
                errorList.add(vo);
                continue;
            }
            AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
            BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);
            convertVoToBatchUpdatePo(amazonAdCampaign, vo,type);
            updateList.add(amazonAdCampaign);
        }
        if(CollectionUtils.isEmpty(updateList)){
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = new BatchResponseVo();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(vos.size());
            data.setFailNum(errorList.size());
            return ResultUtil.success(data);
        }
        Result result = commUpdateBatch(updateList,type);
        /**
         * TODO 广告活动增加日志
         * 操作类型：批量调整广告活动每日预算，广告位，状态
         * 逻辑：创建广告新对象（每日预算，广告位，状态）修改
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        for (AmazonAdCampaignAll newAdCampaign : updateList){
            AmazonAdCampaignAll oldAdCampaign = amazonAdCampaignMap.get(newAdCampaign.getId());
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(oldAdCampaign, newAdCampaign);
            adManageOperationLog.setIp(ip);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            //记操作日志
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = (BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>) result.getData();
            List<BatchCampaignVo> errorList1 = data.getErrorList();
            if(CollectionUtils.isNotEmpty(errorList)){
                errorList1.addAll(errorList);

            }
            List<AmazonAdCampaignAll> campaignSuccess = data.getSuccessList();

            if(CollectionUtils.isNotEmpty(campaignSuccess)){
                //记录预算竞价修改标识
                campaignSuccess.forEach(e -> {
                    if (dailyBudgetUpdateList.contains(e.getCampaignId())) {
                        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), e.getCampaignId());
                        stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
                    }
                    if (placementTopUpdateList.contains(e.getCampaignId())) {
                        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode(), e.getCampaignId());
                        stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
                    }
                    if (placementProductPageUpdateList.contains(e.getCampaignId())) {
                        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode(), e.getCampaignId());
                        stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
                    }
                    if (placementRestOfSearchUpdateList.contains(e.getCampaignId())) {
                        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode(), e.getCampaignId());
                        stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
                    }
                    if (placementSiteAmazonBusinessUpdateList.contains(e.getCampaignId())) {
                        String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_SITE_AMAZON_BUSINESS.getCode(), e.getCampaignId());
                        stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
                    }
                });
                //更新成功数据打日志
                log.info("用户批量更新成功，typ:{},updateId:{},puid :{},shopid:{},logoinIp:{},更新成功数据：{}",type,uid,puid,shopId,ip,JSONUtil.objectToJson(campaignSuccess));
            }
            //收集失败/成功日志
            Map<String, AmazonAdCampaignAll> successDataMap = campaignSuccess.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e->e));
            Map<String, BatchCampaignVo> errorDataMap = errorList1.stream().collect(Collectors.toMap(BatchCampaignVo::getCampaignId, e-> e));
            for (AdManageOperationLog adManageOperationLog: adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getCampaignId()).getFailReason());
                }
            }

            data.setCountNum( (errorList1 == null? 0 : errorList1.size())+(campaignSuccess == null? 0 : campaignSuccess.size())) ;
            data.setFailNum( errorList1 == null? 0 : errorList1.size());
            //前端不需要展示成功消息，减少消耗移除成功数据
            data.getSuccessList().clear();
        }
        if (result.error()) {
            result.setMsg("更新失败，请稍后重试");
            for (AdManageOperationLog adManageOperationLog: adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
        }
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> batchUpdateMultiShopCampaign(List<BatchCampaignVo> vos, Integer puid, Integer uid, String type) {
        for (BatchCampaignVo batchCampaignVo :vos) {
            if (StringUtils.isBlank(batchCampaignVo.getType())) {
                batchCampaignVo.setType(Constants.SP);
            }
        }
        //根据店铺分组
        Map<Integer, List<BatchCampaignVo>> voMap = vos.stream().collect(Collectors.groupingBy(BatchCampaignVo::getShopId));
//        String adType = vos.get(0).getType();
        BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> batchData = new BatchResponseVo<>();
        Result<BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>> result;
        BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data;
        //根据店铺批量调用更新接口
        for (Map.Entry<Integer, List<BatchCampaignVo>> entry : voMap.entrySet()) {
            //根据类型分组
            Map<String, List<BatchCampaignVo>> listMap = entry.getValue().stream().collect(Collectors.groupingBy(BatchCampaignVo::getType));
            for (Map.Entry<String, List<BatchCampaignVo>> entryList : listMap.entrySet()) {
                if (Constants.SD.equalsIgnoreCase(entryList.getKey())) {
                    result = sdCampaignService.updateBatchCampaign(entryList.getValue(), type);
                } else if (Constants.SB.equalsIgnoreCase(entryList.getKey())) {
                    try {
                        result = sbCampaignService.updateBatchCampaign(entryList.getValue(), type);
                    } catch (InterruptedException e) {
                        log.error("updateBatchCampaign error", e);
                        throw new RuntimeException(e);
                    }
                } else {
                    result = this.batchUpdateCampaign(entryList.getValue(), puid, uid, entry.getKey(), type);
                }
                if (result.error()) {
                    return ResultUtil.error(result.getMsg());
                }
                data = result.getData();
                if (data != null) {
                    batchData.addCountNum(data.getCountNum());
                    batchData.addSuccessNum(data.getSuccessNum());
                    batchData.addFailNum(data.getFailNum());
                    batchData.addSuccessList(data.getSuccessList());
                    batchData.addErrorList(data.getErrorList());
                }
            }
        }
        return ResultUtil.returnSucc(batchData);
    }

    @Override
    public Result updateStatus(Integer puid, Integer uid, String loginIp, Long id, CpcStatusEnum statusEnum) {
        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdCampaignAll adCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, adCampaign);


        String oldState = oldAmazonAdCampaign.getState();
        if (oldState.equals(statusEnum.name())) {
            return ResultUtil.success();
        }

        oldAmazonAdCampaign.setState(statusEnum.name());
        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignState(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动状态
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(adCampaign, oldAmazonAdCampaign);

        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateName(Integer puid, Integer uid, String loginIp, Long id, String name) {
        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);


        //判断活动名称是否存在
        String oldName = oldAmazonAdCampaign.getName();
        if (!oldName.equals(name) && amazonAdCampaignAllDao.exist(puid, oldAmazonAdCampaign.getShopId(), name.trim(),CampaignTypeEnum.sp.getCampaignType())) {
            return ResultUtil.error("名称已存在");
        }

        oldAmazonAdCampaign.setName(name.trim());
        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignName(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动名称
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, oldAmazonAdCampaign);
        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateBudget(Integer puid, Integer uid, String loginIp, Long id, Double budget) {
        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }
        if (StateEnum.ARCHIVED.value().equals(oldAmazonAdCampaign.getState())) {
            return ResultUtil.error("已归档活动不支持编辑");
        }
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);


        Double oldDailyBudget = oldAmazonAdCampaign.getBudget() == null ?  null  : oldAmazonAdCampaign.getBudget().doubleValue() ;
        if (Objects.equals(oldDailyBudget, budget)) {
            return ResultUtil.success();
        }

        //校验站点广告最大值最小值
        if (StringUtils.isNotBlank(oldAmazonAdCampaign.getMarketplaceId()) && budget != null) {
            Double maxValue = BudgetValueEnum.getMaxValue(oldAmazonAdCampaign.getMarketplaceId());
            Double minValue = BudgetValueEnum.getMinValue(oldAmazonAdCampaign.getMarketplaceId());
            if (maxValue != null) {
                if (budget > maxValue) {
                    return ResultUtil.error("每日预算填写错误，最大值："+maxValue);

                }
            }
            if (minValue != null) {
                if (budget < minValue) {
                    return ResultUtil.error("每日预算填写错误，最小值："+minValue);
                }
            }
        }

        oldAmazonAdCampaign.setBudget(budget == null ? null : BigDecimal.valueOf(budget));
        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignDailyBudget(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动每日预算
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, oldAmazonAdCampaign);

        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), oldAmazonAdCampaign.getCampaignId());
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updatePlacemet(Integer puid, Integer uid, String loginIp, Long id, PredicateEnum predicateEnum, Double percentage) {
        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);

        String placement = predicateEnum.value(); // 广告位
        String adjustments = oldAmazonAdCampaign.getAdjustments();
        Double oldPercetage = null;
        List<Adjustment> list;
        if (StringUtils.isNotBlank(adjustments)) {
            try {
                list = JSONUtil.jsonToObjectThrowable(adjustments, new TypeReference<List<Adjustment>>() {});
            } catch (IOException e) {
                throw new RuntimeException();
            }

            Map<String, Adjustment> map = list.stream().collect(Collectors.toMap(Adjustment::getPredicate, e -> e));
            if (map.containsKey(placement)) {
                oldPercetage = map.get(placement).getPercentage();
                if (percentage == null) {
                    map.remove(placement);
                } else if (!Objects.equals(map.get(placement).getPercentage(), percentage)) {
                    map.get(placement).setPercentage(percentage);
                } else {
                    return ResultUtil.success();
                }
            } else if (percentage != null) {
                Adjustment adjustment = new Adjustment();
                adjustment.setPredicate(placement);
                adjustment.setPercentage(percentage);
                list.add(adjustment);
            } else {
                return ResultUtil.success();
            }
        } else if (percentage != null) {
            list = new ArrayList<>(1);
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(placement);
            adjustment.setPercentage(percentage);
            list.add(adjustment);
        } else {
            return ResultUtil.success();
        }

        oldAmazonAdCampaign.setAdjustments(JSONUtil.objectToJson(list));
        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignPlacementBidding(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动广告位
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, oldAmazonAdCampaign);
        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            //广告管理页面竞价标识
            this.setPlacementBidUpdateIdentifying(placement, oldAmazonAdCampaign.getCampaignId());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateStartDate(Integer puid, Integer uid, String loginIp, Long id, String startDate) {
        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }

        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);

        if (startDate == null) {
            startDate = "";
        }

        String oldStartDate = "";
        if (oldAmazonAdCampaign.getStartDate() != null) {
            oldStartDate = DateUtil.dateToStrWithFormat(oldAmazonAdCampaign.getStartDate(), DateUtil.PATTERN);
        }
        if (startDate.equals(oldStartDate)) {
            return ResultUtil.success();
        }

        if ("".equals(startDate)) {
            oldAmazonAdCampaign.setStartDate(null);
        } else {
            oldAmazonAdCampaign.setStartDate(DateUtil.strToDate(startDate, DateUtil.PATTERN));
        }

        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignStartDate(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动开始时间
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, oldAmazonAdCampaign);
        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updateEndDate(Integer puid, Integer uid, String loginIp, Long id, String endDate) {
        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);


        if (endDate == null) {
            endDate = "";
        }

        String oldEndDate = "";
        if (oldAmazonAdCampaign.getEndDate() != null) {
            oldEndDate = DateUtil.dateToStrWithFormat(oldAmazonAdCampaign.getEndDate(), DateUtil.PATTERN);
        }
        if (endDate.equals(oldEndDate)) {
            return ResultUtil.success();
        }

        if ("".equals(endDate)) {
            oldAmazonAdCampaign.setEndDate(null);
            oldAmazonAdCampaign.setEndTimeStr("");
        } else {
            oldAmazonAdCampaign.setEndDate(DateUtil.strToDate(endDate, DateUtil.PATTERN));
            oldAmazonAdCampaign.setEndTimeStr(endDate);
        }

        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignEndDate(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动结束时间
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, oldAmazonAdCampaign);
        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result updatePlacementAdjustment(Integer puid, Integer uid, String loginIp, Long id, String placement, String value) {
        if (!PredicateEnum.PLACEMENTTOP.value().equals(placement)
                && !PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(placement)
                && !PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(placement)
                && !PredicateEnum.SITEAMAZONBUSINESS.value().equals(placement)) {
            return ResultUtil.error("请求参数错误");
        }

        AmazonAdCampaignAll oldAmazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId( puid,id);
        if (oldAmazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(oldAmazonAdCampaign, amazonAdCampaign);


        Double percentage = Double.valueOf(value);
        Double oldPercetage = null;
        String adjustments = oldAmazonAdCampaign.getAdjustments();
        if (StringUtils.isNotBlank(adjustments)) {
            List<Adjustment> adjustmentList;
            try {
                adjustmentList = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(adjustments, new TypeReference<List<Adjustment>>() {});
            } catch (IOException e) {
                log.error("adjustment:", e);
                return ResultUtil.error("系统异常");
            }

            Map<String, Adjustment> adjustmentMap = adjustmentList.stream().collect(Collectors.toMap(Adjustment::getPredicate, Function.identity()));
            if (adjustmentMap.containsKey(placement)) {
                if (percentage.equals(adjustmentMap.get(placement).getPercentage())) {
                    return ResultUtil.success();
                }
                adjustmentMap.get(placement).setPercentage(percentage);
                oldPercetage = adjustmentMap.get(placement).getPercentage();
            } else {
                Adjustment adjustment = new Adjustment();
                adjustment.setPredicate(placement);
                adjustment.setPercentage(percentage);
                adjustmentMap.put(placement, adjustment);
            }
            adjustments = JSONUtil.objectToJson(adjustmentMap.values());
        } else {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(placement);
            adjustment.setPercentage(percentage);
            adjustments = JSONUtil.objectToJson(Collections.singletonList(adjustment));
        }

        oldAmazonAdCampaign.setAdjustments(adjustments);
        oldAmazonAdCampaign.setUpdateId(uid);

        Result result = updateCampaignPlacementBidding(oldAmazonAdCampaign);
        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动竞价
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, oldAmazonAdCampaign);

        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            //记操作日志
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            //广告管理页面竞价标识
            this.setPlacementBidUpdateIdentifying(placement, oldAmazonAdCampaign.getCampaignId());
        }
        if (result.error()) {
            result.setMsg(result.getMsg() != null ? result.getMsg() : "操作失败");
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result batchUpdatePlacementAdjustment(Integer puid, Integer uid, String loginIp, List<BatchUpdatePlacementAdjustmentRequest.UpdatePlacementAdjustment> placementAdjustmentList) {
        List<Long> idList = placementAdjustmentList.stream().map(e->e.getId().getValue()).distinct().collect(Collectors.toList());
        List<AmazonAdCampaignAll> oldList = amazonAdCampaignAllDao.getListByLongIdList(puid,idList);
        List<BatchCampaignVo> errorList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(oldList)) {
            Result result = new Result();
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = new BatchResponseVo<>();
            data.setCountNum(placementAdjustmentList.size());
            data.setFailNum(placementAdjustmentList.size());
            result.setCode(Result.ERROR);
            result.setData(data);
            return result;
        }
        List<String> updatePlacementTopBidList = new ArrayList<>();
        List<String> updatePlacementProductPageBidList = new ArrayList<>();
        List<String> updatePlacementRestOfSearchBidList = new ArrayList<>();
        List<String> updateAmazonBusinessBidList = new ArrayList<>();
        Map<Long, AmazonAdCampaignAll> oldMap = oldList.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getId,Function.identity()));
        Map<Long, AmazonAdCampaignAll> newMap = Maps.newHashMap();
        for (BatchUpdatePlacementAdjustmentRequest.UpdatePlacementAdjustment updatePlacementAdjustment : placementAdjustmentList) {
            BatchCampaignVo vo = new BatchCampaignVo();
            AmazonAdCampaignAll oldAmazonAdCampaign = oldMap.get(updatePlacementAdjustment.getId().getValue());
            if (oldAmazonAdCampaign == null) {
                vo.setFailReason("该广告活动不存在");
                errorList.add(vo);
                continue;
            }
            if (!PredicateEnum.PLACEMENTTOP.value().equals(updatePlacementAdjustment.getPlacement())
                    && !PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(updatePlacementAdjustment.getPlacement())
                    && !PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(updatePlacementAdjustment.getPlacement())
                    && !PredicateEnum.SITEAMAZONBUSINESS.value().equals(updatePlacementAdjustment.getPlacement())) {
                vo.setName(oldAmazonAdCampaign.getName());
                vo.setCampaignId(oldAmazonAdCampaign.getCampaignId());
                vo.setFailReason("参数错误");
                errorList.add(vo);
                continue;
            } else {
                AmazonAdCampaignAll newAmazonAdCampaign = null;
                if (MapUtils.isNotEmpty(newMap) && newMap.containsKey(updatePlacementAdjustment.getId().getValue())) {
                    newAmazonAdCampaign = newMap.get(updatePlacementAdjustment.getId().getValue());
                } else {
                    newAmazonAdCampaign = new AmazonAdCampaignAll();
                    BeanUtils.copyProperties(oldAmazonAdCampaign, newAmazonAdCampaign);
                }
                Double percentage = null;
                if (StringUtils.isNotBlank(updatePlacementAdjustment.getValue())) {
                    percentage = Double.valueOf(updatePlacementAdjustment.getValue());
                }
                Double oldPercetage = null;
                String adjustments = newAmazonAdCampaign.getAdjustments();
                if (StringUtils.isNotBlank(adjustments)) {
                    List<Adjustment> adjustmentList = null;
                    try {
                        adjustmentList = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(adjustments, new TypeReference<List<Adjustment>>() {});
                    } catch (IOException e) {
                        vo.setFailReason("系统异常");
                        vo.setCampaignId(newAmazonAdCampaign.getCampaignId());
                        vo.setName(newAmazonAdCampaign.getName());
                        errorList.add(vo);
                        log.error("adjustment:", e);
                        continue;
                    }

                    Map<String, Adjustment> adjustmentMap = adjustmentList.stream().collect(Collectors.toMap(Adjustment::getPredicate, Function.identity()));
                    if (adjustmentMap.containsKey(updatePlacementAdjustment.getPlacement())) {
                        if (percentage == null) {
                            oldPercetage = adjustmentMap.get(updatePlacementAdjustment.getPlacement()).getPercentage();
                            adjustmentMap.get(updatePlacementAdjustment.getPlacement()).setPercentage(adjustmentMap.get(updatePlacementAdjustment.getPlacement()).getPercentage());
                        } else {
                            oldPercetage = adjustmentMap.get(updatePlacementAdjustment.getPlacement()).getPercentage();
                            adjustmentMap.get(updatePlacementAdjustment.getPlacement()).setPercentage(percentage);
                        }
                    } else {
                        Adjustment adjustment = new Adjustment();
                        adjustment.setPredicate(updatePlacementAdjustment.getPlacement());
                        if (percentage == null) {
                            adjustment.setPercentage(0.0);
                        } else {
                            adjustment.setPercentage(percentage);
                        }
                        adjustmentMap.put(updatePlacementAdjustment.getPlacement(), adjustment);
                    }
                    adjustments = JSONUtil.objectToJson(adjustmentMap.values());
                } else {
                    Adjustment adjustment = new Adjustment();
                    adjustment.setPredicate(updatePlacementAdjustment.getPlacement());
                    if (percentage == null) {
                        adjustment.setPercentage(0.0);
                    } else {
                        adjustment.setPercentage(percentage);
                    }
                    adjustments = JSONUtil.objectToJson(Collections.singletonList(adjustment));
                }
                newAmazonAdCampaign.setAdjustments(adjustments);
                newAmazonAdCampaign.setUpdateId(uid);
                newMap.put(newAmazonAdCampaign.getId(),newAmazonAdCampaign);
                //竞价修改标识
                if (!Objects.equals(percentage, oldPercetage)) {
                    if (PredicateEnum.PLACEMENTTOP.value().equals(updatePlacementAdjustment.getPlacement())) {
                        updatePlacementTopBidList.add(oldAmazonAdCampaign.getCampaignId());
                    } else if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(updatePlacementAdjustment.getPlacement())) {
                        updatePlacementProductPageBidList.add(oldAmazonAdCampaign.getCampaignId());
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(updatePlacementAdjustment.getPlacement())) {
                        updatePlacementRestOfSearchBidList.add(oldAmazonAdCampaign.getCampaignId());
                    } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(updatePlacementAdjustment.getPlacement())) {
                        updateAmazonBusinessBidList.add(oldAmazonAdCampaign.getCampaignId());
                    }
                }
            }
        }
        if (MapUtils.isEmpty(newMap)) {
            Result result = new Result();
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = new BatchResponseVo<>();
            data.setCountNum(placementAdjustmentList.size());
            data.setFailNum(placementAdjustmentList.size());
            result.setCode(Result.ERROR);
            result.setData(data);
            return result;
        }
        List<AmazonAdCampaignAll> newList = new ArrayList<>(newMap.values());
        Result result = updatePlacementBatch(newList,Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE);

        /**
         * TODO 广告活动增加日志
         * 操作类型：编辑广告活动竞价
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = new ArrayList<>();
        for (AmazonAdCampaignAll newAdCampaign : newList){
            AmazonAdCampaignAll oldAdCampaign = oldMap.get(newAdCampaign.getId());
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(oldAdCampaign, newAdCampaign);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLogs.add(adManageOperationLog);
        }

        if (result.success()) {
            BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> data = (BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll>) result.getData();
            Integer errorCount = 0;
            Integer count = placementAdjustmentList.size();
            List<BatchCampaignVo> errorList1 = data.getErrorList();
            if (CollectionUtils.isNotEmpty(data.getErrorList())) {
                if(CollectionUtils.isNotEmpty(errorList)){
                    Map<Long,BatchCampaignVo> errorMap = errorList.stream().collect(Collectors.toMap(BatchCampaignVo::getId, Function.identity(), (e1, e2) -> e2));
                    errorCount += placementAdjustmentList.stream().filter(e -> errorMap.containsKey(e.getId().getValue())).collect(Collectors.toList()).size();
                }
                Map<Long,BatchCampaignVo> errorMap1 = data.getErrorList().stream().collect(Collectors.toMap(BatchCampaignVo::getId, Function.identity(), (e1, e2) -> e2));
                errorCount += placementAdjustmentList.stream().filter(e -> errorMap1.containsKey(e.getId().getValue())).collect(Collectors.toList()).size();
            }
            List<AmazonAdCampaignAll> campaignSuccess = data.getSuccessList();

            //竞价修改标识
            campaignSuccess.forEach(e -> {
                if (updatePlacementTopBidList.contains(e.getCampaignId())) {
                    this.setPlacementBidUpdateIdentifying(PredicateEnum.PLACEMENTTOP.value(), e.getCampaignId());
                }
                if (updatePlacementProductPageBidList.contains(e.getCampaignId())) {
                    this.setPlacementBidUpdateIdentifying(PredicateEnum.PLACEMENTPRODUCTPAGE.value(), e.getCampaignId());
                }
                if (updatePlacementRestOfSearchBidList.contains(e.getCampaignId())) {
                    this.setPlacementBidUpdateIdentifying(PredicateEnum.PLACEMENTRESTOFSEARCH.value(), e.getCampaignId());
                }
                if (updateAmazonBusinessBidList.contains(e.getCampaignId())) {
                    this.setPlacementBidUpdateIdentifying(PredicateEnum.SITEAMAZONBUSINESS.value(), e.getCampaignId());
                }
            });
            //收集失败/成功日志
            Map<String, AmazonAdCampaignAll> successDataMap = campaignSuccess.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e->e));
            Map<String, BatchCampaignVo> errorDataMap = data.getErrorList().stream().collect(Collectors.toMap(BatchCampaignVo::getCampaignId, e-> e));
            for (AdManageOperationLog adManageOperationLog: adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getCampaignId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getCampaignId()).getFailReason());
                }
            }

            data.setCountNum(count) ;
            data.setFailNum(errorCount);
            data.setSuccessNum(count-errorCount);
            //前端不需要展示成功消息，减少消耗移除成功数据
            data.getSuccessList().clear();

        }
        if (result.error()) {
            result.setMsg("更新失败，请稍后重试");
            for (AdManageOperationLog adManageOperationLog: adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
        }
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }



    @Override
    public Result archive(Integer puid, Integer uid, String loginIp, Long id) {
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaignAllDao.getByPuidAndId(puid,id);
        if (amazonAdCampaign == null) {
            return ResultUtil.error("对象不存在");
        }

        Result result = cpcCampaignApiService.archive(amazonAdCampaign);

        /**
         * TODO 广告活动增加日志
         * 操作类型：归档广告活动
         * 逻辑：创建新对象归档状态修改，比较归档状态
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        AmazonAdCampaignAll newAmazonAdCampaign = new AmazonAdCampaignAll();
        BeanUtils.copyProperties(amazonAdCampaign, newAmazonAdCampaign);
        newAmazonAdCampaign.setState(CpcStatusEnum.archived.name());
        newAmazonAdCampaign.setUpdateId(uid);
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getAdManageOperationLog(amazonAdCampaign, newAmazonAdCampaign);

        adManageOperationLog.setIp(loginIp);
        if (result.success()) {
            amazonAdCampaign.setUpdateId(uid);
            amazonAdCampaign.setState(CpcStatusEnum.archived.name());
            amazonAdCampaignAllDao.updateById(puid,amazonAdCampaign);
            saveDoris(Collections.singletonList(amazonAdCampaign), false, true);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
        }
        if (result.error()) {
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        //end
        return result;
    }

    @Override
    public Result addNote(Integer puid, Integer uid, String loginIp, Integer shopId, Integer campaignId, String note) {
        AmazonAdCampaignNote noteObj = new AmazonAdCampaignNote();
        noteObj.setPuid(puid);
        noteObj.setShopId(shopId);
        noteObj.setCampaignId(campaignId);
        noteObj.setNote(note);
        try {
            amazonAdCampaignNoteDao.save(puid, noteObj);
            return ResultUtil.success();
        } catch (Exception e) {
            log.error("addNote:", e);
        }

        return ResultUtil.error("action.fail");
    }

    @Override
    public Result syncCampaign(int puid, Integer shopId, String marketplaceId, List<String> campaignIdList) {

        //获取配置信息
        AmazonAdProfile amazonAdProfile = amazonAdProfileDao.getProfile(puid, shopId);
        if (amazonAdProfile == null) {
           return ResultUtil.error("没有站点对应的配置信息");
        }

        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shopAuth == null) {
            return ResultUtil.error("店铺不存在");
        }

        try {
            for (String campaignId : campaignIdList) {
                AmazonAdCampaignAll campaign = amazonAdCampaignAllDao.getByCampaignId(puid, shopId, marketplaceId, campaignId,CampaignTypeEnum.sp.getCampaignType());
                if (campaign == null || campaign.getState().equals(CpcStatusEnum.archived.name())) {  //已存档的  不支持同步
                    continue;
                }
                cpcAdSyncService.syncSpByShop(shopAuth, campaignId,null);
            }
            return ResultUtil.success("同步完成");

        } catch (Exception e) {
            return ResultUtil.error("同步异常");
        }
    }



    @Override
    public Page getPageList(Integer puid, CampaignPageParam param, Page page) {
        List<CampaignPageVo> voList = new ArrayList<>();

        page = amazonAdCampaignAllDao.getPageList(puid, param, page);

        List<AmazonAdCampaignAll> poList = page.getRows();
        if (CollectionUtils.isNotEmpty(poList)) {

            List<String> portfolioIds = poList.stream().filter(p -> p.getPortfolioId() != null).map(AmazonAdCampaignAll::getPortfolioId).collect(Collectors.toList());

            Map<String, AmazonAdPortfolio> portfolioMap = null;
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, param.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }


            Map<Integer, User> userMap = userDao.listByPuid(puid).stream().collect(Collectors.toMap(User::getId, e -> e));//判断user表中id是否存在
            long t1 = System.currentTimeMillis();
            // 按活动分组获取活动的汇总数据
            List<String> campaignIds = poList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList());
            Map<String, AmazonAdCampaignAllReport> campaignReportMap = amazonAdCampaignReportService.listSumReports(puid, param.getShopId(),
                            poList.get(0).getMarketplaceId(), param.getStartDate(), param.getEndDate(), campaignIds).stream()
                    .collect(Collectors.toMap(AmazonAdCampaignAllReport::getCampaignId, e -> e));
            Map<String, AdHomePerformancedto> latestReportMap = amazonAdCampaignReportService.listLatestReports(puid, param.getShopId(), campaignIds, false)
                    .stream().collect(Collectors.toMap(AdHomePerformancedto::getCampaignId, Function.identity(), (x, y) -> x));
            log.info("广告管理--广告活动接口调用-按campaignId获取sp广告活动报告- 花费时间 {} ,params: {}", (System.currentTimeMillis()-t1),JSONUtil.objectToJson(param));

            long t2 = System.currentTimeMillis();

            // 获取汇总信息用于填充新增类型指标占比数据
            AdMetricDto adMetricDto = amazonAdCampaignReportService.getSumAdMetric(puid, param.getShopId(), param.getStartDate(), param.getEndDate(), param);

            // 取店铺销售额
            ShopSaleDto shopSaleDto =  new ShopSaleDto();
            if (param.getShopSales() != null) {  // 最外层查了一次了
                shopSaleDto.setSumRange(param.getShopSales());
            }
            Map<String,Long> outOfBudgetTime = new HashMap<>();

            CampaignPageVo vo;
            page.setRows(voList);
            for (AmazonAdCampaignAll amazonAdCampaign : poList) {
                vo = new CampaignPageVo();
                voList.add(vo);
                convertPoToPageVo(amazonAdCampaign, vo,outOfBudgetTime);
                filterAdMetricData(adMetricDto, vo);

                if (StringUtils.isNotBlank(amazonAdCampaign.getPortfolioId())) {
                    if (portfolioMap != null && portfolioMap.containsKey(amazonAdCampaign.getPortfolioId())) {
                        vo.setPortfolioName(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getName());
                        vo.setIsHidden(portfolioMap.get(amazonAdCampaign.getPortfolioId()).getIsHidden());
                    } else {
                        vo.setPortfolioName("广告组合待同步");
                    }
                } else {
                    vo.setPortfolioName("-");
                }

                // 创建人
                if (userMap.containsKey(amazonAdCampaign.getCreateId())) {
                    User user = userMap.get(amazonAdCampaign.getCreateId());
                    if (StringUtils.isNotBlank(user.getNickname())) {
                        vo.setCreator(user.getNickname());
                    }
                }
                // 填充报告数据
                if (campaignReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillReportDataIntoPageVo(vo, campaignReportMap.get(amazonAdCampaign.getCampaignId()).getReportBase(), shopSaleDto);
                }
                // 填充最新数据
                if (latestReportMap.get(amazonAdCampaign.getCampaignId()) != null) {
                    cpcCommService.fillLatestReportDataIntoPageVo(vo, latestReportMap.get(amazonAdCampaign.getCampaignId()));
                }

                //兼容本产品广告销量（sd无广告销量）
                if (CampaignTypeEnum.sb.getCampaignType().equals(vo.getType()) || CampaignTypeEnum.sd.getCampaignType().equals(vo.getType())) {
                    vo.setAdSelfSaleNum(0);
                    vo.setAdOtherSaleNum(0);
                }
            }

            log.info("广告管理--广告活动接口调用-广告活动填充报告数据等- 花费时间 {} ,params: {}", (System.currentTimeMillis()-t2),JSONUtil.objectToJson(param));

        }

        return page;
    }

    // po -> 列表页vo
    private void convertPoToPageVo(AmazonAdCampaignAll amazonAdCampaign, CampaignPageVo vo,Map<String,Long> outOfBudgetTime) {
        vo.setId(Long.valueOf(amazonAdCampaign.getId()));
        vo.setShopId(amazonAdCampaign.getShopId());
        vo.setCampaignId(amazonAdCampaign.getCampaignId());
        vo.setName(amazonAdCampaign.getName());
        vo.setSbType(amazonAdCampaign.getIsMultiAdGroupsEnabled());
        vo.setState(amazonAdCampaign.getState());
        amazonAdCampaign.setServingStatus(amazonAdCampaign.getServingStatus());
        vo.setServingStatus(amazonAdCampaign.getServingStatus());
        vo.setPortfolioId(amazonAdCampaign.getPortfolioId());
        vo.setServingStatusDec(amazonAdCampaign.getServingStatusDec());
        vo.setServingStatusName(amazonAdCampaign.getServingStatusName());
        //根据状态code查询状态描述
        if (AmazonAdCampaign.stateEnum.enabled.getCode().equals(amazonAdCampaign.getState())) {
            AmazonAdCampaign.servingStatusEnum servingStatusEnum = UCommonUtil.getByCode(amazonAdCampaign.getServingStatus(), AmazonAdCampaign.servingStatusEnum.class);
            vo.setServingStatusDec(null == servingStatusEnum ? StringUtils.EMPTY : servingStatusEnum.getDescription());
            if (AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                vo.setServingStatus("CAMPAIGN_OUT_OF_BUDGET");
            }
            if (AmazonAdCampaign.servingStatusEnum.CAMPAIGN_OUT_OF_BUDGET.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus()) || AmazonAdCampaignAll.servingStatusEnum.outOfBudget.getCode().equalsIgnoreCase(amazonAdCampaign.getServingStatus())) {
                //如果状态是超过预算
                String outOfTimeStr = "";
                if(amazonAdCampaign.getOutOfBudgetTime() != null) {
                    try {
                        LocalDateTime localDateTime = LocalDateTimeUtil.ofEpochSecondToDateTime(amazonAdCampaign.getOutOfBudgetTime());
                        ZoneId zoneId = ZoneUtil.getZoneIdByAmzSite(amazonAdCampaign.getMarketplaceId());
                        localDateTime = LocalDateTimeUtil.getZoneTime(localDateTime, ZoneId.systemDefault(), zoneId);
                        Date date = LocalDateTimeUtil.convertLDTToDate(localDateTime);
                        outOfTimeStr = DateUtil.dateToStrWithFormat(date, "HH:mm");
                    } catch (Exception e){
                        log.error("转换超预算时间错误",e);
                    }
                }
                vo.setServingStatusName(vo.getServingStatusName() + " " + outOfTimeStr);
                vo.setOutOfBudget(true);
            }
        }

        vo.setDailyBudget(String.valueOf(amazonAdCampaign.getBudget()));
        vo.setBudgetType(amazonAdCampaign.getBudgetType());
        vo.setCampaignType(amazonAdCampaign.getCampaignType());
        vo.setTargetingType(amazonAdCampaign.getTargetingType());
        vo.setStrategy(amazonAdCampaign.getStrategy());
        vo.setStartDate(amazonAdCampaign.getStartDateStr());
        vo.setEndDate(amazonAdCampaign.getEndDateStr());
        vo.setCreateTime(amazonAdCampaign.getCreateTime());
        vo.setUpdateTime(amazonAdCampaign.getUpdateTime());
        vo.setType(amazonAdCampaign.getType());
        if(CampaignTypeEnum.sp.getCampaignType().equals(amazonAdCampaign.getType())){
            vo.setCampaignTargetingType(amazonAdCampaign.getTargetingType());
        }
        if(CampaignTypeEnum.sb.getCampaignType().equals(amazonAdCampaign.getType())){
            vo.setCampaignTargetingType(Constants.MANUAL);
            vo.setTargetType(amazonAdCampaign.getTargetType());
        }

        if(CampaignTypeEnum.sd.getCampaignType().equals(amazonAdCampaign.getType())){
            vo.setCampaignTargetingType(amazonAdCampaign.getTactic());
        }

        //分时调价
        vo.setIsBudgetPricing(amazonAdCampaign.getIsBudgetPricing());
        vo.setPricingBudgetState(amazonAdCampaign.getPricingBudgetState());
        vo.setIsSpacePricing(amazonAdCampaign.getIsSpacePricing());
        vo.setPricingSpaceState(amazonAdCampaign.getPricingSpaceState());
        vo.setIsStatePricing(amazonAdCampaign.getIsStatePricing());
        vo.setPricingStartStopState(amazonAdCampaign.getPricingStartStopState());
        vo.setCostType(amazonAdCampaign.getCostType());
        if (CampaignTypeEnum.sb.getCampaignType().equals(amazonAdCampaign.getType())) {
            vo.setBrandEntityId(amazonAdCampaign.getBrandEntityId());
            vo.setBidOptimization(amazonAdCampaign.getBidOptimization());
            if (amazonAdCampaign.getBidMultiplier() != null) {
                vo.setBidMultiplier(amazonAdCampaign.getBidMultiplier().doubleValue());
            }
        }
        vo.setMarketplaceId(amazonAdCampaign.getMarketplaceId());

        // 广告位调整价
        if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(amazonAdCampaign.getAdjustments(), new TypeReference<List<Adjustment>>() {});
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementProductPage(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementTop(String.valueOf(adjustment.getPercentage()));
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        vo.setPlacementRestOfSearch(String.valueOf(adjustment.getPercentage()));
                    }
                }
            }
        }
    }


    private void filterSumMetricData(List<AmazonAdCampaignAllReport> allReports, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(allReports)) {
            return;
        }
        adMetricDto.setSumCost(allReports.stream().filter(item -> item != null && item.getCost() != null).map(AmazonAdCampaignAllReport::getCost).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdSale(allReports.stream().filter(item -> item != null && item.getTotalSales() != null).map(AmazonAdCampaignAllReport::getTotalSales).reduce(BigDecimal.ZERO, BigDecimal::add));
        adMetricDto.setSumAdOrderNum(BigDecimal.valueOf(allReports.stream().filter(item -> item != null && item.getOrderNum() != null).mapToInt(AmazonAdCampaignAllReport::getOrderNum).sum()));
        adMetricDto.setSumOrderNum(BigDecimal.valueOf(allReports.stream().filter(item -> item != null && item.getSaleNum() != null).mapToInt(AmazonAdCampaignAllReport::getSaleNum).sum()));
    }

    // 填充指标总和数据
    private void filterAdMetricData(AdMetricDto adMetricDto, CampaignPageVo vo) {
        if (adMetricDto == null) {
            vo.setSumCost(BigDecimal.ZERO);
            vo.setSumAdSale(BigDecimal.ZERO);
            vo.setSumAdOrderNum(BigDecimal.ZERO);
            vo.setSumOrderNum(BigDecimal.ZERO);
            return;
        }
        vo.setSumCost(adMetricDto.getSumCost() == null ? BigDecimal.ZERO : adMetricDto.getSumCost());
        vo.setSumAdSale(adMetricDto.getSumAdSale() == null ? BigDecimal.ZERO : adMetricDto.getSumAdSale());
        vo.setSumAdOrderNum(adMetricDto.getSumAdOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumAdOrderNum());
        vo.setSumOrderNum(adMetricDto.getSumOrderNum() == null ? BigDecimal.ZERO : adMetricDto.getSumOrderNum());
    }


    // 更新逻辑的公用部分
    private Result commUpdate(AmazonAdCampaignAll amazonAdCampaign, boolean isUpdateStartDate) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        CampaignEntityV3 campaign = makeUpdateCampaignByCampaignPo(amazonAdCampaign);
        if (!isUpdateStartDate) {
            campaign.setStartDate(null);
        }

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();
            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(),amazonAdCampaign);
                //写入doris
                saveDoris(shop.getPuid(), shop.getId(), Collections.singletonList(amazonAdCampaign.getCampaignId()));
                //更新成功同步一次活动
                cpcAdSyncService.syncSpCampaignState(shop,successResultV3.getCampaignId());
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                return ResultUtil.error(AmazonErrorUtils.getError(errorList.get(0).getErrors().get(0).getErrorMessage()));
            }
        } else {
            return ResultUtil.error(errMsg);
        }

        return ResultUtil.success();
    }


    /**
     * 更新活动名称
     * @param amazonAdCampaign
     * @return
     */
    private Result updateCampaignName(AmazonAdCampaignAll amazonAdCampaign) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        //只修改广告活动名称
        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        campaign.setName(amazonAdCampaign.getName());

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();

            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(),amazonAdCampaign);
                saveDoris(Collections.singletonList(amazonAdCampaign), false, true);
                //更新成功同步一次活动
                cpcAdSyncService.syncSpCampaignState(shop,successResultV3.getCampaignId());
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                return ResultUtil.error(AmazonErrorUtils.getError(errorList.get(0).getErrors().get(0).getErrorMessage()));
            }
        } else {
            return ResultUtil.error(errMsg);
        }
        return ResultUtil.success();
    }


    /**
     * 更新活动广告位
     * @param amazonAdCampaign
     * @return
     */
    private Result updateCampaignPlacementBidding(AmazonAdCampaignAll amazonAdCampaign) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy())) {

            List<Adjustment> adjustments = null;
            if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
            }

            CamapginDynamicBidding camapginDynamicBidding = new CamapginDynamicBidding();
            camapginDynamicBidding.setStrategy(CampaignStrategyV3.fromOldValue(amazonAdCampaign.getStrategy()).getValue());

            List<PlacementBidding> placementList = new ArrayList<>();
            if (adjustments != null) {
                for (Adjustment adjustment : adjustments) {
                    PlacementBidding placementBidding = new PlacementBidding();
                    placementBidding.setPercentage(adjustment.getPercentage().intValue());
                    CampaignPlacementV3 campaignPlacementV3 = CampaignPlacementV3.fromOldValue(adjustment.getPredicate());
                    if (campaignPlacementV3 != null) {
                        placementBidding.setPlacement(campaignPlacementV3.getValue());
                    }
                    placementList.add(placementBidding);
                }
                camapginDynamicBidding.setPlacementBidding(placementList);
            }

            campaign.setDynamicBidding(camapginDynamicBidding);
        }

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();

            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(),amazonAdCampaign);
                saveDoris(Collections.singletonList(amazonAdCampaign), false, true);
                //更新成功同步一次活动
                cpcAdSyncService.syncSpCampaignState(shop,successResultV3.getCampaignId());
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                ErrorItemV3 error = errorList.get(0).getErrors().get(0);
                return ResultUtil.error(error.getErrorMessage());
            }
        } else {
            return ResultUtil.error(errMsg);
        }
        return ResultUtil.success();
    }



    /**
     * 更新活动开始时间
     * @param amazonAdCampaign
     * @return
     */
    private Result updateCampaignStartDate(AmazonAdCampaignAll amazonAdCampaign) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        campaign.setStartDate(amazonAdCampaign.getStartDateStr());

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();

            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(),amazonAdCampaign);
                saveDoris(Collections.singletonList(amazonAdCampaign), false, true);
                //更新成功同步一次活动
                cpcAdSyncService.syncSpCampaignState(shop,successResultV3.getCampaignId());
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                ErrorItemV3 error = errorList.get(0).getErrors().get(0);
                if (errorMap.containsKey(error.getErrorMessage())) {
                    return ResultUtil.error(errorMap.get(error.getErrorMessage()));
                } else {
                    return ResultUtil.error(error.getErrorMessage());
                }
            }
        } else {
            return ResultUtil.error(errMsg);
        }
        return ResultUtil.success();
    }

    /**
     * 更新活动结束时间
     * @param amazonAdCampaign
     * @return
     */
    private Result updateCampaignEndDate(AmazonAdCampaignAll amazonAdCampaign) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        //endDate可以为null,但是接口jar包处理逻辑会忽略null,所以传"",接口jar再转成null
        campaign.setEndDate(amazonAdCampaign.getEndTimeStr());

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();

            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(), amazonAdCampaign);
                if (amazonAdCampaign.getEndDate() == null) {
                    //结束时间为null时,为不影响现在功能,重新更新一次数据库,后期重构吧
                    amazonAdCampaignAllDao.setEndDateNull(amazonAdCampaign);
                    amazonAdCampaign.setEndDate(null);
                    amazonAdCampaign.setEndTimeStr("");
                }
                saveDoris(Collections.singletonList(amazonAdCampaign), false, true);

                //更新成功同步一次活动
                cpcAdSyncService.syncSpCampaignState(shop,successResultV3.getCampaignId());
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                ErrorItemV3 error = errorList.get(0).getErrors().get(0);
                return ResultUtil.error(error.getErrorMessage());
            }
        } else {
            return ResultUtil.error(errMsg);
        }
        return ResultUtil.success();
    }


    /**
     * 更新活动状态
     * @param amazonAdCampaign
     * @return
     */
    private Result updateCampaignState(AmazonAdCampaignAll amazonAdCampaign) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        //只修改广告状态
        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        campaign.setState(AdStateV3.fromOldValue(amazonAdCampaign.getState()).getValue());

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();

            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(),amazonAdCampaign);
                saveDoris(Collections.singletonList(amazonAdCampaign), false, true);
                //更新成功同步一次活动
                cpcAdSyncService.syncSpCampaignState(shop,successResultV3.getCampaignId());
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                ErrorItemV3 error = errorList.get(0).getErrors().get(0);
                return ResultUtil.error(error.getErrorMessage());
            }
        } else {
            return ResultUtil.error(errMsg);
        }
        return ResultUtil.success();
    }
    /**
     * 更新活动预算
     * @param amazonAdCampaign
     * @return
     */
    private Result updateCampaignDailyBudget(AmazonAdCampaignAll amazonAdCampaign) {
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }

        //只修改广告活动预算
        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        //组装预算对象
        CampaignBudget budget = new CampaignBudget();
        budget.setBudget(amazonAdCampaign.getBudget().doubleValue());
        campaign.setBudget(budget);

        UpdateSpCampaignV3Response response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), Collections.singletonList(campaign), true);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }

        if (response.getError() != null) {
            return ResultUtil.error(response.getError().getMessage());
        }

        //处理返回结果中的错误信息
        String errMsg = "更新广告活动失败";
        if (response.getData() != null) {
            //更新成功
            List<campaignSuccessResultV3> successList = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errorList = response.getData().getCampaigns().getError();

            for (campaignSuccessResultV3 successResultV3 : successList) {
                amazonAdCampaignAllDao.updateById(amazonAdCampaign.getPuid(),amazonAdCampaign);
                saveDoris(Collections.singletonList(amazonAdCampaign), false, true);
                //更新成功同步一次活动
                ThreadPoolUtil.getCpcAdShopAsyncPool().execute(() -> {
                    try {
                        cpcAdSyncService.syncSpCampaignState(shop, successResultV3.getCampaignId());
                    } catch (Exception e) {
                        log.error("updateCampaignDailyBudget, sync campaign state error, puid={} shopId={} campaignId={}", shop.getPuid(), shop.getId(), amazonAdCampaign.getCampaignId(), e);
                    }
                });
            }

            if (CollectionUtils.isNotEmpty(errorList)) {
                ErrorItemV3 error = errorList.get(0).getErrors().get(0);
                return ResultUtil.error(error.getErrorMessage());
            }
        } else {
            return ResultUtil.error(errMsg);
        }
        return ResultUtil.success();
    }



    /**
     * 批量更新并返回更新失败数据
     * @param amazonAdCampaigns
     * @return
     */
    private Result commUpdateBatch(List<AmazonAdCampaignAll> amazonAdCampaigns,String type) {
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaigns.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        List<CampaignEntityV3> campaigns = Lists.newArrayListWithCapacity(amazonAdCampaigns.size());

        UpdateSpCampaignV3Response response;
        //v3接口 归档操作,走指定接口
        if (StateEnum.ARCHIVED.value().equalsIgnoreCase(amazonAdCampaign.getState())) {
            for (AmazonAdCampaignAll a : amazonAdCampaigns){
                CampaignEntityV3 campaign = new CampaignEntityV3();
                if (StringUtils.isNotBlank(a.getCampaignId())) {
                    campaign.setCampaignId(a.getCampaignId());
                }
                campaigns.add(campaign);
            }

             response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaigns(shopAuthService.getAdToken(shop),
                    amazonAdCampaign.getProfileId(), shop.getMarketplaceId(),
                     amazonAdCampaigns.stream().map(AmazonAdCampaignAll::getCampaignId)
                             .collect(Collectors.toList()), false);
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                // 刷新token重试一次
                shopAuthService.refreshCpcAuth(shop);
                response =  CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).delCampaigns(shopAuthService.getAdToken(shop),
                        amazonAdCampaign.getProfileId(), shop.getMarketplaceId(),
                        amazonAdCampaigns.stream().map(AmazonAdCampaignAll::getCampaignId)
                                .collect(Collectors.toList()), false);
            }
        } else {
            for (AmazonAdCampaignAll a : amazonAdCampaigns){
                campaigns.add(makeCampaignV3ByCampaignPo(a,type));
            }
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), campaigns, true);
            // token过期再重试一次
            if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
                //刷新token
                shopAuthService.refreshCpcAuth(shop);
                response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                        amazonAdCampaign.getMarketplaceId(), campaigns, true);
            }
        }

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        Map<String, AmazonAdCampaignAll> amazonAdCampaignMap = amazonAdCampaigns.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> batchResponseVo = new BatchResponseVo();
        List<BatchCampaignVo> errorList = Lists.newArrayList();
        List<AmazonAdCampaignAll> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getData() != null) {
            List<campaignSuccessResultV3> successs = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errors = response.getData().getCampaigns().getError();

            List<Long> successId = Lists.newArrayList();
            //成功
            for (campaignSuccessResultV3 successResultV3 : successs) {
                AmazonAdCampaignAll amazonAdCampaignSuccess = amazonAdCampaignMap.remove(successResultV3.getCampaignId());
                if (amazonAdCampaignSuccess != null) {
                    successList.add(amazonAdCampaignSuccess);
                }
                successId.add(Long.valueOf(successResultV3.getCampaignId()));
            }
            //失败
            for (ErrorItemResultV3 errorItemResultV3 : errors) {
                //根据index获取campaignId
                String campaignId = campaigns.get(errorItemResultV3.getIndex()).getCampaignId();

                AmazonAdCampaignAll amazonAdCampaignFail = amazonAdCampaignMap.remove(campaignId);
                if (amazonAdCampaignFail != null) {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(amazonAdCampaignFail.getId());
                    campaignVoError.setName(amazonAdCampaignFail.getName());
                    campaignVoError.setCampaignId(amazonAdCampaignFail.getCampaignId());
                    campaignVoError.setId(amazonAdCampaignFail.getId());
                    //更新失败数据处理
                    if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors())) {
                        campaignVoError.setFailReason(AmazonErrorUtils.getError(errorItemResultV3.getErrors().get(0).getErrorMessage()));
                    } else {
                        campaignVoError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(campaignVoError);
                }
            }

            if (CollectionUtils.isNotEmpty(successList)) {
                amazonAdCampaignAllDao.batchUpdateAmazonAdCampaign(amazonAdCampaign.getPuid(), successList, type);
                saveDoris(shop.getPuid(), shop.getId(), successList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList()));
            }
            //更新成功同步一次活动
            if (CollectionUtils.isNotEmpty(successId)) {
                cpcAdSyncService.syncSpCampaignState(shop, StringUtils.join(successId, ","));
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdCampaignMap)) {
                amazonAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setFailReason("参数错误");
                    campaignVoError.setCampaignId(v.getCampaignId());
                    campaignVoError.setId(v.getId());
                    errorList.add(campaignVoError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getMessage())) {
            //授权失败
            if (MapUtils.isNotEmpty(amazonAdCampaignMap)) {
                String message = response.getError().getMessage();
                amazonAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setFailReason(message);
                    campaignVoError.setCampaignId(v.getCampaignId());
                    campaignVoError.setId(v.getId());
                    errorList.add(campaignVoError);
                });
            }
        } else {
            //所有数据没有执行成功
            if (MapUtils.isNotEmpty(amazonAdCampaignMap)) {
                amazonAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setFailReason("网络延迟，请稍后重试");
                    campaignVoError.setCampaignId(v.getCampaignId());
                    campaignVoError.setId(v.getId());
                    errorList.add(campaignVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdCampaigns.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }


    /**
     * 批量更新并返回更新失败数据
     * @param amazonAdCampaigns
     * @return
     */
    private Result updatePlacementBatch(List<AmazonAdCampaignAll> amazonAdCampaigns,String type) {
        AmazonAdCampaignAll amazonAdCampaign = amazonAdCampaigns.get(0);

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(amazonAdCampaign.getShopId(), amazonAdCampaign.getPuid());
        if (shop == null) {
            return ResultUtil.error("店铺不存在");
        }
        List<CampaignEntityV3> campaigns = Lists.newArrayListWithCapacity(amazonAdCampaigns.size());

        UpdateSpCampaignV3Response response;
        for (AmazonAdCampaignAll a : amazonAdCampaigns){
            campaigns.add(makeCampaignV3ByCampaignPo(a,type));
        }
        response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                amazonAdCampaign.getMarketplaceId(), campaigns, true);
        // token过期再重试一次
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            //刷新token
            shopAuthService.refreshCpcAuth(shop);
            response = CampaignSpV3Client.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).putCampaigns(shopAuthService.getAdToken(shop), amazonAdCampaign.getProfileId(),
                    amazonAdCampaign.getMarketplaceId(), campaigns, true);
        }

        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        Map<String, AmazonAdCampaignAll> amazonAdCampaignMap = amazonAdCampaigns.stream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
        BatchResponseVo<BatchCampaignVo, AmazonAdCampaignAll> batchResponseVo = new BatchResponseVo();
        List<BatchCampaignVo> errorList = Lists.newArrayList();
        List<AmazonAdCampaignAll> successList = Lists.newArrayList();
        //处理返回结果中的错误信息
        if (response.getData() != null) {
            List<campaignSuccessResultV3> successs = response.getData().getCampaigns().getSuccess();
            List<ErrorItemResultV3> errors = response.getData().getCampaigns().getError();

            List<Long> successId = Lists.newArrayList();
            //成功
            for (campaignSuccessResultV3 successResultV3 : successs) {
                AmazonAdCampaignAll amazonAdCampaignSuccess = amazonAdCampaignMap.remove(successResultV3.getCampaignId());
                if (amazonAdCampaignSuccess != null) {
                    successList.add(amazonAdCampaignSuccess);
                }
                successId.add(Long.valueOf(successResultV3.getCampaignId()));
            }
            //失败
            for (ErrorItemResultV3 errorItemResultV3 : errors) {
                //根据index获取campaignId
                String campaignId = campaigns.get(errorItemResultV3.getIndex()).getCampaignId();

                AmazonAdCampaignAll amazonAdCampaignFail = amazonAdCampaignMap.remove(campaignId);
                if (amazonAdCampaignFail != null) {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(amazonAdCampaignFail.getId());
                    campaignVoError.setName(amazonAdCampaignFail.getName());
                    campaignVoError.setCampaignId(amazonAdCampaignFail.getCampaignId());
                    campaignVoError.setId(amazonAdCampaignFail.getId());
                    //更新失败数据处理
                    if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors())) {
                        ErrorItemV3 error = errorItemResultV3.getErrors().get(0);
                        campaignVoError.setFailReason(String.valueOf(error.getErrorMessage()));
                    } else {
                        campaignVoError.setFailReason("更新失败，请稍后重试");
                    }
                    errorList.add(campaignVoError);
                }
            }

            if (CollectionUtils.isNotEmpty(successList)) {
                amazonAdCampaignAllDao.batchUpdateAmazonAdCampaign(amazonAdCampaign.getPuid(), successList, type);
                saveDoris(shop.getPuid(), shop.getId(), successList.stream().map(AmazonAdCampaignAll::getCampaignId).collect(Collectors.toList()));
            }
            //更新成功同步一次活动
            if (CollectionUtils.isNotEmpty(successId)) {
                cpcAdSyncService.syncSpCampaignState(shop, StringUtils.join(successId, ","));
            }
            //剩余未匹配到的数据是接口未返回campaignId 的数据，一般都是发生了错误
            if (MapUtils.isNotEmpty(amazonAdCampaignMap)) {
                amazonAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setFailReason("参数错误");
                    campaignVoError.setCampaignId(v.getCampaignId());
                    campaignVoError.setId(v.getId());
                    errorList.add(campaignVoError);
                });
            }

        } else if (response.getError() != null && StringUtils.isNotBlank(response.getError().getMessage())) {
            //授权失败
            if (MapUtils.isNotEmpty(amazonAdCampaignMap)) {
                String message = response.getError().getMessage();
                amazonAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setFailReason(message);
                    campaignVoError.setCampaignId(v.getCampaignId());
                    campaignVoError.setId(v.getId());
                    errorList.add(campaignVoError);
                });
            }
        } else {
            //所有数据没有执行成功
            if (MapUtils.isNotEmpty(amazonAdCampaignMap)) {
                amazonAdCampaignMap.forEach((k, v) -> {
                    BatchCampaignVo campaignVoError = new BatchCampaignVo();
                    campaignVoError.setDxmCampaignId(v.getId());
                    campaignVoError.setName(v.getName());
                    campaignVoError.setFailReason("网络延迟，请稍后重试");
                    campaignVoError.setCampaignId(v.getCampaignId());
                    campaignVoError.setId(v.getId());
                    errorList.add(campaignVoError);
                });
            }
        }
        batchResponseVo.setCountNum(amazonAdCampaigns.size());
        batchResponseVo.setFailNum(errorList.size());
        batchResponseVo.setErrorList(errorList);
        batchResponseVo.setSuccessNum(successList.size());
        batchResponseVo.setSuccessList(successList);
        return ResultUtil.success(batchResponseVo);
    }

    // 更新活动时vo->po
    private void convertVoToBatchUpdatePo(AmazonAdCampaignAll amazonAdCampaign, BatchCampaignVo vo,String type) {
        amazonAdCampaign.setCreateInAmzup(Constants.UPDATE_IN_AMZUP);
        amazonAdCampaign.setId(vo.getDxmCampaignId());
        amazonAdCampaign.setUpdateId(vo.getUid());
        if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            amazonAdCampaign.setBudget(vo.getDailyBudget() == null ? null : BigDecimal.valueOf(vo.getDailyBudget()));
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            amazonAdCampaign.setState(vo.getState());
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)) {
            // 竞价策略
            if(StringUtils.isNotBlank(vo.getStrategy())){
                amazonAdCampaign.setStrategy(vo.getStrategy());
            }

            // 根据广告位调整竞价
            List<Adjustment> list = Lists.newArrayListWithExpectedSize(2);
            if (vo.getPlacementProductPage() != null) {
                Adjustment adjustment = new Adjustment();
                adjustment.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
                adjustment.setPercentage(vo.getPlacementProductPage());
                list.add(adjustment);
            }
            if (vo.getPlacementTop() != null) {
                Adjustment adjustment = new Adjustment();
                adjustment.setPredicate(PredicateEnum.PLACEMENTTOP.value());
                adjustment.setPercentage(vo.getPlacementTop());
                list.add(adjustment);
            }
            if (vo.getPlacementRestOfSearch() != null) {
                Adjustment adjustment = new Adjustment();
                adjustment.setPredicate(PredicateEnum.PLACEMENTRESTOFSEARCH.value());
                adjustment.setPercentage(vo.getPlacementRestOfSearch());
                list.add(adjustment);
            }
            if (vo.getPlacementSiteAmazonBusiness() != null && Constants.placementSiteAmazonBusinessMarketplaceIds.contains(amazonAdCampaign.getMarketplaceId())) {
                Adjustment adjustment = new Adjustment();
                adjustment.setPredicate(PredicateEnum.SITEAMAZONBUSINESS.value());
                adjustment.setPercentage(vo.getPlacementSiteAmazonBusiness());
                list.add(adjustment);
            }

            //

            //批量调整广告竞价策略可以选择不修改，这里将原数据和新数据合并，使用新数据覆盖旧数据；
            if(CollectionUtils.isNotEmpty(list)){
                /**
                 * placementTop
                 * placementProductPage
                 * 目前就只有这两个类型
                 */
                if(StringUtils.isNotBlank(amazonAdCampaign.getAdjustments()) && list.size() != 2){
                    List<Adjustment> adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
                    if(CollectionUtils.isNotEmpty(adjustments)){

                        Map<String, Adjustment> adjustmentMap = adjustments.stream().collect(Collectors.toMap(Adjustment::getPredicate, e -> e));
                        for (Adjustment adjustment : list){
                            adjustmentMap.remove(adjustment.getPredicate());
                        }
                        if(MapUtils.isNotEmpty(adjustmentMap)){
                            list.addAll(adjustmentMap.values());
                        }
                    }
                }
                amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(list));

            }
        }


    }


    public void convertVoToBatchUpdateNeKeywordsPo(AmazonAdCampaignNeKeywords campaignNeKeyword, BatchNeKeywordVo vo) {
        campaignNeKeyword.setUpdateId(vo.getUid());
        campaignNeKeyword.setState(CpcStatusEnum.deleted.name());
    }

    public void convertVoToBatchUpdateNeTargetingsPo(AmazonAdCampaignNetargetingSp campaignNeTargeting, BatchNeTargetVo vo) {
        campaignNeTargeting.setUpdateId(vo.getUid());
        campaignNeTargeting.setState(CpcStatusEnum.archived.name());
    }


    // 更新活动时vo->po
    private void convertVoToUpdatePo(AmazonAdCampaignAll amazonAdCampaign, CampaignVo vo) {
        amazonAdCampaign.setName(vo.getName().trim());
        amazonAdCampaign.setBudget(vo.getDailyBudget() == null ? null : BigDecimal.valueOf(vo.getDailyBudget()));
        amazonAdCampaign.setCreateInAmzup(Constants.UPDATE_IN_AMZUP);
        amazonAdCampaign.setId(vo.getDxmCampaignId());
        amazonAdCampaign.setUpdateId(vo.getUid());
        amazonAdCampaign.setStartDate(DateUtil.strToDate4(vo.getStartDateStr()));

        if (vo.getEndDateStr() != null) {
            if ("".equals(vo.getEndDateStr())) {
                amazonAdCampaign.setEndDate(null);
                amazonAdCampaign.setEndTimeStr("");
            } else {
                amazonAdCampaign.setEndDate(DateUtil.strToDate4(vo.getEndDateStr()));
            }
        }

        if (vo.getPortfolioId() != null) {
            amazonAdCampaign.setPortfolioId(vo.getPortfolioId());
        }

        // 竞价策略
        amazonAdCampaign.setStrategy(vo.getStrategy());

        // 根据广告位调整竞价
        List<Adjustment> list = Lists.newArrayListWithExpectedSize(2);
        if (vo.getPlacementProductPage() != null) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
            adjustment.setPercentage(vo.getPlacementProductPage());
            list.add(adjustment);
        }
        if (vo.getPlacementTop() != null) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTTOP.value());
            adjustment.setPercentage(vo.getPlacementTop());
            list.add(adjustment);
        }
        if (vo.getPlacementRestOfSearch() != null) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTRESTOFSEARCH.value());
            adjustment.setPercentage(vo.getPlacementRestOfSearch());
            list.add(adjustment);
        }
        if (vo.getPlacementSiteAmazonBusiness() != null && Constants.placementSiteAmazonBusinessMarketplaceIds.contains(amazonAdCampaign.getMarketplaceId())) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.SITEAMAZONBUSINESS.value());
            adjustment.setPercentage(vo.getPlacementSiteAmazonBusiness());
            list.add(adjustment);
        }
        amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(list));
    }

    // 创建活动时vo->po
    @Override
    public AmazonAdCampaignAll convertVoToCreatePo(CampaignVo vo, AmazonAdProfile profile) {
        AmazonAdCampaignAll amazonAdCampaign = new AmazonAdCampaignAll();
        amazonAdCampaign.setPuid(vo.getPuid());
        amazonAdCampaign.setName(vo.getName());

        amazonAdCampaign.setBudget(vo.getDailyBudget() == null ? null : BigDecimal.valueOf(vo.getDailyBudget()));

        amazonAdCampaign.setShopId(profile.getShopId());
        amazonAdCampaign.setProfileId(profile.getProfileId());
        amazonAdCampaign.setMarketplaceId(profile.getMarketplaceId());

        amazonAdCampaign.setCreateId(vo.getUid());
        amazonAdCampaign.setTargetingType(vo.getTargetingType());
        amazonAdCampaign.setCampaignType(Constants.SPONSORED_PRODUCTS);
        amazonAdCampaign.setState(Constants.ENABLED);
        amazonAdCampaign.setCreateInAmzup(Constants.CREATE_IN_AMZUP);

        if (Objects.nonNull(vo.getPortfolioId())) {
            amazonAdCampaign.setPortfolioId(vo.getPortfolioId());
        }

        if (StringUtils.isNotBlank(vo.getStartDateStr())) {
            amazonAdCampaign.setStartDate(DateUtil.strToDate4(vo.getStartDateStr()));
        }
        if (StringUtils.isNotBlank(vo.getEndDateStr())) {
            amazonAdCampaign.setEndDate(DateUtil.strToDate4(vo.getEndDateStr()));
        }

        // 竞价策略
        amazonAdCampaign.setStrategy(vo.getStrategy());

        // 根据广告位调整竞价
        List<Adjustment> list = Lists.newArrayListWithExpectedSize(2);
        if (vo.getPlacementProductPage() != null) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTPRODUCTPAGE.value());
            adjustment.setPercentage(vo.getPlacementProductPage());
            list.add(adjustment);
        }
        if (vo.getPlacementTop() != null) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTTOP.value());
            adjustment.setPercentage(vo.getPlacementTop());
            list.add(adjustment);
        }
        if (vo.getPlacementRestOfSearch() != null) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.PLACEMENTRESTOFSEARCH.value());
            adjustment.setPercentage(vo.getPlacementRestOfSearch());
            list.add(adjustment);
        }
        if (vo.getPlacementSiteAmazonBusiness() != null && Constants.placementSiteAmazonBusinessMarketplaceIds.contains(profile.getMarketplaceId())) {
            Adjustment adjustment = new Adjustment();
            adjustment.setPredicate(PredicateEnum.SITEAMAZONBUSINESS.value());
            adjustment.setPercentage(vo.getPlacementSiteAmazonBusiness());
            list.add(adjustment);
        }
        amazonAdCampaign.setAdjustments(JSONUtil.objectToJson(list));
        amazonAdCampaign.setAdTargetType(vo.getTargetingType());
        amazonAdCampaign.setType(CampaignTypeEnum.sp.getCampaignType());
        return amazonAdCampaign;
    }

    private List<AmazonAdKeyword> turnToAdManageOperationKeywordLogPo(List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywords) {
        List<AmazonAdKeyword> list = new ArrayList<>();
        AmazonAdKeyword adKeyword;
        for (AmazonAdCampaignNeKeywords amazonAdCampaignNeKeyword : amazonAdCampaignNeKeywords) {
            adKeyword = new AmazonAdKeyword();
            BeanUtils.copyProperties(amazonAdCampaignNeKeyword, adKeyword);
            list.add(adKeyword);
        }
        return list;
    }

    private List<AmazonAdTargeting> turnToAdManageOperationTargetLogPo(List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSps) {
        List<AmazonAdTargeting> list = new ArrayList<>();
        AmazonAdTargeting adTargeting;
        for (AmazonAdCampaignNetargetingSp amazonAdTargeting : amazonAdCampaignNetargetingSps) {
            adTargeting = new AmazonAdTargeting();
            BeanUtils.copyProperties(amazonAdTargeting, adTargeting);
            list.add(adTargeting);
        }
        return list;
    }

    // 组装接口数据
    private Campaign makeCampaignByCampaignPo(AmazonAdCampaignAll amazonAdCampaign) {
        Campaign campaign = new Campaign();
        if (StringUtils.isNotBlank(amazonAdCampaign.getCampaignId())) {
            campaign.setCampaignId(Long.valueOf(amazonAdCampaign.getCampaignId()));
        }

        campaign.setCampaignType(Constants.SPONSORED_PRODUCTS);
        campaign.setTargetingType(amazonAdCampaign.getTargetingType());
        campaign.setName(amazonAdCampaign.getName());
        campaign.setState(amazonAdCampaign.getState());
        campaign.setDailyBudget(amazonAdCampaign.getBudget().doubleValue());
        campaign.setStartDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getStartDate(), "yyyyMMdd"));

        if (amazonAdCampaign.getEndDate() != null) {
            campaign.setEndDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getEndDate(), "yyyyMMdd"));
        } else if ("".equalsIgnoreCase(amazonAdCampaign.getEndTimeStr())){
            campaign.setEndDate("");
        }

        if (amazonAdCampaign.getPortfolioId() != null) {
            if ("".equalsIgnoreCase(amazonAdCampaign.getPortfolioId())) {
                campaign.setPortfolioId(0L);  // 用户移除广告组合
            } else {
                campaign.setPortfolioId(Long.valueOf(amazonAdCampaign.getPortfolioId()));
            }
        }

        if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy())) {
            Bidding bidding = new Bidding();
            bidding.setStrategy(amazonAdCampaign.getStrategy());
            List<Adjustment> adjustments = null;
            if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
            }
            bidding.setAdjustments(adjustments);
            campaign.setBidding(bidding);
        }

        return campaign;
    }

    @Override
    public CampaignEntityV3 makeCampaignByCampaignPoV3(AmazonAdCampaignAll amazonAdCampaign) {
        CampaignEntityV3 campaign = new CampaignEntityV3();
        campaign.setTargetingType(SpV3TargetingTypeEnum.getSpV3TargetingTypeEnumByValue(amazonAdCampaign.getTargetingType()).valueV3());
        campaign.setName(amazonAdCampaign.getName());
        campaign.setState(SpV3StateEnum.getSpV3StateEnumByValue(amazonAdCampaign.getState()).valueV3());
        CampaignBudget budget = new CampaignBudget();
        budget.setBudget(amazonAdCampaign.getBudget().doubleValue());
        campaign.setBudget(budget);
        campaign.setStartDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getStartDate(), "yyyy-MM-dd"));

        if (amazonAdCampaign.getEndDate() != null) {
            campaign.setEndDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getEndDate(), "yyyy-MM-dd"));
        } else if ("".equalsIgnoreCase(amazonAdCampaign.getEndTimeStr())){
            campaign.setEndDate("");
        }


        if (amazonAdCampaign.getPortfolioId() != null) {
            if ("".equalsIgnoreCase(amazonAdCampaign.getPortfolioId())) {
                campaign.setPortfolioId(StringUtils.EMPTY);  // 用户移除广告组合
            } else {
                campaign.setPortfolioId(amazonAdCampaign.getPortfolioId());
            }
        }
        if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy()) && CampaignStrategyV3.fromOldValue(amazonAdCampaign.getStrategy()) != null) {

            List<Adjustment> adjustments = null;
            if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
            }

            CamapginDynamicBidding camapginDynamicBidding = new CamapginDynamicBidding();
            camapginDynamicBidding.setStrategy(CampaignStrategyV3.fromOldValue(amazonAdCampaign.getStrategy()).getValue());

            List<PlacementBidding> placementList = new ArrayList<>();
            if (adjustments != null) {
                for (Adjustment adjustment : adjustments) {
                    PlacementBidding placementBidding = new PlacementBidding();
                    placementBidding.setPercentage(adjustment.getPercentage().intValue());
                    CampaignPlacementV3 campaignPlacementV3 = CampaignPlacementV3.fromOldValue(adjustment.getPredicate());
                    if (campaignPlacementV3 != null) {
                        placementBidding.setPlacement(campaignPlacementV3.getValue());
                    }
                    placementList.add(placementBidding);
                }
                camapginDynamicBidding.setPlacementBidding(placementList);
            }

            campaign.setDynamicBidding(camapginDynamicBidding);
        }

        return campaign;
    }


    // 组装更新SP接口数据
    private CampaignEntityV3 makeUpdateCampaignByCampaignPo(AmazonAdCampaignAll amazonAdCampaign) {
        CampaignEntityV3 campaign = new CampaignEntityV3();
        if (StringUtils.isNotBlank(amazonAdCampaign.getCampaignId())) {
            campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        }

        campaign.setTargetingType(TargetingTypeV3.fromOldValue(amazonAdCampaign.getTargetingType()).getValue());
        campaign.setName(amazonAdCampaign.getName());
        campaign.setState(amazonAdCampaign.getState().toUpperCase());
        //组装预算对象
        CampaignBudget budget = new CampaignBudget();
        budget.setBudget(amazonAdCampaign.getBudget().doubleValue());
        campaign.setBudget(budget);
        campaign.setStartDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getStartDate(), "yyyy-MM-dd"));

        if (amazonAdCampaign.getEndDate() != null) {
            campaign.setEndDate(DateUtil.dateToStrWithFormat(amazonAdCampaign.getEndDate(), "yyyy-MM-dd"));
        } else if ("".equalsIgnoreCase(amazonAdCampaign.getEndTimeStr())){
            campaign.setEndDate(StringUtils.EMPTY);
        }

        if (amazonAdCampaign.getPortfolioId() != null) {
            if ("".equalsIgnoreCase(amazonAdCampaign.getPortfolioId())) {
                campaign.setPortfolioId(StringUtils.EMPTY);  // 用户移除广告组合
            } else {
                campaign.setPortfolioId(amazonAdCampaign.getPortfolioId());
            }
        }

        if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy())) {

            List<Adjustment> adjustments = null;
            if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
            }

            CamapginDynamicBidding camapginDynamicBidding = new CamapginDynamicBidding();
            camapginDynamicBidding.setStrategy(CampaignStrategyV3.fromOldValue(amazonAdCampaign.getStrategy()).getValue());

            List<PlacementBidding> placementList = new ArrayList<>();
            if (adjustments != null) {
                for (Adjustment adjustment : adjustments) {
                    PlacementBidding placementBidding = new PlacementBidding();
                    placementBidding.setPercentage(adjustment.getPercentage().intValue());
                    CampaignPlacementV3 campaignPlacementV3 = CampaignPlacementV3.fromOldValue(adjustment.getPredicate());
                    if (campaignPlacementV3 != null) {
                        placementBidding.setPlacement(campaignPlacementV3.getValue());
                    }
                    placementList.add(placementBidding);
                }
                camapginDynamicBidding.setPlacementBidding(placementList);
            }

            campaign.setDynamicBidding(camapginDynamicBidding);
        }

        return campaign;
    }


    /**
     * 根据更新类型进行指定字段填充(接口升级V3)
     * @param amazonAdCampaign
     * @param type
     * @return
     */
    private CampaignEntityV3 makeCampaignV3ByCampaignPo(AmazonAdCampaignAll amazonAdCampaign,String type) {
        CampaignEntityV3 campaign = new CampaignEntityV3();
        if (StringUtils.isNotBlank(amazonAdCampaign.getCampaignId())) {
            campaign.setCampaignId(amazonAdCampaign.getCampaignId());
        }
        if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            //组装预算对象
            CampaignBudget budget = new CampaignBudget();
            budget.setBudget(amazonAdCampaign.getBudget().doubleValue());
            campaign.setBudget(budget);
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            campaign.setState(AdStateV3.fromOldValue(amazonAdCampaign.getState()).getValue());
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)) {
            if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy())) {
                List<Adjustment> adjustments = null;
                if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                    adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
                }

                CamapginDynamicBidding camapginDynamicBidding = new CamapginDynamicBidding();
                camapginDynamicBidding.setStrategy(CampaignStrategyV3.fromOldValue(amazonAdCampaign.getStrategy()).getValue());

                List<PlacementBidding> placementList = new ArrayList<>();
                if (adjustments != null) {
                    for (Adjustment adjustment : adjustments) {
                        PlacementBidding placementBidding = new PlacementBidding();
                        placementBidding.setPercentage(adjustment.getPercentage().intValue());
                        CampaignPlacementV3 campaignPlacementV3 = CampaignPlacementV3.fromOldValue(adjustment.getPredicate());
                        if (campaignPlacementV3 != null) {
                            placementBidding.setPlacement(campaignPlacementV3.getValue());
                        }
                        placementList.add(placementBidding);
                    }
                    camapginDynamicBidding.setPlacementBidding(placementList);
                }

                campaign.setDynamicBidding(camapginDynamicBidding);
            }
        } else {
            return null ;
        }
        return campaign;
    }

    /**
     * 根据更新类型进行指定字段填充
     * @param amazonAdCampaign
     * @param type
     * @return
     */
    private Campaign makeCampaignByCampaignPo(AmazonAdCampaignAll amazonAdCampaign,String type) {
        Campaign campaign = new Campaign();
        campaign.setCampaignType(Constants.SPONSORED_PRODUCTS);
        if (StringUtils.isNotBlank(amazonAdCampaign.getCampaignId())) {
            campaign.setCampaignId(Long.valueOf(amazonAdCampaign.getCampaignId()));
        }
        if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            campaign.setDailyBudget(amazonAdCampaign.getBudget().doubleValue());
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            campaign.setState(amazonAdCampaign.getState());
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)) {
            if (StringUtils.isNotBlank(amazonAdCampaign.getStrategy())) {
                Bidding bidding = new Bidding();
                bidding.setStrategy(amazonAdCampaign.getStrategy());
                List<Adjustment> adjustments = null;
                if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
                    adjustments = JSONUtil.jsonToArray(amazonAdCampaign.getAdjustments(), Adjustment.class);
                }
                bidding.setAdjustments(adjustments);
                campaign.setBidding(bidding);
            }
        } else {
            return null ;
        }
        return campaign;
    }

    /**
     * 根据更新类型进行数据校验
     * @param batchCampaignVo
     * @param type
     * @return
     */
    private BatchCampaignVo checkBatchVo(BatchCampaignVo batchCampaignVo,String type) {

        if(batchCampaignVo.getDxmCampaignId() == null || batchCampaignVo.getDxmCampaignId() < 1 ){
            batchCampaignVo.setFailReason("参数错误");
            return batchCampaignVo;
        }

        if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)) {
            if(batchCampaignVo.getDailyBudget() == null || batchCampaignVo.getDailyBudget().isNaN() || batchCampaignVo.getDailyBudget() <1 || batchCampaignVo.getDailyBudget() >1000000){
                batchCampaignVo.setFailReason("每日预算请输入1-1000000");
            }
            return batchCampaignVo;
        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)) {
            if(StringUtils.isEmpty(batchCampaignVo.getState())){
                batchCampaignVo.setFailReason("参数错误");
            }
            return batchCampaignVo;
        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)){
            if(StringUtils.isBlank(batchCampaignVo.getStrategy()) && batchCampaignVo.getPlacementProductPage() == null && batchCampaignVo.getPlacementTop() == null && batchCampaignVo.getPlacementRestOfSearch() == null){
                batchCampaignVo.setFailReason("广告位最少要设置一个值");
            }
            return batchCampaignVo;
        } else {
            batchCampaignVo.setFailReason("参数错误");
            return batchCampaignVo;
        }

    }

    private String checkAddNeKeywordsVo(List<NeKeywordsVo> neKeywords) {
        for (NeKeywordsVo vo : neKeywords) {
            if (StringUtils.isBlank(vo.getKeywordText()) || vo.getMatchType() == null) {
                return "没有关键词信息";
            }
        }
        return null;
    }

    // vo -> po
    private List<AmazonAdCampaignNeKeywords> convertAddNeKeywordsVoToPo(Integer uid, AmazonAdCampaignAll amazonAdCampaign, List<NeKeywordsVo> neKeywords) {
        List<AmazonAdCampaignNeKeywords> amazonAdKeywords = new ArrayList<>(neKeywords.size());
        AmazonAdCampaignNeKeywords amazonAdKeyword;
        for (NeKeywordsVo vo : neKeywords) {
            amazonAdKeyword = new AmazonAdCampaignNeKeywords();
            amazonAdKeyword.setPuid(amazonAdCampaign.getPuid());
            amazonAdKeyword.setShopId(amazonAdCampaign.getShopId());
            amazonAdKeyword.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
            amazonAdKeyword.setProfileId(amazonAdCampaign.getProfileId());
            amazonAdKeyword.setCampaignId(amazonAdCampaign.getCampaignId());
            amazonAdKeyword.setKeywordText(vo.getKeywordText());
            amazonAdKeyword.setMatchType(vo.getMatchType());
            amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
            amazonAdKeyword.setCreateId(uid);
            amazonAdKeywords.add(amazonAdKeyword);
        }
        return amazonAdKeywords;
    }

    // 转换对象(AmazonAdCampaignNeKeywords->AmazonAdKeyword)
    private List<AmazonAdKeyword> convertAmazonAdKeyword(List<AmazonAdCampaignNeKeywords> campaignNeKeywords) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(campaignNeKeywords.size());
        AmazonAdKeyword amazonAdKeyword;
        for (AmazonAdCampaignNeKeywords keywords : campaignNeKeywords) {
            amazonAdKeyword = new AmazonAdKeyword();
            amazonAdKeyword.setPuid(keywords.getPuid());
            amazonAdKeyword.setShopId(keywords.getShopId());
            amazonAdKeyword.setMarketplaceId(keywords.getMarketplaceId());
            amazonAdKeyword.setProfileId(keywords.getProfileId());
            amazonAdKeyword.setCampaignId(keywords.getCampaignId());
            amazonAdKeyword.setKeywordText(keywords.getKeywordText());
            amazonAdKeyword.setMatchType(keywords.getMatchType());
            amazonAdKeyword.setState(keywords.getState());
            amazonAdKeyword.setCreateId(keywords.getCreateId());
            amazonAdKeyword.setKeywordId(keywords.getKeywordId());
            amazonAdKeywords.add(amazonAdKeyword);
        }
        return amazonAdKeywords;
    }

    private List<Object> buildUpLogMessage(Map<Long,AmazonAdCampaignAll> oldList,List<AmazonAdCampaignAll> newList,String type){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        if(Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)){
            dataList.add("SP广告活动批量修改每日预算");

        } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)){
            dataList.add("SP广告活动批量修改广告位");

        } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
            dataList.add("SP广告活动批量修改状态");

        }
        newList.forEach(e ->{
            AmazonAdCampaignAll old = oldList.get(e.getId());

            if(Constants.CPC_CAMPAIGN_BATCH_UPDATE_BUDGET.equals(type)){

                builder.append("campaignId:").append(e.getCampaignId());
                if(old != null){
                    builder.append(",旧值:").append(old.getBudget());
                }
                builder.append(",新值:").append(e.getBudget());
            } else if (Constants.CPC_CAMPAIGN_BATCH_UPDATE_ADVERTISING_SPACE.equals(type)){

                builder.append("campaignId:").append(e.getCampaignId());
                if(old != null){
                    builder.append(",旧值:").append(old.getStrategy()).append(old.getAdjustments());
                }
                builder.append(",新值:").append(e.getStrategy()).append(e.getAdjustments());
            } else if (Constants.CPC_BATCH_UPDATE_STATUS.equals(type)){
                
                builder.append("campaignId:").append(e.getCampaignId());
                if(old != null){
                    builder.append(",旧值:").append(old.getState());
                }
                builder.append(",新值:").append(e.getState());
            }
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }


    private List<Object> buildTargetUpLogMessage(Map<Long, AmazonAdCampaignNetargetingSp> oldList, List<AmazonAdCampaignNetargetingSp> newList) {
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        newList.forEach(e -> {
            AmazonAdCampaignNetargetingSp old = oldList.get(e.getId());
            builder.append("SP活动否定商品定位批量修改状态,");
            builder.append("targetId:").append(e.getTargetId());
            if(old != null){
                builder.append(",旧值:").append(old.getState());
            }
            builder.append(",新值:").append(e.getState());
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }

    private List<Object> buildKeywordUpLogMessage(Map<Long,AmazonAdCampaignNeKeywords> oldList,
                                                  List<AmazonAdCampaignNeKeywords> newList) {
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        newList.forEach(e -> {
            AmazonAdCampaignNeKeywords old = oldList.get(e.getId());
            builder.append("SP活动否定关键词批量修改状态,");
            builder.append("keywordId:").append(e.getKeywordId());
            if(old != null){
                builder.append(",旧值:").append(old.getState());
            }
            builder.append(",新值:").append(e.getState());
            dataList.add(builder.toString());
            builder.delete(0,builder.length());
        });
        return dataList;
    }

    @Override
    public List<AdCampaignOptionVo> getAllCampaigns(Integer shopId) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(shopId);
        if (shopAuth==null) {
            AssertUtil.fail("店铺未授权");
        }

        List<AmazonAdCampaignAll> campaignsByShop = amazonAdCampaignAllDao.getCampaignsByShop(shopAuth.getPuid(), shopAuth.getId(),CampaignTypeEnum.sp.getCampaignType());
        List<AdCampaignOptionVo> adCampaignOptionVos = new ArrayList<>();
        campaignsByShop.stream().filter(Objects::nonNull).forEach(cam-> {
            AdCampaignOptionVo optionVo = AdCampaignOptionVo.builder()
                    .campaignId(cam.getCampaignId())
                    .name(cam.getName()).build();
            adCampaignOptionVos.add(optionVo);
        });
        return adCampaignOptionVos;
    }

    @Override
    public Result getBudgetRecommendation(Integer puid, Integer shopId, List<String> campaignIds) {
        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }
        SpCampaignBudgetRecommendationResponse response = BudgetRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBudgetRecommendationsResponse(shopAuthService.getAdToken(shop), profile.getProfileId(), profile.getMarketplaceId(), campaignIds);
        if (response != null && response.getStatusCode() != null && response.getStatusCode() == 401) {
            // 刷新token重试一次
            shopAuthService.refreshCpcAuth(shop);
            response = BudgetRecommendationClient.getInstance(dynamicRefreshNacosConfiguration.getAmazonProxyEnable()).getBudgetRecommendationsResponse(shopAuthService.getAdToken(shop), profile.getProfileId(), profile.getMarketplaceId(), campaignIds);
        }
        if (response == null) {
            return ResultUtil.error("网络延迟，请稍后重试");
        }
        if (response.getError() != null && response.getError().getDescription() != null) {
            return ResultUtil.returnErr(AmazonErrorUtils.getError(response.getError().getDescription()));
        }
        if (response.getError() != null && response.getError().getDetails() != null) {
            return ResultUtil.returnErr(response.getError().getDetails());
        }
        return ResultUtil.returnSucc(response.getResult());
    }

    @Override
    public Result<BatchDataResponse> batchAddNeKeywords(AddCampaignNeKeywordsVo addNeKeywordsVo, String loginIp) {
        Result<BatchDataResponse> finalResult = ResultUtil.success();
        StringBuilder errStr = new StringBuilder();
        List<Int32Value> errorList = Lists.newArrayList();
        List<Int32Value> succList = Lists.newArrayList();
        List<NeKeywordsVo> neKeywords = addNeKeywordsVo.getNeKeywords();

        List<String> campaignIds = neKeywords.stream().filter(item -> StringUtils.isNotBlank(item.getCampaignId()))
                .map(NeKeywordsVo::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> campaignAllMap = amazonAdCampaignAllDao.listByCampaignId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), campaignIds, Constants.SP).stream()
                .filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));

        // 排除已存在的关键词
        Iterator<NeKeywordsVo> it = neKeywords.iterator();
        NeKeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdCampaignNeKeywordsDao.exist(addNeKeywordsVo.getPuid(), next.getCampaignId(), next.getKeywordText(), next.getMatchType())) {
                it.remove();
                errStr.append("targetValue:" + next.getKeywordText() + ",desc:该否定关键词已存在;</br>");
                errorList.add(Int32Value.of(next.getIndex()));
            }
            if (campaignAllMap.get(next.getCampaignId()) == null) {
                it.remove();
                errStr.append("targetValue:" + next.getKeywordText() + ",desc:该否定关键词的广告活动不存在;</br>");
                errorList.add(Int32Value.of(next.getIndex()));
            }
            // 包含特殊符号处理
            String errMsg = SpecSymbolUtils.validateSymbol(next.getKeywordText());
            if (StringUtils.isNotBlank(errMsg)) {
                errStr.append("targetValue:" + next.getKeywordText() + ",desc:" + errMsg + "</br>");
                errorList.add(Int32Value.of(next.getIndex()));
            }
        }

        if (neKeywords.size() == 0) {
            if (StringUtils.isNotBlank(errStr)) {
                return ResultUtil.returnErr(errStr.toString());
            } else {
                return ResultUtil.returnErr("request param error");
            }
        }

        List<SpNeKeywordsVo> spNeKeywordsVos = Lists.newArrayList();
        neKeywords.forEach(item ->{
            SpNeKeywordsVo spNeKeywordsVo = new SpNeKeywordsVo();
            AmazonAdCampaignAll amazonAdCampaign = campaignAllMap.get(item.getCampaignId());
            spNeKeywordsVo.setPuid(amazonAdCampaign.getPuid());
            spNeKeywordsVo.setShopId(amazonAdCampaign.getShopId());
            spNeKeywordsVo.setMarketplaceId(amazonAdCampaign.getMarketplaceId());
            spNeKeywordsVo.setProfileId(amazonAdCampaign.getProfileId());
            spNeKeywordsVo.setCampaignId(amazonAdCampaign.getCampaignId());
            spNeKeywordsVo.setIndex(item.getIndex());
            spNeKeywordsVo.setKeywordText(item.getKeywordText());
            spNeKeywordsVo.setMatchType(item.getMatchType());
            spNeKeywordsVo.setState(CpcStatusEnum.enabled.name());
            spNeKeywordsVo.setUid(addNeKeywordsVo.getUid());
            spNeKeywordsVos.add(spNeKeywordsVo);
        });
        Result result = cpcNeKeywordsApiService.createCampaignNegativeKeyword(spNeKeywordsVos);
        if (result.error() && StringUtils.isNotBlank(result.getMsg())) {
            errStr.append(result.getMsg());
            return ResultUtil.returnErr(errStr.toString());
        }
        List<AmazonAdCampaignNeKeywords> amazonAdKeywords = convertBatchAddNeKeywordsVoToPo(spNeKeywordsVos);
        /**
         * TODO 活动否定关键词投放增加日志
         * 操作类型：否定投放新增（活动否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AmazonAdKeyword> amazonAdKeywordList = convertAmazonAdKeyword(amazonAdKeywords);
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
        List<ErrorItemResultV3> resultList = (List<ErrorItemResultV3>)result.getData();
        Map<Integer,ErrorItemResultV3> resultV3Map = new HashMap<>();
        if(CollectionUtils.isNotEmpty(resultList)){
            resultV3Map = resultList.stream().collect(Collectors.toMap(ErrorItemResultV3::getIndex,e->e,(e1,e2)->e2));
        }
        int resultIndex = 0;
        for (AmazonAdKeyword keyword : amazonAdKeywordList) {
            AdManageOperationLog operationLog = adManageOperationLogService.getkeywordsLog(null, keyword);
            operationLog.setIp(loginIp);
            if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                String errors = "";
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                if (CollectionUtils.isNotEmpty(resultList)) {

                    ErrorItemResultV3 resultV3 = resultV3Map.get(Integer.valueOf(resultIndex));
                    String msg = "添加失败";
                    if(resultV3 != null ){
                        msg = resultV3.getErrors().get(0).getErrorMessage();
                    }
                    errors = "targetValue:" + keyword.getKeywordText() + ",desc:" + msg + ";</br>";
                } else {
                    errors = result.getMsg();
                }
                operationLog.setResultInfo(errors);
            }
            operationLogs.add(operationLog);
            resultIndex++;
        }

        adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);
        if (result.success()) {
            amazonAdKeywords = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
            amazonAdCampaignNeKeywordsDao.insertOnDuplicateKeyUpdate(addNeKeywordsVo.getPuid(), amazonAdKeywords);
            saveCampaignNekeywordDoris(amazonAdKeywords, true, true);
        }
        if (StringUtils.isNotBlank(result.getMsg())) {
            errStr.append(result.getMsg());
        }
        List<Int32Value> errList = spNeKeywordsVos.stream().filter(item -> StringUtils.isBlank(item.getKeywordId())).map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errList)) {
            errorList.addAll(errList);
        }
        succList = spNeKeywordsVos.stream().filter(item -> StringUtils.isNotBlank(item.getKeywordId())).map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        finalResult.setData(BatchDataResponse.newBuilder()
                .setFailMsg(errStr.toString())
                .addAllFail(errorList)
                .addAllSuccess(succList).build());

        return finalResult;

    }

    /**
     * 广告活动-创建否定商品投放
     * @return
     */
    @Override
    public Result<BatchDataResponse> batchAddNeTargets(CampaignNeTargetingSpAddParam addParam) {
        Result<BatchDataResponse> result = ResultUtil.success();
        StringBuilder errorMsg = new StringBuilder();
        List<Int32Value> errorList = Lists.newArrayList();
        List<Int32Value> successList = Lists.newArrayList();
        List<NeTargetingVo> targetVos = addParam.getAsinList();
        //查询店铺
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(addParam.getShopId());
        if (shopAuth==null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        List<String> campaignIds = targetVos.stream().filter(item -> StringUtils.isNotBlank(item.getCampaignId()))
                .map(NeTargetingVo::getCampaignId).collect(Collectors.toList());
        Map<String, AmazonAdCampaignAll> campaignAllMap = amazonAdCampaignAllDao.listByCampaignId(shopAuth.getPuid(), shopAuth.getId(), campaignIds, Constants.SP).stream()
                .filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity()));

        NeTargetingVo next;
        Iterator<NeTargetingVo> iterator = targetVos.iterator();
        while (iterator.hasNext()) {
            next = iterator.next();
            //查询活动是否存在
            AmazonAdCampaignAll existAmazonAdCampaign = campaignAllMap.get(next.getCampaignId());
            if (existAmazonAdCampaign== null) {
                errorMsg.append("targetValue:" + (StringUtils.isNotBlank(next.getAsin()) ? next.getAsin() : next.getBrandName()) + ",desc: 该广告活动不存在;</br>");
                iterator.remove();
                errorList.add(Int32Value.of(next.getIndex()));
            }
            if (Constants.MANUAL.equalsIgnoreCase(existAmazonAdCampaign.getTargetingType())) {
//                AssertUtil.fail("SP手动类型广告活动不支持添加否定投放");
                errorMsg.append("targetValue:" + (StringUtils.isNotBlank(next.getAsin()) ? next.getAsin() : next.getBrandName()) + ",desc: 该广告活动属于SP手动类型广告活动不支持添加否定投放;</br>");
                iterator.remove();
                errorList.add(Int32Value.of(next.getIndex()));
            }
        }
        if (CollectionUtils.isEmpty(targetVos)) {
            if (StringUtils.isNotBlank(errorMsg)) {
                return ResultUtil.returnErr(errorMsg.toString());
            } else {
                return ResultUtil.returnErr("request param error");
            }
        }

        //调用接口,创建
        List<CampaignNeTargetingSpAddReturnVo> returnVos = campaignNeTargetingApiService.create(shopAuth, targetVos);
        /**
         * TODO 活动否定商品投放增加日志
         * 操作类型：否定投放新增（活动否定商品投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
        returnVos.forEach(vo -> {
            AmazonAdTargeting targeting = new AmazonAdTargeting();
            targeting.setPuid(addParam.getPuid());
            targeting.setShopId(shopAuth.getId());
            targeting.setMarketplaceId(shopAuth.getMarketplaceId());
            targeting.setCampaignId(vo.getCampaignId());
            targeting.setType("negativeAsin");
            targeting.setCreateId(addParam.getUid());
            targeting.setTargetId(vo.getTargetId());
            targeting.setTitle(vo.getTitle());
            targeting.setTargetingValue(vo.getAsin());
            AdManageOperationLog operationLog = adManageOperationLogService.getTargetsLog(null, targeting);
            operationLog.setIp(addParam.getLoginIp());
            if ("SUCCESS".equals(vo.getState())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                operationLog.setResultInfo(vo.getFailReason());
            }
            operationLogs.add(operationLog);
        });
        adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);

        List<AmazonAdCampaignNetargetingSp> list = new ArrayList<>();

        //保存成功数据
        if (CollectionUtils.isNotEmpty(returnVos)) {
            returnVos.forEach(re->{

                AmazonAdCampaignNetargetingSp campaignNetargetingSp = AmazonAdCampaignNetargetingSp.builder()
                        .createId(addParam.getPuid())
                        .state(StateEnum.ENABLED.value())
                        .createState(re.getState())
                        .campaignId(re.getCampaignId())
                        .createInAmzup(1)
                        .targetText(re.getAsin())
                        .targetId(re.getTargetId())
                        .title(re.getTitle())
                        .imgUrl(re.getImgUrl())
                        .puid(shopAuth.getPuid())
                        .shopId(shopAuth.getId())
                        .marketplaceId(shopAuth.getMarketplaceId())
                        .type("asin").build();
                list.add(campaignNetargetingSp);
            });
        } else {
//            AssertUtil.fail("请求接口失败");
            return ResultUtil.returnErr("请求接口失败");
        }

        //批量保存数据
        if (CollectionUtils.isNotEmpty(list)) {
            campaignNetargetingSpDao.batchSave(shopAuth.getPuid(), list);
            List<String> collect = list.stream().map(AmazonAdCampaignNetargetingSp::getTargetId).collect(Collectors.toList());
            saveCampaignNetargetDoris(shopAuth.getPuid(), shopAuth.getId(), collect);
        }
        String errMsg = targetVos.stream().filter(item -> StringUtils.isNotBlank(item.getFailReason())).map(item ->
                        "targetValue:" + item.getAsin() + ",desc: " + item.getFailReason() + "</br>")
                .collect(Collectors.joining(";"));
        if (StringUtils.isNotBlank(errMsg)) {
            errorMsg.append(errMsg);
        }
        List<Int32Value> errList = targetVos.stream().filter(item -> StringUtils.isBlank(item.getTargetId()))
                .map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(errList)) {
            errorList.addAll(errList);
        }
        successList = targetVos.stream().filter(item -> StringUtils.isNotBlank(item.getTargetId()))
                .map(item -> Int32Value.of(item.getIndex())).collect(Collectors.toList());
        result.setData(BatchDataResponse.newBuilder()
                .setFailMsg(errorMsg.toString())
                .addAllFail(errorList)
                .addAllSuccess(successList).build());

        return result;
    }

    // 广告活动层级否定关键词投放：vo -> po
    private List<AmazonAdCampaignNeKeywords> convertBatchAddNeKeywordsVoToPo(List<SpNeKeywordsVo> neKeywords) {
        List<AmazonAdCampaignNeKeywords> amazonAdKeywords = new ArrayList<>(neKeywords.size());
        AmazonAdCampaignNeKeywords amazonAdKeyword;
        for (SpNeKeywordsVo vo : neKeywords) {
            amazonAdKeyword = new AmazonAdCampaignNeKeywords();
            amazonAdKeyword.setPuid(vo.getPuid());
            amazonAdKeyword.setShopId(vo.getShopId());
            amazonAdKeyword.setMarketplaceId(vo.getMarketplaceId());
            amazonAdKeyword.setProfileId(vo.getProfileId());
            amazonAdKeyword.setCampaignId(vo.getCampaignId());
            if (StringUtils.isNotBlank(vo.getKeywordId())) {
                amazonAdKeyword.setKeywordId(vo.getKeywordId());
            }
            amazonAdKeyword.setKeywordText(vo.getKeywordText());
            amazonAdKeyword.setMatchType(vo.getMatchType());
            amazonAdKeyword.setState(vo.getState());
            amazonAdKeyword.setCreateId(vo.getUid());
            amazonAdKeywords.add(amazonAdKeyword);
        }
        return amazonAdKeywords;
    }

    /**
     * 设置广告位竞价修改标识到redis中
     * @param placement
     * @param campaignId
     */
    private void setPlacementBidUpdateIdentifying(String placement, String campaignId) {
        //广告管理页面日志
        Integer code = null;
        if (PredicateEnum.PLACEMENTTOP.value().equals(placement)) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode();
        } else if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(placement)) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode();
        } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(placement)) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode();
        } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(placement)) {
            code = AmazonAdOperationLogChangeTypeEnum.PLACEMENT_SITE_AMAZON_BUSINESS.getCode();
        }
        if (code != null) {
            String logKey = String.format("%s-%s", code, campaignId);
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
    }

    private void fillBudgetAndBidUpdateKey(AmazonAdCampaignAll amazonAdCampaign, AmazonAdCampaignAll oldAmazonAdCampaign) {
        //修改预算标识
        if (amazonAdCampaign.getBudget() != null && oldAmazonAdCampaign.getBudget() != null && amazonAdCampaign.getBudget().compareTo(oldAmazonAdCampaign.getBudget()) != 0) {
            String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.CAMPAIGN_BUDGET_AMOUNT.getCode(), oldAmazonAdCampaign.getCampaignId());
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
        Double oldPlacementProductPageBid = 0.00;
        Double oldPlacementTopBid = 0.00;
        Double oldPlacementRestOfSearchBid = 0.00;
        Double oldPlacementSiteAmazonBusiness = 0.00;
        if (StringUtils.isNotBlank(oldAmazonAdCampaign.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(oldAmazonAdCampaign.getAdjustments(), new TypeReference<List<Adjustment>>() {});
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        oldPlacementProductPageBid = adjustment.getPercentage();
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        oldPlacementTopBid = adjustment.getPercentage();
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        oldPlacementRestOfSearchBid = adjustment.getPercentage();
                    } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(adjustment.getPredicate())) {
                        oldPlacementSiteAmazonBusiness = adjustment.getPercentage();
                    }
                }
            }
        }
        Double newPlacementProductPageBid = 0.00;
        Double newPlacementTopBid = 0.00;
        Double newPlacementRestOfSearchBid = 0.00;
        Double newPlacementSiteAmazonBusiness = 0.00;
        if (StringUtils.isNotBlank(amazonAdCampaign.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(amazonAdCampaign.getAdjustments(), new TypeReference<List<Adjustment>>() {});
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        newPlacementProductPageBid = adjustment.getPercentage();
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        newPlacementTopBid = adjustment.getPercentage();
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        newPlacementRestOfSearchBid = adjustment.getPercentage();
                    } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(adjustment.getPredicate())) {
                        newPlacementSiteAmazonBusiness = adjustment.getPercentage();
                    }
                }
            }
        }
        if (!Objects.equals(oldPlacementProductPageBid, newPlacementProductPageBid)) {
            String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_DETAIL_PAGE.getCode(), oldAmazonAdCampaign.getCampaignId());
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
        if (!Objects.equals(oldPlacementTopBid, newPlacementTopBid)) {
            String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_TOP_OF_SEARCH.getCode(), oldAmazonAdCampaign.getCampaignId());
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
        if (!Objects.equals(oldPlacementRestOfSearchBid, newPlacementRestOfSearchBid)) {
            String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_GROUP_REST_OF_SEARCH.getCode(), oldAmazonAdCampaign.getCampaignId());
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
        if (!Objects.equals(oldPlacementSiteAmazonBusiness, newPlacementSiteAmazonBusiness)) {
            String logKey = String.format("%s-%s", AmazonAdOperationLogChangeTypeEnum.PLACEMENT_SITE_AMAZON_BUSINESS.getCode(), oldAmazonAdCampaign.getCampaignId());
            stringRedisService.set(logKey, logKey, Constants.AD_MANAGE_OPERATION_LOG_TIME, TimeUnit.HOURS);
        }
    }

    /**
     * 填充预算和竞价更新的标识
     * @param dailyBudgetUpdateList
     * @param placementProductPageUpdateList
     * @param placementTopUpdateList
     * @param vo
     * @param oldAmazonAdCampaign
     */
    private void fillBudgetAndBidUpdateKey(Set<String> dailyBudgetUpdateList, Set<String> placementProductPageUpdateList, Set<String> placementTopUpdateList,
                                           Set<String> placementRestOfSearchUpdateList, Set<String> placementSiteAmazonBusinessUpdateList,
                                           BatchCampaignVo vo, AmazonAdCampaignAll oldAmazonAdCampaign) {
        if (vo.getDailyBudget() != null && !Objects.equals(oldAmazonAdCampaign.getBudget().doubleValue(), vo.getDailyBudget())) {
            dailyBudgetUpdateList.add(oldAmazonAdCampaign.getCampaignId());
        }
        Double oldPlacementProductPageBid = 0.00;
        Double oldPlacementTopBid = 0.00;
        Double oldPlacementRestOfSearchBid = 0.00;
        Double oldPlacementSiteAmazonBusiness = 0.00;
        if (StringUtils.isNotBlank(oldAmazonAdCampaign.getAdjustments())) {
            List<Adjustment> adjustments = null;
            try {
                adjustments = JSONUtil.jsonToObjectIgnoreUnKnownThrowable(oldAmazonAdCampaign.getAdjustments(), new TypeReference<List<Adjustment>>() {});
            } catch (IOException e) {
                log.error("adjustment:", e);
            }
            if (CollectionUtils.isNotEmpty(adjustments)) {
                for (Adjustment adjustment : adjustments) {
                    if (PredicateEnum.PLACEMENTPRODUCTPAGE.value().equals(adjustment.getPredicate())) {
                        oldPlacementProductPageBid = adjustment.getPercentage();
                    } else if (PredicateEnum.PLACEMENTTOP.value().equals(adjustment.getPredicate())) {
                        oldPlacementTopBid = adjustment.getPercentage();
                    } else if (PredicateEnum.PLACEMENTRESTOFSEARCH.value().equals(adjustment.getPredicate())) {
                        oldPlacementRestOfSearchBid = adjustment.getPercentage();
                    } else if (PredicateEnum.SITEAMAZONBUSINESS.value().equals(adjustment.getPredicate())) {
                        oldPlacementSiteAmazonBusiness = adjustment.getPercentage();
                    }
                }
            }
        }
        if (vo.getPlacementProductPage() != null && !Objects.equals(oldPlacementProductPageBid, vo.getPlacementProductPage())) {
            placementProductPageUpdateList.add(oldAmazonAdCampaign.getCampaignId());
        }
        if (vo.getPlacementTop() != null && !Objects.equals(oldPlacementTopBid, vo.getPlacementTop())) {
            placementTopUpdateList.add(oldAmazonAdCampaign.getCampaignId());
        }
        if (vo.getPlacementRestOfSearch() != null && !Objects.equals(oldPlacementRestOfSearchBid, vo.getPlacementRestOfSearch())) {
            placementRestOfSearchUpdateList.add(oldAmazonAdCampaign.getCampaignId());
        }
        if (vo.getPlacementSiteAmazonBusiness() != null && !Objects.equals(oldPlacementSiteAmazonBusiness, vo.getPlacementSiteAmazonBusiness())) {
            placementSiteAmazonBusinessUpdateList.add(oldAmazonAdCampaign.getCampaignId());
        }
    }

    /**
     * 写入doris
     * @param amazonAdCampaignList
     */
    @Override
    public void saveDoris(List<AmazonAdCampaignAll> amazonAdCampaignList, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdCampaignAll> collect = amazonAdCampaignList.stream().map(x -> {
                OdsAmazonAdCampaignAll odsAmazonAdCampaignAll = new OdsAmazonAdCampaignAll();
                BeanUtils.copyProperties(x, odsAmazonAdCampaignAll);
                if (create) {
                    odsAmazonAdCampaignAll.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdCampaignAll.setUpdateTime(date);
                }
                if (StringUtils.isNotBlank(odsAmazonAdCampaignAll.getState())) {
                    odsAmazonAdCampaignAll.setState(odsAmazonAdCampaignAll.getState().toLowerCase());
                }
                return odsAmazonAdCampaignAll;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param campaignIdList
     */
    private void saveDoris(Integer puid, Integer shopId, List<String> campaignIdList) {
        try {
            if (CollectionUtils.isEmpty(campaignIdList)) {
                return;
            }
            List<AmazonAdCampaignAll> campaignAllList = amazonAdCampaignAllDao.listByCampaignId(puid, shopId, campaignIdList, CampaignTypeEnum.sp.getCampaignType());
            saveDoris(campaignAllList, false, false);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param amazonAdCampaignNeKeywordsList
     */
    @Override
    public void saveCampaignNekeywordDoris(List<AmazonAdCampaignNeKeywords> amazonAdCampaignNeKeywordsList, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdCampaignNeKeywords> collect = amazonAdCampaignNeKeywordsList.stream().map(x -> {
                OdsAmazonAdCampaignNeKeywords odsAmazonAdCampaignNeKeywords = new OdsAmazonAdCampaignNeKeywords();
                BeanUtils.copyProperties(x, odsAmazonAdCampaignNeKeywords);
                if (create) {
                    odsAmazonAdCampaignNeKeywords.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdCampaignNeKeywords.setUpdateTime(date);
                }

                MarketplaceTimeZoneEnum marketplaceTimeZoneEnum = MarketplaceTimeZoneEnum.map.get(x.getMarketplaceId());
                if (odsAmazonAdCampaignNeKeywords.getCreationDate() == null && odsAmazonAdCampaignNeKeywords.getCreateTime() != null) {
                    odsAmazonAdCampaignNeKeywords.setCreationDate(LocalDateTimeUtil.convertDateToLDT(odsAmazonAdCampaignNeKeywords.getCreateTime()));
                    odsAmazonAdCampaignNeKeywords.setAmazonCreateTime(LocalDateTimeUtil.convertDateToLDT(odsAmazonAdCampaignNeKeywords.getCreateTime(), TimeZone.getTimeZone(marketplaceTimeZoneEnum.getZone_id()).toZoneId()));
                }
                if (odsAmazonAdCampaignNeKeywords.getCreationDate() != null) {
                    odsAmazonAdCampaignNeKeywords.setAmazonCreateTime(LocalDateTimeUtil.convertChinaToSiteTime(odsAmazonAdCampaignNeKeywords.getCreationDate(), odsAmazonAdCampaignNeKeywords.getMarketplaceId()));
                    odsAmazonAdCampaignNeKeywords.setCreationAfterDate(odsAmazonAdCampaignNeKeywords.getAmazonCreateTime().plusDays(30L).toLocalDate());
                    odsAmazonAdCampaignNeKeywords.setCreationBeforeDate(odsAmazonAdCampaignNeKeywords.getAmazonCreateTime().plusDays(-30L).toLocalDate());
                }
                if (StringUtils.isNotBlank(odsAmazonAdCampaignNeKeywords.getState())) {
                    odsAmazonAdCampaignNeKeywords.setState(odsAmazonAdCampaignNeKeywords.getState().toLowerCase());
                }
                return odsAmazonAdCampaignNeKeywords;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param amazonAdCampaignNetargetingSpList
     */
    @Override
    public void saveCampaignNeTargetDoris(List<AmazonAdCampaignNetargetingSp> amazonAdCampaignNetargetingSpList, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdCampaignNetargetingSp> collect = amazonAdCampaignNetargetingSpList.stream().map(x -> {
                OdsAmazonAdCampaignNetargetingSp odsAmazonAdCampaignNetargetingSp = new OdsAmazonAdCampaignNetargetingSp();

                BeanUtils.copyProperties(x, odsAmazonAdCampaignNetargetingSp);
                if (create) {
                    odsAmazonAdCampaignNetargetingSp.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdCampaignNetargetingSp.setUpdateTime(date);
                }
                MarketplaceTimeZoneEnum marketplaceTimeZoneEnum = MarketplaceTimeZoneEnum.map.get(odsAmazonAdCampaignNetargetingSp.getMarketplaceId());
                if (odsAmazonAdCampaignNetargetingSp.getCreationDate() == null && odsAmazonAdCampaignNetargetingSp.getCreateTime() != null) {
                    odsAmazonAdCampaignNetargetingSp.setCreationDate(LocalDateTimeUtil.convertDateToLDT(odsAmazonAdCampaignNetargetingSp.getCreateTime()));
                    odsAmazonAdCampaignNetargetingSp.setAmazonCreateTime(LocalDateTimeUtil.convertDateToLDT(odsAmazonAdCampaignNetargetingSp.getCreateTime(), TimeZone.getTimeZone(marketplaceTimeZoneEnum.getZone_id()).toZoneId()));
                }
                if (odsAmazonAdCampaignNetargetingSp.getCreationDate() != null) {
                    odsAmazonAdCampaignNetargetingSp.setAmazonCreateTime(LocalDateTimeUtil.convertChinaToSiteTime(odsAmazonAdCampaignNetargetingSp.getCreationDate(), odsAmazonAdCampaignNetargetingSp.getMarketplaceId()));
                    odsAmazonAdCampaignNetargetingSp.setCreationAfterDate(odsAmazonAdCampaignNetargetingSp.getAmazonCreateTime().plusDays(30L).toLocalDate());
                    odsAmazonAdCampaignNetargetingSp.setCreationBeforeDate(odsAmazonAdCampaignNetargetingSp.getAmazonCreateTime().plusDays(-30L).toLocalDate());
                }
                if (StringUtils.isNotBlank(odsAmazonAdCampaignNetargetingSp.getState())) {
                    odsAmazonAdCampaignNetargetingSp.setState(odsAmazonAdCampaignNetargetingSp.getState().toLowerCase());
                }
                return odsAmazonAdCampaignNetargetingSp;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }


    /**
     * 写入doris
     * @param keywordIdList
     */
    @Override
    public void saveCampaignNekeywordDoris(Integer puid, Integer shopId, List<String> keywordIdList) {
        try {
            if (CollectionUtils.isEmpty(keywordIdList)) {
                return;
            }
            List<AmazonAdCampaignNeKeywords> list = amazonAdCampaignNeKeywordsDao.listByKeywordId(puid, shopId, keywordIdList);
            saveCampaignNekeywordDoris(list, false, false);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param targetIdList
     */
    @Override
    public void saveCampaignNetargetDoris(Integer puid, Integer shopId, List<String> targetIdList) {
        try {
            if (CollectionUtils.isEmpty(targetIdList)) {
                return;
            }
            List<AmazonAdCampaignNetargetingSp> list = campaignNetargetingSpDao.listByTargetId(puid, shopId, targetIdList);
            saveCampaignNeTargetDoris(list, false, false);
        } catch (Exception e) {
            log.error("sp campaign save doris error", e);
        }
    }
}
