package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdSbAdsReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbAdsReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.doris.util.DorisJSONUtil;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Created by lm on 2021/5/14.
 *
 */
@Repository
@Slf4j
public class AmazonAdSbAdsReportDaoImpl extends BaseShardingDaoImpl<AmazonAdSbAdsReport> implements IAmazonAdSbAdsReportDao {

    @Autowired
    private IDorisService dorisService;

    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Override
    public void insertOrUpdateList(Integer puid, List<AmazonAdSbAdsReport> list) {
        StringBuilder sql = new StringBuilder("INSERT INTO `t_amazon_ad_sb_ads_report` (`puid`,`shop_id`,`marketplace_id`,`count_date`,`ad_format`,`campaign_name`,")
                .append("`campaign_id`,`campaign_status`,`campaign_budget`,`campaign_budget_type`,`campaign_rule_based_budget`,`applicable_budget_rule_id`,`applicable_budget_rule_name`,")
                .append("`ad_group_name`,`ad_group_id`,`ad_id`,`query_id`,")
                .append("`impressions`,`clicks`,`currency`,`cost`,`sales14d`,`sales14d_same_sku`,`conversions14d`,`conversions14d_same_sku`,`detail_page_views_clicks14d`,")
                .append("`orders_new_to_brand14d`,`orders_new_to_brand_percentage14d`,`order_rate_new_to_brand14d`,`sales_new_to_brand14d`,`sales_new_to_brand_percentage14d`,")
                .append("`units_ordered_new_to_brand14d`,`units_ordered_new_to_brand_percentage14d`,`units_sold14d`,`dpv14d`,")
                .append("`vctr`,`video5second_view_rate`,`video5second_views`,`video_first_quartile_views`,`video_midpoint_views`,`video_third_quartile_views` ,")
                .append("`video_unmutes`,`viewable_impressions`,`video_complete_views`,`vtr`,`branded_searches14d`,`create_time`,`update_time` ) values");
        List<Object> argsList = Lists.newArrayList();
        for (AmazonAdSbAdsReport report : list) {
            sql.append(" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,now(),now()),");
            argsList.add(puid);
            argsList.add(report.getShopId());
            argsList.add(report.getMarketplaceId());
            argsList.add(report.getCountDate());
            argsList.add(report.getAdFormat());
            argsList.add(report.getCampaignName());
            argsList.add(report.getCampaignId());
            argsList.add(report.getCampaignStatus());
            argsList.add(report.getCampaignBudget());
            argsList.add(report.getCampaignBudgetType());
            argsList.add(report.getCampaignRuleBasedBudget());
            argsList.add(report.getApplicableBudgetRuleId());
            argsList.add(report.getApplicableBudgetRuleName());
            argsList.add(report.getAdGroupName());
            argsList.add(report.getAdGroupId());
            argsList.add(report.getAdId());
            argsList.add(report.getQueryId());
            argsList.add(report.getImpressions());
            argsList.add(report.getClicks());
            argsList.add(report.getCurrency());
            argsList.add(report.getCost());
            argsList.add(report.getSales14d());
            argsList.add(report.getSales14dSameSKU());
            argsList.add(report.getConversions14d());
            argsList.add(report.getConversions14dSameSKU());
            argsList.add(report.getDetailPageViewsClicks14d());
            argsList.add(report.getOrdersNewToBrand14d());
            argsList.add(report.getOrdersNewToBrandPercentage14d());
            argsList.add(report.getOrderRateNewToBrand14d());
            argsList.add(report.getSalesNewToBrand14d());
            argsList.add(report.getSalesNewToBrandPercentage14d());
            argsList.add(report.getUnitsOrderedNewToBrand14d());
            argsList.add(report.getUnitsOrderedNewToBrandPercentage14d());
            argsList.add(report.getUnitsSold14d());
            argsList.add(report.getDpv14d());
            argsList.add(report.getVctr());
            argsList.add(report.getVideo5SecondViewRate());
            argsList.add(report.getVideo5SecondViews());
            argsList.add(report.getVideoFirstQuartileViews());
            argsList.add(report.getVideoMidpointViews());
            argsList.add(report.getVideoThirdQuartileViews());
            argsList.add(report.getVideoUnmutes());
            argsList.add(report.getViewableImpressions());
            argsList.add(report.getVideoCompleteViews());
            argsList.add(report.getVtr());
            argsList.add(report.getBrandedSearches14d());
        }

        sql.deleteCharAt(sql.length()-1);
        sql.append(" on duplicate key update `campaign_name`=values(campaign_name),`campaign_status`=values(campaign_status),`campaign_budget`=values(campaign_budget),`campaign_budget_type`=values(campaign_budget_type),`campaign_rule_based_budget`=values(campaign_rule_based_budget),");
        sql.append("`applicable_budget_rule_id`=values(applicable_budget_rule_id),`applicable_budget_rule_name`=values(applicable_budget_rule_name),");
        sql.append("`ad_group_name`=values(ad_group_name),`ad_group_id`=values(ad_group_id),`ad_id`=values(ad_id),`query_id`=values(query_id),");
        sql.append("`impressions`=values(impressions),`clicks`=values(clicks),`currency`=values(currency),`cost`=values(cost),`sales14d`=values(sales14d),");
        sql.append("`sales14d_same_sku`=values(sales14d_same_sku),`conversions14d`=values(conversions14d),`conversions14d_same_sku`=values(conversions14d_same_sku),`detail_page_views_clicks14d`=values(detail_page_views_clicks14d),`orders_new_to_brand14d`=values(orders_new_to_brand14d),`orders_new_to_brand_percentage14d`=values(orders_new_to_brand_percentage14d),`order_rate_new_to_brand14d`=values(order_rate_new_to_brand14d),");
        sql.append("`sales_new_to_brand14d`=values(sales_new_to_brand14d),`sales_new_to_brand_percentage14d`=values(sales_new_to_brand_percentage14d),`units_ordered_new_to_brand14d`=values(units_ordered_new_to_brand14d),`units_ordered_new_to_brand_percentage14d`=values(units_ordered_new_to_brand_percentage14d),`units_sold14d`=values(units_sold14d),`dpv14d`=values(dpv14d),");
        sql.append("`vctr`=values(vctr),`video5second_view_rate`=values(video5second_view_rate),`video5second_views`=values(video5second_views),`video_first_quartile_views`=values(video_first_quartile_views),");
        sql.append("`video_midpoint_views`=values(video_midpoint_views),`video_third_quartile_views`=values(video_third_quartile_views),`video_unmutes`=values(video_unmutes),");
        sql.append("`viewable_impressions`=values(viewable_impressions),`video_complete_views`=values(video_complete_views),`vtr`=values(vtr),`branded_searches14d`=values(branded_searches14d)");

        getJdbcTemplate(puid).update(sql.toString(),argsList.toArray());
    }

    @Override
    public void insertDorisList(Integer puid, List<AmazonAdSbAdsReport> list) {
        if (!dynamicRefreshConfiguration.verifyGroupAndAdReport(puid)){
            return;
        }
        try {
            String time = LocalDateTimeUtil.formatTime(LocalDateTime.now(), LocalDateTimeUtil.YMDHMS_DATE_FORMAT);
            List<Map<String, Object>> map = list.stream().map(k -> {
                Map<String, Object> objectMap = DorisJSONUtil.dbObj2FieldMap(k);
                LocalDateTimeUtil.setDorisValue(objectMap, k.getCountDate(), time);
                return objectMap;
            }).collect(Collectors.toList());
            dorisService.saveDorisMapByRoutineLoad("doris_ods_t_amazon_ad_sb_ads_report", map);
        } catch (Exception e) {
            log.error("save doris kafka error = {}", e.getMessage());
        }
    }

    @Override
    public List<AmazonAdSbAdsReport> getChartList(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, String keywordId) {
        String sql = "select * from t_amazon_ad_sb_ads_report where puid=? and shop_id=? and marketplace_id=? and ad_id=? and count_date>=? and count_date<=?  order by count_date";
        return getJdbcTemplate(puid).query(sql,new Object[]{puid,shopId,marketplaceId,keywordId,startStr,endStr},getMapper());
    }


    @Override
    public List<AmazonAdSbAdsReport> listSumReports(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, List<String> adIds) {
        String sql = "SELECT puid, shop_id, ad_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM `t_amazon_ad_sb_ads_report` where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .equalTo("marketplace_id", marketplaceId)
                .greaterThanOrEqualTo("count_date", startStr)
                .lessThanOrEqualTo("count_date", endStr)
                .in("ad_id", adIds.toArray())
                .groupBy("ad_id")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public List<AmazonAdSbAdsReport> listReports(int puid, Integer shopId, String startDate, String endDate, String adId) {
        String sql = "SELECT puid, count_date,campaign_id,ad_group_id,ad_id,shop_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM `t_amazon_ad_sb_ads_report` where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("ad_id", adId)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }


    @Override
    public Page getPageList(int puid, SearchVo search, Page page) {
        StringBuilder selectSql = new StringBuilder("select count_date,puid,shop_id,marketplace_id,campaign_name,ad_id,query_id,sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d,")
                .append("sum(`conversions14d_same_sku`) conversions14d_same_sku,sum(`sales14d_same_sku`) sales14d_same_sku,sum(`vctr`) vctr,")
                .append("sum(`orders_new_to_brand14d`) orders_new_to_brand14d,sum(`orders_new_to_brand_percentage14d`) orders_new_to_brand_percentage14d,sum(`order_rate_new_to_brand14d`) order_rate_new_to_brand14d,")
                .append("sum(`sales_new_to_brand14d`) sales_new_to_brand14d ,sum(`sales_new_to_brand_percentage14d`) sales_new_to_brand_percentage14d,sum(`units_ordered_new_to_brand14d`) units_ordered_new_to_brand14d,sum(`units_ordered_new_to_brand_percentage14d`) units_ordered_new_to_brand_percentage14d ,")
                .append("sum(`video5second_view_rate`) video5second_view_rate,sum(`video5second_views`) video5second_views,sum(`video_first_quartile_views`) video_first_quartile_views,")
                .append("sum(`video_midpoint_views`) video_midpoint_views,sum(`video_third_quartile_views`) video_third_quartile_views,")
                .append("sum(`video_unmutes`) video_unmutes,sum(`viewable_impressions`) viewable_impressions,sum(`video_complete_views`) video_complete_views,sum(`vtr`) vtr")
                .append(" FROM t_amazon_ad_sb_ads_report ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( select target_id FROM `t_amazon_ad_sb_ads_report` ");
        List<Object> argsList = Lists.newArrayList();
        StringBuilder whereSql = new StringBuilder(" where puid=? ");
        argsList.add(puid);
        if (search.getAdFormat() != null) {
            whereSql.append("and ad_format=?");
            argsList.add(search.getAdFormat());
        }
        if (search.getShopId() != null) {
            whereSql.append("and shop_id=? ");
            argsList.add(search.getShopId());
        } else if (CollectionUtils.isNotEmpty(search.getShopIds())) {
            whereSql.append("and shop_id in ('").append(StringUtils.join(search.getShopIds(),"','")).append("') ");
        }
        whereSql.append("and count_date>=? and count_date<=? ");
        argsList.add(DateUtil.dateToStrWithFormat(search.getStart(),"yyyyMMdd"));
        argsList.add(DateUtil.dateToStrWithFormat(search.getEnd(),"yyyyMMdd"));
        if (StringUtils.isNotBlank(search.getCampaignId())) {
            whereSql.append(" and campaign_id = ?");
            argsList.add(search.getCampaignId());
        }

        whereSql.append("group by shop_id,query_id ");
        if ("daily".equals(search.getTabType())) {
            whereSql.append(", count_date");
        }
        selectSql.append(whereSql);
        countSql.append(whereSql).append(") t");
        if(StringUtils.isNotBlank(search.getOrderField()) && StringUtils.isNotBlank(search.getOrderValue())){  //排序
            String field = ReportService.getSbReportField(search.getOrderField(), true);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
                selectSql.append(" ,query_id ");   //增加一个排序字段
                if("desc".equals(search.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args, AmazonAdSbAdsReport.class);
    }

    @Override
    public Page detailPageList(int puid, Integer shopId, String marketplaceId, ReportParam param, Page page) {
        StringBuilder selectSql = new StringBuilder(" SELECT * from t_amazon_ad_sb_ads_report ");
        StringBuilder countSql = new StringBuilder("select count(*)").append(" FROM `t_amazon_ad_sb_ads_report` ");
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and target_id=? and count_date>=? and count_date<=? ");
        List<Object> argsList = Lists.newArrayList();
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(marketplaceId);
        argsList.add(param.getTargetId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        selectSql.append(whereSql);
        countSql.append(whereSql);
        if(StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderValue())){
            String field = ReportService.getSbReportField(param.getOrderField(), false);
            if(StringUtils.isNotBlank(field)){
                selectSql.append(" order by ").append(field);
                if("desc".equals(param.getOrderValue())){
                    selectSql.append("desc");
                }
            }
        } else {
            selectSql.append(" order by count_date desc");
        }
        Object[] args = argsList.toArray();
        return this.getPageResult(puid, page.getPageNo(), page.getPageSize(), countSql.toString(), args, selectSql.toString(), args,AmazonAdSbAdsReport.class);
    }

    @Override
    public AmazonAdSbAdsReport getSumReport(int puid, Integer shopId, String marketplaceId, String startStr, String endStr, ReportParam param) {
        StringBuilder sql = new StringBuilder("SELECT sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(`units_ordered14d`) units_ordered14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks ")
                .append(" FROM `t_amazon_ad_sb_ads_report` where`puid`=? and`shop_id`=?")
                .append("  and`marketplace_id`=? and campaign_id=? and ad_group_id=? and `count_date`>=? and count_date<=?  ");
        List<AmazonAdSbAdsReport> list = getJdbcTemplate(puid).query(sql.toString(), new Object[]{puid, shopId, marketplaceId, param.getCampaignId(), param.getGroupId(), startStr, endStr}, getMapper());
        return list != null && list.size() > 0 ? list.get(0) : null;
    }


    /**
     * 由于亚马逊接口在处理历史数据时没有给旧的数据adId，所以只能我们这边做特殊处理，
     * 没有adId的数据用group_id 来查，所以用了一个冗余字段query_id,有ad_id 的数据填充ad_id，没有ad_id填充ad_group_id
     * ad管理数据也是这样的逻辑，这样方便查询，不用管有没有ad_id
     * @param puid
     * @param shopId
     * @param startStr
     * @param endStr
     * @param param
     * @param queryIds
     * @return
     */
    @Override
    public List<AdHomePerformancedto> listSumReportByQueryId(Integer puid, Integer shopId, String startStr, String endStr, AdAdsPageParam param, List<String> queryIds) {
        if (CollectionUtils.isEmpty(queryIds)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder(" select  'sb' as type,ad_id,query_id, count_date,sum(cost) `cost`, sum(sales14d) total_sales, sum(sales14d_same_sku) ad_sales, sum(impressions) `impressions`, sum(clicks) `clicks`, ");
        sql.append(" sum(conversions14d) as sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`, sum(units_sold14d) `units_sold14d`   FROM `t_amazon_ad_sb_ads_report` where puid= ? ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();
        if (param.getShopId() != null) {
            whereSql.append(" and shop_id = ? ");
            argsList.add(param.getShopId());
        }
        whereSql.append(SqlStringUtil.dealInList("query_id", queryIds, argsList));

        whereSql.append("  and count_date >= ? and count_date <= ? group by query_id ");
        argsList.add(startStr);
        argsList.add(endStr);

        sql.append(whereSql);
        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        .type(re.getString("type"))
                        .adId(re.getString("ad_id"))
                        .queryId(re.getString("query_id"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());

    }


    @Override
    public List<AdHomePerformancedto> getSbReportByDate(Integer puid, Integer shopId, String startStr, String endStr, AdAdsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' as type,c.ad_id ad_id, c.query_id query_id, count_date,sum(cost) `cost`, sum(sales14d) total_sales, sum(sales14d_same_sku) ad_sales, sum(impressions) `impressions`, sum(clicks) `clicks`, ");
        sql.append(" sum(conversions14d) as sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_sold14d) `units_sold14d` ");
        sql.append(" from t_amazon_sb_ads c join t_amazon_ad_sb_ads_report r on r.puid=c.puid and r.shop_id=c.shop_id and r.query_id=c.query_id where c.puid= ?  ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", list, argsList));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        if ("name".equalsIgnoreCase(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //状态

            whereSql.append( " and c.name like  ? ");
            argsList.add("%"+ param.getSearchValue().trim()+"%");
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? group by c.query_id  ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adId(re.getString("ad_id"))
                        .queryId(re.getString("query_id"))
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .type(re.getString("type"))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    private StringBuilder subWhereSql(AdAdsPageParam param, List<Object> argsList) {
        StringBuilder subWhereSql = new StringBuilder();
        //高级筛选
        if(param.getUseAdvanced()){
            BigDecimal shopSales = param.getShopSales() != null ? param.getShopSales() : BigDecimal.valueOf(0);

            subWhereSql.append(" having 1=1 ");
            //展示量
            if(param.getImpressionsMin() != null){
                subWhereSql.append(" and impressions >= ?");
                argsList.add(param.getImpressionsMin());
            }
            if(param.getImpressionsMax() != null){
                subWhereSql.append(" and impressions <= ?");
                argsList.add(param.getImpressionsMax());
            }
            //点击量
            if(param.getClicksMin() != null){
                subWhereSql.append(" and clicks >= ?");
                argsList.add(param.getClicksMin());
            }
            if(param.getClicksMax() != null){
                subWhereSql.append(" and clicks <= ?");
                argsList.add(param.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(param.getClickRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(param.getClickRateMin());
            }
            if(param.getClickRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(param.getClickRateMax());
            }
            //花费
            if(param.getCostMin() != null){
                subWhereSql.append(" and cost >= ?");
                argsList.add(param.getCostMin());
            }
            if(param.getCostMax() != null){
                subWhereSql.append(" and cost <= ?");
                argsList.add(param.getCostMax());
            }
            //cpc  平均点击费用
            if(param.getCpcMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(param.getCpcMin());
            }
            if(param.getCpcMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(param.getCpcMax());
            }
            //广告订单量
            if(param.getOrderNumMin() != null){
                subWhereSql.append(" and sale_num >= ?");
                argsList.add(param.getOrderNumMin());
            }
            if(param.getOrderNumMax() != null){
                subWhereSql.append(" and sale_num <= ?");
                argsList.add(param.getOrderNumMax());
            }
            //广告销售额
            if(param.getSalesMin() != null){
                subWhereSql.append(" and total_sales >= ?");
                argsList.add(param.getSalesMin());
            }
            if(param.getSalesMax() != null){
                subWhereSql.append(" and total_sales <= ?");
                argsList.add(param.getSalesMax());
            }
            //订单转化率
            if(param.getSalesConversionRateMin() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(param.getSalesConversionRateMin());
            }
            if(param.getSalesConversionRateMax() != null){
                subWhereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(param.getSalesConversionRateMax());
            }
            //acos
            if(param.getAcosMin() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(param.getAcosMin());
            }
            if(param.getAcosMax() != null){
                subWhereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(param.getAcosMax());
            }
            // roas
            if (param.getRoasMin() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(param.getRoasMin());
            }
            // roas
            if (param.getRoasMax() != null) {
                subWhereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(param.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAcotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (param.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAcotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (param.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(param.getAsotsMin());
                } else {
                    subWhereSql.append(" and 0 >= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (param.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    subWhereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(param.getAsotsMax());
                } else {
                    subWhereSql.append(" and 0 <= ? ");
                    argsList.add(param.getAcotsMin());
                }
            }
        }
        return subWhereSql;
    }


    @Override
    public List<AdHomePerformancedto> getSbReportByQueryIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> queryIdList) {
        if (CollectionUtils.isEmpty(queryIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' as type,count_date,sum(cost) `cost`, sum(sales14d) total_sales, sum(sales14d_same_sku) ad_sales, sum(impressions) `impressions`, sum(clicks) `clicks`, ");
        sql.append(" sum(conversions14d) as sale_num,sum(conversions14d_same_sku) `conversions14d_same_sku`,sum(orders_new_to_brand14d) `orders_new_to_brand14d`,");
        sql.append(" sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(units_sold14d) `units_sold14d` ");
        sql.append(" from t_amazon_ad_sb_ads_report where puid= ? and shop_id= ? ");

        argsList.add(puid);
        argsList.add(shopId);


        sql.append(SqlStringUtil.dealInList("query_id", queryIdList, argsList));

        sql.append("  and count_date >= ? and count_date <= ? group by count_date  ");
        argsList.add(startStr);
        argsList.add(endStr);
        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdHomePerformancedto>() {
            @Override
            public AdHomePerformancedto mapRow(ResultSet re, int i) throws SQLException {
                AdHomePerformancedto dto = AdHomePerformancedto.builder()
                        .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                        .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                        .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                        .countDate(re.getString("count_date"))
                        /**
                         * TODO 广告报告重构
                         * 本广告产品订单量
                         */
                        .adSaleNum(Optional.ofNullable(re.getInt("conversions14d_same_sku")).orElse(0))
                        .salesNum(Optional.ofNullable(re.getInt("units_sold14d")).orElse(0))
                        //本广告产品销售额
                        .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                        .type(re.getString("type"))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }

    @Override
    public List<AmazonAdSbAdsReport> listReportsByQueryId(int puid, Integer shopId, String startDate, String endDate, String queryId) {
        String sql = "SELECT puid, count_date,campaign_id,ad_group_id,ad_id,shop_id, sum(`cost`) cost, sum(`sales14d`) sales14d, sum(`sales14d_same_sku`) sales14d_same_sku," +
                "sum(`impressions`) impressions, sum(`clicks`) clicks, sum(`conversions14d`) conversions14d," +
                "sum(`conversions14d_same_sku`) conversions14d_same_sku " +
                " FROM `t_amazon_ad_sb_ads_report` where ";

        ConditionBuilder conditionBuilder = new ConditionBuilder.Builder()
                .equalTo("puid", puid)
                .equalTo("shop_id", shopId)
                .greaterThanOrEqualTo("count_date", startDate)
                .lessThanOrEqualTo("count_date", endDate)
                .equalTo("query_id", queryId)
                .groupBy("count_date")
                .build();

        sql += conditionBuilder.getSql();

        return getJdbcTemplate(puid).query(sql, getMapper(), conditionBuilder.getValues());
    }

    @Override
    public AdMetricDto getSumAdMetric(Integer puid, Integer shopId, String startStr, String endStr, AdAdsPageParam param) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sb' as type,sum(cost) cost, sum(sales14d)  total_sales, ");
        sql.append(" sum(conversions14d) order_num, 0 sale_num ");
        sql.append(" from t_amazon_sb_ads c join t_amazon_ad_sb_ads_report r on r.puid=c.puid and r.shop_id=c.shop_id and r.query_id=c.query_id where c.puid= ?  ");

        argsList.add(puid);
        StringBuilder whereSql = new StringBuilder();

        if (param.getShopId() != null) {  //店铺
            whereSql.append(" and c.shop_id = ? ");
            argsList.add(param.getShopId());
        }

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("c.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("c.ad_group_id", list, argsList));
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            whereSql.append(SqlStringUtil.dealInList("c.state", statusList, argsList));
        }

        whereSql.append("  and r.count_date >= ? and r.count_date <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);

        whereSql.append(subWhereSql(param, argsList)); // 高级筛选
        sql.append(whereSql);

        List<AdMetricDto> list = getJdbcTemplate(puid).query(sql.toString(), new RowMapper<AdMetricDto>() {
            @Override
            public AdMetricDto mapRow(ResultSet re, int i) throws SQLException {
                AdMetricDto dto = AdMetricDto.builder()
                        .sumCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .sumOrderNum(Optional.ofNullable(re.getBigDecimal("sale_num")).orElse(BigDecimal.ZERO))
                        .sumAdSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                        .sumAdOrderNum(Optional.ofNullable(re.getBigDecimal("order_num")).orElse(BigDecimal.ZERO))
                        .build();
                return dto;
            }
        }, argsList.toArray());

        return list != null && list.size() > 0 ? list.get(0) : null;
    }

    @Override
    public AmazonAdSbAdsReport getSbSumReport(int puid, Integer shopId, String marketplaceId, String reportDate, String adFormat) {
        StringBuilder sql = new StringBuilder("SELECT type, sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d,")
                .append("sum(`impressions`) impressions,sum(`clicks`) clicks")
                .append(" FROM `t_amazon_ad_sb_ads_report` where`puid`=? and`shop_id`=? and`marketplace_id`=? and`count_date`=? and type = 'sb' and`ad_format`=?");
        return getJdbcTemplate(puid).queryForObject(sql.toString(),new Object[]{puid,shopId,marketplaceId,reportDate, adFormat},getMapper());

    }

    @Override
    public List<AmazonAdSbAdsReport> getSbVideoReportSumByDateAndType(int puid, Integer shopId, String type, String startDate, String endDate) {
        StringBuilder sql = new StringBuilder("SELECT query_id, sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(units_sold14d) units_sold14d, sum(conversions14d_same_sku) conversions14d_same_sku ")
                .append(" FROM `t_amazon_ad_sb_ads_report` where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=?  group by query_id ");
        List<Object> args = Lists.newArrayList();
        args.add(puid);
        args.add(shopId);
        args.add(type);
        args.add(startDate);
        args.add(endDate);
        return getJdbcTemplate(puid).query(sql.toString(),args.toArray(),getMapper());
    }


    @Override
    public List<AmazonAdSbAdsReport> getSumCountDateReportByDateAndType(int puid, Integer shopId, String type, String startDate, String endDate) {

        StringBuilder sql = new StringBuilder("SELECT count_date, sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d , sum(units_sold14d) units_sold14d, sum(conversions14d_same_sku) conversions14d_same_sku ")
                .append(" FROM `t_amazon_ad_sb_ads_report` where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=? group by count_date ");
        return getJdbcTemplate(puid).query(sql.toString(), new Object[]{puid, shopId, type, startDate, endDate}, getMapper());

    }

    @Override
    public AmazonAdSbAdsReport getSumReportByDateAndType(int puid, Integer shopId, String type, String startDate, String endDate) {

        StringBuilder sql = new StringBuilder("SELECT sum(clicks) clicks, sum(impressions) impressions,sum(`cost`) cost,sum(`sales14d`) sales14d,sum(`conversions14d`) conversions14d, sum(units_sold14d) units_sold14d, sum(conversions14d_same_sku) conversions14d_same_sku ")
                .append(" FROM `t_amazon_ad_sb_ads_report` where`puid`=? and`shop_id`=? and`ad_format`=? and count_date>=? and count_date<=?  ");
        return getJdbcTemplate(puid).queryForObject(sql.toString(), new Object[]{puid, shopId, type, startDate, endDate}, getMapper());

    }


    @Override
    public List<InvoiceSbAdsDto> getInvoiceProductList(Integer puid, Integer shopId, List<String> campaignId, String start, String end) {
        if (CollectionUtils.isEmpty(campaignId)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT SUM(cost) cost , campaign_id,  query_id, max(update_time) update_time FROM ");
        sql.append(" t_amazon_ad_sb_ads_report ");
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);

        sql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(start);
        argsList.add(end);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        sql.append(" group by puid, shop_id, query_id ");

        sql.append(" limit 100000 ");

        return getJdbcTemplate(puid).query(sql.toString(), new RowMapper<InvoiceSbAdsDto>() {
            @Override
            public InvoiceSbAdsDto mapRow(ResultSet re, int i) throws SQLException {
                InvoiceSbAdsDto dto = InvoiceSbAdsDto.builder()
                        .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                        .campaignId(re.getString("campaign_id"))
                        .queryId(re.getString("query_id"))
                        .updateTime(re.getTimestamp("update_time"))
                        .build();
                return dto;
            }
        }, argsList.toArray());
    }



    @Override
    public LocalDateTime getInvoiceMaxUpdateTime(Integer puid, Integer shopId, List<String> campaignId, String start, String end) {
        if (CollectionUtils.isEmpty(campaignId)) {
            return null;
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("SELECT max(update_time) FROM ");
        sql.append(" t_amazon_ad_sb_ads_report ");
        sql.append(" WHERE puid = ? and shop_id = ? ");
        argsList.add(puid);
        argsList.add(shopId);

        sql.append(" and count_date >= ? and count_date <= ? ");
        argsList.add(start);
        argsList.add(end);
        sql.append(SqlStringUtil.dealInList("campaign_id", campaignId, argsList));
        sql.append(" and  cost > 0 ");

        return getJdbcTemplate(puid).queryForObject(sql.toString(), LocalDateTime.class, argsList.toArray());


    }

}
