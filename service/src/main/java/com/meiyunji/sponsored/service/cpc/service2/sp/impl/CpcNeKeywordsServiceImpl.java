package com.meiyunji.sponsored.service.cpc.service2.sp.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.StateEnum;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeKeywordSuccessResultV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeTargetApiResponseV3;
import com.amazon.advertising.spV3.campaign.entity.CampaignNegativeTargetSuccessResultV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordApiResponseV3;
import com.amazon.advertising.spV3.negativekeyword.entity.NegativeKeywordSuccessResultV3;
import com.amazon.advertising.spV3.response.ErrorItemResultV3;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.springjdbc.ConditionBuilder;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.rpc.sp.campaign.CampaignNeKeywordInfoResp;
import com.meiyunji.sponsored.rpc.sp.campaign.CampaignNeTargetInfoResp;
import com.meiyunji.sponsored.rpc.sp.campaign.CopySpAdsReq;
import com.meiyunji.sponsored.rpc.sp.campaign.NeKeywordInfoResp;
import com.meiyunji.sponsored.rpc.sp.neKeyword.NeKeywordResponse;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsPageRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.common.qo.WordTranslateQo;
import com.meiyunji.sponsored.service.common.service.IWordTranslateService;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportDataDto;
import com.meiyunji.sponsored.service.cpc.dto.NeTargetReportFilterDto;
import com.meiyunji.sponsored.service.cpc.manager.CpcSpTargetingManager;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcNeKeywordsService;
import com.meiyunji.sponsored.service.cpc.service2.sp.ICpcSpCampaignService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.CpcStatusEnum;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdNeKeywordDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdNeKeyword;
import com.meiyunji.sponsored.service.doris.service.IDorisService;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.MarketplaceTimeZoneEnum;
import com.meiyunji.sponsored.service.enums.NeTargetReportBeforeAfterDateEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogResultEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.negative.request.NegativeArchiveRequest;
import com.meiyunji.sponsored.service.util.LocalDateTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by xp on 2021/3/30.
 * 实现类
 */
@Service
@Slf4j
public class CpcNeKeywordsServiceImpl implements ICpcNeKeywordsService {

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private CpcNeKeywordsApiService cpcNeKeywordsApiService;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonSbAdCampaignDao amazonSbAdCampaignDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;
    @Autowired
    private IAmazonAdCampaignNeKeywordsDao amazonAdCampaignNeKeywordsDao;
    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    @Autowired
    private IDorisService dorisService;
    @Autowired
    private CpcSpTargetingManager cpcSpTargetingManager;
    @Autowired
    private IOdsAmazonAdNeKeywordDao odsAmazonAdNeKeywordDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IAmazonSbAdNeKeywordDao amazonSbAdNeKeywordDao;

    @Resource
    private ICpcSpCampaignService cpcSpCampaignService;
    @Autowired
    private IWordTranslateService wordTranslateService;



    @Override
    public Result<Page<NeKeywordsPageVo>> pageList(NeKeywordsPageParam param) {
        Page<AmazonAdKeyword> page = amazonAdKeywordDaoRoutingService.neKeywordsPageList(param.getPuid(), param);
        Page<NeKeywordsPageVo> voPage = new Page<>();
        BeanUtils.copyProperties(page, voPage);

        if (CollectionUtils.isNotEmpty(page.getRows())) {
            List<NeKeywordsPageVo> list = new ArrayList<>(page.getRows().size());
            voPage.setRows(list);
            NeKeywordsPageVo vo;
            for (AmazonAdKeyword amazonAdKeyword : page.getRows()) {
                vo = new NeKeywordsPageVo();
                list.add(vo);
                vo.setId(amazonAdKeyword.getId());
                vo.setShopId(amazonAdKeyword.getShopId());
                vo.setState(amazonAdKeyword.getState());
                vo.setDxmAdGroupId(amazonAdKeyword.getDxmGroupId());
                vo.setMatchType(amazonAdKeyword.getMatchType());
                vo.setKeywordText(amazonAdKeyword.getKeywordText());
                vo.setCreateTime(DateUtil.dateToStrWithTime(amazonAdKeyword.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
            }
        }

        return ResultUtil.returnSucc(voPage);
    }

    @Override
    public Result<List<NeKeywordResponse.Data>> addNeKeywords(AddNeKeywordsVo addNeKeywordsVo, String loginIp) {
        List<NeKeywordsVo> neKeywords = addNeKeywordsVo.getNeKeywords();
        if (CollectionUtils.isEmpty(neKeywords)) {
            return ResultUtil.error("对象不存在");
        }

        // 检查参数数据
        String msg = checkAddNeKeywordsVo(neKeywords);
        if (StringUtils.isNotBlank(msg)) {
            return ResultUtil.error("对象不存在");
        }

        // 检查是否已存在此关键词
        Iterator<NeKeywordsVo> it = neKeywords.iterator();
        NeKeywordsVo next;
        while (it.hasNext()) {
            next = it.next();
            if (amazonAdKeywordDaoRoutingService.exist(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getGroupId(), next.getKeywordText(), next.getMatchType(), Constants.NEGATIVE)) {
                it.remove();
            }
        }

        if (addNeKeywordsVo.getNeKeywords().size() == 0) {
            return ResultUtil.success();
        }

        AmazonAdGroup amazonAdGroup = amazonAdGroupDao.getByAdGroupId(addNeKeywordsVo.getPuid(), addNeKeywordsVo.getShopId(), addNeKeywordsVo.getGroupId());
        if (amazonAdGroup == null) {
            return ResultUtil.error("对象不存在");
        }

        List<AmazonAdKeyword> amazonAdKeywords = convertAddNeKeywordsVoToPo(addNeKeywordsVo.getUid(),amazonAdGroup, neKeywords);

        Result result = cpcNeKeywordsApiService.createNegativeKeywordV3(amazonAdKeywords);
        /**
         * TODO 否定关键词投放增加日志
         * 操作类型：否定投放新增（否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(2);
        List<NeKeywordResponse.Data> errorList = new ArrayList<>();
        for (AmazonAdKeyword keyword : amazonAdKeywords) {
            AdManageOperationLog operationLog = adManageOperationLogService.getkeywordsLog(null, keyword);
            operationLog.setIp(loginIp);
            if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                String errMsg = "";
                if (StringUtils.isNotBlank(keyword.getError())) {
                    errMsg = "targetValue:" + keyword.getKeywordText() + ",desc:" +keyword.getError();
                } else {
                    errMsg = result.getMsg();
                }
                NeKeywordResponse.Data.Builder builder = NeKeywordResponse.Data.newBuilder();
                builder.setKeywordText(keyword.getKeywordText());
                builder.setMatchType(keyword.getMatchType());
                errorList.add(builder.build());
                operationLog.setResultInfo(errMsg);
            }
            operationLogs.add(operationLog);
        }

        adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs);

        if (result.success()) {
            amazonAdKeywords = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
            //走双写逻辑，双写统一了类所以这边又转了一次，后面可以不用这个方法
            amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(addNeKeywordsVo.getPuid(), amazonAdKeywords, Constants.NEGATIVE);
            AmazonAdKeyword keyword = amazonAdKeywords.get(0);
            List<String> keywordIds = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
            saveDoris(keyword.getPuid(), keyword.getShopId(), keywordIds, true);
        }

        if (CollectionUtils.isNotEmpty(errorList) && errorList.size() > 0) {
            result.setData(errorList.stream().distinct().collect(Collectors.toList()));
        } else {
            result.setData(null);
        }

        return result;

    }
    @Autowired
    private IAmazonAdCampaignNetargetingSpDao campaignNetargetingSpDao;

    @Override
    public Result<List<NeKeywordInfoResp>> addNeKeywordsWithAuthed(AmazonAdGroup group, List<AmazonAdKeyword> amazonAdKeywords, String loginIp, ShopAuth shop, int indexOffset) {
        if (CollectionUtils.isEmpty(amazonAdKeywords)) {
            return ResultUtil.success(Collections.emptyList());
        }
        Result<NegativeKeywordApiResponseV3> negativeKeywordV3Result = cpcSpTargetingManager.createNegativeKeywordV3(amazonAdKeywords, shop);
        if (!negativeKeywordV3Result.success()) {
            return ResultUtil.returnErr(negativeKeywordV3Result.getMsg());
        }
        List<NeKeywordInfoResp> neTargetInfoResps = new ArrayList<>();

        for (NegativeKeywordSuccessResultV3 negativeKeywordSuccessResultV3 : negativeKeywordV3Result.getData().getNegativeKeywords().getSuccess()) {
            NeKeywordInfoResp.Builder neKeywordSuccessResp = NeKeywordInfoResp.newBuilder();
            neKeywordSuccessResp.setCode(Result.SUCCESS);
            neKeywordSuccessResp.setIndex(negativeKeywordSuccessResultV3.getIndex() + indexOffset);
            neKeywordSuccessResp.setNeKeywordTargetId(negativeKeywordSuccessResultV3.getNegativeKeywordId());
            neTargetInfoResps.add(neKeywordSuccessResp.build());
            //后续操作根据 targetId是否为null 判断是否创建投放成功
            amazonAdKeywords.get(negativeKeywordSuccessResultV3.getIndex()).setKeywordId(negativeKeywordSuccessResultV3.getNegativeKeywordId());
        }

        for (ErrorItemResultV3 errorItemResultV3 : negativeKeywordV3Result.getData().getNegativeKeywords().getError()) {
            NeKeywordInfoResp.Builder neKeywordErrResp = NeKeywordInfoResp.newBuilder();
            neKeywordErrResp.setCode(Result.ERROR);
            neKeywordErrResp.setIndex(errorItemResultV3.getIndex() + indexOffset);
            neKeywordErrResp.setErrMsg(errorItemResultV3.getErrors().get(0).getErrorMessage());
            if ("duplicateValueError".equalsIgnoreCase(errorItemResultV3.getErrors().get(0).getErrorType())) {
                neKeywordErrResp.setErrMsg("此关键词已存在");
            }
            neTargetInfoResps.add(neKeywordErrResp.build());
            //用于操作日志
            amazonAdKeywords.get(errorItemResultV3.getIndex()).setError(errorItemResultV3.getErrors().get(0).getErrorMessage());
        }

        //排序
        neTargetInfoResps.sort(Comparator.comparingInt(NeKeywordInfoResp::getIndex));

        /**
         * TODO 否定关键词投放增加日志
         * 操作类型：否定投放新增（否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(amazonAdKeywords.size());
        for (AmazonAdKeyword keyword : amazonAdKeywords) {
            AdManageOperationLog operationLog = adManageOperationLogService.getkeywordsLog(null, keyword);
            operationLog.setIp(loginIp);
            if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                String errMsg = "";
                if (StringUtils.isNotBlank(keyword.getError())) {
                    errMsg = "targetValue:" + keyword.getKeywordText() + ",desc:" + keyword.getError();
                } else {
                    errMsg = negativeKeywordV3Result.getMsg();
                }
                NeKeywordResponse.Data.Builder builder = NeKeywordResponse.Data.newBuilder();
                builder.setKeywordText(keyword.getKeywordText());
                builder.setMatchType(keyword.getMatchType());
                operationLog.setResultInfo(errMsg);
            }
            operationLogs.add(operationLog);
        }
        //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
        CompletableFuture.runAsync(() -> adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
            log.error("否定关键词投放, add log to es error:", e);
            return null;
        });

        //将创建成功的否定关键词投放保存进mysql和doris，并维护广告组类型
        if (negativeKeywordV3Result.success()) {
            amazonAdKeywords = amazonAdKeywords.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(amazonAdKeywords)) {
                return ResultUtil.success(neTargetInfoResps);
            }
            try {
                //走双写逻辑，双写统一了类所以这边又转了一次，后面可以不用这个方法
                amazonAdKeywordDaoRoutingService.insertOnDuplicateKeyUpdate(shop.getPuid(), amazonAdKeywords, Constants.NEGATIVE);
                AmazonAdKeyword keyword = amazonAdKeywords.get(0);
                List<String> keywordIds = amazonAdKeywords.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
                saveDoris(keyword.getPuid(), keyword.getShopId(), keywordIds, true);

                // 维护广告组类型
                if (Objects.isNull(group.getAdGroupType())) {
                    group.setAdGroupType(Constants.GROUP_TYPE_KEYWORD);
                    amazonAdGroupDao.updateById(shop.getPuid(), group);
                }
            } catch (Exception e) {
                log.error("保存否定关键词投放失败, AddNeKeywordsVo:{}", JSON.toJSONString(amazonAdKeywords), e);
            }
        }
        return ResultUtil.success(neTargetInfoResps);
    }

    private List<AmazonAdKeyword> convertAmazonAdKeyword(List<AmazonAdCampaignNeKeywords> campaignNeKeywords, String campaignId) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(campaignNeKeywords.size());
        AmazonAdKeyword amazonAdKeyword;
        for (AmazonAdCampaignNeKeywords keywords : campaignNeKeywords) {
            amazonAdKeyword = new AmazonAdKeyword();
            amazonAdKeyword.setPuid(keywords.getPuid());
            amazonAdKeyword.setShopId(keywords.getShopId());
            amazonAdKeyword.setMarketplaceId(keywords.getMarketplaceId());
            amazonAdKeyword.setProfileId(keywords.getProfileId());
            amazonAdKeyword.setCampaignId(keywords.getCampaignId());
            amazonAdKeyword.setKeywordText(keywords.getKeywordText());
            amazonAdKeyword.setMatchType(keywords.getMatchType());
            amazonAdKeyword.setState(keywords.getState());
            amazonAdKeyword.setCreateId(keywords.getCreateId());
            amazonAdKeyword.setKeywordId(keywords.getKeywordId());
            amazonAdKeyword.setError(keywords.getError());
            amazonAdKeywords.add(amazonAdKeyword);
        }
        return amazonAdKeywords;
    }

    @Override
    public Result<List<CampaignNeKeywordInfoResp>> addCampaignNeKeywordsWithAuthed(List<AmazonAdCampaignNeKeywords> campaignNeKeywordsList, String campaignId, ShopAuth targetShop, CopySpAdsReq request, ShopAuth sourceShop, AmazonAdProfile amazonAdProfile, int indexOffset) {
        if (CollectionUtils.isEmpty(campaignNeKeywordsList)) {
            return ResultUtil.success(Collections.emptyList());
        }
        campaignNeKeywordsList = campaignNeKeywordsList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Result<CampaignNegativeKeywordApiResponseV3> campaignNegativeKeywordV3Result = cpcSpTargetingManager.createCampaignNegativeKeywordV3(campaignNeKeywordsList, targetShop, amazonAdProfile);
        if (!campaignNegativeKeywordV3Result.success()) {
            return ResultUtil.returnErr(campaignNegativeKeywordV3Result.getMsg());
        }
        List<CampaignNeKeywordInfoResp> neTargetInfoResps = new ArrayList<>();

        for (CampaignNegativeKeywordSuccessResultV3 negativeKeywordSuccessResultV3 : campaignNegativeKeywordV3Result.getData().getCampaignNegativeKeywords().getSuccess()) {
            Integer index = negativeKeywordSuccessResultV3.getIndex();
            String campaignNegativeKeywordId = negativeKeywordSuccessResultV3.getCampaignNegativeKeywordId();

            CampaignNeKeywordInfoResp.Builder neKeywordSuccessResp = CampaignNeKeywordInfoResp.newBuilder();
            neKeywordSuccessResp.setCode(Result.SUCCESS);
            neKeywordSuccessResp.setIndex(index + indexOffset);
            neKeywordSuccessResp.setNeKeywordTargetId(campaignNegativeKeywordId);
            neTargetInfoResps.add(neKeywordSuccessResp.build());
            //后续操作根据 targetId是否为null 判断是否创建投放成功
            campaignNeKeywordsList.get(index).setKeywordId(campaignNegativeKeywordId);
        }

        for (ErrorItemResultV3 errorItemResultV3 : campaignNegativeKeywordV3Result.getData().getCampaignNegativeKeywords().getError()) {
            Integer index = errorItemResultV3.getIndex();
            String errorMessage = "";
            if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors()) && errorItemResultV3.getErrors().get(0) != null) {
                errorMessage = errorItemResultV3.getErrors().get(0).getErrorMessage();
            }

            CampaignNeKeywordInfoResp.Builder neKeywordErrResp = CampaignNeKeywordInfoResp.newBuilder();
            neKeywordErrResp.setCode(Result.ERROR);
            neKeywordErrResp.setIndex(index + indexOffset);
            neKeywordErrResp.setErrMsg(errorMessage);
            if ("duplicateValueError".equalsIgnoreCase(errorItemResultV3.getErrors().get(0).getErrorType())) {
                neKeywordErrResp.setErrMsg("此关键词已存在");
            }
            neTargetInfoResps.add(neKeywordErrResp.build());
            //用于操作日志
            campaignNeKeywordsList.get(index).setError(errorMessage);
        }

        //排序
        neTargetInfoResps.sort(Comparator.comparingInt(CampaignNeKeywordInfoResp::getIndex));

        /**
         * TODO 否定关键词投放增加日志
         * 操作类型：否定投放新增（否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AmazonAdKeyword> amazonAdKeywordList = convertAmazonAdKeyword(campaignNeKeywordsList, campaignId);
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(campaignNeKeywordsList.size());
        for (AmazonAdKeyword keyword : amazonAdKeywordList) {
            AdManageOperationLog operationLog = adManageOperationLogService.getkeywordsLog(null, keyword);
            operationLog.setIp(request.getLoginIp());
            if (StringUtils.isNotBlank(keyword.getKeywordId())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                String errMsg = "";
                if (StringUtils.isNotBlank(keyword.getError())) {
                    errMsg = "targetValue:" + keyword.getKeywordText() + ",desc:" + keyword.getError();
                } else {
                    errMsg = campaignNegativeKeywordV3Result.getMsg();
                }
                NeKeywordResponse.Data.Builder builder = NeKeywordResponse.Data.newBuilder();
                builder.setKeywordText(keyword.getKeywordText());
                builder.setMatchType(keyword.getMatchType());
                operationLog.setResultInfo(errMsg);
            }
            operationLogs.add(operationLog);
        }
        //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
        CompletableFuture.runAsync(() -> adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
            log.error("活动层级否定关键词投放, add log to es error:", e);
            return null;
        });

        //将创建成功的否定关键词投放保存进mysql和doris，并维护广告组类型
        if (campaignNegativeKeywordV3Result.success()) {
            campaignNeKeywordsList = campaignNeKeywordsList.stream().filter(e -> StringUtils.isNotBlank(e.getKeywordId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(campaignNeKeywordsList)) {
                return ResultUtil.success(neTargetInfoResps);
            }
            try {
                amazonAdCampaignNeKeywordsDao.insertOnDuplicateKeyUpdate(targetShop.getPuid(), campaignNeKeywordsList);
                List<String> collect = campaignNeKeywordsList.stream().map(AmazonAdCampaignNeKeywords::getKeywordId).collect(Collectors.toList());
                cpcSpCampaignService.saveCampaignNekeywordDoris(targetShop.getPuid(), targetShop.getId(), collect);
            } catch (Exception e) {
                log.error("保存否定关键词投放失败, AddNeKeywordsVo:{}, campaignId:{}", JSON.toJSONString(campaignNeKeywordsList), campaignId, e);
            }
        }
        return ResultUtil.success(neTargetInfoResps);
    }


    private List<AmazonAdTargeting> convertAmazonAdTargeting(List<AmazonAdCampaignNetargetingSp> campaignNeKeywords, ShopAuth shop) {
        List<AmazonAdTargeting> amazonAdTargetings = new ArrayList<>(campaignNeKeywords.size());
        AmazonAdTargeting amazonAdKeyword;
        for (AmazonAdCampaignNetargetingSp targeting : campaignNeKeywords) {
            amazonAdKeyword = new AmazonAdTargeting();
            amazonAdKeyword.setPuid(targeting.getPuid());
            amazonAdKeyword.setShopId(targeting.getShopId());
            amazonAdKeyword.setMarketplaceId(targeting.getMarketplaceId());
            amazonAdKeyword.setCampaignId(targeting.getCampaignId());
            amazonAdKeyword.setType(targeting.getType());
            amazonAdKeyword.setCreateId(targeting.getCreateId());
            amazonAdKeyword.setTargetId(targeting.getTargetId());
            amazonAdKeyword.setTitle(targeting.getTitle());
            amazonAdKeyword.setTargetingValue(targeting.getTargetText());
            amazonAdKeyword.setError(targeting.getError());
            amazonAdTargetings.add(amazonAdKeyword);
        }
        return amazonAdTargetings;
    }

    @Override
    public Result<List<CampaignNeTargetInfoResp>> addCampaignNeTargetingsWithAuthed(Integer createId, List<AmazonAdCampaignNetargetingSp> amazonAdTargetings, String campaignId, String loginIp, ShopAuth shop, AmazonAdProfile amazonAdProfile, int indexOffset) {
        if (CollectionUtils.isEmpty(amazonAdTargetings)) {
            return ResultUtil.success(Collections.emptyList());
        }
        amazonAdTargetings = amazonAdTargetings.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Result<CampaignNegativeTargetApiResponseV3> campaignNegativeTargetingV3 = cpcSpTargetingManager.createCampaignNegativeTargetingV3(amazonAdTargetings, shop, amazonAdProfile);
        if (!campaignNegativeTargetingV3.success()) {
            return ResultUtil.error(campaignNegativeTargetingV3.getMsg());
        }
        List<CampaignNeTargetInfoResp> neTargetInfoResps = new ArrayList<>();

        for (CampaignNegativeTargetSuccessResultV3 negativeKeywordSuccessResultV3 : campaignNegativeTargetingV3.getData().getCampaignNegativeTargetingClauses().getSuccess()) {
            Integer index = negativeKeywordSuccessResultV3.getIndex();
            String campaignNegativeTargetingClauseId = negativeKeywordSuccessResultV3.getCampaignNegativeTargetingClauseId();

            CampaignNeTargetInfoResp.Builder neKeywordSuccessResp = CampaignNeTargetInfoResp.newBuilder();
            neKeywordSuccessResp.setCode(Result.SUCCESS);
            neKeywordSuccessResp.setIndex(index + indexOffset);
            neKeywordSuccessResp.setNeProductTargetId(campaignNegativeTargetingClauseId);
            neTargetInfoResps.add(neKeywordSuccessResp.build());
            //后续操作根据 targetId是否为null 判断是否创建投放成功
            AmazonAdCampaignNetargetingSp netargetingSp = amazonAdTargetings.get(index);
            if (netargetingSp != null) {
                netargetingSp.setTargetId(campaignNegativeTargetingClauseId);
                //补充保存进数据库的信息
                netargetingSp.setCreateId(createId);
                netargetingSp.setState(StateEnum.ENABLED.value());
                netargetingSp.setCreateState("SUCCESS");
                netargetingSp.setCreateInAmzup(1);
                netargetingSp.setPuid(shop.getPuid());
                netargetingSp.setShopId(shop.getId());
                netargetingSp.setMarketplaceId(amazonAdProfile.getMarketplaceId());
                netargetingSp.setType("asin");
            }

        }

        for (ErrorItemResultV3 errorItemResultV3 : campaignNegativeTargetingV3.getData().getCampaignNegativeTargetingClauses().getError()) {
            Integer index = errorItemResultV3.getIndex();
            String errorMessage = "";
            if (CollectionUtils.isNotEmpty(errorItemResultV3.getErrors()) && errorItemResultV3.getErrors().get(0) != null) {
                errorMessage = errorItemResultV3.getErrors().get(0).getErrorMessage();
            }

            CampaignNeTargetInfoResp.Builder neKeywordErrResp = CampaignNeTargetInfoResp.newBuilder();
            neKeywordErrResp.setCode(Result.ERROR);
            neKeywordErrResp.setIndex(index + indexOffset);
            neKeywordErrResp.setErrMsg(errorMessage);
            if ("duplicateValueError".equalsIgnoreCase(errorItemResultV3.getErrors().get(0).getErrorType())) {
                neKeywordErrResp.setErrMsg("此关键词已存在");
            }
            neTargetInfoResps.add(neKeywordErrResp.build());
            //用于操作日志
            AmazonAdCampaignNetargetingSp netargetingSp = amazonAdTargetings.get(index);
            if (netargetingSp != null) {
                //补充保存进数据库的信息
                netargetingSp.setCreateId(createId);
                netargetingSp.setState(StateEnum.ENABLED.value());
                netargetingSp.setCreateState("ERROR");
                netargetingSp.setCreateInAmzup(1);
                netargetingSp.setPuid(shop.getPuid());
                netargetingSp.setShopId(shop.getId());
                netargetingSp.setMarketplaceId(amazonAdProfile.getMarketplaceId());
                netargetingSp.setType("asin");
                netargetingSp.setError(errorMessage);
            }
        }

        //排序
        neTargetInfoResps.sort(Comparator.comparingInt(CampaignNeTargetInfoResp::getIndex));

        /**
         * TODO 否定关键词投放增加日志
         * 操作类型：否定投放新增（否定关键词投放）
         * 逻辑：获取亚马逊返回的结果，通过keywordId获取成功失败结果返回日志
         * start
         */
        List<AmazonAdTargeting> amazonAdTargetingsPo = convertAmazonAdTargeting(amazonAdTargetings, shop);
        List<AdManageOperationLog> operationLogs = Lists.newArrayListWithExpectedSize(amazonAdTargetings.size());
        for (AmazonAdTargeting targeting : amazonAdTargetingsPo) {
            AdManageOperationLog operationLog = adManageOperationLogService.getTargetsLog(null, targeting);
            operationLog.setIp(loginIp);
            if (StringUtils.isNotBlank(targeting.getTargetId())) {
                operationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            } else {
                operationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                String errMsg;
                if (StringUtils.isNotBlank(targeting.getError())) {
                    errMsg = "targetValue:" + targeting.getTargetingValue() + ", desc:" + targeting.getError();
                } else {
                    errMsg = campaignNegativeTargetingV3.getMsg();
                }
                operationLog.setResultInfo(errMsg);
            }
            operationLogs.add(operationLog);
        }
        //批量操作(先根据result成功/失败分组,再根据广告活动分组，最后根据广告组分组合并一条日志)
        CompletableFuture.runAsync(() -> adManageOperationLogService.batchLogsMergeByAdGroup(operationLogs), ThreadPoolUtil.getCreateAdForEsExecutor()).exceptionally(e -> {
            log.error("活动层级否定产品投放, add log to es error:", e);
            return null;
        });

        //将创建成功的否定关键词投放保存进mysql和doris，并维护广告组类型
        if (campaignNegativeTargetingV3.success()) {
            //amazonAdTargetings = amazonAdTargetings.stream().filter(e -> StringUtils.isNotBlank(e.getTargetId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(amazonAdTargetings)) {
                return ResultUtil.success(neTargetInfoResps);
            }
            try {
                campaignNetargetingSpDao.batchSave(shop.getPuid(), amazonAdTargetings);
                List<String> collect = amazonAdTargetings.stream().map(AmazonAdCampaignNetargetingSp::getTargetId).collect(Collectors.toList());
                cpcSpCampaignService.saveCampaignNetargetDoris(shop.getPuid(), shop.getId(), collect);
            } catch (Exception e) {
                log.error("保存否定关键词投放失败, AddNeKeywordsVo:{}, campaignId:{}", JSON.toJSONString(amazonAdTargetings), campaignId, e);
            }
        }
        return ResultUtil.success(neTargetInfoResps);
    }

    @Override
    public Result updateState(Integer puid, Integer uid, Long id, String state) {
        AmazonAdKeyword oldAmazonAdKeyword = amazonAdKeywordDaoRoutingService.getByPuidAndId(puid, id, Constants.NEGATIVE);
        if (oldAmazonAdKeyword == null) {
            return ResultUtil.error("没有关键词信息");
        }

        String oldState = oldAmazonAdKeyword.getState();
        if (oldState.equals(state)) {
            return ResultUtil.success();
        }

        oldAmazonAdKeyword.setState(state);
        oldAmazonAdKeyword.setUpdateId(uid);

        Result<List<AmazonAdKeyword>> result = cpcNeKeywordsApiService.update(Lists.newArrayList(oldAmazonAdKeyword));
        if (result.success()) {
            List<AmazonAdKeyword> succList = result.getData();
            List<String> keywordIds = new ArrayList<>();
            for (AmazonAdKeyword amazonAdKeyword : succList) {
                amazonAdKeyword.setUpdateId(uid);
                amazonAdKeywordDaoRoutingService.updateById(puid,amazonAdKeyword, Constants.NEGATIVE);
                keywordIds.add(amazonAdKeyword.getKeywordId());
            }
            if (CollectionUtils.isNotEmpty(keywordIds)) {
                AmazonAdKeyword keyword = succList.get(0);
                saveDoris(keyword.getPuid(), keyword.getShopId(), keywordIds, true);
            }

            // 可能部分成功
            if (StringUtils.isNotBlank(result.getMsg())) {
                return ResultUtil.returnErr(result.getMsg());
            }
        }

        return result;
    }

    @Override
    public Result archive(Integer puid, Integer uid, Long id, String loginIp) {
        AmazonAdKeyword amazonAdKeyword = amazonAdKeywordDaoRoutingService.getByPuidAndId(puid, id, Constants.NEGATIVE);
        if (amazonAdKeyword == null) {
            log.error("否定关键词为空 puid:{} id:{}: ", puid, id);
            return ResultUtil.error("没有关键词信息，请刷新页面重试");
        }
        AmazonAdKeyword adKeyword = new AmazonAdKeyword();
        BeanUtils.copyProperties(amazonAdKeyword, adKeyword);

        adKeyword.setUpdateId(uid);
        adKeyword.setState(CpcStatusEnum.archived.name());
        Result result = cpcNeKeywordsApiService.archive(amazonAdKeyword);
        /**
         * TODO 否定关键词投放增加日志
         * 操作类型：归档否定关键词投放
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithExpectedSize(2);
        AdManageOperationLog adManageOperationLog = adManageOperationLogService.getkeywordsLog(amazonAdKeyword, adKeyword);
        adManageOperationLog.setIp(loginIp);

        if (result.success()) {
            amazonAdKeyword.setUpdateId(uid);
            amazonAdKeyword.setState(CpcStatusEnum.archived.name());
            amazonAdKeywordDaoRoutingService.updateById(puid, amazonAdKeyword, Constants.NEGATIVE);
            adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
            saveDoris(puid, amazonAdKeyword.getShopId(), Lists.newArrayList(amazonAdKeyword.getKeywordId()), true);
        }
        if (result.error()) {
            adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
            adManageOperationLog.setResultInfo(result.getMsg());
        }
        adManageOperationLogs.add(adManageOperationLog);
        adManageOperationLogService.printAdOperationLog(adManageOperationLogs);
        return result;
    }

    @Override
    public Result<BatchResponseVo<BatchNeKeywordVo, AmazonAdKeyword>> batchArchive(Integer puid, Integer shopId, Integer uid, String loginIp, List<Long> idList) {
        List<AmazonAdKeyword> neSpKeywordList = amazonAdKeywordDaoRoutingService.getListByLongIdList(puid, idList, Constants.NEGATIVE);
        if (CollectionUtils.isEmpty(neSpKeywordList)) {
            return ResultUtil.returnErr("没有关键词信息");
        }

        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }

        Map<Long, AmazonAdKeyword> amazonSpAdNeKeywordMap = neSpKeywordList.stream().collect(Collectors.toMap(AmazonAdKeyword::getId, e -> e));
        List<BatchNeKeywordVo> errorList = new ArrayList<>();
        List<AmazonAdKeyword> archiveList = new ArrayList<>();

        for (Long id : idList) {
            BatchNeKeywordVo vo = new BatchNeKeywordVo();
            AmazonAdKeyword neSpKeyword = amazonSpAdNeKeywordMap.get(id);
            vo.setIsFail(false);
            vo.setUid(uid);
            vo.setId(id);
            if (neSpKeyword == null) {
                vo.setIsFail(true);
                vo.setFailReason("关键词不存在");
                errorList.add(vo);
                continue;
            }
            String keywordId = neSpKeyword.getKeywordId();
            if (StringUtils.isBlank(keywordId)) {
                vo.setIsFail(true);
                vo.setFailReason("平台keyword id 为空, 请同步该活动再操作");
                errorList.add(vo);
                continue;
            }
            AmazonAdKeyword amazonSpAdKeyword = new AmazonAdKeyword();
            BeanUtils.copyProperties(neSpKeyword, amazonSpAdKeyword);
            convertVoToBatchUpdatePo(amazonSpAdKeyword, vo);
            archiveList.add(amazonSpAdKeyword);
        }

        if (CollectionUtils.isEmpty(archiveList)) {
            BatchResponseVo<BatchNeKeywordVo, AmazonAdKeyword> data = new BatchResponseVo<>();
            data.setErrorList(errorList);
            data.setSuccessNum(0);
            data.setCountNum(idList.size());
            return ResultUtil.success(data);
        }

        Result<BatchResponseVo<BatchNeKeywordVo, AmazonAdKeyword>> result = cpcNeKeywordsApiService.batchArchive(shop, profile, archiveList);
        /**
         * TODO 否定关键词（组）投放增加日志
         * 操作类型：归档否定关键词投放
         * 逻辑：首先跟旧数据比较，把修改的字段回显到日志对象。
         * start
         */
        List<AdManageOperationLog> adManageOperationLogs = Lists.newArrayListWithCapacity(2);
        for (AmazonAdKeyword newAdKeyword : archiveList) {
            AmazonAdKeyword oldAdKeyword = amazonSpAdNeKeywordMap.get(newAdKeyword.getId());
            AdManageOperationLog adManageOperationLog = adManageOperationLogService.getkeywordsLog(oldAdKeyword, newAdKeyword);
            adManageOperationLog.setIp(loginIp);
            adManageOperationLogs.add(adManageOperationLog);
        }
        if (result.success()) {
            // 记录操作日志
            BatchResponseVo<BatchNeKeywordVo, AmazonAdKeyword> resultData = result.getData();
            List<BatchNeKeywordVo> amazonAdSpNeKeywordError = resultData.getErrorList();
            if (CollectionUtils.isNotEmpty(errorList)) {
                amazonAdSpNeKeywordError.addAll(errorList);
            }
            List<AmazonAdKeyword> amazonAdSpNeKeywordSuccess = resultData.getSuccessList();

            if (CollectionUtils.isNotEmpty(amazonAdSpNeKeywordSuccess)) {
                amazonAdKeywordDaoRoutingService.updateList(puid, amazonAdSpNeKeywordSuccess, Constants.NEGATIVE);
                List<String> keywordIds = amazonAdSpNeKeywordSuccess.stream().map(AmazonAdKeyword::getKeywordId).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(keywordIds)) {
                    saveDoris(puid, shopId, keywordIds, true);
                }

                //更新成功数据打日志
                log.info("用户批量归档成功，updateId:{},puid :{},shopid:{},logoinIp:{},更新成功数据：{}", uid, puid, shopId, loginIp, JSONUtil.objectToJson(amazonAdSpNeKeywordSuccess));
            }
            //收集日志
            Map<String, AmazonAdKeyword> successDataMap = amazonAdSpNeKeywordSuccess.stream().collect(Collectors.toMap(AmazonAdKeyword::getKeywordId, e -> e));
            Map<String, BatchNeKeywordVo> errorDataMap = amazonAdSpNeKeywordError.stream().collect(Collectors.toMap(BatchNeKeywordVo::getKeywordId, e -> e));
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                if (!StringUtil.isEmptyObject(successDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.SUCCESS.getResultValue());
                }
                if (!StringUtil.isEmptyObject(errorDataMap.get(adManageOperationLog.getTargetId()))) {
                    adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                    adManageOperationLog.setResultInfo(errorDataMap.get(adManageOperationLog.getTargetId()).getFailReason());
                }
            }

            resultData.setCountNum((amazonAdSpNeKeywordError == null ? 0 : amazonAdSpNeKeywordError.size())+ (amazonAdSpNeKeywordSuccess == null ? 0 : amazonAdSpNeKeywordSuccess.size()));
            resultData.setFailNum(amazonAdSpNeKeywordError == null ? 0 : amazonAdSpNeKeywordError.size());
            //前端不需要展示成功消息，减少消耗移除成功数据
            resultData.getSuccessList().clear();
        }
        if (result.error()) {
            result.setMsg("更新失败，请稍后重试");
            for (AdManageOperationLog adManageOperationLog : adManageOperationLogs) {
                adManageOperationLog.setResult(OperationLogResultEnum.FAIL.getResultValue());
                adManageOperationLog.setResultInfo(result.getMsg());
            }
        }
        //批量操作日志需根据成功/失败，然后再根据广告活动以及广告组分组
        //目的：根据分组筛选出一条记录
        adManageOperationLogService.batchLogsMergeByAdGroup(adManageOperationLogs);
        return result;
    }

    @Override
    public Page<NeKeywordsPageRpcVo> allNekeywordPageList(NeKeywordsPageParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }
        List<String> campaignIds = null;
        Integer totalSize = null;
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            long t = Instant.now().toEpochMilli();
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sp.getCampaignType(), param.getStatus(), param.getServingStatus());
            log.info("Sp否定关键词查询广告活动花费时间：{}", System.currentTimeMillis()-t);
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            campaignIds = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(param.getPuid(), param.getShopId(), param.getPortfolioId(), CampaignTypeEnum.sb.getCampaignType(), param.getStatus(), param.getServingStatus());
        }
        if (CollectionUtils.isEmpty(campaignIds)) {
            return new Page<>(param.getPageNo(), param.getPageSize());
        }
        param.setCampaignIdList(campaignIds);
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            //去Doris中查询总条数，解决mysql中count(*)查询慢的问题
            long t1 = Instant.now().toEpochMilli();
            totalSize = odsAmazonAdNeKeywordDao.getTotalSizeFromDoris(param.getPuid(), param);
            log.info("Sp否定关键词统计总数花费时间：{}", System.currentTimeMillis()-t1);
            param.setTotalSize(totalSize);
        }
        long t2 = Instant.now().toEpochMilli();
        Page<NeKeywordsPageDto> page = amazonAdKeywordDaoRoutingService.getAllTypeNeKeyword(param.getPuid(), param);
        log.info("Sp否定关键词查询基础数据花费时间：{}", System.currentTimeMillis()-t2);
        Page<NeKeywordsPageRpcVo> voPage = new Page<>();
        voPage.setPageNo(page.getPageNo());
        voPage.setPageSize(page.getPageSize());
        voPage.setTotalSize(page.getTotalSize());
        voPage.setTotalPage(page.getTotalPage());

        List<NeKeywordsPageDto> rows = page.getRows();
        getAllKeyWordVoList(param, shopAuth, voPage, rows);
        return voPage;

    }

    @Override
    public Page<NeKeywordsPageRpcVo> allNekeywordPageListV2(NeKeywordsPageParam param, boolean isExport) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(param.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("未授权");
        }

        //1,查询符合条件的page<keywordId>
        Page<String> neKeywordIdPage = getNeKeywordIdPage(param);

        //2,根据page<keywordId>构建返回数据
        return buildRespVoPage(neKeywordIdPage, shopAuth, param, isExport);
    }

    private Page<NeKeywordsPageRpcVo> buildRespVoPage(Page<String> neKeywordIdPage, ShopAuth shopAuth, NeKeywordsPageParam param, boolean isExport){
        Page<NeKeywordsPageRpcVo> voPage = new Page<>();
        BeanUtils.copyProperties(neKeywordIdPage, voPage);

        //查询
        List<NeKeywordsPageRpcVo> campaignNeKeywordsPageRpcVoList = buildGroupNeKeywordsPageRows(neKeywordIdPage.getRows(), shopAuth, param.getNeTargetReportFilterDto(), param.getType(), isExport);

        //排序
        //sortList(param, campaignNeKeywordsPageRpcVoList);

        voPage.setRows(campaignNeKeywordsPageRpcVoList);
        return voPage;
    }

    private Page<String> getNeKeywordIdPage(NeKeywordsPageParam param) {
        NeTargetReportFilterDto neTargetReportFilterDto = param.getNeTargetReportFilterDto();

        //①不需要筛选报告数据
        if (!Boolean.TRUE.equals(neTargetReportFilterDto.getOnlyShowImpressions()) && !Boolean.TRUE.equals(neTargetReportFilterDto.getDoAdvancedFilter()) && (StringUtil.isEmpty(neTargetReportFilterDto.getOrderField()) || StringUtil.isEmpty(neTargetReportFilterDto.getOrderType()))) {
            return odsAmazonAdNeKeywordDao.pageGroupNeKeywordWithoutReportFilter(param);
        }

        //②从前后30天报告表中查数据
        if (StringUtils.isAnyBlank(neTargetReportFilterDto.getReportStartDate(), neTargetReportFilterDto.getReportEndDate())) {
            //从投放表还是搜索词表
            return odsAmazonAdNeKeywordDao.page30DaysGroupNeKeywordWithReportFilter(param);
        } else { //③从自定义时间报告表中查数据
            return odsAmazonAdNeKeywordDao.pageGroupNeKeywordWithReportFilter(param);
        }

    }

   /* private void sortList(NeKeywordsPageParam param, List<NeKeywordsPageRpcVo> list){
        //list排序
        String orderField = param.getNeTargetReportFilterDto().getOrderField();
        String orderType = param.getNeTargetReportFilterDto().getOrderType();
        if ( StringUtils.isNotBlank(orderField) && StringUtils.isNotBlank(orderType)) {
            Comparator<NeKeywordsPageRpcVo> comparator ;
            switch (orderField) {
                case "acos":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getAcos);
                    break;
                case "adOrderNum":
                    comparator =Comparator.comparing(NeKeywordsPageRpcVo::getAdOrderNum);
                    break;
                case "clicks":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getClicks);
                    break;
                case "impressions":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getImpressions);
                    break;
                case "adCost":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getAdCost);
                    break;
                case "adSale":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getAdSale);
                    break;
                case "ctr":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getCtr);
                    break;
                case "cvr":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getCvr);
                    break;
                case "roas":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getRoas);
                    break;
                case "adCostPerClick":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getAdCostPerClick);
                    break;
                case "cpa":
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getCpa);
                    break;
                default:
                    comparator = Comparator.comparing(NeKeywordsPageRpcVo::getCreateTime);
            }
            // 根据 desc 确定是否需要反转排序
            if (OrderTypeEnum.desc.getType().equals(orderType)) {
                comparator = comparator.reversed();
            }
            list.sort(comparator);
        }
    }*/

    private List<NeKeywordsPageRpcVo> buildGroupNeKeywordsPageRows(List<String> keywordIdList, ShopAuth shopAuth, NeTargetReportFilterDto neTargetReportFilterDto, String type, boolean isExport) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return Collections.emptyList();
        }
        Integer puid = shopAuth.getPuid();
        Integer shopId = shopAuth.getId();
        if (Constants.SP.equalsIgnoreCase(type)) {
            ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
            conditionBuilder.equalTo("puid", puid);
            conditionBuilder.equalTo("shop_id", shopId);
            conditionBuilder.in("keyword_id", keywordIdList.toArray());
            List<AmazonAdNeKeyword> amazonAdNeKeywords = amazonAdNekeywordDao.listByCondition(puid, conditionBuilder.build());
            if (CollectionUtils.isEmpty(amazonAdNeKeywords)) {
                return Collections.emptyList();
            }
            Map<String, AmazonAdNeKeyword> neKeywordsMap = amazonAdNeKeywords.stream()
                .filter(e -> Objects.nonNull(e) && StringUtil.isNotEmpty(e.getKeywordId()))
                .collect(Collectors.toMap(AmazonAdNeKeyword::getKeywordId, Function.identity(), (existing, replacement) -> existing));

            //sp广告活动
            Map<String, AmazonAdCampaignAll> spCampaignMap = new HashMap<>();
            List<String> spCampaignIds = amazonAdNeKeywords.stream()
                .filter(Objects::nonNull)
                .map(AmazonAdNeKeyword::getCampaignId)
                .filter(StringUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(puid,shopId, shopAuth.getMarketplaceId(), spCampaignIds);
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream()
                        .filter(campaign -> Objects.nonNull(campaign) && StringUtil.isNotEmpty(campaign.getCampaignId()))
                        .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (existing, replacement) -> existing));
                }
            }
            //sp广告组
            Map<String, AmazonAdGroup> spGroupMap = new HashMap<>();
            List<String> spGroupIds = amazonAdNeKeywords.stream()
                .filter(Objects::nonNull)
                .map(AmazonAdNeKeyword::getAdGroupId)
                .filter(StringUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(spGroupIds)) {
                List<AmazonAdGroup> adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, shopId, shopAuth.getMarketplaceId(), spGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    spGroupMap = adGroupByIds.stream()
                        .filter(group -> Objects.nonNull(group) && StringUtil.isNotEmpty(group.getAdGroupId()))
                        .collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, Function.identity(), (existing, replacement) -> existing));
                }
            }
            //查询创建者名称
            Map<Integer, User> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(amazonAdNeKeywords)) {
                List<Integer> createIds = amazonAdNeKeywords.stream()
                    .filter(Objects::nonNull)
                    .map(AmazonAdNeKeyword::getCreateId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(createIds)) {
                    List<User> users = userDao.listByIds(puid, createIds);
                    if (CollectionUtils.isNotEmpty(users)) {
                        userMap = users.stream()
                            .filter(user -> Objects.nonNull(user) && Objects.nonNull(user.getId()))
                            .collect(Collectors.toMap(User::getId, Function.identity(), (existing, replacement) -> existing));
                    }
                }
            }

            //查询报告数据
            List<NeTargetReportDataDto> reportByKeyWordIds;
            //从前后30天报告表中查数据
            if (StringUtils.isAnyBlank(neTargetReportFilterDto.getReportStartDate(), neTargetReportFilterDto.getReportEndDate())) {
                reportByKeyWordIds = odsAmazonAdNeKeywordDao.get30ReportByKeyWordIds(puid, shopAuth.getId(), keywordIdList, neTargetReportFilterDto, Constants.SP);
            } else {
                reportByKeyWordIds = odsAmazonAdNeKeywordDao.getReportByKeyWordIds(puid, shopAuth.getId(), keywordIdList, neTargetReportFilterDto, Constants.SP);
            }
            Map<String, NeTargetReportDataDto> reportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(reportByKeyWordIds)) {
                reportMap = reportByKeyWordIds.stream()
                    .filter(neTargetReportDataDto -> Objects.nonNull(neTargetReportDataDto) && StringUtil.isNotEmpty(neTargetReportDataDto.getTargetId()))
                    .collect(Collectors.toMap(NeTargetReportDataDto::getTargetId, Function.identity(), (existing, replacement) -> existing));
            }
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = amazonAdNeKeywords.stream().map(e -> new WordTranslateQo(shopAuth.getMarketplaceId(), e.getKeywordText())).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, isExport);

            return buildSpNeKeywordsPageRows(keywordIdList, neKeywordsMap, shopAuth, spCampaignMap, spGroupMap, userMap, reportMap, wordTranslateMap);

        }
        if (Constants.SB.equalsIgnoreCase(type)) {
            ConditionBuilder.Builder conditionBuilder = new ConditionBuilder.Builder();
            conditionBuilder.equalTo("puid", puid);
            conditionBuilder.equalTo("shop_id", shopId);
            conditionBuilder.in("keyword_id", keywordIdList.toArray());
            List<AmazonSbAdNeKeyword> amazonAdNeKeywords = amazonSbAdNeKeywordDao.listByCondition(puid, conditionBuilder.build());
            if (CollectionUtils.isEmpty(amazonAdNeKeywords)) {
                return Collections.emptyList();
            }
            Map<String, AmazonSbAdNeKeyword> neKeywordsMap = amazonAdNeKeywords.stream()
                .filter(e -> Objects.nonNull(e) && StringUtil.isNotEmpty(e.getKeywordId()))
                .collect(Collectors.toMap(AmazonSbAdNeKeyword::getKeywordId, Function.identity(), (existing, replacement) -> existing));


            //sb广告活动
            Map<String, AmazonAdCampaignAll> sbCampaignMap = new HashMap<>();
            List<String> sbCampaignIds = amazonAdNeKeywords.stream()
                .filter(Objects::nonNull)
                .map(AmazonSbAdNeKeyword::getCampaignId)
                .filter(StringUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                List<AmazonAdCampaignAll> sbCampaignList = amazonSbAdCampaignDao.getByCampaignIds(puid, shopId, sbCampaignIds);
                if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                    sbCampaignMap = sbCampaignList.stream()
                        .filter(sbCampaign -> Objects.nonNull(sbCampaign) && StringUtil.isNotEmpty(sbCampaign.getCampaignId()))
                        .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (existing, replacement) -> existing));
                }
            }
            //sb广告组
            Map<String, AmazonSbAdGroup> sbGroupMap = new HashMap<>();
            List<String> sbGroupIds = amazonAdNeKeywords.stream()
                .filter(Objects::nonNull)
                .map(AmazonSbAdNeKeyword::getAdGroupId)
                .filter(StringUtil::isNotEmpty)
                .distinct()
                .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(sbGroupIds)) {
                List<AmazonSbAdGroup> adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(puid, shopId, sbGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    sbGroupMap = adGroupByIds.stream()
                        .filter(adgroup -> Objects.nonNull(adgroup) && StringUtil.isNotEmpty(adgroup.getAdGroupId()))
                        .collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, Function.identity(), (existing, replacement) -> existing));
                }
            }

            //查询创建者名称
            Map<Integer, User> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(amazonAdNeKeywords)) {
                List<Integer> createIds = amazonAdNeKeywords.stream()
                    .filter(Objects::nonNull)
                    .map(AmazonSbAdNeKeyword::getCreateId)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(createIds)) {
                    List<User> users = userDao.listByIds(puid, createIds);
                    if (CollectionUtils.isNotEmpty(users)) {
                        userMap = users.stream().collect(Collectors.toMap(User::getId, e -> e));
                    }
                }
            }

            //查询报告数据
            List<NeTargetReportDataDto> reportByKeyWordIds;
            //从前后30天报告表中查数据
            if (StringUtils.isAnyBlank(neTargetReportFilterDto.getReportStartDate(), neTargetReportFilterDto.getReportEndDate())) {
                reportByKeyWordIds = odsAmazonAdNeKeywordDao.get30ReportByKeyWordIds(puid, shopAuth.getId(), keywordIdList, neTargetReportFilterDto, Constants.SB);
            } else {
                reportByKeyWordIds = odsAmazonAdNeKeywordDao.getReportByKeyWordIds(puid, shopAuth.getId(), keywordIdList, neTargetReportFilterDto, Constants.SB);
            }
            Map<String, NeTargetReportDataDto> reportMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(reportByKeyWordIds)) {
                reportMap = reportByKeyWordIds.stream()
                    .filter(neTargetReportDataDto -> Objects.nonNull(neTargetReportDataDto) && StringUtil.isNotEmpty(neTargetReportDataDto.getTargetId()))
                    .collect(Collectors.toMap(NeTargetReportDataDto::getTargetId, Function.identity(), (existing, replacement) -> existing));
            }
            //获取翻译词
            List<WordTranslateQo> wordTranslateQos = amazonAdNeKeywords.stream().map(e -> new WordTranslateQo(shopAuth.getMarketplaceId(), e.getKeywordText())).collect(Collectors.toList());
            Map<String, String> wordTranslateMap = wordTranslateService.getWordTranslateMap(puid, wordTranslateQos, isExport);

            return buildSbNeKeywordsPageRows(keywordIdList, neKeywordsMap, shopAuth, sbCampaignMap, sbGroupMap, userMap, reportMap, wordTranslateMap);
        }
        return Collections.emptyList();
    }

    private List<NeKeywordsPageRpcVo> buildSbNeKeywordsPageRows(List<String> keywordIdList, Map<String, AmazonSbAdNeKeyword> neKeywordsMap, ShopAuth shopAuth, Map<String, AmazonAdCampaignAll> sbCampaignMap, Map<String, AmazonSbAdGroup> sbGroupMap, Map<Integer, User> userMap, Map<String, NeTargetReportDataDto> reportMap, Map<String, String> wordTranslateMap) {
        List<NeKeywordsPageRpcVo> list = new ArrayList<>(keywordIdList.size());
        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息
        for (String keywordId : keywordIdList) {
            if (!neKeywordsMap.containsKey(keywordId)) {
                continue;
            }
            AmazonSbAdNeKeyword amazonAdKeyword = neKeywordsMap.get(keywordId);
            NeKeywordsPageRpcVo.Builder builder = NeKeywordsPageRpcVo.newBuilder();
            builder.setTargetId(keywordId);
            if (amazonAdKeyword.getId() != null) {
                builder.setId(Int64Value.of(amazonAdKeyword.getId()));
            }
            if (shopAuth != null) {
                builder.setMarketplaceId(shopAuth.getMarketplaceId());
            }
            if (amazonAdKeyword.getShopId() != null) {
                builder.setShopId(Int32Value.of(amazonAdKeyword.getShopId()));
            }

            if (amazonAdKeyword.getCreateTime() != null) {
                builder.setCreateTime(DateUtil.dateToStrWithFormat(amazonAdKeyword.getCreateTime(), DateUtil.PATTERN_DATE_TIME));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                builder.setState(amazonAdKeyword.getState());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getMatchType())) {
                builder.setMatchType(amazonAdKeyword.getMatchType());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordText())) {
                builder.setKeywordText(amazonAdKeyword.getKeywordText());
                builder.setKeywordTextCn(wordTranslateMap.getOrDefault(wordTranslateService.getWordTranslateKey(amazonAdKeyword.getMarketplaceId(), amazonAdKeyword.getKeywordText()), ""));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                builder.setCampaignId(amazonAdKeyword.getCampaignId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                builder.setGroupId(amazonAdKeyword.getAdGroupId());
            }
                builder.setType("sb");

            //广告活动信息填充
            if (MapUtils.isNotEmpty(sbCampaignMap) && sbCampaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                AmazonAdCampaignAll amazonSbAdCampaign = sbCampaignMap.get(amazonAdKeyword.getCampaignId());
                if (StringUtils.isNotBlank(amazonSbAdCampaign.getName())) {
                    builder.setCampaignName(amazonSbAdCampaign.getName());
                    builder.setCampaignState(amazonSbAdCampaign.getState());
                }

                if (StringUtils.isNotBlank(amazonSbAdCampaign.getPortfolioId())) {
                    builder.setPortfolioId(amazonSbAdCampaign.getPortfolioId());
                    if (portfolioMap.containsKey(amazonSbAdCampaign.getPortfolioId())) {
                        AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSbAdCampaign.getPortfolioId());
                        builder.setPortfolioName(amazonAdPortfolio.getName());
                        builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                    } else {
                        AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(amazonSbAdCampaign.getPuid(), amazonSbAdCampaign.getShopId(), amazonSbAdCampaign.getPortfolioId());
                        if (amazonAdPortfolio != null) {
                            builder.setPortfolioName(amazonAdPortfolio.getName());
                            portfolioMap.put(amazonSbAdCampaign.getPortfolioId(), amazonAdPortfolio);
                            builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            builder.setPortfolioName("广告组合待同步");
                        }
                    }
                } else {
                    builder.setPortfolioName("-");
                }

            }
            //广告组名称填充
            if (MapUtils.isNotEmpty(sbGroupMap) && sbGroupMap.containsKey(amazonAdKeyword.getAdGroupId())) {
                if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getName())) {
                    builder.setAdGroupName(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getName());
                }
                if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdGroupType())) {
                    builder.setAdGroupType(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdGroupType());
                }
                if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdFormat())) {
                    builder.setCampaignTargetingType(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdFormat());
                }
                if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdGroupType())) {
                    builder.setAdGroupType(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdGroupType());
                }
                if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getState())) {
                    builder.setAdGroupState(sbGroupMap.get(amazonAdKeyword.getAdGroupId()).getState());
                }
            }
            //填充创建者
            if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdKeyword.getCreateId())) {
                builder.setCreateName(userMap.get(amazonAdKeyword.getCreateId()).getNickname());
            }
            //填充报告数据
            if (MapUtils.isNotEmpty(reportMap) && reportMap.containsKey(amazonAdKeyword.getKeywordId())) {
                fillCampaignKeywordReportDataIntoPageVo(builder, reportMap.get(amazonAdKeyword.getKeywordId()));
            }
            list.add(builder.build());
        }
        return list;
    }

    private List<NeKeywordsPageRpcVo> buildSpNeKeywordsPageRows(List<String> keywordIdList, Map<String, AmazonAdNeKeyword> neKeywordsMap, ShopAuth shopAuth, Map<String, AmazonAdCampaignAll> spCampaignMap, Map<String, AmazonAdGroup> spGroupMap, Map<Integer, User> userMap, Map<String, NeTargetReportDataDto> reportMap, Map<String, String> wordTranslateMap) {
        List<NeKeywordsPageRpcVo> list = new ArrayList<>(keywordIdList.size());
        Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息
        for (String keywordId : keywordIdList) {
            if (!neKeywordsMap.containsKey(keywordId)) {
                continue;
            }
            AmazonAdNeKeyword amazonAdKeyword = neKeywordsMap.get(keywordId);
            NeKeywordsPageRpcVo.Builder builder = NeKeywordsPageRpcVo.newBuilder();
            builder.setTargetId(amazonAdKeyword.getKeywordId());
            if (amazonAdKeyword.getId() != null) {
                builder.setId(Int64Value.of(amazonAdKeyword.getId()));
            }
            builder.setMarketplaceId(shopAuth.getMarketplaceId());
            if (amazonAdKeyword.getShopId() != null) {
                builder.setShopId(Int32Value.of(amazonAdKeyword.getShopId()));
            }
            if (amazonAdKeyword.getDxmGroupId() != null) {
                builder.setDxmAdGroupId(Int64Value.of(amazonAdKeyword.getDxmGroupId()));
            }
            if (amazonAdKeyword.getCreationDate() != null || amazonAdKeyword.getCreateTime() != null) {
                if (amazonAdKeyword.getCreationDate() != null) {
                    builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(amazonAdKeyword.getCreationDate(), shopAuth.getMarketplaceId())));
                } else {
                    builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTimeUtil.convertDateToLDT(amazonAdKeyword.getCreateTime()), shopAuth.getMarketplaceId())));
                }
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                builder.setState(amazonAdKeyword.getState());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getMatchType())) {
                builder.setMatchType(amazonAdKeyword.getMatchType());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordText())) {
                builder.setKeywordText(amazonAdKeyword.getKeywordText());
                builder.setKeywordTextCn(wordTranslateMap.getOrDefault(wordTranslateService.getWordTranslateKey(amazonAdKeyword.getMarketplaceId(), amazonAdKeyword.getKeywordText()), ""));
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                builder.setCampaignId(amazonAdKeyword.getCampaignId());
            }
            if (StringUtils.isNotBlank(amazonAdKeyword.getAdGroupId())) {
                builder.setGroupId(amazonAdKeyword.getAdGroupId());
            }
            builder.setType("sp");

            //广告活动信息填充
            if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                AmazonAdCampaignAll campaign = spCampaignMap.get(amazonAdKeyword.getCampaignId());
                if (StringUtils.isNotBlank(campaign.getName())) {
                    builder.setCampaignName(campaign.getName());
                    builder.setCampaignState(campaign.getState());
                }
                if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                    builder.setCampaignTargetingType(campaign.getTargetingType());
                }

                if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                    builder.setPortfolioId(campaign.getPortfolioId());
                    if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                        AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                        builder.setPortfolioName(amazonAdPortfolio.getName());
                        builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                    } else {
                        AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                        if (amazonAdPortfolio != null) {
                            builder.setPortfolioName(amazonAdPortfolio.getName());
                            portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                            builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                        } else {
                            builder.setPortfolioName("广告组合待同步");
                        }
                    }
                } else {
                    builder.setPortfolioName("-");
                }
            }
            //广告组名称填充
            if (MapUtils.isNotEmpty(spGroupMap) && spGroupMap.containsKey(amazonAdKeyword.getAdGroupId())) {
                if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getAdGroupId()).getName())) {
                    builder.setAdGroupName(spGroupMap.get(amazonAdKeyword.getAdGroupId()).getName());
                }
                if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdGroupType())) {
                    builder.setAdGroupType(spGroupMap.get(amazonAdKeyword.getAdGroupId()).getAdGroupType());
                }
                if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getAdGroupId()).getState())) {
                    builder.setAdGroupState(spGroupMap.get(amazonAdKeyword.getAdGroupId()).getState());
                }
            }

        //填充创建者
        if (MapUtils.isNotEmpty(userMap) && userMap.containsKey(amazonAdKeyword.getCreateId())) {
            builder.setCreateName(userMap.get(amazonAdKeyword.getCreateId()).getNickname());
        }
        //填充报告数据
        if (MapUtils.isNotEmpty(reportMap) && reportMap.containsKey(amazonAdKeyword.getKeywordId())) {
            fillCampaignKeywordReportDataIntoPageVo(builder, reportMap.get(amazonAdKeyword.getKeywordId()));
        }
            list.add(builder.build());
        }
        return list;
    }

    private void fillCampaignKeywordReportDataIntoPageVo(NeKeywordsPageRpcVo.Builder builder, NeTargetReportDataDto dto) {
        if (dto == null) {
            return;
        }
        if (dto.getTotalSales()!= null && dto.getCost() != null) {
            builder.setAcos((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getCost().multiply(new BigDecimal("100")).divide(dto.getTotalSales(), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getAdOrderNum() != null) {
            builder.setAdOrderNum(dto.getAdOrderNum());
        }
        if (dto.getClicks() != null) {
            builder.setClicks(dto.getClicks());
        }
        if (dto.getImpressions() != null) {
            builder.setImpressions(dto.getImpressions());
        }
        if (dto.getCost() != null) {
            builder.setAdCost(dto.getCost().doubleValue());
        }
        if (dto.getTotalSales()!= null) {
            builder.setAdSale(dto.getTotalSales().doubleValue());
        }
        if (dto.getClicks() != null && dto.getImpressions() != null) {
            builder.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getImpressions())).doubleValue());
        }
        if (dto.getAdOrderNum() != null && dto.getClicks() != null) {
            builder.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(dto.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(dto.getClicks())).doubleValue());
        }
        if (dto.getTotalSales() != null && dto.getCost() != null) {
            builder.setRoas((dto.getTotalSales().compareTo(BigDecimal.ZERO) == 0 || dto.getCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : dto.getTotalSales().divide(dto.getCost(), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getCost()!= null && dto.getClicks() != null) {
            builder.setAdCostPerClick((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getClicks() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getClicks()), 4, RoundingMode.HALF_UP)).doubleValue());
        }
        if (dto.getCost()!= null && dto.getAdOrderNum() != null) {
            builder.setCpa((dto.getCost().compareTo(BigDecimal.ZERO) == 0 || dto.getAdOrderNum() == 0 ? BigDecimal.ZERO : dto.getCost().divide(new BigDecimal(dto.getAdOrderNum()), 4, RoundingMode.HALF_UP)).doubleValue());
        }
    }

    private void getAllKeyWordVoList(NeKeywordsPageParam param, ShopAuth shopAuth, Page<NeKeywordsPageRpcVo> voPage, List<NeKeywordsPageDto> rows) {
        if (CollectionUtils.isNotEmpty(rows)) {
            //查询sp广告活动ID
            List<String> spCampaignIds = rows.stream().filter(item -> Constants.SP.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getCampaignId).collect(Collectors.toList());

            //查询sp广告组
            List<String> spGroupIds = rows.stream().filter(item -> Constants.SP.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getGroupId).collect(Collectors.toList());

            //查询sb广告活动ID
            List<String> sbCampaignIds = rows.stream().filter(item -> Constants.SB.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getCampaignId).collect(Collectors.toList());

            //查询sb广告活动ID
            List<String> sbGroupIds = rows.stream().filter(item -> Constants.SB.equalsIgnoreCase(item.getType())).map(NeKeywordsPageDto::getGroupId).collect(Collectors.toList());

            Map<String, AmazonAdCampaignAll> spCampaignMap = null;
            //sp广告活动
            if (CollectionUtils.isNotEmpty(spCampaignIds)) {
                List<AmazonAdCampaignAll> spAdCampaigns = amazonAdCampaignDao.getByCampaignIds(param.getPuid(), param.getShopId(), shopAuth.getMarketplaceId(), spCampaignIds);
                if (CollectionUtils.isNotEmpty(spAdCampaigns)) {
                    spCampaignMap = spAdCampaigns.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }


            //sb广告活动
            Map<String, AmazonAdCampaignAll> sbCampaignMap = null;
            if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                List<AmazonAdCampaignAll> sbCampaignList = amazonSbAdCampaignDao.getByCampaignIds(shopAuth.getPuid(), shopAuth.getId(), sbCampaignIds);
                if (CollectionUtils.isNotEmpty(sbCampaignIds)) {
                    sbCampaignMap = sbCampaignList.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, e -> e));
                }
            }


            Map<String, AmazonAdGroup> spGroupMap = null;
            //sp广告组
            if (CollectionUtils.isNotEmpty(spGroupIds)) {
                List<AmazonAdGroup> adGroupByIds = amazonAdGroupDao.getAdGroupByIds(shopAuth.getPuid(), param.getShopId(), shopAuth.getMarketplaceId(), spGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    spGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, e -> e));
                }
            }
            Map<String, AmazonSbAdGroup> sbGroupMap = null;
            //sb广告组
            if (CollectionUtils.isNotEmpty(sbGroupIds)) {
                List<AmazonSbAdGroup> adGroupByIds = amazonSbAdGroupDao.getAdGroupByIds(shopAuth.getPuid(), param.getShopId(), sbGroupIds);
                if (CollectionUtils.isNotEmpty(adGroupByIds)) {
                    sbGroupMap = adGroupByIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonSbAdGroup::getAdGroupId, e -> e));
                }
            }

            List<NeKeywordsPageRpcVo> list = new ArrayList<>(rows.size());
            voPage.setRows(list);
            NeKeywordsPageVo vo;
            Map<String, AmazonAdPortfolio> portfolioMap = new HashMap<>(); //广告组合信息
            for (NeKeywordsPageDto amazonAdKeyword : rows) {
                NeKeywordsPageRpcVo.Builder builder = NeKeywordsPageRpcVo.newBuilder();
                if (amazonAdKeyword.getId() != null) {
                    builder.setId(Int64Value.of(amazonAdKeyword.getId()));
                }
                if (shopAuth != null) {
                    builder.setMarketplaceId(shopAuth.getMarketplaceId());
                }
                if (amazonAdKeyword.getShopId() != null) {
                    builder.setShopId(Int32Value.of(amazonAdKeyword.getShopId()));
                }
                if (amazonAdKeyword.getDxmAdGroupId() != null) {
                    builder.setDxmAdGroupId(Int64Value.of(amazonAdKeyword.getDxmAdGroupId()));
                }
                if (Constants.SP.equalsIgnoreCase(param.getType())) {
                    if (amazonAdKeyword.getCreationDate() != null || amazonAdKeyword.getCreateTime() != null) {
                        if (amazonAdKeyword.getCreationDate() != null) {
                            builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(amazonAdKeyword.getCreationDate(), shopAuth.getMarketplaceId())));
                        } else {
                            builder.setCreateTime(DateUtil.dateToStrWithTime(LocalDateTimeUtil.convertChinaToSiteTime(LocalDateTimeUtil.convertDateToLDT(DateUtil.stringToDate(amazonAdKeyword.getCreateTime())), shopAuth.getMarketplaceId())));
                        }
                    }
                } else {
                    if (amazonAdKeyword.getCreateTime() != null) {
                        builder.setCreateTime(DateUtil.dateToStrWithFormat(DateUtil.stringToDate(amazonAdKeyword.getCreateTime()),DateUtil.PATTERN_DATE_TIME));
                    }
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getState())) {
                    builder.setState(amazonAdKeyword.getState());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getMatchType())) {
                    builder.setMatchType(amazonAdKeyword.getMatchType());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getKeywordText())) {
                    builder.setKeywordText(amazonAdKeyword.getKeywordText());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getCampaignId())) {
                    builder.setCampaignId(amazonAdKeyword.getCampaignId());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getGroupId())) {
                    builder.setGroupId(amazonAdKeyword.getGroupId());
                }
                if (StringUtils.isNotBlank(amazonAdKeyword.getType())) {
                    builder.setType(amazonAdKeyword.getType());
                }

                if (Constants.SP.equalsIgnoreCase(amazonAdKeyword.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(spCampaignMap) && spCampaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                        AmazonAdCampaignAll campaign = spCampaignMap.get(amazonAdKeyword.getCampaignId());
                        if (StringUtils.isNotBlank(campaign.getName())) {
                            builder.setCampaignName(campaign.getName());
                        }
                        if (StringUtils.isNotBlank(campaign.getState())) {
                            builder.setCampaignState(campaign.getState());
                        }
                        if (StringUtils.isNotBlank(campaign.getTargetingType())) {
                            builder.setCampaignTargetingType(campaign.getTargetingType());
                        }

                        if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                            builder.setPortfolioId(campaign.getPortfolioId());
                            if (portfolioMap.containsKey(campaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(campaign.getPortfolioId());
                                builder.setPortfolioName(amazonAdPortfolio.getName());
                                builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(campaign.getPuid(), campaign.getShopId(), campaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    builder.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(campaign.getPortfolioId(), amazonAdPortfolio);
                                    builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    builder.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            builder.setPortfolioName("-");
                        }
                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(spGroupMap) && spGroupMap.containsKey(amazonAdKeyword.getGroupId())) {
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getGroupId()).getName())) {
                            builder.setAdGroupName(spGroupMap.get(amazonAdKeyword.getGroupId()).getName());
                        }
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(spGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(spGroupMap.get(amazonAdKeyword.getGroupId()).getState())) {
                            builder.setAdGroupState(spGroupMap.get(amazonAdKeyword.getGroupId()).getState());
                        }
                    }

                }

                if (Constants.SB.equalsIgnoreCase(amazonAdKeyword.getType())) {
                    //广告信息填充
                    if (MapUtils.isNotEmpty(sbCampaignMap) && sbCampaignMap.containsKey(amazonAdKeyword.getCampaignId())) {
                        AmazonAdCampaignAll amazonSbAdCampaign = sbCampaignMap.get(amazonAdKeyword.getCampaignId());
                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getName())) {
                            builder.setCampaignName(amazonSbAdCampaign.getName());
                        }
                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getState())) {
                            builder.setCampaignState(amazonSbAdCampaign.getState());
                        }

                        if (StringUtils.isNotBlank(amazonSbAdCampaign.getPortfolioId())) {
                            builder.setPortfolioId(amazonSbAdCampaign.getPortfolioId());
                            if (portfolioMap.containsKey(amazonSbAdCampaign.getPortfolioId())) {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioMap.get(amazonSbAdCampaign.getPortfolioId());
                                builder.setPortfolioName(amazonAdPortfolio.getName());
                                builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                            } else {
                                AmazonAdPortfolio amazonAdPortfolio = portfolioDao.getByPortfolioId(amazonSbAdCampaign.getPuid(), amazonSbAdCampaign.getShopId(), amazonSbAdCampaign.getPortfolioId());
                                if (amazonAdPortfolio != null) {
                                    builder.setPortfolioName(amazonAdPortfolio.getName());
                                    portfolioMap.put(amazonSbAdCampaign.getPortfolioId(), amazonAdPortfolio);
                                    builder.setIsHidden(amazonAdPortfolio.getIsHidden());
                                } else {
                                    builder.setPortfolioName("广告组合待同步");
                                }
                            }
                        } else {
                            builder.setPortfolioName("-");
                        }

                    }
                    //广告组名称填充
                    if (MapUtils.isNotEmpty(sbGroupMap) && sbGroupMap.containsKey(amazonAdKeyword.getGroupId())) {
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getName())) {
                            builder.setAdGroupName(sbGroupMap.get(amazonAdKeyword.getGroupId()).getName());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdFormat())) {
                            builder.setCampaignTargetingType(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdFormat());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType())) {
                            builder.setAdGroupType(sbGroupMap.get(amazonAdKeyword.getGroupId()).getAdGroupType());
                        }
                        if (StringUtils.isNotBlank(sbGroupMap.get(amazonAdKeyword.getGroupId()).getState())) {
                            builder.setAdGroupState(sbGroupMap.get(amazonAdKeyword.getGroupId()).getState());
                        }
                    }
                }
                list.add(builder.build());
            }
        }
    }

    private void convertVoToBatchUpdatePo(AmazonAdKeyword amazonAdKeyword, BatchNeKeywordVo vo) {
        amazonAdKeyword.setId(vo.getId());
        amazonAdKeyword.setUpdateId(vo.getUid());
        amazonAdKeyword.setState(CpcStatusEnum.archived.name());
    }

    private List<Object> buildUpLogMessage(Map<Long, AmazonAdKeyword> oldList, List<AmazonAdKeyword> newList){
        List<Object> dataList = new ArrayList<>();
        StringBuilder builder = new StringBuilder();
        dataList.add("SP否定关键词投放批量修改状态");
        newList.forEach(e ->{
            AmazonAdKeyword old = oldList.get(e.getId());
            builder.append("KeywordId:").append(e.getKeywordId());
            if (old != null) {
                builder.append(",旧值:").append(old.getState());
            }
            builder.append(",新值:").append(e.getState());
            dataList.add(builder.toString());
            builder.delete(0, builder.length());
        });
        return dataList;
    }

    @Override
    public List<AmazonAdKeyword> convertAddNeKeywordsVoToPo(Integer uid, AmazonAdGroup amazonAdGroup, List<NeKeywordsVo> neKeywords) {
        List<AmazonAdKeyword> amazonAdKeywords = new ArrayList<>(neKeywords.size());
        AmazonAdKeyword amazonAdKeyword;
        for (NeKeywordsVo vo : neKeywords) {
            amazonAdKeyword = new AmazonAdKeyword();
            amazonAdKeyword.setPuid(amazonAdGroup.getPuid());
            amazonAdKeyword.setShopId(amazonAdGroup.getShopId());
            amazonAdKeyword.setMarketplaceId(amazonAdGroup.getMarketplaceId());
            amazonAdKeyword.setProfileId(amazonAdGroup.getProfileId());
            amazonAdKeyword.setAdGroupId(amazonAdGroup.getAdGroupId());
            amazonAdKeyword.setDxmGroupId(amazonAdGroup.getId());
            amazonAdKeyword.setCampaignId(amazonAdGroup.getCampaignId());
            amazonAdKeyword.setKeywordText(StringUtil.replaceSpecialSymbol(vo.getKeywordText().trim()));
            amazonAdKeyword.setMatchType(vo.getMatchType());
            amazonAdKeyword.setType(Constants.NEGATIVE);
            amazonAdKeyword.setState(CpcStatusEnum.enabled.name());
            amazonAdKeyword.setCreateId(uid);
            amazonAdKeywords.add(amazonAdKeyword);
        }
        return amazonAdKeywords;
    }

    private String checkAddNeKeywordsVo(List<NeKeywordsVo> neKeywords) {
        for (NeKeywordsVo vo : neKeywords) {
            if (StringUtils.isBlank(vo.getKeywordText()) || vo.getMatchType() == null) {
                return "cpc.none.keyword";
            }
        }
        return null;
    }


    /**
     * 写入doris
     * @param puid
     * @param shopId
     * @param keywordIdList
     */
    @Override
    public void saveDoris(Integer puid, Integer shopId, List<String> keywordIdList, boolean create) {
        try {
            if (CollectionUtils.isEmpty(keywordIdList)){
                return;
            }
            List<AmazonAdNeKeyword> keywordIds = amazonAdNekeywordDao.getByKeywordIds(puid, shopId, keywordIdList);
            saveDoris(keywordIds, false, false);
        } catch (Exception e) {
            log.error("sp keyword save doris error", e);
        }
    }

    /**
     * 写入doris
     * @param keywordList
     * @param create
     */
    @Override
    public void saveDoris(List<AmazonAdNeKeyword> keywordList, boolean create, boolean update) {
        try {
            Date date = new Date();
            List<OdsAmazonAdNeKeyword> collect = keywordList.stream().map(x -> {
                OdsAmazonAdNeKeyword odsAmazonAdKeyword = new OdsAmazonAdNeKeyword();
                BeanUtils.copyProperties(x, odsAmazonAdKeyword);
                if (create) {
                    odsAmazonAdKeyword.setCreateTime(date);
                }
                if (update) {
                    odsAmazonAdKeyword.setUpdateTime(date);
                }
                MarketplaceTimeZoneEnum marketplaceTimeZoneEnum = MarketplaceTimeZoneEnum.map.get(x.getMarketplaceId());
                if (odsAmazonAdKeyword.getCreationDate() == null && odsAmazonAdKeyword.getCreateTime() != null) {
                    odsAmazonAdKeyword.setCreationDate(LocalDateTimeUtil.convertDateToLDT(odsAmazonAdKeyword.getCreateTime()));
                    odsAmazonAdKeyword.setAmazonCreateTime(LocalDateTimeUtil.convertDateToLDT(odsAmazonAdKeyword.getCreateTime(), TimeZone.getTimeZone(marketplaceTimeZoneEnum.getZone_id()).toZoneId()));
                }
                if (odsAmazonAdKeyword.getCreationDate() != null) {
                    odsAmazonAdKeyword.setAmazonCreateTime(LocalDateTimeUtil.convertChinaToSiteTime(odsAmazonAdKeyword.getCreationDate(), odsAmazonAdKeyword.getMarketplaceId()));
                    odsAmazonAdKeyword.setCreationAfterDate(odsAmazonAdKeyword.getAmazonCreateTime().plusDays(30L).toLocalDate());
                    odsAmazonAdKeyword.setCreationBeforeDate(odsAmazonAdKeyword.getAmazonCreateTime().plusDays(-30L).toLocalDate());
                }
                if (StringUtils.isNotBlank(odsAmazonAdKeyword.getState())) {
                    odsAmazonAdKeyword.setState(odsAmazonAdKeyword.getState().toLowerCase());
                }
                return odsAmazonAdKeyword;
            }).collect(Collectors.toList());
            dorisService.saveDoris(collect);
        } catch (Exception e) {
            log.error("sp keyword save doris error", e);
        }
    }

    @Override
    public List<AmazonAdNeKeyword> listByKeywordText(Integer puid, Integer shopId, List<NegativeArchiveRequest.NegativeInfo> infoList) {
        return amazonAdNekeywordDao.listByKeywordText(puid, shopId, infoList);
    }
}