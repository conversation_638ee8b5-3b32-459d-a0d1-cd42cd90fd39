package com.meiyunji.sponsored.service.multiPlatform.tiktok.model.request;

import com.meiyunji.sponsored.service.annotation.AllowedValues;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class GmvMaxCampaignStatusUpdateParam {

    @NotNull(message = "店铺ID不能为空")
    private Integer shopId;
    @NotNull(message = "广告主ID不能为空")
    private String advertiserId;
    @NotNull(message = "活动ID不能为空")
    private String campaignId;
    @AllowedValues(message = "操作状态不正确", allowedValues = {"ENABLE", "DISABLE", "DELETE"})
    private String status;

}
