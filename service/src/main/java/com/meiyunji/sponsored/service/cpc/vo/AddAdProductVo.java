package com.meiyunji.sponsored.service.cpc.vo;

import com.meiyunji.sponsored.rpc.adCommon.ProductAddResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by xp on 2021/4/8.
 * 添加广告产品vo
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class AddAdProductVo {

    private Integer shopId;
    private Integer puid;
    private Integer uid;
    private String groupId;
    private List<ProductVo> products;
    private String loginIp;
    private List<ProductAddResponse.AddFailInfo> failInfoList;
    private List<DisplayErrorMsg> displayErrorMsgList;

    @Data
    public static class DisplayErrorMsg {
        private String asin;
        private String msku;
        private String adGroupName;
        private String error;
    }
}
