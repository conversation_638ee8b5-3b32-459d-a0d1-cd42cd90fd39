package com.meiyunji.sponsored.service.wordFrequency.dao;

import com.meiyunji.sponsored.common.springjdbc.IBaseShardingSphereDao;
import com.meiyunji.sponsored.service.wordFrequency.bo.WordRootTranslatorBo;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSb;
import com.meiyunji.sponsored.service.wordFrequency.po.AmazonAdWordRootKeywordSp;

import java.util.List;


/**
 * @author: liweibin
 * @email: <EMAIL>
 * @date: 2024-05-20  22:44
 */
public interface IAmazonAdWordRootKeywordSbDao extends IBaseShardingSphereDao<AmazonAdWordRootKeywordSb> {

    /**
     * 词根翻译查询
     */
    List<WordRootTranslatorBo> listTranslatorBoByShopId(Integer puid, Integer shopId, String start, String end, int limit);

    /**
     * 批量更新词根翻译
     */
    void batchUpdateWordRootCn(Integer puid, List<WordRootTranslatorBo> updateList);

    void batchInsertOrUpdateSbKeyword(Integer puid, List<AmazonAdWordRootKeywordSb> amazonAdWordRootKeywordSbList);

    /**
     * 根据关键词列表获取对应词根的关键词id
     */
    List<String> listKeywordIdByWordRootAndKeywordIdList(Integer puid, Integer shopId, String wordRoot, List<String> keywordIds);

    int deleteByPuidAndShopId(Integer puid, Integer shopId, Integer limit);
}
