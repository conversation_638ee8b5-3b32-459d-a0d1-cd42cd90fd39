package com.meiyunji.sponsored.service.cpc.dao;


import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dto.*;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.newDashboard.bo.EffectDataBo;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public interface IAmazonMarketingStreamDataDao extends IDorisBaseDao<AmazonMarketingStreamData> {


    /**
     * 查询小时级数据
     */
    List<AmazonMarketingStreamData> listByHourly(FeedHourlySelectDTO feedHourlySelectDTO);

    /**
     * 查询小时级数据 多店铺
     */
    List<AmazonMarketingStreamData> listByHourlyMultiple(FeedHourlySelectDTO feedHourlySelectDTO);

    /**
     * 查询小时级数据
     */
    List<AmazonMarketingStreamData> listByPlacementHourly(FeedHourlySelectDTO feedHourlySelectDTO);

    /**
     * 查询小时级数据
     */
    List<AmazonMarketingStreamData> listByKeywordHourly(FeedHourlySelectDTO feedHourlySelectDTO);

    List<AmazonMarketingStreamData> listWeekByHourly(FeedHourlySelectDTO feedHourlySelectDTO);

    /**
     * 查询小时级数据 多店铺
     */
    List<AmazonMarketingStreamData> listWeekByHourlyMultiple(FeedHourlySelectDTO feedHourlySelectDTO);

    /**
     * 查询小时级数据
     */
    List<AmazonMarketingStreamData> listBitmapByHourly(FeedHourlySelectDTO feedHourlySelectDTO);

    List<ProductPerspectiveCountDto> getDiagnoseCount4Campaign(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<ProductPerspectiveCountDto> getDiagnoseCount4Keyword(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<ProductPerspectiveCountDto> getDiagnoseCount4Placement(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<ProductPerspectiveCountDto> getAllTypeDiagnoseCount4Campaign(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<ProductPerspectiveCountDto> getAllTypeDiagnoseCount4Keyword(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<AmazonMarketingStreamData> aggregateSellersByHour(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> aggregateSellersByHourAndType(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsProductPerspectiveCampaignHourlyReport(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsTargetHourlyReport(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsAllTypeTargetHourlyReport(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> aggregatePlacementSellersByHour(CampaignHourlyReportSelectDto queryDto, List<CampaignPlacementDto> campaignIdPlacementList);

    List<AmazonMarketingStreamData> statisticsByHour(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> groupStatisticsByHour(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> campaignStatisticsByHour(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisCampaignView(CampaignHourlyReportSelectDto queryDto, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    Page<AmazonMarketingStreamData> productPerspectiveAnalysisTargetView(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisPlacementViewAggregate(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    Page<AmazonMarketingStreamData> productPerspectiveAnalysisPlacementView(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisAllCampaignView(CampaignViewParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    Page<AmazonMarketingStreamData> productPerspectiveAnalysisAllTargetViewPage(ViewBaseParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisAllTargetViewList(ViewBaseParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    AmazonMarketingStreamData sumAdStreamByKeywordIdsAndAdIds(AdKeywordStreamDataParam param);

    List<AmazonMarketingStreamData> queryAdStreamByKeywordIdsAndAdIds(AdKeywordStreamDataParam param);
    List<AmazonMarketingStreamData> queryAdStreamByKeywordIdsAndAdIdsGroupByHour(AdKeywordStreamDataParam param);
    List<AmazonMarketingStreamData> productPerspectiveAnalysisTargetViewSumAdMetric(ProductPerspectiveDiagnoseSelectDto baseParam, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisAllTargetViewSumAdMetric(ViewBaseParam param, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto dto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisPlacementViewSumAdMetric(ProductPerspectiveDiagnoseSelectDto request, BigDecimal shopSales, ProductPerspectiveAnalysisFilterDto advance);

    List<AmazonMarketingStreamData> statisticsProductHourlyReportOfTarget(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsTargetHourlyReportOfAd(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsTargetHourlyReportOfPlacement(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsTargetHourlyReportOfPlacementMultiple(FeedHourlySelectDTO feedHourlySelectDTO);

    List<AmazonMarketingStreamData> statisticsProductHourlyReportOfPlacement(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsProductHourlyReport(FeedHourlySelectDTO feedHourlySelectDTO);

    List<AmazonMarketingStreamData> getHourReportByCampaignId(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> productPerspectiveAnalysisGetHourReportByCampaignId(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> getHourReportByKeywordId(CampaignHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsByWeek(CampaignHourlyReportSelectDto x);

    /**
     * 统计周叠加数据，group by time, weekday
     * @param queryDto
     * @return
     */
    List<AmazonMarketingStreamData> statisticsByWeek(CommonHourlyReportSelectDto queryDto);

    List<AmazonMarketingStreamData> statisticsProductWeeklySuperpositionReport(CampaignHourlyReportSelectDto queryDto);

    /**
     * 广告看板获取广告效果、转化页面
     * @return
     */
    List<EffectDataBo> getEffectData(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency,
                                     String startDate, String endDate, String modelType, List<ShopAuth> shopAuths, List<String> siteToday, Boolean isSiteToday,
                                     List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIds);

    List<EffectDataBo> getEffectData(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency,
                                     String startDate, String endDate, String modelType, List<ShopAuth> shopAuths, List<String> siteToday, Boolean isSiteToday,
                                     List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIds, Boolean isYesterday);

    /**
     * 广告看板获取广告效果、转化汇总页面
     * @return
     */
    EffectDataBo getEffectAggregateData(Integer puid, List<String> sellerList, List<String> marketplaceIdList, String currency,
                                        String startDate, String endDate, String modelType, List<String> siteToday, Boolean isSiteToday,
                                        List<String> portfolioIds, List<String> campaignIds, List<Integer> shopIds);

    /**
     * 获取Top五的 广告活动
     */
    List<AmazonMarketingStreamData> getHourlyDataTopFiveCampaign(List<CampaignLastDayHourlyReportDto> list, String dateTime, String orderBy, Map<String, Double> rateMap);

    /**
     * 增加活动Id的小时级查询
     */
    List<AmazonMarketingStreamData> getTopFiveCampaignIndicatorDoris(List<CampaignLastDayHourlyReportDto> list, String dateTime);

    /**
     * 统计活动
     */
    List<AmazonMarketingStreamData> statisticsByDateTimeRangeAndCampaignIdDoris(List<CampaignLastDayHourlyReportDto> list, String dateTime);

    /**
     * 统计活动
     */
    List<AmazonMarketingStreamData> statisticsByDateTimeRangeDoris(List<CampaignLastDayHourlyReportDto> list, String dateTime);
    /**
     * 查询小时级数据 - 多站点、多店铺
     */
    List<AmazonMarketingStreamData> listMultiShopByHourly(FeedHourlySelectMultiShopDTO feedHourlySelectMultiShopDTO);

    /**
     * 查询小时级数据 - 多站点、多店铺（周叠加）
     */
    List<AmazonMarketingStreamData> listWeekByHourlyMultiShop(FeedHourlySelectDTOMultiShop queryBuilder);
}
