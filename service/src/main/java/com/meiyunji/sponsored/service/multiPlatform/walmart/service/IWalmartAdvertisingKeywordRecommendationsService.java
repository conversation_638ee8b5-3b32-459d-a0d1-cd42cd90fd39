package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.exception.ServiceException;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingKeywordRecommendations;
import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;
import com.meiyunji.sponsored.service.multiPlatform.walmart.dto.WalmartAdvertisingBatchUpdateBidDTO;


import java.util.List;
import java.util.Map;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingKeywordRecommendationsService {

    /**
     * 新增
     */
    Long add(WalmartAdvertisingKeywordRecommendations keywordRecommendations);

    /**
     * 删除
     */
    int delete(Integer puid, Long id);

    /**
     * 更新
     */
    int update(WalmartAdvertisingKeywordRecommendations keywordRecommendations);

    Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams);

    void syncKeywordRecommendations(Integer puid, Long shopId);

    void executeKeywordRecommendations(List<Long> ids);

    int insertOrUpdate(Integer puid, List<WalmartAdvertisingKeywordRecommendations> keywordRecommendations);

    //    private static final Logger logger = LoggerFactory.getLogger(WalmartAdvertisingKeywordRecommendationsServiceImpl.class);
    //
    //    @Autowired
    //    private IWalmartAdvertisingKeywordRecommendationsDao walmartAdvertisingKeywordRecommendationsDao;
    //    @Autowired
    //    private IWalmartAdvertiserAttributesService walmartAdvertiserAttributesService;
    //    @Autowired
    //    private IWalmartAdvertisingSnapshotService advertisingSnapshotService;
    //    @Autowired
    //    private IWalmartAdvertisingGroupDao walmartAdvertisingGroupDao;
    //    @Autowired
    //    private IWalmartAdvertisingKeywordService walmartAdvertisingKeywordService;
    //
    //    @Override
    //    public Long add(WalmartAdvertisingKeywordRecommendations keywordRecommendations) {
    //        return walmartAdvertisingKeywordRecommendationsDao.add(keywordRecommendations);
    //    }
    //
    //    @Override
    //    public int delete(Integer puid, Long id) {
    //        return walmartAdvertisingKeywordRecommendationsDao.delete(puid, id);
    //    }
    //
    //    @Override
    //    public int update(WalmartAdvertisingKeywordRecommendations keywordRecommendations) {
    //        return walmartAdvertisingKeywordRecommendationsDao.update(keywordRecommendations);
    //    }
    //
    //    @Override
    //    public Page getPageList(int puid, int pageNo, int pageSize, Map<String, Object> queryParams) {
    //        WalmartAdvertisingKeywordRecommendations lastKeywordRecommendation = walmartAdvertisingKeywordRecommendationsDao.getLastKeywordRecommendation(puid);
    //        if(lastKeywordRecommendation == null || lastKeywordRecommendation.getReportDate() == null){
    //            return new Page();
    //        }
    //        queryParams.put("reportDate", DateUtil.dateToStrNoTime(lastKeywordRecommendation.getReportDate()));
    //        queryParams.put("lastUpdateDate", DateUtil.dateToStrWithTime(lastKeywordRecommendation.getUpdateTime()));
    //        return walmartAdvertisingKeywordRecommendationsDao.getPageList(puid, pageNo, pageSize, queryParams);
    //    }
    //
    void syncKeywordRecommendations(Integer puid, Integer shopId);

    void snapshotExecute(WalmartAdvertisingSnapshot snapshot, String detailsStr, String jobStatus);

    //关键词推荐添加到广告组
    void keywordRecommendationsAddGroup(int puid, WalmartAdvertisingBatchUpdateBidDTO dto) throws ServiceException;


}
