package com.meiyunji.sponsored.service.export.handler;

import com.amazon.advertising.mode.PredicateEnum;
import com.google.common.collect.Lists;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.ProcessMsg;
import com.meiyunji.sponsored.common.support.StringRedisService;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.rpc.export.AdPlacementDataResponse;
import com.meiyunji.sponsored.rpc.export.KeywordDataResponse;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAdManagePageExportTaskDao;
import com.meiyunji.sponsored.service.cpc.dao.IAdPlacementAmazonBusinessReportDao;
import com.meiyunji.sponsored.service.cpc.po.AdManagePageExportTask;
import com.meiyunji.sponsored.service.cpc.service2.ICpcCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageParam;
import com.meiyunji.sponsored.service.cpc.vo.PlacementPageVo;
import com.meiyunji.sponsored.service.enums.CampaignTypeEnum;
import com.meiyunji.sponsored.service.enums.CpcPlaceNameEnum;
import com.meiyunji.sponsored.service.enums.StrategyEnum;
import com.meiyunji.sponsored.service.enums.TargetingEnum;
import com.meiyunji.sponsored.service.excel.excelTools.WriteHandlerBuild;
import com.meiyunji.sponsored.service.excel.excelTools.service.IExcelService;
import com.meiyunji.sponsored.service.export.AdManagePageExportTaskHandler;
import com.meiyunji.sponsored.service.export.constants.AdManagePageExportTaskConstant;
import com.meiyunji.sponsored.service.vo.CpcSpaceVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.meiyunji.sponsored.service.export.util.ExportStringUtil.*;

/**
 * <AUTHOR>
 * @date 2024-07-24 15:43:37
 */

@Service(AdManagePageExportTaskConstant.PLACEMENT)
@Slf4j
public class PlacementPageExportTaskHandler implements AdManagePageExportTaskHandler {


    @Autowired
    private IAdManagePageExportTaskDao adManagePageExportTaskDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private CpcShopDataService cpcShopDataService;
    @Autowired
    private ICpcCampaignService cpcCampaignService;
    @Autowired
    private StringRedisService stringRedisService;
    @Autowired
    private IExcelService excelService;
    @Resource
    private IAdPlacementAmazonBusinessReportDao adPlacementAmazonBusinessReportDao;

    private static String currency;

    /**
     * 导出
     * @param task
     */
    @Override
    public void export(AdManagePageExportTask task) {
        PlacementPageParam param = JSONUtil.jsonToObject(task.getParam(), PlacementPageParam.class);
        if (param == null) {
            log.error(String.format("Placement export error, param is null, task id : %d", task.getId()));
            adManagePageExportTaskDao.updateErrorStatus(task.getId());
            return;
        }
        ShopAuth auth = shopAuthDao.getScAndVcById(param.getShopId());
        // 取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate(), param.getEndDate());
        if (shopSalesByDate == null) {
            shopSalesByDate = BigDecimal.ZERO;
        }
        param.setShopSales(shopSalesByDate);
        param.setPageNo(1);
        param.setPageSize(Constants.EXPORT_MAX_SIZE);
        // 是否是企业购的导出
        boolean isAmazonBusiness = PredicateEnum.SITEAMAZONBUSINESS.value().equals(param.getCampaignSite());
        List<PlacementPageVo> voList;
        if (isAmazonBusiness) {
            // 以二级列表数据的样式导出
            param.setQuerySublist(true);
            voList = cpcCampaignService.getAllPlacementVoList(param.getPuid(), param, auth);
            if (StringUtils.isNotBlank(param.getOrderField()) && StringUtils.isNotBlank(param.getOrderType())) {
                voList = PageUtil.sort(voList, param.getOrderField(), param.getOrderType());
            }
        } else {
            Page<PlacementPageVo> pageList = cpcCampaignService.getAllDorisPlacementVoList(param.getPuid(), param, auth);
            voList = pageList.getRows();
        }
        if (CollectionUtils.isEmpty(voList)) {
            //修改任务状态
            adManagePageExportTaskDao.updateSuccessStatus(task.getId(), null, 100);
            //修改状态，前端收到后转圈效果停止
            stringRedisService.set(param.getUuid(), new ProcessMsg(-1, 0, "导出数据为空"));
            return;
        }
        voList = voList.stream().filter(Objects::nonNull).collect(Collectors.toList());
//        List<AdPlacementDataResponse.PlacementPageVo> list = voList.stream().filter(Objects::nonNull).map(PlacementPageExportTaskHandler::buildGrpcVo).collect(Collectors.toList());
        currency = AmznEndpoint.getByMarketplaceId(auth.getMarketplaceId()).getCurrencyCode().value();
        String fileName = auth.getName() + "_广告位" + "_" + param.getStartDate() + "_" + param.getEndDate();
        int count = 0;
        List<String> urlList = new ArrayList<>();
        //页面渲染
        WriteHandlerBuild build = new WriteHandlerBuild().rate();
        //集合分片
        List<List<PlacementPageVo>> partition = Lists.partition(voList, Constants.EXPORT_MAX_SIZE);
        List<String> voExcludeFields = new ArrayList<>();
        if (!isAmazonBusiness) {
            voExcludeFields.add("amazonBusinessPercentage");
        }
        for (List<PlacementPageVo> sublist : partition) {
            List<CpcSpaceVo> AdPlacementVoList = new LinkedList<>();
            for (PlacementPageVo placeVo : sublist) {
                AdPlacementVoList.add(this.buildExportVo(placeVo));

            }
            if (!AdPlacementVoList.isEmpty()) {
                //设置表头
                Class clazz = CpcSpaceVo.class;
                build = build.currencyNew(clazz);
                urlList.add(excelService.easyExcelHandlerExport(param.getPuid(), AdPlacementVoList, fileName + "(" + count++ + ")", clazz, build, voExcludeFields));
            }
        }
        //修改任务状态
        adManagePageExportTaskDao.updateSuccessStatus(task.getId(), JSONUtil.objectToJson(urlList), 100);
        stringRedisService.set(param.getUuid(), new ProcessMsg(1, urlList.size(), "导出成功", urlList));
    }

    private CpcSpaceVo buildExportVo(PlacementPageVo item) {
        CpcSpaceVo csVo = new CpcSpaceVo();
        csVo.setImpressions(Optional.ofNullable(item.getImpressions()).orElse(0));
        csVo.setClicks(Optional.ofNullable(item.getClicks()).orElse(0));
        csVo.setAdOrderNum(Optional.ofNullable(item.getAdOrderNum()).orElse(0));
        csVo.setStrategy(StrategyEnum.getStrategyValue(item.getStrategy()));
        //广告位名称
        csVo.setName(CpcPlaceNameEnum.getPredicateValue(StringUtils.defaultString(item.getPredicate())));
        if ("产品页面".equals(csVo.getName()) || "搜索结果顶部(首页)".equals(csVo.getName()) || "搜索结果的其余位置".equals(csVo.getName())) {
            //默认调整
            csVo.setPercentage(getPlacementProductPage(StringUtils.defaultString(item.getPercentage())));
        }
        csVo.setAmazonBusinessPercentage(getPlacementProductPage(StringUtils.defaultString(item.getAmazonBusinessPercentage())));
        //点击率
        csVo.setCtr(modifyFormat(StringUtils.defaultIfBlank(item.getCtr(), "0")));
        //订单转化率
        csVo.setCvr(modifyFormat(StringUtils.defaultIfBlank(item.getCvr(), "0")));
        //ACoS
        csVo.setAcos(modifyFormat(StringUtils.defaultIfBlank(item.getAcos(), "0")));
        //ACoTS
        csVo.setAcots(modifyFormat(StringUtils.defaultIfBlank(item.getAcots(), "0")));
        csVo.setRoas(StringUtils.defaultIfBlank(item.getRoas(), "0"));
        //ASoTS
        csVo.setAsots(modifyFormat(StringUtils.defaultIfBlank(item.getAsots(), "0")));

        //广告花费
        csVo.setAdCost(currency + formatToNumber(StringUtils.defaultIfBlank(item.getAdCost(), "0")));
        //平均点击费用(特殊处理)
        csVo.setAdCostPerClick(currency + getAdCostPerClick(StringUtils.defaultIfBlank(item.getAdCostPerClick(), "0")));
        //广告销售额
        csVo.setAdSale(currency + formatToNumber(StringUtils.defaultIfBlank(item.getAdSale(), "0")));
        //所属广告活动
        csVo.setAdvertisingActivities(StringUtils.defaultString(item.getCampaignName()));
        //投放类型
        csVo.setCampaignTargetingType(TargetingEnum.getTargetingValue(StringUtils.defaultString(item.getCampaignTargetingType())));
        //推广类型
        csVo.setType(CampaignTypeEnum.getCampaignValue(StringUtils.defaultString(item.getType())));

        //广告组合
        csVo.setPortfolioName(StringUtils.defaultString(item.getPortfolioName()));

        //本广告产品订单量
        csVo.setAdSaleNum(Optional.ofNullable(item.getAdSaleNum()).orElse(0));
        //其他产品广告订单量
        csVo.setAdOtherOrderNum(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0));
        //本广告产品销售额
        csVo.setAdSales(StringUtils.defaultIfBlank(item.getAdSales(), "0"));
        //其他产品广告销售额
        csVo.setAdOtherSales(StringUtils.defaultIfBlank(item.getAdOtherSales(), "0"));
        //广告销量
        csVo.setOrderNum(Optional.ofNullable(item.getOrderNum()).orElse(0));
        //本广告产品销量
        csVo.setAdSelfSaleNum(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0));
        //其他产品广告销量
        csVo.setAdOtherSaleNum(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0));
        csVo.setAdCostPercentage(modifyFormat(StringUtils.defaultIfBlank(item.getAdCostPercentage(), "0")));
        csVo.setAdSalePercentage(modifyFormat(StringUtils.defaultIfBlank(item.getAdSalePercentage(), "0")));
        csVo.setAdOrderNumPercentage(modifyFormat(StringUtils.defaultIfBlank(item.getAdOrderNumPercentage(), "0")));
        csVo.setOrderNumPercentage(modifyFormat(StringUtils.defaultIfBlank(item.getOrderNumPercentage(), "0")));
        csVo.setAdvertisingUnitPrice(currency + formatToNumber(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0")));
        return csVo;
    }

    @Deprecated
    private CpcSpaceVo buildExportVo(AdPlacementDataResponse.PlacementPageVo placeVo) {
        CpcSpaceVo csVo = new CpcSpaceVo();
        csVo.setImpressions(placeVo.getImpressions().getValue());
        csVo.setClicks(placeVo.getClicks().getValue());
        csVo.setAdOrderNum(placeVo.getAdOrderNum().getValue());
        csVo.setStrategy(StrategyEnum.getStrategyValue(placeVo.getStrategy()));
        //广告位名称
        csVo.setName(CpcPlaceNameEnum.getPredicateValue(placeVo.getPredicate()));
        if ("产品页面".equals(csVo.getName()) || "搜索结果顶部(首页)".equals(csVo.getName()) || "搜索结果的其余位置".equals(csVo.getName())) {
            //默认调整
            csVo.setPercentage(getPlacementProductPage(placeVo.getPercentage()));
        }
        //点击率
        csVo.setCtr(modifyFormat(placeVo.getCtr()));
        //订单转化率
        csVo.setCvr(modifyFormat(placeVo.getCvr()));
        //ACoS
        csVo.setAcos(modifyFormat(placeVo.getAcos()));
        //ACoTS
        csVo.setAcots(modifyFormat(placeVo.getAcots()));
        csVo.setRoas(placeVo.getRoas());
        //ASoTS
        csVo.setAsots(modifyFormat(placeVo.getAsots()));

        //广告花费
        csVo.setAdCost(currency + formatToNumber(placeVo.getAdCost()));
        //平均点击费用(特殊处理)
        csVo.setAdCostPerClick(currency + getAdCostPerClick(placeVo.getAdCostPerClick()));
        //广告销售额
        csVo.setAdSale(currency + formatToNumber(placeVo.getAdSale()));
        //所属广告活动
        csVo.setAdvertisingActivities(placeVo.getCampaignName());
        //投放类型
        csVo.setCampaignTargetingType(TargetingEnum.getTargetingValue(placeVo.getCampaignTargetingType()));
        //推广类型
        csVo.setType(CampaignTypeEnum.getCampaignValue(placeVo.getType()));

        //广告组合
        csVo.setPortfolioName(placeVo.getPortfolioName());

        //本广告产品订单量
        csVo.setAdSaleNum(placeVo.getAdSaleNum().getValue());
        //其他产品广告订单量
        csVo.setAdOtherOrderNum(placeVo.getAdOtherOrderNum().getValue());
        //本广告产品销售额
        csVo.setAdSales(placeVo.getAdSales());
        //其他产品广告销售额
        csVo.setAdOtherSales(placeVo.getAdOtherSales());
        //广告销量
        csVo.setOrderNum(placeVo.getOrderNum().getValue());
        //本广告产品销量
        csVo.setAdSelfSaleNum(placeVo.getAdSelfSaleNum().getValue());
        //其他产品广告销量
        csVo.setAdOtherSaleNum(placeVo.getAdOtherSaleNum().getValue());
        csVo.setAdCostPercentage(modifyFormat(placeVo.getAdCostPercentage()));
        csVo.setAdSalePercentage(modifyFormat(placeVo.getAdSalePercentage()));
        csVo.setAdOrderNumPercentage(modifyFormat(placeVo.getAdOrderNumPercentage()));
        csVo.setOrderNumPercentage(modifyFormat(placeVo.getOrderNumPercentage()));
        csVo.setAdvertisingUnitPrice(currency + formatToNumber(placeVo.getAdvertisingUnitPrice()));
        return csVo;
    }

    @Deprecated
    private static AdPlacementDataResponse.PlacementPageVo buildGrpcVo(PlacementPageVo item) {
        AdPlacementDataResponse.PlacementPageVo.Builder vo = AdPlacementDataResponse.PlacementPageVo.newBuilder();
        if (StringUtils.isNotBlank(item.getCampaignName())) {
            vo.setCampaignName(item.getCampaignName());
        }
        if (StringUtils.isNotBlank(item.getStrategy())) {
            vo.setStrategy(item.getStrategy());
        }
        if (StringUtils.isNotBlank(item.getPredicate())) {
            vo.setPredicate(item.getPredicate());
        }
        if (StringUtils.isNotBlank(item.getType())) {
            vo.setType(item.getType());
        }
        if (StringUtils.isNotBlank(item.getCampaignTargetingType())) {
            vo.setCampaignTargetingType(item.getCampaignTargetingType());
        }

        if (StringUtils.isNotBlank(item.getPercentage())) {
            vo.setPercentage(item.getPercentage());
        }

        if (item.getPortfolioName() != null) {
            vo.setPortfolioName(item.getPortfolioName());
        }

        vo.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        vo.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        vo.setAdOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOrderNum()).orElse(0)));
        vo.setAdCostPerClick(StringUtils.isNotBlank(item.getAdCostPerClick()) ? item.getAdCostPerClick() : "0");
        vo.setCtr(StringUtils.isNotBlank(item.getCtr()) ? item.getCtr() : "0");
        vo.setCvr(StringUtils.isNotBlank(item.getCvr()) ? item.getCvr() : "0");
        vo.setAcos(StringUtils.isNotBlank(item.getAcos()) ? item.getAcos() : "0");
        vo.setAdCost(StringUtils.isNotBlank(item.getAdCost()) ? item.getAdCost() : "0");
        vo.setAcots(StringUtils.isNotBlank(item.getAcots()) ? item.getAcots() : "0");
        vo.setRoas(StringUtils.isNotBlank(item.getRoas()) ? item.getRoas() : "0");
        vo.setAsots(StringUtils.isNotBlank(item.getAsots()) ? item.getAsots() : "0");
        vo.setAdSale(StringUtils.isNotBlank(item.getAdSale()) ? item.getAdSale() : "0");
        /**
         * TODO 广告报告重构
         * 本广告产品订单量
         */
        vo.setAdSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSaleNum()).orElse(0)));
        //其他产品广告订单量
        vo.setAdOtherOrderNum(Int32Value.of(Optional.ofNullable(item.getAdOtherOrderNum()).orElse(0)));
        //本广告产品销售额
        vo.setAdSales(StringUtils.isNotBlank(item.getAdSales()) ? item.getAdSales() : "0");
        //其他产品广告销售额
        vo.setAdOtherSales(StringUtils.isNotBlank(item.getAdOtherSales()) ? item.getAdOtherSales() : "0");
        //广告销量
        vo.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        //本广告产品销量
        vo.setAdSelfSaleNum(Int32Value.of(Optional.ofNullable(item.getAdSelfSaleNum()).orElse(0)));
        //其他产品广告销量
        vo.setAdOtherSaleNum(Int32Value.of(Optional.ofNullable(item.getAdOtherSaleNum()).orElse(0)));
        // 花费占比
        vo.setAdCostPercentage(StringUtils.isNotBlank(item.getAdCostPercentage()) ? item.getAdCostPercentage() : "0");
        // 销售额占比
        vo.setAdSalePercentage(StringUtils.isNotBlank(item.getAdSalePercentage()) ? item.getAdSalePercentage() : "0");
        // 订单量占比
        vo.setAdOrderNumPercentage(StringUtils.isNotBlank(item.getAdOrderNumPercentage()) ? item.getAdOrderNumPercentage() : "0");
        // 销量占比
        vo.setOrderNumPercentage(StringUtils.isNotBlank(item.getOrderNumPercentage()) ? item.getOrderNumPercentage() : "0");
        // 广告笔单价
        vo.setAdvertisingUnitPrice(Optional.ofNullable(item.getAdvertisingUnitPrice()).map(String::valueOf).orElse("0"));
        return vo.build();
    }
}
