package com.meiyunji.sponsored.service.cpc.dao.impl;

import com.google.common.collect.Lists;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.springjdbc.BaseShardingDaoImpl;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.service.cpc.dao.IWxCpcSbQueryKeywordReportDao;
import com.meiyunji.sponsored.service.cpc.po.CpcSbQueryKeywordReport;
import com.meiyunji.sponsored.service.cpc.service.impl.ReportService;
import com.meiyunji.sponsored.service.cpc.vo.CpcQueryWordDto;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.MatchValueEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;


/**
 * CpcSbQueryKeywordReport
 * <AUTHOR>
 * @date 2023/1/4
 */
@Repository
public class WxCpcSbQueryKeywordReportDaoImpl extends BaseShardingDaoImpl<CpcSbQueryKeywordReport> implements IWxCpcSbQueryKeywordReportDao {


    @Override
    public Page pageList(int puid, CpcQueryWordDto dto, Page page) {

        StringBuilder selectSql = new StringBuilder("SELECT puid,shop_id,count_date , `query`, `query_cn`,ad_format,keyword_id,keyword_text,match_type,ad_group_id,ad_group_name,campaign_id,campaign_name,sum(`cost`) cost,")
                .append(" sum(`sales14d`) sales14d, sum(`impressions`) impressions,sum(`clicks`) clicks,sum(`conversions14d`) conversions14d, ")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share` FROM `t_cpc_sb_query_keyword_report` ");
        StringBuilder countSql = new StringBuilder("select count(*) from ( ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByKeyword(puid,dto,argsList);
        if (whereSql == null) {
            return new Page<>(page.getPageNo(), page.getPageSize(), 0, 0, Lists.newArrayList());
        }
        selectSql.append(whereSql);

        StringBuilder sql = new StringBuilder("select t.* from ( ");
        sql.append(selectSql);
        sql.append(") t left join t_amazon_ad_keyword_sb w  on  t.puid=w.puid and t.shop_id = w.shop_id and  t.keyword_id = w.keyword_id ");

        if (StringUtils.isNotBlank(dto.getState())) {
            sql.append(" and w.state = ? ");
            argsList.add(dto.getState());
        }

        if(StringUtils.isNotBlank(dto.getOrderField()) && StringUtils.isNotBlank(dto.getOrderValue())){
            String orderField = ReportService.getSbReportField(dto.getOrderField(),false);
            if(StringUtils.isNotBlank(orderField)){
                sql.append(" order by ").append(orderField);
                if("desc".equals(dto.getOrderValue())){
                    sql.append(" desc");
                }
                sql.append(" , query desc ");
            }
        }

        countSql.append(sql).append(") c");

        Object[] args = argsList.toArray();

        return this.getPageResult(puid,page.getPageNo(), page.getPageSize(), countSql.toString(), args, sql.toString(), args,CpcSbQueryKeywordReport.class);
    }


    @Override
    public List<AdHomePerformancedto> getReportKeywordByDate(Integer puid, CpcQueryWordDto dto) {
        dto.setCampaignId("");
        dto.setGroupId("");
        StringBuilder sql = new StringBuilder("select keyword_id keyword_id, count_date,sum(cost) `cost`, sum(sales14d) total_sales, ")
                .append(" sum(impressions) `impressions`, sum(clicks) `clicks`,sum(conversions14d) as sale_num,")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share` FROM `t_cpc_sb_query_keyword_report` ");
        List<Object> argsList = Lists.newArrayList();
        String whereSql = getWhereSqlCountByDate(puid, dto, argsList);
        if (whereSql == null) {
            return Lists.newArrayList();
        }
        sql.append(whereSql);

        return getJdbcTemplate(puid).query(sql.toString(), (re, i) -> AdHomePerformancedto.builder()
                .keywordId(re.getString("keyword_id"))
                .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))
                .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                .countDate(re.getString("count_date"))
                .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                .impressionRank(Optional.ofNullable(re.getInt("impression_rank")).orElse(0))
                .impressionShare(Optional.ofNullable(re.getDouble("impression_share")).orElse(0.0))
                .build(), argsList.toArray());
    }


    public List<AdHomePerformancedto> getReportKeywordByKeywordIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> keywordIdList,
                                                                      CpcQueryWordDto dto) {
        if (CollectionUtils.isEmpty(keywordIdList)) {
            return new ArrayList<>();
        }

        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder("select count_date, SUM(impressions) impressions,SUM(clicks) clicks,")
                .append(" SUM(cost) cost,SUM(conversions14d) sale_num,SUM(sales14d) total_sales,")
                .append(" sum(orders_new_to_brand14d) `orders_new_to_brand14d`, sum(sales_new_to_brand14d) `sales_new_to_brand14d`,sum(impression_rank) `impression_rank`,")
                .append(" sum(impression_share) `impression_share` FROM `t_cpc_sb_query_keyword_report` where puid= ? and shop_id= ?  ");

        argsList.add(puid);
        argsList.add(shopId);

        sql.append(SqlStringUtil.dealInList("keyword_id", keywordIdList, argsList));
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return Lists.newArrayList();
            } else {
                sql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) {
                    //模糊搜索
                    sql.append(" and ").append(field).append(" like ?");
                    argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchValue()) + "%");
                } else {
                    //默认精确
                    sql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }

        sql.append(" and count_date >= ? and count_date <= ? group by count_date ");
        argsList.add(startStr);
        argsList.add(endStr);

        return getJdbcTemplate(puid).query(sql.toString(), (re, i) -> {
            AdHomePerformancedto dto1 = AdHomePerformancedto.builder()
                    .adCost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .adOrderNum(Optional.ofNullable(re.getInt("sale_num")).orElse(0))  //销量字段订单
                    .adSale(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.ofNullable(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.ofNullable(re.getInt("impressions")).orElse(0))
                    .countDate(re.getString("count_date"))
                    .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
                    .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
                    .impressionRank(Optional.ofNullable(re.getInt("impression_rank")).orElse(0))
                    .impressionShare(Optional.ofNullable(re.getDouble("impression_share")).orElse(0.0))
                    .build();
            return dto1;
        }, argsList.toArray());
    }

    private String getWhereSqlCountByKeyword(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=? and marketplace_id=? and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getMarketplaceId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypeList,argsList));
        }
        if (StringUtils.isNotBlank(dto.getCampaignId())) {
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));

        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) {
            //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return null;
            } else {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }
        if (StringUtils.isNotBlank(dto.getGroupId())) {
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }

        if (StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class, dto.getSearchField());
            if (StringUtils.isNotEmpty(field)) {
                if ("blur".equals(dto.getSearchType())) {
                    //模糊搜索
                    if (dto.getSearchVelueList().size() > 1) {
                        whereSql.append(" and (").append(SqlStringUtil.dealLikeListOr(field, dto.getSearchVelueList(), argsList, false)).append(")");
                    } else {
                        whereSql.append(" and ").append(field).append(" like ?");
                        argsList.add("%" + SqlStringUtil.dealLikeSql(dto.getSearchVelueList().get(0)) + "%");
                    }

                } else {//默认精确
                    if (dto.getSearchVelueList().size() > 1) {
                        whereSql.append(SqlStringUtil.dealInList(field, dto.getSearchVelueList(), argsList));
                    } else {
                        whereSql.append(" and ").append(field).append(" = ?");
                        argsList.add(dto.getSearchVelueList().get(0));
                    }
                }
            }
        }
        whereSql.append(" group by keyword_id,`query` ");
        if (dto.getUseAdvanced()) {
            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if (dto.getImpressionsMin() != null) {
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if (dto.getImpressionsMax() != null) {
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if (dto.getClicksMin() != null) {
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if (dto.getClicksMax() != null) {
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if (dto.getClickRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if (dto.getClickRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if (dto.getCostMin() != null) {
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if (dto.getCostMax() != null) {
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if (dto.getCpcMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if (dto.getCpcMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }

            //广告订单量
            if (dto.getOrderNumMin() != null) {
                whereSql.append(" and conversions14d >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if (dto.getOrderNumMax() != null) {
                whereSql.append(" and conversions14d <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if (dto.getSalesMin() != null) {
                whereSql.append(" and sales14d >= ?");
                argsList.add(dto.getSalesMin());
            }
            if (dto.getSalesMax() != null) {
                whereSql.append(" and sales14d <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if (dto.getSalesConversionRateMin() != null) {
                whereSql.append(" and ROUND(ifnull(conversions14d/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if (dto.getSalesConversionRateMax() != null) {
                whereSql.append(" and ROUND(ifnull(conversions14d/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if (dto.getAcosMin() != null) {
                whereSql.append(" and ROUND(ifnull(cost/sales14d,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if (dto.getAcosMax() != null) {
                whereSql.append(" and ROUND(ifnull(cost/sales14d,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(sales14d/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(sales14d/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(sales14d,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(sales14d,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }

    private String getWhereSqlCountByDate(int puid, CpcQueryWordDto dto, List<Object> argsList) {
        StringBuilder whereSql = new StringBuilder(" where puid=? and shop_id=?  and count_date>=? and count_date<=? ");
        argsList.add(puid);
        argsList.add(dto.getShopId());
        argsList.add(dto.getStart());
        argsList.add(dto.getEnd());
        /**
         *   根据匹配类型（matchType）查对应表的数据
         *   matchType in ('close-match','loose-match')'紧密匹配'，'宽泛匹配' 不查询该表数据
         *   matchType in ('broad','phrase','exact')'广泛匹配','词组匹配','精准匹配'查询该表数据
         *   matchTypes：符合查询该表数据的条件('广泛匹配','词组匹配','精准匹配')数组
         *   MatchValueEnum值是('广泛匹配','词组匹配','精准匹配')的枚举
         */
        List<String> matchTypeList = StringUtil.stringToList(dto.getMatchType(),StringUtil.SPLIT_COMMA);
        List<String> matchTypes = Lists.newArrayList();
        //不带匹配类型条件查询不走下面逻辑
        // start
        if (CollectionUtils.isNotEmpty(matchTypeList)) {
            for (String matchType : matchTypeList) {
                if (StringUtils.isNotBlank(MatchValueEnum.getMatchValue(matchType))) {
                    matchTypes.add(matchType);
                }
            }
            //如果匹配条件为(紧密匹配，宽泛匹配),则不查询数据
            if (CollectionUtils.isEmpty(matchTypes)){
                whereSql.append(" group by keyword_id,`query` having 1=0 ");
                return whereSql.toString();
            }
        }
        // end
        if (CollectionUtils.isNotEmpty(matchTypes)){
            whereSql.append(SqlStringUtil.dealInList("match_type",matchTypes,argsList));
        }
        if(StringUtils.isNotBlank(dto.getCampaignId())){
            List<String> list = StringUtil.splitStr(dto.getCampaignId());
            whereSql.append(SqlStringUtil.dealInList("campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(dto.getCampaignIdList())) { //广告组合查询
            whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCampaignIdList(), argsList));
        }
        // 满足权限的广告活动ID
        if (dto.getCheckProductRightPair() != null && dto.getCheckProductRightPair().getLeft()) {
            if (CollectionUtils.isEmpty(dto.getCheckProductRightPair().getRight())) {
                return null;
            } else {
                whereSql.append(SqlStringUtil.dealInList("campaign_id", dto.getCheckProductRightPair().getRight(), argsList));
            }
        }
        if(StringUtils.isNotBlank(dto.getGroupId())){
            List<String> list = StringUtil.splitStr(dto.getGroupId());
            whereSql.append(SqlStringUtil.dealInList("ad_group_id", list, argsList));
        }
        if (StringUtils.isNotBlank(dto.getKeywordId())) {
            whereSql.append(" and keyword_id = ? ");
            argsList.add(dto.getKeywordId());
        }

        if(StringUtils.isNotBlank(dto.getSearchField()) && StringUtils.isNotBlank(dto.getSearchValue())){
            String field = SqlStringUtil.getSqlField(CpcSbQueryKeywordReport.class,dto.getSearchField());
            if(StringUtils.isNotEmpty(field)){
                if("blur".equals(dto.getSearchType())){ //模糊搜索
                    whereSql.append(" and ").append(field).append(" like ?");
                    argsList.add("%"+SqlStringUtil.dealLikeSql(dto.getSearchValue())+"%");
                }else{//默认精确
                    whereSql.append(" and ").append(field).append(" = ?");
                    argsList.add(dto.getSearchValue());
                }
            }
        }
        whereSql.append(" group by keyword_id,`query` ");
        if(dto.getUseAdvanced()){

            BigDecimal shopSales = dto.getShopSales() != null ? dto.getShopSales() : BigDecimal.valueOf(0);

            whereSql.append(" having 1=1 ");
            //展示量
            if(dto.getImpressionsMin() != null){
                whereSql.append(" and impressions >= ?");
                argsList.add(dto.getImpressionsMin());
            }
            if(dto.getImpressionsMax() != null){
                whereSql.append(" and impressions <= ?");
                argsList.add(dto.getImpressionsMax());
            }
            //点击量
            if(dto.getClicksMin() != null){
                whereSql.append(" and clicks >= ?");
                argsList.add(dto.getClicksMin());
            }
            if(dto.getClicksMax() != null){
                whereSql.append(" and clicks <= ?");
                argsList.add(dto.getClicksMax());
            }
            //点击率（clicks/impressions）
            if(dto.getClickRateMin() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) >= ?");
                argsList.add(dto.getClickRateMin());
            }
            if(dto.getClickRateMax() != null){
                whereSql.append(" and ROUND(ifnull(clicks/impressions,0),4) <= ?");
                argsList.add(dto.getClickRateMax());
            }
            //花费
            if(dto.getCostMin() != null){
                whereSql.append(" and cost >= ?");
                argsList.add(dto.getCostMin());
            }
            if(dto.getCostMax() != null){
                whereSql.append(" and cost <= ?");
                argsList.add(dto.getCostMax());
            }
            //cpc  平均点击费用
            if(dto.getCpcMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) >= ?");
                argsList.add(dto.getCpcMin());
            }
            if(dto.getCpcMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/clicks,0),2) <= ?");
                argsList.add(dto.getCpcMax());
            }
            //广告订单量
            if(dto.getOrderNumMin() != null){
                whereSql.append(" and sale_num >= ?");
                argsList.add(dto.getOrderNumMin());
            }
            if(dto.getOrderNumMax() != null){
                whereSql.append(" and sale_num <= ?");
                argsList.add(dto.getOrderNumMax());
            }
            //广告销售额
            if(dto.getSalesMin() != null){
                whereSql.append(" and total_sales >= ?");
                argsList.add(dto.getSalesMin());
            }
            if(dto.getSalesMax() != null){
                whereSql.append(" and total_sales <= ?");
                argsList.add(dto.getSalesMax());
            }
            //订单转化率
            if(dto.getSalesConversionRateMin() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) >= ?");
                argsList.add(dto.getSalesConversionRateMin());
            }
            if(dto.getSalesConversionRateMax() != null){
                whereSql.append(" and ROUND(ifnull(sale_num/clicks,0),4) <= ?");
                argsList.add(dto.getSalesConversionRateMax());
            }
            //acos
            if(dto.getAcosMin() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) >= ?");
                argsList.add(dto.getAcosMin());
            }
            if(dto.getAcosMax() != null){
                whereSql.append(" and ROUND(ifnull(cost/total_sales,0),4) <= ?");
                argsList.add(dto.getAcosMax());
            }
            // roas
            if (dto.getRoasMin() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) >= ?");
                argsList.add(dto.getRoasMin());
            }
            // roas
            if (dto.getRoasMax() != null) {
                whereSql.append(" and ROUND(ifnull(total_sales/cost,0),2) <= ?");
                argsList.add(dto.getRoasMax());
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAcotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // acots  需要乘以店铺销售额
            if (dto.getAcotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(cost,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAcotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots 需要乘以店铺销售额
            if (dto.getAsotsMin() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) >= ? ");
                    argsList.add(dto.getAsotsMin());
                } else {
                    whereSql.append(" and 0 >= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
            // asots  需要乘以店铺销售额
            if (dto.getAsotsMax() != null) {
                if (shopSales.doubleValue() > 0) {
                    whereSql.append(" and ROUND((ifnull(total_sales,0) / ").append(shopSales).append(" ),4) <= ? ");
                    argsList.add(dto.getAsotsMax());
                } else {
                    whereSql.append(" and 0 <= ? ");
                    argsList.add(dto.getAcotsMin());
                }
            }
        }
        return whereSql.toString();
    }


}