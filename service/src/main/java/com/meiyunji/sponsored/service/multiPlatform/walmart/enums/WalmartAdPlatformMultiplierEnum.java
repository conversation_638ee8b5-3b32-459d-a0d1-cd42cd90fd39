package com.meiyunji.sponsored.service.multiPlatform.walmart.enums;

import lombok.Getter;

/**
 * @author: ys
 * @date: 2025/4/21 10:47
 * @describe:
 */
@Getter
public enum WalmartAdPlatformMultiplierEnum {
    DESKTOP("Desktop", "PC端"),
    APP("App", "App"),
    Mobile("Mobile", "移动端"),
    ;
    private String code;
    private String msg;

    WalmartAdPlatformMultiplierEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public static WalmartAdPlatformMultiplierEnum getWalmartAdPlatformMultiplierEnumByCode (String code) {
        for (WalmartAdPlatformMultiplierEnum en : WalmartAdPlatformMultiplierEnum.values()) {
            if (en.getCode().equalsIgnoreCase(code)) {
                return en;
            }
        }
        return null;
    }
}
