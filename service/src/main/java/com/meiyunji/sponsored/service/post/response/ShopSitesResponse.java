package com.meiyunji.sponsored.service.post.response;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 帖子获取店铺请求resp
 * @Author: heqiwen
 * @Date: 2025/03/31 10:28
 */
@Data
public class ShopSitesResponse implements Serializable {
    private static final long serialVersionUID = 123456L;

    private List<ShopSite> shopSites;

    @Data
    public static class ShopSite {
        private Integer shopId;
        private String shopName;
        private String sellingPartnerId;
        private String region;
        private String marketplaceId;
        private String marketplace;
        private String marketplaceCN;
        private String domain;
        private Boolean lowCostStore = false;
        private String status;
        private Boolean supportLowCostStore;
    }
}
