package com.meiyunji.sponsored.service.excel.excelTools.writeHandler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.write.merge.AbstractMergeStrategy;
import com.meiyunji.sponsored.service.excel.excelTools.dto.RowRangeDto;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.util.CellRangeAddress;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/6/30 11:20
 */
public class MergeHandler extends AbstractMergeStrategy {

    private Map<Integer, List<RowRangeDto>> strategyMap;

    public MergeHandler(Map<Integer, List<RowRangeDto>> strategyMap) {
        this.strategyMap = strategyMap;
    }

    @Override
    protected void merge(Sheet sheet, Cell cell, Head head, Integer integer) {
        if (cell.getRowIndex() == 1 && cell.getColumnIndex() == 0) {
            /**
             * 保证每个cell被合并一次，如果不加上面的判断，因为是一个cell一个cell操作的，
             * 例如合并A2:A3,当cell为A2时，合并A2,A3，但是当cell为A3时，又是合并A2,A3，
             * 但此时A2,A3已经是合并的单元格了
             */
            for (Map.Entry<Integer, List<RowRangeDto>> entry : strategyMap.entrySet()) {
                Integer columnIndex = entry.getKey();
                entry.getValue().forEach(rowRange -> {
                    //添加一个合并请求
                    sheet.addMergedRegionUnsafe(new CellRangeAddress(rowRange.getStart(),
                            rowRange.getEnd(), columnIndex, columnIndex));
                });
            }
        }
    }

    public static void fillStrategyMap(Map<Integer, List<RowRangeDto>> mergeMap, Integer col, int index) {
        List<RowRangeDto> rowRangeDtoList = mergeMap.get(col) == null ? new ArrayList<>() : mergeMap.get(col);
        boolean flag = false;
        for (RowRangeDto dto : rowRangeDtoList) {
            //分段list中是否有end索引是上一行索引的，如果有，则索引+1
            if (dto.getEnd() == index) {
                dto.setEnd(index + 1);
                flag = true;
            }
        }
        //如果没有，则新增分段
        if (!flag) {
            rowRangeDtoList.add(new RowRangeDto(index, index + 1));
        }
        mergeMap.put(col, rowRangeDtoList);
    }
}
