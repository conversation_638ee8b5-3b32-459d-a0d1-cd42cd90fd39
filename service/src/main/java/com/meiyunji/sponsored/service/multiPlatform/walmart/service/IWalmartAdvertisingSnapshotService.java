package com.meiyunji.sponsored.service.multiPlatform.walmart.service;

import com.meiyunji.sponsored.service.multiPlatform.walmart.po.WalmartAdvertisingSnapshot;

import java.util.List;

/**
 * @author: pxq
 * @date: 2025/02/24
 * @Description:
 */
public interface IWalmartAdvertisingSnapshotService {


    int add(WalmartAdvertisingSnapshot advertisingSnapshot);

    int getUndoneCountByType(Integer type);

    int getUndoneCountByType(Integer puid, Integer shopId, Integer type);

    int updateState(Long id, Integer state, String jobStatus, String error);


    /**
     * @author: pxq
     * @date: 2025/02/24
     * @description: 执行快照
     */
    void syncSnapshot(Long id);
    Boolean syncSnapshotAndHandle(String snapshotId);

    List<Long>getUndoneId();

    WalmartAdvertisingSnapshot getUnderwayByType(Integer type);

}
