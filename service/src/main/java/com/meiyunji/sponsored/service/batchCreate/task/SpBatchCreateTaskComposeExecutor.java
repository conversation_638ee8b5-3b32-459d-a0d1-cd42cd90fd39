package com.meiyunji.sponsored.service.batchCreate.task;

import com.meiyunji.sponsored.common.util.ThreadPoolUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.batchCreate.dto.task.CallAmazonApiTaskResultDto;
import com.meiyunji.sponsored.service.batchCreate.enums.SpBatchCreateAdTaskLevelEnum;
import com.meiyunji.sponsored.service.enums.ShopAdStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sun<PERSON><PERSON>@dianxiaomi.com
 * @date: 2023-11-17  17:09
 */

/**
 * sp批量创建的任务管理和执行类，任务编排
 */

@Component
@Slf4j
public class SpBatchCreateTaskComposeExecutor {

    @Autowired
    private CampaignCallAmazonApiTask campaignCallAmazonApiTask;

    @Autowired
    private GroupCallAmazonApiTask groupCallAmazonApiTask;

    @Autowired
    private ProductCallAmazonApiTask productCallAmazonApiTask;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private TaskStatusHelper taskStatusHelper;

    @Autowired
    private BatchTaskStatusUpdateTask batchTaskStatusUpdateTask;

    @Autowired
    private KeywordCallAmazonApiTask keywordCallAmazonApiTask;

    @Autowired
    private CategoryTargetCallAmazonApiTask categoryTargetCallAmazonApiTask;

    @Autowired
    private AutoTargetCallAmazonApiTask autoTargetCallAmazonApiTask;

    @Autowired
    private NekeywordCallAmazonApiTask nekeywordCallAmazonApiTask;

    @Autowired
    private NegativeTargetingCallAmazonApiTask negativeTargetingCallAmazonApiTask;

    private ThreadPoolExecutor threadPool = ThreadPoolUtil.getSpBatchCreateCallAmazonAPIPool();

    /**
     * 批量创建广告活动
     * @param resultDto
     */
    public void executeAmazonBatchCampaign(CallAmazonApiTaskResultDto resultDto) {

        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList()) || !validateShop(Collections.singletonMap(SpBatchCreateAdTaskLevelEnum.LEVEL_CAMPAIGN, resultDto))) {
            return;
        }

        log.info("sp batch create, start call campaign amazon api, batch tarceId: {}, taskId: {}, shopId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), resultDto.getShopId());

        //广告活动->广告组->广告产品
        CompletableFuture<CallAmazonApiTaskResultDto> productFuture = CompletableFuture.supplyAsync(() -> campaignCallAmazonApiTask.call(resultDto), threadPool)
                .thenComposeAsync(campaignResultDto -> CompletableFuture.supplyAsync(() -> groupCallAmazonApiTask.call(campaignResultDto)), threadPool)
                .thenComposeAsync(groupResultDto -> CompletableFuture.supplyAsync(() -> productCallAmazonApiTask.call(groupResultDto)), threadPool);

        //投放->主任务状态
        executeAmazonBatchTargeting(productFuture, resultDto.getPuid(), resultDto.getShopId(), resultDto.getTaskId());

    }

    private void createSBCampaign(CallAmazonApiTaskResultDto resultDto) {

        log.info("sp batch create, start call campaign amazon api, batch tarceId: {}, taskId: {}, shopId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), resultDto.getShopId());

//        if (Objects.isNull(req.getCampaignId())) {
//            CompletableFuture<CallAmazonApiTaskResultDto> createFuture = CompletableFuture
//                    .supplyAsync(() -> campaignCallAmazonApiTask.call(resultDto), threadPool)
//                    .thenComposeAsync(campaignResultDto -> CompletableFuture.supplyAsync(() -> groupCallAmazonApiTask.call(campaignResultDto)), threadPool)
//                    .thenComposeAsync(groupResultDto -> CompletableFuture.supplyAsync(() -> targetCallAmazonApiTask.call(targetResultDto)), threadPool)
//                    .thenComposeAsync(targetResultDto -> CompletableFuture.supplyAsync(() -> neTargetCallAmazonApiTask.call(neTargetResultDto)), threadPool)
//                    .thenComposeAsync(neTargetResultDto-> CompletableFuture.supplyAsync(() -> adsCallAmazonApiTask.call(adsResultDto)), threadPool);
//        }
//
//
//        if (Objects.nonNull(req.getCampaignId()) && StringUtils.isEmpty(req.getAdGroupId()) && StringUtils.isEmpty(req.getAdGroupId())) {
//            //校验campaignId是否存在
//            checkCampaignId(req.getCampaignId(), req.getAdGroupId());
//
//            //继续创建
//            CompletableFuture<CallAmazonApiTaskResultDto> createFuture = CompletableFuture.supplyAsync(() -> groupCallAmazonApiTask.call(campaignResultDto)), threadPool)
//                    .thenComposeAsync(groupResultDto -> CompletableFuture.supplyAsync(() -> targetCallAmazonApiTask.call(targetResultDto)), threadPool)
//                    .thenComposeAsync(targetResultDto -> CompletableFuture.supplyAsync(() -> neTargetCallAmazonApiTask.call(neTargetResultDto)), threadPool)
//                    .thenComposeAsync(neTargetResultDto-> CompletableFuture.supplyAsync(() -> adsCallAmazonApiTask.call(adsResultDto)), threadPool);
//        }
//
//        if (StringUtils.isNotBlank(req.getAdGroupId()) && StringUtils.isEmpty(req.getTargetId())) {
//            //校验adGroupId是否存在
//            checkAdGroupId(req.getAdGroupId(), req.getTargetId());
//
//            //继续创建
//            CompletableFuture<CallAmazonApiTaskResultDto> createFuture = CompletableFuture.supplyAsync(() -> targetCallAmazonApiTask.call(targetResultDto)), threadPool)
//                    .thenComposeAsync(targetResultDto -> CompletableFuture.supplyAsync(() -> neTargetCallAmazonApiTask.call(neTargetResultDto)), threadPool)
//                    .thenComposeAsync(neTargetResultDto-> CompletableFuture.supplyAsync(() -> adsCallAmazonApiTask.call(adsResultDto)), threadPool);
//        }
//
//        if (StringUtils.isNotBlank(req.getTargetId()) && StringUtils.isEmpty(req.getNeTargetId())) {
//            //校验targetId是否存在
//            checkTargetId(req.getTargetId(), req.getNeTargetId());
//
//            //继续创建
//            CompletableFuture<CallAmazonApiTaskResultDto> createFuture = CompletableFuture.supplyAsync(() -> neTargetCallAmazonApiTask.call(neTargetResultDto)), threadPool)
//                    .thenComposeAsync(neTargetResultDto-> CompletableFuture.supplyAsync(() -> adsCallAmazonApiTask.call(adsResultDto)), threadPool);
//        }
//
//        if (StringUtils.isNotBlank(req.getNeTargetId()) && StringUtils.isEmpty(req.getAdId())) {
//            //校验targetId是否存在
//            checkNeTargetId(req.getNeTargetId(), req.getAdId());
//
//            //继续创建
//            CompletableFuture.supplyAsync(() -> adsCallAmazonApiTask.call(adsResultDto)), threadPool);
//        }
    }


    /**
     * 批量创建广告组
     * @param resultDto
     */
    public void executeAmazonBatchGroup(CallAmazonApiTaskResultDto resultDto) {

        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList()) || !validateShop(Collections.singletonMap(SpBatchCreateAdTaskLevelEnum.LEVEL_GROUP, resultDto))) {
            return;
        }

        log.info("sp batch create, start call group amazon api, batch tarceId: {}, taskId: {}, shopId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), resultDto.getShopId());

        //广告组->广告产品
        CompletableFuture<CallAmazonApiTaskResultDto> productFuture = CompletableFuture.supplyAsync(() -> groupCallAmazonApiTask.call(resultDto), threadPool)
                .thenComposeAsync(groupResultDto -> {
                    groupResultDto.setCurrentLevel(false);
                    return CompletableFuture.supplyAsync(() -> productCallAmazonApiTask.call(groupResultDto));
                }, threadPool);


        //投放->主任务状态
        executeAmazonBatchTargeting(productFuture, resultDto.getPuid(), resultDto.getShopId(), resultDto.getTaskId());

    }


    /**
     * 批量创建广告产品
     * @param resultDto
     */
    public void executeAmazonBatchProduct(CallAmazonApiTaskResultDto resultDto) {


        if (CollectionUtils.isEmpty(resultDto.getSuccessIdList()) || !validateShop(Collections.singletonMap(SpBatchCreateAdTaskLevelEnum.LEVEL_PRODUCT, resultDto))) {
            return;
        }

        log.info("sp batch create, start call product amazon api, batch tarceId: {}, taskId: {}, shopId: {}", resultDto.getTraceDto().getTraceId(), resultDto.getTaskId(), resultDto.getShopId());

        //广告产品任务
        CompletableFuture<CallAmazonApiTaskResultDto> productFuture = CompletableFuture.supplyAsync(() -> productCallAmazonApiTask.call(resultDto), threadPool);

        //投放->主任务状态
        executeAmazonBatchTargeting(productFuture, resultDto.getPuid(), resultDto.getShopId(), resultDto.getTaskId());

    }

    /**
     * 定时任务批量创建投放
     * 调用亚马逊接口创建关键词投放，商品投放，自动投放，否定关键词，否定商品5种
     * @param taskId 任务id
     * @param resultDtoMap 5种投放需要的数据集合
     */
    public void executeAmazonBatchTargeting(Integer puid, Integer shopId, Long taskId, Map<SpBatchCreateAdTaskLevelEnum, CallAmazonApiTaskResultDto> resultDtoMap) {
        List<CompletableFuture<Void>> futureList = new ArrayList<>(resultDtoMap.size());
        resultDtoMap.forEach((k, v) -> {
            if (!CollectionUtils.isEmpty(v.getSuccessIdList())) {
                switch (k) {
                    case LEVEL_KEYWORD:
                        CompletableFuture<Void> keywordFuture = CompletableFuture.runAsync(() -> keywordCallAmazonApiTask.call(v), threadPool);
                        futureList.add(keywordFuture);
                        break;
                    case LEVEL_TARGETING:
                        CompletableFuture<Void> targetingFuture = CompletableFuture.runAsync(() -> categoryTargetCallAmazonApiTask.call(v), threadPool);
                        futureList.add(targetingFuture);
                        break;
                    case LEVEL_AUTO_TARGETING:
                        CompletableFuture<Void> autoTargetingFuture = CompletableFuture.runAsync(() -> autoTargetCallAmazonApiTask.call(v), threadPool);
                        futureList.add(autoTargetingFuture);
                        break;
                    case LEVEL_NEKEYWORD:
                        CompletableFuture<Void> nekeywordFuture = CompletableFuture.runAsync(() -> nekeywordCallAmazonApiTask.call(v), threadPool);
                        futureList.add(nekeywordFuture);
                        break;
                    case LEVEL_NETATRGETING:
                        CompletableFuture<Void> netargetingFuture = CompletableFuture.runAsync(() -> negativeTargetingCallAmazonApiTask.call(v), threadPool);
                        futureList.add(netargetingFuture);
                        break;
                }
            }
        });

        if (!CollectionUtils.isEmpty(futureList)) {
            //等待所有投放执行完成
            CompletableFuture<Void> combindFuture = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()]));
            //执行主任务状态变更
            combindFuture.thenRunAsync(() -> batchTaskStatusUpdateTask.update(puid, shopId, taskId), threadPool);
        } else {
            CompletableFuture.runAsync(() -> batchTaskStatusUpdateTask.update(puid, shopId, taskId), threadPool);
        }
    }


    /**
     * 广告产品执行完后执行投放和修改主任务状态
     * @param productFuture
     * @param taskId
     */
    private void executeAmazonBatchTargeting(CompletableFuture<CallAmazonApiTaskResultDto> productFuture, Integer puid, Integer shopId, Long taskId) {
        //投放
        CompletableFuture<Void> keywordFuture = productFuture.thenAcceptAsync(productResultDto ->{
            productResultDto.setCurrentLevel(false);
            keywordCallAmazonApiTask.call(productResultDto);
        }, threadPool);
        CompletableFuture<Void> targetingFuture = productFuture.thenAcceptAsync(productResultDto -> {
            productResultDto.setCurrentLevel(false);
            categoryTargetCallAmazonApiTask.call(productResultDto);
        }, threadPool);
        CompletableFuture<Void> autoTargetingFuture = productFuture.thenAcceptAsync(productResultDto ->{
            productResultDto.setCurrentLevel(false);
            autoTargetCallAmazonApiTask.call(productResultDto);
        }, threadPool);
        CompletableFuture<Void> nekeywordFuture = productFuture.thenAcceptAsync(productResultDto -> {
            productResultDto.setCurrentLevel(false);
            nekeywordCallAmazonApiTask.call(productResultDto);
        }, threadPool);
        CompletableFuture<Void> netargetingFuture = productFuture.thenAcceptAsync(productResultDto ->{
            productResultDto.setCurrentLevel(false);
            negativeTargetingCallAmazonApiTask.call(productResultDto);
        }, threadPool);

        //等待执行完成触发主任务状态更新
        CompletableFuture<Void> combindFuture = CompletableFuture.allOf(keywordFuture, targetingFuture, autoTargetingFuture, nekeywordFuture, netargetingFuture);
        combindFuture.thenRunAsync(() -> batchTaskStatusUpdateTask.update(puid, shopId, taskId), threadPool);
    }


    /**
     * 校验店铺
     * @param resultDtoMap
     * @return
     */
    private boolean validateShop(Map<SpBatchCreateAdTaskLevelEnum, CallAmazonApiTaskResultDto> resultDtoMap) {

        if (resultDtoMap.isEmpty()) {
            return false;
        }

        CallAmazonApiTaskResultDto dto = resultDtoMap.values().stream().findFirst().get();

        //店铺校验
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(dto.getShopId(), dto.getPuid());
        if (shop != null && !ShopAdStatusEnum.UNAUTH.getName().equals(shop.getAdStatus())) {
            return true;
        }

        resultDtoMap.forEach((k, v) -> {
            //全部失败
            if (!CollectionUtils.isEmpty(v.getSuccessIdList())) {
                String msg = k.getDesc() + "创建失败";
                Map<Long, String> map = new HashMap<>();
                v.getSuccessIdList().forEach(x -> map.put(x, msg));
                log.error("sp batch create, shop is null or unauth, fail all, batch traceId: {}, taskId: {}, shopId: {}", v.getTraceDto().getTraceId(), v.getTaskId(), v.getShopId());
                taskStatusHelper.batchUpdateErrStatus(v.getPuid(), v.getTaskId(), map, k, k.getDesc() + "创建失败", false);
                if (SpBatchCreateAdTaskLevelEnum.LEVEL_PRODUCT == k) {
                    //更新所在广告组下的投放
                    taskStatusHelper.batchUpdateErrStatus(v.getPuid(), v.getTaskId(), map, SpBatchCreateAdTaskLevelEnum.LEVEL_ALL_PRODUCT, SpBatchCreateAdTaskLevelEnum.LEVEL_ALL_PRODUCT.getDesc() + "创建失败", false);
                }
            }
        });

        return false;
    }

}
