package com.meiyunji.sponsored.service.cpc.service2.impl;

import com.alibaba.fastjson.JSON;
import com.amazon.advertising.mode.PredicateEnum;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.protobuf.Int32Value;
import com.google.protobuf.Int64Value;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.permission.annotation.AdProductPermissionFilter;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.common.util.PageUtil;
import com.meiyunji.sponsored.grpc.common.GetGroupHourReportResponsePb;
import com.meiyunji.sponsored.grpc.common.GetPlacementHourReportResponsePb;
import com.meiyunji.sponsored.grpc.entry.ReportDateModelPb;
import com.meiyunji.sponsored.rpc.adAggregateHour.AdPageBasicData;
import com.meiyunji.sponsored.rpc.adCommon.*;
import com.meiyunji.sponsored.rpc.vo.CampaignNeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.CampaignNeTargetingSpRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeKeywordsPageRpcVo;
import com.meiyunji.sponsored.rpc.vo.NeTargetingPageRpcVo;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.adProductRight.service.IAdProductRightService;
import com.meiyunji.sponsored.service.config.DynamicRefreshConfiguration;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.helper.CpcPageWordRootHelper;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.*;
import com.meiyunji.sponsored.service.cpc.service2.*;
import com.meiyunji.sponsored.service.cpc.service2.handlers.CpcPageIdsHandler;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbAdsService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbCampaignChangeHistoryService;
import com.meiyunji.sponsored.service.cpc.service2.sb.ICpcSbCampaignService;
import com.meiyunji.sponsored.service.cpc.service2.sp.*;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.enums.*;
import com.meiyunji.sponsored.service.log.qo.OperationLogQo;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.log.vo.AdManageLogDayVo;
import com.meiyunji.sponsored.service.log.vo.AdManageLogVo;
import com.meiyunji.sponsored.service.monitor.SaveMonitor;
import com.meiyunji.sponsored.service.monitor.enums.MonitorPageFunctionEnum;
import com.meiyunji.sponsored.service.monitor.enums.MonitorTypeEnum;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.CampaignAggregateHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.PlacementAggregateHourParam;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdCampaignHourReportService;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdGroupHourReportService;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdPlacementHourReportService;
import com.meiyunji.sponsored.service.util.GrayUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.jsoup.helper.StringUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: wade
 * @date: 2021/9/13 20:13
 * @describe: 广告通用接口(联合查询sp sb sd) 主要包含列表页面和指标数据和chart图数据接口
 */
@Service
@Slf4j
public class CpcCommonServiceImpl implements ICpcCommonService {

    @Autowired
    private ICpcCampaignService cpcCampaignService;

    @Autowired
    private ICpcAdGroupService cpcAdGroupService;

    @Autowired
    private ICpcService cpcService;

    @Autowired
    private ICpcProductService cpcProductService;

    @Autowired
    private ICpcQueryTargetingReportService cpcQueryTargetingReportService;

    @Autowired
    private ICpcQueryKeywordReportService cpcQueryKeywordReportService;

    @Autowired
    private ICpcSpCampaignService cpcSpCampaignService;

    @Autowired
    private ICpcNeKeywordsService cpcNeKeywordsService;

    @Autowired
    private ICpcTargetingService cpcTargetingService;

    @Autowired
    private ICpcKeywordsService cpcKeywordsService;

    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;
    @Autowired
    private IAmazonAdCampaignAllDao amazonAdCampaignAllDao;
    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;
    @Autowired
    private IAmazonSbAdGroupDao amazonSbAdGroupDao;
    @Autowired
    private ICpcSpCampaignChangeHistoryService cpcSpCampaignChangeHistoryService;

    @Autowired
    private ICpcSbCampaignChangeHistoryService cpcSbCampaignChangeHistoryService;

    @Autowired
    private IAmazonSdAdTargetingDao sdAdTargetingDao;
    @Autowired
    private IAmazonSbAdTargetingDao sbAdTargetingDao;
    @Autowired
    private ICpcSbCampaignService sbCampaignService;
    @Autowired
    private IAmazonSdAdGroupDao amazonSdAdGroupDao;
    @Autowired
    private IAmazonAdProductDao spAdProductDao;
    @Autowired
    private IAmazonSdAdProductDao sdAdProductDao;
    @Autowired
    private IAmazonSbAdKeywordDao sbAdKeywordDao;
    @Autowired
    private ICpcSbQueryKeywordReportService sbQueryKeywordReportService;
    @Autowired
    private IAdManageOperationLogService manageOperationLogService;

    @Autowired
    private IAmazonAdPortfolioService portfolioService;
    @Autowired
    private IAmazonAdPortfolioDao amazonAdPortfolioDao;

    @Autowired
    private ICpcPortfolioService cpcPortfolioService;

    @Autowired
    private ICpcAsinService cpcAsinService;

    @Autowired
    private IAmazonAdCampaignAllService amazonAdCampaignAllService;

    @Autowired
    private IAmazonAdCampaignHourReportService amazonAdCampaignHourReportService;
    @Autowired
    private ICpcSbAdsService cpcSbAdsService;

    @Autowired
    private IAmazonAdPlacementHourReportService amazonAdPlacementHourReportService;

    @Resource
    private CpcPageIdsHandler cpcPageIdsHandler;

    @Autowired
    private IAmazonAdGroupHourReportService amazonAdGroupHourReportService;
    @Autowired
    private DynamicRefreshConfiguration dynamicRefreshConfiguration;

    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;

    @Autowired
    private IAdProductRightService adProductRightService;

    @Autowired
    private IAmazonAdCampaignAllDorisDao amazonAdCampaignAllDorisDao;
    @Resource
    private IAmazonAdGroupDorisDao amazonAdGroupDorisDao;


    /**
     * 所有广告活动和广告组(功能模块过滤)
     *
     * @param puid
     * @param shopId
     * @param type
     * @param campaignType
     * @param groupType
     * @param modular
     * @return
     */
    @Override
    public List<AllTypeCampAndGroupResponse.CampAndGroupVo> getAllTypeCampAndGroup(Integer puid, Integer shopId, String type, String campaignType, String groupType, String modular) {
        return getAllTypeCampAndGroup(puid, shopId, type, campaignType, groupType, modular, false);
    }

    /**
     * 所有广告活动和广告组(功能模块过滤)
     *
     * @param puid
     * @param shopId
     * @param type
     * @param campaignType
     * @param groupType
     * @param modular
     * @return
     */
    @Override
    public List<AllTypeCampAndGroupResponse.CampAndGroupVo> getAllTypeCampAndGroupProductRight(Integer puid, Integer shopId, String type, String campaignType, String groupType, String modular) {
        return getAllTypeCampAndGroup(puid, shopId, type, campaignType, groupType, modular, true);
    }


    /**
     * 所有广告活动和广告组(功能模块过滤)
     *
     * @param puid
     * @param shopId
     * @param type
     * @param campaignType
     * @param groupType
     * @param modular
     * @return
     */
    private List<AllTypeCampAndGroupResponse.CampAndGroupVo> getAllTypeCampAndGroup(Integer puid, Integer shopId, String type, String campaignType, String groupType, String modular, boolean isProductRight) {

        List<AllTypeCampAndGroupResponse.CampAndGroupVo> campAndGroupVos = Lists.newArrayList();
        //分多次查询,组合一起
        if (AdModularEnum.keywordTarget.getCode().equalsIgnoreCase(modular)) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode());
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.neKeyword.getCode().equalsIgnoreCase(modular)) {
            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode());
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.productTarget.getCode().equalsIgnoreCase(modular)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos1 = Lists.newArrayList();
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos2 = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode());
                spCampaignAndGroupVos2 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null);
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode());
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null);
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos1, spCampaignAndGroupVos2, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.neTarget.getCode().equalsIgnoreCase(modular)) {
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode());
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode());
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, TargetingEnum.Product.getTargetingType(), null);
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.adProduct.getCode().equalsIgnoreCase(modular)) {
            // 5.广告：  Sp sd
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null);
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null);
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.queryWord.getCode().equalsIgnoreCase(modular)) {
            // 6.搜索词： 关键词产生：  SP手动（关键词类型）--  (全部)
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null);
        } else if (AdModularEnum.queryTarget.getCode().equalsIgnoreCase(modular)) {
            // 7.搜索词： 商品投放产生：  SP自动、SP手动（商品投放类型）
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> campAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode());
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> campAndGroupVos2 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null);
            campAndGroupVos = Stream.of(campAndGroupVos1, campAndGroupVos2).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.campaignNeKeyword.getCode().equalsIgnoreCase(modular)) {
            // 8.活动否定关键词:  SP（全部）
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null);
        } else if (AdModularEnum.campaignNeTarget.getCode().equalsIgnoreCase(modular)) {
            // 9.活动否定商品：  SP自动
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null);
        } else if (AdModularEnum.group.getCode().equalsIgnoreCase(modular)) {
            // 10.广告组  sp sd
            List<AllTypeCampAndGroupResponse.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null);
            }

            List<AllTypeCampAndGroupResponse.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null);
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else {
            campAndGroupVos = getCampAndGroupVos(puid, shopId, type, campaignType, groupType);
        }

        Pair<Boolean, List<String>> productRightCampaignIdsFromGrpc = null;
        if (isProductRight) {
            productRightCampaignIdsFromGrpc = adProductRightService.getProductRightCampaignIdsFromGrpc(puid, shopId, CampaignTypeEnum.valueOf(type));
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                return  Lists.newArrayList();
            }
        }

        if (isProductRight && CollectionUtils.isNotEmpty(campAndGroupVos)) {
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isNotEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                //先将广告活动id 转成set 提高遍历判断效率
                HashSet<String> campaignIds = Sets.newHashSet(productRightCampaignIdsFromGrpc.getValue());
                campAndGroupVos = campAndGroupVos.stream().filter(e -> campaignIds.contains(e.getCampaignId())).collect(Collectors.toList());
            }
        }

        return campAndGroupVos;
    }

    private List<AllTypeCampAndGroupResponse.CampAndGroupVo> getCampAndGroupVos(Integer puid, Integer shopId, String type, String campaignType, String groupType) {
        //查询所有类型的广告活动(sp sb sd)
        List<AdCampaignOptionVo> allCampaigns = amazonAdCampaignDao.getCampaignsByType(puid, shopId, type, campaignType, groupType, null, null, null);
        List<AdGroupOptionVo> allGroups = null;
        //查询广告活动对应广告组
        Map<String, List<AdGroupOptionVo>> camMap = null;
        //类型为sb 则不查询广告组 (sb无广告组概念)
        //2022-11-22 sb 结构调整已经有广告组概念
        if (StringUtils.isBlank(type) || type.equalsIgnoreCase(Constants.SP) || type.equalsIgnoreCase(Constants.SD) || type.equalsIgnoreCase(Constants.SB)) {
            List<String> typeList = StringUtils.isBlank(type) ? null : Stream.of(type).collect(Collectors.toList());
            allGroups = amazonAdGroupDao.getAllGroupsByType(puid, shopId, typeList, groupType, null, null);
            camMap = allGroups.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdGroupOptionVo::getCampaignId));
        }
        //组装数据
        List<AllTypeCampAndGroupResponse.CampAndGroupVo> campAndGroupVos = new ArrayList<>();
        Map<String, List<AdGroupOptionVo>> finalCamMap = camMap;
        allCampaigns.stream().filter(Objects::nonNull).forEach(item -> {
            AllTypeCampAndGroupResponse.CampAndGroupVo.Builder builder = AllTypeCampAndGroupResponse.CampAndGroupVo.newBuilder()
                    .setCampaignId(item.getCampaignId())
                    .setCampaignName(item.getName())
                    .setState(item.getState())
                    .setType(item.getType());
            if (item.getCostType() != null) {
                builder.setCostType(item.getCostType());
            }
            if (item.getTargetingType() != null) {
                builder.setTargetingType(item.getTargetingType());
            }
            if (MapUtils.isNotEmpty(finalCamMap) && finalCamMap.containsKey(item.getCampaignId())) {
                List<AdGroupOptionVo> adGroupOptionVos = finalCamMap.get(item.getCampaignId());
                if (CollectionUtils.isNotEmpty(adGroupOptionVos)) {
                    List<AllTypeCampAndGroupResponse.CampAndGroupVo.AdGroupVos> groupRpcVos = Lists.newArrayList();
                    adGroupOptionVos.stream().forEach(group -> {
                        AllTypeCampAndGroupResponse.CampAndGroupVo.AdGroupVos.Builder groupVoBuilder = AllTypeCampAndGroupResponse.CampAndGroupVo.AdGroupVos.newBuilder();
                        groupVoBuilder.setGroupId(group.getGroupId());
                        if (StringUtils.isNotBlank(group.getName())) {
                            groupVoBuilder.setName(group.getName());
                        }
                        if (StringUtils.isNotBlank(group.getState())) {
                            groupVoBuilder.setState(group.getState());
                        }
                        if ((Constants.SP.equalsIgnoreCase(item.getType()) || Constants.SD.equalsIgnoreCase(item.getType()))
                                && StringUtils.isNotBlank(item.getTargetingType())) {
                            groupVoBuilder.setTargetingType(item.getTargetingType());
                        }
                        if (Constants.SB.equalsIgnoreCase(item.getType()) && StringUtils.isNotBlank(group.getTargetingType())) {
                            groupVoBuilder.setTargetingType(group.getTargetingType());
                        }
                        groupRpcVos.add(groupVoBuilder.build());
                    });
                    builder.addAllGroupVos(groupRpcVos);
                }
            }
            if (StringUtils.isNotBlank(item.getBrandEntityId())) {
                builder.setBrandEntityId(item.getBrandEntityId());
            }
            campAndGroupVos.add(builder.build());
        });
        return campAndGroupVos;
    }

    /**
     * 所有广告活动数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.CAMPAIGN)
    @Override
    public AllCampaignDataResponse.CampaignHomeVo getAllCampaignData(Integer puid, CampaignPageParam param) {
        //处理灰度逻辑，灰度用户走新查询逻辑，其他用户走旧逻辑
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getCampaignPageWhitePuidSet();
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPageCampaign())) {
            log.info("doris begin select");
            this.handlePercentParam(param);
            return cpcCampaignService.getDorisCampaignData(puid, param);
        }
        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            this.handlePercentParam(param);
            return cpcCampaignService.getCampaignListPage(puid, param);
        } else {
            return cpcCampaignService.getOldCampaignData(puid, param);
        }
    }

    /**
     * 所有广告活动数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllCampaignDataResponse.CampaignHomeVo getAllWxCampaignData(Integer puid, CampaignPageParam param) {
//        Set<String> whitePuidSet = dynamicRefreshConfiguration.getCampaignPageWhitePuidSet();
//        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
//            return cpcCampaignService.getCampaignListPage(puid, param);
//        } else {
        return cpcCampaignService.getOldCampaignData(puid, param);
//        }
    }

    /**
     * 广告活动-汇总数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.CAMPAIGN)
    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getAllCampaignAggregateData(Integer puid, CampaignPageParam param) {
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getCampaignPageWhitePuidSet();
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPageCampaign())) {
            log.info("doris begin select");
            return cpcCampaignService.getDorisAllCampaignAggregateData(puid, param);
        }
        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            log.info("getAllCampaignAggregateData param {}", JSON.toJSONString(param));
            return cpcCampaignService.getAllCampaignAggregateData(puid, param);//灰度用户走新方法
        } else {
            return cpcCampaignService.getOldAllCampaignAggregateData(puid, param);
        }
    }

    /**
     * 广告活动-汇总数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllCampaignAggregateDataResponse.CampaignHomeVo getAllWxCampaignAggregateData(Integer puid, CampaignPageParam param) {
//        Set<String> whitePuidSet = dynamicRefreshConfiguration.getCampaignPageWhitePuidSet();
//        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
//            return cpcCampaignService.getAllWxCampaignAggregateData(puid, param);//灰度用户走新方法
//        } else {
        return cpcCampaignService.getOldAllWxCampaignAggregateData(puid, param);
//        }
    }


    /**
     * 所有广告位数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.PLACEMENT)
    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression = "#param.adType",
            strategy = PermissionFilterStrategy.FILTER
    )
    public AllPlacementDataResponse.AdPlacementHomeVo getAllPlacementData(Integer puid, PlacementPageParam param) {
        Set<String> whiteDorisPuidSet = dynamicRefreshConfiguration.getDorisPagePlacement();
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, whiteDorisPuidSet) &&
                !PredicateEnum.SITEAMAZONBUSINESS.value().equals(param.getCampaignSite())) {
            log.info("doris begin select");
            handlePercentParam(param);
            return cpcCampaignService.getAllDorisPlacementData(puid, param);
        }
        return cpcCampaignService.getAllPlacementData(puid, param);
    }

    /**
     * 广告位指标数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.PLACEMENT)
    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression = "#param.adType",
            strategy = PermissionFilterStrategy.FILTER
    )
    public AllPlacementAggregateDataResponse.AdPlacementHomeVo getAllPlacementAggregateData(Integer puid, PlacementPageParam param) {

        Set<String> whiteDorisPuidSet = dynamicRefreshConfiguration.getDorisPagePlacement();
        // 企业购走mysql查询
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, whiteDorisPuidSet) &&
                !PredicateEnum.SITEAMAZONBUSINESS.value().equals(param.getCampaignSite())) {
            log.info("doris begin select");
            return cpcCampaignService.getAllDorisPlacementAggregateData(puid, param);
        }
        return cpcCampaignService.getAllPlacementAggregateData(puid, param);
    }

    /**
     * 所有广告组合数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.PORTFOLIO)
    @Override
    public AllPortfolioDataListResponse.PortfolioHomeVo getAllPortfolioDataList(Integer puid, PortfolioPageParam param) {
        return cpcPortfolioService.getAllPortfolioDataList(puid, param);
    }

    /**
     * 广告组合-汇总、指标数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.PORTFOLIO)
    @Override
    public AllPortfolioAggregateDataListResponse.PortfolioHomeVo getAllPortfolioAggregateDataList(Integer puid, PortfolioPageParam param) {
        if (dynamicRefreshConfiguration.verifyDorisPage(puid, dynamicRefreshConfiguration.getDorisPagePortfolio())) {
            return cpcPortfolioService.getAllDorisPortfolioAggregateDataList(puid, param);
        } else {
            return cpcPortfolioService.getAllPortfolioAggregateDataList(puid, param);
        }
    }

    /**
     * 所有已购买Asin数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllAsinDataResponse.AsinHomeVo getAllAsinData(Integer puid, AdAsinPageParam param) {
        return cpcAsinService.getAllAsinData(puid, param);
    }


    /**
     * 所有已购买Asin汇总数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllAsinAggregateDataResponse.AsinHomeVo getAllAsinAggregateData(Integer puid, AdAsinPageParam param) {
        return cpcAsinService.getAllAsinAggregateData(puid, param);
    }


    /**
     * 已购买Asin关联数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllAssociationAsinDataResponse.AsinHomeVo getAllAssociationAsinData(Integer puid, AdAsinPageParam param) {
        return cpcAsinService.getAllAssociationAsinData(puid, param);
    }

    @Override
    public AdGroupKeywordRankResponse getAdGroupKeywordRank(Integer puid, AdGroupKeywordRankParam adGroupKeywordRankParam) {
        return cpcQueryKeywordReportService.getAdGroupKeywordRank(puid, adGroupKeywordRankParam);
    }


    /**
     * 所有广告组数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.GROUP)
    @Override
    public AllGroupDataResponse.GroupHomeVo getAllGroupData(Integer puid, GroupPageParam param) {
        if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageGroup();
            if (dynamicRefreshConfiguration.verifyDorisPage(puid, whitePuidSet)) {
                log.info("begin doris select");
                handleGroupPercentParam(param);
                return cpcAdGroupService.getSpGroupDorisData(puid, param);
            }
        }
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getGroupPageWhitePuidSet();
        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            handleGroupPercentParam(param);
            return cpcAdGroupService.getNewAllGroupData(puid, param);
        } else {
            return cpcAdGroupService.getAllGroupData(puid, param);
        }
    }

    /**
     * 所有广告组数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllGroupDataResponse.GroupHomeVo getAllWxGroupData(Integer puid, GroupPageParam param) {
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getGroupPageWhitePuidSet();
//        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
//            return cpcAdGroupService.getNewAllGroupData(puid, param);
//        } else {
        return cpcAdGroupService.getAllWxGroupData(puid, param);
//        }
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.GROUP)
    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getAllGroupAggregateData(Integer puid, GroupPageParam param) {
        if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageGroup();
            if (dynamicRefreshConfiguration.verifyDorisPage(puid, whitePuidSet)) {
                log.info("begin doris select");
                this.handleGroupPercentParam(param);
                return cpcAdGroupService.getSpGroupDorisAggregateData(puid, param);
            }
        }
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getGroupPageWhitePuidSet();
        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            return cpcAdGroupService.getAllGroupAggregateDataNew(puid, param);
        } else {
            this.handleGroupPercentParam(param);
            return cpcAdGroupService.getAllGroupAggregateData(puid, param);
        }
    }

    @Override
    public AllGroupAggregateDataResponse.GroupHomeVo getAllWxGroupAggregateData(Integer puid, GroupPageParam param) {
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getGroupPageWhitePuidSet();
//        if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
//            return cpcAdGroupService.getWxAllGroupAggregateDataNew(puid, param);
//        } else {
        return cpcAdGroupService.getOldWxAllGroupAggregateData(puid, param);
//        }
    }

    /**
     * 所有广告产品数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.AD_PRODUCT)
    @Override
    public AllProductDataResponse.AdProductHomeVo getAllProductData(Integer puid, AdProductPageParam param) {
        // 判断sp 切白名单
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageAdProduct();
            if (dynamicRefreshConfiguration.verifyDorisPage(puid, whitePuidSet)) {
                this.handlePercentParam(param);
                return cpcProductService.getAllSpProductDorisData(puid, param);
            }
        }
        return cpcProductService.getAllProductData(puid, param);
    }

    private void handlePercentParam(AdProductPageParam campaignPageParam) {
        campaignPageParam.setClickRateMin(campaignPageParam.getClickRateMin() != null ? MathUtil.divide(campaignPageParam.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setClickRateMax(campaignPageParam.getClickRateMax() != null ? MathUtil.divide(campaignPageParam.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMin(campaignPageParam.getAcosMin() != null ? MathUtil.divide(campaignPageParam.getAcosMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMax(campaignPageParam.getAcosMax() != null ? MathUtil.divide(campaignPageParam.getAcosMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMin(campaignPageParam.getSalesConversionRateMin() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMax(campaignPageParam.getSalesConversionRateMax() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMin(campaignPageParam.getAcotsMin() != null ? MathUtil.divide(campaignPageParam.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMax(campaignPageParam.getAcotsMax() != null ? MathUtil.divide(campaignPageParam.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMin(campaignPageParam.getAsotsMin() != null ? MathUtil.divide(campaignPageParam.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMax(campaignPageParam.getAsotsMax() != null ? MathUtil.divide(campaignPageParam.getAsotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMin(campaignPageParam.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMax(campaignPageParam.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMin(campaignPageParam.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMax(campaignPageParam.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.AD_PRODUCT)
    @Override
    public AllProductAggregateDataResponse.AdProductHomeVo getAllProductAggregateData(Integer puid, AdProductPageParam param) {
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageAdProduct();
            if (dynamicRefreshConfiguration.verifyDorisPage(puid, whitePuidSet)) {
//                this.handlePercentParam(param);
                return cpcProductService.getAllSpProductDorisAggregateData(puid, param);
            }
        }
        return cpcProductService.getAllProductAggregateData(puid, param);
    }

    /**
     * 所有搜索词(投放)数据
     *
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.QUERY_TARGET)
    @Override
    public AllQueryTargetDataResponse.AdQueryTargetingHomeVo getAllQueryTargetData(Integer puid, CpcQueryWordDto dto, Page page) {
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
        if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, whitePuidSet)) {
            this.queryWordPageParamDateFormat(dto);
            return cpcQueryTargetingReportService.getDorisAllQueryTargetData(puid, dto, page);
        }
        return cpcQueryTargetingReportService.getAllQueryTargetData(puid, dto, page);
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.QUERY_TARGET)
    @Override
    public AllQueryTargetAggregateDataResponse.AdQueryTargetingHomeVo getAllQueryTargetAggregateData(Integer puid, CpcQueryWordDto dto) {
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
        if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, whitePuidSet)) {
            this.queryWordPageParamDateFormat(dto);
            return cpcQueryTargetingReportService.getDorisAllQueryTargetAggregateData(puid, dto);
        }
        return cpcQueryTargetingReportService.getAllQueryTargetAggregateData(puid, dto);
    }

    /**
     * 所有搜索词(关键词)数据
     *
     * @param puid
     * @param dto
     * @param page
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.QUERY_KEYWORD)
    @Override
    public AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page) {
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
        //doris 大小写敏感， 搜索词报告都是小写，如果前端传入的是大写统一转小写
        if (StringUtils.isNotBlank(dto.getSearchValue())) {
            dto.setSearchValue(dto.getSearchValue().toLowerCase());
        }
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());

        if ("sb".equalsIgnoreCase(dto.getType())) {
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, whitePuidSet)) {
                this.queryWordPageParamDateFormat(dto);
                dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, false));
                return sbQueryKeywordReportService.getDorisAllQueryWordData(puid, dto, page);
            } else {
                CpcPageWordRootHelper.clearUnsupportedOrderField(dto);
                return sbQueryKeywordReportService.getAllQueryWordData(puid, dto, page);
            }
        } else {
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, whitePuidSet)) {
                this.queryWordPageParamDateFormat(dto);
                dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, false));
                return cpcQueryKeywordReportService.getDorisAllQueryWordData(puid, dto, page);
            } else {
                CpcPageWordRootHelper.clearUnsupportedOrderField(dto);
                return cpcQueryKeywordReportService.getAllQueryWordData(puid, dto, page);
            }
        }
    }

    @Override
    public AllSearchTermDataResponse.AllSearchTermData allSearchTermList(Integer puid, CpcQueryWordDto dto, Page page) {
        //doris 大小写敏感， 搜索词报告都是小写，如果前端传入的是大写统一转小写
        if (StringUtils.isNotBlank(dto.getSearchValue())) {
            dto.setSearchValue(dto.getSearchValue().toLowerCase());
        }
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(),
                dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, false)
                && !StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN));
        return cpcQueryKeywordReportService.getAllSearchTermList(puid, dto, page);
    }

    @Override
    public List<SearchTermSourceTargetDetailResponse.SourceTargetDetail> searchTermSourceTargetDetail(Integer puid, CpcQueryWordDto dto) {
        if (StringUtils.isNotBlank(dto.getSearchValue())) {
            dto.setSearchValue(dto.getSearchValue().toLowerCase());
        }
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(),
                dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, false));
        return cpcQueryKeywordReportService.searchTermSourceTargetDetail(puid, dto);
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.QUERY_KEYWORD)
    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {

        //doris 大小写敏感， 搜索词报告都是小写，如果前端传入的是大写统一转小写
        if (StringUtils.isNotBlank(dto.getSearchValue())) {
            dto.setSearchValue(dto.getSearchValue().toLowerCase());
        }
        if (CampaignTypeEnum.sb.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            if (Constants.KEYWORDS_RELATED_TO_YOUR_BRAND_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_brand);
            } else if (Constants.KEYWORDS_RELATED_TO_YOUR_LANDING_PAGES_CN.equals(dto.getSearchValue())) {
                dto.setSearchValue(Constants.keywords_related_to_your_landing_pages);
            }
        }

        if (CampaignTypeEnum.sp.getCampaignType().equalsIgnoreCase(dto.getType()) && StringUtils.isNotBlank(dto.getSearchValue())) {
            SpKeywordGroupValueEnum keywordGroupValueEnumByTextCn = SpKeywordGroupValueEnum.getKeywordGroupValueEnumByTextCn(dto.getSearchValue());
            if (keywordGroupValueEnumByTextCn != null) {
                dto.setSearchValue(keywordGroupValueEnumByTextCn.getKeywordText());
            }
        }
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getDorisPageQueryWordRootWhitePuidSet();
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());

        if ("sb".equalsIgnoreCase(dto.getType())) {
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, whitePuidSet)) {
                this.queryWordPageParamDateFormat(dto);
                dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, true));
                return sbQueryKeywordReportService.getDorisAllQueryWordAggregateData(puid, dto);
            } else {
                CpcPageWordRootHelper.clearUnsupportedOrderField(dto);
                return sbQueryKeywordReportService.getAllQueryWordAggregateData(puid, dto);
            }
        } else {
            if (dynamicRefreshConfiguration.verifyDorisPageByPuid(puid, whitePuidSet)) {
                this.queryWordPageParamDateFormat(dto);
                dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, true));
                return cpcQueryKeywordReportService.getDorisAllQueryWordAggregateData(puid, dto);
            } else {
                CpcPageWordRootHelper.clearUnsupportedOrderField(dto);
                return cpcQueryKeywordReportService.getAllQueryWordAggregateData(puid, dto);
            }
        }
    }

    @Override
    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo allSearchTermAggregateData(Integer puid, CpcQueryWordDto dto) {
        //doris 大小写敏感， 搜索词报告都是小写，如果前端传入的是大写统一转小写
        if (StringUtils.isNotBlank(dto.getSearchValue())) {
            dto.setSearchValue(dto.getSearchValue().toLowerCase());
        }
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(),
                dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        dto.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(dto, false)
                && !StringUtils.equalsIgnoreCase(dto.getSearchTermType(), Constants.SEARCH_TERM_TYPE_ASIN));
        return cpcQueryKeywordReportService.getDorisAllSearchTermAggregateData(puid, dto);
    }

    /**
     * 所有否定关键词分页(活动层)数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public Page<CampaignNeKeywordsPageRpcVo> neKeywordsPageList(Integer puid, CampaignNeKeywordsPageParam param) {
        return cpcSpCampaignService.neKeywordsPageList(puid, param);
    }

    /**
     * 所有活动否定商品数据
     *
     * @param param
     * @return
     */
    @Override
    public Page<CampaignNeTargetingSpRpcVo> neTargetingPageList(CampaignNeTargetingSpParam param) {
        return cpcSpCampaignService.neTargetingPageList(param);
    }


    /**
     * 所有广告组否定关键词数据
     *
     * @param param
     * @return
     */
    public Page<NeKeywordsPageRpcVo> allNekeywordPageList(NeKeywordsPageParam param) {
        return cpcNeKeywordsService.allNekeywordPageList(param);
    }

    /**
     * 所有否定投放数据
     *
     * @param param
     * @return
     */
    public Page<NeTargetingPageRpcVo> getAllNeTargeting(NeTargetingPageParam param) {
        return cpcTargetingService.getAllNeTargetingPageList(param.getPuid(), param);
    }


    /**
     * 所有关键词数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.KEYWORD)
    @Override
    public AllKeyWordDataResponse.AdkeywordHomeRpcVo getAllKeyWordData(Integer puid, KeywordsPageParam param) {
        Set<String> targetDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTarget();
        Set<String> keywordDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageKeyword();
        Set<String> targetWhitePuidSet = dynamicRefreshConfiguration.getAdmanagePageTargetWhitePuidSet();
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getAdmanagePageWhitePuidSet();
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        if ((dynamicRefreshConfiguration.verifyDorisPage(puid, targetDorisWhitePuidSet) && Constants.SP.equalsIgnoreCase(param.getType())) ||
                (dynamicRefreshConfiguration.verifyDorisPage(puid, keywordDorisWhitePuidSet) && Constants.SB.equalsIgnoreCase(param.getType()))) {
            this.keywordsPageParamDateFormat(param);
            param.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(param, false));
            // 后续sb、sp查询都走该方法从doris中查询数据，后面三个方法废弃不走。
            return cpcKeywordsService.getDorisAllKeyWordData(puid, param);
        } else if (CollectionUtils.isNotEmpty(targetWhitePuidSet) && (targetWhitePuidSet.contains("all") || targetWhitePuidSet.contains(puid.toString()))) {
            CpcPageWordRootHelper.clearUnsupportedOrderField(param);
            return cpcKeywordsService.getNewAllKeyWordData(puid, param);
        } else if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            CpcPageWordRootHelper.clearUnsupportedOrderField(param);
            return cpcKeywordsService.getAllKeyWordData(puid, param);
        } else {
            CpcPageWordRootHelper.clearUnsupportedOrderField(param);
            return cpcKeywordsService.getOldAllKeyWordData(puid, param);
        }
    }

    /**
     * 导出所有关键词数据（兼容新旧关键词列表页性能优化）
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<KeywordsPageVo> getExportAllKeyWordData(ShopAuth shopAuth, Integer puid, KeywordsPageParam param) {
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            Set<String> keywordDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageKeyword();
            if (dynamicRefreshConfiguration.verifyDorisPage(puid, keywordDorisWhitePuidSet)) {
                // 新版本导出从Doris取值
                return cpcKeywordsService.getDorisSpKeywordVoList(shopAuth, param.getPuid(), param, true).getRows();
            } else {
                // 老版本导出从mysql取值
                return cpcKeywordsService.getSpKeywordVoList(shopAuth, param.getPuid(), param, null, true);
            }
        } else {
            Set<String> keywordDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageKeyword();
            if (dynamicRefreshConfiguration.verifyDorisPage(puid, keywordDorisWhitePuidSet)) {
                // 新版本导出从doris取值
                return cpcKeywordsService.getDorisSbKeywordVoList(shopAuth, param.getPuid(), param, true).getRows();
            } else {
                // 老版本导出从mysql取值
                return cpcKeywordsService.getSbKeywordVoList(shopAuth, param.getPuid(), param, null, true);
            }
        }
    }

    /**
     * 所有关键词数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllKeyWordDataResponse.AdkeywordHomeRpcVo getAllWxKeyWordData(Integer puid, KeywordsPageParam param) {
        return cpcKeywordsService.getOldAllWxKeyWordData(puid, param);
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.KEYWORD)
    @Override
    public AllKeyWordAggregateDataResponse.AdkeywordHomeRpcVo getAllKeyWordAggregateData(Integer puid, KeywordsPageParam param) {
        Set<String> targetDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTarget();
        Set<String> keywordDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageKeyword();
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getAdmanagePageWhitePuidSet();
        boolean supportAbaRankOrder = GrayUtil.isHit(puid, dynamicRefreshConfiguration.getSupportAbaRankOrderWhiteList(), dynamicRefreshConfiguration.getSupportAbaRankPercentage());
        if ((dynamicRefreshConfiguration.verifyDorisPage(puid, targetDorisWhitePuidSet) && Constants.SP.equalsIgnoreCase(param.getType())) ||
                (dynamicRefreshConfiguration.verifyDorisPage(puid, keywordDorisWhitePuidSet) && Constants.SB.equalsIgnoreCase(param.getType()))) {
            this.keywordsPageParamDateFormat(param);
            param.setQueryJoinSearchTermsRank(supportAbaRankOrder && CpcPageWordRootHelper.queryAboutAbaRank(param, true));
            // 后续sb、sp查询都走该方法从doris中查询数据，后面两个方法废弃不走。
            return cpcKeywordsService.getDorisAllKeyWordAggregateData(puid, param);
        } else if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            CpcPageWordRootHelper.clearUnsupportedOrderField(param);
            return cpcKeywordsService.getAllKeyWordAggregateData(puid, param);
        } else {
            this.keywordAggregateParamUpdate(param);
            CpcPageWordRootHelper.clearUnsupportedOrderField(param);
            return cpcKeywordsService.getOldAllKeyWordAggregateData(puid, param);
        }
    }

    @Override
    public AllKeyWordAggregateDataResponse.AdkeywordHomeRpcVo getAllWxKeyWordAggregateData(Integer puid, KeywordsPageParam param) {
        this.keywordAggregateParamUpdate(param);
        return cpcKeywordsService.getOldAllWxKeyWordAggregateData(puid, param);
    }

    /**
     * 所有投放数据
     *
     * @param puid
     * @param param
     * @return
     */
    @SaveMonitor(type = MonitorTypeEnum.LIST, tableName = MonitorPageFunctionEnum.TARGET)
    @Override
    public AllTargetDataResponse.AdTargetingHomeRpcVo getAllTargetData(Integer puid, TargetingPageParam param) {
        Set<String> targetDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTarget();
        Set<String> targetSbDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTargetSb();
        Set<String> targetSdDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTargetSd();
        Set<String> targetWhitePuidSet = dynamicRefreshConfiguration.getAdmanagePageTargetWhitePuidSet();
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getAdmanagePageWhitePuidSet();
        if ((dynamicRefreshConfiguration.verifyDorisPage(puid, targetDorisWhitePuidSet) && Constants.SP.equalsIgnoreCase(param.getType())) ||
                (dynamicRefreshConfiguration.verifyDorisPage(puid, targetSbDorisWhitePuidSet) && Constants.SB.equalsIgnoreCase(param.getType())) ||
                (dynamicRefreshConfiguration.verifyDorisPage(puid, targetSdDorisWhitePuidSet) && Constants.SD.equalsIgnoreCase(param.getType()))) {
            this.targetingPageParamDateFormat(param);
            return cpcTargetingService.getDorisAllTargetData(puid, param);
        } else if (CollectionUtils.isNotEmpty(targetWhitePuidSet) && (targetWhitePuidSet.contains("all") || targetWhitePuidSet.contains(puid.toString()))) {
            return cpcTargetingService.getNewAllTargetData(puid, param, true);
        } else if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            return cpcTargetingService.getAllTargetData(puid, param, true);
        } else {
            this.targetDataParamUpdate(param);
            return cpcTargetingService.getOldAllTargetData(puid, param);
        }
    }

    /**
     * 导出所有投放数据（兼容新旧投放列表页性能优化）
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<TargetingPageVo> getExportAllTargetData(ShopAuth shopAuth, Integer puid, TargetingPageParam param) {
        // 老版本导出
        if (Constants.SP.equalsIgnoreCase(param.getType())) {
            this.targetingPageParamDateFormat(param);
            // 新版本导出切Doris
            return cpcTargetingService.getDorisSpTargetVoList(shopAuth, param.getPuid(), param, true).getRows();
//            return cpcTargetingService.getSpTargetVoList(shopAuth, param.getPuid(), param, null, true);
        } else if (Constants.SB.equalsIgnoreCase(param.getType())) {
            Set<String> targetSbDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTargetSb();
            if ((dynamicRefreshConfiguration.verifyDorisPage(puid, targetSbDorisWhitePuidSet) && Constants.SB.equalsIgnoreCase(param.getType()))) {
                return cpcTargetingService.getDorisSbTargetVoList(shopAuth, param.getPuid(), param, true).getRows();
            } else {
                return cpcTargetingService.getSbTargetVoList(shopAuth, param.getPuid(), param, null, true);
            }
        } else {
            Set<String> targetSdDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTargetSd();
            if ((dynamicRefreshConfiguration.verifyDorisPage(puid, targetSdDorisWhitePuidSet) && Constants.SD.equalsIgnoreCase(param.getType()))) {
                return cpcTargetingService.getDorisSdTargetVoList(shopAuth, param.getPuid(), param, true).getRows();
            }
            return cpcTargetingService.getSdTargetVoList(shopAuth, param.getPuid(), param, null, true);
        }
    }

    /**
     * 所有投放数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllTargetDataResponse.AdTargetingHomeRpcVo getAllWxTargetData(Integer puid, TargetingPageParam param) {
        this.targetDataParamUpdate(param);
        return cpcTargetingService.getOldAllWxTargetData(puid, param);
    }

    @SaveMonitor(type = MonitorTypeEnum.SUM, tableName = MonitorPageFunctionEnum.TARGET)
    @Override
    public AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getAllTargetAggregateData(Integer puid, TargetingPageParam param) {
        Set<String> targetDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTarget();
        Set<String> targetSbDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTargetSb();
        Set<String> targetSdDorisWhitePuidSet = dynamicRefreshConfiguration.getDorisPageTargetSd();
        Set<String> whitePuidSet = dynamicRefreshConfiguration.getAdmanagePageWhitePuidSet();
        if ((dynamicRefreshConfiguration.verifyDorisPage(puid, targetDorisWhitePuidSet) && Constants.SP.equalsIgnoreCase(param.getType())) ||
                (dynamicRefreshConfiguration.verifyDorisPage(puid, targetSbDorisWhitePuidSet) && Constants.SB.equalsIgnoreCase(param.getType())) ||
                (dynamicRefreshConfiguration.verifyDorisPage(puid, targetSdDorisWhitePuidSet) && Constants.SD.equalsIgnoreCase(param.getType()))) {
            this.targetingPageParamDateFormat(param);
            return cpcTargetingService.getDorisAllTargetAggregateData(puid, param);
        } else if (CollectionUtils.isNotEmpty(whitePuidSet) && (whitePuidSet.contains("all") || whitePuidSet.contains(puid.toString()))) {
            return cpcTargetingService.getAllTargetAggregateData(puid, param, true);
        } else {
            this.targetAggregateParamUpdate(param);
            return cpcTargetingService.getOldAllTargetAggregateData(puid, param);
        }
    }

    @Override
    public AllTargetAggregateDataResponse.AdTargetingHomeRpcVo getAllWxTargetAggregateData(Integer puid, TargetingPageParam param) {
        this.targetAggregateParamUpdate(param);
        return cpcTargetingService.getOldAllWxTargetAggregateData(puid, param);
    }

    @Override
    public List<CampaignBeyondBudgetvo> getCampaignBeyondBudget(AdCampaignChangeHistoryParam param) {

        if (Constants.SP.equals(param.getType())) {
            return cpcSpCampaignChangeHistoryService.getAllHistoryByCampaignId(param);
        }
        if (Constants.SB.equals(param.getType())) {
            return cpcSbCampaignChangeHistoryService.getAllHistoryByCampaignId(param);
        }
        return null;
    }

    @Override
    public List<CampaignBeyondBudgetHourVo> getCampaignBeyondBudgetHour(AdCampaignChangeHistoryParam param) {

        if (Constants.SP.equals(param.getType())) {
            return cpcSpCampaignChangeHistoryService.getAllHistoryByCampaignIdHour(param);
        }
        if (Constants.SB.equals(param.getType())) {
            return cpcSbCampaignChangeHistoryService.getAllHistoryByCampaignIdHour(param);
        }
        return null;
    }

    @Override
    public CampaignBeyondBudgetPageResponse getCampaignBeyondBudgetPage(AdCampaignChangeHistoryParam param) {

        if (Constants.SP.equals(param.getType())) {
            return cpcSpCampaignChangeHistoryService.getAllHistoryByCampaignIdPage(param);
        }
        if (Constants.SB.equals(param.getType())) {
            return cpcSbCampaignChangeHistoryService.getAllHistoryByCampaignIdPage(param);
        }
        CampaignBeyondBudgetPageResponse.Builder builder = CampaignBeyondBudgetPageResponse.newBuilder();
        return builder.setCode(Int32Value.of(Result.SUCCESS)).build();
    }

    @Override
    public List<String> getAsinByCampaignId(Integer puid, Integer shopId, String campaignId, String type) {

        if (Constants.SP.equals(type)) {
            return spAdProductDao.getAsinByCampaignId(puid, shopId, campaignId);
        }
        if (Constants.SB.equals(type)) {
            return sbCampaignService.getAsinByCampaignsId(puid, shopId, campaignId);

        }
        if (Constants.SD.equals(type)) {
            return sdAdProductDao.getAsinByCampaignId(puid, shopId, campaignId);
        }

        return new ArrayList<>();
    }

    /**
     * 获取sd广告组下的产品最先创建的asin
     *
     * @param puid
     * @param shopId
     * @param campaignId
     * @param adGroupId
     * @return
     */
    @Override
    public String getAsinByAdGroupId(Integer puid, Integer shopId, String campaignId, String adGroupId) {
        return sdAdProductDao.getAsinByAdGroupId(puid, shopId, campaignId, adGroupId);
    }

    @Override
    public List<String> getKeywordByCampaignId(Integer puid, Integer shopId, String campaignId, String type) {

        if (Constants.SP.equals(type)) {
            return amazonAdKeywordDaoRoutingService.getKeywordByCampaignId(puid, shopId, campaignId);
        }
        if (Constants.SB.equals(type)) {
            return sbAdKeywordDao.getKeywordByCampaignId(puid, shopId, campaignId);

        }

        return new ArrayList<>();
    }


    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroupPage(CampaignAndGroupPageListParam param) {
        return getAllTypeCampAndGroupPage(param, false);
    }

    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroupPageProductRight(CampaignAndGroupPageListParam param) {
        return getAllTypeCampAndGroupPage(param, true);
    }


    private Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroupPage(CampaignAndGroupPageListParam param, boolean isProductRight) {


        //分页
        Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        //产品权限
        Pair<Boolean, List<String>> productRightCampaignIdsFromGrpc = null;
        if (isProductRight) {
            productRightCampaignIdsFromGrpc = adProductRightService.getProductRightCampaignIdsFromGrpc(puid, shopId, CampaignTypeEnum.valueOf(type));
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                return  PageUtil.getPage(voPage, Lists.newArrayList());
            }
        }

        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = getAllTypeCampAndGroupData(param);
        if (isProductRight && CollectionUtils.isNotEmpty(campAndGroupVos)) {
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isNotEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                //先将广告活动id 转成set 提高遍历判断效率
                HashSet<String> campaignIds = Sets.newHashSet(productRightCampaignIdsFromGrpc.getValue());
                campAndGroupVos = campAndGroupVos.stream().filter(e -> campaignIds.contains(e.getCampaignId())).collect(Collectors.toList());
            }
        }
        Comparator<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> comparator = Comparator.comparingInt(each -> {
            String state = each.getState();
            if (StringUtils.isNotBlank(state)) {
                String s = state.toLowerCase();
                StateEnum stateEnum = StateEnum.stateTypeMap.get(s);
                return stateEnum == null ? 9 : stateEnum.getOrder();
            }
            return 9;
        });

        if (comparator != null) {
            if (OrderTypeEnum.desc.getType().equals(param.getOrderBy())) {
                comparator = comparator.reversed();
            }
            campAndGroupVos.sort(comparator);
        }
        return PageUtil.getPage(voPage, campAndGroupVos);
    }

    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPage(CampaignAndGroupPageListParam param) {
        return getAllSortedTypeCampAndGroupPage(param, false);
    }

    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageProductRight(CampaignAndGroupPageListParam param) {
        return getAllSortedTypeCampAndGroupPage(param, true);
    }


    private Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPage(CampaignAndGroupPageListParam param, boolean isProductRight) {

        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        //分页
        Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        //产品权限
        Pair<Boolean, List<String>> productRightCampaignIdsFromGrpc = null;
        if (isProductRight) {
            productRightCampaignIdsFromGrpc = adProductRightService.getProductRightCampaignIdsFromGrpc(puid, shopId, CampaignTypeEnum.valueOf(type));
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                return  PageUtil.getPage(voPage, Lists.newArrayList());
            }
        }

        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = getAllSortedTypeCampAndGroupData(param);

        if (isProductRight && CollectionUtils.isNotEmpty(campAndGroupVos)) {
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isNotEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                //先将广告活动id 转成set 提高遍历判断效率
                HashSet<String> campaignIds = Sets.newHashSet(productRightCampaignIdsFromGrpc.getValue());
                campAndGroupVos = campAndGroupVos.stream().filter(e -> campaignIds.contains(e.getCampaignId())).collect(Collectors.toList());
            }
        }

        if (StringUtils.isNotBlank(param.getExcludeState())) {
            campAndGroupVos = campAndGroupVos.stream().filter(each -> !each.getState().equals(param.getExcludeState()))
                    .filter(each -> StateEnum.stateTypeMap.get(each.getState()) != null)
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(param.getOrderByField())) {
            Comparator<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> comparator = null;
            if (AdOrderByFieldEnum.STATE.getCode().equals(param.getOrderByField())) {
                comparator = Comparator.comparingInt(each -> {
                    String state = each.getState();
                    StateEnum.stateTypeMap.get(state);
                    return StateEnum.stateTypeMap.get(state).getOrder();
                });
            }
            if (comparator != null) {
                if (OrderTypeEnum.desc.getType().equals(param.getOrderBy())) {
                    comparator = comparator.reversed();
                }
                campAndGroupVos.sort(comparator);
            }
        }

        //分页
        Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> page = PageUtil.getPage(voPage, campAndGroupVos);

        // 填充活动名称
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> newRows = new ArrayList<>();
        List<String> campaignIds = page.getRows().stream().map(AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo::getCampaignId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            page.setRows(newRows);
            return page;
        }
        Map<String, AmazonAdCampaignAll> campaignMap = amazonAdCampaignAllDao.getByCampaignIds(param.getPuid(), param.getShopId(), null, campaignIds, param.getType()).stream()
                .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (oldVal, newVal) -> newVal));
        List<String> portfolioIds = campaignMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> portfolioNameMap = CollectionUtils.isEmpty(portfolioIds) ? Collections.emptyMap() :
                amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds)
                        .stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, AmazonAdPortfolio::getName, (oldVal, newVal) -> newVal));
        for (AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo data : page.getRows()) {
            AmazonAdCampaignAll campaignAll = campaignMap.get(data.getCampaignId());
            if (campaignAll == null) {
                continue;
            }
            AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = data.toBuilder();
            if ("group".equals(param.getItemType()) && StringUtils.isNotBlank(campaignAll.getName())) {
                builder.setCampaignName(campaignAll.getName());
            }
            if (StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                builder.setPortFolioId(campaignAll.getPortfolioId());
                String portfolioName = portfolioNameMap.get(campaignAll.getPortfolioId());
                if (StringUtils.isNotBlank(portfolioNameMap.get(campaignAll.getPortfolioId()))) {
                    builder.setPortFolioName(portfolioName);
                }
            }
            newRows.add(builder.build());
        }
        page.setRows(newRows);
        return page;
    }

    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageNew(CampaignAndGroupPageListParam param) {
        return getAllSortedTypeCampAndGroupPageNew(param, false);
    }

    @Override
    public Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageNewProductRight(CampaignAndGroupPageListParam param) {
        return getAllSortedTypeCampAndGroupPageNew(param, true);
    }


    private Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupPageNew(CampaignAndGroupPageListParam param, boolean isProductRight) {

        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        //分页
        Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        //产品权限
        Pair<Boolean, List<String>> productRightCampaignIdsFromGrpc = null;
        if (isProductRight) {
            productRightCampaignIdsFromGrpc = adProductRightService.getProductRightCampaignIdsFromGrpc(puid, shopId, CampaignTypeEnum.valueOf(type));
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                return  PageUtil.getPage(voPage, Lists.newArrayList());
            }
        }

        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = getAllSortedTypeCampAndGroupDataNew(param);

        if (isProductRight && CollectionUtils.isNotEmpty(campAndGroupVos)) {
            if (productRightCampaignIdsFromGrpc.getKey() && CollectionUtils.isNotEmpty(productRightCampaignIdsFromGrpc.getValue())) {
                //先将广告活动id 转成set 提高遍历判断效率
                HashSet<String> campaignIds = Sets.newHashSet(productRightCampaignIdsFromGrpc.getValue());
                campAndGroupVos = campAndGroupVos.stream().filter(e -> campaignIds.contains(e.getCampaignId())).collect(Collectors.toList());
            }
        }


        if (StringUtils.isNotBlank(param.getExcludeState())) {
            campAndGroupVos = campAndGroupVos.stream().filter(each -> !each.getState().equals(param.getExcludeState()))
                    .filter(each -> StateEnum.stateTypeMap.get(each.getState()) != null)
                    .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(param.getOrderByField())) {
            Comparator<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> comparator = null;
            if (AdOrderByFieldEnum.STATE.getCode().equals(param.getOrderByField())) {
                comparator = Comparator.comparingInt(each -> {
                    String state = each.getState();
                    StateEnum.stateTypeMap.get(state);
                    return StateEnum.stateTypeMap.get(state).getOrder();
                });
            }
            if (comparator != null) {
                if (OrderTypeEnum.desc.getType().equals(param.getOrderBy())) {
                    comparator = comparator.reversed();
                }
                campAndGroupVos.sort(comparator);
            }
        }

        //分页
        voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        Page<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> page = PageUtil.getPage(voPage, campAndGroupVos);

        // 填充活动名称
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> newRows = new ArrayList<>();
        List<String> campaignIds = page.getRows().stream().map(AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo::getCampaignId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(campaignIds)) {
            page.setRows(newRows);
            return page;
        }
        Map<String, AmazonAdCampaignAll> campaignMap = amazonAdCampaignAllDao.getByCampaignIds(param.getPuid(), param.getShopId(), null, campaignIds, param.getType()).stream()
                .collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, Function.identity(), (oldVal, newVal) -> newVal));
        List<String> portfolioIds = campaignMap.values().stream().map(AmazonAdCampaignAll::getPortfolioId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<String, String> portfolioNameMap = CollectionUtils.isEmpty(portfolioIds) ? Collections.emptyMap() :
                amazonAdPortfolioDao.getPortfolioList(param.getPuid(), param.getShopId(), portfolioIds)
                        .stream().collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, AmazonAdPortfolio::getName, (oldVal, newVal) -> newVal));
        for (AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo data : page.getRows()) {
            AmazonAdCampaignAll campaignAll = campaignMap.get(data.getCampaignId());
            if (campaignAll == null) {
                continue;
            }
            AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = data.toBuilder();
            if ("group".equals(param.getItemType()) && StringUtils.isNotBlank(campaignAll.getName())) {
                builder.setCampaignName(campaignAll.getName());
            }
//            if ("campaign".equals(param.getItemType())) {
//                Optional.of(campaignAll.getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
//                Optional.of(campaignAll.getAdGoal()).filter(StringUtils::isNotEmpty)
//                        .map(SBCampaignGoalEnum::getSBCampaignGoalEnumByType)
//                        .map(SBCampaignGoalEnum::getCode).ifPresent(builder::setAdGoal);
//            }
            if (StringUtils.isNotBlank(campaignAll.getPortfolioId())) {
                builder.setPortFolioId(campaignAll.getPortfolioId());
                String portfolioName = portfolioNameMap.get(campaignAll.getPortfolioId());
                if (StringUtils.isNotBlank(portfolioNameMap.get(campaignAll.getPortfolioId()))) {
                    builder.setPortFolioName(portfolioName);
                }
            }
            newRows.add(builder.build());
        }
        page.setRows(newRows);
        return page;
    }

    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampAndGroupData(CampaignAndGroupPageListParam param) {
        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        String modular = param.getModular();
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = Lists.newArrayList();
        //分多次查询,组合一起
        if (AdModularEnum.keywordTarget.getCode().equalsIgnoreCase(param.getModular())) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
                spCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                sbCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.neKeyword.getCode().equalsIgnoreCase(param.getModular())) {
            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.productTarget.getCode().equalsIgnoreCase(modular)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos1 = Lists.newArrayList();
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos2 = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                if ("campaign".equalsIgnoreCase(param.getItemType())) {
                    spCampaignAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                    spCampaignAndGroupVos2 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                } else {
                    spCampaignAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                }
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos1, spCampaignAndGroupVos2, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.neTarget.getCode().equalsIgnoreCase(modular)) {
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.adProduct.getCode().equalsIgnoreCase(modular)) {
            // 5.广告：  Sp sd
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.queryWord.getCode().equalsIgnoreCase(modular)) {
            // 6.搜索词： 关键词产生：  SP手动（关键词类型）--  (全部)
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        } else if (AdModularEnum.queryTarget.getCode().equalsIgnoreCase(modular)) {
            // 7.搜索词： 商品投放产生：  SP自动、SP手动（商品投放类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos1 = new ArrayList<>();
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos2 = new ArrayList<>();
            if ("campaign".equalsIgnoreCase(param.getItemType())) {
                campAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                campAndGroupVos2 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            } else {
                campAndGroupVos1 = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(campAndGroupVos1, campAndGroupVos2).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.campaignNeKeyword.getCode().equalsIgnoreCase(modular)) {
            // 8.活动否定关键词:  SP（全部）
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        } else if (AdModularEnum.campaignNeTarget.getCode().equalsIgnoreCase(modular)) {
            // 9.活动否定商品：  SP自动
            campAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        } else if (AdModularEnum.group.getCode().equalsIgnoreCase(modular)) {
            // 10.广告组  sp sd
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sdCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else {
            campAndGroupVos = getCampAndGroupVos(puid, shopId, type, param.getCampaignType(), param.getGroupType(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        }
        return campAndGroupVos;
    }

    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupData(CampaignAndGroupPageListParam param) {
        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        String targetingType = param.getTargetingType();
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos;
        //分多次查询,组合一起
        if (AdTargetTypeEnum.KEYWORD.getCode().equalsIgnoreCase(targetingType)) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
                spCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), false, null);
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                sbCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdTargetTypeEnum.NE_KEYWORD.getCode().equalsIgnoreCase(targetingType)) {
            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdTargetTypeEnum.PRODUCT.getCode().equalsIgnoreCase(targetingType)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), false, null);
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, TargetingEnum.Product.getTargetingType(), null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdTargetTypeEnum.NE_PRODUCT.getCode().equalsIgnoreCase(targetingType)) {
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, TargetingEnum.Product.getTargetingType(), null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        }else if ("createSpAds".equalsIgnoreCase(targetingType)) {
            campAndGroupVos = getCreateSpAdsCampAndGroupVos(puid, shopId, param.getItemType(), param.getCampaignIds(), param.getSearchValue());
        }  else {
            campAndGroupVos = getCampAndGroupVos(puid, shopId, type, param.getCampaignType(), param.getGroupType(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), true, param.getIsMultiAdGroupsEnabled());
        }
        return campAndGroupVos;
    }

    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getAllSortedTypeCampAndGroupDataNew(CampaignAndGroupPageListParam param) {
        Integer puid = param.getPuid();
        Integer shopId = param.getShopId();
        String type = param.getType();
        String targetingType = param.getTargetingType();
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = Lists.newArrayList();
        //分多次查询,组合一起
        if (AdTargetTypeEnum.KEYWORD.getCode().equalsIgnoreCase(targetingType)) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
                spCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), false, null);
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                sbCampaignAndGroupVos = getCampAndGroupVos(param.getPuid(), param.getShopId(), Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdTargetTypeEnum.NE_KEYWORD.getCode().equalsIgnoreCase(targetingType)) {
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();

            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            if (AdModularEnum.campaignNeKeyword.getCode().equalsIgnoreCase(param.getModular())) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, param.getCampaignType(), GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            } else if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdTargetTypeEnum.PRODUCT.getCode().equalsIgnoreCase(targetingType)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), false, null);
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdTargetTypeEnum.NE_PRODUCT.getCode().equalsIgnoreCase(targetingType)) {
            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            if (AdModularEnum.campaignNeTarget.getCode().equalsIgnoreCase(param.getModular())) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            } else if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else {
            campAndGroupVos = getCampAndGroupVos(puid, shopId, type, param.getCampaignType(), param.getGroupType(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), true, param.getIsMultiAdGroupsEnabled());
        }
        return campAndGroupVos;
    }
    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getCreateSpAdsCampAndGroupVos(Integer puid, Integer shopId, String itemType, String campaignIds, String name) {
        //查询所有类型的广告活动(sp sb sd)
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = new ArrayList<>();
        if ("campaign".equalsIgnoreCase(itemType)) {
            List<AdCampaignOptionVo> allCampaigns = amazonAdCampaignDao.getCampaignsByType(puid, shopId, "sp", "manual", null, name, null, null);
            allCampaigns.stream().filter(Objects::nonNull).forEach(item -> {
                AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                    .setCampaignId(item.getCampaignId())
                    .setCampaignName(item.getName())
                    .setState(item.getState())
                    .setType(item.getType());
                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    builder.setCostType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCostType())) {
                    builder.setCostType(item.getCostType());
                } else if (StringUtils.isEmpty(item.getAdGoal()) || SBCampaignGoalEnum.PAGE_VISIT.getType().equalsIgnoreCase(item.getAdGoal())) {
                    builder.setCostType(SBCampaignCostTypeEnum.CPC.getCode());
                }
                SBCampaignGoalEnum sbCampaignGoalEnumByType = SBCampaignGoalEnum.getSBCampaignGoalEnumByType(item.getAdGoal());
                if (sbCampaignGoalEnumByType != null) {
                    Optional.ofNullable(sbCampaignGoalEnumByType.getCode()).ifPresent(builder::setAdGoal);
                }
                if(StringUtils.isNotBlank(item.getBrandEntityId())) {
                    builder.setBrandEntityId(item.getBrandEntityId());
                }
                campAndGroupVos.add(builder.build());
            });
        } else if ("group".equalsIgnoreCase(itemType)) {
            List<AdGroupOptionVo> allGroups = amazonAdGroupDao.getSpGroupsByType(puid, shopId, campaignIds, name);
            if (CollectionUtils.isNotEmpty(allGroups)) {
                List<String> campaignIdList = allGroups.parallelStream().map(AdGroupOptionVo::getCampaignId)//
                    .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                List<AmazonAdCampaignAll> allCampaigns = amazonAdCampaignDao.getByCampaignIds(puid, shopId, campaignIdList);
                Map<String, AmazonAdCampaignAll> allCampaignMap = allCampaigns.parallelStream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, v1 -> v1, (old, current) -> current));
                allGroups.stream().filter(Objects::nonNull).forEach(item -> {
                    AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                        .setCampaignId(item.getCampaignId())
                        .setGroupName(item.getName())
                        .setGroupId(item.getGroupId())
                        .setState(item.getState() == null ? "" : item.getState())
                        .setType(item.getType());
                    Optional.ofNullable(item.getTactic()).filter(StringUtils::isNotEmpty).ifPresent(builder::setTactic);
                    Optional.ofNullable(item.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
                    if (Objects.nonNull(allCampaignMap.get(item.getCampaignId()))) {
                        Optional.ofNullable(allCampaignMap.get(item.getCampaignId()).getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                    }
                    campAndGroupVos.add(builder.build());
                });
            }
        }
        return campAndGroupVos;
    }


    @Override
    public Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPage(CampaignAndGroupPageParam param) {
        return getProductRightAllTypeCampaignAndGroupPage(param, false);
    }


    @Override
    public Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPageProductRight(CampaignAndGroupPageParam param) {
        return getProductRightAllTypeCampaignAndGroupPage(param, true);
    }


    private Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getProductRightAllTypeCampaignAndGroupPage(CampaignAndGroupPageParam param, boolean isProductRight) {
        if (isProductRight) {
            return getAllTypeCampaignAndGroupPageDoris(param);
        } else {
            return getAllTypeCampaignAndGroupPageNonProductRight(param);
        }
    }



    private Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPageNonProductRight (CampaignAndGroupPageParam param) {

        Integer puid = param.getPuid();
        String shopId = param.getShopId();
        String type = param.getType();
        String modular = param.getModular();
        List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = Lists.newArrayList();
        //分多次查询,组合一起
        if (AdModularEnum.keywordTarget.getCode().equalsIgnoreCase(param.getModular())) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
                spCampaignAndGroupVos = getCampaignAndGroupVos(param.getPuid(), param.getShopId(), Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                sbCampaignAndGroupVos = getCampaignAndGroupVos(param.getPuid(), param.getShopId(), Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.neKeyword.getCode().equalsIgnoreCase(param.getModular())) {
            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else if (AdModularEnum.productTarget.getCode().equalsIgnoreCase(modular)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos1 = Lists.newArrayList();
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos2 = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                if ("campaign".equalsIgnoreCase(param.getItemType())) {
                    spCampaignAndGroupVos1 = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                    spCampaignAndGroupVos2 = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                } else {
                    spCampaignAndGroupVos1 = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                }
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos1, spCampaignAndGroupVos2, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.neTarget.getCode().equalsIgnoreCase(modular)) {
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SD, TargetingEnum.Product.getTargetingType(), null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sbCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.adProduct.getCode().equalsIgnoreCase(modular)) {
            // 5.广告：  Sp sd
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sdCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.queryWord.getCode().equalsIgnoreCase(modular)) {
            // 6.搜索词： 关键词产生：  SP手动（关键词类型）--  (全部)
            campAndGroupVos = getCampaignAndGroupVos(puid, shopId, param.getType(), null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        } else if (AdModularEnum.queryTarget.getCode().equalsIgnoreCase(modular)) {
            // 7.搜索词： 商品投放产生：  SP自动、SP手动（商品投放类型）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos1 = new ArrayList<>();
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos2 = new ArrayList<>();
            if ("campaign".equalsIgnoreCase(param.getItemType())) {
                campAndGroupVos1 = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
                campAndGroupVos2 = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            } else {
                campAndGroupVos1 = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.MANUAL, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }
            campAndGroupVos = Stream.of(campAndGroupVos1, campAndGroupVos2).flatMap(Collection::stream).collect(Collectors.toList());
        } else if (AdModularEnum.campaignNeKeyword.getCode().equalsIgnoreCase(modular)) {
            // 8.活动否定关键词:  SP（全部）
            campAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        } else if (AdModularEnum.campaignNeTarget.getCode().equalsIgnoreCase(modular)) {
            // 9.活动否定商品：  SP自动
            campAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        } else if (AdModularEnum.group.getCode().equalsIgnoreCase(modular)) {
            // 10.广告组  sp sd
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                spCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                sdCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                sbCampaignAndGroupVos = getCampaignAndGroupVos(puid, shopId, Constants.SB, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
            }

            campAndGroupVos = Stream.of(spCampaignAndGroupVos, sdCampaignAndGroupVos, sbCampaignAndGroupVos).flatMap(Collection::stream).collect(Collectors.toList());

        } else {
            campAndGroupVos = getCampaignAndGroupVos(puid, shopId, type, param.getCampaignType(), param.getGroupType(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds());
        }

        //分页
        Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> voPage = new Page<>();
        voPage.setPageNo(param.getPageNo());
        voPage.setPageSize(param.getPageSize());
        Comparator<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> comparator = Comparator.comparingInt(each -> {
            String state = each.getState();
            if (StringUtils.isNotBlank(state)) {
                String s = state.toLowerCase();
                StateEnum stateEnum = StateEnum.stateTypeMap.get(s);
                return stateEnum == null ? 9 : stateEnum.getOrder();
            }
            return 9;
        });

        if (comparator != null) {
            campAndGroupVos.sort(comparator);
        }
        return PageUtil.getPage(voPage, campAndGroupVos);
    }

    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getCampAndGroupVos(Integer puid, Integer shopId, String type, String campaignType, String groupType, String name, String itemType, String campaignIds, String portfolioId, String groupIds) {
        return getCampAndGroupVos(puid, shopId, type, campaignType, groupType, name, itemType, campaignIds, portfolioId, groupIds, true, null);
    }

    private List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> getCampAndGroupVos(Integer puid, Integer shopId, String type, String campaignType, String groupType, String name, String itemType, String campaignIds, String portfolioId, String groupIds, boolean queryAuto,
        Boolean isMultiAdGroupsEnabled) {
        //查询所有类型的广告活动(sp sb sd)
        List<AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = new ArrayList<>();
        if ("campaign".equalsIgnoreCase(itemType)) {
            List<AdCampaignOptionVo> allCampaigns = amazonAdCampaignDao.getCampaignsByType(puid, shopId, type, campaignType, groupType, name, campaignIds, portfolioId);
            if (isMultiAdGroupsEnabled != null && isMultiAdGroupsEnabled) {
                allCampaigns = allCampaigns.stream().filter(adCampaignOptionVo -> adCampaignOptionVo.getIsMultiAdGroupsEnabled() != null && adCampaignOptionVo.getIsMultiAdGroupsEnabled()).collect(Collectors.toList());
            }
            allCampaigns.stream().filter(Objects::nonNull).forEach(item -> {
                AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                    .setCampaignId(item.getCampaignId())
                    .setCampaignName(item.getName())
                    .setState(item.getState())
                    .setType(item.getType());
                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    builder.setCostType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCostType())) {
                    builder.setCostType(item.getCostType());
                } else if (StringUtils.isEmpty(item.getAdGoal()) || SBCampaignGoalEnum.PAGE_VISIT.getType().equalsIgnoreCase(item.getAdGoal())) {
                    builder.setCostType(SBCampaignCostTypeEnum.CPC.getCode());
                }
                SBCampaignGoalEnum sbCampaignGoalEnumByType = SBCampaignGoalEnum.getSBCampaignGoalEnumByType(item.getAdGoal());
                if (sbCampaignGoalEnumByType != null) {
                    Optional.ofNullable(sbCampaignGoalEnumByType.getCode()).ifPresent(builder::setAdGoal);
                }
                if(StringUtils.isNotBlank(item.getBrandEntityId())) {
                    builder.setBrandEntityId(item.getBrandEntityId());
                }
                campAndGroupVos.add(builder.build());
            });
        } else if ("group".equalsIgnoreCase(itemType)) {
            //类型为sb 则不查询广告组 (sb无广告组概念)
            if (StringUtils.isBlank(type) || type.equalsIgnoreCase(Constants.SP) || type.equalsIgnoreCase(Constants.SD) || type.equalsIgnoreCase(Constants.SB)) {
                List<String> typeList = StringUtils.isBlank(type) ? null : Stream.of(type).collect(Collectors.toList());
                if (StringUtils.isNotBlank(portfolioId)) {
                    List<String> campaignIdsByPortfolioId = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, shopId, portfolioId, type, campaignIds);
                    if (CollectionUtils.isNotEmpty(campaignIdsByPortfolioId)) {
                        campaignIds = StringUtil.join(campaignIdsByPortfolioId, ",");
                    } else {
                        return campAndGroupVos;
                    }
                }
                List<AdGroupOptionVo> allGroups = amazonAdGroupDao.getAllGroupsByType(puid, shopId, typeList, groupType, name, campaignIds, groupIds, queryAuto);
                if (CollectionUtils.isNotEmpty(allGroups)) {
                    List<String> campaignIdList = allGroups.parallelStream().map(AdGroupOptionVo::getCampaignId)
                            .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
                    List<AmazonAdCampaignAll> allCampaigns = amazonAdCampaignDao.getByCampaignIds(puid, shopId, campaignIdList);
                    Map<String,AmazonAdCampaignAll> allCampaignMap = allCampaigns.parallelStream().collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, v1 -> v1, (old, current) -> current));
                allGroups.stream().filter(Objects::nonNull).forEach(item -> {
                    AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                            .setCampaignId(item.getCampaignId())
                            .setGroupName(item.getName())
                            .setGroupId(item.getGroupId())
                            .setState(item.getState() == null ? "" : item.getState())
                            .setType(item.getType());
                    Optional.ofNullable(item.getTactic()).filter(StringUtils::isNotEmpty).ifPresent(builder::setTactic);
                    Optional.ofNullable(item.getAdFormat()).filter(StringUtils::isNotEmpty).ifPresent(builder::setAdFormat);
                    if (Objects.nonNull(allCampaignMap.get(item.getCampaignId()))) {
                        Optional.ofNullable(allCampaignMap.get(item.getCampaignId()).getCostType()).filter(StringUtils::isNotEmpty).ifPresent(builder::setCostType);
                    }
                    campAndGroupVos.add(builder.build());
                });
                }

            }
        }
        return campAndGroupVos;
    }

    private List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getCampaignAndGroupVos(Integer puid, String shopId, String type, String campaignType, String groupType, String name, String itemType, String campaignIds, String portfolioId, String groupIds) {
        //查询所有类型的广告活动(sp sb sd)
        List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = new ArrayList<>();
        if ("campaign".equalsIgnoreCase(itemType)) {
            List<AdCampaignOptionVo> allCampaigns = amazonAdCampaignDao.getCampaignsByType(puid, shopId, type, campaignType, groupType, name, campaignIds, portfolioId);
            allCampaigns.stream().filter(Objects::nonNull).forEach(item -> {
                AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                        .setCampaignId(item.getCampaignId())
                        .setCampaignName(item.getName())
                        .setState(item.getState())
                        .setType(item.getType());
                if (StringUtils.isNotBlank(item.getTargetingType())) {
                    builder.setCostType(item.getTargetingType());
                }
                if (StringUtils.isNotBlank(item.getCostType())) {
                    builder.setCostType(item.getCostType());
                }
                campAndGroupVos.add(builder.build());
            });
        } else if ("group".equalsIgnoreCase(itemType)) {
            //类型为sb 则不查询广告组 (sb无广告组概念)
            if (StringUtils.isBlank(type) || type.equalsIgnoreCase(Constants.SP) || type.equalsIgnoreCase(Constants.SD) || type.equalsIgnoreCase(Constants.SB)) {
                List<String> typeList = StringUtils.isBlank(type) ? null : Stream.of(type).collect(Collectors.toList());
                if (StringUtils.isNotBlank(portfolioId)) {
                    List<String> campaignIdsByPortfolioId = amazonAdCampaignAllDao.getCampaignIdsByPortfolioId(puid, shopId, portfolioId, type, campaignIds);
                    if (CollectionUtils.isNotEmpty(campaignIdsByPortfolioId)) {
                        campaignIds = StringUtil.join(campaignIdsByPortfolioId, ",");
                    } else {
                        return campAndGroupVos;
                    }
                }
                List<AdGroupOptionVo> allGroups = amazonAdGroupDao.getAllGroupsByType(puid, shopId, typeList, groupType, name, campaignIds, groupIds);
                allGroups.stream().filter(Objects::nonNull).forEach(item -> {
                    AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                            .setCampaignId(item.getCampaignId())
                            .setGroupName(item.getName())
                            .setGroupId(item.getGroupId())
                            .setState(item.getState() == null ? "" : item.getState())
                            .setType(item.getType());
                    campAndGroupVos.add(builder.build());
                });
            }
        }
        return campAndGroupVos;
    }


    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#operationLogQo.shopId",
            adTypeExpression = "#operationLogQo.adType",
            strategy = PermissionFilterStrategy.FILTER
    )
    public OperationLogPageResponse.Page operationLogPageList(OperationLogQo qo) {
        Page<AdManageLogVo> adManageLogVoPage = manageOperationLogService.pageList(qo);
        OperationLogPageResponse.Page.Builder builder = OperationLogPageResponse.Page.newBuilder();
        if (adManageLogVoPage == null) {
            return builder.build();
        }

        builder.setPageNo(Int32Value.of(adManageLogVoPage.getPageNo()));
        builder.setTotalPage(Int32Value.of(adManageLogVoPage.getTotalPage()));
        builder.setPageSize(Int32Value.of(adManageLogVoPage.getPageSize()));
        builder.setTotalSize(Int32Value.of(adManageLogVoPage.getTotalSize()));
        if (CollectionUtils.isEmpty(adManageLogVoPage.getRows())) {
            return builder.build();
        }
        List<OperationLogVo> collect = adManageLogVoPage.getRows().stream().map(e -> manageLogToVo(e)).collect(Collectors.toList());
        builder.addAllRows(collect);

        return builder.build();
    }

    @Override
    public RowData operationLogDayList(OperationLogQo qo) {
        Map<String, AdManageLogDayVo> stringAdManageLogDayVoMap = manageOperationLogService.dayList(qo);
        RowData.Builder builder = RowData.newBuilder();
        Map<String, DayDataVo> dayData = Maps.newHashMap();
        if (stringAdManageLogDayVoMap == null || stringAdManageLogDayVoMap.isEmpty()) {
            return builder.build();
        }
        stringAdManageLogDayVoMap.forEach((k, v) -> {
            dayData.put(k, manageLogToDayVo(v));
        });
        builder.putAllDay(dayData);
        return builder.build();
    }


    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression = "#param.adType",
            strategy = PermissionFilterStrategy.FILTER
    )
    public AllCampaignOrGroupAggregateDataAndOperationResponse.CampaignOrGroup getAllGroupOrCampaignAggregateData(Integer puid, CampaignOrGroupParam param) {

        OperationLogQo qo = new OperationLogQo();
        BeanUtils.copyProperties(param, qo);
//        RowData rowData = param.isNoNeedQueryLog() ? null : operationLogDayList(qo);
        RowData rowData = operationLogDayList(qo);
        AllCampaignOrGroupAggregateDataAndOperationResponse.CampaignOrGroup.Builder builder = AllCampaignOrGroupAggregateDataAndOperationResponse.CampaignOrGroup.newBuilder();
        if (rowData != null) {
            builder.setDayOperation(rowData);
        }
        if (StringUtils.isNotBlank(param.getGroupId()) || StringUtils.isNotBlank(param.getGroupIds())) {
            GroupPageParam q = new GroupPageParam();
            BeanUtils.copyProperties(param, q);
            q.setMultiCampaignId(param.getCampaignIds());
            q.setMultiGroupId(param.getGroupIds());
            String portfolioIds = Lists.newArrayList(param.getPortfolioId(), param.getPortfolioIds()).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(portfolioIds)) {
                q.setPortfolioId(portfolioIds);
            }
            AllGroupAggregateDataResponse.GroupHomeVo allGroupAggregateData = getAllGroupAggregateData(param.getPuid(), q);
            if (allGroupAggregateData.hasAggregateDataVo()) {
                builder.setAggregateDataVo(allGroupAggregateData.getAggregateDataVo());
            }
            if (allGroupAggregateData.getDayList() != null) {
                builder.addAllDay(allGroupAggregateData.getDayList());
            }
            if (allGroupAggregateData.getWeekList() != null) {
                builder.addAllWeek(allGroupAggregateData.getWeekList());
            }
            if (allGroupAggregateData.getMonthList() != null) {
                builder.addAllMonth(allGroupAggregateData.getMonthList());
            }
        } else {
            CampaignPageParam q = new CampaignPageParam();
            BeanUtils.copyProperties(param, q);
            if (StringUtils.isNotBlank(q.getCampaignId())) {
                q.setCampaignIdList(Arrays.asList(q.getCampaignId().split(",")));
            }
            String portfolioIds = Lists.newArrayList(param.getPortfolioId(), param.getPortfolioIds()).stream().filter(StringUtils::isNotBlank).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(portfolioIds)) {
                q.setPortfolioId(portfolioIds);
            }
            AllCampaignAggregateDataResponse.CampaignHomeVo allCampaignAggregateData = getAllCampaignAggregateData(param.getPuid(), q);

            if (allCampaignAggregateData.hasAggregateDataVo()) {
                builder.setAggregateDataVo(allCampaignAggregateData.getAggregateDataVo());
            }
            if (allCampaignAggregateData.getDayList() != null) {
                builder.addAllDay(allCampaignAggregateData.getDayList());
            }
            if (allCampaignAggregateData.getWeekList() != null) {
                builder.addAllWeek(allCampaignAggregateData.getWeekList());
            }
            if (allCampaignAggregateData.getMonthList() != null) {
                builder.addAllMonth(allCampaignAggregateData.getMonthList());
            }
        }

        return builder.build();
    }


    /**
     * 所有广告产品数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public AllAdsDataResponse.AdCreateHomeVo getAllAdsData(Integer puid, AdAdsPageParam param) {
        return cpcSbAdsService.getAllAdsData(puid, param);
    }


    @Override
    public AllAdsAggregateDataResponse.AdCreateHomeVo getAllAdsAggregateData(Integer puid, AdAdsPageParam param) {
        return cpcSbAdsService.getAllAdsAggregateData(puid, param);
    }


    private DayDataVo manageLogToDayVo(AdManageLogDayVo vo) {
        DayDataVo.Builder builder = DayDataVo.newBuilder();
        if (vo.getDate() != null) {
            builder.setDate(vo.getDate());
        }
        if (CollectionUtils.isNotEmpty(vo.getDayData())) {
            List<OperationLogVo> collect = vo.getDayData().stream().map(this::manageLogToVo).collect(Collectors.toList());
            builder.addAllDayData(collect);
        }
        if (vo.getCount() != null) {
            builder.setCount(vo.getCount());
        }
        return builder.build();
    }

    private OperationLogVo manageLogToVo(AdManageLogVo vo) {
        OperationLogVo.Builder builder = OperationLogVo.newBuilder();
        if (vo.getAction() != null) {
            builder.setAction(vo.getAction());
        }
        if (vo.getAdGroupId() != null) {
            builder.setAdGroupId(vo.getAdGroupId());
        }
        if (vo.getCampaignId() != null) {
            builder.setCampaignId(vo.getCampaignId());
        }
        if (vo.getAdGroupType() != null) {
            builder.setAdGroupType(vo.getAdGroupType());
        }
        if (vo.getAdType() != null) {
            builder.setAdType(vo.getAdType());
        }
        if (vo.getCampaignName() != null) {
            builder.setCampaignName(vo.getCampaignName());
        }
        if (vo.getCampaignTargetingType() != null) {
            builder.setCampaignTargetingType(vo.getCampaignTargetingType());
        }
        if (vo.getFrom() != null) {
            builder.setFrom(vo.getFrom());
        }
        if (vo.getGroupName() != null) {
            builder.setGroupName(vo.getGroupName());
        }
        if (StringUtils.isNotBlank(vo.getPortfolioId())) {
            builder.setPortfolioId(vo.getPortfolioId());
        }
        if (StringUtils.isNotBlank(vo.getPortfolioName())) {
            builder.setPortfolioName(vo.getPortfolioName());
        }
        if (vo.getMarketplaceId() != null) {
            builder.setMarketplaceId(vo.getMarketplaceId());
        }
        if (vo.getTargetType() != null) {
            builder.setTargetType(vo.getTargetType());
        }
        if (vo.getTargetName() != null) {
            builder.setTargetName(vo.getTargetName());
        }
        if (vo.getAdjustmentRange() != null) {
            builder.setAdjustmentRange(vo.getAdjustmentRange());
        }
        if (vo.getTemplateName() != null) {
            builder.setTemplateName(vo.getTemplateName());
        }
        if (vo.getTemplateId() != null) {
            builder.setTemplateId(Int64Value.of(vo.getTemplateId()));
        }
        if (vo.getMatchType() != null) {
            builder.setMatchType(vo.getMatchType());
        }
        if (vo.getOperationObjectName() != null) {
            builder.setOperationObjectName(vo.getOperationObjectName());
        }

        if (vo.getOperationContent() != null) {

            List<OperationContent> collect = vo.getOperationContent().stream().map(e -> {
                OperationContent.Builder builder1 = OperationContent.newBuilder();
                if (e.getName() != null) {
                    builder1.setName(e.getName());
                }
                if (e.getNewValue() != null) {
                    builder1.setNewValue(e.getNewValue());
                }
                if (e.getTitle() != null) {
                    builder1.setTitle(e.getTitle());
                }
                if (e.getPreviousValue() != null) {
                    if (!"null".equalsIgnoreCase(e.getPreviousValue())) {
                        builder1.setPreviousValue(e.getPreviousValue());
                    }
                }
                if (StringUtils.isNotBlank(e.getType())) {
                    builder1.setType(e.getType());
                }
                if (StringUtils.isNotBlank(e.getBrandDesc())) {
                    builder1.setBrandDesc(e.getBrandDesc());
                }
                if (StringUtils.isNotBlank(e.getPriceDesc())) {
                    builder1.setPriceDesc(e.getPriceDesc());
                }
                if (StringUtils.isNotBlank(e.getRatingDesc())) {
                    builder1.setRatingDesc(e.getRatingDesc());
                }
                return builder1.build();
            }).collect(Collectors.toList());
            builder.addAllOperationContent(collect);
        }
        if (vo.getOperationTime() != null) {
            builder.setOperationTime(vo.getOperationTime());
        }
        if (vo.getSiteOperationTime() != null) {
            builder.setSiteOperationTime(vo.getSiteOperationTime());
        }
        if (vo.getOperationObject() != null) {
            builder.setOperationObject(vo.getOperationObject());
        }
        if (vo.getResult() != null) {
            builder.setResult(vo.getResult().toString());
        }
        if (vo.getResultInfo() != null) {
            builder.setResultInfo(vo.getResultInfo());
        }
        if (vo.getShopId() != null) {
            builder.setShopId(vo.getShopId());
        }
        if (vo.getUid() != null) {
            builder.setUid(vo.getUid());
        }
        if (vo.getUserName() != null) {
            builder.setUserName(vo.getUserName());
        }
        if (vo.getAsin() != null) {
            builder.setAsin(vo.getAsin());
        }
        if (vo.getSku() != null) {
            builder.setMsku(vo.getSku());
        }
        return builder.build();
    }

    @Deprecated
    @Override
    public GetCampaignHourReportResponse.CampaignHour getCampaignHourReport(Integer puid, CampaignHourParam param, ReportDateModelPb.ReportDateModel dateModel) {
        return amazonAdCampaignHourReportService.getListGprc(puid, param, dateModel);
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateCampaignHourReport(Integer puid, List<String> temporaryIds, CampaignAggregateHourParamVO param) {
        return amazonAdCampaignHourReportService.getAggregateList(puid, temporaryIds, param, ReportDateModelPb.ReportDateModel.HOURLY);
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateDailyWeeklyAndMonthly(Integer puid, List<String> temporaryIds,
                                                                                        CampaignAggregateHourParamVO param, ReportDateModelPb.ReportDateModel dateModel) {
        return amazonAdCampaignHourReportService.getAggregateList(puid, temporaryIds, param, dateModel);
    }

    @Override
    public GetCampaignBudgetHourReportResponse.CampaignHour getCampaignBudgetHourReport(Integer puid, CampaignHourParam param, ReportDateModelPb.ReportDateModel dateModel) {
        return amazonAdCampaignHourReportService.getBudgetListGprc(puid, param, dateModel);
    }

    /**
     * 广告活动-微信汇总数据
     *
     * @param puid
     * @param param
     * @return
     */
    public AllCampaignAggregateDataResponse.CampaignHomeVo getWxAllCampaignAggregateData(Integer puid, CampaignPageParam param) {
        return cpcCampaignService.getOldAllCampaignAggregateData(puid, param);
    }

    /**
     * sp关键词投放
     * 更新关键词实时排名
     */
    public Result updateKeywordRank(Integer puid, Integer shopId, String keywordId, String advRank, Integer uid) {
        return cpcKeywordsService.updateKeywordRank(puid, shopId, keywordId, advRank, uid);
    }

    @Override
    public Result<List<AmazonAdPortfolio>> queryPortfolio(Integer puid, Integer shopId, String marketplaceId, String portfolioName, Integer pageSize, Integer pageNo) {
        Result<List<AmazonAdPortfolio>> result = new Result<>();
        try {
            List<AmazonAdPortfolio> amazonAdPortfolioList = amazonAdPortfolioDao.getPortfolioListAutoRule(puid, shopId, marketplaceId, portfolioName, pageSize, pageNo);
            result.setData(amazonAdPortfolioList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            log.error("自动化规则下拉查询广告组合异常:", e);
        }
        return result;
    }

    @Override
    public Result<Page<AmazonAdCampaignAll>> queryCampaign(Integer puid, Integer shopId, List<String> types, List<String> portfolioIds, Integer pageSize, Integer pageNo, String campaignName, String queryType, String targetingType, String campaignId, List<String> statusList) {
        Result<Page<AmazonAdCampaignAll>> result = new Result<>();
        try {
            if (StringUtils.isNotBlank(queryType) && CollectionUtils.isNotEmpty(portfolioIds)) {
                Page<AmazonAdCampaignAll> page = new Page<>();
                page.setPageNo(pageNo);
                page.setPageSize(pageSize);
                result.setData(page);
            }
            Page<AmazonAdCampaignAll> amazonAdCampaignAllList = amazonAdCampaignAllDao.getList(puid, shopId, types, portfolioIds, pageSize, pageNo, campaignName, targetingType, campaignId, statusList);
            result.setData(amazonAdCampaignAllList);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            log.error("自动化规则下拉查询广告活动异常:", e);
        }
        return result;
    }

    @Override
    public Result<Page<AmazonAdGroup>> queryGroup(Integer puid, Integer shopId, List<String> types, List<String> campaignIds, Integer pageSize, Integer pageNo, String groupName, String queryType, List<String> adGroupTypes, List<String> statusList) {
        Result<Page<AmazonAdGroup>> result = new Result<>();
        try {
            if (StringUtils.isNotBlank(queryType) && CollectionUtils.isNotEmpty(campaignIds)) {
                Page<AmazonAdGroup> page = new Page<>();
                page.setPageNo(pageNo);
                page.setPageSize(pageSize);
                result.setData(page);
            }
            Page<AmazonAdGroup> amazonAdGroupPage = amazonAdGroupDao.queryGroup(puid, shopId, types, groupName, campaignIds, adGroupTypes, pageSize, pageNo, statusList);
            result.setData(amazonAdGroupPage);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            log.error("自动化规则下拉查询广告组异常:", e);
        }
        return result;
    }

    @Override
    public Result<List<String>> queryGroupType(Integer puid, Integer shopId, String campaignId) {
        Result<List<String>> result = new Result<>();
        try {
            List<String> groupTypeList = new ArrayList<>();
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, shopId, campaignId);
            if (amazonAdCampaignAll != null) {
                if ("sp".equals(amazonAdCampaignAll.getType())) {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.listByPuidAndCampaignId(amazonAdCampaignAll.getPuid(), amazonAdCampaignAll.getShopId(),
                            amazonAdCampaignAll.getCampaignId());
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupList.forEach(e -> {
                            if (StringUtils.isNotBlank(e.getAdGroupType())) {
                                groupTypeList.add(e.getAdGroupType());
                            }
                        });
                    }
                } else if ("sb".equals(amazonAdCampaignAll.getType())) {
                    List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.listByCampaignId(amazonAdCampaignAll.getPuid(), amazonAdCampaignAll.getShopId(),
                            amazonAdCampaignAll.getCampaignId());
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                        amazonSbAdGroupList.forEach(e -> {
                            if (StringUtils.isNotBlank(e.getAdGroupType())) {
                                groupTypeList.add(e.getAdGroupType());
                            }
                        });
                    }
                } else if ("sd".equals(amazonAdCampaignAll.getType())) {
                    List<AmazonSdAdGroup> amazonSdAdGroupList = amazonSdAdGroupDao.listByPuidAndCampaignId
                            (amazonAdCampaignAll.getPuid(), amazonAdCampaignAll.getShopId(),
                                    amazonAdCampaignAll.getCampaignId());
                    if (CollectionUtils.isNotEmpty(amazonSdAdGroupList)) {
                        amazonSdAdGroupList.forEach(e -> {
                            groupTypeList.add(e.getTactic());
                        });
                    }
                }
                result.setCode(Result.SUCCESS);
                result.setMsg("查询广告组类型成功");
                result.setData(groupTypeList);
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("查询广告组类型异常");
            log.error("查询广告组类型异常:", e);
        }
        return result;
    }

    @Override
    public Result<CampaignAndGroupTargetTypeVo> queryCampaignAndGroupType(Integer puid, Integer shopId, String campaignId, String groupId) {
        Result<CampaignAndGroupTargetTypeVo> result = new Result<>();
        if (StringUtils.isEmpty(campaignId)) {
            return result;
        }
        CampaignAndGroupTargetTypeVo campaignAndGroupTargetTypeVo = new CampaignAndGroupTargetTypeVo();
        try {
            List<GroupTargetTypeVo> groupTypeInfoList = new ArrayList<>();
            AmazonAdCampaignAll amazonAdCampaignAll = amazonAdCampaignAllDao.getByCampaignId(puid, shopId, campaignId);
            if (amazonAdCampaignAll != null) {
                campaignAndGroupTargetTypeVo.setCampaignTargetType(amazonAdCampaignAll.getAdTargetType());
                campaignAndGroupTargetTypeVo.setGroupTargetTypeVo(groupTypeInfoList);
                if ("sp".equals(amazonAdCampaignAll.getType())) {
                    List<AmazonAdGroup> amazonAdGroupList = amazonAdGroupDao.getInfoByCampaignIdAndGroupId(amazonAdCampaignAll.getPuid(), amazonAdCampaignAll.getShopId(),
                            amazonAdCampaignAll.getCampaignId(), groupId);
                    if (CollectionUtils.isNotEmpty(amazonAdGroupList)) {
                        amazonAdGroupList.forEach(e -> {
                            if (StringUtils.isNotBlank(e.getAdGroupType())) {
                                GroupTargetTypeVo groupType = GroupTargetTypeVo.builder()
                                        .groupId(e.getAdGroupId())
                                        .groupTargetType(e.getAdGroupType())
                                        .build();
                                groupTypeInfoList.add(groupType);
                            }
                        });
                    }
                } else if ("sb".equals(amazonAdCampaignAll.getType())) {
                    List<AmazonSbAdGroup> amazonSbAdGroupList = amazonSbAdGroupDao.getInfoByCampaignIdAndGroupId(amazonAdCampaignAll.getPuid(), amazonAdCampaignAll.getShopId(),
                            amazonAdCampaignAll.getCampaignId(), groupId);
                    if (CollectionUtils.isNotEmpty(amazonSbAdGroupList)) {
                        amazonSbAdGroupList.forEach(e -> {
                            if (StringUtils.isNotBlank(e.getAdGroupType())) {
                                GroupTargetTypeVo groupType = GroupTargetTypeVo.builder()
                                        .groupId(e.getAdGroupId())
                                        .groupTargetType(e.getAdGroupType())
                                        .build();
                                groupTypeInfoList.add(groupType);
                            }
                        });
                    }
                } else if ("sd".equals(amazonAdCampaignAll.getType())) {
                    List<AmazonSdAdGroup> amazonSdAdGroupList = amazonSdAdGroupDao.getInfoByCampaignIdAndGroupId
                            (amazonAdCampaignAll.getPuid(), amazonAdCampaignAll.getShopId(),
                                    amazonAdCampaignAll.getCampaignId(), groupId);
                    if (CollectionUtils.isNotEmpty(amazonSdAdGroupList)) {
                        amazonSdAdGroupList.forEach(e -> {
                            GroupTargetTypeVo groupType = GroupTargetTypeVo.builder()
                                    .groupId(e.getAdGroupId())
                                    .groupTargetType(e.getTactic())
                                    .build();
                            groupTypeInfoList.add(groupType);
                        });
                    }
                }
                result.setCode(Result.SUCCESS);
                result.setMsg("查询广告组类型成功");
                result.setData(campaignAndGroupTargetTypeVo);
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("查询广告组类型异常");
            log.error("查询广告组类型异常:", e);
        }
        return result;
    }

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementHourReport(Integer puid, PlacementHourParam param, ReportDateModelPb.ReportDateModel dateModel) {
        return amazonAdPlacementHourReportService.getListGrpc(puid, param, dateModel);
    }

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementAggregateHourReport(Integer puid, PlacementAggregateHourVo param) {
        //在这里需要查询批量暂存的idList
        String pageSign = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getPageSign).orElse("");
        Integer shopId = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getShopId).map(Int32Value::getValue).orElse(0);
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(puid, pageSign, "", shopId, new PlacementAggregateHourVo[]{param});
        return amazonAdPlacementHourReportService.getAggregateList(puid, idList, param, ReportDateModelPb.ReportDateModel.HOURLY);

    }

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementDailyWeeklyAndMonthly(Integer puid, PlacementAggregateHourVo param,
                                                                                                                           ReportDateModelPb.ReportDateModel dateModel) {
        //在这里需要查询批量暂存的idList
        String pageSign = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getPageSign).orElse("");
        Integer shopId = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getShopId).map(Int32Value::getValue).orElse(0);
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(puid, pageSign, "", shopId, new PlacementAggregateHourVo[]{param});
        return amazonAdPlacementHourReportService.getAggregateList(puid, idList, param, dateModel);
    }

    @Override
    public GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour getGroupAggregateHourReport(Integer puid, GroupAggregateHourVo param) {
        //在这里需要查询批量暂存的idList
        String pageSign = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getPageSign).orElse("");
        Integer shopId = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getShopId).map(Int32Value::getValue).orElse(0);
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(puid, pageSign, "", shopId, new GroupAggregateHourVo[]{param});
        return amazonAdGroupHourReportService.getAggregateList(puid, idList, param, ReportDateModelPb.ReportDateModel.HOURLY);
    }

    @Override
    public GetGroupHourReportResponsePb.GetGroupHourReportResponse.GroupHour getGroupDailyWeeklyAndMonthly(Integer puid, GroupAggregateHourVo param, ReportDateModelPb.ReportDateModel dateModel) {
        //在这里需要查询批量暂存的idList
        String pageSign = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getPageSign).orElse("");
        Integer shopId = Optional.ofNullable(param.getAdPageBasicData()).map(AdPageBasicData::getShopId).map(Int32Value::getValue).orElse(0);
        List<String> idList = cpcPageIdsHandler.getCampaignIdsTemporary(puid, pageSign, "", shopId, new GroupAggregateHourVo[]{param});
        return amazonAdGroupHourReportService.getAggregateList(puid, idList, param, dateModel);
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateCampaignHourReport(Integer puid, CampaignAggregateHourParam param) {
        return amazonAdCampaignHourReportService.getAggregateHourList(puid, param);
    }

    @Override
    public GetPlacementHourReportResponsePb.GetPlacementHourReportResponse.PlacementHour getPlacementAggregateHourReport(Integer puid, PlacementAggregateHourParam param) {
        return amazonAdPlacementHourReportService.getAggregateList(puid, param);
    }

    @Override
    public GetCampaignHourReportResponse.CampaignHour getAggregateDailyWeeklyAndMonthlyMultiShop(int puid, List<String> relationIds, CampaignAggregateHourMultiShopParamVO param, ReportDateModelPb.ReportDateModel dateModel) {
        return amazonAdCampaignHourReportService.getAggregateListMultiShop(puid, relationIds, param, dateModel);
    }

    private void keywordAggregateParamUpdate(KeywordsPageParam param) {
        param.setSalesRateNewToBrandFTDMin(param.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setSalesRateNewToBrandFTDMax(param.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setUnitsOrderedRateNewToBrandFTDMin(param.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setUnitsOrderedRateNewToBrandFTDMax(param.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setOrderRateNewToBrandFTDMin(param.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setOrderRateNewToBrandFTDMax(param.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setSalesConversionRateMin(param.getSalesConversionRateMin() != null ? MathUtil.divide(param.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        param.setSalesConversionRateMax(param.getSalesConversionRateMax() != null ? MathUtil.divide(param.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        param.setClickRateMin(param.getClickRateMin() != null ? MathUtil.divide(param.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        param.setClickRateMax(param.getClickRateMax() != null ? MathUtil.divide(param.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        param.setAcosMin(param.getAcosMin() != null ? MathUtil.divide(param.getAcosMin(), BigDecimal.valueOf(100)) : null);
        param.setAcosMax(param.getAcosMax() != null ? MathUtil.divide(param.getAcosMax(), BigDecimal.valueOf(100)) : null);
        param.setAcotsMin(param.getAcotsMin() != null ? MathUtil.divide(param.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        param.setAcotsMax(param.getAcotsMax() != null ? MathUtil.divide(param.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        param.setAsotsMin(param.getAsotsMin() != null ? MathUtil.divide(param.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        param.setAsotsMax(param.getAsotsMax() != null ? MathUtil.divide(param.getAsotsMax(), BigDecimal.valueOf(100)) : null);
    }

    private void keywordsPageParamDateFormat(KeywordsPageParam param) {
        //日期转换格式
        param.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareStartDate(StringUtils.isEmpty(param.getCompareStartDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareEndDate(StringUtils.isEmpty(param.getCompareEndDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    private void targetingPageParamDateFormat(TargetingPageParam param) {
        //日期转换格式
        param.setStartDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setEndDate(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareStartDate(StringUtils.isEmpty(param.getCompareStartDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareEndDate(StringUtils.isEmpty(param.getCompareEndDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    private void queryWordPageParamDateFormat(CpcQueryWordDto param) {
        //日期转换格式
        param.setStart(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getStart(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setEnd(DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getEnd(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareStartDate(StringUtils.isEmpty(param.getCompareStartDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareStartDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
        param.setCompareEndDate(StringUtils.isEmpty(param.getCompareEndDate()) ? null : DateUtil.dateToStrWithFormat(DateUtil.strToDate(param.getCompareEndDate(), DateUtil.PATTERN_YYYYMMDD), DateUtil.PATTERN));
    }

    private void targetDataParamUpdate(TargetingPageParam param) {
        param.setOrderRateNewToBrandFTDMin(param.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setOrderRateNewToBrandFTDMax(param.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setSalesRateNewToBrandFTDMin(param.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setSalesRateNewToBrandFTDMax(param.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setUnitsOrderedRateNewToBrandFTDMin(param.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setUnitsOrderedRateNewToBrandFTDMax(param.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setBrandNewBuyerOrderConversionRateMin(param.getBrandNewBuyerOrderConversionRateMin() != null ? MathUtil.divide(param.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100), 6) : null);
        param.setBrandNewBuyerOrderConversionRateMax(param.getBrandNewBuyerOrderConversionRateMax() != null ? MathUtil.divide(param.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100), 6) : null);
    }

    private void targetAggregateParamUpdate(TargetingPageParam param) {
        param.setClickRateMin(param.getClickRateMin() != null ? MathUtil.divide(param.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        param.setClickRateMax(param.getClickRateMax() != null ? MathUtil.divide(param.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        param.setAcosMin(param.getAcosMin() != null ? MathUtil.divide(param.getAcosMin(), BigDecimal.valueOf(100)) : null);
        param.setAcosMax(param.getAcosMax() != null ? MathUtil.divide(param.getAcosMax(), BigDecimal.valueOf(100)) : null);
        param.setSalesConversionRateMin(param.getSalesConversionRateMin() != null ? MathUtil.divide(param.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        param.setSalesConversionRateMax(param.getSalesConversionRateMax() != null ? MathUtil.divide(param.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        param.setAcotsMin(param.getAcotsMin() != null ? MathUtil.divide(param.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        param.setAcotsMax(param.getAcotsMax() != null ? MathUtil.divide(param.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        param.setAsotsMin(param.getAsotsMin() != null ? MathUtil.divide(param.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        param.setAsotsMax(param.getAsotsMax() != null ? MathUtil.divide(param.getAsotsMax(), BigDecimal.valueOf(100)) : null);
        param.setOrderRateNewToBrandFTDMin(param.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setOrderRateNewToBrandFTDMax(param.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setSalesRateNewToBrandFTDMin(param.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setSalesRateNewToBrandFTDMax(param.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setUnitsOrderedRateNewToBrandFTDMin(param.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(param.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        param.setUnitsOrderedRateNewToBrandFTDMax(param.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(param.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        param.setBrandNewBuyerOrderConversionRateMin(param.getBrandNewBuyerOrderConversionRateMin() != null ? MathUtil.divide(param.getBrandNewBuyerOrderConversionRateMin(), BigDecimal.valueOf(100), 6) : null);
        param.setBrandNewBuyerOrderConversionRateMax(param.getBrandNewBuyerOrderConversionRateMax() != null ? MathUtil.divide(param.getBrandNewBuyerOrderConversionRateMax(), BigDecimal.valueOf(100), 6) : null);
    }


    private void handlePercentParam(PlacementPageParam campaignPageParam) {
        campaignPageParam.setClickRateMin(campaignPageParam.getClickRateMin() != null ? MathUtil.divide(campaignPageParam.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setClickRateMax(campaignPageParam.getClickRateMax() != null ? MathUtil.divide(campaignPageParam.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMin(campaignPageParam.getAcosMin() != null ? MathUtil.divide(campaignPageParam.getAcosMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMax(campaignPageParam.getAcosMax() != null ? MathUtil.divide(campaignPageParam.getAcosMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMin(campaignPageParam.getSalesConversionRateMin() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMax(campaignPageParam.getSalesConversionRateMax() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMin(campaignPageParam.getAcotsMin() != null ? MathUtil.divide(campaignPageParam.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMax(campaignPageParam.getAcotsMax() != null ? MathUtil.divide(campaignPageParam.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMin(campaignPageParam.getAsotsMin() != null ? MathUtil.divide(campaignPageParam.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMax(campaignPageParam.getAsotsMax() != null ? MathUtil.divide(campaignPageParam.getAsotsMax(), BigDecimal.valueOf(100)) : null);
//        campaignPageParam.setOrderRateNewToBrandFTDMin(campaignPageParam.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
//        campaignPageParam.setOrderRateNewToBrandFTDMax(campaignPageParam.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
//        campaignPageParam.setSalesRateNewToBrandFTDMin(campaignPageParam.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
//        campaignPageParam.setSalesRateNewToBrandFTDMax(campaignPageParam.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
//        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
//        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
    }

    /**
     * 广告活动列表页处理高级筛选百分比参数
     *
     * @param campaignPageParam
     */
    private void handlePercentParam(CampaignPageParam campaignPageParam) {
        campaignPageParam.setClickRateMin(campaignPageParam.getClickRateMin() != null ? MathUtil.divide(campaignPageParam.getClickRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setClickRateMax(campaignPageParam.getClickRateMax() != null ? MathUtil.divide(campaignPageParam.getClickRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMin(campaignPageParam.getAcosMin() != null ? MathUtil.divide(campaignPageParam.getAcosMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcosMax(campaignPageParam.getAcosMax() != null ? MathUtil.divide(campaignPageParam.getAcosMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMin(campaignPageParam.getSalesConversionRateMin() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setSalesConversionRateMax(campaignPageParam.getSalesConversionRateMax() != null ? MathUtil.divide(campaignPageParam.getSalesConversionRateMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMin(campaignPageParam.getAcotsMin() != null ? MathUtil.divide(campaignPageParam.getAcotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAcotsMax(campaignPageParam.getAcotsMax() != null ? MathUtil.divide(campaignPageParam.getAcotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMin(campaignPageParam.getAsotsMin() != null ? MathUtil.divide(campaignPageParam.getAsotsMin(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setAsotsMax(campaignPageParam.getAsotsMax() != null ? MathUtil.divide(campaignPageParam.getAsotsMax(), BigDecimal.valueOf(100)) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMin(campaignPageParam.getOrderRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setOrderRateNewToBrandFTDMax(campaignPageParam.getOrderRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getOrderRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMin(campaignPageParam.getSalesRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setSalesRateNewToBrandFTDMax(campaignPageParam.getSalesRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getSalesRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMin(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMin(), BigDecimal.valueOf(100), 6) : null);
        campaignPageParam.setUnitsOrderedRateNewToBrandFTDMax(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax() != null ? MathUtil.divide(campaignPageParam.getUnitsOrderedRateNewToBrandFTDMax(), BigDecimal.valueOf(100), 6) : null);
    }

    private void handleGroupPercentParam(GroupPageParam param) {
        param.setClickRateMin(Optional.ofNullable(param.getClickRateMin()).map(c -> MathUtil.divide(c, BigDecimal.valueOf(100))).orElse(null));
        param.setClickRateMax(Optional.ofNullable(param.getClickRateMax()).map(c -> MathUtil.divide(c, BigDecimal.valueOf(100))).orElse(null));
        param.setAcosMin(Optional.ofNullable(param.getAcosMin()).map(a -> MathUtil.divide(a, BigDecimal.valueOf(100))).orElse(null));
        param.setAcosMax(Optional.ofNullable(param.getAcosMax()).map(a -> MathUtil.divide(a, BigDecimal.valueOf(100))).orElse(null));
        param.setSalesConversionRateMin(Optional.ofNullable(param.getSalesConversionRateMin()).map(s -> MathUtil.divide(s, BigDecimal.valueOf(100))).orElse(null));
        param.setSalesConversionRateMax(Optional.ofNullable(param.getSalesConversionRateMax()).map(s -> MathUtil.divide(s, BigDecimal.valueOf(100))).orElse(null));
        param.setAcotsMin(Optional.ofNullable(param.getAcotsMin()).map(a -> MathUtil.divide(a, BigDecimal.valueOf(100))).orElse(null));
        param.setAcotsMax(Optional.ofNullable(param.getAcotsMax()).map(a -> MathUtil.divide(a, BigDecimal.valueOf(100))).orElse(null));
        param.setAsotsMin(Optional.ofNullable(param.getAsotsMin()).map(a -> MathUtil.divide(a, BigDecimal.valueOf(100))).orElse(null));
        param.setAsotsMax(Optional.ofNullable(param.getAsotsMax()).map(a -> MathUtil.divide(a, BigDecimal.valueOf(100))).orElse(null));
        param.setOrderRateNewToBrandFTDMin(Optional.ofNullable(param.getOrderRateNewToBrandFTDMin()).map(o -> MathUtil.divide(o, BigDecimal.valueOf(100), 6)).orElse(null));
        param.setOrderRateNewToBrandFTDMax(Optional.ofNullable(param.getOrderRateNewToBrandFTDMax()).map(o -> MathUtil.divide(o, BigDecimal.valueOf(100), 6)).orElse(null));
        param.setSalesRateNewToBrandFTDMin(Optional.ofNullable(param.getSalesRateNewToBrandFTDMin()).map(s -> MathUtil.divide(s, BigDecimal.valueOf(100), 6)).orElse(null));
        param.setSalesRateNewToBrandFTDMax(Optional.ofNullable(param.getSalesRateNewToBrandFTDMax()).map(s -> MathUtil.divide(s, BigDecimal.valueOf(100), 6)).orElse(null));
        param.setUnitsOrderedRateNewToBrandFTDMin(Optional.ofNullable(param.getUnitsOrderedRateNewToBrandFTDMin()).map(u -> MathUtil.divide(u, BigDecimal.valueOf(100), 6)).orElse(null));
        param.setUnitsOrderedRateNewToBrandFTDMax(Optional.ofNullable(param.getUnitsOrderedRateNewToBrandFTDMax()).map(u -> MathUtil.divide(u, BigDecimal.valueOf(100), 6)).orElse(null));
    }



    @Override
    @AdProductPermissionFilter(
            type = PermissionFilterType.CAMPAIGN,
            shopIdsExpression = "#param.shopId",
            adTypeExpression = "#param.adType",
            strategy = PermissionFilterStrategy.FILTER
    )
    public Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getAllTypeCampaignAndGroupPageDoris(CampaignAndGroupPageParam param) {

        Integer puid = param.getPuid();
        String shopId = param.getShopId();
        String type = param.getType();
        String modular = param.getModular();
        List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = Lists.newArrayList();
        //分多次查询,组合一起
        if (AdModularEnum.keywordTarget.getCode().equalsIgnoreCase(param.getModular())) {
            // 1.关键词投放：  SP手动（关键词类型）、SB（关键词类型）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(param.getType())) {
                return getCampaignAndGroupVosDoris(param.getPuid(), param.getShopId(), Constants.SP, Constants.MANUAL, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SB.equalsIgnoreCase(param.getType())) {
                return getCampaignAndGroupVosDoris(param.getPuid(), param.getShopId(), Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

        } else if (AdModularEnum.neKeyword.getCode().equalsIgnoreCase(param.getModular())) {
            // 2.否定关键词：  SP（关键词类型）、SB（关键词类型）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(param.getType()) || Constants.SP.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SB, null, GroupTypeEnum.keyword.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

        } else if (AdModularEnum.productTarget.getCode().equalsIgnoreCase(modular)) {
            // 3.商品投放：  SP自动  手动（商品投放类型）、SB（商品投放类型）、SD（全部）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos1 = Lists.newArrayList();
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos2 = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                if ("campaign".equalsIgnoreCase(param.getItemType())) {
                    return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
                } else {
                    return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, Constants.MANUAL, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
                }
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SD, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }


        } else if (AdModularEnum.neTarget.getCode().equalsIgnoreCase(modular)) {
            // 4.否定商品：  SP（商品投放类型）、SB（商品投放类型）、SD（商品）
            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> spCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SP.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, null, GroupTypeEnum.targeting.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sbCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SB.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SB, null, GroupTypeEnum.product.getCode(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

            List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> sdCampaignAndGroupVos = Lists.newArrayList();
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SD, TargetingEnum.Product.getTargetingType(), null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }
        } else if (AdModularEnum.adProduct.getCode().equalsIgnoreCase(modular)) {
            // 5.广告：  Sp sd
            if (StringUtils.isBlank(type) || Constants.SD.equalsIgnoreCase(type) || Constants.SP.equalsIgnoreCase(type)) {
                return getCampaignAndGroupVosDoris(puid, shopId, type, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }
        } else if (AdModularEnum.queryWord.getCode().equalsIgnoreCase(modular)) {
            // 6.搜索词： 关键词产生：  SP手动（关键词类型）--  (全部)
            return getCampaignAndGroupVosDoris(puid, shopId, param.getType(), null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
        } else if (AdModularEnum.queryTarget.getCode().equalsIgnoreCase(modular)) {
            // 7.搜索词： 商品投放产生：  SP自动、SP手动（商品投放类型）
            if ("campaign".equalsIgnoreCase(param.getItemType())) {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            } else {
                return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, Constants.MANUAL, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
            }

        } else if (AdModularEnum.campaignNeKeyword.getCode().equalsIgnoreCase(modular)) {
            // 8.活动否定关键词:  SP（全部）
            return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
        } else if (AdModularEnum.campaignNeTarget.getCode().equalsIgnoreCase(modular)) {
            // 9.活动否定商品：  SP自动
            return getCampaignAndGroupVosDoris(puid, shopId, Constants.SP, Constants.AUTO, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
        } else if (AdModularEnum.group.getCode().equalsIgnoreCase(modular)) {
            return getCampaignAndGroupVosDoris(puid, shopId, type, null, null, param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
        } else {
            return getCampaignAndGroupVosDoris(puid, shopId, type, param.getCampaignType(), param.getGroupType(), param.getSearchValue(), param.getItemType(), param.getCampaignIds(), param.getPortfolioId(), param.getGroupIds(), param.getPageSize(), param.getPageNo());
        }
        return new Page(1, param.getPageSize());
    }


    private Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> getCampaignAndGroupVosDoris(Integer puid, String shopId, String type, String campaignType, String groupType, String name, String itemType, String campaignIds, String portfolioId, String groupIds, int pageSize, int pageNo) {
        //查询所有类型的广告活动(sp sb sd)
        Page<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> page = new Page<>();
        page.setPageSize(pageSize);
        page.setPageNo(pageNo);
        List<AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo> campAndGroupVos = new ArrayList<>();
        if ("campaign".equalsIgnoreCase(itemType)) {
            Page<AdCampaignOptionVo> adCampaignOptionVoPage = amazonAdCampaignAllDorisDao.pageCampaignsByType(puid, shopId, type, campaignType, name, campaignIds, portfolioId, pageSize, pageNo);
            if (adCampaignOptionVoPage != null && CollectionUtils.isNotEmpty(adCampaignOptionVoPage.getRows())) {
                adCampaignOptionVoPage.getRows().stream().filter(Objects::nonNull).forEach(item -> {
                    AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                            .setCampaignId(item.getCampaignId())
                            .setCampaignName(item.getName())
                            .setState(item.getState())
                            .setType(item.getType());
                    if (StringUtils.isNotBlank(item.getTargetingType())) {
                        builder.setCostType(item.getTargetingType());
                    }
                    if (StringUtils.isNotBlank(item.getCostType())) {
                        builder.setCostType(item.getCostType());
                    }
                    campAndGroupVos.add(builder.build());
                });

            } else {
                return page;
            }
            if (adCampaignOptionVoPage != null) {
                page.setTotalPage(adCampaignOptionVoPage.getTotalPage());
                page.setTotalSize(adCampaignOptionVoPage.getTotalSize());
                page.setPageNo(adCampaignOptionVoPage.getPageNo());
                page.setPageSize(adCampaignOptionVoPage.getPageSize());
                page.setRows(campAndGroupVos);
            }
        } else if ("group".equalsIgnoreCase(itemType)) {
            if (StringUtils.isBlank(type) || type.equalsIgnoreCase(Constants.SP) || type.equalsIgnoreCase(Constants.SD) || type.equalsIgnoreCase(Constants.SB)) {
                List<String> typeList = StringUtils.isBlank(type) ? null : Stream.of(type).collect(Collectors.toList());
                Page<AdGroupOptionVo> adGroupOptionVoPage = amazonAdGroupDorisDao.pageAllGroupsByType(puid, shopId, typeList, groupType, name, campaignIds, groupIds, portfolioId, pageNo, pageSize);

                if (adGroupOptionVoPage != null && CollectionUtils.isNotEmpty(adGroupOptionVoPage.getRows())) {
                    adGroupOptionVoPage.getRows().stream().filter(Objects::nonNull).forEach(item -> {
                        AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.Builder builder = AllTypeCampaignAndGroupPageResponse.Page.CampAndGroupVo.newBuilder()
                                .setCampaignId(item.getCampaignId())
                                .setGroupName(item.getName())
                                .setGroupId(item.getGroupId())
                                .setState(item.getState() == null ? "" : item.getState())
                                .setType(item.getType());
                        campAndGroupVos.add(builder.build());
                    });
                }
                if (adGroupOptionVoPage != null) {
                    page.setTotalPage(adGroupOptionVoPage.getTotalPage());
                    page.setTotalSize(adGroupOptionVoPage.getTotalSize());
                    page.setPageNo(adGroupOptionVoPage.getPageNo());
                    page.setPageSize(adGroupOptionVoPage.getPageSize());
                    page.setRows(campAndGroupVos);
                }
            }
        }

        return page;
    }
}
