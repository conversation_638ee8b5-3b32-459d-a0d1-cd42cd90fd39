package com.meiyunji.sponsored.service.export.convert.impl;

import com.meiyunji.sponsored.service.export.constants.AdReportExportTypeEnum;
import com.meiyunji.sponsored.service.export.constants.ConvertBeanIdConstant;
import com.meiyunji.sponsored.service.export.convert.ReportVoExportConvert;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.vo.ReportVo;
import com.meiyunji.sponsored.service.cpc.vo.SearchVo;
import com.meiyunji.sponsored.service.vo.AdFlowVo;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.meiyunji.sponsored.common.util.StringUtil.keepNum;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2023-09-08  14:23
 */

@Component(ConvertBeanIdConstant.AD_FLOW)
public class FlowReportExportConvert implements ReportVoExportConvert {

    @Override
    public AdReportExportTypeEnum getReportExportType() {
        return AdReportExportTypeEnum.AD_FLOW;
    }

    public void processExcelDataList(ReportVo reportVo, List list, Map<Integer, ShopAuth> shopAuthMap, SearchVo searchVo) {
        AdFlowVo vo = new AdFlowVo();
        vo.setStartDate(searchVo.getStartDate());
        vo.setEndDate(searchVo.getEndDate());
        //每日日期格式转换
        if ("daily".equals(searchVo.getTabType()) && StringUtils.isNotBlank(reportVo.getCountDate())) {
            Date dailyDate = DateUtil.strToDate(reportVo.getCountDate(), "yyyyMMdd");
            vo.setDailyDate(DateUtil.dateToStrWithFormat(dailyDate, "yyyy-MM-dd"));
        }
        vo.setCampaignName(reportVo.getCampaignName());
        vo.setCampaignStatus(reportVo.getCampaignStatus());
        vo.setGrossImpressions(reportVo.getGrossImpressions());
        vo.setImpressions(Long.valueOf(reportVo.getImpressions()));
        vo.setInvalidImpressions(reportVo.getInvalidImpressions());
        vo.setInvalidImpressionRateStrForExcel(modifyFormat(keepNum(reportVo.getInvalidImpressionRate(), 2)));
        vo.setGrossClickThroughs(reportVo.getGrossClickThroughs());
        vo.setClicks(Long.valueOf(reportVo.getClicks()));
        vo.setInvalidClickThroughs(reportVo.getInvalidClickThroughs());
        vo.setInvalidClickThroughRateForExcel(modifyFormat(keepNum(reportVo.getInvalidClickThroughRate(), 2)));

        if (MapUtils.isNotEmpty(shopAuthMap) && shopAuthMap.containsKey(reportVo.getShopId())) {
            ShopAuth shop = shopAuthMap.get(reportVo.getShopId());
            vo.setShopName(shop.getName());
        }
        list.add(vo);
    }
    private String modifyFormat(String obj) {
        if (StringUtils.isNotBlank(obj)) {
            BigDecimal bd = new BigDecimal(obj);
            DecimalFormat df = new DecimalFormat("#.00");
            //大于0 小于1
            if (bd.compareTo(BigDecimal.ZERO) > 0 && bd.compareTo(new BigDecimal(1)) < 0) {
                return "0" + df.format(bd).toString() + "%";
            } else if (bd.compareTo(BigDecimal.ZERO) == 0) {
                return "0.00%";
            } else {
                return df.format(bd).toString() + "%";
            }
        } else {
            return null;
        }
    }
}
