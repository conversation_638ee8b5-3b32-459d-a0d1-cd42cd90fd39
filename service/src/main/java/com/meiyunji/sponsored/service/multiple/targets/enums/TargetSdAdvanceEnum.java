package com.meiyunji.sponsored.service.multiple.targets.enums;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.util.HashSet;
import java.util.Set;

/**
 * 投放sb列表页-高级筛选字段枚举
 *
 * @author: zzh
 * @create: 2024-10-18 10:05
 */
@Getter
public enum TargetSdAdvanceEnum {
    BIDDING_MIN("biddingMin", " and IFNULL(t.bid, g.default_bid) >= ? ", new HashSet<>()),
    BIDDING_MAX("biddingMax", " and IFNULL(t.bid, g.default_bid) <= ? ", new HashSet<>()),
    IMPRESSIONS_MIN("impressionsMin", " and impressionsDoris >= ? ", CollectionUtil.newHashSet("impressionsDoris")),
    IMPRESSIONS_MAX("impressionsMax", " and impressionsDoris <= ? ", CollectionUtil.newHashSet("impressionsDoris")),
    CLICKS_MIN("clicksMin", " and clicksDoris >= ? ", CollectionUtil.newHashSet("clicksDoris")),
    CLICKS_MAX("clicksMax", " and clicksDoris <= ? ", CollectionUtil.newHashSet("clicksDoris")),
    CLICK_RATE_MIN("clickRateMin", " and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) >= ? ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    CLICK_RATE_MAX("clickRateMax", " and ROUND(ifnull(clicksDoris/impressionsDoris,0),4) <= ? ", CollectionUtil.newHashSet("clicksDoris", "impressionsDoris")),
    COST_MIN("costMin", " and costDoris >= ? ", CollectionUtil.newHashSet("costDoris")),
    COST_MAX("costMax", " and costDoris <= ? ", CollectionUtil.newHashSet("costDoris")),
    CPA_MIN("cpaMin", " and ROUND(ifnull(costDoris/orderNumDoris, 0), 4) >= ? ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    CPA_MAX("cpaMax", " and ROUND(ifnull(costDoris/orderNumDoris, 0), 4) <= ? ", CollectionUtil.newHashSet("costDoris", "orderNumDoris")),
    CPC_MIN("cpcMin", " and ROUND(ifnull(costDoris/clicksDoris,0),2) >= ? ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    CPC_MAX("cpcMax", " and ROUND(ifnull(costDoris/clicksDoris,0),2) <= ? ", CollectionUtil.newHashSet("costDoris", "clicksDoris")),
    VCPM_MIN("vcpmMin", " and ROUND(ifnull((costDoris/viewImpressionsDoris) * 1000, 0), 4) >= ? ", CollectionUtil.newHashSet("costDoris", "viewImpressionsDoris")),
    VCPM_MAX("vcpmMax", " and ROUND(ifnull((costDoris/viewImpressionsDoris) * 1000, 0), 4) <= ? ", CollectionUtil.newHashSet("costDoris", "viewImpressionsDoris")),
    ORDER_NUM_MIN("orderNumMin", " and orderNumDoris >= ? ", CollectionUtil.newHashSet("orderNumDoris")),
    ORDER_NUM_MAX("orderNumMax", " and orderNumDoris <= ? ", CollectionUtil.newHashSet("orderNumDoris")),
    AD_SALE_NUM_MIN("adSaleNumMin", " and ifnull(adOrderNumDoris, 0) >= ? ", CollectionUtil.newHashSet("adOrderNumDoris")),
    AD_SALE_NUM_MAX("adSaleNumMax", " and ifnull(adOrderNumDoris, 0) <= ? ", CollectionUtil.newHashSet("adOrderNumDoris")),
    SALES_MIN("salesMin", " and totalSalesDoris >= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    SALES_MAX("salesMax", " and totalSalesDoris <= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES_MIN("adSalesMin", " and ifnull(adSalesDoris, 0) >= ? ", CollectionUtil.newHashSet("adSalesDoris")),
    AD_SALES_MAX("adSalesMax", " and ifnull(adSalesDoris, 0) <= ? ", CollectionUtil.newHashSet("adSalesDoris")),
    ACOS_MIN("acosMin", " and ROUND(ifnull(costDoris/totalSalesDoris,0),4) >= ? ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ACOS_MAX("acosMax", " and ROUND(ifnull(costDoris/totalSalesDoris,0),4) <= ? ", CollectionUtil.newHashSet("costDoris", "totalSalesDoris")),
    ROAS_MIN("roasMin", " and ROUND(ifnull(totalSalesDoris/costDoris,0),4) >= ? ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    ROAS_MAX("roasMax", " and ROUND(ifnull(totalSalesDoris/costDoris,0),4) <= ? ", CollectionUtil.newHashSet("totalSalesDoris", "costDoris")),
    SALES_CONVERSION_RATE_MIN("salesConversionRateMin", " and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) >= ? ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    SALES_CONVERSION_RATE_MAX("salesConversionRateMax", " and ROUND(ifnull(orderNumDoris/clicksDoris,0),4) <= ? ", CollectionUtil.newHashSet("orderNumDoris", "clicksDoris")),
    ACOTS_MIN("acotsMin", " and ROUND(ifnull(costDoris/shopSalesDoris,0),4) >= ? ", CollectionUtil.newHashSet("costDoris")),
    ACOTS_MAX("acotsMax", " and ROUND(ifnull(costDoris/shopSalesDoris,0),4) <= ? ", CollectionUtil.newHashSet("costDoris")),
    ASOTS_MIN("asotsMin", " and ROUND(ifnull(totalSalesDoris/shopSalesDoris,0),4) >= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    ASOTS_MAX("asotsMax", " and ROUND(ifnull(totalSalesDoris/shopSalesDoris,0),4) <= ? ", CollectionUtil.newHashSet("totalSalesDoris")),
    AD_SALES_TOTAL_MIN("adSalesTotalMin", " and saleNumDoris >= ? ", CollectionUtil.newHashSet("saleNumDoris")),
    AD_SALES_TOTAL_MAX("adSalesTotalMax", " and saleNumDoris <= ? ", CollectionUtil.newHashSet("saleNumDoris")),
    BRAND_NEW_BUYER_ORDER_CONVERSION_RATE_MIN("brandNewBuyerOrderConversionRateMin", " and ROUND(ifnull(ordersNewToBrand14dDoris/clicksDoris, 0), 4) >= ? ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "clicksDoris")),
    BRAND_NEW_BUYER_ORDER_CONVERSION_RATE_MAX("brandNewBuyerOrderConversionRateMax", " and ROUND(ifnull(ordersNewToBrand14dDoris/clicksDoris, 0), 4) <= ? ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "clicksDoris")),
    ORDERS_NEW_TO_BRAND_MIN("ordersNewToBrandFTDMin", " and ifnull(ordersNewToBrand14dDoris, 0) >= ? ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris")),
    ORDERS_NEW_TO_BRAND_MAX("ordersNewToBrandFTDMax", " and ifnull(ordersNewToBrand14dDoris, 0) <= ? ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris")),
    ORDER_RATE_NEW_TO_BRAND_FTD_MIN("orderRateNewToBrandFTDMin", " and ROUND(ROUND(ifnull((ordersNewToBrand14dDoris/orderNumDoris), 0), 6), 4) >= ? ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "orderNumDoris")),
    ORDER_RATE_NEW_TO_BRAND_FTD_MAX("orderRateNewToBrandFTDMax", " and ROUND(ROUND(ifnull((ordersNewToBrand14dDoris/orderNumDoris), 0), 6), 4) <= ? ", CollectionUtil.newHashSet("ordersNewToBrand14dDoris", "orderNumDoris")),
    SALES_NEW_TO_BRAND_MIN("salesNewToBrandFTDMin", " and ifnull(salesNewToBrand14dDoris, 0) >= ? ", CollectionUtil.newHashSet("salesNewToBrand14dDoris")),
    SALES_NEW_TO_BRAND_MAX("salesNewToBrandFTDMax", " and ifnull(salesNewToBrand14dDoris, 0) <= ? ", CollectionUtil.newHashSet("salesNewToBrand14dDoris")),
    SALES_RATE_NEW_TO_BRAND_FTD_MIN("salesRateNewToBrandFTDMin", " and ROUND(ROUND(ifnull((salesNewToBrand14dDoris/totalSalesDoris), 0), 6), 4) >= ? ", CollectionUtil.newHashSet("salesNewToBrand14dDoris", "totalSalesDoris")),
    SALES_RATE_NEW_TO_BRAND_FTD_MAX("salesRateNewToBrandFTDMax", " and ROUND(ROUND(ifnull((salesNewToBrand14dDoris/totalSalesDoris), 0), 6), 4) <= ? ", CollectionUtil.newHashSet("salesNewToBrand14dDoris", "totalSalesDoris")),
    VIEW_ABILITY_RATE_MIN("viewabilityRateMin", " and ROUND(ifnull((viewImpressionsDoris/impressionsDoris) * 100, 0), 4) >= ? ", CollectionUtil.newHashSet("viewImpressionsDoris", "impressionsDoris")),
    VIEW_ABILITY_RATE_MAX("viewabilityRateMax", " and ROUND(ifnull((viewImpressionsDoris/impressionsDoris) * 100, 0), 4) <= ? ", CollectionUtil.newHashSet("viewImpressionsDoris", "impressionsDoris")),
    VIEW_CLICK_THROUGH_RATE_MIN("viewClickThroughRateMin", " and ROUND(ifnull((clicksDoris/viewImpressionsDoris), 0), 4) >= ? ", CollectionUtil.newHashSet("clicksDoris", "viewImpressionsDoris")),
    VIEW_CLICK_THROUGH_RATE_MAX("viewClickThroughRateMax", " and ROUND(ifnull((clicksDoris/viewImpressionsDoris), 0), 4) <= ? ", CollectionUtil.newHashSet("clicksDoris", "viewImpressionsDoris")),
    BRANDED_SEARCHES_MIN("brandedSearchesMin", " and brandedSearches14dDoris >= ? ", CollectionUtil.newHashSet("brandedSearches14dDoris")),
    BRANDED_SEARCHES_MAX("brandedSearchesMax", " and brandedSearches14dDoris <= ? ", CollectionUtil.newHashSet("brandedSearches14dDoris")),
    ADVERTISING_UNIT_PRICE_MIN("advertisingUnitPriceMin", " and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) >= ? ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    ADVERTISING_UNIT_PRICE_MAX("advertisingUnitPriceMax", " and ROUND(ifnull(totalSalesDoris/orderNumDoris, 0), 2) <= ? ", CollectionUtil.newHashSet("totalSalesDoris", "orderNumDoris")),
    ADD_TO_CART_MIN("addToCartMin", " and addToCartDoris >= ? ", CollectionUtil.newHashSet("addToCartDoris")),
    ADD_TO_CART_MAX("addToCartMax", " and addToCartDoris <= ? ", CollectionUtil.newHashSet("addToCartDoris")),
    VIDEO_COMPLETE_VIEWS_MIN("videoCompleteViewsMin", " and ifnull(videoCompleteViewsDoris , 0) >= ? ", CollectionUtil.newHashSet("videoCompleteViewsDoris")),
    VIDEO_COMPLETE_VIEWS_MAX("videoCompleteViewsMax", " and ifnull(videoCompleteViewsDoris , 0) <= ? ", CollectionUtil.newHashSet("videoCompleteViewsDoris")),
    ;

    // 高级筛选字段
    private final String code;
    // having字段
    private final String havingBy;
    // 涉及字段
    private final Set<String> columnList;

    TargetSdAdvanceEnum(String code, String havingBy, Set<String> columnList) {
        this.code = code;
        this.havingBy = havingBy;
        this.columnList = columnList;
    }

    /**
     * 根据code获取统计字段集合
     */
    public static Set<String> getSetByCode(String code) {
        for (TargetSdAdvanceEnum advanceEnum : TargetSdAdvanceEnum.values()) {
            if (advanceEnum.getCode().equals(code)) {
                return advanceEnum.getColumnList();
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据code获取高级筛选
     */
    public static String getHavingByByCode(String code , String targetType) {
        for (TargetSdAdvanceEnum havingByEnum : TargetSdAdvanceEnum.values()) {
            if (havingByEnum.getCode().equals(code)) {
                return havingByEnum.getHavingBy();
            }
        }
        return null;
    }
}
