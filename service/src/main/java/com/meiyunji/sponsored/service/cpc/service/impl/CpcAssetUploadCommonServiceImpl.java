package com.meiyunji.sponsored.service.cpc.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.amazon.advertising.assets.entity.assetsGet.AssetVersionList;
import com.amazon.advertising.assets.entity.assetsGet.GetAssetResult;
import com.amazon.advertising.assets.entity.assetsRegister.CaProgramSpecifications;
import com.amazon.advertising.assets.entity.assetsRegister.CaSpecificationList;
import com.amazon.advertising.assets.entity.assetsRegister.RegisterAssetResult;
import com.amazon.advertising.assets.entity.assetsUpload.UploadAssetResult;
import com.amazon.advertising.assets.enums.AssetSubTypeEnum;
import com.amazon.advertising.assets.enums.AssetTypeEnum;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.service.CpcAssetUploadCommonService;
import com.meiyunji.sponsored.service.cpc.service2.assets.CpcAssetsApiService;
import com.meiyunji.sponsored.service.cpc.vo.SBCommonErrorVo;
import com.meiyunji.sponsored.service.cpc.vo.assets.ImageAssetVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: ys
 * @date: 2024/8/16 15:21
 * @describe:
 */
@Service
@Slf4j
public class CpcAssetUploadCommonServiceImpl implements CpcAssetUploadCommonService {

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private IAmazonAdProfileDao amazonAdProfileDao;
    @Autowired
    private CpcAssetsApiService cpcAssetsApiService;
    @Override
    public Result<ImageAssetVo> uploadVideoAsset(Integer puid, Integer shopId,
                                                 String fileName, String videoType,
                                                 byte[] bytes, String brandEntityId,
                                                 List<String> tags, List<String> checkTypeList) {
        if (Objects.isNull(puid) || Objects.isNull(shopId)) {
            log.error("upload video asset param is null error puid:{}, shopId:{}", puid, shopId);
            return ResultUtil.returnErr("没有CPC授权");
        }
        if (Objects.isNull(bytes) || bytes.length == 0) {
            log.error("upload video content is null, puid:{}, shopId:{}", puid, shopId);
            return ResultUtil.returnErr("对应上传的视频资源为空");
        }
        if (CollectionUtils.isEmpty(tags)
                || StringUtils.isEmpty(fileName) || StringUtils.isEmpty(videoType)) {
            log.error("upload video about video param is null,, tags:{}, fileName:{}," +
                    "videoType:{}", tags, fileName, videoType);
            return ResultUtil.returnErr("对应的上传视频参数不能为空");
        }
        ShopAuth shop = shopAuthDao.getScAndVcByIdAndPuid(shopId, puid);
        if (shop == null) {
            return ResultUtil.returnErr("没有CPC授权");
        }

        AmazonAdProfile profile = amazonAdProfileDao.getProfile(puid, shopId);
        if (profile == null) {
            log.error("upload video user profile is null, puid:{}, shopId:{}", puid, shopId);
            return ResultUtil.returnErr("没有站点对应的配置信息");
        }
        Result<UploadAssetResult> urlResult = cpcAssetsApiService.getAssetUploadUrl(shop, profile, fileName);
        if (urlResult.error()) {
            log.error("upload video get asset upload url error:{}, puid:{}, shopId:{}", urlResult.getMsg(), puid, shopId);
            return ResultUtil.returnErr("网络超时,请稍后重试."+urlResult.getMsg());
        }
        String uploadUrl = urlResult.getData().getUrl();
        Result<?> uploadResult = cpcAssetsApiService.uploadAsset(shop, uploadUrl, bytes, videoType);
        if (uploadResult.error()){
            log.error("upload video upload asset content error:{}, puid:{}, shopId:{}", urlResult.getMsg(), puid, shopId);
            return ResultUtil.returnErr("网络超时,请稍后重试."+urlResult.getMsg());
        }
        Result<RegisterAssetResult> registerResult = cpcAssetsApiService.registerAsset(shop, profile, fileName, uploadUrl, AssetTypeEnum.VIDEO.name(),
                Collections.singletonList(AssetSubTypeEnum.BACKGROUND_VIDEO.name()), tags, Optional.ofNullable(brandEntityId).map(Collections::singletonList).orElse(null), null);
        if (registerResult.error()) {
            log.error("upload video register asset error:{}, puid:{}, shopId:{}", urlResult.getMsg(), puid, shopId);
            return ResultUtil.returnErr("网络超时,请稍后重试."+registerResult.getMsg());
        }

        //直接在此处进行判断是否符合视频规则
        RegisterAssetResult data = registerResult.getData();

        if (Objects.isNull(data)) {
            log.error("upload video register data is null, puid:{}, shopId:{}", puid, shopId);
            return ResultUtil.returnErr("视频素材向亚马逊注册失败");
        }
        List<CaProgramSpecifications> checkFailList = data.getFailedSpecChecks();
        if (CollectionUtils.isNotEmpty(checkFailList) && CollectionUtils.isNotEmpty(checkTypeList)) {
            List<String> failCheckMsg = new ArrayList<>();
            Map<String, CaProgramSpecifications> failMap = checkFailList.parallelStream().collect(Collectors.toMap(CaProgramSpecifications::getSpecProgramName, v1 -> v1, (old, current) -> current));
            for (String check : checkTypeList) {
                CaProgramSpecifications failCheck;
                if (Objects.isNull(failCheck = failMap.get(check)) || CollectionUtils.isEmpty(failCheck.getSpecifications())) continue;
                for (CaSpecificationList spec : failCheck.getSpecifications()) {
                    List<String> failMsgList = spec.getArguments();
                    failMsgList.add(spec.getActualValue());
                    failCheckMsg.add(SBCommonErrorVo.getAssetErrorVo(spec.getFailureReason(), failMsgList).getMsg());
                }
            }
            if (CollectionUtils.isNotEmpty(failCheckMsg)) {
                return ResultUtil.returnErr(JSONObject.toJSONString(failCheckMsg));
            }
        }

        ImageAssetVo assetVo = new ImageAssetVo();
        assetVo.setAssetId(data.getAssetId());
        assetVo.setVersionId(data.getVersionId());
        Result<GetAssetResult> assetResult = cpcAssetsApiService.getAsset(shop, profile, assetVo.getAssetId(), assetVo.getVersionId());
        if (assetResult.error()) {
            log.error("upload video get asset error:{}, puid:{}, shopId:{}", urlResult.getMsg(), puid, shopId);
            return ResultUtil.returnSucc(assetVo);
        }

        GetAssetResult asset;
        List<AssetVersionList> versions;
        if ((asset = assetResult.getData()) != null && CollectionUtils.isNotEmpty(versions = asset.getAssetVersionList())) {
            assetVo.setAssetId(asset.getAssetGlobal().getAssetId());
            AssetVersionList version = versions.get(0);

            //校验视频上传后的标签，检查是否符合创建SBV的要求
            List<String> checkApprovedList = version.getSpecCheckApprovedPrograms();
            if (CollectionUtils.isEmpty(checkApprovedList)) {
                return ResultUtil.returnErr("亚马逊上传请求超时，请稍后重试");
            }
            if (checkApprovedList.stream().noneMatch(checkTypeList::contains)) {
                return ResultUtil.returnErr("视频格式不符合要求，请检查后重新上传");
            }
            assetVo.setName(version.getName());
            assetVo.setUrl(version.getUrl());
            assetVo.setMediaType(version.getFileMetadata().getContentType());
            assetVo.setHeight(version.getFileMetadata().getHeight());
            assetVo.setWidth(version.getFileMetadata().getWidth());
        }
        return ResultUtil.returnSucc(assetVo);
    }
}
