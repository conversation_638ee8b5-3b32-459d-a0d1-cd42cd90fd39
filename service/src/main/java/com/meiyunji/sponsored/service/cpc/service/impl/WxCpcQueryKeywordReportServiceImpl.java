package com.meiyunji.sponsored.service.cpc.service.impl;

import com.google.common.collect.Lists;
import com.google.protobuf.BoolValue;
import com.google.protobuf.DoubleValue;
import com.google.protobuf.Int32Value;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.util.AssertUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.DoubleUtil;
import com.meiyunji.sponsored.common.util.MathUtil;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeAggregateDataRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AdHomeChartRpcVo;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordAggregateDataResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllQueryWordDataResponse;
import com.meiyunji.sponsored.rpc.vo.ReportRpcVo;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.po.*;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service.IWxCpcQueryKeywordReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/1/3
 */
@Service
@Slf4j
public class WxCpcQueryKeywordReportServiceImpl extends ReportService<CpcQueryKeywordReport> implements IWxCpcQueryKeywordReportService {

    @Autowired
    private IWxCpcQueryKeywordReportDao wxCpcQueryKeywordReportDao;
    @Autowired
    private IAmazonAdCampaignDao amazonAdCampaignDao;

    @Autowired
    private IAmazonAdGroupDao amazonAdGroupDao;

    @Autowired
    private AdChartDataProcess adChartDataProcess;

    @Autowired
    private IScVcShopAuthDao shopAuthDao;

    @Autowired
    private CpcShopDataService cpCShopDataService;
    @Autowired
    private IAmazonAdPortfolioDao portfolioDao;

    @Autowired
    private IAmazonAdNeKeywordDao amazonAdNekeywordDao;

    @Autowired
    private IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;



    public AllQueryWordDataResponse.AdQueryWordsHomeVo getAllQueryWordData(Integer puid, CpcQueryWordDto dto, Page page) {

        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);

        //组装vo
        long allKeywordTime = System.currentTimeMillis();
        Page pageVo = getAdQueryWordsPageVo(puid, dto, page);
        log.info("==============================查询所有搜索词关键词花费时间 {} ==============================", System.currentTimeMillis() - allKeywordTime);
        if (pageVo.getTotalSize() > Constants.TOTALSIZELIMIT) {
            int totalPage = Constants.TOTALSIZELIMIT / pageVo.getPageSize();
            pageVo.setTotalPage(totalPage);
            pageVo.setTotalSize(Constants.TOTALSIZELIMIT);
        }

        //处理分页
        AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.Builder pageBuilder = AllQueryWordDataResponse.AdQueryWordsHomeVo.Page.newBuilder();
        pageBuilder.setPageNo(Int32Value.of(pageVo.getPageNo()));
        pageBuilder.setPageSize(Int32Value.of(pageVo.getPageSize()));
        pageBuilder.setTotalPage(Int32Value.of(pageVo.getTotalPage()));
        pageBuilder.setTotalSize(Int32Value.of(pageVo.getTotalSize()));
        List<ReportVo> rows = pageVo.getRows();
        if (CollectionUtils.isNotEmpty(rows)) {
            List<ReportRpcVo> rpcVos = rows.stream().filter(Objects::nonNull).map(this::convertToProtobufBean).collect(Collectors.toList());
            pageBuilder.addAllRows(rpcVos);
        }

        return AllQueryWordDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setPage(pageBuilder.build())
                .build();
    }


    public AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo getAllQueryWordAggregateData(Integer puid, CpcQueryWordDto dto) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcById(dto.getShopId());
        if (shopAuth == null) {
            AssertUtil.fail("店铺未授权");
        }

        ShopSaleDto shopSaleDto = cpCShopDataService.getShopSaleData(dto.getShopId(), dto.getStart(), dto.getEnd());
        BigDecimal shopSaleData;
        if (shopSaleDto == null || shopSaleDto.getSumRange() == null) {
            shopSaleData = BigDecimal.ZERO;
        } else {
            shopSaleData = shopSaleDto.getSumRange();
        }
        dto.setShopSales(shopSaleData);

        String currency = AmznEndpoint.getByMarketplaceId(shopAuth.getMarketplaceId()).getCurrencyCode().value();


        LocalDate startDate = LocalDate.parse(dto.getStart(), DateTimeFormatter.BASIC_ISO_DATE);
        LocalDate endDate = LocalDate.parse(dto.getEnd(), DateTimeFormatter.BASIC_ISO_DATE);
        Period between = Period.between(startDate, endDate);
        String compareStartDate = startDate.minus(between).minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));
        String compareEndDate = startDate.minusDays(1L).format(DateTimeFormatter.ofPattern(DateUtil.PATTERN_YYYYMMDD));

        CpcQueryWordDto compareDto = new CpcQueryWordDto();
        BeanUtils.copyProperties(dto, compareDto);
        compareDto.setStart(compareStartDate);
        compareDto.setEnd(compareEndDate);


        //按条件查询所有数据
        List<AdHomePerformancedto> list = new ArrayList<>();
        List<AdHomePerformancedto> compareList = new ArrayList<>();
        List<AdHomePerformancedto> reportDayList = new ArrayList<>();

        boolean isNull = false;

        if (StringUtils.isNotBlank(dto.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, dto.getShopId(), dto.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                dto.setCampaignIdList(campaignIds);
            } else {
                isNull = true;
            }
        }

        if (isNull) {
            list = new ArrayList<>();
            compareList = new ArrayList<>();
            reportDayList = new ArrayList<>();
        } else {
            if (StringUtils.isNotBlank(dto.getKeywordId())) {
                List<AdHomePerformancedto> keywordList = getReportKeywordByDateOrHistory(puid,dto);
                List<AdHomePerformancedto> compareKeywordList = getReportKeywordByDateOrHistory(puid, compareDto);
                if (CollectionUtils.isNotEmpty(compareKeywordList)) {
                    compareList.addAll(compareKeywordList);
                }
                if (CollectionUtils.isNotEmpty(keywordList)) {
                    list.addAll(keywordList);
                    List<String> keywordIdList = keywordList.stream().map(AdHomePerformancedto::getKeywordId).collect(Collectors.toList());

                    List<AdHomePerformancedto> keywordDayList = getReportKeywordByKeywordIdListOrHistory(puid,dto,keywordIdList);
                    if (CollectionUtils.isNotEmpty(keywordDayList)) {
                        reportDayList.addAll(keywordDayList);
                    }
                }
            }
            if (StringUtils.isNotBlank(dto.getTargetId())) {
                List<AdHomePerformancedto> targetList = getReportTargetByDateOrHistory(puid,dto);
                List<AdHomePerformancedto> compareTargetList = getReportTargetByDateOrHistory(puid,compareDto);
                if (CollectionUtils.isNotEmpty(compareTargetList)) {
                    compareList.addAll(compareTargetList);
                }
                if (CollectionUtils.isNotEmpty(targetList)) {
                    list.addAll(targetList);
                    List<String> targetIdList = targetList.stream().map(AdHomePerformancedto::getTargetId).collect(Collectors.toList());

                    List<AdHomePerformancedto> targetDayList = getReportTargetByTargetIdListOrHistory(puid,dto,targetIdList);
                    if (CollectionUtils.isNotEmpty(targetDayList)) {
                        reportDayList.addAll(targetDayList);
                    }
                }
            }
        }

        //汇总指标数据
        AdHomeAggregateDataRpcVo aggregateDataVo = getQueryKeywordAggregateDataVo(list, shopSaleData);
        AdHomeAggregateDataRpcVo compareAggregateDataVo = getQueryKeywordAggregateDataVo(compareList, shopSaleData);

        //处理环比数据
        AdHomeAggregateDataRpcVo adHomeAggregateDataRpcVo = getQueryKeywordAggregateDataChainVo(aggregateDataVo, compareAggregateDataVo);


        //处理chart数据
        List<AdHomeChartRpcVo> dayPerformanceVos = adChartDataProcess.getDayPerformanceVos(currency, reportDayList, shopSaleData);

        return AllQueryWordAggregateDataResponse.AdQueryWordsHomeVo.newBuilder()
                .setAggregateDataVo(adHomeAggregateDataRpcVo)
                .addAllDay(dayPerformanceVos)
                .build();
    }


    /**
     * 汇总数据组装
     *
     * @param rows
     * @return
     */
    private AdHomeAggregateDataRpcVo getQueryKeywordAggregateDataVo(List<AdHomePerformancedto> rows, BigDecimal shopSales) {

        //为前端渲染页面   集合为0时,也返回对象,不返回null
        if (CollectionUtils.isEmpty(rows)) {
            return AdHomeAggregateDataRpcVo.newBuilder()
                    .setAcos("0")
                    .setAsots("0")
                    .setAcots("0")
                    .setRoas("0")
                    .setAdCost("0")
                    .setAdCostPerClick("0")
                    .setAdOrderNum(Int32Value.of(0))
                    .setCvr("0")
                    .setCtr("0")
                    .setAdSale("0")
                    .setClicks(Int32Value.of(0))
                    .setImpressions(Int32Value.of(0))
                    .setCpa("0")
                    .setOrderNum(Int32Value.of(0))
                    .build();
        }

        //点击量
        int sumClicks = rows.stream().filter(item -> item.getClicks() != null).mapToInt(AdHomePerformancedto::getClicks).sum();
        //广告订单数
        int sumAdOrderNum = rows.stream().filter(item -> item.getAdOrderNum() != null).mapToInt(AdHomePerformancedto::getAdOrderNum).sum();
        //曝光量
        int sumImpressions = rows.stream().filter(item -> item.getImpressions() != null).mapToInt(AdHomePerformancedto::getImpressions).sum();
        //广告销售额
        BigDecimal sumAdSale = rows.stream().filter(item -> item.getAdSale() != null).map(e -> e.getAdSale()).reduce(BigDecimal.ZERO, BigDecimal::add);
        //广告花费
        BigDecimal sumAdcost = rows.stream().filter(item -> item.getAdCost() != null).map(e -> e.getAdCost()).reduce(BigDecimal.ZERO, BigDecimal::add);

        //ACoS
        BigDecimal sumAcos = sumAdSale.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(sumAdSale, 4, BigDecimal.ROUND_HALF_UP);
        //平均点击费
        BigDecimal sumAdCostPerClick = sumClicks == 0 ? BigDecimal.ZERO : sumAdcost.divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //订单转化率
        BigDecimal sumCVr = sumClicks == 0 ? BigDecimal.ZERO : new BigDecimal(sumAdOrderNum).multiply(new BigDecimal("100")).divide(new BigDecimal(sumClicks), 4, BigDecimal.ROUND_HALF_UP);
        //点击率（CTR）
        BigDecimal sumCtr = sumImpressions == 0 ? BigDecimal.ZERO : new BigDecimal(sumClicks).multiply(new BigDecimal("100")).divide(new BigDecimal(sumImpressions), 4, BigDecimal.ROUND_HALF_UP);
        //roas
        BigDecimal roas = sumAdcost.compareTo(BigDecimal.ZERO) == 0  ? BigDecimal.ZERO : sumAdSale.divide(sumAdcost, 4, BigDecimal.ROUND_HALF_UP);
        //acots
        BigDecimal acots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdcost.multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP);//asots
        BigDecimal asots = shopSales.compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : sumAdSale.multiply(new BigDecimal("100")).divide(shopSales, 4, BigDecimal.ROUND_HALF_UP);
        BigDecimal cpa = sumAdcost.compareTo(BigDecimal.ZERO) == 0 || BigDecimal.valueOf(sumAdOrderNum).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(sumAdcost, BigDecimal.valueOf(sumAdOrderNum));
        //CPC,VCPM广告销量
        int sumSalesNum = rows.stream().filter(item -> item != null && item.getSalesNum() != null).mapToInt(AdHomePerformancedto::getSalesNum).sum();

        return AdHomeAggregateDataRpcVo.newBuilder()
                .setAcos(sumAcos.stripTrailingZeros().toPlainString())
                .setAcots(acots.stripTrailingZeros().toPlainString())
                .setAsots(asots.stripTrailingZeros().toPlainString())
                .setRoas(roas.stripTrailingZeros().toPlainString())
                .setAdCost(sumAdcost.stripTrailingZeros().toPlainString())
                .setAdCostPerClick(sumAdCostPerClick.stripTrailingZeros().toPlainString())
                .setAdOrderNum(Int32Value.of(sumAdOrderNum))
                .setCvr(sumCVr.stripTrailingZeros().toPlainString())
                .setCtr(sumCtr.stripTrailingZeros().toPlainString())
                .setAdSale(sumAdSale.stripTrailingZeros().toPlainString())
                .setClicks(Int32Value.of(sumClicks))
                .setImpressions(Int32Value.of(sumImpressions))
                .setCpa(cpa.stripTrailingZeros().toPlainString())
                .setOrderNum(Int32Value.of(sumSalesNum))
                .build();
    }


    private AdHomeAggregateDataRpcVo getQueryKeywordAggregateDataChainVo(AdHomeAggregateDataRpcVo aggregateDataVo,AdHomeAggregateDataRpcVo compareAggregateDataVo){
        AdHomeAggregateDataRpcVo.Builder builder = aggregateDataVo.toBuilder();
        if (compareAggregateDataVo == null) {
            return builder
                    .setAcosCompare("0")
                    .setClicksCompare(0)
                    .setCtrCompare("0")
                    .setCvrCompare("0")
                    .setImpressionsCompare(0)
                    .setAdCostCompare("0")
                    .setAdSaleCompare("0")
                    .setAdOrderNumCompare(0)
                    .setAdCostPerClickCompare("0")
                    .setAcosChain("0")
                    .setClicksChain("0")
                    .setCtrChain("0")
                    .setCvrChain("0")
                    .setImpressionsChain("0")
                    .setAdCostChain("0")
                    .setAdSaleChain("0")
                    .setAdOrderNumChain("0")
                    .setAdCostPerClickChain("0")
                    .build();
        }

        return builder
                .setAcosCompare(compareAggregateDataVo.getAcos())
                .setClicksCompare(compareAggregateDataVo.getClicks().getValue())
                .setCtrCompare(compareAggregateDataVo.getCtr())
                .setCvrCompare(compareAggregateDataVo.getCvr())
                .setImpressionsCompare(compareAggregateDataVo.getImpressions().getValue())
                .setAdCostCompare(compareAggregateDataVo.getAdCost())
                .setAdSaleCompare(compareAggregateDataVo.getAdSale())
                .setAdOrderNumCompare(compareAggregateDataVo.getAdOrderNum().getValue())
                .setAdCostPerClickCompare(compareAggregateDataVo.getAdCostPerClick())
                .setAcosChain(calculationChain(new BigDecimal(aggregateDataVo.getAcos()),new BigDecimal(compareAggregateDataVo.getAcos())))
                .setClicksChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getClicks().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getClicks().getValue())))
                .setCtrChain(calculationChain(new BigDecimal(aggregateDataVo.getCtr()),new BigDecimal(compareAggregateDataVo.getCtr())))
                .setCvrChain(calculationChain(new BigDecimal(aggregateDataVo.getCvr()),new BigDecimal(compareAggregateDataVo.getCvr())))
                .setImpressionsChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getImpressions().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getImpressions().getValue())))
                .setAdCostChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCost()),new BigDecimal(compareAggregateDataVo.getAdCost())))
                .setAdSaleChain(calculationChain(new BigDecimal(aggregateDataVo.getAdSale()),new BigDecimal(compareAggregateDataVo.getAdSale())))
                .setAdOrderNumChain(calculationChain(BigDecimal.valueOf(aggregateDataVo.getAdOrderNum().getValue()),BigDecimal.valueOf(compareAggregateDataVo.getAdOrderNum().getValue())))
                .setAdCostPerClickChain(calculationChain(new BigDecimal(aggregateDataVo.getAdCostPerClick()),new BigDecimal(compareAggregateDataVo.getAdCostPerClick())))
                .build();
    }

    private String calculationChain(BigDecimal data,BigDecimal dataCompare) {
        if (dataCompare.compareTo(BigDecimal.ZERO) == 0 && data.compareTo(BigDecimal.ZERO) == 1) {
            return "100.0000";
        }
        return MathUtil.divideByZero(MathUtil.subtract(data, dataCompare).multiply(BigDecimal.valueOf(100)), dataCompare).toString();
    }




    private ReportRpcVo convertToProtobufBean(ReportVo item) {
        ReportRpcVo.Builder voBuilder = ReportRpcVo.newBuilder();
        if (org.apache.commons.lang.StringUtils.isNotBlank(item.getCountDate())) {
            voBuilder.setCountDate(item.getCountDate());
        }

        if (item.getShopId() != null) {
            voBuilder.setShopId(Int32Value.of(item.getShopId()));
        }
        voBuilder.setCpc(DoubleValue.of(Optional.ofNullable(item.getCpc()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setImpressions(Int32Value.of(Optional.ofNullable(item.getImpressions()).orElse(0)));
        voBuilder.setClicks(Int32Value.of(Optional.ofNullable(item.getClicks()).orElse(0)));
        voBuilder.setOrderNum(Int32Value.of(Optional.ofNullable(item.getOrderNum()).orElse(0)));
        voBuilder.setSaleNum(Int32Value.of(Optional.ofNullable(item.getSaleNum()).orElse(0)));
        voBuilder.setClickRate(DoubleValue.of(Optional.ofNullable(item.getClickRate()).orElse(0.0)));
        voBuilder.setSales(DoubleValue.of(Optional.ofNullable(item.getSales()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setSalesConversionRate(DoubleValue.of(Optional.ofNullable(item.getSalesConversionRate()).orElse(0.0)));
        voBuilder.setCost(DoubleValue.of(Optional.ofNullable(item.getCost()).orElse(BigDecimal.ZERO).doubleValue()));
        voBuilder.setAcos(DoubleValue.of(Optional.ofNullable(item.getAcos()).orElse(BigDecimal.ZERO).doubleValue()));


        if (StringUtils.isNotBlank(item.getCampaignId())) {
            voBuilder.setCampaignId(item.getCampaignId());
        }
        if (StringUtils.isNotBlank(item.getAdGroupId())) {
            voBuilder.setAdGroupId(item.getAdGroupId());
        }
        if (StringUtils.isNotBlank(item.getAdGroupType())) {
            voBuilder.setAdGroupType(item.getAdGroupType());
        }
        if (StringUtils.isNotBlank(item.getAdGroupName())) {
            voBuilder.setAdGroupName(item.getAdGroupName());
        }

        if (StringUtils.isNotBlank(item.getCampaignName())) {
            voBuilder.setCampaignName(item.getCampaignName());
        }

        if (StringUtils.isNotBlank(item.getKeywordText())) {
            voBuilder.setKeywordText(item.getKeywordText());
        }
        if (StringUtils.isNotBlank(item.getMatchType())) {
            voBuilder.setMatchType(item.getMatchType());
        }
        if (StringUtils.isNotBlank(item.getSku())) {
            voBuilder.setSku(item.getSku());
        }
        if (StringUtils.isNotBlank(item.getAsin())) {
            voBuilder.setAsin(item.getAsin());
        }

        if (StringUtils.isNotBlank(item.getQuery())) {
            voBuilder.setQuery(item.getQuery());
        }

        if (StringUtils.isNotBlank(item.getQueryCn())) {
            voBuilder.setQueryCn(item.getQueryCn());
        }

        if (StringUtils.isNotBlank(item.getParentAsin())) {
            voBuilder.setParentAsin(item.getParentAsin());
        }
        if (StringUtils.isNotBlank(item.getTitle())) {
            voBuilder.setTitle(item.getTitle());
        }
        if (StringUtils.isNotBlank(item.getMainImage())) {
            voBuilder.setMainImage(item.getMainImage());
        }
        if (StringUtils.isNotBlank(item.getNegaType())) {
            voBuilder.setNegaType(item.getNegaType());
        }
        if (StringUtils.isNotBlank(item.getTargetingType())) {
            voBuilder.setTargetingType(item.getTargetingType());
        }
        if (StringUtils.isNotBlank(item.getKeywordId())) {
            voBuilder.setKeywordId(item.getKeywordId());
        }
        if (StringUtils.isNotBlank(item.getAdId())) {
            voBuilder.setAdId(item.getAdId());
        }
        if (StringUtils.isNotBlank(item.getTargetingText())) {
            voBuilder.setTargetingText(item.getTargetingText());
        }
        if (StringUtils.isNotBlank(item.getSpCampaignType())) {
            voBuilder.setSpCampaignType(item.getSpCampaignType());
        }
        if (StringUtils.isNotBlank(item.getSpGroupType())) {
            voBuilder.setSpGroupType(item.getSpGroupType());
        }

        if (StringUtils.isNotBlank(item.getSpTargetType())) {
            voBuilder.setSpTargetType(item.getSpTargetType());
        }

        if (StringUtils.isNotBlank(item.getTargetId())) {
            voBuilder.setTargetId(item.getTargetId());
        }
        if (item.getIsBroad() != null) {
            voBuilder.setIsBroad(BoolValue.of(item.getIsBroad()));
        }
        if (item.getIsPhrase() != null) {
            voBuilder.setIsPhrase(BoolValue.of(item.getIsPhrase()));
        }
        if (item.getIsExact() != null) {
            voBuilder.setIsExact(BoolValue.of(item.getIsExact()));
        }
        if (item.getIsNegativeExact() != null) {
            voBuilder.setIsNegativeExact(BoolValue.of(item.getIsNegativeExact()));
        }
        if (item.getIsNegativePhrase() != null) {
            voBuilder.setIsNegativePhrase(BoolValue.of(item.getIsNegativePhrase()));
        }
        if (item.getIsTargetType() != null) {
            voBuilder.setIsTargetType(BoolValue.of(item.getIsTargetType()));
        }
        if (item.getDefaultBid() != null) {
            voBuilder.setDefaultBid(item.getDefaultBid());
        }
        if (item.getPortfolioId() != null) {
            voBuilder.setPortfolioId(item.getPortfolioId());
        }
        if (item.getPortfolioName() != null) {
            voBuilder.setPortfolioName(item.getPortfolioName());
        }
        if (item.getIsHidden() != null) {
            voBuilder.setIsHidden(item.getIsHidden());
        }
        if (item.getType() != null) {
            voBuilder.setType(item.getType());
        }
        if (item.getIsAdd() != null) {
            voBuilder.setIsAdd(item.getIsAdd());
        }
        if (item.getIsNegativeAdd() != null) {
            voBuilder.setIsNegativeAdd(item.getIsNegativeAdd());
        }
        return voBuilder.build();
    }


    public Page getAdQueryWordsPageVo(Integer puid, CpcQueryWordDto dto, Page page) {
        //todo:干掉不合理参数,避免前端误传
        dto.setCampaignIds(null);
        //分页数据
        long allKeywordTime = System.currentTimeMillis();
        if (StringUtils.isNotBlank(dto.getPortfolioId())) {
            List<String> campaignIds = amazonAdCampaignDao.getCampaignIdsByPortfolioId(puid, dto.getShopId(), dto.getPortfolioId());
            if (CollectionUtils.isNotEmpty(campaignIds)) {
                dto.setCampaignIdList(campaignIds);
            } else {
                return page;
            }
        }
        page = pageManageListHistoryOrNow(puid, dto, page);
        log.info("===============================查询所有搜索词关键词花费时间 分页 {} ===============================", System.currentTimeMillis() - allKeywordTime);

        List<com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo> poList = page.getRows();

        List<String> campaignIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getCampaignId).distinct().collect(Collectors.toList());
        List<String> groupIds = poList.stream().filter(Objects::nonNull).map(com.meiyunji.sponsored.moudle.cpc.vo.AdQueryOptionVo::getAdGroupId).distinct().collect(Collectors.toList());

        Map<String, AmazonAdPortfolio> portfolioMap = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            List<String> portfolioIds = amazonAdCampaignDao.getPortfolioListByCampaignIds(puid, dto.getShopId(), campaignIds);
            if (CollectionUtils.isNotEmpty(portfolioIds)) {
                portfolioMap = portfolioDao.getPortfolioList(puid, dto.getShopId(), portfolioIds).stream()
                        .collect(Collectors.toMap(AmazonAdPortfolio::getPortfolioId, e -> e));
            }
        }

        //批量查询广告活动和广告组
        long allKeywordTime2 = System.currentTimeMillis();
        List<AmazonAdCampaignAll> byCampaignIds = null;
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            byCampaignIds = amazonAdCampaignDao.getByCampaignIds(puid, dto.getShopId(), null, campaignIds);
        }

        Map<String, AmazonAdCampaignAll> campaignMap = null;
        if (CollectionUtils.isNotEmpty(byCampaignIds)) {
            campaignMap = byCampaignIds.stream().filter(Objects::nonNull).collect(Collectors.toMap(AmazonAdCampaignAll::getCampaignId, item -> item, (a, b) -> a));
        }

        Map<String, AmazonAdGroup> groupMap = null;
        List<AmazonAdGroup> adGroupByIds = null;
        if (CollectionUtils.isNotEmpty(groupIds)) {
            adGroupByIds = amazonAdGroupDao.getAdGroupByIds(puid, dto.getShopId(), null, groupIds);
        }

        if (CollectionUtils.isNotEmpty(adGroupByIds)) {
            groupMap = adGroupByIds.stream().filter(Objects::nonNull).distinct().collect(Collectors.toMap(AmazonAdGroup::getAdGroupId, item -> item, (a, b) -> a));
        }
        log.info("==============================查询所有搜索词关键词花费时间 批量查询数据 {} ==============================", System.currentTimeMillis() - allKeywordTime2);


        if (CollectionUtils.isNotEmpty(poList)) {
            List<ReportVo> list = Lists.newArrayListWithExpectedSize(poList.size());
            Map<String, AmazonAdCampaignAll> finalCampaignMap = campaignMap;
            Map<String, AmazonAdGroup> finalGroupMap = groupMap;
            Map<String, AmazonAdPortfolio> finalPortfolioMap = portfolioMap;

            List<AmazonAdKeyword> keywordList = null;
            Map<String, List<AmazonAdKeyword>> groupKeywordMapList = new HashMap<>();

            getKeywordData(puid, dto.getShopId(), groupIds, keywordList, groupKeywordMapList);

            poList.stream().filter(Objects::nonNull).forEach(e -> {
                ReportVo vo = new ReportVo();
                vo.setType(Constants.SP);
                vo.setImpressions(Optional.ofNullable(e.getImpressions()).orElse(0));
                vo.setClicks(Optional.ofNullable(e.getClicks()).orElse(0));
                vo.setOrderNum(Optional.ofNullable(e.getSaleNum()).orElse(0));
                //广告销量(原来取saleNum字段，现在改成salesNum字段)
                vo.setSaleNum(Optional.ofNullable(e.getSalesNum()).orElse(0));
                vo.setCost(Optional.ofNullable(e.getCost()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                vo.setSales(Optional.ofNullable(e.getTotalSales()).orElse(BigDecimal.ZERO).setScale(2, BigDecimal.ROUND_HALF_UP));
                Double clickRate = vo.getImpressions() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getClicks()) * 100, Double.valueOf(vo.getImpressions()), 2);
                vo.setClickRate(clickRate);
                Double salesConversionRate = vo.getClicks() == 0 ? 0.00 : DoubleUtil.divide(Double.valueOf(vo.getOrderNum()) * 100, Double.valueOf(vo.getClicks()), 2);
                vo.setSalesConversionRate(salesConversionRate);
                BigDecimal rate = BigDecimal.valueOf(vo.getClicks()).compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), BigDecimal.valueOf(vo.getClicks()));
                BigDecimal cpc = rate.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setCpc(cpc);

                BigDecimal rate2 = e.getTotalSales().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : MathUtil.divide(vo.getCost(), e.getTotalSales());
                rate2 = MathUtil.multiply(rate2, BigDecimal.valueOf(100));
                BigDecimal acos = rate2.setScale(2, BigDecimal.ROUND_HALF_UP);
                vo.setAcos(acos);

                vo.setQuery(e.getQuery());
                vo.setQueryCn(e.getQueryCn());
                if (e.getType().equalsIgnoreCase("keyword")) {
                    vo.setKeywordText(e.getKeywordText());
                    vo.setMatchType(e.getMatchType());
                    vo.setKeywordId(e.getKeywordId());
                    vo.setTargetingType(Constants.MANUAL); //关键词应该只有手动
                    vo.setIsTargetType(false);
                } else {
                    //asin有自动和手动
                    vo.setTargetId(e.getTargetId());
                    // 兼容前端
                    if ("close-match".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("close_match");
                    } else if ("loose-match".equalsIgnoreCase(e.getTargetingExpression())) {
                        vo.setMatchType("loose_match");
                    }
                    vo.setKeywordText("自动投放组");
                    vo.setIsTargetType(true);
                    vo.setTargetingType("TARGETING_EXPRESSION_PREDEFINED".equals(e.getTargetingType()) ? Constants.AUTO : Constants.MANUAL);
                }


                //检查该搜索词是否添加过
                if (groupKeywordMapList.size() > 0 && groupKeywordMapList.containsKey(e.getAdGroupId()) && StringUtils.isNotBlank(e.getQuery())) {
                    List<AmazonAdKeyword> amazonAdKeywords = groupKeywordMapList.get(e.getAdGroupId());
                    if (CollectionUtils.isNotEmpty(amazonAdKeywords)) {
                        for (AmazonAdKeyword keyword : amazonAdKeywords) {
                            if (StringUtils.isNotBlank(e.getQuery()) && StringUtils.isNotBlank(keyword.getKeywordText())) {
                                if (e.getQuery().trim().equalsIgnoreCase(keyword.getKeywordText().trim())) {
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.EXACT)) {
                                        vo.setIsExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.BROAD)) {
                                        vo.setIsBroad(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.PHRASE)) {
                                        vo.setIsPhrase(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEEXACT)) {
                                        vo.setIsNegativeExact(true);
                                    }
                                    if (keyword.getMatchType().equalsIgnoreCase(Constants.NEGATIVEPHRASE)) {
                                        vo.setIsNegativePhrase(true);
                                    }
                                }
                            }
                        }
                    }
                }

                vo.setCampaignId(e.getCampaignId());
                //广告活动名称使用最新的
                if (MapUtils.isNotEmpty(finalCampaignMap) && finalCampaignMap.containsKey(e.getCampaignId())) {
                    AmazonAdCampaignAll campaign = finalCampaignMap.get(vo.getCampaignId());
                    vo.setCampaignName(campaign.getName());
                    if (Constants.MANUAL.equals(campaign.getTargetingType())) {
                        vo.setIsAdd(Constants.ARCHIVED.equals(campaign.getState()) ? 1 : 0);
                    } else {
                        vo.setIsAdd(1);
                    }
                    vo.setIsNegativeAdd(Constants.ARCHIVED.equals(campaign.getState()) ? 1 : 0);

                    if (StringUtils.isNotBlank(campaign.getPortfolioId())) {
                        AmazonAdPortfolio adPortfolio = portfolioDao.getByPortfolioId(puid, campaign.getShopId(), campaign.getPortfolioId());
                        vo.setPortfolioId(campaign.getPortfolioId());
                        if (finalPortfolioMap.containsKey(campaign.getPortfolioId())) {
                            AmazonAdPortfolio amazonAdPortfolio = finalPortfolioMap.get(campaign.getPortfolioId());
                            vo.setPortfolioName(amazonAdPortfolio.getName());
                            vo.setIsHidden(adPortfolio.getIsHidden());
                        } else {
                            vo.setPortfolioName("广告组合待同步");
                        }
                    } else {
                        vo.setPortfolioName("-");
                    }
                } else {
                    vo.setCampaignName(e.getCampaignName());
                    vo.setIsAdd(1);
                    vo.setIsNegativeAdd(1);
                }
                vo.setAdGroupId(e.getAdGroupId());
                //广告组名称使用最新的
                if (MapUtils.isNotEmpty(finalGroupMap) && finalGroupMap.containsKey(e.getAdGroupId())) {
                    AmazonAdGroup adGroup = finalGroupMap.get(e.getAdGroupId());
                    vo.setAdGroupName(adGroup.getName());
                    vo.setAdGroupType(adGroup.getAdGroupType());
                    vo.setDefaultBid(adGroup.getDefaultBid());
                    if (vo.getIsAdd() != 1 && Constants.GROUP_TYPE_KEYWORD.equals(adGroup.getAdGroupType())) {
                        vo.setIsAdd(Constants.ARCHIVED.equals(adGroup.getState()) ? 1 : 0);
                    } else {
                        vo.setIsAdd(1);
                    }
                    if (vo.getIsNegativeAdd() != 1 && (Constants.GROUP_TYPE_KEYWORD.equals(adGroup.getAdGroupType())) || Constants.AUTO.equals(adGroup.getAdGroupType())) {
                        vo.setIsNegativeAdd(Constants.ARCHIVED.equals(adGroup.getState()) ? 1 : 0);
                    } else {
                        vo.setIsNegativeAdd(1);
                    }
                } else {
                    vo.setAdGroupName(e.getAdGroupName());
                    vo.setIsAdd(1);
                    vo.setIsNegativeAdd(1);
                }
                list.add(vo);
            });
            page.setRows(list);
        }
        return page;
    }



    // 获取投放信息
    private void getKeywordData(Integer puid, Integer shopId, List<String> groupIds, List<AmazonAdKeyword> keywordList, Map<String, List<AmazonAdKeyword>> groupKeywordMapList) {
        if (CollectionUtils.isNotEmpty(groupIds)) {
            keywordList = amazonAdKeywordDaoRoutingService.listByGroupIdList(puid, shopId, groupIds);
        }

        if (CollectionUtils.isNotEmpty(keywordList)) {
            for (AmazonAdKeyword keyword : keywordList) {
                if (StringUtils.isBlank(keyword.getAdGroupId())) {
                    continue;
                }
                if (groupKeywordMapList.containsKey(keyword.getAdGroupId())) {
                    groupKeywordMapList.get(keyword.getAdGroupId()).add(keyword);
                } else {
                    groupKeywordMapList.put(keyword.getAdGroupId(), Lists.newArrayList(keyword));
                }
            }
        }
    }


    private Page pageManageListHistoryOrNow(int puid, CpcQueryWordDto dto, Page page) {
        return wxCpcQueryKeywordReportDao.pageManageList(puid, dto, page);

    }


    private List<AdHomePerformancedto> getReportKeywordByDateOrHistory(Integer puid,CpcQueryWordDto dto){
        return wxCpcQueryKeywordReportDao.getReportKeywordByDate(puid, dto);
    }

    private List<AdHomePerformancedto> getReportKeywordByKeywordIdListOrHistory(Integer puid,CpcQueryWordDto dto, List<String> keywordIdList){
        return wxCpcQueryKeywordReportDao.getReportKeywordByKeywordIdList(puid, dto.getShopId(),
                    dto.getStart(), dto.getEnd(), keywordIdList, dto);

    }

    private List<AdHomePerformancedto>  getReportTargetByDateOrHistory(Integer puid,CpcQueryWordDto dto){
        return wxCpcQueryKeywordReportDao.getReportTargetByDate(puid, dto);
    }

    private List<AdHomePerformancedto>  getReportTargetByTargetIdListOrHistory(Integer puid,CpcQueryWordDto dto,List<String> targetIdList){
        return wxCpcQueryKeywordReportDao.getReportTargetByTargetIdList(puid, dto.getShopId(), dto.getStart(),
                    dto.getEnd(), targetIdList, dto);

    }


}
