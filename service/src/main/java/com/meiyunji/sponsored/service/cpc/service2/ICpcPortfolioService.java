package com.meiyunji.sponsored.service.cpc.service2;

import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.rpc.adCommon.AllPortfolioAggregateDataListResponse;
import com.meiyunji.sponsored.rpc.adCommon.AllPortfolioDataListResponse;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdPortfolio;
import com.meiyunji.sponsored.service.cpc.vo.MultiShopPortfolioListParam;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageParam;
import com.meiyunji.sponsored.service.cpc.vo.PortfolioPageVo;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdPortfolio;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/08/18
 * 广告组合业务
 */
public interface ICpcPortfolioService {
    /**
     * 通过筛选条件获取广告组合页面信息
     * @param puid
     * @param param
     * @return
     */
    AllPortfolioDataListResponse.PortfolioHomeVo getAllPortfolioDataList(Integer puid, PortfolioPageParam param);

    /**
     * 广告组合--图表汇总、指标数据
     * @param puid
     * @param param
     * @return
     */
    AllPortfolioAggregateDataListResponse.PortfolioHomeVo getAllPortfolioAggregateDataList(Integer puid, PortfolioPageParam param);

    /**
     * 广告组合--图表汇总、指标数据
     * @param puid
     * @param param
     * @return
     */
    AllPortfolioAggregateDataListResponse.PortfolioHomeVo getAllDorisPortfolioAggregateDataList(Integer puid, PortfolioPageParam param);

    /**
     * 获取广告组合未分页前的所有数据
     * @param puid
     * @param shopId
     * @param param
     * @return
     */
    List<PortfolioPageVo> getAllPortfolioVoList(Integer puid, Integer shopId, PortfolioPageParam param);

    /**
     * 查询广告组合的分页数据
     * @param puid
     * @param param
     * @param voPage
     * @param isExport
     * @return
     */
    List<PortfolioPageVo> getPortfolioVoList(Integer puid, PortfolioPageParam param, Page<PortfolioPageVo> voPage, boolean isExport);

    Page<AmazonAdPortfolio> getMultiShopPortfolioList(MultiShopPortfolioListParam param);

    /**
     * 查询广告组合的分页数据(doris)
     * @param puid
     * @param param
     * @param voPage
     * @param isExport
     * @return
     */
    List<PortfolioPageVo> getDorisPortfolioVoList(Integer puid, PortfolioPageParam param, Page<PortfolioPageVo> voPage, boolean isExport);

    Page<OdsAmazonAdPortfolio> getMultiShopPortfolioListDoris(MultiShopPortfolioListParam param);
}
