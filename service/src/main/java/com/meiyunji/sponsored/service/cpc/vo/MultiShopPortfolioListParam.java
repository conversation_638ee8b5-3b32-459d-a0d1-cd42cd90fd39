package com.meiyunji.sponsored.service.cpc.vo;

import lombok.Data;

import java.util.List;

/**
 * @author: sun<PERSON><PERSON>
 * @email: sunin<PERSON>@dianxiaomi.com
 * @date: 2024-05-20  09:36
 */
@Data
public class MultiShopPortfolioListParam {

    private Integer puid;

    private List<Integer> shopIdList;

    private String searchValue;

    private List<String> searchValueList;

    private String searchType;

    private Integer pageNo;

    private Integer pageSize;

    private List<String> marketplaceId;

    private List<String> portfolioIdList;

    // 位置信息 left 左侧栏 默认top 顶部筛选  不同位置排序规则不一样
    private String position;

}
