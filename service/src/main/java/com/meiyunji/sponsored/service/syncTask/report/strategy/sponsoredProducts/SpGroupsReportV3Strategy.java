package com.meiyunji.sponsored.service.syncTask.report.strategy.sponsoredProducts;

import com.alibaba.fastjson.JSONReader;
import com.google.common.collect.Lists;
import com.meiyunji.sellfox.aadas.types.enumeration.AmazonReportV3Type;
import com.meiyunji.sellfox.aadas.types.message.notification.ReportReadyNotification;
import com.meiyunji.sponsored.common.springjdbc.PartitionSqlUtil;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.service.account.dao.ISlaveVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IVcShopAuthDao;
import com.meiyunji.sponsored.service.account.po.VcShopAuth;
import com.meiyunji.sponsored.service.config.CosBucketClient;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdGroupReportDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdGroupReport;
import com.meiyunji.sponsored.service.enums.ShopTypeEnum;
import com.meiyunji.sponsored.service.syncTask.entity.SponsoredProductCampaigns;
import com.meiyunji.sponsored.service.syncTask.report.strategy.AbstractReportProcessStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.zip.GZIPInputStream;

@Component
@Slf4j
@ConditionalOnProperty(name = "aadas.scheduler.executors.reports-consumer.enabled", havingValue = "true")
public class SpGroupsReportV3Strategy extends AbstractReportProcessStrategy {
    private final IAmazonAdGroupReportDao amazonAdGroupReportDao;
    private final PartitionSqlUtil partitionSqlUtil;
    @Resource
    private ISlaveVcShopAuthDao vcShopAuthDao;

    public SpGroupsReportV3Strategy(CosBucketClient dataBucketClient,
                                    IAmazonAdGroupReportDao amazonAdGroupReportDao,
                                    PartitionSqlUtil partitionSqlUtil) {
        super(dataBucketClient);
        this.amazonAdGroupReportDao = amazonAdGroupReportDao;
        this.partitionSqlUtil = partitionSqlUtil;
    }

    @Override
    public Boolean checkValid(ReportReadyNotification notification) {
        return notification.getVersion() == 3 && notification.getV3Type() == AmazonReportV3Type.sp_groups;
    }

    @Override
    public void processReport(ReportReadyNotification notification) throws Exception {
        try (InputStreamReader inputStreamReader = new InputStreamReader(new GZIPInputStream(new ByteArrayInputStream(dataBucketClient.getObjectToBytes(notification.getPath()))));JSONReader jsonReader = new JSONReader(inputStreamReader)) {
            jsonReader.startArray();
            List<SponsoredProductCampaigns> reports = Lists.newArrayListWithExpectedSize(batchSize);
            VcShopAuth byIdAndPuid = vcShopAuthDao.getByIdAndPuid(notification.getMarketplaceIdentifier(), notification.getSellerIdentifier());
            String shopType = ShopTypeEnum.SC.getCode();
            if (byIdAndPuid != null && byIdAndPuid.getId() != null) {
                shopType = ShopTypeEnum.VC.getCode();
            }
            while (jsonReader.hasNext()) {
                jsonReader.startObject();
                SponsoredProductCampaigns report = new SponsoredProductCampaigns();
                report.readFromJsonReader(jsonReader);
                jsonReader.endObject();
                reports.add(report);
                if (report.getImpressions() != 0) {
                    reports.add(report);
                }
                if (reports.size() >= batchSize) {
                    dealReports(notification, reports, shopType);
                    reports = Lists.newArrayListWithExpectedSize(batchSize);
                }
            }
            jsonReader.endArray();
            if (CollectionUtils.isNotEmpty(reports)) {
                dealReports(notification, reports, shopType);
            }
        } catch (Exception e) {
            log.info("报告处理发生错误{}@{} reportType={} countDate={}", notification.getSellerIdentifier()
                    , notification.getMarketplaceIdentifier(), notification.getType(), notification.getDate(), e);
            throw e;
        }
    }

    private void dealReports(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        List<AmazonAdGroupReport> poList = getPoByReportAdGroup(notification, reports, shopType);
        List<List<AmazonAdGroupReport>> partition = Lists.partition(poList, batchSize);
        for (List<AmazonAdGroupReport> amazonAdGroupReports : partition) {
            partitionSqlUtil.save(notification.getSellerIdentifier(), amazonAdGroupReports, 0, amazonAdGroupReportDao::insertList);
            if (DateUtil.checkDateRange(notification.getV3EndDate(), 2L)) {
                amazonAdGroupReportDao.insertDorisList(notification.getSellerIdentifier(), amazonAdGroupReports);
            }
        }
    }

    private List<AmazonAdGroupReport> getPoByReportAdGroup(ReportReadyNotification notification, List<SponsoredProductCampaigns> reports, String shopType) {
        List<AmazonAdGroupReport> list = Lists.newArrayList();
        AmazonAdGroupReport amazonAdGroupReport;
        for (SponsoredProductCampaigns report : reports) {
            amazonAdGroupReport = new AmazonAdGroupReport();
            amazonAdGroupReport.setPuid(notification.getSellerIdentifier());
            amazonAdGroupReport.setShopId(notification.getMarketplaceIdentifier());
            amazonAdGroupReport.setMarketplaceId(notification.getMarketplace().getId());
            amazonAdGroupReport.setCountDate(report.getDate().format(DateTimeFormatter.ofPattern("yyyMMdd")));
            amazonAdGroupReport.setCampaignId(String.valueOf(report.getCampaignId()));
            amazonAdGroupReport.setAdGroupId(String.valueOf(report.getAdGroupId()));
            amazonAdGroupReport.setAdGroupName(report.getAdGroupName());
            amazonAdGroupReport.setCampaignName(report.getCampaignName());

            amazonAdGroupReport.setImpressions(report.getImpressions());
            amazonAdGroupReport.setClicks(report.getClicks());
            amazonAdGroupReport.setCost(report.getCost());
            if (ShopTypeEnum.VC.getCode().equalsIgnoreCase(shopType)) {
                amazonAdGroupReport.setTotalSales(report.getSales14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdGroupReport.setAdSales(report.getAttributedSalesSameSku14d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdGroupReport.setOrderNum(report.getUnitsSoldClicks14d());
                amazonAdGroupReport.setAdOrderNum(report.getUnitsSoldSameSku14d());
                amazonAdGroupReport.setSaleNum(report.getPurchases14d());
                amazonAdGroupReport.setAdSaleNum(report.getPurchasesSameSku14d());
            } else {
                amazonAdGroupReport.setTotalSales(report.getSales7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdGroupReport.setAdSales(report.getAttributedSalesSameSku7d().setScale(2, BigDecimal.ROUND_HALF_UP));
                amazonAdGroupReport.setOrderNum(report.getUnitsSoldClicks7d());
                amazonAdGroupReport.setAdOrderNum(report.getUnitsSoldSameSku7d());
                amazonAdGroupReport.setSaleNum(report.getPurchases7d());
                amazonAdGroupReport.setAdSaleNum(report.getPurchasesSameSku7d());
            }
            list.add(amazonAdGroupReport);
        }
        return list;
    }
}
