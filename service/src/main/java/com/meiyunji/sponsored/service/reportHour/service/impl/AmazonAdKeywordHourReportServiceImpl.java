package com.meiyunji.sponsored.service.reportHour.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sellfox.ams.api.entry.HourlyReportDataPb;
import com.meiyunji.sellfox.ams.api.service.*;
import com.meiyunji.sponsored.common.util.*;
import com.meiyunji.sponsored.service.account.dao.IScVcShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.cpc.bo.AmazonAdProductPerspectiveBO;
import com.meiyunji.sponsored.service.cpc.dao.*;
import com.meiyunji.sponsored.service.cpc.dto.CampaignHourlyReportSelectDto;
import com.meiyunji.sponsored.service.cpc.dto.FeedHourlySelectDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProduct;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdSbKeywordReport;
import com.meiyunji.sponsored.service.cpc.po.AmazonMarketingStreamData;
import com.meiyunji.sponsored.service.cpc.service.IAmazonAdKeywordDaoRoutingService;
import com.meiyunji.sponsored.service.cpc.service2.IAmazonAdFeedReportService;
import com.meiyunji.sponsored.service.cpc.service2.impl.CpcShopDataService;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.vo.AdMetricDto;
import com.meiyunji.sponsored.service.cpc.vo.KeywordHourParam;
import com.meiyunji.sponsored.service.dataWarehouse.statsDo.ShopSaleDto;
import com.meiyunji.sponsored.service.doris.dao.*;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdKeywordReport;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSbKeywordReport;
import com.meiyunji.sponsored.service.multiple.common.utils.MultipleUtils;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.handler.ViewManageHandler;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewHourParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.KeywordViewParam;
import com.meiyunji.sponsored.service.productPerspectiveAnalysis.qo.ViewBaseParam;
import com.meiyunji.sponsored.service.reportHour.constants.HourConvert;
import com.meiyunji.sponsored.service.reportHour.service.IAmazonAdKeywordHourReportService;
import com.meiyunji.sponsored.service.reportHour.utils.AggregationDataUtil;
import com.meiyunji.sponsored.service.reportHour.utils.ReportChartUtil;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordAndTargetHourVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfAdVo;
import com.meiyunji.sponsored.service.reportHour.vo.AdKeywordTargetHourOfPlacementVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AmazonAdKeywordHourReportServiceImpl implements IAmazonAdKeywordHourReportService {
    private final IScVcShopAuthDao shopAuthDao;
    private final AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub;
    private final IAmazonAdProductDao amazonAdProductDao;
    private final IAmazonAdKeywordReportDao amazonAdKeywordReportDao;
    private final IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao;
    private final IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao;
    private final CpcShopDataService cpcShopDataService;
    private final IAmazonAdFeedReportService amazonAdFeedReportService;
    private final IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService;
    private final IOdsAmazonAdProductDao odsAmazonAdProductDao;
    private final IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao;
    private final IAmazonSbAdKeywordDao amazonSbAdKeywordDao;
    private final ViewManageHandler viewManageHandler;

    @Resource
    private MultiThreadQueryAndMergeUtil multiThreadQueryAndMergeUtil;
    @Resource
    private IOdsAmazonAdKeywordReportDao odsAmazonAdKeywordReportDao;
    @Resource
    private IOdsAmazonAdSbKeywordReportDao odsAmazonAdSbKeywordReportDao;
    @Resource
    private IDwsSaleProfitShopDayDao dwsSaleProfitShopDayDao;

    public AmazonAdKeywordHourReportServiceImpl(
            IScVcShopAuthDao shopAuthDao,
            @Qualifier("adFeedBlockingStub") AmsApiGrpc.AmsApiBlockingStub adFeedBlockingStub,
            IAmazonAdProductDao amazonAdProductDao, IAmazonAdKeywordReportDao amazonAdKeywordReportDao, IAmazonAdSbKeywordReportDao amazonAdSbKeywordReportDao, IAmazonMarketingStreamDataDao amazonMarketingStreamDataDao, CpcShopDataService cpcShopDataService,
            IAmazonAdFeedReportService amazonAdFeedReportService,
            IAmazonAdKeywordDaoRoutingService amazonAdKeywordDaoRoutingService,
            IOdsAmazonAdProductDao odsAmazonAdProductDao,
            IOdsAmazonAdProductReportDao odsAmazonAdProductReportDao,
            IAmazonSbAdKeywordDao amazonSbAdKeywordDao,
            ViewManageHandler viewManageHandler
    ) {
        this.shopAuthDao = shopAuthDao;
        this.adFeedBlockingStub = adFeedBlockingStub;
        this.amazonAdProductDao = amazonAdProductDao;
        this.amazonAdKeywordReportDao = amazonAdKeywordReportDao;
        this.amazonAdSbKeywordReportDao = amazonAdSbKeywordReportDao;
        this.amazonMarketingStreamDataDao = amazonMarketingStreamDataDao;
        this.cpcShopDataService = cpcShopDataService;
        this.amazonAdFeedReportService = amazonAdFeedReportService;
        this.amazonAdKeywordDaoRoutingService = amazonAdKeywordDaoRoutingService;
        this.odsAmazonAdProductDao = odsAmazonAdProductDao;
        this.odsAmazonAdProductReportDao = odsAmazonAdProductReportDao;
        this.amazonSbAdKeywordDao = amazonSbAdKeywordDao;
        this.viewManageHandler = viewManageHandler;
    }

    /**
     * 查询关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getHourList(int puid, KeywordHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        selectDto.setWeekdayList(weekdayList);
        if (StringUtils.isNotBlank(param.getKeywordId())) {
            selectDto.setItemIds(Collections.singletonList(param.getKeywordId()));
        }

        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }

        List<AmazonMarketingStreamData> statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.statisticsTargetHourlyReport(selectDto));

        //获取小时对比数据
        List<AmazonMarketingStreamData> compareResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), shopAuth.getMarketplaceId(), param.getStartDateCompare()));
            selectDto.setEndDate(param.getEndDateCompare());
            boolean returnCompareEmptyList = false;
            if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
                List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), selectDto.getStartDate(), selectDto.getEndDate());
                if (CollectionUtils.isEmpty(adIdList)) {
                    returnCompareEmptyList = true;
                }
                selectDto.setAdIds(adIdList);
            }
            compareResponse = (returnCompareEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.statisticsTargetHourlyReport(selectDto));
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adKeywordAndTargetHourVo);

        }
        return voList;
    }

    /**
     * 查询竞价关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getBidHourList(int puid, KeywordHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(param.getStartDate());
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getKeywordId())) {
            selectDto.setItemIds(Collections.singletonList(param.getKeywordId()));
        }
        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }

        List<AmazonMarketingStreamData> statisticsByHourResponse = (returnEmptyList ? new ArrayList<>() : amazonMarketingStreamDataDao.getHourReportByKeywordId(selectDto));

        //获取小时对比数据
        Map<String, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();

        //组装数据
        hourlyReportDataMap = getBidHourlyReportDataMap(statisticsByHourResponse, hourlyReportDataMap);
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>();
        String startDate = param.getStartDate();
        String endDate = param.getEndDate();
        // 日期格式化
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            // 起始日期
            Date d1 = sdf.parse(startDate);
            // 结束日期
            Date d2 = sdf.parse(endDate);
            Date tmp = d1;
            Calendar dd = Calendar.getInstance();
            dd.setTime(d1);
            while (tmp.getTime() <= d2.getTime()) {
                Date finalTmp = tmp;
                Map<String, AmazonMarketingStreamData> finalHourlyReportDataMap =
                        hourlyReportDataMap;
                HourConvert.twentyFourHoursList.stream().forEachOrdered(item -> {
                    String dateKey = sdf.format(finalTmp) + " " + item;
                    if (null != finalHourlyReportDataMap.get(dateKey)) {
                        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleBidVo(finalHourlyReportDataMap.get(dateKey));
                        voList.add(adKeywordAndTargetHourVo);
                    }
                });
                // 天数加上1
                dd.add(Calendar.DAY_OF_MONTH, 1);
                tmp = dd.getTime();
            }
        } catch (ParseException e) {
            e.printStackTrace();
        }

        return voList;
    }

    /**
     * 关键词月维度报告
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getMonthlyList(int puid, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdKeywordDailyReports(puid, adType, param);
        reports = ReportChartUtil.getMonthReportVos(reports);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdKeywordAndTargetHourVo> compareList = getAdKeywordDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getMonthReportVos(compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingMonthCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdKeywordAndTargetHourVo> paddingMonthCompare(KeywordHourParam param, List<AdKeywordAndTargetHourVo> reports, List<AdKeywordAndTargetHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdKeywordAndTargetHourVo> resList = new ArrayList<>();
        Map<String, AdKeywordAndTargetHourVo> map = StreamUtil.toMap(reports, AdKeywordAndTargetHourVo::getLabel);
        Map<String, AdKeywordAndTargetHourVo> compareMap = StreamUtil.toMap(compareList, AdKeywordAndTargetHourVo::getLabel);
        DateTimeFormatter monthFormatter = DateTimeFormatter.ofPattern("yyyy-MM");
        for (; !YearMonth.from(start).isAfter(YearMonth.from(end));
             start = start.plusMonths(1), startCompare = startCompare.plusMonths(1)) {
            AdKeywordAndTargetHourVo report = map.get(start.format(monthFormatter));
            AdKeywordAndTargetHourVo compareReport = compareMap.get(startCompare.format(monthFormatter));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdKeywordAndTargetHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(start.format(monthFormatter));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    /**
     * 关键词周维度报告
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getWeeklyList(int puid, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdKeywordDailyReports(puid, adType, param);
        reports = ReportChartUtil.getWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdKeywordAndTargetHourVo> compareList = getAdKeywordDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getWeekReportVos(param.getStartDate(), param.getEndDate(), compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdKeywordAndTargetHourVo> paddingWeekCompare(List<AdKeywordAndTargetHourVo> reports, List<AdKeywordAndTargetHourVo> compareList) {
        if (CollectionUtils.isEmpty(reports)) {
            return reports;
        }
        for (int i = 0; i < reports.size() && i < compareList.size(); i++) {
            reports.get(i).compareDataSet(compareList.get(i));
        }
        return reports;
    }

    /**
     * 关键词天维度报告
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDailyList(int puid, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdKeywordDailyReports(puid, adType, param);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdKeywordAndTargetHourVo> compareList = getAdKeywordDailyReports(puid, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdKeywordAndTargetHourVo> paddingDayCompare(KeywordHourParam param, List<AdKeywordAndTargetHourVo> reports, List<AdKeywordAndTargetHourVo> compareList) {
        LocalDate start = LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate end = LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE);
        LocalDate startCompare = LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE);
        List<AdKeywordAndTargetHourVo> resList = new ArrayList<>();
        Map<String, AdKeywordAndTargetHourVo> map = StreamUtil.toMap(reports, AdKeywordAndTargetHourVo::getLabel);
        Map<String, AdKeywordAndTargetHourVo> compareMap = StreamUtil.toMap(compareList, AdKeywordAndTargetHourVo::getLabel);
        for (; !start.isAfter(end); start = start.plusDays(1), startCompare = startCompare.plusDays(1)) {
            AdKeywordAndTargetHourVo report = map.get(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
            AdKeywordAndTargetHourVo compareReport = compareMap.get(startCompare.format(DateTimeFormatter.ISO_LOCAL_DATE));
            if (Objects.isNull(report) && Objects.isNull(compareReport)) {
                continue;
            }
            AdKeywordAndTargetHourVo vo;
            if (Objects.isNull(report)) {
                vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(start.format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
            } else {
                vo = report;
            }
            log.info("vo: {}, compareReport: {}", JSONObject.toJSONString(vo), JSONObject.toJSONString(compareReport));
            vo.compareDataSet(compareReport);
            resList.add(vo);
        }
        return resList;
    }

    private List<AdKeywordAndTargetHourVo> getAdKeywordDailyReports(int puid, String adType, KeywordHourParam param) {
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), param.getPuid());
        //日,周,月报告数据
        if ("SP".equalsIgnoreCase(adType)) {
            List<AmazonAdKeywordReport> reports =
                    amazonAdKeywordReportDao.getReportByKeywordId(puid, param.getShopId(),
                            start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")), shopAuth.getMarketplaceId(), param.getKeywordId());
            return reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();

                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getTotalSales());
                vo.setAdSelfSale(item.getAdSales());
                vo.setAdOtherSale(item.getAdOtherSales());
                vo.setAdOrderNum(item.getSaleNum());
                vo.setSelfAdOrderNum(item.getAdSaleNum());
                vo.setOtherAdOrderNum(item.getAdOtherOrderNum());
                vo.setAdSaleNum(item.getOrderNum());
                vo.setAdSelfSaleNum(item.getAdOrderNum());
                vo.setAdOtherSaleNum(item.getAdOtherSaleNum());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        } else if ("SB".equalsIgnoreCase(adType)) {
            List<AmazonAdSbKeywordReport> reports =
                    amazonAdSbKeywordReportDao.getReportByKeywordId(puid, param.getShopId(),
                            start.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            end.format(DateTimeFormatter.ofPattern("yyyyMMdd")),
                            shopAuth.getMarketplaceId(), param.getKeywordId());
            List<AdKeywordAndTargetHourVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(LocalDate.parse(item.getCountDate(), DateTimeFormatter.ofPattern("yyyyMMdd"))
                        .format(DateTimeFormatter.ISO_LOCAL_DATE));
                vo.setDate(vo.getLabel());
                vo.setAdSale(item.getSales14d());
                vo.setAdSelfSale(item.getSales14dSameSKU());
                vo.setAdOtherSale(item.getSales14d().subtract(item.getSales14dSameSKU()));
                vo.setAdOrderNum(item.getConversions14d());
                vo.setSelfAdOrderNum(item.getConversions14dSameSKU());
                vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSKU());
                vo.setAdSaleNum(Objects.isNull(item.getUnitsSold14d()) ? 0 : item.getUnitsSold14d());
                vo.setAdCost(item.getCost());
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setViewableImpressions(item.getViewableImpressions());
                vo.setOrdersNewToBrand(item.getOrdersNewToBrand14d());
                vo.setUnitsOrderedNewToBrand(item.getUnitsOrderedNewToBrand14d());
                vo.setSalesNewToBrand(item.getSalesNewToBrand14d());
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
//                vo.setVrt(MathUtil.divideIntegerByOneHundred(item.getViewableImpressions(), item.getImpressions()));
//                vo.setVCtr(MathUtil.divideIntegerByOneHundred(item.getClicks(), item.getViewableImpressions()));
//                vo.setAdvertisingUnitPrice(MathUtil.divideByZero(item.getSales14d(), BigDecimal.valueOf(item.getConversions14d()), 2));
//                vo.setAdvertisingProductUnitPrice(MathUtil.divideByZero(item.getSales14dSameSKU(), item.getConversions14dSameSKU() != null ? BigDecimal.valueOf(item.getConversions14dSameSKU()) : BigDecimal.ZERO,2));
//                vo.setAdvertisingOtherProductUnitPrice(MathUtil.divideByZero(item.getSales14d().subtract(item.getSales14dSameSKU()), BigDecimal.valueOf(item.getConversions14d() - (Optional.ofNullable(item.getConversions14dSameSKU()).orElse(0)))));
//                vo.setOrdersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getOrdersNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(item.getConversions14d())));
//                vo.setSalesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(item.getSalesNewToBrand14d(), BigDecimal.valueOf(100)), item.getSales14d()));
//                vo.setUnitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(item.getUnitsOrderedNewToBrand14d()), BigDecimal.valueOf(100)),BigDecimal.valueOf(item.getConversions14d())));
                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        }
        return null;
    }

    /**
     * 按广告产品查询关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordTargetHourOfAdVo> getListOfAd(int puid, KeywordHourParam param) {
        return convertToHourOfAdVos(getDetailListOfAd(puid, param));
    }

    /**
     * 按广告产品查询关键词小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDetailListOfAd(int puid, KeywordHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getKeywordId())) {
            selectDto.setItemIds(Lists.newArrayList(param.getKeywordId()));
        }

        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }
        if (returnEmptyList) {
            return new ArrayList<>();
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfAd(selectDto);

        //pb对象转vo对象
        List<AdKeywordAndTargetHourVo> hourVos = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //查询所有对应的asin
        List<String> adIds = hourVos.stream().map(AdKeywordAndTargetHourVo::getAdId).distinct().collect(Collectors.toList());
        List<AmazonAdProduct> adProducts = amazonAdProductDao.getByAdIds(puid, shopAuth.getId(), adIds);
        Map<String, AmazonAdProduct> adProductMap = adProducts.stream().collect(Collectors.toMap(
                AmazonAdProduct::getAdId, Function.identity(), (a, b) -> a));
        //填充asin信息
        List<AdKeywordAndTargetHourVo> voList = hourVos.stream().peek(item -> {
            if (adProductMap.containsKey(item.getAdId())) {
                item.setAsin(adProductMap.get(item.getAdId()).getAsin());
            }
        }).collect(Collectors.toList());
        //时段无数据填充0值
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getAsin));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setAsin(k);
                voList.add(vo);
            }
        });
        return voList.stream().filter(o -> StringUtils.isNotBlank(o.getAsin()))
                .sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getAsin)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
    }


    /**
     * 查询广告位维度关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordTargetHourOfPlacementVo> getListOfPlacement(int puid, KeywordHourParam param) {
        return convertToHourOfPlacementVos(getDetailListOfPlacement(puid, param));
    }

    /**
     * 查询广告位维度关键词小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDetailListOfPlacement(int puid, KeywordHourParam param) {
        //校验店铺信息
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        selectDto.setSellerIds(Collections.singletonList(shopAuth.getSellingPartnerId()));
        selectDto.setMarketplaceId(shopAuth.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getDataStartTime(shopAuth.getSellingPartnerId(), selectDto.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (StringUtils.isNotBlank(param.getKeywordId())) {
            selectDto.setItemIds(Lists.newArrayList(param.getKeywordId()));
        }

        boolean returnEmptyList = false;
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            List<String> adIdList = odsAmazonAdProductReportDao.adIdListByProduct(puid, Collections.singletonList(shopAuth.getId()), shopAuth.getMarketplaceId(), param.getFindType(), param.getFindValue(), param.getStartDate(), param.getEndDate());
            if (CollectionUtils.isEmpty(adIdList)) {
                returnEmptyList = true;
            }
            selectDto.setAdIds(adIdList);
        }
        if (returnEmptyList) {
            return new ArrayList<>();
        }

        List<AmazonMarketingStreamData> dataList =
                amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfPlacement(selectDto);

        //pb对象转vo对象
        List<AdKeywordAndTargetHourVo> voList = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdKeywordAndTargetHourVo::getPlacementOrder)
                .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());

    }


    private List<AdKeywordTargetHourOfAdVo> convertToHourOfAdVos(List<AdKeywordAndTargetHourVo> hourVos) {
        //按asin汇总数据
        Map<String, List<AdKeywordAndTargetHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getAsin));

        List<AdKeywordTargetHourOfAdVo> adKeywordHourOfAdVos = new ArrayList<>(hourVoMap.size());

        for (Map.Entry<String, List<AdKeywordAndTargetHourVo>> entry : hourVoMap.entrySet()) {
            AdKeywordTargetHourOfAdVo adKeywordHourOfAdVo = new AdKeywordTargetHourOfAdVo();
            adKeywordHourOfAdVo.setAsin(entry.getKey());
            List<AdKeywordAndTargetHourVo> asinHourVos = entry.getValue();

            adKeywordHourOfAdVo.setDetails(asinHourVos);
            adKeywordHourOfAdVo.staticsFromHourVos(asinHourVos);
            adKeywordHourOfAdVos.add(adKeywordHourOfAdVo);
        }
        return adKeywordHourOfAdVos;
    }


    private List<AdKeywordTargetHourOfPlacementVo> convertToHourOfPlacementVos(List<AdKeywordAndTargetHourVo> hourVos) {
        //按asin汇总数据
        Map<String, List<AdKeywordAndTargetHourVo>> hourVoMap =
                hourVos.stream().filter(Objects::nonNull).collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));

        List<AdKeywordTargetHourOfPlacementVo> adKeywordHourOfPlacementVos = new ArrayList<>(3);

        for (Map.Entry<String, List<AdKeywordAndTargetHourVo>> entry : hourVoMap.entrySet()) {
            AdKeywordTargetHourOfPlacementVo adKeywordHourOfPlacementVo = new AdKeywordTargetHourOfPlacementVo();
            List<AdKeywordAndTargetHourVo> asinHourVos = entry.getValue();
            adKeywordHourOfPlacementVo.setDetails(asinHourVos);
            adKeywordHourOfPlacementVo.staticsFromHourVos(asinHourVos);
            adKeywordHourOfPlacementVo.setPlacement(entry.getKey());
            adKeywordHourOfPlacementVos.add(adKeywordHourOfPlacementVo);
        }
        return adKeywordHourOfPlacementVos.stream().collect(Collectors.toList());
    }

    private AdKeywordAndTargetHourVo handleBidVo(GetHourReportByKeywordIdResponsePb.GetHourReportByKeywordIdResponse.Item data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(data.getDatetime());
//        AdKeywordAndTargetHourVo adKeywordAndTargetHourVoCompare = bidConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = bidConvertTo(data);
        vo.setAdSale(adKeywordAndTargetHourVo.getAdSale());
        vo.setAdSelfSale(adKeywordAndTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adKeywordAndTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adKeywordAndTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adKeywordAndTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adKeywordAndTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adKeywordAndTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adKeywordAndTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adKeywordAndTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adKeywordAndTargetHourVo.getAdCost());
        vo.setClicks(adKeywordAndTargetHourVo.getClicks());
        vo.setImpressions(adKeywordAndTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adKeywordAndTargetHourVo.getAdCostPerClick());
        vo.setCpa(adKeywordAndTargetHourVo.getCpa());
        vo.setAcos(adKeywordAndTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        return vo;
    }

    private AdKeywordAndTargetHourVo handleBidVo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        vo.setLabel(DateUtil.dateToStrWithTime(data.getTimeWindowStart(), "yyyy-MM-dd HH"));
//        AdKeywordAndTargetHourVo adKeywordAndTargetHourVoCompare = bidConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = bidConvertTo(data);
        vo.setAdSale(adKeywordAndTargetHourVo.getAdSale());
        vo.setAdSelfSale(adKeywordAndTargetHourVo.getAdSelfSale());
        vo.setAdOtherSale(adKeywordAndTargetHourVo.getAdOtherSale());
        vo.setAdOrderNum(adKeywordAndTargetHourVo.getAdOrderNum());
        vo.setSelfAdOrderNum(adKeywordAndTargetHourVo.getSelfAdOrderNum());
        vo.setOtherAdOrderNum(adKeywordAndTargetHourVo.getOtherAdOrderNum());
        vo.setAdSaleNum(adKeywordAndTargetHourVo.getAdSaleNum());
        vo.setAdSelfSaleNum(adKeywordAndTargetHourVo.getAdSelfSaleNum());
        vo.setAdOtherSaleNum(adKeywordAndTargetHourVo.getAdOtherSaleNum());
        vo.setAdCost(adKeywordAndTargetHourVo.getAdCost());
        vo.setClicks(adKeywordAndTargetHourVo.getClicks());
        vo.setImpressions(adKeywordAndTargetHourVo.getImpressions());
        vo.setAdCostPerClick(adKeywordAndTargetHourVo.getAdCostPerClick());
        vo.setCpa(adKeywordAndTargetHourVo.getCpa());
        vo.setAcos(adKeywordAndTargetHourVo.getAcos());
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        return vo;
    }

    private AdKeywordAndTargetHourVo handleVo(Integer hour, HourlyReportDataPb.HourlyReportData data,
                                              HourlyReportDataPb.HourlyReportData dataCompare) {
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVoCompare = spConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = spConvertTo(data);
        AdKeywordAndTargetHourVo vo = AdKeywordAndTargetHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adKeywordAndTargetHourVo.getAdSale())
                .adSelfSale(adKeywordAndTargetHourVo.getAdSelfSale())
                .adOtherSale(adKeywordAndTargetHourVo.getAdOtherSale())
                .adOrderNum(adKeywordAndTargetHourVo.getAdOrderNum())
                .selfAdOrderNum(adKeywordAndTargetHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adKeywordAndTargetHourVo.getOtherAdOrderNum())
                .adSaleNum(adKeywordAndTargetHourVo.getAdSaleNum())
                .adSelfSaleNum(adKeywordAndTargetHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adKeywordAndTargetHourVo.getAdOtherSaleNum())
                .adCost(adKeywordAndTargetHourVo.getAdCost())
                .clicks(adKeywordAndTargetHourVo.getClicks())
                .impressions(adKeywordAndTargetHourVo.getImpressions())
                .adCostPerClick(adKeywordAndTargetHourVo.getAdCostPerClick())
                .cpa(adKeywordAndTargetHourVo.getCpa())
                .acos(adKeywordAndTargetHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks())))
                .roas(adKeywordAndTargetHourVo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adKeywordAndTargetHourVo.getAdSale().divide(adKeywordAndTargetHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adKeywordAndTargetHourVoCompare.getClicks())
                .impressionsCompare(adKeywordAndTargetHourVoCompare.getImpressions())
                .adSaleNumCompare(adKeywordAndTargetHourVoCompare.getAdSaleNum())
                .adSaleCompare(adKeywordAndTargetHourVoCompare.getAdSale())
                .adCostCompare(adKeywordAndTargetHourVoCompare.getAdCost())
                .adOrderNumCompare(adKeywordAndTargetHourVoCompare.getAdOrderNum())
                .build();
        vo.afterPropertiesSet();//为各对比率属性设值
        return vo;
    }

    private AdKeywordAndTargetHourVo handleVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare) {
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVoCompare = spConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = spConvertTo(data);
        AdKeywordAndTargetHourVo vo = AdKeywordAndTargetHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adKeywordAndTargetHourVo.getAdSale())
                .adSelfSale(adKeywordAndTargetHourVo.getAdSelfSale())
                .adOtherSale(adKeywordAndTargetHourVo.getAdOtherSale())
                .adOrderNum(adKeywordAndTargetHourVo.getAdOrderNum())
                .selfAdOrderNum(adKeywordAndTargetHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adKeywordAndTargetHourVo.getOtherAdOrderNum())
                .adSaleNum(adKeywordAndTargetHourVo.getAdSaleNum())
                .adSelfSaleNum(adKeywordAndTargetHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adKeywordAndTargetHourVo.getAdOtherSaleNum())
                .adCost(adKeywordAndTargetHourVo.getAdCost())
                .clicks(adKeywordAndTargetHourVo.getClicks())
                .impressions(adKeywordAndTargetHourVo.getImpressions())
                .adCostPerClick(adKeywordAndTargetHourVo.getAdCostPerClick())
                .cpa(adKeywordAndTargetHourVo.getCpa())
                .acos(adKeywordAndTargetHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks())))
                .roas(adKeywordAndTargetHourVo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adKeywordAndTargetHourVo.getAdSale().divide(adKeywordAndTargetHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adKeywordAndTargetHourVoCompare.getClicks())
                .impressionsCompare(adKeywordAndTargetHourVoCompare.getImpressions())
                .adSaleNumCompare(adKeywordAndTargetHourVoCompare.getAdSaleNum())
                .adSaleCompare(adKeywordAndTargetHourVoCompare.getAdSale())
                .adCostCompare(adKeywordAndTargetHourVoCompare.getAdCost())
                .adOrderNumCompare(adKeywordAndTargetHourVoCompare.getAdOrderNum())
                .adCostPerClickCompare(adKeywordAndTargetHourVoCompare.getAdCostPerClick())
                .acosCompare(adKeywordAndTargetHourVoCompare.getAcos())
                .ctrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getImpressions())))
                .cvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getClicks())))
                .roasCompare(adKeywordAndTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ?
                        BigDecimal.ZERO : adKeywordAndTargetHourVoCompare.getAdSale().divide(adKeywordAndTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP))
                .build();
        vo.calculateCompareRate();//为各对比率属性设值
        return vo;
    }

    private AdKeywordAndTargetHourVo handleSbVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVoCompare = sbConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = sbConvertTo(data);
        AdKeywordAndTargetHourVo vo = AdKeywordAndTargetHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adKeywordAndTargetHourVo.getAdSale())
                .adSelfSale(adKeywordAndTargetHourVo.getAdSelfSale())
                .adOtherSale(adKeywordAndTargetHourVo.getAdOtherSale())
                .adOrderNum(adKeywordAndTargetHourVo.getAdOrderNum())
                .selfAdOrderNum(adKeywordAndTargetHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adKeywordAndTargetHourVo.getOtherAdOrderNum())
                .adSaleNum(adKeywordAndTargetHourVo.getAdSaleNum())
                .adSelfSaleNum(adKeywordAndTargetHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adKeywordAndTargetHourVo.getAdOtherSaleNum())
                .adCost(adKeywordAndTargetHourVo.getAdCost())
                .clicks(adKeywordAndTargetHourVo.getClicks())
                .impressions(adKeywordAndTargetHourVo.getImpressions())
                .viewableImpressions(adKeywordAndTargetHourVo.getViewableImpressions())
                .ordersNewToBrand(adKeywordAndTargetHourVo.getOrdersNewToBrand())
                .unitsOrderedNewToBrand(adKeywordAndTargetHourVo.getUnitsOrderedNewToBrand())
                .salesNewToBrand(adKeywordAndTargetHourVo.getSalesNewToBrand())
                .adCostPerClick(adKeywordAndTargetHourVo.getAdCostPerClick())
                .cpa(adKeywordAndTargetHourVo.getCpa())
                .acos(adKeywordAndTargetHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks())))
                .roas(adKeywordAndTargetHourVo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adKeywordAndTargetHourVo.getAdSale().divide(adKeywordAndTargetHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adKeywordAndTargetHourVoCompare.getClicks())
                .impressionsCompare(adKeywordAndTargetHourVoCompare.getImpressions())
                .adSaleNumCompare(adKeywordAndTargetHourVoCompare.getAdSaleNum())
                .adSaleCompare(adKeywordAndTargetHourVoCompare.getAdSale())
                .adCostCompare(adKeywordAndTargetHourVoCompare.getAdCost())
                .adOrderNumCompare(adKeywordAndTargetHourVoCompare.getAdOrderNum())
                .adCostPerClickCompare(adKeywordAndTargetHourVoCompare.getAdCostPerClick())
                .acosCompare(adKeywordAndTargetHourVoCompare.getAcos())
                .ctrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getImpressions())))
                .cvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getClicks())))
                .roasCompare(adKeywordAndTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adKeywordAndTargetHourVoCompare.getAdSale().divide(adKeywordAndTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP))
                .vrt(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getViewableImpressions(), adKeywordAndTargetHourVo.getImpressions()))
                .vCtr(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getClicks(), adKeywordAndTargetHourVo.getViewableImpressions()))
                .advertisingUnitPrice(MathUtil.divideByZero(adKeywordAndTargetHourVo.getAdSale(), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum()), 2))
                .advertisingProductUnitPrice(MathUtil.divideByZero(adKeywordAndTargetHourVo.getAdSelfSale(), BigDecimal.valueOf(adKeywordAndTargetHourVo.getSelfAdOrderNum()), 2))
                .advertisingOtherProductUnitPrice(MathUtil.divideByZero(adKeywordAndTargetHourVo.getAdSale().subtract(adKeywordAndTargetHourVo.getAdSelfSale()), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum() - adKeywordAndTargetHourVo.getSelfAdOrderNum())))
                .ordersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum())))
                .salesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(adKeywordAndTargetHourVo.getSalesNewToBrand(), BigDecimal.valueOf(100)), adKeywordAndTargetHourVo.getAdSale()))
                .unitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdSaleNum())))
                .acots(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getAdCost(), shopSalesByDate))
                .asots(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getAdSale(), shopSalesByDate))
                .build();
        vo.calculateCompareRate();//为各对比率属性设值
        return vo;
    }

    /**
     * sp、sb通用
     */
    private AdKeywordAndTargetHourVo handleBaseVo(Integer hour, AmazonMarketingStreamData data, AmazonMarketingStreamData dataCompare, BigDecimal shopSalesByDate) {
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVoCompare = this.baseConvertTo(dataCompare);
        AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = this.baseConvertTo(data);
        AdKeywordAndTargetHourVo vo = AdKeywordAndTargetHourVo.builder()
                .label(HourConvert.hourMap.get(hour))
                .hour(hour)
                .adSale(adKeywordAndTargetHourVo.getAdSale())
                .adSelfSale(adKeywordAndTargetHourVo.getAdSelfSale())
                .adOtherSale(adKeywordAndTargetHourVo.getAdOtherSale())
                .adOrderNum(adKeywordAndTargetHourVo.getAdOrderNum())
                .selfAdOrderNum(adKeywordAndTargetHourVo.getSelfAdOrderNum())
                .otherAdOrderNum(adKeywordAndTargetHourVo.getOtherAdOrderNum())
                .adSaleNum(adKeywordAndTargetHourVo.getAdSaleNum())
                .adSelfSaleNum(adKeywordAndTargetHourVo.getAdSelfSaleNum())
                .adOtherSaleNum(adKeywordAndTargetHourVo.getAdOtherSaleNum())
                .adCost(adKeywordAndTargetHourVo.getAdCost())
                .clicks(adKeywordAndTargetHourVo.getClicks())
                .impressions(adKeywordAndTargetHourVo.getImpressions())
                .viewableImpressions(adKeywordAndTargetHourVo.getViewableImpressions())
                .ordersNewToBrand(adKeywordAndTargetHourVo.getOrdersNewToBrand())
                .unitsOrderedNewToBrand(adKeywordAndTargetHourVo.getUnitsOrderedNewToBrand())
                .salesNewToBrand(adKeywordAndTargetHourVo.getSalesNewToBrand())
                .adCostPerClick(adKeywordAndTargetHourVo.getAdCostPerClick())
                .cpa(adKeywordAndTargetHourVo.getCpa())
                .acos(adKeywordAndTargetHourVo.getAcos())
                .ctr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getImpressions())))
                .cvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getClicks())))
                .roas(adKeywordAndTargetHourVo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : adKeywordAndTargetHourVo.getAdSale().divide(adKeywordAndTargetHourVo.getAdCost(), 4, RoundingMode.HALF_UP))
                .clicksCompare(adKeywordAndTargetHourVoCompare.getClicks())
                .impressionsCompare(adKeywordAndTargetHourVoCompare.getImpressions())
                .adSaleNumCompare(adKeywordAndTargetHourVoCompare.getAdSaleNum())
                .adSaleCompare(adKeywordAndTargetHourVoCompare.getAdSale())
                .adCostCompare(adKeywordAndTargetHourVoCompare.getAdCost())
                .adOrderNumCompare(adKeywordAndTargetHourVoCompare.getAdOrderNum())
                .adCostPerClickCompare(adKeywordAndTargetHourVoCompare.getAdCostPerClick())
                .acosCompare(adKeywordAndTargetHourVoCompare.getAcos())
                .ctrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getImpressions())))
                .cvrCompare(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVoCompare.getClicks())))
                .roasCompare(adKeywordAndTargetHourVoCompare.getAdSale().compareTo(BigDecimal.ZERO) == 0 || adKeywordAndTargetHourVoCompare.getAdCost().compareTo(BigDecimal.ZERO) == 0 ?
                        BigDecimal.ZERO : adKeywordAndTargetHourVoCompare.getAdSale().divide(adKeywordAndTargetHourVoCompare.getAdCost(), 4, RoundingMode.HALF_UP))
                .vrt(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getViewableImpressions(), adKeywordAndTargetHourVo.getImpressions()))
                .vCtr(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getClicks(), adKeywordAndTargetHourVo.getViewableImpressions()))
                .advertisingUnitPrice(MathUtil.divideByZero(adKeywordAndTargetHourVo.getAdSale(), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum()), 2))
                .advertisingProductUnitPrice(MathUtil.divideByZero(adKeywordAndTargetHourVo.getAdSelfSale(), BigDecimal.valueOf(adKeywordAndTargetHourVo.getSelfAdOrderNum()), 2))
                .advertisingOtherProductUnitPrice(MathUtil.divideByZero(adKeywordAndTargetHourVo.getAdSale().subtract(adKeywordAndTargetHourVo.getAdSelfSale()), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum() - adKeywordAndTargetHourVo.getSelfAdOrderNum())))
                .ordersNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getOrdersNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdOrderNum())))
                .salesNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(adKeywordAndTargetHourVo.getSalesNewToBrand(), BigDecimal.valueOf(100)), adKeywordAndTargetHourVo.getAdSale()))
                .unitsOrderedNewToBrandPercentage(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(adKeywordAndTargetHourVo.getUnitsOrderedNewToBrand()), BigDecimal.valueOf(100)), BigDecimal.valueOf(adKeywordAndTargetHourVo.getAdSaleNum())))
                .acots(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getAdCost(), shopSalesByDate))
                .asots(MathUtil.divideIntegerByOneHundred(adKeywordAndTargetHourVo.getAdSale(), shopSalesByDate))
                .build();
        vo.calculateCompareRate();//为各对比率属性设值
        return vo;
    }

    private AdKeywordAndTargetHourVo bidConvertTo(GetHourReportByKeywordIdResponsePb.GetHourReportByKeywordIdResponse.Item data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
//        vo.setAdId(data.getAdId());
//        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
//        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(data.getDatetime());
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo bidConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
//        vo.setAdId(data.getAdId());
//        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
//        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(data.getTime());
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo spConvertTo(HourlyReportDataPb.HourlyReportData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7D());
        vo.setSelfAdOrderNum(data.getAttributedConversions7DSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7D(), data.getAttributedConversions7DSameSku()));
        vo.setAdSale(BigDecimal.valueOf(data.getAttributedSales7D()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(BigDecimal.valueOf(data.getAttributedSales7DSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(BigDecimal.valueOf(data.getAttributedSales7D()), BigDecimal.valueOf(data.getAttributedSales7DSameSku())).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7D());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7DSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7D(), data.getAttributedUnitsOrdered7DSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo spConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        return vo;
    }

    private AdKeywordAndTargetHourVo sbConvertTo(AmazonMarketingStreamData data) {
        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions14d());
        vo.setSelfAdOrderNum(data.getAttributedConversions14dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions14d(), data.getAttributedConversions14dSameSku()));
        vo.setAdSale(data.getAttributedSales14d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales14dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales14d(), data.getAttributedSales14dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered14d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered14dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered14d(), data.getAttributedUnitsOrdered14dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(data.getViewableImpressions().intValue());
        vo.setOrdersNewToBrand(data.getAttributedOrdersNewToBrand14d());
        vo.setUnitsOrderedNewToBrand(data.getAttributedUnitsOrderedNewToBrand14d());
        vo.setSalesNewToBrand(data.getAttributedSalesNewToBrand14d());
        return vo;
    }

    /**
     * sp、sb通用
     * @param data
     * @return
     */
    private AdKeywordAndTargetHourVo baseConvertTo(AmazonMarketingStreamData data) {

        AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
        if (data == null) {
            return vo;
        }
        vo.setAdId(data.getAdId());
        vo.setPlacement(data.getPlacement());
        //广告位排序
        if ("Detail Page on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("产品页面");
            vo.setPlacementOrder(3);
        } else if ("Top of Search on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacement("搜索结果顶部(首页)");
            vo.setPlacementOrder(1);
        } else if ("Other on-Amazon".equalsIgnoreCase(vo.getPlacement())) {
            vo.setPlacementOrder(2);
            vo.setPlacement("搜索结果的其余位置");
        } else {
            vo.setPlacementOrder(4);
        }
        vo.setClicks(data.getClicks());
        vo.setAdOrderNum(data.getAttributedConversions7d());
        vo.setSelfAdOrderNum(data.getAttributedConversions7dSameSku());
        vo.setOtherAdOrderNum(MathUtil.subtractInteger(data.getAttributedConversions7d(), data.getAttributedConversions7dSameSku()));
        vo.setAdSale(data.getAttributedSales7d().setScale(4, RoundingMode.HALF_UP));
        vo.setAdSelfSale(data.getAttributedSales7dSameSku().setScale(4, RoundingMode.HALF_UP));
        vo.setAdOtherSale(MathUtil.subtract(data.getAttributedSales7d(), data.getAttributedSales7dSameSku()).setScale(4, RoundingMode.HALF_UP));
        vo.setAdSaleNum(data.getAttributedUnitsOrdered7d());
        vo.setAdSelfSaleNum(data.getAttributedUnitsOrdered7dSameSku());
        vo.setAdOtherSaleNum(MathUtil.subtractInteger(data.getAttributedUnitsOrdered7d(), data.getAttributedUnitsOrdered7dSameSku()));
        vo.setImpressions(data.getImpressions());
        LocalTime localTime = LocalTime.parse(data.getTime(), DateTimeFormatter.ISO_TIME);
        vo.setAdCost(BigDecimal.valueOf(data.getCost()));
        vo.setLabel(HourConvert.hourMap.get(localTime.getHour()));
        vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
        vo.setCpa(vo.getAdOrderNum() == 0 ? BigDecimal.ZERO : vo.getAdCost().divide(BigDecimal.valueOf(vo.getAdOrderNum()), 4, RoundingMode.HALF_UP));
        vo.setAcos(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdCost().multiply(new BigDecimal("100")).divide(vo.getAdSale(), 4, RoundingMode.HALF_UP));
        vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
        vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
        vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
        vo.setViewableImpressions(data.getViewableImpressions().intValue());
        vo.setOrdersNewToBrand(data.getAttributedOrdersNewToBrand14d());
        vo.setUnitsOrderedNewToBrand(data.getAttributedUnitsOrderedNewToBrand14d());
        vo.setSalesNewToBrand(data.getAttributedSalesNewToBrand14d());
        return vo;
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getIntegerHourlyReportDataMap(
            KeywordHourlyReportResponsePb.KeywordHourlyReportResponse statisticsByHourResponse,
            Map<Integer, HourlyReportDataPb.HourlyReportData> hourlyReportDataMap) {
        if (statisticsByHourResponse != null && statisticsByHourResponse.getDataCount() > 0) {
            hourlyReportDataMap = statisticsByHourResponse.getDataList().stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<Integer, AmazonMarketingStreamData> getIntegerHourlyReportDataMap(
            List<AmazonMarketingStreamData> amazonMarketingStreamDataList,
            Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(amazonMarketingStreamDataList)) {
            hourlyReportDataMap = amazonMarketingStreamDataList.stream().collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, (e1, e2) -> e2));
        }
        return hourlyReportDataMap;
    }

    private Map<String, AmazonMarketingStreamData> getBidHourlyReportDataMap(
            List<AmazonMarketingStreamData> statisticsByHourResponse,
            Map<String, AmazonMarketingStreamData> hourlyReportDataMap) {
        if (CollectionUtils.isNotEmpty(statisticsByHourResponse)) {
            hourlyReportDataMap = statisticsByHourResponse.stream()
                    .collect(Collectors.toMap(e1 -> DateUtil.dateToStrWithTime(e1.getTimeWindowStart(), "yyyy-MM-dd HH"), e1 -> e1));
        }
        return hourlyReportDataMap;
    }


    /**
     * 查询关键词小时级数据聚合
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getHourListAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param) {
        FeedHourlySelectDTO selectDto = buildMultiple(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                param.getKeywordIds(),
                param.getWeeks(),
                shopAuths
        );
        //获取小时级数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getFeedHourlyReport(selectDto, param.getKeywordIds());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        //获取小时对比数据
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            selectDto.setStart(LocalDate.parse(param.getStartDateCompare()));
            selectDto.setEnd(LocalDate.parse(param.getEndDateCompare()));
            hourlyReportDataCompareMap = getFeedHourlyReport(selectDto, param.getKeywordIds());
        }

        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adKeywordAndTargetHourVo);

        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }


    /**
     * 多店铺构造条件
     */
    public static FeedHourlySelectDTO buildMultiple(LocalDate start, LocalDate end, List<String> targetId, String week, List<ShopAuth> shopAuths){
        FeedHourlySelectDTO feedHourlySelectDTO = new FeedHourlySelectDTO();
        if (CollectionUtils.isNotEmpty(targetId)) {
            feedHourlySelectDTO.setKeywordIds(targetId);
        }
        feedHourlySelectDTO.setPuid(shopAuths.get(0).getPuid());
        feedHourlySelectDTO.setStart(start);
        feedHourlySelectDTO.setEnd(end);
        if (StringUtils.isNotBlank(week)) {
            feedHourlySelectDTO.setWeekdayList(StringUtil.splitInt(week));
        }
        feedHourlySelectDTO.setIsCompare(0);
        feedHourlySelectDTO.setShopAuths(shopAuths);
        List<String> sellerIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId);
        List<String> marketplaceIdList = StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId);
        List<String> concatWsList = StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId());
        feedHourlySelectDTO.setSellerIdList(sellerIdList);
        feedHourlySelectDTO.setMarketplaceIdList(marketplaceIdList);
        feedHourlySelectDTO.setConcatWsList(concatWsList);
        return feedHourlySelectDTO;
    }

    private Map<Integer, AmazonMarketingStreamData> getFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }

        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, selectDto, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }

        return reportDataMap;

    }


    private Map<Integer, AmazonMarketingStreamData> getAllTypeFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, selectDto, (x, y) -> callAllTypeFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;
    }

    private List<AmazonMarketingStreamData> callAllTypeFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> keywordIds) {
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(selectDto, queryDto);
        queryDto.setItemIds(keywordIds);

        List<AmazonMarketingStreamData> result = Lists.newArrayList();
        List<String> sellerIds = queryDto.getSellerIds();
        List<List<String>> sellerIdPartition = Lists.partition(sellerIds, Constants.DORIS_IN_PARTITION_MAX);
        for (List<String> sellerIdList : sellerIdPartition) {
            queryDto.setSellerIds(sellerIdList);
            List<AmazonMarketingStreamData> data = amazonMarketingStreamDataDao.statisticsAllTypeTargetHourlyReport(queryDto);
            if (CollectionUtils.isNotEmpty(data)) {
                result.addAll(data);
            }
        }
        queryDto.setSellerIds(sellerIds);
        return result;
    }

    private Map<Integer, AmazonMarketingStreamData> getFeedHourlyReport(FeedHourlySelectDTO selectDto, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new HashMap<>();
        }
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, selectDto, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, AmazonMarketingStreamData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;
    }

    private List<AmazonMarketingStreamData> callFeedHourlyReport(CampaignHourlyReportSelectDto selectDto, List<String> keywordIds) {
        CampaignHourlyReportSelectDto queryDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(selectDto, queryDto);
        queryDto.setItemIds(keywordIds);
        return amazonMarketingStreamDataDao.statisticsTargetHourlyReport(queryDto);
    }

    private List<AmazonMarketingStreamData> callFeedHourlyReport(FeedHourlySelectDTO selectDTO, List<String> targetIds) {
        //复制防并发问题
        FeedHourlySelectDTO copySelectDTO = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(selectDTO, copySelectDTO);
        copySelectDTO.setKeywordIds(targetIds);
        return amazonAdFeedReportService.getHourStreamData(copySelectDTO);
    }


    private HourlyReportDataPb.HourlyReportData aggregationHourlyReport(HourlyReportDataPb.HourlyReportData value1, HourlyReportDataPb.HourlyReportData value2) {
        HourlyReportDataPb.HourlyReportData.Builder newBuilder = HourlyReportDataPb.HourlyReportData.newBuilder();
        newBuilder.setKeywordId(value1.getKeywordId());
        newBuilder.setAdId(value1.getAdId());
        newBuilder.setAdGroupId(value1.getAdGroupId());
        newBuilder.setDate(value1.getDate());
        newBuilder.setKeywordText(value1.getKeywordText());
        newBuilder.setPlacement(value1.getPlacement());
        newBuilder.setTime(value1.getTime());

        newBuilder.setAttributedConversions1D(MathUtil.sumInteger(value1.getAttributedConversions1D(), value2.getAttributedConversions1D()));
        newBuilder.setAttributedConversions7D(MathUtil.sumInteger(value1.getAttributedConversions7D(), value2.getAttributedConversions7D()));
        newBuilder.setAttributedConversions14D(MathUtil.sumInteger(value1.getAttributedConversions14D(), value2.getAttributedConversions14D()));
        newBuilder.setAttributedConversions30D(MathUtil.sumInteger(value1.getAttributedConversions30D(), value2.getAttributedConversions30D()));
        newBuilder.setAttributedConversions1DSameSku(MathUtil.sumInteger(value1.getAttributedConversions1DSameSku(), value2.getAttributedConversions1DSameSku()));
        newBuilder.setAttributedConversions7DSameSku(MathUtil.sumInteger(value1.getAttributedConversions7DSameSku(), value2.getAttributedConversions7DSameSku()));
        newBuilder.setAttributedConversions14DSameSku(MathUtil.sumInteger(value1.getAttributedConversions14DSameSku(), value2.getAttributedConversions14DSameSku()));
        newBuilder.setAttributedConversions30DSameSku(MathUtil.sumInteger(value1.getAttributedConversions30DSameSku(), value2.getAttributedConversions30DSameSku()));

        newBuilder.setAttributedSales1D(MathUtil.sum(value1.getAttributedSales1D(), value2.getAttributedSales1D()));
        newBuilder.setAttributedSales7D(MathUtil.sum(value1.getAttributedSales7D(), value2.getAttributedSales7D()));
        newBuilder.setAttributedSales14D(MathUtil.sum(value1.getAttributedSales14D(), value2.getAttributedSales14D()));
        newBuilder.setAttributedSales30D(MathUtil.sum(value1.getAttributedSales30D(), value2.getAttributedSales30D()));
        newBuilder.setAttributedSales1DSameSku(MathUtil.sum(value1.getAttributedSales1DSameSku(), value2.getAttributedSales1DSameSku()));
        newBuilder.setAttributedSales7DSameSku(MathUtil.sum(value1.getAttributedSales7DSameSku(), value2.getAttributedSales7DSameSku()));
        newBuilder.setAttributedSales14DSameSku(MathUtil.sum(value1.getAttributedSales14DSameSku(), value2.getAttributedSales14DSameSku()));
        newBuilder.setAttributedSales30DSameSku(MathUtil.sum(value1.getAttributedSales30DSameSku(), value2.getAttributedSales30DSameSku()));

        newBuilder.setAttributedUnitsOrdered1D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered1D(), value2.getAttributedUnitsOrdered1D()));
        newBuilder.setAttributedUnitsOrdered7D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered7D(), value2.getAttributedUnitsOrdered7D()));
        newBuilder.setAttributedUnitsOrdered14D(MathUtil.sum(value1.getAttributedUnitsOrdered14D(), value2.getAttributedUnitsOrdered14D()));
        newBuilder.setAttributedUnitsOrdered30D(MathUtil.sumInteger(value1.getAttributedUnitsOrdered30D(), value2.getAttributedUnitsOrdered30D()));
        newBuilder.setAttributedUnitsOrdered1DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered1DSameSku(), value2.getAttributedUnitsOrdered1DSameSku()));
        newBuilder.setAttributedUnitsOrdered7DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered7DSameSku(), value2.getAttributedUnitsOrdered7DSameSku()));
        newBuilder.setAttributedUnitsOrdered14DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered14DSameSku(), value2.getAttributedUnitsOrdered14DSameSku()));
        newBuilder.setAttributedUnitsOrdered30DSameSku(MathUtil.sumInteger(value1.getAttributedUnitsOrdered30DSameSku(), value2.getAttributedUnitsOrdered30DSameSku()));


        newBuilder.setClicks(MathUtil.sumLong(value1.getClicks(), value2.getClicks()));
        newBuilder.setCost(MathUtil.sum(value1.getCost(), value2.getCost()));
        newBuilder.setImpressions(MathUtil.sumLong(value1.getImpressions(), value2.getImpressions()));

        return newBuilder.build();

    }


    /**
     * 关键词天维度报告
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDailyListAll(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdKeywordDailyAllReports(puid, shopAuths, adType, param);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdKeywordAndTargetHourVo> compareList = getAdKeywordDailyAllReports(puid, shopAuths, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingDayCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    private List<AdKeywordAndTargetHourVo> getAdKeywordDailyAllReports(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param) {
        if (CollectionUtils.isEmpty(param.getKeywordIds())) {
            return new ArrayList<>();
        }
        LocalDate start = LocalDate.parse(param.getStartDate());
        LocalDate end = LocalDate.parse(param.getEndDate());
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        List<String> marketplaceIds = shopAuths.stream().map(ShopAuth::getMarketplaceId).distinct().collect(Collectors.toList());
        //日,周,月报告数据
        if ("SP".equalsIgnoreCase(adType)) {
            List<OdsAmazonAdKeywordReport> reports =
                    odsAmazonAdKeywordReportDao.getSumReportByKeywordIdsByCountDate(puid, shopIds, marketplaceIds,
                            start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), param.getKeywordIds(), MultipleUtils.changeRate(shopAuths), AmznEndpoint.US.getCurrencyCode().value());
            return reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(DateUtil.dateToStrWithFormat(item.getCountDay(), DateUtil.PATTERN));
                vo.setDate(vo.getLabel());
                vo.setAdSale(BigDecimal.valueOf(item.getTotalSales()));
                vo.setAdSelfSale(BigDecimal.valueOf(item.getAdSales()));
                vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()));
                vo.setAdOrderNum(item.getSaleNum());
                vo.setSelfAdOrderNum(item.getAdSaleNum());
                vo.setOtherAdOrderNum(MathUtil.subtractInteger(item.getSaleNum(), item.getAdSaleNum()));
                vo.setAdSaleNum(item.getOrderNum());
                vo.setAdSelfSaleNum(item.getAdOrderNum());
                vo.setAdOtherSaleNum(MathUtil.subtractInteger(item.getOrderNum(), item.getAdOrderNum()));
                vo.setAdCost(BigDecimal.valueOf(item.getCost()));
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
        } else if ("SB".equalsIgnoreCase(adType)) {
            List<OdsAmazonAdSbKeywordReport> reports =
                    odsAmazonAdSbKeywordReportDao.getReportByKeywordIdsByCountDate(puid, shopIds, marketplaceIds,
                            start.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            end.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), param.getKeywordIds(), MultipleUtils.changeRate(shopAuths), AmznEndpoint.US.getCurrencyCode().value());
            List<AdKeywordAndTargetHourVo> adKeywordAndTargetHourVoList = reports.stream().map(item -> {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(DateUtil.dateToStrWithFormat(item.getCountDay(), DateUtil.PATTERN));
                vo.setDate(vo.getLabel());
                vo.setAdSale(BigDecimal.valueOf(item.getSales14d()));
                vo.setAdSelfSale(BigDecimal.valueOf(item.getSales14dSameSku()));
                vo.setAdOtherSale(MathUtil.subtract(vo.getAdSale(), vo.getAdSelfSale()));
                vo.setAdOrderNum(item.getConversions14d());
                vo.setSelfAdOrderNum(item.getConversions14dSameSku());
                vo.setOtherAdOrderNum(item.getConversions14d() - item.getConversions14dSameSku());
                vo.setAdSaleNum(item.getUnitsSold14d() == null ? 0 : item.getUnitsSold14d());
                vo.setAdCost(BigDecimal.valueOf(item.getCost()));
                vo.setClicks(Long.valueOf(item.getClicks()));
                vo.setImpressions(Long.valueOf(item.getImpressions()));
                vo.setAdCostPerClick(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getClicks())));
                vo.setCpa(MathUtil.divideByZero(vo.getAdCost(), BigDecimal.valueOf(vo.getAdOrderNum())));
                vo.setAcos(MathUtil.divideByZero(MathUtil.multiply(vo.getAdCost(), BigDecimal.valueOf(100)), vo.getAdSale()));
                vo.setCtr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getClicks()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getImpressions())));
                vo.setCvr(MathUtil.divideByZero(MathUtil.multiply(BigDecimal.valueOf(vo.getAdOrderNum()), BigDecimal.valueOf(100)), BigDecimal.valueOf(vo.getClicks())));
                vo.setRoas(vo.getAdSale().compareTo(BigDecimal.ZERO) == 0 || vo.getAdCost().compareTo(BigDecimal.ZERO) == 0 ? BigDecimal.ZERO : vo.getAdSale().divide(vo.getAdCost(), 4, RoundingMode.HALF_UP));
                return vo;
            }).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(adKeywordAndTargetHourVoList)) {
                //新增占比数据指标
                AdMetricDto adMetricDto = new AdMetricDto();
                filterSumMetricData(adKeywordAndTargetHourVoList, adMetricDto);
                filterMetricData(adKeywordAndTargetHourVoList, adMetricDto);
            }
            return adKeywordAndTargetHourVoList;
        }
        return null;
    }

    /**
     * 关键词周维度报告
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getWeeklyAllList(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdKeywordDailyAllReports(puid, shopAuths, adType, param);
        reports = ReportChartUtil.getWeekReportVos(param.getStartDate(), param.getEndDate(), reports);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdKeywordAndTargetHourVo> compareList = getAdKeywordDailyAllReports(puid, shopAuths, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getWeekReportVos(param.getStartDate(), param.getEndDate(), compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingWeekCompare(reports, compareList);
        } else {
            return reports;
        }
    }


    /**
     * 关键词月维度报告
     *
     * @param puid
     * @param adType
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getMonthlyAllList(int puid, List<ShopAuth> shopAuths, String adType, KeywordHourParam param, List<AdKeywordAndTargetHourVo> compares) {
        List<AdKeywordAndTargetHourVo> reports = getAdKeywordDailyAllReports(puid, shopAuths, adType, param);
        reports = ReportChartUtil.getMonthReportVos(reports);
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            String startDate = param.getStartDate();
            String endDate = param.getEndDate();
            param.setStartDate(param.getStartDateCompare());
            param.setEndDate(param.getEndDateCompare());
            List<AdKeywordAndTargetHourVo> compareList = getAdKeywordDailyAllReports(puid, shopAuths, adType, param);
            if (Objects.nonNull(compares) && CollectionUtils.isNotEmpty(compareList)) {
                compares.addAll(compareList);
            }
            compareList = ReportChartUtil.getMonthReportVos(compareList);
            param.setStartDate(startDate);
            param.setEndDate(endDate);
            return paddingMonthCompare(param, reports, compareList);
        } else {
            return reports;
        }
    }

    /**
     * 查询广告位维度关键词小时级数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordTargetHourOfPlacementVo> getListOfPlacementAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param) {
        return convertToHourOfPlacementVos(getDetailListOfPlacementAll(puid, shopAuths, param));
    }

    /**
     * 查询广告位维度关键词小时级明细数据
     *
     * @param puid
     * @param param
     * @return
     */
    @Override
    public List<AdKeywordAndTargetHourVo> getDetailListOfPlacementAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param) {
        //获取小时级数据
        FeedHourlySelectDTO selectDto = buildMultiple(LocalDate.parse(param.getStartDate()),
                LocalDate.parse(param.getEndDate()),
                null,
                param.getWeeks(),
                shopAuths
        );

        //pb对象转vo对象
        List<AmazonMarketingStreamData> dataList = getFeedHourlyOfPlacementReport(selectDto, param.getKeywordIds());
        List<AdKeywordAndTargetHourVo> voList = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });

        return voList.stream().sorted(Comparator.comparingInt(AdKeywordAndTargetHourVo::getPlacementOrder)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());

    }

    @Override
    public List<AdKeywordAndTargetHourVo> getSbHourList(int puid, KeywordHourParam param) {
        ShopAuth shopAuth = shopAuthDao.getScAndVcByIdAndPuid(param.getShopId(), puid);
        if (shopAuth == null) {
            return null;
        }
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(param.getShopId(), param.getStartDate().replace("-", ""), param.getEndDate().replace("-", ""));
        //获取小时级数据
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setSellerId(shopAuth.getSellingPartnerId());
        builder.setMarketplaceId(shopAuth.getMarketplaceId());
        builder.setStart(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setType(param.getType());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.setWeekdayList(StringUtil.splitInt(param.getWeeks(), ","));
        }
        if (StringUtils.isNotBlank(param.getKeywordId())) {
            List<String> keywordIds = Lists.newArrayList();
            keywordIds.add(param.getKeywordId());
            builder.setKeywordIds(keywordIds);
        }
        //产品透视需要查adid再过滤一层
        if (StringUtils.isNotBlank(param.getFindType()) && StringUtils.isNotBlank(param.getFindValue())) {
            ViewBaseParam viewBaseParam = new ViewBaseParam();
            viewBaseParam.setPuid(shopAuth.getPuid());
            viewBaseParam.setShopIdList(Collections.singletonList(shopAuth.getId()));
            viewBaseParam.setMarketplaceId(shopAuth.getMarketplaceId());
            viewBaseParam.setSearchType(param.getFindType());
            viewBaseParam.setSearchValue(param.getFindValue());
            viewBaseParam.setType(param.getType());
            List<String> adIds = viewManageHandler.queryProductBoLists(viewBaseParam.getPuid(), viewBaseParam)
                    .stream().map(AmazonAdProductPerspectiveBO::getAdId).distinct().collect(Collectors.toList());
            builder.setAdIds(adIds);
        }
        List<AmazonMarketingStreamData> amazonMarketingStreamDataList = amazonMarketingStreamDataDao.listByKeywordHourly(builder);

        //获取小时对比数据
        List<AmazonMarketingStreamData> compareResponse = null;
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = new HashMap<>();
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            compareResponse = amazonMarketingStreamDataDao.listByKeywordHourly(builder);
            hourlyReportDataCompareMap = getIntegerHourlyReportDataMap(compareResponse, hourlyReportDataCompareMap);
        }

        //组装数据
        hourlyReportDataMap = getIntegerHourlyReportDataMap(amazonMarketingStreamDataList, hourlyReportDataMap);
        List<AdKeywordAndTargetHourVo> voList = Lists.newArrayList();
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleSbVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
            voList.add(adKeywordAndTargetHourVo);

        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList;
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getSbHourListAll(int puid, List<ShopAuth> shopAuths, KeywordHourParam param) {
        BigDecimal shopSalesByDate = dwsSaleProfitShopDayDao.sumShopSaleByDateRange(param.getPuid(), param.getShopIdList(), param.getStartDate(), param.getEndDate(), MultipleUtils.changeRate(shopAuths));
        //获取小时级数据
        FeedHourlySelectDTO builder = new FeedHourlySelectDTO();
        builder.setPuid(puid);
        builder.setShopAuths(shopAuths);
        builder.setSellerIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getSellingPartnerId));
        builder.setMarketplaceIdList(StreamUtil.toListDistinct(shopAuths, ShopAuth::getMarketplaceId));
        builder.setConcatWsList(StreamUtil.toListDistinct(shopAuths, it-> it.getSellingPartnerId()+","+it.getMarketplaceId()));
        builder.setStart(LocalDate.parse(param.getStartDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setEnd(LocalDate.parse(param.getEndDate(), DateTimeFormatter.ISO_LOCAL_DATE));
        builder.setType(param.getType());
        if (StringUtils.isNotBlank(param.getWeeks())) {
            builder.setWeekdayList(StringUtil.splitInt(param.getWeeks(), ","));
        }
        //获取小时对比数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getFeedHourlyReport(builder, param.getKeywordIds());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStart(LocalDate.parse(param.getStartDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            builder.setEnd(LocalDate.parse(param.getEndDateCompare(), DateTimeFormatter.ISO_LOCAL_DATE));
            hourlyReportDataCompareMap = getFeedHourlyReport(builder, param.getKeywordIds());
        }

        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleSbVo(hour, hourlyReportDataMap.get(hour), hourlyReportDataCompareMap.get(hour), shopSalesByDate);
            voList.add(adKeywordAndTargetHourVo);

        }

        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getViewHourListAll(int puid, KeywordViewHourParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        builder.setSellerIds(sellerIds);
        builder.setMarketplaceId(param.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        builder.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        builder.setWeekdayList(weekdayList);
        if (CollectionUtils.isNotEmpty(param.getAdIdList())) {
            builder.setAdIds(param.getAdIdList());
        }

        if (StringUtils.isNotBlank(param.getAggregateType())) {
            //若为某个关键词的汇总，先获取该关键词的keywordId与总汇总id取交集
            this.getKeywordIdListByKeywordViewParam(puid, param);
        }
        //获取小时对比数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = getFeedHourlyReport(builder, param.getKeywordIdList());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDateCompare()));
            builder.setEndDate(param.getEndDateCompare());
            hourlyReportDataCompareMap = getFeedHourlyReport(builder, param.getKeywordIdList());
        }

        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = handleVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour));
            voList.add(adKeywordAndTargetHourVo);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    @Override
    public List<AdKeywordAndTargetHourVo> getViewHourListAllType(int puid, KeywordViewHourParam param) {
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return new ArrayList<>();
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto builder = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        builder.setSellerIds(sellerIds);
        builder.setMarketplaceId(param.getMarketplaceId());
        builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        builder.setEndDate(param.getEndDate());
        List<Integer> weekdayList = Optional.of(param.getWeeks()).filter(StringUtils::isNotBlank).map(w -> StringUtil.splitInt(w, ",")).orElse(HourConvert.weeKs);
        builder.setWeekdayList(weekdayList);
        if (CollectionUtils.isNotEmpty(param.getAdIdList())) {
            builder.setAdIds(param.getAdIdList());
        }

        if (StringUtils.isNotBlank(param.getAggregateType())) {
            //若为某个关键词的汇总，先获取该关键词的keywordId与总汇总id取交集
            this.getAllKeywordIdListByKeywordViewParam(puid, param);
        }
        //获取小时对比数据
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataMap = this.getAllTypeFeedHourlyReport(builder, param.getKeywordIdList());
        Map<Integer, AmazonMarketingStreamData> hourlyReportDataCompareMap = new HashMap<>();
        if (Integer.valueOf(1).equals(param.getIsCompare())) {
            builder.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDateCompare()));
            builder.setEndDate(param.getEndDateCompare());
            hourlyReportDataCompareMap = this.getAllTypeFeedHourlyReport(builder, param.getKeywordIdList());
        }
        //获取店铺销售额
        BigDecimal shopSalesByDate = cpcShopDataService.getShopSalesByDate(puid, param.getShopIdList(), param.getStartDate(), param.getEndDate());
        List<AdKeywordAndTargetHourVo> voList = new ArrayList<>(24);
        //按24小时返回数据
        for (Integer hour : HourConvert.hourMap.keySet()) {
            AdKeywordAndTargetHourVo adKeywordAndTargetHourVo = this.handleBaseVo(hour, hourlyReportDataMap.get(hour),
                    hourlyReportDataCompareMap.get(hour), shopSalesByDate);
            voList.add(adKeywordAndTargetHourVo);
        }
        if (CollectionUtils.isNotEmpty(voList)) {
            //新增占比数据指标
            AdMetricDto adMetricDto = new AdMetricDto();
            filterSumMetricData(voList, adMetricDto);
            filterMetricData(voList, adMetricDto);
        }
        return voList.stream().sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getHour)).collect(Collectors.toList());
    }

    private void getKeywordIdListByKeywordViewParam(int puid, KeywordViewHourParam param) {
        KeywordViewParam keywordViewParam = new KeywordViewParam();
        keywordViewParam.setPuid(param.getPuid());
        keywordViewParam.setShopIdList(param.getShopIdList());
        keywordViewParam.setCampaignId(param.getCampaignId());
        keywordViewParam.setAdGroupIdList(StringUtil.stringToList(param.getGroupId(), StringUtil.SPLIT_COMMA));
        keywordViewParam.setMatchType(param.getMatchType());
        keywordViewParam.setStatus(param.getStatus());
        keywordViewParam.setServingStatus(param.getServingStatus());
        keywordViewParam.setKeywordText(param.getAggregateType());
        keywordViewParam.setQueryType(param.getQueryType());
        keywordViewParam.setQueryValue(param.getQueryValue());
        List<String> keywordIds = amazonAdKeywordDaoRoutingService.getKeywordIdListByKeywordViewParam(puid, keywordViewParam);
        if (CollectionUtils.isEmpty(keywordIds) || CollectionUtils.isEmpty(param.getKeywordIdList())) {
            param.setKeywordIdList(new ArrayList<>());
        } else {
            keywordIds.retainAll(param.getKeywordIdList());
            param.setKeywordIdList(keywordIds);
        }
    }

    /**
     * 多种广告类型获取关键词id
     * @param puid
     * @param param
     */
    private void getAllKeywordIdListByKeywordViewParam(int puid, KeywordViewHourParam param) {
        KeywordViewParam keywordViewParam = new KeywordViewParam();
        keywordViewParam.setPuid(param.getPuid());
        keywordViewParam.setShopIdList(param.getShopIdList());
        keywordViewParam.setCampaignId(param.getCampaignId());
        keywordViewParam.setAdGroupIdList(StringUtil.stringToList(param.getGroupId(), StringUtil.SPLIT_COMMA));
        keywordViewParam.setKeywordIdList(param.getKeywordIdList());
        keywordViewParam.setMatchType(param.getMatchType());
        keywordViewParam.setStatus(param.getStatus());
        keywordViewParam.setServingStatus(param.getServingStatus());
        keywordViewParam.setKeywordText(param.getAggregateType());
        keywordViewParam.setQueryType(param.getQueryType());
        keywordViewParam.setQueryValue(param.getQueryValue());

        List<String> typeList = StringUtil.splitStr(param.getAdType()).stream().map(String::toLowerCase).collect(Collectors.toList());
        List<String> keywordIds = new ArrayList<>();
        if (typeList.contains(Constants.SP)) {
            keywordIds.addAll(amazonAdKeywordDaoRoutingService.getKeywordIdListByKeywordViewParam(puid, keywordViewParam));
        }
        if (typeList.contains(Constants.SB)) {
            keywordIds.addAll(amazonSbAdKeywordDao.getKeywordViewIdList(puid, keywordViewParam));
        }
        if (CollectionUtils.isEmpty(keywordIds) || CollectionUtils.isEmpty(param.getKeywordIdList())) {
            param.setKeywordIdList(new ArrayList<>());
        } else {
            keywordIds.retainAll(param.getKeywordIdList());
            param.setKeywordIdList(keywordIds);
        }
    }


    private List<AmazonMarketingStreamData> getFeedHourlyOfPlacementReport(CampaignHourlyReportSelectDto builder, List<String> keywordIds) {

        if (CollectionUtils.isEmpty(keywordIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, builder, (x, y) -> callFeedHourlyOfPlacementReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getPlacement();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;


    }

    private List<AmazonMarketingStreamData> getFeedHourlyOfPlacementReport(FeedHourlySelectDTO builder, List<String> targetIds) {
        if (CollectionUtils.isEmpty(targetIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> targetIds, builder, (x, y) -> callFeedHourlyOfPlacementReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getPlacement();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;
    }

    private List<AmazonMarketingStreamData> callFeedHourlyOfPlacementReport(CampaignHourlyReportSelectDto builder, List<String> keywordIds) {
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(builder, selectDto);
        selectDto.setItemIds(keywordIds);
        return amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfPlacement(selectDto);
    }

    private List<AmazonMarketingStreamData> callFeedHourlyOfPlacementReport(FeedHourlySelectDTO builder, List<String> targetIds) {
        FeedHourlySelectDTO selectDto = new FeedHourlySelectDTO();
        BeanUtils.copyProperties(builder, selectDto);
        selectDto.setKeywordIds(targetIds);
        return amazonAdFeedReportService.getTargetOfPlacementStreamData(selectDto);
    }

    private void filterSumMetricData(List<AdKeywordAndTargetHourVo> voList, AdMetricDto adMetricDto) {
        if (CollectionUtils.isEmpty(voList)) {
            return;
        }
        for (AdKeywordAndTargetHourVo vo : voList) {
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdCost())).
                    map(AdKeywordAndTargetHourVo::getAdCost).ifPresent(v -> adMetricDto.setSumCost(Optional.ofNullable(adMetricDto.getSumCost()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSale())).
                    map(AdKeywordAndTargetHourVo::getAdSale).ifPresent(v -> adMetricDto.setSumAdSale(Optional.ofNullable(adMetricDto.getSumAdSale()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdOrderNum())).
                    map(AdKeywordAndTargetHourVo::getAdOrderNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumAdOrderNum(Optional.ofNullable(adMetricDto.getSumAdOrderNum()).orElse(BigDecimal.ZERO).add(v)));
            Optional.ofNullable(vo).filter(item -> Objects.nonNull(item.getAdSaleNum())).
                    map(AdKeywordAndTargetHourVo::getAdSaleNum).map(BigDecimal::valueOf).ifPresent(v -> adMetricDto.setSumOrderNum(Optional.ofNullable(adMetricDto.getSumOrderNum()).orElse(BigDecimal.ZERO).add(v)));
        }
    }

    // 填充指标占比数据
    private void filterMetricData(List<AdKeywordAndTargetHourVo> voList, AdMetricDto adMetricDto) {
        for (AdKeywordAndTargetHourVo vo : voList) {
            if (adMetricDto == null) {
                vo.setAdCostPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdSalePercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setAdOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                vo.setOrderNumPercentage(BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP));
                continue;
            }
            computeMetricData(adMetricDto, vo);
        }
    }

    private void computeMetricData(AdMetricDto adMetricDto, AdKeywordAndTargetHourVo vo) {
        // 花费占比：单条广告的花费占所查询结果中所有广告花费之和的比例 单个广告花费/查询结果广告花费之和*100%
        if (vo.getAdCost() != null
                && adMetricDto.getSumCost() != null
                && adMetricDto.getSumCost().doubleValue() > 0) {
            vo.setAdCostPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdCost().toString(), adMetricDto.getSumCost().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 销售额占比：单条广告的销售额占查询结果中所有广告销售额之和的比例 单个广告销售额/查询结果广告销售额之和*100%
        if (vo.getAdSale() != null
                && adMetricDto.getSumAdSale() != null
                && adMetricDto.getSumAdSale().doubleValue() > 0) {
            vo.setAdSalePercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSale().toString(), adMetricDto.getSumAdSale().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告订单数占比：单条广告的订单数占查询结果中所有广告订单数的比例 单个广告订单数/查询结果广告订单数之和*100%
        if (vo.getAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum() != null
                && adMetricDto.getSumAdOrderNum().doubleValue() > 0) {
            vo.setAdOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdOrderNum().toString(), adMetricDto.getSumAdOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }

        // 广告销量占比：单条广告的销量占比查询结果中所有广告销量之和的比例 单个广告组合广告销量/查询结果广告组合广告销量之和*100%
        if (vo.getAdSaleNum() != null
                && adMetricDto.getSumOrderNum() != null
                && adMetricDto.getSumOrderNum().doubleValue() > 0) {
            vo.setOrderNumPercentage(new BigDecimal(MathUtil.multiply(MathUtil.divide(vo.getAdSaleNum().toString(), adMetricDto.getSumOrderNum().toString()), "100")).setScale(2, RoundingMode.HALF_UP));
        }
    }

    private Map<Integer, HourlyReportDataPb.HourlyReportData> getFeedHourlyReport(AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.Builder builder, List<String> keywordIds) {
        if (CollectionUtils.isEmpty(keywordIds) || CollectionUtils.isEmpty(builder.getAdIdList())) {
            return new HashMap<>();
        }
        List<AggregateTargetHourlyReportResponsePb.AggregateTargetHourlyReportResponse> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, builder, (x, y) -> callFeedHourlyReport(y, x));
        Map<Integer, HourlyReportDataPb.HourlyReportData> reportDataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            reportDataMap = productHourlyReportResponses.stream().filter(Objects::nonNull).filter(e -> e.getDataCount() > 0).map(AggregateTargetHourlyReportResponsePb.AggregateTargetHourlyReportResponse::getDataList).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport));
        }
        return reportDataMap;
    }

    private AggregateTargetHourlyReportResponsePb.AggregateTargetHourlyReportResponse callFeedHourlyReport(AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.Builder builder, List<String> keywordIds) {
        AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.Builder sendBuilder = AggregateTargetHourlyReportRequestPb.AggregateTargetHourlyReportRequest.newBuilder();
        sendBuilder.addAllSellerId(builder.getSellerIdList());
        sendBuilder.setMarketplaceId(builder.getMarketplaceId());
        sendBuilder.setStartDate(builder.getStartDate());
        sendBuilder.setEndDate(builder.getEndDate());
        if (CollectionUtils.isNotEmpty(builder.getWeekdayList())) {
            sendBuilder.addAllWeekday(builder.getWeekdayList());
        }
        sendBuilder.addAllTargetId(keywordIds);
        sendBuilder.addAllAdId(builder.getAdIdList());
        return adFeedBlockingStub.aggregateTargetHourlyReport(sendBuilder.build());
    }

    @Override
    public List<AdKeywordTargetHourOfPlacementVo> getViewHourListAllTypeOfPlacement(KeywordViewHourParam param) {
        Integer puid = param.getPuid();
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return Collections.emptyList();
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        selectDto.setSellerIds(sellerIds);
        selectDto.setMarketplaceId(param.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getAdIdList())) {
            selectDto.setAdIds(param.getAdIdList());
        }
        if (StringUtils.isNotBlank(param.getAggregateType())) {
            //若为某个关键词的汇总，先获取该关键词的keywordId与总汇总id取交集
            this.getAllKeywordIdListByKeywordViewParam(puid, param);
        }
        List<AmazonMarketingStreamData> dataList = getFeedHourlyOfPlacementReport(selectDto, param.getKeywordIdList());
        List<AdKeywordAndTargetHourVo> voList = dataList.stream().map(this::spConvertTo).collect(Collectors.toList());
        //填充无数据的时间段
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getPlacement));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setPlacement(k);
                //广告位排序
                if ("产品页面".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(3);
                } else if ("搜索结果顶部(首页)".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(1);
                } else if ("搜索结果的其余位置".equalsIgnoreCase(vo.getPlacement())) {
                    vo.setPlacementOrder(2);
                } else {
                    vo.setPlacementOrder(4);
                }
                voList.add(vo);
            }
        });
        List<AdKeywordAndTargetHourVo> vos = voList.stream().sorted(Comparator.comparingInt(AdKeywordAndTargetHourVo::getPlacementOrder)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
        vos.forEach(item -> {
            item.setTitle(item.getLabel());
        });
        return convertToHourOfPlacementVos(vos);
    }

    @Override
    public List<AdKeywordTargetHourOfAdVo> getViewHourListAllTypeOfAd(KeywordViewHourParam param) {
        Integer puid = param.getPuid();
        List<ShopAuth> shopAuths = shopAuthDao.listByPuidAndMarketplace(puid, Collections.singletonList(param.getMarketplaceId()), param.getShopIdList());
        if (CollectionUtils.isEmpty(shopAuths)) {
            return Collections.emptyList();
        }
        //获取小时级数据
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        List<String> sellerIds = shopAuths.stream().map(ShopAuth::getSellingPartnerId).collect(Collectors.toList());
        selectDto.setSellerIds(sellerIds);
        selectDto.setMarketplaceId(param.getMarketplaceId());
        selectDto.setStartDate(amazonAdFeedReportService.getSellerIdsDataStartTime(sellerIds, param.getMarketplaceId(), param.getStartDate()));
        selectDto.setEndDate(param.getEndDate());
        if (CollectionUtils.isNotEmpty(param.getAdIdList())) {
            selectDto.setAdIds(param.getAdIdList());
        }
        if (StringUtils.isNotBlank(param.getAggregateType())) {
            //若为某个关键词的汇总，先获取该关键词的keywordId与总汇总id取交集
            this.getAllKeywordIdListByKeywordViewParam(puid, param);
        }
        List<AmazonMarketingStreamData> dataList = getFeedHourlyOfAdReport(selectDto, param.getKeywordIdList());
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        List<String> adIds = dataList.stream().map(AmazonMarketingStreamData::getAdId).distinct().collect(Collectors.toList());
        List<Integer> shopIds = shopAuths.stream().map(ShopAuth::getId).collect(Collectors.toList());
        //查询所有对应的asin
        List<AmazonAdProduct> adProducts = amazonAdProductDao.getByAdIds(puid, shopIds, adIds);
        Map<String, AmazonAdProduct> adProductMap = adProducts.stream().collect(Collectors.toMap(
                AmazonAdProduct::getAdId, Function.identity(), (a, b) -> a));
        dataList.forEach(item -> {
            if (adProductMap.containsKey(item.getAdId())) {
                item.setAsin(adProductMap.get(item.getAdId()).getAsin());
            }
        });
        Collection<AmazonMarketingStreamData> values = dataList.stream().collect(Collectors.toMap(k -> {
            LocalTime localTime = LocalTime.parse(k.getTime(), DateTimeFormatter.ISO_TIME);
            return localTime.getHour() + "&&" + k.getAsin();
        }, v -> v, AggregationDataUtil::aggregationHourlyReport)).values();

        //pb对象转vo对象
        List<AdKeywordAndTargetHourVo> hourVos = values.stream().map(this::spConvertTo).collect(Collectors.toList());

        //填充asin信息
        List<AdKeywordAndTargetHourVo> voList = hourVos.stream().peek(item -> {
            if (adProductMap.containsKey(item.getAdId())) {
                item.setAsin(adProductMap.get(item.getAdId()).getAsin());
            }
        }).collect(Collectors.toList());
        //时段无数据填充0值
        Map<String, List<AdKeywordAndTargetHourVo>> voMap =
                voList.stream().collect(Collectors.groupingBy(AdKeywordAndTargetHourVo::getAsin));
        //所有时间
        Collection<String> allHours = HourConvert.hourMap.values();

        voMap.forEach((k, v) -> {
            List<String> hourList = v.stream().map(AdKeywordAndTargetHourVo::getLabel).collect(Collectors.toList());
            List<String> needFilledHour = allHours.stream()
                    .filter(item -> !hourList.contains(item)).collect(Collectors.toList());
            for (String hour : needFilledHour) {
                AdKeywordAndTargetHourVo vo = new AdKeywordAndTargetHourVo();
                vo.setLabel(hour);
                vo.setHour(Integer.valueOf(hour.split("-")[0]));
                vo.setAsin(k);
                voList.add(vo);
            }
        });

        List<AdKeywordAndTargetHourVo> vos = voList.stream().filter(o -> StringUtils.isNotBlank(o.getAsin()))
                .sorted(Comparator.comparing(AdKeywordAndTargetHourVo::getAsin)
                        .thenComparingInt(o -> Integer.parseInt(o.getLabel().split("-")[0])))
                .collect(Collectors.toList());
        vos.forEach(item -> {
            item.setTitle(item.getLabel());
        });
        return convertToHourOfAdVos(vos);
    }

    private List<AmazonMarketingStreamData> getFeedHourlyOfAdReport(CampaignHourlyReportSelectDto builder, List<String> keywordIds){
        if (CollectionUtils.isEmpty(keywordIds)) {
            return new ArrayList<>();
        }
        List<AmazonMarketingStreamData> result = new ArrayList<>();
        List<List<AmazonMarketingStreamData>> productHourlyReportResponses = multiThreadQueryAndMergeUtil.multiThreadQuery(() -> keywordIds, builder, (x, y) -> callFeedHourlyOfAdReport(y, x));
        if (CollectionUtils.isNotEmpty(productHourlyReportResponses)) {
            Collection<AmazonMarketingStreamData> values = productHourlyReportResponses.stream().filter(CollectionUtils::isNotEmpty).flatMap(Collection::stream).collect(Collectors.toMap(e1 -> {
                LocalTime localTime = LocalTime.parse(e1.getTime(), DateTimeFormatter.ISO_TIME);
                return localTime.getHour() + "&&" + e1.getAdId();
            }, e1 -> e1, AggregationDataUtil::aggregationHourlyReport)).values();
            if (CollectionUtils.isNotEmpty(values)) {
                result = Lists.newArrayList(values);
            }
        }
        return result;
    }

    private List<AmazonMarketingStreamData> callFeedHourlyOfAdReport(CampaignHourlyReportSelectDto builder, List<String> keywordIds) {
        CampaignHourlyReportSelectDto selectDto = new CampaignHourlyReportSelectDto();
        BeanUtils.copyProperties(builder, selectDto);
        selectDto.setItemIds(keywordIds);
        return amazonMarketingStreamDataDao.statisticsTargetHourlyReportOfAd(selectDto);
    }
}
