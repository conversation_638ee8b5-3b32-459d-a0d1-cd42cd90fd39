package com.meiyunji.sponsored.service.doris.dao.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.enums.OrderTypeEnum;
import com.meiyunji.sponsored.common.springjdbc.ObjectMapper;
import com.meiyunji.sponsored.common.util.DateUtil;
import com.meiyunji.sponsored.common.util.SqlStringUtil;
import com.meiyunji.sponsored.common.util.StringUtil;
import com.meiyunji.sponsored.rpc.asins.PageListAsinsRequest;
import com.meiyunji.sponsored.service.cpc.bo.AdAsinOrderBo;
import com.meiyunji.sponsored.service.cpc.constants.strategy.AdTargetStrategyTypeEnum;
import com.meiyunji.sponsored.service.cpc.dto.TargetReportHourlyDTO;
import com.meiyunji.sponsored.service.cpc.po.AmazonSdAdTargeting;
import com.meiyunji.sponsored.service.cpc.po.SdTargetTypeShowEnum;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.cpc.util.SqlStringReportUtil;
import com.meiyunji.sponsored.service.cpc.vo.*;
import com.meiyunji.sponsored.service.cpc.vo.chartVo.AdHomePerformancedto;
import com.meiyunji.sponsored.service.enums.AdOrderByFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.dto.DashboardAdTargetingMatrixTopDto;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardDataFieldEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.DashboardOrderByEnum;
import com.meiyunji.sponsored.service.newDashboard.enums.KeywordDataFieldEnum;
import com.meiyunji.sponsored.service.util.Constant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import com.meiyunji.sponsored.common.springjdbc.DorisBaseDaoImpl;
import com.meiyunji.sponsored.service.doris.dao.IOdsAmazonAdSdTargetingReportDao;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdSdTargetingReport;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * sd商品投放报告(OdsAmazonAdSdTargetingReport)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:20
 */
@Repository
@Slf4j
public class OdsAmazonAdSdTargetingReportDaoImpl extends DorisBaseDaoImpl<OdsAmazonAdSdTargetingReport> implements IOdsAmazonAdSdTargetingReportDao {

    private static final Map<String, String> sdTargetingFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> sdTargetingContainRateFieldSumMap = Maps.newHashMap();
    private static final Map<String, String> sdTargetingOrderFieldMap = Maps.newHashMap();

    static {
        sdTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.COST.getCode(), "ifnull(sum(report.cost * c.rate), 0) as cost ");
        sdTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.TOTAL_SALES.getCode(), "ifnull(sum(sales14d * c.rate), 0) as totalSales ");
        sdTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.ACOS.getCode(), " ifnull(ROUND(ifnull(sum(cost * c.rate), 0)/ ifnull(sum(sales14d * c.rate), 0), 4), 0) as acos ");
        sdTargetingContainRateFieldSumMap.put(DashboardDataFieldEnum.ROAS.getCode(), " ifnull(ROUND(ifnull(sum(sales14d * c.rate), 0)/ ifnull(sum(cost * c.rate), 0), 4), 0) as roas ");

        sdTargetingFieldSumMap.put(DashboardDataFieldEnum.IMPRESSIONS.getCode(), "ifnull(sum(report.impressions), 0) as impressions ");
        sdTargetingFieldSumMap.put(DashboardDataFieldEnum.CLICKS.getCode(), "ifnull(sum(report.clicks), 0) as clicks ");
        sdTargetingFieldSumMap.put(DashboardDataFieldEnum.ORDER_NUM.getCode(), " ifnull(sum(conversions14d), 0) as orderNum ");
        sdTargetingFieldSumMap.put(DashboardDataFieldEnum.SALE_NUM.getCode(), "ifnull(sum(units_ordered14d), 0) as saleNum ");
        sdTargetingFieldSumMap.put(DashboardDataFieldEnum.CLICK_RATE.getCode(), " ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) as clickRate ");//点击率
        sdTargetingFieldSumMap.put(DashboardDataFieldEnum.CONVERSION_RATE.getCode(), " ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) as conversionRate ");//转化率

        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.IMPRESSIONS.getCode(), " impressions as impressions ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.CLICKS.getCode(), " clicks as clicks ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.COST.getCode(), " cost as cost ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.ORDER_NUM.getCode(), " conversions14d as adOrder ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.TOTAL_SALES.getCode()," sales14d as adSales ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.CPC.getCode()," cost as cost,clicks as clicks ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.CPA.getCode()," cost as cost,conversions14d as adOrder ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.ACOS.getCode()," cost as cost,sales14d as adSales ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.ROAS.getCode()," cost as cost,sales14d as adSales ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.CONVERSION_RATE.getCode(), " clicks as clicks,conversions14d as adOrder ");
        sdTargetingOrderFieldMap.put(KeywordDataFieldEnum.CLICK_RATE.getCode(), " clicks as clicks,impressions as impressions ");
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getTargetingTopList(Integer puid, List<String> marketplaceIdList,
                                                                      List<Integer> shopIdList, String currency,
                                                                      String startDate, String endDate, DashboardDataFieldEnum dataField,
                                                                      List<String> targetIdList, Integer limit, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                      List<String> campaignIds, Boolean noZero) {
        boolean isWhere = false;
        if (Objects.isNull(sdTargetingFieldSumMap.get(dataField.getCode())) && Objects.isNull(sdTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT 'sd' as `type`, report.target_id targetingId, ");
        if (Objects.nonNull(sdTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            for (String rateField : sdTargetingContainRateFieldSumMap.keySet()) {
                sb.append(sdTargetingContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            sb.append(sdTargetingFieldSumMap.get(dataField.getCode()));
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append(" report ");
        if (Objects.nonNull(sdTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month ");
            sb.append(" and report.puid = ? ");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            isWhere = true;
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append("and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append("and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append("and report.target_id in ('").append(StringUtils.join(targetIdList, "','")).append("') ");
        }


        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }

        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }

        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            if (isWhere) {
                sb.append(" and ");
            } else {
                sb.append(" where ");
            }
            sb.append(" report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sd' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }

        sb.append(" group by report.target_id ");
        sb.append(" ORDER BY ").append(dataField.getCode());
        if (StringUtils.isNotEmpty(orderBy.getCode())) {
            sb.append(" ").append(orderBy.getCode());
        }
        if (Objects.nonNull(limit) && limit != 0) {
            sb.append(" LIMIT ").append(limit);
        }
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<DashboardAdTargetingMatrixTopDto> getTargetingTopInfoList(Integer puid, List<String> marketplaceIdList, List<Integer> shopIdList, String currency, String startDate, String endDate, DashboardDataFieldEnum dataField, List<String> targetIdList, DashboardOrderByEnum orderBy, List<String> siteToday, Boolean isSiteToday, List<String> portfolioIds,
                                                                          List<String> campaignIds, Boolean noZero) {
        if (Objects.isNull(sdTargetingFieldSumMap.get(dataField.getCode())) && Objects.isNull(sdTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            log.error("query can not get sort sql from the map, dataFiled:{}", dataField.getCode());
            return Collections.emptyList();
        }
        List<Object> argsList = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        sb.append(" SELECT 'sd' as `type`, report.target_id targetingId, ");
        if (Objects.nonNull(sdTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sdTargetingFieldSumMap.forEach((k, v) -> {
                    sb.append(v);
                    sb.append(",");
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        } else {
            for (String rateField : sdTargetingContainRateFieldSumMap.keySet()) {
                sb.append(sdTargetingContainRateFieldSumMap.get(rateField));
                sb.append(",");
            }
            sdTargetingFieldSumMap.forEach((k, v) -> {
                if (!k.equals((dataField.getCode()))) {
                    sb.append(v);
                    sb.append(",");
                }
            });
            sb.deleteCharAt(sb.length() - 1); //去除最后一列查询语句末尾的","
        }
        sb.append(" from ").append(getJdbcHelper().getTable()).append(" report ");
        if (Objects.isNull(sdTargetingContainRateFieldSumMap.get(dataField.getCode()))) {
            sb.append(" join (select * from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? ");
            sb.append(" and c.month >= ").append(DateUtil.dateStringFormat(startDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(" and c.month <= ").append(DateUtil.dateStringFormat(endDate, DateUtil.PATTERN, DateUtil.PATTERN_YYYYMM)).append(" ");
            sb.append(") c");
            sb.append(" on c.marketplace_id = report.marketplace_id and report.count_month = c.month and report.puid = ?");
            argsList.add(puid);
            argsList.add(currency);
        } else {
            sb.append(" where report.puid = ? ");
        }
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
            sb.append(" and report.marketplace_id in ('").append(StringUtils.join(marketplaceIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(shopIdList)) {
            sb.append(" and report.shop_id in ('").append(StringUtils.join(shopIdList, "','")).append("') ");
        }
        if (CollectionUtils.isNotEmpty(targetIdList)) {
            sb.append(" and report.target_id in ('").append(StringUtils.join(targetIdList, "','")).append("') ");
        }



        if (CollectionUtils.isNotEmpty(siteToday) && Boolean.TRUE.equals(isSiteToday)) {
            sb.append(SqlStringUtil.dealDorisInList("concat_ws('|', report.marketplace_id, report.count_day)", siteToday, argsList));
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            LocalDate now = LocalDate.now();
            argsList.add(now.plusDays(-1).format(DateTimeFormatter.ISO_LOCAL_DATE));
            argsList.add(now.plusDays(1).format(DateTimeFormatter.ISO_LOCAL_DATE));
        } else {
            sb.append(" and report.count_day >= ? and report.count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        if (CollectionUtils.isNotEmpty(portfolioIds)) {
            sb.append(" and report.campaign_id in ( ");
            sb.append(" select campaign_id from ods_t_amazon_ad_campaign_all where puid = ? and type = 'sd' ");
            argsList.add(puid);
            if (portfolioIds.contains(Constant.NON_PORTFOLIO_ID)) {
                if (portfolioIds.size() == 1) {
                    sb.append(" and ( ").append(" portfolio_id is null or portfolio_id = '' ) ");
                } else {
                    ArrayList<String> pr = Lists.newArrayList(portfolioIds);
                    pr.add("");
                    sb.append(" and ( ").append(" portfolio_id is null ")
                            .append(SqlStringUtil.dealInListOr("portfolio_id", pr, argsList))
                            .append( " )  ");
                }
            } else {
                sb.append(SqlStringUtil.dealDorisInList("portfolio_id", portfolioIds, argsList));
            }
            if (CollectionUtils.isNotEmpty(marketplaceIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("marketplace_id", marketplaceIdList, argsList));
            }
            if (CollectionUtils.isNotEmpty(shopIdList)) {
                sb.append(SqlStringUtil.dealDorisInList("shop_id", shopIdList, argsList));
            }

            sb.append(" ) ");
        }
        if (CollectionUtils.isNotEmpty(campaignIds)) {
            sb.append(SqlStringUtil.dealDorisInList("report.campaign_id", campaignIds, argsList));
        }
        if (Boolean.TRUE.equals(noZero)) {
            sb.append(" and " + dataField.getCode() + " <> 0 ");
        }


        sb.append(" group by report.target_id ");
        return getJdbcTemplate().query(sb.toString(), new ObjectMapper<>(DashboardAdTargetingMatrixTopDto.class), argsList.toArray());
    }

    @Override
    public List<AsinLibsDetailVo> getSdAsinReportDataByTargetId(Integer puid, AsinLibsDetailParam param, List<String> sdTargetIds) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder selectSql = new StringBuilder();
        selectSql.append("select target_id targetId, any(targeting_text) targetingText, sum(cost * c.rate) cost, sum(impressions) impressions, " +
                "sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales, ifnull(ROUND(ifnull(sum(cost), 0)/ ifnull(sum(sales14d), 0), 4), 0) acos, " +
                "ifnull(ROUND(ifnull(sum(sales14d), 0)/ ifnull(sum(cost), 0), 4), 0) roas, ifnull(sum(cost * c.rate)/sum(clicks),0) cpc, ifnull(sum(cost * c.rate)/sum(conversions14d),0) cpa, " +
                "ifnull(ROUND(ifnull(sum(conversions14d)/ sum(clicks), 0), 4), 0) salesConversionRate, ifnull(ROUND(ifnull(sum(clicks)/ sum(impressions), 0), 4), 0) clickRate ");
        selectSql.append(" from ").append(getJdbcHelper().getTable()).append(" r ");
        selectSql.append(" join (select * from dim_currency_rate where puid = ? and `to` = ?) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        argsList.add(puid);
        argsList.add(param.getTo());
        selectSql.append(" join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        selectSql.append(" where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopIdList())) {
            selectSql.append(SqlStringUtil.dealInList("r.shop_id", param.getShopIdList(), argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCountry())) {
            selectSql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getCountry(), argsList));
        }
        selectSql.append(" and r.count_day >= ? and r.count_day <= ? ");
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        if (CollectionUtils.isNotEmpty(sdTargetIds)) {
            selectSql.append(SqlStringUtil.dealInList("r.target_id", sdTargetIds, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getSearchAdGroupList())) {
            selectSql.append(SqlStringUtil.dealInList("r.ad_group_id", param.getSearchAdGroupList(), argsList));
        }
        selectSql.append(" group by target_id ");
        return getJdbcTemplate().query(selectSql.toString(), new ObjectMapper<>(AsinLibsDetailVo.class), argsList.toArray());
    }

    @Override
    public List<AdAsinOrderBo> getOrderFieldByAsins(Integer puid, List<String> shopIds, PageListAsinsRequest param, List<String> asinList) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlSb = new StringBuilder("select targeting_text asin ");
        if (StringUtils.isNotBlank(param.getOrderField())) {
            sqlSb.append(",");
            sqlSb.append(sdTargetingOrderFieldMap.get(param.getOrderField()));
        }
        sqlSb.append("from ods_t_amazon_ad_sd_targeting_report report where puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(shopIds)){
            sqlSb.append(SqlStringUtil.dealInList("report.shop_id",shopIds,argsList));
        }
        //通过来源站点筛选
        if (StringUtils.isNotEmpty(param.getMarketplaceId())) {
            sqlSb.append(SqlStringUtil.dealInList("report.marketplace_id", Arrays.asList(param.getMarketplaceId().split(",")), argsList));
        }

        if (StringUtils.isNotBlank(param.getStartDate()) && StringUtils.isNotBlank(param.getEndDate())) {
            sqlSb.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(param.getStartDate());
            argsList.add(param.getEndDate());
        }
        sqlSb.append(" and target_id in ");

        sqlSb.append(" (SELECT target_id FROM ods_t_amazon_ad_targeting_sd d WHERE d.puid = ? ");
        argsList.add(puid);

        sqlSb.append("and d.type = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)) {
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : asinList) {
                lowerCaseList.add(str.toLowerCase());
            }
            sqlSb.append(SqlStringUtil.dealInList("lower(d.target_text)", lowerCaseList, argsList));
        }
        sqlSb.append(" ) ");
        return getJdbcTemplate().query(sqlSb.toString(), new ObjectMapper<>(AdAsinOrderBo.class), argsList.toArray());
    }

    @Override
    public List<AsinLibsDetailVo> getAsinReportByAsins(Integer puid, PageListAsinsRequest param,
                                                      String startDate, String endDate,
                                                      List<String> asinList, String currency) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder("select targeting_text asin, sum(cost * c.rate) cost, sum(impressions) impressions, sum(clicks) clicks, sum(conversions14d) saleNum, sum(sales14d * c.rate) totalSales from ods_t_amazon_ad_sd_targeting_report r ");
        sql.append("join (select * from dim_currency_rate where puid = ?");
        argsList.add(puid);
        sql.append(" and `to` = ? ");
        argsList.add(currency);
        sql.append(" ) c on r.puid = c.puid and DATE_FORMAT(r.count_date, '%Y%m') = c.month ");
        sql.append("join dim_marketplace_info m on m.marketplace_id = r.marketplace_id and c.`from` = m.currency ");
        sql.append("where r.puid = ? ");
        argsList.add(puid);
        if (CollectionUtils.isNotEmpty(param.getShopListList())) {
            sql.append(SqlStringUtil.dealInList("shop_id", param.getShopListList(), argsList));
        }
        //通过来源站点筛选
        if (CollectionUtils.isNotEmpty(param.getContryList())) {
            sql.append(SqlStringUtil.dealInList("r.marketplace_id", param.getContryList(), argsList));
        }

        if (StringUtils.isNotBlank(startDate) && StringUtils.isNotBlank(endDate)) {
            sql.append(" and count_day >= ? and count_day <= ? ");
            argsList.add(startDate);
            argsList.add(endDate);
        }
        sql.append(" and target_id in ");

        sql.append(" (SELECT target_id FROM ods_t_amazon_ad_targeting_sd d WHERE d.puid = ? ");
        argsList.add(puid);

        sql.append("and d.type = 'asin' ");

        if (CollectionUtils.isNotEmpty(asinList)) {
            // 创建一个新的列表来存储转换后的字符串
            List<String> lowerCaseList = new ArrayList<>();
            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
            for (String str : asinList) {
                lowerCaseList.add(str.toLowerCase());
            }
            sql.append(SqlStringUtil.dealInList("lower(d.target_text)", lowerCaseList, argsList));
        }
        sql.append(" ) ");
//        if (CollectionUtils.isNotEmpty(asinList)) {
//            // 创建一个新的列表来存储转换后的字符串
//            List<String> lowerCaseList = new ArrayList<>();
//            // 遍历原列表，将每个字符串转换为小写并添加到新列表中
//            for (String str : asinList) {
//                lowerCaseList.add("asin=\"" + str.toLowerCase() + "\"");
//            }
//            sql.append(SqlStringUtil.dealInList("lower(targeting_text)", lowerCaseList, argsList));
//        }
        sql.append(" group by targeting_text");
        return getJdbcTemplate().query(sql.toString(), (res, i) -> {
            String strAsin = res.getString("asin").replaceAll("asin=", "").replaceAll("\"", "");
            String asin = Optional.of(strAsin).filter(StringUtils::isNotEmpty).orElse("");
            return AsinLibsDetailVo.builder()
                    .asin(asin)
                    .cost(res.getBigDecimal("cost"))
                    .impressions(res.getLong("impressions"))
                    .clicks(res.getLong("clicks"))
                    .saleNum(res.getInt("saleNum"))
                    .totalSales(res.getBigDecimal("totalSales"))
                    .build();
        }, argsList.toArray());
    }

    @Override
    public int getTargetAllCount(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sqlCountSb = new StringBuilder("select count(*) from (select k.target_id ");
        StringBuilder sb = new StringBuilder(" from ods_t_amazon_ad_targeting_sd k");
        // 没有高级筛选或排序时，只查基础数据表即为列表页所有的关键词，有高级筛选或排序时需连报告表过滤
        if (joinReport(param, false)) {
            sb.append(" left join ( ").append(sdTargetingReportSql(param, argsList)).append(" ) r on k.target_id = r.target_id ");
        }
        if (joinGroup(param, false)) {
            sb.append(" join ods_t_amazon_ad_group_sd g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        if (joinCampaign(param, false)) {
            // 如果高级筛选vcpm，则只展示 cost_type=vcpm
            sb.append(" join ods_t_amazon_ad_campaign_all c on k.puid = c.puid and k.shop_id = c.shop_id and k.campaign_id = c.campaign_id ")
                    .append(" and c.puid = ? and c.shop_id = ? and lower(c.type) = 'sd' and lower(c.cost_type) = 'vcpm' ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sb.append(getTargetPageWhereSql(puid, param, argsList));
        sb.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sb.append(getTargetPageHavingSql(param, argsList));
        }
        sqlCountSb.append(sb).append(" ) f");
        Object[] args = argsList.toArray();
        return countPageResult(puid, sqlCountSb.toString(), args);
    }

    private String sdTargetingReportSql(TargetingPageParam param, List<Object> argsList) {
        String sql = " select " +
                " target_id, " +
                " ifnull(sum(impressions), 0) impressions," +
                " ifnull(sum(clicks), 0) clicks," +
                " ifnull(sum(cost), 0) cost," +
                // 销售额
                " ifnull(sum(sales14d), 0) total_sales," +
                // 本产品销售额
                " ifnull(sum(sales14d_same_sku), 0) ad_sales," +
                // 订单量
                " ifnull(sum(conversions14d), 0) sale_num," +
                // 本产品订单量
                " ifnull(sum(conversions14d_same_sku), 0) ad_sale_num," +
                // 销量
                " ifnull(sum(units_ordered14d), 0) sales_num," +
                // 品牌新买家订单量
                " ifnull(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d," +
                // 可见展示次数
                " ifnull(sum(view_impressions), 0) view_impressions," +
                // 品牌新买家销售额
                " ifnull(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d," +
                // 品牌新买家观看量
                " ifnull(sum(new_to_brand_detail_page_views), 0) new_to_brand_detail_page_views," +
                // 加购次数
                " ifnull(sum(add_to_cart), 0) add_to_cart," +
                // 视频播至1/4次数
                " ifnull(sum(video_first_quartile_views), 0) video_first_quartile_views," +
                // 视频播至1/2次数
                " ifnull(sum(video_Midpoint_Views), 0) video_Midpoint_Views," +
                // 视频播至3/4次数
                " ifnull(sum(video_third_quartile_views), 0) video_third_quartile_views," +
                // 视频完整播放次数
                " ifnull(sum(video_complete_views), 0) video_complete_views," +
                // 视频取消静音
                " ifnull(sum(video_unmutes), 0) video_unmutes," +
                // 品牌搜索次数
                " ifnull(sum(branded_searches14d), 0) branded_searches14d," +
                // DPV 在广告浏览或点击后的14天内，商品详细页面的浏览量
                " ifnull(sum(detail_page_view14d), 0) detail_page_view14d" +
                " from ods_t_amazon_ad_sd_targeting_report where " +
                " puid = ? and shop_id = ? and count_day >= ? and count_day <= ? " +
                " group by target_id ";
        argsList.add(param.getPuid());
        argsList.add(param.getShopId());
        argsList.add(param.getStartDate());
        argsList.add(param.getEndDate());
        return sql;
    }

    private boolean joinReport(TargetingPageParam param, boolean checkOrderField) {
        boolean filter = param.getUseAdvanced()
                && (Objects.nonNull(param.getImpressionsMin()) || Objects.nonNull(param.getImpressionsMax()) ||
                Objects.nonNull(param.getClicksMin()) || Objects.nonNull(param.getClicksMax()) ||
                Objects.nonNull(param.getClickRateMin()) || Objects.nonNull(param.getClickRateMax()) ||
                Objects.nonNull(param.getCostMin()) || Objects.nonNull(param.getCostMax()) ||
                Objects.nonNull(param.getCpaMin()) || Objects.nonNull(param.getCpaMax()) ||
                Objects.nonNull(param.getCpcMin()) || Objects.nonNull(param.getCpcMax()) ||
                Objects.nonNull(param.getVcpmMin()) || Objects.nonNull(param.getVcpmMax()) ||
                Objects.nonNull(param.getOrderNumMin()) || Objects.nonNull(param.getOrderNumMax()) ||
                Objects.nonNull(param.getAdSaleNumMin()) || Objects.nonNull(param.getAdSaleNumMax()) ||
                Objects.nonNull(param.getSalesMin()) || Objects.nonNull(param.getSalesMax()) ||
                Objects.nonNull(param.getAdSalesMin()) || Objects.nonNull(param.getAdSalesMax()) ||
                Objects.nonNull(param.getAcosMin()) || Objects.nonNull(param.getAcosMax()) ||
                Objects.nonNull(param.getRoasMin()) || Objects.nonNull(param.getRoasMax()) ||
                Objects.nonNull(param.getSalesConversionRateMin()) || Objects.nonNull(param.getSalesConversionRateMax()) ||
                Objects.nonNull(param.getAcotsMin()) || Objects.nonNull(param.getAcotsMax()) ||
                Objects.nonNull(param.getAsotsMin()) || Objects.nonNull(param.getAsotsMax()) ||
                Objects.nonNull(param.getAdSalesTotalMin()) || Objects.nonNull(param.getAdSalesTotalMax()) ||
                // 品牌新买家订单转化率
                Objects.nonNull(param.getBrandNewBuyerOrderConversionRateMin()) || Objects.nonNull(param.getBrandNewBuyerOrderConversionRateMax()) ||
                // 品牌新买家订单量
                Objects.nonNull(param.getOrdersNewToBrandFTDMin()) || Objects.nonNull(param.getOrdersNewToBrandFTDMax()) ||
                // 品牌新买家订单百分比
                Objects.nonNull(param.getOrderRateNewToBrandFTDMin()) || Objects.nonNull(param.getOrderRateNewToBrandFTDMax()) ||
                // “品牌新买家”销售额
                Objects.nonNull(param.getSalesNewToBrandFTDMin()) || Objects.nonNull(param.getSalesNewToBrandFTDMax()) ||
                // 品牌新买家销售额百分比
                Objects.nonNull(param.getSalesRateNewToBrandFTDMin()) || Objects.nonNull(param.getSalesRateNewToBrandFTDMax()) ||
                // 观看率
                Objects.nonNull(param.getViewabilityRateMin()) || Objects.nonNull(param.getViewabilityRateMax()) ||
                // 观看点击率
                Objects.nonNull(param.getViewClickThroughRateMin()) || Objects.nonNull(param.getViewClickThroughRateMax()) ||
                // 品牌搜索次数
                Objects.nonNull(param.getBrandedSearchesMin()) || Objects.nonNull(param.getBrandedSearchesMax()) ||
                // 广告笔单价
                Objects.nonNull(param.getAdvertisingUnitPriceMin()) || Objects.nonNull(param.getAdvertisingUnitPriceMax()) ||
                // 加购次数
                Objects.nonNull(param.getAddToCartMin()) || Objects.nonNull(param.getAddToCartMax()) ||
                // 视频播放完整次数
                Objects.nonNull(param.getVideoCompleteViewsMin()) || Objects.nonNull(param.getVideoCompleteViewsMax()));
        if (!checkOrderField) {
            return filter;
        }
        if (StringUtils.isNotBlank(param.getOrderField())
                && StringUtils.equalsAnyIgnoreCase(param.getOrderType(), "asc", "desc")
                && !StringUtils.equalsIgnoreCase(param.getOrderField(), SqlStringReportUtil.BID)) {
            // 竞价不在报告表 不用连报告表查询
            return true;
        }
        return filter;
    }

    private boolean joinGroup(TargetingPageParam param, boolean checkOrderField) {
        boolean filter = param.getUseAdvanced() && (Objects.nonNull(param.getBidMin()) || Objects.nonNull(param.getBidMax()));
        if (!checkOrderField) {
            return filter;
        }
        if (StringUtils.isNotBlank(param.getOrderField())
                && StringUtils.equalsAnyIgnoreCase(param.getOrderType(), "asc", "desc")
                && SqlStringReportUtil.BID.equals(param.getOrderField())) {
            return true;
        }
        return filter;
    }

    private boolean joinCampaign(TargetingPageParam param, boolean checkOrderField) {
        boolean filter = param.getUseAdvanced() && (Objects.nonNull(param.getVcpmMin()) || Objects.nonNull(param.getVcpmMax()));
        if (!checkOrderField) {
            return filter;
        }
        if (StringUtils.isNotBlank(param.getOrderField())
                && StringUtils.equalsAnyIgnoreCase(param.getOrderType(), "asc", "desc")
                && AdOrderByFieldEnum.VCPM.getCode().equals(param.getOrderField())) {
            return true;
        }
        return filter;
    }

    private String getTargetPageWhereSql(Integer puid, TargetingPageParam param, List<Object> argsList) {
        StringBuilder sb = new StringBuilder();
        sb.append(" where k.puid = ? and k.shop_id = ?");
        argsList.add(puid);
        argsList.add(param.getShopId());

        if (StringUtils.isNotBlank(param.getCampaignId())) {  //广告活动id
            List<String> list = StringUtil.splitStr(param.getCampaignId());
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getCampaignIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.campaign_id", param.getCampaignIdList(), argsList));
        }

        if (StringUtils.isNotBlank(param.getGroupId())) {  //广告组id
            List<String> list = StringUtil.splitStr(param.getGroupId());
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.ad_group_id", list, argsList));
        }
        if (CollectionUtils.isNotEmpty(param.getGroupIdList())) { //广告组合查询
            sb.append(SqlStringUtil.dealInList("k.ad_group_id", param.getGroupIdList(), argsList));
        }
        // 广告策略筛选
        if(CollectionUtils.isNotEmpty(param.getAdStrategyTypeList())){
            String sql = AdTargetStrategyTypeEnum.getSql(param.getAdStrategyTypeList(), param.getAutoRuleIds(),param.getAutoRuleGroupIds(), argsList, "k.target_id","k.ad_group_id");
            if(StringUtils.isNotEmpty(sql)){
                sb.append(sql);
            }
        }
        //标签管理
        if (CollectionUtils.isNotEmpty(param.getTargetIds())) {
            sb.append(SqlStringUtil.dealBitMapDorisInList("k.target_id", param.getTargetIds(), argsList));
        }

        //匹配新版SD投放融合
        List<String> tacticTargetList = Arrays.asList(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode(),
                SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode(),
                SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode());
        List<String> filterTargetTypeList;
        if (StringUtils.isNotBlank(param.getFilterTargetType()) && CollectionUtils.isNotEmpty(filterTargetTypeList = Arrays.asList(param.getFilterTargetType().split(",")))) {
            List<String> conditionList = Lists.newArrayList();
            if (tacticTargetList.parallelStream().anyMatch(filterTargetTypeList::contains)) {
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.VIEW_PURCHASE.getCode())) {
                    conditionList.add("k.target_type in ('views', 'purchases')");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.INMARKET_AUDIENCE.getCode())) {
                    conditionList.add("k.type in ('In-market')");
                }
                if (filterTargetTypeList.contains(SdTargetTypeShowEnum.AUDIENCE_CONTENT_CATEGORY.getCode())){
                    //兴趣及生活方式受众(兴趣：Interest ; 内容分类：contentCategorySameAs ; 生活事件：Life event ; 生活方式：Lifestyle; 受众：audienceSameAs ; 受众： Audience)
                    conditionList.add("k.type in ('Interest', 'contentCategorySameAs', 'Life event', 'Lifestyle', 'audienceSameAs', 'Audience')");
                }
            } else {
                if (filterTargetTypeList.contains("asin")) {
                    conditionList.add("k.type='asin'");
                } else if (filterTargetTypeList.contains("category")) {
                    conditionList.add("k.target_type = 'asinCategorySameAs'");
                } else if (filterTargetTypeList.contains("similarProduct")) {
                    //受众投放类型(亚马逊消费者：audience ; 购买再营销：purchases ; 再营销浏览定向：views ; 与推广商品相似：similarProduct)
                    conditionList.add("k.target_type = 'similarProduct'");
                }
            }
            if (CollectionUtils.isNotEmpty(conditionList)) {
                sb.append(" and ( ").append(String.join(" or ", conditionList)).append(" ) ");
            }
        }

        if (StringUtils.isNotBlank(param.getSearchField()) && StringUtils.isNotBlank(param.getSearchValue())) {  //搜索值
            String lowerSearchValue = param.getSearchValue().toLowerCase();
            if ("asin".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and lower(k.target_text) = ? and k.type = 'asin' ");
                argsList.add(lowerSearchValue);
            } else if ("category".equalsIgnoreCase(param.getSearchField())) {
                sb.append(" and lower(k.target_text) like ? and k.type = 'category' ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(lowerSearchValue) + "%");
            } else {
                sb.append(" and lower(k.target_text) like ? ");
                argsList.add("%" + SqlStringUtil.dealLikeSql(lowerSearchValue) + "%");
            }
        }

        if (StringUtils.isNotBlank(param.getStatus())) {  //状态
            List<String> statusList = StringUtil.splitStr(param.getStatus(), ",");
            sb.append(SqlStringUtil.dealInList("k.state", statusList, argsList));
        }

        //服务状态筛选
        if (StringUtils.isNotBlank(param.getServingStatus())) {
            // 仅显示正在投放
            if (StringUtils.isNotBlank(param.getServingStatus()) && Constants.ENABLED.equals(param.getServingStatus())) {
                sb.append(" and k.serving_status = ? ");
                argsList.add(AmazonSdAdTargeting.servingStatusEnum.TARGETING_CLAUSE_STATUS_LIVE.getCode());
            } else {
                List<String> list = StringUtil.splitStr(param.getServingStatus(), ",");
                sb.append(SqlStringUtil.dealInList("k.serving_status", list, argsList));
            }
        }

        if (StringUtils.isNotEmpty(param.getChosenTargetType())) {
            sb.append(" and k.tactic_type = ? ");
            argsList.add(param.getChosenTargetType());
        }

        return sb.toString();
    }

    private String getTargetPageHavingSql(TargetingPageParam param, List<Object> argsList) {
        BigDecimal shopSales = Objects.nonNull(param.getShopSales()) ? param.getShopSales() : BigDecimal.ZERO;
        List<String> havingList = Lists.newArrayList();
        buildHaving(param, "bid", " ifnull(any_value(k.bid), any_value(g.default_bid)) ", havingList, argsList);
        buildHaving(param, "impressions", " ifnull(sum(r.impressions), 0) ", havingList, argsList);
        buildHaving(param, "clicks", " ifnull(sum(r.clicks), 0) ", havingList, argsList);
        buildHaving(param, "clickRate", " round(ifnull(sum(r.clicks) / sum(r.impressions) * 100, 0), 2) ", havingList, argsList);
        buildHaving(param, "cost", " ifnull(sum(r.cost), 0) ", havingList, argsList);
        buildHaving(param, "cpa", " round(ifnull(sum(r.cost) / sum(r.sale_num), 0), 2) ", havingList, argsList);
        buildHaving(param, "cpc", " round(ifnull(sum(r.cost) / sum(r.clicks), 0), 2) ", havingList, argsList);
        buildHaving(param, "vcpm", " round(ifnull(sum(r.cost) / sum(r.view_impressions) * 1000, 0), 2) ", havingList, argsList);
        buildHaving(param, "orderNum", " ifnull(sum(r.sale_num), 0) ", havingList, argsList);
        buildHaving(param, "adSaleNum", " ifnull(sum(r.ad_sale_num), 0) ", havingList, argsList);
        buildHaving(param, "sales", " ifnull(sum(r.total_sales), 0) ", havingList, argsList);
        buildHaving(param, "adSales", " ifnull(sum(r.ad_sales), 0) ", havingList, argsList);
        buildHaving(param, "acos", " round(ifnull(sum(r.cost) / sum(r.total_sales) * 100, 0), 2) ", havingList, argsList);
        buildHaving(param, "roas", " round(ifnull(sum(r.total_sales) / sum(r.cost), 0), 2) ", havingList, argsList);
        buildHaving(param, "salesConversionRate", " round(ifnull(sum(r.sale_num) / sum(r.clicks) * 100, 0), 2) ", havingList, argsList);
        buildHaving(param, "acots", " round(ifnull(sum(r.cost) / " + shopSales + " * 100, 0), 2) ", havingList, argsList);
        buildHaving(param, "asots", " round(ifnull(sum(r.total_sales) / " + shopSales + " * 100, 0), 2) ", havingList, argsList);
        buildHaving(param, "adSalesTotal", " ifnull(sum(r.sales_num), 0) ", havingList, argsList);
        buildHaving(param, "brandNewBuyerOrderConversionRate", " round(ifnull(sum(r.orders_new_to_brand14d) / sum(r.clicks) * 100, 0), 2) ", havingList, argsList);
        buildHaving(param, "ordersNewToBrandFTD", " ifnull(sum(r.orders_new_to_brand14d), 0) ", havingList, argsList);
        buildHaving(param, "orderRateNewToBrandFTD", " round(ifnull(sum(r.orders_new_to_brand14d) / sum(r.sale_num) * 100, 0), 2) ", havingList, argsList);
        // “品牌新买家”销售额
        buildHaving(param, "salesNewToBrandFTD", " ifnull(sum(r.sales_new_to_brand14d), 0) ", havingList, argsList);
        // 品牌新买家销售额百分比
        buildHaving(param, "salesRateNewToBrandFTD", " round(ifnull(sum(r.sales_new_to_brand14d) / sum(r.total_sales) * 100, 0), 2) ", havingList, argsList);
        // 观看率
        buildHaving(param, "viewabilityRate", " round(ifnull(sum(r.view_impressions) / sum(r.impressions) * 100, 0), 2) ", havingList, argsList);
        // 观看点击率
        buildHaving(param, "viewClickThroughRate", " round(ifnull(sum(r.clicks) / sum(r.view_impressions) * 100, 0), 2) ", havingList, argsList);
        // 品牌搜索次数
        buildHaving(param, "brandedSearches", " ifnull(sum(r.branded_searches14d), 0) ", havingList, argsList);
        // 广告笔单价
        buildHaving(param, "advertisingUnitPrice", " round(ifnull(sum(r.total_sales) / sum(r.sale_num), 0), 2) ", havingList, argsList);
        // 加购次数
        buildHaving(param, "addToCart", " ifnull(sum(r.add_to_cart), 0) ", havingList, argsList);
        // 视频完整播放次数
        buildHaving(param, "videoCompleteViews", " ifnull(sum(r.video_complete_views), 0) ", havingList, argsList);
        return CollectionUtils.isNotEmpty(havingList) ? " having " + String.join(" and ", havingList) : "";
    }

    private void buildHaving(TargetingPageParam param, String field, String condition, List<String> havingList, List<Object> argsList) {
        try {
            Field fieldMin = param.getClass().getDeclaredField(field + "Min");
            fieldMin.setAccessible(true);
            Optional.ofNullable(fieldMin.get(param)).ifPresent(i -> {
                havingList.add(condition + " >= ? ");
                argsList.add(i);
            });

            Field fieldMax = param.getClass().getDeclaredField(field + "Max");
            fieldMax.setAccessible(true);
            Optional.ofNullable(fieldMax.get(param)).ifPresent(i -> {
                havingList.add(condition + " <= ? ");
                argsList.add(i);
            });
        } catch (NoSuchFieldException | IllegalAccessException e) {
            log.error("通过反射构造高级筛选 error ", e);
        }
    }

    @Override
    public Page<TargetPageVo> getTargetPage(TargetingPageParam param) {
        Integer puid = param.getPuid();
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select k.target_id targetId from ods_t_amazon_ad_targeting_sd k ");
        if (joinReport(param, true)) {
            sql.append(" left join ( ").append(sdTargetingReportSql(param, argsList)).append(" ) r on k.target_id = r.target_id ");
        }
        if (joinGroup(param, true)) {
            sql.append(" join ods_t_amazon_ad_group_sd g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        if (joinCampaign(param, true)) {
            sql.append(" join ods_t_amazon_ad_campaign_all c on k.puid = c.puid and k.shop_id = c.shop_id and k.campaign_id = c.campaign_id ")
                    .append(" and c.puid = ? and c.shop_id = ? and lower(c.type) = 'sd' ");
            argsList.add(puid);
            argsList.add(param.getShopId());
            // 如果高级筛选vcpm，则只展示 cost_type=vcpm
            if (param.getUseAdvanced() && (Objects.nonNull(param.getVcpmMin()) || Objects.nonNull(param.getVcpmMax()))) {
                sql.append(" and lower(c.cost_type) = 'vcpm' ");
            }
        }
        sql.append(getTargetPageWhereSql(puid, param, argsList));
        sql.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sql.append(getTargetPageHavingSql(param, argsList));
        }
        String countSql = "select count(*) from ( " + sql + " ) ct ";
        // 排序
        sql.append(targetPageOrderSql(param));
        Object[] args = argsList.toArray();
        return getPageResultByClass(param.getPageNo(), param.getPageSize(), countSql, args, sql.toString(), args, TargetPageVo.class);
    }

    private String targetPageOrderSql(TargetingPageParam param) {
        if (StringUtils.isBlank(param.getOrderField()) ||
                !StringUtils.equalsAnyIgnoreCase(param.getOrderType(), "asc", "desc")) {
            return " order by any_value(k.creation_date) desc ";
        }
        String orderType = StringUtils.equalsIgnoreCase(param.getOrderType(), "asc") ? "asc" : "desc";
        String orderClause;
        switch (param.getOrderField()) {
            case "bid":
                orderClause = " ifnull(any_value(k.bid), any_value(g.default_bid)) ";
                break;
            case "impressions":
                orderClause = " sum(r.impressions) ";
                break;
            case "clicks":
                orderClause = " sum(r.clicks) ";
                break;
            case "ctr":
                orderClause = " ifnull(sum(r.clicks) / sum(r.impressions), 0) ";
                break;
            case "adCost":
            case "adCostPercentage":
            case "acots":
                orderClause = " sum(r.cost) ";
                break;
            case "acos":
                orderClause = " ifnull(sum(r.cost) / sum(r.total_sales), 0) ";
                break;
            case "roas":
                orderClause = " ifnull(sum(r.total_sales) / sum(r.cost), 0) ";
                break;
            case "asots":
                orderClause = " sum(r.total_sales) ";
                break;
            case "advertisingUnitPrice":
                orderClause = " ifnull(sum(r.total_sales) / sum(r.sale_num), 0) ";
                break;
            case "adCostPerClick":
                orderClause = " ifnull(sum(r.cost) / sum(r.clicks), 0) ";
                break;
            case "cpa":
                orderClause = " ifnull(sum(r.cost) / sum(r.sale_num), 0) ";
                break;
            case "vcpm":
                int value = OrderTypeEnum.desc.getType().equals(orderType) ? Integer.MIN_VALUE : Integer.MAX_VALUE;
                orderClause = " if(lower(any_value(c.cost_type)) = 'vcpm', ifnull(sum(r.cost) / sum(r.view_impressions), 0), " + value + ")";
                break;
            case "viewImpressions":
                orderClause = " sum(r.view_impressions) ";
                break;
            case "cvr":
                orderClause = " ifnull(sum(r.sale_num) / sum(r.clicks), 0) ";
                break;
            case "adOrderNum":
            case "adOrderNumPercentage":
                orderClause = " sum(r.sale_num) ";
                break;
            case "adSale":
            case "adSalePercentage":
                orderClause = " sum(r.total_sales) ";
                break;
            case "orderNum":
            case "orderNumPercentage":
                orderClause = " sum(r.sales_num) ";
                break;
            case "adSaleNum":
                orderClause = " sum(r.ad_sale_num) ";
                break;
            case "adSales":
                orderClause = " sum(r.ad_sales) ";
                break;
            case "ordersNewToBrandFTD":
                orderClause = " sum(r.orders_new_to_brand14d) ";
                break;
            case "orderRateNewToBrandFTD":
                orderClause = " ifnull(sum(r.orders_new_to_brand14d) / sum(r.sale_num), 0) ";
                break;
            case "ordersNewToBrandPercentageFTD":
                orderClause = " ifnull(sum(r.orders_new_to_brand14d) / sum(r.clicks), 0) ";
                break;
            case "salesNewToBrandFTD":
                orderClause = " sum(r.sales_new_to_brand14d) ";
                break;
            case "salesRateNewToBrandFTD":
                orderClause = " ifnull(sum(r.sales_new_to_brand14d) / sum(r.total_sales), 0) ";
                break;
            case "newToBrandDetailPageViews":
                orderClause = " sum(r.new_to_brand_detail_page_views) ";
                break;
            case "videoFirstQuartileViews":
                orderClause = " sum(r.video_first_quartile_views) ";
                break;
            case "videoMidpointViews":
                orderClause = " sum(r.video_Midpoint_Views) ";
                break;
            case "videoThirdQuartileViews":
                orderClause = " sum(r.video_third_quartile_views) ";
                break;
            case "videoCompleteViews":
                orderClause = " sum(r.video_complete_views) ";
                break;
            case "videoUnmutes":
                orderClause = " sum(r.video_unmutes) ";
                break;
            case "viewabilityRate":
                orderClause = " ifnull(sum(r.view_impressions) / sum(r.impressions), 0) ";
                break;
            case "viewClickThroughRate":
                orderClause = " ifnull(sum(r.clicks) / sum(r.view_impressions), 0) ";
                break;
            case "addToCart":
                orderClause = " sum(r.add_to_cart) ";
                break;
            case "addToCartRate":
                orderClause = " ifnull(sum(r.add_to_cart) / sum(r.impressions), 0) ";
                break;
            case "eCPAddToCart":
                orderClause = " ifnull(sum(r.cost) / sum(r.add_to_cart), 0) ";
                break;
            case "detailPageViews":
                orderClause = " sum(r.detail_page_view14d) ";
                break;
            case "brandedSearches":
                orderClause = " sum(r.branded_searches14d) ";
                break;
            default:
                orderClause = "";
        }
        if (StringUtils.isNotBlank(orderClause)) {
            return " order by " + orderClause + orderType;
        } else {
            return " order by any_value(k.creation_date) desc ";
        }
    }

    @Override
    public List<TargetPageVo> getReportListByTargetIds(Integer puid, Integer shopId, List<String> targetIdList, String startDate, String endDate) {

        if (CollectionUtils.isEmpty(targetIdList)) {
            return new ArrayList<>();
        }
        List<Object> argsList = new ArrayList<>();

        StringBuilder sql = new StringBuilder();
        sql.append(" select ")
                .append(" target_id, 'sd' type,")
                .append(" ifnull(sum(impressions), 0) impressions,")
                .append(" ifnull(sum(clicks), 0) clicks,")
                .append(" ifnull(sum(cost), 0) cost,")
                // 销售额
                .append(" ifnull(sum(sales14d), 0) total_sales,")
                // 本产品销售额
                .append(" ifnull(sum(sales14d_same_sku), 0) ad_sales,")
                // 订单量
                .append(" ifnull(sum(conversions14d), 0) sale_num,")
                // 本产品订单量
                .append(" ifnull(sum(conversions14d_same_sku), 0) ad_sale_num,")
                // 销量
                .append(" ifnull(sum(units_ordered14d), 0) order_num,")
                // 品牌新买家订单量
                .append(" ifnull(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                // 可见展示次数
                .append(" ifnull(sum(view_impressions), 0) view_impressions,")
                // 品牌新买家销售额
                .append(" ifnull(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                // 品牌新买家观看量
                .append(" ifnull(sum(new_to_brand_detail_page_views), 0) new_to_brand_detail_page_views,")
                // 加购次数
                .append(" ifnull(sum(add_to_cart), 0) add_to_cart,")
                // 视频播至1/4次数
                .append(" ifnull(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                // 视频播至1/2次数
                .append(" ifnull(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                // 视频播至3/4次数
                .append(" ifnull(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                // 视频完整播放次数
                .append(" ifnull(sum(video_complete_views), 0) video_complete_views,")
                // 视频取消静音
                .append(" ifnull(sum(video_unmutes), 0) video_unmutes,")
                // 品牌搜索次数
                .append(" ifnull(sum(branded_searches14d), 0) branded_searches14d,")
                // DPV 在广告浏览或点击后的14天内，商品详细页面的浏览量
                .append(" ifnull(sum(detail_page_view14d), 0) detail_page_view14d")
                .append(" from ods_t_amazon_ad_sd_targeting_report where ")
                .append(" puid = ? and shop_id = ? and count_day >= ? and count_day <= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        argsList.add(startDate);
        argsList.add(endDate);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIdList, argsList));
        sql.append(" group by target_id ");
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(),
                (rs, i) -> {
                    TargetPageVo vo = new TargetPageVo();
                    vo.setTargetId(rs.getString("target_id"));
                    vo.setType(rs.getString("type"));
                    vo.setCost(rs.getBigDecimal("cost"));
                    vo.setImpressions(rs.getInt("impressions"));
                    vo.setClicks(rs.getInt("clicks"));
                    vo.setTotalSales(rs.getBigDecimal("total_sales"));
                    vo.setAdSales(rs.getBigDecimal("ad_sales"));
                    vo.setSaleNum(rs.getInt("sale_num"));
                    vo.setAdSaleNum(rs.getInt("ad_sale_num"));
                    vo.setOrderNum(rs.getInt("order_num"));
                    vo.setOrdersNewToBrand14d(rs.getInt("orders_new_to_brand14d"));
                    vo.setSalesNewToBrand14d(rs.getBigDecimal("sales_new_to_brand14d"));
                    vo.setViewImpressions(rs.getInt("view_impressions"));
                    vo.setNewToBrandDetailPageViews(rs.getInt("new_to_brand_detail_page_views"));
                    vo.setAddToCart(rs.getInt("add_to_cart"));
                    vo.setVideoFirstQuartileViews(rs.getInt("video_first_quartile_views"));
                    vo.setVideoMidpointViews(rs.getInt("video_Midpoint_Views"));
                    vo.setVideoThirdQuartileViews(rs.getInt("video_third_quartile_views"));
                    vo.setVideoCompleteViews(rs.getInt("video_complete_views"));
                    vo.setVideoUnmutes(rs.getInt("video_unmutes"));
                    vo.setBrandedSearches(rs.getInt("branded_searches14d"));
                    vo.setDetailPageViews(rs.getInt("detail_page_view14d"));
                    return vo;
                });
    }

    @Override
    public AdMetricDto getTargetPageSumMetricData(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder(" select k.target_id targetId,")
                .append(" sum(cost) cost,")
                .append(" sum(total_sales) total_sales,")
                .append(" sum(sale_num) sale_num,")
                .append(" sum(sales_num) sales_num")
                .append(" from ods_t_amazon_ad_targeting_sd k left join (")
                .append(sdTargetingReportSql(param, argsList)).append(" ) r on k.target_id = r.target_id ");
        if (joinGroup(param, false)) {
            sql.append(" join ods_t_amazon_ad_group_sd g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        if (joinCampaign(param, false)) {
            sql.append(" join ods_t_amazon_ad_campaign_all c on k.puid = c.puid and k.shop_id = c.shop_id and k.campaign_id = c.campaign_id ")
                    .append(" and c.puid = ? and c.shop_id = ? and lower(c.type) = 'sd' and lower(c.cost_type) = 'vcpm' ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sql.append(getTargetPageWhereSql(puid, param, argsList));
        sql.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sql.append(getTargetPageHavingSql(param, argsList));
        }

        String sumSql = "select " +
                " SUM(cost) sumCost," +
                " SUM(total_sales) sumAdSale," +
                " SUM(sale_num) sumAdOrderNum," +
                " SUM(sales_num) sumOrderNum" +
                " from (" + sql + ") st ";

        Object[] args = argsList.toArray();
        List<AdMetricDto> list = getJdbcTemplate().query(sumSql, args, new BeanPropertyRowMapper<>(AdMetricDto.class));
        return list.size() == 1 ? list.get(0) : null;
    }

    @Override
    public List<String> getTargetIdListByPage(Integer puid, TargetingPageParam param) {
        List<Object> argsList = new ArrayList<>();
        String sql = this.getTargetIdListByPageSql(puid, param, argsList, false);
        return this.getJdbcTemplate().queryForList(sql.toString(), String.class, argsList.toArray());
    }

    private String getTargetIdListByPageSql(Integer puid, TargetingPageParam param, List<Object> argsList, boolean isLeftJoin) {
        StringBuilder sql = new StringBuilder(" select k.target_id targetId from ods_t_amazon_ad_targeting_sd k ");
        if (isLeftJoin) {
            sql.append(" left ");
        }
        sql.append(" join ");
        sql.append(" ( ").append(sdTargetingReportSql(param, argsList)).append(" ) r on k.target_id = r.target_id ");
        if (joinGroup(param, false)) {
            sql.append(" join ods_t_amazon_ad_group_sd g on k.puid = g.puid and k.shop_id = g.shop_id and k.ad_group_id = g.ad_group_id ")
                    .append(" and g.puid = ? and g.shop_id = ? ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        if (joinCampaign(param, false)) {
            sql.append(" join ods_t_amazon_ad_campaign_all c on k.puid = c.puid and k.shop_id = c.shop_id and k.campaign_id = c.campaign_id ")
                    .append(" and c.puid = ? and c.shop_id = ? and lower(c.type) = 'sd' and lower(c.cost_type) = 'vcpm' ");
            argsList.add(puid);
            argsList.add(param.getShopId());
        }
        sql.append(getTargetPageWhereSql(puid, param, argsList));
        sql.append(" group by k.target_id ");
        if (param.getUseAdvanced()) {
            sql.append(getTargetPageHavingSql(param, argsList));
        }
        return sql.toString();
    }

    @Override
    public List<AdHomePerformancedto> getReportAggregateByTargetIdList(Integer puid, Integer shopId, String startStr, String endStr, List<String> targetIdList, boolean isGroupByDate) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sd' type,");
        if (isGroupByDate) {
            sql.append(" any_value(count_date) count_date, ");
        }
        sql.append(" ifnull(sum(impressions), 0) impressions,")
                .append(" ifnull(sum(clicks), 0) clicks,")
                .append(" ifnull(sum(cost), 0) cost,")
                .append(" ifnull(sum(sales14d), 0) total_sales,")
                .append(" ifnull(sum(sales14d_same_sku), 0) ad_sales,")
                .append(" ifnull(sum(conversions14d), 0) sale_num, ")
                .append(" ifnull(sum(conversions14d_same_sku), 0) conversions14d_same_sku,")
                .append(" ifnull(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append(" ifnull(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append(" ifnull(sum(units_ordered14d), 0) sales_num,")
                .append(" ifnull(sum(view_impressions), 0) view_impressions,")
                .append(" ifnull(sum(new_to_brand_detail_page_views), 0) new_to_brand_detail_page_views,")
                .append(" ifnull(sum(add_to_cart), 0) add_to_cart,")
                .append(" ifnull(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append(" ifnull(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append(" ifnull(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append(" ifnull(sum(video_complete_views), 0) video_complete_views,")
                .append(" ifnull(sum(video_unmutes), 0) video_unmutes,")
                .append(" ifnull(sum(branded_searches14d), 0) branded_searches14d,")
                .append(" ifnull(sum(detail_page_view14d), 0) detail_page_view14d")
                .append(" from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(shopId);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIdList, argsList));
        sql.append(" and count_day >= ? and count_day <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);
        if (isGroupByDate) {
            sql.append(" group by count_day ");
        }
//        return getJdbcTemplate().query(sql.toString(), argsList.toArray(), new BeanPropertyRowMapper<>(AdHomePerformancedto.class));
        return getJdbcTemplate().query(sql.toString(), argsList.toArray(),
                (re, i) ->
                        AdHomePerformancedto.builder()
//                                .targetId(re.getString("target_id"))
                                .adCost(re.getBigDecimal("cost"))
                                .adOrderNum(re.getInt("sale_num"))
                                .adSale(re.getBigDecimal("total_sales"))
                                .clicks(re.getInt("clicks"))
                                .impressions(re.getInt("impressions"))
                                .countDate(isGroupByDate ? re.getString("count_date") : "")
                                //本广告产品订单量
                                .adSaleNum(re.getInt("conversions14d_same_sku"))
                                //本广告产品销售额
                                .adSales(re.getBigDecimal("ad_sales"))
                                //广告销量
                                .salesNum(re.getInt("sales_num"))
                                //“品牌新买家”订单量
                                .ordersNewToBrand14d(re.getInt("orders_new_to_brand14d"))
                                //“品牌新买家”销售额
                                .salesNewToBrand14d(re.getBigDecimal("sales_new_to_brand14d"))
                                .type(re.getString("type"))
                                //可见展示次数
                                .viewImpressions(re.getInt("view_impressions"))
                                .newToBrandDetailPageViews(re.getInt("new_to_brand_detail_page_views"))
                                .addToCart(re.getInt("add_to_cart"))
                                .videoFirstQuartileViews(re.getInt("video_first_quartile_views"))
                                .videoMidpointViews(re.getInt("video_Midpoint_Views"))
                                .videoThirdQuartileViews(re.getInt("video_third_quartile_views"))
                                .videoCompleteViews(re.getInt("video_complete_views"))
                                .videoUnmutes(re.getInt("video_unmutes"))
                                .brandedSearches(re.getInt("branded_searches14d"))
                                .detailPageViews(re.getInt("detail_page_view14d"))
                                .build()
        );
    }

    @Override
    public AdHomePerformancedto getReportAggregateCompareData(Integer puid, TargetingPageParam param, String startStr, String endStr) {
        List<Object> argsList = new ArrayList<>();
        //按天聚合
        StringBuilder sql = new StringBuilder(" select 'sd' type,");
        sql.append(" ifnull(sum(impressions), 0) impressions,")
                .append(" ifnull(sum(clicks), 0) clicks,")
                .append(" ifnull(sum(cost), 0) cost,")
                .append(" ifnull(sum(sales14d), 0) total_sales,")
                .append(" ifnull(sum(sales14d_same_sku), 0) ad_sales,")
                .append(" ifnull(sum(conversions14d), 0) sale_num, ")
                .append(" ifnull(sum(conversions14d_same_sku), 0) conversions14d_same_sku,")
                .append(" ifnull(sum(orders_new_to_brand14d), 0) orders_new_to_brand14d,")
                .append(" ifnull(sum(sales_new_to_brand14d), 0) sales_new_to_brand14d,")
                .append(" ifnull(sum(units_ordered14d), 0) sales_num,")
                .append(" ifnull(sum(view_impressions), 0) view_impressions,")
                .append(" ifnull(sum(new_to_brand_detail_page_views), 0) new_to_brand_detail_page_views,")
                .append(" ifnull(sum(add_to_cart), 0) add_to_cart,")
                .append(" ifnull(sum(video_first_quartile_views), 0) video_first_quartile_views,")
                .append(" ifnull(sum(video_Midpoint_Views), 0) video_Midpoint_Views,")
                .append(" ifnull(sum(video_third_quartile_views), 0) video_third_quartile_views,")
                .append(" ifnull(sum(video_complete_views), 0) video_complete_views,")
                .append(" ifnull(sum(video_unmutes), 0) video_unmutes,")
                .append(" ifnull(sum(branded_searches14d), 0) branded_searches14d,")
                .append(" ifnull(sum(detail_page_view14d), 0) detail_page_view14d")
                .append(" from ")
                .append(this.getJdbcHelper().getTable())
                .append(" where puid= ? and shop_id= ? ");
        argsList.add(puid);
        argsList.add(param.getShopId());
        sql.append(" and count_day >= ? and count_day <= ? ");
        argsList.add(startStr);
        argsList.add(endStr);
        sql.append(" and target_id in ( ").append(this.getTargetIdListByPageSql(puid, param, argsList, true)).append(" ) ");
        List<AdHomePerformancedto> list = getJdbcTemplate().query(sql.toString(), (re, i) ->
                AdHomePerformancedto.builder()
                        .adCost(re.getBigDecimal("cost"))
                        .adOrderNum(re.getInt("sale_num"))
                        .adSale(re.getBigDecimal("total_sales"))
                        .clicks(re.getInt("clicks"))
                        .impressions(re.getInt("impressions"))
                        //本广告产品订单量
                        .adSaleNum(re.getInt("conversions14d_same_sku"))
                        //本广告产品销售额
                        .adSales(re.getBigDecimal("ad_sales"))
                        //广告销量
                        .salesNum(re.getInt("sales_num"))
                        //“品牌新买家”订单量
                        .ordersNewToBrand14d(re.getInt("orders_new_to_brand14d"))
                        //“品牌新买家”销售额
                        .salesNewToBrand14d(re.getBigDecimal("sales_new_to_brand14d"))
                        .type(re.getString("type"))
                        //可见展示次数
                        .viewImpressions(re.getInt("view_impressions"))
                        .newToBrandDetailPageViews(re.getInt("new_to_brand_detail_page_views"))
                        .addToCart(re.getInt("add_to_cart"))
                        .videoFirstQuartileViews(re.getInt("video_first_quartile_views"))
                        .videoMidpointViews(re.getInt("video_Midpoint_Views"))
                        .videoThirdQuartileViews(re.getInt("video_third_quartile_views"))
                        .videoCompleteViews(re.getInt("video_complete_views"))
                        .videoUnmutes(re.getInt("video_unmutes"))
                        .brandedSearches(re.getInt("branded_searches14d"))
                        .detailPageViews(re.getInt("detail_page_view14d"))
                        .build(), argsList.toArray());
        return list.size() == 1 ? list.get(0) : new AdHomePerformancedto();
    }

    @Override
    public List<TargetReportHourlyDTO> getReportByTargetId(Integer puid, List<Integer> shopIds, List<String> marketplaceIds, String startDate, String endDate,
                                                           List<String> targetIds, boolean changeRate, String currency) {
        List<Object> args = new ArrayList<>();
        //子查询sql
        StringBuilder sql = new StringBuilder("SELECT any(count_date) count_date,")
                .append(" ifnull(sum(clicks), 0) as clicks, ifnull(sum(`impressions`), 0) as `impressions`,")
                .append(" ifnull(sum(conversions14d), 0) as order_num, ifnull(sum(units_ordered14d), 0) as sale_num,")
                .append(" ifnull(sum(conversions14d_same_sku), 0) as ad_order_num, ");
//                .append(" sum(view_impressions) as view_impressions, ")
//                .append("sum(orders_new_to_brand14d) as orders_new_to_brand14d, sum(units_ordered_new_to_brand14d) as units_ordered_new_to_brand14d, ")
        if (changeRate) {
            sql.append(" ifnull(sum(cost * d.rate), 0) as cost, ")
                    .append(" ifnull(sum(sales14d * d.rate), 0) as total_sales, ifnull(sum(sales14d_same_sku * d.rate), 0) as ad_sales ");
//                    .append(" sum(sales_new_to_brand14d) as sales_new_to_brand14d");
        } else {
            sql.append(" ifnull(sum(cost), 0) as cost, ")
                    .append(" ifnull(sum(sales14d), 0) as total_sales, ifnull(sum(sales14d_same_sku), 0) as ad_sales ");
//                    .append(" sum(sales_new_to_brand14d) as sales_new_to_brand14d");
        }
        sql.append(" from ").append(this.getJdbcHelper().getTable()).append(" r ");
        if (changeRate) {
            String start = DateUtil.dateToStrWithFormat(DateUtil.strToDate(startDate, "yyyy-MM-dd"), "yyyyMM");
            String end = DateUtil.dateToStrWithFormat(DateUtil.strToDate(endDate, "yyyy-MM-dd"), "yyyyMM");
            //关联币种表、汇率表
            sql.append(" join ");
            sql.append(" ( select m.marketplace_id,c.month,c.rate from dim_currency_rate c join dim_marketplace_info m on c.`from` = m.currency and puid = ? and `to` = ? and c.month >= ? and c.month <= ? ");
            args.add(puid);
            args.add(currency);
            args.add(start);
            args.add(end);
            sql.append(SqlStringUtil.dealInList("m.marketplace_id", marketplaceIds, args));
            sql.append(" ) d ");
            sql.append(" on d.marketplace_id = r.marketplace_id and d.month = r.count_month ");
        }
        sql.append(" where`puid`=? ");
        args.add(puid);
        sql.append(SqlStringUtil.dealBitMapDorisInList("shop_id", shopIds, args));
        sql.append(" and `count_day`>=? and count_day<=? ");
        args.add(startDate);
        args.add(endDate);
        sql.append(SqlStringUtil.dealBitMapDorisInList("target_id", targetIds, args));
        sql.append(" group by count_day ");

        return getJdbcTemplate().query(sql.toString(),(re, i) -> {
            TargetReportHourlyDTO dto = TargetReportHourlyDTO.builder()
                    .countDate(re.getString("count_date"))
                    .cost(Optional.ofNullable(re.getBigDecimal("cost")).orElse(BigDecimal.ZERO))
                    .orderNum(Optional.of(re.getInt("order_num")).orElse(0))
                    .totalSales(Optional.ofNullable(re.getBigDecimal("total_sales")).orElse(BigDecimal.ZERO))
                    .clicks(Optional.of(re.getInt("clicks")).orElse(0))
                    .impressions(Optional.of(re.getInt("impressions")).orElse(0))
                    //广告销量
                    .saleNum(Optional.of(re.getInt("sale_num")).orElse(0))
                    ////本广告产品订单量
                    .adSaleNum(Optional.of(re.getInt("ad_order_num")).orElse(0))
                    //本广告产品销售额
                    .adSales(Optional.ofNullable(re.getBigDecimal("ad_sales")).orElse(BigDecimal.ZERO))

//                    .viewableImpressions(Optional.ofNullable(re.getInt("view_impressions")).orElse(0))
//                    .ordersNewToBrand14d(Optional.ofNullable(re.getInt("orders_new_to_brand14d")).orElse(0))
//                    .unitsOrderedNewToBrand14d(Optional.ofNullable(re.getInt("units_ordered_new_to_brand14d")).orElse(0))
//                    .salesNewToBrand14d(Optional.ofNullable(re.getBigDecimal("sales_new_to_brand14d")).orElse(BigDecimal.ZERO))
//                    .vcpmCost(Optional.ofNullable(re.getBigDecimal("vcpm_cost")).orElse(BigDecimal.ZERO))
//                    .vcpmImpressions(re.getInt("vcpm_impressions"))
                    .build();
            return dto;
        } , args.toArray());
    }
}

