package com.meiyunji.sponsored.service.strategy.service.impl;

import com.google.api.client.util.Lists;
import com.meiyunji.amazon.mws.base.AmznEndpoint;
import com.meiyunji.sponsored.common.base.Page;
import com.meiyunji.sponsored.common.base.Result;
import com.meiyunji.sponsored.common.util.JSONUtil;
import com.meiyunji.sponsored.common.util.ResultUtil;
import com.meiyunji.sponsored.common.util.StreamUtil;
import com.meiyunji.sponsored.service.account.dao.ISellfoxRoleUserDao;
import com.meiyunji.sponsored.service.account.dao.ISellfoxShopUserDao;
import com.meiyunji.sponsored.service.account.dao.IShopAuthDao;
import com.meiyunji.sponsored.service.account.dao.IUserDao;
import com.meiyunji.sponsored.service.account.dao.*;
import com.meiyunji.sponsored.service.account.po.ShopAuth;
import com.meiyunji.sponsored.service.account.po.User;
import com.meiyunji.sponsored.service.cpc.dao.IAmazonAdProfileDao;
import com.meiyunji.sponsored.service.cpc.po.AmazonAdProfile;
import com.meiyunji.sponsored.service.cpc.util.Constants;
import com.meiyunji.sponsored.service.enums.CurrencyUnitEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogActionEnum;
import com.meiyunji.sponsored.service.log.enums.OperationLogTargetEnum;
import com.meiyunji.sponsored.service.log.po.AdManageOperationLog;
import com.meiyunji.sponsored.service.log.po.OperationContent;
import com.meiyunji.sponsored.service.log.service.IAdManageOperationLogService;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetScheduleDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetScheduleSequenceDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetTemplateDao;
import com.meiyunji.sponsored.service.strategy.dao.AdvertiseStrategyTopBudgetTemplateSequenceDao;
import com.meiyunji.sponsored.service.strategy.enums.AdStrategyEnableStatusEnum;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetSchedule;
import com.meiyunji.sponsored.service.strategy.po.AdvertiseStrategyTopBudgetTemplate;
import com.meiyunji.sponsored.service.strategy.service.IAdvertiseStrategyToBudgetTemplateService;
import com.meiyunji.sponsored.service.strategy.vo.*;
import com.meiyunji.sponsored.service.taskGrpcApi.ProfilesApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AdvertiseStrategyToBudgetTemplateServiceImpl implements IAdvertiseStrategyToBudgetTemplateService {

    @Autowired
    private AdvertiseStrategyTopBudgetTemplateDao advertiseStrategyTopBudgetTemplateDao;
    @Autowired
    private AdvertiseStrategyTopBudgetScheduleDao advertiseStrategyTopBudgetScheduleDao;
    @Autowired
    private AdvertiseStrategyTopBudgetScheduleSequenceDao advertiseStrategyTopBudgetScheduleSequenceDao;
    @Autowired
    private AdvertiseStrategyTopBudgetTemplateSequenceDao advertiseStrategyTopBudgetTemplateSequenceDao;
    @Autowired
    private ProfilesApi profilesApi;
    @Autowired
    private IAmazonAdProfileDao profileDao;
    @Autowired
    private IScVcShopAuthDao shopAuthDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private ISellfoxShopUserDao shopUserDao;
    @Autowired
    private ISellfoxRoleUserDao sellfoxRoleUserDao;
    @Autowired
    private IAdManageOperationLogService adManageOperationLogService;
    @Autowired
    private IVcShopAuthDao vcShopAuthDao;

    @Autowired
    private IShopAuthDao scShopAuthDao;


    @Override
    public Result<Page<AdvertiseStrategyTopBudgetTemplate>> getPageList(PageTopBudgetParam param) {
        Result<Page<AdvertiseStrategyTopBudgetTemplate>> result = new Result<>();
        try {
            Page<AdvertiseStrategyTopBudgetTemplate> page = new Page<>();
            page.setPageNo(param.getPageNo());
            page.setPageSize(param.getPageSize());
            Page<AdvertiseStrategyTopBudgetTemplate> advertiseStrategyTemplateTopBudgetPage =
                    advertiseStrategyTopBudgetTemplateDao.getPageList(param);
            if (CollectionUtils.isNotEmpty(advertiseStrategyTemplateTopBudgetPage.getRows())) {
                page.setTotalPage(advertiseStrategyTemplateTopBudgetPage.getTotalPage());
                page.setTotalSize(advertiseStrategyTemplateTopBudgetPage.getTotalSize());
                page.setRows(advertiseStrategyTemplateTopBudgetPage.getRows());
            }
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("查询异常:"+e);
            log.error("puid={} shopIds={} 分页查询模板数据异常:",param.getPuid(),param.getShopIds(),e);
        }
        return result;
    }

    @Override
    public Result<Page<AdvertiseStrategyTopBudgetTemplate>> getAdmPageList(PageTopBudgetParam param) {
        Result<Page<AdvertiseStrategyTopBudgetTemplate>> result = new Result<>();
        try {
            Page<AdvertiseStrategyTopBudgetTemplate> page = new Page<>();
            page.setPageNo(param.getPageNo());
            page.setPageSize(param.getPageSize());
            Page<AdvertiseStrategyTopBudgetTemplate> advertiseStrategyTemplateTopBudgetPage =
                    advertiseStrategyTopBudgetTemplateDao.getAdmPageList(param);
            if (CollectionUtils.isNotEmpty(advertiseStrategyTemplateTopBudgetPage.getRows())) {
                page.setTotalPage(advertiseStrategyTemplateTopBudgetPage.getTotalPage());
                page.setTotalSize(advertiseStrategyTemplateTopBudgetPage.getTotalSize());
                page.setRows(advertiseStrategyTemplateTopBudgetPage.getRows());
            }
            result.setData(page);
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("查询异常:"+e);
            log.error("traceId:{} puid:{} shopIds:{} 分页查询模板数据异常:", param.getTraceId(), param.getPuid(), param.getShopIds(), e);
        }
        return result;
    }

    @Override
    public Result<List<ErrorToBudgetMsg>> insertTemplate(AddTopBudgetVo addTopBudgetVo, String traceId) {
        List<ErrorToBudgetMsg> errorToBudgetMsgs = Lists.newArrayList();
        double topBudget = 9.99999999E8;
        //查询所有门店
        List<ShopAuth> shopAuths = shopAuthDao.listScAndVcAllByPuid(addTopBudgetVo.getPuid());
        Map<Integer, ShopAuth> shopMap = null;
        if (CollectionUtils.isNotEmpty(shopAuths)) {
            shopMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
        }

        List<AdManageOperationLog> opLogs = Lists.newArrayList();

        for (Integer shopId : addTopBudgetVo.getShopIdList()) {

            AdManageOperationLog opLog = new AdManageOperationLog();
            opLog.setAction(OperationLogActionEnum.ADD.getOperationValue());

            //生成的主键ID和taskId
            Long taskId = advertiseStrategyTopBudgetTemplateSequenceDao.genId();
            try {
                //原始值校验
                if (addTopBudgetVo.getOriginToBudgetValueVo().getUseTopBudget()) {
                    if (addTopBudgetVo.getOriginToBudgetValueVo().getToBudgetValue().compareTo(BigDecimal.valueOf(1)) < 0) {
                        return ResultUtil.returnErr("每日预算上限最小不能为1");
                    }
                    if (addTopBudgetVo.getOriginToBudgetValueVo().getToBudgetValue().compareTo(BigDecimal.valueOf(21000000)) > 0) {
                        return ResultUtil.returnErr("每日预算上限最大不能为21000000");
                    }
                }

                opLog.setPuid(addTopBudgetVo.getPuid());
                opLog.setUid(addTopBudgetVo.getCreateUid());
                opLog.setIp(addTopBudgetVo.getLoginIp());
                opLog.setTemplateId(taskId);
                opLog.setTargetId(String.valueOf(taskId));
                opLog.setShopId(shopId);
                if (StringUtils.isNotBlank(addTopBudgetVo.getMarketplaceId())) {
                    opLog.setMarketplaceId(addTopBudgetVo.getMarketplaceId());
                }
                opLog.setTarget(OperationLogTargetEnum.spTopBudget.getTargetValue());
                opLog.setOperationObject(OperationLogTargetEnum.spTopBudget.getTargetValue());
                List<OperationContent> opContents = new ArrayList<>();
                Map<Integer, List<ToBudgetRuleVo>> map = StreamUtil.groupingBy(addTopBudgetVo.getToBudgetRuleVoList(), ToBudgetRuleVo::getSiteDate);
                for (Map.Entry<Integer, List<ToBudgetRuleVo>> entry : map.entrySet()) {
                    List<ToBudgetRuleVo> ruleVoList = entry.getValue();
                    OperationContent opContent = new OperationContent();
                    opContent.setTitle("新建模板");
                    if (entry.getKey() == 0) {
                        opContent.setName("每日");
                    } else {
                        opContent.setName(Constants.getDateMap().get(entry.getKey()));
                    }
                    List<String> budgetDescList = ruleVoList.stream().map(ToBudgetRuleVo::getBudgetDesc).collect(Collectors.toList());
                    opContent.setNewValue(StringUtils.join(budgetDescList, "；"));
                    opContents.add(opContent);
                }
                opLog.setOperationContent(JSONUtil.objectToJson(opContents));
                opLog.setMessage(opLog.handleMessage(opContents));

                AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate = new AdvertiseStrategyTopBudgetTemplate();
                advertiseStrategyTopBudgetTemplate.setId(taskId);
                advertiseStrategyTopBudgetTemplate.setPuid(addTopBudgetVo.getPuid());
                advertiseStrategyTopBudgetTemplate.setShopId(shopId);
                advertiseStrategyTopBudgetTemplate.setMarketplaceId(addTopBudgetVo.getMarketplaceId());
                advertiseStrategyTopBudgetTemplate.setTaskId(taskId);
                advertiseStrategyTopBudgetTemplate.setType(addTopBudgetVo.getType());
                AmazonAdProfile amazonAdProfile = profileDao.getProfile(addTopBudgetVo.getPuid(), shopId);
                if (amazonAdProfile != null) {
                    advertiseStrategyTopBudgetTemplate.setProfileId(amazonAdProfile.getProfileId());
                } else {
                    ErrorToBudgetMsg errorToBudgetMsg = new ErrorToBudgetMsg();
                    errorToBudgetMsg.setErrorMsg("无站点配置信息");
                    errorToBudgetMsg.setShopId(advertiseStrategyTopBudgetTemplate.getShopId());
                    if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(shopId)) {
                        errorToBudgetMsg.setShopName(shopMap.get(shopId).getName());
                    }
                    errorToBudgetMsgs.add(errorToBudgetMsg);
                    opLog.setResult(1);
                    opLog.setResultInfo("无站点配置信息");
                    opLogs.add(opLog);
                    continue;
                }
                advertiseStrategyTopBudgetTemplate.setCreateUid(addTopBudgetVo.getCreateUid());
                advertiseStrategyTopBudgetTemplate.setUpdateUid(addTopBudgetVo.getUpdateUid());
                advertiseStrategyTopBudgetTemplate.setCreateName(addTopBudgetVo.getCreateName());
                advertiseStrategyTopBudgetTemplate.setUpdateName(addTopBudgetVo.getUpdateName());
                List<ToBudgetRuleVo> ruleVoList = addTopBudgetVo.getToBudgetRuleVoList();
                String originJson = JSONUtil.objectToJson(addTopBudgetVo.getOriginToBudgetValueVo());
                List<AdvertiseStrategyTopBudgetSchedule> list = Lists.newArrayListWithCapacity
                        (ruleVoList.size());
                String rule = JSONUtil.objectToJson(addTopBudgetVo.getToBudgetRuleVoList());
                advertiseStrategyTopBudgetTemplate.setOriginValue(originJson);
                advertiseStrategyTopBudgetTemplate.setRule(rule);
                OriginToBudgetValueVo reductionValueVo = new OriginToBudgetValueVo();
                if (amazonAdProfile.getDailyBudget() != null && amazonAdProfile.getDailyBudget() <topBudget) {
                    reductionValueVo.setUseTopBudget(true);
                    reductionValueVo.setToBudgetValue(BigDecimal.valueOf(amazonAdProfile.getDailyBudget()));
                } else {
                    reductionValueVo.setUseTopBudget(false);
                }
                String reductionValueJson = JSONUtil.objectToJson(reductionValueVo);
                advertiseStrategyTopBudgetTemplate.setReductionValue(reductionValueJson);
                for (ToBudgetRuleVo toBudgetRuleVo : ruleVoList) {
                    AdvertiseStrategyTopBudgetSchedule advertiseStrategyTopBudgetSchedule = new AdvertiseStrategyTopBudgetSchedule();
                    Long scheduleId = advertiseStrategyTopBudgetScheduleSequenceDao.genId();
                    advertiseStrategyTopBudgetSchedule.setId(scheduleId);
                    advertiseStrategyTopBudgetSchedule.setTaskId(taskId);
                    advertiseStrategyTopBudgetSchedule.setType(addTopBudgetVo.getType());
                    advertiseStrategyTopBudgetSchedule.setPuid(addTopBudgetVo.getPuid());
                    advertiseStrategyTopBudgetSchedule.setShopId(shopId);
                    advertiseStrategyTopBudgetSchedule.setProfileId(advertiseStrategyTopBudgetTemplate.getProfileId());
                    advertiseStrategyTopBudgetSchedule.setMarketplaceId(addTopBudgetVo.getMarketplaceId());
                    advertiseStrategyTopBudgetSchedule.setDay(toBudgetRuleVo.getSiteDate());
                    advertiseStrategyTopBudgetSchedule.setStart(toBudgetRuleVo.getStartTimeSite() * 60);
                    advertiseStrategyTopBudgetSchedule.setEnd(toBudgetRuleVo.getEndTimeSite() * 60);
                    OriginToBudgetValueVo newToBudgetValue = new OriginToBudgetValueVo();
                    newToBudgetValue.setUseTopBudget(toBudgetRuleVo.getUseTopBudget());
                    if (toBudgetRuleVo.getToBudgetValue() != null && toBudgetRuleVo.getUseTopBudget()) {
                        if (toBudgetRuleVo.getToBudgetValue().compareTo(BigDecimal.valueOf(1)) < 0) {
                            return ResultUtil.returnErr("每日预算上限最小不能为1");
                        }
                        if (toBudgetRuleVo.getToBudgetValue().compareTo(BigDecimal.valueOf(21000000)) > 0) {
                            return ResultUtil.returnErr("每日预算上限最大不能为21000000");
                        }
                        newToBudgetValue.setToBudgetValue(toBudgetRuleVo.getToBudgetValue());
                    }
                    String newJson = JSONUtil.objectToJson(newToBudgetValue);
                    advertiseStrategyTopBudgetSchedule.setNewValue(newJson);
                    advertiseStrategyTopBudgetSchedule.setOriginValue(originJson);
                    advertiseStrategyTopBudgetSchedule.setReductionValue(reductionValueJson);
                    list.add(advertiseStrategyTopBudgetSchedule);
                }
                //判断当前是否需要立即应用
                if (addTopBudgetVo.getIsUse() == 0) {
                    advertiseStrategyTopBudgetTemplate.setStatus("ENABLED");
                    //查询出当前店铺使用的模板
                    AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate1 =
                            advertiseStrategyTopBudgetTemplateDao.selectByStatus(addTopBudgetVo.getPuid(), shopId);
                    //将当前店铺使用的模板置为无效并将aadas服务的数据删除
                    if (advertiseStrategyTopBudgetTemplate1 != null) {
                        advertiseStrategyTopBudgetTemplateDao.updateStatus(addTopBudgetVo.getPuid(),
                                advertiseStrategyTopBudgetTemplate1.getId(), "DISABLED");
                    }
                    //插入新模板并置为有效
                    advertiseStrategyTopBudgetTemplateDao.insertTemplate(addTopBudgetVo.getPuid(), advertiseStrategyTopBudgetTemplate);
                    //批量插入生成任务记录
                    advertiseStrategyTopBudgetScheduleDao.batchInsert(addTopBudgetVo.getPuid(), list);
                    opLog.setResult(0);
                    //推送到aadas服务中
                    try {
                        profilesApi.setTopBudgetTask(taskId, list, false);
                    } catch (Exception e) {
                        //推送任务异常,事务回滚
                        advertiseStrategyTopBudgetTemplateDao.deleteTemplateId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getId());
                        advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule(advertiseStrategyTopBudgetTemplate.getPuid(),
                                advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
                        if (advertiseStrategyTopBudgetTemplate1 != null) {
                            advertiseStrategyTopBudgetTemplateDao.updateStatus(addTopBudgetVo.getPuid(), advertiseStrategyTopBudgetTemplate1.getId(), "ENABLED");
                        }
                        //错误日志打印
                        log.error("traceId:{} puid:{} shopId:{} taskId:{} 新增顶级预算模板失败亚马逊接口报错:",
                                traceId, addTopBudgetVo.getPuid(), shopId, taskId, e);
                        ErrorToBudgetMsg errorToBudgetMsg = new ErrorToBudgetMsg();
                        errorToBudgetMsg.setErrorMsg("店铺账号异常");
                        errorToBudgetMsg.setShopId(shopId);
                        if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(shopId)) {
                            errorToBudgetMsg.setShopName(shopMap.get(shopId).getName());
                        }
                        errorToBudgetMsgs.add(errorToBudgetMsg);
                        opLog.setResult(1);
                        opLog.setResultInfo("店铺账号异常");
                        continue;
                    }
                } else if (addTopBudgetVo.getIsUse() == 1) {
                    advertiseStrategyTopBudgetTemplate.setStatus("DISABLED");
                    advertiseStrategyTopBudgetTemplateDao.insertTemplate(addTopBudgetVo.getPuid(), advertiseStrategyTopBudgetTemplate);
                    advertiseStrategyTopBudgetScheduleDao.batchInsert(addTopBudgetVo.getPuid(), list);
                    opLog.setResult(0);
                }
                opLogs.add(opLog);
            } catch (Exception e) {
                ErrorToBudgetMsg errorToBudgetMsg = new ErrorToBudgetMsg();
                errorToBudgetMsg.setErrorMsg("店铺账号异常");
                errorToBudgetMsg.setShopId(shopId);
                if (MapUtils.isNotEmpty(shopMap) && shopMap.containsKey(shopId)) {
                    errorToBudgetMsg.setShopName(shopMap.get(shopId).getName());
                }
                errorToBudgetMsgs.add(errorToBudgetMsg);
                log.error("traceId:{} puid:{} shopId:{} taskId:{} 新增顶级预算模板失败系统异常:",
                        traceId, addTopBudgetVo.getPuid(), shopId, taskId, e);
                opLog.setResult(1);
                opLog.setResultInfo("店铺账号异常");
                opLogs.add(opLog);
                continue;
            }
        }
        adManageOperationLogService.printAdOtherOperationLog(opLogs);
        return ResultUtil.returnSucc(errorToBudgetMsgs);
    }

    @Override
    public Result<Integer> updateTemplate(UpdateTopBudgetVo updateTopBudgetVo, String traceId) {

        Result<Integer> result = new Result<>();
        AdManageOperationLog opLog = new AdManageOperationLog();
        try {
            //原始值校验
            if (updateTopBudgetVo.getOriginToBudgetValueVo().getUseTopBudget()) {
                if (updateTopBudgetVo.getOriginToBudgetValueVo().getToBudgetValue().compareTo(BigDecimal.valueOf(1)) < 0) {
                    return ResultUtil.returnErr("每日预算上限最小不能为1");
                }
                if (updateTopBudgetVo.getOriginToBudgetValueVo().getToBudgetValue().compareTo(BigDecimal.valueOf(21000000)) > 0) {
                    return ResultUtil.returnErr("每日预算上限最大不能为21000000");
                }
            }
            AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate =
                    advertiseStrategyTopBudgetTemplateDao.selectByTaskId(updateTopBudgetVo.getPuid(), updateTopBudgetVo.getShopId(), updateTopBudgetVo.getTaskId());
            if (advertiseStrategyTopBudgetTemplate == null) {
                return ResultUtil.returnErr("模板已被删除，请刷新页面重试");
            }

            opLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
            opLog.setPuid(updateTopBudgetVo.getPuid());
            opLog.setUid(updateTopBudgetVo.getUpdateUid());
            opLog.setIp(updateTopBudgetVo.getLoginIp());
            opLog.setTemplateId(updateTopBudgetVo.getTaskId());
            opLog.setTargetId(String.valueOf(updateTopBudgetVo.getTaskId()));
            opLog.setShopId(updateTopBudgetVo.getShopId());
            if (StringUtils.isNotBlank(updateTopBudgetVo.getMarketplaceId())) {
                opLog.setMarketplaceId(updateTopBudgetVo.getMarketplaceId());
            }
            opLog.setTarget(OperationLogTargetEnum.spTopBudget.getTargetValue());
            opLog.setOperationObject(OperationLogTargetEnum.spTopBudget.getTargetValue());
            List<OperationContent> opContents = new ArrayList<>();

            String currencyUnit = "";
            AmznEndpoint amznEndpoint = AmznEndpoint.getByMarketplaceId(updateTopBudgetVo.getMarketplaceId());
            if (Objects.nonNull(amznEndpoint)) {
                String currency = amznEndpoint.getCurrencyCode().value();
                CurrencyUnitEnum unitEnum = CurrencyUnitEnum.getByCurrency(currency);
                if (Objects.nonNull(unitEnum)) {
                    currencyUnit = unitEnum.getUnit();
                }
            }
            OriginToBudgetValueVo originToBudgetValueVo = updateTopBudgetVo.getOriginToBudgetValueVo();
            opContents.add(OperationContent.builder()
                    .title("编辑模板")
                    .name("未设置预算时间段")
                    .newValue(Boolean.TRUE.equals(originToBudgetValueVo.getUseTopBudget()) ? "使用SP每日预算上限" + originToBudgetValueVo.getToBudgetValue().stripTrailingZeros().toPlainString() + currencyUnit
                            : "使用广告系列顶级预算")
                    .build());

            Map<Integer, List<ToBudgetRuleVo>> map = StreamUtil.groupingBy(updateTopBudgetVo.getToBudgetRuleVoList(), ToBudgetRuleVo::getSiteDate);
            for (Map.Entry<Integer, List<ToBudgetRuleVo>> entry : map.entrySet()) {
                List<ToBudgetRuleVo> ruleVoList = entry.getValue();
                OperationContent opContent = new OperationContent();
                opContent.setTitle("编辑模板");
                if (entry.getKey() == 0) {
                    opContent.setName("每日");
                } else {
                    opContent.setName(Constants.getDateMap().get(entry.getKey()));
                }
                List<String> budgetDescList = ruleVoList.stream().map(ToBudgetRuleVo::getBudgetDesc).collect(Collectors.toList());
                opContent.setNewValue(StringUtils.join(budgetDescList, "；"));
                opContents.add(opContent);
            }
            opLog.setOperationContent(JSONUtil.objectToJson(opContents));
            opLog.setMessage(opLog.handleMessage(opContents));

            advertiseStrategyTopBudgetTemplate.setShopId(updateTopBudgetVo.getShopId());
            advertiseStrategyTopBudgetTemplate.setMarketplaceId(updateTopBudgetVo.getMarketplaceId());
            advertiseStrategyTopBudgetTemplate.setUpdateUid(updateTopBudgetVo.getUpdateUid());
            advertiseStrategyTopBudgetTemplate.setUpdateName(updateTopBudgetVo.getUpdateName());
            List<ToBudgetRuleVo> ruleVoList = updateTopBudgetVo.getToBudgetRuleVoList();
            String originJson = JSONUtil.objectToJson(updateTopBudgetVo.getOriginToBudgetValueVo());
            List<AdvertiseStrategyTopBudgetSchedule> list = Lists.newArrayListWithCapacity
                    (ruleVoList.size());
            String rule = JSONUtil.objectToJson(updateTopBudgetVo.getToBudgetRuleVoList());
            advertiseStrategyTopBudgetTemplate.setOriginValue(originJson);
            advertiseStrategyTopBudgetTemplate.setRule(rule);
            advertiseStrategyTopBudgetTemplate.setType(updateTopBudgetVo.getType());
            for (ToBudgetRuleVo toBudgetRuleVo : ruleVoList) {
                AdvertiseStrategyTopBudgetSchedule advertiseStrategyTopBudgetSchedule = new AdvertiseStrategyTopBudgetSchedule();
                Long scheduleId = advertiseStrategyTopBudgetScheduleSequenceDao.genId();
                advertiseStrategyTopBudgetSchedule.setId(scheduleId);
                advertiseStrategyTopBudgetSchedule.setTaskId(updateTopBudgetVo.getTaskId());
                advertiseStrategyTopBudgetSchedule.setType(updateTopBudgetVo.getType());
                advertiseStrategyTopBudgetSchedule.setPuid(updateTopBudgetVo.getPuid());
                advertiseStrategyTopBudgetSchedule.setShopId(updateTopBudgetVo.getShopId());
                advertiseStrategyTopBudgetSchedule.setProfileId(advertiseStrategyTopBudgetTemplate.getProfileId());
                advertiseStrategyTopBudgetSchedule.setMarketplaceId(updateTopBudgetVo.getMarketplaceId());
                advertiseStrategyTopBudgetSchedule.setDay(toBudgetRuleVo.getSiteDate());
                advertiseStrategyTopBudgetSchedule.setStart(toBudgetRuleVo.getStartTimeSite() * 60);
                advertiseStrategyTopBudgetSchedule.setEnd(toBudgetRuleVo.getEndTimeSite() * 60);
                OriginToBudgetValueVo newToBudgetValue = new OriginToBudgetValueVo();
                newToBudgetValue.setUseTopBudget(toBudgetRuleVo.getUseTopBudget());
                if (toBudgetRuleVo.getToBudgetValue() != null && toBudgetRuleVo.getUseTopBudget()) {
                    if (toBudgetRuleVo.getToBudgetValue().compareTo(BigDecimal.valueOf(1)) < 0) {
                        return ResultUtil.returnErr("每日预算上限最小不能为1");
                    }
                    if (toBudgetRuleVo.getToBudgetValue().compareTo(BigDecimal.valueOf(21000000)) > 0) {
                        return ResultUtil.returnErr("每日预算上限最大不能为21000000");
                    }
                    newToBudgetValue.setToBudgetValue(toBudgetRuleVo.getToBudgetValue());
                }
                String newJson = JSONUtil.objectToJson(newToBudgetValue);
                advertiseStrategyTopBudgetSchedule.setNewValue(newJson);
                advertiseStrategyTopBudgetSchedule.setOriginValue(originJson);
                advertiseStrategyTopBudgetSchedule.setReductionValue(advertiseStrategyTopBudgetTemplate.getReductionValue());
                list.add(advertiseStrategyTopBudgetSchedule);
            }
            if (updateTopBudgetVo.getStatus().equals("DISABLED")) {
                advertiseStrategyTopBudgetTemplateDao.updateTemplate(updateTopBudgetVo.getPuid(), advertiseStrategyTopBudgetTemplate);
                advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule(updateTopBudgetVo.getPuid(),
                        updateTopBudgetVo.getShopId(), updateTopBudgetVo.getTaskId());
                advertiseStrategyTopBudgetScheduleDao.batchInsert(updateTopBudgetVo.getPuid(), list);
            } else {
                advertiseStrategyTopBudgetTemplateDao.updateTemplate(updateTopBudgetVo.getPuid(), advertiseStrategyTopBudgetTemplate);
                advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule(updateTopBudgetVo.getPuid(),
                        updateTopBudgetVo.getShopId(), updateTopBudgetVo.getTaskId());
                advertiseStrategyTopBudgetScheduleDao.batchInsert(updateTopBudgetVo.getPuid(), list);
                //推送到aadas服务中
                try {
                    profilesApi.setTopBudgetTask(updateTopBudgetVo.getTaskId(),list,false);
                } catch (Exception e) {
                    //错误日志打印
                    log.error("traceId:{} puid:{} shopId:{} taskId:{} 更新顶级预算模板失败亚马逊接口报错:",
                            traceId, updateTopBudgetVo.getPuid(), updateTopBudgetVo.getShopId(), updateTopBudgetVo.getTaskId(), e);
                    opLog.setResult(1);
                    opLog.setResultInfo("更新失败亚马逊接口报错");
                    adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
                    return ResultUtil.returnErr("更新失败亚马逊接口报错:"+e);
                }
            }
            result.setCode(Result.SUCCESS);
            result.setMsg("修改模板成功");
            result.setData(updateTopBudgetVo.getTaskId().intValue());
            opLog.setResult(0);
            adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("顶级预算模板修改异常:"+e);
            log.error("traceId:{} puid:{} shopId:{} taskId:{} 顶级预算模板修改异常:",
                    traceId, updateTopBudgetVo.getPuid(), updateTopBudgetVo.getShopId(), updateTopBudgetVo.getTaskId(), e);
            opLog.setResult(1);
            opLog.setResultInfo("顶级预算模板修改异常");
            adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
        }
        return result;
    }

    @Override
    public Result<Integer> updateStatus(UpdateTopBudgetStatusVo updateTopBudgetStatusVo, String traceId) {
        Result<Integer> result = new Result<>();
        AdManageOperationLog opLog = new AdManageOperationLog();
        opLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
        opLog.setPuid(updateTopBudgetStatusVo.getPuid());
        opLog.setUid(updateTopBudgetStatusVo.getUid());
        opLog.setIp(updateTopBudgetStatusVo.getLoginIp());
        opLog.setTemplateId(updateTopBudgetStatusVo.getTaskId());
        opLog.setTargetId(String.valueOf(updateTopBudgetStatusVo.getTaskId()));
        opLog.setShopId(updateTopBudgetStatusVo.getShopId());
        if (StringUtils.isNotBlank(updateTopBudgetStatusVo.getMarketplaceId())) {
            opLog.setMarketplaceId(updateTopBudgetStatusVo.getMarketplaceId());
        }
        opLog.setTarget(OperationLogTargetEnum.spTopBudget.getTargetValue());
        opLog.setOperationObject(OperationLogTargetEnum.spTopBudget.getTargetValue());

        List<OperationContent> opContents = new ArrayList<>();
        opContents.add(OperationContent.builder()
                .title("应用状态")
                .newValue((updateTopBudgetStatusVo.getStatus().equals("ENABLED") ? "开启" : "暂停") + "分时SP预算上限")
                .build());
        opLog.setOperationContent(JSONUtil.objectToJson(opContents));
        opLog.setMessage(opLog.handleMessage(opContents));
        try {
            if (updateTopBudgetStatusVo.getStatus().equals("ENABLED")) {
                //查询是否有开启的模板
                AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate = advertiseStrategyTopBudgetTemplateDao.
                        selectByStatus(updateTopBudgetStatusVo.getPuid(), updateTopBudgetStatusVo.getShopId());
                //如果有开启的模板则必须删除数据
                if (advertiseStrategyTopBudgetTemplate != null) {
                    advertiseStrategyTopBudgetTemplateDao.updateStatus(advertiseStrategyTopBudgetTemplate.getPuid(),
                            advertiseStrategyTopBudgetTemplate.getId(),"DISABLED");
                }
                //新数据推送
                List<AdvertiseStrategyTopBudgetSchedule> list =
                        advertiseStrategyTopBudgetScheduleDao.listByTaskId(updateTopBudgetStatusVo.getPuid(), updateTopBudgetStatusVo.getShopId(), updateTopBudgetStatusVo.getTaskId());
                advertiseStrategyTopBudgetTemplateDao.updateStatus(updateTopBudgetStatusVo.getPuid(),
                        updateTopBudgetStatusVo.getId(), updateTopBudgetStatusVo.getStatus());
                //推送到aadas服务中
                try {
                    profilesApi.setTopBudgetTask(updateTopBudgetStatusVo.getTaskId(),list,false);
                    log.info("setTopBudgetTask list:{}"+list);
                } catch (Exception e) {
                    //错误日志打印
                    log.error("traceId:{} puid:{} shopId:{} taskId:{} 更新顶级预算模板失败亚马逊接口报错:",
                            traceId, updateTopBudgetStatusVo.getPuid(), updateTopBudgetStatusVo.getShopId(), updateTopBudgetStatusVo.getTaskId(), e);
                    opLog.setResult(1);
                    opLog.setResultInfo("更新顶级预算模板失败亚马逊接口报错");
                    adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
                    return ResultUtil.returnErr("更新失败亚马逊接口报错:"+e);
                }
            } else {
                //数据到任务调度服务删除数据
                profilesApi.removeTopBudgetTask(updateTopBudgetStatusVo.getPuid(), updateTopBudgetStatusVo.getShopId(), updateTopBudgetStatusVo.getTaskId(), true);
                advertiseStrategyTopBudgetTemplateDao.updateStatus(updateTopBudgetStatusVo.getPuid(),
                        updateTopBudgetStatusVo.getId(), updateTopBudgetStatusVo.getStatus());
            }
            opLog.setResult(0);
            adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("顶级预算模板启停异常:"+e);
            log.error("traceId:{} puid:{} shopId:{} taskId:{} 顶级预算模板启停异常:",
                    traceId, updateTopBudgetStatusVo.getPuid(), updateTopBudgetStatusVo.getShopId(), updateTopBudgetStatusVo.getTaskId(), e);
            opLog.setResult(1);
            opLog.setResultInfo(result.getMsg());
            adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
        }
        return result;
    }

    @Override
    public Result<Integer> deleteTemplate(DeleteTopBudgetVo deleteTopBudgetVo, String traceId) {
        Result<Integer> result = new Result<>();
        AdManageOperationLog opLog = new AdManageOperationLog();
        opLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
        opLog.setPuid(deleteTopBudgetVo.getPuid());
        opLog.setUid(deleteTopBudgetVo.getUid());
        opLog.setIp(deleteTopBudgetVo.getLoginIp());
        opLog.setTemplateId(deleteTopBudgetVo.getId());
        opLog.setTargetId(String.valueOf(deleteTopBudgetVo.getId()));
        opLog.setTarget(OperationLogTargetEnum.spTopBudget.getTargetValue());
        opLog.setOperationObject(OperationLogTargetEnum.spTopBudget.getTargetValue());
        try {
            AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate =
                advertiseStrategyTopBudgetTemplateDao.selectByPrimaryKey(deleteTopBudgetVo.getPuid(), deleteTopBudgetVo.getId());
            if (advertiseStrategyTopBudgetTemplate == null) {
                result.setCode(Result.ERROR);
                result.setMsg("查询不到当前模板数据");
                return result;
            }
            if (CollectionUtils.isEmpty(deleteTopBudgetVo.getAuthedShopIdList()) || !deleteTopBudgetVo.getAuthedShopIdList().contains(advertiseStrategyTopBudgetTemplate.getShopId())) {
                result.setCode(Result.ERROR);
                result.setMsg("未授权");
                return result;
            }
            if (advertiseStrategyTopBudgetTemplate.getStatus().equals("ENABLED")) {
                //数据到任务调度服务删除数据
                profilesApi.removeTopBudgetTask(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId(), true);
                advertiseStrategyTopBudgetTemplateDao.deleteTemplateId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getId());
                advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule
                    (advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
            } else {
                advertiseStrategyTopBudgetTemplateDao.deleteTemplateId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getId());
                advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule
                    (advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
            }

            ShopAuth shopAuth = shopAuthDao.getScAndVcById(advertiseStrategyTopBudgetTemplate.getShopId());
            if (Objects.nonNull(shopAuth)) {
                opLog.setShopId(shopAuth.getId());
                opLog.setMarketplaceId(shopAuth.getMarketplaceId());

                List<OperationContent> opContents = new ArrayList<>();
                opContents.add(OperationContent.builder()
                        .title("操作")
                        .newValue("删除模板" + shopAuth.getName())
                        .build());
                opLog.setOperationContent(JSONUtil.objectToJson(opContents));
                opLog.setMessage(opLog.handleMessage(opContents));
                opLog.setResult(0);
                adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
            }
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("顶级预算模板删除异常:" + e);
            log.error("traceId:{} puid:{} shopId:{} taskId:{} 顶级预算模板删除异常:",
                traceId, deleteTopBudgetVo.getPuid(), deleteTopBudgetVo.getShopId(), deleteTopBudgetVo.getTaskId(), e);
        }
        return result;
    }

    @Override
    public Result<Integer> transferTemplate(TransferTopBudgetVo transferTopBudgetVo, String traceId) {
        Result<Integer> result = new Result<>();
        AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate = advertiseStrategyTopBudgetTemplateDao.
                selectByPrimaryKey(transferTopBudgetVo.getPuid(), transferTopBudgetVo.getTemplateId());
        if (transferTopBudgetVo.getId() != null) {
            //把当前模板置为无效
            advertiseStrategyTopBudgetTemplateDao.updateStatus(transferTopBudgetVo.getPuid(),
                    transferTopBudgetVo.getId(), "DISABLED");
        }
        try {
            //转移的模板置为有效并向aadas推送数据
            List<AdvertiseStrategyTopBudgetSchedule> list = advertiseStrategyTopBudgetScheduleDao.listByTaskId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
            profilesApi.setTopBudgetTask(advertiseStrategyTopBudgetTemplate.getTaskId(), list, false);
            advertiseStrategyTopBudgetTemplateDao.updateStatus(transferTopBudgetVo.getPuid(),
                    transferTopBudgetVo.getTemplateId(), "ENABLED");
        } catch (Exception e) {
            result.setCode(Result.ERROR);
            result.setMsg("转移异常:" + e);
            //还原当前数据
            advertiseStrategyTopBudgetTemplateDao.updateStatus(transferTopBudgetVo.getPuid(),
                    transferTopBudgetVo.getTemplateId(), "DISABLED");
            if (transferTopBudgetVo.getId() != null) {
                advertiseStrategyTopBudgetTemplateDao.updateStatus(transferTopBudgetVo.getPuid(),
                        transferTopBudgetVo.getId(), "ENABLED");
            }
            log.error("traceId:{} templateId:{}  puid:{} 转移的模板置为有效并向aadas推送数据异常:", traceId, transferTopBudgetVo.getTemplateId(), transferTopBudgetVo.getPuid(), e);
        }

        return result;
    }

    @Override
    public Result<List<MarketplaceVo>> getSiteIdList(Integer puid, Integer userId, String traceId) {
        try {
            //查询所有门店
            List<ShopAuth> shopAuths = shopAuthDao.listScAndVcAllByPuid(puid);
            Map<Integer, ShopAuth> shopMap = null;
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                shopMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }
            List<Integer> allShopIds = scShopAuthDao.getAllShopId(puid);
            List<Integer> shopIdList = Lists.newArrayList();
            User user = userDao.getById(userId);
            List<Integer> adminUserIdList = sellfoxRoleUserDao.getAdminUserIdList(puid);
            if (userId.equals(user.getPuid()) || (CollectionUtils.isNotEmpty(adminUserIdList) && adminUserIdList.contains(userId))) {
                // 主账号直接取店铺id
                shopIdList = allShopIds;
            } else {
                // 非主账号查关联, 这里需要排除已删除的店铺
                List<Integer> shopIds = shopUserDao.getShopIdListByUser(user.getPuid(), user.getId());
                shopIds = shopIds.stream().filter(allShopIds::contains).distinct().collect(Collectors.toList());
                shopIdList = shopIds;
            }
            //vc 店铺没有目前没有做权限，先就这样做吧；
            List<Integer> allVcShopId = vcShopAuthDao.getAllShopId(puid);
            shopIdList.addAll(allVcShopId);
            shopIdList = shopIdList.stream().distinct().collect(Collectors.toList());
            List<Integer> adShopIdList = advertiseStrategyTopBudgetTemplateDao.selectByShopIdList(puid,null);
            List<MarketplaceVo> marketplaceVoList = Lists.newArrayList();
            Map<Integer, ShopAuth> finalShopMap = shopMap;
            List<MarketplaceVo> finalMarketplaceVoList = marketplaceVoList;
            shopIdList.forEach(e->{
                MarketplaceVo marketplaceVo = new MarketplaceVo();
                if (CollectionUtils.isNotEmpty(adShopIdList)) {
                    if (!adShopIdList.contains(e)) {
                        if (MapUtils.isNotEmpty(finalShopMap) && finalShopMap.containsKey(e)) {
                            if (finalShopMap.get(e).getAdStatus().equals("auth")) {
                                marketplaceVo.setMarketplaceId(finalShopMap.get(e).getMarketplaceId());
                                marketplaceVo.setMarketplaceName(finalShopMap.get(e).getMarketplaceName());
                            }
                        }
                    }
                } else {
                    if (MapUtils.isNotEmpty(finalShopMap) && finalShopMap.containsKey(e)) {
                        if (finalShopMap.get(e).getAdStatus().equals("auth")) {
                            marketplaceVo.setMarketplaceId(finalShopMap.get(e).getMarketplaceId());
                            marketplaceVo.setMarketplaceName(finalShopMap.get(e).getMarketplaceName());
                        }
                    }
                }
                finalMarketplaceVoList.add(marketplaceVo);
            });

            if (CollectionUtils.isNotEmpty(finalMarketplaceVoList)) {
                marketplaceVoList = finalMarketplaceVoList.stream().distinct().
                        collect(Collectors.toList());
            }
            return ResultUtil.returnSucc(marketplaceVoList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} userId:{} 顶级预算查看站点异常:", traceId, puid, userId, e);
            return ResultUtil.returnErr("顶级预算查看站点异常:"+e);
        }
    }

    @Override
    public Result<List<ShopAuth>> getShopIdList (Integer puid, String marketplaceId,Integer userId, String traceId) {
        try {
            //查询所有门店
            List<ShopAuth> shopAuths = shopAuthDao.listScAndVcAllByPuid(puid);
            Map<Integer, ShopAuth> shopMap = null;
            if (CollectionUtils.isNotEmpty(shopAuths)) {
                shopMap = shopAuths.stream().filter(Objects::nonNull).collect(Collectors.toMap(ShopAuth::getId, Function.identity()));
            }
            List<Integer> allShopIds = scShopAuthDao.getShopByMid(puid,marketplaceId);
            List<Integer> shopIdList = Lists.newArrayList();
            User user = userDao.getById(userId);
            List<Integer> adminUserIdList = sellfoxRoleUserDao.getAdminUserIdList(puid);
            if (userId.equals(user.getPuid()) || (CollectionUtils.isNotEmpty(adminUserIdList) && adminUserIdList.contains(userId))) {
                // 主账号直接取店铺id
                shopIdList = allShopIds;
            } else {
                // 非主账号查关联, 这里需要排除已删除的店铺
                List<Integer> shopIds = shopUserDao.getShopIdListByUser(user.getPuid(), user.getId());
                shopIds = shopIds.stream().filter(allShopIds::contains).distinct().collect(Collectors.toList());
                shopIdList = shopIds;
            }
            //vc 店铺没有目前没有做权限，先就这样做吧；
            List<Integer> allVcShopId = vcShopAuthDao.getShopByMid(puid, marketplaceId);
            shopIdList.addAll(allVcShopId);
            shopIdList = shopIdList.stream().distinct().collect(Collectors.toList());
            List<Integer> adShopIdList = advertiseStrategyTopBudgetTemplateDao.selectByShopIdList(puid,marketplaceId);
            List<ShopAuth> shopAuthList = Lists.newArrayList();
            Map<Integer, ShopAuth> finalShopMap = shopMap;
            shopIdList.forEach(e->{
                if (CollectionUtils.isNotEmpty(adShopIdList)) {
                    if (!adShopIdList.contains(e)) {
                        if (MapUtils.isNotEmpty(finalShopMap) && finalShopMap.containsKey(e)) {
                            shopAuthList.add(finalShopMap.get(e));
                        }
                    }
                } else {
                    if (MapUtils.isNotEmpty(finalShopMap) && finalShopMap.containsKey(e)) {
                        shopAuthList.add(finalShopMap.get(e));
                    }
                }
            });
            return ResultUtil.returnSucc(shopAuthList);
        } catch (Exception e) {
            log.error("traceId:{} puid:{} marketplaceId:{} 顶级预算查看店铺异常:", traceId, puid, marketplaceId, e);
            return ResultUtil.returnErr("顶级预算查看店铺异常:"+e);
        }
    }

    @Override
    public Result<Integer> batchUpdateStatus(Integer puid, String status, List<Long> idList, List<Integer> authedShopIdList, Integer uid, String loginIp) {
        Result<Integer> result = new Result<>();
        List<AdvertiseStrategyTopBudgetTemplate> advertiseStrategyTopBudgetTemplateList = advertiseStrategyTopBudgetTemplateDao.getListByLongIdList(puid, idList);
        if (CollectionUtils.isEmpty(advertiseStrategyTopBudgetTemplateList)) {
            result.setCode(Result.ERROR);
            result.setMsg("当前没有数据修改");
            return result;
        }
        List<Integer> existedShopIdList = advertiseStrategyTopBudgetTemplateList.stream().map(AdvertiseStrategyTopBudgetTemplate::getShopId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(authedShopIdList) || !new HashSet<>(authedShopIdList).containsAll(existedShopIdList)) {
            result.setCode(Result.ERROR);
            result.setMsg("未授权");
            return result;
        }
        for (AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate : advertiseStrategyTopBudgetTemplateList) {
            try {
                if (status.equals("ENABLED")) {
                    //新数据推送
                    List<AdvertiseStrategyTopBudgetSchedule> list =
                        advertiseStrategyTopBudgetScheduleDao.listByTaskId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
                    advertiseStrategyTopBudgetTemplateDao.updateStatus(advertiseStrategyTopBudgetTemplate.getPuid(),
                        advertiseStrategyTopBudgetTemplate.getId(), status);
                    //推送到aadas服务中
                    try {
                        profilesApi.setTopBudgetTask(advertiseStrategyTopBudgetTemplate.getTaskId(), list, false);
                    } catch (Exception e) {
                        //错误日志打印
                        log.error("traceId:{} puid:{} shopId:{} taskId:{} 更新顶级预算模板失败亚马逊接口报错:", advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId(), e);
                        batchUpdateLog(puid, status, uid, loginIp, advertiseStrategyTopBudgetTemplate.getTaskId(), advertiseStrategyTopBudgetTemplate.getShopId(),
                                advertiseStrategyTopBudgetTemplate.getMarketplaceId(), "更新失败亚马逊接口报错");
                        return ResultUtil.returnErr("更新失败亚马逊接口报错:" + e);
                    }
                    batchUpdateLog(puid, status, uid, loginIp, advertiseStrategyTopBudgetTemplate.getTaskId(), advertiseStrategyTopBudgetTemplate.getShopId(),
                            advertiseStrategyTopBudgetTemplate.getMarketplaceId(), null);
                } else {
                    //数据到任务调度服务删除数据
                    profilesApi.removeTopBudgetTask(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId(), true);
                    advertiseStrategyTopBudgetTemplateDao.updateStatus(advertiseStrategyTopBudgetTemplate.getPuid(),
                            advertiseStrategyTopBudgetTemplate.getId(), status);
                    batchUpdateLog(puid, status, uid, loginIp, advertiseStrategyTopBudgetTemplate.getTaskId(), advertiseStrategyTopBudgetTemplate.getShopId(),
                            advertiseStrategyTopBudgetTemplate.getMarketplaceId(), null);
                }
            } catch (Exception e) {
                result.setCode(Result.ERROR);
                result.setMsg("顶级预算模板启停异常:" + e);
                log.error("puid:{} shopId:{} taskId:{} 顶级预算模板启停异常:", advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId(), e);
            }
        }
        return result;
    }

    private void batchUpdateLog(Integer puid, String status, Integer uid, String loginIp, long taskId, int shopId, String marketplaceId, String errorMsg) {
        try {
            AdManageOperationLog opLog = new AdManageOperationLog();
            opLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
            opLog.setPuid(puid);
            opLog.setUid(uid);
            opLog.setIp(loginIp);
            opLog.setTemplateId(taskId);
            opLog.setTargetId(String.valueOf(taskId));
            opLog.setShopId(shopId);
            opLog.setMarketplaceId(marketplaceId);
            opLog.setTarget(OperationLogTargetEnum.spTopBudget.getTargetValue());
            opLog.setOperationObject(OperationLogTargetEnum.spTopBudget.getTargetValue());

            List<OperationContent> opContents = new ArrayList<>();
            opContents.add(OperationContent.builder()
                    .title("应用状态")
                    .newValue(("ENABLED".equals(status) ? "开启" : "暂停") + "分时SP预算上限")
                    .build());
            opLog.setOperationContent(JSONUtil.objectToJson(opContents));
            opLog.setMessage(opLog.handleMessage(opContents));

            if (StringUtils.isNotBlank(errorMsg)) {
                opLog.setResult(1);
                opLog.setResultInfo(errorMsg);
            }else {
                opLog.setResult(0);
            }
            adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
        } catch (Exception e) {
            log.error("puid:{} shopId:{} taskId:{} 批量更新日志异常:", puid, shopId, taskId, e);
        }
    }

    @Override
    public Result<Integer> batchDeleteTemplate(Integer puid, List<Long> idList, List<Integer> authedShopIdList, Integer uid, String loginIp) {
        Result<Integer> result = new Result<>();
        List<AdvertiseStrategyTopBudgetTemplate> advertiseStrategyTopBudgetTemplateList = advertiseStrategyTopBudgetTemplateDao.getListByLongIdList(puid, idList);
        if (CollectionUtils.isEmpty(advertiseStrategyTopBudgetTemplateList)) {
            result.setCode(Result.ERROR);
            result.setMsg("当前没有数据修改");
            return result;
        }
        List<Integer> existedShopIdList = advertiseStrategyTopBudgetTemplateList.stream().map(AdvertiseStrategyTopBudgetTemplate::getShopId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(authedShopIdList) || !new HashSet<>(authedShopIdList).containsAll(existedShopIdList)) {
            result.setCode(Result.ERROR);
            result.setMsg("未授权");
            return result;
        }

        List<ShopAuth> shopAuthList = shopAuthDao.listAllByIds(puid, existedShopIdList);
        Map<Integer, ShopAuth> shopAuthMap = StreamUtil.toMap(shopAuthList, ShopAuth::getId);

        for (AdvertiseStrategyTopBudgetTemplate advertiseStrategyTopBudgetTemplate : advertiseStrategyTopBudgetTemplateList) {
            try {
                if (advertiseStrategyTopBudgetTemplate.getStatus().equals("ENABLED")) {
                    //数据到任务调度服务删除数据
                    profilesApi.removeTopBudgetTask(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId(), true);
                    advertiseStrategyTopBudgetTemplateDao.deleteTemplateId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getId());
                    advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule
                        (advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
                } else {
                    advertiseStrategyTopBudgetTemplateDao.deleteTemplateId(advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getId());
                    advertiseStrategyTopBudgetScheduleDao.deleteStrategySchedule
                            (advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId());
                }
                batchDeleteLog(puid, uid, loginIp, advertiseStrategyTopBudgetTemplate.getTaskId(), advertiseStrategyTopBudgetTemplate.getShopId(),
                        shopAuthMap.get(advertiseStrategyTopBudgetTemplate.getShopId()), null);
            } catch (Exception e) {
                result.setCode(Result.ERROR);
                result.setMsg("顶级预算模板删除异常:"+e);
                batchDeleteLog(puid, uid, loginIp, advertiseStrategyTopBudgetTemplate.getTaskId(), advertiseStrategyTopBudgetTemplate.getShopId(),
                        shopAuthMap.get(advertiseStrategyTopBudgetTemplate.getShopId()), result.getMsg());
                log.error("traceId:{} puid:{} shopId:{}  顶级预算模板删除异常:",advertiseStrategyTopBudgetTemplate.getPuid(), advertiseStrategyTopBudgetTemplate.getShopId(), advertiseStrategyTopBudgetTemplate.getTaskId(), e);
            }
        }
        return result;
    }

    private void batchDeleteLog(Integer puid, Integer uid, String loginIp, long taskId, int shopId, ShopAuth shopAuth, String errorMsg) {
        try {
            if (Objects.isNull(shopAuth)) {
                log.info("删除分时SP顶级预算模板，店铺信息不存在，puid:{} shopId:{} taskId:{}", puid, shopId, taskId);
                return;
            }
            AdManageOperationLog opLog = new AdManageOperationLog();
            opLog.setAction(OperationLogActionEnum.EDIT.getOperationValue());
            opLog.setPuid(puid);
            opLog.setUid(uid);
            opLog.setIp(loginIp);
            opLog.setTemplateId(taskId);
            opLog.setTargetId(String.valueOf(taskId));
            opLog.setShopId(shopId);
            opLog.setMarketplaceId(shopAuth.getMarketplaceId());
            opLog.setTarget(OperationLogTargetEnum.spTopBudget.getTargetValue());
            opLog.setOperationObject(OperationLogTargetEnum.spTopBudget.getTargetValue());

            List<OperationContent> opContents = new ArrayList<>();
            opContents.add(OperationContent.builder()
                    .title("操作")
                    .newValue("删除模板" + shopAuth.getName())
                    .build());
            opLog.setOperationContent(JSONUtil.objectToJson(opContents));
            opLog.setMessage(opLog.handleMessage(opContents));
            if (StringUtils.isNotBlank(errorMsg)) {
                opLog.setResult(1);
                opLog.setResultInfo(errorMsg);
            } else {
                opLog.setResult(0);
            }
            adManageOperationLogService.printAdOtherOperationLog(com.google.common.collect.Lists.newArrayList(opLog));
        } catch (Exception e) {
            log.error("puid:{} shopId:{} taskId:{} 批量删除日志异常:", puid, shopId, taskId, e);
        }
    }

    @Override
    public void disableAllTemplateStatus(Integer puid, Integer shopId) {
        log.info("分时sp顶级预算自动更新模板状态, puid: {}, shopId: {}", puid, shopId);
        List<AdvertiseStrategyTopBudgetTemplate> disabledList = advertiseStrategyTopBudgetTemplateDao.getEnabledTemplate(puid, shopId);
        if (CollectionUtils.isEmpty(disabledList)) {
            return;
        }
        for (AdvertiseStrategyTopBudgetTemplate template : disabledList) {
            advertiseStrategyTopBudgetTemplateDao.updateStatus(template.getPuid(), template.getId(), AdStrategyEnableStatusEnum.DISABLED.getCode());
            //数据到任务调度服务删除数据
            profilesApi.removeTopBudgetTask(template.getPuid(), template.getShopId(), template.getTaskId(), false);

        }
    }

}
