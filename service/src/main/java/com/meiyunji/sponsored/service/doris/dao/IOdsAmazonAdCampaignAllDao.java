package com.meiyunji.sponsored.service.doris.dao;

import com.meiyunji.sponsored.common.springjdbc.IDorisBaseDao;
import com.meiyunji.sponsored.service.adTagSystem.resp.QueryShopInfoResp;
import com.meiyunji.sponsored.service.doris.po.OdsAmazonAdCampaignAll;

import java.util.List;

/**
 * amazon广告所有类型活动表(OdsAmazonAdCampaignAll)数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-26 10:46:15
 */
public interface IOdsAmazonAdCampaignAllDao extends IDorisBaseDao<OdsAmazonAdCampaignAll> {
    //根据广告活动id获取数据
    List<OdsAmazonAdCampaignAll> listByCampaignId(Integer puid, List<Integer> shopIdList, List<String> campaignIdList);

    List<Integer> getShopIdsByCampaignIdsOrPortfolioIds(int puid,List<Integer> shopIdList, List<String> campaignIds, List<String> portfolioIds);

    String getCampaignIdsByPortfolioIdSql(Integer puid, Integer shopId, List<Object> argsList, String portfolioId, String type, String state, String servingStatus);

    List<QueryShopInfoResp.ShopVo> getCampaignCountGroupByShopId(Integer puid, List<String> shopIdList, List<String> campaignIdList);
}

