package com.meiyunji.sponsored.common.util;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class ThreadPoolUtil {

    //线程缓冲队列
    //private static BlockingQueue<Runnable> bqueue = new ArrayBlockingQueue<>(20000);

    //核心线程数，会一直存活，即使没有任务，线程池也会维护线程的最少数量
    //private static final int SIZE_CORE_POOL = 50;
    //线程池维护线程的最大数量
    //private static final int SIZE_MAX_POOL = 500;
    //线程池维护线程所允许的空闲时间
    private static final long ALIVE_TIME = 6000;

    private static ThreadPoolExecutor aggregateMultiQueryThreadPool = new ThreadPoolExecutor(8, 20, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor timeSharingStrategyThreadPool = new ThreadPoolExecutor(32, 40, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor submitAutoRuleThreadPool = new ThreadPoolExecutor(50, 200, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor autoRuleThreadPool = new ThreadPoolExecutor(32, 40, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor updateTimeSharingStrategyThreadPool = new ThreadPoolExecutor(100, 100, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor transferTimeSharingStrategyThreadPool = new ThreadPoolExecutor(100, 100, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor removeTimeSharingStrategyThreadPool = new ThreadPoolExecutor(100, 100, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor syncAsinImage = new ThreadPoolExecutor(10, 50, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor syncAsinImageTitle = new ThreadPoolExecutor(20, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    //临时使用

    private static ThreadPoolExecutor commodityImgPool = new ThreadPoolExecutor(30, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    /**
     * 广告管理线程池
     */
    private static ThreadPoolExecutor batchAdManagePool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 拉取广告服务商产品信息元数据线程池
     */
    private static ThreadPoolExecutor productMetadataPool = new ThreadPoolExecutor(8,8, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor spPoolExecutor = new ThreadPoolExecutor(30, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor sbPoolExecutor = new ThreadPoolExecutor(30, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor sdPoolExecutor = new ThreadPoolExecutor(30, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor shopPoolExecutor = new ThreadPoolExecutor(30, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor allShopPoolExecutor = new ThreadPoolExecutor(30, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    //多店铺获取建议预算线程池
    private final static ThreadPoolExecutor MULTI_SHOP_BUDGET_RECOMMENDATION_POOL = new ThreadPoolExecutor(5, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 搜索词词频线程池
     */
    private static ThreadPoolExecutor queryWordRootPool = new ThreadPoolExecutor(10, 20, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 关键词投放列表页线程池
     */
    private static ThreadPoolExecutor keywordPagePool = new ThreadPoolExecutor(8, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    private static ThreadPoolExecutor campaignPagePool = new ThreadPoolExecutor(8, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private final static ThreadPoolExecutor CAMPAIGN_DORIS_PAGE_POOL = new ThreadPoolExecutor(10, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * sp广告产品 汇总 多线程
     */
    private final static ThreadPoolExecutor AD_PRODUCT_DORIS_PAGE_POOL = new ThreadPoolExecutor(10, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    /**
     * 商品投放、自动投放列表页线程池
     */
    private static ThreadPoolExecutor targetPagePool = new ThreadPoolExecutor(8, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    private static ThreadPoolExecutor cpcStrategyRetryPoolExecutor = new ThreadPoolExecutor(40, 120, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public static ThreadPoolExecutor getCpcStrategyRetryPoolExecutor() {
        return cpcStrategyRetryPoolExecutor;
    }

    private static ThreadPoolExecutor groupPagePool = new ThreadPoolExecutor(8, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor wordRootCalculatePool = new ThreadPoolExecutor(8, 8, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor timeSharingAutoRuleThreadPool = new ThreadPoolExecutor(16, 20, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    public static ThreadPoolExecutor getPostcodeGetPool() {
        return postcodeGetPool;
    }

    private static ThreadPoolExecutor postcodeGetPool = new ThreadPoolExecutor(8, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    private static ThreadPoolExecutor listPostcodeGetPool = new ThreadPoolExecutor(8, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    private static ThreadPoolExecutor sbCampaignBatchUpdateThreadPool = new ThreadPoolExecutor(10, 10, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    public static ThreadPoolExecutor getListPostcodeGetPool() {
        return listPostcodeGetPool;
    }

    public static ThreadPoolExecutor getAggregateMultiQueryThreadPool() {
        return aggregateMultiQueryThreadPool;
    }
    public static ThreadPoolExecutor getProductMetadataPool() {
        return productMetadataPool;
    }

    public static ThreadPoolExecutor getBatchAdManagePool() {
        return batchAdManagePool;
    }

    public static ThreadPoolExecutor getTimeSharingStrategyThreadPool() {
        return timeSharingStrategyThreadPool;
    }

    public static ThreadPoolExecutor getSubmitAutoRuleThreadPool() {
        return submitAutoRuleThreadPool;
    }

    public static ThreadPoolExecutor getAutoRuleThreadPool() {
        return autoRuleThreadPool;
    }

    public static ThreadPoolExecutor getUpdateTimeSharingStrategyThreadPool() {
        return updateTimeSharingStrategyThreadPool;
    }

    public static ThreadPoolExecutor getTransferTimeSharingStrategyThreadPool() {
        return transferTimeSharingStrategyThreadPool;
    }

    public static ThreadPoolExecutor getRemoveTimeSharingStrategyThreadPool() {
        return removeTimeSharingStrategyThreadPool;
    }

    public static ThreadPoolExecutor getTimeSharingAutoRuleThreadPool() {
        return timeSharingAutoRuleThreadPool;
    }


    /**
     * cpc ad 店铺同步
     */
    private static final ThreadPoolExecutor CPC_AD_SHOP_ASYNC_POOL = new ThreadPoolExecutor(8,
            8, ALIVE_TIME,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingQueue<>(),
//            new ThreadFactoryBuilder().setNameFormat("shop-sync-%d").build(),
            new ThreadPoolExecutor.DiscardPolicy());

    public static ThreadPoolExecutor getCpcAdShopAsyncPool() {
        return CPC_AD_SHOP_ASYNC_POOL;
    }

    /**
     * 广告数据同步线程池
     */
    private static ThreadPoolExecutor advertiseDataSyncPool = new ThreadPoolExecutor(50, 200,
            ALIVE_TIME, TimeUnit.MILLISECONDS, new SynchronousQueue<>(), Executors.defaultThreadFactory(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getAdvertiseDataSyncPool() {
        return advertiseDataSyncPool;
    }


    /**
     * 同步sp广告管理线程池
     */
    private static ThreadPoolExecutor cpcSpSyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sp广告管理线程池
     */
    private static ThreadPoolExecutor cpcSpType1SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sp广告管理线程池
     */
    private static ThreadPoolExecutor cpcSpType2SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sp广告管理线程池
     */
    private static ThreadPoolExecutor cpcSpType4SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    /**
     * 同步sp广告管理线程池
     */
    private static ThreadPoolExecutor cpcSpType3SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());





    private static ThreadPoolExecutor walmartReportSyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());




    private static ThreadPoolExecutor walmartSnapshotPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    public static ThreadPoolExecutor getWalmartSyncManagePool() {
        return walmartSyncManagePool;
    }

    private static ThreadPoolExecutor walmartSyncManagePool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    private static ThreadPoolExecutor walmartSyncKeywordByCampaignIdPool = new ThreadPoolExecutor(10, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    private static ThreadPoolExecutor walmartSyncItemRecommendationsPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public static ThreadPoolExecutor getWalmartSyncItemRecommendationsPool() {
        return walmartSyncItemRecommendationsPool;
    }

    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSbSyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSbType1SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSbType2SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSbType3SyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    /**
     * 同步sd广告管理线程池
     */
    private static ThreadPoolExecutor cpcSdSyncPool = new ThreadPoolExecutor(10, 30, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSdType1SyncPool = new ThreadPoolExecutor(10, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSdType2SyncPool = new ThreadPoolExecutor(10, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSdType3SyncPool = new ThreadPoolExecutor(10, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSdType4SyncPool = new ThreadPoolExecutor(10, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());
    /**
     * 同步sb广告管理线程池
     */
    private static ThreadPoolExecutor cpcSdType5SyncPool = new ThreadPoolExecutor(10, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public static ThreadPoolExecutor getCpcSdType1SyncPool() {
        return cpcSdType1SyncPool;
    }

    public static ThreadPoolExecutor getCpcSdType2SyncPool() {
        return cpcSdType2SyncPool;
    }

    public static ThreadPoolExecutor getCpcSdType3SyncPool() {
        return cpcSdType3SyncPool;
    }

    public static ThreadPoolExecutor getCpcSdType4SyncPool() {
        return cpcSdType4SyncPool;
    }

    public static ThreadPoolExecutor getCpcSdType5SyncPool() {
        return cpcSdType5SyncPool;
    }

    public static ThreadPoolExecutor getWeeklyGroupExecutorForHour() {
        return weeklyGroupExecutorForHour;
    }

    public static ThreadPoolExecutor getWeeklyCampaignExecutorForHour() {
        return weeklyCampaignExecutorForHour;
    }

    public static ThreadPoolExecutor getWeeklyTargerExecutorForHour() {
        return weeklyTargerExecutorForHour;
    }

    private final static ThreadPoolExecutor AGGREGATE_PLACEMENT_DORIS_SYNC_POOL = new ThreadPoolExecutor(8, 8, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getStoreSpotlightStorePool() {
        return STORE_SPOTLIGHT_STORE_POOL;
    }
    public static ThreadPoolExecutor getStoreSpotlightPagePool() {
        return STORE_SPOTLIGHT_PAGE_POOL;
    }

    private static ThreadPoolExecutor weeklyGroupExecutorForHour = new ThreadPoolExecutor(9, 9, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor weeklyCampaignExecutorForHour = new ThreadPoolExecutor(9, 9, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor weeklyTargerExecutorForHour = new ThreadPoolExecutor(9, 9, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor createAdForEsExecutor = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor createAdForTargetExecutor = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor createAdForSyncExecutor = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor printWxLogExecutor = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor queryBidRecommendationsForSpAdsExecutor = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor queryDataDomainExecutor = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor wordTranslateSetRedisExecutor = new ThreadPoolExecutor(8, 8, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor wordTranslateInsertTableExecutor = new ThreadPoolExecutor(8, 8, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getQueryDataDomainExecutor() {
        return queryDataDomainExecutor;
    }

    public static ThreadPoolExecutor getQueryBidRecommendationsForSpAdsExecutor() {
        return queryBidRecommendationsForSpAdsExecutor;
    }

    public static ThreadPoolExecutor getCreateAdForEsExecutor() {
        return createAdForEsExecutor;
    }

    public static ThreadPoolExecutor getCreateAdForTargetExecutor() {
        return createAdForTargetExecutor;
    }

    public static ThreadPoolExecutor getCreateAdForSyncExecutor() {
        return createAdForSyncExecutor;
    }

    public static ThreadPoolExecutor getPrintWxLogExecutor() {
        return printWxLogExecutor;
    }

    private final static ThreadPoolExecutor STORE_SPOTLIGHT_STORE_POOL = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private final static ThreadPoolExecutor STORE_SPOTLIGHT_PAGE_POOL = new ThreadPoolExecutor(16, 16, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor aggregateCampaignSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor aggregatePlacementSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
        TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor cpcTargetAggregateIdsSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor terminateBatchSpSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor perspectiveAggregateKeyWordSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor perspectiveAggregateTargetSyncPool = new ThreadPoolExecutor(8, 8, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor perspectiveAggregateCampaignSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor perspectiveAggregatePlacementSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor campaignTagAggregateSyncPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    public static ThreadPoolExecutor getWalmartReportSyncPool() {
        return walmartReportSyncPool;
    }

    public static ThreadPoolExecutor getWalmartSnapshotPool() {
        return walmartSnapshotPool;
    }

    /**
     * 同步搜索词翻译数据
     */
    private static ThreadPoolExecutor keywordLocalizationPool =
            new ThreadPoolExecutor(30, 50, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 给聚合产品报告新增的父aisn字段同步父asin值
     */
    private static ThreadPoolExecutor aggregationProductReportOfParentAsinPool =
            new ThreadPoolExecutor(30, 100, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     * 同步聚合产品报告父asin为None的字段再次同步父asin值
     */
    private static ThreadPoolExecutor aggregationProductOfNoneParentAsinReportPool =
            new ThreadPoolExecutor(3, 5, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    /**
     *  获取信用卡账单数据同步
     *
     */

    private static ThreadPoolExecutor syncInvoicePool = new ThreadPoolExecutor(5, 5, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public static ThreadPoolExecutor getSyncInvoicePool() {
        return syncInvoicePool;
    }

    /**
     *  获取信用卡账单数据同步
     *
     */

    private static ThreadPoolExecutor syncInvoiceDetailsPool = new ThreadPoolExecutor(5, 5, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());


    /**
     * 广告活动线程池
     */
    private static ThreadPoolExecutor syncHistoryPool = new ThreadPoolExecutor(5, 5, ALIVE_TIME, TimeUnit.MILLISECONDS, new SynchronousQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 数据库比对线程池
     */
    private static ThreadPoolExecutor dbComparePool = new ThreadPoolExecutor(16, 16, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 报告下载中心线程池
     */
    private static ThreadPoolExecutor downloadCenterPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 诊断优化定位数量线程池
     */
    private static ThreadPoolExecutor productPerspectiveDiagnosePool = new ThreadPoolExecutor(12, 12, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 商品透视分析-顶部asin初始化线程池
     */
    private static ThreadPoolExecutor productPerspectiveInitAsinPool = new ThreadPoolExecutor(8, 12, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 广告活动新建同步
     */
    private static ThreadPoolExecutor campaignCreateSyncPool = new ThreadPoolExecutor(3, 3, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    private static ThreadPoolExecutor adsUpdaterSyncPool = new ThreadPoolExecutor(3, 3, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 同步否定关键词
     */
    private static ThreadPoolExecutor negativeSyncPool = new ThreadPoolExecutor(3, 3, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor spBatchCreateCallAmazonAPIPool = new ThreadPoolExecutor(12, 12, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor spBatchCreateSaveData2DBPool = new ThreadPoolExecutor(6, 6, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor batchCreateRetryPool = new ThreadPoolExecutor(5, 20, ALIVE_TIME,
            TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor ManagementStreamMessagePool = new ThreadPoolExecutor(50, 100, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor adGroupProcessPool = new ThreadPoolExecutor(16, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor targetProcessPool = new ThreadPoolExecutor(32, 40, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor streamGroupStrategyProcessPool = new ThreadPoolExecutor(16, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor executeRecordMessageVoPool = new ThreadPoolExecutor(32, 40, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getExecuteRecordMessageVoPool() {
        return executeRecordMessageVoPool;
    }

    private static ThreadPoolExecutor executeTargetTaskPool = new ThreadPoolExecutor(8, 16, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor sbTargetSyncPool = new ThreadPoolExecutor(8, 16, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor sdTargetSyncPool = new ThreadPoolExecutor(8, 16, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor sbNewCreateCampaignThreadPool = new ThreadPoolExecutor(12, 12, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    private static ThreadPoolExecutor sdNewCreateCampaignThreadPool = new ThreadPoolExecutor(12, 12, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    private static ThreadPoolExecutor invoiceProductPool = new ThreadPoolExecutor(20, 20, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public static ThreadPoolExecutor getInvoiceProductPool() {
        return invoiceProductPool;
    }

    private static ThreadPoolExecutor delShopAdProcessThreadPool = new ThreadPoolExecutor(4, 6, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());
    public static ThreadPoolExecutor getDelShopAdProcessThreadPool() {
        return delShopAdProcessThreadPool;
    }

    public static ThreadPoolExecutor getStreamGroupStrategyProcessPool() {
        return streamGroupStrategyProcessPool;
    }


    public static ThreadPoolExecutor getAdGroupProcessPool() {
        return adGroupProcessPool;
    }

    public static ThreadPoolExecutor getTargetProcessPool() {
        return targetProcessPool;
    }

    public static ThreadPoolExecutor getManagementStreamMessagePool() {
        return ManagementStreamMessagePool;
    }

    /**
     * 获取建议竞价线程池
     */
    private static ThreadPoolExecutor batchGetSuggestedBitPool = new ThreadPoolExecutor(12, 12, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    /**
     * 获取建议竞价线程池
     */
    private static final ThreadPoolExecutor SP_BATCH_GET_SUGGESTED_BIT_POOL = new ThreadPoolExecutor(20, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor ManagementStreamSbTargetMessagePool = new ThreadPoolExecutor(20, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor managementGetBidPool = new ThreadPoolExecutor(20, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());


    private static ThreadPoolExecutor managementGetBudgetPool = new ThreadPoolExecutor(20, 20, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    private static ThreadPoolExecutor exportGrabRankingSnapshotNotImagePool = new ThreadPoolExecutor(5, 5, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), new ThreadPoolExecutor.AbortPolicy());

    private static ThreadPoolExecutor exportGrabRankingSnapshotImagePool = new ThreadPoolExecutor(3, 3, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(100), new ThreadPoolExecutor.AbortPolicy());

    private static ThreadPoolExecutor multiGroupAddProductPool = new ThreadPoolExecutor(40, 40, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(300), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getMultiGroupAddProductPool() {
        return multiGroupAddProductPool;
    }

    public static ThreadPoolExecutor getExportGrabRankingSnapshotNotImagePool() {
        return exportGrabRankingSnapshotNotImagePool;
    }

    public static ThreadPoolExecutor getExportGrabRankingSnapshotImagePool() {
        return exportGrabRankingSnapshotImagePool;
    }

    public static ThreadPoolExecutor getManagementGetBudgetPool() {
        return managementGetBudgetPool;
    }

    public static ThreadPoolExecutor getManagementGetBidPool() {
        return managementGetBidPool;
    }

    public static ThreadPoolExecutor getBatchCreateRetryPool() {
        return batchCreateRetryPool;
    }

    public static ThreadPoolExecutor getNegativeSyncPool() {
        return negativeSyncPool;
    }

    public static ThreadPoolExecutor getAdsUpdateSyncPool() {
        return adsUpdaterSyncPool;
    }

    public static ThreadPoolExecutor getCampaignCreateSyncPool() {
        return campaignCreateSyncPool;
    }

    public static ThreadPoolExecutor getSDTargetCreateSyncPool() {
        return sdTargetCreateSyncPool;
    }

    public static ThreadPoolExecutor getSyncHistoryPool() {
        return syncHistoryPool;
    }


    public static ThreadPoolExecutor getSyncInvoiceDetailsPool() {
        return syncInvoiceDetailsPool;
    }
    public static ThreadPoolExecutor getCpcSpType1SyncPool() {
        return cpcSpType1SyncPool;
    }

    public static ThreadPoolExecutor getCpcSpType2SyncPool() {
        return cpcSpType2SyncPool;
    }

    public static ThreadPoolExecutor getCpcSpType4SyncPool() {
        return cpcSpType4SyncPool;
    }

    public static ThreadPoolExecutor getCpcSpType3SyncPool() {
        return cpcSpType3SyncPool;
    }

    public static ThreadPoolExecutor getCpcSbType1SyncPool() {
        return cpcSbType1SyncPool;
    }

    public static ThreadPoolExecutor getCpcSbType2SyncPool() {
        return cpcSbType2SyncPool;
    }

    public static ThreadPoolExecutor getCpcSbType3SyncPool() {
        return cpcSbType3SyncPool;
    }

    public static ThreadPoolExecutor getKeywordLocalizationPool() {
        return keywordLocalizationPool;
    }

    public static ThreadPoolExecutor getManagementStreamSbTargetMessagePool() {
        return ManagementStreamSbTargetMessagePool;
    }

    public static ThreadPoolExecutor getAggregationProductReportOfParentAsinPool() {
        return aggregationProductReportOfParentAsinPool;
    }

    public static ThreadPoolExecutor getAggregationProductReportOfNoneParentAsinPool() {
        return aggregationProductOfNoneParentAsinReportPool;
    }



    public static ThreadPoolExecutor getCommodityImgPool() {
        return commodityImgPool;
    }


    public static ThreadPoolExecutor getSyncAsinImage() {
        return syncAsinImage;
    }

    public static void setSyncAsinImage(ThreadPoolExecutor syncAsinImage) {
        ThreadPoolUtil.syncAsinImage = syncAsinImage;
    }

    public static ThreadPoolExecutor getCpcSpSyncPool() {
        return cpcSpSyncPool;
    }

    public static ThreadPoolExecutor getCpcSbSyncPool() {
        return cpcSbSyncPool;
    }

    public static ThreadPoolExecutor getCpcSdSyncPool() {
        return cpcSdSyncPool;
    }

    public static ThreadPoolExecutor getSyncAsinImageTitle() {
        return syncAsinImageTitle;
    }

    public static ThreadPoolExecutor getSpPoolExecutor() {
        return spPoolExecutor;
    }

    public static ThreadPoolExecutor getSbPoolExecutor() {
        return sbPoolExecutor;
    }

    public static ThreadPoolExecutor getSdPoolExecutor() {
        return sdPoolExecutor;
    }

    public static ThreadPoolExecutor getShopPoolExecutor() {
        return shopPoolExecutor;
    }

    public static ThreadPoolExecutor getAllShopPoolExecutor() {
        return allShopPoolExecutor;
    }

    public static ThreadPoolExecutor getDbComparePool() {
        return dbComparePool;
    }

    public static ThreadPoolExecutor getDownloadCenterPool() {
        return downloadCenterPool;
    }

    public static ThreadPoolExecutor getProductPerspectiveDiagnosePool() {
        return productPerspectiveDiagnosePool;
    }

    public static ThreadPoolExecutor getProductPerspectiveInitAsinPool() {
        return productPerspectiveInitAsinPool;
    }


    public static ThreadPoolExecutor getQueryWordRootPool() {
        return queryWordRootPool;
    }

    public static ThreadPoolExecutor getKeywordPagePool() {
        return keywordPagePool;
    }

    public static ThreadPoolExecutor getTargetPagePool() {
        return targetPagePool;
    }

    public static ThreadPoolExecutor getCampaignPagePool() {
        return campaignPagePool;
    }

    public static ThreadPoolExecutor getCampaignDorisPagePool() {
        return CAMPAIGN_DORIS_PAGE_POOL;
    }

    public static ThreadPoolExecutor getMultiShopBudgetRecommendationPool() {
        return MULTI_SHOP_BUDGET_RECOMMENDATION_POOL;
    }

    public static ThreadPoolExecutor getAdProductDorisPagePool() {
        return AD_PRODUCT_DORIS_PAGE_POOL;
    }


    public static ThreadPoolExecutor getGroupPagePool() {
        return groupPagePool;
    }

    public static void setSyncAsinImageTitle(ThreadPoolExecutor syncAsinImageTitle) {
        ThreadPoolUtil.syncAsinImageTitle = syncAsinImageTitle;
    }

    public static ThreadPoolExecutor getWordRootCalculatePool() {
        return wordRootCalculatePool;
    }

    public static ThreadPoolExecutor getCpcAggregateIdsSyncPool() {
        return aggregateCampaignSyncPool;
    }

    public static ThreadPoolExecutor getAggregatePlacementSyncPool() {
        return aggregatePlacementSyncPool;
    }

    public static ThreadPoolExecutor getAggregatePlacementDorisSyncPool() {
        return AGGREGATE_PLACEMENT_DORIS_SYNC_POOL;
    }

    public static ThreadPoolExecutor getCpcTargetAggregateIdsSyncPool() {
        return cpcTargetAggregateIdsSyncPool;
    }

    public static ThreadPoolExecutor getTerminateBatchSpSyncPool() {
        return terminateBatchSpSyncPool;
    }

    public static ThreadPoolExecutor getSpBatchCreateCallAmazonAPIPool() {
        return spBatchCreateCallAmazonAPIPool;
    }

    public static ThreadPoolExecutor getSbNewCreateCampaignThreadPool() {
        return sbNewCreateCampaignThreadPool;
    }
    public static ThreadPoolExecutor getSdNewCreateCampaignThreadPool() {
        return sdNewCreateCampaignThreadPool;
    }

    public static ThreadPoolExecutor getSpBatchCreateSaveData2DBPool() {
        return spBatchCreateSaveData2DBPool;
    }

    public static ThreadPoolExecutor getBatchGetSuggestedBitPool() {
        return batchGetSuggestedBitPool;
    }

    public static ThreadPoolExecutor getSpBatchGetSuggestedBitPool() {
        return SP_BATCH_GET_SUGGESTED_BIT_POOL;
    }


    public static ThreadPoolExecutor getPerspectiveAggregateKeyWordSyncPool() {
        return perspectiveAggregateKeyWordSyncPool;
    }

    public static ThreadPoolExecutor getPerspectiveAggregateTargetSyncPool() {
        return perspectiveAggregateTargetSyncPool;
    }

    public static ThreadPoolExecutor getPerspectiveAggregateCampaignSyncPool() {
        return perspectiveAggregateCampaignSyncPool;
    }

    public static ThreadPoolExecutor getPerspectiveAggregatePlacementSyncPool() {
        return perspectiveAggregatePlacementSyncPool;
    }

    public static ThreadPoolExecutor getSbCampaignBatchUpdateThreadPool() {
        return sbCampaignBatchUpdateThreadPool;
    }

    public static ThreadPoolExecutor getCampaignTagAggregateSyncPool() {
        return campaignTagAggregateSyncPool;
    }

    private final static ThreadPoolExecutor handelMonitorDataWorkerPool = new ThreadPoolExecutor(5, 10, 6000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());

    public static ThreadPoolExecutor getHandelMonitorDataWorkerPool() {
        return handelMonitorDataWorkerPool;
    }

    private static ThreadPoolExecutor adDashboardHourDataThreadPool = new ThreadPoolExecutor(16, 20, 2000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>());

    public static ThreadPoolExecutor getAdDashboardHourDataThreadPool() {
        return adDashboardHourDataThreadPool;
    }

    public static ThreadPoolExecutor getExecuteTargetTaskPool() {
        return executeTargetTaskPool;
    }

    public static ThreadPoolExecutor getSbTargetSyncPool() {
        return sbTargetSyncPool;
    }

    public static ThreadPoolExecutor getSdTargetSyncPool() {
        return sdTargetSyncPool;
    }

    private static ThreadPoolExecutor sendSyncManageWorkerPool = new ThreadPoolExecutor(5, 10, 6000, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(1000), new ThreadPoolExecutor.DiscardPolicy());

    public static ThreadPoolExecutor getSendSyncManageWorkerPool() {
        return sendSyncManageWorkerPool;
    }

    public static ThreadPoolExecutor getWalmartSyncKeywordByCampaignIdPool() {
        return walmartSyncKeywordByCampaignIdPool;
    }

    /**
     * SD投放创建
     */
    private static ThreadPoolExecutor sdTargetCreateSyncPool = new ThreadPoolExecutor(3, 3, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getWordTranslateSetRedisExecutor() {
        return wordTranslateSetRedisExecutor;
    }

    public static ThreadPoolExecutor getWordTranslateInsertTableExecutor() {
        return wordTranslateInsertTableExecutor;
    }
    /**
     * 帖子同步
     */
    private static ThreadPoolExecutor syncPostProfilesPool = new ThreadPoolExecutor(5, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 帖子手动同步
     */
    private static ThreadPoolExecutor manualSyncPostsPool = new ThreadPoolExecutor(5, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getSyncPostProfilesPool() {
        return syncPostProfilesPool;
    }
    private static ThreadPoolExecutor syncPostPool = new ThreadPoolExecutor(5, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getSyncPostPool() {
        return syncPostPool;
    }

    public static ThreadPoolExecutor getManualSyncPostsPool() {
        return manualSyncPostsPool;
    }

    private static ThreadPoolExecutor syncPostReportAllPool = new ThreadPoolExecutor(5, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getSyncPostReportAllPool() {
        return syncPostReportAllPool;
    }

    private static ThreadPoolExecutor syncPostReportIncrementPool = new ThreadPoolExecutor(5, 10, ALIVE_TIME, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<>(), new ThreadPoolExecutor.CallerRunsPolicy());

    public static ThreadPoolExecutor getSyncPostReportIncrementPool() {
        return syncPostReportIncrementPool;
    }

    /**
     * 等待机制：考虑吞吐量和内存，不要一直往队列里面加任务(慎用)
     *
     * @param threadPoolExecutor:
     */
    public static void waiting(ThreadPoolExecutor threadPoolExecutor) {
        if (threadPoolExecutor.getQueue().size() > threadPoolExecutor.getCorePoolSize() * 5) {
            while (true) {
                if (threadPoolExecutor.getQueue().size() < threadPoolExecutor.getCorePoolSize() / 5) {
                    break;
                } else {
                    try {
                        Thread.sleep(300);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    /**
     * 等待线程池任务执行结束，并关闭线程池
     *
     * @param threadPoolExecutor：
     */
    public static void waitingFinish(ThreadPoolExecutor threadPoolExecutor) {
        while (true) {
            if (threadPoolExecutor.getActiveCount() == 0) {
                break;
            }
            try {
                Thread.sleep(2000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    public static String getStackTrace() {
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        StringBuilder stackTraceBuilder = new StringBuilder();
        for (StackTraceElement element : stackTrace) {
            stackTraceBuilder.append(element + "\n");
        }
        return stackTraceBuilder.toString();
    }

    public static long getAliveTime() {
        return ALIVE_TIME;
    }


}
