package com.meiyunji.sponsored.common.permission.context;

import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 权限上下文
 * 用于在权限检查过程中传递相关信息
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PermissionContext {

    /**
     * 主账号ID
     */
    private Integer puid;

    /**
     * 子账号ID
     */
    private Integer uid;

    /**
     * 店铺ID
     */
    private Integer shopId;

    /**
     * 站点ID
     */
    private String marketplaceId;

    /**
     * 请求参数
     */
    private Map<String, Object> requestParams;

    /**
     * 方法名称
     */
    private String methodName;

    /**
     * 类名称
     */
    private String className;

    /**
     * 活动id字段  如果需要根据活动id拦截，则需要该字段
     */
    private String campaignId;

    // ==================== 第一类：用户信息 ====================
    /**
     * 检查是否有默认权限（管理员或主账号不需要权限过滤）
     * @return true表示有默认权限，不需要权限过滤
     */
    public boolean hasAdminPermission() {
        // 检查是否为管理员用户
        if (isAdminUser != null && isAdminUser) {
            return true;
        }

        // 检查是否为主账号（puid == uid）
        return puid != null && puid.equals(uid);
    }

    /**
     * 是否为管理员用户
     */
    private Boolean isAdminUser;

    // ==================== 第二类：活动过滤所需字段 ====================

    /**
     * 店铺ID列表
     */
    private List<Integer> shopIds;

    /**
     * 广告类型列表
     */
    private List<String> adType;

    /**
     * 过滤类型
     */
    private PermissionFilterType filterType;

    /**
     * 权限过滤策略
     */
    private PermissionFilterStrategy filterStrategy;
}
