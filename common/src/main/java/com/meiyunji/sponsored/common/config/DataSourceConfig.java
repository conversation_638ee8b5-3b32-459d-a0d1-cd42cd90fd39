package com.meiyunji.sponsored.common.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shardingsphere.driver.api.ShardingSphereDataSourceFactory;
import org.apache.shardingsphere.infra.config.RuleConfiguration;
import org.apache.shardingsphere.infra.config.algorithm.ShardingSphereAlgorithmConfiguration;
import org.apache.shardingsphere.sharding.api.config.ShardingRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.rule.ShardingTableRuleConfiguration;
import org.apache.shardingsphere.sharding.api.config.strategy.sharding.HintShardingStrategyConfiguration;
import org.apache.shardingsphere.sharding.api.config.strategy.sharding.NoneShardingStrategyConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.*;

/**
 * @author: wade
 * @date: 2021/10/18 14:39
 * @describe: 多数据源配置
 */
@Slf4j
@Configuration
public class DataSourceConfig {

    @Value(value = "${shardingSphere.table-node-rule:None}")
    private String tableNodeRule;

    @Value(value = "${shardingSphere.database.config-file:database-sharding-prod.yml}")
    private String databaseConfigFile;

    @Value(value = "${shardingSphere.table.config-file:table-sharding-prod.yml}")
    private String tableConfigFile;

    @Value(value = "${shardingSphere.table-128-node-rule:None}")
    private String table128NodeRule;

    @Value(value = "${shardingSphere.table128.config-file:table-128-sharding-prod.yml}")
    private String table128ConfigFile;

    /**
     *  把此配置设置为false, 即在开发环境启动时,只加载一个分库,提高开发效率
     */
    @Value(value = "${datasource.is-shard:true}")
    private Boolean isShard;

    @Value(value = "${spring.profiles.active}")
    private String active;

    /**
     * 新增加vip数据源后,记得增加map对应值
     */
    public static final Map<String, String> vipData = new HashMap<String, String>() {{
        put("6090", "ds_vip_6090");
        put("8032", "ds_vip_8032");
        put("18617", "ds_vip_18617");
        put("16092", "ds_vip_16092");
        put("14059", "ds_vip_14059");
        put("16997", "ds_vip_16997");
        put("19077", "ds_vip_19077");
        put("19949", "ds_vip_19949");
        put("12584", "ds_vip_12584");
        put("21871", "ds_vip_21871");
        put("18736", "ds_vip_18736");
        put("32530", "ds_vip_32530");
        put("36234", "ds_vip_36234");
        put("38111", "ds_vip_38111");
        put("37788", "ds_vip_37788");
        put("39663", "ds_vip_39663");
        put("60084", "ds_vip_60084");
        put("14787", "ds_vip_14787");
        put("36866", "ds_vip_36866");
        put("95191", "ds_vip_95191");
        put("53991", "ds_vip_53991");
        put("99412", "ds_vip_99412");
        put("98805", "ds_vip_98805");
        put("96881", "ds_vip_96881");
        put("101691", "ds_vip_101691");
        put("86583", "ds_vip_86583");
        put("51900", "ds_vip_51900");
        put("78476", "ds_vip_78476");
        put("69808", "ds_vip_69808");
        put("99934", "ds_vip_99934");
        put("878", "ds_vip_878");
        put("12670", "ds_vip_12670");
        put("124593", "ds_vip_124593");
        put("59324", "ds_vip_59324");
        put("127643", "ds_vip_127643");
        put("84935", "ds_vip_84935");
        put("131932", "ds_vip_131932");
        put("131288", "ds_vip_131288");
        put("6891", "ds_vip_6891");
        put("220834", "ds_vip_220834");
    }};

    /**
     * ----高度关注-----
     * 新增数据源后,记得维护这个字段
     */
    private final static String DB_NODE = "['db_0','db_1','db_2','db_3','db_4','db_5','db_6','db_7'," +
            "'vip_6090','vip_8032','vip_18617','vip_16092','vip_14059','vip_16997','vip_19077','vip_19949'," +
            "'vip_12584','vip_21871','vip_18736','vip_32530','vip_36234','vip_38111','vip_37788','vip_39663','vip_60084', 'vip_14787', 'vip_36866', 'vip_95191', 'vip_53991', 'vip_99412', 'vip_98805', " +
            "'vip_96881','vip_101691', 'vip_86583', 'vip_51900', 'vip_78476', 'vip_69808', 'vip_99934', 'vip_878', 'vip_12670', 'vip_124593', 'vip_59324', 'vip_127643', 'vip_84935','vip_131932','vip_131288', " +
            "'vip_6891','vip_220834']";

    /**
     * 关联表解决union或join表路由问题
     * 比如表 a,b表使用了union或join查询,f
     * 则需要添加 Arrays.asList(a, b)
     */
    public final static List<List<String>> ASSOCIATION_LOGIC_TABLES = Arrays.asList(
            Arrays.asList("t_cpc_query_targeting_report", "t_cpc_query_keyword_report", "t_cpc_query_targeting_report_hot", "t_cpc_query_keyword_report_hot"),
            Arrays.asList("t_amazon_word_root_keyword_sp", "t_amazon_word_root_targeting_sp", "t_amazon_word_root_query"),
            Arrays.asList("t_amazon_word_root_keyword_sp", "t_amazon_word_root_targeting_sp", "t_cpc_query_keyword_report", "t_cpc_query_targeting_report"),
            Arrays.asList("t_amazon_ad_product_report", "t_amazon_ad_product_report_hot", "t_amazon_ad_product"),
            Arrays.asList("t_amazon_ad_keyword_report", "t_amazon_ad_keyword_report_hot", "t_amazon_ad_keyword","t_cpc_targeting_report","t_cpc_targeting_report_hot", "t_amazon_ad_targeting"),
            Arrays.asList("t_amazon_ad_campaign_all_report", "t_amazon_ad_campaign_all_report_hot", "t_amazon_ad_campaign_all", "t_amazon_ad_gross_and_invalid_traffic_all_report", "t_amazon_ad_campaign_placement_report", "t_amazon_ad_placement_amazon_business_report"),
            Arrays.asList("t_amazon_ad_sb_keyword_report", "t_amazon_ad_sb_keyword_report_hot", "t_amazon_ad_keyword_sb"),
            Arrays.asList("t_amazon_ad_group_report", "t_amazon_ad_group_report_hot", "t_amazon_ad_group"),
            Arrays.asList("t_amazon_ad_sb_targeting_report", "t_amazon_ad_sb_targeting_report_hot", "t_amazon_ad_sb_keyword_report", "t_amazon_ad_sb_keyword_report_hot"),
            Arrays.asList("t_cpc_sb_query_keyword_report", "t_cpc_sb_query_keyword_report_hot",
                    "t_cpc_query_keyword_report", "t_cpc_query_keyword_report_hot",
                    "t_cpc_query_targeting_report", "t_cpc_query_targeting_report_hot",
                    "t_amazon_ad_keyword_sb", "t_ad_auto_rule_status", "t_amazon_ad_targeting", "t_amazon_ad_keyword"),
            Arrays.asList("t_amazon_ad_ne_targeting", "t_amazon_ad_netargeting_sb", "t_amazon_ad_netargeting_sd"),
            Arrays.asList("t_amazon_ad_ne_keyword", "t_amazon_ad_nekeyword_sb", "t_amazon_ad_campaign_nekeywords")
    );

    /**
     * 解决hot表路由问题
     * 比如表a有hot表，即a表和a_hot表，共用同一个dao，那么此处需要配置a_hot表
     * ！！不配置表路由会有问题，路由参考：BaseShardingSphereDaoImpl#getJdbcTemplate
     * ！！如果hot表加入了union或join的配置：ASSOCIATION_LOGIC_TABLES，则此处可以不用配置
     */
    public final static List<String> ASSOCIATION_HOT_LOGIC_TABLES = Arrays.asList(
            "t_amazon_ad_product_aggregation_report_hot"
    );

    // 为 adDb 配置事务管理器
    @Bean(name = "adTransactionManager")
    public PlatformTransactionManager adTransactionManager(
            @Qualifier("adDb") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }


    @Bean(name = "db",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties(value = "spring.datasource.druid.db")
    public DataSource dataSource() {
        if ((active.equals("alpha") || active.equals("prod")) && !isShard) {
            log.info("===================线上环境必须配置数据库分片===================");
            System.exit(-1);
        }
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "slaveDb",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties(value = "spring.datasource.druid.slave-db")
    public DataSource slaveDataSource() {
        if ((active.equals("alpha") || active.equals("prod")) && !isShard) {
            log.info("===================线上环境必须配置数据库分片===================");
            System.exit(-1);
        }
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "adDb",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties(value = "spring.datasource.druid.ad-db")
    public DataSource adDataSource() {
        if ((active.equals("alpha") || active.equals("prod")) && !isShard) {
            log.info("===================线上环境必须配置数据库分片===================");
            System.exit(-1);
        }
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "db0",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties(value = "spring.datasource.druid.db0")
    @Primary
    public DataSource dataSource0() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "db1",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.db1")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource1() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "db2",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.db2")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource2() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(name = "db3",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.db3")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource3() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "db4",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties(value = "spring.datasource.druid.db4")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource4() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "db5",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.db5")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource5() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "db6",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.db6")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource6() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(name = "db7",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.db7")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSource7() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(name = "vip6090",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip6090")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip6090() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip8032",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip8032")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip8032() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip18617",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip18617")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip18617() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip16092",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip16092")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip16092() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip14059",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip14059")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip14059() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip16997",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip16997")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip16997() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip19077",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip19077")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip19077() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip19949",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip19949")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip19949() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip12584",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip12584")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip12584() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(name = "vip21871",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip21871")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip21871() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip18736",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip18736")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip18736() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip32530",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip32530")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip32530() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip36234",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip36234")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip36234() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip38111",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip38111")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip38111() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip37788",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip37788")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip37788() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip39663",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip39663")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip39663() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip60084",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip60084")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip60084() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip14787",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip14787")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip14787() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip36866",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip36866")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip36866() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip95191",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip95191")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip95191() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip53991",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip53991")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip53991() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip99412",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip99412")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip99412() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip98805",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip98805")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip98805() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip96881",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip96881")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip96881() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip101691",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip101691")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip101691() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip86583",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip86583")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip86583() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip51900",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip51900")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip51900() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip78476",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip78476")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip78476() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip69808",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip69808")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip69808() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip99934",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip99934")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip99934() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip878",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip878")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip878() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(name = "vip12670",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip12670")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip12670() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip124593",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip124593")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip124593() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip59324",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip59324")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip59324() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip127643",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip127643")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip127643() {
        return DruidDataSourceBuilder.create().build();
    }


    @Bean(name = "vip84935",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip84935")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip84935() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip131932",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip131932")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip131932() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip131288",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip131288")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip131288() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip6891",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip6891")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip6891() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "vip220834",initMethod = "init",destroyMethod = "close")
    @ConfigurationProperties("spring.datasource.druid.vip220834")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public DataSource dataSourceVip220834() {
        return DruidDataSourceBuilder.create().build();
    }

    @Bean(name = "jdbcTemplate")
    public JdbcTemplate jdbcTemplate(
            @Qualifier("db") DataSource db) {
        return new JdbcTemplate(db);
    }

    @Bean(name = "jdbcTemplateSlaveDb")
    public JdbcTemplate jdbcTemplateSlaveDb(
            @Qualifier("slaveDb") DataSource db) {
        return new JdbcTemplate(db);
    }

    @Bean(name = "jdbcTemplateAdDb")
    public JdbcTemplate jdbcTemplateAdDb(
            @Qualifier("adDb") DataSource db) {
        return new JdbcTemplate(db);
    }

    @Bean(name = "jdbcTemplate0")
    public JdbcTemplate jdbcTemplate0(
            @Qualifier("db0") DataSource db0) {
        return new JdbcTemplate(db0);
    }

    @Bean(name = "jdbcTemplate1")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate1(
            @Qualifier("db1") DataSource db1) {
        return new JdbcTemplate(db1);
    }

    @Bean(name = "jdbcTemplate2")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate2(
            @Qualifier("db2") DataSource db2) {
        return new JdbcTemplate(db2);
    }

    @Bean(name = "jdbcTemplate3")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate3(
            @Qualifier("db3") DataSource db3) {
        return new JdbcTemplate(db3);
    }

    @Bean(name = "jdbcTemplate4")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate4(
            @Qualifier("db4") DataSource db4) {
        return new JdbcTemplate(db4);
    }

    @Bean(name = "jdbcTemplate5")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate5(
            @Qualifier("db5") DataSource db5) {
        return new JdbcTemplate(db5);
    }

    @Bean(name = "jdbcTemplate6")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate6(
            @Qualifier("db6") DataSource db6) {
        return new JdbcTemplate(db6);
    }

    @Bean(name = "jdbcTemplate7")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplate7(
            @Qualifier("db7") DataSource db7) {
        return new JdbcTemplate(db7);
    }

    @Bean(name = "jdbcTemplateVip6090")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip6090(
            @Qualifier("vip6090") DataSource vip6090) {
        return new JdbcTemplate(vip6090);
    }

    @Bean(name = "jdbcTemplateVip8032")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip8032(
            @Qualifier("vip8032") DataSource vip8032) {
        return new JdbcTemplate(vip8032);
    }

    @Bean(name = "jdbcTemplateVip18617")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip18617(
            @Qualifier("vip18617") DataSource vip18617) {
        return new JdbcTemplate(vip18617);
    }

    @Bean(name = "jdbcTemplateVip16092")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip16092(
            @Qualifier("vip16092") DataSource vip16092) {
        return new JdbcTemplate(vip16092);
    }

    @Bean(name = "jdbcTemplateVip14059")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip14059(
            @Qualifier("vip14059") DataSource vip14059) {
        return new JdbcTemplate(vip14059);
    }

    @Bean(name = "jdbcTemplateVip16997")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip16997(
            @Qualifier("vip16997") DataSource vip16997) {
        return new JdbcTemplate(vip16997);
    }

    @Bean(name = "jdbcTemplateVip19077")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip19077(
            @Qualifier("vip19077") DataSource vip19077) {
        return new JdbcTemplate(vip19077);
    }

    @Bean(name = "jdbcTemplateVip19949")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip19949(
            @Qualifier("vip19949") DataSource vip19949) {
        return new JdbcTemplate(vip19949);
    }

    @Bean(name = "jdbcTemplateVip12584")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip12584(
            @Qualifier("vip12584") DataSource vip12584) {
        return new JdbcTemplate(vip12584);
    }


    @Bean(name = "jdbcTemplateVip21871")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip21871(
            @Qualifier("vip21871") DataSource vip21871) {
        return new JdbcTemplate(vip21871);
    }

    @Bean(name = "jdbcTemplateVip18736")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip18736(
            @Qualifier("vip18736") DataSource vip18736) {
        return new JdbcTemplate(vip18736);
    }

    @Bean(name = "jdbcTemplateVip32530")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip32530(
            @Qualifier("vip32530") DataSource vip32530) {
        return new JdbcTemplate(vip32530);
    }

    @Bean(name = "jdbcTemplateVip36234")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip36234(
            @Qualifier("vip36234") DataSource vip36234) {
        return new JdbcTemplate(vip36234);
    }

    @Bean(name = "jdbcTemplateVip38111")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip38111(
            @Qualifier("vip38111") DataSource vip38111) {
        return new JdbcTemplate(vip38111);
    }

    @Bean(name = "jdbcTemplateVip37788")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip37788(
            @Qualifier("vip37788") DataSource vip37788) {
        return new JdbcTemplate(vip37788);
    }

    @Bean(name = "jdbcTemplateVip39663")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip39663(
            @Qualifier("vip39663") DataSource vip39663) {
        return new JdbcTemplate(vip39663);
    }

    @Bean(name = "jdbcTemplateVip60084")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip60084(
            @Qualifier("vip60084") DataSource vip60084) {
        return new JdbcTemplate(vip60084);
    }

    @Bean(name = "jdbcTemplateVip14787")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip14787(
            @Qualifier("vip14787") DataSource vip14787) {
        return new JdbcTemplate(vip14787);
    }

    @Bean(name = "jdbcTemplateVip36866")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip36866(
            @Qualifier("vip36866") DataSource vip36866) {
        return new JdbcTemplate(vip36866);
    }

    @Bean(name = "jdbcTemplateVip95191")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip95191(
            @Qualifier("vip95191") DataSource vip95191) {
        return new JdbcTemplate(vip95191);
    }

    @Bean(name = "jdbcTemplateVip53991")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip53991(
            @Qualifier("vip53991") DataSource vip53991) {
        return new JdbcTemplate(vip53991);
    }

    @Bean(name = "jdbcTemplateVip99412")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip99412(
            @Qualifier("vip99412") DataSource vip99412) {
        return new JdbcTemplate(vip99412);
    }

    @Bean(name = "jdbcTemplateVip98805")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip98805(
            @Qualifier("vip98805") DataSource vip98805) {
        return new JdbcTemplate(vip98805);
    }

    @Bean(name = "jdbcTemplateVip96881")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip96881(
            @Qualifier("vip96881") DataSource vip96881) {
        return new JdbcTemplate(vip96881);
    }

    @Bean(name = "jdbcTemplateVip101691")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip101691(
            @Qualifier("vip101691") DataSource vip101691) {
        return new JdbcTemplate(vip101691);
    }

    @Bean(name = "jdbcTemplateVip86583")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip86583(
            @Qualifier("vip86583") DataSource vip86583) {
        return new JdbcTemplate(vip86583);
    }

    @Bean(name = "jdbcTemplateVip51900")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip51900(
            @Qualifier("vip51900") DataSource vip51900) {
        return new JdbcTemplate(vip51900);
    }

    @Bean(name = "jdbcTemplateVip78476")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip78476(
            @Qualifier("vip78476") DataSource vip78476) {
        return new JdbcTemplate(vip78476);
    }

    @Bean(name = "jdbcTemplateVip69808")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip69808(
            @Qualifier("vip69808") DataSource vip69808) {
        return new JdbcTemplate(vip69808);
    }

    @Bean(name = "jdbcTemplateVip99934")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip99934(
            @Qualifier("vip99934") DataSource vip99934) {
        return new JdbcTemplate(vip99934);
    }

    @Bean(name = "jdbcTemplateVip878")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip878(
            @Qualifier("vip878") DataSource vip878) {
        return new JdbcTemplate(vip878);
    }

    @Bean(name = "jdbcTemplateVip12670")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip12670(
            @Qualifier("vip12670") DataSource vip12670) {
        return new JdbcTemplate(vip12670);
    }

    @Bean(name = "jdbcTemplateVip124593")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip124593(
            @Qualifier("vip124593") DataSource vip124593) {
        return new JdbcTemplate(vip124593);
    }

    @Bean(name = "jdbcTemplateVip59324")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip59324(
            @Qualifier("vip59324") DataSource vip59324) {
        return new JdbcTemplate(vip59324);
    }

    @Bean(name = "jdbcTemplateVip127643")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip127643(
            @Qualifier("vip127643") DataSource vip127643) {
        return new JdbcTemplate(vip127643);
    }

    @Bean(name = "jdbcTemplateVip84935")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip84935(
            @Qualifier("vip84935") DataSource vip84935) {
        return new JdbcTemplate(vip84935);
    }

    @Bean(name = "jdbcTemplateVip131932")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip131932(
            @Qualifier("vip131932") DataSource vip131932) {
        return new JdbcTemplate(vip131932);
    }

    @Bean(name = "jdbcTemplateVip131288")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip131288(
            @Qualifier("vip131288") DataSource vip131288) {
        return new JdbcTemplate(vip131288);
    }

    @Bean(name = "jdbcTemplateVip6891")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip6891(
            @Qualifier("vip6891") DataSource vip6891) {
        return new JdbcTemplate(vip6891);
    }

    @Bean(name = "jdbcTemplateVip220834")
    @ConditionalOnProperty(value = "datasource.is-shard", havingValue = "true")
    public JdbcTemplate jdbcTemplateVip220834(
            @Qualifier("vip220834") DataSource vip220834) {
        return new JdbcTemplate(vip220834);
    }


    @Autowired(required = false)
    @Qualifier("jdbcTemplate0")
    private JdbcTemplate jdbcTemplate0;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate1")
    private JdbcTemplate jdbcTemplate1;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate2")
    private JdbcTemplate jdbcTemplate2;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate3")
    private JdbcTemplate jdbcTemplate3;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate4")
    private JdbcTemplate jdbcTemplate4;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate5")
    private JdbcTemplate jdbcTemplate5;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate6")
    private JdbcTemplate jdbcTemplate6;

    @Autowired(required = false)
    @Qualifier("jdbcTemplate7")
    private JdbcTemplate jdbcTemplate7;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip6090")
    private JdbcTemplate jdbcTemplateVip6090;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip8032")
    private JdbcTemplate jdbcTemplateVip8032;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip18617")
    private JdbcTemplate jdbcTemplateVip18617;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip16092")
    private JdbcTemplate jdbcTemplateVip16092;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip14059")
    private JdbcTemplate jdbcTemplateVip14059;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip16997")
    private JdbcTemplate jdbcTemplateVip16997;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip19077")
    private JdbcTemplate jdbcTemplateVip19077;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip19949")
    private JdbcTemplate jdbcTemplateVip19949;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip12584")
    private JdbcTemplate jdbcTemplateVip12584;


    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip21871")
    private JdbcTemplate jdbcTemplateVip21871;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip18736")
    private JdbcTemplate jdbcTemplateVip18736;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip32530")
    private JdbcTemplate jdbcTemplateVip32530;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip36234")
    private JdbcTemplate jdbcTemplateVip36234;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip38111")
    private JdbcTemplate jdbcTemplateVip38111;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip37788")
    private JdbcTemplate jdbcTemplateVip37788;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip39663")
    private JdbcTemplate jdbcTemplateVip39663;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip60084")
    private JdbcTemplate jdbcTemplateVip60084;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip14787")
    private JdbcTemplate jdbcTemplateVip14787;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip36866")
    private JdbcTemplate jdbcTemplateVip36866;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip95191")
    private JdbcTemplate jdbcTemplateVip95191;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip53991")
    private JdbcTemplate jdbcTemplateVip53991;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip99412")
    private JdbcTemplate jdbcTemplateVip99412;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip98805")
    private JdbcTemplate jdbcTemplateVip98805;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip96881")
    private JdbcTemplate jdbcTemplateVip96881;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip101691")
    private JdbcTemplate jdbcTemplateVip101691;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip86583")
    private JdbcTemplate jdbcTemplateVip86583;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip51900")
    private JdbcTemplate jdbcTemplateVip51900;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip78476")
    private JdbcTemplate jdbcTemplateVip78476;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip69808")
    private JdbcTemplate jdbcTemplateVip69808;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip99934")
    private JdbcTemplate jdbcTemplateVip99934;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip878")
    private JdbcTemplate jdbcTemplateVip878;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip12670")
    private JdbcTemplate jdbcTemplateVip12670;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip124593")
    private JdbcTemplate jdbcTemplateVip124593;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip59324")
    private JdbcTemplate jdbcTemplateVip59324;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip127643")
    private JdbcTemplate jdbcTemplateVip127643;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip84935")
    private JdbcTemplate jdbcTemplateVip84935;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip131932")
    private JdbcTemplate jdbcTemplateVip131932;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip131288")
    private JdbcTemplate jdbcTemplateVip131288;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip6891")
    private JdbcTemplate jdbcTemplateVip6891;

    @Autowired(required = false)
    @Qualifier("jdbcTemplateVip220834")
    private JdbcTemplate jdbcTemplateVip220834;


    @Bean(name = "shardingJdbcMap")
    public Map<String, JdbcTemplate> shardingJdbcMap() {
        Map<String, JdbcTemplate> map = new HashMap<>(16);
        map.put("db0", jdbcTemplate0);
        map.put("db1", isShard ? jdbcTemplate1 : jdbcTemplate0);
        map.put("db2", isShard ? jdbcTemplate2 : jdbcTemplate0);
        map.put("db3", isShard ? jdbcTemplate3 : jdbcTemplate0);
        map.put("db4", isShard ? jdbcTemplate4 : jdbcTemplate0);
        map.put("db5", isShard ? jdbcTemplate5 : jdbcTemplate0);
        map.put("db6", isShard ? jdbcTemplate6 : jdbcTemplate0);
        map.put("db7", isShard ? jdbcTemplate7 : jdbcTemplate0);
        return map;
    }

    @Bean(name = "vipJdbcTemplateMap")
    public Map<String, JdbcTemplate> vipJdbcTemplateMap() {
        Map<String, JdbcTemplate> map = new HashMap<>(64);
        map.put("vip6090", isShard ?  jdbcTemplateVip6090 : jdbcTemplate0);
        map.put("vip8032", isShard ? jdbcTemplateVip8032 : jdbcTemplate0);
        map.put("vip18617", isShard ? jdbcTemplateVip18617 : jdbcTemplate0);
        map.put("vip16092", isShard ? jdbcTemplateVip16092 : jdbcTemplate0);
        map.put("vip14059", isShard ? jdbcTemplateVip14059 : jdbcTemplate0);
        map.put("vip16997", isShard ? jdbcTemplateVip16997 : jdbcTemplate0);
        map.put("vip19077", isShard ? jdbcTemplateVip19077 : jdbcTemplate0);
        map.put("vip19949", isShard ? jdbcTemplateVip19949 : jdbcTemplate0);
        map.put("vip12584", isShard ? jdbcTemplateVip12584 : jdbcTemplate0);
        map.put("vip21871", isShard ? jdbcTemplateVip21871 : jdbcTemplate0);
        map.put("vip18736", isShard ? jdbcTemplateVip18736 : jdbcTemplate0);
        map.put("vip32530", isShard ? jdbcTemplateVip32530 : jdbcTemplate0);
        map.put("vip36234", isShard ? jdbcTemplateVip36234 : jdbcTemplate0);
        map.put("vip38111", isShard ? jdbcTemplateVip38111 : jdbcTemplate0);
        map.put("vip37788", isShard ? jdbcTemplateVip37788 : jdbcTemplate0);
        map.put("vip39663", isShard ? jdbcTemplateVip39663 : jdbcTemplate0);
        map.put("vip60084", isShard ? jdbcTemplateVip60084 : jdbcTemplate0);
        map.put("vip14787", isShard ? jdbcTemplateVip14787 : jdbcTemplate0);
        map.put("vip36866", isShard ? jdbcTemplateVip36866 : jdbcTemplate0);
        map.put("vip95191", isShard ? jdbcTemplateVip95191 : jdbcTemplate0);
        map.put("vip53991", isShard ? jdbcTemplateVip53991 : jdbcTemplate0);
        map.put("vip99412", isShard ? jdbcTemplateVip99412 : jdbcTemplate0);
        map.put("vip98805", isShard ? jdbcTemplateVip98805 : jdbcTemplate0);
        map.put("vip96881", isShard ? jdbcTemplateVip96881 : jdbcTemplate0);
        map.put("vip101691", isShard ? jdbcTemplateVip101691 : jdbcTemplate0);
        map.put("vip86583", isShard ? jdbcTemplateVip86583 : jdbcTemplate0);
        map.put("vip51900", isShard ? jdbcTemplateVip51900 : jdbcTemplate0);
        map.put("vip78476", isShard ? jdbcTemplateVip78476 : jdbcTemplate0);
        map.put("vip69808", isShard ? jdbcTemplateVip69808 : jdbcTemplate0);
        map.put("vip99934", isShard ? jdbcTemplateVip99934 : jdbcTemplate0);
        map.put("vip878", isShard ? jdbcTemplateVip878 : jdbcTemplate0);
        map.put("vip12670", isShard ? jdbcTemplateVip12670 : jdbcTemplate0);
        map.put("vip124593", isShard ? jdbcTemplateVip124593 : jdbcTemplate0);
        map.put("vip59324", isShard ? jdbcTemplateVip59324 : jdbcTemplate0);
        map.put("vip127643", isShard ? jdbcTemplateVip127643 : jdbcTemplate0);
        map.put("vip84935", isShard ? jdbcTemplateVip84935 : jdbcTemplate0);
        map.put("vip131932", isShard ? jdbcTemplateVip131932 : jdbcTemplate0);
        map.put("vip131288", isShard ? jdbcTemplateVip131288 : jdbcTemplate0);
        map.put("vip6891", isShard ? jdbcTemplateVip6891 : jdbcTemplate0);
        map.put("vip220834", isShard ? jdbcTemplateVip220834 : jdbcTemplate0);
        return map;
    }



    /**
     * 获取真实表节点的方法
     * @return
     */
    private String getActuallyTableNode() {
        //None表示未拆节
        if (!"None".equalsIgnoreCase(tableNodeRule)) {
            return "_${" + tableNodeRule + "}";
        } else {
            return "";
        }
    }



    /**
     * 获取真实表节点的方法
     * @return
     */
    private String getActuallyTable128Node() {
        //None表示未拆节
        if (!"None".equalsIgnoreCase(table128NodeRule)) {
            return "_${" + table128NodeRule + "}";
        } else {
            return "";
        }
    }

    /**
     * ===================shardingSphere分表规则开始=======================
     * 分表的具体的思路是 1.把报告拆分为32张表,配置分表规则
     *                2.与报告有join或union语句联名查询的管理数据表也需要配置规则,但是不拆表,为了兼容旧sql
     */

    private ShardingTableRuleConfiguration getSpCpcTargetingReportTableRuleConfig() {
        String tableName = "t_cpc_targeting_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpCpcTargetingReportHotTableRuleConfig() {
        String tableName = "t_cpc_targeting_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpCpcTargetingTableRuleConfig() {
        String tableName = "t_amazon_ad_targeting";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTable128Node());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTable128Node())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-128-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAllCampaignReportHotTableRuleConfig() {
        String tableName = "t_amazon_ad_campaign_all_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getWordRootKeywordSpConfig() {
        String tableName = "t_amazon_word_root_keyword_sp";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getWordRootQueryConfig() {
        String tableName = "t_amazon_word_root_query";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getWordRootKeywordSbConfig() {
        String tableName = "t_amazon_word_root_keyword_sb";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getWordRootTargetingSpConfig() {
        String tableName = "t_amazon_word_root_targeting_sp";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getKeywordWordRootKeywordSpConfig() {
        String tableName = "t_amazon_ad_word_root_keyword_sp";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getKeywordWordRootKeywordSbConfig() {
        String tableName = "t_amazon_ad_word_root_keyword_sb";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }


    private ShardingTableRuleConfiguration getSpAllCampaignReportTableRuleConfig() {
        String tableName = "t_amazon_ad_campaign_all_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAPlacementCampaignReportTableRuleConfig() {
        String tableName = "t_amazon_ad_campaign_placement_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getAdPlacementAmazonBusinessTableRuleConfig() {
        String tableName = "t_amazon_ad_placement_amazon_business_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getAllGrossAndInvalidTrafficReportTableRuleConfig() {
        String tableName = "t_amazon_ad_gross_and_invalid_traffic_all_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAllCampaignTableRuleConfig() {
        String tableName = "t_amazon_ad_campaign_all";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAdGroupReportTableRuleConfig() {
        String tableName = "t_amazon_ad_group_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAdGroupReportHotTableRuleConfig() {
        String tableName = "t_amazon_ad_group_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAdGroupTableRuleConfig() {
        String tableName = "t_amazon_ad_group";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbAdKeywordReportTableRuleConfig() {
        String tableName = "t_amazon_ad_sb_keyword_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbAdKeywordReportHotTableRuleConfig() {
        String tableName = "t_amazon_ad_sb_keyword_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbAdKeywordTableRuleConfig() {
        String tableName = "t_amazon_ad_keyword_sb";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbAdTargetingReportTableRuleConfig() {
        String tableName = "t_amazon_ad_sb_targeting_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbAdTargetingReportHotTableRuleConfig() {
        String tableName = "t_amazon_ad_sb_targeting_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getAdAutoRuleStatusConfig() {
        String tableName = "t_ad_auto_rule_status";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }



    private ShardingTableRuleConfiguration getSbQueryKeywordReportConfig() {
        String tableName = "t_cpc_sb_query_keyword_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbQueryKeywordReportHotConfig() {
        String tableName = "t_cpc_sb_query_keyword_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getBudgetUsageTableRuleConfig() {
        String tableName = "t_amazon_ad_budget_usage";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpKeywordReportTableRuleConfig() {
        String tableName = "t_amazon_ad_keyword_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpKeywordReportHotTableRuleConfig() {
        String tableName = "t_amazon_ad_keyword_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpKeywordTableRuleConfig() {
        String tableName = "t_amazon_ad_keyword";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTable128Node());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTable128Node())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-128-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpNeKeywordTableRuleConfig() {
        String tableName = "t_amazon_ad_ne_keyword";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTable128Node());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTable128Node())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-128-TABLE"));
        }
        return tableRuleConfiguration;
    }


    private ShardingTableRuleConfiguration getSpNeTargetingTableRuleConfig() {
        String tableName = "t_amazon_ad_ne_targeting";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTable128Node());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTable128Node())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-128-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAdProductReportTableRuleConfig() {
        String tableName = "t_amazon_ad_product_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAdProductReportHotTableRuleConfig() {
        String tableName = "t_amazon_ad_product_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpAdProductTableRuleConfig() {
        String tableName = "t_amazon_ad_product";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getAdProductAggregationTableRuleConfig() {
        String tableName = "t_amazon_ad_product_aggregation_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getAdProductAggregationHotTableRuleConfig() {
        String tableName = "t_amazon_ad_product_aggregation_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpKeywordQueryReportTableRuleConfig() {
        String tableName = "t_cpc_query_keyword_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpKeywordQueryReportHotTableRuleConfig() {
        String tableName = "t_cpc_query_keyword_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpTargetQueryReportTableRuleConfig() {
        String tableName = "t_cpc_query_targeting_report";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpTargetQueryReportHotTableRuleConfig() {
        String tableName = "t_cpc_query_targeting_report_hot";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }


    private ShardingTableRuleConfiguration getSbAdNeKeywordTableRuleConfig() {
        String tableName = "t_amazon_ad_nekeyword_sb";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getCampaignNekeywordsTableRuleConfig() {
        String tableName = "t_amazon_ad_campaign_nekeywords";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSbAdNeTargetingTableRuleConfig() {
        String tableName = "t_amazon_ad_netargeting_sb";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSdAdNeTargetingTableRuleConfig() {
        String tableName = "t_amazon_ad_netargeting_sd";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName);
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpKeywordExtendTableRuleConfig() {
        String tableName = "t_amazon_ad_keyword_extend";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    private ShardingTableRuleConfiguration getSpTargetingExtendTableRuleConfig() {
        String tableName = "t_amazon_ad_targeting_extend";
        ShardingTableRuleConfiguration tableRuleConfiguration =
                new ShardingTableRuleConfiguration(tableName, "ds_${" + DB_NODE + "}." + tableName + getActuallyTableNode());
        tableRuleConfiguration.setDatabaseShardingStrategy(new HintShardingStrategyConfiguration("HINT"));
        if (StringUtils.isNotBlank(getActuallyTableNode())) {
            tableRuleConfiguration.setTableShardingStrategy(new HintShardingStrategyConfiguration("HINT-TABLE"));
        }
        return tableRuleConfiguration;
    }

    /**
     * shardingSphere配置
     */
    private ShardingRuleConfiguration getShardingRuleConfiguration() {
        //配置所有分表规则
        ShardingRuleConfiguration shardingRuleConfiguration = new ShardingRuleConfiguration();
        List<ShardingTableRuleConfiguration> list = new ArrayList<>();

        list.add(getSpAdProductReportTableRuleConfig());
        list.add(getSpAdProductTableRuleConfig());

        list.add(getSpKeywordReportTableRuleConfig());
        list.add(getSpKeywordTableRuleConfig());

        list.add(getSpKeywordQueryReportTableRuleConfig());
        list.add(getSpTargetQueryReportTableRuleConfig());
        list.add(getAdProductAggregationTableRuleConfig());

        list.add(getSpCpcTargetingReportTableRuleConfig());
        list.add(getSpCpcTargetingTableRuleConfig());
        list.add(getSpAllCampaignReportTableRuleConfig());
        list.add(getSpAPlacementCampaignReportTableRuleConfig());
        list.add(getAdPlacementAmazonBusinessTableRuleConfig());
        list.add(getSpAllCampaignTableRuleConfig());
        list.add(getSpAdGroupReportTableRuleConfig());
        list.add(getSpAdGroupTableRuleConfig());
        list.add(getSbAdKeywordReportTableRuleConfig());
        list.add(getSbAdKeywordTableRuleConfig());
        list.add(getBudgetUsageTableRuleConfig());
        list.add(getSbAdTargetingReportTableRuleConfig());

        list.add(getSpAllCampaignReportHotTableRuleConfig());
        list.add(getSpAdProductReportHotTableRuleConfig());
        list.add(getSpKeywordReportHotTableRuleConfig());
        list.add(getAdProductAggregationHotTableRuleConfig());
        list.add(getSpKeywordQueryReportHotTableRuleConfig());
        list.add(getSpTargetQueryReportHotTableRuleConfig());
        list.add(getSpCpcTargetingReportHotTableRuleConfig());
        list.add(getSpAdGroupReportHotTableRuleConfig());
        list.add(getSbAdKeywordReportHotTableRuleConfig());
        list.add(getSbAdTargetingReportHotTableRuleConfig());
        list.add(getSbQueryKeywordReportConfig());
        list.add(getSbQueryKeywordReportHotConfig());
        list.add(getAdAutoRuleStatusConfig());
        list.add(getWordRootKeywordSpConfig());
        list.add(getWordRootQueryConfig());
        list.add(getWordRootKeywordSbConfig());
        list.add(getWordRootTargetingSpConfig());
        list.add(getKeywordWordRootKeywordSpConfig());
        list.add(getKeywordWordRootKeywordSbConfig());
        list.add(getSpNeKeywordTableRuleConfig());
        list.add(getSpNeTargetingTableRuleConfig());
        list.add(getSbAdNeKeywordTableRuleConfig());
        list.add(getSbAdNeTargetingTableRuleConfig());
        list.add(getSdAdNeTargetingTableRuleConfig());
        list.add(getAllGrossAndInvalidTrafficReportTableRuleConfig());
        list.add(getCampaignNekeywordsTableRuleConfig());
        list.add(getSpKeywordExtendTableRuleConfig());
        list.add(getSpTargetingExtendTableRuleConfig());
        shardingRuleConfiguration.setTables(list);

        //配置算法: 分库和分表都走hint算法,解析sql只把逻辑表名解析为实际表名
        Map<String, ShardingSphereAlgorithmConfiguration> algorithmMap = new HashMap<>();
        Properties dbProperties = new Properties();
        dbProperties.setProperty("strategy", "hint");
        dbProperties.setProperty("algorithmClassName", "com.meiyunji.sponsored.common.shardingSphere.MyHintShardingAlgorithm");
        dbProperties.setProperty("config-file", databaseConfigFile);
        ShardingSphereAlgorithmConfiguration databaseAlgorithmConfig = new ShardingSphereAlgorithmConfiguration("CLASS_BASED", dbProperties);
        algorithmMap.put("HINT", databaseAlgorithmConfig);

        Properties tableProperties = new Properties();
        tableProperties.setProperty("strategy", "hint");
        tableProperties.setProperty("algorithmClassName", "com.meiyunji.sponsored.common.shardingSphere.MyTableHintShardingAlgorithm");
        tableProperties.setProperty("config-file", tableConfigFile);
        ShardingSphereAlgorithmConfiguration tableAlgorithmConfig = new ShardingSphereAlgorithmConfiguration("CLASS_BASED", tableProperties);

        algorithmMap.put("HINT-TABLE", tableAlgorithmConfig);

        Properties table128Properties = new Properties();
        table128Properties.setProperty("strategy", "hint");
        table128Properties.setProperty("algorithmClassName", "com.meiyunji.sponsored.common.shardingSphere.MyTableHintShardingAlgorithm");
        table128Properties.setProperty("config-file", table128ConfigFile);
        ShardingSphereAlgorithmConfiguration table128AlgorithmConfig = new ShardingSphereAlgorithmConfiguration("CLASS_BASED", table128Properties);

        algorithmMap.put("HINT-128-TABLE", table128AlgorithmConfig);

        shardingRuleConfiguration.setShardingAlgorithms(algorithmMap);
        shardingRuleConfiguration.setDefaultTableShardingStrategy(new NoneShardingStrategyConfiguration());
        return shardingRuleConfiguration;

    }

    /**
     * shardingSphere数据源配置
     *
     * @return
     */
    @Bean("shardingSphereDataSource")
    public DataSource shardingSphereDataSource() throws SQLException {
        Map<String, DataSource> dataSourceMap = new HashMap<>();
        dataSourceMap.put("ds_db_0", dataSource0());
        dataSourceMap.put("ds_db_1", isShard ? dataSource1() : dataSource0());
        dataSourceMap.put("ds_db_2", isShard ? dataSource2() : dataSource0());
        dataSourceMap.put("ds_db_3", isShard ? dataSource3() : dataSource0());
        dataSourceMap.put("ds_db_4", isShard ? dataSource4() : dataSource0());
        dataSourceMap.put("ds_db_5", isShard ? dataSource5() : dataSource0());
        dataSourceMap.put("ds_db_6", isShard ? dataSource6() : dataSource0());
        dataSourceMap.put("ds_db_7", isShard ? dataSource7() : dataSource0());
        //vip
        dataSourceMap.put("ds_vip_6090", isShard ? dataSourceVip6090() : dataSource0());
        dataSourceMap.put("ds_vip_8032", isShard ? dataSourceVip8032() : dataSource0());
        dataSourceMap.put("ds_vip_18617", isShard ? dataSourceVip18617() : dataSource0());
        dataSourceMap.put("ds_vip_16092", isShard ? dataSourceVip16092() : dataSource0());
        dataSourceMap.put("ds_vip_14059", isShard ? dataSourceVip14059() : dataSource0());
        dataSourceMap.put("ds_vip_16997", isShard ? dataSourceVip16997() : dataSource0());
        dataSourceMap.put("ds_vip_19077", isShard ? dataSourceVip19077() : dataSource0());
        dataSourceMap.put("ds_vip_19949", isShard ? dataSourceVip19949() : dataSource0());
        dataSourceMap.put("ds_vip_12584", isShard ? dataSourceVip12584() : dataSource0());
        dataSourceMap.put("ds_vip_21871", isShard ? dataSourceVip21871() : dataSource0());
        dataSourceMap.put("ds_vip_18736", isShard ? dataSourceVip18736() : dataSource0());
        dataSourceMap.put("ds_vip_32530", isShard ? dataSourceVip32530() : dataSource0());
        dataSourceMap.put("ds_vip_36234", isShard ? dataSourceVip36234() : dataSource0());
        dataSourceMap.put("ds_vip_38111", isShard ? dataSourceVip38111() : dataSource0());
        dataSourceMap.put("ds_vip_37788", isShard ? dataSourceVip37788() : dataSource0());
        dataSourceMap.put("ds_vip_39663", isShard ? dataSourceVip39663() : dataSource0());
        dataSourceMap.put("ds_vip_60084", isShard ? dataSourceVip60084() : dataSource0());
        dataSourceMap.put("ds_vip_14787", isShard ? dataSourceVip14787(): dataSource0());
        dataSourceMap.put("ds_vip_36866", isShard ? dataSourceVip36866(): dataSource0());
        dataSourceMap.put("ds_vip_95191", isShard ? dataSourceVip95191(): dataSource0());
        dataSourceMap.put("ds_vip_53991", isShard ? dataSourceVip53991(): dataSource0());
        dataSourceMap.put("ds_vip_99412", isShard ? dataSourceVip99412(): dataSource0());
        dataSourceMap.put("ds_vip_98805", isShard ? dataSourceVip98805(): dataSource0());
        dataSourceMap.put("ds_vip_96881", isShard ? dataSourceVip96881(): dataSource0());
        dataSourceMap.put("ds_vip_101691", isShard ? dataSourceVip101691(): dataSource0());
        dataSourceMap.put("ds_vip_86583", isShard ? dataSourceVip86583(): dataSource0());
        dataSourceMap.put("ds_vip_51900", isShard ? dataSourceVip51900(): dataSource0());
        dataSourceMap.put("ds_vip_78476", isShard ? dataSourceVip78476(): dataSource0());
        dataSourceMap.put("ds_vip_69808", isShard ? dataSourceVip69808(): dataSource0());
        dataSourceMap.put("ds_vip_99934", isShard ? dataSourceVip99934(): dataSource0());
        dataSourceMap.put("ds_vip_878", isShard ? dataSourceVip878(): dataSource0());
        dataSourceMap.put("ds_vip_12670", isShard ? dataSourceVip12670(): dataSource0());
        dataSourceMap.put("ds_vip_124593", isShard ? dataSourceVip124593(): dataSource0());
        dataSourceMap.put("ds_vip_59324", isShard ? dataSourceVip59324(): dataSource0());
        dataSourceMap.put("ds_vip_127643", isShard ? dataSourceVip127643(): dataSource0());
        dataSourceMap.put("ds_vip_84935", isShard ? dataSourceVip84935(): dataSource0());
        dataSourceMap.put("ds_vip_131932", isShard ? dataSourceVip131932(): dataSource0());
        dataSourceMap.put("ds_vip_131288", isShard ? dataSourceVip131288(): dataSource0());
        dataSourceMap.put("ds_vip_6891", isShard ? dataSourceVip6891(): dataSource0());
        dataSourceMap.put("ds_vip_220834", isShard ? dataSourceVip220834(): dataSource0());

        List<RuleConfiguration> ruleConfig = new ArrayList<>();
        ruleConfig.add(getShardingRuleConfiguration());

        Properties props = new Properties();
        //sql日志
        //props.setProperty("sql-show", "true");
        return ShardingSphereDataSourceFactory.createDataSource(dataSourceMap, ruleConfig, props);
    }

    /**
     * 把shardingSphere的数据源注入到JdbcTemplate, 保持和原系统一样写法
     *
     * @return
     */
    @Bean(name = "shardingSphereJdbcTemplate")
    public JdbcTemplate shardingSphereJdbcTemplate() throws SQLException {
        return new JdbcTemplate(shardingSphereDataSource());
    }

    //===================shardingSphere分表规则结束=======================


}
