package com.meiyunji.sponsored.common.base;

/**
 * 赛狐权限 resource. 此静态变量的值和 t_sellfox_right.resource 的值一一对应.
 *   所有二级resource不为空的 将会控制数据权限
 * heqiwen 2025/04/01
 */
public interface SellfoxRightResource {

    public static final String MOD_AD_POST__VIEW_LIST = "MOD_AD_POST.VIEW_LIST";         //广告 / 帖子 / 查看
    public static final String MOD_AD_POST__EDIT_DATA = "MOD_AD_POST.EDIT_DATA";          //广告 / 帖子 / 编辑
    public static final String MOD_AD_POST__EXPORT_DATA = "MOD_AD_POST.EXPORT_DATA";     //广告 / 帖子 / 导出


    public static final String MOD_AD_MANAGE__VIEW_LIST = "MOD_AD_MANAGE.VIEW_LIST";
}
