package com.meiyunji.sponsored.common.permission.annotation;

import com.meiyunji.sponsored.common.permission.enums.PermissionFilterStrategy;
import com.meiyunji.sponsored.common.permission.enums.PermissionFilterType;

import java.lang.annotation.*;

/**
 * 广告产品权限过滤注解
 * 统一处理所有类型的广告产品权限过滤（Campaign、ASIN等）
 * <AUTHOR>
 * @date 2025-01-27
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface AdProductPermissionFilter {

    /**
     * 权限过滤类型
     * 支持：CAMPAIGN（活动）、ASIN（产品）
     */
    PermissionFilterType type() default PermissionFilterType.CAMPAIGN;


    /**
     * 店铺ID字段表达式
     * 支持SpEL表达式，如 #reqParam.shopIds, #request.shopIds
     */
    String shopIdsExpression() default "";

    /**
     * 广告类型字段表达式（仅CAMPAIGN类型使用）
     * 支持SpEL表达式，如 #reqParam.adType
     * 支持的广告类型：sp, sb, sd
     */
    String adTypeExpression() default "";

    /**
     * 是否启用权限过滤
     */
    boolean enabled() default true;

    /**
     * 权限过滤策略
     */
    PermissionFilterStrategy strategy() default PermissionFilterStrategy.FILTER;

    /**
     * 自定义配置参数
     * 可以用于传递特定类型的额外配置，格式：key=value
     * 例如：["threshold=1000", "useCache=true"]
     */
    String[] customConfig() default {};

    /**
     * 描述信息（用于文档和调试）
     */
    String description() default "";
}
