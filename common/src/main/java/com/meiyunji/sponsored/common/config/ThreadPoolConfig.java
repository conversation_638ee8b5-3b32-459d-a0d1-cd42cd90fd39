package com.meiyunji.sponsored.common.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 线程池配置
 *
 * <AUTHOR>
 * @date 2024/09/29
 */
@Configuration
public class ThreadPoolConfig {

    @Bean
    public ThreadPoolTaskExecutor spCopyTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(16);
        executor.setMaxPoolSize(32);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(128);
        executor.setThreadNamePrefix("spCopyThreadPool - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor spBatchCreateTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(32);
        executor.setMaxPoolSize(64);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(256);
        executor.setThreadNamePrefix("spBatchCreateThreadPool - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor purchasedItemExportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(32);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(128);
        executor.setThreadNamePrefix("purchasedItemExportTaskExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }


    @Bean
    public ThreadPoolTaskExecutor walmartSyncManagementExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(16);
        executor.setMaxPoolSize(32);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(128);
        executor.setThreadNamePrefix("walmartSyncManagementThreadPool - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor perspectiveHourExportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(0);
        executor.setMaxPoolSize(64);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(256);
        executor.setThreadNamePrefix("perspectiveHourExportTaskExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor aggregationTargetDataExportTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(32);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(128);
        executor.setThreadNamePrefix("aggregationTargetDataExportTaskExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor adExportExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(32);
        executor.setMaxPoolSize(256);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(0);
        executor.setThreadNamePrefix("adExportExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor campaignAdPageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(32);
        executor.setMaxPoolSize(256);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(0);
        executor.setThreadNamePrefix("campaignAdPageExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor targetAdPageExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(32);
        executor.setMaxPoolSize(256);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(0);
        executor.setThreadNamePrefix("targetAdPageExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor tiktokCampaignSyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(16);
        executor.setMaxPoolSize(32);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(128);
        executor.setThreadNamePrefix("tiktokCampaignSyncExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }

    @Bean
    public ThreadPoolTaskExecutor tiktokAddOrUpdateCampaignSyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(16);
        executor.setMaxPoolSize(32);
        executor.setKeepAliveSeconds(6);
        executor.setQueueCapacity(128);
        executor.setThreadNamePrefix("tiktokAddOrUpdateCampaignSyncExecutor - ");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new TraceLogMdcTaskDecorator());
        return executor;
    }
}
