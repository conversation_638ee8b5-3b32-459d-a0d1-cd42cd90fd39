package com.meiyunji.sponsored.common.security;

/**
 * 赛狐权限 resource. 此静态变量的值和 t_sellfox_right.resource 的值一一对应.
 * <p>
 * 如何保证严格匹配? 每次修改 t_sellfox_resource 之后, 运行 SellfoxRightResourceTest 生成语句, 然后复制到这个类.
 * <p>
 * chenyunshi 2020/12/29
 */
public class SellfoxRightResource {

    public static final String PURCHASE_COST = "purchaseCost";              // 安全列 - 采购成本
    public static final String INVENTORY_COST = "inventoryCost";            // 安全列 - 库存成本
    public static final String OVERALL_OPERATION__GOODS_VIEW_ALL = "OVERALL_OPERATION.GOODS_VIEW_ALL"; //全局 - 全局控制权限-查看所有商品              // 安全列 - 采购成本

    public static final String MOD_DASHBOARD__VIEW = "MOD_DASHBOARD.VIEW";           // 首页 / 首页 / 查看
    public static final String MOD_COMMODITY__VIEW_LIST = "MOD_COMMODITY.VIEW_LIST";           // 商品 / 商品列表 / 查看
    public static final String MOD_COMMODITY__ADD_DATA = "MOD_COMMODITY.ADD_DATA";           // 商品 / 商品列表 / 添加商品
    public static final String MOD_COMMODITY__IMPORT_DATA = "MOD_COMMODITY.IMPORT_DATA";           // 商品 / 商品列表 / 导入商品
    public static final String MOD_COMMODITY__IMPORT_DXM_DATA = "MOD_COMMODITY.IMPORT_DXM_DATA";           // 商品 / 商品列表 / 导入店小秘商品
    public static final String MOD_COMMODITY__EXPORT_DATA = "MOD_COMMODITY.EXPORT_DATA";           // 商品 / 商品列表 / 导出

    public static final String MOD_COMMODITY__EDIT_DATA = "MOD_COMMODITY.EDIT_DATA";           // 商品 / 商品列表 / 编辑
    public static final String MOD_COMMODITY__DELETE_DATA = "MOD_COMMODITY.DELETE_DATA";           // 商品 / 商品列表 / 删除
    public static final String MOD_COMMODITY_CATEGORY__VIEW_LIST = "MOD_COMMODITY_CATEGORY.VIEW_LIST";           // 商品 / 商品分类 / 查看
    public static final String MOD_COMMODITY_CATEGORY__ADD_DATA = "MOD_COMMODITY_CATEGORY.ADD_DATA";           // 商品 / 商品分类 / 新增
    public static final String MOD_COMMODITY_CATEGORY__EDIT_DATA = "MOD_COMMODITY_CATEGORY.EDIT_DATA";           // 商品 / 商品分类 / 编辑

    public static final String MOD_COMMODITY_CATEGORY__DELETE_DATA = "MOD_COMMODITY_CATEGORY.DELETE_DATA";           // 商品 / 商品分类 / 删除
    public static final String MOD_PRODUCT__VIEW_LIST = "MOD_PRODUCT.VIEW_LIST";           // 销售 / 在线产品 / 查看
    public static final String MOD_PRODUCT__IMPORT_MATCH = "MOD_PRODUCT.IMPORT_MATCH";           // 销售 / 在线产品 / 导入配对
    public static final String MOD_PRODUCT__ASSIGN_SALES = "MOD_PRODUCT.ASSIGN_SALES";           // 销售 / 在线产品 / 分配业务员
    public static final String MOD_PRODUCT__SYNC_PRODUCT = "MOD_PRODUCT.SYNC_PRODUCT";           // 销售 / 在线产品 / 同步产品

    public static final String MOD_PRODUCT__TOGGLE_MATCH = "MOD_PRODUCT.TOGGLE_MATCH";           // 销售 / 在线产品 / 配对/解除配对
    public static final String MOD_PRODUCT__PRINT_LABEL = "MOD_PRODUCT.PRINT_LABEL";           // 销售 / 在线产品 / 打印标签
    public static final String MOD_PRODUCT__EXPORT_DATA = "MOD_PRODUCT.EXPORT_DATA";           // 销售 / 在线产品 / 导出
    public static final String MOD_ORDER__VIEW_LIST = "MOD_ORDER.VIEW_LIST";           // 销售 / 订单列表 / 查看
    public static final String MOD_ORDER__EDIT_EVALUATION = "MOD_ORDER.EDIT_EVALUATION";           // 销售 / 订单列表 / 标记测评/导入测评费

    public static final String MOD_ORDER__EDIT_FBM = "MOD_ORDER.EDIT_FBM";           // 销售 / 订单列表 / 录入FBM运费/导入FBM运费
    public static final String MOD_ORDER__EDIT_PURCHASE_COST = "MOD_ORDER.EDIT_PURCHASE_COST";  // 销售 / 订单列表 / 订单详情页/ 自定义采购成本
    public static final String MOD_ORDER__EDIT_REMARK = "MOD_ORDER.EDIT_REMARK";           // 销售 / 订单列表 / 备注
    public static final String MOD_ORDER__CONTACT_USER = "MOD_ORDER.CONTACT_USER";           // 销售 / 订单列表 / 联系买家
    public static final String MOD_ORDER__EXPORT_DATA = "MOD_ORDER.EXPORT_DATA";           // 销售 / 订单列表 / 导出

    public static final String MOD_EVALUATION_VIEW_LIST = "MOD_EVALUATION.VIEW_LIST";       // 销售 / 测评订单 / 查看
    public static final String MOD_EVALUATION_EDIT_DATA = "MOD_EVALUATION.EDIT_DATA";            // 销售 / 测评订单 / 添加测评订单、录入、导入测评费
    public static final String MOD_EVALUATION_EDIT_REMARK = "MOD_EVALUATION.EDIT_REMARK";        // 销售 / 测评订单 / 备注
    public static final String MOD_EVALUATION_EXPORT_LIST = "MOD_EVALUATION.EXPORT_LIST";        // 销售 / 测评订单 / 导出

    public static final String MOD_AFTER_SALES__VIEW_LIST = "MOD_AFTER_SALES.VIEW_LIST";           // 销售 / 售后评价 / 查看
    public static final String MOD_AFTER_SALES__EDIT_DATA = "MOD_AFTER_SALES.EDIT_DATA";           // 销售 / 售后评价 / 编辑
    public static final String MOD_REFUND_ORDER__VIEW_LIST = "MOD_REFUND_ORDER.VIEW_LIST";           // 销售 / 退货订单 / 查看
    public static final String MOD_REFUND_ORDER__EXPORT_DATA = "MOD_REFUND_ORDER.EXPORT_DATA";           // 销售 / 退货订单 / 导出
    public static final String MOD_REPLACE_ORDER__VIEW_LIST = "MOD_REPLACE_ORDER.VIEW_LIST";           // 销售 / 换货订单 / 查看
    public static final String MOD_REPLACE_ORDER__EXPORT_DATA = "MOD_REPLACE_ORDER.EXPORT_DATA";           // 销售 / 换货订单 / 导出
    public static final String MOD_REMOVE_ORDER__VIEW_LIST = "MOD_REMOVE_ORDER.VIEW_LIST";           // 销售 / 移除订单 / 查看
    public static final String MOD_REMOVE_ORDER__EXPORT_DATA = "MOD_REMOVE_ORDER.EXPORT_DATA";           // 销售 / 移除订单 / 导出
    public static final String MOD_FBM_AUTH__VIEW_LIST = "MOD_FBM_AUTH.VIEW_LIST";           // 销售 / 待审核 / 查看

    public static final String MOD_FBM_AUTH__AUTH_DATA = "MOD_FBM_AUTH.AUTH_DATA";           // 销售 / 待审核 / 审核通过
    public static final String MOD_FBM_AUTH__ASSIGN_CHANNEL = "MOD_FBM_AUTH.ASSIGN_CHANNEL";           // 销售 / 待审核 / 分配渠道
    public static final String MOD_FBM_AUTH__ASSIGN_WAREHOUSE = "MOD_FBM_AUTH.ASSIGN_WAREHOUSE";           // 销售 / 待审核 / 分配仓库
    public static final String MOD_FBM_AUTH__ABANDON_DATA = "MOD_FBM_AUTH.ABANDON_DATA";           // 销售 / 待审核 / 作废包裹
    public static final String MOD_FBM_LOGIC__VIEW_LIST = "MOD_FBM_LOGIC.VIEW_LIST";           // 销售 / 物流下单 / 查看

    public static final String MOD_FBM_LOGIC__MANUAL_UPDATE = "MOD_FBM_LOGIC.MANUAL_UPDATE";           // 销售 / 物流下单 / 手动更新
    public static final String MOD_FBM_LOGIC__CHANGE_LOGIC = "MOD_FBM_LOGIC.CHANGE_LOGIC";           // 销售 / 物流下单 / 更换渠道
    public static final String MOD_FBM_LOGIC__ABANDON_DATA = "MOD_FBM_LOGIC.ABANDON_DATA";           // 销售 / 物流下单 / 作废包裹
    public static final String MOD_FBM_LOGIC__QUERY_TRACK_NO = "MOD_FBM_LOGIC.QUERY_TRACK_NO";           // 销售 / 物流下单 / 获取单号
    public static final String MOD_FBM_LOGIC__ASSIGN_CHANNEL = "MOD_FBM_LOGIC.ASSIGN_CHANNEL";           // 销售 / 物流下单 / 分配渠道
    public static final String MOD_FBM_LOGIC__MOVE_TO_ALLOC = "MOD_FBM_LOGIC.MOVE_TO_ALLOC";           // 销售 / 物流下单 / 移入待配货

    public static final String MOD_FBM_ALLOC__VIEW_LIST = "MOD_FBM_ALLOC.VIEW_LIST";           // 销售 / 待配货 / 查看
    public static final String MOD_FBM_ALLOC__ALLOC_STOCK = "MOD_FBM_ALLOC.ALLOC_STOCK";           // 销售 / 待配货 / 分配库存
    public static final String MOD_FBM_ALLOC__CHANGE_LOGIC = "MOD_FBM_ALLOC.CHANGE_LOGIC";           // 销售 / 待配货 / 更换渠道
    public static final String MOD_FBM_ALLOC__ABANDON_DATA = "MOD_FBM_ALLOC.ABANDON_DATA";           // 销售 / 待配货 / 作废包裹
    public static final String MOD_FBM_PRINT__VIEW_LIST = "MOD_FBM_PRINT.VIEW_LIST";           // 销售 / 待打单 / 查看

    public static final String MOD_FBM_PRINT__PRINT_MD_FORM = "MOD_FBM_PRINT.PRINT_MD_FORM";           // 销售 / 待打单 / 打印面单
    public static final String MOD_FBM_PRINT__PRINT_PICK_FORM = "MOD_FBM_PRINT.PRINT_PICK_FORM";           // 销售 / 待打单 / 打印拣货单
    public static final String MOD_FBM_PRINT__CONFIRM_SHIP = "MOD_FBM_PRINT.CONFIRM_SHIP";           // 销售 / 待打单 / 确认发货
    public static final String MOD_FBM_PRINT__CHANGE_LOGIC = "MOD_FBM_PRINT.CHANGE_LOGIC";           // 销售 / 待打单 / 更换渠道
    public static final String MOD_FBM_PRINT__ABANDON_DATA = "MOD_FBM_PRINT.ABANDON_DATA";           // 销售 / 待打单 / 作废包裹

    public static final String MOD_FBM_SHIP__VIEW_LIST = "MOD_FBM_SHIP.VIEW_LIST";           // 销售 / 已发货 / 查看
    public static final String MOD_FBM_SHIP__PRINT_MD_FORM = "MOD_FBM_SHIP.PRINT_MD_FORM";           // 销售 / 已发货 / 打印面单
    public static final String MOD_FBM_SHIP__RESEND_DATA = "MOD_FBM_SHIP.RESEND_DATA";           // 销售 / 已发货 / 重发包裹
    public static final String MOD_FBM_ABANDON__VIEW_LIST = "MOD_FBM_ABANDON.VIEW_LIST";           // 销售 / 已作废 / 查看
    public static final String MOD_FBM_ABANDON__RESEND_DATA = "MOD_FBM_ABANDON.RESEND_DATA";           // 销售 / 已作废 / 重发包裹//

    public static final String MOD_FBM_SUBMIT__VIEW_LIST = "MOD_FBM_SUBMIT.VIEW_LIST";           // 销售 / 提交平台 / 查看
    public static final String MOD_FBM_SUBMIT__SUBMIT_AMAZON = "MOD_FBM_SUBMIT.SUBMIT_AMAZON";           // 销售 / 提交平台 / 提交亚马逊
    public static final String MOD_FBM_SUBMIT__GET_UPDATE = "MOD_FBM_SUBMIT.GET_UPDATE";           // 销售 / 提交平台 / 获取更新信息
    public static final String MOD_FBM_SUBMIT__MARK_IGNORE = "MOD_FBM_SUBMIT.MARK_IGNORE";           // 销售 / 提交平台 / 标记忽略
    public static final String MOD_FBM_SUBMIT__ORDER_DETAIL = "MOD_FBM_SUBMIT.ORDER_DETAIL";           // 销售 / 提交平台 / 订单详情

    public static final String MOD_LOGIC__VIEW_LIST = "MOD_LOGIC.VIEW_LIST";           // 销售 / 物流设置 / 查看
    public static final String MOD_LOGIC__ADD_LOGIC = "MOD_LOGIC.ADD_LOGIC";           // 销售 / 物流设置 / 添加物流商
    public static final String MOD_LOGIC__EDIT_DATA = "MOD_LOGIC.EDIT_DATA";           // 销售 / 物流设置 / 编辑物流渠道

    public static final String MOD_EMAIL__VIEW_LIST = "MOD_EMAIL.VIEW_LIST";           // 客服 / 邮件消息 / 查看
    public static final String MOD_EMAIL__SYNC_DATA = "MOD_EMAIL.SYNC_DATA";           // 客服 / 邮件消息 / 同步邮件
    public static final String MOD_EMAIL__REPLY_ALREADY = "MOD_EMAIL.REPLY_ALREADY";           // 客服 / 邮件消息 / 已在平台回复
    public static final String MOD_EMAIL__TOGGLE_READ = "MOD_EMAIL.TOGGLE_READ";           // 客服 / 邮件消息 / 标记已读/未读
    public static final String MOD_EMAIL__UPLOAD_ATTACH = "MOD_EMAIL.UPLOAD_ATTACH";           // 客服 / 邮件消息 / 上传附件
    public static final String MOD_EMAILTEMPLATE__ACTIVE = "MOD_EMAILTEMPLATE.ACTIVE";           // 客服 / 邮件模板 / 添加/编辑/删除
    public static final String MOD_EMAILTEMPLATE__VIRE_LIST = "MOD_EMAILTEMPLATE.VIEW.LIST";           // 客服 / 邮件模板 / 查看

    public static final String MOD_EMAIL__REPLY = "MOD_EMAIL.REPLY";           // 客服 / 邮件消息 / 回复
    public static final String MOD_EMAL_REVIEW__VIEW_LIST = "MOD_EMAL_REVIEW.VIEW_LIST";           // 客服 / 请求规则 / 查看
    public static final String MOD_EMAL_REVIEW__EDIT_RULE = "MOD_EMAL_REVIEW.EDIT_RULE";           // 客服 / 请求规则 / 添加规则/开启/禁用/删除/编辑
    public static final String MOD_EMAL_LOG__VIEW_LIST = "MOD_EMAL_LOG.VIEW_LIST";           // 客服 / 请求记录 / 查看
    public static final String MOD_FBA__VIEW_LIST = "MOD_FBA.VIEW_LIST";           // FBA / FBA货件 / 查看

    public static final String MOD_FBA__CREATE_FORM = "MOD_FBA.CREATE_FORM";           // FBA / FBA货件 / 生成发货单
    public static final String MOD_FBA__ADD_DATA = "MOD_FBA.ADD_DATA";           // FBA / FBA货件 / 创建货件
    public static final String MOD_FBA__PRINT_LABEL = "MOD_FBA.PRINT_LABEL";           // FBA / FBA货件 / 打印标签
    public static final String MOD_FBA__SYNC_DATA = "MOD_FBA.SYNC_DATA";           // FBA / FBA货件 / 同步货件
    public static final String MOD_FBA__EDIT_REMARK = "MOD_FBA.EDIT_REMARK";           // FBA / FBA货件 / 备注
    public static final String MOD_FBA__EDIT_LOGIC = "MOD_FBA.EDIT_LOGIC";           // FBA / FBA货件 / 编辑物流
    public static final String MOD_FBA__EXPORT_SHIPMENT = "MOD_FBA.EXPORT_SHIPMENT";  // FBA / FBA货件 / 导出
    public static final String MOD_FBA__LOGISTICS_EDIT = "MOD_FBA.LOGISTICS_EDIT";    // FBA / 头程物流 / 编辑
    public static final String MOD_FBA__LOGISTICS_DETAIL = "MOD_FBA.LOGISTICS_DETAIL";    // FBA / 头程物流 / 查看详情

    public static final String MOD_FBA_FORM__VIEW_LIST = "MOD_FBA_FORM.VIEW_LIST";           // FBA / 发货单 / 查看
    public static final String MOD_FBA_FORM__ALLOC_STOCK = "MOD_FBA_FORM.ALLOC_STOCK";           // FBA / 发货单 / 分配库存
    public static final String MOD_FBA_FORM__DELETE_DATA = "MOD_FBA_FORM.DELETE_DATA";           // FBA / 发货单 / 删除
    public static final String MOD_FBA_FORM__CONFIRM_SHIP = "MOD_FBA_FORM.CONFIRM_SHIP";           // FBA / 发货单 / 确认发货
    public static final String MOD_FBA_FORM__RE_ALLOC = "MOD_FBA_FORM.RE_ALLOC";           // FBA / 发货单 / 重新配货
    public static final String MOD_FBA_FORM__EDIT_DATA = "MOD_FBA_FORM.EDIT_DATA";           // FBA / 发货单 / 编辑
    public static final String MOD_FBA_FORM__RESTORE_STOCK = "MOD_FBA_FORM.RESTORE_STOCK";           // FBA / 发货单 / 恢复库存
    public static final String MOD_FBA_FORM__EXPORT_DATA = "MOD_FBA_FORM.EXPORT_DATA";           // FBA / 发货单 / 导出
    public static final String MOD_FBA_FORM__PRINT_DATA = "MOD_FBA_FORM.PRINT_DATA";           // FBA / 发货单 / 打印
    public static final String MOD_FBA_FORM__IMPORT_LOGISTICS_DATA = "MOD_FBA_FORM.IMPORT_LOGISTICS_DATA";           // FBA / 发货单 / 导入物流费用

    public static final String MOD_FBA_FORM__ABANDON_DATA = "MOD_FBA_FORM.ABANDON_DATA";           // FBA / 发货单 / 作废
    public static final String MOD_FBA__VIEW_HEADTRIP = "MOD_FBA.VIEW_HEADTRIP";           // FBA / 头程分摊 / 查看
    public static final String MOD_FBA__EXPORT_HEADTRIP = "MOD_FBA.EXPORT_HEADTRIP";           // FBA / 头程分摊 / 导出
    public static final String MOD_AD__VIEW_LIST = "MOD_AD.VIEW_LIST";           // 广告 / 全部广告 / 查看
    public static final String MOD_AD__EDIT_DATA = "MOD_AD.EDIT_DATA";           // 广告 / 全部广告 / 编辑

    public static final String MOD_USER_WORD__VIEW_LIST = "MOD_USER_WORD.VIEW_LIST";           // 广告 / 用户搜索词 / 查看
    public static final String MOD_USER_WORD__EDIT_DATA = "MOD_USER_WORD.EDIT_DATA";           // 广告 / 用户搜索词 / 编辑
    public static final String MOD_USER_WORD__EXPORT_DATA = "MOD_USER_WORD.EXPORT_DATA";           // 广告 / 用户搜索词 / 导出

    public static final String MOD_AD_SHOP__VIEW_LIST = "MOD_AD_SHOP.VIEW_LIST";           // 广告 / 店铺广告数据 / 查看
    public static final String MOD_AD_PRODUCT__VIEW_LIST = "MOD_AD_PRODUCT.VIEW_LIST";           // 广告 / 产品广告数据 / 查看
    public static final String MOD_AD_PRODUCT__EXPORT_DATA = "MOD_AD_PRODUCT.EXPORT_DATA";           // 广告 / 产品广告数据 / 导出
    public static final String MOD_KEYWORD__VIEW_LIST = "MOD_KEYWORD.VIEW_LIST";           // 广告 / 关键词报告 / 查看
    public static final String MOD_KEYWORD__EXPORT_DATA = "MOD_KEYWORD.EXPORT_DATA";           // 广告 / 关键词报告 / 导出
    public static final String MOD_AD_ACT__VIEW_LIST = "MOD_AD_ACT.VIEW_LIST";           // 广告 / 广告活动报告 / 查看
    public static final String MOD_AD_ACT__EXPORT_DATA = "MOD_AD_ACT.EXPORT_DATA";           // 广告 / 广告活动报告 / 导出
    public static final String MOD_AD_GROUP__VIEW_LIST = "MOD_AD_GROUP.VIEW_LIST";           // 广告 / 广告组报告 / 查看

    public static final String MOD_AD_MANAGE__VIEW_LIST = "MOD_AD_MANAGE.VIEW_LIST";        //广告 / 广告管理  / 查看
    public static final String MOD_AD_MANAGE__ADD_DATA = "MOD_AD_MANAGE.EDIT_DATA";          //广告 / 广告管理 / 编辑
    public static final String MOD_AD_MANAGE__EXPORT_DATA = "MOD_AD_MANAGE.EXPORT_DATA";          //广告 / 广告管理 / 导出

    public static final String MOD_MULTI_PLATFORM_TIKTOK_SHOP_EDIT = "MOD_MULTI_PLATFORM_TIKTOK_SHOP.EDIT";  //多平台Tk店铺授权/编辑/删除
    public static final String MOD_AD_MANAGE_TIKTOK__VIEW_LIST = "MOD_AD_MANAGE_TIKTOK.VIEW_LIST";        //广告 / TikTok广告管理  / 查看
    public static final String MOD_AD_MANAGE_TIKTOK__ADD_DATA = "MOD_AD_MANAGE_TIKTOK.EDIT_DATA";          //广告 / TikTok广告管理 / 编辑

    public static final String MOD_AD_DAILY__VIEW_LIST = "MOD_AD_DAILY.VIEW_LIST";        //广告 / 广告日报  / 查看
    public static final String MOD_AD_DAILY__EDIT_DATA = "MOD_AD_DAILY.EDIT_DATA";          //广告 / 广告日报  / 编辑
    public static final String MOD_AD_DAILY__EXPORT_DATA = "MOD_AD_DAILY.EXPORT_DATA";    //广告 / 广告日报  / 导出

    public static final String MOD_AD_GROUP__EXPORT_DATA = "MOD_AD_GROUP.EXPORT_DATA";           // 广告 / 广告组报告 / 导出

    public static final String MOD_AD_SP__VIEW_LIST = "MOD_AD_SP.VIEW_LIST";        // 广告 / sp / 查看
    public static final String MOD_AD_SP__EXPORT_DATA = "MOD_AD_SP.EXPORT_DATA";    // 广告 / sp / 导出
    public static final String MOD_AD_SB__VIEW_LIST = "MOD_AD_SB.VIEW_LIST";        // 广告 / sb / 查看
    public static final String MOD_AD_SB__EXPORT_DATA = "MOD_AD_SB.EXPORT_DATA";    // 广告 / sb / 导出
    public static final String MOD_AD_SD__VIEW_LIST = "MOD_AD_SD.VIEW_LIST";        // 广告 / sd / 查看
    public static final String MOD_AD_SD__EXPORT_DATA = "MOD_AD_SD.EXPORT_DATA";    // 广告 / sd / 导出

    public static final String MOD_TIME_TASK__VIEW_LIST = "MOD_TIME_TASK.VIEW_LIST";           // 广告 / 定时任务 / 查看
    public static final String MOD_TIME_TASK__ADD_DATA = "MOD_TIME_TASK.ADD_DATA";           // 广告 / 定时任务 / 添加定时任务
    public static final String MOD_TIME_TASK__EDIT_DATA = "MOD_TIME_TASK.EDIT_DATA";           // 广告 / 定时任务 / 编辑

    public static final String MOD_PURCHASE__VIEW_LIST = "MOD_PURCHASE.VIEW_LIST";           // 采购 / 采购单 / 查看
    public static final String MOD_PURCHASE__ADD_FORM = "MOD_PURCHASE.ADD_FORM";           // 采购 / 采购单 / 添加采购单
    public static final String MOD_PURCHASE__PAY_IT = "MOD_PURCHASE.PAY_IT";           // 采购 / 采购单 / 批量下单/下单
    public static final String MOD_PURCHASE__EDIT_DATA = "MOD_PURCHASE.EDIT_DATA";           // 采购 / 采购单 / 编辑
    public static final String MOD_PURCHASE__DELETE_DATA = "MOD_PURCHASE.DELETE_DATA";           // 采购 / 采购单 / 删除

    public static final String MOD_PURCHASE__END_GOOD_ARRIVE = "MOD_PURCHASE.END_GOOD_ARRIVE";           // 采购 / 采购单 / 结束到货/批量结束到货
    public static final String MOD_SUPPLIER__VIEW_LIST = "MOD_SUPPLIER.VIEW_LIST";           // 采购 / 供应商列表 / 查看
    public static final String MOD_SUPPLIER__ADD_DATA = "MOD_SUPPLIER.ADD_DATA";           // 采购 / 供应商列表 / 添加供应商/导入供应商
    public static final String MOD_SUPPLIER__EDIT_DATA = "MOD_SUPPLIER.EDIT_DATA";           // 采购 / 供应商列表 / 编辑
    public static final String MOD_SUPPLIER__DELETE_DATA = "MOD_SUPPLIER.DELETE_DATA";           // 采购 / 供应商列表 / 删除

    public static final String MOD_WAREHOUSE__VIEW_LIST = "MOD_WAREHOUSE.VIEW_LIST";           // 仓库 / 仓库列表 / 查看
    public static final String MOD_WAREHOUSE__EXPORT_DATA = "MOD_WAREHOUSE.EXPORT_DATA";           // 仓库 / 仓库列表 / 导出
    public static final String MOD_WAREHOUSE__ADD_DATA = "MOD_WAREHOUSE.ADD_DATA";           // 仓库 / 仓库列表 / 添加自建仓库
    public static final String MOD_WAREHOUSE__EDIT_DATA = "MOD_WAREHOUSE.EDIT_DATA";           // 仓库 / 仓库列表 / 编辑
    public static final String MOD_WAREHOUSE__DELETE_DATA = "MOD_WAREHOUSE.DELETE_DATA";           // 仓库 / 仓库列表 / 删除
    public static final String MOD_WAREHOUSE_ITEM__VIEW_LIST = "MOD_WAREHOUSE_ITEM.VIEW_LIST";           // 仓库 / 库存明细 / 查看
    public static final String MOD_WAREHOUSE_ITEM_EXPORT_DATA = "MOD_WAREHOUSE_ITEM.EXPORT_DATA";           // 仓库 / 库存明细 / 导出

    public static final String MOD_WAREHOUSE_ITEM__IMPORT_DATA = "MOD_WAREHOUSE_ITEM.IMPORT_DATA";           // 仓库 / 库存明细 / 导入库存初始值
    public static final String MOD_FBA_WAREHOUSE__VIEW_LIST = "MOD_FBA_WAREHOUSE.VIEW_LIST";           // 仓库 / FBA库存 / 查看
    public static final String MOD_WAREHOUSE_TO_ARRIVE__VIEW_LIST = "MOD_WAREHOUSE_TO_ARRIVE.VIEW_LIST";           // 仓库 / 待到货 / 查看
    public static final String MOD_WAREHOUSE_TO_ARRIVE__TO_ARRIVE = "MOD_WAREHOUSE_TO_ARRIVE.TO_ARRIVE";           // 仓库 / 待到货 / 到货

    public static final String MOD_OVERSEA__VIEW_LIST = "MOD_OVERSEA.VIEW_LIST";           // 海外仓 / 备货单 / 查看
    public static final String MOD_OVERSEA__ADD_DATA = "MOD_OVERSEA.ADD_DATA";            // 海外仓 / 备货单 / 添加
    public static final String MOD_OVERSEA__EDIT_DATA = "MOD_OVERSEA.EDIT_DATA";           // 海外仓 / 备货单 / 编辑
    public static final String MOD_OVERSEA__STOCK_ALLOT = "MOD_OVERSEA.STOCK_ALLOT";           // 海外仓 / 备货单 / 分配库存
    public static final String MOD_OVERSEA__STOCK_SHIP = "MOD_OVERSEA.STOCK_SHIP";           // 海外仓 / 备货单 / 发货
    public static final String MOD_OVERSEA__STOCK_BACK = "MOD_OVERSEA.STOCK_BACK";           // 海外仓 / 备货单 / 重新配货
    public static final String MOD_OVERSEA__STOCK_SIGN = "MOD_OVERSEA.STOCK_SIGN";           // 海外仓 / 备货单 / 收货
    public static final String MOD_OVERSEA__STOCK_CANCEL = "MOD_OVERSEA.STOCK_CANCEL";           // 海外仓 / 备货单 / 作废
    public static final String MOD_OVERSEA__EXPORT_LIST = "MOD_OVERSEA.EXPORT_LIST";           // 海外仓 / 备货单 / 导出

    public static final String MOD_OVERSEA_BATCH__VIEW_LIST = "MOD_OVERSEA_BATCH.VIEW_LIST";           // 海外仓 / 批次 / 查看
    public static final String MOD_OVERSEA_BATCH__EXPORT_LIST = "MOD_OVERSEA_BATCH.EXPORT_LIST";       // 海外仓 / 批次 / 导出

    public static final String MOD_OTHER_IN__VIEW_LIST = "MOD_OTHER_IN.VIEW_LIST";           // 仓库 / 其它入库 / 查看
    public static final String MOD_OTHER_IN__ADD_EDIT_FORM = "MOD_OTHER_IN.ADD_EDIT_FORM";           // 仓库 / 其它入库 / 添加入库单
    public static final String MOD_OTHER_IN__CONFIRM_IN = "MOD_OTHER_IN.CONFIRM_IN";           // 仓库 / 其它入库 / 确认入库
    public static final String MOD_OTHER_IN__DELETE_DATA = "MOD_OTHER_IN.DELETE_DATA";           // 仓库 / 其它入库 / 删除
    public static final String MOD_OTHER_IN__PRINT_DATA = "MOD_OTHE_IN.PRINT_DATA";           // 仓库 / 其它入库 / 打印
    public static final String MOD_OTHER_OUT__VIEW_LIST = "MOD_OTHER_OUT.VIEW_LIST";           // 仓库 / 其它出库 / 查看

    public static final String MOD_OTHER_OUT__ADD_EDIT_FORM = "MOD_OTHER_OUT.ADD_EDIT_FORM";           // 仓库 / 其它出库 / 添加/编辑出库单
    public static final String MOD_OTHER_OUT__CONFIRM_OUT = "MOD_OTHER_OUT.CONFIRM_OUT";           // 仓库 / 其它出库 / 确认出库
    public static final String MOD_OTHER_OUT__DELETE_DATA = "MOD_OTHER_OUT.DELETE_DATA";           // 仓库 / 其它出库 / 删除
    public static final String MOD_OTHER_OUT__PRINT_DATA = "MOD_OTHE_OUT.PRINT_DATA";           // 仓库 / 其它出库 / 打印
    public static final String MOD_ADJUST_INOUT__VIEW_DATA = "MOD_ADJUST_INOUT.VIEW_DATA";           // 仓库 / 调整单 / 查看
    public static final String MOD_ADJUST_INOUT__ADD_DATA = "MOD_ADJUST_INOUT.ADD_DATA";           // 仓库 / 调整单 / 添加
    public static final String MOD_ADJUST_INOUT__PRINT_DATA = "MOD_ADJUST_INOUT.PRINT_DATA";           // 仓库 / 调整单 / 打印
    public static final String MOD_ASSEMBLE_INOUT__VIEW_DATA  = "MOD_ASSEMBLE_INOUT.VIEW_DATA";           // 仓库 / 加工单 / 查看
    public static final String MOD_ASSEMBLE_INOUT__ADD_EDIT_REMOVE_DATA = "MOD_ASSEMBLE_INOUT.ADD_EDIT_REMOVE_DATA";           // 仓库 / 加工单 / 添加、编辑、删除
    public static final String MOD_ASSEMBLE_INOUT__ALLOC_STOCK   = "MOD_ASSEMBLE_INOUT.ALLOC_STOCK";           // 仓库 / 加工单 / 分配库存
    public static final String MOD_ASSEMBLE_INOUT__COMPLETE   = "MOD_ASSEMBLE_INOUT.COMPLETE";           // 仓库 / 加工单 / 完成加工
    public static final String MOD_ASSEMBLE_INOUT__PRINT   = "MOD_ASSEMBLE_INOUT.PRINT";           // 仓库 / 加工单 / 打印
    public static final String MOD_INVENTORY_LOG__VIEW_LIST = "MOD_INVENTORY_LOG.VIEW_LIST";           // 仓库 / 库存流水 / 查看

    public static final String MOD_INVENTORY_LOG__EXPORT_DATA = "MOD_INVENTORY_LOG.EXPORT_DATA";           // 仓库 / 库存流水 / 导出
    public static final String MOD_STAT_SALES__VIEW_LIST = "MOD_STAT_SALES.VIEW_LIST";           // 数据 / 销量统计 / 查看
    public static final String MOD_STAT_SALES__EXPORT_DATA = "MOD_STAT_SALES.EXPORT_DATA";           // 数据 / 销量统计 / 导出
    public static final String MOD_STAT_PRODUCT__VIEW_LIST = "MOD_STAT_PRODUCT.VIEW_LIST";           // 数据 / 产品表现 / 查看
    public static final String MOD_STAT_PRODUCT__EXPORT_DATA = "MOD_STAT_PRODUCT.EXPORT_DATA";           // 数据 / 产品表现 / 导出

    public static final String MOD_STAT_SHOP__VIEW_LIST = "MOD_STAT_SHOP.VIEW_LIST";           // 数据 / 店铺统计 / 查看
    public static final String MOD_STAT_SHOP__EXPORT_DATA = "MOD_STAT_SHOP.EXPORT_DATA";           // 数据 / 店铺统计 / 导出
    public static final String MOD_SHOP_PERFORMANCE__VIEW_LIST = "MOD_SHOP_PERFORMANCE.VIEW_LIST";           // 数据 / 店铺表现 / 查看
    public static final String MOD_SHOP_PERFORMANCE__EXPORT_DATA = "MOD_SHOP_PERFORMANCE.EXPORT_DATA";           // 数据 / 店铺表现 / 导出
    public static final String MOD_PROFIX_PRODUCT__VIEW_LIST = "MOD_PROFIX_PRODUCT.VIEW_LIST";           // 数据 / 产品利润 / 查看

    public static final String MOD_PROFIX_PRODUCT__EXPORT_DATA = "MOD_PROFIX_PRODUCT.EXPORT_DATA";           // 数据 / 产品利润 / 导出
    public static final String MOD_PROFIX_PRODUCT__IMPORT_ASIN_FEE = "MOD_PROFIX_PRODUCT.IMPORT_ASIN_FEE";   // 数据 / 产品利润 / 导入ASIN其他费
    public static final String MOD_PROFIX_PRODUCT__ASIN_FEE_MANAGE = "MOD_PROFIX_PRODUCT.ASIN_FEE_MANAGE";   // 数据 / 产品利润 / ASIN费用类型管理

    public static final String MOD_PROFIX_SHOP__VIEW_LIST = "MOD_PROFIX_SHOP.VIEW_LIST";           // 数据 / 店铺利润 / 查看
    public static final String MOD_PROFIX_SHOP__EXPORT_DATA = "MOD_PROFIX_SHOP.EXPORT_DATA";           // 数据 / 店铺利润 / 导出
    public static final String MOD_PROFIX_SHOP__IMPORT_SHOP_FEE = "MOD_PROFIX_SHOP.IMPORT_SHOP_FEE";   // 数据 / 店铺利润 / 导入店铺其他费
    public static final String MOD_PROFIX_SHOP__SHOP_FEE_MANAGE = "MOD_PROFIX_SHOP.SHOP_FEE_MANAGE";   // 数据 / 店铺利润 / 店铺费用类型管理


    public static final String MOD_FBA_INVENTORY_REPORT__VIEW_LIST = "MOD_FBA_INVENTORY_REPORT.VIEW_LIST";           // 数据 / 库存报表 / 查看
    public static final String MOD_FBA_INVENTORY_REPORT__EXPORT_DATA = "MOD_FBA_INVENTORY_REPORT.EXPORT_DATA";           // 数据 / 库存报表 / 导出

    public static final String MOD_COMPENSATION__VIEW_LIST = "MOD_COMPENSATION.VIEW_LIST";           // 数据 / 赔偿 / 查看
    public static final String MOD_COMPENSATION__EXPORT_DATA = "MOD_COMPENSATION.EXPORT_DATA";           // 数据 / 赔偿 / 导出

    public static final String MOD_INVENTORY_CHECK__VIEW_LIST = "MOD_INVENTORY_CHECK.VIEW_LIST";           // 数据 / 盘库 / 查看
    public static final String MOD_STORE_FEE_MONTH__VIEW_LIST = "MOD_STORE_FEE_MONTH.VIEW_LIST";           // 数据 / 月仓储费 / 查看
    public static final String MOD_STORE_FEE_MONTH__EXPORT_DATA = "MOD_STORE_FEE_MONTH.EXPORT_DATA";           // 数据 / 月仓储费 / 导出
    public static final String MOD_STORE_FEE_LONG__VIEW_LIST = "MOD_STORE_FEE_LONG.VIEW_LIST";           // 数据 / 长期仓储费 / 查看
    public static final String MOD_STORE_FEE_LONG__EXPORT_DATA = "MOD_STORE_FEE_LONG.EXPORT_DATA";           // 数据 / 长期仓储费 / 导出

    public static final String MOD_REMOVE_GOOD__VIEW_LIST = "MOD_REMOVE_GOOD.VIEW_LIST";           // 数据 / 移除货件 / 查看
    public static final String MOD_REMOVE_GOOD__EXPORT_DATA = "MOD_REMOVE_GOOD.EXPORT_DATA";           // 数据 / 移除货件 / 导出
    public static final String MOD_REPORT_PROFIT__VIEW_LIST = "MOD_REPORT_PROFIT.VIEW_LIST";           // 财务 / 利润报表 / 查看
    public static final String MOD_REPORT_PROFIT__EXPORT_DATA = "MOD_REPORT_PROFIT.EXPORT_DATA";           // 财务 / 利润报表 / 导出
    public static final String MOD_PERFORMANCE__VIEW_LIST = "MOD_PERFORMANCE.VIEW_LIST";           // 财务 / 业绩报表 / 查看
    public static final String MOD_PERFORMANCE__VIEW_ALL_LIST = "MOD_PERFORMANCE.VIEW_ALL_LIST";   // 财务 / 业绩报表 / 查看所有报告

    public static final String MOD_PERFORMANCE__EXPORT_DATA = "MOD_PERFORMANCE.EXPORT_DATA";           // 财务 / 业绩报表 / 导出
    public static final String MOD_STAT_PROFIT__VIEW_LIST = "MOD_STAT_PROFIT.VIEW_LIST";           // 财务 / 批次成本 / 查看
    public static final String MOD_STAT_PROFIT__EXPORT_DATA = "MOD_STAT_PROFIT.EXPORT_DATA";           // 财务 / 批次成本 / 导出
    public static final String MOD_RETURN_MONEY__VIEW_LIST = "MOD_RETURN_MONEY.VIEW_LIST";           // 财务 / 回款记录 / 查看
    public static final String MOD_RETURN_MONEY__EXPORT_DATA = "MOD_RETURN_MONEY.EXPORT_DATA";           // 财务 / 回款记录 / 导出

    public static final String MOD_KEYWORD_RANK__VIEW_LIST = "MOD_KEYWORD_RANK.VIEW_LIST";           // 工具 / 关键词排名 / 查看
    public static final String MOD_KEYWORD_RANK__ADD_DATA = "MOD_KEYWORD_RANK.ADD_DATA";           // 工具 / 关键词排名 / 添加监控
    public static final String MOD_KEYWORD_RANK__DELETE_DATA = "MOD_KEYWORD_RANK.DELETE_DATA";           // 工具 / 关键词排名 / 移除
    public static final String MOD_RIVAL_MONITOR__VIEW_LIST = "MOD_RIVAL_MONITOR.VIEW_LIST";           // 工具 / 竞品监控 / 查看
    public static final String MOD_RIVAL_MONITOR__ADD_DATA = "MOD_RIVAL_MONITOR.ADD_DATA";           // 工具 / 竞品监控 / 添加监控

    public static final String MOD_RIVAL_MONITOR__DELETE_DATA = "MOD_RIVAL_MONITOR.DELETE_DATA";           // 工具 / 竞品监控 / 移除
    public static final String MOD_UP_NEW__VIEW_LIST = "MOD_UP_NEW.VIEW_LIST";           // 工具 / 上新监控 / 查看
    public static final String MOD_UP_NEW__ADD_SHOP = "MOD_UP_NEW.ADD_SHOP";           // 工具 / 上新监控 / 添加店铺
    public static final String MOD_UP_NEW__DELETE_DATA = "MOD_UP_NEW.DELETE_DATA";           // 工具 / 上新监控 / 移除
    public static final String MOD_USER__VIEW_LIST = "MOD_USER.VIEW_LIST";           // 设置 / 子账号 / 查看

    public static final String MOD_USER__EDIT_DATA = "MOD_USER.EDIT_DATA";           // 设置 / 子账号 / 编辑
    public static final String MOD_USER__TOGGLE_DATA = "MOD_USER.TOGGLE_DATA";           // 设置 / 子账号 / 禁用
    public static final String MOD_USER__DELETE_DATA = "MOD_USER.DELETE_DATA";           // 设置 / 子账号 / 删除
    public static final String MOD_USER__ADD_DATA = "MOD_USER.ADD_DATA";           // 设置 / 子账号 / 增加
    public static final String MOD_USER__ENABLE_DATA = "MOD_USER.ENABLE_DATA";           // 设置 / 子账号 / 启用

    public static final String MOD_ROLE__VIEW_LIST = "MOD_ROLE.VIEW_LIST";           // 设置 / 角色管理 / 查看
    public static final String MOD_ROLE__ADD_DATA = "MOD_ROLE.ADD_DATA";           // 设置 / 角色管理 / 添加角色
    public static final String MOD_ROLE__ADD_EDIT_COPY_DATA = "MOD_ROLE.ADD_EDIT_COPY_DATA";           // 设置 / 角色管理 / 添加/编辑/复制
    public static final String MOD_ROLE__DELETE_DATA = "MOD_ROLE.DELETE_DATA";           // 设置 / 角色管理 / 删除
    public static final String MOD_ROLE__COPY_DATA = "MOD_ROLE.COPY_DATA";           // 设置 / 角色管理 / 复制
    public static final String MOD_ROLE__EDIT_USERS = "MOD_ROLE.EDIT_USERS";           // 设置 / 角色管理 / 编辑成员

    public static final String MOD_PLUGIN_ONLINE__VIEW_LIST = "MOD_PLUGIN_ONLINE.VIEW_LIST";           // 设置 / 在线状态 / 查看
    public static final String MOD_PLUGIN_REPORT__VIEW_LIST = "MOD_PLUGIN_REPORT.VIEW_LIST";           // 设置 / 报表记录 / 查看

    //    public static final String MOD_BUY_PACKAGE__VIEW_LIST = "MOD_BUY_PACKAGE.VIEW_LIST";           // 设置 / 套餐购买 / 查看/编辑
    public static final String MOD_BUY_PACKAGE__PAY_IT = "MOD_BUY_PACKAGE.PAY_IT";           // 设置 / 套餐购买 / 购买
    public static final String MOD_BUY_PACKAGE__VIEW_DETAIL = "MOD_BUY_PACKAGE.VIEW_DETAIL";           // 设置 / 交易明细 / 查看
    public static final String MOD_SHOP_AUTH__VIEW_LIST = "MOD_SHOP_AUTH.VIEW_LIST";           // 设置 / 店铺授权 / 查看
    public static final String MOD_SHOP_AUTH__EDIT_DATA = "MOD_SHOP_AUTH.EDIT_DATA";           // 设置 / 店铺授权 / 编辑账号名称

    public static final String MOD_SHOP_AUTH__AUTH_AD = "MOD_SHOP_AUTH.AUTH_AD";           // 设置 / 店铺授权 / 广告授权
    public static final String MOD_SHOP_AUTH__AUTH_SHOP = "MOD_SHOP_AUTH.AUTH_SHOP";           // 设置 / 店铺授权 / 授权店铺/重新授权店铺
    public static final String MOD_SHOP_AUTH__ATTACH_EMAIL = "MOD_SHOP_AUTH.ATTACH_EMAIL";           // 设置 / 店铺授权 / 绑定邮箱
    public static final String MOD_SHOP_AUTH__DELETE_AUTH = "MOD_SHOP_AUTH.DELETE_AUTH";           // 设置 / 店铺授权 / 删除店铺授权

    public static final String MOD_FBA_PLAN__VIEW_LIST = "MOD_FBA_PLAN.VIEW_LIST";           // FBA / 发货计划 / 查看
    public static final String MOD_FBA_PLAN__ADD_PLAN = "MOD_FBA_PLAN.ADD_PLAN";           // FBA / 发货计划 / 创建计划
    public static final String MOD_FBA_PLAN__ADD_CARTON = "MOD_FBA_PLAN.ADD_CARTON";           // FBA / 发货计划 / 创建货件
    public static final String MOD_FBA_PLAN__MARK_FINISH = "MOD_FBA_PLAN.MARK_FINISH";           // FBA / 发货计划 / 标记完成
    public static final String MOD_FBA_PLAN__VIEW_DETAIL = "MOD_FBA_PLAN.VIEW_DETAIL";           // FBA / 发货计划 / 详情
    public static final String MOD_FBA_PLAN__DELETE_DATA = "MOD_FBA_PLAN.DELETE_DATA";           // FBA / 发货计划 / 删除
    public static final String MOD_PURCHASE_GENERATE_FORM = "MOD_PURCHASE.GENERATE_FORM";           // FBA / 发货计划 / 生成采购单

    /**
     * FBA -> 成本调整
     */
    public static final String MOD_FBA_FORM__COST_ADJUST = "MOD_FBA_FORM.COST_ADJUSTMENT";              // FBA / 发货单 / 成本调整
    public static final String MOD_FBA_ADJUST__VIEW_LIST = "MOD_ADJUST.VIEW_LIST";                      // FBA / 成本调整 / 查看
    public static final String MOD_FBA_ADJUST__CREATE_OR_EDIT = "MOD_ADJUST.CREATE_OR_EDIT";            // FBA / 成本调整 / 编辑
    public static final String MOD_FBA_ADJUST__AUDIT_OR_REJECT = "MOD_ADJUST.AUDIT_OR_REJECT";          // FBA / 成本调整 / 审核
    public static final String MOD_FBA_ADJUST__DELETE = "MOD_ADJUST.DELETE";                            // FBA / 成本调整 / 删除

    public static final String MOD_FBA_SUGGESTION__VIEW_LIST = "MOD_FBA_SUGGESTION.VIEW_LIST";      //FBA/补货建议/查看
    public static final String MOD_FBA_SUGGESTION__VIEW_SET = "MOD_FBA_SUGGESTION.VIEW_SET";      //FBA/补货建议/设置/批量设置规则/同步数据/关注/取消关注
    public static final String MOD_FBA_SUGGESTION__EXPORT_DATA = "MOD_FBA_SUGGESTION.EXPORT_DATA";      //FBA/补货建议/导出


    public static final String MOD_FINANCE__VIEW_LIST = "MOD_FINANCE.VIEW_LIST";                         // 设置 / 业务设置/ 财务 / 查看/编辑
    public static final String MOD_WAREHOUSE_SETTING__EDIT_DATA = "MOD_WAREHOUSE_SETTING.EDIT_DATA";     // 设置 / 业务设置/ 仓库 / 查看/编辑
    public static final String MOD_AUTO_MATCH_EDIT_DATA = "MOD_AUTO_MATCH.EDIT_DATA";                    // 设置 / 业务设置/ 销售 / 查看/编辑
    public static final String MOD_MESSAGE_NOTICE__VIEW_EDIT = "MOD_MESSAGE_NOTICE.VIEW_EDIT";                    // 设置 / 业务设置/ 消息 / 查看/编辑-差评提醒
    public static final String MOD_MESSAGE_ORDER_NOTICE__VIEW_EDIT = "MOD_MESSAGE_ORDER.VIEW_EDIT";                    // 设置 / 业务设置/ 消息 / 查看/编辑-测评退款/退货
    public static final String MOD_MESSAGE_SALE_VIEW_DEIT = "MOD_MESSAGE_SALE.VIEW_DEIT";                    // 设置 / 业务设置/ 消息 / 查看/编辑-测评退款/退货


    public static final String MOD_EXCHANGE_RATE__VIEW_LIST = "MOD_EXCHANGE_RATE.VIEW_LIST";           // 设置 / 汇率设置 / 查看
    public static final String MOD_EXCHANGE_RATE__EDIT_DATA = "MOD_EXCHANGE_RATE.EDIT_DATA";          // 设置 / 汇率设置 / 编辑

    public static final String MOD_PURCHASE_PLAN_VIEW_EDIT="MOD_PURCHASE_PLAN.VIEW_EDIT";             //设置 / 业务设置 / 采购计划 /  查看/编辑


    public static final String MOD_PURCHASE_PLAN_VIRE_LIST="MOD_PURCHASE_PLAN.VIEW_LIST";               // 采购 / 采购计划 /查看
    public static final String MOD_PURCHASE_PLAN_ADD="MOD_PURCHASE_PLAN.ADD";                           // 采购 / 采购计划 /添加
    public static final String MOD_PURCHASE_PLAN_DELETE="MOD_PURCHASE_PLAN.DELETE";                     // 采购 / 采购计划 /删除/批量删除
    public static final String MOD_PURCHASE_PLAN_GENERATE_FORM="MOD_PURCHASE_PLAN.PURCHASENO";          // 采购 / 采购计划 /生成采购单
    public static final String MOD_PURCHASE_PLAN_GENERATE_ASSEMBLE="MOD_PURCHASE_PLAN.PRE_ASSEMBLE";    // 采购 / 采购计划 /生成加工单


    public static final String MOD_ASIN_DASHBOARD__VIEW_LIST = "MOD_ASIN_DASHBOARD.VIEW_LIST";           // 数据 / asin看板 / 查看
    public static final String MOD_ASIN_DASHBOARD__EXPORT_DATA = "MOD_ASIN_DASHBOARD.EXPORT_DATA";           // 数据 / asin看板 / 导出

    public static final String MOD_REPORT_CENTER_VIEW_LIST="MOD_REPORT_CENTER.VIEW_LIST";               // 报告下载中心 / 报告下载中心 /查看
    public static final String MOD_REPORT_CENTER_ADD="MOD_REPORT_CENTER.ADD";                           // 报告下载中心 / 报告下载中心 /添加
    public static final String MOD_REPORT_CENTER_DELETE="MOD_REPORT_CENTER.DELETE";                     // 报告下载中心 / 报告下载中心 /删除/批量删除
    public static final String MOD_REPORT_CENTER_EXPORT="MOD_REPORT_CENTER.EXPORT";                 // 报告下载中心 / 报告下载中心 /下载/批量下载

    public static final String MODE_VAT_SHOP_VIEW_LIST = "MODE_VAT_SHOP.VIEW_LIST";                      //vat /上传设置 /查看
    public static final String MODE_VAT_SHOP_EDIT_DATA = "MODE_VAT_SHOP.EDIT_DATA";                      //vat /上传设置 /开启、编辑
    public static final String MODE_VAT_ORDER_VIEW_LIST = "MODE_VAT_ORDER.VIEW_LIST";                    //vat /发票管理 /查看
    public static final String MODE_VAT_ORDER_UPLOAD_INVOICE = "MODE_VAT_ORDER.UPLOAD_INVOICE";          //vat /发票管理 /上传发票
    public static final String MODE_VAT_ORDER_VIEW_INVOICE = "MODE_VAT_ORDER.VIEW_INVOICE";              //vat /发票管理 /查看发票
    public static final String MODE_VAT_SHIP_VIEW_LIST = "MODE_VAT_SHIP.VIEW_LIST";                      //vat /上传记录 /查看
    public static final String MODE_VAT_SHIP_UPLOAD_INVOICE = "MODE_VAT_SHIP.UPLOAD_INVOICE";            //vat /上传记录 /上传发票
    public static final String MODE_VAT_SHIP_VIEW_INVOICE = "MODE_VAT_SHIP.VIEW_INVOICE";                //vat /上传记录 /查看发票

}
