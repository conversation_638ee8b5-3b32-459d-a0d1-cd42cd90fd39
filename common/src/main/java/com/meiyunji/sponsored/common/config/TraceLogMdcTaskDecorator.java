package com.meiyunji.sponsored.common.config;

import com.meiyunji.sponsored.common.permission.context.PermissionContext;
import com.meiyunji.sponsored.common.permission.context.PermissionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;
import java.util.Map;

/**
 * @Description: 异步线程获得主线程的上下文，为了获取主线程traceId
 * @author: z<PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/11/14 11:35
 **/
@Slf4j
public class TraceLogMdcTaskDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        Map<String, String> mdcContextMap = MDC.getCopyOfContextMap();

        PermissionContext permissionContextCopy = PermissionContextHolder.copyPermissionContext();

        long sourceThreadId = Thread.currentThread().getId();

        return () -> {
            String currentThreadName = Thread.currentThread().getName();
            long currentThreadId = Thread.currentThread().getId();

            // 判断是否为同一线程（处理拒绝策略为当前线程执行的情况）
            boolean isSameThread = (sourceThreadId == currentThreadId);

            if (isSameThread) {
                log.debug("任务在原线程执行，跳过上下文设置: thread={}({})", currentThreadName, currentThreadId);
                runnable.run();
                return;
            }

            Map<String, String> originalMdcContext = MDC.getCopyOfContextMap();
            PermissionContext originalPermissionContext = PermissionContextHolder.getContext();

            try {
                if (mdcContextMap != null) {
                    MDC.setContextMap(mdcContextMap);
                }

                if (permissionContextCopy != null) {
                    PermissionContextHolder.setContext(permissionContextCopy);
                }

                if (log.isDebugEnabled()) {
                    log.debug("异步任务开始执行 - 上下文已设置: targetThread={}({}), Permission={}",
                            currentThreadName, currentThreadId,
                            permissionContextCopy != null ? permissionContextCopy.getFilterType() : "null");
                }
                runnable.run();

            }  finally {
                if (originalMdcContext != null) {
                    MDC.setContextMap(originalMdcContext);
                } else {
                    MDC.clear();
                }

                if (originalPermissionContext != null) {
                    PermissionContextHolder.setContext(originalPermissionContext);
                } else {
                    PermissionContextHolder.clear();
                }
            }
        };
    }
}