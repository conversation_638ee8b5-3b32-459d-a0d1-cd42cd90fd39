package com.meiyunji.sponsored.common.config;

import org.slf4j.MDC;
import org.springframework.core.task.TaskDecorator;

import java.util.Map;

/**
 * @Description: 异步线程获得主线程的上下文，为了获取主线程traceId
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @date: 2023/11/14 11:35
 **/
public class TraceLogMdcTaskDecorator implements TaskDecorator {

    @Override
    public Runnable decorate(Runnable runnable) {
        Map<String, String> map = MDC.getCopyOfContextMap();

        return () -> {
            try {
                if (map != null) {
                    MDC.setContextMap(map);
                }
                runnable.run();
            } finally {
                MDC.clear();
            }
        };
    }
}