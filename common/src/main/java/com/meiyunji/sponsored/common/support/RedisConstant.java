package com.meiyunji.sponsored.common.support;

/**
 * <AUTHOR> sujinpeng
 * @Date : 2019/7/10
 * @Time : 16:54
 * @Description : redis key
 * 命名规范,key在能表名含义的同时尽可能简短
 * 如果有变量,用:分隔
 */
public class RedisConstant {

    /**
     * 验证码发送的key,每个Ip会有一个标识
     * 用于限制发送频次
     * captcha_send:ip
     */
    public static final String CAPTCHA_COUNT = "captcha_count:%s";

    /**
     * 验证码key
     * 存储的值是map
     * captcha:email
     */
    public static final String CAPTCHA = "captcha:%s";

    public static final String SHOP_REFRESH_TOKEN = "refresh_token:%s:%s";

    public static final String PRODUCT_PRICING_TOKEN = "product_pricing:%s";

    public static final String REPLENISH_SUGGESTION_TOKEN = "ReplenishSuggestion:%s";
    public static final String REPLENISH_SUGGESTION_SELLER_TOKEN = "ReplenishSuggestionBySellerId:%s";

    public static final String REPLENISH_SUGGESTION_KEY = "ReplenishSuggestionKey:%s";

    /**
     * 店铺cpctoken锁(sellerId和re)
     */
    public static final String SHOP_AD_REFRESH_TOKEN = "ad_refresh_token:%s:%s";

    public static final String MWS_REQUEST_LIMIT = "amazon_request_limit.%s.%s.%s";

    /**
     * 产品图片缓存一天(puid,shopId,asin)
     */
    public static final String PRODUCT_MAIN_IMAGE = "product_main_image.%s.%s.%s";

    /**
     * asin图片缓存一天
     */
    public static final String ASIN_IMAGE = "ASIN_IMAGE.%s";

    /**
     * 采购单锁(puid,采购单号)
     */
    public static final String WAREHOUSE_PURCHASE = "warehouse_purchase_%s_%s";

    /**
     * 仓库占用锁（puid, 原始单据号, 仓库ID)
     */
    public static final String WAREHOUSE_OCCUPY_STOCK = "WH_OCCUPY_STOCK_%s_%s_%s";


    /**
     * 优惠价，存储需要同步的数据
     */
    public static final String PRICING_SHOP_AUTH_LIST = "PRICING_SHOP_AUTH_LIST";
    /**
     * 优惠价，存储获取店铺信息的时间
     */
    public static final String PRICING_SHOP_AUTH_LIST_TIME = "PRICING_SHOP_AUTH_LIST_TIME";

    /**
     * 广告店铺
     */
    public static final String AD_REQUEST_REPORT_LIMIT = "AD_REQUEST_REPORT_LIMIT:";

    public static final String AD_REQUEST_REPORT_LOCK_LIMIT = "AD_REQUEST_REPORT_LOCK_LIMIT:";

    public static final String SEllFOX_AD_AMAZON_ATTRIBUTION_PUBLISHERS = "SEllFOX_AD_AMAZON_ATTRIBUTION_PUBLISHERS";

    public static final String SEllFOX_ATTRIBUTION_PUBLISH_ASIN_LOCK = "SEllFOX_ATTRIBUTION_PUBLISH_ASIN_LOCK:";
    public static final String SEllFOX_ATTRIBUTION_PUBLISH_TOP_URL_LOCK = "SEllFOX_ATTRIBUTION_PUBLISH_TOP_URL_LOCK:";
    public static final String SELLFOX_AMAZON_REPORT_DATA_PROCESS_LOCK = "SELLFOX_AMAZON_REPORT_DATA_PROCESS_LOCK:";

    public static final String SELLFOX_AD_AUTH_COUNT = "SELLFOX_AD_AUTH:%s:%s";

    public static final String SELLFOX_AD_SB_CAMPAIGN_FORMAT = "SELLFOX_AD_SB_CAMPAIGN_FORMAT:%s:%s:%s";

    public static final String SELLFOX_AD_SB_DAILY_REPORT = "SELLFOX_AD_SB_DAILY_REPORT:%s:%s:%s:%s";

    public static final String SELLFOX_AD_SB_DAILY_TARGET_REPORT = "SELLFOX_AD_SB_DAILY_TARGET_REPORT:%s:%s:%s:%s";

    public static final String SELLFOX_AD_SB_DAILY_KEYWORD_REPORT = "SELLFOX_AD_SB_DAILY_KEYWORD_REPORT:%s:%s:%s:%s";

    public static final Object AD_SCHEDULE_TASK_FINISHED_LOCK = "AD_SCHEDULE_TASK_FINISHED_LOCK:%s:%s:%s:%s:%s";

    public static final String SELLFOX_AD_SB_GROUP_FORMAT = "SELLFOX_AD_SB_GROUP_FORMAT:%s:%s:%s";

    public static final String SELLFOX_SYNC_AMAZON_AD_SHOP_FORMAT = "SELLFOX_SYNC_AMAZON_AD_SHOP_FORMAT:PUID:%s:SHOP_ID:%s";

    public static final String SPONSORED_AD_TOKEN_SELLER_REGION_CACHE_KEY = "SPONSORED_AD_TOKEN_CACHE_KEY:SELLER:%s:REGION:%s";

    public static final String SPONSORED_AD_STRATEGY_SCHEDULE_KEY = "SPONSORED_AD_STRATEGY_SCHEDULE_KE:%s";

    public static final String SPONSORED_AD_STRATEGY_PROCESS_TASK_KEY = "SPONSORED_AD_STRATEGY_PROCESS_TASK_KEY:%s";
    public static final String AD_TARGET_TASK_KEY = "AD_TARGET_TASK_KEY:%s";

    public static final String AD_MANAGE_PAGE_EXPORT_TASK_KEY = "AD_MANAGE_PAGE_EXPORT_TASK_KEY:%s";

    public static final String SELLFOX_AD_SD_CAMPAIGN_TYPE = "SELLFOX_AD_SD_CAMPAIGN_TYPE:%s:%s:%s";

    public static final String SELLFOX_AD_ACTIVE_MONITOR = "SELLFOX_AD_ACTIVE_MONITOR:%s";

    public static final String SELLFOX_AD_ACTIVE_COUNT = "SELLFOX_AD_ACTIVE_COUNT:%s";

    /**
     * redis key
     */
    public static final String SPONSORED_AD_SHOP_SALE_CACHE_KEY = "AD_SHOP_SALE_:%s";

    /**
     * 拼接redis的key 用于redis key
     */
    public static final String SPONSORED_AD_SHOP_SALE_CACHE_REDIS_KEY ="key:%s:%s:%s:%s";

    public static final String SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY = "SPONSORED_AD_STRATEGY_AD_GROUP_ID_TASK_KEY:%s";

    /**
     * 统计亚马逊stream成功率的redisKey
     */
    public static final String AMAZON_MANAGEMENT_STREAM_COUNT_REDIS_KEY = "SELLFOX_AMAZON_STREAM:%s:";


    public static final String AMAZON_SYNC_REDIS_KEY = "SELLFOX_AMAZON_SYNC:%s";

    public static final String SPONSORED_AUTO_RULE_SCHEDULE_KEY = "SPONSORED_AUTO_RULE_SCHEDULE_KEY:%s";

    public static final String SPONSORED_AUTO_RULE_PROCESS_TASK_KEY = "SPONSORED_AUTO_RULE_PROCESS_TASK_KEY:%s";

    public static final String SPONSORED_AD_STRATEGY_SUBMIT_TASK_KEY = "SPONSORED_AD_STRATEGY_SUBMIT_TASK_KEY:%s";

    public static final String SPONSORED_AUTO_RULE_SUBMIT_TASK_KEY = "SPONSORED_AUTO_RULE_SUBMIT_TASK_KEY:%s";
    /**
     * 预警指标的key
     */
    public static final String SPONSORED_WARNING_INDEX_KEY = "SPONSORED_WARNING_INDEX_KEY:%s";

    public static final String AD_OPERATION_NOTES_LOG_KEY = "AD_OPERATION_NOTES_LOG_KEY:%s";

    public static final String WALMART_SYNC_MANAGE_TASK_KEY = "WALMART_SYNC_MANAGE_TASK_KEY:%s:%s";
    public static final String WALMART_SYNC_HANDLE_SNAPSHOT_TASK_KEY = "WALMART_SYNC_HANDLE_SNAPSHOT_TASK_KEY:%s:%s";

    /**
     * 自动化规则模板禁用key
     */
    public static final String AUTO_RULE_DISABLED_TEMPLATE_KEY = "AUTO_RULE_DISABLED_TEMPLATE:%s";

    /**
     * 分时模板禁用key
     */
    public static final String STRATEGY_DISABLED_TEMPLATE_KEY = "STRATEGY_DISABLED_TEMPLATE:%s";

    /**
     * tiktok广告授权key=puid+uuid
     */
    public static final String TIKTOK_AD_AUTH_UUID = "TIKTOK_AD_AUTH_UUID:%s_%s";
    /**
     * 帖子同步
     */
    public static final String SELLFOX_POST_SYNC_KEY = "SELLFOX_POST_SYNC_KEY:%s:%s";

    /**
     * 帖子同步限制key
     */
    public static final String SELLFOX_POST_SYNC_LIMIT_KEY = "SELLFOX_POST_SYNC_LIMIT_KEY:%s:%s";

    /**
     * tiktok广告达人列表key=puid_advertiserId_storeId_bcId
     */
    public static final String TIKTOK_IDENTITY_GET = "TIKTOK_AD_AUTH_UUID:%s_%s_%s_%s";
}
