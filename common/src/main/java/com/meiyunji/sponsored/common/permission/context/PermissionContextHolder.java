package com.meiyunji.sponsored.common.permission.context;

import java.util.ArrayList;
import java.util.HashMap;

/**
 * 权限上下文持有者
 * 使用ThreadLocal存储权限上下文信息
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public class PermissionContextHolder {

    private static final ThreadLocal<PermissionContext> CONTEXT_HOLDER = new ThreadLocal<>();

    /**
     * 设置权限上下文
     */
    public static void setContext(PermissionContext context) {
        CONTEXT_HOLDER.set(context);
    }

    /**
     * 获取权限上下文
     */
    public static PermissionContext getContext() {
        return CONTEXT_HOLDER.get();
    }

    /**
     * 清理权限上下文
     */
    public static void clear() {
        CONTEXT_HOLDER.remove();
    }

    /**
     * 检查是否有权限上下文
     */
    public static boolean hasContext() {
        return CONTEXT_HOLDER.get() != null;
    }


    /**
     * 深拷贝权限上下文
     */
    public static PermissionContext copyPermissionContext() {
        try {
            PermissionContext original = PermissionContextHolder.getContext();
            if (original == null) {
                return null;
            }

            // 创建深拷贝
            return PermissionContext.builder()
                    .puid(original.getPuid())
                    .uid(original.getUid())
                    .isAdminUser(original.getIsAdminUser())
                    .shopIds(original.getShopIds() != null ? new ArrayList<>(original.getShopIds()) : null)
                    .adType(original.getAdType() != null ? new ArrayList<>(original.getAdType()) : null)
                    .filterType(original.getFilterType())
                    .filterStrategy(original.getFilterStrategy())
                    .methodName(original.getMethodName())
                    .className(original.getClassName())
                    .campaignId(original.getCampaignId())
                    // requestParams   这里如果深拷贝会影响性能  暂时不处理,目前用法不涉及到修改共享变量 仅存在读
                    .requestParams(original.getRequestParams() != null ?
                            new HashMap<>(original.getRequestParams()) : null)
                    .build();
        } catch (Exception e) {
            return null;
        }
    }
}
