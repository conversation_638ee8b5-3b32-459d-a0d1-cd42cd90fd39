package com.meiyunji.sponsored.common.util;


import com.meiyunji.sponsored.common.base.Result;

/**
 * 返回值工具类
 */
public class ResultUtil {
    public static int SUCCESS = 0;
    public static int E_NO_CONTENT = 100;

    /**
     * 成功
     *
     * @param object 成功返回的数据
     * @return result
     */
    public static <T> Result<T> success(T object) {
        Result<T> result = new Result<T>();
        result.setCode(0);
        result.setMsg("success");
        result.setData(object);
        return result;
    }

    /**
     * 成功
     *
     * @return result
     */
    public static <T> Result<T> success() {
        return success(null);
    }

    /**
     * 成功
     * @return result<T>
     */
    public static <T> Result<T> returnSucc(T obj) {
        return new Result<>(0, "", obj);
    }

    /**
     * 成功
     *
     * @param msg 成功的消息
     * @return result
     */
    public static <T> Result<T> success(String msg) {
        Result<T> result = new Result<T>();
        result.setCode(0);
        result.setMsg(msg);
        return result;
    }

    /**
     * 成功
     *
     * @param msg    成功的消息
     * @param object 返回的数据
     * @return result
     */
    public static <T> Result<T> success(String msg, T object) {
        Result<T> result = new Result<T>();
        result.setCode(0);
        result.setMsg(msg);
        result.setData(object);
        return result;
    }

    /**
     * 失败
     * @return result<T>
     */
    public static <T> Result<T> returnErr(String msg) {
        return new Result<>(-1, msg, null);
    }

    /**
     * 失败
     *
     * @param code 失败错误码,自定义
     * @param msg  失败错误信息
     * @return result
     */
    public static <T> Result<T> error(Integer code, String msg) {
        Result<T> result = new Result<T>();
        result.setCode(code);
        result.setMsg(msg);
        return result;
    }

    /**
     * 失败,默认code=-1
     *
     * @param msg 失败原因
     * @return result
     */
    public static <T> Result<T> error(String msg) {
        Result<T> result = new Result<T>();
        result.setCode(-1);
        result.setMsg(msg);
        return result;
    }

    /**
     * 失败
     *
     * @param code 失败错误码
     * @param data 失败返回的数据
     * @return result
     */
    public static <T> Result<T> error(Integer code, T data) {
        Result<T> result = new Result<T>();
        result.setCode(code);
        result.setData(data);
        return result;
    }

    /**
     * 失败,默认code=-1
     *
     * @return result
     */
    public static <T> Result<T> error() {
        Result<T> result = new Result<T>();
        result.setCode(-1);
        return result;
    }

    public static <T> Result<T> successWithData(T object) {
        Result<T> result = new Result<T>();
        result.setCode(0);
        result.setMsg("success");
        result.setData(object);
        return result;
    }

    public static <T> Result<T> error(T data) {
        Result<T> result = new Result<>();
        result.setCode(-1);
        result.setData(data);
        return result;
    }
}
