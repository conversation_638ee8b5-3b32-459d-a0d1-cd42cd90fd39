package com.meiyunji.sponsored.common.config;

import com.google.common.collect.Sets;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 动态刷新nacos配置
 *
 * <AUTHOR>
 * @date 2023/07/28
 */
@RefreshScope
@Component
public class DynamicRefreshNacosConfiguration {

    //冷热数据分离二期表，因一二期分开上，所以二期需要单独配置，不能共用一期的配置
    public static Set<String> phase2Table = Sets.newHashSet(
            "t_cpc_query_keyword_report",
            "t_cpc_query_targeting_report",
            "t_cpc_targeting_report",
            "t_amazon_ad_group_report",
            "t_amazon_ad_sb_keyword_report"
    );


    @Value("${sync.task.size:2000}")
    private Integer syncSize;

    public Integer getSyncSize() {
        return syncSize;
    }

    public void setSyncSize(Integer syncSize) {
        this.syncSize = syncSize;
    }


    /**
     * kafka的报告消息数量
     */
    @Value("${routine.load.size:50}")
    private Integer routineLoadSize;

    public Integer getRoutineLoadSize() {
        return routineLoadSize;
    }

    public void setRoutineLoadSize(Integer routineLoadSize) {
        this.routineLoadSize = routineLoadSize;
    }

    /**
     * 冷热数据分离是否写入hot表，默认否
     */
    @Value("${hotTable.enable.write:false}")
    private boolean hotTableWriteEnable;

    /**
     * 冷热数据分离是否写入hot表，默认否
     */
    @Value("${hotTable.enable.query:false}")
    private boolean hotTableQueryEnable;

    /**
     * 冷热数据分离二期是否写入hot表，默认否
     */
    @Value("${hotTable.enable.writePhase2:false}")
    private boolean hotTableWritePhase2Enable;

    /**
     * 冷热数据分离二期是否写入hot表，默认否
     */
    @Value("${hotTable.enable.queryPhase2:false}")
    private boolean hotTableQueryPhase2Enable;

    @Value("${sponsored.multi.query.thread.size:500}")
    private Integer MAX_CAMPAIGN_SIZE;

    @Value("${sync.data.size:2000}")
    private Integer syncDataSize;

    @Value("${perspective.enableHotAsin:false}")
    private boolean perspectiveEnableHotAsin;

    @Value("${perspective.initAsinShopIdPartition:30}")
    private Integer initAsinShopIdPartition;

    @Value("${amazon.suggestBid.enable:false}")
    private Boolean amazonProxyEnable;

    @Value("${doris.realTimeSyncMode:close}")
    private String dorisRealTimeSyncMode;

    @Value("#{'${mysql.shardRule:500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1}'.empty ? null : '${mysql.shardRule:500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1}'.split(',')}")
    private List<String> shardRule;


    @Value("${tag.initDate:2024-10-24 15:00:00}")
    private String tagInitDate;

    public String getTagInitDate() {
        return tagInitDate;
    }

    public void setTagInitDate(String tagInitDate) {
        this.tagInitDate = tagInitDate;
    }

    @Value("#{'${mysql.shardRule:1000,900,800,700,600,500,400,300,200,100,20,5,4,3,2,1}'.empty ? null : '${mysql.shardRule:1000,900,800,700,600,500,400,300,200,100,20,5,4,3,2,1}'.split(',')}")
    private List<String> shardRule2;

    @Value("${mysql.shardRule.enable:false}")
    private Boolean shardRuleEnable;

    public List<String> getShardRule() {
        return shardRule;
    }

    public void setShardRule(List<String> shardRule) {
        this.shardRule = shardRule;
    }

    public Boolean getShardRuleEnable() {
        return shardRuleEnable;
    }

    public void setShardRuleEnable(Boolean shardRuleEnable) {
        this.shardRuleEnable = shardRuleEnable;
    }

    @Value("${reportTriggerAutorule.grayPuid:NONE}")
    private String reportTriggerAutoruleGrayPuid;

    @Value("${reportTriggerAutorule.sbsd.grayPuid:NONE}")
    private String reportTriggerAutoruleSbSdGrayPuid;

    @Value("${reportTriggerAutorule.grayPercent:0}")
    private Integer reportTriggerAutoruleGrayPercent;

    @Value("${reportTriggerAutorule.sbsd.grayPercent:0}")
    private Integer reportTriggerAutoruleSbSdGrayPercent;

    @Value("${reportTriggerAutorule.partitionSize:100}")
    private Integer reportTriggerAutorulePartitionSize;

    @Value("${reportTriggerAutorule.groupTargetPartitionSize:50}")
    private Integer reportTriggerAutoruleGroupTargetPartitionSize;

    @Value("${reportTriggerAutorule.groupKeywordPartitionSize:20}")
    private Integer reportTriggerAutoruleGroupKeywordPartitionSize;

    @Value("${reportTriggerAutorule.warning:false}")
    private boolean reportTriggerAutoRuleWarning;

    @Value("${tag.count:20}")
    private Integer tagCount;

    @Value("${tag.nameSize:20}")
    private Integer tagNameSize;


    public Integer getTagCount() {
        return tagCount;
    }

    public void setTagCount(Integer tagCount) {
        this.tagCount = tagCount;
    }

    public Integer getTagNameSize() {
        return tagNameSize;
    }

    public void setTagNameSize(Integer tagNameSize) {
        this.tagNameSize = tagNameSize;
    }

    @Value("${reportTriggerAutorule.costWarningSeconds:300}")
    private Integer costWarningSeconds;

    @Value("${reportTriggerAutorule.spUsePulsar:true}")
    private boolean reportTriggerAutoRuleSpUsePulsar;

    @Value("${autoruleCallAadras.useKafka.consistency:true}")
    private boolean autoruleCallAadrasUseKafkaConsistency;

    @Value("${autoruleCallAadras.useKafka.consistencyMaxRequestSize:1}")
    private int autoruleCallAadrasUseKafkaConsistencyMaxRequestSize;

    @Value("${autoruleCallAadras.useKafka.messageRequestSizeThresholdPercent:70}")
    private int autoruleCallAadrasUseKafkaMessageRequestSizeThresholdPercent;

    @Value("${autoruleCallAadras.useKafka.grayPuid:NONE}")
    private String autoruleCallAadrasUseKafkaGrayPuid;

    @Value("${autoruleCallAadras.useKafka.grayPercent:0}")
    private Integer autoruleCallAadrasUseKafkaGrayPercent;

    @Value("${log.routineLoad.max.request.size:5}")
    private int logRoutineLoadMaxRequestSize;

    @Value("${druid.filter.whiteSql:}")
    private Set<String> druidFilterWhiteSqls;

    public Set<String> getDruidFilterWhiteSqls() {
        return druidFilterWhiteSqls;
    }

    public void setDruidFilterWhiteSqls(Set<String> druidFilterWhiteSqls) {
        this.druidFilterWhiteSqls = druidFilterWhiteSqls;
    }

    public int getLogRoutineLoadMaxRequestSize() {
        return logRoutineLoadMaxRequestSize;
    }

    public void setLogRoutineLoadMaxRequestSize(int logRoutineLoadMaxRequestSize) {
        this.logRoutineLoadMaxRequestSize = logRoutineLoadMaxRequestSize;
    }


    @Value("${post.whitelist:}")
    private String postWhitelist;

    public int getPostSyncRetry() {
        return postSyncRetry;
    }

    public int getPostProfileSyncRetry() {
        return postProfileSyncRetry;
    }

    public int getPostReportSyncRetry() {
        return postReportSyncRetry;
    }

    @Value("${post.syncPost.retry:30}")
    private int postSyncRetry;

    @Value("${post.syncProfilePost.retry:10}")
    private int postProfileSyncRetry;

    @Value("${post.postReportSyncRetry.retry:10}")
    private int postReportSyncRetry;

    @Value("${post.postPercentage:0}")
    private int postPercentage;

    public Integer getSyncDataSize() {
        return syncDataSize;
    }

    public void setSyncDataSize(Integer syncDataSize) {
        this.syncDataSize = syncDataSize;
    }

    public Boolean getAmazonProxyEnable() {
        return amazonProxyEnable;
    }

    public void setAmazonProxyEnable(Boolean amazonProxyEnable) {
        this.amazonProxyEnable = amazonProxyEnable;
    }

    public boolean isHotTableWriteEnable() {
        return hotTableWriteEnable;
    }

    public void setHotTableWriteEnable(boolean hotTableWriteEnable) {
        this.hotTableWriteEnable = hotTableWriteEnable;
    }

    public boolean isHotTableQueryEnable() {
        return hotTableQueryEnable;
    }

    public void setHotTableQueryEnable(boolean hotTableQueryEnable) {
        this.hotTableQueryEnable = hotTableQueryEnable;
    }

    public boolean isHotTableWritePhase2Enable() {
        return hotTableWritePhase2Enable;
    }

    public void setHotTableWritePhase2Enable(boolean hotTableWritePhase2Enable) {
        this.hotTableWritePhase2Enable = hotTableWritePhase2Enable;
    }

    public boolean isHotTableQueryPhase2Enable() {
        return hotTableQueryPhase2Enable;
    }

    public void setHotTableQueryPhase2Enable(boolean hotTableQueryPhase2Enable) {
        this.hotTableQueryPhase2Enable = hotTableQueryPhase2Enable;
    }

    public Integer getMAX_CAMPAIGN_SIZE() {
        return MAX_CAMPAIGN_SIZE;
    }

    public boolean isPerspectiveEnableHotAsin() {
        return perspectiveEnableHotAsin;
    }

    public Integer getInitAsinShopIdPartition() {
        return initAsinShopIdPartition;
    }

    public String getDorisRealTimeSyncMode() {
        return dorisRealTimeSyncMode;
    }

    public String getReportTriggerAutoruleGrayPuid() {
        return reportTriggerAutoruleGrayPuid;
    }

    public String getReportTriggerAutoruleSbSdGrayPuid() {
        return reportTriggerAutoruleSbSdGrayPuid;
    }

    public Integer getReportTriggerAutoruleGrayPercent() {
        return reportTriggerAutoruleGrayPercent;
    }

    public Integer getReportTriggerAutoruleSbSdGrayPercent() {
        return reportTriggerAutoruleSbSdGrayPercent;
    }

    public Integer getReportTriggerAutorulePartitionSize() {
        return reportTriggerAutorulePartitionSize;
    }

    public Integer getReportTriggerAutoruleGroupTargetPartitionSize() {
        return reportTriggerAutoruleGroupTargetPartitionSize;
    }

    public Integer getReportTriggerAutoruleGroupKeywordPartitionSize() {
        return reportTriggerAutoruleGroupKeywordPartitionSize;
    }

    public boolean getReportTriggerAutoRuleWarning() {
        return reportTriggerAutoRuleWarning;
    }

    public Integer getCostWarningSeconds() {
        return costWarningSeconds;
    }

    public List<String> getShardRule2() {
        return shardRule2;
    }

    public boolean isAutoruleCallAadrasUseKafkaConsistency() {
        return autoruleCallAadrasUseKafkaConsistency;
    }

    public int getAutoruleCallAadrasUseKafkaConsistencyMaxRequestSize() {
        return autoruleCallAadrasUseKafkaConsistencyMaxRequestSize;
    }

    public int getAutoruleCallAadrasUseKafkaMessageRequestSizeThresholdPercent() {
        return autoruleCallAadrasUseKafkaMessageRequestSizeThresholdPercent;
    }

    public String getAutoruleCallAadrasUseKafkaGrayPuid() {
        return autoruleCallAadrasUseKafkaGrayPuid;
    }

    public Integer getAutoruleCallAadrasUseKafkaGrayPercent() {
        return autoruleCallAadrasUseKafkaGrayPercent;
    }

    public List<Integer> getPostWhitelist() {
        if (StringUtils.isEmpty(postWhitelist)) {
            return Collections.emptyList();
        } else {
            return Arrays.stream(StringUtils.split(postWhitelist, ",")).map(NumberUtils::toInt).collect(Collectors.toList());
        }
    }

    public Integer getPostPercentage() {
        return postPercentage;
    }
}
