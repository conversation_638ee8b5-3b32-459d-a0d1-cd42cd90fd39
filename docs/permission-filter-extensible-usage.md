# 扩展性权限过滤使用指南

## 概述

重构后的权限过滤系统具有良好的扩展性和可读性，支持多种权限过滤类型：

- **Campaign权限过滤**：基于活动ID的权限控制
- **ASIN权限过滤**：基于产品ASIN的权限控制
- **可扩展设计**：易于添加新的权限过滤类型

## 核心设计

### 1. 权限过滤类型枚举

```java
public enum PermissionFilterType {
    CAMPAIGN("CAMPAIGN", "活动权限过滤"),
    ASIN("ASIN", "ASIN权限过滤"),
    PRODUCT("PRODUCT", "产品权限过滤"),
    SHOP("SHOP", "店铺权限过滤");
}
```

### 2. 权限过滤器接口

```java
public interface PermissionFilter {
    PermissionFilterType getSupportedType();
    boolean supports(PermissionFilterType filterType);
    String generateFilterSql(String fieldName, PermissionContext context, List<Object> argsList);
    boolean validateContext(PermissionContext context);
}
```

### 3. 通用权限SQL构建器

```java
public class PermissionSqlBuilder {
    // 通用方法
    public static StringBuilder addPermissionFilter(StringBuilder sql, List<Object> argsList, 
                                                   PermissionFilterType filterType, String fieldName);
    
    // 便捷方法
    public static StringBuilder addCampaignFilter(StringBuilder sql, List<Object> argsList, String campaignField);
    public static StringBuilder addAsinFilter(StringBuilder sql, List<Object> argsList, String asinField);
}
```

## 使用方式

### 1. Campaign权限过滤

```java
@Repository
public class CampaignDaoImpl {

    @CampaignPermissionFilter(
        campaignField = "campaign_id",
        shopIdsExpression = "#reqParam.shopIds", 
        adTypeExpression = "#reqParam.adType"
    )
    public List<Campaign> getCampaignsByCondition(CampaignQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT * FROM ods_t_amazon_ad_campaign_all ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 添加Campaign权限过滤
        PermissionSqlBuilder.addCampaignFilter(sql, argsList, "campaign_id");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

### 2. ASIN权限过滤

```java
@Repository
public class ProductDaoImpl {

    @AsinPermissionFilter(
        asinField = "asin",
        shopIdsExpression = "#reqParam.shopIds"
    )
    public List<Product> getProductsByAsin(ProductQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT * FROM ods_t_amazon_ad_product ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 添加ASIN权限过滤
        PermissionSqlBuilder.addAsinFilter(sql, argsList, "asin");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

### 3. 通用权限过滤

```java
@Repository
public class ReportDaoImpl {

    @CampaignPermissionFilter(
        campaignField = "r.campaign_id",
        shopIdsExpression = "#reqParam.shopIds", 
        adTypeExpression = "#reqParam.adType"
    )
    public List<ReportDto> getCampaignReport(ReportQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT r.*, p.asin ");
        sql.append("FROM ods_t_cpc_campaign_report r ");
        sql.append("LEFT JOIN ods_t_amazon_ad_product p ON r.campaign_id = p.campaign_id ");
        sql.append("WHERE r.puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 使用通用方法添加权限过滤
        PermissionSqlBuilder.addPermissionFilter(sql, argsList, PermissionFilterType.CAMPAIGN, "r.campaign_id");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

## 扩展新的权限过滤类型

### 1. 创建新的权限过滤器

```java
@Component
public class ShopPermissionFilter implements PermissionFilter {
    
    @Override
    public PermissionFilterType getSupportedType() {
        return PermissionFilterType.SHOP;
    }
    
    @Override
    public boolean supports(PermissionFilterType filterType) {
        return PermissionFilterType.SHOP == filterType;
    }
    
    @Override
    public String generateFilterSql(String fieldName, PermissionContext context, List<Object> argsList) {
        // 实现店铺权限过滤逻辑
        List<Integer> allowedShopIds = getUserAllowedShopIds(context);
        if (CollectionUtils.isEmpty(allowedShopIds)) {
            return "";
        }
        
        StringBuilder sql = new StringBuilder();
        sql.append("(").append(fieldName).append(" IN (");
        for (int i = 0; i < allowedShopIds.size(); i++) {
            if (i > 0) sql.append(",");
            sql.append("?");
            argsList.add(allowedShopIds.get(i));
        }
        sql.append("))");
        
        return sql.toString();
    }
    
    @Override
    public boolean validateContext(PermissionContext context) {
        return context != null && context.getPuid() != null && context.getUid() != null;
    }
    
    private List<Integer> getUserAllowedShopIds(PermissionContext context) {
        // 实现获取用户允许访问的店铺ID列表的逻辑
        return context.getShopIds();
    }
}
```

### 2. 创建新的注解

```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface ShopPermissionFilter {
    String shopField() default "shop_id";
    String shopIdsExpression() default "#reqParam.shopIds";
    boolean enabled() default true;
}
```

### 3. 扩展切面

```java
@Aspect
@Component
public class PermissionFilterAspect {
    
    @Around("@annotation(shopPermissionFilter)")
    public Object interceptShopPermission(ProceedingJoinPoint joinPoint, 
                                        ShopPermissionFilter shopPermissionFilter) throws Throwable {
        return executeWithPermissionContext(joinPoint, () -> 
            buildShopPermissionContext(joinPoint, shopPermissionFilter));
    }
    
    private PermissionContext buildShopPermissionContext(ProceedingJoinPoint joinPoint, 
                                                       ShopPermissionFilter annotation) {
        // 构建店铺权限上下文
        return PermissionContext.builder()
                .filterType(PermissionFilterType.SHOP)
                // ... 其他字段
                .build();
    }
}
```

### 4. 添加便捷方法

```java
public class PermissionSqlBuilder {
    
    /**
     * 添加店铺权限过滤
     */
    public static StringBuilder addShopFilter(StringBuilder sql, List<Object> argsList, String shopField) {
        return addPermissionFilter(sql, argsList, PermissionFilterType.SHOP, shopField);
    }
    
    /**
     * 添加店铺权限过滤（使用默认字段名）
     */
    public static StringBuilder addShopFilter(StringBuilder sql, List<Object> argsList) {
        return addShopFilter(sql, argsList, "shop_id");
    }
}
```

## 实际使用示例

### 示例1：活动报表查询（Campaign过滤）

```java
@Repository
public class CampaignReportDaoImpl {

    @CampaignPermissionFilter(
        campaignField = "r.campaign_id",
        shopIdsExpression = "#qo.shopIds", 
        adTypeExpression = "#qo.adTypeList"
    )
    public List<CampaignReportDto> getCampaignReport(CampaignReportQo qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT r.campaign_id, r.impressions, r.clicks, r.cost ");
        sql.append("FROM ods_t_cpc_campaign_report r ");
        sql.append("WHERE r.puid = ? AND r.report_date >= ? ");
        argsList.add(qo.getPuid());
        argsList.add(qo.getStartDate());
        
        // Campaign权限过滤
        PermissionSqlBuilder.addCampaignFilter(sql, argsList, "r.campaign_id");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

### 示例2：产品查询（ASIN过滤）

```java
@Repository
public class ProductDaoImpl {

    @AsinPermissionFilter(
        asinField = "p.asin",
        shopIdsExpression = "#request.shopIds"
    )
    public List<ProductDto> getProductsByShop(ProductQueryRequest request) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT p.asin, p.title, p.price ");
        sql.append("FROM ods_t_amazon_ad_product p ");
        sql.append("WHERE p.puid = ? AND p.shop_id = ? ");
        argsList.add(request.getPuid());
        argsList.add(request.getShopId());
        
        // ASIN权限过滤
        PermissionSqlBuilder.addAsinFilter(sql, argsList, "p.asin");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

### 示例3：关键词查询（多重过滤）

```java
@Repository
public class KeywordDaoImpl {

    @CampaignPermissionFilter(
        campaignField = "k.campaign_id",
        shopIdsExpression = "#qo.shopIds", 
        adTypeExpression = "#qo.adType"
    )
    public List<KeywordDto> getKeywordsByCampaign(KeywordQueryRequest qo) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT k.*, p.asin ");
        sql.append("FROM ods_t_amazon_ad_keyword k ");
        sql.append("LEFT JOIN ods_t_amazon_ad_product p ON k.campaign_id = p.campaign_id ");
        sql.append("WHERE k.puid = ? ");
        argsList.add(qo.getPuid());
        
        // Campaign权限过滤
        PermissionSqlBuilder.addCampaignFilter(sql, argsList, "k.campaign_id");
        
        // 如果需要，还可以添加ASIN权限过滤
        // PermissionSqlBuilder.addAsinFilter(sql, argsList, "p.asin");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

## 优势

### 1. 扩展性良好
- **插件化设计**：新的权限过滤类型只需实现 `PermissionFilter` 接口
- **自动注册**：Spring自动发现和注册权限过滤器
- **类型安全**：使用枚举确保类型安全

### 2. 可读性良好
- **清晰的接口**：每个权限过滤器职责单一
- **统一的API**：所有权限过滤都使用相同的调用方式
- **明确的注解**：不同类型的权限过滤使用不同的注解

### 3. 维护性强
- **解耦设计**：权限过滤逻辑与业务逻辑分离
- **集中管理**：权限过滤器统一管理
- **易于测试**：每个组件都可以独立测试

## 注意事项

1. **注解必须匹配**：使用 `@CampaignPermissionFilter` 时只能调用 `addCampaignFilter`
2. **类型检查**：权限上下文的 `filterType` 必须与调用的方法匹配
3. **参数验证**：每个权限过滤器都会验证上下文参数的完整性
4. **性能考虑**：复杂的权限过滤可能影响查询性能，建议优化数据库索引
