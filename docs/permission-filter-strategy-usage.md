# 权限过滤策略使用指南

## 概述

优化后的 `PermissionFilterAspect#executeWithPermissionContext` 方法现在支持两种权限过滤策略：

1. **SQL_INJECTION（SQL注入策略）**：在SQL构建时注入权限过滤条件（默认策略）
2. **RESULT_FILTER（结果过滤策略）**：在方法执行后对结果进行权限过滤

## 主要优化点

### 1. 策略感知的执行流程

```java
private Object executeWithPermissionContext(ProceedingJoinPoint joinPoint,
                                           ContextBuilder contextBuilder) throws Throwable {
    try {
        // 1. 构建权限上下文（包含策略信息）
        PermissionContext context = contextBuilder.build();
        
        if (context != null) {
            // 2. 设置权限上下文到ThreadLocal
            PermissionContextHolder.setContext(context);
            
            // 3. 根据策略进行预处理
            handlePermissionStrategy(context, joinPoint);
        }

        // 4. 执行原方法
        Object result = joinPoint.proceed();

        // 5. 如果是结果过滤策略，对结果进行后处理
        if (context != null && isResultFilterStrategy(context.getFilterStrategy())) {
            result = handleResultFilter(context, result);
        }

        return result;
    } finally {
        // 6. 清理权限上下文
        PermissionContextHolder.clear();
    }
}
```

### 2. 策略处理逻辑

```java
private void handlePermissionStrategy(PermissionContext context, ProceedingJoinPoint joinPoint) {
    PermissionFilterStrategy strategy = PermissionFilterStrategy.fromCode(context.getFilterStrategy());
    
    switch (strategy) {
        case SQL_INJECTION:
            // SQL注入策略：权限过滤在SQL构建时处理，这里不需要额外操作
            logger.debug("SQL注入策略：权限过滤将在SQL构建时处理");
            break;
            
        case RESULT_FILTER:
            // 结果过滤策略：在方法执行后处理结果
            logger.debug("结果过滤策略：权限过滤将在方法执行后处理");
            break;
    }
}
```

## 使用示例

### 1. SQL注入策略（默认）

```java
@Repository
public class CampaignDaoImpl {

    @CampaignPermissionFilter(
        campaignField = "campaign_id",
        shopIdsExpression = "#reqParam.shopIds", 
        adTypeExpression = "#reqParam.adType",
        strategy = FilterStrategy.SQL_INJECTION  // 默认策略
    )
    public List<Campaign> getCampaignsByCondition(CampaignQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT * FROM ods_t_amazon_ad_campaign_all ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 权限过滤SQL会自动注入到这里
        PermissionSqlBuilder.addCampaignFilter(sql, argsList, "campaign_id");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

### 2. 结果过滤策略

```java
@Repository
public class CampaignDaoImpl {

    @CampaignPermissionFilter(
        campaignField = "campaign_id",
        shopIdsExpression = "#reqParam.shopIds", 
        adTypeExpression = "#reqParam.adType",
        strategy = FilterStrategy.RESULT_FILTER  // 结果过滤策略
    )
    public List<Campaign> getCampaignsByCondition(CampaignQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT * FROM ods_t_amazon_ad_campaign_all ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 不需要添加权限过滤SQL，结果会在方法执行后过滤
        List<Campaign> results = getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
        
        // 权限过滤会在 executeWithPermissionContext 中自动处理
        return results;
    }
}
```

### 3. ASIN权限过滤

```java
@Repository
public class ProductDaoImpl {

    @AsinPermissionFilter(
        asinField = "asin",
        shopIdsExpression = "#reqParam.shopIds",
        strategy = FilterStrategy.SQL_INJECTION
    )
    public List<Product> getProductsByAsin(ProductQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        sql.append("SELECT * FROM ods_t_amazon_ad_product ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // ASIN权限过滤
        PermissionSqlBuilder.addAsinFilter(sql, argsList, "asin");
        
        return getJdbcTemplate().query(sql.toString(), getRowMapper(), argsList.toArray());
    }
}
```

## 策略选择建议

### SQL注入策略（推荐）
- **优点**：性能最好，在数据库层面过滤，减少数据传输
- **适用场景**：大部分查询场景，特别是数据量大的查询
- **限制**：需要能够修改SQL语句

### 结果过滤策略
- **优点**：灵活性高，适用于复杂的权限逻辑
- **适用场景**：复杂查询、无法修改SQL的场景、需要复杂权限判断的场景
- **限制**：性能相对较差，需要在应用层处理

## 配置示例

```java
// 权限上下文会自动包含策略信息
PermissionContext context = PermissionContext.builder()
    .puid(puid)
    .uid(uid)
    .isAdminUser(isAdminUser)
    .shopIds(shopIds)
    .adType(adType)
    .filterType(PermissionFilterType.CAMPAIGN)
    .filterStrategy("SQL_INJECTION")  // 策略信息
    .methodName(method.getName())
    .className(method.getDeclaringClass().getSimpleName())
    .requestParams(requestParams)
    .build();
```

## 日志输出

优化后的方法会输出更详细的日志信息：

```
DEBUG - 设置权限上下文: type=CAMPAIGN, strategy=SQL_INJECTION, puid=123, uid=456, shopIds=[1,2,3]
DEBUG - SQL注入策略：权限过滤将在SQL构建时处理
DEBUG - 成功添加CAMPAIGN权限过滤(策略:SQL_INJECTION): field=campaign_id
```

## 扩展性

当前的优化保持了良好的扩展性，未来可以轻松添加新的策略：

1. 在 `PermissionFilterStrategy` 枚举中添加新策略
2. 在 `handlePermissionStrategy` 方法中添加对应的处理逻辑
3. 根据需要实现策略特定的处理器

这种设计既满足了当前的需求，又为未来的扩展留下了空间，同时避免了过度设计和类的膨胀。
