# 权限过滤组件关系与使用示例

## 🏗️ 组件关系图

```mermaid
sequenceDiagram
    participant Client as 客户端调用
    participant Annotation as @AdProductPermissionFilter
    participant Aspect as PermissionFilterAspect
    participant Factory as PermissionFilterFactory
    participant Filter as PermissionFilter
    participant SqlBuilder as PermissionSqlBuilder
    participant Service as AdProductRightService
    participant DA<PERSON> as DAO层
    participant DB as 数据库

    Note over Client,DB: 1. 权限过滤流程
    Client->>+Annotation: 调用带注解的方法
    Annotation->>+Aspect: 触发AOP拦截
    
    Note over Aspect: 2. 权限上下文构建
    Aspect->>Aspect: buildPermissionContext()
    Aspect->>Aspect: 设置权限上下文到ThreadLocal
    
    Note over Aspect,DAO: 3. 执行业务方法
    Aspect->>+DAO: 执行原方法
    
    Note over DAO,DB: 4. SQL权限过滤
    DAO->>+SqlBuilder: addPermissionFilter()
    SqlBuilder->>+Factory: 获取过滤器
    Factory->>+Filter: 创建对应类型过滤器
    Filter->>+Service: 调用权限服务
    Service->>+DB: 查询权限数据
    DB-->>-Service: 返回权限SQL
    Service-->>-Filter: 返回过滤SQL
    Filter-->>-SqlBuilder: 返回SQL片段
    SqlBuilder-->>-DAO: 返回完整SQL
    DAO->>+DB: 执行带权限的SQL
    DB-->>-DAO: 返回过滤后数据
    
    DAO-->>-Aspect: 返回结果
    Aspect-->>-Client: 返回最终结果
```

## 🔗 组件职责详解

### 1. AdProductPermissionFilter（注解层）
```java
/**
 * 权限过滤注解 - 声明式配置
 * 职责：定义权限过滤的元数据
 */
@AdProductPermissionFilter(
    type = PermissionFilterType.CAMPAIGN,     // 权限类型
    field = "campaign_id",                    // 权限字段
    shopIdsExpression = "#reqParam.shopIds",  // 店铺表达式
    adTypeExpression = "#reqParam.adType",    // 广告类型表达式
    strategy = PermissionFilterStrategy.SQL_INJECTION
)
```

### 2. PermissionFilterAspect（切面层）
```java
/**
 * 权限过滤切面 - AOP拦截器
 * 职责：
 * 1. 拦截带注解的方法
 * 2. 构建权限上下文
 * 3. 协调整个过滤流程
 */
@Component
@Aspect
public class PermissionFilterAspect {
    
    @Around("@annotation(adProductPermissionFilter)")
    public Object interceptAdProductPermission(ProceedingJoinPoint joinPoint,
                                             AdProductPermissionFilter adProductPermissionFilter) {
        // 1. 构建权限上下文
        PermissionContext context = buildAdProductPermissionContext(joinPoint, adProductPermissionFilter);
        
        // 2. 设置到ThreadLocal
        PermissionContextHolder.setContext(context);
        
        try {
            // 3. 执行原方法
            return joinPoint.proceed();
        } finally {
            // 4. 清理上下文
            PermissionContextHolder.clear();
        }
    }
}
```

### 3. PermissionFilterFactory（工厂层）
```java
/**
 * 权限过滤器工厂 - 过滤器管理
 * 职责：根据权限类型创建对应的过滤器实例
 */
@Component
public class PermissionFilterFactory {
    
    @Autowired
    private List<PermissionFilter> permissionFilters;
    
    private final Map<PermissionFilterType, PermissionFilter> filterMap = new HashMap<>();
    
    public PermissionFilter getFilter(PermissionFilterType filterType) {
        return filterMap.get(filterType);
    }
}
```

### 4. PermissionFilter（过滤器接口）
```java
/**
 * 权限过滤器接口 - 过滤行为定义
 * 职责：定义权限过滤的标准行为
 */
public interface PermissionFilter {
    
    /**
     * 获取支持的过滤类型
     */
    PermissionFilterType getSupportedType();
    
    /**
     * 生成权限过滤SQL
     */
    String generateFilterSql(String fieldName, PermissionContext context, List<Object> argsList);
}
```

### 5. PermissionSqlBuilder（SQL构建层）
```java
/**
 * 权限SQL构建器 - SQL注入工具
 * 职责：将权限条件注入到SQL中
 */
@Component
public class PermissionSqlBuilder {
    
    public static StringBuilder addPermissionFilter(StringBuilder sql, 
                                                   List<Object> argsList, 
                                                   String fieldName) {
        // 1. 获取权限上下文
        PermissionContext context = PermissionContextHolder.getContext();
        
        // 2. 获取对应的过滤器
        PermissionFilter filter = filterFactory.getFilter(context.getFilterType());
        
        // 3. 生成权限过滤SQL
        String permissionSql = filter.generateFilterSql(fieldName, context, argsList);
        
        // 4. 注入到原SQL中
        if (StringUtils.isNotBlank(permissionSql)) {
            sql.append(" AND ").append(permissionSql);
        }
        
        return sql;
    }
}
```

## 🎯 使用示例

### 示例1：Campaign权限过滤

```java
@Repository
public class CampaignDaoImpl {

    /**
     * 权限过滤示例 - Campaign数据
     */
    @AdProductPermissionFilter(
        type = PermissionFilterType.CAMPAIGN,
        field = "campaign_id",
        shopIdsExpression = "#reqParam.shopIds",
        adTypeExpression = "#reqParam.adType",
        strategy = PermissionFilterStrategy.SQL_INJECTION,
        description = "活动数据权限过滤"
    )
    public List<Campaign> getCampaignsByCondition(CampaignQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        // 1. 构建基础SQL
        sql.append("SELECT campaign_id, campaign_name, campaign_type ");
        sql.append("FROM ods_t_amazon_ad_campaign_all ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 2. 添加业务条件
        if (StringUtils.isNotBlank(reqParam.getCampaignName())) {
            sql.append("AND campaign_name LIKE ? ");
            argsList.add("%" + reqParam.getCampaignName() + "%");
        }
        
        // 3. 添加权限过滤（关键步骤）
        PermissionSqlBuilder.addPermissionFilter(sql, argsList, "campaign_id");
        
        // 4. 执行查询
        return getJdbcTemplate().query(sql.toString(), getCampaignRowMapper(), argsList.toArray());
    }
}
```

**执行流程：**
1. `@AdProductPermissionFilter` 触发 `PermissionFilterAspect` 拦截
2. 构建权限上下文，包含用户信息、店铺ID、广告类型等
3. 执行 `getCampaignsByCondition` 方法
4. 调用 `PermissionSqlBuilder.addPermissionFilter()` 注入权限条件
5. 最终SQL变为：
```sql
SELECT campaign_id, campaign_name, campaign_type 
FROM ods_t_amazon_ad_campaign_all 
WHERE puid = ? 
  AND campaign_name LIKE ? 
  AND campaign_id IN (
    SELECT campaign_id FROM ods_amazon_ad_product_permission 
    WHERE puid = ? AND uid = ? AND shop_id IN (?, ?, ?)
  )
```

### 示例2：ASIN权限过滤

```java
@Repository
public class ProductDaoImpl {

    /**
     * 权限过滤示例 - ASIN数据
     */
    @AdProductPermissionFilter(
        type = PermissionFilterType.ASIN,
        field = "asin",
        shopIdsExpression = "#reqParam.shopIds",
        strategy = PermissionFilterStrategy.SQL_INJECTION,
        description = "ASIN产品权限过滤"
    )
    public List<Product> getProductsByAsin(ProductQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        // 1. 构建基础SQL
        sql.append("SELECT asin, product_name, category ");
        sql.append("FROM ods_t_amazon_ad_product ");
        sql.append("WHERE puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 2. 添加业务条件
        if (CollectionUtils.isNotEmpty(reqParam.getAsins())) {
            sql.append("AND asin IN (");
            for (int i = 0; i < reqParam.getAsins().size(); i++) {
                if (i > 0) sql.append(", ");
                sql.append("?");
                argsList.add(reqParam.getAsins().get(i));
            }
            sql.append(") ");
        }
        
        // 3. 添加权限过滤
        PermissionSqlBuilder.addPermissionFilter(sql, argsList, "asin");
        
        return getJdbcTemplate().query(sql.toString(), getProductRowMapper(), argsList.toArray());
    }
}
```

### 示例3：权限拦截示例

```java
@Service
public class CampaignServiceImpl {

    /**
     * 权限拦截示例 - 方法级权限控制
     */
    @AdProductPermissionFilter(
        type = PermissionFilterType.CAMPAIGN,
        field = "campaign_id",
        shopIdsExpression = "#campaignId",  // 直接使用方法参数
        strategy = PermissionFilterStrategy.RESULT_FILTER,
        description = "单个活动权限拦截"
    )
    public Campaign getCampaignById(String campaignId, Integer puid, Integer uid) {
        // 1. 查询活动数据（不带权限过滤）
        Campaign campaign = campaignDao.findById(campaignId);
        
        if (campaign == null) {
            throw new BizServiceException("活动不存在");
        }
        
        // 2. 权限过滤会在方法执行后自动处理
        // 如果用户没有权限访问该活动，会被自动过滤掉
        return campaign;
    }
    
    /**
     * 批量操作权限拦截
     */
    @AdProductPermissionFilter(
        type = PermissionFilterType.CAMPAIGN,
        field = "campaign_id",
        shopIdsExpression = "#reqParam.shopIds",
        adTypeExpression = "#reqParam.adType",
        strategy = PermissionFilterStrategy.SQL_INJECTION,
        description = "批量活动操作权限控制"
    )
    public BatchOperationResult batchUpdateCampaigns(BatchUpdateRequest reqParam) {
        // 1. 获取用户有权限的活动列表（自动过滤）
        List<Campaign> campaigns = campaignDao.getCampaignsByIds(reqParam.getCampaignIds());
        
        // 2. 执行批量更新
        int updatedCount = 0;
        for (Campaign campaign : campaigns) {
            // 只能更新有权限的活动
            campaignDao.updateCampaign(campaign);
            updatedCount++;
        }
        
        return BatchOperationResult.builder()
                .totalCount(reqParam.getCampaignIds().size())
                .successCount(updatedCount)
                .build();
    }
}
```

### 示例4：复杂查询权限过滤

```java
@Repository
public class ReportDaoImpl {

    /**
     * 复杂报表查询权限过滤
     */
    @AdProductPermissionFilter(
        type = PermissionFilterType.CAMPAIGN,
        field = "c.campaign_id",  // 使用表别名
        shopIdsExpression = "#reqParam.shopIds",
        adTypeExpression = "#reqParam.adType",
        strategy = PermissionFilterStrategy.SQL_INJECTION,
        description = "活动报表权限过滤"
    )
    public List<CampaignReport> getCampaignReport(ReportQueryRequest reqParam) {
        List<Object> argsList = new ArrayList<>();
        StringBuilder sql = new StringBuilder();
        
        // 1. 复杂的多表关联查询
        sql.append("SELECT c.campaign_id, c.campaign_name, ");
        sql.append("       SUM(s.impressions) as total_impressions, ");
        sql.append("       SUM(s.clicks) as total_clicks, ");
        sql.append("       SUM(s.cost) as total_cost ");
        sql.append("FROM ods_t_amazon_ad_campaign_all c ");
        sql.append("LEFT JOIN ods_t_amazon_ad_statistics s ON c.campaign_id = s.campaign_id ");
        sql.append("WHERE c.puid = ? ");
        argsList.add(reqParam.getPuid());
        
        // 2. 添加时间范围条件
        if (reqParam.getStartDate() != null && reqParam.getEndDate() != null) {
            sql.append("AND s.report_date BETWEEN ? AND ? ");
            argsList.add(reqParam.getStartDate());
            argsList.add(reqParam.getEndDate());
        }
        
        // 3. 添加权限过滤（支持表别名）
        PermissionSqlBuilder.addPermissionFilter(sql, argsList, "c.campaign_id");
        
        // 4. 分组和排序
        sql.append("GROUP BY c.campaign_id, c.campaign_name ");
        sql.append("ORDER BY total_cost DESC ");
        
        return getJdbcTemplate().query(sql.toString(), getReportRowMapper(), argsList.toArray());
    }
}
```

## 🔄 执行时序图

```mermaid
sequenceDiagram
    participant App as 应用调用
    participant Aspect as PermissionFilterAspect
    participant Context as PermissionContext
    participant DAO as DAO层
    participant SqlBuilder as PermissionSqlBuilder
    participant Factory as PermissionFilterFactory
    participant Filter as CampaignPermissionFilter
    participant Service as AdProductRightService
    participant DB as 数据库

    App->>+Aspect: 调用带@AdProductPermissionFilter的方法
    
    Note over Aspect,Context: 权限上下文构建阶段
    Aspect->>+Context: buildPermissionContext()
    Context->>Context: 提取用户信息(puid, uid)
    Context->>Context: 提取店铺信息(shopIds)
    Context->>Context: 提取广告类型(adType)
    Context-->>-Aspect: 返回权限上下文
    
    Aspect->>Context: setContext(context)
    
    Note over Aspect,DAO: 业务方法执行阶段
    Aspect->>+DAO: 执行原方法
    DAO->>DAO: 构建基础SQL
    
    Note over DAO,DB: SQL权限注入阶段
    DAO->>+SqlBuilder: addPermissionFilter(sql, args, "campaign_id")
    SqlBuilder->>Context: getContext()
    SqlBuilder->>+Factory: getFilter(CAMPAIGN)
    Factory-->>-SqlBuilder: CampaignPermissionFilter
    
    SqlBuilder->>+Filter: generateFilterSql("campaign_id", context, args)
    Filter->>+Service: getProductRightCampaignIdsSql(...)
    Service->>Service: 检查用户权限
    Service->>Service: 构建权限SQL
    Service-->>-Filter: 返回权限SQL片段
    Filter-->>-SqlBuilder: 返回完整权限SQL
    
    SqlBuilder->>SqlBuilder: 注入权限条件到原SQL
    SqlBuilder-->>-DAO: 返回带权限的完整SQL
    
    DAO->>+DB: 执行带权限过滤的SQL
    DB-->>-DAO: 返回过滤后的数据
    DAO-->>-Aspect: 返回查询结果
    
    Aspect->>Context: clear()
    Aspect-->>-App: 返回最终结果
```

## 🎯 关键特性

### 1. **声明式权限控制**
- 通过注解声明权限需求
- 业务代码无侵入
- 配置灵活，支持SpEL表达式

### 2. **自动SQL注入**
- 透明的权限条件注入
- 数据库层面过滤，性能最优
- 支持复杂查询和表别名

### 3. **多策略支持**
- SQL注入策略：性能最优
- 结果过滤策略：灵活性最高
- 可扩展新的过滤策略

### 4. **类型安全**
- 基于枚举的类型系统
- 编译时类型检查
- 运行时类型验证

这种设计实现了高内聚、低耦合的权限控制架构，既保证了性能，又提供了良好的扩展性和可维护性。
